logger:
  zap-config:
    level: warn # 日志级别
    development: false
    disableCaller: false
    disableStacktrace: false
    encoding: "console"
    # zap encoder 配置
    encoderConfig:
      messageKey: "message"
      levelKey: "level"
      timeKey: "time"
      nameKey: "logger"
      callerKey: "caller"
      stacktraceKey: "stacktrace"
      lineEnding: ""
      levelEncoder: "capital"
      timeEncoder: "iso8601"
      durationEncoder: "seconds"
      callerEncoder: "short"
      nameEncoder: ""
      EncodeDuration: zapcore.SecondsDurationEncoder,
      params:
        service: "my-service"
        version: "1.0.0"
    outputPaths:
      - "stderr"
    #initialFields:
      #app: "account"
    errorOutputPaths:
      - "stderr"
  lumberjack-config:
    # 写日志的文件名称
    filename: "logs/main.log"
    # 每个日志文件长度的最大大小，单位是 MiB。默认 100MiB
    maxSize: 10
    # 日志保留的最大天数(只保留最近多少天的日志)
    maxAge: 15
    # 只保留最近多少个日志文件，用于控制程序总日志的大小
    maxBackups: 10
    # 是否使用本地时间，默认使用 UTC 时间
    localTime: true
    # 是否压缩日志文件，压缩方法 gzip
    compress: false
  CallerSkip: 4

