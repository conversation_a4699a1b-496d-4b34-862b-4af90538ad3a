[service]
Domain = "fontree"
AppMode = "local"
HttpPort = ":9022"
IsHTTPS = false
OssDomain = false
GhId = "wx2ab0adfa3346d44f"
FddPersonVerifyUrl = "https://warehouse.szjixun.cn/uni-Identify-quality/index.html"
FddSignContractUrl =  "https://warehouse.szjixun.cn/uni-Identify-quality/index2.html"
FddVerifyHost = "realnameverify-test07.fadada.com"
FddSignHost =  "testapi06.fadada.com"
FddApiHost = "https://testapi06.fadada.com/api/"
Host = "https://auction-test2.szjixun.cn"
ShopHost = "https://auction-mall-test2.szjixun.cn"
ShopQrUrl = "https://auction-mall-test2.szjixun.cn"

[file]
ImagePath = "./runtime/images"

[bos]
AccessKeyId="LTAI5tLz1fSK53FQAEC9uNSb"
AccessKeySecret="******************************"
Endpoint="oss-cn-hangzhou.aliyuncs.com"
BucketName="fontree-test"
BosBaseDir = "inventory"
CdnHost="https://cdn-test.szjixun.cn"

[redis]
RedisDB =  "2"
RedisAddr = "127.0.0.1:6379"
RedisPW = ""
RedisDBNAme = "2"
PoolSize = 5

[wxPay]
MchID = "1642672845"
MchCertificateSerialNumber = "6AE855722E2237E6490002F5E1DAA24D1475C2FD"
MchAPIv3Key = "9zkOKV6hngYzxeAnOGBeIe6l7ySg4jxw"
PayNotifyCodeUrl = "https://warehouse.szjixun.cn/api/warehouse/synalkdf1u349adsk"
SuppleNotifyCodeUrl = "https://warehouse.szjixun.cn/api/warehouse/supplelkdf1u349adsk"
V2BatchPayNotifyCodeUrl = "https://warehouse.szjixun.cn/api/v2/warehouse/synalkdf1u349adsk"

[fdd]
URL="https://testapi.fadada.com:8443/api/"
AppId="405867"
AppSecret="8OaACwG6hAfV2MlG787srE1u"
ContentType="application/x-www-form-urlencoded;charset=UTF-8"
FileContentType="multipart/form-data;charset=utf8"

[msg]
defaultSignNo= "220028"

[aliPay]
isProd=true
appId="2021005138681619"
apiEncryptType="cert"
appPrivateKey="./conf/payCerts/alipay_yixuntong/appPrivateKey.txt"
appPublicCert="./conf/payCerts/alipay_yixuntong/appCertPublicKey_2021005138681619.crt"
aliPayRootCert="./conf/payCerts/alipay_yixuntong/alipayRootCert.crt"
aliPayPublicKey="./conf/payCerts/alipay_yixuntong/alipayCertPublicKey_RSA2.crt"
bodyEncryptKey="lea5gITBeCfbmfRPhsQBOA=="

[wePay]
appId= wx20bb6ea9752295aa
mchId= 1642672845
serialNo= 6AE855722E2237E6490002F5E1DAA24D1475C2FD
apiV3Key= 9zkOKV6hngYzxeAnOGBeIe6l7ySg4jxw
privateKey= ./conf/paycerts/wepay_yixuntong/apiclient_key.pem
callbackHost= "http://a.test.szjixun.cn"
orderExpireMinute=5
returnUrl="http://a.test.szjixun.cn/api/v1/fenghe/pay/show/result"

[otel]
host= "127.0.0.1:4317"
rate= 0.9
name= "拍卖商城"
open= true


[zapLog]
level = "info"
filename = "logs/fenghe_zap.log"
max_size = 5
max_age = 30
max_backups = 30

[fonchain]
Path = "http://*************:8000"
Ledger = "main"

