dubbo:
  registries:
    demoZK:
      protocol: zookeeper
      timeout: 3s
      address: 127.0.0.1:2181
  consumer:
    request-timeout: 30s
    references:
      ChainClientImpl:
        timeout: 40s
        retries: 0
        protocol: tri
        interface: com.fontree.microservices.common.chain
        filter: cshutdown,sign,fonDomain<PERSON>ilter,fonValidateFilter
        params:
          .accessKeyId: "SYD8-Chain-04"
          .secretAccessKey: "Al-Chain-FDF112"
      AccountClientImpl:
        protocol: tri
        interface: com.fontree.microservices.common.Account
        filter: cshutdown,sign,fonDomainFilter,fonValidateFilter,fonOtel
        timeout: 15s
        retries: 0
        params:
          .accessKeyId: "Accountksl"
          .secretAccessKey: "BSDY-FDF1-Fontree_account"
      PositionClientImpl:
        interface: com.fontree.microservices.common.Position # must be compatible with grpc or dubbo-java
        filter: cshutdown,sign,fonDomain<PERSON>ilter,fonValidate<PERSON><PERSON>er,fonOtel
        retries: 0
        params:
          .accessKeyId: "SYD8-23DF"
          .secretAccessKey: "BSDY-FDF1"
        methods:
          - name: "Update"
            retries: 0
      RuleClientImpl:
        timeout: 15s
        interface: com.fontree.microservices.common.Rule # must be compatible with grpc or dubbo-java
        filter: cshutdown,sign,fonDomainFilter,fonValidateFilter,fonOtel
        params:
          .accessKeyId: "SYD8-23DF"
          .secretAccessKey: "BSDY-FDF1"
        methods:
          - name: "Create"
            retries: 0
      FengheProviderClientImpl:
        protocol: tri
        interface: com.fontree.microservices.common.fengheProvider # must be compatible with grpc or dubbo-java
        retries: 0
        filter: tps,fonOtel
      CustomContractClientImpl:
        protocol: tri
        filter: tps,fonOtel
        interface: com.fontree.microservices.custom.contract
      OrderClientImpl:
        retries: 0
        protocol: tri
        interface: com.fontree.microservices.common.order # must be compatible with grpc or dubbo-java
        filter: cshutdown,sign,fonDomainFilter,fonValidateFilter,fonOtel
        params:
          .accessKeyId: "SYD8-order-04"
          .secretAccessKey: "Al-order-FDF112"
      ContractClientImpl:
        timeout: 15s
        retries: 0
        interface: com.fontree.microservices.common.contract # must be compatible with grpc or dubbo-java
        filter: cshutdown,sign,fonDomainFilter,fonValidateFilter,fonOtel
        params:
          .accessKeyId: "SYD8-Chain-04"
          .secretAccessKey: "Al-Chain-FDF112"
      ArtworkQueryClientImpl:
        protocol: tri
        interface: com.fontree.microservices.common.ArtworkQuery
        filter: tps,fonOtel
      SeriesClientImpl:
        interface: com.fontree.microservices.common.seriesProviderV2
        filter: tps,fonOtel
        retries: 0
      BrandClientImpl:
        protocol: tri
        filter: tps,fonOtel
        interface: com.fontree.microservices.common.shopBrandProviderV2
        retries: 0
      ArtistClientImpl:
        protocol: tri
        interface: com.fontree.microservices.common.Artist
        filter: cshutdown,fonDomainFilter,fonValidateFilter,fonOtel
      PaymentCentClientImpl:
        protocol: tri
        retries: 0
        interface: com.fontree.microservices.common.payment.cent # must be compatible with grpc or dubbo-java
        filter: cshutdown,fonDomainFilter,fonValidateFilter,fonOtel
        params:
          .accessKeyId: "Paymentksl"
          .secretAccessKey: "BSDY-FDF1-Fontree_payment"
      DepartmentClientImpl:
        timeout: 15s
        retries: 0
        interface: com.fontree.microservices.common.Department # must be compatible with grpc or dubbo-java
        filter: cshutdown,sign,fonDomainFilter,fonValidateFilter
        params:
          .accessKeyId: "SYD8-23DF"
          .secretAccessKey: "BSDY-FDF1"
      PhotoWallProviderClientImpl:
        interface: com.fontree.microservices.common.photoWallProvider
logger:
  zap-config:
    level: error # 日志级别
    development: false
    disableCaller: false
    disableStacktrace: false
    encoding: "console"
    # zap encoder 配置
    encoderConfig:
      messageKey: "message"
      levelKey: "level"
      timeKey: "time"
      nameKey: "logger"
      callerKey: "caller"
      stacktraceKey: "stacktrace"
      lineEnding: ""
      levelEncoder: "capital"
      timeEncoder: "iso8601"
      durationEncoder: "seconds"
      callerEncoder: "short"
      nameEncoder: ""
      EncodeDuration: zapcore.SecondsDurationEncoder,
      params:
        service: "my-service"
        version: "1.0.0"
    outputPaths:
      - "stderr"
      #initialFields:
      #app: "account"
    errorOutputPaths:
      - "stderr"
  lumberjack-config:
    # 写日志的文件名称
    filename: "logs/main.log"
    # 每个日志文件长度的最大大小，单位是 MiB。默认 100MiB
    maxSize: 10
    # 日志保留的最大天数(只保留最近多少天的日志)
    maxAge: 15
    # 只保留最近多少个日志文件，用于控制程序总日志的大小
    maxBackups: 10
    # 是否使用本地时间，默认使用 UTC 时间
    localTime: true
    # 是否压缩日志文件，压缩方法 gzip
    compress: false
  CallerSkip: 4