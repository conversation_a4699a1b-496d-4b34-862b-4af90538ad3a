[service]
Domain = "fontree"
AppMode = "prod"
HttpPort = ":9022"
Host = "https://fenghe-auction.szjixun.cn"
ShopHost = "https://fenghe-mall.szjixun.cn"
ShopQrUrl = "https://shop.szjixun.cn"

[file]
ImagePath = "./runtime/images"

[bos]
AccessKeyId="LTAI5tHfjSmWXHqfWgaL7Uo5"
AccessKeySecret="******************************"
Endpoint="oss-cn-hangzhou-internal.aliyuncs.com"
BucketName="erp-k8s-store"
BosBaseDir = "inventory"
CdnHost="https://e-cdn.fontree.cn"

[redis]
RedisDB = "2"
RedisAddr = "intel-hz-svc-fontree-redis-service:6379"
RedisPW = "fonchain_opv:kP6tW4tS3qB2dW4aE6uI5cX2"
RedisDbName = "2"
PoolSize = 150

[fdd]
URL="https://textapi.fadada.com/api2/"
AppId="501701"
AppSecret="goMdfSnrE3Ab99oLpCRP191S"
ContentType="application/x-www-form-urlencoded;charset=UTF-8"
FileContentType="multipart/form-data;charset=utf8"

[msg]
defaultSignNo= "220028"

[stripePay]
#有拍品支付二维码支付参数（包含线下线上支付）
signatureKey ="whsec_b5S3znioA6B8eCeRgky7f95loaAdwMXZ"
callbackHost= "https://auction.szjixun.cn"
successWebEndpoint= "/payment/result"
cancelWebEndpoint= "/payment/result"
#无拍品下线支付二维码的支付参数（只有线下支付）
zeroLotNoSignatureKey="whsec_9jbCfuIxq8zAIjMPkg3x0iBDv03Dc3lm"
zeroLotNoCallbackHost = "https://auction-pay.szjixun.cn"
zeroLotNoSuccessWebEndpoint="/collectCode/payment/result"
zeroLotNoCancelWebEndpoint="/collectCode/payment/result"

[aliPay]
isProd=true
appId="2021005138681619"
apiEncryptType="cert"
appPrivateKey="./conf/payCerts/alipay_yixuntong/appPrivateKey.txt"
appPublicCert="./conf/payCerts/alipay_yixuntong/appCertPublicKey_2021005138681619.crt"
aliPayRootCert="./conf/payCerts/alipay_yixuntong/alipayRootCert.crt"
aliPayPublicKey="./conf/payCerts/alipay_yixuntong/alipayCertPublicKey_RSA2.crt"
bodyEncryptKey="lea5gITBeCfbmfRPhsQBOA=="

[wePay]
appId= "wx1fd7cccf98cb5554"
mchId= "1642672845"
serialNo= "6AE855722E2237E6490002F5E1DAA24D1475C2FD"
apiV3Key= "9zkOKV6hngYzxeAnOGBeIe6l7ySg4jxw"
privateKey= "./conf/payCerts/wepay_yixuntong/apiclient_key.pem"
apiClientCert= "./conf/payCerts/wepay_yixuntong/apiclient_cert.pem"

[antom]
channelCode="Antom_For_Fenghe"
channelType="antom"

[zapLog]
level = "info"
filename = "logs/fenghe_zap.log"
max_size = 5
max_age = 30
max_backups = 30

[fonchain]
Path = "http://************:8080"
Ledger = "x0y5wvpkoh"
