[service]
Domain = "fontree"
AppMode = "test"
HttpPort = ":9022"
OssDomain = false
Host = "https://auction-test2.szjixun.cn"
ShopHost = "https://auction-mall-test2.szjixun.cn"
ShopQrUrl = "https://auction-mall-test2.szjixun.cn"

[file]
ImagePath = "./runtime/images"

[bos]
AccessKeyId="LTAI5tLz1fSK53FQAEC9uNSb"
AccessKeySecret="******************************"
Endpoint="oss-cn-hangzhou.aliyuncs.com"
BucketName="fontree-test"
BosBaseDir = "inventory"
CdnHost="https://cdn-test.szjixun.cn"

[redis]
RedisDB = "1"
RedisAddr = "**************:6379"
RedisPW = "kP6tW4tS3qB2dW4aE6uI5cX2"
RedisDBNAme = "1"
PoolSize = 5

[fdd]
URL="https://testapi.fadada.com:8443/api/"
AppId="405867"
AppSecret="8OaACwG6hAfV2MlG787srE1u"
ContentType="application/x-www-form-urlencoded;charset=UTF-8"
FileContentType="multipart/form-data;charset=utf8"

[msg]
defaultSignNo= "220028"

[stripePay]
#有拍品支付二维码支付参数（包含线下线上支付）
signatureKey ="whsec_b9iQo6L1rJFv2XzENMVXMaqfejnwVlEG"
callbackHost= "https://auction-test.szjixun.cn"
successWebEndpoint = "/payment/result"
cancelWebEndpoint = "/payment/result"
#无拍品下线支付二维码的支付参数（只有线下支付）
zeroLotNoSignatureKey = "whsec_9jbCfuIxq8zAIjMPkg3x0iBDv03Dc3lm"
zeroLotNoCallbackHost = "https://auction-pay.szjixun.cn"
zeroLotNoSuccessWebEndpoint = "/collectCode/payment/result"
zeroLotNoCancelWebEndpoint = "/collectCode/payment/result"
[offlineQrCode]
qrCodeHost = "http://127.0.0.1:9021/web"

[aliPay]
isProd=true
appId="2021005138681619"
apiEncryptType="cert"
appPrivateKey="./conf/payCerts/alipay_yixuntong/appPrivateKey.txt"
appPublicCert="./conf/payCerts/alipay_yixuntong/appCertPublicKey_2021005138681619.crt"
aliPayRootCert="./conf/payCerts/alipay_yixuntong/alipayRootCert.crt"
aliPayPublicKey="./conf/payCerts/alipay_yixuntong/alipayCertPublicKey_RSA2.crt"
bodyEncryptKey="lea5gITBeCfbmfRPhsQBOA=="
[wePay]
#appId= "wx20bb6ea9752295aa"
#mchId= "1642672845"
#serialNo= "6AE855722E2237E6490002F5E1DAA24D1475C2FD"
#apiV3Key= "9zkOKV6hngYzxeAnOGBeIe6l7ySg4jxw"
#privateKey= "./conf/payCerts/wepay_yixuntong/apiclient_key.pem"
#orderExpireMinute=5

#丰链艺树
appId= "wx1fd7cccf98cb5554"
mchId= "1642672845"
serialNo= "6AE855722E2237E6490002F5E1DAA24D1475C2FD"
apiV3Key= "9zkOKV6hngYzxeAnOGBeIe6l7ySg4jxw"
privateKey= "./conf/payCerts/wepay_yixuntong/apiclient_key.pem"
apiClientCert= "./conf/payCerts/wepay_yixuntong/apiclient_cert.pem"

[antom]
channelCode="Antom_For_Fenghe"
channelType="antom"

[otel]
host= "*************:4317"
rate= 0.1
name= "拍卖商城-test"
open= true
debug= false

[zapLog]
level = "info"
filename = "logs/fenghe_zap.log"
max_size = 5
max_age = 30
max_backups = 30

[fonchain]
Path = "http://*************:8000"
Ledger = "main"