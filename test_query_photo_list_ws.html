<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>QueryPhotoListWs WebSocket 测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }

        .container {
            background: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }

        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }

        .form-group {
            margin-bottom: 15px;
        }

        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #555;
        }

        input, textarea {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
            box-sizing: border-box;
        }

        textarea {
            height: 60px;
            resize: vertical;
        }

        .button-group {
            text-align: center;
            margin: 20px 0;
        }

        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            margin: 0 5px;
        }

        button:hover {
            background-color: #0056b3;
        }

        button:disabled {
            background-color: #ccc;
            cursor: not-allowed;
        }

        .disconnect {
            background-color: #dc3545;
        }

        .disconnect:hover {
            background-color: #c82333;
        }

        .status {
            padding: 10px;
            border-radius: 4px;
            margin-bottom: 20px;
            font-weight: bold;
        }

        .status.connected {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .status.disconnected {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .status.connecting {
            background-color: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }

        .messages {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 15px;
            height: 400px;
            overflow-y: auto;
            font-family: monospace;
            white-space: pre-wrap;
        }

        .message {
            margin-bottom: 10px;
            padding: 8px;
            border-radius: 4px;
        }

        .message.sent {
            background-color: #e3f2fd;
            border-left: 4px solid #2196f3;
        }

        .message.received {
            background-color: #f3e5f5;
            border-left: 4px solid #9c27b0;
        }

        .message.error {
            background-color: #ffebee;
            border-left: 4px solid #f44336;
        }

        .timestamp {
            color: #666;
            font-size: 12px;
        }

        .row {
            display: flex;
            gap: 20px;
        }

        .col {
            flex: 1;
        }
    </style>
</head>
<body>
    <h1>QueryPhotoListWs WebSocket 测试工具</h1>

    <div class="container">
        <div id="status" class="status disconnected">未连接</div>

        <div class="row">
            <div class="col">
                <h3>连接配置</h3>
                <div class="form-group">
                    <label for="wsUrl">WebSocket URL:</label>
                    <input type="text" id="wsUrl" value="ws://localhost:8080/api/photoWall/queryPhotoListWs?page=1&pageSize=10" placeholder="WebSocket服务器地址">
                </div>

                <h3>请求参数 (QueryPhotoListReq)</h3>
                <div class="form-group">
                    <label for="page">页码 (page):</label>
                    <input type="number" id="page" value="1" min="1">
                </div>

                <div class="form-group">
                    <label for="pageSize">每���大小 (pageSize):</label>
                    <input type="number" id="pageSize" value="10" min="1" max="100">
                </div>

                <div class="form-group">
                    <label for="blockHash">区块哈希 (blockHash):</label>
                    <input type="text" id="blockHash" placeholder="可选">
                </div>

                <div class="form-group">
                    <label for="transactionHash">交易哈希 (transactionHash):</label>
                    <input type="text" id="transactionHash" placeholder="可选">
                </div>

                <div class="form-group">
                    <label for="blockHeight">区块高度 (blockHeight):</label>
                    <input type="number" id="blockHeight" placeholder="可选" min="0">
                </div>

                <div class="form-group">
                    <label for="windUpTime">上链时间 (windUpTime):</label>
                    <input type="text" id="windUpTime" placeholder="可选，格式: 2006-01-02 15:04:05">
                </div>

                <div class="form-group">
                    <label for="photoUrl">图片URL (photoUrl):</label>
                    <input type="text" id="photoUrl" placeholder="可选">
                </div>

                <div class="button-group">
                    <button id="connectBtn" onclick="connect()">连接</button>
                    <button id="disconnectBtn" onclick="disconnect()" disabled>断开连接</button>
                    <button id="sendRequestBtn" onclick="sendRequest()" disabled>发送请求</button>
                    <button id="clearBtn" onclick="clearMessages()">清空消息</button>
                </div>
            </div>

            <div class="col">
                <h3>消息日志</h3>
                <div id="messages" class="messages"></div>

                <div style="margin-top: 15px;">
                    <div style="display: flex; gap: 10px; align-items: center;">
                        <span>推送模式:</span>
                        <span id="pushMode">手动触发 (点击"发送请求"按钮)</span>
                    </div>
                    <div style="margin-top: 10px;">
                        <small style="color: #666;">
                            * 连接建立后，需要手动点击"发送请求"按钮才会推送数据<br>
                            * 可以通过WebSocket发送更新的查询参数<br>
                            * 支持WebSocket ping/pong心跳检测
                        </small>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        let ws = null;
        let isConnected = false;

        function updateStatus(status, className) {
            const statusEl = document.getElementById('status');
            statusEl.textContent = status;
            statusEl.className = `status ${className}`;
        }

        function addMessage(content, type = 'received') {
            const messagesEl = document.getElementById('messages');
            const messageEl = document.createElement('div');
            messageEl.className = `message ${type}`;

            const timestamp = new Date().toLocaleTimeString();
            const timestampEl = document.createElement('div');
            timestampEl.className = 'timestamp';
            timestampEl.textContent = `[${timestamp}]`;

            const contentEl = document.createElement('div');
            if (type === 'sent') {
                contentEl.textContent = `发送: ${content}`;
            } else if (type === 'error') {
                contentEl.textContent = `错误: ${content}`;
            } else {
                // 尝试格式化JSON
                try {
                    const parsed = JSON.parse(content);
                    contentEl.textContent = `接收: ${JSON.stringify(parsed, null, 2)}`;
                } catch (e) {
                    contentEl.textContent = `接收: ${content}`;
                }
            }

            messageEl.appendChild(timestampEl);
            messageEl.appendChild(contentEl);
            messagesEl.appendChild(messageEl);
            messagesEl.scrollTop = messagesEl.scrollHeight;
        }

        function getRequestParams() {
            return {
                page: parseInt(document.getElementById('page').value) || 1,
                pageSize: parseInt(document.getElementById('pageSize').value) || 10,
                blockHash: document.getElementById('blockHash').value || "",
                transactionHash: document.getElementById('transactionHash').value || "",
                blockHeight: parseInt(document.getElementById('blockHeight').value) || 0,
                windUpTime: document.getElementById('windUpTime').value || "",
                photoUrl: document.getElementById('photoUrl').value || ""
            };
        }

        function connect() {
            if (isConnected) return;

            // 收集请求参数
            const req = getRequestParams();

            // 构建带查询参数的WebSocket URL
            let baseUrl = document.getElementById('wsUrl').value.split('?')[0];
            let queryParams = new URLSearchParams();

            queryParams.set('page', req.page.toString());
            queryParams.set('pageSize', req.pageSize.toString());

            if (req.blockHash) queryParams.set('blockHash', req.blockHash);
            if (req.transactionHash) queryParams.set('transactionHash', req.transactionHash);
            if (req.blockHeight > 0) queryParams.set('blockHeight', req.blockHeight.toString());
            if (req.windUpTime) queryParams.set('windUpTime', req.windUpTime);
            if (req.photoUrl) queryParams.set('photoUrl', req.photoUrl);

            const finalUrl = `${baseUrl}?${queryParams.toString()}`;

            updateStatus('连接中...', 'connecting');
            addMessage(`构建的WebSocket URL: ${finalUrl}`, 'sent');
            addMessage(`请求参数: ${JSON.stringify(req, null, 2)}`, 'sent');

            try {
                // 使用构建的URL创建WebSocket连接
                ws = new WebSocket(finalUrl, ['p0', 'p1']);

                ws.onopen = function(event) {
                    isConnected = true;
                    updateStatus('已连接', 'connected');
                    addMessage('WebSocket连接已建立，点击"发送请求"按钮来获取数据', 'received');

                    document.getElementById('connectBtn').disabled = true;
                    document.getElementById('disconnectBtn').disabled = false;
                    document.getElementById('sendRequestBtn').disabled = false;
                };

                ws.onmessage = function(event) {
                    addMessage(event.data, 'received');
                };

                ws.onclose = function(event) {
                    isConnected = false;
                    updateStatus('连接已断开', 'disconnected');

                    let reason = '';
                    switch(event.code) {
                        case 1000:
                            reason = '正常关闭';
                            break;
                        case 1006:
                            reason = '连接异常断开，可能是服务器问题或网络问题';
                            break;
                        case 1011:
                            reason = '服务器遇到意外情况';
                            break;
                        default:
                            reason = event.reason || '未知原因';
                    }

                    addMessage(`连接关闭: 代码=${event.code}, 原因=${reason}`, 'error');

                    document.getElementById('connectBtn').disabled = false;
                    document.getElementById('disconnectBtn').disabled = true;
                    document.getElementById('sendRequestBtn').disabled = true;
                };

                ws.onerror = function(event) {
                    addMessage('WebSocket连接错误，请检查：\n1. 服务器是否启动\n2. URL路径是否正确\n3. 端口是否开放\n4. 防火墙设置', 'error');
                    console.error('WebSocket error:', event);
                    updateStatus('连接失败', 'disconnected');
                };

            } catch (error) {
                addMessage(`连接失败: ${error.message}`, 'error');
                updateStatus('连接失败', 'disconnected');
            }
        }

        function sendRequest() {
            if (!ws || !isConnected) {
                addMessage('请先建立WebSocket连接', 'error');
                return;
            }

            try {
                // 发送当前的查询参数和isSend信号
                const req = getRequestParams();
                const message = {
                    req: req,
                    isSend: true
                };

                ws.send(JSON.stringify(message));
                addMessage(`发送请求信号: ${JSON.stringify(message, null, 2)}`, 'sent');
            } catch (error) {
                addMessage(`发送请求失败: ${error.message}`, 'error');
            }
        }

        function disconnect() {
            if (ws && isConnected) {
                ws.close();
            }
        }

        function clearMessages() {
            document.getElementById('messages').innerHTML = '';
        }

        // 页面加载时的初始化
        document.addEventListener('DOMContentLoaded', function() {
            addMessage('测试工具已准备就绪。点击"连接"建立连接，然后点击"发送请求"获取数据。', 'sent');
        });

        // 监听回车键快速连接
        document.addEventListener('keypress', function(e) {
            if (e.key === 'Enter' && !isConnected) {
                connect();
            } else if (e.key === 'Enter' && isConnected) {
                sendRequest();
            }
        });
    </script>
</body>
</html>
