package oss

import (
	"fmt"
	"github.com/aliyun/aliyun-oss-go-sdk/oss"
	"io/ioutil"
	"os"
	"path"
	"testing"
)

func TestOssUploadLocal12(t *testing.T) {
	// 从环境变量中获取临时访问凭证。运行本代码示例之前，请确保已设置环境变量OSS_ACCESS_KEY_ID、OSS_ACCESS_KEY_SECRET、OSS_SESSION_TOKEN。
	/// 从环境变量中获取访问凭证。运行本代码示例之前，请确保已设置环境变量OSS_ACCESS_KEY_ID和OSS_ACCESS_KEY_SECRET。
	/*provider, err := oss.New("oss-cn-hangzhou.aliyuncs.com", "LTAI5tHfjSmWXHqfWgaL7Uo5", "******************************")

	if err != nil {
		fmt.Println("Error:", err)
		os.Exit(-1)
	}

	*/
	//erp - k8s - store.oss - cn - hangzhou - internal.aliyuncs.com

	// 创建OSSClient实例。
	// yourEndpoint填写Bucket对应的Endpoint，以华东1（杭州）为例，填写为https://oss-cn-hangzhou.aliyuncs.com。其他Region请按实际情况填写。
	client, err := oss.New("oss-cn-hangzhou.aliyuncs.com", "LTAI5tHfjSmWXHqfWgaL7Uo5", "******************************")
	//client, err := oss.New("oss-cn-hangzhou-internal.aliyuncs.com", "LTAI5tHfjSmWXHqfWgaL7Uo5", "******************************")
	if err != nil {
		fmt.Println("Error:", err)
		os.Exit(-1)
	}

	// 填写Bucket名称，例如examplebucket。
	bucketName := "erp-k8s-store"
	// 填写文件完整路径，例如exampledir/exampleobject.txt。文件完整路径中不能包含Bucket名称。
	objectName := "1.png"
	// 下载OSS文件到本地文件，并保存到指定的本地路径中。如果指定的本地文件存在会覆盖，不存在则新建。
	// 如果未指定本地路径，则下载后的文件默认保存到示例程序所属项目对应本地路径中。

	// 获取存储空间。
	bucket, err := client.Bucket(bucketName)
	if err != nil {
		panic(err)
	}

	// 指定签名URL过期时间，单位为秒，本示例以设置签名ULR过期时间为30天为例。您可以根据实际业务场景，设置合理的过期时间。
	signedURL, err := bucket.SignURL(objectName, oss.HTTPGet, 100)
	fmt.Println(signedURL, err)

}
func TestOssUploadLocal(t *testing.T) {
	bossConfig := Bos{
		AccessKeyId:     "LTAI5tHfjSmWXHqfWgaL7Uo5",
		AccessKeySecret: "******************************",
		Endpoint:        "oss-cn-hangzhou.aliyuncs.com",
		Host:            "https://oss-cn-hangzhou.aliyuncs.com",
		BucketName:      "erp-k8s-store",
		BosBaseDir:      "inventory",
		CdnHost:         "https://e-cdn.fontree.cn",
	}

	//Endpoint="bj.bcebos.com"
	//Host="https://bj.bcebos.com"

	LoadOssEnv(bossConfig)
	type args struct {
		ossFile   string
		localFile string
	}
	tests := []struct {
		name    string
		args    args
		want    string
		wantErr bool
	}{
		// TODO: Add test cases.
		{args: args{ossFile: "3.png", localFile: "2.png"}},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := OssUploadLocal(tt.args.ossFile, tt.args.localFile)
			fmt.Println("1-----", got, err)
			if (err != nil) != tt.wantErr {
				t.Errorf("OssUploadLocal() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if got != tt.want {
				t.Errorf("OssUploadLocal() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestOssUploadToBosDev(t *testing.T) {
	bossConfig := Bos{
		AccessKeyId:     "LTAI5tLz1fSK53FQAEC9uNSb",
		AccessKeySecret: "******************************",
		Endpoint:        "oss-cn-hangzhou.aliyuncs.com",
		Host:            "https://oss-cn-hangzhou.aliyuncs.com",
		BucketName:      "fontree-test",
		BosBaseDir:      "tt",
		CdnHost:         "https://cdn-test.szjixun.cn",
	}

	LoadOssEnv(bossConfig)

	type args struct {
		suffix    string
		fileBytes []byte
	}
	fileExt := path.Ext("2.png")
	fmt.Println(fileExt)
	content, err := ioutil.ReadFile("2.png")
	if err != nil {
		fmt.Println("Error reading file:", err)
		return
	}

	tests := []struct {
		name    string
		args    args
		want    string
		wantErr bool
	}{
		// TODO: Add test cases.
		{args: args{suffix: path.Ext("2.png"), fileBytes: content}},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := OssUploadToBos(tt.args.suffix, tt.args.fileBytes)
			if (err != nil) != tt.wantErr {
				t.Errorf("OssUploadToBos() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if got != tt.want {
				t.Errorf("OssUploadToBos() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestOssUploadToBos(t *testing.T) {
	bossConfig := Bos{
		AccessKeyId:     "LTAI5tHfjSmWXHqfWgaL7Uo5",
		AccessKeySecret: "******************************",
		Endpoint:        "oss-cn-hangzhou.aliyuncs.com",
		Host:            "https://oss-cn-hangzhou.aliyuncs.com",
		BucketName:      "erp-k8s-store",
		BosBaseDir:      "inventory",
		CdnHost:         "https://e-cdn.fontree.cn",
	}

	LoadOssEnv(bossConfig)

	type args struct {
		suffix    string
		fileBytes []byte
	}
	fileExt := path.Ext("2.png")
	fmt.Println(fileExt)
	content, err := ioutil.ReadFile("2.png")
	if err != nil {
		fmt.Println("Error reading file:", err)
		return
	}

	tests := []struct {
		name    string
		args    args
		want    string
		wantErr bool
	}{
		// TODO: Add test cases.
		{args: args{suffix: path.Ext("2.png"), fileBytes: content}},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := OssUploadToBos(tt.args.suffix, tt.args.fileBytes)
			if (err != nil) != tt.wantErr {
				t.Errorf("OssUploadToBos() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if got != tt.want {
				t.Errorf("OssUploadToBos() got = %v, want %v", got, tt.want)
			}
		})
	}
}
