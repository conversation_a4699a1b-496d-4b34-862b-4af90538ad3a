package oss

import (
	"fmt"
	"github.com/fonchain_enterprise/utils/objstorage"
	"github.com/google/uuid"
)

type OssVal struct {
	obj objstorage.API
	bos Bos
}

var ossObj OssVal

//Oss 对象存储配置信息
type Bos struct {
	AccessKeyId     string
	AccessKeySecret string
	Endpoint        string
	Host            string
	CdnHost         string
	BucketName      string
	BosBaseDir      string
}

func LoadEnv(bos Bos) (err error) {
	v, err := objstorage.NewBOS(bos.AccessKeyId, bos.AccessKeySecret, bos.Endpoint)
	if err != nil {
		return err
	}

	ossObj.bos = bos
	ossObj.obj = v

	return nil
}

//UploadLocal 上传本地
//UploadLocal("art_block_chain/files/artwork/1.png")
func UploadLocal(ossFile string, localFile string) (string, error) {
	api := ossObj.obj
	ossConfig := ossObj.bos
	fmt.Printf("%+v", ossConfig)

	//eTag, err := api.PutObjectFromFile(ossConfig.Endpoint, "art_block_chain/files/artwork/"+tmp1+"_artworkal.jpg", ossConfig.Endpoint)
	_, err := api.PutObjectFromFile(ossConfig.BucketName, ossFile, localFile)
	if err != nil {
		return "", err
	}

	return ossConfig.Host + "/" + ossConfig.BucketName + "/" + ossFile, nil
}

func UploadToBos(suffix string, fileBytes []byte) (string, error) {
	api := ossObj.obj
	ossConfig := ossObj.bos
	fmt.Printf("1-------------%+v", ossConfig)

	newUUid, uerror := uuid.NewUUID()
	if uerror != nil {
		return "", uerror
	}

	ossFile := ossConfig.BosBaseDir + "/files/img/" + newUUid.String() + suffix

	//eTag, err := api.PutObjectFromFile(ossConfig.Endpoint, "art_block_chain/files/artwork/"+tmp1+"_artworkal.jpg", ossConfig.Endpoint)
	_, err := api.PutObjectFromBytes(ossConfig.BucketName, ossFile, fileBytes)
	fmt.Println("1---", err)
	fmt.Println("1---", ossConfig.BucketName)

	if err != nil {
		return "", err
	}

	return ossConfig.CdnHost + "/" + ossFile, nil
}
