package oss

import (
	"fmt"
	"github.com/fonchain_enterprise/utils/objstorage"
	"github.com/google/uuid"
)

type AliOssVal struct {
	obj objstorage.API
	bos Bos
}

var aliOssObj AliOssVal

func LoadOssEnv(bos <PERSON><PERSON>) (err error) {
	v, err := objstorage.NewOSS(bos.AccessKeyId, bos.AccessKeySecret, bos.Endpoint)
	if err != nil {
		return err
	}

	aliOssObj.bos = bos
	aliOssObj.obj = v

	return nil
}

//UploadLocal 上传本地
//UploadLocal("art_block_chain/files/artwork/1.png")
func OssUploadLocal(ossFile string, localFile string) (string, error) {
	api := aliOssObj.obj
	ossConfig := aliOssObj.bos
	fmt.Printf("%+v", ossConfig)

	//eTag, err := api.PutObjectFromFile(ossConfig.Endpoint, "art_block_chain/files/artwork/"+tmp1+"_artworkal.jpg", ossConfig.Endpoint)
	fmt.Println("这边看看", ossFile, localFile)
	_, err := api.PutObjectFromFile(ossConfig.BucketName, ossFile, localFile)
	if err != nil {
		return "", err
	}

	return ossConfig.CdnHost + "/" + ossFile, nil
}

func OssUploadToBos(suffix string, fileBytes []byte) (string, error) {
	api := aliOssObj.obj
	ossConfig := aliOssObj.bos
	fmt.Printf("1-------------%+v", ossConfig)

	newUUid, uerror := uuid.NewUUID()
	if uerror != nil {
		return "", uerror
	}

	ossFile := ossConfig.BosBaseDir + "/files/img/" + newUUid.String() + suffix

	//eTag, err := api.PutObjectFromFile(ossConfig.Endpoint, "art_block_chain/files/artwork/"+tmp1+"_artworkal.jpg", ossConfig.Endpoint)
	_, err := api.PutObjectFromBytes(ossConfig.BucketName, ossFile, fileBytes)
	fmt.Println("1---", err)
	fmt.Println("1---", ossConfig.BucketName)

	if err != nil {
		return "", err
	}

	return ossConfig.CdnHost + "/" + ossFile, nil
}
