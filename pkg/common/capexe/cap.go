package capexe

import (
	"fmt"
	"time"
)

type CapExe struct {
	startTime     time.Time
	realStartTime time.Time
	i             int
}

func (m *CapExe) Log(name string) {
	fmt.Printf("执行次数：%d次，当前执行 %s,执行耗时：%s,总共耗时%s \n", m.i, name, time.Now().Sub(m.startTime), time.Now().Sub(m.realStartTime))
	m.startTime = time.Now()
	m.i++
}

func NewCapExe() *CapExe {
	return &CapExe{
		startTime:     time.Now(),
		realStartTime: time.Now(),
		i:             0,
	}
}
