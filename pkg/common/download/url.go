// Package download -----------------------------
// @file      : url.go
// <AUTHOR> JJXu
// @contact   : <EMAIL>
// @time      : 2025/6/5 09:55
// -------------------------------------------
package download

import (
	"github.com/fonchain_enterprise/client-auction/cmd/config"
	"github.com/gin-gonic/gin"
	"net/url"
)

func GetErpHostUrl(c *gin.Context) *url.URL {
	baseUrl, err := url.Parse(c.Request.Host)
	if err != nil {
		switch config.AppConfig.Service.AppMode {
		default:
			baseUrl = &url.URL{
				Scheme: "http",
				Host:   "127.0.0.1:9022",
			}
		case "test":
			baseUrl = &url.URL{
				Scheme: "http",
				Host:   "**************:9020",
			}
		case "prod":
			baseUrl = &url.URL{
				Scheme: "https",
				Host:   "erp.fontree.cn",
			}
		}
	}
	return baseUrl
}
