package m

var JWTSecret = []byte("asdfqwer1234")

const (
	SERVER_CONFIG = "../conf/conf.ini"
	LOG_CONFIG    = "../conf/log.yaml"
	Domain        = "pinjian"
)

const (
	TokenTime    = 24
	StatusInDoor = 1
)

const (
	DateNormalYMDFormat = "2006-01-02"
)

const (
	SUCCESS = "success"
	FAILED  = "failed"
)

const (
	ERRORCONFIG = "配置文件读取错误,请检查文件路径:"
	ERRORAUTH   = "没有权限"
)

const (
	//丰链编号为20开头
	FC       = iota + 200
	FONCHAIN //丰链商城
)

const (
	LineStatusRun      = 1
	LineStatusStop     = 2
	CircuitStatusOpen  = 1
	CircuitStatusClose = 2
)

// 订单状态参数
const (
	ORDER_STATUS_NEED_PUBLIC  = iota + 1 //待发布
	ORDER_STATUS_NEED_CONFIRM            //待确认
	ORDER_STATUS_APPEAL                  //申诉状态
	ORDER_STATUS_CONFIRMED               //已确认
)

const (
	Order_Doing             = iota + 1 //待总监确认 待发布
	Order_Need_Seller_Sure             //待销售确认
	Order_Status_Que                   //申诉订单
	Order_Done                         //完成
	Order_Need_Cashier_Sure            //待出纳确认
	Order_Need_Control_Sure            //待内控确认
	Order_Need_Finance_Sure            //待财务确认
)

const (
	REPORT_STATUS_NEED_PUBLIC   = iota + 1 //待发布
	REPORT_PUBLISH                         //已发布
	REPORT_NEED_CASHIER_SURE               //待出纳补充
	REPORT_NEED_BOSS_SURE                  //带最后总价确认
	REPORT_CASHIER_FINAL_OPTION            //待出纳确认 重置
)

const (
	TYPE_WEEK    = iota + 1 //周
	TYPE_MOUNTH             //月
	TYPE_QUARTER            //日期
)
