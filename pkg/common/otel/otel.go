package otel

import (
	"context"
	"fmt"
	"go.opentelemetry.io/otel"
	"go.opentelemetry.io/otel/attribute"
	"go.opentelemetry.io/otel/exporters/otlp/otlptrace/otlptracegrpc"
	"go.opentelemetry.io/otel/exporters/stdout/stdouttrace"
	"go.opentelemetry.io/otel/propagation"
	"go.opentelemetry.io/otel/sdk/resource"
	tracesdk "go.opentelemetry.io/otel/sdk/trace"
	semconv "go.opentelemetry.io/otel/semconv/v1.26.0"
	"google.golang.org/grpc/credentials/insecure"
	"log"

	"google.golang.org/grpc"
)

var tp *tracesdk.TracerProvider

// InitTracer 初始化 OpenTelemetry 追踪器
func InitTracer(host string, rate float64, projectName string, debug bool) func() {
	ctx := context.Background()
	exp, err := stdouttrace.New(
		stdouttrace.WithPrettyPrint(),
		stdouttrace.WithWriter(log.Writer()), // 将日志输出到标准日志库
	)

	if err != nil {
		log.Fatalf("failed to create exporter: %v", err)
	}
	// 创建一个 gRPC 连接到 OpenTelemetry Collector
	//conn, err := grpc.DialContext(ctx, "localhost:4317", grpc.WithInsecure(), grpc.WithBlock())
	conn, err := grpc.NewClient(host, grpc.WithTransportCredentials(insecure.NewCredentials()), grpc.WithBlock())
	fmt.Println("连接", conn, err)
	if err != nil {
		log.Fatalf("Failed to create gRPC connection to collector: %v", err)
	}

	// 创建一个 OTLP 追踪导出器
	exporter, err := otlptracegrpc.New(ctx, otlptracegrpc.WithGRPCConn(conn))

	fmt.Println("连接1-", exporter, err)
	if err != nil {
		log.Fatalf("Failed to create trace exporter: %v", err)
	}

	// 创建一个资源，用于描述你的应用程序
	r, err := resource.Merge(
		resource.Default(),
		resource.NewWithAttributes(
			semconv.SchemaURL,
			semconv.ServiceName(projectName),
			attribute.String("environment", "development"),
		),
	)

	fmt.Println("连接1-", r, err)
	if err != nil {
		log.Fatalf("Failed to create resource: %v", err)
	}
	sampler := tracesdk.TraceIDRatioBased(rate)

	if debug == true {
		// 创建一个追踪器提供者
		tp = tracesdk.NewTracerProvider(
			tracesdk.WithBatcher(exporter),
			tracesdk.WithResource(r),
			tracesdk.WithBatcher(exp),
		)
	} else {
		tp = tracesdk.NewTracerProvider(
			tracesdk.WithBatcher(exporter),
			tracesdk.WithResource(r),
			tracesdk.WithSampler(sampler),
		)
	}

	// 设置全局追踪器提供者
	otel.SetTracerProvider(tp)

	otel.SetTextMapPropagator(propagation.TraceContext{})

	return func() {
		if err := tp.Shutdown(context.Background()); err != nil {
			fmt.Printf("Error shutting down tracer provider: %v \n", err)
		}
	}
}

func GetOtel() *tracesdk.TracerProvider {
	return tp
}
