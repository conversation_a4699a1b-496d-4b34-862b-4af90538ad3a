package filter

import (
	"context"
	"go.opentelemetry.io/otel"
	"go.opentelemetry.io/otel/propagation"
)

import (
	"dubbo.apache.org/dubbo-go/v3/common/extension"
	"dubbo.apache.org/dubbo-go/v3/filter"
	"dubbo.apache.org/dubbo-go/v3/protocol"
)

func init() {
	extension.SetFilter("fonOtel", NewFontreeOtelFonFilter)
}

func NewFontreeOtelFonFilter() filter.Filter {
	return &FontreeOtelFonFilter{}
}

type FontreeOtelFonFilter struct {
}

func (f *FontreeOtelFonFilter) Invoke(ctx context.Context, invoker protocol.Invoker, invocation protocol.Invocation) protocol.Result {
	tracer := otel.Tracer("")

	ctx, span := tracer.Start(ctx, invoker.GetURL().ServiceKey()+"#"+invocation.MethodName())
	defer span.End()
	// 注入追踪上下文到 Dubbo 请求头
	//新加 扩散spanid
	propagator := otel.GetTextMapPropagator()

	carrier := make(map[string]string)
	propagator.Inject(ctx, propagation.MapCarrier(carrier))

	for k, v := range carrier {
		invocation.SetAttachment(k, v)
	}

	result := invoker.Invoke(ctx, invocation)

	if result.Error() != nil {
		span.RecordError(result.Error())
	}

	return result
}

func (f *FontreeOtelFonFilter) OnResponse(ctx context.Context, result protocol.Result, invoker protocol.Invoker, protocol protocol.Invocation) protocol.Result {
	return result
}
