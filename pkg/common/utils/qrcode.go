// Package utils -----------------------------
// @file      : qrcode.go
// <AUTHOR> JJXu
// @contact   : <EMAIL>
// @time      : 2025/2/19 16:56
// -------------------------------------------
package utils

import (
	"encoding/base64"
	"github.com/skip2/go-qrcode"
)

func GenerateQRCode(context string) (string, error) {
	// 使用 go-qrcode 库生成二维码图片并写入缓冲区
	buffer, err := qrcode.Encode(context, qrcode.Medium, 256)
	if err != nil {
		return "", err
	}
	// 将缓冲区的内容（二维码图片的字节流）编码为 Base64 字符串
	base64String := base64.StdEncoding.EncodeToString(buffer)
	return base64String, nil
}
