// Package utils -----------------------------
// @file      : host.go
// <AUTHOR> JJXu
// @contact   : <EMAIL>
// @time      : 2025/1/26 11:40
// -------------------------------------------
package utils

import "net/url"

// ReplaceHostWithOrigin
// 将host地址替换为请求头中的origin参数值
// requestOrigin: 请求头的Origin参数值
// currentUrl: 当前的url
func ReplaceHostWithOrigin(requestOrigin string, currentUrl string) string {
	requestOriginParser, _ := url.Parse(requestOrigin)
	currentUrlParser, _ := url.Parse(currentUrl)
	if requestOriginParser.Host != currentUrlParser.Host {
		currentUrlParser.Host = requestOriginParser.Host
	}
	return currentUrlParser.String()
}
