// Package utils -----------------------------
// @file      : auth_test.go
// <AUTHOR> JJXu
// @contact   : <EMAIL>
// @time      : 2023/5/15 9:13
// -------------------------------------------
package utils

import (
	"fmt"
	"github.com/shopspring/decimal"
	"testing"
)

func TestAuthenticationTwo(t *testing.T) {
	var auth = AuthService{
		Name:  "蒋英",
		IDNum: "320522198008111327",
	}
	err := AuthenticationTwo(auth)
	if err != nil {
		t.<PERSON>rror(err)
	} else {
		fmt.Println("认证成功")
	}
}

func TestAuthenticationTwo_xiangGang(t *testing.T) {
	var auth = AuthService{
		Name:       "廖琼",
		IDNum:      "E150614317",
		VerifyType: 4,
	}
	err := AuthenticationTwo(auth)
	if err != nil {
		t.<PERSON>rror(err)
	} else {
		fmt.Println("认证成功")
	}
}

func TestAuthentication(t *testing.T) {
	var auth = AuthService{
		Name:   "耿阳",
		IDNum:  "",
		TelNum: "18205052627",
	}
	err := AuthenticationThree(auth)
	if err != nil {
		t.Error(err)
	} else {
		fmt.Println("认证成功")
	}
}

func Test1(t *testing.T) {
	moneyDecimal, err := decimal.NewFromString("")
	fmt.Println(moneyDecimal, err)

	if err != nil {
		fmt.Println("失败")
	}
	if moneyDecimal.LessThanOrEqual(decimal.NewFromInt(0)) {
		fmt.Println("小于登陆0")
	}

}
