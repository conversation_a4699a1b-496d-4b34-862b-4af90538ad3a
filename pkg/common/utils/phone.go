package utils

import (
	"fmt"
	"regexp"
	"strings"
)

// PhoneValidator 手机号校验器
type PhoneValidator struct {
	countryRules map[string]*countryRule // 国家/地区规则
}

// 国家/地区规则
type countryRule struct {
	countryCode string              // 国家码，如"86"
	regex       *regexp.Regexp      // 手机号正则表达式
	carriers    map[string][]string // 运营商前缀映射
	isoCode     string              // ISO 3166-1 alpha-2 国家代码
}

// NewPhoneValidator 创建手机号校验器
func NewPhoneValidator() (*PhoneValidator, error) {
	// 初始化各国规则

	// 中国手机号规则
	chinaRegex, err := regexp.Compile(`^1[3-9]\d{9}$`)
	if err != nil {
		return nil, fmt.Errorf("编译中国手机号正则表达式失败: %v", err)
	}

	//// 美国手机号规则
	//usRegex, err := regexp.Compile(`^(?:\+?1)?[2-9]\d{2}[2-9]\d{6}$`)
	//if err != nil {
	//	return nil, fmt.Errorf("编译美国手机号正则表达式失败: %v", err)
	//}
	//
	//// 英国手机号规则
	//ukRegex, err := regexp.Compile(`^(?:\+?44)?(?:1\d{9}|7(?:[1345789]\d{8}|624\d{6}))$`)
	//if err != nil {
	//	return nil, fmt.Errorf("编译英国手机号正则表达式失败: %v", err)
	//}
	//// 日本手机号规则
	//// 格式: +81 X-XXX-XXXX 或 0X-XXX-XXXX
	//jpRegex, err := regexp.Compile(`^(?:\+81|0)?(70|80|90)\d{8}$`)
	//if err != nil {
	//	return nil, fmt.Errorf("编译日本手机号正则表达式失败: %v", err)
	//}

	validator := &PhoneValidator{
		countryRules: make(map[string]*countryRule),
	}

	// 添加中国规则
	validator.addCountryRule("86", &countryRule{
		countryCode: "86",
		regex:       chinaRegex,
		carriers: map[string][]string{
			"中国移动": {"134", "135", "136", "137", "138", "139", "147", "148", "150", "151", "152", "157", "158", "159", "165", "172", "178", "182", "183", "184", "187", "188", "195", "197", "198"},
			"中国联通": {"130", "131", "132", "145", "146", "155", "156", "166", "167", "171", "175", "176", "185", "186", "196"},
			"中国电信": {"133", "141", "149", "153", "162", "173", "174", "177", "180", "181", "189", "190", "191", "193", "199"},
			"中国广电": {"192"},
		},
		isoCode: "CN",
	})
	//
	//// 添加美国规则
	//validator.addCountryRule("US", &countryRule{
	//	countryCode: "1",
	//	regex:       usRegex,
	//	carriers: map[string][]string{
	//		"AT&T":     {"201", "202", "203", "205", "206"},
	//		"Verizon":  {"208", "209", "210", "212", "213"},
	//		"T-Mobile": {"215", "216", "217", "218", "219"},
	//	},
	//	isoCode: "US",
	//})
	//
	//// 添加英国规则
	//validator.addCountryRule("GB", &countryRule{
	//	countryCode: "44",
	//	regex:       ukRegex,
	//	carriers: map[string][]string{
	//		"EE":       {"7410", "7411", "7412", "7413", "7414"},
	//		"O2":       {"77", "78"},
	//		"Vodafone": {"79"},
	//	},
	//	isoCode: "GB",
	//})
	//
	//// 添加日本规则
	//validator.addCountryRule("JP", &countryRule{
	//	countryCode: "81",
	//	regex:       jpRegex,
	//	carriers: map[string][]string{
	//		"NTT Docomo": {"70", "80", "90"},
	//		"KDDI":       {"70", "80", "90"},
	//		"SoftBank":   {"70", "80", "90"},
	//		"Rakuten":    {"70"},
	//	},
	//	isoCode: "JP",
	//})
	//
	//// 台湾地区手机号规则
	//// 格式: +886 9XX XXX XXX 或 09XX XXX XXX
	//twRegex, err := regexp.Compile(`^(?:\+?886|0)?9\d{8}$`)
	//if err != nil {
	//	return nil, fmt.Errorf("编译台湾手机号正则表达式失败: %v", err)
	//}
	//
	//// 香港地区手机号规则
	//// 格式: +852 XXXX XXXX 或 9/6XXXXXXX
	//hkRegex, err := regexp.Compile(`^(?:\+?852)?[569]\d{7}$`)
	//if err != nil {
	//	return nil, fmt.Errorf("编译香港手机号正则表达式失败: %v", err)
	//}
	//// 添加台湾地区规则
	//validator.addCountryRule("TW", &countryRule{
	//	countryCode: "886",
	//	regex:       twRegex,
	//	carriers: map[string][]string{
	//		"中华电信":  {"9"},
	//		"台湾大哥大": {"9"},
	//		"远传电信":  {"9"},
	//		"亚太电信":  {"9"},
	//	},
	//	isoCode: "TW",
	//})
	//
	//// 添加香港地区规则
	//validator.addCountryRule("HK", &countryRule{
	//	countryCode: "852",
	//	regex:       hkRegex,
	//	carriers: map[string][]string{
	//		"中国移动香港": {"5", "6", "9"},
	//		"和记电讯":   {"5", "6", "9"},
	//		"数码通电讯":  {"5", "6", "9"},
	//		"电讯盈科":   {"5", "6", "9"},
	//	},
	//	isoCode: "HK",
	//})
	return validator, nil
}

// 添加国家/地区规则
func (v *PhoneValidator) addCountryRule(countryCode string, rule *countryRule) {
	v.countryRules[countryCode] = rule
}

// 校验手机号格式
func (v *PhoneValidator) Validate(phone string) bool {
	phone = strings.TrimSpace(phone)
	if phone == "" {
		return false
	}
	countryCode, phone := v.GetCountryCode(phone)
	// 使用中国手机号规则
	vd, ok := v.countryRules[countryCode]
	if !ok {
		return true
	}
	return vd.regex.MatchString(phone)
}

// 获取手机号的国家代码和处理后的手机号
func (v *PhoneValidator) GetCountryCode(phone string) (string, string) {
	phone = strings.TrimSpace(phone)
	if phone == "" {
		return "", ""
	}
	phoneItems := strings.Split(phone, "-")
	if len(phoneItems) == 2 {
		countryCode, strippedPhone := phoneItems[0], phoneItems[1]
		return countryCode, strippedPhone
	} else {
		return "86", phone
	}
}

// 提取国家码和去除国家码后的手机号
//func (v *PhoneValidator) extractCountryCode(phone string) (string, string) {
//	// 检查是否有+号前缀
//	if strings.HasPrefix(phone, "+") {
//		// 格式: +国家码手机号
//		// 简化处理，截取前2-4位作为国家码
//		maxLen := len(phone)
//		if maxLen > 5 {
//			maxLen = 5
//		}
//
//		for i := 2; i <= maxLen; i++ {
//			countryPart := phone[1:i]
//			stripped := phone[i:]
//
//			// 查找匹配的国家码
//			for code, rule := range v.countryRules {
//				if rule.countryCode == countryPart {
//					return code, stripped
//				}
//			}
//		}
//	}
//
//	// 没有+前缀，则默认使用中国区号
//	return "CN", phone
//}
