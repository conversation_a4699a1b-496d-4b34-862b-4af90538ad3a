package utils

import (
	"fmt"
	"github.com/fonchain/utils/baidu"
	"testing"
	"time"
)

func TestGetNumFromString(t *testing.T) {
	fmt.Println(time.Now())
	//s, e := json.Marshal(GetNumFromString("ztf1n2f3f4f5f6f7f8"))
	//fmt.Println(string(s), e)
}

func TestGetNumFromString1(t *testing.T) {
	baidu.OcrGetIdCardByUrl("", "front")
}

//func TestGetInfoFrom(t *testing.T) {
//	type args struct {
//		urlString string
//	}
//	tests := []struct {
//		name    string
//		args    args
//		want    *vo.WarehouseOcrRes
//		wantErr bool
//	}{
//		// TODO: Add test cases.
//		{args: args{urlString: "http://www.artkey.art/cert/goods/T10720001"}},
//		{args: args{urlString: "http://www.artkey.art/cert/detail/17168"}},
//		{args: args{urlString: "http://www.artkey.art/cert/detail/7852"}},
//	}
//	for _, tt := range tests {
//		t.Run(tt.name, func(t *testing.T) {
//			got, err := GetInfoFrom(tt.args.urlString)
//			fmt.Println(got.ArtworkName)
//			fmt.Println(got.ArtworkImg)
//			if (err != nil) != tt.wantErr {
//				t.Errorf("GetInfoFrom() error = %v, wantErr %v", err, tt.wantErr)
//				return
//			}
//			if !reflect.DeepEqual(got, tt.want) {
//				t.Errorf("GetInfoFrom() got = %v, want %v", got, tt.want)
//			}
//		})
//	}
//}

func TestRegNumber(t *testing.T) {
	type args struct {
		str string
	}
	tests := []struct {
		name  string
		args  args
		want  int
		want1 int
	}{

		{args: args{str: "181cm*224cm"}},
		{args: args{str: "181cm224cm"}},
		{args: args{str: "181cm*22ldk014cm"}},
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, got1, _ := RegNumber(tt.args.str)
			if got != tt.want {
				t.Errorf("RegNumber() got = %v, want %v", got, tt.want)
			}
			if got1 != tt.want1 {
				t.Errorf("RegNumber() got1 = %v, want %v", got1, tt.want1)
			}
		})
	}
}

func TestUrlChangeHost(t *testing.T) {
	type args struct {
		originalURL string
		host        string
	}
	tests := []struct {
		name    string
		args    args
		want    string
		wantErr bool
	}{
		// TODO: Add test cases.
		{args: args{host: "www.baidu.com", originalURL: "https://testapi.fadada.com:8443/api/verify_return_back.api?transactionNo=2054c7aab2f349219db9f627c3deec85&amp;personName=%25E8%25AE%25B8%25E6%2597%25BB%25E8%25BD%25B6&amp;status=2&amp;authenticationType=1&amp;sign=QTk1MDEzMTc3RDk0RkNGMDQzMkM5ODBFRDI5QTdENzk3MTAwNEVFQQ==&amp;time=1695713008352"}},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := UrlChangeHost(tt.args.originalURL, tt.args.host)
			if (err != nil) != tt.wantErr {
				t.Errorf("UrlChangeHost() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if got != tt.want {
				t.Errorf("UrlChangeHost() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestGetMd5(t *testing.T) {
	type args struct {
		str string
	}
	tests := []struct {
		name string
		args args
		want string
	}{
		// TODO: Add test cases.
		{args: args{str: "123123123122222222222222222"}},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := GetMd5(tt.args.str); got != tt.want {
				t.Errorf("GetMd5() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestGetBase64FromUrl(t *testing.T) {
	type args struct {
		imageURL string
	}
	tests := []struct {
		name    string
		args    args
		want    string
		wantErr bool
	}{
		// TODO: Add test cases.
		{args: args{imageURL: "https://cdns.fontree.cn/fonchain-main/prod/image/default/artwork/93b242b7-10d0-4095-b713-9cf8830abccb.JPG?x-bce-process=image/format,f_webp/resize,w_2560,h_2560"}},
		{args: args{imageURL: "https://cdns.fontree.cn/fonchain-main/prod/image/default/artwork/93b242b7-10d0-4095-b713-9cf8830abccb.JPG"}},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := GetBase64FromUrl(tt.args.imageURL)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetBase64FromUrl() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if got != tt.want {
				t.Errorf("GetBase64FromUrl() got = %v, want %v", got, tt.want)
			}
		})
	}
}
