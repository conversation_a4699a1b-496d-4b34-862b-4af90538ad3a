// Package utils -----------------------------
// @file      : jwt.go
// <AUTHOR> JJXu
// @contact   : <EMAIL>
// @time      : 2025/1/26 11:41
// -------------------------------------------
package utils

import (
	"encoding/json"
	"github.com/fonchain_enterprise/client-auction/pkg/e"
	"github.com/fonchain_enterprise/client-auction/pkg/middleware"
	"github.com/gin-gonic/gin"
)

func GetJwtUser(c *gin.Context) (userInfo *middleware.LoginInfo) {
	userInfo = &middleware.LoginInfo{}
	userInfoAny, ex := c.Get(e.JwtInfo)
	if ex {
		dataBytes, err := json.Marshal(&userInfoAny)
		if err != nil {
			return &middleware.LoginInfo{}
		}
		_ = json.Unmarshal(dataBytes, &userInfo)
		return userInfo
	}
	return
}
