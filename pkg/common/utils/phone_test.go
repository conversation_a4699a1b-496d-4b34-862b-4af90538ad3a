// Package utils -----------------------------
// @file      : phone_test.go
// <AUTHOR> JJXu
// @contact   : <EMAIL>
// @time      : 2025/6/3 17:04
// -------------------------------------------
package utils

import (
	"fmt"
	"testing"
)

func TestNewPhoneValidator(t *testing.T) {
	validator, err := NewPhoneValidator()
	if err != nil {
		fmt.Println("创建校验器失败:", err)
		return
	}

	// 测试手机号
	testPhones := []string{
		"13800138000",    // 中国移动
		"86-13900139000", // 中国移动（带国家码）
		"18912345678",    // 中国电信
		"17612345678",    // 中国联通
		"19212345678",    // 中国广电
		"12-125551234",   //美国手机号1
		"81-9012345678",  //日本手机号
		"88-6912345678",  //台湾手机号
		"85-298765432",   //香港手机号
		"44-7700900000",  //通用手机号
		"12345678901",    // 无效号码
		"1380013800",     // 长度不足
	}

	// 执行测试
	for _, phone := range testPhones {
		isValid := validator.Validate(phone)
		countryCode, stripePhone := validator.GetCountryCode(phone)
		fmt.Printf("国家号: %s  手机号: %-15s  有效性: %-5t \n", countryCode, stripePhone, isValid)
	}
}
