package utils

import (
	"bytes"
	"crypto/md5"
	"crypto/sha256"
	"encoding/base64"
	"encoding/hex"
	"encoding/json"
	"errors"
	"fmt"
	"github.com/fonchain_enterprise/client-auction/pkg/e"
	"github.com/fonchain_enterprise/client-auction/pkg/model/vo"
	"github.com/gin-gonic/gin"
	"github.com/nfnt/resize"
	"github.com/shopspring/decimal"
	"github.com/tealeg/xlsx"
	"image"
	"image/jpeg"
	"io"
	"io/ioutil"
	"math/rand"
	"net/http"
	"net/url"
	"os"
	"regexp"
	"strconv"
	"strings"
	"time"
)

type CircuitStatus struct {
	Num    uint8
	IsOpen bool
}

const ErrorMsg = "解析script失败"

// ToString 转string
func ToString(value interface{}) string {
	switch v := value.(type) {
	case string:
		return v
	case int:
		return strconv.FormatInt(int64(v), 10)
	case int8:
		return strconv.FormatInt(int64(v), 10)
	case int16:
		return strconv.FormatInt(int64(v), 10)
	case int32:
		return strconv.FormatInt(int64(v), 10)
	case int64:
		return strconv.FormatInt(v, 10)
	case uint:
		return strconv.FormatUint(uint64(v), 10)
	case uint8:
		return strconv.FormatUint(uint64(v), 10)
	case uint16:
		return strconv.FormatUint(uint64(v), 10)
	case uint32:
		return strconv.FormatUint(uint64(v), 10)
	case uint64:
		return strconv.FormatUint(v, 10)
	}
	return ""
}

// Contains 是否包含
func Contains(elems []string, elem string) bool {
	for _, e := range elems {
		if elem == e {
			return true
		}
	}
	return false
}

// CheckTruth 检测字符串是不是真
func CheckTruth(vals ...string) bool {
	for _, val := range vals {
		if val != "" && !strings.EqualFold(val, "false") {
			return true
		}
	}
	return false
}

func CheckDirPath(path string, create bool) (exists bool, err error) {
	exists = false
	if path == "" {
		err = errors.New(e.GetMsg(e.InvalidParams))
		return
	}
	if _, err = os.Stat(path); os.IsNotExist(err) {
		if !create {
			return
		}
		if err = os.MkdirAll(path, os.ModePerm); err != nil {
			return
		}
	}
	exists = true
	return
}

func ToExcelByte(titleList []string, dataList []interface{}) ([]byte) {
	// 生成一个新的文件
	file := xlsx.NewFile()
	// 添加sheet页
	sheet, _ := file.AddSheet("Sheet1")
	// 插入表头
	titleRow := sheet.AddRow()
	for _, v := range titleList {
		cell := titleRow.AddCell()
		cell.Value = v
	}
	// 插入内容
	for _, v := range dataList {
		row := sheet.AddRow()
		row.WriteStruct(v, -1)
	}

	var buffer bytes.Buffer
	_ = file.Write(&buffer)
	return buffer.Bytes()

}

func ToExcel(titleList []string, dataList []interface{}) (content io.ReadSeeker) {
	// 生成一个新的文件
	file := xlsx.NewFile()
	// 添加sheet页
	sheet, _ := file.AddSheet("Sheet1")
	// 插入表头
	titleRow := sheet.AddRow()
	for _, v := range titleList {
		cell := titleRow.AddCell()
		cell.Value = v
	}
	// 插入内容
	for _, v := range dataList {
		row := sheet.AddRow()
		row.WriteStruct(v, -1)
	}

	var buffer bytes.Buffer
	_ = file.Write(&buffer)
	content = bytes.NewReader(buffer.Bytes())
	return
}

// ResponseXls  content 为上面生成的io.ReadSeeker， fileTag 为返回前端的文件名
func ResponseXls(c *gin.Context, content io.ReadSeeker, fileTag string) {
	fileName := fmt.Sprintf("%s.xlsx", fileTag)
	c.Writer.Header().Add("Content-Disposition", fmt.Sprintf(`attachment; filename="%s"`, fileName))
	c.Writer.Header().Add("Content-Type", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet")
	http.ServeContent(c.Writer, c.Request, fileName, time.Now(), content)
}

func GetNumFromString(str string) []*CircuitStatus {
	b, _ := regexp.MatchString(`^zt([nf][0-9]{1,2}){1,10}$`, str)
	if b == true {
		var nums []*CircuitStatus
		//fmt.Println(b)
		compileRegex := regexp.MustCompile("([nf]{1}[1-9][0-9]*)")
		matchArr := compileRegex.FindAllString(str, -1) // FindStringSubmatch 方法是提取出匹配的字符串，然后通过[]string返回。我们可以看到，第1个匹配到的是这个字符串本身，从第2个开始，才是我们想要的字符串。
		for _, t := range matchArr {
			temp := &CircuitStatus{}
			fmt.Println(t)

			if strings.HasPrefix(t, "n") {
				temp.IsOpen = true
			} else if strings.HasPrefix(t, "f") {
				temp.IsOpen = false
			} else {
				continue
			}

			re := regexp.MustCompile("\\d+")
			tt := re.FindString(t)
			val, err := strconv.Atoi(tt)
			if err != nil {
				continue
			}
			temp.Num = uint8(val)

			nums = append(nums, temp)

		}

		return nums
	}
	return nil

}

// CalculateMoney 计算价格,平尺，长宽
func CalculateMoney(moneySize decimal.Decimal, days, artworkSquareSize int) string {
	return moneySize.
		Mul(decimal.NewFromInt(int64(days))).
		Mul(decimal.NewFromInt(int64(artworkSquareSize))).
		Round(2).
		String()
}

func CalculateMoneySum(a, b string) string {
	if a == "" {
		a = "0"
	}

	if b == "" {
		b = "0"
	}

	aDeci, err := decimal.NewFromString(a)
	if err != nil {
		fmt.Println("2-------", err)
		return ""
	}
	dDeci, err := decimal.NewFromString(b)

	if err != nil {
		fmt.Println("1-------", err)
		return ""
	}

	return aDeci.Add(dDeci).
		Round(2).
		String()
}

// CalculateMoneyDay 计算价格,平尺，长宽
func CalculateMoneyDay(moneySize decimal.Decimal, artworkSquareSize int) string {
	return moneySize.
		Mul(decimal.NewFromInt(int64(artworkSquareSize))).
		Round(2).
		String()
}

//func GetInfoFrom(urlString string) (*vo.WarehouseOcrRes, error) {
//
//	resObj := &vo.WarehouseOcrRes{}
//	// 发送一个HTTP GET请求并获取响应
//	res, err := http.Get(urlString)
//	if err != nil {
//		return nil, err
//	}
//	defer res.Body.Close()
//
//	// 解析HTML
//	doc, err := goquery.NewDocumentFromReader(res.Body)
//	if err != nil {
//		return nil, err
//	}
//
//	// 获取指定内容的元素
//	/*
//		doc.Find("h1").Each(func(i int, s *goquery.Selection) {
//			// 打印元素的文本内容
//			fmt.Println(s.Text())
//		})
//	*/
//
//	doc.Find("#original_img").Each(func(i int, s *goquery.Selection) {
//		src, exists := s.Attr("src")
//		if exists {
//			resObj.ArtworkImg = src
//			fmt.Printf("src: %s\n", src)
//		} else {
//			fmt.Println("src attribute not found")
//		}
//	})
//
//	doc.Find(".certificate-number").Each(func(i int, s *goquery.Selection) {
//		resObj.ArtworkNum = s.Text()
//	})
//
//	doc.Find(".creator").Each(func(i int, s *goquery.Selection) {
//		resObj.ArtistName = s.Text()
//	})
//
//	doc.Find(".artwork-size").Each(func(i int, s *goquery.Selection) {
//		resObj.ArtworkSize = s.Text()
//		resObj.ArtworkSizeL, resObj.ArtworkSizeW, err = RegNumber(resObj.ArtworkSize)
//		resObj.ArtworkSquareSize = resObj.CalculateSquareSize()
//	})
//
//	/*
//		jsonObj, err := RegNumberByScript(doc.Find("script").Last().Text())
//
//		if err != nil && err.Error() != ErrorMsg {
//			return nil, err
//		}
//	*/
//
//	doc.Find(".tyfon_cert_view").Find("div").First().Each(func(i int, s *goquery.Selection) {
//		resObj.ArtworkName = strings.TrimSpace(strings.Replace(s.Text(), "artworkName:", "", -1))
//	})
//
//	/*
//		doc.Find("#original_img").First().Each(func(i int, s *goquery.Selection) {
//			fmt.Println(s.Attr("src"))
//		})
//	*/
//
//	return resObj, nil
//	/*
//		res := vo.WarehouseOcrRes{
//			ArtworkName: "李白华都富测试",
//		}
//
//	*/
//}

func RegNumber(str string) (int, int, error) {
	//str := "181cm*224cm"

	// 定义正则表达式模式
	pattern := `(\d+)cm`

	// 编译正则表达式
	re := regexp.MustCompile(pattern)

	// 查找匹配的数字
	matches := re.FindAllStringSubmatch(str, -1)

	// 提取数字并转换为整数
	numbers := make([]int, len(matches))
	for i, match := range matches {
		number, _ := strconv.Atoi(match[1])
		numbers[i] = number
	}

	// 只获取前两个数字
	if len(numbers) != 2 {
		return 0, 0, errors.New("尺寸解析失败")
	}

	return numbers[0], numbers[1], nil

}

func RegNumberByScript(str string) (*vo.ArtworkRes, error) {

	// 使用正则表达式提取id和type参数的值
	re := regexp.MustCompile(`://artkey.fontree.cn/web/detail.html\?id=([^&]+)&amp;type=([^&]+)'`)
	matches := re.FindStringSubmatch(str)

	if len(matches) != 3 {
		fmt.Println(str)
		return nil, errors.New(ErrorMsg)
	}

	id := matches[1]
	typ := matches[2]

	urlStr := fmt.Sprintf("https://artkey.fontree.cn/mgmt/artkey_cert/query?id=%s&type=%s", id, typ)

	// 发起GET请求
	resp, err := http.Get(urlStr)
	if err != nil {
		//log.Fatal(err)
		return nil, err
	}
	defer resp.Body.Close()

	// 读取响应体
	body, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		return nil, err
	}

	// 解析JSON数据到Response结构体
	var response *vo.ArtworkRes
	err = json.Unmarshal(body, &response)
	if err != nil {
		return nil, err
	}

	// 打印解析后的数据

	fmt.Println(response)

	return response, nil
}

func UrlChangeHost(originalURL, host string) (string, error) {
	parsedURL, err := url.Parse(originalURL)
	if err != nil {
		fmt.Println("URL 解析失败:", err)
		return "", err
	}

	// 替换host
	parsedURL.Host = host

	// 重新生成URL字符串
	newURL := parsedURL.String()
	return newURL, nil
}

func UrlChangeHost2(originalURL, host string) (string, error) {
	parsedURL, err := url.Parse(originalURL)
	if err != nil {
		fmt.Println("URL 解析失败:", err)
		return "", err
	}

	// 替换host
	parsedURL.Host = host

	// 重新生成URL字符串
	newURL := parsedURL.String()
	return newURL, nil
}

func GetMd5(str string) string {

	hash := md5.Sum([]byte(str))
	md5String := hex.EncodeToString(hash[:])

	fmt.Println("MD5 Hash (16位)：", md5String)
	return md5String
}

func GetBase64FromUrl(imageURL string) (string, error) {
	u, err := url.Parse(imageURL)
	if err != nil {
		fmt.Println("解析URL出错:", err)
		return "", err
	}

	// 删除URL中的压缩参数
	q := u.Query()
	delete(q, "x-oss-process")
	u.RawQuery = q.Encode()
	downloadURL := u.String()

	res, err := http.Get(downloadURL)

	if err != nil {
		fmt.Println("Error downloading the image")
		return "", err
	}
	defer res.Body.Close()

	if res.StatusCode != 200 {
		fmt.Println("Error downloading the image")
		return "", nil
	}

	// Decode the image
	img, _, err := image.Decode(res.Body)

	if err != nil {
		fmt.Println("Error decoding the image", err.Error())
		return "", err
	}

	// Compress the image to required size
	m := resize.Resize(300, 0, img, resize.Lanczos3)
	//m := resize.Thumbnail(300, 300, img, resize.Lanczos3)

	out := new(bytes.Buffer)
	var opt jpeg.Options
	opt.Quality = 80

	// Write new image to buffer
	err = jpeg.Encode(out, m, &opt)

	if err != nil {
		fmt.Println("Error encoding the image")
		return "", err
	}

	// Convert to base64
	imgBase64 := base64.StdEncoding.EncodeToString(out.Bytes())

	return imgBase64, nil
}

func SHA256V(str []byte) string {
	h := sha256.New()
	h.Write(str)
	return hex.EncodeToString(h.Sum(nil))
}

const charset = "0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ"

func GenerateRandomString(length int) string {
	// 初始化随机种子（仅需一次）
	rand.Seed(time.Now().UnixNano())

	b := make([]byte, length)
	for i := range b {
		b[i] = charset[rand.Intn(len(charset))]
	}
	return string(b)
}
