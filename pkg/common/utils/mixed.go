// Package utils -----------------------------
// @file      : mixed.go
// <AUTHOR> JJXu
// @contact   : <EMAIL>
// @time      : 2025/4/12 11:10
// -------------------------------------------
package utils

type DefineBaseVale interface {
	~int | ~int32 | ~int16 | ~int64 | ~uint | ~string
}

// 取差集
// if base = []{1,2} ,compare = {1,3} ,then result is {2}
func Difference[T DefineBaseVale](base []T, compare ...T) (different []T) {
	if compare == nil {
		return base
	}
	different = []T{}
	for _, c := range base {
		var same bool
		for _, v := range compare {
			if c == v {
				same = true
				break // 找到相同元素后可以提前退出内层循环
			}
		}
		if !same {
			different = append(different, c) // 添加未在compare中找到的元素
		}
	}
	return
}
func Unique[T DefineBaseVale](base []T) (result []T) {
	for _, c := range base {
		var find bool
		for _, v := range result {
			if c == v {
				find = true
				break
			}
		}
		if !find {
			c := c
			result = append(result, c)
		}
	}
	return
}
