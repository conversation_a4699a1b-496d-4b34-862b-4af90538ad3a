package utils

import (
	"bytes"
	"fmt"
	"go.uber.org/zap"
	"image"
	"image/jpeg"
	"os"
	"strings"

	"github.com/nfnt/resize"
)

const (
	CompressLimit = 1024 * 1024 * 1
)

// CompressJPG 压缩图片，并返回缩略图路径
func CompressJPG(dst string, newDst string) (err error) {
	filebytes, err := os.ReadFile(dst)
	if err != nil {
		return err
	}
	compressbytes := compressImageResource(filebytes)
	fo, err := os.Create(newDst)
	defer fo.Close()
	if err != nil {
		return err
	}
	_, err = fo.Write(compressbytes)
	if err != nil {
		return err
	}
	return nil
}

// func compressImageResource(data []byte) []byte {
// 	img, _, err := image.Decode(bytes.NewReader(data))
// 	if err != nil {
// 		return data
// 	}
// 	buf := bytes.Buffer{}
// 	err = jpeg.Encode(&buf, img, &jpeg.Options{Quality: 1})
// 	if err != nil {
// 		return data
// 	}
// 	if buf.Len() > len(data) {
// 		return data
// 	}
// 	return buf.Bytes()
// }
const MaxHeight uint = 600

func compressImageResource(data []byte) []byte {
	if len(data) < CompressLimit {
		return data
	}
	img, _, err := image.Decode(bytes.NewReader(data))
	if err != nil {
		return data
	}
	m := resize.Resize(0, MaxHeight, img, resize.Lanczos3)
	buf := bytes.Buffer{}

	err = jpeg.Encode(&buf, m, &jpeg.Options{Quality: 100})
	if err != nil {
		zap.L().Error("compressImageResource Encode err", zap.Error(err))
		return data
	}
	if buf.Len() > len(data) {
		return data
	}
	fmt.Println(buf.Len())
	if buf.Len() >= CompressLimit {
		bytes := compressImageResource(buf.Bytes())
		return bytes
	}
	return buf.Bytes()
}

// 存储图片至指定路径
func StorePhotoToPath(oldPath, newPath string) error {
	// 先创建目录
	var dir string
	var err error
	path := strings.Split(newPath, "/")
	// path2 := strings.Split(oldPath, "/")
	for i := 0; i < len(path)-1; i++ {
		dir += path[i] + "/"
		if _, err = os.Stat(dir + "/"); err != nil {
			if !os.IsExist(err) {
				os.MkdirAll(dir+"/", os.ModePerm)
			}
		}
	}
	err = os.Rename(oldPath, newPath)
	return err
}
