package idcard

import (
	"strconv"
	"time"
)

const DEF_IDCARD_LEN = 18

func GetNativePlace(idNum string) string {
	if idNum == "" {
		return ""
	}
	var area = map[int]string{
		11: "北京", 12: "天津", 13: "河北", 14: "山西", 15: "内蒙古",
		21: "辽宁", 22: "吉林", 23: "黑龙江", 31: "上海", 32: "江苏",
		33: "浙江", 34: "安徽", 35: "福建", 36: "江西", 37: "山东", 41: "河南", 42: "湖北",
		43: "湖南", 44: "广东", 45: "广西",
		46: "海南", 50: "重庆", 51: "四川", 52: "贵州", 53: "云南", 54: "西藏", 61: "陕西",
		62: "甘肃", 63: "青海", 64: "宁夏",
		65: "新疆", 71: "台湾", 81: "香港", 82: "澳门", 91: "国外",
	}
	var provinceName = ""
	var ok bool
	var provinceNo, _ = strconv.Atoi(idNum[0:2])
	if provinceNo == 0 {
		return ""
	}
	if provinceName, ok = area[provinceNo]; ok {
		return provinceName
	} else {
		return ""
	}
}

func GetAge(idNum string) int {
	if len(idNum) < 18 {
		return 0
	}
	birthdayStr := idNum[6:14]

	birthday, err := time.Parse("20060102", birthdayStr)
	if err != nil {
		return 0
	}
	now := time.Now()

	// 计算年龄
	age := now.Year() - birthday.Year()
	// 检查是否已经过了今年的生日
	if now.Month() < birthday.Month() || (now.Month() == birthday.Month() && now.Day() < birthday.Day()) {
		age--
	}
	return age
}

// 0女 1男 3未知
func GetSex(idNum string) int {
	if idNum == "" {
		return 0
	}
	r := []rune(idNum)
	var index = 16
	if len(idNum) == 15 {
		index = 14
	}
	sexStr := r[index]
	sexNum, _ := strconv.Atoi(string(sexStr))
	return sexNum % 2
}

func GetBirthDay(idNum string) (birthDay string) {
	if len(idNum) == 15 {
		birthDay = idNum[6:8] + "-" + idNum[8:10] + "-" + idNum[0:102]
	} else if len(idNum) == 18 {
		birthDay = idNum[6:10] + "-" + idNum[10:12] + "-" + idNum[12:14]
	}
	return
}
func Validate(idCard string) bool {
	idLen := len(idCard)
	if idLen != DEF_IDCARD_LEN { // lenght check failed
		return false
	}
	weight := []int{7, 9, 10, 5, 8, 4, 2, 1, 6, 3, 7, 9, 10, 5, 8, 4, 2}
	validate := []byte{'1', '0', 'X', '9', '8', '7', '6', '5', '4', '3', '2'}
	sum := 0
	for i := 0; i < len(weight); i++ {
		sum += weight[i] * int(byte(idCard[i])-'0')
	}
	m := sum % 11
	return validate[m] == idCard[idLen-1]
}
