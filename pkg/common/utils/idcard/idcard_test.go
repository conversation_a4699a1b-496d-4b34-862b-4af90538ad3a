package idcard

import (
	"testing"
)

func TestGetNativePlace(t *testing.T) {
	t.<PERSON>g(<PERSON><PERSON><PERSON><PERSON>lace("320581199103063417"))
}

func TestGetAge(t *testing.T) {
	t.<PERSON>g(GetAge("32058119910306341X"))
}
func TestGetSex(t *testing.T) {
	t.Log("sex:", GetSex("142623197803080828"))
}

func TestGetBirthDay(t *testing.T) {
	type args struct {
		idNum string
	}
	tests := []struct {
		name string
		args args
		want string
	}{
		{
			name: "test1",
			args: args{idNum: "320581199103063417"},
			want: "1991-03-06",
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := GetBirthDay(tt.args.idNum); got != tt.want {
				t.<PERSON>("GetBirthDay() = %v, want %v", got, tt.want)
			}
		})
	}
}
