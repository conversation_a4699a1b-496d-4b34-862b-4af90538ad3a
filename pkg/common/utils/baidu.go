package utils

import (
	"bytes"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"time"
)

const (
	baiduRealNameAk = "AlbWwhnZINIy1YqWP3agQA42"
	baiduRealNameSk = "NL2nT4rnGHIcpopBI93a7G85WscluOEH"
)

// 二要素实名认证
func AuthenticationTwo(req AuthService) error {
	urls := "https://aip.baidubce.com/rest/2.0/face/v3/person/idmatch" //二要素
	//AK, SK := "Tru89O1zosjSCIN2uUlEctbE", "rLgGU2gwNvEQAo0UooTwArtvqBTaL3Y3"
	accessToken, err := GetAccessToken(baiduRealNameAk, baiduRealNameSk)
	if err != nil {
		return err
	}
	urls = urls + "?access_token=" + accessToken.Access_token
	//fmt.Println("idnum", req.IDNum)
	//var a = url.Values{"id_card_number": {req.IDNum}, "name": {req.Name}, "verify_type": {req.VerifyType}, "nation": {"CHN"}}
	//result, err := PostForm(urls, a)
	//if err != nil {
	//	return err
	//}

	//reqs := simpleRequest.NewRequest()
	//reqs.Headers().ConentType_json()
	//reqs.Body().Sets(map[string]any{
	//	"id_card_number": req.IDNum,
	//	"name":           req.Name,
	//	"verify_type":    4,
	//	"nation":         "CHN",
	//})
	//result, _ := reqs.POST(urls)
	body, _ := json.Marshal(&req)
	result, _ := Post(urls, string(body))
	var results RealnameRes
	if err = json.Unmarshal([]byte(result), &results); err != nil {
		return err
	}
	if results.Error_code != 0 {
		switch results.Error_code {
		case 222023:
			return errors.New("请检查手机号、姓名、身份证号格式是否正确")
		case 222022:
			return errors.New("请填写正确的身份证号")
		default:
			return errors.New(results.Error_msg)
		}
	}

	if results.Result.Verify_state == 1 {
		return errors.New("实名认证失败")
	}
	if results.Result.Verify_state == 2 {
		return errors.New("未查询到此手机号")
	}
	return nil
}

// 三要素实名认证
func AuthenticationThree(req AuthService) error {
	urls := "https://aip.baidubce.com/rest/2.0/face/v1/mobile/verify" //指明使用HTTPS协议,三要素
	//AK, SK := "Tru89O1zosjSCIN2uUlEctbE", "rLgGU2gwNvEQAo0UooTwArtvqBTaL3Y3"
	accessToken, err := GetAccessToken(baiduRealNameAk, baiduRealNameSk)
	if err != nil {
		return err
	}
	urls = urls + "?access_token=" + accessToken.Access_token
	fmt.Println("idnum", req.IDNum)
	var a = url.Values{"id_card_number": {req.IDNum}, "name": {req.Name}, "mobile": {req.TelNum}}
	result, err := PostForm(urls, a)
	if err != nil {
		return err
	}
	var results RealnameRes
	if err = json.Unmarshal([]byte(result), &results); err != nil {
		return err
	}
	fmt.Println(result)
	if results.Error_code != 0 {
		switch results.Error_code {
		case 222023:
			return errors.New("请检查手机号、姓名、身份证号格式是否正确")
		case 222022:
			return errors.New("请填写正确的身份证号")
		default:
			return errors.New(results.Error_msg)
		}
	}
	if results.Result.Verify_state == 1 {
		return errors.New("实名认证失败")
	}
	if results.Result.Verify_state == 2 {
		return errors.New("未查询到此手机号")
	}
	return nil
}

func GetAccessToken(client_id, client_secret string) (AccessToken, error) {
	var (
		resObj AccessToken
		err    error
	)
	url := "https://aip.baidubce.com/oauth/2.0/token"
	urlReq := "?grant_type=client_credentials&client_id=" + client_id + "&client_secret=" + client_secret
	res := get(url + urlReq)
	if err = json.Unmarshal([]byte(res), &resObj); err != nil {
		return resObj, err
	}
	if resObj.Error != "" {
		return resObj, errors.New(resObj.Error_description)
	}
	return resObj, err
}

type AccessToken struct {
	Refresh_token     string `json:"refresh_token"`
	Expires_in        uint64 `json:"expires_in"`
	Scope             string `json:"scope"`
	Session_key       string `json:"session_key"`
	Access_token      string `json:"access_token"`
	Session_secret    string `json:"session_secret"`
	Error             string `json:"error"`
	Error_description string `json:"error_description"`
}

func get(url string) string {

	// 超时时间：5秒
	client := &http.Client{Timeout: 5 * time.Second}
	resp, err := client.Get(url)
	if err != nil {
		panic(err)
	}
	defer resp.Body.Close()
	var buffer [512]byte
	result := bytes.NewBuffer(nil)
	for {
		n, err := resp.Body.Read(buffer[0:])
		result.Write(buffer[0:n])
		if err != nil && err == io.EOF {
			break
		} else if err != nil {
			panic(err)
		}
	}

	return result.String()
}

type RealnameRes struct {
	Result RealnameResult `json:"result"`
	Log_id interface{}    `json:"log_id"` //三要素
	// Log_id     int64  `json:"log_id"` //二要素
	Error_code int    `json:"error_code"`
	Error_msg  string `json:"error_msg"`
}

type RealnameResult struct {
	Verify_state int `json:"verify_state"`
}

// 发送post请求
func PostForm(urlStr string, data url.Values) (string, error) {
	resp, err := http.PostForm(urlStr, data)

	if err != nil {
		// handle error
	}

	defer resp.Body.Close()
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		// handle error
	}

	return string(body), err

}

type AuthService struct {
	Name       string `form:"name" json:"name,omitempty" binding:"required"`
	IDNum      string `form:"id_card_number" json:"id_card_number,omitempty" binding:"required"`
	TelNum     string `form:"tel_num" json:"telNum,omitempty"  binding:"required"`
	VerifyType int    `form:"verify_type" json:"verify_type,omitempty"  binding:"required"` //0 大陆身份证(默认);  1 港澳台居民来往内地通行证;  2 外国人永久居留身份证。  3:定居国外的中国公民护照  4 港澳台居住证
	Nation     string `json:"nation,omitempty"`
}

func Post(url, data string) (string, error) {
	reader := bytes.NewReader([]byte(data))

	request, err := http.NewRequest("POST", url, reader)
	if err != nil {
		return "", err
	}
	defer request.Body.Close() //程序在使用完回复后必须关闭回复的主体
	request.Header.Set("Content-Type", "application/json;charset=UTF-8")
	//必须设定该参数,POST参数才能正常提交，意思是以json串提交数据

	client := http.Client{}
	resp, err := client.Do(request) //Do 方法发送请求，返回 HTTP 回复
	if err != nil {
		return "", err
	}

	respBytes, err := io.ReadAll(resp.Body)
	if err != nil {
		return "", err
	}

	//byte数组直接转成string，优化内存
	// str := (*string)(unsafe.Pointer(&respBytes))
	return string(respBytes), nil
}
