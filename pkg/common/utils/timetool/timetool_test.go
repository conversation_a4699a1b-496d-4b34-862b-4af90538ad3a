package timetool

import (
	"fmt"
	"regexp"
	"strconv"
	"testing"
	"time"
)

func TestTime(t *testing.T) {

	ss("n1")
	ss("f1n1")
	ss("ff")
	ss("f1")
	ss("n")
	ss("heathy")

	fmt.Println(GetYmdFromString("2022-01-01 00:00:00"))
}

func ss(s string) {
	fmt.Println(s)
	b, _ := regexp.MatchString(`^[nf][0-9]{1,2}$`, s)
	if b == true {
		fmt.Printf("查找结果%s：%s \n", s, "通知开关板")
	}
}

func TestTime2(t *testing.T) {
	b, _ := regexp.MatchString(`^zt([nf][0-9]{1,2}){1,10}$`, "ztf1f2f3f4f5f6f7f8")
	if b == true {
		//fmt.Println(b)
		compileRegex := regexp.MustCompile("([1-9][0-9]*)")
		matchArr := compileRegex.FindAllString("ztf1f2f3f4f5f6f7f8", -1) // FindStringSubmatch 方法是提取出匹配的字符串，然后通过[]string返回。我们可以看到，第1个匹配到的是这个字符串本身，从第2个开始，才是我们想要的字符串。
		fmt.Println(matchArr)
		for _, t := range matchArr {
			i, err := strconv.ParseUint(string(t), 16, 8)
			fmt.Println("---", i)
			if err != nil {
				panic(err)
			}
			fmt.Println(t)
		}

	}

}

func TestGetFutureDaysFromNow(t *testing.T) {
	type args struct {
		end time.Time
	}
	tests := []struct {
		name string
		args args
		want int
	}{
		{args: args{end: time.Now().AddDate(0, 0, 10)}, want: 10},
		{args: args{end: time.Now().AddDate(0, 0, -10)}, want: -10},
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := GetFutureDaysFromNow(tt.args.end); got != tt.want {
				t.Errorf("GetFutureDaysFromNow() = %v, want %v", got, tt.want)
			}
		})
	}
}
