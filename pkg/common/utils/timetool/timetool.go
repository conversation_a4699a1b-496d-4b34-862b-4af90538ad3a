package timetool

import (
	"math"
	"time"
)

func GetYmdFromString(date string) string {
	t, err := time.ParseInLocation("2006-01-02 15:04:05", date, time.Local)
	if err != nil {
		return date
	}
	return t.Format("2006-01-02")
}

//GetSubDaysBetweenDay 相差日期正整数
func GetSubDaysBetweenDay(start, end time.Time) int {
	duration := end.Sub(start)
	days := int(math.Abs(duration.Hours() / 24))
	return days
}

//GetFutureDaysFromNow 计算未来到今天的相差日期
func GetFutureDaysFromNow(end time.Time) int {
	now := time.Now()
	start := time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 0, now.Location())

	realEnd := time.Date(end.Year(), end.Month(), end.Day(), 0, 0, 0, 0, end.Location())

	duration := realEnd.Sub(start)
	days := int(duration.Hours() / 24)

	return days
}
