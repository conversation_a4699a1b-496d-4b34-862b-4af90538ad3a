package cache

import (
	"fmt"
	"testing"
	"time"
)

func TestLockConcurrency(t *testing.T) {

	redisConfig := RedisConfig{
		RedisDB:     "1",
		RedisAddr:   "172.16.100.114:6379",
		RedisPw:     "kP6tW4tS3qB2dW4aE6uI5cX2",
		RedisDbName: "1",
		PoolSize:    10,
	}

	LoadRedis(redisConfig)

	type args struct {
		key   string
		value string
		ms    time.Duration
	}
	tests := []struct {
		name    string
		args    args
		want    bool
		wantErr bool
	}{
		// TODO: Add test cases.
		{args: args{key: "ttt", value: "111", ms: 5 * time.Second}, want: true, wantErr: false},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := LockConcurrency(tt.args.key, tt.args.value, tt.args.ms)
			got, err = LockConcurrency(tt.args.key, tt.args.value, tt.args.ms)
			got, err = LockConcurrency(tt.args.key, tt.args.value, tt.args.ms)
			got, err = LockConcurrency(tt.args.key, tt.args.value, tt.args.ms)
			fmt.Println(got, err)
			DeleteLock(tt.args.key, "22")
			fmt.Println()
			fmt.Println()
			fmt.Println()
			fmt.Println()
			fmt.Println()

			if (err != nil) != tt.wantErr {
				t.Errorf("LockConcurrency() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if got != tt.want {
				t.Errorf("LockConcurrency() got = %v, want %v", got, tt.want)
			}
		})
	}
}
