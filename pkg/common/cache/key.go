package cache

import (
	"fmt"
	"time"
)

func GetCreateReqKey(batchId string) string {
	return fmt.Sprintf("create:req:%s", batchId)
}

func GetBitterReqKey(userId uint32) string {
	return fmt.Sprintf("create:inventory:req:%d", userId)
}

func UserOpenidByUserId(userid int) string {
	return fmt.Sprintf("user:openid:%d", userid)
}

/**信号开始**/
// GetSquareChannel Buy 实施列表用
func GetSquareChannel(auctionUuid string) string {
	return fmt.Sprintf("channel:server:square:%s", auctionUuid)
}

func GetScreenChannel(auctionUuid string) string {
	return fmt.Sprintf("channel:server:square:%s", auctionUuid)
}

func GetCirQueue() string {
	return fmt.Sprintf("list:cirlaction")
}

func GetArtworkChannel(auctionArtworkUuid string) string {
	return fmt.Sprintf("channel:client:artwork:%s", auctionArtworkUuid)
}

// GetFansChannel 用户频道
func GetFansChannel(auctionUuid string) string {
	return fmt.Sprintf("channel:fans:live:%s", auctionUuid)
}

/**信号开始结束**/

// GetPriceNowKey 临时存取价格
func GetPriceNowKey(currency, price string) string {
	return fmt.Sprintf("channel:price:%s:%s", currency, price)
}

func GetTotalRate() string {
	return fmt.Sprintf("rate:total")
}

func GetCurrency(from, to string) string {
	return fmt.Sprintf("rate:currency:%s-%s:%s", from, to, time.Now().Format("2006-01-02"))
}

func GetTotalRateDaily() string {
	return fmt.Sprintf("rate:total:daily:%s", time.Now().Format("2006-01-02"))
}

func GetTotalRateDailyNum() string {
	return fmt.Sprintf("rate:total:num:%s", time.Now().Format("2006-01-02"))
}

func GetTotalRateHash(currency string, userId uint32) string {
	return fmt.Sprintf("rate:hash:%d:%s:%s", userId, currency, time.Now().Format("2006-01-02"))
}

func GetLandMsg(lang string) string {
	return fmt.Sprintf("lang:msg:%s", lang)
}

// GetFddToUrl 用户频道
func GetFddToUrl(key string) string {
	return fmt.Sprintf("auction:fdd:su:to:%s", key)
}

// GetFddSynUrl 用户频道
func GetFddSynUrl(key string) string {
	return fmt.Sprintf("auction:fdd:%s", key)
}

// GetNowDayNum 获取当前天的人次
func GetNowDayNum(auctionUuid string) string {
	return fmt.Sprintf("day:data:num:%s:%s", auctionUuid, time.Now().Format("2006-01-02"))
}

// GetNowDayUser 当前人数
func GetNowDayUser(auctionUuid string) string {
	return fmt.Sprintf("day:data:user:%s:%s", auctionUuid, time.Now().Format("2006-01-02"))
}

// GetSampleBiggestNum 获取最高人数
func GetSampleBiggestNum(auctionUuid string) string {
	return fmt.Sprintf("day:data:biggest:date:num:%s:%s", auctionUuid, time.Now().Format("2006-01-02"))
}

// GetLiveTotalNum 实时人数
func GetLiveTotalNum(auctionUuid string) string {
	return fmt.Sprintf("day:data:biggest:live:num:%s:%s", auctionUuid, time.Now().Format("2006-01-02"))
}

func RateLimitKey(finger, path string) string {
	return fmt.Sprintf("rate:limit:%s:%s:%d", path, finger, time.Now().Unix())
}

// GetLimitSampleArtworkBuy 限制同一个藏品和价格的出价
func GetLimitSampleArtworkBuy(artworkUuid, price string) string {
	return fmt.Sprintf("buy:artwork:%s:price:%s", artworkUuid, price)
}

func GetPhotoWallChannel() string {
	return fmt.Sprintf("photo:wall:send")
}
