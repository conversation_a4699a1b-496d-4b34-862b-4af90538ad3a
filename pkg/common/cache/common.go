package cache

import (
	"encoding/json"
	"errors"
	"fmt"
	"github.com/go-redis/redis"
	"strconv"
	"time"

	"dubbo.apache.org/dubbo-go/v3/common/logger"
)

// RedisClient Redis缓存客户端单例
var (
	RedisClient *redis.Client
)

type RedisConfig struct {
	RedisDB     string
	RedisAddr   string
	RedisPw     string
	RedisDbName string
	PoolSize    int
}

var deleteScript = redis.NewScript(`	
	if redis.call("get",KEYS[1]) == ARGV[1] then
    	return redis.call("del",KEYS[1])
	else
    	return 0
	end
`)

// LoadRedis 在中间件中初始化redis链接
func LoadRedis(configEnv RedisConfig) {
	db, _ := strconv.ParseUint(configEnv.RedisDbName, 10, 64)
	client := redis.NewClient(&redis.Options{
		Addr:     configEnv.RedisAddr,
		Password: configEnv.RedisPw,
		PoolSize: configEnv.PoolSize,
		DB:       int(db),
	})
	_, err := client.Ping().Result()
	if err != nil {
		logger.Info(err)
		panic(err)
	}
	RedisClient = client
}

// LockConcurrency 锁死并发
func LockConcurrency(key string, value string, ms time.Duration) (bool, error) {
	fmt.Println(ms)
	setNx := RedisClient.SetNX(key, value, ms)

	result, err := setNx.Result()
	fmt.Println(result, err)

	if err != nil {
		fmt.Println(fmt.Sprintf("锁死加解密的时候，redis 失效,err%s,%v", key, err))
		//panic(err)
		return false, err
	}

	if result {
		fmt.Printf("新增key存在，设置成功%s \n", key)
	} else {
		fmt.Println(fmt.Sprintf("新增key已存在，设置失败%s", key))
		return false, errors.New("温馨提示：点击过快或者误点击了")
	}

	return true, nil

}

func DeleteLock(key string, value string) {
	s := deleteScript.Run(RedisClient, []string{key}, value).Val()
	fmt.Println(s)
}

// IsInList 是否存在其中
func IsInList(key, kk string) bool {

	//key := "frontree:not:send:msg"
	str := RedisClient.Get(key) // 使用新部门的岗位
	fmt.Println("1-----", str)
	var ids []string

	isExist, err := RedisClient.Exists(key).Result()

	if isExist != 1 { //不存在
		return false
	}

	val, err := RedisClient.Get(key).Result()
	if err != nil {
		return false
	}

	err = json.Unmarshal([]byte(val), &ids)
	if err != nil {
		fmt.Println("error decoding JSON: %s", err)
		return false
	}

	for _, v := range ids {
		if v == kk { //老的
			return true
		}
	}
	return false
}
