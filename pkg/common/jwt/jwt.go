package jwt

import (
	"github.com/fonchain_enterprise/client-auction/api/account"
	"github.com/fonchain_enterprise/client-auction/pkg/e"
	"math/rand"
	"time"

	"github.com/dgrijalva/jwt-go"
)

type Claims struct {
	ID       uint64
	OpenId   string
	NickName string
	TelNum   string
	Avatar   string
	IDNum    string
	jwt.StandardClaims
}

type RefreshClaims struct {
	ID  uint `json:"id"`
	RId int  `json:"rid"`
	jwt.StandardClaims
}

func GenerateTokenForUser(userInfo *account.UserInfo, jwtSecret []byte) (string, error) {
	nowTime := time.Now()
	expireTime := nowTime.Add(time.Duration(e.JwtExpireHours) * time.Hour)
	claims := Claims{
		ID:       userInfo.ID,
		TelNum:   userInfo.TelNum,
		NickName: userInfo.NickName,
		Avatar:   userInfo.Avatar,
		IDNum:    userInfo.IDNum,
		StandardClaims: jwt.StandardClaims{
			ExpiresAt: expireTime.Unix(),
			IssuedAt:  nowTime.Unix(),
			NotBefore: nowTime.Unix(),
			Issuer:    "mall",
		},
	}

	tokenClaims := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
	token, err := tokenClaims.SignedString(jwtSecret)
	return token, err
}

// ParseTokenForUser 解析用户token
func ParseTokenForUser(tokenString string, jwtSecret []byte) (*Claims, error) {
	token, err := jwt.ParseWithClaims(tokenString, &Claims{}, func(token *jwt.Token) (interface{}, error) {
		return jwtSecret, nil
	})

	if err != nil {
		return nil, err
	}

	if claims, ok := token.Claims.(*Claims); ok && token.Valid {
		return claims, nil
	}

	return nil, err
}

// GenerateToken 签发用户Token
func GenerateToken(info *account.WxBoxUserInfo, jwtSecret []byte) (string, error) {
	nowTime := time.Now()
	expireTime := nowTime.Add(time.Duration(24) * time.Hour)
	claims := Claims{
		OpenId: info.OpenId,
		StandardClaims: jwt.StandardClaims{
			ExpiresAt: expireTime.Unix(),
			Issuer:    "mall",
		},
	}

	if info.User != nil {
		claims.NickName = info.User.RealName
		claims.TelNum = info.User.TelNum
		claims.Avatar = info.User.Avatar
		claims.IDNum = info.User.IDNum
	}

	tokenClaims := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
	token, err := tokenClaims.SignedString(jwtSecret)
	return token, err
}

// ParseToken 验证用户token
func ParseToken(token string, jwtSecret []byte) (*Claims, error) {
	tokenClaims, err := jwt.ParseWithClaims(token, &Claims{}, func(token *jwt.Token) (interface{}, error) {
		return jwtSecret, nil
	})
	if tokenClaims != nil {
		if claims, ok := tokenClaims.Claims.(*Claims); ok && tokenClaims.Valid {
			return claims, nil
		}
	}
	return nil, err
}

// GenerateRefreshToken 签发用户Token
func GenerateRefreshToken(id uint, domain string, hour int, jwtSecret []byte) (string, error) {
	nowTime := time.Now()
	expireTime := nowTime.Add(time.Duration(hour) * time.Hour)
	claims := RefreshClaims{
		ID:  id,
		RId: rand.Intn(999),
		StandardClaims: jwt.StandardClaims{
			ExpiresAt: expireTime.Unix(),
			Issuer:    domain,
		},
	}
	tokenClaims := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
	token, err := tokenClaims.SignedString(jwtSecret)
	return token, err
}

// ParseRefreshToken 验证用户token
func ParseRefreshToken(token string, jwtSecret []byte) (*RefreshClaims, error) {
	tokenClaims, err := jwt.ParseWithClaims(token, &RefreshClaims{}, func(token *jwt.Token) (interface{}, error) {
		return jwtSecret, nil
	})
	if tokenClaims != nil {
		if claims, ok := tokenClaims.Claims.(*RefreshClaims); ok && tokenClaims.Valid {
			return claims, nil
		}
	}
	return nil, err
}
