package jwt

import (
	"fmt"
	"github.com/fonchain_enterprise/client-auction/pkg/common/m"
	"testing"
)

func TestParseToken(t *testing.T) {

	str := "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***************************************************************************************************************************************************************************************************************.8zG3OA9uRJnzxYorQ2_cK6FhQuMlsi7WF8zkIic1ggM"

	claims, err := ParseToken(str, m.JWTSecret)
	fmt.Println(err)
	fmt.Println(claims)
}

func TestRefreshToken(t *testing.T) {
	str1, err := GenerateRefreshToken(70, "fontree", 20, m.JWTSecret)
	fmt.Println(str1, err)

	claims, err := ParseRefreshToken(str1, m.JWTSecret)
	fmt.Println(err)
	fmt.Println(claims)

}
