package middleware

import (
	"bytes"
	"fmt"
	"github.com/fonchain_enterprise/client-auction/pkg/e"
	"github.com/fonchain_enterprise/utils/zap_log"
	"github.com/gin-gonic/gin"
	"io/ioutil"
)

// RecordLog 核销员登陆校验 isAuthCheck检测权限
func RecordLog() gin.HandlerFunc {

	return func(ctx *gin.Context) {
		authorization := ctx.GetHeader(e.Authorization)
		body, _ := ioutil.ReadAll(ctx.Request.Body)
		ctx.Request.Body = ioutil.NopCloser(bytes.NewBuffer(body))

		writer := &responseCapture{ResponseWriter: ctx.Writer, Body: &bytes.Buffer{}}
		ctx.Writer = writer

		ctx.Next() // 继续处理请求

		// 在请求处理完成后，捕获输出的返回数据
		responseData := writer.Body.Bytes()

		// 可以在这里对responseData进行处理，比如记录日志、监控等
		zap_log.Warn("request:" + string(body) + "  response:" + string(responseData) + "---jwt" + fmt.Sprintf("%+v", authorization))

		// 将捕获的数据重新写回到原始的ResponseWriter中
		//ctx.Writer.Write(responseData)
	}
}

// 自定义的ResponseWriter，用于捕捉输出数据
type responseCapture struct {
	gin.ResponseWriter
	Body *bytes.Buffer
}

func (w *responseCapture) Write(data []byte) (int, error) {
	// 将输出数据同时写入Body中捕捉
	w.Body.Write(data)
	// 写入原始的ResponseWriter中，保证数据正常返回给客户端
	return w.ResponseWriter.Write(data)
}
