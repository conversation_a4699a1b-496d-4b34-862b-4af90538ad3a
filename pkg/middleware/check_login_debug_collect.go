// Package middleware -----------------------------
// @file      : check_login_debug_collect.go
// <AUTHOR> JJXu
// @contact   : <EMAIL>
// @time      : 2025/2/6 09:36
// -------------------------------------------
package middleware

import (
	"fmt"
)

// check login 方式调试日志收集
type checkLoginDebugCollet struct {
	hide    bool
	logData []logData
}
type logData struct {
	msg   any
	level string
}

// AddLog
// msg: string或error类型
// logLevel: error,info等
func (c *checkLoginDebugCollet) AddLog(msg any, logLevel ...string) {
	data := logData{
		msg: msg,
	}
	if logLevel != nil {
		data.level = logLevel[0]
	}
	c.logData = append(c.logData, data)
}
func (c *checkLoginDebugCollet) Println() {
	if c.hide {
		return
	}
	if len(c.logData) == 0 {
		return
	}
	for _, v := range c.logData {
		switch v.level {
		case "":
			fmt.Printf("%+v\n", v.msg)
		case "error":
			fmt.Println("Error:", v.msg)
		case "info":
			fmt.Println("Info:", v.msg)
		case "debug":
			fmt.Println("Debug:", v.msg)
		}
	}
}
