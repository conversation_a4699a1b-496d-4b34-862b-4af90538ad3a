package middleware

import (
	"time"

	"github.com/fonchain/utils/jwt"
	"github.com/fonchain_enterprise/client-auction/pkg/e"

	"github.com/gin-gonic/gin"
)

// JWT token验证中间件
func JWT() gin.HandlerFunc {
	return func(c *gin.Context) {
		var code int
		var data interface{}
		code = 200
		token := c.<PERSON>("Authorization")
		if token == "" {
			code = 404
		} else {
			claims, err := jwt.ParseToken(token, e.JWTSecret)
			if err != nil {
				code = e.ErrorAuthCheckTokenFail
			} else if time.Now().Unix() > claims.ExpiresAt {
				code = e.ErrorAuthCheckTokenTimeout
			}
		}
		if code != e.SUCCESS {
			c.JSON(200, gin.H{
				"code":   code,
				"status": e.InvalidToken,
				"msg":    e.GetMsg(e.InvalidToken),
				"data":   data,
			})
			c.Abort()
			return
		}
		c.Next()
	}
}
