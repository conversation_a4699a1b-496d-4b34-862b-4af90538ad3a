package middleware

import (
	"dubbo.apache.org/dubbo-go/v3/common/logger"
	"encoding/json"
	"fmt"
	api "github.com/fonchain_enterprise/client-auction/api/account"
	"github.com/fonchain_enterprise/client-auction/api/rule"
	"github.com/fonchain_enterprise/client-auction/pkg/model/login"
	"github.com/fonchain_enterprise/client-auction/pkg/serializer"
	"github.com/fonchain_enterprise/client-auction/pkg/service"
	"github.com/fonchain_enterprise/client-auction/pkg/utils/secret"
	"github.com/gin-gonic/gin"
	"github.com/gorilla/websocket"
	"net/http"
	"time"
)

func CheckLoginWs(provider *api.AccountClientImpl) gin.HandlerFunc {

	return func(ctx *gin.Context) {

		fmt.Println()
		fmt.Println()
		fmt.Println()
		startTime := time.Now()
		realStartTime := time.Now()

		//如果没有登录
		authorization := ctx.DefaultQuery("token", "")
		if authorization == "" {
			returnNotLogin(ctx, "less param token")
			return
		}

		jwt, err := secret.GetJwtFromStrAdm(authorization)

		if err != nil {
			returnNotLogin(ctx, err.Error())
			return
		}

		logger.Error("0 本地解密:时间:", time.Now().Sub(startTime))
		startTime = time.Now()

		//0 解密
		req := api.DecryptJwtRequest{
			Token: jwt,
		}

		info, err := provider.DecryptJwt(ctx, &req)
		logger.Error("1 解密:时间:", time.Now().Sub(startTime))
		startTime = time.Now()

		if err != nil {
			returnNotLogin(ctx, err.Error())
			return
		}

		//1 获取用户的账号信息
		infoReq := &api.InfoRequest{
			ID:    info.ID,
			Scene: "base",
		}

		infoRes, err := service.AccountProvider.Info(ctx, infoReq)

		logger.Error("2 帐号信息:", time.Now().Sub(startTime))
		startTime = time.Now()

		if err != nil {
			returnNotLogin(ctx, err.Error())
			return
		}

		//3 获取用户的岗位信息
		uReq := rule.RulesRequest{
			AccountID: info.ID,
		}

		qres, err1 := service.RuleProvider.UserInfo(ctx, &uReq)

		fmt.Println("---------end帐号,rule的userInfo:时间:", time.Now().Sub(startTime))
		logger.Error("3 rule 帐号信息:", time.Now().Sub(startTime))
		startTime = time.Now()

		if err1 != nil {
			returnNotLogin(ctx, err1.Error())
			return
		}

		loginInfo := login.Info{
			Domain:         info.Domain,
			ID:             info.ID,
			Account:        info.Account,
			NickName:       info.NickName,
			PositionUsers:  qres.PositionUsers,
			Extend:         infoRes.Info.Extend,
			TelNum:         infoRes.Info.TelNum,
			Avatar:         infoRes.Info.Avatar,
			JumpTo:         "",
			DepartmentName: "",
		}

		if infoRes.Info.Extend != nil {
			loginInfo.JumpTo = infoRes.Info.Extend.JumpTo
		}

		if len(qres.PositionUsers) >= 1 {
			loginInfo.DepartmentName = qres.PositionUsers[0].DepartmentName
		}

		ctx.Set("jwtInfo", loginInfo)
		logger.Error("---------check_login:总时间", time.Now().Sub(startTime))
		logger.Error("总时间", time.Now().Sub(realStartTime))

		ctx.Next()

	}
}

func returnNotLogin(c *gin.Context, msg string) {

	// 获取WebSocket连接
	var cstUpgrader = websocket.Upgrader{
		Subprotocols:      []string{"p0", "p1"},
		ReadBufferSize:    1024,
		WriteBufferSize:   1024,
		EnableCompression: true,
		Error: func(w http.ResponseWriter, r *http.Request, status int, reason error) {
			http.Error(w, reason.Error(), status)
		},
		CheckOrigin: func(r *http.Request) bool {
			return true
		},
	}

	ws, err := cstUpgrader.Upgrade(c.Writer, c.Request, http.Header{"Set-Cookie": {"sessionID=1234"}})

	if err != nil {
		return
	}

	defer func() {
		fmt.Println(ws.Close())
	}()

	wsTempRes := &serializer.WsResponse{
		Code:   401,
		Status: 1,
		Msg:    msg,
	}

	b, _ := json.Marshal(wsTempRes)

	_ = ws.WriteMessage(1, b) //
	fmt.Println("1--------------------12")
	c.Abort()

	return

}
