package middleware

import (
	"errors"
	"fmt"
	"github.com/fonchain_enterprise/client-auction/pkg/common/cache"
	"github.com/fonchain_enterprise/client-auction/pkg/e"
	"github.com/fonchain_enterprise/client-auction/pkg/service"
	"github.com/gin-gonic/gin"
	"time"
)

// RateLimit 限制
func RateLimit() gin.HandlerFunc {

	return func(ctx *gin.Context) {

		finerPrint := ctx.GetHeader("fingerprint")
		path := ctx.Request.URL.Path

		key := cache.RateLimitKey(finerPrint, path)
		isLockSuccess, err := cache.RedisClient.SetNX(key, "1", 300*time.Millisecond).Result()
		fmt.Println(isLockSuccess, err)
		fmt.Println("1------", key)

		if err != nil {
			fmt.Println(err)
			return
		}

		if !isLockSuccess { //设置失败
			fmt.Println("设置失败")
			fmt.Println()
			fmt.Println()
			service.Error(ctx, e.InvalidParams, errors.New("请求频率过快"))
			return
		}

		fmt.Println("设置成功")

		ctx.Next()

	}
}
