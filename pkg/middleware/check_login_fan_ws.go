package middleware

import (
	"fmt"
	api "github.com/fonchain_enterprise/client-auction/api/account"
	"github.com/fonchain_enterprise/client-auction/pkg/e"
	model "github.com/fonchain_enterprise/client-auction/pkg/model/common"
	"github.com/fonchain_enterprise/client-auction/pkg/service"
	"github.com/fonchain_enterprise/client-auction/pkg/utils/secret"
	"github.com/gin-gonic/gin"
)

func CheckLoginFansWs(provider *api.AccountClientImpl) gin.HandlerFunc {

	return func(ctx *gin.Context) {

		//如果没有登录
		fmt.Println("开始")
		//如果没有登录
		authorization := ctx.DefaultQuery("token", "")
		fmt.Println("1------------", authorization)

		if authorization == "" {
			//service.Error(ctx, e.NotLogin, errors.New(ErrNotLogin))
			returnNotLogin(ctx, "没有认证")
			return
		}

		jwt, err := secret.GetJwtFromStr(authorization)

		if err != nil {
			//service.Error(ctx, e.NotLogin, errors.New(ErrNotLogin))
			returnNotLogin(ctx, err.Error())
			return
		}

		req := api.DecryptJwtRequest{
			Token: jwt,
		}

		info, err := provider.DecryptJwt(ctx, &req)

		if err != nil {
			returnNotLogin(ctx, err.Error())
			//service.Error(ctx, e.NotLogin, err)
			return
		}

		//获取用户的账号信息
		infoReq := &api.InfoRequest{
			ID:     info.ID,
			Domain: e.MallAuctionDomain,
		}

		infoRes, err := service.AccountProvider.Info(ctx, infoReq)

		if err != nil {
			returnNotLogin(ctx, err.Error())
			return
		}

		loginInfo := LoginInfo{
			Domain:         info.Domain,
			ID:             info.ID,
			Account:        info.Account,
			NickName:       info.NickName,
			Extend:         infoRes.Info.Extend,
			TelNum:         infoRes.Info.TelNum,
			IdNum:          infoRes.Info.IDNum,
			RealName:       infoRes.Info.RealName,
			JumpTo:         "",
			DepartmentName: "",
		}

		if infoRes.Info.Extend != nil {
			loginInfo.JumpTo = infoRes.Info.Extend.JumpTo
		}

		ctx.Set("jwtInfo", loginInfo)
		var mLoginInfo model.LoginInfo
		mLoginInfo.ID = loginInfo.ID
		mLoginInfo.NickName = loginInfo.NickName
		ctx.Set("mLoginInfo", mLoginInfo)
		ctx.Next()
	}
}
