package middleware

import (
	"errors"
	"fmt"
	api "github.com/fonchain_enterprise/client-auction/api/account"
	"github.com/fonchain_enterprise/client-auction/pkg/e"
	"github.com/fonchain_enterprise/client-auction/pkg/logic"
	"github.com/fonchain_enterprise/client-auction/pkg/model/login"
	"github.com/fonchain_enterprise/client-auction/pkg/service"
	"github.com/fonchain_enterprise/client-auction/pkg/utils/secret"
	"github.com/gin-gonic/gin"
	"github.com/opentracing/opentracing-go/log"
	"time"
)

func CheckLogin(provider *api.AccountClientImpl) gin.HandlerFunc {

	return func(ctx *gin.Context) {

		fmt.Println()
		fmt.Println()
		if provider == nil {
			service.Error(ctx, e.Error, errors.New("server init failed"))
			log.Error(errors.New("账号微服务没有初始化或传参为nil"))
			return
		}
		startTime := time.Now()
		realStartTime := time.Now()

		debugCollector := new(checkLoginDebugCollet)
		//debugCollector.hide = true //注释掉打印日志
		defer debugCollector.Println()
		debugCollector.AddLog("开始")

		//如果没有登录
		authorization := ctx.GetHeader(e.Authorization)
		if authorization == "" {
			service.NotLoginRes(ctx, logic.ConvertLoginMsg(ctx, e.ErrNotLogin))
			return
		}
		fmt.Println("", authorization)

		jwt, err := secret.GetJwtFromStrAdm(authorization)

		debugCollector.AddLog(fmt.Sprintf("0 本地解密 :%v", time.Now().Sub(startTime)))
		startTime = time.Now()

		if err != nil {
			service.NotLoginRes(ctx, err.Error())
			return
		}

		//0 解密
		req := api.DecryptJwtRequest{
			Token: jwt,
		}

		info, err := service.AccountProvider.DecryptJwt(ctx.Request.Context(), &req)
		debugCollector.AddLog(fmt.Sprintf("1 微服务解密:时间:%v", time.Now().Sub(startTime)))
		startTime = time.Now()

		if err != nil {
			service.NotLoginRes(ctx, err.Error())
			return
		}

		if info.OfflineCode == e.OfflineSqueeze {
			service.Error(ctx, e.NotLoginSqueeze, errors.New(e.ErrOfflineSqueeze))
			return
		}

		//1 获取用户的账号信息
		infoReq := &api.InfoRequest{
			ID:     info.ID,
			Scene:  "base",
			Domain: e.VerifierDomain,
		}

		infoRes, err := service.AccountProvider.Info(ctx.Request.Context(), infoReq)

		debugCollector.AddLog(fmt.Sprintf("2 帐号信息 时间:%v", time.Now().Sub(startTime)), "error")
		startTime = time.Now()

		if err != nil {
			service.Error(ctx, e.Error, err)
			return
		}

		//3 获取用户的岗位信息
		/*
			uReq := rule.RulesRequest{
				AccountID: info.ID,
			}

			qres, err1 := service.RuleProvider.UserInfo(ctx, &uReq)
			if err1 != nil {
				service.Error(ctx, e.Error, err)
				return
			}
		*/

		debugCollector.AddLog(fmt.Sprintf("3 rule信息info:时间:%s", time.Now().Sub(startTime)))
		startTime = time.Now()

		loginInfo := login.Info{
			Domain:   info.Domain,
			ID:       info.ID,
			Account:  info.Account,
			NickName: info.NickName,
			//PositionUsers:  qres.PositionUsers,
			Extend:         infoRes.Info.Extend,
			TelNum:         infoRes.Info.TelNum,
			Avatar:         infoRes.Info.Avatar,
			JumpTo:         "",
			DepartmentName: "",
		}

		if infoRes.Info.Extend != nil {
			loginInfo.JumpTo = infoRes.Info.Extend.JumpTo
		}

		/*
			if len(qres.PositionUsers) >= 1 {
				loginInfo.DepartmentName = qres.PositionUsers[0].DepartmentName
			}
		*/

		ctx.Set("jwtInfo", loginInfo)

		debugCollector.AddLog(fmt.Sprintf("4 rule信息info:时间:%s", time.Now().Sub(startTime)))
		debugCollector.AddLog(fmt.Sprintf(":总时间%v", time.Now().Sub(realStartTime)))

		fmt.Println("1--------------", loginInfo)
		ctx.Next()

	}
}
