package middleware

import (
	"fmt"
	api "github.com/fonchain_enterprise/client-auction/api/account"
	"github.com/fonchain_enterprise/client-auction/pkg/model/login"
	"github.com/fonchain_enterprise/client-auction/pkg/utils/secret"
	"github.com/gin-gonic/gin"
	"time"
)

// CheckLoginWsBase 不检测是否下线
func CheckLoginWsBase(provider *api.AccountClientImpl) gin.HandlerFunc {

	return func(ctx *gin.Context) {
		fmt.Println("开始")
		startTime := time.Now()
		//如果没有登录
		authorization := ctx.DefaultQuery("token", "")
		if authorization == "" {
			returnNotLogin(ctx, "less param token")
			return
		}

		jwt, err := secret.GetJwtFromStrAdm(authorization)

		if err != nil {
			returnNotLogin(ctx, err.Error())
			return
		}

		//0 解密
		req := api.DecryptJwtRequest{
			Token: jwt,
		}

		info, err := provider.DecryptJwt(ctx.Request.Context(), &req)
		fmt.Println("---------end帐号微服务解密:时间:", time.Now().Sub(startTime))

		if err != nil {
			returnNotLogin(ctx, err.Error())
			return
		}

		loginInfo := login.Info{
			Domain:         info.Domain,
			ID:             info.ID,
			Account:        info.Account,
			NickName:       info.NickName,
			JumpTo:         "",
			DepartmentName: "",
		}

		ctx.Set("jwtInfo", loginInfo)
		fmt.Println("---------check_login:总时间", time.Now().Sub(startTime))
		ctx.Next()

	}
}
