package middleware

import (
	"errors"
	"fmt"
	api "github.com/fonchain_enterprise/client-auction/api/account"
	"github.com/fonchain_enterprise/client-auction/pkg/e"
	model "github.com/fonchain_enterprise/client-auction/pkg/model/common"
	"github.com/fonchain_enterprise/client-auction/pkg/service"
	"github.com/fonchain_enterprise/client-auction/pkg/utils/secret"
	"github.com/gin-gonic/gin"
	"log"
)

const (
	ErrNotLogin = "请先登录"
)

const (
	Authorization = "Authorization"
)

type LoginInfo struct {
	Domain         string
	ID             uint64
	Account        string
	TelNum         string
	NickName       string
	JumpTo         string
	DepartmentName string
	IdNum          string
	RealName       string
	Sex            uint64
	Extend         *api.Extend
	FddInfo        *api.FddInfo
	UserExtend     *api.UserExtend
	//PositionUsers  []*rule.PositionUser
}

func (l *LoginInfo) GetName() string {
	name := l.NickName
	/*
		if l.RealName != ""{
			name = l.RealName
		}

	*/

	return name
}

func CheckLoginFans(provider *api.AccountClientImpl) gin.HandlerFunc {

	return func(ctx *gin.Context) {

		//如果没有登录
		authorization := ctx.GetHeader(Authorization)
		if authorization == "" {
			service.Error(ctx, e.NotLogin, errors.New(ErrNotLogin))
			return
		}

		jwt, err := secret.GetJwtFromStr(authorization)

		if err != nil {
			service.Error(ctx, e.NotLogin, errors.New(ErrNotLogin))
			return
		}

		info := &api.DecryptJwtResponse{}

		if provider != nil {
			req := api.DecryptJwtRequest{
				Token: jwt,
			}
			info, err = provider.DecryptJwt(ctx.Request.Context(), &req)
			if err != nil {
				service.Error(ctx, e.NotLogin, err)
				return
			}
		} else {
			log.Println("账号微服务没有初始化或传参为nil")
			service.Error(ctx, e.NotLogin, errors.New(ErrNotLogin))
			return
		}

		//获取用户的账号信息
		infoReq := &api.InfoRequest{
			ID:     info.ID,
			Domain: e.MallAuctionDomain,
		}

		infoRes, err := service.AccountProvider.Info(ctx.Request.Context(), infoReq)

		if err != nil {
			service.Error(ctx, e.NotLogin, errors.New(ErrNotLogin))
			return
		}

		fddInfo, err := service.AccountProvider.FddUserFindByUserId(ctx.Request.Context(), &api.UserInfo{ID: info.ID, Domain: "auction"})

		fmt.Println("法大大", fddInfo, err)

		loginInfo := LoginInfo{
			Domain:         info.Domain,
			ID:             info.ID,
			Account:        info.Account,
			NickName:       info.NickName,
			Extend:         infoRes.Info.Extend,
			TelNum:         infoRes.Info.TelNum,
			IdNum:          infoRes.Info.IDNum,
			RealName:       infoRes.Info.RealName,
			Sex:            infoRes.Info.Sex,
			UserExtend:     infoRes.Info.UserExtend,
			FddInfo:        fddInfo,
			JumpTo:         "",
			DepartmentName: "",
		}

		if infoRes.Info.Extend != nil {
			loginInfo.JumpTo = infoRes.Info.Extend.JumpTo
		}

		ctx.Set(e.JwtInfo, loginInfo)
		var mLoginInfo model.LoginInfo
		mLoginInfo.ID = loginInfo.ID
		mLoginInfo.NickName = loginInfo.NickName
		ctx.Set("mLoginInfo", mLoginInfo)
		ctx.Next()
	}
}
