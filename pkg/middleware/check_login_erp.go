// Package middleware -----------------------------
// @file      : check_login_erp.go
// <AUTHOR> JJXu
// @contact   : <EMAIL>
// @time      : 2025/1/24 16:26
// -------------------------------------------
package middleware

import (
	"errors"
	"fmt"
	api "github.com/fonchain_enterprise/client-auction/api/account"
	"github.com/fonchain_enterprise/client-auction/pkg/e"
	"github.com/fonchain_enterprise/client-auction/pkg/logic"
	model "github.com/fonchain_enterprise/client-auction/pkg/model/common"
	"github.com/fonchain_enterprise/client-auction/pkg/service"
	"github.com/fonchain_enterprise/client-auction/pkg/utils/secret"
	"github.com/gin-gonic/gin"
)

func CheckLoginErpOld(provider *api.AccountClientImpl) gin.HandlerFunc {

	return func(ctx *gin.Context) {

		//如果没有登录
		authorization := ctx.GetHeader(Authorization)
		fmt.Println("authorization", authorization)
		if authorization == "" {
			service.Error(ctx, e.NotLogin, errors.New(ErrNotLogin))
			return
		}

		jwt, err := secret.GetJwtFromStr(authorization)

		if err != nil {
			service.Error(ctx, e.NotLogin, errors.New(ErrNotLogin))
			return
		}

		req := api.DecryptJwtRequest{
			Token:  jwt,
			Domain: e.VerifierDomain,
		}

		info, err := provider.DecryptJwt(ctx.Request.Context(), &req)
		fmt.Printf("jwt info:%+v\n", info)
		fmt.Printf("DecryptJwt err:%v\n", err)
		if err != nil {
			service.NotLoginRes(ctx, err.Error())
			return
		}
		if info.IsOffline == true {
			service.Error(ctx, e.NotLogin, errors.New(logic.ConvertOfflineMsg(ctx, e.ErrOffline)))
			return
		}

		//获取用户的账号信息
		infoReq := &api.InfoRequest{
			ID:    info.ID,
			Scene: "base",
		}

		infoRes, err := service.AccountProvider.Info(ctx.Request.Context(), infoReq)
		if err != nil {
			service.Error(ctx, e.Error, err)
			return
		}
		loginInfo := LoginInfo{
			Domain:         info.Domain,
			ID:             info.ID,
			Account:        info.Account,
			NickName:       info.NickName,
			Extend:         infoRes.Info.Extend,
			TelNum:         infoRes.Info.TelNum,
			IdNum:          infoRes.Info.IDNum,
			RealName:       infoRes.Info.RealName,
			JumpTo:         "",
			DepartmentName: "",
		}

		if infoRes.Info.Extend != nil {
			loginInfo.JumpTo = infoRes.Info.Extend.JumpTo
		}

		ctx.Set(e.JwtInfo, loginInfo)
		var mLoginInfo model.LoginInfo
		mLoginInfo.ID = loginInfo.ID
		mLoginInfo.NickName = loginInfo.NickName
		ctx.Set("mLoginInfo", mLoginInfo)
		ctx.Next()
	}
}
