package logic

import (
	"github.com/fonchain_enterprise/client-auction/pkg/common/cache"
	"github.com/fonchain_enterprise/client-auction/pkg/model/dto"
)

/*
*
1.1今日累计在线人数：今天进入直播间的总人次，1人进入n次记为n次；

	1.2今日累计用户数：今天进入直播间的总人数，1人进入多次记为1次；
	1.3今日最高在线人数：记录今天本场次最高在线人数；
*/
func GetAuctionStat(auctionUuid string) *dto.AuctionStat {

	//todo 查询拍卖人数统计
	auctionStat := &dto.AuctionStat{
		TodayOnlinePersonTotal: 0,
		TodayOnlineUserTotal:   0,
		TodayOnlinePersonMax:   0,
	}

	nowDayNum := cache.GetNowDayNum(auctionUuid)        //获取当前天的人次
	sampleNum := cache.GetSampleBiggestNum(auctionUuid) //获取最高人数
	getNowDayUser := cache.GetNowDayUser(auctionUuid)   //当天人数 dau

	type AuctionStat struct {
		TodayOnlinePersonTotal int32 `json:"todayOnlinePersonTotal"` // 今日累计在线人数
		TodayOnlineUserTotal   int32 `json:"todayOnlineUserTotal"`   // 今日累计用户数
		TodayOnlinePersonMax   int32 `json:"todayOnlinePersonMax"`   // 今日最高人数
	}

	auctionStat.TodayOnlinePersonTotal, _ = cache.RedisClient.Get(nowDayNum).Int64()
	auctionStat.TodayOnlineUserTotal, _ = cache.RedisClient.Get(sampleNum).Int64()
	auctionStat.TodayOnlinePersonTotal, _ = cache.RedisClient.Get(getNowDayUser).Int64()
	return auctionStat
}
