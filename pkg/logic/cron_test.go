package logic

import (
	"github.com/fonchain_enterprise/client-auction/pkg/common/cache"
	"testing"
)

func Test_getMoneyFromCurrency(t *testing.T) {
	redisConfig := cache.RedisConfig{
		RedisDB:     "1",
		RedisAddr:   "172.16.100.114:6379",
		RedisPw:     "kP6tW4tS3qB2dW4aE6uI5cX2",
		RedisDbName: "1",
		PoolSize:    10,
	}

	cache.LoadRedis(redisConfig)

	type args struct {
		from  string
		to    string
		money string
	}
	tests := []struct {
		name    string
		args    args
		want    string
		wantErr bool
	}{
		// TODO: Add test cases.
		{args: args{from: "CNY", to: "USD", money: "102"}},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := getMoneyFromCurrency(tt.args.from, tt.args.to, tt.args.money)
			if (err != nil) != tt.wantErr {
				t.Errorf("getMoneyFromCurrency() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if got != tt.want {
				t.Errorf("getMoneyFromCurrency() got = %v, want %v", got, tt.want)
			}
		})
	}
}
