package live

import (
	"bytes"
	"crypto/aes"
	"crypto/cipher"
	"crypto/rand"
	"crypto/sha1"
	"encoding/base64"
	"fmt"
	"golang.org/x/crypto/pbkdf2"
	"io"
)

// PKCS7 填充
func pkcs7Padding(data []byte, blockSize int) []byte {
	padding := blockSize - len(data)%blockSize
	padText := bytes.Repeat([]byte{byte(padding)}, padding)
	return append(data, padText...)
}

// 加密
func encrypt(plaintext, password, salt []byte, iterations int) (string, error) {
	// 生成密钥
	key := pbkdf2.Key(password, salt, iterations, 32, sha1.New)

	// 创建一个新的 AES 块
	block, err := aes.NewCipher(key)
	if err != nil {
		return "", err
	}

	// 创建一个随机的 IV
	iv := make([]byte, aes.BlockSize)
	if _, err := io.ReadFull(rand.Reader, iv); err != nil {
		return "", err
	}

	// 对明文进行 PKCS7 填充
	plaintext = pkcs7Padding(plaintext, aes.BlockSize)

	// 加密
	ciphertext := make([]byte, len(plaintext))
	mode := cipher.NewCBCEncrypter(block, iv)
	mode.CryptBlocks(ciphertext, plaintext)

	// 将 IV 和密文拼接在一起并编码为 Base64
	combined := append(iv, ciphertext...)
	encoded := base64.StdEncoding.EncodeToString(combined)

	return encoded, nil
}

func CreateCodeUrl(url string) string {
	password := "live-skkoql-1239-key"
	salt := []byte("aldk100128ls")
	iterations := 10000
	plaintext := []byte(url)

	encryptedText, err := encrypt(plaintext, []byte(password), salt, iterations)
	if err != nil {
		fmt.Println("Error:", err)
		return ""
	}

	fmt.Println("Encrypted Text:", encryptedText)
	return encryptedText
}
