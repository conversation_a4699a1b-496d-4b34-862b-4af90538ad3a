package live

import "testing"

func TestCreateCodeUrl(t *testing.T) {
	type args struct {
		plaintext string
	}
	tests := []struct {
		name string
		args args
		want string
	}{
		// TODO: Add test cases.
		{args: args{plaintext: "rtcs://kdjfladf.adkfjla.ajlf/aoqeur/kadfj?token=123uo0djfkadsujhf010324yu"}},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := CreateCodeUrl(tt.args.plaintext); got != tt.want {
				t.Errorf("CreateCodeUrl() = %v, want %v", got, tt.want)
			}
		})
	}
}
