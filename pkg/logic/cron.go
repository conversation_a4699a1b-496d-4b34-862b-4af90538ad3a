package logic

import (
	"encoding/json"
	"errors"
	"fmt"
	"github.com/fonchain/utils/currencyExchange"
	"github.com/fonchain_enterprise/client-auction/pkg/common/cache"
	"github.com/fonchain_enterprise/client-auction/pkg/model/dto"
	"github.com/shopspring/decimal"
	"time"
)

type CurrencyInfo struct {
	Currency string `json:"currency"`
	Price    string `json:"price"`
}

func SynMoneyRate() {
	setCurrencyExchangeRate()
}

func setCurrencyExchangeRate() {
	lockKey := "my_lock_key"
	lockValue := "unique_value" // 确保每个请求有不同的唯一值
	lockTimeout := 10 * time.Second

	// 尝试获取锁
	setNx := cache.RedisClient.SetNX(lockKey, lockValue, lockTimeout)

	result, err := setNx.Result()
	if err != nil {
		fmt.Println("错误", result)
		return
	}
	if !result {
		fmt.Println("错误,key已存在，设置失败")
		return
	}

	incr := cache.RedisClient.Incr(cache.GetTotalRateDailyNum()).Val()
	if incr >= 3 {
		fmt.Println("次数过多")
		return
	}

	res, err := currencyExchange.CurrencyExchangeRate()
	rates := &dto.CurrencyRate{}

	for i, v := range res.ShowapiResBody.List {
		if v.Code == "USD" {
			rates.USD = res.ShowapiResBody.List[i]
		}

		if v.Code == "CNY" {
			rates.CNY = res.ShowapiResBody.List[i]
			rates.RMB = res.ShowapiResBody.List[i]
		}

		if v.Code == "JPY" {
			rates.JPY = res.ShowapiResBody.List[i]
		}

		if v.Code == "TWD" {
			rates.TWD = res.ShowapiResBody.List[i]
		}

		if v.Code == "HKD" {
			rates.HKD = res.ShowapiResBody.List[i]
		}

		if v.Code == "EUR" {
			rates.EUR = res.ShowapiResBody.List[i]
		}

	}

	if rates.HKD == nil || rates.CNY == nil || rates.TWD == nil || rates.JPY == nil || rates.USD == nil || rates.EUR == nil {
		fmt.Println("汇率数据不全")
		return
	}

	tempStr, _ := json.Marshal(rates)

	cache.RedisClient.Set(cache.GetTotalRate(), string(tempStr), 0)
	cache.RedisClient.Set(cache.GetTotalRateDaily(), string(tempStr), 0)

	fmt.Println(res.ShowapiResBody, err)

	str := cache.RedisClient.Get(cache.GetTotalRateDaily()).Val()

	re := &dto.CurrencyRate{}

	json.Unmarshal([]byte(str), &re)
	fmt.Println("1------------------")
	fmt.Println("1------------------")
	fmt.Println("1------------------")
	fmt.Println(re)

	fmt.Println("1------------------")
	fmt.Println("1------------------")
	fmt.Println("1------------------")
	fmt.Println("1------------------")
}

func getMoneyFromCurrency(from, to, money string) (string, error) {
	rate, err := getFromAndToCurrency(from, to)
	if err != nil {
		return "", err
	}

	moneyDec, err := decimal.NewFromString(money)
	if err != nil {
		return "", err
	}

	rateDec, err := decimal.NewFromString(rate)
	if err != nil {
		return "", err
	}
	newDec := rateDec.Mul(moneyDec)
	return newDec.Round(2).String(), nil

}

func getFromAndToCurrency(from, to string) (string, error) {

	rate := cache.RedisClient.Get(cache.GetCurrency(from, to)).Val()
	if rate != "" {
		return rate, nil
	}

	res, err := currencyExchange.CurrencyRate(from, to)
	if err != nil {
		return "", err
	}

	cache.RedisClient.Set(cache.GetCurrency(from, to), res, 0)

	return res, nil
}

func GetAllCurrencyPrice(currency, price string) ([]CurrencyInfo, error) {
	var res []CurrencyInfo

	realCurrency := currency
	if currency == "RMB" {
		currency = "CNY"
	}

	if currency != "CNY" && currency != "JPY" && currency != "USD" {
		return nil, errors.New("不支持的货币类型")
	}

	res = append(res, CurrencyInfo{Currency: realCurrency, Price: price})

	if currency != "CNY" {
		price, err := getMoneyFromCurrency(currency, "CNY", price)
		if err != nil {
			return nil, err
		}
		res = append(res, CurrencyInfo{Currency: "CNY", Price: price})
	}

	if currency != "JPY" {
		price, err := getMoneyFromCurrency(currency, "JPY", price)
		if err != nil {
			return nil, err
		}
		res = append(res, CurrencyInfo{Currency: "JPY", Price: price})
	}

	if currency != "USD" {
		price, err := getMoneyFromCurrency(currency, "USD", price)
		if err != nil {
			return nil, err
		}
		res = append(res, CurrencyInfo{Currency: "USD", Price: price})
	}
	return res, nil

}

func GetCurrencyExchangeRate(currency string, isGlobal bool) string {
	return getCurrencyExchangeRate(currency, isGlobal)
}

func getCurrencyExchangeRate(currency string, isGlobal bool) string {

	//NewCap := capexe.NewCapExe()
	str := cache.RedisClient.Get(cache.GetTotalRate()).Val()
	//NewCap.Log("redis获取 ")

	re := &dto.CurrencyRate{}

	json.Unmarshal([]byte(str), &re)

	//NewCap.Log("redis解压 ")
	var temp *currencyExchange.CoinCurrency
	temp = re.USD

	if currency == "JPY" {
		temp = re.JPY
	}

	if currency == "CNY" || currency == "RMB" {
		temp = re.RMB
	}

	if currency == "USD" {
		temp = re.USD
	}

	if isGlobal == true {
		if currency == "TWD" {
			temp = re.TWD
		}

		if currency == "HKD" {
			temp = re.HKD
		}

		if currency == "EUR" {
			temp = re.EUR
		}

	}

	if temp.Code == "TWD" && temp.HuiOut == "" {
		return temp.ChaoIn
	}
	//NewCap.Log("redis其他 ")

	return temp.HuiOut
}
