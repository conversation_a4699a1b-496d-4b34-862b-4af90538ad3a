package logic

import (
	"encoding/json"
	"errors"
	"github.com/fonchain_enterprise/client-auction/pkg/common/cache"
	"github.com/fonchain_enterprise/client-auction/pkg/e"
)

type FanChannel struct {
	Type               string
	AuctionArtworkUuid string
	TipType            string //当type为tip的时候，需要传递tipType
	UserId             uint32 //当type为tip的时候，需要传递tipType
}

// SendFansInfo 给用户发送数据 提示 tip artwork nowAuctionPrice auctionPriceList first(除了tip)
func SendFansInfo(auctionUuid, msgType string) error {
	if _, isExist := e.FanAuctionStateMap[msgType]; !isExist {
		return errors.New("")
	}

	tempRes := FanChannel{
		Type: msgType,
	}

	tempBytes, err := json.Marshal(tempRes)
	if err != nil {
		return err
	}

	cache.RedisClient.Publish(cache.GetFansChannel(auctionUuid), string(tempBytes)) //done

	return nil
}

// SendFansTip  给用户发送 tip 只有临时的那个
func SendFansTip(auctionUuid, tipType string, UserId uint32) error {

	tempRes := FanChannel{
		Type:    e.FanAuctionChannel_Tip,
		TipType: tipType,
		UserId:  UserId,
	}

	tempBytes, err := json.Marshal(tempRes)
	if err != nil {
		return err
	}

	cache.RedisClient.Publish(cache.GetFansChannel(auctionUuid), string(tempBytes))

	return nil
}

func SendFansTipAndOverArtworkUuid(auctionUuid, auctionArtworkUuid string, userId uint32) error {

	tempRes := FanChannel{
		Type:               e.FanAuctionChannel_Tip,
		TipType:            e.FanAuctionChannel_TempArtowkrOver,
		AuctionArtworkUuid: auctionArtworkUuid,
		UserId:             userId,
	}

	tempBytes, err := json.Marshal(tempRes)
	if err != nil {
		return err
	}

	cache.RedisClient.Publish(cache.GetFansChannel(auctionUuid), string(tempBytes))

	return nil
}

func SendAdminLiveBuys(auctionUuid, auctionArtworkUuid string) error {

	cache.RedisClient.Publish(cache.GetSquareChannel(auctionUuid), auctionArtworkUuid)

	return nil
}

// SendFansTipAndSuccessPay TODO auctionArtworkUuid参数疑似错误
func SendFansTipAndSuccessPay(auctionUuid, auctionArtworkUuid string, userId uint32) error {

	type FanChannel struct {
		Type               string
		AuctionArtworkUuid string
		TipType            string //当type为tip的时候，需要传递tipType
		UserId             uint32 //当type为tip的时候，需要传递tipType
	}

	tempRes := FanChannel{
		Type: "payList",
		//TipType:            "successBid",
		AuctionArtworkUuid: auctionArtworkUuid,
		UserId:             userId,
	}

	tempBytes, err := json.Marshal(tempRes)
	if err != nil {
		return err
	}

	cache.RedisClient.Publish(cache.GetFansChannel(auctionUuid), string(tempBytes)) //done

	return nil
}
