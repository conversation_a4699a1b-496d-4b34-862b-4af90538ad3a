package logic

import (
	"github.com/fonchain_enterprise/client-auction/pkg/e"
	"github.com/gin-gonic/gin"
	"strings"
)

// GetLanguageCurrency 获取币种
func GetLanguageCurrency(c *gin.Context) string {
	return "RMB"
	currency := "RMB"
	lang := c.Request.Header.Get("Accept-Language")
	lang = strings.Split(lang, ",")[0]
	if lang == "zh-CN" || lang == "zh" || lang == "ZhCN" {
		currency = "RMB"
	} else if lang == "ja" || lang == "ja-JP" { //日本
		currency = "JPY"
	} else {
		currency = "USD"
	}

	return currency
}

// GetLiveRegion 通过语言切换不同的直播节点, 国内(大陆) 其他日本
func GetLiveRegion(c *gin.Context) string {
	lang := c.Request.Header.Get("Accept-Language")
	lang = strings.Split(lang, ",")[0]
	if lang == "zh-CN" || lang == "zh" || lang == "ZhCN" {
		lang = "CNY"
	} else {
		lang = "JYP"
	}

	return lang
}

func GetLanguage(c *gin.Context) string {
	lang := c.Request.Header.Get("Accept-Language")
	lang = strings.ToLower(strings.Split(lang, ",")[0])
	if lang == "" { //没有传递则是中文
		lang = "ZhCN"
	} else if lang == "zh-cn" || lang == "zh" || lang == "zhcn" {
		lang = "ZhCN"
	} else if lang == "zh-tw" || lang == "zhtw" {
		lang = "ZhTW"
	} else if lang == "ja" || lang == "ja-jp" { //日本
		lang = "Ja"
	} else if lang == "en" {
		lang = "EN"
	} else {
		lang = "EN"
	}

	return lang
}

// GetLanguageTime TODO: 有时间的时候调整
func GetLanguageTime(startTime, lang string) string {
	if lang == "" { //没有传递则是中文
		return startTime
	} else if lang == "ZhCN" {
		lang = startTime
	} else if lang == "zh-TW" || lang == "ZhTW" {
		if startTime == "北京时间" {
			return "北京時間"
		} else {
			return "東京時間"
		}
	} else if lang == "ja" || lang == "ja-JP" { //日本

		if startTime == "北京时间" {
			return "北京時間"
		} else {
			return "東京時間"
		}
	} else {
		if startTime == "北京时间" {
			return "Beijing Time"
		} else {
			return "Tokyo Time"
		}
	}

	return startTime
}

func ConvertOfflineMsg(c *gin.Context, key string) string {
	language := GetLanguage(c)
	switch language {
	case "EN":
		return e.OfflineMap[strings.Join([]string{key, "EN"}, "")]
	case "ZhCN":
		return e.OfflineMap[strings.Join([]string{key, "ZhCN"}, "")]
	case "ZhTW":
		return e.OfflineMap[strings.Join([]string{key, "ZhTW"}, "")]
	default:
		return key
	}
}

func ConvertLoginMsg(c *gin.Context, key string) string {
	language := GetLanguage(c)
	switch language {
	case "EN":
		return e.LoginMap[strings.Join([]string{key, "EN"}, "")]
	case "ZhCN":
		return e.LoginMap[strings.Join([]string{key, "ZhCN"}, "")]
	case "ZhTW":
		return e.LoginMap[strings.Join([]string{key, "ZhTW"}, "")]
	default:
		return key
	}
}

func ConvertWeek(language string, week int32) string {
	switch language {
	case "EN":
		switch week {
		case 1:
			return "Monday"
		case 2:
			return "Tuesday"
		case 3:
			return "Wednesday"
		case 4:
			return "Thursday"
		case 5:
			return "Friday"
		case 6:
			return "Saturday"
		case 7:
			return "Sunday"
		}
	case "ZhCN":
		switch week {
		case 1:
			return "星期一"
		case 2:
			return "星期二"
		case 3:
			return "星期三"
		case 4:
			return "星期四"
		case 5:
			return "星期五"
		case 6:
			return "星期六"
		case 7:
			return "星期日"
		}
	case "ZhTW":
		switch week {
		case 1:
			return "星期一"
		case 2:
			return "星期二"
		case 3:
			return "星期三"
		case 4:
			return "星期四"
		case 5:
			return "星期五"
		case 6:
			return "星期六"
		case 7:
			return "星期日"
		}
	}
	return ""
}

func ConvertAmPm(language string, amPm string) string {
	switch language {
	case "EN":
		switch amPm {
		case "上午":
			return "AM"
		case "下午":
			return "PM"
		}
	}
	return amPm
}

func ConvertOaMsg(c *gin.Context, key string) string {
	language := GetLanguage(c)
	switch language {
	case "EN":
		return e.GetMsgEN(strings.Join([]string{key, "EN"}, ""))
	case "ZhCN":
		return e.GetMsgZhCN(strings.Join([]string{key, "ZhCN"}, ""))
	case "ZhTW":
		return e.GetMsgZhTW(strings.Join([]string{key, "ZhTW"}, ""))
	default:
		return key
	}
}
