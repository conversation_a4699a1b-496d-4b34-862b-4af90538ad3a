package logic

import (
	"github.com/fonchain_enterprise/client-auction/pkg/common/cache"
	"testing"
)

func TestGetCurrencyMoney(t *testing.T) {

	redisConfig := cache.RedisConfig{
		RedisDB:     "1",
		RedisAddr:   "172.16.100.114:6379",
		RedisPw:     "kP6tW4tS3qB2dW4aE6uI5cX2",
		RedisDbName: "1",
		PoolSize:    10,
	}

	cache.LoadRedis(redisConfig)

	type args struct {
		moneyRmb string
		currency string
	}
	tests := []struct {
		name string
		args args
		want string
	}{
		// TODO: Add test cases.
		{args: args{moneyRmb: "100", currency: "USD"}},
		{args: args{moneyRmb: "100", currency: "JPY"}},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := GetCurrencyMoney(tt.args.moneyRmb, tt.args.currency); got != tt.want {
				t.Errorf("GetCurrencyMoney() = %v, want %v", got, tt.want)
			}
		})
	}
}
