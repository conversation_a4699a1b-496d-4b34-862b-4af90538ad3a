package lang

import (
	"bytes"
	"encoding/json"
	"errors"
	"fmt"
	"github.com/fonchain_enterprise/client-auction/pkg/common/cache"
	"io/ioutil"
	"net/http"
)

// 定义响应结构体
type Response struct {
	Status int `json:"status"`
	Data   *struct {
		Content string `json:"content"`
	} `json:"data"`
	Message string `json:"msg"`
	Code    int    `json:"code"`
}

func GetConvertString(msg, lang string) string {
	//解析
	cacheNewMsg := cache.RedisClient.HGet(cache.GetLandMsg(lang), msg).Val()
	if cacheNewMsg != "" {
		return cacheNewMsg
	}

	newMsg, err := realGetConvertString(msg, lang)
	if err != nil { //提示

		return msg
	}

	cache.RedisClient.HSet(cache.GetLandMsg(lang), msg, newMsg)

	return newMsg
}

func realGetConvertString(msg, lang string) (string, error) {
	newMsg := msg
	// 定义请求的URL
	url := "https://common.szjixun.cn/api/msg/translate/cn"

	// 定义要发送的数据
	data := map[string]string{
		"lang": lang,
		"msg":  msg,
	}

	// 将数据编码为JSON格式
	jsonData, err := json.Marshal(data)
	if err != nil {
		fmt.Printf("Error encoding JSON: %v\n", err)
		return newMsg, err
	}

	// 创建一个新的HTTP请求
	req, err := http.NewRequest("POST", url, bytes.NewBuffer(jsonData))
	if err != nil {
		fmt.Printf("Error creating request: %v\n", err)
		return newMsg, err
	}

	// 设置请求头
	req.Header.Set("Content-Type", "application/json")

	// 创建一个HTTP客户端并发送请求
	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		fmt.Printf("Error sending request: %v\n", err)
		return newMsg, err
	}
	defer resp.Body.Close()

	// 读取响应体
	body, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		fmt.Printf("Error reading response body: %v\n", err)
		return newMsg, err
	}

	// 解析响应体到Response结构体
	var response Response
	err = json.Unmarshal(body, &response)
	if err != nil {
		fmt.Printf("Error unmarshalling response: %v\n", err)
		return newMsg, err
	}

	// 打印响应状态和内容
	fmt.Printf("Response status: %d\n", response.Status)
	fmt.Printf("Message: %s\n", response.Message)
	fmt.Printf("Code: %d\n", response.Code)
	fmt.Printf("Translated content: %s\n", response.Data.Content)

	if response.Code != 0 {
		return newMsg, errors.New(response.Message)
	}

	if response.Data == nil {
		return newMsg, errors.New("翻译错误")
	}

	return response.Data.Content, nil
}
