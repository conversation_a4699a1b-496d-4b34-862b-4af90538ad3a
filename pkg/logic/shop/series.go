package shop

import (
	"fmt"
	"github.com/fonchain_enterprise/client-auction/api/fenghe"
	backendSeries "github.com/fonchain_enterprise/client-auction/api/series"
	"github.com/fonchain_enterprise/client-auction/api/shopbrand"
	"github.com/fonchain_enterprise/client-auction/pkg/e"
	"github.com/fonchain_enterprise/client-auction/pkg/logic"
	"github.com/fonchain_enterprise/client-auction/pkg/service"
	"github.com/fonchain_enterprise/client-auction/pkg/service/fenghe/dto"
	"github.com/gin-gonic/gin"
	"github.com/jinzhu/copier"
)

func GetSeriesDetailAndBind(c *gin.Context, req *backendSeries.SeriesDetailReq) {

	req.Lang = logic.GetLanguage(c)
	resp, err := service.BackendSeriesProvider.SeriesDetail(c, req)
	if err != nil {
		service.Error(c, e.Failed, err)
		return
	}
	brandResp, _ := service.BrandProvider.BrandInfo(c, &shopbrand.BrandInforeq{
		BrandUid: resp.BrandId,
		Lang:     req.Lang,
	})
	var result = dto.ShowDetail{
		SeriesUid:  resp.SeriesUuid,
		SeriesName: resp.SeriesName,
		BrandId:    resp.BrandId,
		BrandName:  resp.BrandName,
		CoverImg:   resp.CoverImg,
		ActionCode: resp.ActionCode,
		Desc:       resp.Desc,
		Link:       resp.Link,
		ArtworkNum: len(resp.Artworks),
	}
	if brandResp != nil {
		result.BrandLogo = brandResp.Logo
	}

	for _, v := range resp.Artworks {
		v := v
		result.ArtworkList = append(result.ArtworkList, dto.Artwork{
			ArtworkUid:  v.ArtworkUid,
			Tfnum:       v.Tfnum,
			ArtworkName: v.ArtworkName,
			HdPic:       v.HdPic,
			Ruler:       v.Ruler,
			Length:      v.Length,
			Width:       v.Width,
			ArtistName:  v.ArtistName,
			ArtistUid:   v.ArtistUid,
			Abstract:    v.Abstract,
			//Tnum:        v.Tnum,
			Price:    v.Price,
			Currency: v.Currency,
		})
	}

	if resp.ActionCode == "auction" {
		result.Auction = &fenghe.AuctionRequest{}

		res, err := service.FengheProvider.DetailAuction(c, &fenghe.AuctionDetail{Uuid: resp.AuctionUuid})
		if err != nil {
			service.Error(c, e.Error, err)
			return
		}

		err = copier.Copy(&result.Auction, &res)
		fmt.Println(1111111111111, err)
	}

	return

}
