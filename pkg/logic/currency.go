package logic

import (
	"github.com/fonchain_enterprise/client-auction/pkg/common/cache"
	"github.com/shopspring/decimal"
	"time"
)

func GetGlobalCurrencyMoney(moneyRmb string, currency string) string {

	rate := getCurrencyExchangeRate(currency, true)

	moneyDe, _ := decimal.NewFromString(moneyRmb)
	rateDe, err := decimal.NewFromString(rate)
	if err != nil || rateDe.IsZero() {
		return ""
	}

	newMoney := moneyDe.Div(rateDe).Mul(decimal.NewFromInt(100)).Round(2).String()

	return newMoney
}

// GetCurrencyMoney 移动端使用使用
func GetCurrencyMoney(moneyRmb string, currency string) string {
	if moneyRmb == "" {
		return ""
	}

	rate := getCurrencyExchangeRate(currency, false)

	moneyDe, _ := decimal.NewFromString(moneyRmb)
	rateDe, _ := decimal.NewFromString(rate)

	newMoney := moneyDe.Div(rateDe).Mul(decimal.NewFromInt(100)).Round(2).String()

	return newMoney
}

// GetCurrencyMoneyFromRate 提前获取汇率
func GetCurrencyMoneyFromRate(moneyRmb string, rate string) string {
	if moneyRmb == "" {
		return ""
	}

	moneyDe, _ := decimal.NewFromString(moneyRmb)
	rateDe, _ := decimal.NewFromString(rate)

	newMoney := moneyDe.Div(rateDe).Mul(decimal.NewFromInt(100)).Round(2).String()

	return newMoney
}

// GetCurrencyMoneyAuction 对拍卖价格进行记录，方便拍卖的时候汇率自动转换
func GetCurrencyMoneyAuction(moneyRmb string, currency string, userId uint32) string {
	rate := getCurrencyExchangeRate(currency, false)

	moneyDe, _ := decimal.NewFromString(moneyRmb)
	rateDe, _ := decimal.NewFromString(rate)

	newMoney := moneyDe.Div(rateDe).Mul(decimal.NewFromInt(100)).Round(2).String()

	cache.RedisClient.HSet(cache.GetTotalRateHash(currency, userId), newMoney, moneyRmb)
	cache.RedisClient.Expire(cache.GetTotalRateHash(currency, userId), 48*time.Hour)

	return newMoney
}
