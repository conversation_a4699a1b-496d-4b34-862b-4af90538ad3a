package culture

import (
	"context"
	"fmt"
	"github.com/fonchain_enterprise/client-auction/api/account"
	"github.com/fonchain_enterprise/client-auction/api/chain"
	backendSeries "github.com/fonchain_enterprise/client-auction/api/series"
	"github.com/fonchain_enterprise/client-auction/pkg/common/cache"
	"github.com/fonchain_enterprise/client-auction/pkg/service"
	"github.com/pkg/errors"
	"github.com/shopspring/decimal"
)

type CurrencyInfo struct {
	Currency string `json:"currency"`
	Price    string `json:"price"`
}

// SyncCast 同步新商品铸造
func SyncCast() {
	castCulture()
}

// SynCirculation 同步商品流转
func SynCirculation() {
	// 从redis获取
	for i := 1; i <= 100; i++ {
		circluctionUuid := cache.RedisClient.RPop(cache.GetCirQueue()).Val()
		trNft(circluctionUuid)
	}
}

// CirculationOverTime 流转超时回滚
func CirculationOverTime() {
	overTimeCirculate()
}

// overTimeCirculate 超时重新设置
func overTimeCirculate() {
	ctx := context.Background()
	_, err := service.BackendSeriesProvider.OverTimeCirculationList(ctx, &backendSeries.CirculationListReq{})
	if err != nil {
		return
	}

}

// castCulture 铸造文创用品
func castCulture() {
	ctx := context.Background()
	orderObjs, err := service.BackendSeriesProvider.CultureArtworkOrderList(ctx, &backendSeries.CultureArtworkOrderListReq{Status: 2, IsEmptyHash: true, PageSize: 9999, Page: 1})
	if err != nil {
		return
	}
	for _, k := range orderObjs.List {
		_, e := cashNft(k.Uuid)
		if e != nil {
			fmt.Println("1-----------", e)
			break
		}
	}
}

// cashNft 铸造nft 帐号绑定
func cashNft(orderUuid string) (string, error) {
	var hash = ""
	ctx := context.Background()

	orderObj, err := service.BackendSeriesProvider.CultureArtworkOrderDetail(ctx, &backendSeries.CultureArtworkOrderDetailReq{OrderUuid: orderUuid})
	if err != nil {
		return hash, err
	}

	if orderObj == nil {
		return hash, errors.New("藏品微服务查询为空")
	}

	userInfo, err := service.AccountProvider.PrivacyInfo(ctx, &account.PrivacyInfoRequest{ID: uint64(orderObj.NowUserId)})
	if err != nil {
		return hash, err
	}

	if userInfo == nil {
		return hash, errors.New("用户信息查询为空")
	}

	cultureArtwork, err := service.BackendSeriesProvider.CultureArtworkDetail(ctx, &backendSeries.CultureArtworkDetailReq{Uuid: orderObj.SeriesCulturalArtworkUuid})
	if err != nil {
		return hash, err
	}

	if cultureArtwork == nil {
		return hash, errors.New("藏品微服务查询为空")
	}

	type TxReceipt struct {
		SerialNum       string `json:"serialNum" binding:"required"`       //交易流水号，跨链交易hash
		PaymentPlatform string `json:"paymentPlatform" binding:"required"` //资金转移平台
		Price           uint64 `json:"price" binding:"required"`           //价格
	}

	priceTemp, err := decimal.NewFromString(cultureArtwork.Price)

	if err != nil {
		return hash, errors.New("初始化price转化失败")
	}

	sellPriceTemp, err := decimal.NewFromString(cultureArtwork.Price)
	if err != nil {
		return hash, errors.New("初始化sellerPrice转化失败")
	}

	price := priceTemp.Mul(decimal.NewFromInt(10000))
	sellPrice := sellPriceTemp.Mul(decimal.NewFromInt(10000))

	txReceipt := &chain.TxReceipt{
		SerialNum:       orderUuid,
		PaymentPlatform: "location",
		Price:           uint64(price.IntPart()),
	}

	nftArtWork := &chain.NFT{
		DciID:             cultureArtwork.Uuid,
		DciHash:           cultureArtwork.Hash, //铸造来源的hash
		DciName:           cultureArtwork.Name,
		DciMaker:          cultureArtwork.BrandAddress,
		DciUrl:            cultureArtwork.BriefImage,
		DciOwner:          userInfo.Account,
		DciType:           1,
		DciCreateProperty: 1,
		DciFrom:           []string{cultureArtwork.Hash},
		DciAttribute:      1,
		LastTransaction:   cultureArtwork.Uuid, //最后交易的hash
		AverageTxPrice:    uint64(price.IntPart()),
		CurTxPrice:        uint64(sellPrice.IntPart()),
		TReceipt:          []*chain.TxReceipt{txReceipt},
		ContractNum:       "",
		CertificateNum:    "",
		Introduction:      cultureArtwork.Detail,
		Length:            0,
		Width:             0,
		Url:               cultureArtwork.BriefImage,
		CollectionName:    cultureArtwork.Name,
		FolderImg:         "",
		PriceJson:         "",
	}

	cashReq := chain.CastNFTRequest{
		ArtWork: nftArtWork,
	}

	res, err := service.ChainProvider.CastNFT(context.Background(), &cashReq)

	if res.Hash != "" {
		_, err1 := service.BackendSeriesProvider.UpdateCastCultureArtworkOrder(ctx, &backendSeries.UpdateCastCultureArtworkOrderReq{OrderUuid: orderObj.Uuid, TransactionHash: res.Hash})
		if err1 != nil {
			return hash, errors.Wrap(err, "更新铸造订单失败")
		}
		hash = res.Hash
	}

	return hash, err
}

// trNft
func trNft(uuid string) (string, error) {
	var hash = ""
	ctx := context.Background()

	circulationObj, err := service.BackendSeriesProvider.CirculationDetail(ctx, &backendSeries.CirculationDetailReq{Uuid: uuid})
	if err != nil {
		return hash, err
	}

	if circulationObj == nil {
		return hash, errors.New("藏品微服务查询为空")
	}

	orderObj, err := service.BackendSeriesProvider.CultureArtworkOrderDetail(ctx, &backendSeries.CultureArtworkOrderDetailReq{OrderUuid: circulationObj.Uuid})
	if err != nil {
		return hash, err
	}

	sellerUserInfo, err := service.AccountProvider.PrivacyInfo(ctx, &account.PrivacyInfoRequest{ID: uint64(circulationObj.SenderId)})
	if err != nil {
		return hash, err
	}

	if sellerUserInfo == nil {
		return hash, errors.New("用户信息查询为空")
	}

	receiveUser, err := service.AccountProvider.PrivacyInfo(ctx, &account.PrivacyInfoRequest{ID: uint64(circulationObj.ReceiverId)})
	if err != nil {
		return hash, err
	}

	if receiveUser == nil {
		return hash, errors.New("用户信息查询为空")
	}

	cultureArtwork, err := service.BackendSeriesProvider.CultureArtworkDetail(ctx, &backendSeries.CultureArtworkDetailReq{Uuid: orderObj.SeriesCulturalArtworkUuid})
	if err != nil {
		return hash, err
	}

	if cultureArtwork == nil {
		return hash, errors.New("藏品微服务查询为空")
	}

	//查询交易商
	seller := &chain.Seller{
		Address:  sellerUserInfo.Account,
		Mnemonic: sellerUserInfo.MnemonicWords,
	}

	req := &chain.TransferRequest{
		BuyAddress:  receiveUser.Account,
		Seller:      seller,
		Hash:        orderObj.Hash,
		ContractNum: circulationObj.Uuid,
		Platform:    1,  //支付宝
		SerialNum:   "", //非跨链交易
	}
	fmt.Println(req)

	_, err = service.ChainProvider.Transfer(ctx, req) //自动落锁 自动解锁

	/*
		_, err1 := service.BackendSeriesProvider.UpdateCirculationHash(ctx, &backendSeries.UpdateCirculationHashReq{Uuid: circulationObj.Uuid, Hash: ttRes.})
		if err1 != nil {
			return hash, errors.Wrap(err, "更新铸造订单失败")
		}

	*/

	return "", err
}
