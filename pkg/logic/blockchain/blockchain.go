package blockchain

import (
	"encoding/json"
	"errors"
	"fmt"
	"github.com/bytedance/sonic"
	appconfig "github.com/fonchain_enterprise/client-auction/cmd/config"
	"github.com/fonchain_enterprise/client-auction/pkg/utils/httputils"
	"net/url"
)

// 上链
/*

	http请求方式: POST
    http://*************:8000/store?ledger=main
*/

const (
	BlockStoreRoute       = "/store"               //新增存证
	BlockHeightRoute      = "/gettransactionblock" //获取区块高度
	BlockStoreDetailRoute = "/getblockbyheight"    //获取区块信息
)

var (
	FailToGet = "获取失败"
)

type StoreRes struct {
	State   uint64     `json:"state"`
	Message string     `json:"message"`
	Data    *StoreData `json:"data"`
}

type StoreData struct {
	Figure string `json:"Figure"`
	OK     bool   `json:"OK"`
}

type StoreHeightRes struct {
	State   uint64 `json:"state"`
	Message string `json:"message"`
	Data    int64  `json:"data"`
}

type StoreReq struct {
	Data string `json:"data"`
}

type StoreDetailRes struct {
	State   uint64       `json:"state"`
	Message string       `json:"message"`
	Data    *StoreDetail `json:"data"`
}

type StoreDetail struct {
	Header HeaderInfo `json:"header"`
	Txs    []string   `json:"txs"`
}

type HeaderInfo struct {
	Height          int64  `json:"height"`
	Timestamp       int64  `json:"timestamp"`
	BlockHash       string `json:"blockHash"`
	PreviousHash    string `json:"previousHash"`
	WorldStateRoot  string `json:"worldStateRoot"`
	TransactionRoot string `json:"transactionRoot"`
}

// CreateStore 新增存证
func CreateStore(req *StoreReq) (*StoreData, error) {
	var (
		resObj    *StoreRes
		err       error
		res       []byte
		code      int
		paramBody []byte
	)
	paramBody, _ = sonic.Marshal(req)
	fmt.Println("addBlockReq------:", paramBody)
	header := map[string]string{
		"content-type": "application/json",
	}
	code, res, err = httputils.Post(appconfig.AppConfig.Fonchain.Path+BlockStoreRoute+"?ledger="+appconfig.AppConfig.Fonchain.Ledger, header, paramBody)
	fmt.Println("addBlockRes------:", code, res, err)
	if err = json.Unmarshal(res, &resObj); err != nil {
		fmt.Println("addBlockRes------:", err, code, res)
		return nil, err
	}
	if code != 200 || resObj.State != 200 {
		fmt.Println("addBlockRes------:", resObj.State, resObj.Data)
		return nil, errors.New(resObj.Message)
	}
	return resObj.Data, err
}

// 查询区块高度
/*
	http请求方式: GET
	http://*************:8000/gettransactionblock?hashData=rV%2FROwIpHKwR8fdgD%2FtB0pYmlZ9lmGoJarC2%2B8oPzkw%3D&ledger=main
*/

// QueryStoreHeight 查询区块高度
func QueryStoreHeight(txID string) (*StoreHeightRes, error) {

	var (
		resObj *StoreHeightRes
		err    error
		res    []byte
		code   int
	)
	code, res, err = httputils.Get(
		fmt.Sprintf(appconfig.AppConfig.Fonchain.Path+BlockHeightRoute+"?hashData=%s&ledger="+appconfig.AppConfig.Fonchain.Ledger, url.QueryEscape(txID)), nil)
	if err = json.Unmarshal(res, &resObj); err != nil {
		return nil, err
	}
	if code != 200 {
		return nil, errors.New(FailToGet)
	}
	return resObj, err
}

// 查询区块详情
/*
    http请求方式: GET
	http://*************:8000/getblockbyheight?number=97891&ledger=maingi
*/

// QueryStoreDetail 查询区块详情
func QueryStoreDetail(height int64) (*StoreDetailRes, error) {
	var (
		resObj *StoreDetailRes
		err    error
		res    []byte
		code   int
	)
	code, res, err = httputils.Get(
		fmt.Sprintf(appconfig.AppConfig.Fonchain.Path+BlockStoreDetailRoute+"?number=%d&ledger="+appconfig.Config{}.Fonchain.Ledger, height), nil)
	if err = json.Unmarshal(res, &resObj); err != nil {
		return nil, err
	}
	if code != 200 {
		return nil, errors.New(FailToGet)
	}
	return resObj, err
}
