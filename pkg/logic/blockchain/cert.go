package blockchain

import (
	"bytes"
	"crypto/sha256"
	"encoding/base64"
	"encoding/hex"
	"fmt"
	appconfig "github.com/fonchain_enterprise/client-auction/cmd/config"
	"image"
	"image/color"
	"image/png"
	"io"
	"net/http"
	"time"

	"github.com/fogleman/gg"
	"github.com/fonchain_enterprise/client-auction/pkg/common/utils"
)

func MakeCertNum(txID string) string {
	// 对 TxID 进行处理，生成证书编号
	// 250711 + TXID 取 6位

	// 解码 Base64
	data, err := base64.StdEncoding.DecodeString(txID)
	if err != nil {
		panic(err)
	}

	// 计算 SHA256 哈希
	hash := sha256.Sum256(data)
	hashHex := hex.EncodeToString(hash[:])

	// 取哈希的前6位
	shortened := hashHex[:6]

	return time.Now().Format("060102") + shortened
}

func MakeCertUrl(uuid, certNum, txID, photoUrl string) []byte {
	// A4 尺寸 (300 DPI): 2480 x 3508 像素
	const (
		width  = 2480
		height = 3508
	)

	//  创建透明底 A4 大小的图片
	dc := gg.NewContext(width, height)
	dc.SetColor(color.Transparent)
	dc.Clear()

	// 设置字体颜色为黑色
	dc.SetColor(color.Black)

	// 尝试加载字体 (跨平台字体路径)
	fontPaths := []string{
		"./static/simfang.ttf", // Linux
	}

	fontLoaded := false
	for _, fontPath := range fontPaths {
		if err := dc.LoadFontFace(fontPath, 78); err == nil {
			fontLoaded = true
			break
		}
	}

	if !fontLoaded {
		fmt.Printf("Warning: Could not load any font, using default\n")
	}

	// 添加证书编号 certNum 位置 x: 131px, y: 2522 + 27px = 2549px
	dc.DrawString(certNum, 164, 2535)

	// 添加交易ID txID 位置 x: 131px, y: 2803 + 57px = 2860px
	dc.DrawString(txID, 164, 2810)

	// 添加照片 photo 位置 x: 136px, y: 3127 + 108.57px = 3235.57px
	// 大小 宽: 425.89px, 高: 217.14px
	if photoUrl != "" {
		if photoImg, err := loadImageFromURL(photoUrl); err == nil {
			// 调整图片大小
			resizedPhoto := resizeImage(photoImg, 426, 217)
			dc.DrawImage(resizedPhoto, 164, 3095)
		} else {
			fmt.Printf("Failed to load photo: %v\n", err)
		}
	}

	// 生成二维码并添加 位置 x: 2137px, y: 3131 + 106.635px = 3237.635px
	// 大小 宽: 213.27px, 高: 213.27px
	//qrContent := fmt.Sprintf("%s/api/v1/fenghe/photo-wall/query?uuid=%s", uuid)
	qrContent := fmt.Sprintf("%s/#/pages/curOrder/phoneCert?uuid=%s", appconfig.AppConfig.Service.ShopHost, uuid)
	if qrBase64, err := utils.GenerateQRCode(qrContent); err == nil {
		if qrImg, err := decodeBase64Image(qrBase64); err == nil {
			// 调整二维码大小
			resizedQR := resizeImage(qrImg, 213, 213)
			dc.DrawImage(resizedQR, 2120, 3095)
		} else {
			fmt.Printf("Failed to decode QR code: %v\n", err)
		}
	} else {
		fmt.Printf("Failed to generate QR code: %v\n", err)
	}

	// 将图片编码为 base64
	buf := new(bytes.Buffer)
	if err := png.Encode(buf, dc.Image()); err != nil {
		fmt.Printf("Failed to encode image: %v\n", err)
		return nil
	}

	// 返回 base64 编码的图片
	return buf.Bytes()
}

// loadImageFromURL 从URL加载图片
func loadImageFromURL(url string) (image.Image, error) {
	resp, err := http.Get(url)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("HTTP error: %d", resp.StatusCode)
	}

	img, _, err := image.Decode(resp.Body)
	if err != nil {
		return nil, err
	}

	return img, nil
}

// decodeBase64Image 解码base64图片
func decodeBase64Image(base64Str string) (image.Image, error) {
	data, err := base64.StdEncoding.DecodeString(base64Str)
	if err != nil {
		return nil, err
	}

	img, _, err := image.Decode(bytes.NewReader(data))
	if err != nil {
		return nil, err
	}

	return img, nil
}

// resizeImage 调整图片大小
func resizeImage(img image.Image, width, height int) image.Image {
	// 创建新的画布
	dc := gg.NewContext(width, height)

	// 计算缩放比例，保持宽高比
	bounds := img.Bounds()
	imgWidth := bounds.Dx()
	imgHeight := bounds.Dy()

	scaleX := float64(width) / float64(imgWidth)
	scaleY := float64(height) / float64(imgHeight)
	scale := scaleX
	if scaleY < scaleX {
		scale = scaleY
	}

	newWidth := int(float64(imgWidth) * scale)
	newHeight := int(float64(imgHeight) * scale)

	// 居中绘制
	x := (width - newWidth) / 2
	y := (height - newHeight) / 2

	dc.DrawImageAnchored(img, x+newWidth/2, y+newHeight/2, 0.5, 0.5)

	return dc.Image()
}

func GetBase64FromUrl(imageURL string) (string, error) {
	resp, err := http.Get(imageURL)
	if err != nil {
		return "", err
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return "", fmt.Errorf("HTTP error: %d", resp.StatusCode)
	}

	resultBody, err := io.ReadAll(resp.Body)
	if err != nil {
		return "", err
	}

	return base64.StdEncoding.EncodeToString(resultBody), nil
}
