package blockchain

import (
	"testing"
)

func TestMakeCertNum(t *testing.T) {
	txID := "dGVzdC10eGlk" // base64 encoded "test-txid"
	certNum := MakeCertNum(txID)

	if len(certNum) != 12 { // 6 digits for date + 6 digits from hash
		t.<PERSON><PERSON><PERSON>("Expected certificate number length to be 12, got %d", len(certNum))
	}

	t.<PERSON>gf("Generated certificate number: %s", certNum)
}

func TestMakeCertUrl(t *testing.T) {
	uuid := "test-uuid-123"
	certNum := "25071112345"
	txID := "test-transaction-id"
	photoUrl := "" // 空的照片URL以避免网络请求

	result := MakeCertUrl(uuid, certNum, txID, photoUrl)

	if result == nil {
		t.<PERSON><PERSON>("Expected non-empty result from MakeCertUrl")
	}

	if len(result) < 100 {
		t.<PERSON><PERSON><PERSON>("Expected base64 result to be longer, got length: %d", len(result))
	}

	t.<PERSON>gf("Generated certificate base64 length: %d", len(result))
	t.Logf("First 100 characters: %s...", result[:min(100, len(result))])
}

func min(a, b int) int {
	if a < b {
		return a
	}
	return b
}
