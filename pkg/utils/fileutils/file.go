package fileutils

import (
	"fmt"
	"go.uber.org/zap"
	"io"
	"net/http"
	"os"
	"strings"
)

// CreateDirPath 递归创建文件夹
func CreateDirPath(path string) (err error) {
	if _, err = os.Stat(path); os.IsNotExist(err) {
		if err = os.Mkdir<PERSON>ll(path, os.ModePerm); err != nil {
			return
		}
	}
	return
}

// SaveUrlFileDisk 保存图片到本地
func SaveUrlFileDisk(url string, path string, filename string) (fullPath string, err error) {
	if err = CreateDirPath(path); err != nil {
		zap.L().Error("SaveUrlFileDisk err ", zap.Error(err))
		return
	}
	if filename == "" {
		stepName := strings.Split(url, "/")
		if len(stepName) > 1 {
			filename = stepName[len(stepName)-1]
		}
	}
	resp, err := http.Get(url)
	if err != nil {
		zap.L().Error("SaveUrlFileDisk err ", zap.Error(err))
		return
	}
	defer func() {
		if _err := recover(); _err != nil {
			zap.L().Error("SaveUrlFileDisk err ", zap.Any("_err", err))
		}
		resp.Body.Close()
	}()
	bytes, err := io.ReadAll(resp.Body)
	fullPath = fmt.Sprintf("%s/%s", path, filename)
	// 写入数据
	err = os.WriteFile(fullPath, bytes, 0777)
	return
}
