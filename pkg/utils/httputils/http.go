package httputils

import (
	"bytes"
	"errors"
	"go.uber.org/zap"
	"io"
	"net/http"
	"time"
)

func Get(url string, header map[string]string) (httpCode int, resultBody []byte, err error) {
	req, err := http.NewRequest("GET", url, nil)
	if err != nil {
		zap.L().Error("get err", zap.Error(err))
		err = errors.New("http请求异常")
		return
	}
	for key, val := range header {
		req.Header.Add(key, val)
	}
	client := &http.Client{
		Timeout: 2 * time.Second,
	}
	resp, err := client.Do(req)
	if err != nil {
		zap.L().Error("get err client do", zap.Error(err))
		err = errors.New("http请求异常")
		return
	}
	defer resp.Body.Close()
	httpCode = resp.StatusCode
	resultBody, _ = io.ReadAll(resp.Body)
	zap.L().Info("get info", zap.Any("url", url), zap.Any("result", string(resultBody)))
	return
}

// PostFormAndHeader
//
//	@Description: POST自定义header
//	@param url 请求地址
//	@param header header
//	@param data 参数
func Post(url string, header map[string]string, paramJson []byte) (httpCode int, respBody []byte, err error) {
	zap.L().Info("PostFormAndHeader", zap.Any("url", url), zap.Any("header", header), zap.Any("paramJson", paramJson))
	req, err := http.NewRequest("POST", url, bytes.NewBuffer(paramJson))
	if err != nil {
		zap.L().Error("post err NewRequest", zap.Error(err))
		err = errors.New("创建http错误")
		return
	}
	if header != nil || len(header) != 0 {
		for key, val := range header {
			if key != "" && val != "" {
				req.Header.Set(key, val)
			}
		}
	}
	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		zap.L().Error("post err client.Do", zap.Error(err))
		err = errors.New("http请求异常")
		return
	}
	defer resp.Body.Close()
	httpCode = resp.StatusCode
	respBody, err = io.ReadAll(resp.Body)
	if err != nil {
		zap.L().Error("post err client.Do", zap.Error(err))
		err = errors.New("http请求异常")
		return
	}
	zap.L().Info("get info", zap.Any("url", url), zap.Any("paramJson", string(paramJson)), zap.Any("result", string(respBody)))
	return
}
