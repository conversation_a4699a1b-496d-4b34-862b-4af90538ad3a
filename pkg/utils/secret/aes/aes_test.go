package aes

import (
	"encoding/hex"
	"testing"
)

func TestSecret(t *testing.T) {
	textA := "我是王德法"
	textABytes := []byte(textA)

	token, err := AesEcrypt(textABytes, PwdKey)
	if err != nil {
		t.<PERSON><PERSON>("加密错误 %s", err.<PERSON><PERSON><PERSON>())
	}

	//fmt.Println(token)
	tokenStr := hex.EncodeToString(token)

	tokenByte, err := hex.DecodeString(tokenStr)
	if err != nil {
		t.<PERSON><PERSON><PERSON>("hex加密错误 %s", err.<PERSON>rror())
	}

	res, err := AesDeCrypt(tokenByte, PwdKey)
	if err != nil {
		t.<PERSON><PERSON>("解密错误 %s", err.<PERSON><PERSON><PERSON>())
	}

	if string(res) == textA {
		t.Logf("加解密通过 %s", string(res))
	} else {
		t.<PERSON><PERSON><PERSON>("解析内容不符合")
	}

}
