package aes

import (
	"fmt"
	"reflect"
	"testing"
)

func TestCreateSecret(t *testing.T) {
	type args struct {
		userMsg CommonSecret
		pwdKey  []byte
	}
	aesSetting := AesSetting{Pwd: []byte("tyfon918tyfon918")}
	tests := []struct {
		name    string
		args    args
		want    string
		wantErr bool
	}{
		// TODO: Add test cases.
		{args: args{pwdKey: []byte("tyfon918tyfon918"), userMsg: CommonSecret{Id: 123, Name: "张三"}}},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := aesSetting.CreateSecret(tt.args.userMsg)
			fmt.Println("1---", got, err)

			user1, err1 := aesSetting.EncryptionSecret(got)
			fmt.Println("2---", user1, err1)

		})
	}

}

func TestCreateSecretDemo(t *testing.T) {
	type args struct {
		id   uint
		name string
	}
	tests := []struct {
		name    string
		args    args
		want    string
		wantErr bool
	}{
		// TODO: Add test cases.
		{args: args{id: 1, name: "卡德加"}},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := CreateSecretDemo(tt.args.id, tt.args.name)

			fmt.Println(1, got, err)

			got2, err2 := EncryptionSecret(got)

			fmt.Println(1, got2, err2)
		})
	}
}

func TestEncryptionSecret(t *testing.T) {
	type args struct {
		token string
	}
	tests := []struct {
		name    string
		args    args
		want    *CommonSecret
		wantErr bool
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := EncryptionSecret(tt.args.token)
			if (err != nil) != tt.wantErr {
				t.Errorf("EncryptionSecret() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("EncryptionSecret() got = %v, want %v", got, tt.want)
			}
		})
	}
}
