package secret

import (
	"encoding/hex"
	"errors"
	"fmt"
	"github.com/fonchain_enterprise/client-auction/pkg/utils/secret/aes"
	"strings"
)

func getToken(token string) (string, error) {

	if strings.Contains(token, "(~!@)") == false {
		return "", errors.New("解析错误")
	}

	str1 := strings.Split(token, "(~!@)")

	if len(str1) != 3 {
		return "", errors.New("解析数量错误")
	}

	return str1[2], nil
}

func GetJwtFromStrAdm(authorization string) (string, error) {

	tokenByte, err := hex.DecodeString(authorization)
	if err != nil {
		return "", err
	}
	defer func() {
		if msg := recover(); msg != nil {
			fmt.Println("panic信息:", msg, "---recover恢复---")
		}
	}()

	token, err := aes.AesDeCrypt(tokenByte, aes.PwdKey)

	if err != nil {
		return "", err
	}

	//解密下
	jwt, err := getToken(string(token))

	if err != nil {
		return "", err
	}

	return jwt, nil

}
