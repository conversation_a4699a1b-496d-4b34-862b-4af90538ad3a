package secret

import (
	"encoding/hex"
	"encoding/json"
	"errors"
	"fmt"
	"github.com/fonchain_enterprise/client-auction/pkg/utils/secret/aes"
	"time"
)

type UserNewSecret struct {
	ID       uint64
	CreateAt time.Time
}

func GetPositionCode(positionName string) string {
	code := "other"
	positionMap := map[string]string{"宣传部": "xcb", "鉴证科": "jzk", "经纪人": "jjr", "普通用户": "com"}

	if v, ok := positionMap[positionName]; ok {
		code = v
	}

	return code
}

func CombineSecret(token string) (string, error) {
	b, err := aes.AesEcrypt([]byte(token), aes.PwdKey)
	if err != nil {
		return "", errors.New("解析错误")
	}

	return hex.EncodeToString(b), nil
}

func GetJwtFromStr(authorization string) (string, error) {

	tokenByte, err := hex.DecodeString(authorization)
	if err != nil {
		return "", err
	}
	defer func() {
		if msg := recover(); msg != nil {
			fmt.Println("panic信息:", msg, "---recover恢复---")
		}
	}()

	token, err := aes.AesDeCrypt(tokenByte, aes.PwdKey)

	if err != nil {
		return "", err
	}

	return string(token), nil

}
func SendMsgSecret(userMsg UserNewSecret) (string, error) {
	v, err := json.Marshal(userMsg)
	if err != nil {
		return "", err
	}

	b, err := aes.AesEcrypt(v, aes.PwdKey)
	if err != nil {
		return "", errors.New("解析错误")
	}

	return hex.EncodeToString(b), nil
}
func SendMsgEncryption(token string) (userMsg *UserNewSecret, err error) {
	tokenByte, err := hex.DecodeString(token)
	if err != nil {
		return nil, err
	}
	fmt.Println(1)
	defer func() {
		if msg := recover(); msg != nil {
			fmt.Println("panic信息:", msg, "---recover恢复---")
			err = errors.New("token 解析失败")
		}
	}()

	fmt.Println(2)
	res, err := aes.AesDeCrypt(tokenByte, aes.PwdKey)
	if err != nil {
		return nil, err
	}

	err = json.Unmarshal(res, &userMsg)

	if err != nil {
		return nil, err
	}

	return userMsg, err
}
