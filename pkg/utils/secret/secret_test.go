package secret

import (
	"encoding/json"
	"fmt"
	"github.com/fonchain_enterprise/client-auction/pkg/model/vo"
	"github.com/google/uuid"
	"math/rand"
	"testing"
	"time"
)

func TestSecret(t *testing.T) {
	tempUrl := fmt.Sprintf("/#/pages/modal3D/3d?sign=2bd725aaa312e48e18bde643f73b28453eb3d720739d9d12142c69f2237a7acdab5b369b1af64a8baa8e6e66db31c7919709b702b3ea7c2e49fb654eda42585d&time=%s&client=%s&nonce=%d",
		time.Now().Format(time.DateTime),
		"tf",
		rand.Intn(999999),
	)

	fmt.Println(tempUrl)
	cultureArtworkReq := vo.CultureArtworkReq{Uuid: "534b0753-66c5-2231-9b44-4f9eda961b70", Num: 1}
	s, _ := json.Marshal(cultureArtworkReq)
	fmt.Println(string(s))

	str, err := CombineSecret(string(s))
	fmt.Println(str, err)

	s1, e1 := GetJwtFromStr(str)
	fmt.Println(s1, e1)

}

func TestSecretInfo(t *testing.T) {
	token := "https://appointtest.szjixun.cn/api/appointment/xx?name=12"
	str, err := CombineSecret(token)
	if err != nil {
		t.Errorf("组合加密错误 %s", err.Error())
		return
	}

	t.Logf("加密之后 %s", str)

	str = "b66054d4f8a80fd93f603224e16999b7ff9db376a89af5919c99bc45a9c00e760163364c3ef3cd2e1370a90dcac5a68d1af25895841dbe71069a9f4f90445b494b35958bb588441f74cf15932a73ef5f379871fc884982e36a72d3cdc83ad96e085288eecb0df88982aa30cb76469d404f210abe0283e52f2e1b602dfb88a0bb23acaf249ea2aef4b58f4ccaa2ca73dd62be30396431982303995e77d9a0fff14a1c3b27407c19c890687b9a0721ac3d1405981d69f3da64edb0923f73be7ed239757c031c02f8df2c3daa7ffff11bf8e0de37dd627730a14a919bd8c57fca4e6c11117fe68ed1930c8bfafe1cae0e6c3ee9770018865b983e2ad4ef404ef76001a186b5be0e9bcd6827587df5f124da5b1d77c94003828509753e4006e6995ce667c5f8a2cf3ad0a44da5245d3dcd6381db5f2d0ed715779c6ded24906ab943"
	tokenExample, err := GetJwtFromStr(str)
	fmt.Println()
	fmt.Println()
	fmt.Println(tokenExample)
	fmt.Println()
	fmt.Println()
	if err != nil {
		t.Errorf("获取token组合加密错误 %s", err.Error())
		return
	}

	if tokenExample != token {
		t.Errorf("解密不符合 %s", "解析内容不匹配")
	}

	t.Logf("加解密通过 %s", tokenExample)

}

func TestGetJwtFromStr(t *testing.T) {
	got, err := GetJwtFromStr("e436d9c8ad2a6bd80bccc74cce0ed87772f03f7427533f55c4c41672dc4a0dfb18ca76603a7e91083e3dc35b01e88762e88131ec8875162352236600b6dc9b4f")
	if err != nil {
		t.Errorf("错误:%+v", err)
		return
	}
	fmt.Println(got)
	got, err = GetJwtFromStr("d1c1608b444547865a783f0ac2a34045ceb6bf3605127f074af968302300b17baf30b19696909fd64d3ec2207f47e7b1cd99c3f8de73b740fc83df98cef6db6e")
	if err != nil {
		t.Errorf("错误:%+v", err)
		return
	}
	fmt.Println(got)

	got, err = GetJwtFromStr("92916269f00a41d367a2f34cbaac9ebe6f04490dba7eed8eb57db2ca07f15ee4051f24fcdbb7fe27e7b36b57c9f3fa9e6ce38dfde004bbab51578639a576b15b")
	if err != nil {
		t.Errorf("错误:%+v", err)
		return
	}
	fmt.Println(got)

}
