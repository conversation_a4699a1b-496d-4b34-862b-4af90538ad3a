package wechat

import (
	"context"
	"crypto/rsa"
	"crypto/x509"
	"encoding/json"
	"fmt"
	"os"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/wechatpay-apiv3/wechatpay-go/core"
	"github.com/wechatpay-apiv3/wechatpay-go/core/auth/verifiers"
	"github.com/wechatpay-apiv3/wechatpay-go/core/downloader"
	"github.com/wechatpay-apiv3/wechatpay-go/core/notify"
	"github.com/wechatpay-apiv3/wechatpay-go/core/option"
	"github.com/wechatpay-apiv3/wechatpay-go/services/payments"
	"github.com/wechatpay-apiv3/wechatpay-go/services/payments/jsapi"
	"github.com/wechatpay-apiv3/wechatpay-go/utils"
)

type Pay struct {
	Description      string `json:"description"`
	OutTradeNo       string `json:"outTradeNo"`
	Cent             int64  `json:"cent"`
	OpenID           string `json:"openID"`
	GhID             string `json:"ghID"`
	AppId            string `json:"appId"`
	PayNotifyCodeUrl string
}

type WxPay struct {
	MchID                      string
	MchCertificateSerialNumber string
	MchAPIv3Key                string
}

func CreatePay(ctx *gin.Context, pay *Pay, wxConfig WxPay) (*jsapi.PrepayWithRequestPaymentResponse, error) {
	resp := &jsapi.PrepayWithRequestPaymentResponse{}
	//0 获取微信公众号信息

	mchPrivateKey, err := loadPrivateKeyWithPath()
	if err != nil {
		return resp, err
	}

	// 使用商户私钥等初始化 client，并使它具有自动定时获取微信支付平台证书的能力
	opts := []core.ClientOption{
		option.WithWechatPayAutoAuthCipher(wxConfig.MchID, wxConfig.MchCertificateSerialNumber, mchPrivateKey, wxConfig.MchAPIv3Key),
	}
	client, err := core.NewClient(ctx, opts...)
	if err != nil {
		return resp, err
	}

	svc := jsapi.JsapiApiService{Client: client}
	fmt.Println(pay.AppId)
	fmt.Println(wxConfig.MchID)
	fmt.Println(wxConfig.MchCertificateSerialNumber)
	fmt.Println(wxConfig.MchAPIv3Key)
	resp, result, err := svc.PrepayWithRequestPayment(ctx,
		jsapi.PrepayRequest{
			Appid:         core.String(pay.AppId),
			Mchid:         core.String(wxConfig.MchID),
			Description:   core.String(pay.Description),
			OutTradeNo:    core.String(pay.OutTradeNo),
			TimeExpire:    core.Time(time.Now()),
			NotifyUrl:     core.String(pay.PayNotifyCodeUrl),
			SupportFapiao: core.Bool(false),
			Amount: &jsapi.Amount{
				Currency: core.String("CNY"),
				Total:    core.Int64(pay.Cent),
			},
			Payer: &jsapi.Payer{
				Openid: core.String(pay.OpenID),
			},
			SceneInfo: &jsapi.SceneInfo{
				PayerClientIp: core.String(ctx.ClientIP()),
				StoreInfo: &jsapi.StoreInfo{
					Id: core.String("0001"),
				},
			},
			SettleInfo: &jsapi.SettleInfo{
				ProfitSharing: core.Bool(false),
			},
		},
	)

	fmt.Println("status=%d resp=%s", result.Response.StatusCode, resp)
	return resp, err

}

// WechatNotifyMsg 支付回调
func WechatNotifyMsg(c *gin.Context, wxConfig WxPay) (*payments.Transaction, error) {
	// 使用 utils 提供的函数从本地文件中加载商户私钥，商户私钥会用来生成请求的签名
	MchAPIv3Key := wxConfig.MchAPIv3Key
	mchCertificateSerialNumber := wxConfig.MchCertificateSerialNumber
	mchID := wxConfig.MchID

	fmt.Println("1")

	mchPrivateKey, err := loadPrivateKeyWithPath()
	if err != nil {
		return nil, err
	}

	// 1. 使用 `RegisterDownloaderWithPrivateKey` 注册下载器
	err = downloader.MgrInstance().RegisterDownloaderWithPrivateKey(c, mchPrivateKey, mchCertificateSerialNumber, mchID, MchAPIv3Key)
	if err != nil {
		return nil, err
	}
	// 2. 获取商户号对应的微信支付平台证书访问器
	certificateVisitor := downloader.MgrInstance().GetCertificateVisitor(mchID)
	// 3. 使用证书访问器初始化 `notify.Handler`
	handler := notify.NewNotifyHandler(MchAPIv3Key, verifiers.NewSHA256WithRSAVerifier(certificateVisitor))

	transaction := new(payments.Transaction)
	_, err = handler.ParseNotifyRequest(c, c.Request, transaction)

	fmt.Println("5")
	// 如果验签未通过，或者解密失败
	if err != nil {
		return nil, err
	}

	fmt.Printf("%+v\n", *transaction)
	tt, _ := json.Marshal(transaction)
	fmt.Println(tt)
	return transaction, nil
}

func WechatNotifyMsgV2(c *gin.Context, wxConfig WxPay) (*payments.Transaction, error) {
	// 使用 utils 提供的函数从本地文件中加载商户私钥，商户私钥会用来生成请求的签名
	MchAPIv3Key := wxConfig.MchAPIv3Key
	mchCertificateSerialNumber := wxConfig.MchCertificateSerialNumber
	mchID := wxConfig.MchID
	fmt.Println(MchAPIv3Key, mchCertificateSerialNumber, mchID)

	// 使用 utils 提供的函数从本地文件中加载商户私钥，商户私钥会用来生成请求的签名
	mchPrivateKey, err := utils.LoadPrivateKeyWithPath("../conf/apiclient_key.pem")

	if err != nil {
		return nil, err
	}

	ctx := context.Background()
	// 使用商户私钥等初始化 client，并使它具有自动定时获取微信支付平台证书的能力
	err = downloader.MgrInstance().RegisterDownloaderWithPrivateKey(ctx, mchPrivateKey, mchCertificateSerialNumber, mchID, MchAPIv3Key)
	if err != nil {
		fmt.Println("使用商户私钥等初始化")
		return nil, err
	}
	// 2. 获取商户号对应的微信支付平台证书访问器
	certificateVisitor := downloader.MgrInstance().GetCertificateVisitor(mchID)
	// 3. 使用证书访问器初始化 `notify.Handler`
	handler := notify.NewNotifyHandler(MchAPIv3Key, verifiers.NewSHA256WithRSAVerifier(certificateVisitor))
	// 获取平台证书访问器
	transaction := new(payments.Transaction)
	_, err = handler.ParseNotifyRequest(context.Background(), c.Request, transaction)
	// 如果验签未通过，或者解密失败
	if err != nil {
		return nil, err
	}
	fmt.Printf("%+v\n", *transaction)
	return transaction, nil
}

func loadPrivateKeyWithPath() (*rsa.PrivateKey, error) {
	return utils.LoadPrivateKeyWithPath("../conf/apiclient_key.pem")

}

// loadPubKeyWithPath使用公钥
func loadPubKeyWithPath() (*x509.Certificate, error) {
	//return utils.LoadCertificate("../conf/apiclient_cert.pem")
	/*
		str := `-----BEGIN CERTIFICATE-----
			MIIEKzCCAxOgAwIBAgIUauhVci4iN+ZJAAL14dqiTRR1wv0wDQYJKoZIhvcNAQEL
			BQAwXjELMAkGA1UEBhMCQ04xEzARBgNVBAoTClRlbnBheS5jb20xHTAbBgNVBAsT
			FFRlbnBheS5jb20gQ0EgQ2VudGVyMRswGQYDVQQDExJUZW5wYXkuY29tIFJvb3Qg
			Q0EwHhcNMjMwNDIzMDMxMzI0WhcNMjgwNDIxMDMxMzI0WjCBhDETMBEGA1UEAwwK
			MTY0MjY3Mjg0NTEbMBkGA1UECgwS5b6u5L+h5ZWG5oi357O757ufMTAwLgYDVQQL
			DCfoi4/lt57nv7zov4XpgJrnvZHnu5znp5HmioDmnInpmZDlhazlj7gxCzAJBgNV
			BAYMAkNOMREwDwYDVQQHDAhTaGVuWmhlbjCCASIwDQYJKoZIhvcNAQEBBQADggEP
			ADCCAQoCggEBAN08WbE2w4lY/rpd7hIzeCDiH9ZhQ8OMIBz4qwhNn9CzYfuabX6B
			P5XMNYpwR34658KBlutKDBs1pzC2QbOX8CSDFEZdansfaiuvRl/xm1lvSp6tnjYc
			UowbGzuhajBY10vAgy00zNcv619TxQKGO1d0QBeed3FTMj6Kl7dnTrXeK/7JCIFe
			XibazmMxYOTtfktN2BThEdckDzMhu8DpDZSeuHUWbDPvwJQiKgSLcq/YvJ8AbjVY
			JuGHL9hpe/OUFM8FMFlndF2jAylywUMobazb7N0Y9WNudYCzSldG6UawTRXAshyc
			YxXjo+ktwEBxWxXFNwEvND/7zGprmgY1PX0CAwEAAaOBuTCBtjAJBgNVHRMEAjAA
			MAsGA1UdDwQEAwID+DCBmwYDVR0fBIGTMIGQMIGNoIGKoIGHhoGEaHR0cDovL2V2
			Y2EuaXRydXMuY29tLmNuL3B1YmxpYy9pdHJ1c2NybD9DQT0xQkQ0MjIwRTUwREJD
			MDRCMDZBRDM5NzU0OTg0NkMwMUMzRThFQkQyJnNnPUhBQ0M0NzFCNjU0MjJFMTJC
			MjdBOUQzM0E4N0FEMUNERjU5MjZFMTQwMzcxMA0GCSqGSIb3DQEBCwUAA4IBAQBB
			CP9SpIgJhrZ5tk2IyW/K7V1sKbv/7UZ1wZ4n8nOX4ELlLjedf5+viWaFgl9rlj3c
			7HFHFXFncPEQyQqdtj6Zx2PPxp4BEhqo/JPAJUA+pBGHo7GFqhfQpPjHh7Liu4Xt
			8ZxLgnCZRe0bfTqN3VtlUARL/15zkvrdkV7+Vj0bxPTmamx4CqoTPsqeguStMYMb
			yBeBhwaJFJ7Wsloh33YzIJMU9u24rol3CxGU+uQu0ULDBOUVqd+xMilHkafTwegh
			Yu34hvaEknL/Z15kOy9iaF+ymiIu5zqEpFWaeEL1AGGSpzicMtrYrJbSBVVb1pwr
			613M4R8RS728YW67cWiW
			-----END CERTIFICATE-----`

	*/

	str, err := os.ReadFile("../conf/apiclient_cert.pem")
	if err != nil {
		return nil, err
	}
	fmt.Println(string(str))

	return utils.LoadCertificateWithPath("../conf/apiclient_cert.pem")
}
