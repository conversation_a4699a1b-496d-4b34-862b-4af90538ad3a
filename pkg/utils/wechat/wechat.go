package wechat

import (
	"encoding/json"
	"errors"
	"fmt"
	"io/ioutil"
	"net/http"
)

type OpenIDInfo struct {
	AccessToken    string `json:"access_token"`
	ExpiresIn      int    `json:"expires_in"`
	RefreshToken   string `json:"refresh_token"`
	Openid         string `json:"openid"`
	Scope          string `json:"scope"`
	IsSnapshotuser int    `json:"is_snapshotuser"`
	Unionid        string `json:"unionid"`
}

func GetOpenID(appID, appSecret, code string) (openInfo *OpenIDInfo, err error) {

	var openIDInfo *OpenIDInfo
	str := "https://api.weixin.qq.com/sns/oauth2/access_token?appid=%s&secret=%s&code=%s&grant_type=authorization_code"
	url := fmt.Sprintf(str, appID, appSecret, code)

	resp, err := http.Get(url)
	if err != nil {
		fmt.Println(err)
		return nil, err
	}

	body, err := ioutil.ReadAll(resp.Body)

	defer resp.Body.Close()

	if resp.StatusCode != 200 {
		fmt.Printf("请求失败%d\n", resp.StatusCode)
		return nil, errors.New(fmt.Sprintf("请求失败%d", resp.StatusCode))
	}

	fmt.Println("返回数据是", string(body))

	err = json.Unmarshal(body, &openInfo)
	if err != nil {
		fmt.Println(err)
		return nil, err
	}
	fmt.Println(openIDInfo)

	return

}
