package vo

import (
	"fmt"
	"testing"
)

func TestOcrRes_CheckIdAndName(t *testing.T) {
	type fields struct {
		RealName string
		IDNum    string
		Path     string
		Age      int
		Birthday string
		Sex      string
	}
	tests := []struct {
		name   string
		fields fields
	}{
		{fields: fields{RealName: "320323199201301018", IDNum: "耕南阳"}},
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := &OcrRes{
				RealName: tt.fields.RealName,
				IDNum:    tt.fields.IDNum,
				Path:     tt.fields.Path,
				Age:      tt.fields.Age,
				Birthday: tt.fields.Birthday,
				Sex:      tt.fields.Sex,
			}
			m.CheckIdAndName()
			fmt.Println(m.IDNum)
			fmt.Println(m.RealName)
		})
	}
}
