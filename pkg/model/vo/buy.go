package vo

// WsRtcLive （LOT 标题、index） （当前价、下口价格） (拍品价格列表) (消息)
type WsRtcLive struct {
	WsType           string `json:"wsType"` //tip artwork nowAuctionPrice auctionPriceList first(除了tip)
	Tip              string `json:"tip"`
	Artwork          string `json:"artwork"`
	NowAuctionPrice  string `json:"nowAuctionPrice"`
	AuctionPriceList string `json:"auctionPriceList"`
}

type Artwork struct {
	Index              uint32 `json:"index"`
	AuctionArtWorkUuid string `json:"auctionArtWorkUuid"`
	ArtworkName        string `json:"artworkName"`
	Name               string `json:"name"`
	IsSelling          bool   `json:"isSelling"` //是否正在售卖
	IsSoled            bool   `json:"isSoled"`   //是否卖出
	TotalNum           uint32 `json:"totalNum"`  //总量
	//状态 开拍之类的
}

type Tip struct {
	TipType string `json:"tipType"` //提示类型 falling即将落锤  othersBid已有其他人出价 successBid竞拍成功 artworkOver(本拍品结束) failBid竞拍失败 over(竞拍结束)
}

type NowAuctionPrice struct {
	Currency  string `json:"currency"` //币种
	NowPrice  string `json:"nowPrice"`
	NextPrice string `json:"nextPrice"`
}

type AuctionPriceList struct {
	Buys []*Buy `json:"buys"`
}

type Buy struct {
	StatusCode  string `json:"StatusCode"`  //(领先 head) (出局 out)
	AuctionType string `json:"auctionType"` //类型  online  scene
	CreatedAt   string `json:"CreatedAt"`   //下单时间
	Money       string `json:"money"`       //金额
	UserId      string `json:"userId"`      //金额
	IsMy        string `json:"isMy"`        //是否是我
}
