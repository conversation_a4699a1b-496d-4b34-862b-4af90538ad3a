package vo

type CultureArtworkReq struct {
	Num  int    `json:"num"`
	Uuid string `json:"Uuid"`
}

type ArtworkRes struct {
	State   int         `json:"state"`
	Message string      `json:"message"`
	Data    ArtworkInfo `json:"data"`
}

type ArtworkInfo struct {
	ArtworkThumbnail                string `json:"artworkThumbnail"`
	ArtworkMicroCharacteristics     string `json:"artworkMicroCharacteristics"`
	CertificateNumber               string `json:"certificateNumber"`
	Creator                         string `json:"creator"`
	CreativeArea                    string `json:"creativeArea"`
	YearOfCreation                  string `json:"yearOfCreation"`
	ArtworkSize                     string `json:"artworkSize"`
	ArtworkSizeL                    int    `json:"artworkSizeL"`
	ArtworkSizeW                    int    `json:"artworkSizeW"`
	ArtCategory                     string `json:"artCategory"`
	ArtMaterial                     string `json:"artMaterial"`
	BlockchainCodeForArtworkCert    string `json:"blockchainCodeForArtworkCert"`
	AuthorCertificateBlockchainCode string `json:"authorCertificateBlockchainCode"`
	CurrentOwner                    string `json:"currentOwner"`
	AccreditationBodies             string `json:"accreditationBodies"`
	Standards                       string `json:"standards"`
	IdentificationUnit              string `json:"identificationUnit"`
	AuthenticationSystem            string `json:"authenticationSystem"`
	ArtworkName                     string `json:"artworkName"`
}
