package vo

import (
	"github.com/fonchain_enterprise/client-auction/api/fenghe"
	"github.com/fonchain_enterprise/client-auction/pkg/model/response"
)

type AuctionBuyRequest struct {
	AuctionArtworkUuid string `json:"auctionArtworkUuid"`
	AuctionStatus      uint32 `json:"auctionStatus"`
	AuctionType        string `json:"auctionType"`
	CreatedAt          string `json:"createdAt"`
	BaseCurrency       string `json:"baseCurrency"`
	BaseMoney          string `json:"baseMoney"`
	Status             uint32 `json:"status"`
	UserName           string `json:"userName"`
	Uuid               string `json:"uuid"`
	BuyCurrency        string `json:"buyCurrency"`
	BuyMoney           string `json:"buyMoney"`
	AdminAuctionStatus uint32 `json:"adminAuctionStatus"`
}

type AuctionBuyListResponse struct {
	Count              uint64                       `json:"count"`
	Data               []*AuctionBuyRequest         `json:"data"`
	AuctionArtworkBase *response.AuctionArtworkBase `json:"auctionArtworkBase"`
	NowAuctionPrice    *fenghe.NowAuctionPrice      `json:"nowAuctionPrice"`
	WsType             string                       `json:"wsType"` //返回类型默认是 空 artworkList
}

type AuctionBuyList struct {
	Page               uint64 `json:"page"`
	PageSize           uint64 `json:"pageSize"`
	AuctionArtworkUuid string `json:"auctionArtworkUuid"`
}
