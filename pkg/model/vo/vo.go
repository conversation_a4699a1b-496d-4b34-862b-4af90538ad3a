package vo

import (
	backendSeries "github.com/fonchain_enterprise/client-auction/api/series"
	"unicode/utf8"
)

type ScanInfoRes struct {
	CultureArtwork *backendSeries.SeriesCultureArtwork `json:"cultureArtwork"`
	Num            int                                 `json:"num"`
}

type OcrRes struct {
	RealName string `json:"realName"`
	IDNum    string `json:"iDNum"`
	Path     string `json:"path"`
	Age      int    `json:"age"`
	Birthday string `json:"birthday"`
	Sex      string `json:"sex"`
}

type CalculateRes struct {
	Money    string `json:"money"`
	DayMoney string `json:"dayMoney"`
}

type JumpUrlRes struct {
	JumpUrl string `json:"jumpUrl"`
}

func (m *OcrRes) CheckIdAndName() {
	if utf8.RuneCountInString(m.RealName) == 18 && utf8.RuneCountInString(m.IDNum) != 18 {
		m.IDNum, m.RealName = m.RealName, m.IDNum
	}
}
