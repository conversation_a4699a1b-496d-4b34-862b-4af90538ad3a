package union

import "github.com/fonchain_enterprise/client-auction/api/account"

type AccountInfo struct {
	ID         uint64              `json:"ID"`
	Domain     string              `json:"domain"`    //环境
	NickName   string              `json:"nickName"`  //昵称
	Account    string              `json:"account"`   //区块链地址
	TelNum     string              `json:"telNum"`    //手机号
	Avatar     string              `json:"avatar"`    //头像
	Status     string              `json:"status"`    //头像
	CreatedAt  string              `json:"createdAt"` //头像
	BirthDate  string              `json:"birthDate"`
	Age        uint64              `json:"age"`
	Sex        uint64              `json:"sex"`
	Title      string              `json:"title"`
	IDNum      string              `json:"idNum"`
	RealName   string              `json:"realName"`
	Lang       string              `json:"lang"`
	UserExtend *account.UserExtend `json:"userExtend"`
	FddInfo    *account.FddInfo    `json:"fddInfo"`
}

type Login struct {
	AccountInfo *AccountInfo `json:"accountInfo"`
	Token       string       `json:"token"`     //环境
	IsReal      bool         `json:"isReal"`    //环境
	IsJumpFdd   bool         `json:"isJumpFdd"` //跳转到法大大
}

type User struct {
	ID           uint64  `json:"ID"`
	Domain       string  `json:"domain"`       //环境
	NickName     string  `json:"nickName"`     //昵称
	TelNum       string  `json:"telNum"`       //手机号
	Password     string  `json:"password"`     //密码
	Avatar       string  `json:"avatar"`       //头像
	Status       string  `json:"status"`       //头像
	EnterDate    string  `json:"enterDate"`    //头像
	WorkYear     float32 `json:"workYear"`     //头像
	IsNeedChange uint64  `json:"isNeedChange"` //是否强制修改密码
	JumpTo       string  `json:"jumpTo"`       //是否跳转老平台 origin-老平台  onsite当前
	//PositionUsers []*position.PositionUser `json:"positionUsers"` //关联用户岗位
	JobNum       string              `json:"jobNum"`
	BirthDate    string              `json:"birthDate"`
	Sex          uint64              `json:"sex"`
	Title        string              `json:"title"`
	IdNum        string              `json:"idNum"`
	RealName     string              `json:"realName"`
	BossId       uint64              `json:"bossId"`
	DepartmentId uint64              `json:"departmentId"`
	Lang         string              `json:"lang"`
	Code         string              `json:"code"`
	UserExtend   *account.UserExtend `json:"userExtend"`
}
