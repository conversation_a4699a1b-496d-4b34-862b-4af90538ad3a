package union

import (
	"github.com/fonchain_enterprise/client-auction/api/account"
	"github.com/fonchain_enterprise/client-auction/api/position"
	"github.com/fonchain_enterprise/client-auction/api/rule"
)

type Extend struct {
	JumpTo          string `json:"JumpTo"`
	Lang            string `json:"Lang"`
	CanScan         bool   `json:"CanScan"`
	ResolutionRatio bool   `json:"ResolutionRatio"`
}

type MobileUser struct {
	ID            uint64                   `json:"ID"`
	Domain        string                   `json:"Domain"`        //环境
	NickName      string                   `json:"NickName"`      //昵称
	TelNum        string                   `json:"TelNum"`        //手机号
	Password      string                   `json:"Password"`      //密码
	Avatar        string                   `json:"Avatar"`        //头像
	Status        string                   `json:"Status"`        //头像
	EnterDate     string                   `json:"EnterDate"`     //头像
	WorkYear      float32                  `json:"WorkYear"`      //头像
	IsNeedChange  uint64                   `json:"IsNeedChange"`  //是否强制修改密码
	JumpTo        string                   `json:"JumpTo"`        //是否跳转老平台 origin-老平台  onsite当前
	PositionUsers []*position.PositionUser `json:"positionUsers"` //关联用户岗位
	JobNum        string                   `json:"JobNum"`
	BirthDate     string                   `json:"BirthDate"`
	Sex           uint64                   `json:"Sex"`
	Title         string                   `json:"Title"`
	LeftDate      string                   `json:"LeftDate"`
	Remark        string                   `json:"Remark"`
	RecentImg     string                   `json:"RecentImg"`   //近照
	ICNum         string                   `json:"ICNum"`       //门禁卡号
	Extend        *account.Extend          `json:"Extend"`      //设置
	Train         string                   `json:"train"`       //培训视频
	Certificate   string                   `json:"certificate"` //证书
	Source        string                   `json:"source"`      //来源
	Operator      *account.Operator        `json:"operator"`    //来源
}

type MobileUserResponse struct {
	ID                   uint64                   `json:"ID"`
	Domain               string                   `json:"Domain"`   //环境
	NickName             string                   `json:"NickName"` //昵称
	TelNum               string                   `json:"TelNum"`   //手机号
	Password             string                   `json:"Password"` //密码
	Avatar               string                   `json:"Avatar"`
	Status               string                   `json:"Status"`
	CreateAt             string                   `json:"CreateAt"`
	RealNameID           uint64                   `json:"RealNameID"`
	RealName             string                   `json:"RealName"`
	IsAdmin              bool                     `json:"IsAdmin"`
	EnterDate            string                   `json:"EnterDate"`         //头像
	WorkYear             float32                  `json:"WorkYear"`          //头像
	IsNeedChange         uint64                   `json:"IsNeedChange"`      //是否强制修改密码
	PositionUsers        []*rule.PositionUser     `json:"PositionUsers"`     //关联用户岗位
	DepartmentLeaders    []*rule.DepartmentLeader `json:"DepartmentLeaders"` //关联用户岗位
	JumpTo               string                   `json:"JumpTo"`            //是否跳转老平台 origin-老平台  onsite当前
	Extend               *account.Extend          `json:"Extend"`            //附件信息
	DepartmentName       string                   `json:"DepartmentName"`
	JobNum               string                   `json:"JobNum"`
	BirthDate            string                   `json:"BirthDate"`
	Age                  uint64                   `json:"Age"`
	Sex                  uint64                   `json:"Sex"`
	Title                string                   `json:"Title"`
	IDNum                string                   `json:"IDNum"`
	LeftDate             string                   `json:"LeftDate"`
	DriverAuth           bool                     `json:"DriverAuth"`
	DriverSupervisorAuth bool                     `json:"DriverSupervisorAuth"`
	Remark               string                   `json:"Remark"`
	IdentityKey          string                   `json:"IdentityKey"` //身份
	RecentImg            string                   `json:"RecentImg"`   //近照
	MailAccount          string                   `json:"MailAccount"` //近照
	Clocks               []*account.ClockUser     `json:"clocks"`
	ICNum                string                   `json:"ICNum"`       //门禁卡号
	Train                string                   `json:"train"`       //培训视频
	Certificate          string                   `json:"certificate"` //证书
	Source               string                   `json:"source"`      //来源
	TrainVideos          []*account.TrainVideo    `json:"TrainVideos"` //关联用户岗位
}

type MobileAccountInfo struct {
	ID                   uint64                `json:"ID"`
	Domain               string                `json:"Domain"`        //环境
	NickName             string                `json:"NickName"`      //昵称
	Account              string                `json:"Account"`       //昵称
	TelNum               string                `json:"TelNum"`        //手机号
	Password             string                `json:"Password"`      //密码
	Avatar               string                `json:"Avatar"`        //头像
	Status               string                `json:"Status"`        //头像
	CreatedAt            string                `json:"CreatedAt"`     //头像
	IsAdmin              bool                  `json:"IsAdmin"`       //头像
	EnterDate            string                `json:"EnterDate"`     //头像
	WorkYear             float32               `json:"WorkYear"`      //头像
	IsNeedChange         uint64                `json:"IsNeedChange"`  //是否强制修改密码
	JumpTo               string                `json:"JumpTo"`        //是否跳转老平台 origin-老平台  onsite当前
	PositionUsers        []*rule.PositionUser  `json:"PositionUsers"` //关联用户岗位
	DepartmentName       string                `json:"DepartmentName"`
	JobNum               string                `json:"JobNum"`
	BirthDate            string                `json:"BirthDate"`
	Age                  uint64                `json:"Age"`
	Sex                  uint64                `json:"Sex"`
	Title                string                `json:"Title"`
	IDNum                string                `json:"IDNum"`
	DriverAuth           bool                  `json:"DriverAuth"`
	DriverSupervisorAuth bool                  `json:"DriverSupervisorAuth"`
	MailAccount          string                `json:"MailAccount"`
	Train                string                `json:"train"`       //培训视频
	Certificate          string                `json:"certificate"` //证书
	TrainVideos          []*account.TrainVideo `json:"TrainVideos"` //关联用户岗位
}

type MobileLogin struct {
	AccountInfo  *MobileAccountInfo `json:"AccountInfo"`
	Token        string             `json:"Token"`        //环境
	RefreshToken string             `json:"RefreshToken"` //refresh_token
	JumpToWhere  string             `json:"JumpToWhere"`  //环境
}
