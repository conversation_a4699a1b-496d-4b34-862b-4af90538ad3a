package query

import (
	"github.com/fonchain_enterprise/client-auction/api/account"
)

type ClientWsReq struct {
	Uuid string `json:"uuid" binding:"required"`
}

type SureReq struct {
	Money string `json:"money" binding:"required"`
	Uuid  string `json:"uuid" binding:"required"`
}

type SendMsgRequest struct {
	Domain      string                             `json:"Domain"`
	TelNum      string                             `json:"TelNum"`
	Project     string                             `json:"Project"`
	SignNo      uint32                             `json:"signNo"`
	MId         uint32                             `json:"mId"`
	Scope       string                             `json:"scope"` //标记模块
	Zone        string                             `json:"zone"`  //地区 不同地区切换不同发送帐号
	CaptchaInfo account.VerifySliderCaptchaRequest `json:"verifyCaptcha"`
}
type UpdateNewTel struct {
	NewTelNum string `json:"newTelNum"` //新手机号
	NewCode   string `json:"newCode"`   //新验证码
	Code      string `json:"code"`      //老手机号
}
type SendMsgNewTel struct {
	NewTelNum string `json:"newTelNum"` //新手机号
	Token     string `json:"token"`     //老手机号
}

type OrderContractQuery struct {
	ID            int64    `json:"ID"`
	OrderNo       string   `json:"orderNo"`       //订单号
	ContractFiles []string `json:"contractFiles"` //pdf合同数据
}

type SeriesArtworkDetailReq struct {
	SeriesUuid string `json:"seriesUuid"`
	ArtworkUid string `json:"artworkUid"`
}

type CurrencyReq struct {
	Currency string `json:"currency"`
	Price    string `json:"price"`
}

type GiveCultureArtworkOrderReq struct {
	OrderUuid string `json:"orderUuid"`
	ToUserTel string `json:"toUserTel"`
	Zone      string `json:"zone"`
}

type ScanSecretRequest struct {
	Sign   string `json:"sign" binding:"required"`
	Time   string `json:"time"`
	Client string `json:"client"`
	Nonce  string `json:"nonce"`
}

type SecretRequest struct {
	Sign      string `json:"sign" binding:"required"`
	Time      string `json:"time"`
	Client    string `json:"client"`
	Nonce     string `json:"nonce"`
	ToTel     string `json:"toTel" binding:"required"`
	Zone      string `json:"zone"`
	PayType   uint32 `json:"payType"`
	ReturnUrl string `json:"returnUrl"`
	QuitUrl   string `json:"quitUrl"`
}

type FddCheckReq struct {
	ToUrl string `json:"toUrl"`
}
