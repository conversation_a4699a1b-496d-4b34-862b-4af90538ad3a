package dto

import (
	"github.com/fonchain_enterprise/client-auction/api/position"
)

type LoginInfo struct {
	Domain         string
	ID             uint64
	Account        string
	NickName       string
	JumpTo         string
	DepartmentName string
	PositionUsers  []*position.PositionUser
}

type BaseDetailResponse struct {
	ID             uint64 `json:"ID"`
	Name           string `json:"name"`
	Remark         string `json:"remark"`
	DepartmentCode string `json:"departmentCode"`
	SyncId         string `json:"syncId"`
}

type RecommendInfoResponse struct {
	StaffId         uint64         `json:"staffId"`
	StaffName       string         `json:"staffName"`
	StaffTelNum     string         `json:"staffTelNum"`
	IsRecommended   bool           `json:"isRecommended"`
	CompanyInfoList []*CompanyInfo `json:"companyInfoList"`
}

type CompanyInfo struct {
	ID   uint64 `json:"ID"`
	Name string `json:"name"`
}
