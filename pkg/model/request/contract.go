package request

type SignContractReq struct {
}
type OrderRecordReq struct {
	UserID   int32
	Page     int64
	PageSize int64
}

type OrderRecordType struct {
	OrderNo     string `json:"orderNo"` //订单号
	Amount      string `json:"amount"`  //金额
	LeftPrice   string `json:"leftPrice"`
	PaidPrice   string `json:"paidPrice"`
	Currency    string `json:"currency"` //货币单位
	Status      int32  `json:"status"`   //支付状态 1 未支付  2 支付成功 3 支付失败 4部分支付
	PayTime     string `json:"payTime"`
	ArtworkUid  string `json:"artworkUid"`
	Tfnum       string `json:"tfnum"`
	ArtworkName string `json:"artworkName"`
	HdPic       string `json:"hdPic"`
	ArtistName  string `json:"artistName"`
	ArtistUid   string `json:"artistUid"`
	Abstract    string `json:"abstract"`
	Width       int32  `json:"width"`
	Length      int32  `json:"length"`
	Ruler       int32  `json:"ruler"`
	ActionCode  string `json:"actionCode"`
}
