package response

type ListResponse struct {
	Status          string         `json:"status"`
	Count           uint64         `json:"count"`
	Data            []*AccountInfo `json:"data"`
	AllCount        uint64         `json:"allCount"`
	TodayBiggestNum uint64         `json:"todayBiggestNum"`
	TodayUser       int64          `json:"todayUser"`
	TodayNum        uint64         `json:"todayNum"`
	LiveNum         uint64         `json:"liveNum"`
}

type AccountInfo struct {
	ID             uint64      `json:"ID"`
	Account        string      `json:"account"`
	NickName       string      `json:"nickName"`
	Type           int64       `json:"type"`
	TelNum         string      `json:"telNum"`
	Status         string      `json:"status"`
	Avatar         string      `json:"avatar"`
	CreateAt       string      `json:"createAt"`
	RealNameID     uint64      `json:"realNameID"`
	RealName       string      `json:"realName"`
	IDNum          string      `json:"iDNum"`
	MnemonicWords  string      `json:"mnemonicWords"`
	IsNeedChange   uint64      `json:"isNeedChange"`
	EnterDate      string      `json:"enterDate"`
	WorkYear       float32     `json:"workYear"`
	Domain         string      `json:"domain"`
	JobNum         string      `json:"jobNum"`
	BirthDate      string      `json:"birthDate"`
	Age            uint64      `json:"age"`
	Sex            uint64      `json:"sex"`
	Title          string      `json:"title"`
	Ip             string      `json:"ip"`
	LoginDate      string      `json:"loginDate"`
	InvitationCode string      `json:"invitationCode"`
	NowLogId       uint64      `json:"nowLogId"`
	CanScan        bool        `json:"canScan"`
	LeftDate       string      `json:"leftDate"`
	Remark         string      `json:"remark"`
	RecentImg      string      `json:"recentImg"`
	MailAccount    string      `json:"mailAccount"`
	ICNum          string      `json:"iCNum"`
	EnglishName    string      `json:"englishName"`
	Train          string      `json:"train"`
	Certificate    string      `json:"certificate"`
	UpdatedAt      string      `json:"updatedAt"`
	SecurityCode   string      `json:"securityCode"`
	BlockAddr      string      `json:"blockAddr"`
	Language       string      `json:"language"`
	UserExtend     *UserExtend `json:"userExtend"`
}

type UserExtend struct {
	Id               string `json:"id"`
	Uuid             string `json:"uuid"`
	UserID           string `json:"userID"`
	Address          string `json:"address"`
	BankName         string `json:"bankName"`
	BankNo           string `json:"bankNo"`
	Zone             string `json:"zone"`
	IsMainland       uint32 `json:"isMainland"`
	IsReal           uint32 `json:"isReal"`   //是否实名
	FromCode         string `json:"fromCode"` // 99999
	RealTime         string `json:"realTime"` // 实名信息时间
	IdType           string `json:"idType"`
	IdNo             string `json:"idNo"`
	RealName         string `json:"realName"`
	PassStatus       uint32 `json:"passStatus"`
	CardA            string `json:"cardA"`
	CardB            string `json:"cardB"`
	RecommendStaffId uint32 `json:"recommendStaffId"`
	IsRecommended    bool   `json:"isRecommended"`
}
