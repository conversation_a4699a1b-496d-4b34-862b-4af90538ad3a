package websocket

import "encoding/json"

// Head 响应数据头
type Head struct {
	Cmd  string `json:"cmd"`  // 消息的cmd 动作
	Data *Data  `json:"data"` // 消息体
}

// Response 响应数据体
type Data struct {
	Code uint32      `json:"code"`
	Msg  string      `json:"msg"`
	Data interface{} `json:"data"` // 数据 json
}

// NewResponseHead 设置返回消息
func NewResponseHead(cmd string, code uint32, codeMsg string, data interface{}) *Head {
	response := NewResponse(code, codeMsg, data)

	return &Head{Cmd: cmd, Data: response}
}

// String to string
func (h *Head) String() (headStr string) {
	headBytes, _ := json.Marshal(h)
	headStr = string(headBytes)
	return
}

// NewResponse 创建新的响应
func NewResponse(code uint32, msg string, data interface{}) *Data {
	return &Data{Code: code, Msg: msg, Data: data}
}
