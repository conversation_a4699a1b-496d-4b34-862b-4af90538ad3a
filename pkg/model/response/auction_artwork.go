package response

type AuctionArtworkListResponse struct {
	Count uint64                `json:"count"`
	Data  []*AuctionArtworkInfo `json:"data"`
}

type AuctionArtworkInfo struct {
	ID            uint32          `json:"ID"`
	Index         uint32          `json:"index"`
	IsSelling     uint32          `json:"isSelling"`     //是否正在拍卖  开拍停拍
	StartPrice    string          `json:"startPrice"`    //起拍价
	SoldPrice     string          `json:"soldPrice"`     //起拍价
	PriceRuleType string          `json:"priceRuleType"` //拍卖类型价格类型
	PriceRuleAdd  string          `json:"priceRuleAdd"`  //售价货币 仅仅展示使用
	ScreenStatus  uint32          `json:"screenStatus"`
	PriceRules    []*PriceRule    `json:"priceRules"`
	IsSold        bool            `json:"isSold"`  //是否售出
	Artwork       *ArtworkRequest `json:"artwork"` //作品名字 缩略图 作者名字
	UserId        uint32          `json:"userId"`
	Uuid          string          `json:"uuid"`
	ArtworkUuid   string          `json:"artworkUuid"`
}

// 竞价表
type PriceRule struct {
	Uuid     string `json:"uuid"`
	Price    string `json:"price"`
	Index    uint32 `json:"index"`
	IntPrice uint32 `json:"intPrice"`
}

type ArtworkRequest struct {
	HdPic      string `json:"hdPic"`
	ArtistName string `json:"artistName"`
	//Abstract   string `json:"abstract"`
	ID   uint32 `json:"ID"`
	Uuid string `json:"uuid"`
	Name string `json:"name"`
}
