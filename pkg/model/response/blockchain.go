package response

type Header struct {
	Version       int    `json:"version"`
	Type          int    `json:"type"`
	SubType       int    `json:"subType"`
	Timestamp     int64  `json:"timestamp"`
	TransactionID string `json:"transactionId"`
}

type Store struct {
	StoreData string `json:"storeData"`
	Extra     string `json:"extra"`
}
type Data struct {
	Header Header `json:"header"`
	Store  Store  `json:"store"`
	Raw    string `json:"raw"`
}
type DealDetailResponse struct {
	State   int    `json:"state"`
	Message string `json:"message"`
	Data    Data   `json:"data"`
}
