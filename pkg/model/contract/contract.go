package contract

type BidInfo struct {
	BidNo    string
	BidPrice string
}

type AuctionArtwork struct {
	BidData            []BidInfo
	ArtworkName        string
	SoldPrice          string
	ArtworkSize        string
	HdPic              string
	Tfnum              string
	AuctionBasePrice   string
	Commission         string
	SignDate           string
	AuctionInfo        AuctionInfo
	BuyName            string
	SoldPriceChinese   string
	AuctionArtworkUuid string
	AuctionLOT         string
}
type FddContractSignReq struct {
	UserID             string
	FddCustomerId      string
	SignOrder          int32
	Phone              string
	LineType           int32
	TestReturnHost     string
	TestReturnEndPoint string
	FddUserInfo        FddUserInfo
	Online             bool
	AuctionUuid        string
	AuctionY           string
	AuctionM           string
	AuctionD           string
	AuctionArtworkData []AuctionArtwork
	SignDate           string
	AuctionInfo        AuctionInfo
	BuyName            string
	AuctionArtworkUuid string
	SeriesUuid         string
	CallBackUrl        string
	SourceType         string
}

type AuctionInfo struct {
	AuctionTime     string
	AuctionAddress  string
	AuctionName     string
	AuctionLOT      string
	PreviewStartY   string
	PreviewStartM   string
	PreviewStartD   string
	PreviewStartHis string
	PreviewEndY     string
	PreviewEndM     string
	PreviewEndD     string
	PreviewEndHis   string
	PreviewTime     string
}

type BidDataDynamicTables struct {
	InsertWay               int        `json:"insertWay"`
	Keyword                 string     `json:"keyword"`
	Datas                   [][]string `json:"datas"`
	BorderFlag              bool       `json:"borderFlag"`
	Headers                 []string   `json:"headers"`
	ColWidthPercent         []int      `json:"colWidthPercent"`
	CellHeight              int        `json:"cellHeight"`
	CellHorizontalAlignment int        `json:"cellHorizontalAlignment"`
	CellVerticalAlignment   int        `json:"cellVerticalAlignment"`
}
