package contract

type FddUserInfo struct {
	UserName string `json:"userName"`
	Address  string `json:"address"`
	Gender   string `json:"gender"`
	CardType string `json:"cardType"`
	CardNo   string `json:"cardNo"`
	Phone    string `json:"phone"`
}

type ContractJmxy1 struct {
	UserName       string `json:"userName"`
	Address        string `json:"address"`
	Gender         string `json:"gender"`
	CardType       string `json:"cardType"`
	CardNo         string `json:"cardNo"`
	Phone          string `json:"phone"`
	PrevStartY     string `json:"prevStartY"`
	PrevStartM     string `json:"prevStartM"`
	PrevStartD     string `json:"prevStartD"`
	PrevStartHis   string `json:"prevStartHis"`
	PrevEndY       string `json:"prevEndY"`
	PrevEndM       string `json:"prevEndM"`
	PrevEndD       string `json:"prevEndD"`
	PrevEndHis     string `json:"prevEndHis"`
	AuctionTime    string `json:"auctionTime"`
	AuctionAddress string `json:"auctionAddress"`
	SignDate       string `json:"signDate"`
}

type ContractJmxz2 struct {
	AuctionTime    string `json:"auctionTime"`
	AuctionAddress string `json:"auctionAddress"`
	AuctionUrl     string `json:"auctionUrl"`
}

type ContractPmgg3 struct {
	AuctionTime        string `json:"auctionTime"`
	AuctionAddress     string `json:"auctionAddress"`
	AuctionUrl         string `json:"auctionUrl"`
	AuctionPreviewTime string `json:"auctionPreviewTime"`
	SignDate           string `json:"signDate"`
}

type ContractPpqr5 struct {
	UserName         string `json:"userName"`
	Address          string `json:"address"`
	Gender           string `json:"gender"`
	CardType         string `json:"cardType"`
	CardNo           string `json:"cardNo"`
	Phone            string `json:"phone"`
	ArtworkName      string `json:"artworkName"`
	ArtworkSize      string `json:"artworkSize"`
	AuctionY         string `json:"auctionY"`
	AuctionM         string `json:"auctionM"`
	AuctionD         string `json:"auctionD"`
	AuctionTitle     string `json:"auctionTitle"`
	Tfnum            string `json:"tfnum"`
	SoldPrice        string `json:"soldPrice"`
	SoldPriceChinese string `json:"soldPriceChinese"`
	AuctionLOT       string `json:"auctionLOT"`
	SignDate         string `json:"signDate"`
}

type SolidInfo struct {
	ArtworkName       string `json:"artworkName"`
	SoldPrice         string `json:"soldPrice"`
	AuctionTime       string `json:"auctionTime"`
	AuctionAddress    string `json:"auctionAddress"`
	ArtworkPic        string `json:"artworkPic"`
	AuctionName       string `json:"auctionName"`
	AuctionLOT        string `json:"auctionLOT"`
	AuctionBasePrice  string `json:"auctionBasePrice"`
	Commission        string `json:"commission"`
	SignDate          string `json:"signDate"`
	BuyName           string `json:"buyName"`
	BuyY              string `json:"buyY"`
	BuyM              string `json:"buyM"`
	BuyD              string `json:"buyD"`
	CardNo            string `json:"cardNo"`
	SolidPriceChinese string `json:"solidPriceChinese"`
}

type ContractInfo struct {
	TemplateId   string
	ContractId   string
	ContractName string
	ShortName    string
}

type SignInfo struct {
	SignKeyword  string
	Keyx         string
	Keyy         string
	ShortName    string
	ContractName string
}

const (
	ContractName1 = "竞买协议"
	ContractName2 = "竞买须知"
	ContractName3 = "拍卖公告"
	ContractName4 = "拍卖规则"
	ContractName5 = "拍品移交确认书"
	ContractName6 = "拍卖笔录-成交确认书"
)

func GetFddSignInfo() map[string]SignInfo {
	var FddSignInfo = map[string]SignInfo{}
	FddSignInfo = map[string]SignInfo{
		ContractName1: {
			SignKeyword:  "自然人签名",
			Keyx:         "100",
			Keyy:         "0",
			ShortName:    "jmxy1",
			ContractName: ContractName1,
		},
		ContractName2: {
			SignKeyword:  "协商解决",
			Keyx:         "100",
			Keyy:         "",
			ShortName:    "jmxz2",
			ContractName: ContractName2,
		},
		ContractName3: {
			SignKeyword:  "微信号",
			Keyx:         "100",
			Keyy:         "",
			ShortName:    "pmgg3",
			ContractName: ContractName3,
		},
		ContractName4: {
			SignKeyword:  "",
			Keyx:         "",
			Keyy:         "",
			ShortName:    "pmgz4",
			ContractName: ContractName4,
		},
		ContractName5: {
			SignKeyword:  "乙方签名",
			Keyx:         "100",
			Keyy:         "0",
			ShortName:    "ppqr5",
			ContractName: ContractName5,
		},
		ContractName6: {
			SignKeyword:  "乙方",
			Keyx:         "100",
			Keyy:         "0",
			ShortName:    "ppbl6",
			ContractName: ContractName6,
		},
	}
	return FddSignInfo
}
