package login

import (
	"errors"
	api "github.com/fonchain_enterprise/client-auction/api/account"
	"github.com/fonchain_enterprise/client-auction/api/rule"
	"github.com/gin-gonic/gin"
)

type Info struct {
	Domain         string
	ID             uint64
	Account        string
	NickName       string
	JumpTo         string
	DepartmentName string
	TelNum         string
	Avatar         string
	Extend         *api.Extend
	PositionUsers  []*rule.PositionUser
}

type User struct {
	ID       uint64 `json:"ID"`
	Domain   string `json:"domain"`   //环境
	NickName string `json:"nickName"` //昵称
	TelNum   string `json:"telNum"`   //手机号
	Avatar   string `json:"avatar"`   //头像
	JobNum   string `json:"jobNum"`   //头像
	Sex      uint64 `json:"sex"`
}

type Login struct {
	AccountInfo *User  `json:"accountInfo"`
	Token       string `json:"token"`       //环境
	JumpToWhere string `json:"jumpToWhere"` //环境
}

func GetUserInfoFromC(c *gin.Context) Info {
	userInfoAny, _ := c.Get("jwtInfo")
	userInfo := userInfoAny.(Info)
	return userInfo
}

func GetUserInfoFromCV2(c *gin.Context) (*Info, error) {
	userInfoAny, isExist := c.Get("jwtInfo")
	if !isExist {
		return nil, errors.New("not exist user info")
	}
	userInfo := userInfoAny.(Info)
	return &userInfo, nil
}

func GetUserInfoFromCNoPanic(c *gin.Context) Info {
	userInfoAny, _ := c.Get("jwtInfo")
	if userInfoAny != nil {
		userInfo := userInfoAny.(Info)
		return userInfo
	} else {
		return Info{}
	}
}
