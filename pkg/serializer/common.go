package serializer

// Response 基础序列化器
type Response struct {
	Status    int         `json:"status"`
	Data      interface{} `json:"data"`
	Msg       string      `json:"msg"`
	Code      int         `json:"code"`
	Error     error       `json:"error"`
	Err       string      `json:"err"`
	Keys      []string    `json:"keys"`
	Mark      string      `json:"mark,omitempty"`
	Page      *PageInfo   `json:"page,omitempty"`
	Positions interface{} `json:"positions,omitempty"`
}

type PageInfo struct {
	Page     int32 `json:"page" query:"page"`
	PageSize int32 `json:"pageSize" query:"pageSize"`
	Total    int32 `json:"total"`
}

type UseFaceClockIn struct {
	Result int    `json:"Result"`
	Msg    string `json:"Msg"`
}

// WsResponse 基础序列化器
type WsResponse struct {
	Status int         `json:"status"`
	Data   interface{} `json:"data"`
	Msg    string      `json:"msg"`
	Code   int         `json:"code"`
	Type   string      `json:"type"`
}
