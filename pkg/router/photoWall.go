package router

import (
	"github.com/fonchain_enterprise/client-auction/pkg/service/photoWall"
	"github.com/gin-gonic/gin"
)

func PhotoWallRoute(r *gin.RouterGroup) {
	noAuth := r.Group("")
	photoWallNoAuth := noAuth.Group("photo-wall")
	{
		photoWallNoAuth.POST("save", photoWall.SavePhoto)
		photoWallNoAuth.POST("update", photoWall.UpdatePhoto)
		photoWallNoAuth.POST("query", photoWall.QueryPhoto)
		photoWallNoAuth.GET("query", photoWall.QueryPhotoGet)
		photoWallNoAuth.POST("query-list", photoWall.QueryPhotoList)
	}

	{
		photoWallNoAuth.GET("sse/query-list", photoWall.QueryPhotoListSse)
	}

	{
		photoWallNoAuth.GET("ws/save", photoWall.SavePhotoWs)
		photoWallNoAuth.GET("ws/query-list", photoWall.QueryPhotoListWs)
	}
}
