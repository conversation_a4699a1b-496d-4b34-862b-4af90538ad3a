package mobile

import (
	"github.com/fonchain_enterprise/client-auction/pkg/middleware"
	"github.com/fonchain_enterprise/client-auction/pkg/service"
	"github.com/fonchain_enterprise/client-auction/pkg/service/auction/mobile"
	"github.com/fonchain_enterprise/client-auction/pkg/service/shop"
	"github.com/fonchain_enterprise/client-auction/pkg/service/task"
	"github.com/gin-gonic/gin"
)

// MobileShopRoute 商城移动端的路由
func MobileShopRoute(r *gin.RouterGroup) {

	Router := r.Group("m/shop")

	Router.POST("scan/info", mobile.ScanSecretInfo)
	Router.POST("scan/receive", mobile.ReceiveScanSecretInfo)
	Router.POST("stripe/webhook", mobile.StripeWebhook) // Stripe支付成功回调,更新订单
	Router.POST("scan/order/update", mobile.UpdateCultureOrder)
	Router.POST("scan/order/pay", mobile.StripeCreate)
	{
		salesRoute := Router.Group("auction")
		salesRoute.POST("detail", mobile.Detail)            //系列列表查询
		salesRoute.POST("artwork/list", mobile.ArtworkList) //系列列表查询
	}

	series := Router.Group("series")
	series.Use(middleware.CheckLoginFans(service.AccountProvider))
	{
		series.POST("save", shop.SaveSeries)
		series.POST("detail", shop.SeriesDetail)
		series.POST("update-lang", shop.UpdateSeriesLang)
		series.POST("autoshelf", shop.AutoShelf)
		series.POST("handshelf", shop.HandShelf)
		series.POST("series-list", shop.SeriesList)
		series.POST("del", shop.SeriesDel)
		series.POST("get/series/language/info", shop.GetSeriesLanguageInfo)
		series.POST("culture/list", shop.CultureList)         // TODO 获取某个文创系列的列表（已售完毕 ）
		series.POST("circulation/list", shop.CirculationList) //
		series.POST("culture/detail", mobile.CultureInfo)
		//series.POST("scan/info", shop.ScanInfo)               // TODO 扫码弹出相应数据（金额，商品名模型，领取状态等待领取，已领取等等）
	}

	brand := Router.Group("brand")
	brand.Use(middleware.CheckLoginFans(service.AccountProvider))
	{
		brand.POST("create", shop.CreateBrand)
		brand.POST("info", shop.BrandInfo)
		brand.POST("update", shop.BrandUpdate)
		brand.POST("del", shop.BrandDel)
		brand.POST("list", shop.BrandList)
		brand.POST("allbrand", shop.AllBrand)
		brand.POST("update/language/info", shop.UpdateLanguageInfo)
		brand.POST("get/all/language/info", shop.GetAllLanguageInfo)
	}

	feedback := Router.Group("feedback")
	feedback.Use(middleware.CheckLoginFans(service.AccountProvider))
	{
		feedback.POST("create", task.FeedBackCreate)
		feedback.POST("top", task.Topping)
		feedback.POST("list", task.FeedBackList)
	}

	recordRouter := Router.Group("record")
	recordRouter.Use(middleware.CheckLoginFans(service.AccountProvider))
	{
		//recordRouter.POST("list", shop.RecordList)
	}

	//和文创系列有关的，我的数据
	userRouter := Router.Group("user/culture")
	userRouter.POST("order/detail", shop.CultureDetail) // TODO 我的文创订单详情
	userRouter.Use(middleware.CheckLoginFans(service.AccountProvider))
	{
		userRouter.POST("order/list", shop.UserCultureList)                // 两个接口合二为一 文创我的列表 外加一个订单个数
		userRouter.POST("circulation/list", shop.FansCirculationList)      //
		userRouter.POST("transfer", shop.TransferCulture)                  //
		userRouter.POST("receive", shop.DrawCulture)                       // TODO 首次赠送的领取接口
		userRouter.POST("transfer/cancel", shop.RefuseCultureArtworkOrder) // TODO 被赠送人取消
		userRouter.POST("transfer/sure", shop.ReceiveCultureArtworkOrder)  // TODO 被赠送人确认收货
	}

}
