// Package mobile -----------------------------
// @file      : mainPage.go
// <AUTHOR> JJXu
// @contact   : <EMAIL>
// @time      : 2025/4/12 12:55
// -------------------------------------------
package mobile

import (
	"github.com/fonchain_enterprise/client-auction/pkg/middleware"
	"github.com/fonchain_enterprise/client-auction/pkg/service"
	"github.com/fonchain_enterprise/client-auction/pkg/service/fenghe/handle"
	"github.com/gin-gonic/gin"
)

// 销售情况
func MainPageRouter(g *gin.RouterGroup) {
	//noAuthRouter := g.Group("")
	authRouter := g.Group("").Use(middleware.CheckLoginFans(service.AccountProvider))
	{
		//------------------------------主页
		authRouter.POST("seriesProfile/query", handle.GetSeriesProfileList)     //系列列表查询
		authRouter.POST("series/show/detail", handle.ShowDetail)                //系列画展详情
		authRouter.POST("series/show/artwork/detail", handle.ShowDetailArtwork) //系列画展详情
		authRouter.POST("series/detail", handle.ShowDetail)                     //系列画展详情
	}

}
