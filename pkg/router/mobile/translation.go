package mobile

import (
	"github.com/fonchain_enterprise/client-auction/pkg/service/fenghe/handle"
	"github.com/gin-gonic/gin"
)

// 翻译相关接口
func TranslationRouter(g *gin.RouterGroup) {
	translationRouter := g.Group("")
	{
		translationRouter.POST("translation/image", handle.TranslationImage) //图片翻译
		translationRouter.POST("translation/audio", handle.TranslationAudio) //音频翻译
		translationRouter.POST("translation/text", handle.TranslationText)   //文本翻译
	}
}
