// Package mobile -----------------------------
// @file      : payment.go
// <AUTHOR> JJXu
// @contact   : <EMAIL>
// @time      : 2025/4/15 18:28
// -------------------------------------------
package mobile

import (
	"github.com/fonchain_enterprise/client-auction/pkg/middleware"
	"github.com/fonchain_enterprise/client-auction/pkg/service"
	"github.com/fonchain_enterprise/client-auction/pkg/service/fenghe/handle"
	"github.com/gin-gonic/gin"
)

func PaymentRouter(g *gin.RouterGroup) {
	NoAuthRouter := g.Group("payment")
	AuthRouter := g.Group("payment").Use(middleware.CheckLoginFans(service.AccountProvider))
	{
		/*
				payment/hook/stripe
			    payment/hook/wepay
				payment/hook/alipay
		*/
		NoAuthRouter.POST("hook/:platform", handle.PaymentWebHook) //支付回调
		NoAuthRouter.POST("info", handle.PaymentInfo)              //查询交易流水信息
		NoAuthRouter.POST("order/in/refund", handle.RefundOrder)   //退款-自己用
	}

	{
		AuthRouter.POST("order/create", handle.CreateOrder)                    //创建订单
		AuthRouter.POST("myOrders", handle.OrderRecords)                       //查询订单列表
		AuthRouter.POST("myOrder", handle.OrderRecordInfo)                     //查询订单列表
		AuthRouter.POST("order/receive/sms", handle.SendSeriesOrderReceiveSms) //发送确认收货短信验证码
		AuthRouter.POST("order/receive/confirm", handle.ConfirmReceiveOrder)   //确认收货
		AuthRouter.POST("order/qr", handle.OrderQr)                            //show订单的二维码
		AuthRouter.POST("order/done", handle.OrderDone)                        //订单完成 确认收货接口
		AuthRouter.POST("order/contract/done", handle.OrderContractDone)       //订单完成 确认收货接口
	}

}
