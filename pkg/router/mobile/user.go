package mobile

import (
	"github.com/fonchain_enterprise/client-auction/pkg/middleware"
	"github.com/fonchain_enterprise/client-auction/pkg/service"
	"github.com/fonchain_enterprise/client-auction/pkg/service/account"
	"github.com/gin-gonic/gin"
)

// userRoute 商城移动端的路由
func userRoute(r *gin.RouterGroup) {

	Router := r.Group("m/shop/v2")

	companyRoute := Router.Group("company")
	companyRoute.Use(middleware.CheckLoginFans(service.AccountProvider))
	{
		companyRoute.POST("all", account.AllCompany) //获取所有公司 Title
	}

	uRoute := Router.Group("user")
	uRoute.Use(middleware.CheckLoginFans(service.AccountProvider))
	{
		//uRoute.POST("/company", shop.UpdateSeriesLang)     //获取绑定的我的公司,放在user_extend 即归属的销售所述的销售公司
		//uRoute.POST("bind/company", account.BindCompany) //删除
		uRoute.POST("bind/url", account.BindSellerQr)        //生成绑定的二维码接口
		uRoute.POST("recommend/info", account.RecommendInfo) //TODO 获取我的推荐人 和 归属的公司
	}
}
