// Package adm -----------------------------
// @file      : salesState.go
// <AUTHOR> JJXu
// @contact   : <EMAIL>
// @time      : 2025/4/12 12:52
// -------------------------------------------
package adm

import (
	"github.com/fonchain_enterprise/client-auction/pkg/middleware"
	"github.com/fonchain_enterprise/client-auction/pkg/service"
	"github.com/fonchain_enterprise/client-auction/pkg/service/fenghe/handle"
	"github.com/gin-gonic/gin"
)

// 销售情况
func SalesSituationRouter(g *gin.RouterGroup) {
	admNoAuthRouter := g.Group("/adm")
	{
		//------------------------------销售情况
		salesRoute := admNoAuthRouter.Group("sales")
		salesAuthErpRouter := admNoAuthRouter.Group("sales").Use(middleware.CheckLogin(service.AccountProvider))
		salesRoute.POST("seriesProfile/query", handle.GetAdmSeriesProfileList)            //系列列表
		salesRoute.POST("order/query", handle.SeriesOrderRecord)                          //订单列表
		salesRoute.POST("order/sale/update", handle.UpdateSeriesOrderSaleStatus)          //更新订单的销售站点和销售id数据
		salesRoute.POST("order/accountEntry/update", handle.UpdateShoeSeriesAccountEntry) //更新入账信息
		salesRoute.POST("payrecord/query", handle.PayRecordList)                          //付款记录
		salesRoute.POST("seriesProfile/initCacheIndex", handle.InitCacheIndex)            //初始化索引
		salesRoute.GET("orderExport", handle.OrderExport)                                 //导出订单
		salesRoute.GET("order/culture/orderExport", handle.CultureOrderExport)            // 导出文创类型订单
		//------------------------------支付证明
		salesAuthErpRouter.POST("order/paymentPprof/create", handle.CreateSeriesPaymentPprof) //创建支付证明，现在用来确认付款
		//salesRoute.POST("order/paymentPprof/delete", handle.DeleteSeriesPaymentPprof)
		//salesRoute.POST("order/paymentPprof/update", handle.UpdateSeriesPaymentPprof)
		//salesRoute.POST("order/paymentPprof/detail", handle.GetSeriesPaymentPprofDetail)
		//salesRoute.POST("order/paymentPprof/query", handle.GetSeriesPaymentPprofList)
		salesAuthErpRouter.POST("order/user/replace", handle.OrderUserReplace)
		salesAuthErpRouter.POST("order/culture/create", handle.CultureOrderCreate) //创建文创类型订单
	}
}
