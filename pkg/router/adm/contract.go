// Package adm -----------------------------
// @file      : serialArtworkLang.go
// <AUTHOR> JJXu
// @contact   : <EMAIL>
// @time      : 2025/4/12 14:32
// -------------------------------------------
package adm

import (
	serviceContractV2 "github.com/fonchain_enterprise/client-auction/pkg/service/contract/v2"
	"github.com/gin-gonic/gin"
)

func ContractRoute(g *gin.RouterGroup) {
	admNoAuthRouter := g.Group("adm/contract")
	{
		admNoAuthRouter.POST("download-contract", serviceContractV2.DownloadContract)
	}
}
