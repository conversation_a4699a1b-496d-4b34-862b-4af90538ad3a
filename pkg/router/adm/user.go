package adm

import (
	"github.com/fonchain_enterprise/client-auction/pkg/middleware"
	"github.com/fonchain_enterprise/client-auction/pkg/service"
	"github.com/fonchain_enterprise/client-auction/pkg/service/account"
	"github.com/gin-gonic/gin"
)

// AdminUserRoute 商城管理的路由
func AdminUserRoute(r *gin.RouterGroup) {
	userRouter := r.Group("adm/user")

	{
		userRouter.Use(middleware.CheckLogin(service.AccountProvider)) //登陆校验中间件
		userRouter.POST("review/list", account.Reviews)                //审核列表
		userRouter.POST("review", account.Review)                      //
	}
}
