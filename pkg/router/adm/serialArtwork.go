// Package adm -----------------------------
// @file      : serialArtworkLang.go
// <AUTHOR> JJXu
// @contact   : <EMAIL>
// @time      : 2025/4/12 14:32
// -------------------------------------------
package adm

import (
	"github.com/fonchain_enterprise/client-auction/pkg/service/fenghe/handle"
	"github.com/gin-gonic/gin"
)

func SeriesArtwork(g *gin.RouterGroup) {
	admNoAuthRouter := g.Group("adm")
	{
		//------------------------------SeriesArtwork 系列画作增删改查
		admNoAuthRouter.POST("seriesArtwork/create", handle.CreateSeriesArtwork)
		admNoAuthRouter.POST("seriesArtwork/delete", handle.DeleteSeriesArtwork)
		admNoAuthRouter.POST("seriesArtwork/update", handle.UpdateSeriesArtwork)
		admNoAuthRouter.POST("seriesArtwork/detail", handle.GetSeriesArtworkDetail)
		admNoAuthRouter.POST("seriesArtwork/query", handle.GetSeriesArtworkList)
		//------------------------------SeriesArtworkLang 系列画作多语言增删改查
		admNoAuthRouter.POST("seriesArtworkLang/save", handle.SaveSeriesArtworkLang)
		admNoAuthRouter.POST("seriesArtworkLang/delete", handle.DeleteSeriesArtworkLang)
		admNoAuthRouter.POST("seriesArtworkLang/detail", handle.GetSeriesArtworkLangDetail)
		admNoAuthRouter.POST("seriesArtworkLang/query", handle.GetSeriesArtworkLangList)
		//------------------------------查询画作系统的画作
		admNoAuthRouter.POST("artwork/query", handle.GetArtworkListInArtworkService)
	}
}
