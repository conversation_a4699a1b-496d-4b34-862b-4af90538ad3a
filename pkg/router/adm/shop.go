package adm

import (
	"github.com/fonchain_enterprise/client-auction/pkg/middleware"
	"github.com/fonchain_enterprise/client-auction/pkg/service"
	"github.com/fonchain_enterprise/client-auction/pkg/service/account"
	"github.com/fonchain_enterprise/client-auction/pkg/service/shop"
	"github.com/fonchain_enterprise/client-auction/pkg/service/task"
	"github.com/gin-gonic/gin"
)

// AdmShopRoute 商城管理的路由
func AdmShopRoute(r *gin.RouterGroup) {
	Router := r.Group("adm/shop")
	series := Router.Group("series")
	r.GET("example/dwom", shop.ExportScanListExam)
	//series.Use(middleware.CheckLogin(service.AccountProvider))
	{
		series.POST("save", shop.SaveSeries)
		series.POST("detail", shop.SeriesDetail)
		series.POST("update-lang", shop.UpdateSeriesLang)
		series.POST("autoshelf", shop.AutoShelf)
		series.POST("handshelf", shop.HandShelf)
		series.POST("series-list", shop.SeriesList)
		series.POST("del", shop.SeriesDel)
		series.POST("get/language/info", shop.GetSeriesLanguageInfo)
		series.POST("culture/save", shop.SaveCultureArtworkBefore)
		series.GET("culture/zip/down", shop.Extend)
		series.GET("scan/list/export", shop.ExportScanList)
		series.POST("scan/list", shop.ScanList)
		series.POST("circulation/list", shop.CirculationList)
	}

	companyRoute := Router.Group("company")
	companyRoute.Use(middleware.CheckLogin(service.AccountProvider))
	{
		companyRoute.POST("all", account.AllCompany) //获取所有的销售站点
	}

	brand := Router.Group("brand")
	brand.Use(middleware.CheckLogin(service.AccountProvider))
	{
		brand.POST("create", shop.CreateBrand)
		brand.POST("info", shop.BrandInfo)
		brand.POST("update", shop.BrandUpdate)
		brand.POST("del", shop.BrandDel)
		brand.POST("list", shop.BrandList)
		brand.POST("allbrand", shop.AllBrand)
		brand.POST("update/language/info", shop.UpdateLanguageInfo)
		brand.POST("get/all/language/info", shop.GetAllLanguageInfo)
	}

	feedback := Router.Group("feedback")
	feedback.Use(middleware.CheckLogin(service.AccountProvider))
	{
		feedback.POST("create", task.FeedBackCreate)
		feedback.POST("top", task.Topping)
		feedback.POST("list", task.FeedBackList)
	}

	recordRouter := Router.Group("record")
	recordRouter.Use(middleware.CheckLogin(service.AccountProvider))
	{
		//recordRouter.POST("list", shop.RecordList)
	}

	currencyRouter := Router.Group("currency")
	currencyRouter.Use(middleware.CheckLogin(service.AccountProvider))
	{
		currencyRouter.POST("info", shop.CurrencyInfo)
	}

}
