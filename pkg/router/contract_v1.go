package router

import (
	"github.com/fonchain_enterprise/client-auction/pkg/middleware"
	"github.com/fonchain_enterprise/client-auction/pkg/service"
	serviceContractV1 "github.com/fonchain_enterprise/client-auction/pkg/service/contract/v1"
	"github.com/gin-gonic/gin"
)

func ContractRoute(r *gin.RouterGroup) {
	auth := r.Group("")
	noAuth := r.Group("")
	auth.Use(middleware.CheckLoginFans(service.AccountProvider))
	contract := auth.Group("contract")
	{
		contract.POST("sign-online", serviceContractV1.SignOnline)
		contract.POST("contract-view", serviceContractV1.ContractView)
		contract.POST("sign-view", serviceContractV1.SignView)
		contract.POST("user-contract-count", serviceContractV1.UserContractCount)
		contract.POST("series-order-view", serviceContractV1.SeriesOrderContractView)
		contract.POST("series-order-sign", serviceContractV1.SeriesOrderContractSign)
	}

	contractNoAuth := noAuth.Group("contract")
	{
		contractNoAuth.POST("upload-template", serviceContractV1.UploadTemplate)
		contractNoAuth.POST("sign-offline", serviceContractV1.SignOffline)
		contractNoAuth.GET("fdd-register-callback", serviceContractV1.FddRegisterCallback)
		contractNoAuth.GET("fdd-sign-callback", serviceContractV1.FddSignCallback)
		contractNoAuth.POST("fdd-info", serviceContractV1.FddInfo)
		contractNoAuth.POST("userinfo", serviceContractV1.UserInfo)
		contractNoAuth.POST("contract-view-offline", serviceContractV1.ContractViewOffline)
		contractNoAuth.POST("test", serviceContractV1.Test)
		contractNoAuth.POST("repair-fdd-contract", serviceContractV1.RepairFddContract)
	}
}
