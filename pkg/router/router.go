package router

import (
	"github.com/fonchain_enterprise/client-auction/cmd/config"
	"github.com/fonchain_enterprise/client-auction/pkg/middleware"
	"github.com/fonchain_enterprise/client-auction/pkg/router/adm"
	"github.com/fonchain_enterprise/client-auction/pkg/router/mobile"
	"github.com/fonchain_enterprise/client-auction/pkg/service"
	"github.com/fonchain_enterprise/client-auction/pkg/service/account"
	"github.com/fonchain_enterprise/client-auction/pkg/service/auction"
	"github.com/fonchain_enterprise/client-auction/pkg/service/auction/artwork"
	"github.com/fonchain_enterprise/client-auction/pkg/service/auctionSession"
	"github.com/fonchain_enterprise/client-auction/pkg/service/buy"
	"github.com/fonchain_enterprise/client-auction/pkg/service/common"
	"github.com/gin-contrib/gzip"
	"github.com/gin-gonic/gin"
	"go.opentelemetry.io/contrib/instrumentation/github.com/gin-gonic/gin/otelgin"
	"net/http"
)

// 路由配置
func NewRouter() *gin.Engine {
	//使用默认gin路由
	r := gin.Default()

	if config.AppConfig.Otel.Open {
		r.Use(otelgin.Middleware("auction-mall"))
	}
	r.StaticFS("/api/v1/fenghe/adm/static/template", http.Dir("static/template"))
	r.StaticFS("/api/v1/fenghe/adm/runtime", http.Dir("runtime"))
	r.Use(middleware.Cors())
	v1 := r.Group("api/v1/fenghe")
	v2 := r.Group("api/v2/fenghe")

	adminV1 := v1.Group("/adm/auction")
	adminV1.Use(gzip.Gzip(gzip.BestSpeed)) // 中间件占用绝大部分内存
	mobileV1 := v1.Group("m")
	ContractRoute(v1)
	ContractV2Route(v2)
	mobile.MobileRouter(v1)
	adm.AdmRouter(v1)
	PhotoWallRoute(v1)

	//TODO 管理系统 三个地方使用websocket 直播控制台两个地方(藏品列表、当前拍品的出价)  大盘显示那边
	{

		//websocket
		{
			wsRoute := adminV1.Group("")
			wsRoute.Use(middleware.CheckLoginWsBase(service.AccountProvider)) //登陆校验中间件
			wsRoute.GET("artwork/buy/list/live", artwork.BuyListLive)         //ws出价记录
			wsRoute.GET("artwork/screen", buy.ScreenInfo)                     //TODO 当前大屏的艺术品数据 同时websocket
		}

		adminV1.Use(middleware.CheckLogin(service.AccountProvider)) //登陆校验中间件
		{
			auctionRoute := adminV1.Group("")
			auctionRoute.POST("detail", auction.Detail)
			auctionRoute.POST("update", auction.Update)
			auctionRoute.POST("regions", auction.Regions)
			auctionRoute.POST("lang/update", auction.UpdateLang)
			auctionRoute.POST("remove", auction.Remove)
			auctionRoute.POST("create", auction.Create)
			auctionRoute.POST("list", auction.List)
			auctionRoute.POST("update/end_num", auction.UpdateEndNum)

			auctionRoute.POST("default/detail", auction.DefaultDetail)
			auctionRoute.POST("default/update", auction.DefaultUpdate)
			auctionRoute.POST("artworks", artwork.List)

			auctionRoute.POST("live/create", auction.CreateLive) //生成直播连接
			auctionRoute.POST("live/set", auction.SetLive)       //开始直播 中止直播
		}

		{ //某场次下的藏品
			artworkRoute := adminV1.Group("/artwork")
			artworkRoute.POST("list", auction.AdminArtworkList)     //某个场次下的艺术品
			artworkRoute.POST("detail", auction.AdminArtworkDetail) //某个场次下的艺术品
			artworkRoute.POST("update", auction.ArtworkUpdate)
			artworkRoute.POST("lang/update", auction.ArtworkLangUpdate)
			artworkRoute.POST("buy/list", artwork.BuyList)           //出价记录
			artworkRoute.POST("buy/local/create", artwork.BuyCreate) //录入现场价 现场成交价录入(把当前需要确认有效的改成无效)
			artworkRoute.POST("buy/sell/status", artwork.SellStatus) //开拍停拍
			artworkRoute.POST("buy/msg", artwork.QuickMsg)           // ws 提醒即将落锤
			artworkRoute.POST("buy/screen", artwork.ScreenStatus)    //投屏/取消
			artworkRoute.POST("buy/sure", artwork.Sure)              //成交确认
			artworkRoute.POST("buy/cancel", artwork.Cancel)          //成交确认
			artworkRoute.POST("buy/effect", artwork.Effect)          //确认有效
		}

		{ //某场次下的藏品
			buyRoute := adminV1.Group("/buy")
			buyRoute.POST("list", buy.List)          //成交记录
			buyRoute.POST("paid/list", buy.PaidList) //支付记录
		}

		{ //粉丝
			fansRoute := v1.Group("/adm/auction/user")
			fansRoute.POST("list", account.List) //TODO 粉丝列表
			fansRoute.POST("qr", account.Qr)     //TODO 二维码
		}
	}

	{
		{
			userV1 := mobileV1.Group("/user")
			//userV1.Use(gzip.Gzip(gzip.BestSpeed))                // 中间件占用绝大部分内存
			userV1.GET("ratesyn", account.SynRate)               //生成验证码
			userV1.POST("captcha", account.GenCaptcha)           //生成验证码
			userV1.POST("send", account.SendMsgBeforeLogin)      //TODO 增加验证码 发送短息(国际，国内)
			userV1.POST("login", account.UserLogin)              //TODO 验证短信
			userV1.POST("channel/test", auction.SendFansTest)    //测试广播推送
			userV1.POST("mobile/send", account.MobileUserSend)   // 手机端登录，完全复制erp
			userV1.POST("mobile/login", account.MobileUserLogin) // 手机端登录，完全复制erp
			//回调信息如下 https://127.0.0.1:9021/api/v1/m/user/fdd/middle/url?key=5452_4815&time=**********&personName=%E8%80%BF%E9%98%B3&transactionNo=c55993827df841cf88f32338ff361096&authenticationType=1&status=2&sign=NDNFNDczMTAzNURGNTRCRjI1OTgxREE4MUEzN0E1MzhCMjMzOTAwQw==
			userV1.GET("fdd/middle/url", account.SureBindFdd)              //法大大认证是否通过后跳转中间页 仅后端使用
			userV1.Use(middleware.CheckLoginFans(service.AccountProvider)) //登陆校验中间件
			userV1.GET("send", account.SendMsg)                            //对本手机发送验证码
			userV1.POST("phone/send", account.UpdateTelSend)               //修改手机号发送验证码
			userV1.POST("phone/send/token", account.UpdateTelSendByToken)  // 更新手机号时候发送验证码
			userV1.POST("phone/update", account.UpdateTel)                 //更新新手机号,校验手机号(token)
			userV1.POST("fdd/check", account.CheckFdd)                     //获取法大大是否认证过接口
			userV1.POST("update", account.UpdateMyInfo)                    //更新个人信息，和法大大信息
			userV1.POST("my/info", account.MyInfo)                         //TODO 同步更新自己的信息
			userV1.POST("artworks", account.Artworks)                      //TODO 我买过的的藏品
			userV1.POST("artwork", account.Artwork)                        //TODO 我买过的的藏品
			userV1.POST("ocr", account.CheckIdOcr)                         //TODO 我买过的的藏品
		}

		{ //
			auctionRoute := mobileV1.Group("/auction")

			{
				outRoute := auctionRoute.Group("out")
				outRoute.Use(middleware.RateLimit())
				outRoute.POST("log/sendlog/aljdfoqueoirhkjsadhfiu", auction.GetLiveUrl)
				outRoute.POST("buy/list", artwork.OutBuyList)
				outRoute.POST("default/detail", auction.Detail)
			}

			auctionRoute.Use(middleware.CheckLoginFans(service.AccountProvider)) //登陆校验中间件
			auctionRoute.POST("default/detail", auction.Detail)                  //场次信息
			auctionRoute.POST("default/artwork/list", auction.ArtworkList)       //拍品列表
			auctionRoute.POST("live/test", artwork.BigScreenTest)
			auctionRoute.POST("log/sendlog", auction.GetLiveUrl) //获取加密后的直播地址
		}

		{
			liveRoute := mobileV1.Group("/auction")
			liveRoute.Use(middleware.CheckLoginFansWs(service.AccountProvider)) //登陆校验中间件
			liveRoute.GET("live", artwork.BuyLive)                              //websocket （LOT 标题、index） （当前价、下口价格） (拍品价格列表) (消息)
		}

		{ //
			artworkRoute := mobileV1.Group("/artwork")
			artworkRoute.Use(middleware.CheckLoginFans(service.AccountProvider)) //登陆校验中间件
			artworkRoute.POST("detail", artwork.AuctionArtworkDetail)            //场次信息
			artworkRoute.POST("buy", artwork.FansBuyCreate)                      //出价
		}

	}

	{
		commonRoute := v1.Group("/common")
		commonRoute.POST("check/phone", account.CheckPhone) // 校验手机号是否是erp系统
		//commonRoute.Use(middleware.CheckLogin(service.AccountProvider, true)) //登陆校验中间件
		commonRoute.POST("upload", common.UploadImg) //上传
	}
	auctionSession.InitRouter(v1)
	//静态文件
	v1.StaticFS("/static", http.Dir("./runtime"))
	return r
}
