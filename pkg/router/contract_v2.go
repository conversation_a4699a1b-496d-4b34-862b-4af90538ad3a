package router

import (
	"fmt"
	"github.com/fonchain_enterprise/client-auction/pkg/middleware"
	"github.com/fonchain_enterprise/client-auction/pkg/service"
	serviceContractV2 "github.com/fonchain_enterprise/client-auction/pkg/service/contract/v2"
	"github.com/gin-gonic/gin"
)

func ContractV2Route(r *gin.RouterGroup) {
	auth := r.Group("")
	noAuth := r.Group("")
	fmt.Println(1)
	auth.Use(middleware.CheckLoginFans(service.AccountProvider))
	contract := auth.Group("contract")
	{
		contract.POST("sign-online", serviceContractV2.SignOnline)
		contract.POST("view", serviceContractV2.ViewContract)
		contract.POST("need-sign", serviceContractV2.NeedSign)
		contract.POST("artwork-contract-view", serviceContractV2.ArtworkContractView)
		contract.POST("signed-auction-list", serviceContractV2.SignedAuctionList)
		contract.POST("signed-auction-detail", serviceContractV2.SignedAuctionContracts)
		contract.POST("download-contract", serviceContractV2.DownloadContract)
		contract.POST("test", serviceContractV2.Test)
	}

	contractNoAuth := noAuth.Group("contract")
	{
		contractNoAuth.GET("fdd-sign-callback", serviceContractV2.FddSignCallback)
	}
}
