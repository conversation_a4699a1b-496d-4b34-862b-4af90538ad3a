package e

var (
	JWTSecret = []byte("asdfqwer1234")
)

const (
	AddUser                 = "addUser"                 //下发人员信息
	EditUser                = "editUser"                //修改人员信息
	DelUser                 = "delUser"                 //删除人员信息
	DelMultiUser            = "delMultiUser"            //批量删除人员
	DelAllUserRet           = "delAllUserRet"           //删除全部人员
	AddVisitRet             = "addVisitRet"             //下发人证对比
	OnlineAuthorization     = "onlineAuthorization"     //抓拍现场照片
	Reboot                  = "reboot"                  //重启设备
	VerifyPhoto             = "verifyPhoto"             //检测图片质量
	AddPassRule             = "addPassRule"             //下发人员通行规则
	GetPassRule             = "getPassRule"             //获取通行规则
	DelPassRule             = "delPassRule"             //删除通行规则
	SetPasswordVerify       = "setPasswordVerify"       //设备密码开门开关
	SetOpenDoorPassword     = "setOpenDoorPassword"     //设置设备开门密码
	UploadFaceInfo          = "uploadFaceInfo"          //通知设备同步人员数据
	GetUserInfo             = "getUserInfo"             //查询设备人员数量和id
	GetUserDetails          = "getUserDetails"          //根据userid读取用户数据
	GetDeviceSettings       = "getDeviceSettings"       //获取设备设置
	CustomHomeLogo          = "customHomeLogo"          //设置设备 logo
	AetAd                   = "setAd"                   //设置广告语
	SetTime                 = "setTime"                 //设置时间
	SetDoor                 = "setDoor"                 //设置开关门
	SetRecognitionInterval  = "setRecognitionInterval"  //设置识别间隔
	SetRecognitionDistance  = "setRecognitionDistance"  //设置识别距离
	SetMaskDetection        = "setMaskDetection"        //口罩检测开关
	SetLivenessDetection    = "setLivenessDetection"    //活体检测
	SetLivenessLevel        = "setLivenessLevel"        //活体检测等级
	SetStrangerRecognition  = "setStrangerRecognition"  //陌生人识别
	SetVolume               = "setVolume"               //设置设备音量
	SetPlayUserName         = "setPlayUserName"         //播放用户名
	SetVoice                = "setVoice"                //设置设备提示语音
	SetPassword             = "setPassword"             //修改进入设置页面密码
	SetRelayState           = "setRelayState"           //设置继电器状态-常开/正常
	SetRecordPictureQuality = "setRecordPictureQuality" //设置设备识别照片抓拍的质量
	SetVisitorCallStatus    = "setVisitorCallStatus"    //访问呼叫开关
	SetPrinterStatus        = "setPrinterStatus"        //打印机开关
	//SetVisitorApplyValue            = "setVisitorApplyValue"            //首页访客申请入库内容
	SetHttpToken                    = "setHttpToken"                    //设置http请求的token
	SetVerifyTimeout                = "setVerifyTimeout"                //设置验证接口超时时长
	SetOpenDoorDelay                = "setOpenDoorDelay"                //设置设备开门延迟
	SetAdminLongPressTime           = "setAdminLongPressTime"           //进入设置页面长按时间
	SetDesensitizationName          = "setDesensitizationName"          //姓名脱敏显示
	SetReadCardInfo                 = "setReadCardInfo"                 //设置读卡扇区
	SetGeofence                     = "setGeofence"                     //设置设备位置围栏
	SetScreensaverState             = "setScreensaverState"             //设备节能屏保开关
	AddCarousePicture               = "addCarousePicture"               //设置设备屏保
	DelCarouselPic                  = "delCarouselPic"                  //删除屏保
	SetScreensaverInterval          = "setScreensaverInterval"          //设置屏保显示时长
	SetTemperatureDetection         = "setTemperatureDetection"         //体温检测开关
	SetTemperatureMode              = "setTemperatureMode"              //体温检测模式
	SetPreliminaryScreeningStandard = "setPreliminaryScreeningStandard" //初筛模式标准
	SetTemperatureCalibrationMode   = "setTemperatureCalibrationMode"   //设置体温校准模式
	SetLowTemperaturePass           = "setLowTemperaturePass"           //低温可通行开关
	SetPlayTemperature              = "setPlayTemperature"              //播报体温开关
	SetReflectivity                 = "setReflectivity"                 //设置体温反射率
	SetTemperatureCorrection        = "setTemperatureCorrection"        //设置体温校准值
	SetTemperatureDetectDistance    = "setTemperatureDetectDistance"    //设置设备体温检测距离
	SetMeasureTemperatureTime       = "setMeasureTemperatureTime"       //设置测温时间段
	SetAutoUpdate                   = "setAutoUpdate"                   //开机自动更新
	GetVersionInfo                  = "getVersionInfo"                  //获取设备版本信息
	PushVersionInfo                 = "pushVersionInfo"                 //发送设备版本信息给服务端
	CheckForUpdates                 = "checkForUpdates"                 //通知设备在线检查更新
	SetVisitorApplyValue            = "setVisitorApplyValue"            //设置设备首页二维码
	SetVisitorQRCodePrefix          = "setVisitorQRCodePrefix"          //设置访客二维码前缀
	SetPanoramicCamera              = "setPanoramicCamera"              //全景相机开关
	SetPanoramicCameraParams        = "setPanoramicCameraParams"        //设置全景相机的参数
	SetRecognitionResultStyle       = "setRecognitionResultStyle"       //识别结果样式
	SetOnlineVerifyAfterRecognition = "setOnlineVerifyAfterRecognition" //识别后在线验证
	SetOnlineVerifyCard             = "setOnlineVerifyCard"             //在线验证卡号
	SetOnlineVerifyIdCard           = "setOnlineVerifyIdCard"           //在线验证身份证号
	SetOnlineRecognitionInterval    = "setOnlineRecognitionInterval"    //设置在线识别间隔
	SetOnlineRecognitionState       = "setOnlineRecognitionState"       //在线识别开关
	SetOnlineRecognition            = "setOnlineRecognition"            //临时启用在线识别
	SetPassType                     = "setPassType"                     //设置可通行人员类型
	SetRecognitionLevel             = "setRecognitionLevel"             //设置设备识别置信度
	SetSwitch                       = "setSwitch"                       //开启/关闭设备识别
	SetDisablePass                  = "setDisablePass"                  //停用状态通行人数
	AddImageAd                      = "addImageAd"                      //下发广告 - 图片
	DelAd                           = "delAd"                           //删除广告
	ChangeADStatus                  = "changeADStatus"                  //启用停用广告
	GetDoorLockState                = "getDoorLockState"                //获取(上报)门锁状态
	SetDoorLockListener             = "setDoorLockListener"             //设置门锁实时监听开关
)
const (
	AuthFinanceKey             = "auth_search_finance"   //财务
	AuthTrainingKey            = "auth_search_training"  //教培
	AuthPublicizeKey           = "auth_search_publicize" //宣传
	AuthWarehouseKey           = "auth_search_warehouse" //仓库
	AuthDriveKey               = "ge_diver_auth"
	AuthEmployeeAllKey         = "auth_search_employee"     //员工档案查看权限
	AuthEmployeeMaiKey         = "auth_search_mai_employee" //员工档案查看权限
	AuthDriveSupervisorKey     = "ge_driver_supervisor_auth"
	SellerBossKey              = "seller_mobile_boss_auth"
	SellerStaffKey             = "seller_mobile_staff_auth"
	SellerTellerKey            = "seller_teller_auth"
	DepartmentKey              = "department_boss_auth"
	ExhibitionInsideKey        = "exhibition_inside_key" //画展包内部
	ExhibitionShowKey          = "exhibition_show_key"   //画展包画展数据
	QueryCanShow               = "can_show_exh"          //查看可画展画展包
	SecretDataBoss             = "secret_data_boss"      //查看可画展画展包加密数据
	AuthArtworkFix             = "artwork-fix-button-mobile-auth"
	AUthOnlySiteLogin          = "only-site-login"
	AuthCollectionAllDetailKey = "auth_collection_all_detail" //员工考勤查看权限
	AuthCollectionMaiDetailKey = "auth_collection_mai_detail" //员工考勤亿麦查看权限
	AuthPayQRCode              = "auth_pay_qr_code"           // 拍卖的收款二维码权限
)

const (
	ErrNotLogin        = "ErrNotLogin"
	ErrOffline         = "ErrOffline"
	ErrOfflineSqueeze  = "您的账号已在其他设备登录"
	ErrAccountNotExist = "当前手机号不在系统中，请您确认手机号码正确性"
	MsgSendWrong       = "已经发送过，验证码尚可用"
)

var LoginMap = map[string]string{
	"ErrNotLoginZhCN": "请先登录",
	"ErrNotLoginEN":   "Please login first",
	"ErrNotLoginZhTW": "請先登錄",
}

var OfflineMap = map[string]string{
	"ErrOfflineZhCN": "您已经下线",
	"ErrOfflineEN":   "You have been offline",
	"ErrOfflineZhTW": "您已經下線",
}

const (
	Authorization = "Authorization"
)

const (
	ApprovalWorkStatusDoing = 1
	ApprovalWorkStatusOk    = 2
	ApprovalWorkStatusFail  = 3

	ApprovalWorkStatusRevoking = 6
	ApprovalWorkStatusRevokeOk = 7
)

const (
	TypeContent    = "content"
	TypeWork       = "work"
	TypeExhibition = "exhibition"
	TypeShow       = "show"
	TypeBundle     = "bundle"
)

const (
	Success      = 200
	Error        = 500
	ParamsError  = 400
	InvalidToken = 501
)

const (
	Domain_Mall = "mall"
	Domain_Erp  = "fontree"
)

const (
	RULE_TYPE_MENU      = "menu"
	RULE_TYPE_BUTTON    = "button"
	RULE_TYPE_INTERFACE = "interface"
)

const (
	Auth_Artwork_Task  = "Auth_Artwork_Task"
	Task_Type_Feedback = "feedback"
	Task_Type_Art      = "art"
	CreateQrUrl        = "https://common.szjixun.cn/api/image/qr/url?url="
)

// 订单状态参数
const (
	ORDER_STATUS_NEED_PUBLIC  = iota + 1 //待发布
	ORDER_STATUS_NEED_CONFIRM            //待确认
	ORDER_STATUS_APPEAL                  //申诉状态
	ORDER_STATUS_CONFIRMED               //已确认
)

const (
	Pay_Need_Pay = iota + 1
	Pay_Success
	Pay_Fail
	Pay_Express
	Pay_Express_Agree
)

const (
	REPORT_STATUS_NEED_PUBLIC   = iota + 1 //待发布
	REPORT_PUBLISH                         //已发布
	REPORT_NEED_CASHIER_SURE               //待出纳补充
	REPORT_NEED_BOSS_SURE                  //带最后总价确认
	REPORT_CASHIER_FINAL_OPTION            //待出纳确认 重置
)

const (
	Transaction_Doing             = iota + 1 //待总监确认
	Transaction_Need_Seller_Sure             //待销售确认
	Transaction_Question                     //申诉订单
	Transaction_Done                         //完成
	Transaction_Need_Cashier_Sure            //待出纳确认
	Transaction_Need_Control_Sure            //待内控确认
	Transaction_Need_Finance_Sure            //待财务确认
)

const (
	Order_Doing             = iota + 1 //待总监确认 待发布
	Order_Need_Seller_Sure             //待销售确认
	Order_Status_Question              //申诉订单
	Order_Done                         //完成
	Order_Need_Cashier_Sure            //待出纳确认
	Order_Need_Control_Sure            //待内控确认
	Order_Need_Finance_Sure            //待财务确认
)

const (
	Pay_Type_Zhi         = iota + 1 //支付宝
	Pay_Type_Transer                //转账
	Pay_Type_Pos                    //Pos机支付
	Pay_Type_Zhi_Transer            //支付宝转账
	Pay_Type_Transer_Pos            //转账和pos机
	Pay_Type_Pos_Zhi                //pos和支付宝
	Pay_Type_All                    //pos和支付宝和转账
)

// 画展包及画展包申请状态码定义
const (
	ARTSHOW_INSIDE = iota + 1 // 内部
	ARTSHOW_PASS              // 可展
	//ARTSHOW_SHOW_PASS                    // 画展包的画展地址和时间 审批
	ARTSHOW_SALE_ADDRESS_PASS // 画展包中画作的销售地址 审批
	ARTSHOW_REWARD_PASS       // 润格 审批
)

// 审批人的状态码
const (
	IsLive = iota // 是否有效 0 有效 1 无效
	IsLiveInvalid
)

// 可画展画展包状态
const (
	NotCanShow = 1 // 不可画展
	CanShow    = 2 // 可画展
)

const (
	Failed                = 1
	Ok                    = 0
	BindError             = 2
	JsonUnmarshal         = 3
	NOTDATA               = 4
	ErrorHttp             = 5
	ErrorBody             = 6
	NotLogin              = 401
	OffLine               = 402
	MsgSended             = 407 //短信已经发送
	CaptchaWrong          = 408 //滑块验证码错误
	NotLoginSqueeze       = 409
	SUCCESS               = 200
	UpdatePasswordSuccess = 201
	DeleteSuccess         = 204
	NotExistInentifier    = 202
	ERROR                 = 500

	InvalidParams = 400

	//成员错误
	ErrorExistNick          = 10001
	ErrorExistUser          = 10002
	ErrorNotExistUser       = 10003
	ErrorNotCompare         = 10004
	ErrorNotComparePassword = 10005
	ErrorFailEncryption     = 10006
	ErrorNotExistProduct    = 10007
	ErrorNotExistAddress    = 10008
	ErrorExistFavorite      = 10009
	ErrorGetUserInfo        = 10010
	ErrorGetDepart          = 10011
	ErrorUpdateAw           = 10012
	ErrorGetArtShow         = 10013
	ErrorPriceRunFailed     = 10014
	ErrorArtistNotLock      = 10015
	ErrorArtistCardId       = 10016
	ErrorIndexes            = 10017
	ErrorNoArtist           = 10018

	//店家错误
	ErrorBossCheckTokenFail        = 20001
	ErrorBossCheckTokenTimeout     = 20002
	ErrorBossToken                 = 20003
	ErrorBoss                      = 20004
	ErrorBossInsufficientAuthority = 20005
	ErrorBossProduct               = 20006

	//管理员错误
	ErrorAuthCheckTokenFail        = 30001 //token 错误
	ErrorAuthCheckTokenTimeout     = 30002 //token 过期
	ErrorAuthToken                 = 30003
	ErrorAuth                      = 30004
	ErrorAuthInsufficientAuthority = 30005
	ErrorReadFile                  = 30006
	ErrorSendEmail                 = 30007
	ErrorCallApi                   = 30008
	ErrorUnmarshalJson             = 30009
	ErrorAdminFindUser             = 30010
	//数据库错误
	ErrorDatabase = 40001

	//对象存储错误
	ErrorOss        = 50001
	ErrorUploadFile = 50002
	ErrorSelect     = 50003

	//店铺错误
	ErrorExistShopName    = 60001
	ErrorNotExistShopName = 60002
	ErrorNotAdmin         = 60003

	ErrNoDomain      = 70001
	ErrTelNum        = 70002
	ErrNoCode        = 70003
	ErrNoID          = 70004
	ErrNickName      = 70005
	InvalidID        = 70006
	InvalidPas       = 70007
	ErrStatus        = 70008
	ErrNoType        = 70009
	ErrNoUserID      = 70010
	ErrNoName        = 70011
	ErrNoDepCode     = 70012
	ErrNoTitle       = 70013
	ErrNoUrl         = 70014
	ErrNoMethod      = 70015
	ErrNotDep        = 70016
	ErrCreateQr      = 70017
	ErrNotSellerBoss = 70018

	//上传
	ErrorUploadVideoCover   = 80001
	ErrorUploadValidParam   = 80002
	ErrorFileReadErr        = 80003
	ErrorFileNotExists      = 80004
	ErrorChunkNotGt         = 80005
	ErrorChunk              = 80006
	ErrorUploadBos          = 80007
	ErrorFileCreate         = 80008
	ERROR_UID               = 80009
	ERROR_NOT_ZIP           = 80010
	ERROR_EMPTY_ZIP         = 80011
	ERROR_COPYRIGHT_CHUNK   = 80012
	ERROR_EMPTY_FILE        = 80013
	ERROR_OPEN_FILE         = 80014
	ERROR_READ_DIR          = 80015
	ERROR_ZIP               = 80016
	ERROR_NO_FILE           = 80017
	ERROR_ROTATE_IMG        = 80018
	ERROR_BAIDU_IMAGE       = 80019
	ERROR_BAIDU_FAIL        = 80020
	ERROR_DOWNLOAD_FILE     = 80021
	ERROR_INVALID_FILE_EXT  = 80022
	ERROR_ALIYUN_DOC_SUBMIT = 80023
	ERROR_ALIYUN_DOC_PARSE  = 80024
	ERROR_DECODE_IMAGE      = 80025
	ErrorUpdateEmployee     = 80026

	//画作
	ErrorAllotUids = 90001

	ErrorBrandList = 11001
	ErrorStoreNTF  = 11002

	//品牌方助记词注册失败
	ErrorCreateAccount = 11010

	ErrorUpdateVideo = 12001

	//画家宝服务错误代码 130000 ~ 139999
	Error13ArtistInfoCodeStart = 130000
	Error13ArtistInfoCodeEnd   = 139999

	//系类服务错误代码 140000 ~ 149999
	Error14SeriesCodeStart = 140000
	Error14SeriesNotShelf  = 140001 //商品已下架
	Error14SeriesCodeEnd   = 149999

	// 文创商品
	// 订单已存在
	Error15OrderExist = 150001
)

const (
	SendPromotionMsgSuccess = 2
)

const (
	NotHasFormula = 1 // 不存在 公式
	HasFormula    = 2 // 存在 公式
	NotCare       = 3 // 忽略 公式
)
