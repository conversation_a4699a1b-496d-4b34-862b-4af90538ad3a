package e

type ErrorCode int

const (
	SynOpen = "open"
)

var (
	ShowStatusNeedSellerSure = 1
	ShowStatusNeedReceive    = 2
	ShowStatusNeedAgreed     = 3
	ShowStatusNeedReap       = 4
	ShowStatusDone           = 5
)

const (
	VerifierDomain        = "fontree"
	ZoneCn                = "86"
	AuctionDomain         = "auction"
	MallAuctionDomain     = "auction" //对不住了，我是用了老数据
	SERVER_CONFIG         = "conf.ini"
	SERVER_DUBBOGO_CONFIG = "dubbogo.yaml"
	MODE_ENV              = "MODE_ENV"
	LoginFromPC           = "pc"
	OfflineSqueeze        = "squeeze"
	JwtInfo               = "jwtInfo"
)

const (
	ErrorOutputReward     = "润格审批未通过,无法导出润格信息"
	ErrorCreateApproval   = "请勿重复提交"
	ErrorExport           = "无数据可导出"
	ErrorExportNotAllowed = "未被允许的数据,不可导出"
	ErrorSaveFile         = "文件保存错误"
	ErrorQueryArtistData  = "查询画家基础信息失败"
	ErrorNotAllow         = "暂无权限"
	ErrorZip              = "文件压缩失败"
	ErrorNotFoundArtwork  = "画作数据缺失"
	ErrorQueryRules       = "获取权限失败"
	ErrorReadTemp         = "模板文件获取错误"
	ErrorEncodeImage      = "图片解码失败"
	ErrorOVerSendBossMsg  = "您今天已经对该审批人同类的审批发送过短信！"

	ErrorCreateTalents   = "创建岗位审核审批失败"
	ErrorRollbackTalents = "回滚岗位审核审批失败"
	ErrorCreateOffer     = "创建入职审核审批失败"
	ErrorRollbackOffer   = "回滚入职审核审批失败"
	ErrorResumeHireJudge = "录用申请确认失败"
	ErrorResumeID        = "无效的简历ID"

	// 同步信息到站点签到
	ErrorSendInfoToSiteSignIn = "同步信息到站点签到失败"

	// 获取用户状态信息失败
	ErrorGetUserStatus = "获取用户状态信息失败"

	// 查询审批内容失败
	ErrorQueryApprovalContent = "查询审批内容失败"

	// 画展包ID不能为空
	ErrorShowUidIsEmpty = "画展包ID不能为空"

	// 获取用户状态信息失败
	ErrorLoanMoney = "借款金额错误"
	ErrorImgExt    = "图片格式错误"
	ErrorImgDecode = "图片解析错误"
)

const (
	FileReadError = "读取文件错误"
	FileSaveError = "保存文件错误"
)

const (
	ErrorQueryHonorInfo      = "获取画家荣誉信息失败"
	ErrorQueryArtShowInfo    = "获取画展包信息失败"
	ErrorUpdateCanShowStatus = "更新画展包可画展状态失败"
)

const (
	ErrNotSameApprovalBossId                 = "当前审批人不一致"
	ErrorApprovalUser                        = "获取审批人错误"
	InvalidApprovalAndCopy                   = "无效的审批人"
	ErrorQueryDirectOrDepartmentApprovalUser = "获取直属上级或部门审批人错误"
	ErrorCurrentApprovalIsNotUsed            = "审批设置无效,当前审批不可用"

	ErrorNotSelectDepartment = "未选择所属部门"

	// 任命审批
	ErrorNotCatchUserDepartmentBoss = "未获取到任命的用户部门负责人"
	ErrorEmptyPromotionStaffUid     = "未获取到任命的员工信息"
	ErrorEmptyHiredStaffUid         = "未获取到录用的员工信息"
)

// 更新 销售站点信息提示
const (
	FailedUpdateArtworkPriceSaleAddress = "更新画展包画作销售站点失败"
	FailedUpdateArtworkSaleAddress      = "更新画作销售站点失败"
)

// 画展包撤销提示
const (
	FailedQueryArtShowInfo            = "查询画展包信息失败"
	ErrorNotAllowedRevokeArtShowApply = "当前审批中的画展包的状态不符合撤销条件"

	ErrorShowInvoiceMiss = "缺少单据信息"
	ErrorNoPrice         = "当前画展包无价格信息"
)

// 画展模块  画展审批
const (
	FailedQueryShowApprovalInfo = "未查询到画展审批信息"
)

const (
	ErrorUserDisabled                 = "用户已停用"
	ErrorNoArtworkLicense             = "画作未授权"
	ErrorNoWorkFile                   = "缺少作品文件"
	ErrorNoDigitalRegisterId          = "数登未申请"
	ErrorNoArtworkUuid                = "未选择画作"
	ErrorArtworkAlreadyDci            = "画作已经dci"
	ErrorArtworkAlreadyDigital        = "画作已经数登"
	ErrorArtworkNotDci                = "Dci尚未申请"
	ErrorUnknown                      = "未知错误"
	ErrorReplaceArtwork               = "不能更换画作"
	ErrorCreateAddressCode            = "创作地址编码为空"
	ErrorGetPayUrl                    = "获取支付链接失败"
	ErrorRightAuthFile                = "肖像权文件错误"
	ErrorDciUserNoReg                 = "dci用户未注册"
	ErrorDciUpload                    = "dci上传失败"
	ErrorDciNoNeedUpdate              = "dci用户无需更新"
	ErrorNotEmpty                     = "不能为空"
	ErrorDownloadFile                 = "下载文件错误"
	ErrorBaiduChainError              = "百度链错误"
	ErrorBaiduChainAlreadyDone        = "百度链已完成"
	ErrorBaiduChaining                = "百度链未完成"
	ErrorOcr                          = "ocr错误"
	ErrorArtworkEditNo                = "画作属性不可修改"
	ErrorDataNil                      = "数据为空"
	ErrorDciUserNOFinish              = "dci用户状态未完结"
	ErrorRulerCount                   = "平尺数计算不一致"
	COPYRIGHT_NOT_IN_ARTIST_INFO_USER = "画家不在画家宝"
	ErrorNoCreationCompletionDate     = "创作完成日期错误"
	ERROR_CARDID_OCR                  = "身份证OCR失败"
	ERROR_ARTIST_BLACKLIST            = "画家已加入黑名单"
	ERROR_EDIT_LIMIT                  = "编辑限制"
	ERROR_DCI_USER_QUERY              = "dci用户查询失败"
)
const (
	CountSimilarIng = "相似度计算中"
	LangChinese     = "zh-CN" //中文
)

var MsgFlags = map[ErrorCode]string{
	SUCCESS:               "操作成功",
	UpdatePasswordSuccess: "修改密码成功",
	NotExistInentifier:    "该第三方账号未绑定",
	ERROR:                 "fail",
	NOTDATA:               "没有数据",
	InvalidParams:         "请求参数错误",
	BindError:             "参数绑定错误，类型不一致",
	JsonUnmarshal:         "Json解析错误",
	ErrorHttp:             "请求错误",
	ErrorBody:             "响应错误",

	ErrorExistNick:          "已存在该昵称",
	ErrorExistUser:          "已存在该用户名",
	ErrorNotExistUser:       "该用户不存在",
	ErrorNotCompare:         "账号密码错误",
	ErrorNotComparePassword: "两次密码输入不一致",
	ErrorFailEncryption:     "加密失败",
	ErrorNotExistProduct:    "该商品不存在",
	ErrorNotExistAddress:    "该收获地址不存在",
	ErrorExistFavorite:      "已收藏该商品",
	ErrorGetUserInfo:        "获取用户信息错误",
	ErrorGetDepart:          "获取部门信息错误",
	ErrorUpdateAw:           "同步画作信息错误",
	ErrorGetArtShow:         "获取画展包信息错误",
	ErrorPriceRunFailed:     "润格审批不通过",
	ErrorArtistNotLock:      "画家不是锁定状态",
	ErrorArtistCardId:       "画家身份证错误",
	ErrorIndexes:            "画家指数错误",
	ErrorNoArtist:           "没有该画家",

	ErrorBossCheckTokenFail:        "商家的Token鉴权失败",
	ErrorBossCheckTokenTimeout:     "商家TOken已超时",
	ErrorBossToken:                 "商家的Token生成失败",
	ErrorBoss:                      "商家Token错误",
	ErrorBossInsufficientAuthority: "商家权限不足",
	ErrorBossProduct:               "商家读文件错误",

	ErrorAuthCheckTokenFail:        "Token鉴权失败",
	ErrorAuthCheckTokenTimeout:     "TOken已超时",
	ErrorAuthToken:                 "Token生成失败",
	ErrorAuth:                      "Token错误",
	ErrorAuthInsufficientAuthority: "权限不足",
	ErrorReadFile:                  "读文件失败",
	ErrorSendEmail:                 "发送邮件失败",
	ErrorCallApi:                   "调用接口失败",
	ErrorUnmarshalJson:             "解码JSON失败",

	ErrorUploadFile:    "上传失败",
	ErrorAdminFindUser: "管理员查询用户失败",
	ErrorSelect:        "查询失败",

	ErrorDatabase: "数据库操作出错,请重试",

	ErrorOss: "OSS配置错误",

	ErrorExistShopName:    "店铺已被注册，请检查店铺名称和统一社会信用码",
	ErrorNotExistShopName: "店铺不存在",
	ErrorNotAdmin:         "非管理员",

	InvalidToken: "Token验证失败",

	ErrorUploadVideoCover:   "视频截取封面错误",
	ErrorUploadValidParam:   "上传参数非法",
	ErrorFileReadErr:        "读取文件错误",
	ErrorFileNotExists:      "文件不存在",
	ErrorChunkNotGt:         "分块数量不一致",
	ErrorChunk:              "读取分块错误",
	ErrorUploadBos:          "上传bos错误",
	ErrorFileCreate:         "文件创建错误",
	ERROR_UID:               "uid创建错误",
	ERROR_NOT_ZIP:           "压缩文件不是zip格式",
	ERROR_EMPTY_ZIP:         "压缩文件为空",
	ERROR_COPYRIGHT_CHUNK:   "批量上传不支持版权图",
	ERROR_EMPTY_FILE:        "文件为空",
	ERROR_OPEN_FILE:         "打开文件错误",
	ERROR_READ_DIR:          "读取文件夹错误",
	ERROR_ZIP:               "zip压缩错误",
	ERROR_NO_FILE:           "没有文件",
	ERROR_ROTATE_IMG:        "旋转图片出错",
	ERROR_BAIDU_IMAGE:       "图片审核不通过",
	ERROR_BAIDU_FAIL:        "图片审核失败",
	ERROR_DOWNLOAD_FILE:     "下载文件错误",
	ERROR_INVALID_FILE_EXT:  "不支持的文件格式",
	ERROR_ALIYUN_DOC_SUBMIT: "文档提交错误",
	ERROR_ALIYUN_DOC_PARSE:  "文档解析错误",
	ERROR_DECODE_IMAGE:      "图片解析错误",

	ErrNoDomain: "环境变量必须要有",
	ErrTelNum:   "手机号码错误",
	ErrNoCode:   "验证码必须要有",
	ErrNoID:     "ID缺少",
	ErrNickName: "请正确填写姓名项",
	InvalidID:   "身份证长度18位",
	InvalidPas:  "密码不小于6位",

	ErrStatus:        "状态非法",
	ErrNoType:        "缺少类型",
	ErrNoUserID:      "缺少用户ID",
	ErrNoName:        "缺少名称",
	ErrNoDepCode:     "缺少部门code",
	ErrNoTitle:       "缺少标题",
	ErrNoUrl:         "缺少url",
	ErrNoMethod:      "缺少method",
	ErrNotDep:        "缺少部门",
	ErrCreateQr:      "生成二维码错误",
	ErrNotSellerBoss: "当前人员身份非销售总监,不能操作",

	ErrorAllotUids: "分配人员错误",

	ErrorBrandList: "获取品牌方列表错误",
	ErrorStoreNTF:  "系列上链错误",

	ErrorUpdateVideo:      "保存成功，同步画家宝错误",
	ErrorUpdateEmployee:   "更新员工档案错误",
	Error14SeriesNotShelf: "该系列已下架",
}

// GetMsg 获取状态码对应信息
func GetMsg(code int) string {
	msg, ok := MsgFlags[ErrorCode(code)]
	if ok {
		return msg
	}
	return MsgFlags[ERROR]
}
