package e

/*const (
	ErrAttendanceMiss        = "考勤规则缺失"
	ErrAttendanceWeekMiss    = "考勤规则周期错误"
	ErrApplytimeDisagreement = "请假时间不合规"
	ErrOvertimeDisagreement  = "加班时间不合规"

	ErrConfirmactiontype = "打卡规则确认错误"
	ErrConfirmweek       = "获取工作周期失败"
	NotNeedUseLeave      = "无需使用余额的请假申请"

	ErrQueryMultiData = "获取日期错误"

	ErrQueryOaRecord         = "查询考勤记录失败"
	ErrQueryAbnormalOaRecord = "查询考勤异常记录失败"

	ErrQueryOaApply             = "查询OA申请记录失败"
	ErrQueryOaLeaveApply        = "查询OA余额申请记录失败"
	ERR_QUERY_OA_APPLY_OVERTIME = "查询加班申请记录失败"
	ErrNotHaveBalance           = "未申请假期余额"
	ErrHaveBalanceApply         = "当前已存在相应的余额申请"

	ErrQueryUser = "查询用户信息失败"

	ErrQueryOaSetting = "查询设置失败"

	ErrQueryOaMonth = "查询考勤信息失败"

	ERR_NOT_NEED_BALANCE = "无需查询余额"

	ErrNoBalance     = "假期余额不足"
	ErrHourNoBalance = "请假时长超出范围"

	ErrApplyTime         = "无效的请假时间"
	ERR_EMPTY_APPLY_TIME = "请假时长不能为0"
	ErrSelectTimeRepeat  = "所选时间段已有申请"

	ErrNotInCurrentYear  = "该请假类型申请时间不在本年度"
	ErrNotInCurrentMonth = "该请假类型申请时间不在本月"

	ErrNotNeedRecordOutworkcheck = "无需确认是否出外勤"

	ErrIsUsed = "该余额申请无法撤销"

	ErrTimeOrder = "开始时间不应晚于结束时间"

	ErrDayoffNotLessOneHour = "调休时长不得少于1小时"

	ErrOvertimeIsZero = "加班时长不得为0"

	ErrOvertimeIsRep = "加班开始时间和结束时间重复"

	ErrLeaveDurationIsZero = "时长不得为0"

	ErrHolidayMiss = "节假日信息缺失"

	ErrIpMiss = "暂无IP信息"

	ErrWifiIsNotIn = "当前不在公司内网"

	ERR_GETIP = "获取IP信息失败"

	ErrLatLog = "当前定位不能为空"

	ErrChickInMap = "打卡地址坐标缺失"

	ErrDbChickInMap = "查询打卡地址坐标失败"

	ErrCacheChickInMap = "缓存打卡地址坐标失败"

	ErrConfirmChickInMap = "打卡地址确认失败"

	ErrNotInChickInMap = "当前不在打卡范围内"

	ErrMissSystemMakeUpParam = "系统补卡参数缺失"

	ErrQueryWorkingTimeList = "查询考勤列表失败"

	ErrorWorkingTimeFindUser = "查询用户失败"

	ErrorWorkingTimeFindUserPosition = "查询用户岗位信息失败"

	ErrorQueryOaProfile = "查询用户考勤信息失败"

	ErrorQueryHoliday = "查询节假日失败"

	ErrorQueryWorkingTime = "查询考勤信息失败"

	ErrorMissWorkingTime = "未设置考勤信息"

	ErrorDelCacheGeo = "删除缓存的打卡经纬度失败"

	ErrorQueryRangeGeo = "查询打卡范围错误"

	ErrorSaveCacheGeo = "缓存打卡经纬度失败"

	ErrorSaveCacheActualGeo = "缓存实际打卡经纬度失败"

	ErrorCheckWorkTime = "判断考勤时间失败"

	ErrorQueryOutWorkCheck = "查询外出回归记录失败"

	ErrorCheckNextClockInTime = "判断下次打卡时间失败"

	ErrorClockIn = "打卡失败"

	SuccessClockIn = "打卡成功"

	ErrorNotAllowedClockIn = "不允许打卡"

	ErrorMissBreakOption = "缺少休息时间信息"

	ErrNotHadWorkingTime = "未关联考勤信息,无法打卡"

	ErrMissMakeUpRule = "缺少补卡规则"

	ErrCanNotCalcOverTimeBehindTwoDays = "当前不支持提交多日期的加班申请"

	ErrQueryPositionInfo = "查询部门岗位信息失败"

	ErrWritePDF = "写入PDF文件失败"

	ErrSystemCommonParam = "系统修改异常参数缺失"

	ErrTimeIsZero = "时长不得为0"

	ErrQueryEmployeeAttendance = "查询员工考勤绑定的考勤信息失败"

	ErrWorkingTimeNotInUse = "考勤设置将于   a   生效"

	ErrActionTimeNotBeforeNextDateFour = "最后一班下班卡的系统补卡时间不得晚于次日凌晨四点"

	ErrorIsNotBeforeOrEqualCollectionMonth = "仅支持查看当前及历史月考勤数据"

	ErrorEmployFindUser = "查询员工档案失败"
)*/

var errorMessagesZhCN = map[string]string{
	"ErrAttendanceMissZhCN":        "考勤规则缺失",
	"ErrAttendanceWeekMissZhCN":    "考勤规则周期错误",
	"ErrApplytimeDisagreementZhCN": "请假时间不合规",
	"ErrOvertimeDisagreementZhCN":  "加班时间不合规",

	"ErrConfirmactiontypeZhCN": "打卡规则确认错误",
	"ErrConfirmweekZhCN":       "获取工作周期失败",
	"NotNeedUseLeaveZhCN":      "无需使用余额的请假申请",

	"ErrQueryMultiDataZhCN": "获取日期错误",

	"ErrQueryOaRecordZhCN":         "查询考勤记录失败",
	"ErrQueryAbnormalOaRecordZhCN": "查询考勤异常记录失败",

	"ErrQueryOaApplyZhCN":             "查询OA申请记录失败",
	"ErrQueryOaLeaveApplyZhCN":        "查询OA余额申请记录失败",
	"ERR_QUERY_OA_APPLY_OVERTIMEZhCN": "查询加班申请记录失败",
	"ErrNotHaveBalanceZhCN":           "未申请假期余额",
	"ErrHaveBalanceApplyZhCN":         "当前已存在相应的余额申请",

	"ErrQueryUserZhCN": "查询用户信息失败",

	"ErrQueryOaSettingZhCN": "查询设置失败",

	"ErrQueryOaMonthZhCN": "查询考勤信息失败",

	"ERR_NOT_NEED_BALANCEZhCN": "无需查询余额",

	"ErrNoBalanceZhCN":     "假期余额不足",
	"ErrHourNoBalanceZhCN": "请假时长超出范围",

	"ErrApplyTimeZhCN":         "无效的请假时间",
	"ERR_EMPTY_APPLY_TIMEZhCN": "请假时长不能为0",
	"ErrSelectTimeRepeatZhCN":  "所选时间段已有申请",

	"ErrNotInCurrentYearZhCN":  "该请假类型申请时间不在本年度",
	"ErrNotInCurrentMonthZhCN": "该请假类型申请时间不在本月",

	"ErrNotNeedRecordOutworkcheckZhCN": "无需确认是否出外勤",

	"ErrIsUsedZhCN": "该余额申请无法撤销",

	"ErrTimeOrderZhCN": "开始时间不应晚于结束时间",

	"ErrDayoffNotLessOneHourZhCN": "调休时长不得少于1小时",

	"ErrOvertimeIsZeroZhCN": "加班时长不得为0",

	"ErrOvertimeIsRepZhCN": "加班开始时间和结束时间重复",

	"ErrLeaveDurationIsZeroZhCN": "时长不得为0",

	"ErrHolidayMissZhCN": "节假日信息缺失",

	"ErrIpMissZhCN": "暂无IP信息",

	"ErrWifiIsNotInZhCN": "当前不在公司内网",

	"ERR_GETIPZhCN": "获取IP信息失败",

	"ErrLatLogZhCN": "当前定位不能为空",

	"ErrChickInMapZhCN": "打卡地址坐标缺失",

	"ErrDbChickInMapZhCN": "查询打卡地址坐标失败",

	"ErrCacheChickInMapZhCN": "缓存打卡地址坐标失败",

	"ErrConfirmChickInMapZhCN": "打卡地址确认失败",

	"ErrNotInChickInMapZhCN": "当前不在打卡范围内",

	"ErrMissSystemMakeUpParamZhCN": "系统补卡参数缺失",

	"ErrQueryWorkingTimeListZhCN": "查询考勤列表失败",

	"ErrorWorkingTimeFindUserZhCN": "查询用户失败",

	"ErrorWorkingTimeFindUserPositionZhCN": "查询用户岗位信息失败",

	"ErrorQueryOaProfileZhCN": "查询用户考勤信息失败",

	"ErrorQueryHolidayZhCN": "查询节假日失败",

	"ErrorQueryWorkingTimeZhCN": "查询考勤信息失败",

	"ErrorMissWorkingTimeZhCN": "未设置考勤信息",

	"ErrorDelCacheGeoZhCN": "删除缓存的打卡经纬度失败",

	"ErrorQueryRangeGeoZhCN": "查询打卡范围错误",

	"ErrorSaveCacheGeoZhCN": "缓存打卡经纬度失败",

	"ErrorSaveCacheActualGeoZhCN": "缓存实际打卡经纬度失败",

	"ErrorCheckWorkTimeZhCN": "判断考勤时间失败",

	"ErrorQueryOutWorkCheckZhCN": "查询外出回归记录失败",

	"ErrorCheckNextClockInTimeZhCN": "判断下次打卡时间失败",

	"ErrorClockInZhCN": "打卡失败",

	"SuccessClockInZhCN": "打卡成功",

	"ErrorNotAllowedClockInZhCN": "不允许打卡",

	"ErrorMissBreakOptionZhCN": "缺少休息时间信息",

	"ErrCheckAddressLimitZhCN": "检查地址失败",

	"ErrUserMissZhCN": "用户信息缺失",

	"ErrNoDataZhCN": "无数据",

	"ErrNotApplyUuidZhCN": "申请UUID不能为空",

	"ErrNotHadWorkingTimeZhCN": "未关联考勤信息,无法打卡",

	"ErrMissMakeUpRuleZhCN": "缺少补卡规则",

	"ErrCanNotCalcOverTimeBehindTwoDaysZhCN": "当前不支持提交多日期的加班申请",

	"ErrorQueryApprovalContentZhCN": "查询审批内容失败",

	"ErrorOverTimeNotInWorkTimeZhCN": "申请加班时间不得在工作时间内",

	"ErrorMissOutBackZhCN": "缺少外出或回公司标识",

	"ErrorMissLngLatZhCN": "缺少经纬度信息",

	"SuccessCreateOutWorkCheckZhCN": "创建外出或回公司记录成功",

	"ErrorCreateOutWorkCheckZhCN": "创建外出或回公司记录失败",

	"ErrorFrequentClockInZhCN": "15秒内只能提交一次",

	"ErrorNoFaceImgZhCN": "用户缺少近照信息",

	"ErrorNoCatchFaceImgZhCN": "未获取到打卡时近照",

	"ErrorFaceCompareZhCN": "人脸比对失败",

	"ErrorFaceCompareNotPassZhCN": "人脸比对不通过",

	"ErrorInvalidChickInMapZhCN": "无效的打卡地址",

	"ErrorTimeIsZeroZhCN": "时长不得为0",

	"ErrQueryEmployeeAttendanceZhCN": "查询员工考勤绑定的考勤信息失败",

	"ErrorWorkingTimeNotInUseZhCN": "考勤设置生效于",

	"ErrorAddressEmptyZhCN": "打卡地址经纬度不能为空",

	"ErrorQueryVersionHistoryZhCN": "查询版本历史失败",
}

var errorMessagesZhTW = map[string]string{
	"ErrAttendanceMissZhTW":        "考勤規則缺失",
	"ErrAttendanceWeekMissZhTW":    "考勤規則週期錯誤",
	"ErrApplytimeDisagreementZhTW": "請假時間不合規",
	"ErrOvertimeDisagreementZhTW":  "加班時間不合規",

	"ErrConfirmactiontypeZhTW": "打卡規則確認錯誤",
	"ErrConfirmweekZhTW":       "獲取工作週期失敗",
	"NotNeedUseLeaveZhTW":      "無需使用餘額的請假申請",

	"ErrQueryMultiDataZhTW": "獲取日期錯誤",

	"ErrQueryOaRecordZhTW":         "查詢考勤記錄失敗",
	"ErrQueryAbnormalOaRecordZhTW": "查詢考勤異常記錄失敗",

	"ErrQueryOaApplyZhTW":             "查詢OA申請記錄失敗",
	"ErrQueryOaLeaveApplyZhTW":        "查詢OA餘額申請記錄失敗",
	"ERR_QUERY_OA_APPLY_OVERTIMEZhTW": "查詢加班申請記錄失敗",
	"ErrNotHaveBalanceZhTW":           "未申請假期餘額",
	"ErrHaveBalanceApplyZhTW":         "當前已存在相應的餘額申請",

	"ErrQueryUserZhTW": "查詢用戶信息失敗",

	"ErrQueryOaSettingZhTW": "查詢設置失敗",

	"ErrQueryOaMonthZhTW": "查詢考勤信息失敗",

	"ERR_NOT_NEED_BALANCEZhTW": "無需查詢餘額",

	"ErrNoBalanceZhTW":     "假期餘額不足",
	"ErrHourNoBalanceZhTW": "請假時長超出範圍",

	"ErrApplyTimeZhTW":         "無效的請假時間",
	"ERR_EMPTY_APPLY_TIMEZhTW": "請假時長不能為0",
	"ErrSelectTimeRepeatZhTW":  "所選時間段已有申請",

	"ErrNotInCurrentYearZhTW":  "該請假類型申請時間不在本年度",
	"ErrNotInCurrentMonthZhTW": "該請假類型申請時間不在本月",

	"ErrNotNeedRecordOutworkcheckZhTW": "無需確認是否出外勤",

	"ErrIsUsedZhTW": "該餘額申請無法撤銷",

	"ErrTimeOrderZhTW": "開始時間不應晚於結束時間",

	"ErrDayoffNotLessOneHourZhTW": "調休時長不得少於1小時",

	"ErrOvertimeIsZeroZhTW": "加班時長不得為0",

	"ErrOvertimeIsRepZhTW": "加班開始時間和結束時間重複",

	"ErrLeaveDurationIsZeroZhTW": "時長不得為0",

	"ErrHolidayMissZhTW": "節假日信息缺失",

	"ErrIpMissZhTW": "暫無IP信息",

	"ErrWifiIsNotInZhTW": "當前不在公司內網",

	"ERR_GETIPZhTW": "獲取IP信息失敗",

	"ErrLatLogZhTW": "當前定位不能為空",

	"ErrChickInMapZhTW": "打卡地址坐標缺失",

	"ErrDbChickInMapZhTW": "查詢打卡地址坐標失敗",

	"ErrCacheChickInMapZhTW": "緩存打卡地址坐標失敗",

	"ErrConfirmChickInMapZhTW": "打卡地址確認失敗",

	"ErrNotInChickInMapZhTW": "當前不在打卡範圍內",

	"ErrMissSystemMakeUpParamZhTW": "系統補卡參數缺失",

	"ErrQueryWorkingTimeListZhTW": "查詢考勤列表失敗",

	"ErrorWorkingTimeFindUserZhTW": "查詢用戶失敗",

	"ErrorWorkingTimeFindUserPositionZhTW": "查詢用戶崗位信息失敗",

	"ErrorQueryOaProfileZhTW": "查詢用戶考勤信息失敗",

	"ErrorQueryHolidayZhTW": "查詢節假日失敗",

	"ErrorQueryWorkingTimeZhTW": "查詢考勤信息失敗",

	"ErrorMissWorkingTimeZhTW": "未設置考勤信息",

	"ErrorDelCacheGeoZhTW": "刪除緩存的打卡經緯度失敗",

	"ErrorQueryRangeGeoZhTW": "查詢打卡範圍錯誤",

	"ErrorSaveCacheGeoZhTW": "緩存打卡經緯度失敗",

	"ErrorSaveCacheActualGeoZhTW": "緩存實際打卡經緯度失敗",

	"ErrorCheckWorkTimeZhTW": "判斷考勤時間失敗",

	"ErrorQueryOutWorkCheckZhTW": "查詢外出回歸記錄失敗",

	"ErrorCheckNextClockInTimeZhTW": "判斷下次打卡時間失敗",

	"ErrorClockInZhTW": "打卡失敗",

	"SuccessClockInZhTW": "打卡成功",

	"ErrorNotAllowedClockInZhTW": "不允許打卡",

	"ErrorMissBreakOptionZhTW": "缺少休息時間信息",

	"ErrCheckAddressLimitZhTW": "檢查地址失敗",

	"ErrUserMissZhTW": "用戶信息缺失",

	"ErrNoDataZhTW": "無數據",

	"ErrNotApplyUuidZhTW": "申請UUID不能為空",

	"ErrNotHadWorkingTimeZhTW": "未關聯考勤信息,無法打卡",

	"ErrMissMakeUpRuleZhTW": "缺少補卡規則",

	"ErrCanNotCalcOverTimeBehindTwoDaysZhTW": "當前不支持提交多日期的加班申請",

	"ErrorQueryApprovalContentZhTW": "查詢審批內容失敗",

	"ErrorOverTimeNotInWorkTimeZhTW": "申請加班時間不得在工作時間內",

	"ErrorMissOutBackZhTW": "缺少外出或回公司標識",

	"ErrorMissLngLatZhTW": "缺少經緯度信息",

	"SuccessCreateOutWorkCheckZhTW": "創建外出或回公司記錄成功",

	"ErrorCreateOutWorkCheckZhTW": "創建外出或回公司記錄失敗",

	"ErrorFrequentClockInZhTW": "15秒內只能提交一次",

	"ErrorNoFaceImgZhTW": "用戶缺少近照信息",

	"ErrorNoCatchFaceImgZhTW": "未獲取到打卡時近照",

	"ErrorFaceCompareZhTW": "人臉比對失敗",

	"ErrorFaceCompareNotPassZhTW": "人臉比對不通過",

	"ErrorInvalidChickInMapZhTW": "無效的打卡地址",

	"ErrorTimeIsZeroZhTW": "時長不得為0",

	"ErrQueryEmployeeAttendanceZhTW": "查詢員工考勤綁定的考勤信息失敗",

	"ErrorWorkingTimeNotInUseZhTW": "考勤設置生效於 ",

	"ErrorAddressEmptyZhTW": "打卡地址經緯度不能為空",

	"ErrorQueryVersionHistoryZhTW": "查詢版本歷史失敗",
}

var errorMessagesEN = map[string]string{
	"ErrAttendanceMissEN":        "Attendance rule missing",
	"ErrAttendanceWeekMissEN":    "Attendance rule period error",
	"ErrApplytimeDisagreementEN": "Leave time non-compliant",
	"ErrOvertimeDisagreementEN":  "Overtime non-compliant",

	"ErrConfirmactiontypeEN": "Attendance rule confirmation error",
	"ErrConfirmweekEN":       "Failed to get work week",
	"NotNeedUseLeaveEN":      "Leave application without balance required",

	"ErrQueryMultiDataEN": "Date retrieval error",

	"ErrQueryOaRecordEN":         "Failed to query attendance record",
	"ErrQueryAbnormalOaRecordEN": "Failed to query abnormal attendance record",

	"ErrQueryOaApplyEN":             "Failed to query OA application record",
	"ErrQueryOaLeaveApplyEN":        "Failed to query OA balance application record",
	"ERR_QUERY_OA_APPLY_OVERTIMEEN": "Failed to query overtime application record",
	"ErrNotHaveBalanceEN":           "No leave balance applied",
	"ErrHaveBalanceApplyEN":         "Existing balance application already present",

	"ErrQueryUserEN": "Failed to query user information",

	"ErrQueryOaSettingEN": "Failed to query settings",

	"ErrQueryOaMonthEN": "Failed to query attendance information",

	"ERR_NOT_NEED_BALANCEEN": "No need to query balance",

	"ErrNoBalanceEN":     "Insufficient leave balance",
	"ErrHourNoBalanceEN": "Leave duration out of range",

	"ErrApplyTimeEN":         "Invalid leave time",
	"ERR_EMPTY_APPLY_TIMEEN": "Leave duration cannot be zero",
	"ErrSelectTimeRepeatEN":  "Application already exists for the selected time period",

	"ErrNotInCurrentYearEN":  "Leave type application time not in current year",
	"ErrNotInCurrentMonthEN": "Leave type application time not in current month",

	"ErrNotNeedRecordOutworkcheckEN": "No need to confirm outwork",

	"ErrIsUsedEN": "This balance application cannot be revoked",

	"ErrTimeOrderEN": "Start time should not be later than end time",

	"ErrDayoffNotLessOneHourEN": "Compensatory leave duration should not be less than 1 hour",

	"ErrOvertimeIsZeroEN": "Overtime duration cannot be zero",

	"ErrOvertimeIsRepEN": "Overtime start and end times are repeated",

	"ErrLeaveDurationIsZeroEN": "Duration cannot be zero",

	"ErrHolidayMissEN": "Holiday information missing",

	"ErrIpMissEN": "No IP information available",

	"ErrWifiIsNotInEN": "Not in company intranet",

	"ERR_GETIPEN": "Failed to get IP information",

	"ErrLatLogEN": "Current location cannot be empty",

	"ErrChickInMapEN": "Attendance address coordinates missing",

	"ErrDbChickInMapEN": "Failed to query attendance address coordinates",

	"ErrCacheChickInMapEN": "Failed to cache attendance address coordinates",

	"ErrConfirmChickInMapEN": "Attendance address confirmation failed",

	"ErrNotInChickInMapEN": "Not within attendance range",

	"ErrMissSystemMakeUpParamEN": "System make-up parameters missing",

	"ErrQueryWorkingTimeListEN": "Failed to query attendance list",

	"ErrorWorkingTimeFindUserEN": "Failed to query user",

	"ErrorWorkingTimeFindUserPositionEN": "Failed to query user position information",

	"ErrorQueryOaProfileEN": "Failed to query user attendance information",

	"ErrorQueryHolidayEN": "Failed to query holiday",

	"ErrorQueryWorkingTimeEN": "Failed to query attendance information",

	"ErrorMissWorkingTimeEN": "Attendance information not set",

	"ErrorDelCacheGeoEN": "Failed to delete cached attendance coordinates",

	"ErrorQueryRangeGeoEN": "Attendance range query error",

	"ErrorSaveCacheGeoEN": "Failed to cache attendance coordinates",

	"ErrorSaveCacheActualGeoEN": "Failed to cache actual attendance coordinates",

	"ErrorCheckWorkTimeEN": "Failed to determine attendance time",

	"ErrorQueryOutWorkCheckEN": "Failed to query outwork return records",

	"ErrorCheckNextClockInTimeEN": "Failed to determine next attendance time",

	"ErrorClockInEN": "Attendance failed",

	"SuccessClockInEN": "Attendance successful",

	"ErrorNotAllowedClockInEN": "Attendance not allowed",

	"ErrorMissBreakOptionEN": "Missing break time information",

	"ErrCheckAddressLimitEN": "Address check failed",

	"ErrUserMissEN": "User information missing",

	"ErrNoDataEN": "No data",

	"ErrNotApplyUuidEN": "Application UUID cannot be empty",

	"ErrNotHadWorkingTimeEN": "No associated attendance information, unable to attendance",

	"ErrMissMakeUpRuleEN": "Make-up rule missing",

	"ErrCanNotCalcOverTimeBehindTwoDaysEN": "Multi-date overtime application not supported",

	"ErrorQueryApprovalContentEN": "Failed to query approval content",

	"ErrorOverTimeNotInWorkTimeEN": "Overtime application time should not be within work hours",

	"ErrorMissOutBackEN": "Missing outwork or return to company identifier",

	"ErrorMissLngLatEN": "Missing longitude and latitude information",

	"SuccessCreateOutWorkCheckEN": "Successfully created outwork or return to company record",

	"ErrorCreateOutWorkCheckEN": "Failed to create outwork or return to company record",

	"ErrorFrequentClockInEN": "Can only submit once every 15 seconds",

	"ErrorNoFaceImgEN": "User missing recent photo",

	"ErrorNoCatchFaceImgEN": "Failed to capture recent photo during attendance",

	"ErrorFaceCompareEN": "Face comparison failed",

	"ErrorFaceCompareNotPassEN": "Face comparison not passed",

	"ErrorInvalidChickInMapEN": "Invalid attendance address",

	"ErrorTimeIsZeroEN": "Duration cannot be zero",

	"ErrQueryEmployeeAttendanceEN": "Failed to query employee attendance binding information",

	"ErrorWorkingTimeNotInUseEN": "Attendance settings will take effect on a",

	"ErrorAddressEmptyEN": "Attendance address coordinates cannot be empty",

	"ErrorQueryVersionHistoryEN": "Failed to query version history",
}

const (
	ErrAttendanceMissEN        = "Attendance rule missing"
	ErrAttendanceWeekMissEN    = "Attendance rule period error"
	ErrApplytimeDisagreementEN = "Leave time non-compliant"
	ErrOvertimeDisagreementEN  = "Overtime non-compliant"

	ErrConfirmactiontypeEN = "Attendance rule confirmation error"
	ErrConfirmweekEN       = "Failed to get work week"
	NotNeedUseLeaveEN      = "Leave application without balance required"

	ErrQueryMultiDataEN = "Date retrieval error"

	ErrQueryOaRecordEN         = "Failed to query attendance record"
	ErrQueryAbnormalOaRecordEN = "Failed to query abnormal attendance record"

	ErrQueryOaApplyEN             = "Failed to query OA application record"
	ErrQueryOaLeaveApplyEN        = "Failed to query OA balance application record"
	ERR_QUERY_OA_APPLY_OVERTIMEEN = "Failed to query overtime application record"
	ErrNotHaveBalanceEN           = "No leave balance applied"
	ErrHaveBalanceApplyEN         = "Existing balance application already present"

	ErrQueryUserEN = "Failed to query user information"

	ErrQueryOaSettingEN = "Failed to query settings"

	ErrQueryOaMonthEN = "Failed to query attendance information"

	ERR_NOT_NEED_BALANCEEN = "No need to query balance"

	ErrNoBalanceEN     = "Insufficient leave balance"
	ErrHourNoBalanceEN = "Leave duration out of range"

	ErrApplyTimeEN         = "Invalid leave time"
	ERR_EMPTY_APPLY_TIMEEN = "Leave duration cannot be zero"
	ErrSelectTimeRepeatEN  = "Application already exists for the selected time period"

	ErrNotInCurrentYearEN  = "Leave type application time not in current year"
	ErrNotInCurrentMonthEN = "Leave type application time not in current month"

	ErrNotNeedRecordOutworkcheckEN = "No need to confirm outwork"

	ErrIsUsedEN = "This balance application cannot be revoked"

	ErrTimeOrderEN = "Start time should not be later than end time"

	ErrDayoffNotLessOneHourEN = "Compensatory leave duration should not be less than 1 hour"

	ErrOvertimeIsZeroEN = "Overtime duration cannot be zero"

	ErrOvertimeIsRepEN = "Overtime start and end times are repeated"

	ErrLeaveDurationIsZeroEN = "Duration cannot be zero"

	ErrHolidayMissEN = "Holiday information missing"

	ErrIpMissEN = "No IP information available"

	ErrWifiIsNotInEN = "Not in company intranet"

	ERR_GETIPEN = "Failed to get IP information"

	ErrLatLogEN = "Current location cannot be empty"

	ErrChickInMapEN = "Attendance address coordinates missing"

	ErrDbChickInMapEN = "Failed to query attendance address coordinates"

	ErrCacheChickInMapEN = "Failed to cache attendance address coordinates"

	ErrConfirmChickInMapEN = "Attendance address confirmation failed"

	ErrNotInChickInMapEN = "Not within attendance range"

	ErrMissSystemMakeUpParamEN = "System make-up parameters missing"

	ErrQueryWorkingTimeListEN = "Failed to query attendance list"

	ErrorWorkingTimeFindUserEN = "Failed to query user"

	ErrorWorkingTimeFindUserPositionEN = "Failed to query user position information"

	ErrorQueryOaProfileEN = "Failed to query user attendance information"

	ErrorQueryHolidayEN = "Failed to query holiday"

	ErrorQueryWorkingTimeEN = "Failed to query attendance information"

	ErrorMissWorkingTimeEN = "Attendance information not set"

	ErrorDelCacheGeoEN = "Failed to delete cached attendance coordinates"

	ErrorQueryRangeGeoEN = "Attendance range query error"

	ErrorSaveCacheGeoEN = "Failed to cache attendance coordinates"

	ErrorSaveCacheActualGeoEN = "Failed to cache actual attendance coordinates"

	ErrorCheckWorkTimeEN = "Failed to determine attendance time"

	ErrorQueryOutWorkCheckEN = "Failed to query outwork return records"

	ErrorCheckNextClockInTimeEN = "Failed to determine next attendance time"

	ErrorClockInEN = "Attendance failed"

	SuccessClockInEN = "Attendance successful"

	ErrorNotAllowedClockInEN = "Attendance not allowed"

	ErrorMissBreakOptionEN = "Missing break time information"

	ErrCheckAddressLimitEN = "Address check failed"

	ErrUserMissEN = "User information missing"

	ErrNoDataEN = "No data"

	ErrNotApplyUuidEN = "Application UUID cannot be empty"

	ErrNotHadWorkingTimeEN = "No associated attendance information, unable to attendance"

	ErrMissMakeUpRuleEN = "Make-up rule missing"

	ErrCanNotCalcOverTimeBehindTwoDaysEN = "Multi-date overtime application not supported"

	// Failed to query approval content
	ErrorQueryApprovalContentEN = "Failed to query approval content"

	// Overtime application time should not be within work hours
	ErrorOverTimeNotInWorkTimeEN = "Overtime application time should not be within work hours"

	ErrorMissOutBackEN = "Missing outwork or return to company identifier"

	ErrorMissLngLatEN = "Missing longitude and latitude information"

	SuccessCreateOutWorkCheckEN = "Successfully created outwork or return to company record"

	ErrorCreateOutWorkCheckEN = "Failed to create outwork or return to company record"

	ErrorFrequentClockInEN = "Can only submit once every 15 seconds"

	ErrorNoFaceImgEN = "User missing recent photo"

	ErrorNoCatchFaceImgEN = "Failed to capture recent photo during attendance"

	ErrorFaceCompareEN = "Face comparison failed"

	ErrorFaceCompareNotPassEN = "Face comparison not passed"

	// Invalid Attendance address
	ErrorInvalidChickInMapEN = "Invalid attendance address"

	ErrorTimeIsZeroEN = "Duration cannot be zero"

	ErrQueryEmployeeAttendanceEN = "Failed to query employee attendance binding information"

	ErrorWorkingTimeNotInUseEN = "Attendance settings will take effect on a"

	ErrorAddressEmptyEN = "Attendance address coordinates cannot be empty"

	ErrorQueryVersionHistoryEN = "Failed to query version history"
)

var errorMessages = map[string]string{
	"ErrAttendanceMiss":                  "ErrAttendanceMiss",
	"ErrAttendanceWeekMiss":              "ErrAttendanceWeekMiss",
	"ErrApplytimeDisagreement":           "ErrApplytimeDisagreement",
	"ErrOvertimeDisagreement":            "ErrOvertimeDisagreement",
	"ErrConfirmactiontype":               "ErrConfirmactiontype",
	"ErrConfirmweek":                     "ErrConfirmweek",
	"NotNeedUseLeave":                    "NotNeedUseLeave",
	"ErrQueryMultiData":                  "ErrQueryMultiData",
	"ErrQueryOaRecord":                   "ErrQueryOaRecord",
	"ErrQueryAbnormalOaRecord":           "ErrQueryAbnormalOaRecord",
	"ErrQueryOaApply":                    "ErrQueryOaApply",
	"ErrQueryOaLeaveApply":               "ErrQueryOaLeaveApply",
	"ERR_QUERY_OA_APPLY_OVERTIME":        "ERR_QUERY_OA_APPLY_OVERTIME",
	"ErrNotHaveBalance":                  "ErrNotHaveBalance",
	"ErrHaveBalanceApply":                "ErrHaveBalanceApply",
	"ErrQueryUser":                       "ErrQueryUser",
	"ErrQueryOaSetting":                  "ErrQueryOaSetting",
	"ErrQueryOaMonth":                    "ErrQueryOaMonth",
	"ERR_NOT_NEED_BALANCE":               "ERR_NOT_NEED_BALANCE",
	"ErrNoBalance":                       "ErrNoBalance",
	"ErrHourNoBalance":                   "ErrHourNoBalance",
	"ErrApplyTime":                       "ErrApplyTime",
	"ERR_EMPTY_APPLY_TIME":               "ERR_EMPTY_APPLY_TIME",
	"ErrSelectTimeRepeat":                "ErrSelectTimeRepeat",
	"ErrNotInCurrentYear":                "ErrNotInCurrentYear",
	"ErrNotInCurrentMonth":               "ErrNotInCurrentMonth",
	"ErrNotNeedRecordOutworkcheck":       "ErrNotNeedRecordOutworkcheck",
	"ErrIsUsed":                          "ErrIsUsed",
	"ErrTimeOrder":                       "ErrTimeOrder",
	"ErrDayoffNotLessOneHour":            "ErrDayoffNotLessOneHour",
	"ErrOvertimeIsZero":                  "ErrOvertimeIsZero",
	"ErrOvertimeIsRep":                   "ErrOvertimeIsRep",
	"ErrLeaveDurationIsZero":             "ErrLeaveDurationIsZero",
	"ErrHolidayMiss":                     "ErrHolidayMiss",
	"ErrIpMiss":                          "ErrIpMiss",
	"ErrWifiIsNotIn":                     "ErrWifiIsNotIn",
	"ERR_GETIP":                          "ERR_GETIP",
	"ErrLatLog":                          "ErrLatLog",
	"ErrChickInMap":                      "ErrChickInMap",
	"ErrDbChickInMap":                    "ErrDbChickInMap",
	"ErrCacheChickInMap":                 "ErrCacheChickInMap",
	"ErrConfirmChickInMap":               "ErrConfirmChickInMap",
	"ErrNotInChickInMap":                 "ErrNotInChickInMap",
	"ErrMissSystemMakeUpParam":           "ErrMissSystemMakeUpParam",
	"ErrQueryWorkingTimeList":            "ErrQueryWorkingTimeList",
	"ErrorWorkingTimeFindUser":           "ErrorWorkingTimeFindUser",
	"ErrorWorkingTimeFindUserPosition":   "ErrorWorkingTimeFindUserPosition",
	"ErrorQueryOaProfile":                "ErrorQueryOaProfile",
	"ErrorQueryHoliday":                  "ErrorQueryHoliday",
	"ErrorQueryWorkingTime":              "ErrorQueryWorkingTime",
	"ErrorMissWorkingTime":               "ErrorMissWorkingTime",
	"ErrorDelCacheGeo":                   "ErrorDelCacheGeo",
	"ErrorQueryRangeGeo":                 "ErrorQueryRangeGeo",
	"ErrorSaveCacheGeo":                  "ErrorSaveCacheGeo",
	"ErrorSaveCacheActualGeo":            "ErrorSaveCacheActualGeo",
	"ErrorCheckWorkTime":                 "ErrorCheckWorkTime",
	"ErrorQueryOutWorkCheck":             "ErrorQueryOutWorkCheck",
	"ErrorCheckNextClockInTime":          "ErrorCheckNextClockInTime",
	"ErrorClockIn":                       "ErrorClockIn",
	"SuccessClockIn":                     "SuccessClockIn",
	"ErrorNotAllowedClockIn":             "ErrorNotAllowedClockIn",
	"ErrorMissBreakOption":               "ErrorMissBreakOption",
	"ErrCheckAddressLimit":               "ErrCheckAddressLimit",
	"ErrUserMiss":                        "ErrUserMiss",
	"ErrNoData":                          "ErrNoData",
	"ErrNotApplyUuid":                    "ErrNotApplyUuid",
	"ErrNotHadWorkingTime":               "ErrNotHadWorkingTime",
	"ErrMissMakeUpRule":                  "ErrMissMakeUpRule",
	"ErrCanNotCalcOverTimeBehindTwoDays": "ErrCanNotCalcOverTimeBehindTwoDays",
	"ErrorQueryApprovalContent":          "ErrorQueryApprovalContent",
	"ErrorOverTimeNotInWorkTime":         "ErrorOverTimeNotInWorkTime",
	"ErrorMissOutBack":                   "ErrorMissOutBack",
	"ErrorMissLngLat":                    "ErrorMissLngLat",
	"SuccessCreateOutWorkCheck":          "SuccessCreateOutWorkCheck",
	"ErrorCreateOutWorkCheck":            "ErrorCreateOutWorkCheck",
	"ErrorFrequentClockIn":               "ErrorFrequentClockIn",
	"ErrorNoFaceImg":                     "ErrorNoFaceImg",
	"ErrorNoCatchFaceImg":                "ErrorNoCatchFaceImg",
	"ErrorFaceCompare":                   "ErrorFaceCompare",
	"ErrorFaceCompareNotPass":            "ErrorFaceCompareNotPass",
	"ErrorInvalidChickInMap":             "ErrorInvalidChickInMap",
	"ErrorTimeIsZero":                    "ErrorTimeIsZero",
	"ErrQueryEmployeeAttendance":         "ErrQueryEmployeeAttendance",
	"ErrorWorkingTimeNotInUse":           "ErrorWorkingTimeNotInUse",
	"ErrorAddressEmpty":                  "ErrorAddressEmpty",
	"ErrorQueryVersionHistory":           "ErrorQueryVersionHistory",
}

const (
	ErrAttendanceMiss        = "ErrAttendanceMiss"
	ErrAttendanceWeekMiss    = "ErrAttendanceWeekMiss"
	ErrApplytimeDisagreement = "ErrApplytimeDisagreement"
	ErrOvertimeDisagreement  = "ErrOvertimeDisagreement"

	ErrConfirmactiontype = "ErrConfirmactiontype"
	ErrConfirmweek       = "ErrConfirmweek"
	NotNeedUseLeave      = "NotNeedUseLeave"

	ErrQueryMultiData = "ErrQueryMultiData"

	ErrQueryOaRecord         = "ErrQueryOaRecord"
	ErrQueryAbnormalOaRecord = "ErrQueryAbnormalOaRecord"

	ErrQueryOaApply             = "ErrQueryOaApply"
	ErrQueryOaLeaveApply        = "ErrQueryOaLeaveApply"
	ERR_QUERY_OA_APPLY_OVERTIME = "ERR_QUERY_OA_APPLY_OVERTIME"
	ErrNotHaveBalance           = "ErrNotHaveBalance"
	ErrHaveBalanceApply         = "ErrHaveBalanceApply"

	ErrQueryUser = "ErrQueryUser"

	ErrQueryOaSetting = "ErrQueryOaSetting"

	ErrQueryOaMonth      = "ErrQueryOaMonth"
	ErrQueryLeaveBalance = "ErrQueryLeaveBalance"

	ERR_NOT_NEED_BALANCE = "ERR_NOT_NEED_BALANCE"

	ErrNoBalance     = "ErrNoBalance"
	ErrHourNoBalance = "ErrHourNoBalance"

	ErrApplyTime         = "ErrApplyTime"
	ERR_EMPTY_APPLY_TIME = "ERR_EMPTY_APPLY_TIME"
	ErrSelectTimeRepeat  = "ErrSelectTimeRepeat"

	ErrNotInCurrentYear  = "ErrNotInCurrentYear"
	ErrNotInCurrentMonth = "ErrNotInCurrentMonth"

	ErrNotNeedRecordOutworkcheck = "ErrNotNeedRecordOutworkcheck"

	ErrIsUsed = "ErrIsUsed"

	ErrTimeOrder = "ErrTimeOrder"

	ErrDayoffNotLessOneHour = "ErrDayoffNotLessOneHour"

	ErrOvertimeIsZero = "ErrOvertimeIsZero"

	ErrOvertimeIsRep = "ErrOvertimeIsRep"

	ErrLeaveDurationIsZero = "ErrLeaveDurationIsZero"

	ErrHolidayMiss = "ErrHolidayMiss"

	ErrIpMiss = "ErrIpMiss"

	ErrWifiIsNotIn = "ErrWifiIsNotIn"

	ERR_GETIP = "ERR_GETIP"

	ErrLatLog = "ErrLatLog"

	ErrChickInMap = "ErrChickInMap"

	ErrDbChickInMap = "ErrDbChickInMap"

	ErrCacheChickInMap = "ErrCacheChickInMap"

	ErrConfirmChickInMap = "ErrConfirmChickInMap"

	ErrNotInChickInMap = "ErrNotInChickInMap"

	ErrMissSystemMakeUpParam = "ErrMissSystemMakeUpParam"

	ErrQueryWorkingTimeList = "ErrQueryWorkingTimeList"

	ErrorWorkingTimeFindUser = "ErrorWorkingTimeFindUser"

	ErrorWorkingTimeFindUserPosition = "ErrorWorkingTimeFindUserPosition"

	ErrorQueryOaProfile = "ErrorQueryOaProfile"

	ErrorQueryHoliday = "ErrorQueryHoliday"

	ErrorQueryWorkingTime = "ErrorQueryWorkingTime"

	ErrorMissWorkingTime = "ErrorMissWorkingTime"

	ErrorDelCacheGeo = "ErrorDelCacheGeo"

	ErrorQueryRangeGeo = "ErrorQueryRangeGeo"

	ErrorSaveCacheGeo = "ErrorSaveCacheGeo"

	ErrorSaveCacheActualGeo = "ErrorSaveCacheActualGeo"

	ErrorCheckWorkTime = "ErrorCheckWorkTime"

	ErrorQueryOutWorkCheck = "ErrorQueryOutWorkCheck"

	ErrorCheckNextClockInTime = "ErrorCheckNextClockInTime"

	ErrorClockIn = "ErrorClockIn"

	SuccessClockIn = "SuccessClockIn"

	ErrorNotAllowedClockIn = "ErrorNotAllowedClockIn"

	ErrorMissBreakOption = "ErrorMissBreakOption"

	ErrCheckAddressLimit = "ErrCheckAddressLimit"

	ErrUserMiss = "ErrUserMiss"

	ErrNoData = "ErrNoData"

	ErrNotApplyUuid = "ErrNotApplyUuid"

	ErrNotHadWorkingTime = "ErrNotHadWorkingTime"

	ErrMissMakeUpRule = "ErrMissMakeUpRule"

	ErrCanNotCalcOverTimeBehindTwoDays = "ErrCanNotCalcOverTimeBehindTwoDays"

	ErrorOverTimeNotInWorkTime = "ErrorOverTimeNotInWorkTime"

	ErrorMissOutBack = "ErrorMissOutBack"

	ErrorMissLngLat = "ErrorMissLngLat"

	SuccessCreateOutWorkCheck = "SuccessCreateOutWorkCheck"

	ErrorCreateOutWorkCheck = "ErrorCreateOutWorkCheck"

	ErrorFrequentClockIn = "ErrorFrequentClockIn"

	ErrorNoFaceImg = "ErrorNoFaceImg"

	ErrorNoCatchFaceImg = "ErrorNoCatchFaceImg"

	ErrorFaceCompare = "ErrorFaceCompare"

	ErrorFaceCompareNotPass = "ErrorFaceCompareNotPass"

	ErrorInvalidChickInMap = "ErrorInvalidChickInMap"

	ErrorTimeIsZero = "ErrorTimeIsZero"

	ErrQueryEmployeeAttendance = "ErrQueryEmployeeAttendance"

	ErrorWorkingTimeNotInUse = "ErrorWorkingTimeNotInUse"

	ErrorAddressEmpty = "ErrorAddressEmpty"

	ErrorQueryVersionHistory = "ErrorQueryVersionHistory"
)

const (
	ErrSystemCommonParam = "系统修改异常参数缺失"

	ErrActionTimeNotBeforeNextDateFour = "最后一班下班卡的系统补卡时间不得晚于次日凌晨四点"

	ErrorIsNotBeforeOrEqualCollectionMonth = "仅支持查看当前及历史月考勤数据"

	ErrorEmployFindUser = "查询员工档案失败"

	ErrQueryPositionInfo = "查询部门岗位信息失败"

	ErrWorkingTimeNotInUse = "考勤设置将于   a   生效"

	ErrWritePDF = "写入PDF文件失败"
)

// export
const (
	ErrorCreateLocalFile = "创建本地文件失败"
	ErrorWriteLocalFile  = "写入本地文件失败"
	ErrorCloseLocalFile  = "关闭本地文件失败"
	ErrorPutOos          = "上传文件失败"
	ErrorWriteExcel      = "写入excel失败"

	ErrorCreateExportTask = "创建导出任务失败"

	ErrParamWithoutWorkDate = "缺少工作日信息"
)

const (
	InUse = iota + 1
	NotInUse
	InUseAndEffectiveDateIsNextDate
)

const (
	IsNotWorkDay = iota + 1
	IsWorkDay
)

const (
	NotAllowOutWork = iota + 1
	AllowOutWork
)

func VerifyMsgKey(key string) bool {
	msg := errorMessages[key]
	if msg == "" {
		return false
	}
	return true
}

func GetMsgZhCN(key string) string {
	return errorMessagesZhCN[key]
}

func GetMsgZhTW(key string) string {
	return errorMessagesZhTW[key]
}

func GetMsgEN(key string) string {
	return errorMessagesEN[key]
}
