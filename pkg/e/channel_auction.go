package e

const (
	FanAuctionChannel_Artwork           = "artwork"          //作品信息
	FanAuctionChannel_NowPrice          = "nowAuctionPrice"  //当前拍卖藏品价格
	FanAuctionChannel_PriceList         = "auctionPriceList" //当前拍卖藏品价格
	FanAuctionChannel_First             = "first"            //首次进入 去了tip数据之外
	FanAuctionChannel_NewArtwork        = "newArtwork"       //切换新的作品
	FanAuctionChannel_StopArtwork       = "stopArtwork"      //当前作品停牌
	FanAuctionChannel_Over              = "over"             //当前场次停止
	FanAuctionChannel_Next              = "next"             //当前场次停止
	FanAuctionChannel_Pay_List          = "payList"          //当前场次停止
	FanAuctionChannel_Change_Price_Info = "changePriceInfo"  //当前场次停止

	FanAuctionChannel_Tip = "tip" // 提示
)

const (
	FanAuctionChannel_Tip_Falling   = "falling"   // 即将落锤
	FanAuctionChannel_Tip_OthersBid = "othersBid" //已有人出价

	FanAuctionChannel_TempArtowkrOver = "temp-over"   //临时结束了  替代
	FanAuctionChannel_Tip_SuccessBid  = "successBid"  // 成功购买
	FanAuctionChannel_Tip_FailBid     = "failBid"     //failBid
	FanAuctionChannel_Tip_ArtworkOver = "artworkOver" //本拍品结束
	FanAuctionChannel_Tip_myBidEffect = "myffectBid"  //已有人出价

	FanAuctionChannel_Tip_other = "other" //other
)

const (
	StatusSuccess = "success"
)

var FanAuctionStateMap = map[string]int{
	FanAuctionChannel_Artwork:           1,
	FanAuctionChannel_NowPrice:          1,
	FanAuctionChannel_PriceList:         1,
	FanAuctionChannel_First:             1,
	FanAuctionChannel_NewArtwork:        1,
	FanAuctionChannel_StopArtwork:       1,
	FanAuctionChannel_Over:              1,
	FanAuctionChannel_Tip:               1,
	FanAuctionChannel_Change_Price_Info: 1,
}
