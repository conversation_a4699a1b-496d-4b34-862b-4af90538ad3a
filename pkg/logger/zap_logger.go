package logger

import (
	appconfig "github.com/fonchain_enterprise/client-auction/cmd/config"
	"github.com/natefinch/lumberjack"
	"go.uber.org/zap"
	"go.uber.org/zap/zapcore"
	"os"
)

func LogInit(appConfig *appconfig.Config) *zap.Logger {
	var err error
	writeSyncer := getLogWriter(appConfig.ZapLog.Filename, appConfig.ZapLog.MaxSize, appConfig.ZapLog.MaxBackups, appConfig.ZapLog.MaxAge)
	encoder := getEncoder()
	var l = new(zapcore.Level)
	err = l.UnmarshalText([]byte(appConfig.ZapLog.Level))
	if err != nil {
		return nil
	}
	var core zapcore.Core
	//if appConfig.System.Mode == "dev" {
	// 进入开发模式，日志输出到终端
	consoleEncoder := zapcore.NewConsoleEncoder(zap.NewDevelopmentEncoderConfig())
	core = zapcore.NewTee(
		zapcore.NewCore(encoder, writeSyncer, l),
		zapcore.NewCore(consoleEncoder, zapcore.Lock(os.Stdout), zapcore.DebugLevel),
	)
	//} else {
	//	core = zapcore.NewCore(encoder, writeSyncer, l)
	//}
	lg := zap.New(core, zap.AddCaller())
	zap.ReplaceGlobals(lg)
	return lg
}

func getEncoder() zapcore.Encoder {
	encoderConfig := zap.NewProductionEncoderConfig()
	encoderConfig.EncodeTime = zapcore.ISO8601TimeEncoder
	encoderConfig.TimeKey = "time"
	encoderConfig.EncodeLevel = zapcore.CapitalLevelEncoder
	encoderConfig.EncodeDuration = zapcore.SecondsDurationEncoder
	encoderConfig.EncodeCaller = zapcore.ShortCallerEncoder
	return zapcore.NewJSONEncoder(encoderConfig)
}

func getLogWriter(filename string, maxSize, maxBackup, maxAge int) zapcore.WriteSyncer {
	lumberJackLogger := &lumberjack.Logger{
		Filename:   filename,
		MaxSize:    maxSize,
		MaxBackups: maxBackup,
		MaxAge:     maxAge,
	}
	return zapcore.AddSync(lumberJackLogger)
}
