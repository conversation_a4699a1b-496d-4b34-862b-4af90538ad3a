package common

import (
	"archive/zip"
	"bytes"
	"errors"
	"fmt"
	"github.com/fonchain_enterprise/client-auction/api/account"
	"github.com/fonchain_enterprise/client-auction/pkg/common/oss"
	"github.com/fonchain_enterprise/client-auction/pkg/e"
	"github.com/fonchain_enterprise/client-auction/pkg/service"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
	"io"
	"mime/multipart"
	"net/http"
	"os"
	"path"
	"path/filepath"
	"strings"
)

func Search(c *gin.Context) {
	service.Success(c, "")
	return
}

func UploadImg(c *gin.Context) {
	file, err := c.FormFile("file")
	if err != nil {
		service.Error(c, e.Error, err)
		return
	}

	buff := new(bytes.Buffer)
	var src multipart.File
	src, err = file.Open()
	defer src.Close()

	_, err = io.Copy(buff, src)
	if err != nil {
		service.Error(c, e.Error, err)
		return
	}

	fileExt := path.Ext(file.Filename)
	filePath, err := oss.OssUploadToBos(fileExt, buff.Bytes())
	if err != nil {
		service.Error(c, e.Error, err)
		return
	}

	res := make(map[string]string)
	res["path"] = filePath

	service.Success(c, res)
	return
}

// 判断是否实名
func IsRealName(ctx *gin.Context, in *account.InfoRequest) (bool, error) {
	fmt.Println("/**********判断是否11实名***********/")
	if in.ID == 0 {
		return false, nil
	}
	fmt.Println("1--------", in)
	userInfo, err := service.AccountProvider.UserInfoById(ctx, in)
	fmt.Println("userInfo: ", userInfo)
	if err != nil {
		fmt.Println("error...", err)
		//service.Error(ctx, e.Error, err)
		return false, err
	}
	fmt.Println("/************22*****************")
	fmt.Println("userinfo: ", userInfo)
	if userInfo.IDNum != "" || userInfo.Passport != nil {
		fmt.Println("实名")
		return true, nil
	}
	return false, nil
}

// CreateDirPath 递归创建文件夹
func CreateDirPath(path string) (err error) {
	if _, err = os.Stat(path); os.IsNotExist(err) {
		if err = os.MkdirAll(path, os.ModePerm); err != nil {
			return
		}
	}
	return
}

func SaveUrlFileDisk(url string, path string, filename string) (fullPath string, err error) {
	if err = CreateDirPath(path); err != nil {
		zap.L().Error("SaveUrlFileDisk err ", zap.Error(err))
		return
	}
	if filename == "" {
		stepName := strings.Split(url, "/")
		if len(stepName) > 1 {
			filename = stepName[len(stepName)-1]
		}
	}

	resp, err := http.Get(url)
	if err != nil {
		err = errors.New(e.GetMsg(e.ERROR_DOWNLOAD_FILE))
		return
	}
	defer func() {
		resp.Body.Close()
	}()
	byteData, err := io.ReadAll(resp.Body)
	fullPath = fmt.Sprintf("%s/%s", path, filename)
	// 写入数据
	err = os.WriteFile(fullPath, byteData, 0777)
	return
}

// ZipFile 打包成zip文件
// @title    ZipDir
// @description   将文件夹下的文件打包成zip
// @auth      dyb             时间
// @param     src        string         文件夹路径
// @return    err        error         错误内容
func ZipFile(srcDir string, zipFilePath string) (err error) {
	// 预防：旧文件无法覆盖
	os.RemoveAll(zipFilePath)
	// 创建：zip文件
	zipfile, _ := os.Create(zipFilePath)
	defer zipfile.Close()
	// 打开：zip文件
	archive := zip.NewWriter(zipfile)
	defer archive.Close()
	// 遍历路径信息
	filepath.Walk(srcDir, func(path string, info os.FileInfo, _ error) error {
		// 如果是源路径，提前进行下一个遍历
		if path == srcDir {
			return nil
		}
		// 获取：文件头信息
		header, _ := zip.FileInfoHeader(info)
		header.Name = strings.TrimPrefix(path, srcDir+`\`)
		header.Name = strings.Replace(path, srcDir, "", 1)
		if header.Name[:1] == "\\" || header.Name[:1] == "/" {
			header.Name = header.Name[1:]
		}
		// 判断：文件是不是文件夹
		if info.IsDir() {
			header.Name += `/`
		} else {
			// 设置：zip的文件压缩算法
			header.Method = zip.Deflate
		}
		// 创建：压缩包头部信息
		writer, _ := archive.CreateHeader(header)
		if !info.IsDir() {
			file, _ := os.Open(path)
			defer file.Close()
			io.Copy(writer, file)
		}
		return nil
	})
	return
}

func CompressFolderToZip(sourceDir, zipFilePath string) error {
	// Create the zip file
	zipFile, err := os.Create(zipFilePath)
	if err != nil {
		return err
	}
	defer zipFile.Close()

	// Create a new zip writer
	archive := zip.NewWriter(zipFile)
	defer archive.Close()

	// Walk through the source directory
	err = filepath.Walk(sourceDir, func(filePath string, info os.FileInfo, err error) error {
		if err != nil {
			return err
		}

		// Skip directories
		if info.IsDir() {
			return nil
		}

		// Create zip header
		header, err := zip.FileInfoHeader(info)
		if err != nil {
			return err
		}

		// Set the name to be relative to the source directory
		header.Name, err = filepath.Rel(sourceDir, filePath)
		if err != nil {
			return err
		}

		// Set compression method
		header.Method = zip.Deflate

		// Create writer for this file
		writer, err := archive.CreateHeader(header)
		if err != nil {
			return err
		}

		// Open the source file
		file, err := os.Open(filePath)
		if err != nil {
			return err
		}
		defer file.Close()

		// Copy file contents to zip
		_, err = io.Copy(writer, file)
		return err
	})

	return err
}
