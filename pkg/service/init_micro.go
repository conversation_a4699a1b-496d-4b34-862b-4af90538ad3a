package service

import (
	"dubbo.apache.org/dubbo-go/v3/config"
	_ "dubbo.apache.org/dubbo-go/v3/imports"
	"fmt"
	"os"
	"strings"
)

func init_micro() {
	config.SetConsumerService(CustomContractProvider)
	config.SetConsumerService(ContractProvider)
	config.SetConsumerService(OrderProvider)
	config.SetConsumerService(PaymentProvider)
	config.SetConsumerService(ChainProvider)

	//一般没有这个环境变量，所以一般是需要加载 走else逻辑
	fmt.Printf("打印环境变量", strings.TrimSpace(os.Getenv("not_artist")))
	if strings.TrimSpace(os.Getenv("not_artist")) == "1" { //

	} else {
		config.SetConsumerService(ArtworkQueryProvider)
		config.SetConsumerService(ArtistProvider)
	}
}

func init_auth() {
	config.SetConsumerService(PositionProvider)
	config.SetConsumerService(AccountProvider)
	config.SetConsumerService(RuleProvider)
	config.SetConsumerService(DepartmentProvider)
}

func init_fenghe() {
	config.SetConsumerService(FengheProvider)
	config.SetConsumerService(BackendSeriesProvider)
	config.SetConsumerService(BrandProvider)
	config.SetConsumerService(ContractProvider)

}

func init_photoWall() {
	config.SetConsumerService(PhotoWallProvider)
}
