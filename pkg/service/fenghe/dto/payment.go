// Package dto -----------------------------
// @file      : payment.go
// <AUTHOR> JJXu
// @contact   : <EMAIL>
// @time      : 2025/4/15 18:18
// -------------------------------------------
package dto

import "github.com/fonchain_enterprise/client-auction/pkg/service/order/payment"

type CreateOrderRequest struct {
	SeriesUid  string `json:"seriesUid"`  //系列uid
	ArtworkUid string `json:"artworkUid"` //画作Uid
	BuyUid     string `json:"buyUid"`     //拍卖uid

	PayPlat   payment.PayPlatType `json:"payPlat"`   //支付平台 1=微信 2=支付宝 3=stripe
	PayMethod payment.PayMethod   `json:"payMethod"` //支付方式 1=H5 2=app
	Price     string              `json:"price"`
	Currency  string              `json:"currency"`
	ReturnUrl string              `json:"returnUrl"` //安通的h5支付返回地址
	V         int                 `json:"v"`         //如果为2时，支付渠道会被记录为其它
}

type PaymentInfoReq struct {
	TradeNo string `json:"tradeNo"` //交易流水号
}

type OrderRecordsReq struct {
	Page            int64  `json:"page"`
	PageSize        int64  `json:"pageSize"`
	OrderNo         string `json:"orderNo"`
	ActionCode      string `json:"actionCode"`
	ReceivingStatus uint32 `json:"receivingStatus"`
}

type OrderRecordType struct {
	OrderNo         string `json:"orderNo"` //订单号
	Amount          string `json:"amount"`  //金额
	LeftPrice       string `json:"leftPrice"`
	PaidPrice       string `json:"paidPrice"`
	Currency        string `json:"currency"` //货币单位
	Status          int32  `json:"status"`   //支付状态 1 未支付  2 支付成功 3 支付失败 4部分支付
	PayTime         string `json:"payTime"`
	ArtworkUid      string `json:"artworkUid"`
	Tfnum           string `json:"tfnum"`
	ArtworkName     string `json:"artworkName"`
	HdPic           string `json:"hdPic"`
	ArtistName      string `json:"artistName"`
	ArtistUid       string `json:"artistUid"`
	Abstract        string `json:"abstract"`
	Width           int32  `json:"width"`
	Length          int32  `json:"length"`
	Ruler           int32  `json:"ruler"`
	SeriesUid       string `json:"seriesUid"`
	ReceivingStatus uint32 `json:"receivingStatus"`
	ShowStatus      uint32 `json:"showStatus"`
	CompanyId       uint32 `json:"companyId"`
	StaffId         uint32 `json:"staffId"`
	CompanyName     uint32 `json:"companyName"`
	StaffName       uint32 `json:"staffName"`
}

type OrderRecordInfoReq struct {
	OrderNo string `json:"orderNo"`
}

type RefundOrderReq struct {
	PayPlat      payment.PayPlatType `json:"payPlat"` //支付平台 1=微信 2=支付宝 3=stripe
	PayUid       string              `json:"payUid"`
	TradeNo      string              `json:"tradeNo"`
	RefundAmount string              `json:"refundAmount"`
	PaidAmount   string              `json:"paidAmount"`
}

type ConfirmReceiveOrderReq struct {
	Code    string `json:"code"`
	OrderNo string `json:"orderNo"`
	TelNum  string `json:"telNum"`
}
type SendSeriesOrderReceiveSmsReq struct {
	Phone string `json:"phone"`
}

type OrderExportReq struct {
	SeriesUid string `json:"seriesUid" form:"seriesUid"`
}
