// Package mainPage -----------------------------
// @file      : dto.go
// <AUTHOR> JJXu
// @contact   : <EMAIL>
// @time      : 2025/4/9 18:38
// -------------------------------------------
package dto

import "github.com/fonchain_enterprise/client-auction/api/fenghe"

type GetAdmSeriesProfileListRequest struct {
	fenghe.GetSeriesProfileListRequest
	SortMode int //1=销售额降序  2=销售和升序	3=创建时间升序  4=创建是将降序
}

type ShowDetail struct {
	SeriesUid     string                 `json:"seriesUid"`
	SeriesName    string                 `json:"seriesName"`
	BrandId       string                 `json:"brandId"`
	BrandName     string                 `json:"brandName"`
	BrandLogo     string                 `json:"brandLogo"`
	CoverImg      string                 `json:"coverImg"`
	ActionCode    string                 `json:"actionCode"`
	Desc          string                 `json:"desc"`
	ArtworkNum    int                    `json:"artworkNum"`
	Link          string                 `json:"link"`
	ArtworkList   []Artwork              `json:"artworkList"`
	Auction       *fenghe.AuctionRequest `json:"auction"`
	AuctionUserNo string                 `json:"auctionUserNo"`
}
type Artwork struct {
	ArtworkUid  string `json:"artworkUid"`
	Tfnum       string `json:"tfnum"`
	ArtworkName string `json:"artworkName"`
	HdPic       string `json:"hdPic"`
	Ruler       int32  `json:"ruler"`
	Length      int32  `json:"length"`
	Width       int32  `json:"width"`
	ArtistName  string `json:"artistName"`
	ArtistUid   string `json:"artistUid"`
	Abstract    string `json:"abstract"`
	//Tnum        string `json:"tnum"`
	OrderNo     string `json:"orderNo"` //购买单号
	BuyerId     int32  `json:"buyerId"` //购买人Id
	Price       string `json:"price"`
	Currency    string `json:"currency"`
	SoldLock    int    `json:"soldLock"`    //0=无 1=再次被上锁
	CompanyId   uint32 `json:"companyId"`   //0=无 1=再次被上锁
	CompanyName string `json:"companyName"` //0=无 1=再次被上锁
	ShowStatus  int64  `json:"showStatus"`  //新版订单状态
	// 新增字段 2025-07-23
	OrderCreatedAt string `json:"orderCreatedAt"` // 订单创建时间
	ArrivalDate    string `json:"arrivalDate"`    // 到账日期
	SignFinishTime string `json:"signFinishTime"` // 合同签署时间
	ReceivingTime  string `json:"receivingTime"`  // 确认收货时间
	PayTime        string `json:"payTime"`        //支付时间
}

type SeriesArtworkPayRecord struct {
	//OrderNo  string `json:"orderNo"`
	//SeriesUid  string `json:"seriesUid"`
	//ActionCode string `json:"actionCode"`
	Page     int64 `json:"page"`
	PageSize int64 `json:"pageSize"`
	fenghe.SeriesOrderData
	Keywords         string `json:"keywords"`
	ArrivalDateBegin string `json:"arrivalDateBegin"` //到账时间起始
	ArrivalDateEnd   string `json:"arrivalDateEnd"`   //到账时间结束
}

type PayRecordListRequest struct {
	OrderNo  string `json:"orderNo"`
	Page     uint64 `json:"page"`
	PageSize uint64 `json:"pageSize"`
}

type PayRecordType struct {
	OrderNo       string   `json:"orderNo"`  //订单号
	TradeNo       string   `json:"tradeNo"`  //交易流水号
	Amount        string   `json:"amount"`   //金额
	Currency      string   `json:"currency"` //货币单位
	Plat          string   `json:"plat"`     //付款渠道
	Status        uint32   `json:"status"`   //支付状态 1 支付成功 2 支付失败 3未支付
	PayTime       string   `json:"payTime"`  //支付时间
	FukuanPprof   []string `json:"fukuanPprof"`
	ShouKuanPprof []string `json:"shouKuanPprof"`
	Remark        string   `json:"remark"`
	OptUserId     int64    `json:"optUserId"`
	OptUserName   string   `json:"optUserName"`
	OptTime       string   `json:"optTime"`
	//LotNo       string   `json:"lotNo"`
	UserId uint32 `json:"userId,omitempty"`
	Phone  string `json:"phone,omitempty"`

	ArrivalDate   string `json:"arrivalDate"`
	PaymentType   string `json:"paymentType"`
	BankAccount   string `json:"bankAccount"`
	AgentIncome   string `json:"agentIncome"`
	AfterTax      string `json:"afterTax"`
	IncomeCompany string `json:"incomeCompany"`
	ServiceFee    string `json:"serviceFee"`
}

type UserItem struct {
	Phone  string `json:"phone"`
	UserId uint32 `json:"userId"`
}
