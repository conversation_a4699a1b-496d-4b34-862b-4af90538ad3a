package dto

import "github.com/fonchain_enterprise/client-auction/api/fenghe"

type GetSeriesPaymentPprofListRequest struct {
	Page     int64 `json:"page"`
	PageSize int64 `json:"pageSize"`
	fenghe.SeriesPaymentPprofData
}
type CreatePprofReq struct {
	OrderNo   string   `json:"orderNo"`
	PprofUrls []string `json:"pprofUrls"`
	Remark    string   `json:"remark"`
	PayTime   string   `json:"payTime"`

	ArrivalDate   string `json:"arrivalDate"`
	PaymentType   string `json:"paymentType"`
	BankAccount   string `json:"bankAccount"`
	AgentIncome   string `json:"agentIncome"`
	AfterTax      string `json:"afterTax"`
	IncomeCompany string `json:"incomeCompany"`
	ServiceFee    string `json:"serviceFee"`
}

type OrderUserReplaceReq struct {
	OrderNo string `json:"orderNo"` //订单编号
	//方式1 指定用户id
	UserId uint64 `json:"userId"` //客户id
	//方式2 指定用户手机号、姓名、身份证
	TelNum   string `json:"telNum"` //手机号
	UserName string `json:"userName"`
	CardId   string `json:"cardId"`

	GenAuctionUserNo bool `json:"genAuctionUserNo"` //生成用户号牌
}

type UserInfo struct {
	UserID   uint64 `json:"userId"`
	UserName string `json:"userName"`
	TelNum   string `json:"telNum"`
}
