package dto

import "github.com/fonchain_enterprise/client-auction/api/fenghe"

type GetSeriesArtworkListRequest struct {
	Page     int64 `json:"page"`
	PageSize int64 `json:"pageSize"`
	fenghe.SeriesArtworkData
	SeriesUid string `json:"seriesUid"`
}

type GetArtworkListInArtworkServiceReq struct {
	ActionCode string `json:"actionCode"`
	Name       string `json:"name"`
	ArtistName string `json:"artistName"`
	Tfnum      string `json:"tfnum"`
	Tnum       string `json:"tnum"`
	ArtistUid  string `json:"artistUid"`
	Page       int64  `json:"page"`
	PageSize   int64  `json:"pageSize"`
}
