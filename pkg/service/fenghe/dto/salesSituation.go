// Package adm -----------------------------
// @file      : salesState.go
// <AUTHOR> JJXu
// @contact   : <EMAIL>
// @time      : 2025/4/12 12:52
// -------------------------------------------
package dto

type SalesRecordType struct {
	LotNo                 string          `json:"lotNo"`       //Lot号
	ArtworkName           string          `json:"artworkName"` //画作名
	ArtworkUid            string          `json:"artworkUid"`  //画作UID
	HdPic                 string          `json:"hdPic"`       //高清图
	Amount                string          `json:"amount"`      //价格
	Currency              string          `json:"currency"`    //货币单位
	Status                int32           `json:"status"`      //支付状态
	PayTime               string          `json:"payTime"`     //付款时间
	BuyTime               string          `json:"buyTime"`     //购买时间
	BuyUserName           string          `json:"buyUserName"` //购买人
	BuyUserTel            string          `json:"buyUserTel"`  //购买人手机号
	OrderNo               string          `json:"orderNo"`     //订单信息
	Tfnum                 string          `json:"tfnum"`       //画作编号
	ReceivingStatus       uint32          `json:"receivingStatus"`
	AuctionUserNo         string          `json:"auctionUserNo"`         //拍卖号
	CompanyId             uint32          `json:"companyId"`             //拍卖号
	CompanyName           string          `json:"companyName"`           //拍卖号
	StaffId               uint32          `json:"staffId"`               //拍卖号
	StaffName             string          `json:"staffName"`             //拍卖号
	ShowStatus            uint32          `json:"showStatus"`            //拍卖号
	AuctionCode           string          `json:"auctionCode"`           //系列类型 show=画展 auction=拍卖
	PayType               uint32          `json:"payType"`               // 支付类型（文创）
	ActualPaymentAmount   string          `json:"actualPaymentAmount"`   // 实际支付的金额（文创）
	ActualPaymentCurrency string          `json:"actualPaymentCurrency"` // 支付币种（文创）
	CommodityInfo         []CommodityInfo `json:"commodityInfo"`
	ArtworkMask           int32           `json:"artworkMask"`    //画作类型 1=一手画 2=二手画
	SoldCopyright         int32           `json:"soldCopyright"`  //是否售卖版权 1=是 2=否
	CopyrightPrice        string          `json:"copyrightPrice"` //版权价格
	SignStatus            int32           `json:"signStatus"`     //合同签署状态 1=未签署 2=已签署
	SignFinishTime        string          `json:"signFinishTime"` //合同全部完成签署时间
	ArrivalDate           string          `json:"arrivalDate"`    //到货时间
	PaymentType           string          `json:"paymentType"`    //付款方式
	BankAccount           string          `json:"bankAccount"`    //入账银行
	AgentIncome           string          `json:"agentIncome"`    //代理收入
	AfterTax              string          `json:"afterTax"`       //税后收入
	IncomeCompany         string          `json:"incomeCompany"`  //收入计公司
	ServiceFee            string          `json:"serviceFee"`     //服务费
	ReceivingTime         string          `json:"receivingTime"`  //确认收货时间
	CreatedAt             string          `json:"createdAt"`      //订单创建时间
}

type AuctionStat struct {
	TodayOnlinePersonTotal int64 `json:"todayOnlinePersonTotal"` // 今日累计在线人数
	TodayOnlineUserTotal   int64 `json:"todayOnlineUserTotal"`   // 今日累计用户数
	TodayOnlinePersonMax   int64 `json:"todayOnlinePersonMax"`   // 今日最高人数
}

type CultureAddition struct {
	PayType               int             `json:"payType"`
	Currency              string          `json:"currency"`
	ActualPaymentAmount   string          `json:"actualPaymentAmount"`
	ActualPaymentCurrency string          `json:"actualPaymentCurrency"`
	CommodityInfo         []CommodityInfo `json:"commodityInfo"`
}

type CommodityInfo struct {
	Name       string `json:"name"`
	BriefImage string `json:"briefImage"`
	Price      string `json:"price"`
	Currency   string `json:"currency"`
	Count      int    `json:"count"`
}
