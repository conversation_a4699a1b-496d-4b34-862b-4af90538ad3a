// Package handle -----------------------------
// @file      : front.go
// <AUTHOR> JJXu
// @contact   : <EMAIL>
// @time      : 2025/4/12 12:50
// -------------------------------------------
package handle

import (
	"context"
	"dubbo.apache.org/dubbo-go/v3/common/logger"
	"errors"
	"fmt"
	"github.com/fonchain_enterprise/client-auction/api/custom_contract"
	"github.com/fonchain_enterprise/client-auction/api/department"
	"github.com/fonchain_enterprise/client-auction/api/fenghe"
	backendSeries "github.com/fonchain_enterprise/client-auction/api/series"
	"github.com/fonchain_enterprise/client-auction/api/shopbrand"
	"github.com/fonchain_enterprise/client-auction/pkg/common"
	"github.com/fonchain_enterprise/client-auction/pkg/e"
	"github.com/fonchain_enterprise/client-auction/pkg/logic"
	"github.com/fonchain_enterprise/client-auction/pkg/middleware"
	"github.com/fonchain_enterprise/client-auction/pkg/model/query"
	"github.com/fonchain_enterprise/client-auction/pkg/service"
	"github.com/fonchain_enterprise/client-auction/pkg/service/fenghe/dto"
	"github.com/gin-gonic/gin"
	"github.com/jinzhu/copier"
	"strings"
	"time"
)

// 客户端-首页-系列列表
func GetSeriesProfileList(c *gin.Context) {
	var req fenghe.GetSeriesProfileListRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		service.Error(c, e.InvalidParams, err, "无效参数")
		return
	}
	req.OmitFields = append(req.OmitFields, "sales_total")
	req.Lang = logic.GetLanguage(c)
	fmt.Println("req.Lang", req.Lang)
	timeStamp := time.Now().Unix()
	//只查上架状态的数据
	req.Where = fmt.Sprintf("shelf_state=2 OR (shelf_state=1 AND launch_end_time > %d and launch_start_time <= %d) OR (shelf_state=1 AND launch_end_time =0 and launch_start_time <= %d)", timeStamp, timeStamp, timeStamp)
	resp, err := service.FengheProvider.GetSeriesProfileList(c, &req)
	if err != nil {
		service.Error(c, e.Error, err)
		return
	}
	service.List(c, resp.List, service.OptionPage(resp.Page, resp.PageSize, resp.Total), service.OptionMsg("查询成功"))
}

func ShowDetailArtwork(c *gin.Context) {
	var queryInfo query.SeriesArtworkDetailReq
	if err := c.ShouldBind(&queryInfo); err != nil {
		logger.Errorf("SeriesDetailReq ShouldBind err", err)
		service.ResponseQuickMsg(c, e.Failed, err.Error(), nil)
		return
	}
	req := backendSeries.SeriesDetailReq{SeriesUuid: queryInfo.SeriesUuid}
	req.Lang = logic.GetLanguage(c)
	fmt.Println("req.Lang", req.Lang)
	resp, err := service.BackendSeriesProvider.SeriesDetail(c, &req)
	if err != nil {
		service.Error(c, e.Failed, err)
		return
	}
	if resp.ShelfState == 0 {
		service.Error(c, e.Error14SeriesNotShelf, nil, "该系列已下架")
		return
	}
	brandResp, _ := service.BrandProvider.BrandInfo(c, &shopbrand.BrandInforeq{
		BrandUid: resp.BrandId,
		Lang:     req.Lang,
	})
	var result = dto.ShowDetail{
		SeriesUid:  resp.SeriesUuid,
		SeriesName: resp.SeriesName,
		BrandId:    resp.BrandId,
		BrandName:  resp.BrandName,
		CoverImg:   resp.CoverImg,
		ActionCode: resp.ActionCode,
		Desc:       resp.Desc,
		Link:       resp.Link,
		ArtworkNum: len(resp.Artworks),
	}
	if brandResp != nil {
		result.BrandLogo = brandResp.Logo
	}
	for _, v := range resp.Artworks {

		if v.ArtworkUid != queryInfo.ArtworkUid {
			continue
		}

		v := v
		tmp := dto.Artwork{
			ArtworkUid:  v.ArtworkUid,
			Tfnum:       v.Tfnum,
			ArtworkName: v.ArtworkName,
			HdPic:       v.HdPic,
			Ruler:       v.Ruler,
			Length:      v.Length,
			Width:       v.Width,
			ArtistName:  v.ArtistName,
			ArtistUid:   v.ArtistUid,
			Abstract:    v.Abstract,
			//Tnum:        v.Tnum,
			Price:    v.Price,
			Currency: v.Currency,
		}
		//查询是否被下单,只要在订单列表中（不管支付是否成功）都不能再次被购买
		var orderResp *fenghe.GetSeriesOrderListResp
		orderResp, err = service.FengheProvider.GetSeriesOrderList(c, &fenghe.GetSeriesOrderListRequest{
			Query:    &fenghe.SeriesOrderData{ArtworkUuid: v.ArtworkUid},
			Page:     1,
			PageSize: 1,
		})
		if err != nil {
			service.Error(c, e.Failed, nil, "查询失败")
			return
		}
		if orderResp.Total > 0 {
			tmp.SoldLock = 1 //上锁
		}
		result.ArtworkList = append(result.ArtworkList, tmp)
	}

	var artworkUidList []string
	for _, v := range result.ArtworkList {
		artworkUidList = append(artworkUidList, v.ArtworkUid)
	}
	if len(artworkUidList) > 0 {
		orderListRes, errs := service.FengheProvider.GetSeriesOrderList(c, &fenghe.GetSeriesOrderListRequest{
			Page:     1,
			PageSize: int64(len(artworkUidList)),
			Where:    fmt.Sprintf("artwork_uuid in ('%s')", strings.Join(artworkUidList, "','")),
		})
		if errs != nil {
			service.Error(c, e.Failed, errs)
			return
		}
		for i, v := range orderListRes.List {
			for ii, vv := range result.ArtworkList {
				if v.ArtworkUuid == vv.ArtworkUid {
					result.ArtworkList[ii].BuyerId = orderListRes.List[i].UserID
					result.ArtworkList[ii].OrderNo = orderListRes.List[i].OrderNo
					result.ArtworkList[ii].ShowStatus = int64(orderListRes.List[i].ShowStatus)
					result.ArtworkList[ii].OrderCreatedAt = orderListRes.List[i].CreatedAt
					//result.ArtworkList[ii].ArrivalDate = orderListRes.List[i].ArrivalDate
					result.ArtworkList[ii].ReceivingTime = orderListRes.List[i].ReceivingTime
					//查询支付时间
					payRecords, _ := service.FengheProvider.ListAuctionPay(c, &fenghe.AuctionPayList{
						OrderNo: v.OrderNo,
						Status:  1,
					})
					if payRecords != nil && payRecords.Data != nil {
						for _, p := range payRecords.Data {
							result.ArtworkList[ii].PayTime = p.UpdatedAt
							break
						}
					}
				}
			}
		}

		//更新合同完全签署时间
		var orderNos []string
		for _, v := range orderListRes.List {
			if v.OrderNo != "" {
				orderNos = append(orderNos, v.OrderNo)
			}
		}
		if len(orderNos) > 0 {
			signInfoResp, _err := service.CustomContractProvider.SignInfo(context.Background(), &custom_contract.SignInfoReq{OrderNos: orderNos})
			if _err != nil {
				service.Error(c, e.Failed, _err, "查询失败")
				return
			}
			if len(signInfoResp.Data) > 0 {
				for i, v := range result.ArtworkList {
					for _, vv := range signInfoResp.Data {
						if vv.OrderID == v.OrderNo {
							result.ArtworkList[i].SignFinishTime = vv.SignDate
						}
					}
				}
			}
		}
	}

	service.Success(c, result)
	return
}

func setCompanyStaff(c context.Context, list []*backendSeries.SeriesArtworkData) {

	companyMap := make(map[uint32]string)

	syncList, _ := service.DepartmentProvider.BaseSyncList(c, &department.BaseAllRequest{})

	for _, v := range syncList.Data {
		companyMap[uint32(v.ID)] = v.Name
	}

	for k, v := range list {
		if v.CompanyId != 0 {
			if _, isE := companyMap[v.CompanyId]; isE {
				list[k].CompanyName = companyMap[v.CompanyId]
			}
		}
	}
}

func ShowDetail(c *gin.Context) {
	var req backendSeries.SeriesDetailReq
	if err := c.ShouldBind(&req); err != nil {
		logger.Errorf("SeriesDetailReq ShouldBind err", err)
		service.ResponseQuickMsg(c, e.Failed, err.Error(), nil)
		return
	}
	if err := req.Validate(); err != nil {
		err = common.SubstrError(err)
		service.ResponseQuickMsg(c, e.Failed, err.Error(), nil)
		return
	}
	req.Lang = logic.GetLanguage(c)
	fmt.Println("req.Lang", req.Lang)
	resp, err := service.BackendSeriesProvider.SeriesDetail(c, &req)
	if err != nil {
		service.Error(c, e.Failed, err)
		return
	}
	if resp.ShelfState == 0 {
		service.Error(c, e.Error14SeriesNotShelf, nil, "该系列已下架")
		return
	}
	brandResp, _ := service.BrandProvider.BrandInfo(c, &shopbrand.BrandInforeq{
		BrandUid: resp.BrandId,
		Lang:     req.Lang,
	})
	var result = dto.ShowDetail{
		SeriesUid:  resp.SeriesUuid,
		SeriesName: resp.SeriesName,
		BrandId:    resp.BrandId,
		BrandName:  resp.BrandName,
		CoverImg:   resp.CoverImg,
		ActionCode: resp.ActionCode,
		Desc:       resp.Desc,
		Link:       resp.Link,
		ArtworkNum: len(resp.Artworks),
	}
	if brandResp != nil {
		result.BrandLogo = brandResp.Logo
	}

	setCompanyStaff(context.Background(), resp.Artworks)

	for _, v := range resp.Artworks {
		v := v
		tmp := dto.Artwork{
			ArtworkUid:  v.ArtworkUid,
			Tfnum:       v.Tfnum,
			ArtworkName: v.ArtworkName,
			HdPic:       v.HdPic,
			Ruler:       v.Ruler,
			Length:      v.Length,
			Width:       v.Width,
			ArtistName:  v.ArtistName,
			ArtistUid:   v.ArtistUid,
			Abstract:    v.Abstract,
			//Tnum:        v.Tnum,
			Price:       v.Price,
			Currency:    v.Currency,
			CompanyId:   v.CompanyId,
			CompanyName: v.CompanyName,
		}

		//查询是否被下单,只要在订单列表中（不管支付是否成功）都不能再次被购买
		var orderResp *fenghe.GetSeriesOrderListResp
		orderResp, err = service.FengheProvider.GetSeriesOrderList(c, &fenghe.GetSeriesOrderListRequest{
			Query:    &fenghe.SeriesOrderData{ArtworkUuid: v.ArtworkUid},
			Page:     1,
			PageSize: 1,
		})
		if err != nil {
			service.Error(c, e.Failed, nil, "查询失败")
			return
		}
		if orderResp.Total > 0 {
			tmp.SoldLock = 1 //上锁
		}
		result.ArtworkList = append(result.ArtworkList, tmp)
	}

	if resp.ActionCode == "auction" && resp.AuctionUuid != "" {
		result.Auction = &fenghe.AuctionRequest{}

		res, err := service.FengheProvider.DetailAuction(c, &fenghe.AuctionDetail{Uuid: resp.AuctionUuid})
		if err != nil {
			service.Error(c, e.Error, err)
			return
		}

		err = copier.Copy(&result.Auction, &res)
		if err != nil {
			service.Error(c, e.Error, err)
			return
		}
		fmt.Println(1111111111111, err)
		//获取用户号牌
		jwtInfoAny, exist := c.Get(e.JwtInfo)
		if !exist {
			service.Error(c, e.NotLogin, errors.New("请登录"), "请登录")
			return
		}
		jwtInfo := jwtInfoAny.(middleware.LoginInfo)
		fmt.Println("查询用户号牌, SessionNo:", resp.AuctionUuid, "\t phone", jwtInfo.TelNum)
		auctionUserInfo, errs := service.FengheProvider.GetAuctionSessionUserNoList(c, &fenghe.GetAuctionSessionUserNoListRequest{
			Query: &fenghe.AuctionSessionUserNoData{
				SessionNo: resp.AuctionUuid,
				Phone:     jwtInfo.TelNum,
			},
			Page:     1,
			PageSize: 1,
		})
		if errs == nil && auctionUserInfo.Total > 0 {
			result.AuctionUserNo = fmt.Sprintf("%03d", auctionUserInfo.List[0].AuctionUserNo)
		}
	}
	var artworkUidList []string
	for _, v := range result.ArtworkList {
		artworkUidList = append(artworkUidList, v.ArtworkUid)
	}
	if len(artworkUidList) > 0 {
		orderListRes, errs := service.FengheProvider.GetSeriesOrderList(c, &fenghe.GetSeriesOrderListRequest{
			Page:     1,
			PageSize: int64(len(artworkUidList)),
			Where:    fmt.Sprintf("artwork_uuid in ('%s')", strings.Join(artworkUidList, "','")),
		})
		if errs != nil {
			service.Error(c, e.Failed, errs)
			return
		}
		for i, v := range orderListRes.List {
			for ii, vv := range result.ArtworkList {
				if v.ArtworkUuid == vv.ArtworkUid {
					result.ArtworkList[ii].BuyerId = orderListRes.List[i].UserID
					result.ArtworkList[ii].OrderNo = orderListRes.List[i].OrderNo
					result.ArtworkList[ii].ShowStatus = int64(orderListRes.List[i].ShowStatus)
				}
			}
		}
	}

	service.Success(c, result)
	return
}

func SeriesDetail(c *gin.Context) {
	var req backendSeries.SeriesDetailReq
	if err := c.ShouldBind(&req); err != nil {
		logger.Errorf("SeriesDetailReq ShouldBind err", err)
		service.ResponseQuickMsg(c, e.Failed, err.Error(), nil)
		return
	}
	if err := req.Validate(); err != nil {
		err = common.SubstrError(err)
		service.ResponseQuickMsg(c, e.Failed, err.Error(), nil)
		return
	}

	service.Success(c, nil)
	return
}
