package handle

import (
	"github.com/fonchain_enterprise/client-auction/api/artwork_query"
	"github.com/fonchain_enterprise/client-auction/api/fenghe"
	"github.com/fonchain_enterprise/client-auction/pkg/common/utils"
	"github.com/fonchain_enterprise/client-auction/pkg/e"
	"github.com/fonchain_enterprise/client-auction/pkg/service"
	"github.com/fonchain_enterprise/client-auction/pkg/service/fenghe/dto"
	"github.com/gin-gonic/gin"
)

// 创建SeriesArtwork
func CreateSeriesArtwork(c *gin.Context) {
	var req fenghe.SeriesArtworkData
	if err := c.ShouldBindJSON(&req); err != nil {
		service.Error(c, e.InvalidParams, err, "无效参数")
		return
	}
	_, err := service.FengheProvider.CreateSeriesArtwork(c, &req)
	if err != nil {
		service.Error(c, e.Failed, err, "创建失败")
		return
	}
	go service.FengheProvider.InitSeriesProfileCache(c, &fenghe.CommonReq{})
	service.Success(c)
}

// 删除SeriesArtwork
func DeleteSeriesArtwork(c *gin.Context) {
	var req fenghe.DeleteSeriesArtworkRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		service.Error(c, e.InvalidParams, err, "无效参数")
		return
	}
	_, err := service.FengheProvider.DeleteSeriesArtwork(c, &req)
	if err != nil {
		service.Error(c, e.Failed, err, "删除失败")
		return
	}
	go service.FengheProvider.InitSeriesProfileCache(c, &fenghe.CommonReq{})
	service.Success(c)
}

// 更新SeriesArtwork
func UpdateSeriesArtwork(c *gin.Context) {
	var req fenghe.SeriesArtworkData
	if err := c.ShouldBindJSON(&req); err != nil {
		service.Error(c, e.InvalidParams, err, "无效参数")
		return
	}
	_, err := service.FengheProvider.UpdateSeriesArtwork(c, &req)
	if err != nil {
		service.Error(c, e.Failed, err, "更新失败")
		return
	}
	go service.FengheProvider.InitSeriesProfileCache(c, &fenghe.CommonReq{})
	service.Success(c)
}

// 使用id查询SeriesArtwork
func GetSeriesArtworkDetail(c *gin.Context) {
	var req fenghe.GetSeriesArtworkByIdRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		service.Error(c, e.InvalidParams, err, "无效参数")
		return
	}
	resp, err := service.FengheProvider.GetSeriesArtworkDetail(c, &req)
	if err != nil {
		service.Error(c, e.Failed, err, "查询失败")
		return
	}
	if req.Lang == "ZhCN" {
		artworkProfileRes, err := service.ArtworkQueryProvider.GetArtworkProfileList(c, &artwork_query.GetArtworkProfileListRequest{
			Query:    &artwork_query.ArtworkProfileData{Uuid: resp.ArtworkUid},
			Page:     1,
			PageSize: 1,
			Select:   []string{"uuid", "name", "hd_pic", "tfnum", "artist_name", "artist_uuid", "abstract", "width", "length", "ruler"},
		})
		if err != nil {
			service.Error(c, e.Failed, err, "查询失败")
			return
		}
		//中文与画作系统同步
		if artworkProfileRes.Total > 0 {
			resp.ArtworkName = artworkProfileRes.List[0].Name
			resp.HdPic = artworkProfileRes.List[0].HdPic
			resp.Ruler = artworkProfileRes.List[0].Ruler
			resp.Length = artworkProfileRes.List[0].Length
			resp.Width = artworkProfileRes.List[0].Width
			resp.ArtistName = artworkProfileRes.List[0].ArtistName
			resp.Abstract = artworkProfileRes.List[0].Abstract
		}
	}
	service.Success(c, resp)
}

// 批量查询SeriesArtwork
func GetSeriesArtworkList(c *gin.Context) {
	var req dto.GetSeriesArtworkListRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		service.Error(c, e.InvalidParams, err, "无效参数")
		return
	}
	var protoReq = fenghe.GetSeriesArtworkListRequest{Query: &fenghe.SeriesArtworkData{}}
	utils.RequestDataConvert(&req, &protoReq)
	resp, err := service.FengheProvider.GetSeriesArtworkList(c, &protoReq)
	if err != nil {
		service.Error(c, e.Failed, err, "查询失败")
		return
	}
	service.List(c, resp.List, service.OptionPage(resp.Page, resp.PageSize, resp.Total), service.OptionMsg("查询成功"))
}
