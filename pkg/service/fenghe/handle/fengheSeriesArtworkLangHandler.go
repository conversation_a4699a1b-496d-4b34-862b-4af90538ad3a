package handle

import (
	"errors"
	"fmt"
	"github.com/fonchain_enterprise/client-auction/api/artist"
	"github.com/fonchain_enterprise/client-auction/api/artwork_query"
	"github.com/fonchain_enterprise/client-auction/api/fenghe"
	"github.com/fonchain_enterprise/client-auction/pkg/common/utils"
	"github.com/fonchain_enterprise/client-auction/pkg/e"
	"github.com/fonchain_enterprise/client-auction/pkg/service"
	"github.com/fonchain_enterprise/client-auction/pkg/service/fenghe/dto"
	"github.com/gin-gonic/gin"
	"strings"
)

// 创建或更新SeriesArtworkLang
func SaveSeriesArtworkLang(c *gin.Context) {
	var req fenghe.SeriesArtworkLangData
	if err := c.ShouldBindJSON(&req); err != nil {
		service.Error(c, e.InvalidParams, err, "无效参数")
		return
	}
	if len([]rune(req.Abstract)) > 2000 {
		service.Error(c, e.InvalidParams, errors.New("简介内容过长"), "简介内容过长")
		return
	}
	_, err := service.FengheProvider.SaveSeriesArtworkLang(c, &req)
	if err != nil {
		service.Error(c, e.Failed, err, "创建失败")
		return
	}
	//go service.FengheProvider.InitSeriesProfileCache(c, &fenghe.CommonReq{})
	service.Success(c)
	return
}

// 删除SeriesArtworkLang
func DeleteSeriesArtworkLang(c *gin.Context) {
	var req fenghe.DeleteSeriesArtworkLangRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		service.Error(c, e.InvalidParams, err, "无效参数")
		return
	}
	_, err := service.FengheProvider.DeleteSeriesArtworkLang(c, &req)
	if err != nil {
		service.Error(c, e.Failed, err, "删除失败")
		return
	}
	//go service.FengheProvider.InitSeriesProfileCache(c, &fenghe.CommonReq{})
	service.Success(c)
}

// 更新SeriesArtworkLang
func UpdateSeriesArtworkLang(c *gin.Context) {
	var req fenghe.SeriesArtworkLangData
	if err := c.ShouldBindJSON(&req); err != nil {
		service.Error(c, e.InvalidParams, err, "无效参数")
		return
	}
	_, err := service.FengheProvider.UpdateSeriesArtworkLang(c, &req)
	if err != nil {
		service.Error(c, e.Failed, err, "更新失败")
		return
	}
	service.Success(c)
}

// 使用id查询SeriesArtworkLang
func GetSeriesArtworkLangDetail(c *gin.Context) {
	var req fenghe.GetSeriesArtworkLangByIdRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		service.Error(c, e.InvalidParams, err, "无效参数")
		return
	}
	resp, err := service.FengheProvider.GetSeriesArtworkLangDetail(c, &req)
	if err != nil {
		service.Error(c, e.Failed, err, "查询失败")
		return
	}
	service.Success(c, resp)
}

// 批量查询SeriesArtworkLang
func GetSeriesArtworkLangList(c *gin.Context) {
	var req dto.GetSeriesArtworkLangListRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		service.Error(c, e.InvalidParams, err, "无效参数")
		return
	}
	var protoReq = fenghe.GetSeriesArtworkLangListRequest{Query: &fenghe.SeriesArtworkLangData{}}
	utils.RequestDataConvert(&req, &protoReq)
	resp, err := service.FengheProvider.GetSeriesArtworkLangList(c, &protoReq)
	if err != nil {
		service.Error(c, e.Failed, err, "查询失败")
		return
	}
	service.List(c, resp.List, service.OptionPage(resp.Page, resp.PageSize, resp.Total), service.OptionMsg("查询成功"))
}

func GetArtworkListInArtworkService(c *gin.Context) {
	var req dto.GetArtworkListInArtworkServiceReq
	if err := c.ShouldBindJSON(&req); err != nil {
		service.Error(c, e.InvalidParams, err, "无效参数")
		return
	}
	if req.ActionCode == "" {
		service.Error(c, e.InvalidParams, errors.New("请填写画展类型"), "请填写画展类型")
		return
	}
	if req.Tnum != "" {
		artistResp, err := service.ArtistProvider.GetArtistProfileList(c, &artist.GetArtistProfileListRequest{
			Query: &artist.ArtistProfileData{
				Tnum: req.Tnum,
			},
			Page:     1,
			PageSize: 1,
		})
		if err != nil {
			service.Error(c, e.Failed, err, "查询失败")
			return
		}
		if artistResp.Total == 0 {
			service.List(c, []any{}, service.OptionPage(1, 1, 0), service.OptionMsg("查询成功"))
			return
		}
		req.ArtistUid = artistResp.List[0].Uid
	}
	if req.Page == 0 {
		req.Page = 1
	}
	if req.PageSize == 0 {
		req.PageSize = 50
	}
	//pageSize := int64(50)
	//if req.Name != "" || req.Tnum != "" || req.ArtistName != "" || req.Tfnum != "" {
	//	pageSize = int64(-1)
	//}
	artworkResp, err := service.ArtworkQueryProvider.GetArtworkProfileList(c, &artwork_query.GetArtworkProfileListRequest{
		Query: &artwork_query.ArtworkProfileData{
			Name:       req.Name,
			ArtistName: req.ArtistName,
			Tfnum:      req.Tfnum,
			ArtistUuid: req.ArtistUid,
		},
		Page:     req.Page,
		PageSize: req.PageSize,
		Where:    "in_source in (1,3) AND artwork_type in (4) AND create_source BETWEEN 1 AND 5",
		Order:    "id desc",
		Select:   []string{"id", "hd_pic", "length", "width", "ruler", "name", "artist_name", "abstract", "tfnum", "uuid", "artist_uuid"},
	})
	if err != nil {
		service.Error(c, e.Failed, err, "查询失败")
		return
	}
	var omitArtworkUidList []string
	switch req.ActionCode {
	case "show":
		//seriesArtworkList, err = service.FengheProvider.GetSeriesArtworkList(c, &fenghe.GetSeriesArtworkListRequest{
		//	ActionCode: "auction",
		//	Page:       1,
		//	PageSize:   -1,
		//})
		auctionArtworkList, errs := service.FengheProvider.ListAuctionArtwork(c, &fenghe.AuctionArtworkList{
			Page:     1,
			PageSize: 999999,
		})
		if errs != nil {
			service.Error(c, e.Failed, errs, "查询失败")
			return
		}
		for _, v := range auctionArtworkList.Data {
			v := v
			omitArtworkUidList = append(omitArtworkUidList, v.ArtworkUuid)
		}
	case "auction":
		seriesArtworkList, errs := service.FengheProvider.GetSeriesArtworkList(c, &fenghe.GetSeriesArtworkListRequest{
			ActionCode: "show",
			Page:       1,
			PageSize:   -1,
		})
		if errs != nil {
			service.Error(c, e.Failed, errs, "查询失败")
			return
		}
		for _, v := range seriesArtworkList.List {
			v := v
			omitArtworkUidList = append(omitArtworkUidList, v.ArtworkUid)
		}
	}
	fmt.Println("omitArtworkUidList", omitArtworkUidList)
	var artistUidList []string
	for _, v := range artworkResp.List {
		v := v
		artistUidList = append(artistUidList, v.ArtistUuid)
	}
	artistUidList = utils.Unique(artistUidList)
	artistListResp, err := service.ArtistProvider.GetArtistProfileList(c, &artist.GetArtistProfileListRequest{
		Page:     1,
		PageSize: int64(len(artistUidList)),
		Where:    fmt.Sprintf("uid in ('%v')", strings.Join(artistUidList, "','")),
	})
	//返回数据
	dataList := []map[string]any{}
	for _, v := range artworkResp.List {
		needOmit := false
		for _, omitArtworkUid := range omitArtworkUidList {
			if omitArtworkUid == v.Uuid {
				needOmit = true
				break
			}
		}
		var artistTnum string
		for _, artsitInfo := range artistListResp.List {
			if v.ArtistUuid == artsitInfo.Uid {
				artistTnum = artsitInfo.Tnum
				break
			}
		}
		//if needOmit {
		//	continue
		//}
		data := make(map[string]any)
		v := v
		data["id"] = v.Id
		data["hdPic"] = v.HdPic
		data["length"] = v.Length
		data["width"] = v.Width
		data["ruler"] = v.Ruler
		data["name"] = v.Name
		data["artistName"] = v.ArtistName
		data["abstract"] = v.Abstract
		data["tfnum"] = v.Tfnum
		data["uuid"] = v.Uuid
		data["omit"] = needOmit
		data["tnum"] = artistTnum
		dataList = append(dataList, data)
	}
	service.List(c, dataList, service.OptionPage(req.Page, req.PageSize, artworkResp.Total), service.OptionMsg("查询成功"))
}
