// Package handle -----------------------------
// @file      : payment.go
// <AUTHOR> JJXu
// @contact   : <EMAIL>
// @time      : 2025/4/15 18:16
// -------------------------------------------
package handle

import (
	"context"
	"errors"
	"fmt"
	"github.com/fonchain_enterprise/client-auction/api/account"
	"github.com/fonchain_enterprise/client-auction/api/fenghe"
	backendSeries "github.com/fonchain_enterprise/client-auction/api/series"
	"github.com/fonchain_enterprise/client-auction/cmd/config"
	"github.com/fonchain_enterprise/client-auction/pkg/common/cache"
	"github.com/fonchain_enterprise/client-auction/pkg/e"
	"github.com/fonchain_enterprise/client-auction/pkg/logic"
	"github.com/fonchain_enterprise/client-auction/pkg/middleware"
	"github.com/fonchain_enterprise/client-auction/pkg/model"
	"github.com/fonchain_enterprise/client-auction/pkg/model/login"
	"github.com/fonchain_enterprise/client-auction/pkg/model/query"
	"github.com/fonchain_enterprise/client-auction/pkg/service"
	accountServ "github.com/fonchain_enterprise/client-auction/pkg/service/account"
	"github.com/fonchain_enterprise/client-auction/pkg/service/fenghe/dto"
	"github.com/fonchain_enterprise/client-auction/pkg/service/order/payment"
	"github.com/fonchain_enterprise/client-auction/pkg/utils/secret/aes"
	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"github.com/wechatpay-apiv3/wechatpay-go/services/payments/app"
	"log"
	"net/url"
	"strconv"
	"strings"
	"sync"
	"time"
)

func SeriesArtworkBuy(c *gin.Context) {
	var req dto.CreateOrderRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		service.Error(c, e.Failed, err)
		return
	}

}

func CreateOrder(c *gin.Context) {
	var req dto.CreateOrderRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		service.Error(c, e.Failed, err)
		return
	}
	//查询系列信息
	seriesInfo, err := service.BackendSeriesProvider.SeriesDetail(c, &backendSeries.SeriesDetailReq{
		SeriesUuid: req.SeriesUid,
	})
	if err != nil {
		service.Error(c, e.Failed, err)
		return
	}
	seriesArtworkData := &backendSeries.SeriesArtworkData{}
	var (
		userInfo              middleware.LoginInfo
		language              = logic.GetLanguage(c)
		notifyUrl, _          = url.JoinPath(config.AppConfig.Service.GetFullHost(), "/api/v1/fenghe/payment/hook/", req.PayPlat.String())
		orderNo               string
		tradeNo               = "" //我方交易流水号,这里根据支付方式自定义前缀
		thirdPartyTradeNo     = "" //第三方交易流水号
		thirdPartyTradeResult any  //调用第三方支付后的返回值
		productName           = ""
		productDesc           = "这是一个测试商品"
		productPriceTotal     = "" //记录的总价
		productCurrency       = "" //记录的货币单位
		auctionArtworkUuid    = "" //拍卖参数
		auctionBuyUuid        = "" //拍卖参数
		buyUserId             uint32
		buyUserName           = "未知用户"
		buyUserTelNum         = "无"
	)
	fmt.Println("notifyUrl:", notifyUrl)
	//--------------------------------------获取购买人信息
	{
		userInfoAny, exist := c.Get("jwtInfo")
		if !exist {
			service.Error(c, e.NotLogin, errors.New("请登录"))
			return
		}
		userInfo = userInfoAny.(middleware.LoginInfo)
		buyUserId = uint32(userInfo.ID)
		buyUserName = userInfo.NickName
		buyUserTelNum = userInfo.TelNum
	}
	fmt.Println("orderNo:", orderNo)
	err = createOrderCacheIns.Check(req.SeriesUid, req.ArtworkUid, int64(buyUserId))
	if err != nil {
		service.Error(c, e.Failed, err)
		return
	}
	defer createOrderCacheIns.Del(req.SeriesUid, req.ArtworkUid)
	//--------------------------------------1.获取画作信息
	{
		seriesInfo, err = service.BackendSeriesProvider.SeriesDetail(c, &backendSeries.SeriesDetailReq{
			SeriesUuid: req.SeriesUid,
			Lang:       language,
		})
		if err != nil {
			service.Error(c, e.Failed, err)
			return
		}
		switch seriesInfo.ActionCode {
		default:
			service.Error(c, e.Failed, errors.New("此系列商品不支持支付"))
			return
		case "auction":
			if req.BuyUid == "" {
				service.Error(c, e.Failed, errors.New("请填写拍卖UID"))
				return
			}
			//找到拍卖商品，获取价格
			buyInfo, err1 := service.FengheProvider.DetailAuctionBuy(c, &fenghe.AuctionBuyDetail{
				Uuid: req.BuyUid,
			})
			if err1 != nil {
				service.Error(c, e.Failed, err1)
				return
			}
			auctionArtwork, err2 := service.FengheProvider.DetailAuctionArtwork(c, &fenghe.AuctionArtworkDetail{Uuid: buyInfo.AuctionArtworkUuid})
			if err2 != nil {
				service.Error(c, e.Failed, err2)
				return
			}
			if buyInfo.Artwork == nil {
				service.Error(c, e.Failed, errors.New("找不到画作"))
				return
			}
			auctionArtworkUuid = buyInfo.AuctionArtworkUuid
			auctionBuyUuid = buyInfo.Uuid
			productName = fmt.Sprintf("Lot%d_%d", auctionArtwork.Index, time.Now().Unix())
			productDesc = fmt.Sprintf("%s", buyInfo.Artwork.Name)
			productCurrency = buyInfo.BuyCurrency
			productPriceTotal = buyInfo.BuyMoney
		case "show":
			//直接从系列中获取价格
			found := false
			for k, v := range seriesInfo.Artworks {
				if v.ArtworkUid == req.ArtworkUid {
					productPriceTotal = v.Price
					productCurrency = v.Currency
					productName = fmt.Sprintf("%s_%d", v.ArtworkName, time.Now().Unix())
					productDesc = fmt.Sprintf("%s", v.ArtworkName)
					found = true
					seriesArtworkData = seriesInfo.Artworks[k]
					break
				}
			}
			if !found {
				service.Error(c, e.Failed, errors.New("找不到画作"))
				return
			}
		}
		///校验
		if productCurrency == "" || strings.ToLower(productCurrency) == "rmb" {
			productCurrency = "CNY"
		}
		if req.Currency == "" || strings.ToLower(req.Currency) == "rmb" {
			req.Currency = "CNY"
		} else if strings.ToLower(req.Currency) != strings.ToLower(productCurrency) {
			service.Error(c, e.Failed, fmt.Errorf("当前币种不支持支付,需要%s但是获取到了%s", productCurrency, req.Currency))
			return
		}
		reqPriceFloat64, _ := strconv.ParseFloat(req.Price, 64)
		productPriceTotalFloat64, _ := strconv.ParseFloat(productPriceTotal, 64)
		if reqPriceFloat64 <= 0 {
			service.Error(c, e.Failed, errors.New("支付价格不能为0"))
			return
		}
		if reqPriceFloat64 > productPriceTotalFloat64 {
			service.Error(c, e.Failed, errors.New("支付价格不能大于画作总价"))
			return
		}
	}
	//-------------------------------------2. 获取或创建订单
	{
		var seriesOrderList *fenghe.GetSeriesOrderListResp
		seriesOrderList, err = service.FengheProvider.GetSeriesOrderList(c, &fenghe.GetSeriesOrderListRequest{
			Query: &fenghe.SeriesOrderData{
				SeriesUid:   req.SeriesUid,
				ArtworkUuid: req.ArtworkUid,
			},
			PageSize: 1,
			Page:     1,
		})
		if err != nil {
			service.Error(c, e.Failed, errors.New("订单创建失败1"), "订单创建失败")
			return
		}
		if seriesOrderList.Total == 0 {
			var seriesOrder *fenghe.CreateSeriesOrderResp
			seriesOrder, err = service.FengheProvider.CreateSeriesOrder(c, &fenghe.SeriesOrderData{
				OrderNo:        orderNo,
				SeriesUid:      req.SeriesUid,
				ActionCode:     seriesInfo.ActionCode,
				ArtworkUuid:    req.ArtworkUid,
				AuctionBuyUuid: req.BuyUid,
				Amount:         productPriceTotal,
				Currency:       productCurrency,
				LeftPrice:      productPriceTotal,
				PaidPrice:      "0",
				Status:         0,
				UserID:         int32(buyUserId),
				UserName:       buyUserName,
				TelNum:         buyUserTelNum,
				CompanyId:      seriesArtworkData.CompanyId,
				StaffId:        userInfo.UserExtend.RecommendStaffId,
			})
			if err != nil {
				service.Error(c, e.Failed, errors.New("订单创建失败2"), "订单创建失败")
				return
			}
			orderNo = seriesOrder.Data.OrderNo
		} else {
			if seriesOrderList.List[0].UserID != int32(buyUserId) {
				if seriesOrderList.List[0].Status == 2 || seriesOrderList.List[0].Status == 4 {
					service.Error(c, e.Failed, errors.New("商品已被他人购买"))
					return
				} else {
					service.Error(c, e.Failed, errors.New("商品已被他人下单"))
					return
				}
			} else if seriesOrderList.List[0].Status == 2 {
				service.Error(c, e.Failed, errors.New("商品已完成支付"))
				return
			}
			orderNo = seriesOrderList.List[0].OrderNo
		}
	}
	//--------------------------------------3.调用第三方支付
	{
		paymentService, errs := payment.NewPayment(req.PayPlat, &config.AppConfig.AllPaymentConfig)
		if errs != nil {
			service.Error(c, e.Failed, errs)
			return
		}
		switch req.PayPlat {
		case payment.AliPay:
			tradeNo = "alipay" + strings.Replace(uuid.NewString(), "-", "", 4)[:26]
		case payment.WePay:
			tradeNo = "wepay" + strings.Replace(uuid.NewString(), "-", "", 4)[:27]
		case payment.Stripe:
			tradeNo = "strpay" + strings.Replace(uuid.NewString(), "-", "", 4)[:26]
		case payment.Antom:
			tradeNo = "antom" + strings.Replace(uuid.NewString(), "-", "", 4)[:26]
		}
		//如果为2时，支付渠道会被记录为其它
		if req.V == 2 {
			tradeNo = "other" + strings.Replace(uuid.NewString(), "-", "", 4)[:26]
		}
		switch req.PayMethod {
		default:
			service.Error(c, e.Failed, errors.New("不支持的支付方式"))
			return
		case payment.App:
			thirdPartyTradeResult, thirdPartyTradeNo, err = paymentService.CreateTradeAppPay(&payment.CommonTradeInfo{
				ReturnUrl:    "",
				NotifyUrl:    notifyUrl,
				Subject:      productName,
				Desc:         productDesc,
				OutTradeNo:   tradeNo,
				Amount:       req.Price,
				Currency:     req.Currency,
				ExpiredAfter: 30 * time.Minute,
				CallbackData: nil,
				Goods:        nil,
			})
			if err != nil {
				service.Error(c, e.Failed, err)
				return
			}
		case payment.H5:
			//if config.AppConfig.Service.AppMode == "prod" {
			//	service.Error(c, e.Failed, errors.New("暂不支持此支付方式，请更换！"))
			//	return
			//}
			if req.ReturnUrl == "" {
				req.ReturnUrl = "http://172.16.100.99:9029/#/pages/pay/index"
			}
			req.ReturnUrl += "?tradeNo=" + url.PathEscape(tradeNo)
			fmt.Println("ReturnUrl", req.ReturnUrl)
			thirdPartyTradeResult, thirdPartyTradeNo, err = paymentService.CreateTradeWebPay(&payment.CommonTradeInfo{
				ReturnUrl:    req.ReturnUrl,
				NotifyUrl:    notifyUrl,
				Subject:      productName,
				Desc:         productDesc,
				OutTradeNo:   tradeNo,
				Amount:       req.Price,
				Currency:     req.Currency,
				ExpiredAfter: 30 * time.Minute,
				CallbackData: nil,
				Goods:        nil,
			})
			if err != nil {
				service.Error(c, e.Failed, err)
				return
			}
		}
	}
	fmt.Println("get third party trade no", thirdPartyTradeNo)
	//--------------------------------------4.创建支付记录
	paymentRecord := &fenghe.AuctionPayRequest{
		SeriesUid:          req.SeriesUid,
		ArtworkUuid:        req.ArtworkUid,
		AuctionArtworkUuid: auctionArtworkUuid,
		AuctionBuyUuid:     auctionBuyUuid,
		Money:              req.Price,
		UserID:             buyUserId,
		UserName:           buyUserName,
		Uuid:               tradeNo,
		Currency:           req.Currency,
		OrderNo:            orderNo,           //订单号
		PaySessionId:       thirdPartyTradeNo, //交易流水号
	}
	_, err = service.FengheProvider.CreateAuctionPay(c, paymentRecord)
	if err != nil {
		service.Error(c, e.Failed, err)
		return
	}
	fmt.Printf("result:%+v\n", thirdPartyTradeResult)
	//--------------------------------------4.整理数据并返回
	if req.PayPlat == payment.WePay {
		//适配前端基座的返回值
		wxResult := thirdPartyTradeResult.(*app.PrepayWithRequestPaymentResponse)
		newResult := map[string]any{
			"Appid":     config.AppConfig.WePay.AppId,
			"TimeStamp": wxResult.TimeStamp,
			"Package":   wxResult.Package,
			"NonceStr":  wxResult.NonceStr,
			"PaySign":   wxResult.Sign,
			"PrepayId":  wxResult.PrepayId,
			"PartnerId": wxResult.PartnerId,
		}
		service.Success(c, map[string]any{
			"orderNo": orderNo,
			"tradeNo": tradeNo,
			"result":  newResult,
		})
	} else {
		service.Success(c, map[string]any{
			"orderNo": orderNo,
			"tradeNo": tradeNo,
			"result":  thirdPartyTradeResult,
		})
	}
}

func PaymentInfo(c *gin.Context) {
	var req dto.PaymentInfoReq
	if err := c.ShouldBindJSON(&req); err != nil {
		service.Error(c, e.Failed, err)
		return
	}
	if req.TradeNo == "" {
		service.Error(c, e.Failed, errors.New("交易流水号不能为空"))
		return
	}
	paymentInfo, err := service.FengheProvider.DetailAuctionPay(c, &fenghe.AuctionPayDetail{
		Uuid: req.TradeNo,
	})
	if err != nil {
		service.Error(c, e.Failed, err)
		return
	}
	service.Success(c, map[string]any{
		"orderNo":      paymentInfo.OrderNo,
		"tradeNo":      paymentInfo.Uuid,
		"money":        paymentInfo.Money,
		"status":       paymentInfo.Status,
		"createdAt":    paymentInfo.CreatedAt,
		"updatedAt":    paymentInfo.UpdatedAt,
		"currency":     paymentInfo.Currency,
		"seriesUid":    paymentInfo.SeriesUid,
		"paySessionId": paymentInfo.PaySessionId,
	})
}

func PaymentWebHook(c *gin.Context) {
	fmt.Println("PaymentWebHook------------------1")
	platform := c.Param("platform")
	fmt.Println("platform", platform)
	var psv payment.IThirdPartyPayment
	var err error
	switch platform {
	case payment.AliPay.String():
		psv, err = payment.NewPayment(payment.AliPay, &config.AppConfig.AllPaymentConfig)
	case payment.WePay.String():
		psv, err = payment.NewPayment(payment.WePay, &config.AppConfig.AllPaymentConfig)
	case payment.Stripe.String():
		psv, err = payment.NewPayment(payment.Stripe, &config.AppConfig.AllPaymentConfig)
	case payment.Antom.String():
		psv, err = payment.NewPayment(payment.Antom, &config.AppConfig.AllPaymentConfig)
	default:
		fmt.Println("不支持的支付平台", platform)
		return
	}
	fmt.Println("PaymentWebHook------------------2")
	if err != nil {
		log.Println("初始化支付服务失败", err)
		return
	}
	fmt.Println("PaymentWebHook------------------3")
	result, errs := psv.ParseNotification(c.Request)
	if errs != nil {
		fmt.Println("ParseNotification err", errs)
		return
	}
	fmt.Println()
	if result.TradeStatus > 1 {
		updatePaymentReq := &fenghe.UpdateTradePaymentReq{
			TradeNo:      result.OutTradeNo,
			TradeStatus:  result.TradeStatus,
			CallbackBody: result.NotifyData,
			Amount:       result.Amount,
		}
		fmt.Printf("updatePaymentReq:%+v\n", updatePaymentReq)
		_, err = service.FengheProvider.UpdateTradePayment(c, updatePaymentReq)
		if err != nil {
			fmt.Println("更新支付状态失败", err)
		} else {
			fmt.Println("更新支付状态成功", result.TradeStatus)
		}
	} else {
		fmt.Println("忽略状态")
	}
	fmt.Printf("result:%+v\n", result)

	PublishWs(result.OutTradeNo)
	//结束回调，否则支付宝还是会不断请求
	service.Success(c)
}

func PublishWs(uuid string) {

	res, err := service.FengheProvider.DetailAuctionBuy(context.Background(), &fenghe.AuctionBuyDetail{Uuid: uuid})
	if err == nil {
		if res != nil && res.AuctionArtworkInfo != nil && res.AuctionArtworkInfo.AuctionUuid != "" {
			_ = logic.SendFansTipAndSuccessPay(res.AuctionArtworkInfo.AuctionUuid, res.AuctionArtworkInfo.AuctionUuid, res.UserID)
		}
	}

	return
}

func OrderRecords(c *gin.Context) {
	var req dto.OrderRecordsReq
	if err := c.ShouldBindJSON(&req); err != nil {
		service.Error(c, e.InvalidParams, err, "无效参数")
		return
	}

	userInfoAny, exist := c.Get("jwtInfo")
	if !exist {
		service.Error(c, e.NotLogin, errors.New("未登录"))
		return
	}
	userInfo := userInfoAny.(middleware.LoginInfo)
	orderListResp, err := service.FengheProvider.GetSeriesOrderList(c, &fenghe.GetSeriesOrderListRequest{
		Query:    &fenghe.SeriesOrderData{UserID: int32(userInfo.ID), ActionCode: req.ActionCode, ReceivingStatus: req.ReceivingStatus},
		Page:     req.Page,
		PageSize: req.PageSize,
	})
	if err != nil {
		service.Error(c, e.Failed, err)
		return
	}
	var artworkUidList []string
	for _, v := range orderListResp.List {
		v := v
		artworkUidList = append(artworkUidList, v.ArtworkUuid)
	}
	//海外没有画作微服务 改为耿阳的查询函数
	//artworkListResp, err := service.ArtworkQueryProvider.GetArtworkProfileList(c, &artwork_query.GetArtworkProfileListRequest{
	//	Page:     1,
	//	PageSize: int64(len(artworkUidList)),
	//	Where:    fmt.Sprintf("uuid in ('%s')", strings.Join(artworkUidList, "','")),
	//	Select:   []string{"uuid", "name", "hd_pic", "tfnum", "artist_name", "artist_uuid", "abstract", "width", "length", "ruler"},
	//})
	//if err != nil {
	//	service.Error(c, e.Failed, err)
	//	return
	//}
	artworkListResp, err := service.FengheProvider.ListArtwork(c, &fenghe.ArtworkList{
		Page:     1,
		PageSize: uint64(len(artworkUidList)),
		UidIn:    artworkUidList,
	})
	if err != nil {
		service.Error(c, e.Failed, err)
		return
	}
	var data []dto.OrderRecordType
	for _, v := range orderListResp.List {
		v := v
		tmp := dto.OrderRecordType{
			OrderNo:         v.OrderNo,
			Amount:          v.Amount,
			Currency:        v.Currency,
			Status:          v.Status,
			ArtworkUid:      v.ArtworkUuid,
			PayTime:         v.UpdatedAt,
			SeriesUid:       v.SeriesUid,
			ReceivingStatus: v.ReceivingStatus,
			ShowStatus:      v.ShowStatus,
			CompanyId:       v.CompanyId,
			StaffId:         v.StaffId,
		}
		for _, vv := range artworkListResp.Data {
			if v.ArtworkUuid == vv.Uuid {
				vv := vv
				tmp.ArtworkName = vv.Name
				tmp.HdPic = vv.HdPic
				tmp.Tfnum = vv.Tfnum
				tmp.ArtistName = vv.ArtistName
				//tmp.ArtistUid = vv.ArtistUuid
				tmp.Abstract = vv.Abstract
				tmp.Width = int32(vv.Width)
				tmp.Length = int32(vv.Length)
				tmp.Ruler = int32(vv.Ruler)
				break
			}
		}
		data = append(data, tmp)
	}
	service.List(c, data, service.OptionPage(req.Page, req.PageSize, orderListResp.Total), service.OptionMsg("查询成功"))
}

func OrderRecordInfo(c *gin.Context) {
	var req dto.OrderRecordInfoReq
	if err := c.ShouldBindJSON(&req); err != nil {
		service.Error(c, e.InvalidParams, err, "无效参数")
		return
	}
	orderDetail, err := service.FengheProvider.GetSeriesOrderDetail(c, &fenghe.GetSeriesOrderByIdRequest{
		OrderNo: req.OrderNo,
	})
	if err != nil {
		service.Error(c, e.Failed, err)
		return
	}
	fmt.Println("orderDetail.ArtworkUuid:", orderDetail.ArtworkUuid)
	//artworkListResp, err := service.ArtworkQueryProvider.GetArtworkProfileList(c, &artwork_query.GetArtworkProfileListRequest{
	//	Query:    &artwork_query.ArtworkProfileData{Uuid: orderDetail.ArtworkUuid},
	//	Page:     1,
	//	PageSize: 1,
	//	Select:   []string{"uuid", "name", "hd_pic", "tfnum", "artist_name", "artist_uuid", "abstract", "width", "length", "ruler"},
	//})
	//if err != nil {
	//	service.Error(c, e.Failed, err)
	//	return
	//}
	artworkDetail, err := service.FengheProvider.DetailArtwork(c, &fenghe.ArtworkDetail{
		Uuid: orderDetail.ArtworkUuid,
	})
	if err != nil {
		service.Error(c, e.Failed, err)
		return
	}
	data := dto.OrderRecordType{
		OrderNo:         orderDetail.OrderNo,
		Amount:          orderDetail.Amount,
		LeftPrice:       orderDetail.LeftPrice,
		PaidPrice:       orderDetail.PaidPrice,
		Currency:        orderDetail.Currency,
		Status:          orderDetail.Status,
		PayTime:         orderDetail.UpdatedAt,
		ArtworkUid:      orderDetail.ArtworkUuid,
		ReceivingStatus: orderDetail.ReceivingStatus,
		Tfnum:           artworkDetail.Tfnum,
		ArtworkName:     artworkDetail.Name,
		HdPic:           artworkDetail.HdPic,
		ArtistName:      artworkDetail.ArtistName,
		//ArtistUid:   artworkInfo.ArtistUuid,
		Abstract:   artworkDetail.Abstract,
		Width:      int32(artworkDetail.Width),
		Length:     int32(artworkDetail.Length),
		Ruler:      int32(artworkDetail.Ruler),
		SeriesUid:  orderDetail.SeriesUid,
		CompanyId:  orderDetail.CompanyId,
		StaffId:    orderDetail.StaffId,
		ShowStatus: orderDetail.ShowStatus,
	}
	service.Success(c, data)
}

var (
	mu      sync.Mutex
	counter int64
)

func generateOrderNo(actionCode string) string {
	mu.Lock()
	defer mu.Unlock()
	// 增加计数器
	cache.RedisClient.Get("orderNoCounter").Scan(&counter)
	counter++
	cache.RedisClient.Set("orderNoCounter", counter, 24*time.Hour)
	now := time.Now()
	switch actionCode {
	case "auction": //拍卖
		return fmt.Sprintf("AU%s%04d", now.Format(time.DateOnly), counter)
	case "show": //画展
		return fmt.Sprintf("AE%s%04d", now.Format(time.DateOnly), counter)
	default:
		return fmt.Sprintf("DE%s%04d", now.Format(time.DateOnly), counter)
	}
}

var createOrderCacheIns = &createOrderCacheControl{Mutex: &sync.Mutex{}}

type createOrderCacheControl struct {
	*sync.Mutex
}

func (c *createOrderCacheControl) Check(seriesUid string, artworkUid string, userId int64) error {
	c.Lock()
	defer c.Unlock()
	key := c.GenerateKey(seriesUid, artworkUid)
	paymentUserId, err := cache.RedisClient.Get(key).Int64()
	if err == nil {
		if paymentUserId == userId {
			return errors.New("支付中，请勿重复操作")
		} else {
			return errors.New("该商品正被其他人下单")
		}
	}
	if paymentUserId == 0 {
		cache.RedisClient.Set(key, userId, 2*time.Minute)
	}
	return nil
}
func (c *createOrderCacheControl) Del(seriesUid string, artworkUid string) {
	c.Lock()
	defer c.Unlock()
	err := cache.RedisClient.Del(c.GenerateKey(seriesUid, artworkUid)).Err()
	if err != nil {
		fmt.Println("删除缓存失败", err)
		return
	}

}
func (c *createOrderCacheControl) GenerateKey(seriesUid string, artworkUid string) string {
	return "createOrder" + seriesUid + ":" + artworkUid
}

func RefundOrder(c *gin.Context) {
	var req dto.RefundOrderReq
	if err := c.ShouldBindJSON(&req); err != nil {
		service.Error(c, e.InvalidParams, err)
		return
	}
	var psv payment.IThirdPartyPayment
	var err error
	switch req.PayPlat {
	case payment.AliPay:
		psv, err = payment.NewPayment(payment.AliPay, &config.AppConfig.AllPaymentConfig)
	case payment.WePay:
		psv, err = payment.NewPayment(payment.WePay, &config.AppConfig.AllPaymentConfig)
	default:
		service.Error(c, e.Failed, errors.New("此平台不支持付款"))
		return
	}
	if err != nil {
		service.Error(c, e.Failed, err)
		return
	}
	refundResult, _, err := psv.TradeRefund(&payment.TradeRefundInfo{
		OutTradeNo:   req.PayUid,
		RefundAmount: req.RefundAmount,
		PaidAmount:   req.PaidAmount,
		TradeNo:      req.TradeNo,
	})
	if err != nil {
		service.Error(c, e.Failed, err)
		return
	}
	service.Success(c, refundResult)

}

func CreateAuctionOrder(artworkUid, buyUid string, buyerId int64, buyerName, buyerTelNum string, priceTotal, currency string, auctionUid string) (err error) {
	var (
		seriesUid string
		c         = context.Background()
		orderNo   = generateOrderNo("auction")
	)
	seriesInfo, err := service.FengheProvider.GetSeriesProfileList(c, &fenghe.GetSeriesProfileListRequest{
		Page:       1,
		PageSize:   1,
		Where:      fmt.Sprintf("auction_uuid = '%s'", auctionUid),
		ActionCode: "auction",
	})
	if err != nil {
		return
	}
	if seriesInfo.Total == 0 {
		err = fmt.Errorf("订单创建失败，找不到系列，auction_uuid = %s", auctionUid)
		return
	}
	seriesUid = seriesInfo.List[0].SeriesUuid
	var seriesOrderList *fenghe.GetSeriesOrderListResp
	seriesOrderList, err = service.FengheProvider.GetSeriesOrderList(c, &fenghe.GetSeriesOrderListRequest{
		Query: &fenghe.SeriesOrderData{
			SeriesUid:   seriesUid,
			ArtworkUuid: artworkUid,
		},
		PageSize: 1,
		Page:     1,
	})
	if err != nil {
		err = errors.New("订单创建失败")
		return
	}
	if seriesOrderList.Total == 0 {
		var seriesOrder *fenghe.CreateSeriesOrderResp
		seriesOrder, err = service.FengheProvider.CreateSeriesOrder(c, &fenghe.SeriesOrderData{
			OrderNo:        orderNo,
			SeriesUid:      seriesUid,
			ActionCode:     "auction",
			ArtworkUuid:    artworkUid,
			AuctionBuyUuid: buyUid,
			Amount:         priceTotal,
			Currency:       currency,
			LeftPrice:      priceTotal,
			PaidPrice:      "0",
			Status:         0,
			UserID:         int32(buyerId),
			UserName:       buyerName,
			TelNum:         buyerTelNum,
		})
		if err != nil {
			err = errors.New("订单创建失败")
			return
		}
		orderNo = seriesOrder.Data.OrderNo
		fmt.Printf("orderNo no: %+v\n", orderNo)
	} else {
		fmt.Printf("series order Info: %+v\n", seriesOrderList.List[0])
		if seriesOrderList.List[0].UserID != int32(buyerId) {
			if seriesOrderList.List[0].Status == 2 || seriesOrderList.List[0].Status == 4 {
				err = errors.New("商品已被他人购买")
				return
			} else {
				err = errors.New("商品已被他人下单")
				return
			}
		} else if seriesOrderList.List[0].Status == 2 {
			err = errors.New("商品已完成支付")
			return
		}
		orderNo = seriesOrderList.List[0].OrderNo
	}
	return
}

func SendSeriesOrderReceiveSms(c *gin.Context) {
	var req dto.SendSeriesOrderReceiveSmsReq
	if err := c.ShouldBindJSON(&req); err != nil {
		service.Error(c, e.InvalidParams, err)
		return
	}
	if req.Phone == "" {
		userInfoAny, exist := c.Get(e.JwtInfo)
		if !exist || userInfoAny == nil {
			service.Error(c, e.NotLogin, errors.New("请登录"))
			return
		}
		switch userIns := userInfoAny.(type) {
		case middleware.LoginInfo:
			req.Phone = userIns.TelNum
		case model.LoginInfo:
			req.Phone = userIns.Phone
		case login.Info:
			req.Phone = userIns.TelNum
		default:
			fmt.Printf("无法解析用户信息：%v\n", userInfoAny)
			service.Error(c, e.Error, errors.New("无法解析用户信息"))
			return
		}
	}
	clientIp := c.ClientIP()
	err, code := accountServ.SendMsgFunc(c, "", clientIp, req.Phone, e.AuctionDomain, 0, 0, "")
	if err != nil {
		service.Error(c, code, err)
		return
	}
	service.Success(c)
}

// OrderContractDone 签署完毕接口
func OrderContractDone(c *gin.Context) {
	var req query.OrderContractQuery
	if err := c.ShouldBindJSON(&req); err != nil {
		service.Error(c, e.InvalidParams, err, "无效参数")
		return
	}

	r := fenghe.SeriesOrderData{ID: req.ID, OrderNo: req.OrderNo, ShowStatus: uint32(e.ShowStatusNeedReap)}
	res, err := service.FengheProvider.UpdateSeriesOrder(c, &r)
	if err != nil {
		service.Error(c, e.Error, err)
		return
	}

	service.Success(c, res)
	return
}

func OrderDone(c *gin.Context) {
	var req fenghe.SeriesOrderData
	if err := c.ShouldBindJSON(&req); err != nil {
		service.Error(c, e.InvalidParams, err, "无效参数")
		return
	}

	r := fenghe.SeriesOrderData{ID: req.ID, OrderNo: req.OrderNo, ShowStatus: uint32(e.ShowStatusDone)}
	res, err := service.FengheProvider.UpdateSeriesOrder(c, &r)
	if err != nil {
		service.Error(c, e.Error, err)
		return
	}

	service.Success(c, res)
	return
}

func OrderQr(c *gin.Context) {
	var req fenghe.GetSeriesOrderByIdRequest
	if err := c.ShouldBindJSON(&req); err != nil {

	}

	info, err := service.FengheProvider.GetSeriesOrderDetail(c, &req)

	if err != nil {
		service.Error(c, e.InvalidParams, err)
		return
	}

	got, err := aes.CreateSecretDemoPay(uint(info.ID), info.OrderNo, info.ArtworkUuid) //生成一个新的密钥

	if err != nil {
		service.Error(c, e.InvalidParams, err)
		return
	}

	//生成二维码
	res := make(map[string]string)
	res["url"] = fmt.Sprintf("https://common.szjixun.cn/api/image/qr/url?url=%s&size=500&level=3", "sellerPay||"+got)

	service.Success(c, res)
	return
}

func ConfirmReceiveOrder(c *gin.Context) {
	var req dto.ConfirmReceiveOrderReq
	if err := c.ShouldBindJSON(&req); err != nil {
		service.Error(c, e.InvalidParams, err)
		return
	}
	if req.TelNum == "" {
		userInfoAny, exist := c.Get(e.JwtInfo)
		if !exist || userInfoAny == nil {
			service.Error(c, e.NotLogin, errors.New("请登录"))
			return
		}
		switch userIns := userInfoAny.(type) {
		case middleware.LoginInfo:
			req.TelNum = userIns.TelNum
		case model.LoginInfo:
			req.TelNum = userIns.Phone
		case login.Info:
			req.TelNum = userIns.TelNum
		default:
			fmt.Printf("无法解析用户信息：%v\n", userInfoAny)
			service.Error(c, e.Error, errors.New("无法解析用户信息"))
			return
		}
	}
	_, err := service.AccountProvider.ValidateCode(c, &account.ValidateCodeReq{
		Code:   req.Code,
		Domain: e.AuctionDomain,
		TelNum: req.TelNum,
	})
	if err != nil {
		service.Error(c, e.Failed, nil, "验证码不正确，请重试")
		return
	}
	updateSeriesReq := &fenghe.SeriesOrderData{
		OrderNo:         req.OrderNo,
		ReceivingStatus: 2,
		ReceivingTime:   time.Now().Format(time.DateTime),
	}
	_, err = service.FengheProvider.UpdateSeriesOrder(c, updateSeriesReq)
	if err != nil {
		service.Error(c, e.Failed, err)
		return
	}
	recordStatusDone(c, updateSeriesReq)

	service.Success(c)
	return
}

// 销售提交支付信息
func recordStatusDone(c *gin.Context, req *fenghe.SeriesOrderData) {
	realReq := &fenghe.RecordSeriesOrderRequest{
		OrderNo:       req.OrderNo,
		ContractFiles: []string{},
		Files:         []string{},
		ShowStatus:    uint32(e.ShowStatusDone),
	}

	service.FengheProvider.RecordSeriesOrder(c, realReq)
	return
}
