package handle

import (
	"encoding/json"
	"fmt"
	"github.com/fonchain_enterprise/client-auction/api/account"
	"github.com/fonchain_enterprise/client-auction/api/artwork_query"
	"github.com/fonchain_enterprise/client-auction/api/department"
	"github.com/fonchain_enterprise/client-auction/api/fenghe"
	"github.com/fonchain_enterprise/client-auction/api/order"
	"github.com/fonchain_enterprise/client-auction/pkg/common/utils"
	"github.com/fonchain_enterprise/client-auction/pkg/e"
	"github.com/fonchain_enterprise/client-auction/pkg/model/login"
	"github.com/fonchain_enterprise/client-auction/pkg/service"
	"github.com/fonchain_enterprise/client-auction/pkg/service/fenghe/dto"
	"github.com/gin-gonic/gin"
	"time"
)

// 创建交易证明
func CreateSeriesPaymentPprof(c *gin.Context) {
	var req dto.CreatePprofReq
	if err := c.ShouldBindJSON(&req); err != nil {
		service.Error(c, e.InvalidParams, err, "无效参数")
		return
	}
	if req.PayTime == "" {
		service.Error(c, e.Failed, nil, "请填写支付时间")
		return
	} else {
		_, err := time.Parse("2006-01-02 15:04", req.PayTime)
		if err != nil {
			service.Error(c, e.Failed, nil, "时间格式错误")
			return
		}
	}
	userInfoAny, exist := c.Get(e.JwtInfo)
	if !exist {
		service.Error(c, e.NotLogin, nil, "请先登录")
		return
	}
	//如果接口调用前没有提交过付款/收款证明则，则使用老逻辑
	var pprofListResTotal int64 = 1
	//pprofListRes, err := service.FengheProvider.GetSeriesPaymentPprofList(c, &fenghe.GetSeriesPaymentPprofListRequest{
	//	Query: &fenghe.SeriesPaymentPprofData{
	//		OrderNo: req.OrderNo,
	//	},
	//	Page:     1,
	//	PageSize: -1,
	//})
	//pprofListResTotal = pprofListRes.Total
	//fmt.Printf("pprofListRes:%+v\n", pprofListRes.List)

	//判断买方是否已经实名审核通过
	{
		orderInfo, errs := service.FengheProvider.GetSeriesOrderDetail(c, &fenghe.GetSeriesOrderByIdRequest{OrderNo: req.OrderNo})
		if errs != nil {
			service.Error(c, e.Failed, errs, "订单信息查询失败")
			return
		}
		res, errs := service.AccountProvider.ReviewRealNameList(c.Request.Context(), &account.ReviewRealNameListRequest{
			UserId:   uint32(orderInfo.UserID),
			Page:     0,
			PageSize: 0,
		})
		if errs != nil {
			service.Error(c, e.Error, errs, "用户实名信息查询失败")
			return
		}
		if len(res.Data) == 0 {
			service.Error(c, e.Error, errs, "找不到用户的实名信息")
			return
		}
		if res.Data[0].PassStatus != 2 {
			service.Error(c, e.Error, errs, "操作被取消，请先进行实名审核！")
			return
		}
	}
	userInfo := userInfoAny.(login.Info)
	fmt.Printf("userInfo:%#v\n", userInfo)
	userData, err := service.AccountProvider.Info(c, &account.InfoRequest{Domain: e.VerifierDomain, ID: userInfo.ID})
	if err != nil {
		service.Error(c, e.Failed, err)
		return
	}
	if pprofListResTotal == 0 {
		fmt.Printf("CreateSeriesPaymentPprof 走了老逻辑 orderNo:%s\n", req.OrderNo)
		//userInfo := middleware.LoginInfo{}
		var protoReq = fenghe.CreateSeriesPaymentPprofReq{
			OrderNo:     req.OrderNo,
			PprofUrls:   req.PprofUrls,
			OptUserId:   int64(userData.Info.ID),
			OptUserName: userData.Info.NickName,
			Remark:      req.Remark,
			PayTime:     req.PayTime,
		}
		fmt.Printf("protoReq:%#v\n", protoReq)
		_, err = service.FengheProvider.CreateSeriesPaymentPprof(c, &protoReq)
		if err != nil {
			service.Error(c, e.Failed, err)
			return
		}
		recordStatus(c, &protoReq)
	} else {
		fmt.Printf("CreateSeriesPaymentPprof 走了新逻辑 orderNo:%s\n", req.OrderNo)
		if req.ArrivalDate == "" || req.PaymentType == "" || req.BankAccount == "" || req.IncomeCompany == "" {
			service.Error(c, e.Failed, nil, "请填写完整的支付信息")
			return
		}
		var pprofList = []*fenghe.CreateSeriesPaymentPprofReqV2_Pprof{}
		for _, v := range req.PprofUrls {
			tmp := &fenghe.CreateSeriesPaymentPprofReqV2_Pprof{
				PicUrl:    v,
				PprofType: 2,
			}
			pprofList = append(pprofList, tmp)
		}
		fmt.Printf("req.PaymentType:%v\n", req.PaymentType)
		var protoReq = fenghe.CreateSeriesPaymentPprofReqV2{
			OrderNo:       req.OrderNo,
			PprofUrls:     pprofList,
			OptUserId:     int64(userData.Info.ID),
			OptUserName:   userData.Info.NickName,
			Remark:        req.Remark,
			PayTime:       req.PayTime,
			ArrivalDate:   req.ArrivalDate,
			PaymentType:   req.PaymentType,
			BankAccount:   req.BankAccount,
			AgentIncome:   req.AgentIncome,
			AfterTax:      req.AfterTax,
			IncomeCompany: req.IncomeCompany,
			ServiceFee:    req.ServiceFee,
		}
		fmt.Printf("protoReq:%#v\n", protoReq)
		_, err = service.FengheProvider.CreateSeriesPaymentPprofV2(c, &protoReq)
		if err != nil {
			service.Error(c, e.Failed, err)
			return
		}
		recordStatus2(c, &protoReq)
		//_, err = service.FengheProvider.UpdateSeriesOrder(c, &fenghe.SeriesOrderData{
		//	OrderNo: req.OrderNo,
		//	Status:  2, //完成付款
		//})
		//if err != nil {
		//	service.Error(c, e.Failed, err)
		//	return
		//}
		//下面的逻辑触发完成付款，把支付金额加入到订单中。
		orderRes, err2 := service.FengheProvider.GetSeriesOrderDetail(c, &fenghe.GetSeriesOrderByIdRequest{
			OrderNo: req.OrderNo,
		})
		if err2 != nil {
			service.Error(c, e.Failed, err2)
			return
		}
		tradePayListRes, err3 := service.FengheProvider.ListAuctionPay(c, &fenghe.AuctionPayList{
			OrderNo: req.OrderNo,
		})
		if err3 != nil {
			service.Error(c, e.Failed, err3)
			return
		}
		//_, _ = service.FengheProvider.UpdateAuctionPay(c, &fenghe.AuctionPayRequest{
		//	OrderNo: req.OrderNo,
		//	Status:  3,
		//})
		updatePaymentReq := &fenghe.UpdateTradePaymentReq{
			TradeNo:      tradePayListRes.Data[0].Uuid, //肯定有支付流水
			TradeStatus:  2,
			CallbackBody: "确认付款",
			Amount:       orderRes.Amount,
		}
		fmt.Printf("updatePaymentReq:%+v\n", updatePaymentReq)
		_, err = service.FengheProvider.UpdateTradePayment(c, updatePaymentReq)
		if err != nil {
			fmt.Println("更新支付状态失败", err)
			service.Error(c, e.Failed, err)
			return
		} else {
			fmt.Println("确认付款，更新支付状态成功")
		}
		//同步到订单微服务
		go func() {
			orderRes, err = service.FengheProvider.GetSeriesOrderDetail(c, &fenghe.GetSeriesOrderByIdRequest{
				OrderNo: req.OrderNo,
			})
			if err != nil {
				return
			}
			orderBytes, _ := json.Marshal(orderRes)
			logRes, _ := service.OrderProvider.CreateLog(c, &order.LogRequest{
				Domain: e.MallAuctionDomain,
				Body:   string(orderBytes),
			})
			var orderRequest = &order.OrderRequest{
				LogID:      logRes.ID,
				BuyerUid:   fmt.Sprintf("%d", orderRes.UserID),
				BuyerName:  orderRes.UserName,
				BuyerTel:   orderRes.TelNum,
				BuyerIdNum: "",
				OrderNum:   orderRes.OrderNo,
				OutOrderId: orderRes.OrderNo,
				SellTime:   orderRes.CreatedAt,
				Gtv:        orderRes.Amount,
			}
			if orderRes.ActionCode == "auction" {
				orderRequest.From = "拍卖"
			} else if orderRes.ActionCode == "show" {
				orderRequest.From = "画展"
			}
			seriesDetail, _ := service.FengheProvider.GetShopSeriesDetail(c, &fenghe.GetSeriesArtworkByIdRequest{SeriesUid: orderRes.SeriesUid})
			if seriesDetail != nil {
				orderRequest.ShowName = seriesDetail.SeriesName
			}
			artworkList, _ := service.ArtworkQueryProvider.GetArtworkProfileList(c, &artwork_query.GetArtworkProfileListRequest{
				Query: &artwork_query.ArtworkProfileData{
					Uuid: orderRes.ArtworkUuid,
				},
			})
			artworkInfo := artwork_query.ArtworkProfileData{}
			if artworkList.Total > 0 {
				artworkInfo = *artworkList.List[0]
			}
			orderRequest.ArtworkName = artworkInfo.Name
			orderRequest.ArtworkNum = artworkInfo.Tfnum
			orderRequest.ArtworkImg = artworkInfo.ArtworkPic
			orderRequest.ArtistName = artworkInfo.ArtistName
			orderRequest.ArtworkType = uint64(artworkInfo.ArtworkType)
			orderRequest.ArtistUid = artworkInfo.ArtistUuid
			orderRequest.ArtworkUid = orderRes.ArtworkUuid
			orderRequest.ArtworkSize = fmt.Sprintf("%d", artworkInfo.Ruler)
			orderRequest.Price = orderRes.Amount

			staff, _ := service.AccountProvider.Info(c.Request.Context(), &account.InfoRequest{ID: uint64(orderRes.StaffId), Domain: e.Domain_Erp, Scene: "base"})
			if staff != nil && staff.Info != nil && staff.IsExist {
				orderRequest.SellerId = staff.Info.ID
				orderRequest.SellerName = staff.Info.NickName
				orderRequest.SellerTel = staff.Info.TelNum
				orderRequest.SellerNum = ""
				orderRequest.SellerImg = ""
				orderRequest.SellerUid = fmt.Sprintf("%d", staff.Info.ID)
			}
			if orderRes.CompanyId != 0 {
				departmentRes, _ := service.DepartmentProvider.Detail(c, &department.DetailRequest{
					ID:     int64(orderRes.CompanyId),
					Domain: e.Domain_Erp,
				})
				if departmentRes != nil {
					orderRequest.SaleSite = departmentRes.Name
					orderRequest.SaleSiteUid = fmt.Sprintf("%d", departmentRes.ID)
				}
			}
			service.OrderProvider.OrderCreate(c, orderRequest)
		}()
	}
	service.Success(c)
	return
}

// 删除交易证明
func DeleteSeriesPaymentPprof(c *gin.Context) {
	var req fenghe.DeleteSeriesPaymentPprofRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		service.Error(c, e.InvalidParams, err, "无效参数")
		return
	}
	_, err := service.FengheProvider.DeleteSeriesPaymentPprof(c, &req)
	if err != nil {
		service.Error(c, e.Failed, err)
		return
	}
	service.Success(c)
}

// 更新交易证明
func UpdateSeriesPaymentPprof(c *gin.Context) {
	var req fenghe.SeriesPaymentPprofData
	if err := c.ShouldBindJSON(&req); err != nil {
		service.Error(c, e.InvalidParams, err, "无效参数")
		return
	}
	_, err := service.FengheProvider.UpdateSeriesPaymentPprof(c, &req)
	if err != nil {
		service.Error(c, e.Failed, err)
		return
	}
	service.Success(c)
}

// 使用id查询交易证明
func GetSeriesPaymentPprofDetail(c *gin.Context) {
	var req fenghe.GetSeriesPaymentPprofByIdRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		service.Error(c, e.InvalidParams, err, "无效参数")
		return
	}
	resp, err := service.FengheProvider.GetSeriesPaymentPprofDetail(c, &req)
	if err != nil {
		service.Error(c, e.Failed, err)
		return
	}
	service.Success(c, resp)
}

// 批量查询交易证明
func GetSeriesPaymentPprofList(c *gin.Context) {
	var req dto.GetSeriesPaymentPprofListRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		service.Error(c, e.InvalidParams, err, "无效参数")
		return
	}
	var protoReq = fenghe.GetSeriesPaymentPprofListRequest{Query: &fenghe.SeriesPaymentPprofData{}}
	utils.RequestDataConvert(&req, &protoReq)
	resp, err := service.FengheProvider.GetSeriesPaymentPprofList(c, &protoReq)
	if err != nil {
		service.Error(c, e.Failed, err)
		return
	}
	service.List(c, resp.List, service.OptionPage(resp.Page, resp.PageSize, resp.Total), service.OptionMsg("查询成功"))
}

func OrderUserReplace(c *gin.Context) {
	var req dto.OrderUserReplaceReq
	if err := c.ShouldBindJSON(&req); err != nil {
		service.Error(c, e.InvalidParams, err, "无效参数")
		return
	}
	fmt.Printf("OrderUserReplace.req:%+v\n", req)
	orderInfo, err := service.FengheProvider.GetSeriesOrderDetail(c, &fenghe.GetSeriesOrderByIdRequest{OrderNo: req.OrderNo})
	if err != nil {
		service.Error(c, e.Failed, err)
		return
	}
	var record = fenghe.TransferOrderRecordData{
		OrderNo:        orderInfo.OrderNo,
		SeriesUid:      orderInfo.SeriesUid,
		AuctionBuyUuid: orderInfo.AuctionBuyUuid,
		OriginUserId:   orderInfo.UserID,
		OriginTelNum:   orderInfo.TelNum,
	}
	var userExist bool
	if req.UserId > 0 {
		//查询用户是否存在
		infoRes, err3 := service.AccountProvider.Info(c, &account.InfoRequest{Domain: e.MallAuctionDomain, ID: req.UserId})
		if err3 != nil {
			service.Error(c, e.Failed, err3)
			return
		}
		if infoRes.Info != nil && infoRes.Info.ID > 0 {
			req.UserId = infoRes.Info.ID
			req.UserName = infoRes.Info.RealName
			req.TelNum = infoRes.Info.TelNum
			userExist = true
		}
	} else if req.UserName == "" && req.TelNum == "" && req.CardId == "" {
		service.Error(c, e.Failed, nil, "用户信息不存在")
		return
	}
	if !userExist {
		vd, _ := utils.NewPhoneValidator()
		if !vd.Validate(req.TelNum) {
			service.Error(c, e.Failed, nil, "手机号格式错误")
			return
		}
		//创建用户
		if err = utils.AuthenticationTwo(utils.AuthService{Name: req.UserName, IDNum: req.CardId}); err != nil {
			service.Error(c, e.Failed, err, "身份证与姓名不匹配")
			return
		}
		registerRequest := account.RegistRequest{
			Domain:    e.MallAuctionDomain,
			TelNum:    req.TelNum,
			NickName:  req.UserName,
			Password:  req.TelNum,
			EnterDate: time.Now().Format(time.DateTime),
			Extend:    &account.Extend{JumpTo: "onsite"}, //origin-老平台  onsite 当前
		}
		var registerResult *account.RequestStatus
		registerResult, err = service.AccountProvider.Register(c, &registerRequest)
		if err != nil {
			return
		}
		req.UserId = registerResult.ID
	}
	if req.GenAuctionUserNo {
		userNoResp, errs := service.FengheProvider.GetAuctionSessionUserNoList(c, &fenghe.GetAuctionSessionUserNoListRequest{
			Query:    &fenghe.AuctionSessionUserNoData{SessionNo: orderInfo.SeriesUid, Phone: req.TelNum},
			Page:     1,
			PageSize: 1,
		})
		if errs != nil {
			service.Error(c, e.Failed, errs)
			return
		}
		if userNoResp.Total == 0 {
			_, err2 := service.FengheProvider.CreateAuctionSessionUserNo(c, &fenghe.AuctionSessionUserNoData{
				SessionNo: orderInfo.SeriesUid,
				Phone:     orderInfo.TelNum,
			})
			if err2 != nil {
				service.Error(c, e.Failed, err2)
				return
			}
		}
	}
	//更新订单
	orderInfo.UserID = int32(req.UserId)
	orderInfo.UserName = req.UserName
	orderInfo.TelNum = req.TelNum
	record.TargetUserId = int32(req.UserId)
	record.TargetTelNum = req.TelNum
	_, err = service.FengheProvider.UpdateSeriesOrder(c, orderInfo)
	if err != nil {
		service.Error(c, e.Failed, err)
		return
	}

	// 添加到换单记录，避免扯皮
	_, _ = service.FengheProvider.CreateTransferOrderRecord(c, &record)

	service.Success(c)
}

// 销售提交支付信息 因为只有show 那啥用，所依放出来
func recordStatus(c *gin.Context, req *fenghe.CreateSeriesPaymentPprofReq) {
	realReq := &fenghe.RecordSeriesOrderRequest{
		OrderNo:       req.OrderNo,
		ContractFiles: []string{},
		Files:         []string{},
		ShowStatus:    uint32(e.ShowStatusNeedAgreed),
	}

	for _, v := range req.PprofUrls {
		realReq.Files = append(realReq.Files, v)
	}

	service.FengheProvider.RecordSeriesOrder(c, realReq)
	return
}

func recordStatus2(c *gin.Context, req *fenghe.CreateSeriesPaymentPprofReqV2) {
	realReq := &fenghe.RecordSeriesOrderRequest{
		OrderNo:       req.OrderNo,
		ContractFiles: []string{},
		Files:         []string{},
		ShowStatus:    uint32(e.ShowStatusNeedAgreed),
	}

	for _, v := range req.PprofUrls {
		v := v
		realReq.Files = append(realReq.Files, v.PicUrl)
	}

	service.FengheProvider.RecordSeriesOrder(c, realReq)
	return
}
