// Package handle -----------------------------
// @file      : backend.go
// <AUTHOR> JJXu
// @contact   : <EMAIL>
// @time      : 2025/4/12 12:50
// -------------------------------------------
package handle

import (
	"context"
	"encoding/json"
	"fmt"
	"os"
	"path/filepath"
	"sort"
	"strconv"
	"strings"
	"time"

	"github.com/dorlolo/exportToExcel"
	"github.com/fonchain_enterprise/client-auction/api/account"
	"github.com/fonchain_enterprise/client-auction/api/artwork_query"
	"github.com/fonchain_enterprise/client-auction/api/custom_contract"
	"github.com/fonchain_enterprise/client-auction/api/department"
	"github.com/fonchain_enterprise/client-auction/api/fenghe"
	backendSeries "github.com/fonchain_enterprise/client-auction/api/series"
	"github.com/fonchain_enterprise/client-auction/pkg/common/utils"
	"github.com/fonchain_enterprise/client-auction/pkg/e"
	"github.com/fonchain_enterprise/client-auction/pkg/logic"
	"github.com/fonchain_enterprise/client-auction/pkg/service"
	"github.com/fonchain_enterprise/client-auction/pkg/service/fenghe/dto"
	"github.com/gin-gonic/gin"
)

// 销售情况列表接口
func GetAdmSeriesProfileList(c *gin.Context) {
	var req dto.GetAdmSeriesProfileListRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		service.Error(c, e.InvalidParams, err, "无效参数")
		return
	}
	switch req.SortMode {
	case 1:
		req.Order = "sales_total desc"
	case 2:
		req.Order = "sales_total asc"
	case 3:
		req.Order = "created_at asc"
	case 4:
		req.Order = "created_at desc"
	}
	resp, err := service.FengheProvider.GetSeriesProfileList(c, &req.GetSeriesProfileListRequest)
	if err != nil {
		service.Error(c, e.Error, err)
		return
	}

	ci, err := logic.GetAllCurrencyPrice("RMB", "1")
	if err != nil {
		service.Error(c, e.Error, err)
		return
	}
	ciMap := map[string]float64{}
	for _, v := range ci {
		rate, err := strconv.ParseFloat(v.Price, 64)
		if err != nil {
			continue
		}
		ciMap[v.Currency] = rate
	}
	for _, v := range resp.List {
		if v.ActionCode != "culture" {
			continue
		}

		sol, err := service.FengheProvider.GetSeriesOrderList(c, &fenghe.GetSeriesOrderListRequest{
			Page:     1,
			PageSize: 10000,
			Query: &fenghe.SeriesOrderData{
				SeriesUid: v.SeriesUuid,
			},
		})
		if err != nil {
			continue
		}

		var totalPaymentAmout float64
		for _, order := range sol.List {
			ca := dto.CultureAddition{}
			if order.CultureAddition != "" && json.Unmarshal([]byte(order.CultureAddition), &ca) == err {
				switch ca.ActualPaymentCurrency {
				case "CNY", "cny":
					value, _ := strconv.ParseFloat(ca.ActualPaymentAmount, 64)
					totalPaymentAmout += value
				case "JPY", "jpy":
					value, _ := strconv.ParseFloat(ca.ActualPaymentAmount, 64)
					totalPaymentAmout += value / ciMap["JPY"]
				case "USD", "usd":
					value, _ := strconv.ParseFloat(ca.ActualPaymentAmount, 64)
					totalPaymentAmout += value / ciMap["USD"]
				}
			}
		}
		v.SalesTotal = strconv.FormatFloat(totalPaymentAmout, 'f', 2, 64)
	}

	service.List(c, resp.List, service.OptionPage(resp.Page, resp.PageSize, resp.Total), service.OptionMsg("查询成功"))
}

func InitCacheIndex(c *gin.Context) {
	_, err := service.FengheProvider.InitSeriesProfileCache(c, &fenghe.CommonReq{})
	if err != nil {
		service.Error(c, e.Error, err)
		return
	}
	service.Success(c)
}

//type TempExport struct {
//	count int
//	Time  time.Time
//}
//
//var exportMap = make(map[string]TempExport)
//var SeriesOrderRecordLocker sync.Mutex

// 拍卖类型数字对应字段：	订单号	支付方式	到账日期	入账银行	代理收入	税后收入	收入计入公司	服务费	画作类型	是否卖版权	版权费用	所属站点	所属销售员	客户签署协议时间	确认收货时间	订单创建时间;	画作编号	画作名称	订单金额(RMB)	付款状态	收货状态	购买时间	订单创建时间	付款时间	购买人	购买人手机号	LOT号	号牌
// 画展类型数字对应字段：	订单号	支付方式	到账日期	入账银行	代理收入	税后收入	收入计入公司	服务费	画作类型	是否卖版权	版权费用	所属站点	所属销售员	客户签署协议时间	确认收货时间	订单创建时间;	画作编号	画作名称	订单金额(RMB)	付款状态	收货状态	购买时间	订单创建时间	付款时间	购买人	购买人手机号	订单状态
func SeriesOrderRecord(c *gin.Context) {

	//SeriesOrderRecordLocker.Lock()
	//defer SeriesOrderRecordLocker.Unlock()
	var req dto.SeriesArtworkPayRecord
	if err := c.ShouldBindJSON(&req); err != nil {
		service.Error(c, e.InvalidParams, err, "无效参数")
		return
	}

	//if _, ok := exportMap[req.SeriesUid]; ok {
	//	if exportMap[req.SeriesUid].count >= 3 && time.Now().Sub(exportMap[req.SeriesUid].Time) < 20*time.Second {
	//		path, fileName, err := orderExportLogic(req.SeriesUid)
	//		if err != nil {
	//			service.Error(c, e.Error, err)
	//			return
	//		}
	//		delete(exportMap, req.SeriesUid)
	//		c.Header("Content-Description", "File Transfer")
	//		c.Header("Content-Type", "application/vnd.openxmlformats-officedocument.wordprocessingml.document")
	//		c.Header("Content-Disposition", "attachment; filename="+fileName)
	//		c.File(path)
	//		return
	//	} else {
	//		data := exportMap[req.SeriesUid]
	//		data.count = data.count + 1
	//		data.Time = time.Now()
	//		exportMap[req.SeriesUid] = data
	//	}
	//} else {
	//	exportMap[req.SeriesUid] = TempExport{}
	//}
	var protoReq = fenghe.GetSeriesOrderListRequest{}
	protoReq.Order = "created_at desc"
	utils.RequestDataConvert(&req, &protoReq)
	if req.Keywords != "" {
		artworkList, _ := service.ArtworkQueryProvider.GetArtworkProfileList(c, &artwork_query.GetArtworkProfileListRequest{
			Query:    nil,
			Page:     1,
			PageSize: -1,
			Where:    fmt.Sprintf("tfnum like '%%%s%%' OR name like '%%%s%%' ", req.Keywords, req.Keywords),
			Select:   []string{"uuid"},
		})
		artworkUidList := []string{}
		if artworkList != nil && artworkList.Total > 0 {
			for _, v := range artworkList.List {
				v := v
				artworkUidList = append(artworkUidList, v.Uuid)
			}
		}
		if len(artworkUidList) > 0 {
			protoReq.Where = fmt.Sprintf("user_name like '%%%s%%' OR tel_num like '%%%s%%' OR artwork_uuid in ('%s') ", req.Keywords, req.Keywords, strings.Join(artworkUidList, "','"))
		} else {
			protoReq.Where = fmt.Sprintf("user_name like '%%%s%%' OR tel_num like '%%%s%%'", req.Keywords, req.Keywords)
		}
	}
	if req.ArrivalDateBegin != "" && req.ArrivalDateEnd != "" {
		if protoReq.Where != "" {
			protoReq.Where += " AND "
		}
		protoReq.Where += fmt.Sprintf("arrival_date >= '%s' AND arrival_date <= '%s'", req.ArrivalDateBegin, req.ArrivalDateEnd)
	}

	orderList, err := service.FengheProvider.GetSeriesOrderList(c, &protoReq)
	if err != nil {
		service.Error(c, e.Failed, err, "查询失败")
		return
	}
	setCompanyAndStaff(c, orderList.List)
	var artworkUidList []string
	for _, v := range orderList.List {
		v := v
		artworkUidList = append(artworkUidList, v.ArtworkUuid)
	}
	artworkList, err2 := service.ArtworkQueryProvider.GetArtworkProfileList(c, &artwork_query.GetArtworkProfileListRequest{
		Page:     1,
		PageSize: int64(len(artworkUidList)),
		Where:    fmt.Sprintf("uuid in ('%s')", strings.Join(artworkUidList, "','")),
		Select:   []string{"uuid", "name", "hd_pic", "tfnum", "mask"},
	})
	if err2 != nil {
		service.Error(c, e.Failed, err2, "查询失败")
		return
	}
	var data []dto.SalesRecordType
	for _, v := range orderList.List {
		v := v
		tmp := dto.SalesRecordType{
			ArtworkUid:      v.ArtworkUuid,
			Amount:          v.Amount,
			Currency:        v.Currency,
			Status:          v.Status,
			BuyTime:         v.CreatedAt,
			BuyUserName:     v.UserName,
			BuyUserTel:      v.TelNum,
			OrderNo:         v.OrderNo,
			ReceivingStatus: v.ReceivingStatus,
			CompanyId:       v.CompanyId,
			CompanyName:     v.CompanyName,
			StaffId:         v.StaffId,
			StaffName:       v.StaffName,
			ShowStatus:      v.ShowStatus,
			AuctionCode:     v.ActionCode,
			ArrivalDate:     v.ArrivalDate,
			PaymentType:     v.PaymentType,
			BankAccount:     v.BankAccount,
			AgentIncome:     v.AgentIncome,
			AfterTax:        v.AfterTax,
			IncomeCompany:   v.IncomeCompany,
			ServiceFee:      v.ServiceFee,
			SignStatus:      int32(v.SignStatus),
			ReceivingTime:   v.ReceivingTime,
			CreatedAt:       v.CreatedAt,
		}

		if tmp.Status == 2 || tmp.Status == 4 {
			payListRes, _ := service.FengheProvider.ListAuctionPay(c, &fenghe.AuctionPayList{
				Page:     1,
				PageSize: 100,
				Status:   1,
				OrderNo:  tmp.OrderNo,
			})
			if payListRes != nil && len(payListRes.Data) > 0 {
				sort.Slice(payListRes.Data, func(i, j int) bool {
					return payListRes.Data[i].UpdatedAt > payListRes.Data[j].UpdatedAt
				})
				tmp.PayTime = payListRes.Data[0].UpdatedAt //老版本的支付时间
				for _, tradeInfo := range payListRes.Data {
					if len(tradeInfo.PprofList) > 0 {
						tmp.PayTime = tradeInfo.PprofList[len(tradeInfo.PprofList)-1].PayTime //新版本的支付时间
					}
				}
			}
		}

		if v.ActionCode == "culture" && v.CultureAddition != "" {
			cultureAddition := dto.CultureAddition{}
			if json.Unmarshal([]byte(v.CultureAddition), &cultureAddition) == nil {
				tmp.ActualPaymentAmount = cultureAddition.ActualPaymentAmount
				tmp.PayType = uint32(cultureAddition.PayType)
				tmp.ActualPaymentCurrency = cultureAddition.ActualPaymentCurrency
				tmp.CommodityInfo = cultureAddition.CommodityInfo
			}
		}

		for _, vv := range artworkList.List {
			if v.ArtworkUuid == vv.Uuid {
				tmp.ArtworkName = vv.Name
				tmp.HdPic = vv.HdPic
				tmp.Tfnum = vv.Tfnum
				tmp.ArtworkMask = vv.Mask
				break
			}
		}
		data = append(data, tmp)
	}

	var auctionStat any
	if req.ActionCode == "auction" {
		//查lot号
		var auctionArtworkList *fenghe.GetAuctionArtworkListResp
		auctionArtworkList, err = service.FengheProvider.GetAuctionArtworkList(c, &fenghe.GetAuctionArtworkListRequest{
			Where:    fmt.Sprintf("artwork_uuid in ('%s')", strings.Join(artworkUidList, "','")),
			Page:     1,
			PageSize: -1,
		})
		if err == nil {
			for i, v := range data {
				for _, vv := range auctionArtworkList.List {
					if v.ArtworkUid == vv.ArtworkUuid {
						vv := vv
						data[i].LotNo = fmt.Sprintf("%d", vv.Index)
						break
					}
				}
			}
		}

		seriesObj, err1 := service.BackendSeriesProvider.SeriesDetail(c, &backendSeries.SeriesDetailReq{SeriesUuid: req.SeriesUid})
		if err1 == nil {
			auctionStat = logic.GetAuctionStat(seriesObj.AuctionUuid)
		}
		if seriesObj != nil && seriesObj.AuctionUuid != "" {
			for i, order := range data {
				//查用户拍卖号
				auctionUserList, err3 := service.FengheProvider.GetAuctionSessionUserNoList(c, &fenghe.GetAuctionSessionUserNoListRequest{
					Query: &fenghe.AuctionSessionUserNoData{
						SessionNo: seriesObj.AuctionUuid,
					},
					Page:     1,
					PageSize: -1,
				})
				if err3 != nil {
					service.Error(c, e.Failed, err3)
					return
				}
				for _, v := range auctionUserList.List {
					if v.Phone == order.BuyUserTel {
						v := v
						data[i].AuctionUserNo = fmt.Sprintf("%03d", v.AuctionUserNo)
						break
					}
				}
			}
		}
	}
	//todo 合同完全签署时间
	var orderNos []string
	for _, v := range data {
		if v.OrderNo != "" {
			orderNos = append(orderNos, v.OrderNo)
		}
	}
	if len(orderNos) > 0 {
		signInfoResp, _err := service.CustomContractProvider.SignInfo(context.Background(), &custom_contract.SignInfoReq{OrderNos: orderNos})
		if _err != nil {
			service.Error(c, e.Failed, _err, "查询失败")
			return
		}
		if len(signInfoResp.Data) > 0 {
			for i, _ := range data {
				for _, v := range signInfoResp.Data {
					if v.OrderID == data[i].OrderNo {
						data[i].SignFinishTime = v.SignDate
					}
				}
			}
		}
	}

	//拍卖今日累计在线
	service.ListStat(c, data, auctionStat, orderList.Page, orderList.PageSize, orderList.Total)
}

func UpdateSeriesOrderSaleStatus(c *gin.Context) {
	var req fenghe.SeriesOrderData
	if err := c.ShouldBindJSON(&req); err != nil {
		service.Error(c, e.InvalidParams, err, "无效参数")
		return
	}

	r := fenghe.SeriesOrderData{ID: req.ID, OrderNo: req.OrderNo, StaffId: req.StaffId, CompanyId: req.CompanyId}
	res, err := service.FengheProvider.UpdateSeriesOrder(c, &r)
	if err != nil {
		service.Error(c, e.Error, err)
		return
	}

	service.Success(c, res)
	return
}

func PayRecordList(c *gin.Context) {
	var req dto.PayRecordListRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		service.Error(c, e.InvalidParams, err, "无效参数")
		return
	}
	if req.OrderNo == "" {
		service.Error(c, e.InvalidParams, nil, "请输入订单号")
		return
	}
	auctionPayList, err := service.FengheProvider.ListAuctionPay(c, &fenghe.AuctionPayList{
		OrderNo:  req.OrderNo,
		Page:     req.Page,
		PageSize: req.PageSize,
		Status:   1, //只查询支付成功的记录
	})
	if err != nil {
		service.Error(c, e.Failed, err, "查询失败")
		return
	}
	var data = []dto.PayRecordType{}
	userIds := []uint64{}
	for _, v := range auctionPayList.Data {
		tmp := dto.PayRecordType{
			OrderNo:       req.OrderNo,
			TradeNo:       v.Uuid,
			Amount:        v.Money,
			Currency:      v.Currency,
			Status:        v.Status,
			PayTime:       v.UpdatedAt, //老版本支付时间
			FukuanPprof:   []string{},
			ShouKuanPprof: []string{},
			Remark:        v.Remark,
			OptUserId:     v.OptUserId,
			OptUserName:   v.OptUserName,
			OptTime:       v.CreatedAt,
			UserId:        v.UserID,

			ArrivalDate:   v.ArrivalDate,
			PaymentType:   v.PaymentType,
			BankAccount:   v.BankAccount,
			AgentIncome:   v.AgentIncome,
			AfterTax:      v.AfterTax,
			IncomeCompany: v.IncomeCompany,
			ServiceFee:    v.ServiceFee,
		}
		userIds = append(userIds, uint64(v.UserID))
		if strings.HasPrefix(v.Uuid, "ali") {
			tmp.Plat = "支付宝"
		} else if strings.HasPrefix(v.Uuid, "we") {
			tmp.Plat = "微信"
		} else if strings.HasPrefix(v.Uuid, "antom") {
			tmp.Plat = "安通"
		} else {
			tmp.Plat = "其它"
		}
		if len(v.PprofList) > 0 {
			for _, pprof := range v.PprofList {
				pprof := pprof
				fmt.Printf("id %d ,PprofType:%d", pprof.ID, pprof.PprofType)
				if pprof.PprofType == 1 {
					tmp.FukuanPprof = append(tmp.FukuanPprof, pprof.PicUrl)
				} else if pprof.PprofType == 2 {
					tmp.ShouKuanPprof = append(tmp.ShouKuanPprof, pprof.PicUrl)
				}
			}
			tmp.PayTime = v.PprofList[len(v.PprofList)-1].PayTime //新版本支付时间
		}
		data = append(data, tmp)
	}
	////找userId对应的手机号,写错接口了，删了可以，留着吧
	//userList, err1 := service.AccountProvider.ListByIDs(c, &account.ListByIDsRequest{
	//	Domain: e.AuctionDomain,
	//	IDs:    userIds,
	//})
	//if err1 != nil {
	//	service.Error(c, e.Failed, err1)
	//	return
	//}
	//for _, v := range userList.Data {
	//	for i, vv := range data {
	//		if v.ID == uint64(vv.UserId) {
	//			v := v
	//			data[i].Phone = v.TelNum
	//			break
	//		}
	//	}
	//}
	////找到用户对应的拍卖号牌
	//orderInfo, err := service.FengheProvider.GetSeriesOrderDetail(c, &fenghe.GetSeriesOrderByIdRequest{OrderNo: req.OrderNo})
	//if err != nil {
	//	service.Error(c, e.Failed, err)
	//	return
	//}
	//seriesInfo, errs := service.BackendSeriesProvider.SeriesDetail(c, &backendSeries.SeriesDetailReq{
	//	SeriesUuid: orderInfo.SeriesUid,
	//})
	//if errs != nil {
	//	service.Error(c, e.Failed, errs)
	//	return
	//}
	//if seriesInfo.ActionCode == "auction" {
	//	auctionUserList, err2 := service.FengheProvider.GetAuctionSessionUserNoList(c, &fenghe.GetAuctionSessionUserNoListRequest{
	//		Query: &fenghe.AuctionSessionUserNoData{
	//			SessionNo: seriesInfo.AuctionUuid,
	//		},
	//		Page:     1,
	//		PageSize: -1,
	//	})
	//	if err2 != nil {
	//		service.Error(c, e.Failed, err2)
	//		return
	//	}
	//	for _, v := range auctionUserList.List {
	//		for i, vv := range data {
	//			if v.Phone == vv.Phone {
	//				v := v
	//				data[i].LotNo = fmt.Sprintf("%03d", v.AuctionUserNo)
	//				break
	//			}
	//		}
	//	}
	//}
	service.List(c, data, service.OptionPage(req.Page, req.PageSize, auctionPayList.Count), service.OptionMsg("查询成功"))
}
func OrderExport(c *gin.Context) {
	var req dto.OrderExportReq
	if err := c.ShouldBindQuery(&req); err != nil {
		service.Error(c, e.InvalidParams, err, "无效参数")
		return
	}
	path, fileName, err := orderExportLogic(req.SeriesUid)
	if err != nil {
		service.Error(c, e.Error, err)
		return
	}
	c.Header("Content-Description", "File Transfer")
	c.Header("Content-Type", "application/vnd.openxmlformats-officedocument.wordprocessingml.document")
	c.Header("Content-Disposition", "attachment; filename="+fileName)
	c.File(path)
}

type ExportAuctionOrderType struct {
	OrderNo         string `json:"orderNo"`         //订单号
	LotNo           string `json:"lotNo"`           //Lot号
	Tfnum           string `json:"tfnum"`           //画作编号
	ArtworkName     string `json:"artworkName"`     //画作名
	Amount          string `json:"amount"`          //金额
	Status          string `json:"status"`          //订单状态
	ReceivingStatus string `json:"receivingStatus"` //收货状态
	ReceivingTime   string `json:"receivingTime"`   //确认收货时间
	ArtworkType     string `json:"artworkType"`     //画作类型
	CreatedAt       string `json:"createdAt"`       //订单创建时间
	PayTime         string `json:"payTime"`         //支付时间
	SignStatus      string `json:"signStatus"`      //签署协议时间
	NickName        string `json:"nickName"`        //购买人
	Phone           string `json:"phone"`           //购买人手机号
	CompanyName     string `json:"companyName"`     //所属站点
	StaffName       string `json:"staffName"`       //所属销售员
	PaymentType     string `json:"paymentType"`     //支付方式
	AuctionUserNo   string `json:"auctionUserNo"`   //号牌
	ArrivalDate     string `json:"arrivalDate"`     //到账日期
	BankAccount     string `json:"bankAccount"`     //入账银行
	AgentIncome     string `json:"agentIncome"`     //代理收入
	AfterTax        string `json:"afterTax"`        //税后收入
	IncomeCompany   string `json:"incomeCompany"`   //收入计入公司
	ServiceFee      string `json:"serviceFee"`      //服务费
}
type ExportShowOrderType struct {
	OrderNo         string `json:"orderNo"`         //订单号
	Tfnum           string `json:"tfnum"`           //画作编号
	ArtworkName     string `json:"artworkName"`     //画作名
	ArtworkType     string `json:"artworkType"`     //画作类型
	Amount          string `json:"amount"`          //金额
	Status          string `json:"status"`          //订单状态
	SoldCopyright   string `json:"soldCopyright"`   //是否卖版权
	CopyrightPrice  string `json:"copyrightPrice"`  //版权费用
	CreatedAt       string `json:"createdAt"`       //订单创建时间
	ReceivingStatus string `json:"receivingStatus"` //收货状态
	ReceivingTime   string `json:"receivingTime"`   //确认收货时间
	PayTime         string `json:"payTime"`         //支付时间
	SignStatus      string `json:"signStatus"`      //签署协议时间
	NickName        string `json:"nickName"`        //购买人
	Phone           string `json:"phone"`           //购买人手机号
	CompanyName     string `json:"companyName"`     //所属站点
	StaffName       string `json:"staffName"`       //所属销售员
	PaymentType     string `json:"paymentType"`     //支付方式
	ArrivalDate     string `json:"arrivalDate"`     //到账日期
	BankAccount     string `json:"bankAccount"`     //入账银行
	AgentIncome     string `json:"agentIncome"`     //代理收入
	AfterTax        string `json:"afterTax"`        //税后收入
	IncomeCompany   string `json:"incomeCompany"`   //收入计入公司
	ServiceFee      string `json:"serviceFee"`      //服务费
}

func orderExportLogic(seriesUid string) (path string, fileName string, err error) {
	seriesInfo, err := service.BackendSeriesProvider.SeriesDetail(context.Background(), &backendSeries.SeriesDetailReq{
		SeriesUuid: seriesUid,
	})
	if err != nil {
		return
	}
	fileDir := os.TempDir()
	fileName = fmt.Sprintf("%s销售情况数据导出%d.xlsx", seriesInfo.SeriesName, time.Now().Unix())
	path = filepath.Join(fileDir, fileName)
	ex := exportToExcel.NewExcel(fileDir, fileName)
	defer func() {
		if err == nil {
			if err = ex.Save(); err != nil {
				fmt.Println("save file err:", err)
				return
			}
		}
	}()
	resp, err := service.FengheProvider.GetExportOrderTypeList(context.Background(), &fenghe.GetExportOrderTypeListRequest{SeriesUid: seriesUid})
	if err != nil {
		return
	}
	var st1 *exportToExcel.Sheet
	switch seriesInfo.ActionCode {
	case "auction":
		st1 = ex.NewSheet("sheet1", ExportAuctionOrderType{})
		err = st1.Title.Gen(
			st1.Title.NewTitleItem(4, "订单号", 1, 1),
			st1.Title.NewTitleItem(4, "Lot号", 1, 2),
			st1.Title.NewTitleItem(4, "画作编号", 1, 3),
			st1.Title.NewTitleItem(4, "画作名", 1, 4),
			st1.Title.NewTitleItem(4, "金额", 1, 5),
			st1.Title.NewTitleItem(4, "订单状态", 1, 6),
			st1.Title.NewTitleItem(4, "收货状态", 1, 7),
			st1.Title.NewTitleItem(4, "确认收货时间", 1, 8),
			st1.Title.NewTitleItem(4, "画作类型", 1, 9),
			st1.Title.NewTitleItem(4, "订单创建时间", 1, 10),
			st1.Title.NewTitleItem(4, "支付时间", 1, 11),
			st1.Title.NewTitleItem(4, "签署协议状态", 1, 12),
			st1.Title.NewTitleItem(4, "购买人", 1, 13),
			st1.Title.NewTitleItem(4, "购买人手机号", 1, 14),
			st1.Title.NewTitleItem(4, "所属站点", 1, 15),
			st1.Title.NewTitleItem(4, "所属销售员", 1, 16),
			st1.Title.NewTitleItem(4, "支付方式", 1, 17),
			st1.Title.NewTitleItem(4, "号牌", 1, 18),
			st1.Title.NewTitleItem(4, "到账日期", 1, 19),
			st1.Title.NewTitleItem(4, "入账银行", 1, 20),
			st1.Title.NewTitleItem(4, "代理收入", 1, 21),
			st1.Title.NewTitleItem(4, "税后收入", 1, 22),
			st1.Title.NewTitleItem(4, "收入计入公司", 1, 23),
			st1.Title.NewTitleItem(4, "服务费", 1, 24),
		)
		if err != nil {
			fmt.Println("generate title failed1:", err.Error())
			return
		}
		var data = []ExportAuctionOrderType{}
		for _, v := range resp.List {
			tmp := ExportAuctionOrderType{
				OrderNo:         v.OrderNo,
				LotNo:           v.LotNo,
				Tfnum:           v.Tfnum,
				ArtworkName:     v.ArtworkName,
				Amount:          v.Amount,
				Status:          v.Status,
				ReceivingStatus: v.ReceivingStatus,
				ReceivingTime:   v.ReceivingTime,
				ArtworkType:     v.ArtworkType,
				CreatedAt:       v.CreatedAt,
				PayTime:         v.PayTime,
				SignStatus:      v.SignStatus,
				NickName:        v.NickName,
				Phone:           v.Phone,
				CompanyName:     v.CompanyName,
				StaffName:       v.SolderName,
				PaymentType:     v.PaymentType,
				AuctionUserNo:   v.AuctionUserNo,
				ArrivalDate:     v.ArrivalDate,
				BankAccount:     v.BankAccount,
				AgentIncome:     v.AgentIncome,
				AfterTax:        v.AfterTax,
				IncomeCompany:   v.IncomeCompany,
				ServiceFee:      v.ServiceFee,
			}
			data = append(data, tmp)
		}

		//todo 签署协议时间查询
		err = st1.FillData(data)
	case "show":
		st1 = ex.NewSheet("sheet1", ExportShowOrderType{})
		err = st1.Title.Gen(
			st1.Title.NewTitleItem(4, "订单号", 1, 1),
			st1.Title.NewTitleItem(4, "画作编号", 1, 2),
			st1.Title.NewTitleItem(4, "画作名", 1, 3),
			st1.Title.NewTitleItem(4, "画作类型", 1, 4),
			st1.Title.NewTitleItem(4, "金额", 1, 5),
			st1.Title.NewTitleItem(4, "订单状态", 1, 6),
			st1.Title.NewTitleItem(4, "是否卖版权", 1, 7),
			st1.Title.NewTitleItem(4, "版权费用", 1, 8),
			st1.Title.NewTitleItem(4, "订单创建时间", 1, 9),
			st1.Title.NewTitleItem(4, "收货状态", 1, 10),
			st1.Title.NewTitleItem(4, "确认收货时间", 1, 11),
			st1.Title.NewTitleItem(4, "支付时间", 1, 12),
			st1.Title.NewTitleItem(4, "签署协议状态", 1, 13),
			st1.Title.NewTitleItem(4, "购买人", 1, 14),
			st1.Title.NewTitleItem(4, "购买人手机号", 1, 15),
			st1.Title.NewTitleItem(4, "所属站点", 1, 16),
			st1.Title.NewTitleItem(4, "所属销售员", 1, 17),
			st1.Title.NewTitleItem(4, "支付方式", 1, 18),
			st1.Title.NewTitleItem(4, "到账日期", 1, 19),
			st1.Title.NewTitleItem(4, "入账银行", 1, 20),
			st1.Title.NewTitleItem(4, "代理收入", 1, 21),
			st1.Title.NewTitleItem(4, "税后收入", 1, 22),
			st1.Title.NewTitleItem(4, "收入计入公司", 1, 23),
			st1.Title.NewTitleItem(4, "服务费", 1, 24),
		)
		if err != nil {
			fmt.Println("generate title failed2:", err.Error())
			return
		}
		var data = []ExportShowOrderType{}
		for _, v := range resp.List {
			tmp := ExportShowOrderType{
				OrderNo:         v.OrderNo,
				Tfnum:           v.Tfnum,
				ArtworkName:     v.ArtworkName,
				ArtworkType:     v.ArtworkType,
				Amount:          v.Amount,
				Status:          v.Status,
				SoldCopyright:   v.SoldCopyright,
				CopyrightPrice:  v.CopyrightPrice,
				CreatedAt:       v.CreatedAt,
				ReceivingStatus: v.ReceivingStatus,
				ReceivingTime:   v.ReceivingTime,
				PayTime:         v.PayTime,
				SignStatus:      v.SignStatus,
				NickName:        v.NickName,
				Phone:           v.Phone,
				CompanyName:     v.CompanyName,
				StaffName:       v.SolderName,
				PaymentType:     v.PaymentType,
				ArrivalDate:     v.ArrivalDate,
				BankAccount:     v.BankAccount,
				AgentIncome:     v.AgentIncome,
				AfterTax:        v.AfterTax,
				IncomeCompany:   v.IncomeCompany,
				ServiceFee:      v.ServiceFee,
			}
			data = append(data, tmp)
		}
		//todo 签署协议时间查询
		err = st1.FillData(data)
	}
	return
}

func setCompanyAndStaff(c context.Context, list []*fenghe.SeriesOrderData) {

	companyMap := make(map[uint32]string)
	staffMap := make(map[uint32]string)
	existStaffMap := make(map[uint32]struct{})
	var staffIds []uint64

	syncList, _ := service.DepartmentProvider.BaseSyncList(c, &department.BaseAllRequest{})

	for _, v := range syncList.Data {
		companyMap[uint32(v.ID)] = v.Name
	}

	for _, v := range list {
		if v.StaffId != 0 {
			if _, isExist := existStaffMap[v.StaffId]; !isExist {
				staffIds = append(staffIds, uint64(v.StaffId))
			}
		}
	}

	infos, _ := service.AccountProvider.ListByIDs(c, &account.ListByIDsRequest{IDs: staffIds})

	for _, v := range infos.Data {
		staffMap[uint32(v.ID)] = v.NickName
	}

	for k, v := range list {
		if v.CompanyId != 0 {
			if _, isE := companyMap[v.CompanyId]; isE {
				list[k].CompanyName = companyMap[v.CompanyId]
			}
		}
		if v.StaffId != 0 {
			if _, isE := staffMap[v.StaffId]; isE {
				list[k].StaffName = staffMap[v.StaffId]
			}
		}
	}
}

func CultureOrderCreate(c *gin.Context) {
	var req fenghe.CreateCultureSeriesOrderRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		service.Error(c, e.InvalidParams, err, "无效参数")
		return
	}
	userInfo := utils.GetJwtUser(c)
	req.Operator = userInfo.NickName
	req.OperatorTel = userInfo.TelNum
	req.OperatorId = uint32(userInfo.ID)
	req.OrderNo = generateOrderNo("culture")
	_, err := service.FengheProvider.CreateCultureSeriesOrder(c, &req)
	if err != nil {
		service.Error(c, e.Error, err)
		return
	}
	service.Success(c)
}

func UpdateShoeSeriesAccountEntry(c *gin.Context) {
	var req fenghe.SeriesOrderData
	if err := c.ShouldBindJSON(&req); err != nil {
		service.Error(c, e.InvalidParams, err, "无效参数")
		return
	}
	if req.OrderNo == "" {
		service.Error(c, e.InvalidParams, nil, "无效参数")
		return
	}
	thisOrder, err := service.FengheProvider.GetSeriesOrderDetail(c, &fenghe.GetSeriesOrderByIdRequest{OrderNo: req.OrderNo})
	if err != nil {
		service.Error(c, e.Error, err)
		return
	}
	thisOrder.AgentIncome = req.AgentIncome
	thisOrder.AfterTax = req.AfterTax
	thisOrder.ServiceFee = req.ServiceFee
	_, err = service.FengheProvider.UpdateSeriesOrder(c, &req)
	if err != nil {
		service.Error(c, e.Error, err)
		return
	}
	service.Success(c)
}

func CultureOrderExport(c *gin.Context) {
	var req dto.OrderExportReq
	if err := c.ShouldBindQuery(&req); err != nil {
		service.Error(c, e.InvalidParams, err, "无效参数")
		return
	}
	path, fileName, err := cultureOrderExportLogic(req.SeriesUid)
	if err != nil {
		service.Error(c, e.Error, err)
		return
	}
	c.Header("Content-Description", "File Transfer")
	c.Header("Content-Type", "application/vnd.openxmlformats-officedocument.wordprocessingml.document")
	c.Header("Content-Disposition", "attachment; filename="+fileName)
	c.File(path)
}

type CultureExportOrderType struct {
	OrderNo               string `json:"orderNo"`
	Status                string `json:"status"`
	Amount                string `json:"amount"`
	Currency              string `json:"currency"`
	ActualPaymentAmount   string `json:"actualPaymentAmount"`
	ActualPaymentcurrency string `json:"actualPaymentcurrency"`
	PayType               string `json:"payType"`
	OperatorTime          string `json:"operatorTime"`
	Operator              string `json:"operator"`
}

func cultureOrderExportLogic(seriesUid string) (path string, fileName string, err error) {
	resp, err := service.FengheProvider.GetSeriesOrderList(context.TODO(), &fenghe.GetSeriesOrderListRequest{
		Query: &fenghe.SeriesOrderData{
			SeriesUid: seriesUid,
		},
		Page:     1,
		PageSize: 99999,
	})
	if err != nil {
		return "", "", err
	}

	fileDir := os.TempDir()
	fileName = "文创销售情况数据导出.xlsx"
	path = filepath.Join(fileDir, fileName)
	ex := exportToExcel.NewExcel(fileDir, fileName)
	defer func() {
		if err == nil {
			if err = ex.Save(); err != nil {
				fmt.Println("save file err:", err)
				return
			}
		}
	}()
	st1 := ex.NewSheet("sheet1", CultureExportOrderType{})
	err = st1.Title.Gen(
		st1.Title.NewTitleItem(4, "订单号", 1, 1),
		st1.Title.NewTitleItem(5, "付款状态", 1, 2),
		st1.Title.NewTitleItem(5, "应付金额", 1, 3),
		st1.Title.NewTitleItem(5, "应付币种", 1, 4),
		st1.Title.NewTitleItem(5, "实付金额", 1, 5),
		st1.Title.NewTitleItem(5, "现金币种", 1, 6),
		st1.Title.NewTitleItem(5, "支付类型", 1, 7),
		st1.Title.NewTitleItem(5, "操作时间", 1, 8),
		st1.Title.NewTitleItem(5, "操作人", 1, 9),
	)
	if err != nil {
		return
	}
	data := []CultureExportOrderType{}
	for _, v := range resp.List {
		var status string
		switch v.Status {
		case 1:
			status = "未支付"
		case 2:
			status = "支付成功"
		case 3:
			status = "支付失败"
		case 4:
			status = "部分支付"
		default:
			status = "未支付"
		}
		var currency string
		switch strings.ToLower(v.Currency) {
		case "cny", "cn":
			currency = "人民币"
		case "jpy", "jp":
			currency = "日元"
		case "usd", "us":
			currency = "美元"
		}
		var ActualPaymentcurrency string
		switch strings.ToLower(v.Currency) {
		case "cny", "cn":
			ActualPaymentcurrency = "人民币"
		case "jpy", "jp":
			ActualPaymentcurrency = "日元"
		case "usd", "us":
			ActualPaymentcurrency = "美元"
		}
		tmp := CultureExportOrderType{
			OrderNo:      v.OrderNo,
			Status:       status,
			Amount:       status,
			Currency:     currency,
			OperatorTime: v.CreatedAt,
			Operator:     v.UserName,
		}
		cultureAddition := dto.CultureAddition{}

		if v.CultureAddition != "" && json.Unmarshal([]byte(v.CultureAddition), &cultureAddition) == nil {
			tmp.ActualPaymentAmount = cultureAddition.ActualPaymentAmount
			tmp.ActualPaymentcurrency = ActualPaymentcurrency
			var payType string
			switch cultureAddition.PayType {
			case 1:
				payType = "现金"
			case 2:
				payType = "刷卡"
			case 3:
				payType = "扫码"
			}
			tmp.PayType = payType
		}
		data = append(data, tmp)
	}
	err = st1.FillData(data)
	return
}
