package handle

import (
	"errors"
	"io"

	"github.com/fonchain/utils/translation"

	"github.com/fonchain_enterprise/client-auction/pkg/service"
	"github.com/gin-gonic/gin"
)

func TranslationImage(ctx *gin.Context) {

	mf, err := ctx.MultipartForm()
	if err != nil {
		service.Error(ctx, 500, errors.New("上传失败"))
		return
	}
	if _, ok := mf.File["image"]; !ok || len(mf.File["image"]) == 0 {
		service.Error(ctx, 500, errors.New("上传失败"))
		return
	}
	f, err := mf.File["image"][0].Open()
	if err != nil {
		service.Error(ctx, 500, errors.New("上传失败"))
		return
	}
	defer f.Close()
	b, err := io.ReadAll(f)
	if err != nil {
		service.Error(ctx, 500, err)
		return
	}
	result, err := translation.NewImageTranslationConfig().
		SetImageDataBytes(b).
		SetFromTo(translation.TranslationLanguage(ctx.Query("from")), translation.TranslationLanguage(ctx.Query("to"))).
		Translation()
	if err != nil {
		service.Error(ctx, 500, err)
		return
	}
	service.Success(ctx, result)

	// fi, err := os.Create(strconv.Itoa(int(time.Now().Unix())) + ".mp3")
	// if err != nil {
	// 	service.Error(ctx, 500, errors.New("上传失败"))
	// 	return
	// }
	// defer fi.Close()
	// fi.Write(b)

}

func TranslationAudio(ctx *gin.Context) {
	mf, err := ctx.MultipartForm()
	if err != nil {
		service.Error(ctx, 500, errors.New("上传失败"))
		return
	}
	if _, ok := mf.File["audio"]; !ok || len(mf.File["audio"]) == 0 {
		service.Error(ctx, 500, errors.New("上传失败"))
		return
	}
	f, err := mf.File["audio"][0].Open()
	if err != nil {
		service.Error(ctx, 500, errors.New("上传失败"))
		return
	}
	defer f.Close()
	b, err := io.ReadAll(f)
	if err != nil {
		service.Error(ctx, 500, err)
		return
	}

	result, err := translation.NewAudioTranslationConfig().
		SetAudioDataBytes(b).
		SetFromTo(translation.TranslationLanguage(ctx.Query("from")), translation.TranslationLanguage(ctx.Query("to"))).
		SetAudioFormat(translation.TranslationAudioFormat(ctx.Query("type"))).
		Translation()
	if err != nil {
		service.Error(ctx, 500, err)
		return
	}
	service.Success(ctx, result)
}

func TranslationText(ctx *gin.Context) {
	result, err := translation.TextTranslation(ctx.Query("text"), translation.TranslationLanguage(ctx.Query("from")), translation.TranslationLanguage(ctx.Query("to")))
	if err != nil {
		service.Error(ctx, 500, err)
		return
	}
	service.Success(ctx, result)
}
