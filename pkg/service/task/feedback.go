package task

import (
	"github.com/fonchain_enterprise/client-auction/api/task"
	"github.com/fonchain_enterprise/client-auction/pkg/e"
	"github.com/fonchain_enterprise/client-auction/pkg/model/login"
	"github.com/fonchain_enterprise/client-auction/pkg/service"
	"github.com/gin-gonic/gin"
	"github.com/gin-gonic/gin/binding"
)

func FeedBackCreate(c *gin.Context) {
	req := &task.TaskRequest{}
	req.Domain = "mall"
	if err := c.ShouldBindBodyWith(&req, binding.JSON); err != nil {
		service.Error(c, e.InvalidParams, err)
		return
	}

	userInfo := login.GetUserInfoFromC(c)
	req.SubmitterID = userInfo.ID
	req.SubmitterName = userInfo.NickName
	req.Type = e.Task_Type_Feedback

	res, err := service.TaskProvider.Create(c, req)
	if err != nil {
		service.Error(c, e.Error, err)
		return
	}

	service.Success(c, res)

	return
}

func Topping(c *gin.Context) {
	req := &task.TaskDetail{}
	req.Domain = "mall"
	if err := c.ShouldBindBodyWith(&req, binding.JSON); err != nil {
		service.Error(c, e.InvalidParams, err)
		return
	}
	res, err := service.TaskProvider.Topping(c, req)
	if err != nil {
		service.Error(c, e.Error, err)
		return
	}

	service.Success(c, res)
	return
}

func FeedBackList(c *gin.Context) {
	req := &task.TaskList{}
	req.Domain = "mall"
	if err := c.ShouldBindBodyWith(&req, binding.JSON); err != nil {
		service.Error(c, e.InvalidParams, err)
		return
	}

	req.Type = e.Task_Type_Feedback

	res, err := service.TaskProvider.List(c, req)

	if err != nil {
		service.Error(c, e.Error, err)
		return
	}

	service.Success(c, res)
	return
}
