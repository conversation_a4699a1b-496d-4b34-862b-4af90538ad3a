package shop

import (
	"encoding/json"
	"fmt"
	"github.com/fonchain_enterprise/client-auction/api/account"
	backendSeries "github.com/fonchain_enterprise/client-auction/api/series"
	"github.com/fonchain_enterprise/client-auction/api/shopbrand"
	"github.com/fonchain_enterprise/client-auction/pkg/e"
	"github.com/fonchain_enterprise/client-auction/pkg/middleware"
	"github.com/fonchain_enterprise/client-auction/pkg/model/query"
	"github.com/fonchain_enterprise/client-auction/pkg/service"
	"github.com/gin-gonic/gin"
)

// CultureList TODO 文创列表
func CultureList(c *gin.Context) {
	var req backendSeries.CultureArtworkListReq
	if err := c.ShouldBind(&req); err != nil {
		service.Error(c, e.Error, err)
		return
	}

	res, err := service.BackendSeriesProvider.CultureArtworkList(c.Request.Context(), &req)
	if err != nil {
		service.Error(c, e.Error, err)
		return
	}
	service.Success(c, res)
	return
}

// ScanInfo TODO
func ScanInfo(c *gin.Context) {
	var req shopbrand.CreateBrandreq
	if err := c.ShouldBind(&req); err != nil {
		service.Error(c, e.Error, err)
		return
	}

	res, err := service.BrandProvider.CreateBrand(c.Request.Context(), &req)
	if err != nil {
		service.Error(c, e.Error, err)
		return
	}
	service.Success(c, res)
	return
}

/**

userRouter.POST("list", shop.UserCultureList) //两个接口合二为一 文创我的列表 外加一个订单个数
userRouter.POST("transfer/list", shop.TransferList) //流转记录列表
userRouter.POST("order/detail", shop.CultureDetail) //我的文创订单详情
userRouter.POST("order/detail", shop.TransferCulture) //我的订单 转赠接口
userRouter.POST("order/detail", shop.DoTransferCulture) //赠送的 确认和退回接口
*/

// UserCultureList TODO
func UserCultureList(c *gin.Context) {
	var req backendSeries.CultureArtworkOrderListReq
	if err := c.ShouldBind(&req); err != nil {
		service.Error(c, e.Error, err)
		return
	}

	userInfoAny, _ := c.Get("jwtInfo")
	userInfo := userInfoAny.(middleware.LoginInfo)
	req.UserId = uint32(userInfo.ID)

	res, err := service.BackendSeriesProvider.CultureArtworkOrderList(c.Request.Context(), &req)

	if err != nil {
		service.Error(c, e.Error, err)
		return
	}

	var ids []uint64
	for _, v := range res.List {
		if v.NowUserId != 0 && v.NowUserName == "" {
			ids = append(ids, uint64(v.NowUserId))
		}
	}

	if len(ids) >= 1 {
		userRes, err := service.AccountProvider.ListByIDs(c.Request.Context(), &account.ListByIDsRequest{IDs: ids})
		if err != nil {
			service.Error(c, e.Error, err)
			return
		}
		if userRes != nil || len(userRes.Data) > 0 {
			userMap := make(map[uint32]string, len(userRes.Data))
			for _, v := range userRes.Data {
				userMap[uint32(v.ID)] = v.NickName
			}

			for k, v := range res.List {
				if v.NowUserId != 0 {
					if v.NowUserName == "" {
						if name, ok := userMap[v.NowUserId]; ok {
							res.List[k].NowUserName = name
						}
					}
				}
			}
		}

	}

	service.Success(c, res)
	return
}

// FansCirculationList 流转列表
func FansCirculationList(c *gin.Context) {
	var req backendSeries.CirculationListReq
	if err := c.ShouldBind(&req); err != nil {
		service.Error(c, e.Error, err)
		return
	}

	userInfoAny, _ := c.Get("jwtInfo")
	userInfo := userInfoAny.(middleware.LoginInfo)
	req.UserId = uint32(userInfo.ID)

	res, err := service.BackendSeriesProvider.FansCirculationList(c.Request.Context(), &req)
	if err != nil {
		service.Error(c, e.Error, err)
		return
	}
	service.Success(c, res)
	return
}

// CirculationList 流转列表
func CirculationList(c *gin.Context) {
	var req backendSeries.CirculationListReq
	if err := c.ShouldBind(&req); err != nil {
		service.Error(c, e.Error, err)
		return
	}

	res, err := service.BackendSeriesProvider.CirculationList(c.Request.Context(), &req)
	if err != nil {
		service.Error(c, e.Error, err)
		return
	}
	service.Success(c, res)
	return
}

// CultureDetail TODO
func CultureDetail(c *gin.Context) {
	var req backendSeries.CultureArtworkOrderDetailReq
	if err := c.ShouldBind(&req); err != nil {
		service.Error(c, e.Error, err)
		return
	}

	res, err := service.BackendSeriesProvider.CultureArtworkOrderDetail(c.Request.Context(), &req)
	if err != nil {
		service.Error(c, e.Error, err)
		return
	}

	if res.NowUserId != 0 {
		userInfo, err := service.AccountProvider.UserInfoById(c.Request.Context(), &account.InfoRequest{ID: uint64(res.NowUserId)})
		if err == nil && userInfo != nil {
			res.NowUserName = userInfo.NickName
		}

	}

	service.Success(c, res)
	return
}

func DrawCulture(c *gin.Context) {
	var req backendSeries.DrawCultureArtworkReq
	if err := c.ShouldBind(&req); err != nil {
		service.Error(c, e.Error, err)
		return
	}

	userInfoAny, _ := c.Get("jwtInfo")
	userInfo := userInfoAny.(middleware.LoginInfo)
	req.UserId = uint32(userInfo.ID)
	req.UserTelNum = userInfo.TelNum

	res, err := service.BackendSeriesProvider.DrawCultureArtwork(c.Request.Context(), &req)
	if err != nil {
		service.Error(c, e.Error, err)
		return
	}
	service.Success(c, res)
	return
}

// RefuseCultureArtworkOrder 确认收入
func RefuseCultureArtworkOrder(c *gin.Context) {
	var reqObj query.ClientWsReq
	if err := c.ShouldBind(&reqObj); err != nil {
		service.Error(c, e.Error, err)
		return
	}

	req := backendSeries.ReceiveCultureArtworkOrderReq{OrderUuid: reqObj.Uuid}
	userInfoAny, _ := c.Get("jwtInfo")
	userInfo := userInfoAny.(middleware.LoginInfo)
	req.To = uint32(userInfo.ID)

	res, err := service.BackendSeriesProvider.RefuseCultureArtworkOrder(c.Request.Context(), &req)
	if err != nil {
		service.Error(c, e.Error, err)
		return
	}
	service.Success(c, res)
	return
}

// ReceiveCultureArtworkOrder 确认收入
func ReceiveCultureArtworkOrder(c *gin.Context) {
	var reqObj query.ClientWsReq
	if err := c.ShouldBind(&reqObj); err != nil {
		service.Error(c, e.Error, err)
		return
	}

	req := backendSeries.ReceiveCultureArtworkOrderReq{OrderUuid: reqObj.Uuid}
	userInfoAny, _ := c.Get("jwtInfo")
	userInfo := userInfoAny.(middleware.LoginInfo)
	req.To = uint32(userInfo.ID)
	req.ToName = userInfo.NickName

	res, err := service.BackendSeriesProvider.ReceiveCultureArtworkOrder(c.Request.Context(), &req)
	if err != nil {
		service.Error(c, e.Error, err)
		return
	}

	service.Success(c, res)
	return
}

// TransferCulture 转增接口
func TransferCulture(c *gin.Context) {
	var reqObj query.GiveCultureArtworkOrderReq
	if err := c.ShouldBind(&reqObj); err != nil {
		service.Error(c, e.Error, err)
		return
	}

	userInfoAny, _ := c.Get("jwtInfo")
	userInfo := userInfoAny.(middleware.LoginInfo)

	if reqObj.Zone != e.ZoneCn && reqObj.Zone != "" {
		reqObj.ToUserTel = reqObj.Zone + reqObj.ToUserTel
	}

	res, err := service.AccountProvider.UserByTel(c, &account.UserByTelRequest{Tel: reqObj.ToUserTel})

	if err != nil {
		service.Error(c, e.Error, err)
		return
	}

	if !res.IsExist {
		service.Error(c, e.Error, fmt.Errorf("转增失败，手机号未注册“豊和”"))
		return
	}

	if res.Info == nil {
		service.Error(c, e.Error, fmt.Errorf("转增失败，手机号未注册“豊和”"))
		return
	}

	s, _ := json.Marshal(res)
	fmt.Println("----------------", res.Info.UserExtend)
	fmt.Println("----------------", string(s))
	if res.Info.UserExtend != nil && res.Info.UserExtend.PassStatus != 2 {
		service.Error(c, e.Error, fmt.Errorf("转增失败，该手机号需要实名"))
		return
	}

	fmt.Println("1----------------", res)
	req := backendSeries.GiveCultureArtworkOrderReq{
		From:      uint32(userInfo.ID),
		OrderUuid: reqObj.OrderUuid,
		To:        uint32(res.Info.ID),
		ToName:    res.Info.NickName,
		FromName:  userInfo.NickName,
	}

	resp, err1 := service.BackendSeriesProvider.GiveCultureArtworkOrder(c.Request.Context(), &req)
	if err1 != nil {
		service.Error(c, e.Error, err1)
		return
	}
	service.Success(c, resp)
	return
}

// DoTransferCulture TODO
func DoTransferCulture(c *gin.Context) {
	var req shopbrand.CreateBrandreq
	if err := c.ShouldBind(&req); err != nil {
		service.Error(c, e.Error, err)
		return
	}

	res, err := service.BrandProvider.CreateBrand(c.Request.Context(), &req)
	if err != nil {
		service.Error(c, e.Error, err)
		return
	}
	service.Success(c, res)
	return
}
