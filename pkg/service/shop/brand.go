package shop

import (
	"dubbo.apache.org/dubbo-go/v3/common/logger"
	"fmt"
	"github.com/fonchain_enterprise/client-auction/api/shopbrand"
	"github.com/fonchain_enterprise/client-auction/pkg/common"
	"github.com/fonchain_enterprise/client-auction/pkg/e"
	"github.com/fonchain_enterprise/client-auction/pkg/logic"
	"github.com/fonchain_enterprise/client-auction/pkg/model/query"
	"github.com/fonchain_enterprise/client-auction/pkg/service"
	"github.com/gin-gonic/gin"
)

func CreateBrand(c *gin.Context) {
	var req shopbrand.CreateBrandreq
	if err := c.ShouldBind(&req); err != nil {
		logger.Errorf("CreateBrand ShouldBind err", err)
		service.ResponseQuickMsg(c, e.Failed, err.Error(), nil)
		return
	}
	if err := req.Validate(); err != nil {
		err = common.SubstrError(err)
		service.ResponseQuickMsg(c, e.Failed, err.Error(), nil)
		return
	}

	resp, err := service.BrandProvider.CreateBrand(c.Request.Context(), &req)
	if err != nil {
		service.ResponseQuickMsg(c, e.Failed, err.Error(), nil)
		return
	}
	service.ResponseQuickMsg(c, e.Ok, resp.Msg, map[string]interface{}{
		"brandUid": resp.BrandUid,
	})
	return
}

func BrandInfo(c *gin.Context) {
	var req shopbrand.BrandInforeq
	if err := c.ShouldBind(&req); err != nil {
		logger.Errorf("BrandInfo ShouldBind err", err)
		service.ResponseQuickMsg(c, e.Failed, err.Error(), nil)
		return
	}
	if err := req.Validate(); err != nil {
		err = common.SubstrError(err)
		service.ResponseQuickMsg(c, e.Failed, err.Error(), nil)
		return
	}
	resp, err := service.BrandProvider.BrandInfo(c.Request.Context(), &req)
	if err != nil {
		service.ResponseQuickMsg(c, e.Failed, err.Error(), nil)
		return
	}
	service.Success(c, resp)
	return
}

func BrandUpdate(c *gin.Context) {
	var req shopbrand.UpdateBrandreq
	if err := c.ShouldBind(&req); err != nil {
		logger.Errorf("BrandUpdate ShouldBind err", err)
		service.ResponseQuickMsg(c, e.Failed, err.Error(), nil)
		return
	}
	if err := req.Validate(); err != nil {
		err = common.SubstrError(err)
		service.ResponseQuickMsg(c, e.Failed, err.Error(), nil)
		return
	}
	resp, err := service.BrandProvider.UpdateBrand(c.Request.Context(), &req)
	if err != nil {
		service.ResponseQuickMsg(c, e.Failed, err.Error(), nil)
		return
	}
	service.Success(c, resp)
	return
}

func BrandDel(c *gin.Context) {
	var req shopbrand.DeleteBrandreq
	if err := c.ShouldBind(&req); err != nil {
		logger.Errorf("BrandUpdate ShouldBind err", err)
		service.ResponseQuickMsg(c, e.Failed, err.Error(), nil)
		return
	}
	if err := req.Validate(); err != nil {
		err = common.SubstrError(err)
		service.ResponseQuickMsg(c, e.Failed, err.Error(), nil)
		return
	}
	resp, err := service.BrandProvider.DeleteBrand(c.Request.Context(), &req)
	if err != nil {
		service.ResponseQuickMsg(c, e.Failed, err.Error(), nil)
		return
	}
	service.Success(c, resp)
	return
}

func BrandList(c *gin.Context) {
	var req shopbrand.BrandListreq
	if err := c.ShouldBind(&req); err != nil {
		logger.Errorf("BrandList ShouldBind err", err)
		service.ResponseQuickMsg(c, e.Failed, err.Error(), nil)
		return
	}
	if err := req.Validate(); err != nil {
		err = common.SubstrError(err)
		service.ResponseQuickMsg(c, e.Failed, err.Error(), nil)
		return
	}
	resp, err := service.BrandProvider.BrandList(c.Request.Context(), &req)
	if err != nil {
		service.ResponseQuickMsg(c, e.Failed, err.Error(), nil)
		return
	}
	service.Success(c, resp)
	return
}

func AllBrand(c *gin.Context) {
	var req shopbrand.AllBrandreq
	if err := c.ShouldBind(&req); err != nil {
		logger.Errorf("AllBrand ShouldBind err", err)
		service.ResponseQuickMsg(c, e.Failed, err.Error(), nil)
		return
	}
	req.PageSize = 9999
	resp, err := service.BrandProvider.AllBrand(c.Request.Context(), &req)
	if err != nil {
		service.ResponseQuickMsg(c, e.Failed, err.Error(), nil)
		return
	}
	fmt.Println(resp)
	service.Success(c, resp)
	return
}

func UpdateLanguageInfo(c *gin.Context) {
	var req shopbrand.UpdateLanguageInfoReq
	if err := c.ShouldBind(&req); err != nil {
		logger.Errorf("UpdateLanguageInfoReq ShouldBind err", err)
		service.ResponseQuickMsg(c, e.Failed, err.Error(), nil)
		return
	}
	resp, err := service.BrandProvider.UpdateLanguageInfo(c.Request.Context(), &req)
	if err != nil {
		service.ResponseQuickMsg(c, e.Failed, err.Error(), nil)
		return
	}
	service.Success(c, resp)
	return
}

func CurrencyInfo(c *gin.Context) {
	var req query.CurrencyReq
	if err := c.ShouldBind(&req); err != nil {
		logger.Errorf("GetAllLanguageInfoReq ShouldBind err", err)
		service.ResponseQuickMsg(c, e.Failed, err.Error(), nil)
		return
	}

	rate, err := logic.GetAllCurrencyPrice(req.Currency, req.Price)
	if err != nil {
		logger.Errorf("GetAllLanguageInfoReq ShouldBind err", err)
		service.Error(c, e.Failed, err)
		return
	}

	service.Success(c, rate)
	return
}

func GetAllLanguageInfo(c *gin.Context) {
	var req shopbrand.GetAllLanguageInfoReq
	fmt.Println("1-1-----------")
	fmt.Println("1-1-----------")
	fmt.Println("1-1-----------")
	fmt.Println("1-1-----------")
	if err := c.ShouldBind(&req); err != nil {
		logger.Errorf("GetAllLanguageInfoReq ShouldBind err", err)
		service.ResponseQuickMsg(c, e.Failed, err.Error(), nil)
		return
	}
	fmt.Println("1--1-12-")
	resp, err := service.BrandProvider.GetAllLanguageInfo(c.Request.Context(), &req)
	fmt.Println("1---", resp, err)
	if err != nil {
		service.ResponseQuickMsg(c, e.Failed, err.Error(), nil)
		return
	}
	service.Success(c, resp)
	return
}
