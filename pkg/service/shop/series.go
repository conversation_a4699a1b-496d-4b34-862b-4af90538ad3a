package shop

import (
	"bytes"
	"context"
	"dubbo.apache.org/dubbo-go/v3/common/logger"
	"encoding/json"
	"fmt"
	"github.com/fonchain_enterprise/client-auction/api/artwork_query"
	"github.com/fonchain_enterprise/client-auction/api/department"
	"github.com/fonchain_enterprise/client-auction/api/fenghe"
	backendSeries "github.com/fonchain_enterprise/client-auction/api/series"
	"github.com/fonchain_enterprise/client-auction/api/shopbrand"
	"github.com/fonchain_enterprise/client-auction/cmd/config"
	"github.com/fonchain_enterprise/client-auction/pkg/common"
	"github.com/fonchain_enterprise/client-auction/pkg/common/utils"
	"github.com/fonchain_enterprise/client-auction/pkg/common/utils/qr"
	"github.com/fonchain_enterprise/client-auction/pkg/e"
	"github.com/fonchain_enterprise/client-auction/pkg/model/vo"
	"github.com/fonchain_enterprise/client-auction/pkg/service"
	"github.com/fonchain_enterprise/client-auction/pkg/utils/secret"
	"github.com/gin-gonic/gin"
	"github.com/jinzhu/copier"
	"github.com/skip2/go-qrcode"
	"io"
	"io/ioutil"
	"math/rand"
	"net/http"
	"os"
	"strings"
	"time"
)

func SaveSeries(c *gin.Context) {
	var req SaveSeriesReq
	companyMap := make(map[string]uint32)
	if err := c.ShouldBind(&req); err != nil {
		logger.Errorf("Create ShouldBind err", err)
		service.ResponseQuickMsg(c, e.Failed, err.Error(), nil)
		return
	}

	if err := req.Validate(); err != nil {
		err = common.SubstrError(err)
		service.ResponseQuickMsg(c, e.Failed, err.Error(), nil)
		return
	}
	//先找画作是否存在,不存在的需要从画作系统取数据
	{
		req.Artworks = nil //前端会传这个导致更新失败，清空
		artworkUidList := []string{}
		for _, v := range req.ArtworkList {
			v := v
			artworkUidList = append(artworkUidList, v.ArtworkUid)
			companyMap[v.ArtworkUid] = v.CompanyId
		}
		seriesArtworkList, err := service.FengheProvider.GetSeriesArtworkList(c.Request.Context(), &fenghe.GetSeriesArtworkListRequest{
			Where:    fmt.Sprintf("artwork_uid in (%v)", "'"+strings.Join(artworkUidList, "','")+"'"),
			Page:     1,
			PageSize: int64(len(artworkUidList)),
		})
		if err != nil {
			service.ResponseQuickMsg(c, e.Failed, err.Error(), nil)
			return
		}
		existArtworkUidList := []string{}
		for _, v := range seriesArtworkList.List {
			v := v
			existArtworkUidList = append(existArtworkUidList, v.ArtworkUid)
			temp := &backendSeries.SeriesArtworkData{
				ID:          v.ID,
				ArtworkId:   v.ArtworkId,
				ArtworkUid:  v.ArtworkUid,
				Tfnum:       v.Tfnum,
				ArtworkName: v.ArtworkName,
				HdPic:       v.HdPic,
				Ruler:       v.Ruler,
				Length:      v.Length,
				Width:       v.Width,
				ArtistName:  v.ArtistName,
				ArtistUid:   v.ArtistUid,
				Abstract:    v.Abstract,
				Tnum:        v.Tnum,
			}

			if _, isExist := companyMap[v.ArtworkUid]; isExist {
				temp.CompanyId = companyMap[v.ArtworkUid]
			}

			req.SaveSeriesReq.Artworks = append(req.SaveSeriesReq.Artworks, temp)
		}
		fmt.Println("req.ArtworkUidList:", artworkUidList)
		fmt.Println("existArtworkUidList:", existArtworkUidList)
		findArtworkUidList := utils.Difference(artworkUidList, existArtworkUidList...)
		fmt.Println("findArtworkUidList:", findArtworkUidList)
		if len(findArtworkUidList) > 0 {
			var artworkReq = artwork_query.GetArtworkProfileListRequest{
				Page:     1,
				PageSize: int64(len(findArtworkUidList)),
				Select: []string{
					"id", "uuid", "tfnum", "hd_pic", "ruler", "length", "width", "name", "artist_name", "artist_uuid", "abstract",
				},
				Where: fmt.Sprintf("uuid in (%s)", "'"+strings.Join(findArtworkUidList, "','")+"'"),
			}
			res, errs := service.ArtworkQueryProvider.GetArtworkProfileList(c, &artworkReq)
			if errs != nil {
				service.ResponseQuickMsg(c, e.Failed, errs.Error(), nil)
				return
			}
			//fmt.Printf("artwork query res :%+v\n", res)
			//fmt.Printf("artwork query resTotal :%d\n", res.Total)
			//fmt.Printf("artwork query len :%d\n", len(res.List))
			for _, artwork := range res.List {
				artwork := artwork

				temp := &backendSeries.SeriesArtworkData{
					ArtworkId:   int64(artwork.Id),
					ArtworkUid:  artwork.Uuid,
					Tfnum:       artwork.Tfnum,
					ArtworkName: artwork.Name,
					HdPic:       artwork.HdPic,
					Ruler:       artwork.Ruler,
					Length:      artwork.Length,
					Width:       artwork.Width,
					ArtistName:  artwork.ArtistName,
					ArtistUid:   artwork.ArtistUuid,
					Abstract:    artwork.Abstract,
					//Tnum:        artwork.Tnum,//todo 没有这个字段
				}

				if _, isExist := companyMap[temp.ArtworkUid]; isExist {
					temp.CompanyId = companyMap[temp.ArtworkUid]
				}

				req.SaveSeriesReq.Artworks = append(req.SaveSeriesReq.Artworks, temp)
			}
		}
		if len(req.SaveSeriesReq.Artworks) != len(artworkUidList) {
			service.ResponseQuickMsg(c, e.Failed, "部分艺术品不存在", nil)
			return
		}
		//排序,画作需要按请求参数中的位置排序
		newArtworks := []*backendSeries.SeriesArtworkData{}
		for _, v := range req.ArtworkList {
			for _, vv := range req.SaveSeriesReq.Artworks {
				if v.ArtworkUid == vv.ArtworkUid {
					vv := vv
					vv.Price = v.Price
					vv.Currency = v.Currency
					newArtworks = append(newArtworks, vv)
					break
				}
			}
		}
		req.SaveSeriesReq.Artworks = newArtworks
	}

	resp, err := service.BackendSeriesProvider.SaveSeries(c.Request.Context(), &req.SaveSeriesReq)
	if err != nil {
		service.ResponseQuickMsg(c, e.Failed, err.Error(), nil)
		return
	}

	//保存
	if req.SaveSeriesReq.ActionCode == "auction" && req.SaveSeriesReq.Auction != nil {
		var langReq fenghe.AuctionRequest
		err = copier.Copy(&langReq, &req.SaveSeriesReq.Auction)
		if err != nil {
			service.ResponseQuickMsg(c, e.Failed, err.Error(), nil)
			return
		}
		langReq.Lang = req.Lang

		_, err = service.FengheProvider.UpdateAuctionLang(c.Request.Context(), &langReq)
		if err != nil {
			service.ResponseQuickMsg(c, e.Failed, err.Error(), nil)
			return
		}
	}
	go service.FengheProvider.InitSeriesProfileCache(c, &fenghe.CommonReq{})
	service.Success(c, resp)
	return
}

// SaveCultureArtworkBefore use memeoery to save culture,it is save database
func SaveCultureArtworkBefore(c *gin.Context) {
	var req backendSeries.SeriesCultureArtwork
	if err := c.ShouldBind(&req); err != nil {
		service.Error(c, e.ERROR, err)
		return
	}

	res, err := service.BackendSeriesProvider.SaveCulturalArtwork(c, &req)
	if err != nil {
		service.Error(c, e.ERROR, err)
		return
	}

	//临时存下redis即可
	service.Success(c, res)
	return
}

func SeriesDetail(c *gin.Context) {
	var req backendSeries.SeriesDetailReq
	if err := c.ShouldBind(&req); err != nil {
		logger.Errorf("SeriesDetailReq ShouldBind err", err)
		service.ResponseQuickMsg(c, e.Failed, err.Error(), nil)
		return
	}
	if err := req.Validate(); err != nil {
		err = common.SubstrError(err)
		service.ResponseQuickMsg(c, e.Failed, err.Error(), nil)
		return
	}
	resp, err := service.BackendSeriesProvider.SeriesDetail(c.Request.Context(), &req)
	if err != nil {
		service.ResponseQuickMsg(c, e.Failed, err.Error(), nil)
		return
	}

	if resp.ActionCode == "auction" && resp.AuctionUuid != "" {
		detailReq := fenghe.AuctionDetail{Uuid: resp.AuctionUuid, Lang: req.Lang}

		res, err := service.FengheProvider.DetailAuction(c.Request.Context(), &detailReq)

		if err != nil {
			service.ResponseQuickMsg(c, e.Failed, err.Error(), nil)
			return
		}
		fmt.Println(res.AuctionArtwork)
		auctionResp := &backendSeries.AuctionRequest{}
		err = copier.Copy(&auctionResp, &res)
		fmt.Println(err, auctionResp)
		resp.Auction = auctionResp

	}

	//优化
	if resp.Artworks == nil {
		resp.Artworks = []*backendSeries.SeriesArtworkData{}
	} else if req.Lang == "ZhCN" {
		//画作中文与画作系统同步
		for i, a := range resp.Artworks {
			artworkProfileRes, err := service.ArtworkQueryProvider.GetArtworkProfileList(c, &artwork_query.GetArtworkProfileListRequest{
				Query:    &artwork_query.ArtworkProfileData{Uuid: a.ArtworkUid},
				Page:     1,
				PageSize: 1,
				Select:   []string{"uuid", "name", "hd_pic", "tfnum", "artist_name", "artist_uuid", "abstract", "width", "length", "ruler"},
			})
			if err != nil {
				service.Error(c, e.Failed, err, "查询失败")
				return
			}
			//中文与画作系统同步
			if artworkProfileRes.Total > 0 {
				resp.Artworks[i].ArtworkName = artworkProfileRes.List[0].Name
				resp.Artworks[i].HdPic = artworkProfileRes.List[0].HdPic
				resp.Artworks[i].Ruler = artworkProfileRes.List[0].Ruler
				resp.Artworks[i].Length = artworkProfileRes.List[0].Length
				resp.Artworks[i].Width = artworkProfileRes.List[0].Width
				resp.Artworks[i].ArtistName = artworkProfileRes.List[0].ArtistName
				resp.Artworks[i].Abstract = artworkProfileRes.List[0].Abstract
			}
		}
	}

	if len(resp.Artworks) > 0 {
		companyMap := make(map[uint32]string)

		artworkProfileRes, _ := service.DepartmentProvider.BaseAll(c, &department.BaseAllRequest{})
		for _, v := range artworkProfileRes.Data {
			companyMap[uint32(v.ID)] = v.Name
		}
		for k, a := range resp.Artworks {
			if _, isExist := companyMap[a.CompanyId]; isExist {
				resp.Artworks[k].CompanyName = companyMap[a.CompanyId]
			}
		}

	}

	service.Success(c, resp)

	return
}

func UpdateSeriesLang(c *gin.Context) {
	var req backendSeries.UpdateSeriesLangReq
	if err := c.ShouldBind(&req); err != nil {
		logger.Errorf("UpdateSeriesLang ShouldBind err", err)
		service.ResponseQuickMsg(c, e.Failed, err.Error(), nil)
		return
	}
	if err := req.Validate(); err != nil {
		err = common.SubstrError(err)
		service.ResponseQuickMsg(c, e.Failed, err.Error(), nil)
		return
	}
	resp, err := service.BackendSeriesProvider.UpdateSeriesLang(c.Request.Context(), &req)
	if err != nil {
		service.ResponseQuickMsg(c, e.Failed, err.Error(), nil)
		return
	}
	//go service.FengheProvider.InitSeriesProfileCache(c, &fenghe.CommonReq{})
	service.Success(c, resp)
	return
}

// AutoShelf 设置自动上架
func AutoShelf(c *gin.Context) {
	var req backendSeries.AutoShelfReq
	if err := c.ShouldBind(&req); err != nil {
		logger.Errorf("AutoShelf ShouldBind err", err)
		service.ResponseQuickMsg(c, e.Failed, err.Error(), nil)
		return
	}
	if err := req.Validate(); err != nil {
		err = common.SubstrError(err)
		service.ResponseQuickMsg(c, e.Failed, err.Error(), nil)
		return
	}
	resp, err := service.BackendSeriesProvider.AutoShelf(c.Request.Context(), &req)
	if err != nil {
		service.ResponseQuickMsg(c, e.Failed, err.Error(), nil)
		return
	}
	// TODO
	//order.SynPaySeriesData(c, req.SeriesUuid)
	//panic("开发中")
	//service.ResponseQuickMsg(c, e.Ok, resp.Msg, nil)
	service.Success(c, resp)
	return
}

func HandShelf(c *gin.Context) {
	var req backendSeries.HandShelfReq
	if err := c.ShouldBind(&req); err != nil {
		logger.Errorf("AutoShelf ShouldBind err", err)
		service.ResponseQuickMsg(c, e.Failed, err.Error(), nil)
		return
	}
	if err := req.Validate(); err != nil {
		err = common.SubstrError(err)
		service.ResponseQuickMsg(c, e.Failed, err.Error(), nil)
		return
	}
	resp, err := service.BackendSeriesProvider.HandShelf(c.Request.Context(), &req)
	if err != nil {
		service.ResponseQuickMsg(c, e.Failed, err.Error(), nil)
		return
	}

	// TODO
	//order.SynPaySeriesData(c, req.SeriesUuid)
	//panic("开发中")

	//service.ResponseQuickMsg(c, e.Ok, resp.Msg, nil)
	service.Success(c, resp)
	return
}

func SeriesList(c *gin.Context) {
	var req backendSeries.SeriesListReq
	if err := c.ShouldBind(&req); err != nil {
		logger.Errorf("AutoShelf ShouldBind err", err)
		service.ResponseQuickMsg(c, e.Failed, err.Error(), nil)
		return
	}
	if err := req.Validate(); err != nil {
		err = common.SubstrError(err)
		service.ResponseQuickMsg(c, e.Failed, err.Error(), nil)
		return
	}

	_, err := service.BrandProvider.GetAllLanguageInfo(c.Request.Context(), &shopbrand.GetAllLanguageInfoReq{})
	if err != nil {
		service.ResponseQuickMsg(c, e.Failed, err.Error(), nil)
		return
	}

	resp, err := service.BackendSeriesProvider.SeriesList(c.Request.Context(), &req)
	if err != nil {
		service.ResponseQuickMsg(c, e.Failed, err.Error(), nil)
		return
	}

	service.Success(c, resp)
	return
}

func SeriesDel(c *gin.Context) {
	var req backendSeries.SeriesDelReq
	if err := c.ShouldBind(&req); err != nil {
		logger.Errorf("SeriesDel ShouldBind err", err)
		service.ResponseQuickMsg(c, e.Failed, err.Error(), nil)
		return
	}
	if err := req.Validate(); err != nil {
		err = common.SubstrError(err)
		service.ResponseQuickMsg(c, e.Failed, err.Error(), nil)
		return
	}
	resp, err := service.BackendSeriesProvider.SeriesDel(c.Request.Context(), &req)
	if err != nil {
		service.ResponseQuickMsg(c, e.Failed, err.Error(), nil)
		return
	}
	go service.FengheProvider.InitSeriesProfileCache(c.Request.Context(), &fenghe.CommonReq{})
	service.Success(c, resp)
	return
}

func GetSeriesLanguageInfo(c *gin.Context) {
	var req backendSeries.GetSeriesLanguageInfoReq
	if err := c.ShouldBind(&req); err != nil {
		logger.Errorf("GetAllLanguageInfoReq ShouldBind err", err)
		service.ResponseQuickMsg(c, e.Failed, err.Error(), nil)
		return
	}
	fmt.Println("1-----------------", req)

	resp, err := service.BackendSeriesProvider.GetSeriesLanguageInfo(c.Request.Context(), &req)
	if err != nil {
		service.ResponseQuickMsg(c, e.Failed, err.Error(), nil)
		return
	}
	service.Success(c, resp)
	return
}

// TranslateList 流转记录
func TranslateList(c *gin.Context) {
	var req backendSeries.GetSeriesLanguageInfoReq
	if err := c.ShouldBind(&req); err != nil {
		logger.Errorf("GetAllLanguageInfoReq ShouldBind err", err)
		service.ResponseQuickMsg(c, e.Failed, err.Error(), nil)
		return
	}

	resp, err := service.BackendSeriesProvider.GetSeriesLanguageInfo(c.Request.Context(), &req)
	if err != nil {
		service.ResponseQuickMsg(c, e.Failed, err.Error(), nil)
		return
	}
	service.Success(c, resp)
	return
}

func ScanList(c *gin.Context) {
	var req backendSeries.ScanListReq
	if err := c.ShouldBind(&req); err != nil {
		service.Error(c, e.Failed, err)
		return
	}

	res, err := service.BackendSeriesProvider.ScanList(context.Background(), &req)
	if err != nil {
		service.Error(c, e.Failed, err)
		return
	}
	service.Success(c, res)
	return
}

func ExportScanListExam(c *gin.Context) {
	type ll struct {
		Name     string
		Index    string
		ShopHash string
		Url      string
	}
	var data []interface{}
	var OrderColumnName = []string{"名称", "编号", "hash", "url"}

	var artworks []*backendSeries.SeriesCultureArtwork
	artworks = append(artworks, &backendSeries.SeriesCultureArtwork{Uuid: "5eb26701-af96-4478-ba57-c8b4fc6b47b1", StockNum: 120, Name: "算盘"})
	artworks = append(artworks, &backendSeries.SeriesCultureArtwork{Uuid: "2dd47312-f2a0-4bc2-b262-a268689f495d", StockNum: 120, Name: "米斗"})
	artworks = append(artworks, &backendSeries.SeriesCultureArtwork{Uuid: "dc5dd5c3-cc25-4961-90ed-68256bb5b981", StockNum: 150, Name: "印章"})

	for _, r := range artworks {
		for i := 1; i <= int(r.StockNum); i++ {
			temp := &ll{
				Name:  r.Name,
				Index: fmt.Sprintf("%d", i),
			}

			temp.ShopHash = utils.SHA256V([]byte(fmt.Sprintf("%s_%d", r.Hash, i)))

			cultureArtworkReq := vo.CultureArtworkReq{Uuid: r.Uuid, Num: i}
			s, _ := json.Marshal(cultureArtworkReq)

			str, err := secret.CombineSecret(string(s))
			if err != nil {
				return
			}
			tempUrl := fmt.Sprintf("%s/#/pages/modal3D/3d?sign=%s&time=%s&client=%s&nonce=%d", "",
				str,
				time.Now().Format(time.DateTime),
				utils.GenerateRandomString(5),
				rand.Intn(999999),
			)

			temp.Url = tempUrl

			data = append(data, temp)
		}
	}

	content := utils.ToExcel(OrderColumnName, data)
	file, err := os.Create("./藏品哈希.xlsx")
	if err != nil {
		fmt.Println("Failed to create file:", err)
		return
	}
	defer file.Close()
	_, err = io.Copy(file, content)
	if err != nil {
		fmt.Println("Failed to save file:", err)
		return
	}

}

func ExportScanList(c *gin.Context) {
	sereiesUuid := c.DefaultQuery("seriesUuid", "")
	//var req backendSeries.ScanListReq
	//fmt.Println("2--------------")

	fmt.Println("-1-------------")

	res, err := service.BackendSeriesProvider.ScanList(context.Background(), &backendSeries.ScanListReq{SeriesUuid: sereiesUuid, Page: 1, PageSize: 99999})
	if err != nil {
		service.Error(c, e.Failed, err)
		return
	}

	var data []interface{}
	var OrderColumnName = []string{"文创产品", "哈希值", "扫码时间", "手机号"}

	fmt.Println("1--2--")

	type ll struct {
		Name      string
		Hash      string
		CreatedAt string
		Phone     string
	}

	for _, r := range res.List {
		temp := ll{
			Name:      r.SeriesCulturalArtworkName,
			Hash:      r.Hash,
			CreatedAt: r.CreatedAt,
			Phone:     r.FirstUserTel,
		}

		data = append(data, &temp)
	}

	fmt.Println("1--3--")
	contentBytes := utils.ToExcelByte(OrderColumnName, data)
	contentReader := bytes.NewReader(contentBytes)
	fmt.Println("123--")

	// 设置响应头 xlsx
	fileName := fmt.Sprintf("扫码下载-%s-%d.xlsx", time.Now().Format(time.DateOnly), rand.Intn(999))
	c.Header("Content-Disposition", "attachment; filename="+fileName)
	c.Header("Content-Type", "text/csv")
	c.Header("Content-Length", string(len(contentBytes)))

	// 流式复制到响应体
	_, err = io.Copy(c.Writer, contentReader)
	if err != nil {
		service.Error(c, e.Failed, err)
		return
	}

	return
}

func Extend(c *gin.Context) {
	sereiesUuid := c.DefaultQuery("seriesUuid", "")

	type ll struct {
		Name     string
		Index    string
		ShopHash string
		Url      string
	}
	var data []interface{}
	var OrderColumnName = []string{"名称", "编号", "hash", "地址"}

	tempDir := "./temp/images"
	err := os.MkdirAll(tempDir, os.ModePerm)
	if err != nil {
		c.String(http.StatusInternalServerError, fmt.Sprintf("Failed to create temporary directory: %v", err))
		return
	}
	defer os.RemoveAll(tempDir) // 在处理完成后删除临时目录及其中的文件
	resp, err := service.BackendSeriesProvider.SeriesDetail(context.Background(), &backendSeries.SeriesDetailReq{SeriesUuid: sereiesUuid})

	if err != nil {
		c.String(http.StatusInternalServerError, fmt.Sprintf("down err: %v", err))
		return
	}

	if resp.ActionCode != "culture" {
		c.String(http.StatusInternalServerError, fmt.Sprintf("down err: 系列类型不对"))
		return
	}
	fmt.Println("1-----------", len(resp.SeriesCultureArtworks))
	fmt.Println("开始测试1--------------------")

	for _, r := range resp.SeriesCultureArtworks {
		for i := 1; i <= int(r.StockNum); i++ {
			temp := &ll{
				Name:  r.Name,
				Index: fmt.Sprintf("%d", i),
			}

			temp.ShopHash = utils.SHA256V([]byte(fmt.Sprintf("%s_%d", r.Hash, i)))

			cultureArtworkReq := vo.CultureArtworkReq{Uuid: r.Uuid, Num: i}
			s, _ := json.Marshal(cultureArtworkReq)

			str, err := secret.CombineSecret(string(s))
			if err != nil {
				c.String(http.StatusInternalServerError, fmt.Sprintf("down err: %v", err))
				return
			}
			tempUrl := fmt.Sprintf("%s/#/pages/modal3D/3d?sign=%s&time=%s&client=%s&nonce=%d", config.AppConfig.Service.ShopQrUrl,
				str,
				time.Now().Format(time.DateTime),
				utils.GenerateRandomString(5),
				rand.Intn(999999),
			)
			temp.Url = tempUrl

			/*
				str, err := CombineSecret(token)
				if err != nil {
					t.Errorf("组合加密错误 %s", err.Error())
					return
				}

				t.Logf("加密之后 %s", str)

				//str = "b66054d4f8a80fd93f603224e16999b7ff9db376a89af5919c99bc45a9c00e760163364c3ef3cd2e1370a90dcac5a68d1af25895841dbe71069a9f4f90445b494b35958bb588441f74cf15932a73ef5f379871fc884982e36a72d3cdc83ad96e085288eecb0df88982aa30cb76469d404f210abe0283e52f2e1b602dfb88a0bb23acaf249ea2aef4b58f4ccaa2ca73dd62be30396431982303995e77d9a0fff14a1c3b27407c19c890687b9a0721ac3d1405981d69f3da64edb0923f73be7ed239757c031c02f8df2c3daa7ffff11bf8e0de37dd627730a14a919bd8c57fca4e6c11117fe68ed1930c8bfafe1cae0e6c3ee9770018865b983e2ad4ef404ef76001a186b5be0e9bcd6827587df5f124da5b1d77c94003828509753e4006e6995ce667c5f8a2cf3ad0a44da5245d3dcd6381db5f2d0ed715779c6ded24906ab943"
				tokenExample, err := GetJwtFromStr(str)

			*/

			qrCode, err := qrcode.Encode(tempUrl, qrcode.Medium, 256)
			if err != nil {
				fmt.Printf("Failed to generate QR code: %v\n", err)
				return
			}
			// 将二维码图片保存到文件
			err = ioutil.WriteFile("./temp/images/"+fmt.Sprintf("%s-%s", temp.Name, temp.Index)+".png", qrCode, 0644)
			if err != nil {
				fmt.Printf("Failed to save QR code image: %v\n", err)
				return
			}
			data = append(data, temp)
		}
	}

	content := utils.ToExcel(OrderColumnName, data)
	file, err := os.Create("./temp/藏品哈希.xlsx")
	if err != nil {
		fmt.Println("Failed to create file:", err)
		return
	}
	defer file.Close()
	_, err = io.Copy(file, content)
	if err != nil {
		fmt.Println("Failed to save file:", err)
		return
	}
	qr.ImagesOnZip(c, "./temp/")

}
