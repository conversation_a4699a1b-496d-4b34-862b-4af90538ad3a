// Package shop -----------------------------
// @file      : dto.go
// <AUTHOR> JJXu
// @contact   : <EMAIL>
// @time      : 2025/4/12 16:27
// -------------------------------------------
package shop

import backendSeries "github.com/fonchain_enterprise/client-auction/api/series"

type SaveSeriesReq struct {
	backendSeries.SaveSeriesReq
	ArtworkList []ArtworkPrice `json:"artworkList"`
}
type ArtworkPrice struct {
	Price      string `json:"price"`
	ArtworkUid string `json:"artworkUid"`
	Currency   string `json:"currency"`  //默认为CNY
	CompanyId  uint32 `json:"companyId"` //绑定销售
}
