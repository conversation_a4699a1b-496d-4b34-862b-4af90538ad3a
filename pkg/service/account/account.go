package account

import (
	"context"
	"encoding/base64"
	"errors"
	"fmt"
	"github.com/fonchain/utils/baidu"
	"github.com/fonchain/utils/mobile"
	"github.com/fonchain_enterprise/client-auction/api/account"
	"github.com/fonchain_enterprise/client-auction/api/chain"
	"github.com/fonchain_enterprise/client-auction/api/custom_contract"
	"github.com/fonchain_enterprise/client-auction/api/fenghe"
	"github.com/fonchain_enterprise/client-auction/api/position"
	"github.com/fonchain_enterprise/client-auction/api/rule"
	"github.com/fonchain_enterprise/client-auction/cmd/config"
	"github.com/fonchain_enterprise/client-auction/pkg/common/cache"
	"github.com/fonchain_enterprise/client-auction/pkg/e"
	"github.com/fonchain_enterprise/client-auction/pkg/logic"
	"github.com/fonchain_enterprise/client-auction/pkg/middleware"
	"github.com/fonchain_enterprise/client-auction/pkg/model/login"
	"github.com/fonchain_enterprise/client-auction/pkg/model/query"
	"github.com/fonchain_enterprise/client-auction/pkg/model/response"
	"github.com/fonchain_enterprise/client-auction/pkg/model/vo"
	"github.com/fonchain_enterprise/client-auction/pkg/model/vo/union"
	"github.com/fonchain_enterprise/client-auction/pkg/service"
	"github.com/fonchain_enterprise/client-auction/pkg/utils/secret"
	"github.com/gin-gonic/gin"
	"github.com/gin-gonic/gin/binding"
	"github.com/jinzhu/copier"
	"io/ioutil"
	"net/http"
	"regexp"
	"strings"
	"time"
)

type UserNewSecret struct {
	ID       uint64
	CreateAt time.Time
}

// OnlySend 用户登录操作
func OnlySend(c *gin.Context) {

	var req account.SendMsgRequest

	if err := c.ShouldBindBodyWith(&req, binding.JSON); err != nil {
		service.Error(c, e.InvalidParams, err)
		return
	}
	req.Domain = e.MallAuctionDomain //无

	res, err := service.AccountProvider.OnlySendMsg(c, &req)
	if err != nil {
		service.Error(c, e.Error, err)
		return
	}

	service.Success(c, res)
	return
}

// SynRate 同步
func SynRate(c *gin.Context) {

	startTime := time.Now()
	logic.SynMoneyRate()

	fmt.Println("开始时间1-", time.Now().Sub(startTime))
	return

}

// GenCaptcha 生成验证码
func GenCaptcha(c *gin.Context) {
	var req account.GenerateSliderCaptchaRequest

	startTime := time.Now()
	if err := c.ShouldBindBodyWith(&req, binding.JSON); err != nil {
		service.Error(c, e.InvalidParams, err)
		return
	}

	if isExist := cache.RedisClient.Get("auction:white:close").Val(); isExist != "" {
		service.Error(c, e.Error, errors.New("当前线上拍卖二维码不可用，请扫线上直播二维码"))
		return
	}

	info, err := service.AccountProvider.GenerateSliderCaptcha(c.Request.Context(), &req)

	if err != nil {
		service.Error(c, e.InvalidParams, err)
		return
	}

	fmt.Println("开始时间", time.Now().Sub(startTime))
	service.Success(c, info)
	return

}

// SendMsgBeforeLogin 用户登录之前发送验证码
func SendMsgBeforeLogin(c *gin.Context) {

	var fromReq query.SendMsgRequest
	resInfo := response.SendMsgRes{
		IsExist: true,  //新号还是老号
		IsReal:  false, //是否有实名过
	}

	if err := c.ShouldBindBodyWith(&fromReq, binding.JSON); err != nil {
		service.Error(c, e.InvalidParams, err)
		return
	}

	req := account.SendMsgRequest{
		Domain:  e.MallAuctionDomain,
		TelNum:  fromReq.TelNum,
		Project: e.MallAuctionDomain,
		SignNo:  fromReq.SignNo,
		MId:     fromReq.MId,
		Scope:   fromReq.Scope,
		Zone:    fromReq.Zone,
	}

	if fromReq.CaptchaInfo.NonceStr != "123789s!" {
		_, err1 := service.AccountProvider.VerifySliderCaptcha(c.Request.Context(), &fromReq.CaptchaInfo)

		if err1 != nil {
			service.Error(c, e.CaptchaWrong, err1)
			return
		}
	}

	fmt.Println(cache.RedisClient.Get("auction:white:open").Val())
	//fmt.Println(cache.RedisClient.Get("auction:white:phone").Val())
	if isExist := cache.RedisClient.Get("auction:white:open").Val(); isExist != "" {
		if !cache.IsInList("auction:white:phone", req.TelNum) { //不发的人
			service.Error(c, e.Error, errors.New("当前线上拍卖二维码不可用，请扫线上直播二维码"))
			return
		}
	}

	if req.Zone != e.ZoneCn && req.Zone != "" {
		req.TelNum = req.Zone + req.TelNum
	}

	isIsExist, err := IsRegister(req.TelNum)
	if err != nil {
		service.Error(c, e.InvalidParams, err)
		return
	}

	if isIsExist == false { //存在账号使用这个发送
		fmt.Println("不存在账号")
		resInfo.IsExist = false
		_, err = RegisterOnlyTel(c, req.TelNum, "", req.Zone)
		if err != nil {
			service.Error(c, e.Error, err)
			return
		}
	}

	err, code := SendMsgFunc(c, req.Zone, c.ClientIP(), req.TelNum, req.Domain, req.SignNo, req.MId, req.Scope)
	if err != nil {
		service.Error(c, code, err)
		return
	}

	info, err := service.AccountProvider.UserByTel(c.Request.Context(), &account.UserByTelRequest{Tel: req.TelNum, Domain: e.MallAuctionDomain})

	if (info.Info.UserExtend != nil) || info.Info.IDNum != "" {
		resInfo.IsReal = true
	}

	service.Success(c, resInfo)

	return
}

func Artworks(c *gin.Context) {
	//创建一个UserLoginService对象
	var req fenghe.AuctionBuyList

	if err := c.ShouldBindBodyWith(&req, binding.JSON); err != nil {
		service.Error(c, e.InvalidParams, err)
		return
	}

	userInfoAny, _ := c.Get("jwtInfo")
	userInfo := userInfoAny.(middleware.LoginInfo)
	req.UserId = uint32(userInfo.ID)
	req.AuctionStatus = 3

	res, err := service.FengheProvider.ListAuctionBuyDetail(c.Request.Context(), &req)

	if err != nil {
		service.Error(c, e.Error, err)
		return
	}

	auctionBuyUidList := []string{}
	currency := logic.GetLanguageCurrency(c)
	for k, v := range res.Data { //起拍价换算
		auctionBuyUidList = append(auctionBuyUidList, res.Data[k].Uuid)
		res.Data[k].LeftCnyPrice = v.LeftPrice
		if res.Data[k].AuctionArtworkInfo == nil {
			res.Data[k].AuctionArtworkInfo = &fenghe.AuctionArtworkInfo{}
		}

		res.Data[k].AuctionArtworkInfo.StartPriceCurrency = currency
		res.Data[k].AuctionArtworkInfo.SoldPriceCurrency = currency
		res.Data[k].BaseCurrency = currency
		res.Data[k].LeftCurrency = currency

		if currency != "RMB" {
			res.Data[k].AuctionArtworkInfo.StartPrice = logic.GetCurrencyMoney(v.AuctionArtworkInfo.StartPrice, currency) //ok
			res.Data[k].AuctionArtworkInfo.SoldPrice = logic.GetCurrencyMoney(v.AuctionArtworkInfo.SoldPrice, currency)   //ok
			res.Data[k].BaseMoney = logic.GetCurrencyMoney(v.BaseMoney, currency)                                         //ok
			res.Data[k].LeftPrice = logic.GetCurrencyMoney(v.LeftPrice, currency)                                         //ok
		}
	}
	//查询订单信息
	fmt.Println("Artwork -- 查询拍卖画作的订单信息")
	orderList, err2 := service.FengheProvider.GetSeriesOrderList(c, &fenghe.GetSeriesOrderListRequest{
		Page:     1,
		PageSize: int64(len(auctionBuyUidList)),
		Where:    fmt.Sprintf("auction_buy_uuid in ('%s')", strings.Join(auctionBuyUidList, "','")),
	})
	if err2 != nil {
		service.Error(c, e.Failed, err2, "查询订单信息失败")
		return
	}
	responseData := AuctionBuyListResponse{
		Count:              res.Count,
		Data:               []ArtworkInfo{},
		AuctionArtworkBase: res.AuctionArtworkBase,
		NowAuctionPrice:    res.NowAuctionPrice,
	}
	for i, v := range res.Data {
		temp := ArtworkInfo{AuctionBuyRequest: res.Data[i]}
		for ii, vv := range orderList.List {
			//fmt.Printf("v.uuid: %s -- vv.AuctionBuyUuid: %s\n", v.Uuid, vv.AuctionBuyUuid)
			if v.Uuid == vv.AuctionBuyUuid {
				temp.SeriesUid = orderList.List[ii].SeriesUid
				temp.ReceivingStatus = orderList.List[ii].ReceivingStatus
				temp.OrderNo = orderList.List[ii].OrderNo
				temp.SignStatus = orderList.List[ii].SignStatus
				break
			}
		}
		responseData.Data = append(responseData.Data, temp)
	}

	service.Success(c, responseData)
	return
}

func Artwork(c *gin.Context) {
	//创建一个UserLoginService对象
	var req fenghe.AuctionBuyDetail

	if err := c.ShouldBindBodyWith(&req, binding.JSON); err != nil {
		service.Error(c, e.InvalidParams, err)
		return
	}

	res, err := service.FengheProvider.DetailAuctionBuy(c.Request.Context(), &req)

	if err != nil {
		service.Error(c, e.Error, err)
		return
	}

	currency := logic.GetLanguageCurrency(c)

	res.AuctionArtworkInfo.SoldPriceCurrency = currency
	res.AuctionArtworkInfo.StartPriceCurrency = currency
	res.AuctionArtworkInfo.StartPrice = logic.GetCurrencyMoney(res.AuctionArtworkInfo.StartPrice, currency) //ok
	res.AuctionArtworkInfo.SoldPrice = logic.GetCurrencyMoney(res.AuctionArtworkInfo.SoldPrice, currency)   //ok

	res.LeftCurrency = currency
	res.BaseCurrency = currency
	res.LeftCnyPrice = res.LeftPrice

	res.LeftPrice = logic.GetCurrencyMoney(res.LeftPrice, currency) //ok
	res.BaseMoney = logic.GetCurrencyMoney(res.BaseMoney, currency) //ok
	// 返回数据，查找系列uid
	result := ArtworkInfo{
		AuctionBuyRequest: res,
	}
	if res.AuctionArtworkInfo != nil {
		//if res.AuctionArtworkInfo.AuctionUuid == "" {
		//	service.Error(c, e.Failed, nil, "找不到拍卖信息，请联系管理员")
		//	return
		//}
		//seriesList, errs := service.FengheProvider.GetSeriesProfileList(c, &fenghe.GetSeriesProfileListRequest{
		//	Page:     1,
		//	PageSize: 1,
		//	Where:    fmt.Sprintf("auction_uuid ='%s'", res.AuctionArtworkInfo.AuctionUuid),
		//})
		//if errs != nil {
		//	service.Error(c, e.Failed, errs)
		//	return
		//}
		//if len(seriesList.List) > 0 {
		//	result.SeriesUid = seriesList.List[0].SeriesUuid
		//} else {
		//	service.Error(c, e.Failed, nil, "该拍卖商品未绑定系列")
		//	return
		//}
		fmt.Println("Artwork -- 查询拍卖画作的订单信息")
		orderInfo, err2 := service.FengheProvider.GetSeriesOrderList(c, &fenghe.GetSeriesOrderListRequest{
			Query:    &fenghe.SeriesOrderData{AuctionBuyUuid: req.Uuid},
			Page:     1,
			PageSize: 1,
		})
		if err2 != nil {
			service.Error(c, e.Failed, err2, "查询订单信息失败")
			return
		}
		if orderInfo.Total > 0 {
			result.ReceivingStatus = orderInfo.List[0].ReceivingStatus
			result.SeriesUid = orderInfo.List[0].SeriesUid
			result.OrderNo = orderInfo.List[0].OrderNo
			result.SignStatus = orderInfo.List[0].SignStatus
			// 新增字段 2025-07-23
			result.OrderCreatedAt = orderInfo.List[0].CreatedAt
			result.ArrivalDate = orderInfo.List[0].ArrivalDate
			result.ReceivingTime = orderInfo.List[0].ReceivingTime
			//查询支付时间
			payRecords, _ := service.FengheProvider.ListAuctionPay(c, &fenghe.AuctionPayList{
				Status: 1,
			})
			if payRecords != nil && payRecords.Data != nil {
				for _, p := range payRecords.Data {
					result.PayTime = p.UpdatedAt
					break
				}
			}
			//更新合同完全签署时间
			var orderNos = []string{orderInfo.List[0].OrderNo}
			if len(orderNos) > 0 {
				signInfoResp, _err := service.CustomContractProvider.SignInfo(context.Background(), &custom_contract.SignInfoReq{OrderNos: orderNos})
				if _err != nil {
					service.Error(c, e.Failed, _err, "查询失败")
					return
				}
				if len(signInfoResp.Data) > 0 {
					for _, vv := range signInfoResp.Data {
						if vv.OrderID == orderInfo.List[0].OrderNo {
							result.SignFinishTime = vv.SignDate
						}
					}
				}
			}
		} else {
			service.Error(c, e.Failed, nil, "找不到拍卖信息，请联系管理员")
			return
		}
	}
	service.Success(c, result)
	return
}

func MyInfo(c *gin.Context) {
	//创建一个UserLoginService对象
	userInfoAny, _ := c.Get("jwtInfo")
	userInfo := userInfoAny.(middleware.LoginInfo)

	userObj, err := GetUserInfoById(c, userInfo.ID)
	if err != nil {
		service.Error(c, e.InvalidParams, err)
		return
	}
	userObj.FddInfo = userInfo.FddInfo

	service.Success(c, userObj)
	return
}

func UpdateMyInfo(c *gin.Context) {
	//创建一个UserLoginService对象
	var req union.User

	if err := c.ShouldBindBodyWith(&req, binding.JSON); err != nil {
		service.Error(c, e.InvalidParams, err)
		return
	}
	userInfoAny, _ := c.Get("jwtInfo")
	userInfo := userInfoAny.(middleware.LoginInfo)

	//账号服务 IdType id-身份证 passport护照 other其他
	//默认身份证
	if req.UserExtend != nil {
		req.RealName = req.UserExtend.RealName
		if req.UserExtend.IsMainland == 1 {
			req.UserExtend.IdType = "id"
		}
	}

	var updateReq = account.UpdateRequest{
		ID:         userInfo.ID,
		Domain:     e.MallAuctionDomain,
		NickName:   req.NickName,
		Password:   req.Password,
		TelNum:     req.TelNum,
		Avatar:     req.Avatar,
		Status:     req.Status,
		EnterDate:  req.EnterDate,
		JobNum:     req.JobNum,
		BirthDate:  req.BirthDate, //出生日期
		Sex:        req.Sex,       //性别
		Title:      req.Title,
		IdNum:      req.IdNum,
		RealName:   req.RealName,
		UserExtend: req.UserExtend,
	}

	if req.JumpTo != "" || req.Lang != "" {
		updateReq.Extend = &account.Extend{JumpTo: req.JumpTo, Lang: req.Lang}
	}

	if req.UserExtend != nil {
		if req.UserExtend.IsMainland == 1 { //是大陆，进行ocr识别

			//1-ocr
			userOcrInfo, err := getFormIdCar(req.UserExtend.CardA)
			if err != nil {
				service.Error(c, e.InvalidParams, err)
				return
			}

			if userOcrInfo.IDNum != req.UserExtend.IdNo {
				service.Error(c, e.InvalidParams, errors.New("身份证号不匹配，请重新上传身份证图片"))
				return
			}

			fmt.Println("userInfo", userInfo.TelNum)
			//2-三要素认证
			isPass, err := mobile.AuthenticationThree(mobile.AuthRequest{IdCard: req.UserExtend.IdNo, Mobile: userInfo.TelNum, Name: req.UserExtend.RealName})

			fmt.Println("-------我来看看------", isPass, err, req.UserExtend.RealName, req.UserExtend.IdNo)
			if err != nil {
				service.Error(c, e.InvalidParams, err)
				return
			}

			if isPass == false {
				service.Error(c, e.InvalidParams, errors.New("三要素认证不通过，请确认手机号和身份证是否一致"))
				return
			}

			//创建一个提交那啥记录
			_, _ = service.AccountProvider.ReviewRealName(context.Background(), &account.ReviewRealNameRequest{UserId: uint32(userInfo.ID), UserName: req.UserExtend.RealName, Ids: []uint32{uint32(userInfo.ID)}, PassStatus: 1})

			fmt.Println("2-12-----")
			if isPass == true {
				updateReq.NickName = req.UserExtend.RealName
				_, _ = service.AccountProvider.ReviewRealName(context.Background(), &account.ReviewRealNameRequest{Ids: []uint32{uint32(userInfo.ID)}, IsPass: true})
			} else {
				_, _ = service.AccountProvider.ReviewRealName(context.Background(), &account.ReviewRealNameRequest{Ids: []uint32{uint32(userInfo.ID)}, IsPass: false})
			}

		} else { //非大陆直接搞
			_, _ = service.AccountProvider.ReviewRealName(context.Background(), &account.ReviewRealNameRequest{Ids: []uint32{uint32(userInfo.ID)}, PassStatus: 1})
		}

	}

	fmt.Println("2------")
	fmt.Println(req.UserExtend)
	fmt.Println(updateReq.UserExtend)
	s, err := service.AccountProvider.Update(context.Background(), &updateReq)

	fmt.Println("1------")
	fmt.Println(s)
	fmt.Println(err)
	if err != nil {
		service.Error(c, e.Error, err)
		return
	}

	userObj, err := GetUserInfoById(c, userInfo.ID)
	if err != nil {
		service.Error(c, e.InvalidParams, err)
		return
	}

	service.Success(c, userObj)
	return
}

// UserLogin 不存在则注册
func UserLogin(c *gin.Context) {

	req := account.LoginRequest{}
	if err := c.ShouldBindBodyWith(&req, binding.JSON); err != nil {
		service.Error(c, e.InvalidParams, err)
		return
	}
	req.Domain = e.MallAuctionDomain

	if req.Code == "" || req.TelNum == "" {
		service.Error(c, e.InvalidParams, errors.New("less params"))
		return
	}

	if req.Zone != e.ZoneCn && req.Zone != "" {
		req.TelNum = req.Zone + req.TelNum
	}

	ip := c.ClientIP()
	req.Ip = ip

	res, err := service.AccountProvider.Login(c.Request.Context(), &req)
	if err != nil {
		service.Error(c, e.Error, err)
		return
	}

	loginInfo, err := GetUserInfo(res)

	if err != nil {
		service.Error(c, e.Error, err)
		return
	}

	if res.AccountInfo.UserExtend != nil && res.AccountInfo.UserExtend.RealName != "" {
		res.AccountInfo.RealName = res.AccountInfo.UserExtend.RealName
	}

	//新用户是大陆的，实名过，然后看法大大的
	if res.AccountInfo.UserExtend != nil && res.AccountInfo.UserExtend.IsMainland == 1 && res.AccountInfo.UserExtend.IsReal == 1 {

		fddInfo, err := service.AccountProvider.FddUserFindByUserId(c.Request.Context(), &account.UserInfo{ID: res.AccountInfo.ID, Domain: e.MallAuctionDomain})

		if err != nil {
			service.Error(c, e.Error, err)
			return
		}

		//法大大没有校验通过，去注册法大大去
		if fddInfo == nil || fddInfo.IsVerify == false {
			loginInfo.IsJumpFdd = true
		}

		fmt.Println(fddInfo)
		fmt.Println(fddInfo.IsVerify)
	}

	service.Success(c, loginInfo)
	return
}

func GetUserInfo(res *account.TokenInfo) (*union.Login, error) {
	accountInfo := &union.AccountInfo{
		ID:         res.AccountInfo.ID,
		Account:    res.AccountInfo.Account,
		NickName:   res.AccountInfo.NickName,
		Domain:     res.AccountInfo.Domain,
		TelNum:     res.AccountInfo.TelNum,
		Status:     res.AccountInfo.Status,
		Avatar:     res.AccountInfo.Avatar,
		CreatedAt:  res.AccountInfo.CreateAt,
		BirthDate:  res.AccountInfo.BirthDate,
		Age:        res.AccountInfo.Age,
		Sex:        res.AccountInfo.Sex,
		Title:      res.AccountInfo.Title,
		IDNum:      res.AccountInfo.IDNum,
		RealName:   res.AccountInfo.RealName,
		Lang:       res.AccountInfo.Extend.Lang,
		UserExtend: res.AccountInfo.UserExtend,
	}

	fmt.Println("是否可以扫码", res.AccountInfo.CanScan)

	if accountInfo.Lang == "" {
		if res.AccountInfo.Language == "" {
			accountInfo.Lang = "zh-CN" // 未修改过语言的则默认中文
		} else {
			accountInfo.Lang = res.AccountInfo.Language
		}
	}

	token, err := secret.CombineSecret(res.Token)

	if err != nil {
		return nil, err
	}

	resInfo := &union.Login{
		Token:       token,
		AccountInfo: accountInfo,
	}
	if accountInfo.UserExtend != nil && accountInfo.UserExtend.IsReal == 1 {
		resInfo.IsReal = true
	}

	if accountInfo.UserExtend != nil && accountInfo.UserExtend.IsReal == 1 {
		resInfo.IsReal = true
	}

	return resInfo, nil

}

func RegisterOnlyTel(c *gin.Context, telNum, code, zone string) (*account.RequestStatus, error) {
	tempNick := telNum

	if len(telNum) <= 4 {
		return nil, errors.New("手机号码格式不对")
	}

	tempNick = telNum[(len(telNum) - 4):]

	//账号服务
	registerRequest := account.RegistRequest{
		NickName: "访客" + tempNick,
		Avatar:   "https://cdn-test.szjixun.cn/fonchain-main/test/image/2076/avatar/6972820e-bee4-46c8-a80e-55d0f96abb51.png", //默认头像
		TelNum:   telNum,
		Password: "Tyfon8899",
		Code:     code,
		Extend:   &account.Extend{},
		Domain:   e.MallAuctionDomain,
		Zone:     zone,
		UserExtend: &account.UserExtend{
			Zone: zone,
		},
	}

	res, err := service.AccountProvider.Register(c, &registerRequest)
	fmt.Println("1---------", res, err)
	if err != nil {
		return nil, err
	}

	//1- 获取助记词
	privacyReq := &account.PrivacyInfoRequest{
		ID: res.ID,
	}

	privacyRes, err := service.AccountProvider.PrivacyInfo(c, privacyReq)

	if err != nil {
		return res, err
	}

	//2- 绑定到区块链交易账号
	initReq := &chain.InitAccountRequest{
		Mnemonic: privacyRes.MnemonicWords,
	}

	_, err = service.ChainProvider.InitAccount(c, initReq)

	if err != nil {
		return res, err
	}

	return res, nil

}

// Qr 二维码
func Qr(c *gin.Context) {

	var req account.ListRequest

	if err := c.ShouldBindBodyWith(&req, binding.JSON); err != nil {
		service.Error(c, e.InvalidParams, err)
		return
	}

	res := map[string]string{
		"qrUrl":       "https://common.szjixun.cn/api/image/qr/url?size=500&level=2&url=" + config.AppConfig.Service.Host,
		"publicQrUrl": "https://common.szjixun.cn/api/image/qr/url?size=500&level=2&url=" + config.AppConfig.Service.Host + "/publicLiveRoom",
	}

	//查看是否有权限
	service.Success(c, res)
	return
}

// List 用户信息
func List(c *gin.Context) {

	//TODO 后期需要修改的代码
	fmt.Println(c.Request.Host)
	if c.Request.Host == "erpapi.fontree.cn" {
		fmt.Println("1----")
		// 目标服务的URL
		targetURL := "https://fenghe-auction.szjixun.cn/api/v1/fenghe/adm/auction/user/list"

		// 创建一个新的HTTP请求
		req, err := http.NewRequest(c.Request.Method, targetURL, c.Request.Body)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create request"})
			return
		}

		// 复制所有的头部信息
		for name, values := range c.Request.Header {
			for _, value := range values {
				req.Header.Add(name, value)
			}
		}

		// 如果有查询参数，则添加到新的请求URL中
		q := req.URL.Query()
		for key, values := range c.Request.URL.Query() {
			for _, value := range values {
				q.Add(key, value)
			}
		}
		req.URL.RawQuery = q.Encode()

		// 使用标准库的http客户端发送请求
		client := &http.Client{}
		resp, err := client.Do(req)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Request failed"})
			return
		}
		defer resp.Body.Close()

		// 读取响应体
		body, err := ioutil.ReadAll(resp.Body)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to read response body"})
			return
		}

		// 将响应头复制给Gin上下文
		for name, values := range resp.Header {
			for _, value := range values {
				c.Header(name, value)
			}
		}

		// 返回状态码和响应体

		//查看是否有权限
		fmt.Println("1----1-1-", string(body))
		c.Status(resp.StatusCode)
		c.Data(resp.StatusCode, "application/json; charset=utf-8", body)

	} else {

		var req account.ListRequest

		if err := c.ShouldBindBodyWith(&req, binding.JSON); err != nil {
			service.Error(c, e.InvalidParams, err)
			return
		}
		req.Domain = e.MallAuctionDomain

		res, err := service.AccountProvider.List(c, &req)
		if err != nil {
			service.Error(c, e.Error, err)
			return
		}
		newRes := new(response.ListResponse)
		copier.Copy(&newRes, res)

		newRes.TodayUser, _ = cache.RedisClient.PFCount(cache.GetNowDayUser(e.DefaultUuid)).Result()
		newRes.TodayBiggestNum, _ = cache.RedisClient.Get(cache.GetSampleBiggestNum(e.DefaultUuid)).Uint64()
		newRes.TodayNum, _ = cache.RedisClient.Get(cache.GetNowDayNum(e.DefaultUuid)).Uint64()
		newRes.LiveNum, _ = cache.RedisClient.Get(cache.GetLiveTotalNum(e.DefaultUuid)).Uint64()

		//查看是否有权限
		service.Success(c, newRes)
	}

	return
}

func IsRegister(TelNum string) (bool, error) {
	userByTelReq := account.UserByTelRequest{
		Tel:    TelNum,
		Domain: e.MallAuctionDomain,
	}

	//查询是否有该账号
	userByTelRes, err := service.AccountProvider.UserByTel(context.Background(), &userByTelReq)
	if err != nil {
		return false, err
	}

	return userByTelRes.IsExist, nil
}

func GetUserInfoById(c *gin.Context, ID uint64) (union.AccountInfo, error) {
	info := union.AccountInfo{}

	req := account.InfoRequest{
		ID:     ID,
		Domain: e.MallAuctionDomain,
	}

	res, err := service.AccountProvider.Info(c.Request.Context(), &req)
	if err != nil {
		return info, err
	}

	info = union.AccountInfo{
		ID:         res.Info.ID,
		Account:    res.Info.Account,
		NickName:   res.Info.NickName,
		Domain:     res.Info.Domain,
		TelNum:     res.Info.TelNum,
		Status:     res.Info.Status,
		Avatar:     res.Info.Avatar,
		CreatedAt:  res.Info.CreateAt,
		BirthDate:  res.Info.BirthDate,
		Age:        res.Info.Age,
		Sex:        res.Info.Sex,
		Title:      res.Info.Title,
		IDNum:      res.Info.IDNum,
		RealName:   res.Info.RealName,
		Lang:       res.Info.Extend.Lang,
		UserExtend: res.Info.UserExtend,
	}

	if res.Info.UserExtend != nil && res.Info.UserExtend.RealName != "" {
		res.Info.RealName = res.Info.UserExtend.RealName
	}

	return info, nil
}

// MobileUserSend 手机端用户登录操作，完全从fonchain main复制
func MobileUserSend(c *gin.Context) {

	var req account.SendMsgRequest

	if err := c.ShouldBindBodyWith(&req, binding.JSON); err != nil {
		service.Error(c, e.InvalidParams, err)
		return
	}

	req.Domain = e.VerifierDomain
	req.Zone = ""

	res, err := service.AccountProvider.SendMsg(context.Background(), &req)
	if err != nil {
		service.Error(c, e.Error, err)
		return
	}

	service.Success(c, res)
	return
}

// 手机端用户登录操作，完全从fonchain main复制
func MobileUserLogin(c *gin.Context) {

	var req account.LoginRequest
	if err := c.ShouldBindBodyWith(&req, binding.JSON); err != nil {
		service.Error(c, e.InvalidParams, err)
		return
	}

	if req.TelNum == "" {
		service.Error(c, e.InvalidParams, errors.New("less params"))
		return
	}

	if req.Code == "" && req.Password == "" {
		service.Error(c, e.InvalidParams, errors.New("less params"))
		return
	}

	req.Domain = e.VerifierDomain
	req.Ip = c.ClientIP()

	res, err := service.AccountProvider.Login(c.Request.Context(), &req)
	if err != nil {
		service.Error(c, e.Error, err)
		return
	}

	//获取用户的岗位信息
	fmt.Println("res.AccountInfo.ID:", res.AccountInfo.ID)
	uReq := rule.RulesRequest{
		AccountID: res.AccountInfo.ID,
	}

	qres, err1 := service.RuleProvider.UserInfo(c.Request.Context(), &uReq)

	if err1 != nil {
		service.Error(c, e.Error, err1)
		return
	}

	if len(qres.PositionUsers) >= 1 {
		//uReq := rule.RulesRequest{
		//	AccountID: res.AccountInfo.ID,
		//}
		//qres, err1 := service.RuleProvider.UserInfo(c, &uReq)
		//if err1 != nil {
		//	service.Error(c, e.Error, err1)
		//	return
		//}

		fmt.Println("qres.PositionUsers:", qres.PositionUsers)
		ruleFlag := false
		for i, _ := range qres.PositionUsers {
			fmt.Println("qres.PositionUsers[i].DepartmentId:", qres.PositionUsers[i].DepartmentId)
			isPayQRCodeAuth := isHavePayQRCodeAuth(c, res.AccountInfo.ID, qres.PositionUsers[i].DepartmentId)
			if isPayQRCodeAuth {
				ruleFlag = true
				break
			}
		}
		if !ruleFlag {
			service.Error(c, e.Error, errors.New("暂无新增收款二维码权限"))
			return
		}
	}

	loginInfo, err := GetUserInfo(res)

	if err != nil {
		service.Error(c, e.Error, err)
		return
	}

	service.Success(c, loginInfo)

	return
}

// SendMsg 用户登录操作
func SendMsg(c *gin.Context) {
	req := account.SendMsgRequest{}

	userInfoAny, _ := c.Get("jwtInfo")
	userInfo := userInfoAny.(middleware.LoginInfo)
	req.TelNum = userInfo.TelNum
	req.Domain = e.MallAuctionDomain
	req.Project = e.MallAuctionDomain
	res, err := service.AccountProvider.SendMsg(context.Background(), &req)

	if err != nil {
		service.Error(c, e.Error, err)
		return
	}

	service.Success(c, res)
	return
}

// UpdateTelSendByToken 发送账号
func UpdateTelSendByToken(c *gin.Context) {
	//创建一个UserLoginService对象
	var req query.SendMsgNewTel

	if err := c.ShouldBindBodyWith(&req, binding.JSON); err != nil {
		service.Error(c, e.InvalidParams, err)
		return
	}
	userInfoAny, _ := c.Get("jwtInfo")
	userInfo := userInfoAny.(middleware.LoginInfo)

	//验证老手机号
	//生成token
	if req.Token == "" {
		service.Error(c, e.InvalidParams, errors.New("token不能为空"))
		return
	}

	tokeInfo := &secret.UserNewSecret{}
	tokeInfo, err := secret.SendMsgEncryption(req.Token)
	if err != nil {
		service.Error(c, e.Error, err)
		return
	}

	if tokeInfo.ID != userInfo.ID {
		service.Error(c, e.Error, errors.New("token校验失败"))
		return
	}

	//新账号发送短信
	var updateReq = account.SendNewTelNumMsgRequest{
		ID:      userInfo.ID,
		Project: e.MallAuctionDomain,
	}

	_, err = service.AccountProvider.SendNewTelNumMsg(context.Background(), &updateReq)
	if err != nil {
		service.Error(c, e.Error, err)
		return
	}

	service.Success(c, map[string]string{"msg": "发送成功,15分钟可用"})
}

// UpdateTelSend 发送账号
func UpdateTelSend(c *gin.Context) {
	//创建一个UserLoginService对象
	var req query.UpdateNewTel
	token := ""

	if err := c.ShouldBindBodyWith(&req, binding.JSON); err != nil {
		service.Error(c, e.InvalidParams, err)
		return
	}
	userInfoAny, _ := c.Get("jwtInfo")
	userInfo := userInfoAny.(middleware.LoginInfo)

	//验证老手机号
	checkReq := &account.CheckMsgRequest{
		TelNum: userInfo.TelNum,
		Domain: e.MallAuctionDomain,
		Code:   req.Code,
	}

	_, err := service.AccountProvider.CheckMsg(c.Request.Context(), checkReq)
	if err != nil {
		service.Error(c, e.Error, err)
		return
	}

	//新账号发送短信
	var updateReq = account.SendNewTelNumMsgRequest{
		ID:        userInfo.ID,
		Code:      req.Code,
		NewTelNum: req.NewTelNum,
		Project:   e.MallAuctionDomain,
	}

	_, err = service.AccountProvider.SendNewTelNumMsg(c.Request.Context(), &updateReq)
	if err != nil {
		service.Error(c, e.Error, err)
		return
	}

	//生成token
	tokeInfo := secret.UserNewSecret{ID: userInfo.ID, CreateAt: time.Now()}
	token, err = secret.SendMsgSecret(tokeInfo)
	if err != nil {
		service.Error(c, e.Error, err)
		return
	}
	fmt.Println(tokeInfo)

	service.Success(c, map[string]string{"msg": "发送成功,15分钟可用", "token": token})
}

// UpdateTel 用户更新手机号
func UpdateTel(c *gin.Context) {
	//创建一个UserLoginService对象
	var req query.UpdateNewTel

	if err := c.ShouldBindBodyWith(&req, binding.JSON); err != nil {
		service.Error(c, e.InvalidParams, err)
		return
	}
	userInfoAny, _ := c.Get("jwtInfo")
	userInfo := userInfoAny.(middleware.LoginInfo)

	//账号服务
	var updateReq = account.SendNewTelNumMsgRequest{
		ID:   userInfo.ID,
		Code: req.Code,
	}

	_, err := service.AccountProvider.UpdateTelNum(c.Request.Context(), &updateReq)
	if err != nil {
		service.Error(c, e.Error, err)
		return
	}

	service.Success(c, map[string]string{})
}
func CheckPhone(c *gin.Context) {
	response := CheckPhoneResponse{}
	req := account.UserByTelRequest{}
	if err := c.ShouldBindBodyWith(&req, binding.JSON); err != nil {
		service.Error(c, e.InvalidParams, err)
		return
	}

	fmt.Println("=========================")
	fmt.Println("DefaultSignNo:", config.AppConfig.Msg.DefaultSignNo)

	if req.Tel == "" {
		service.Error(c, e.InvalidParams, errors.New("less params"))
		return
	}
	matched, _ := regexp.MatchString(`^\d+$`, req.Tel)
	if !matched {
		service.Error(c, e.InvalidParams, errors.New("invalid phone"))
		return
	}
	req.Domain = e.MallAuctionDomain
	userRes, err := service.AccountProvider.UserByTel(c.Request.Context(), &req)
	response.IsExist = false
	if err == nil && userRes != nil && userRes.IsExist && userRes.Info != nil && userRes.Info.ID != 0 && userRes.Info.Status == "notactive" {
		response.IsExist = true
		fmt.Println("当前手机号用户存在erp系统：", req.Tel)
		service.Success(c, &response)
		return
	}

	service.Success(c, &response)
	return
}

type CheckPhoneResponse struct {
	IsExist bool
}

func isHavePayQRCodeAuth(c *gin.Context, ID uint64, DepartmentID uint64) bool {
	isLeader := false

	req := position.DoIHavaAuthRequest{
		UserId:       ID,
		Url:          e.AuthPayQRCode,
		DepartmentID: DepartmentID,
	}

	res, err := service.PositionProvider.DoIHavaAuth(c, &req)

	fmt.Println("auth: ", res)
	fmt.Println("err: ", err)
	fmt.Println("res.Hava: ", res.Hava)
	if err == nil && res.Hava == true {
		isLeader = true
	}

	return isLeader
}

// Reviews 实名审批信息列表
func Reviews(c *gin.Context) {
	//创建一个UserLoginService对象
	var req account.ReviewRealNameListRequest

	if err := c.ShouldBindBodyWith(&req, binding.JSON); err != nil {
		service.Error(c, e.InvalidParams, err)
		return
	}

	res, err := service.AccountProvider.ReviewRealNameList(c.Request.Context(), &req)

	if err != nil {
		service.Error(c, e.Error, err)
		return
	}

	service.Success(c, res)
	return
}

// Review 实名认证
func Review(c *gin.Context) {
	//创建一个UserLoginService对象
	var req account.ReviewRealNameRequest

	if err := c.ShouldBindBodyWith(&req, binding.JSON); err != nil {
		service.Error(c, e.InvalidParams, err)
		return
	}

	userInfoAny, _ := c.Get("jwtInfo")
	userInfo := userInfoAny.(login.Info)
	req.UserId = uint32(userInfo.ID)
	req.UserName = userInfo.NickName

	res, err := service.AccountProvider.ReviewRealName(c.Request.Context(), &req)

	if err != nil {
		service.Error(c, e.Error, err)
		return
	}
	service.Success(c, res)
	return
}

func CheckIdOcr(c *gin.Context) {

	var req account.UserExtend

	if err := c.ShouldBindBodyWith(&req, binding.JSON); err != nil {
		service.Error(c, e.InvalidParams, err)
		return
	}

	res, err := getFormIdCar(req.CardA)
	if err != nil {
		service.Error(c, e.Error, err)
		return
	}

	service.Success(c, res)
	return
}

func getFormIdCar(realIDImgA string) (res vo.OcrRes, err error) {
	resObj, err := http.Get(realIDImgA)
	if err != nil {
		fmt.Println("网络请求错误：", err)
		return
	}

	defer resObj.Body.Close()

	// 读取图片数据
	imageData, err := ioutil.ReadAll(resObj.Body)
	if err != nil {
		fmt.Println("读取图片数据错误：", err)
		return
	}

	// 将图片数据转换为base64编码
	base64Data := base64.StdEncoding.EncodeToString(imageData)
	fmt.Println(base64Data)

	result, err := baidu.OcrGetIdCard(base64Data, "front")

	if err != nil {
		return
	}

	res = vo.OcrRes{
		IDNum:    result.IdCard,
		RealName: result.Name,
		Path:     result.Path,
		Age:      result.Age,
		Birthday: dd(result.Birthday),
		Sex:      result.Sex,
	}

	return res, nil
}

// dd 实在没时间好好写代码
func dd(dateStr string) string {
	// 1. 解析字符串为time.Time类型
	// 注意：Go的布局字符串必须使用参考时间"2006-01-02 15:04:05"的格式
	t, err := time.Parse("20060102", dateStr)
	if err != nil {
		fmt.Printf("日期解析错误: %v\n", err)
		return ""
	}

	// 2. 格式化为目标格式
	formattedDate := t.Format("2006-01-02")

	fmt.Println("原始日期:", dateStr)
	fmt.Println("转换后日期:", formattedDate)
	return formattedDate

}
