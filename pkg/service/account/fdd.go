package account

import (
	"context"
	"errors"
	"fmt"
	"github.com/fonchain_enterprise/client-auction/api/account"
	"github.com/fonchain_enterprise/client-auction/api/contract"
	"github.com/fonchain_enterprise/client-auction/cmd/config"
	cache2 "github.com/fonchain_enterprise/client-auction/pkg/common/cache"
	"github.com/fonchain_enterprise/client-auction/pkg/common/utils"
	"github.com/fonchain_enterprise/client-auction/pkg/e"
	"github.com/fonchain_enterprise/client-auction/pkg/middleware"
	"github.com/fonchain_enterprise/client-auction/pkg/model/query"
	"github.com/fonchain_enterprise/client-auction/pkg/service"
	"github.com/gin-gonic/gin"
	"math/rand"
	"time"
)

// CheckFdd 法大大是否认证过
func CheckFdd(c *gin.Context) {

	var req query.FddCheckReq
	if err := c.ShouldBind(&req); err != nil {
		service.Error(c, e.Error, err)
		return
	}

	if req.ToUrl == "" {
		req.ToUrl = c.Request.Host + "/#/pages/protocol/result"
	}

	//创建一个UserLoginService对象
	var err error
	userInfoAny, _ := c.Get("jwtInfo")
	userInfo := userInfoAny.(middleware.LoginInfo)
	isVer := false
	h5Url := ""

	if userInfo.FddInfo != nil && userInfo.FddInfo.IsVerify == true {
		isVer = true
	}

	isMainLand := false
	fmt.Println(userInfo.UserExtend)
	if userInfo.UserExtend != nil && userInfo.UserExtend.IsMainland == 1 {
		isMainLand = true
	}

	isNeedJump := false
	if isMainLand == true && isVer == false {
		isNeedJump = true
		h5Url, err = GetBindFdd(userInfo, c.Request.Host, req.ToUrl)
		fmt.Println(h5Url, err)

		if err != nil {
			service.Error(c, e.Error, err)
			return
		}
	}

	service.Success(c, map[string]interface{}{"isMainland": isMainLand, "isVerify": isVer, "isNeedJump": isNeedJump, "h5Url": h5Url})

	return
}

/*
*

GetBindFdd
toUrl: 绑定法大大后跳转的地址(带域名相对地址)  https://wwww.baud.com/api/v1/feng/1.html
*/
func GetBindFdd(userInfo middleware.LoginInfo, host, toUrl string) (string, error) {
	c := context.Background()
	CertType := ""
	fddOpenId := utils.GetMd5(fmt.Sprintf("%s-%d", userInfo.TelNum, userInfo.ID))
	fmt.Println(fddOpenId)
	req := contract.RegisterPersonRequest{
		OpenId: fddOpenId,
	}

	res, err := service.ContractProvider.RegisterPerson(c, &req) //指定
	if err != nil {
		return "", err
	}
	fmt.Println(res)
	key := fmt.Sprintf("%d_%d", rand.Intn(10000), userInfo.ID)
	uri := fmt.Sprintf("%s/api/v1/fenghe/m/user/fdd/middle/url?key=%s&time=%d", config.AppConfig.Service.Host, key, time.Now().Unix())
	fmt.Println(uri)
	personVerifyReq := &contract.PersonVerifyRequest{
		CustomerId:      res.CustomerId,
		VerifiedWay:     "1",
		CustomerName:    userInfo.RealName,
		CustomerIdentNo: userInfo.IdNum,
		Mobile:          userInfo.TelNum,
		ReturnUrl:       uri,
		//ReturnUrl: "https://stock.szjixun.cn/uni-Identify-quality/index.html",
		CertType: CertType,
	}
	fmt.Println("法大大数据", personVerifyReq)

	personVerifyRes, err := service.ContractProvider.PersonVerify(c, personVerifyReq) //指定

	if err != nil {
		return "", err
	}

	createUser := account.FddCreateUserRequest{
		WxUserId:      0,
		TransactionNo: personVerifyRes.TransactionNo,
		CustomerId:    res.CustomerId,
		IdType:        CertType,
		UserId:        userInfo.ID,
	}
	_, err = service.AccountProvider.FddCreateUserV2(c, &createUser)

	if err != nil {
		return "", err
	}

	fmt.Println("2--新页面页面", personVerifyRes.Url)

	cache2.RedisClient.Set(cache2.GetFddSynUrl(key), personVerifyRes.TransactionNo, 0)
	cache2.RedisClient.Set(cache2.GetFddToUrl(key), toUrl, 30*24*time.Hour)

	return personVerifyRes.Url, err
}

// SureBindFdd 注册成功 绑定法大大  请求   ?transactionNo=********&key=122kksksk&time=*********
func SureBindFdd(c *gin.Context) {

	toUrl := config.AppConfig.Service.ShopHost + "/#/pages/protocol/result"
	key := c.Query("key")
	if key == "" {
		service.Error(c, e.Error, errors.New("没有数据"))
		return
	}

	transactionNo := cache2.RedisClient.Get(cache2.GetFddSynUrl(key)).Val()

	if transactionNo == "" {
		c.Redirect(302, toUrl)
		return
	}
	tempToUrl := cache2.RedisClient.Get(cache2.GetFddToUrl(key)).Val()
	if tempToUrl != "" {
		toUrl = tempToUrl
	}

	createUser := account.FddCreateUserRequest{
		TransactionNo: transactionNo,
		IsVerify:      true,
	}

	_, err := service.AccountProvider.FddUpdateUser(c, &createUser)

	if err != nil {
		service.Error(c, e.Error, err)
		return
	}

	c.Redirect(302, toUrl)

	return
}
func CheckFddCustomerId(c *gin.Context) string {
	userInfoAny, _ := c.Get("jwtInfo")
	userInfo := userInfoAny.(middleware.LoginInfo)
	if userInfo.UserExtend != nil && userInfo.UserExtend.IsMainland == 1 && userInfo.UserExtend.IsReal == 1 {
		fddInfo, err := service.AccountProvider.FddUserFindByUserId(c, &account.UserInfo{ID: userInfo.ID, Domain: e.MallAuctionDomain})
		if err != nil {
			return ""
		}
		//法大大没有校验通过，去注册法大大去
		if fddInfo == nil || fddInfo.IsVerify == false {
			return ""
		}
		return fddInfo.CustomerId
	}
	return ""
}
