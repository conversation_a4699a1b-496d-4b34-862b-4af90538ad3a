// Package account -----------------------------
// @file      : dto.go
// <AUTHOR> JJXu
// @contact   : <EMAIL>
// @time      : 2025/4/22 13:25
// -------------------------------------------
package account

import (
	"github.com/fonchain_enterprise/client-auction/api/fenghe"
)

type ArtworkInfo struct {
	*fenghe.AuctionBuyRequest
	SeriesUid       string `json:"seriesUid"`
	ReceivingStatus uint32 `json:"receivingStatus"`
	OrderNo         string `json:"orderNo"`
	SignStatus      uint32 `json:"signStatus"`
	// 新增字段 2025-07-23
	OrderCreatedAt string `json:"orderCreatedAt"` // 订单创建时间
	ArrivalDate    string `json:"arrivalDate"`    // 到账日期
	SignFinishTime string `json:"signFinishTime"` // 合同签署时间
	ReceivingTime  string `json:"receivingTime"`  // 确认收货时间
	PayTime        string `json:"payTime"`        //支付时间
}
type AuctionBuyListResponse struct {
	Count              uint64                     `protobuf:"varint,1,opt,name=count,proto3" json:"count"`
	Data               []ArtworkInfo              `protobuf:"bytes,2,rep,name=data,proto3" json:"data"`
	AuctionArtworkBase *fenghe.AuctionArtworkBase `protobuf:"bytes,3,opt,name=auctionArtworkBase,proto3" json:"auctionArtworkBase"` //仅仅当auctionArtworkUuid有值时返回
	NowAuctionPrice    *fenghe.NowAuctionPrice    `protobuf:"bytes,4,opt,name=nowAuctionPrice,proto3" json:"nowAuctionPrice"`       //仅仅当auctionArtworkUuid有值时返回
}
