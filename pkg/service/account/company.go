package account

import (
	"fmt"
	"github.com/fonchain_enterprise/client-auction/api/account"
	"github.com/fonchain_enterprise/client-auction/api/department"
	"github.com/fonchain_enterprise/client-auction/pkg/e"
	"github.com/fonchain_enterprise/client-auction/pkg/middleware"
	"github.com/fonchain_enterprise/client-auction/pkg/model/dto"
	"github.com/fonchain_enterprise/client-auction/pkg/service"
	"github.com/fonchain_enterprise/client-auction/pkg/utils/secret/aes"
	"github.com/gin-gonic/gin"
	"github.com/gin-gonic/gin/binding"
	"github.com/jinzhu/copier"
)

func AllCompany(c *gin.Context) {
	var req account.GenerateSliderCaptchaRequest

	if err := c.ShouldBindBodyWith(&req, binding.JSON); err != nil {
		service.Error(c, e.InvalidParams, err)
		return
	}

	res, err := service.DepartmentProvider.BaseSyncList(c.Request.Context(), &department.BaseAllRequest{})

	if err != nil {
		service.Error(c, e.InvalidParams, err)
		return
	}
	var baseDetailResponses []*dto.BaseDetailResponse
	err = copier.Copy(&baseDetailResponses, res.Data)

	service.Success(c, baseDetailResponses)
	return

}

// 客户绑定公司
func BindCompany(c *gin.Context) {
	var req account.GenerateSliderCaptchaRequest

	if err := c.ShouldBindBodyWith(&req, binding.JSON); err != nil {
		service.Error(c, e.InvalidParams, err)
		return
	}

	res, err := service.DepartmentProvider.BaseSyncList(c.Request.Context(), &department.BaseAllRequest{})

	if err != nil {
		service.Error(c, e.InvalidParams, err)
		return
	}

	service.Success(c, res)
	return

}

func BindSellerQr(c *gin.Context) {
	userInfoAny, _ := c.Get("jwtInfo")
	userInfo := userInfoAny.(middleware.LoginInfo)

	got, err := aes.CreateSecretDemo(uint(userInfo.ID), userInfo.NickName) //生成一个新的密钥

	if err != nil {
		service.Error(c, e.InvalidParams, err)
		return
	}

	//生成二维码
	res := make(map[string]string)
	res["url"] = fmt.Sprintf("https://common.szjixun.cn/api/image/qr/url?url=%s&size=500&level=3", "bindSeller||"+got)

	service.Success(c, res)
	return
}

func RecommendInfo(c *gin.Context) {
	userInfoAny, _ := c.Get("jwtInfo")
	userInfo := userInfoAny.(middleware.LoginInfo)

	recommendInfoResponse := &dto.RecommendInfoResponse{}

	if userInfo.UserExtend == nil || userInfo.UserExtend.RecommendStaffId == 0 {
		service.Success(c, recommendInfoResponse)
		return
	}

	staff, _ := service.AccountProvider.Info(c.Request.Context(), &account.InfoRequest{ID: uint64(userInfo.UserExtend.RecommendStaffId), Domain: e.Domain_Erp, Scene: "base"})

	fmt.Println(staff)
	if staff == nil || staff.Info == nil || staff.IsExist == false {
		service.Success(c, recommendInfoResponse)
		return
	}

	recommendInfoResponse.CompanyInfoList = make([]*dto.CompanyInfo, 0)
	recommendInfoResponse.StaffId = staff.Info.ID
	recommendInfoResponse.StaffName = staff.Info.NickName
	recommendInfoResponse.StaffTelNum = staff.Info.TelNum
	recommendInfoResponse.IsRecommended = true

	res, err := service.DepartmentProvider.BaseSyncList(c.Request.Context(), &department.BaseAllRequest{Domain: e.Domain_Erp, UserID: uint32(staff.Info.ID)})
	if err != nil {
		service.Error(c, e.InvalidParams, err)
		return
	}

	for _, v := range res.Data {
		recommendInfoResponse.CompanyInfoList = append(recommendInfoResponse.CompanyInfoList, &dto.CompanyInfo{ID: v.ID, Name: v.Name})
	}

	service.Success(c, recommendInfoResponse)

	return
}
