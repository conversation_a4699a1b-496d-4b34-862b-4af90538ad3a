// Package account -----------------------------
// @file      : snedMsg.go
// <AUTHOR> JJXu
// @contact   : <EMAIL>
// @time      : 2025/5/19 17:00
// -------------------------------------------
package account

import (
	"context"
	"fmt"
	"github.com/fonchain_enterprise/client-auction/api/account"
	"github.com/fonchain_enterprise/client-auction/pkg/e"
	"github.com/fonchain_enterprise/client-auction/pkg/service"
)

func SendMsgFunc(c context.Context, zone, clientIp, telNum, domain string, signNo, mid uint32, scope string) (err error, code int) {
	// 不为空的非 86数字
	if zone != e.ZoneCn && zone != "" {

		// ============================== redis检查ip开始
		ip := clientIp
		fmt.Println("当前ip：", ip, ";手机号：", telNum, ";domain：", domain)

		tempReq := &account.SendNationMsgRequest{
			Domain:  domain,
			TelNum:  telNum,
			Project: e.AuctionDomain,
			SignNo:  signNo,
			MId:     mid,
			Scope:   scope,
		}
		_, err = service.AccountProvider.SendNationMsg(c, tempReq)
		if err != nil {
			if err.Error() == e.MsgSendWrong {
				return err, e.MsgSended
			}
			return err, e.Error
		}
	} else {
		_, err = service.AccountProvider.SendMsg(c, &account.SendMsgRequest{
			Domain:  domain,
			TelNum:  telNum,
			Project: e.AuctionDomain,
			SignNo:  signNo,
			MId:     mid,
			Scope:   scope,
		})
		if err != nil {
			if err.Error() == e.MsgSendWrong {
				return err, e.MsgSended
			}
			return err, e.Error
		}
	}
	return
}
