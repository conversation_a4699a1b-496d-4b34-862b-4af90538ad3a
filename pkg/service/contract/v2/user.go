package contract

import (
	"context"
	"fmt"
	"github.com/fonchain_enterprise/client-auction/api/custom_contract"
	"github.com/fonchain_enterprise/client-auction/pkg/middleware"
	modelContract "github.com/fonchain_enterprise/client-auction/pkg/model/contract"
	"github.com/fonchain_enterprise/client-auction/pkg/service"
	"go.uber.org/zap"
)

func UpdateUserInfo(loginUserInfo middleware.LoginInfo) (userInfo *custom_contract.UserInfo, err error) {
	ctx := context.Background()
	var userInfoPhoneResp *custom_contract.UserInfoV2Resp
	//查询用户信息
	if userInfoPhoneResp, err = service.CustomContractProvider.UserInfoV2(ctx, &custom_contract.UserInfoV2Req{
		UserID:       fmt.Sprint(loginUserInfo.ID),
		RegisterType: modelContract.RegisterTypeOnline,
	}); err != nil {
		return
	}
	zap.L().Info("UpdateUserInfo userInfoPhoneResp", zap.Any("userInfoPhoneResp", userInfoPhoneResp))
	zap.L().Info("UpdateUserInfo loginUserInfo", zap.Any("loginUserInfo", loginUserInfo))
	// id 身份证    passport 护照     other 其他
	var idType int32
	switch loginUserInfo.UserExtend.IdType {
	case "id":
		idType = 1
	case "passport":
		idType = 2
	case "other":
		idType = 3
	}
	userInfoReq := &custom_contract.UserInfo{
		CountryCode:       "",
		Phone:             loginUserInfo.TelNum,
		UserName:          loginUserInfo.RealName,
		Gender:            int32(loginUserInfo.Sex),
		Birthday:          "",
		Address:           loginUserInfo.UserExtend.Address,
		BankName:          loginUserInfo.UserExtend.BankName,
		BankNo:            loginUserInfo.UserExtend.BankNo,
		FddCustomerId:     loginUserInfo.FddInfo.CustomerId,
		CardId:            loginUserInfo.UserExtend.IdNo,
		CardType:          idType,
		CardTypeName:      loginUserInfo.UserExtend.IdType,
		IsMainLand:        int32(loginUserInfo.UserExtend.IsMainland),
		UserID:            fmt.Sprint(loginUserInfo.ID),
		FddRegisterStatus: 2,
		RegisterType:      modelContract.RegisterTypeOnline,
	}
	if userInfoPhoneResp.UserInfo == nil || userInfoPhoneResp.UserInfo.UserID == "" {
		// 更新用户信息
		zap.L().Info("UpdateUserInfoV2-1", zap.Any("userInfoReq", userInfoReq))
		_, err = service.CustomContractProvider.UpdateUserInfoV2(ctx, &custom_contract.UpdateUserInfoV2Req{
			UserInfo: userInfoReq,
		})
		userInfo = userInfoReq
		if err != nil {
			return
		}
	} else {
		go func() {
			// 更新用户信息
			zap.L().Info("UpdateUserInfoV2-2", zap.Any("userInfoReq", userInfoReq))
			_, err = service.CustomContractProvider.UpdateUserInfoV2(ctx, &custom_contract.UpdateUserInfoV2Req{
				UserInfo: userInfoReq,
			})
		}()
		userInfo = userInfoPhoneResp.UserInfo
	}
	return
}
