package contract

import (
	"context"
	"encoding/base64"
	"errors"
	"fmt"
	"github.com/fonchain_enterprise/client-auction/api/custom_contract"
	"github.com/fonchain_enterprise/client-auction/api/fenghe"
	appconfig "github.com/fonchain_enterprise/client-auction/cmd/config"
	"github.com/fonchain_enterprise/client-auction/pkg/common/cache"
	"github.com/fonchain_enterprise/client-auction/pkg/e"
	modelContract "github.com/fonchain_enterprise/client-auction/pkg/model/contract"
	"github.com/fonchain_enterprise/client-auction/pkg/model/response"
	"github.com/fonchain_enterprise/client-auction/pkg/service"
	"github.com/fonchain_enterprise/client-auction/pkg/service/common"
	"github.com/fonchain_enterprise/client-auction/pkg/service/contract"
	"github.com/fonchain_enterprise/client-auction/pkg/utils/fileutils"
	"github.com/gin-gonic/gin"
	"github.com/gin-gonic/gin/binding"
	"go.uber.org/zap"
	"os"
	"path/filepath"
	"time"
)

func SignOnline(ctx *gin.Context) {
	var (
		req          *custom_contract.SignOnlineV2Req
		respUserInfo *custom_contract.UserInfo
		signReq      modelContract.FddContractSignReq
		contractData []string
		isMainland   bool
		err          error
		url          string
	)
	//FIXME  签署过了 就不要签署了
	if err = ctx.ShouldBindBodyWith(&req, binding.JSON); err != nil {
		service.Error(ctx, e.InvalidParams, err)
		return
	}
	//if req.SourceType == "" {
	//	req.SourceType = "online"
	//}
	loginUserInfo := contract.GetLoginUserInfo(ctx)
	if loginUserInfo.ID == 0 {
		service.Error(ctx, e.Error, errors.New("请先登录"))
		return
	}
	if req.AuctionUuid == "" && req.SeriesUuid == "" {
		service.Error(ctx, e.Error, errors.New("系列或拍卖信息为空"))
		return
	}
	if loginUserInfo.UserExtend.IsMainland == 1 {
		isMainland = true
	}
	reply := cache.RedisClient.SetNX(fmt.Sprintf("SignOnline_%d", loginUserInfo.ID), time.Now().Unix(), time.Second*1)
	if !reply.Val() {
		service.Error(ctx, e.Error, errors.New("请求太频繁，稍后重试"))
		return
	}
	if respUserInfo, err = UpdateUserInfo(loginUserInfo); err != nil {
		service.Error(ctx, e.Error, errors.New(err.Error()))
		return
	}
	zap.L().Info("ProtocolSignOnline respUserInfo", zap.Any("respUserInfo", respUserInfo))
	req.SignOrder = 1
	if len(req.AuctionArtworkUuids) != 0 {
		req.SignOrder = 2
	}
	if isMainland { // 内地用户
		if loginUserInfo.FddInfo.CustomerId == "" {
			err = errors.New("法大大没有实名")
			service.Error(ctx, e.Error, err)
			return
		}
		if req.SourceType == "" {
			if len(req.AuctionArtworkUuids) > 0 {
				respAuctionInfoResp, _err := service.CustomContractProvider.AuctionArtworkInfo(context.Background(), &custom_contract.AuctionArtworkInfoReq{
					AuctionArtworkUuid: req.AuctionArtworkUuids[0],
					UserID:             respUserInfo.UserID,
				})
				if _err != nil {
					service.Error(ctx, e.Error, _err)
					return
				}
				req.SourceType = respAuctionInfoResp.AuctionType
			}
		}
		if signReq, contractData, err = fddContractSignReq(respUserInfo, req); err != nil {
			service.Error(ctx, e.Error, err)
			return
		}
		url, err = FddContractSign(signReq, contractData)
		if err != nil {
			service.Error(ctx, e.Error, err)
			return
		}
		service.Success(ctx, response.SignContractResp{
			SignType:     "fdd",
			Action:       "signContract",
			FddVerifyUrl: url,
		})
		return
	} else {
		req.UserInfo = respUserInfo
		respV2, err := service.CustomContractProvider.SignOnlineV2(ctx, req)
		if err != nil {
			service.Error(ctx, e.ERROR, err)
			return
		}
		zap.L().Info("SignOnlineV2 respV2", zap.Any("respV2", respV2))
		for _, v := range respV2.OrderNos {
			_, _err := service.FengheProvider.UpdateSeriesOrder(ctx, &fenghe.SeriesOrderData{OrderNo: v, SignStatus: 2})
			if _err != nil {
				zap.L().Error("UpdateSeriesOrder err", zap.Error(_err))
				service.Error(ctx, e.Error, _err)
				return
			}
		}
		service.Success(ctx, response.SignContractResp{
			SignType:     "custom",
			Action:       "",
			FddVerifyUrl: "",
		})
	}
}

func fddContractSignReq(respUserInfo *custom_contract.UserInfo, req *custom_contract.SignOnlineV2Req) (signReq modelContract.FddContractSignReq, contractData []string, err error) {
	var (
		bidData           []modelContract.BidInfo
		auctionInfoV2Resp *custom_contract.AuctionInfoV2Resp
		artworkName       string
		soldPrice         string
	)
	if req.SourceType == "local" {
		if len(req.AuctionArtworkUuids) == 0 {
			contractData = []string{modelContract.ContractName1, modelContract.ContractName2,
				modelContract.ContractName3, modelContract.ContractName4}
		} else {
			contractData = []string{modelContract.ContractName5, modelContract.ContractName6}
		}
	}
	if req.SourceType == "online" {
		contractData = []string{modelContract.ContractName1, modelContract.ContractName2,
			modelContract.ContractName3, modelContract.ContractName4, modelContract.ContractName5, modelContract.ContractName6}
	}

	auctionInfoV2Resp, err = service.CustomContractProvider.AuctionInfoV2(context.Background(),
		&custom_contract.AuctionInfoV2Req{AuctionUuid: req.AuctionUuid, SeriesUuid: req.SeriesUuid})
	if err != nil {
		return
	}
	var bidResp *custom_contract.ArtworkBidDataResp = &custom_contract.ArtworkBidDataResp{}
	var auctionArtworkUuids []string
	auctionArtworkUuids = req.AuctionArtworkUuids
	var auctionArtworkData []modelContract.AuctionArtwork
	signReq = modelContract.FddContractSignReq{
		UserID:        respUserInfo.UserID,
		FddCustomerId: respUserInfo.FddCustomerId,
		SignOrder:     req.SignOrder,
		Phone:         respUserInfo.Phone,
		LineType:      1,
		FddUserInfo: modelContract.FddUserInfo{
			UserName: respUserInfo.UserName,
			Address:  respUserInfo.Address,
			Gender:   modelContract.GenderMm[int32(respUserInfo.Gender)],
			CardType: "身份证",
			CardNo:   respUserInfo.CardId,
			Phone:    respUserInfo.Phone,
		},
		Online:             true,
		AuctionUuid:        req.AuctionUuid,
		AuctionArtworkData: auctionArtworkData,
		BuyName:            respUserInfo.UserName,
		SignDate:           time.Now().Format("2006-01-02"),
		SeriesUuid:         req.SeriesUuid,
		CallBackUrl:        req.CallbackUrl,
		SourceType:         req.SourceType,
	}
	cache.RedisClient.HSet("fddUrl", fmt.Sprint(respUserInfo.UserID), signReq.CallBackUrl)
	signReq.CallBackUrl = fmt.Sprint(respUserInfo.UserID)
	for _, auctionArtworkUuid := range auctionArtworkUuids {
		bidData, artworkName, soldPrice, bidResp, err = getBidData(respUserInfo.Phone, auctionArtworkUuid)
		if err != nil {
			return
		}
		var imgData2 []byte
		hdPicUrl := fmt.Sprintf("%s?x-oss-process=image/resize,m_fixed,h_80,w_120", bidResp.HdPic)
		hdPicPath, _ := fileutils.SaveUrlFileDisk(hdPicUrl, "./runtime", bidResp.Tfnum+filepath.Ext(bidResp.HdPic))
		imgData2, err = os.ReadFile(hdPicPath)
		hdPicBase64 := base64.StdEncoding.EncodeToString(imgData2)
		signReq.AuctionY = bidResp.AuctionY
		signReq.AuctionM = bidResp.AuctionM
		signReq.AuctionD = bidResp.AuctionD
		signReq.AuctionArtworkData = append(signReq.AuctionArtworkData, modelContract.AuctionArtwork{
			BidData:          bidData,
			ArtworkSize:      bidResp.ArtworkSize,
			HdPic:            hdPicBase64,
			Tfnum:            bidResp.Tfnum,
			AuctionBasePrice: bidResp.AuctionBasePrice,
			Commission:       bidResp.Commission,
			SoldPriceChinese: bidResp.SoldPriceChinese,
			ArtworkName:      artworkName,
			SoldPrice:        soldPrice,
			AuctionInfo: modelContract.AuctionInfo{
				AuctionLOT: bidResp.AuctionData.AuctionLOT,
			},
			AuctionArtworkUuid: auctionArtworkUuid,
		})
	}
	signReq.AuctionInfo = modelContract.AuctionInfo{
		AuctionTime:     auctionInfoV2Resp.AuctionInfo.AuctionTime,
		AuctionAddress:  auctionInfoV2Resp.AuctionInfo.AuctionAddress,
		AuctionName:     auctionInfoV2Resp.AuctionInfo.AuctionName,
		PreviewStartY:   auctionInfoV2Resp.AuctionInfo.PreviewStartY,
		PreviewStartM:   auctionInfoV2Resp.AuctionInfo.PreviewStartM,
		PreviewStartD:   auctionInfoV2Resp.AuctionInfo.PreviewStartD,
		PreviewStartHis: auctionInfoV2Resp.AuctionInfo.PreviewStartHis,
		PreviewEndY:     auctionInfoV2Resp.AuctionInfo.PreviewEndY,
		PreviewEndM:     auctionInfoV2Resp.AuctionInfo.PreviewEndM,
		PreviewEndD:     auctionInfoV2Resp.AuctionInfo.PreviewEndD,
		PreviewEndHis:   auctionInfoV2Resp.AuctionInfo.PreviewEndHis,
		PreviewTime:     auctionInfoV2Resp.AuctionInfo.PreviewTime,
	}
	return
}

func getBidData(phone, auctionArtworkUuid string) (bidData []modelContract.BidInfo, artworkName, soldPrice string, respBid *custom_contract.ArtworkBidDataResp, err error) {
	respBid = &custom_contract.ArtworkBidDataResp{}
	respBid, err = service.CustomContractProvider.ArtworkBidData(context.Background(), &custom_contract.ArtworkBidDataReq{
		AuctionArtworkUuid: auctionArtworkUuid, Phone: phone,
	})
	if err != nil {
		return
	}
	if respBid == nil || len(respBid.BigData) == 0 {
		err = errors.New("没有获取竞拍记录数据")
		return
	}
	for _, v := range respBid.BigData {
		bidData = append(bidData, modelContract.BidInfo{
			BidNo:    v.BidNo,
			BidPrice: v.BidPrice,
		})
	}
	artworkName = respBid.ArtworkName
	soldPrice = respBid.SoldPrice
	return
}

func ViewContract(ctx *gin.Context) {
	var (
		req          *custom_contract.ContractViewV2Req
		viewResp     *custom_contract.ContractViewV2Resp
		respUserInfo *custom_contract.UserInfo
		err          error
	)
	if err = ctx.ShouldBindBodyWith(&req, binding.JSON); err != nil {
		service.Error(ctx, e.InvalidParams, err)
		return
	}
	//if req.SourceType == "" {
	//	req.SourceType = "online"
	//}
	loginUserInfo := contract.GetLoginUserInfo(ctx)
	zap.L().Info("ViewContract loginUserInfo", zap.Any("loginUserInfo", loginUserInfo))
	if respUserInfo, err = UpdateUserInfo(loginUserInfo); err != nil {
		service.Error(ctx, e.Error, errors.New(err.Error()))
		return
	}
	req.UserID = fmt.Sprint(respUserInfo.UserID)
	//if req.SourceType == "" {
	//	req.SourceType = "online"
	//}
	if viewResp, err = service.CustomContractProvider.ContractViewV2(ctx, req); err != nil {
		service.Error(ctx, e.ERROR, err)
		return
	}
	service.Success(ctx, viewResp)
}

func NeedSign(ctx *gin.Context) {
	var (
		req          *custom_contract.UserContractNeedSignReq
		resp         *custom_contract.UserContractNeedSignResp
		respUserInfo *custom_contract.UserInfo
		err          error
	)
	if err = ctx.ShouldBindBodyWith(&req, binding.JSON); err != nil {
		service.Error(ctx, e.InvalidParams, err)
		return
	}
	loginUserInfo := contract.GetLoginUserInfo(ctx)
	if loginUserInfo.ID == 0 {
		service.Error(ctx, e.Error, errors.New("请先登录"))
		return
	}
	if req.SeriesUuid == "" {
		service.Error(ctx, e.Error, errors.New("系列信息为空"))
		return
	}
	reply := cache.RedisClient.SetNX(fmt.Sprintf("NeedSign_%d", loginUserInfo.ID), time.Now().Unix(), time.Second*1)
	if !reply.Val() {
		service.Error(ctx, e.Error, errors.New("请求太频繁，稍后重试"))
		return
	}
	if respUserInfo, err = UpdateUserInfo(loginUserInfo); err != nil {
		service.Error(ctx, e.Error, errors.New(err.Error()))
		return
	}
	req.UserInfo = respUserInfo
	resp, err = service.CustomContractProvider.UserContractNeedSign(ctx, req)
	if err != nil {
		service.Error(ctx, e.ERROR, err)
		return
	}
	service.Success(ctx, resp)
}

func ArtworkContractView(ctx *gin.Context) {
	var (
		req      *custom_contract.ArtworkContractViewReq
		viewResp *custom_contract.ArtworkContractViewResp
		err      error
	)
	if err = ctx.ShouldBindBodyWith(&req, binding.JSON); err != nil {
		service.Error(ctx, e.InvalidParams, err)
		return
	}
	loginUserInfo := contract.GetLoginUserInfo(ctx)
	req.UserID = fmt.Sprint(loginUserInfo.ID)
	viewResp, err = service.CustomContractProvider.ArtworkContractView(context.Background(), req)
	if err != nil {
		service.Error(ctx, e.ERROR, err)
		return
	}
	service.Success(ctx, viewResp)
}

func SignedAuctionList(ctx *gin.Context) {
	var (
		req      *custom_contract.SignedAuctionListReq
		viewResp *custom_contract.SignedAuctionListResp
		err      error
	)
	if err = ctx.ShouldBindBodyWith(&req, binding.JSON); err != nil {
		service.Error(ctx, e.InvalidParams, err)
		return
	}
	loginUserInfo := contract.GetLoginUserInfo(ctx)
	req.UserID = fmt.Sprint(loginUserInfo.ID)
	viewResp, err = service.CustomContractProvider.SignedAuctionList(context.Background(), req)
	if err != nil {
		service.Error(ctx, e.ERROR, err)
		return
	}
	service.Success(ctx, viewResp)
}

func SignedAuctionContracts(ctx *gin.Context) {
	var (
		req      *custom_contract.SignedAuctionContractsReq
		viewResp *custom_contract.SignedAuctionContractsResp
		err      error
	)
	if err = ctx.ShouldBindBodyWith(&req, binding.JSON); err != nil {
		service.Error(ctx, e.InvalidParams, err)
		return
	}
	loginUserInfo := contract.GetLoginUserInfo(ctx)
	req.UserID = fmt.Sprint(loginUserInfo.ID)
	viewResp, err = service.CustomContractProvider.SignedAuctionContracts(context.Background(), req)
	if err != nil {
		service.Error(ctx, e.ERROR, err)
		return
	}
	service.Success(ctx, viewResp)
}

func DownloadContract(ctx *gin.Context) {
	var (
		req      *custom_contract.ArtworkContractViewReq
		viewResp *custom_contract.ArtworkContractViewResp
		err      error
	)
	if err = ctx.ShouldBindBodyWith(&req, binding.JSON); err != nil {
		service.Error(ctx, e.InvalidParams, err)
		return
	}
	//loginUserInfo := contract.GetLoginUserInfo(ctx)
	//req.UserID = fmt.Sprint(loginUserInfo.ID)
	viewResp, err = service.CustomContractProvider.ArtworkContractView(context.Background(), req)
	if err != nil {
		service.Error(ctx, e.ERROR, err)
		return
	}
	//var jmxyPath, jmxzPath, pmggPath, ppqrPath, ppblPath string
	if len(viewResp.Data) == 0 {
		service.Error(ctx, e.ERROR, errors.New("没有合同数据"))
		return
	}
	var url string
	for _, v := range viewResp.Data {
		if viewResp.SeriesType == 1 {
			if v.Jmxy1 == "" {
				service.Error(ctx, e.ERROR, errors.New("没有合同数据"))
				return
			}
			//下载合同打包成zip
			_, err = common.SaveUrlFileDisk(v.Jmxy1, "./runtime/contract/"+req.OrderNo, "竞买协议.pdf")
			_, err = common.SaveUrlFileDisk(v.Jmxz2, "./runtime/contract/"+req.OrderNo, "竞买须知.pdf")
			_, err = common.SaveUrlFileDisk(v.Pmgg3, "./runtime/contract/"+req.OrderNo, "拍卖公告.pdf")
			_, err = common.SaveUrlFileDisk(v.Ppqr5, "./runtime/contract/"+req.OrderNo, "拍卖确认书.pdf")
			_, err = common.SaveUrlFileDisk(v.Ppbl6, "./runtime/contract/"+req.OrderNo, "拍卖笔录.pdf")
		} else {
			_, err = common.SaveUrlFileDisk(v.Xsht1, "./runtime/contract/"+req.OrderNo, "销售合同.pdf")
		}
		// 压缩pdf
		zipPath := fmt.Sprintf("./runtime/%s_contract.zip", req.OrderNo)
		err = common.CompressFolderToZip("./runtime/contract/"+req.OrderNo, zipPath)
		if err != nil {
			service.Error(ctx, e.ERROR, err)
			return
		}
	}
	url = fmt.Sprintf(appconfig.AppConfig.Service.Host+"/api/v1/fenghe/static/%s_contract.zip", req.OrderNo)
	service.Success(ctx, map[string]string{"downloadUrl": url})
}

func Test(ctx *gin.Context) {
}
