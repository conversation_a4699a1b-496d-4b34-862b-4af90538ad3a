package contract

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"github.com/fonchain_enterprise/client-auction/api/contract"
	"github.com/fonchain_enterprise/client-auction/api/custom_contract"
	"github.com/fonchain_enterprise/client-auction/api/fenghe"
	appconfig "github.com/fonchain_enterprise/client-auction/cmd/config"
	"github.com/fonchain_enterprise/client-auction/pkg/common/cache"
	"github.com/fonchain_enterprise/client-auction/pkg/common/oss"
	modelContract "github.com/fonchain_enterprise/client-auction/pkg/model/contract"
	"github.com/fonchain_enterprise/client-auction/pkg/service"
	"github.com/fonchain_enterprise/client-auction/pkg/service/common"
	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"go.uber.org/zap"
	"net/url"
	"strings"
	"time"
)

func FddContractSign(req modelContract.FddContractSignReq, contractData []string) (signUrl string, err error) {
	templateResp, err := service.CustomContractProvider.TemplateData(context.Background(), &custom_contract.TemplateDataReq{TemplateId: ""})
	if err != nil {
		return
	}
	zap.L().Info("FddContractSign TemplateData", zap.Any("templateResp", templateResp))
	if len(contractData) == 0 {
		err = errors.New("未查询到法大大合同！")
		return
	}
	var templateContracts []modelContract.ContractInfo
	for _, v := range templateResp.Data {
		for _, vv := range contractData {
			if vv == v.ContractName {
				templateContracts = append(templateContracts, modelContract.ContractInfo{
					TemplateId:   v.FddTemplateId,
					ContractId:   uuid.NewString(),
					ContractName: v.ContractName,
					ShortName:    v.ShortName,
				})
			}
		}
	}
	getFddSignInfo := modelContract.GetFddSignInfo()
	var signData []*contract.SignData
	var signInfo *contract.SignData
	batchID := fmt.Sprintf("PZJ_B_%d", time.Now().UnixNano())
	for _, v := range templateContracts {
		//wg.Add(1)
		info := v
		// 竞买协议、竞买须知、拍卖公告、拍卖规则
		//go func(info modelContract.ContractInfo) {
		//	wg.Done()
		if getFddSignInfo[info.ContractName].SignKeyword == "" {
			continue
		}
		GenerateContractReq := &contract.GenerateContractRequest{
			TemplateId: info.TemplateId,
			ContractId: info.ContractId,
		}
		if v.ContractName == modelContract.ContractName1 {
			param := modelContract.ContractJmxy1{
				UserName:       req.FddUserInfo.UserName,
				Address:        req.FddUserInfo.Address,
				Gender:         req.FddUserInfo.Gender,
				CardType:       req.FddUserInfo.CardType,
				CardNo:         req.FddUserInfo.CardNo,
				Phone:          req.FddUserInfo.Phone,
				PrevStartY:     req.AuctionInfo.PreviewStartY,
				PrevStartM:     req.AuctionInfo.PreviewStartM,
				PrevStartD:     req.AuctionInfo.PreviewStartD,
				PrevStartHis:   req.AuctionInfo.PreviewStartHis,
				PrevEndY:       req.AuctionInfo.PreviewEndY,
				PrevEndM:       req.AuctionInfo.PreviewEndM,
				PrevEndD:       req.AuctionInfo.PreviewEndD,
				PrevEndHis:     req.AuctionInfo.PreviewEndHis,
				AuctionTime:    req.AuctionInfo.AuctionTime,
				AuctionAddress: req.AuctionInfo.AuctionAddress,
				SignDate:       req.SignDate,
			}
			paramStr, _err := json.Marshal(param)
			if _err != nil {
				err = errors.New("序列化错误")
				return
			}
			GenerateContractReq.ParameterMap = string(paramStr)
			if signInfo, err = GenerateContract(GenerateContractReq, v, req, batchID); err != nil {
				return
			}
			signData = append(signData, signInfo)
		}
		if v.ContractName == modelContract.ContractName2 {
			param := modelContract.ContractJmxz2{
				AuctionTime:    req.AuctionInfo.AuctionTime,
				AuctionAddress: req.AuctionInfo.AuctionAddress,
				AuctionUrl:     "豊和APP",
			}
			paramStr, _err := json.Marshal(param)
			if _err != nil {
				err = errors.New("序列化错误")
				return
			}
			GenerateContractReq.ParameterMap = string(paramStr)
			if signInfo, err = GenerateContract(GenerateContractReq, v, req, batchID); err != nil {
				return
			}
			signData = append(signData, signInfo)
		}
		if v.ContractName == modelContract.ContractName3 {
			param := modelContract.ContractPmgg3{
				AuctionTime:        req.AuctionInfo.AuctionTime,
				AuctionAddress:     req.AuctionInfo.AuctionAddress,
				AuctionUrl:         "豊和APP",
				AuctionPreviewTime: req.AuctionInfo.PreviewTime,
				SignDate:           req.SignDate,
			}
			paramStr, _err := json.Marshal(param)
			if _err != nil {
				err = errors.New("序列化错误")
				return
			}
			GenerateContractReq.ParameterMap = string(paramStr)
			if signInfo, err = GenerateContract(GenerateContractReq, v, req, batchID); err != nil {
				return
			}
			signData = append(signData, signInfo)
		}

		for _, auctionArtwork := range req.AuctionArtworkData {
			if v.ContractName == modelContract.ContractName5 {
				param := modelContract.ContractPpqr5{
					UserName:         req.FddUserInfo.UserName,
					Address:          req.FddUserInfo.Address,
					Gender:           req.FddUserInfo.Gender,
					CardType:         req.FddUserInfo.CardType,
					CardNo:           req.FddUserInfo.CardNo,
					Phone:            req.FddUserInfo.Phone,
					ArtworkName:      auctionArtwork.ArtworkName,
					ArtworkSize:      auctionArtwork.ArtworkSize,
					AuctionY:         req.AuctionY,
					AuctionM:         req.AuctionM,
					AuctionD:         req.AuctionD,
					AuctionTitle:     req.AuctionInfo.AuctionName,
					Tfnum:            auctionArtwork.Tfnum,
					SoldPrice:        auctionArtwork.SoldPrice,
					SoldPriceChinese: auctionArtwork.SoldPriceChinese,
					AuctionLOT:       auctionArtwork.AuctionInfo.AuctionLOT,
					SignDate:         req.SignDate,
				}
				paramStr, _err := json.Marshal(param)
				if _err != nil {
					err = errors.New("序列化错误")
					return
				}
				GenerateContractReq.ContractId = uuid.NewString()
				GenerateContractReq.ParameterMap = string(paramStr)
				req.AuctionArtworkUuid = auctionArtwork.AuctionArtworkUuid
				if signInfo, err = GenerateContract(GenerateContractReq, v, req, batchID); err != nil {
					return
				}
				signData = append(signData, signInfo)
			}
			if v.ContractName == modelContract.ContractName6 {
				var bidDataDynamicTables []modelContract.BidDataDynamicTables
				tempInfo := modelContract.BidDataDynamicTables{
					InsertWay:               1,
					Keyword:                 "竞拍记录",
					Datas:                   [][]string{},
					BorderFlag:              true,
					Headers:                 []string{"竟买号牌", "竞买价格"},
					ColWidthPercent:         []int{2, 2},
					CellHeight:              30,
					CellHorizontalAlignment: 0,
					CellVerticalAlignment:   5,
				}
				for _, vv := range auctionArtwork.BidData {
					tempInfo.Datas = append(tempInfo.Datas, []string{vv.BidNo, vv.BidPrice})
				}
				bidDataDynamicTables = append(bidDataDynamicTables, tempInfo)
				bidTableBytes, _ := json.Marshal(bidDataDynamicTables)
				GenerateContractReq.DynamicTables = string(bidTableBytes)
				// 成交信息
				param := modelContract.SolidInfo{
					ArtworkName:       auctionArtwork.ArtworkName,
					SoldPrice:         auctionArtwork.SoldPrice,
					AuctionTime:       req.AuctionInfo.AuctionTime,
					AuctionAddress:    req.AuctionInfo.AuctionAddress,
					ArtworkPic:        auctionArtwork.HdPic,
					AuctionLOT:        auctionArtwork.AuctionInfo.AuctionLOT,
					AuctionBasePrice:  auctionArtwork.AuctionBasePrice,
					Commission:        "", //auctionArtwork.Commission
					SignDate:          req.SignDate,
					BuyName:           req.BuyName,
					BuyY:              req.AuctionY,
					BuyM:              req.AuctionM,
					BuyD:              req.AuctionD,
					CardNo:            req.FddUserInfo.CardNo,
					SolidPriceChinese: auctionArtwork.SoldPriceChinese,
				}
				paramStr, _err := json.Marshal(param)
				if _err != nil {
					err = errors.New("序列化错误")
					return
				}
				GenerateContractReq.ContractId = uuid.NewString()
				GenerateContractReq.ParameterMap = string(paramStr)
				req.AuctionArtworkUuid = auctionArtwork.AuctionArtworkUuid
				if signInfo, err = GenerateContract(GenerateContractReq, v, req, batchID); err != nil {
					return
				}
				signData = append(signData, signInfo)
			}
		}
		//}(info)
	}
	callbackUrl := url.QueryEscape(req.CallBackUrl)
	fddUrl := fmt.Sprintf("%s/api/v2/fenghe/contract/fdd-sign-callback?batchNo=%s&time=%d&callbackUrl=%s",
		appconfig.AppConfig.Service.Host, batchID, time.Now().Unix(), callbackUrl)
	if req.Online {
		fddUrl = fmt.Sprintf("%s&online=1", fddUrl)
	}
	//if config.AppConfig.Service.AppMode != "prod" {
	//	fddUrl = fmt.Sprintf("%s&returnHost=%s&returnEndPoint=%s", fddUrl, req.TestReturnHost, req.TestReturnEndPoint)
	//}
	fmt.Println("fddUrl", fddUrl)
	zap.L().Info("GenerateContractInBatches start", zap.Any("auctionArtworkUuids", req.AuctionArtworkUuid))
	batchReq := &contract.GenerateContractInBatchesRequest{
		BatchId:        batchID,
		BatchTitle:     "批量签署",
		SignData:       signData,
		CustomerId:     req.FddCustomerId,
		MobileSignType: "1",
		//一键签署需要增加 batchId、和type 参数 地址超过了500位，法大大报错了
		ReturnUrl: fddUrl,
		Fdd:       GetFddFromConfig(),
	}
	zap.L().Info("GenerateContractInBatches ing", zap.Any("batchReq", batchReq))
	result, err := service.ContractProvider.GenerateContractInBatches(context.Background(), batchReq)
	if err != nil {
		zap.L().Error("GenerateContractInBatches err", zap.Any("err", err))
		return
	}
	zap.L().Info("GenerateContractInBatches end", zap.Any("result", result))
	signUrl = result.JumpUrl
	//wg.Wait()
	return
}

func GetFddFromConfig() *contract.Fdd {
	return &contract.Fdd{
		AppId:       appconfig.AppConfig.Fdd.AppId,
		AppSecret:   appconfig.AppConfig.Fdd.AppSecret,
		Url:         appconfig.AppConfig.Fdd.URL,
		ContentType: appconfig.AppConfig.Fdd.ContentType,
		//FileContentType: config.AppConfig.Fdd.FileContentType,
	}
}

func GenerateContract(GenerateContractReq *contract.GenerateContractRequest, info modelContract.ContractInfo, req modelContract.FddContractSignReq, batchID string) (signInfo *contract.SignData, err error) {
	// 生成合同
	getFddSignInfo := modelContract.GetFddSignInfo()
	_, err = service.ContractProvider.GenerateContract(context.Background(), GenerateContractReq)
	if err != nil {
		return
	}
	_, err = service.CustomContractProvider.UpdateContract(context.Background(), &custom_contract.UpdateContractReq{
		ContractId:         GenerateContractReq.ContractId,
		TemplateId:         info.TemplateId,
		ContractName:       info.ContractName,
		UserId:             req.UserID,
		SignType:           2,
		BatchNo:            batchID,
		Status:             1,
		SignOrder:          req.SignOrder,
		Phone:              req.Phone,
		ShortName:          getFddSignInfo[info.ContractName].ShortName,
		LineType:           req.LineType,
		AuctionUuid:        req.AuctionUuid,
		AuctionArtworkUuid: req.AuctionArtworkUuid,
		SeriesUuid:         req.SeriesUuid,
		SourceType:         req.SourceType,
	})
	if err != nil {
		return
	}
	signInfo = &contract.SignData{
		ContractId:    GenerateContractReq.ContractId,
		TransactionId: fmt.Sprintf("Auction%d", time.Now().UnixNano()),
		SignKeyword:   getFddSignInfo[info.ContractName].SignKeyword,
		Keyx:          getFddSignInfo[info.ContractName].Keyx,
		Keyy:          getFddSignInfo[info.ContractName].Keyy,
	}
	return
}

func FddSignCallback(ctx *gin.Context) {
	queryParams := ctx.Request.URL.Query()
	fddInfoByte, _ := json.Marshal(queryParams)
	var returnUrl string
	fmt.Println(queryParams.Get("key"))
	fmt.Println(string(fddInfoByte))
	batchNo := queryParams.Get("batchNo")
	req := &custom_contract.UpdateContractBatchReq{
		BatchNo: queryParams.Get("batchNo"),
		Status:  2,
		FddInfo: string(fddInfoByte),
	}
	_, err := service.CustomContractProvider.UpdateContractBatch(ctx, req)
	returnUrl = queryParams.Get("callbackUrl")
	//returnUrl, _ = url.QueryUnescape(returnUrl)
	key := fmt.Sprint(returnUrl)
	fddVal := cache.RedisClient.HGet("fddUrl", key)
	returnUrl = fddVal.Val()
	cache.RedisClient.HDel("fddUrl", key)
	// 查询批量数据
	batchResp, err := service.CustomContractProvider.ContractData(ctx, &custom_contract.ContractDataReq{
		BatchNo: batchNo,
	})
	if err != nil {
		ctx.Redirect(302, returnUrl)
		return
	}
	for _, v := range batchResp.Data {
		var viewResp *contract.JumpCommonResponse
		viewResp, err = service.ContractProvider.ViewContract(ctx, &contract.ContractRequest{ContractId: v.ContractId})
		if err == nil {
			downloadUrl, _ := FddDownload(v.ContractId)
			respV2, _ := service.CustomContractProvider.UpdateContract(ctx, &custom_contract.UpdateContractReq{
				ContractId:  v.ContractId,
				ViewUrl:     viewResp.JumpUrl,
				DownloadUrl: downloadUrl,
				NeedOrderNo: true,
			})
			zap.L().Info("FddSignCallback UpdateContract respV2", zap.Any("respV2", respV2))
			for _, orderNo := range respV2.OrderNos {
				_, err = service.FengheProvider.UpdateSeriesOrder(ctx, &fenghe.SeriesOrderData{OrderNo: orderNo, SignStatus: 2})
				if err != nil {
					zap.L().Error("UpdateSeriesOrder err", zap.Error(err))
				}
			}
		}
	}
	ctx.Redirect(302, returnUrl)
	return
}

func FddDownload(contractId string) (ossUrl string, err error) {
	var (
		resp     *contract.JumpCommonResponse
		fullPath string
	)
	resp, err = service.ContractProvider.DownLoadContractPdf(context.Background(), &contract.ContractRequest{
		ContractId: contractId,
	})
	if err != nil {
		return
	}
	fullPath, err = common.SaveUrlFileDisk(resp.JumpUrl, "./runtime/contract", contractId+".pdf")
	if err != nil {
		return
	}
	// 下载pdf，转存oss
	ossUrl, err = oss.OssUploadLocal(strings.Replace(fullPath, "./runtime", "auction", 1), fullPath)
	return
}
