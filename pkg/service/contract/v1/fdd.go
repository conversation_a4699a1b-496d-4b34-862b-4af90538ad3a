package v1

import (
	"context"
	"dubbo.apache.org/dubbo-go/v3/common/logger"
	"encoding/json"
	"errors"
	"fmt"
	"github.com/fonchain_enterprise/client-auction/api/contract"
	"github.com/fonchain_enterprise/client-auction/api/custom_contract"
	"github.com/fonchain_enterprise/client-auction/cmd/config"
	"github.com/fonchain_enterprise/client-auction/pkg/e"
	modelContract "github.com/fonchain_enterprise/client-auction/pkg/model/contract"
	"github.com/fonchain_enterprise/client-auction/pkg/service"
	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"strconv"
	"sync"
	"time"
)

var wg sync.WaitGroup

// 上传模板
func UploadTemplate(ctx *gin.Context) {
	templateID := ctx.PostForm("templateId")
	if templateID == "" {
		service.Error(ctx, e.Failed, errors.New(e.GetMsg(e.InvalidParams)))
		return
	}
	respTemplate, err := service.CustomContractProvider.TemplateData(context.Background(), &custom_contract.TemplateDataReq{TemplateId: templateID})
	if err != nil {
		service.Error(ctx, e.Failed, err)
		return
	}
	if respTemplate == nil || respTemplate.Data == nil || len(respTemplate.Data) == 0 {
		service.Error(ctx, e.Failed, errors.New(e.GetMsg(e.NOTDATA)))
		return
	}
	//service.Success(ctx, respTemplate)
	respUpload, err := service.ContractProvider.UploadTemplate(context.Background(), &contract.UploadTemplateRequest{
		TemplateId: respTemplate.Data[0].FddTemplateId,
		DocUrl:     respTemplate.Data[0].ContractCdnUrl,
	})
	if err != nil {
		service.Error(ctx, e.Failed, err)
		return
	}
	service.Success(ctx, respUpload)
}

//线上签合同
func FddContractSign(req modelContract.FddContractSignReq, contractData []string) (url string, err error) {
	templateResp, err := service.CustomContractProvider.TemplateData(context.Background(), &custom_contract.TemplateDataReq{TemplateId: ""})
	if err != nil {
		return
	}
	if len(contractData) == 0 {
		err = errors.New("未查询到法大大合同")
		return
	}
	var templateContracts []modelContract.ContractInfo
	for _, v := range templateResp.Data {
		for _, vv := range contractData {
			if vv == v.ContractName {
				templateContracts = append(templateContracts, modelContract.ContractInfo{
					TemplateId:   v.FddTemplateId,
					ContractId:   uuid.NewString(),
					ContractName: v.ContractName,
					ShortName:    v.ShortName,
				})
			}
		}
	}
	getFddSignInfo := modelContract.GetFddSignInfo()
	var signData []*contract.SignData
	var signInfo *contract.SignData
	batchID := fmt.Sprintf("PZJ_B_%d", time.Now().UnixNano())
	for _, v := range templateContracts {
		//wg.Add(1)
		info := v
		// 竞买协议、竞买须知、拍卖公告、拍卖规则
		//go func(info modelContract.ContractInfo) {
		//	wg.Done()
		if getFddSignInfo[info.ContractName].SignKeyword == "" {
			continue
		}
		GenerateContractReq := &contract.GenerateContractRequest{
			TemplateId: info.TemplateId,
			ContractId: info.ContractId,
		}
		if v.ContractName == modelContract.ContractName1 {
			param := modelContract.ContractJmxy1{
				UserName:       req.FddUserInfo.UserName,
				Address:        req.FddUserInfo.Address,
				Gender:         req.FddUserInfo.Gender,
				CardType:       req.FddUserInfo.CardType,
				CardNo:         req.FddUserInfo.CardNo,
				Phone:          req.FddUserInfo.Phone,
				PrevStartY:     req.AuctionInfo.PreviewStartY,
				PrevStartM:     req.AuctionInfo.PreviewStartM,
				PrevStartD:     req.AuctionInfo.PreviewStartD,
				PrevStartHis:   req.AuctionInfo.PreviewStartHis,
				PrevEndY:       req.AuctionInfo.PreviewEndY,
				PrevEndM:       req.AuctionInfo.PreviewEndM,
				PrevEndD:       req.AuctionInfo.PreviewEndD,
				PrevEndHis:     req.AuctionInfo.PreviewEndHis,
				AuctionTime:    req.AuctionInfo.AuctionTime,
				AuctionAddress: req.AuctionInfo.AuctionAddress,
				SignDate:       req.SignDate,
			}
			paramStr, _err := json.Marshal(param)
			if _err != nil {
				err = errors.New("序列化错误")
				return
			}
			GenerateContractReq.ParameterMap = string(paramStr)
			if signInfo, err = GenerateContract(GenerateContractReq, v, req, batchID); err != nil {
				return
			}
			signData = append(signData, signInfo)
		}
		if v.ContractName == modelContract.ContractName2 {
			param := modelContract.ContractJmxz2{
				AuctionTime:    req.AuctionInfo.AuctionTime,
				AuctionAddress: req.AuctionInfo.AuctionAddress,
				AuctionUrl:     "豊和APP",
			}
			paramStr, _err := json.Marshal(param)
			if _err != nil {
				err = errors.New("序列化错误")
				return
			}
			GenerateContractReq.ParameterMap = string(paramStr)
			if signInfo, err = GenerateContract(GenerateContractReq, v, req, batchID); err != nil {
				return
			}
			signData = append(signData, signInfo)
		}
		if v.ContractName == modelContract.ContractName3 {
			param := modelContract.ContractPmgg3{
				AuctionTime:        req.AuctionInfo.AuctionTime,
				AuctionAddress:     req.AuctionInfo.AuctionAddress,
				AuctionUrl:         "豊和APP",
				AuctionPreviewTime: req.AuctionInfo.PreviewTime,
				SignDate:           req.SignDate,
			}
			paramStr, _err := json.Marshal(param)
			if _err != nil {
				err = errors.New("序列化错误")
				return
			}
			GenerateContractReq.ParameterMap = string(paramStr)
			if signInfo, err = GenerateContract(GenerateContractReq, v, req, batchID); err != nil {
				return
			}
			signData = append(signData, signInfo)
		}

		for _, auctionArtwork := range req.AuctionArtworkData {
			if v.ContractName == modelContract.ContractName5 {
				param := modelContract.ContractPpqr5{
					UserName:         req.FddUserInfo.UserName,
					Address:          req.FddUserInfo.Address,
					Gender:           req.FddUserInfo.Gender,
					CardType:         req.FddUserInfo.CardType,
					CardNo:           req.FddUserInfo.CardNo,
					Phone:            req.FddUserInfo.Phone,
					ArtworkName:      auctionArtwork.ArtworkName,
					ArtworkSize:      auctionArtwork.ArtworkSize,
					AuctionY:         req.AuctionY,
					AuctionM:         req.AuctionM,
					AuctionD:         req.AuctionD,
					AuctionTitle:     req.AuctionInfo.AuctionName,
					Tfnum:            auctionArtwork.Tfnum,
					SoldPrice:        auctionArtwork.SoldPrice,
					SoldPriceChinese: auctionArtwork.SoldPriceChinese,
					AuctionLOT:       auctionArtwork.AuctionLOT,
					SignDate:         req.SignDate,
				}
				paramStr, _err := json.Marshal(param)
				if _err != nil {
					err = errors.New("序列化错误")
					return
				}
				GenerateContractReq.ContractId = uuid.NewString()
				GenerateContractReq.ParameterMap = string(paramStr)
				req.AuctionArtworkUuid = auctionArtwork.AuctionArtworkUuid
				if signInfo, err = GenerateContract(GenerateContractReq, v, req, batchID); err != nil {
					return
				}
				signData = append(signData, signInfo)
			}
			if v.ContractName == modelContract.ContractName6 {
				var bidDataDynamicTables []modelContract.BidDataDynamicTables
				tempInfo := modelContract.BidDataDynamicTables{
					InsertWay:               1,
					Keyword:                 "竞拍记录",
					Datas:                   [][]string{},
					BorderFlag:              true,
					Headers:                 []string{"竟买号牌", "竞买价格"},
					ColWidthPercent:         []int{2, 2},
					CellHeight:              30,
					CellHorizontalAlignment: 0,
					CellVerticalAlignment:   5,
				}
				for _, vv := range auctionArtwork.BidData {
					tempInfo.Datas = append(tempInfo.Datas, []string{vv.BidNo, vv.BidPrice})
				}
				bidDataDynamicTables = append(bidDataDynamicTables, tempInfo)
				bidTableBytes, _ := json.Marshal(bidDataDynamicTables)
				GenerateContractReq.DynamicTables = string(bidTableBytes)
				// 成交信息
				param := modelContract.SolidInfo{
					ArtworkName:       auctionArtwork.ArtworkName,
					SoldPrice:         auctionArtwork.SoldPrice,
					AuctionTime:       req.AuctionInfo.AuctionTime,
					AuctionAddress:    req.AuctionInfo.AuctionAddress,
					ArtworkPic:        auctionArtwork.HdPic,
					AuctionLOT:        auctionArtwork.AuctionLOT,
					AuctionBasePrice:  auctionArtwork.AuctionBasePrice,
					Commission:        "", //auctionArtwork.Commission
					SignDate:          req.SignDate,
					BuyName:           req.BuyName,
					BuyY:              req.AuctionY,
					BuyM:              req.AuctionM,
					BuyD:              req.AuctionD,
					CardNo:            req.FddUserInfo.CardNo,
					SolidPriceChinese: auctionArtwork.SoldPriceChinese,
				}
				paramStr, _err := json.Marshal(param)
				if _err != nil {
					err = errors.New("序列化错误")
					return
				}
				GenerateContractReq.ContractId = uuid.NewString()
				GenerateContractReq.ParameterMap = string(paramStr)
				req.AuctionArtworkUuid = auctionArtwork.AuctionArtworkUuid
				if signInfo, err = GenerateContract(GenerateContractReq, v, req, batchID); err != nil {
					return
				}
				signData = append(signData, signInfo)
			}
		}

		//}(info)
	}
	fddUrl := fmt.Sprintf("%s/api/v1/fenghe/contract/fdd-sign-callback?batchNo=%s&time=%d&signOrder=%d",
		config.AppConfig.Service.Host, batchID, time.Now().Unix(), req.SignOrder)
	if req.Online {
		fddUrl = fmt.Sprintf("%s&online=1", fddUrl)
	}
	//if config.AppConfig.Service.AppMode != "prod" {
	//	fddUrl = fmt.Sprintf("%s&returnHost=%s&returnEndPoint=%s", fddUrl, req.TestReturnHost, req.TestReturnEndPoint)
	//}
	fmt.Println("fddUrl", fddUrl)
	result, err := service.ContractProvider.GenerateContractInBatches(context.Background(), &contract.GenerateContractInBatchesRequest{
		BatchId:        batchID,
		BatchTitle:     "批量签署",
		SignData:       signData,
		CustomerId:     req.FddCustomerId,
		MobileSignType: "1",
		//一键签署需要增加 batchId、和type 参数 地址超过了500位，法大大报错了
		ReturnUrl: fddUrl,
		Fdd:       GetFddFromConfig(),
	})
	if err != nil {
		return
	}
	url = result.JumpUrl
	//wg.Wait()
	return
}

func FddRegisterCallback(ctx *gin.Context) {
	queryParams := ctx.Request.URL.Query()
	fddInfoByte, _ := json.Marshal(queryParams)
	fddStatus, _ := strconv.ParseInt(queryParams.Get("status"), 10, 32)
	var returnUrl string
	logger.Errorf("FddRegisterCallbackqueryParams", queryParams)
	var userId string
	fmt.Println(queryParams.Get("key"))
	fmt.Println(string(fddInfoByte))
	req := &custom_contract.UpdateUserInfoReq{
		UserId:        queryParams.Get("key"),
		TransactionNo: queryParams.Get("transactionNo"),
		FddStatus:     int32(fddStatus),
		Status:        int32(fddStatus),
		FddInfo:       string(fddInfoByte),
		RegisterType:  modelContract.RegisterTypeOffline,
	}
	resp, err := service.CustomContractProvider.UpdateUserInfo(ctx, req)
	if err != nil {
		returnUrl = fmt.Sprintf("%s&errMsg=%s", returnUrl, err.Error())
	}
	if resp != nil {
		userId = resp.UserId
	}
	//returnUrl = cache.RedisClient.Get("fddRegisterReturnUrl").Val()
	//if config.AppConfig.Service.AppMode != "prod" {
	//	returnUrl = fmt.Sprintf("%s%s", queryParams.Get("returnHost"), queryParams.Get("returnEndPoint"))
	//} else {
	//if returnUrl == "" {
	//	returnUrl = fmt.Sprintf("%s/licenseIssuance/protocol", config.AppConfig.Service.Host)
	//}
	//}
	returnUrl = fmt.Sprintf("%s/licenseIssuance/personal-Info", config.AppConfig.Service.Host)
	returnUrl = fmt.Sprintf("%s?fddStatus=%d&userId=%s&number=1&auctionUid=%s", returnUrl, fddStatus, userId, queryParams.Get("auctionUuid"))
	ctx.Redirect(302, returnUrl)
	return
}

func FddSignCallback(ctx *gin.Context) {
	queryParams := ctx.Request.URL.Query()
	fddInfoByte, _ := json.Marshal(queryParams)
	var returnUrl string
	logger.Errorf("FddSignCallback", queryParams)
	fmt.Println(queryParams.Get("key"))
	fmt.Println(string(fddInfoByte))
	signOrder := queryParams.Get("signOrder")
	batchNo := queryParams.Get("batchNo")
	req := &custom_contract.UpdateContractBatchReq{
		BatchNo: queryParams.Get("batchNo"),
		Status:  2,
		FddInfo: string(fddInfoByte),
	}
	_, err := service.CustomContractProvider.UpdateContractBatch(ctx, req)
	if signOrder == "" {
		signOrder = "1"
	}
	//returnUrl = cache.RedisClient.Get(fmt.Sprintf("fddSignReturnUrl_%s", signOrder)).Val()
	//if config.AppConfig.Service.AppMode != "prod" {
	//	returnUrl = fmt.Sprintf("%s%s", queryParams.Get("returnHost"), queryParams.Get("returnEndPoint"))
	//} else {
	//if returnUrl == "" {

	if queryParams.Get("online") == "1" {
		//returnUrl = fmt.Sprintf("%s/pages/pay/index?type=auction", config.AppConfig.Service.Host)
		if config.AppConfig.Service.AppMode == "prod" {
			returnUrl = "https://shop.szjixun.cn/#/pages/pay/index?type=auction"
		} else {
			returnUrl = "https://auction-mall-test2.szjixun.cn/#/pages/pay/index?type=auction"
		}
	} else {
		if signOrder == "1" {
			returnUrl = fmt.Sprintf("%s/licenseIssuance/result?1=1", config.AppConfig.Service.Host)
		} else if signOrder == "2" {
			returnUrl = fmt.Sprintf("%s/collectCode/payment?1=1", config.AppConfig.Service.Host)
		}
	}
	//}
	//}

	returnUrl = fmt.Sprintf("%s&source=contract&batchNo=%s", returnUrl, batchNo)
	// 查询批量数据
	batchResp, err := service.CustomContractProvider.ContractData(ctx, &custom_contract.ContractDataReq{
		BatchNo: batchNo,
	})
	if err != nil {
		returnUrl = fmt.Sprintf("%s&errMsg=%s", returnUrl, err.Error())
		ctx.Redirect(302, returnUrl)
		return
	}
	for _, v := range batchResp.Data {
		var viewResp *contract.JumpCommonResponse
		viewResp, err = service.ContractProvider.ViewContract(ctx, &contract.ContractRequest{ContractId: v.ContractId})
		if err == nil {
			downloadUrl, _ := FddDownload(v.ContractId)
			_, _ = service.CustomContractProvider.UpdateContract(ctx, &custom_contract.UpdateContractReq{
				ContractId:  v.ContractId,
				ViewUrl:     viewResp.JumpUrl,
				DownloadUrl: downloadUrl,
			})
		}
	}
	//FIXME
	returnUrl = fmt.Sprintf("%s/licenseIssuance/result", config.AppConfig.Service.Host)
	ctx.Redirect(302, returnUrl)
	return
}

// 公司盖章 先不盖章，我在模板盖章
func CompanySignature(nodeID, customerId, signatureId string) {
	extSignAutoRequest := contract.ExtSignAutoRequest{
		TransactionId: fmt.Sprintf("PJ%d", time.Now().UnixNano()),
		ContractId:    nodeID,
		CustomerId:    customerId, //公司的帐号Id，王溢韬给
		DocTitle:      "拍卖笔录 成交确认书",
		SignKeyword:   "拍卖行",    //签章位置定位
		KeyX:          "10",        //位置定位
		KeyY:          "-5",        //位置定位
		SignatureId:   signatureId, // 王溢韬给
		//Fdd:           service.GetFddFromConfig(),
	}
	autoRes, err := service.ContractProvider.ExtSignAuto(context.Background(), &extSignAutoRequest) //指定
	fmt.Println(autoRes)
	fmt.Println(err)
}

func GenerateContract(GenerateContractReq *contract.GenerateContractRequest, info modelContract.ContractInfo, req modelContract.FddContractSignReq, batchID string) (signInfo *contract.SignData, err error) {
	// 生成合同
	getFddSignInfo := modelContract.GetFddSignInfo()
	_, err = service.ContractProvider.GenerateContract(context.Background(), GenerateContractReq)
	if err != nil {
		return
	}
	_, err = service.CustomContractProvider.UpdateContract(context.Background(), &custom_contract.UpdateContractReq{
		ContractId:         GenerateContractReq.ContractId,
		TemplateId:         info.TemplateId,
		ContractName:       info.ContractName,
		UserId:             req.UserID,
		SignType:           2,
		BatchNo:            batchID,
		Status:             1,
		SignOrder:          req.SignOrder,
		Phone:              req.Phone,
		ShortName:          getFddSignInfo[info.ContractName].ShortName,
		LineType:           req.LineType,
		AuctionUuid:        req.AuctionUuid,
		AuctionArtworkUuid: req.AuctionArtworkUuid,
	})
	if err != nil {
		return
	}
	signInfo = &contract.SignData{
		ContractId:    GenerateContractReq.ContractId,
		TransactionId: fmt.Sprintf("Auction%d", time.Now().UnixNano()),
		SignKeyword:   getFddSignInfo[info.ContractName].SignKeyword,
		Keyx:          getFddSignInfo[info.ContractName].Keyx,
		Keyy:          getFddSignInfo[info.ContractName].Keyy,
	}
	return
}
