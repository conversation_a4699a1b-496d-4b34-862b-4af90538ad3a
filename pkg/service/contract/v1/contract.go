package v1

import (
	"context"
	"dubbo.apache.org/dubbo-go/v3/common/logger"
	"encoding/base64"
	"errors"
	"fmt"
	api "github.com/fonchain_enterprise/client-auction/api/account"
	"github.com/fonchain_enterprise/client-auction/api/contract"
	"github.com/fonchain_enterprise/client-auction/api/custom_contract"
	"github.com/fonchain_enterprise/client-auction/cmd/config"
	"github.com/fonchain_enterprise/client-auction/pkg/common/cache"
	"github.com/fonchain_enterprise/client-auction/pkg/common/oss"
	"github.com/fonchain_enterprise/client-auction/pkg/e"
	"github.com/fonchain_enterprise/client-auction/pkg/middleware"
	modelContract "github.com/fonchain_enterprise/client-auction/pkg/model/contract"
	"github.com/fonchain_enterprise/client-auction/pkg/model/response"
	"github.com/fonchain_enterprise/client-auction/pkg/service"
	"github.com/fonchain_enterprise/client-auction/pkg/service/account"
	"github.com/fonchain_enterprise/client-auction/pkg/service/common"
	"github.com/fonchain_enterprise/client-auction/pkg/utils/fileutils"
	"github.com/fonchain_enterprise/client-auction/pkg/utils/stringutils"
	"github.com/gin-gonic/gin"
	"github.com/gin-gonic/gin/binding"
	"github.com/jinzhu/copier"
	"os"
	"path/filepath"
	"strings"
	"time"
)

func SignOffline(ctx *gin.Context) {
	var (
		req           *custom_contract.ProtocolSignOfflineReq
		resp          *custom_contract.ProtocolSignOfflineResp
		userInfoResp  *custom_contract.UserInfoReqResp
		fddCustomerId string
		userId        string
		err           error
		url           string
	)
	if err = ctx.ShouldBindBodyWith(&req, binding.JSON); err != nil {
		service.Error(ctx, e.Failed, errors.New(e.GetMsg(e.InvalidParams)))
		return
	}
	//FIXME
	req.UserInfo.CountryCode = "86"
	fmt.Println("SignOffline")
	fmt.Printf("%+v", req)
	fmt.Println("SignOffline")
	tempData := req.SignImgFileData
	req.SignImgFileData = ""
	logger.Errorf("ProtocolSignOfflineReq %+v", req)
	req.SignImgFileData = tempData
	// 判断是第一次还是二次
	if req.SignOrder == 0 {
		err = errors.New("签名次序为空")
		service.Error(ctx, e.Error, err)
		return
	}
	var isMainland bool = false
	if req.SignOrder == 1 {
		if req.UserInfo.CountryCode == "86" && req.UserInfo.CardType == 1 {
			isMainland = true
		}
		if req.UserInfo.CardId == "" {
			err = errors.New("身份证号码不能为空")
			service.Error(ctx, e.Error, err)
			return
		}
	}
	if req.SignOrder == 2 {
		if req.UserInfo.CountryCode == "86" {
			isMainland = true
		}
		//if req.AuctionArtworkUuid == "" {
		//	service.Error(ctx, e.Error, errors.New("画作编号错误"))
		//	return
		//}
	}
	if req.UserInfo == nil || req.UserInfo.Phone == "" {
		service.Error(ctx, e.Error, errors.New("手机号为空"))
		return
	}
	//查询用户信息
	userInfoPhoneResp, err := service.CustomContractProvider.UserInfoByPhone(ctx, &custom_contract.UserInfoByPhoneReq{
		Phone: req.UserInfo.Phone,
	})
	if err != nil {
		service.Error(ctx, e.ERROR, err)
		return
	}
	if userInfoPhoneResp.SpecialType == 1 {
		isMainland = false
	}

	reply := cache.RedisClient.SetNX(fmt.Sprintf("SignOffline_%s", req.UserInfo.Phone), time.Now().Unix(), time.Second*1)
	if !reply.Val() {
		service.Error(ctx, e.Error, errors.New("请求太频繁，稍后重试"))
		return
	}

	// FIXME 杭州特殊操作
	req.SignOrder = 2
	isMainland = true
	req.UserInfo.CountryCode = "86"
	if req.SignOrder == 1 { // 第一次签名
		if req.UserInfo.CountryCode == "" {
			err = errors.New("国家码为空")
			service.Error(ctx, e.Error, err)
			return
		}
		if req.UserInfo.Phone == "" || req.UserInfo.UserName == "" {
			err = errors.New("手机号或者用户名为空")
			service.Error(ctx, e.Error, err)
			return
		}
		if isMainland {
			if userInfoResp, err = service.CustomContractProvider.UserInfo(context.Background(),
				&custom_contract.UserInfoReq{Phone: req.UserInfo.Phone, NeedRegister: true, CountryCode: req.UserInfo.CountryCode,
					CardId: req.UserInfo.CardId, RegisterType: modelContract.RegisterTypeOffline, Nickname: req.UserInfo.UserName}); err != nil {
				service.Error(ctx, e.Error, err)
				return
			}
			fddCustomerId = userInfoResp.FddCustomerId
			userId = userInfoResp.UserId
			if userInfoResp.FddCustomerId == "" || userInfoResp.FddRegisterStatus != 2 {
				if req.UserInfo.CardId == "" {
					err = errors.New("身份证号码不能为空")
					service.Error(ctx, e.Error, err)
					return
				}
				registerResult, _err := FddUserRegister(req.UserInfo.Phone, req.UserInfo.UserName, req.UserInfo.CardId, userId, req.TestReturnHost, req.TestReturnEndPoint, req.AuctionUuid)
				if _err != nil {
					service.Error(ctx, e.Error, _err)
					return
				}
				fddCustomerId = registerResult.CustomerId
				userInfoProto := &custom_contract.UserInfo{}
				_ = copier.Copy(userInfoProto, req.UserInfo)
				updateUserResp, _ := service.CustomContractProvider.UpdateUserInfo(context.Background(), &custom_contract.UpdateUserInfoReq{
					UserId:        userInfoResp.UserId,
					FddCustomerId: registerResult.CustomerId,
					IsMainland:    1,
					TransactionNo: registerResult.transactionNo,
					UserInfo:      userInfoProto,
					RegisterType:  modelContract.RegisterTypeOffline,
				})
				userId = updateUserResp.UserId
				service.Success(ctx, response.SignContractResp{
					SignType:     "fdd",
					Action:       "fddVerify",
					FddVerifyUrl: registerResult.url,
				})
				return
			}
			// 法大大合同第一次签署
			signReq := modelContract.FddContractSignReq{
				Online:             false,
				UserID:             userId,
				FddCustomerId:      fddCustomerId,
				SignOrder:          req.SignOrder,
				Phone:              req.UserInfo.Phone,
				LineType:           2,
				TestReturnHost:     req.TestReturnHost,
				TestReturnEndPoint: req.TestReturnEndPoint,
				FddUserInfo: modelContract.FddUserInfo{
					UserName: req.UserInfo.UserName,
					Address:  req.UserInfo.Address,
					Gender:   modelContract.GenderMm[req.UserInfo.Gender],
					CardType: modelContract.CardTypeMm[req.UserInfo.CardType],
					CardNo:   req.UserInfo.CardId,
					Phone:    req.UserInfo.Phone,
				},
			}
			url, err = FddContractSign(signReq, []string{modelContract.ContractName1, modelContract.ContractName2,
				modelContract.ContractName3, modelContract.ContractName4})
			if err != nil {
				service.Error(ctx, e.Error, err)
				return
			}
			service.Success(ctx, response.SignContractResp{
				SignType:     "fdd",
				Action:       "signContract",
				FddVerifyUrl: url,
			})
			return
		} else {
			resp, err = service.CustomContractProvider.ProtocolSignOffline(ctx, req)
			if err != nil {
				service.Error(ctx, e.ERROR, err)
				return
			}
		}
	} else if req.SignOrder == 2 { // 第二次签名
		var auctionUuid string
		if config.AppConfig.Service.AppMode != "prod" {
			auctionUuid = "a95910fc-5f9a-4b8a-b31e-536b49907a32" //a95910fc-5f9a-4b8a-b31e-536b49907a32
		} else {
			auctionUuid = "b51ba082-d993-46ab-807f-bd6b68b3e909"
		}
		if req.AuctionArtworkUuids, err = GetAuctionArtworkUuidsByPhone(req.UserInfo.Phone, auctionUuid); err != nil {
			service.Error(ctx, e.Error, err)
			return
		}
		if len(req.AuctionArtworkUuids) == 0 {
			service.Error(ctx, e.Error, errors.New("没有拍品信息"))
			return
		}
		if userInfoResp, err = service.CustomContractProvider.UserInfo(context.Background(),
			&custom_contract.UserInfoReq{Phone: req.UserInfo.Phone, NeedRegister: true, CountryCode: req.UserInfo.CountryCode,
				CardId: req.UserInfo.CardId, RegisterType: modelContract.RegisterTypeOffline, Nickname: req.UserInfo.UserName}); err != nil {
			service.Error(ctx, e.Error, err)
			return
		}
		fddCustomerId = userInfoResp.FddCustomerId
		userId = userInfoResp.UserId
		if userInfoResp.FddCustomerId == "" || userInfoResp.FddRegisterStatus != 2 {
			if req.UserInfo.CardId == "" {
				err = errors.New("身份证号码不能为空")
				service.Error(ctx, e.Error, err)
				return
			}
			registerResult, _err := FddUserRegister(req.UserInfo.Phone, req.UserInfo.UserName, req.UserInfo.CardId, userId, req.TestReturnHost, req.TestReturnEndPoint, req.AuctionUuid)
			if _err != nil {
				service.Error(ctx, e.Error, _err)
				return
			}
			fddCustomerId = registerResult.CustomerId
			userInfoProto := &custom_contract.UserInfo{}
			_ = copier.Copy(userInfoProto, req.UserInfo)
			updateUserResp, _ := service.CustomContractProvider.UpdateUserInfo(context.Background(), &custom_contract.UpdateUserInfoReq{
				UserId:        userInfoResp.UserId,
				FddCustomerId: registerResult.CustomerId,
				IsMainland:    1,
				TransactionNo: registerResult.transactionNo,
				UserInfo:      userInfoProto,
				RegisterType:  modelContract.RegisterTypeOffline,
			})
			userId = updateUserResp.UserId
			service.Success(ctx, response.SignContractResp{
				SignType:     "fdd",
				Action:       "fddVerify",
				FddVerifyUrl: registerResult.url,
			})
			return
		}
		req.AuctionArtworkUuid = req.AuctionArtworkUuids[0]
		if isMainland {
			var auctionArtworkData []modelContract.AuctionArtwork
			var auctionArtworkUuids []string
			auctionArtworkUuids = req.AuctionArtworkUuids
			if req.AuctionArtworkUuid != "" {
				auctionArtworkUuids = append(auctionArtworkUuids, req.AuctionArtworkUuid)
			}
			auctionArtworkUuids = stringutils.RemoveDuplicates(auctionArtworkUuids)
			var bidResp *custom_contract.ArtworkBidDataResp
			for _, auctionArtworkUuid := range auctionArtworkUuids {
				bidData, artworkName, soldPrice, respBid, _err := getBidData(req.UserInfo.Phone, auctionArtworkUuid)
				if _err != nil {
					service.Error(ctx, e.Error, _err)
					return
				}
				bidResp = respBid
				var imgData2 []byte
				hdPicUrl := fmt.Sprintf("%s?x-oss-process=image/resize,m_fixed,h_80,w_120", bidResp.HdPic)
				hdPicPath, _ := fileutils.SaveUrlFileDisk(hdPicUrl, "./runtime", bidResp.Tfnum+filepath.Ext(bidResp.HdPic))
				imgData2, err = os.ReadFile(hdPicPath)
				hdPicBase64 := base64.StdEncoding.EncodeToString(imgData2)
				auctionArtworkData = append(auctionArtworkData, modelContract.AuctionArtwork{
					BidData:            bidData,
					HdPic:              hdPicBase64,
					Tfnum:              bidResp.Tfnum,
					AuctionBasePrice:   bidResp.AuctionBasePrice,
					Commission:         bidResp.Commission,
					ArtworkName:        artworkName,
					ArtworkSize:        bidResp.ArtworkSize,
					SoldPrice:          soldPrice,
					SoldPriceChinese:   respBid.SoldPriceChinese,
					AuctionArtworkUuid: auctionArtworkUuid,
					AuctionLOT:         bidResp.AuctionData.AuctionLOT,
				})
			}
			//orderResp, _err := service.CustomContractProvider.OfflineSignOrder(ctx, &custom_contract.OfflineSignOrderReq{Phone: req.UserInfo.Phone})
			//if _err != nil {
			//	service.Error(ctx, e.ERROR, _err)
			//	return
			//}
			//if orderResp.SignOrder != 1 {
			//	err = errors.New("第一次合同签署未完成")
			//	service.Error(ctx, e.ERROR, err)
			//	return
			//}
			if userInfoResp, err = service.CustomContractProvider.UserInfo(ctx, &custom_contract.UserInfoReq{
				Phone:        req.UserInfo.Phone,
				Nickname:     req.UserInfo.UserName,
				RegisterType: 2,
			}); err != nil {
				service.Error(ctx, e.ERROR, err)
				return
			}
			if userInfoResp.UserId == "" || userInfoResp.FddCustomerId == "" {
				service.Error(ctx, e.ERROR, errors.New("未查询到用户信息"))
				return
			}
			url, err = FddContractSign(modelContract.FddContractSignReq{
				Online:             false,
				UserID:             userInfoResp.UserId,
				FddCustomerId:      userInfoResp.FddCustomerId,
				SignOrder:          2,
				Phone:              req.UserInfo.Phone,
				LineType:           2,
				AuctionArtworkData: auctionArtworkData,
				TestReturnHost:     req.TestReturnHost,
				TestReturnEndPoint: req.TestReturnEndPoint,
				AuctionArtworkUuid: req.AuctionArtworkUuid,
				FddUserInfo: modelContract.FddUserInfo{
					UserName: req.UserInfo.UserName,
					Address:  req.UserInfo.Address,
					Gender:   modelContract.GenderMm[req.UserInfo.Gender],
					CardType: modelContract.CardTypeMm[req.UserInfo.CardType],
					CardNo:   req.UserInfo.CardId,
					Phone:    req.UserInfo.Phone,
				},
				AuctionInfo: modelContract.AuctionInfo{
					AuctionTime:    bidResp.AuctionData.AuctionTime,
					AuctionAddress: bidResp.AuctionData.AuctionAddress,
					AuctionName:    bidResp.AuctionData.AuctionName,
					//AuctionLOT:      bidResp.AuctionData.AuctionLOT,
					PreviewStartY:   bidResp.AuctionData.PreviewStartY,
					PreviewStartM:   bidResp.AuctionData.PreviewStartM,
					PreviewStartD:   bidResp.AuctionData.PreviewStartD,
					PreviewStartHis: bidResp.AuctionData.PreviewStartHis,
					PreviewEndY:     bidResp.AuctionData.PreviewEndY,
					PreviewEndM:     bidResp.AuctionData.PreviewEndM,
					PreviewEndD:     bidResp.AuctionData.PreviewEndD,
					PreviewEndHis:   bidResp.AuctionData.PreviewEndHis,
					PreviewTime:     bidResp.AuctionData.PreviewTime,
				},
				AuctionUuid: req.AuctionArtworkUuid,
				AuctionY:    bidResp.AuctionY,
				AuctionM:    bidResp.AuctionM,
				AuctionD:    bidResp.AuctionD,
				BuyName:     req.UserInfo.UserName,
				SignDate:    time.Now().Format("2006-01-02"),
			}, []string{
				modelContract.ContractName1,
				modelContract.ContractName2,
				modelContract.ContractName3,
				modelContract.ContractName4,
				modelContract.ContractName5,
				modelContract.ContractName6,
			})
			if err != nil {
				service.Error(ctx, e.Error, err)
				return
			}
			if strings.Contains(url, "http") {
				service.Success(ctx, response.SignContractResp{
					SignType:     "fdd",
					Action:       "signContract",
					FddVerifyUrl: url,
				})
				return
			} else {
				service.Error(ctx, e.Error, errors.New(url))
				return
			}
			return
		} else {
			resp, err = service.CustomContractProvider.ProtocolSignOffline(ctx, req)
			if err != nil {
				service.Error(ctx, e.ERROR, err)
				return
			}
		}
	} else {
		err = errors.New("签名次序错误")
		return
	}
	fmt.Println(fddCustomerId)
	// 86是法大大
	service.Success(ctx, resp)
}

func GetFddFromConfig() *contract.Fdd {
	return &contract.Fdd{
		AppId:       config.AppConfig.Fdd.AppId,
		AppSecret:   config.AppConfig.Fdd.AppSecret,
		Url:         config.AppConfig.Fdd.URL,
		ContentType: config.AppConfig.Fdd.ContentType,
		//FileContentType: config.AppConfig.Fdd.FileContentType,
	}
}

func getBidData(phone, auctionArtworkUuid string) (bidData []modelContract.BidInfo, artworkName, soldPrice string, respBid *custom_contract.ArtworkBidDataResp, err error) {
	respBid = &custom_contract.ArtworkBidDataResp{}
	respBid, err = service.CustomContractProvider.ArtworkBidData(context.Background(), &custom_contract.ArtworkBidDataReq{
		AuctionArtworkUuid: auctionArtworkUuid, Phone: phone,
	})
	if err != nil {
		return
	}
	if respBid == nil || len(respBid.BigData) == 0 {
		err = errors.New("没有获取竞拍记录数据")
		return
	}
	for _, v := range respBid.BigData {
		bidData = append(bidData, modelContract.BidInfo{
			BidNo:    v.BidNo,
			BidPrice: v.BidPrice,
		})
	}
	artworkName = respBid.ArtworkName
	soldPrice = respBid.SoldPrice
	return
}

func SignOnline(ctx *gin.Context) {
	var (
		req         *custom_contract.ProtocolSignOnlineReq
		bidData     []modelContract.BidInfo
		err         error
		url         string
		artworkName string
		soldPrice   string
	)
	if err = ctx.ShouldBindBodyWith(&req, binding.JSON); err != nil {
		service.Error(ctx, e.InvalidParams, err)
		return
	}
	fmt.Println("SignOnline")
	fmt.Printf("%+v", req)
	fmt.Println("SignOnline")
	tempData := req.SignImgFileData
	req.SignImgFileData = ""
	logger.Errorf("ProtocolSignOnline req %+v", req)
	req.SignImgFileData = tempData
	userInfoAny, ok := ctx.Get("jwtInfo")
	if config.AppConfig.Service.AppMode == "local" {
		ok = true
		userInfoAny = middleware.LoginInfo{
			ID:       520,
			TelNum:   "18260168418",
			RealName: "张三",
			IdNum:    "6219881987198719",
			Sex:      1,
			UserExtend: &api.UserExtend{
				IsMainland: 1,
				IdType:     "护照",
				IdNo:       "6219881987198719",
				Address:    "浙江省杭州市西湖区",
				RealName:   "张三",
			},
			FddInfo: &api.FddInfo{
				CustomerId: "6C8265142786AAFB3EA29CC65961A655",
			},
		}
	}
	if !ok {
		service.Error(ctx, e.Error, errors.New("请先登录"))
		return
	}
	// XXX  b51ba082-d993-46ab-807f-bd6b68b3e909  写死杭州的拍卖场次UUID 没有为什么 就这样吧
	//auctionUuid := "b51ba082-d993-46ab-807f-bd6b68b3e909"
	//if req.AuctionArtworkUuids, err = GetAuctionArtworkUuidsByPhone(req.UserInfo.Phone, auctionUuid); err != nil {
	//	return
	//}
	if req.AuctionArtworkUuid == "" && len(req.AuctionArtworkUuids) == 0 {
		service.Error(ctx, e.Error, errors.New("画作信息为空"))
		return
	}
	var isMainland bool = false
	userInfo := userInfoAny.(middleware.LoginInfo)
	req.UserID = fmt.Sprint(userInfo.ID)
	req.Phone = userInfo.TelNum
	req.IsMainLand = userInfo.UserExtend.IsMainland
	//req.FddCustomerId = userInfo.FddInfo.CustomerId
	if config.AppConfig.Service.AppMode == "local" {
		req.FddCustomerId = "6C8265142786AAFB3EA29CC65961A655"
	} else {
		req.FddCustomerId = account.CheckFddCustomerId(ctx)
	}
	logger.Errorf("SignOnline 1 %+v ", req)
	logger.Errorf("SignOnline 2 %s ", req.FddCustomerId)
	if req.IsMainLand == 1 {
		isMainland = true
	}
	reply := cache.RedisClient.SetNX(fmt.Sprintf("SignOnline_%s", req.Phone), time.Now().Unix(), time.Second*1)
	if !reply.Val() {
		service.Error(ctx, e.Error, errors.New("请求太频繁，稍后重试"))
		return
	}
	//查询用户信息
	userInfoPhoneResp, err := service.CustomContractProvider.UserInfoByPhone(ctx, &custom_contract.UserInfoByPhoneReq{
		Phone: req.Phone,
	})
	if err != nil {
		service.Error(ctx, e.ERROR, err)
		return
	}
	if userInfoPhoneResp.SpecialType == 1 {
		isMainland = false
	}
	fmt.Printf("ProtocolSignOnline isMainLand %+v", isMainland)
	fmt.Printf("ProtocolSignOnline userInfo %+v", userInfo)
	logger.Errorf("ProtocolSignOnline userInfo %+v", userInfo)
	//respBid, _err := service.CustomContractProvider.ArtworkBidData(ctx, &custom_contract.ArtworkBidDataReq{
	//	AuctionArtworkUuid: req.AuctionArtworkUuid, Phone: userInfo.TelNum,
	//})
	//if _err != nil {
	//	service.Error(ctx, e.ERROR, _err)
	//	return
	//}
	//if respBid == nil || len(respBid.BigData) == 0 {
	//	err = errors.New("无法获取竞拍记录数据")
	//	service.Error(ctx, e.ERROR, err)
	//	return
	//}
	if isMainland { // 内地用户
		if req.FddCustomerId == "" {
			err = errors.New("法大大实名认证失败")
			service.Error(ctx, e.Error, err)
			return
		}
		var bidResp *custom_contract.ArtworkBidDataResp
		var auctionArtworkUuids []string
		auctionArtworkUuids = req.AuctionArtworkUuids
		var auctionArtworkData []modelContract.AuctionArtwork
		if req.AuctionArtworkUuid != "" {
			auctionArtworkUuids = append(auctionArtworkUuids, req.AuctionArtworkUuid)
		}
		auctionArtworkUuids = stringutils.RemoveDuplicates(auctionArtworkUuids)
		for _, auctionArtworkUuid := range auctionArtworkUuids {
			bidData, artworkName, soldPrice, bidResp, err = getBidData(userInfo.TelNum, auctionArtworkUuid)
			if err != nil {
				service.Error(ctx, e.Error, err)
				return
			}
			var imgData2 []byte
			hdPicUrl := fmt.Sprintf("%s?x-oss-process=image/resize,m_fixed,h_80,w_120", bidResp.HdPic)
			hdPicPath, _ := fileutils.SaveUrlFileDisk(hdPicUrl, "./runtime", bidResp.Tfnum+filepath.Ext(bidResp.HdPic))
			imgData2, err = os.ReadFile(hdPicPath)
			hdPicBase64 := base64.StdEncoding.EncodeToString(imgData2)
			auctionArtworkData = append(auctionArtworkData, modelContract.AuctionArtwork{
				BidData:          bidData,
				ArtworkSize:      bidResp.ArtworkSize,
				HdPic:            hdPicBase64,
				Tfnum:            bidResp.Tfnum,
				AuctionBasePrice: bidResp.AuctionBasePrice,
				Commission:       bidResp.Commission,
				SoldPriceChinese: bidResp.SoldPriceChinese,
				ArtworkName:      artworkName,
				SoldPrice:        soldPrice,
				AuctionInfo: modelContract.AuctionInfo{
					AuctionLOT: bidResp.AuctionData.AuctionLOT,
				},
				AuctionArtworkUuid: auctionArtworkUuid,
			})
		}
		// 1中国身份证 2护照 3其他
		var idType int32
		switch userInfo.UserExtend.IdType {
		case "身份证":
			idType = 1
		case "护照":
			idType = 2
		case "其他":
			idType = 3
		}
		_, _ = service.CustomContractProvider.UpdateUserInfo(context.Background(), &custom_contract.UpdateUserInfoReq{
			UserId:        req.UserID,
			FddCustomerId: req.FddCustomerId,
			IsMainland:    1,
			FddStatus:     2,
			Status:        2,
			UserInfo: &custom_contract.UserInfo{
				UserName:      userInfo.RealName,
				Gender:        int32(userInfo.Sex),
				Birthday:      "",
				Address:       userInfo.UserExtend.Address,
				BankName:      "",
				BankNo:        "",
				FddCustomerId: "",
				CardId:        userInfo.UserExtend.IdNo,
				CardType:      idType,
				Sex:           "",
				CardTypeName:  "",
				Phone:         req.Phone,
				CountryCode:   "86",
			},
			RegisterType: modelContract.RegisterTypeOnline,
		})

		signReq := modelContract.FddContractSignReq{
			UserID:             req.UserID,
			FddCustomerId:      req.FddCustomerId,
			SignOrder:          1,
			Phone:              userInfo.TelNum,
			LineType:           1,
			TestReturnHost:     req.TestReturnHost,
			TestReturnEndPoint: req.TestReturnEndPoint,
			FddUserInfo: modelContract.FddUserInfo{
				UserName: userInfo.UserExtend.RealName,
				Address:  userInfo.UserExtend.Address,
				Gender:   modelContract.GenderMm[int32(userInfo.Sex)],
				CardType: "身份证",
				CardNo:   userInfo.IdNum,
				Phone:    userInfo.TelNum,
			},
			Online:             true,
			AuctionUuid:        req.AuctionArtworkUuid,
			AuctionY:           bidResp.AuctionY,
			AuctionM:           bidResp.AuctionM,
			AuctionD:           bidResp.AuctionD,
			AuctionArtworkData: auctionArtworkData,
			AuctionInfo: modelContract.AuctionInfo{
				AuctionTime:    bidResp.AuctionData.AuctionTime,
				AuctionAddress: bidResp.AuctionData.AuctionAddress,
				AuctionName:    bidResp.AuctionData.AuctionName,
				//AuctionLOT:      bidResp.AuctionData.AuctionLOT,
				PreviewStartY:   bidResp.AuctionData.PreviewStartY,
				PreviewStartM:   bidResp.AuctionData.PreviewStartM,
				PreviewStartD:   bidResp.AuctionData.PreviewStartD,
				PreviewStartHis: bidResp.AuctionData.PreviewStartHis,
				PreviewEndY:     bidResp.AuctionData.PreviewEndY,
				PreviewEndM:     bidResp.AuctionData.PreviewEndM,
				PreviewEndD:     bidResp.AuctionData.PreviewEndD,
				PreviewEndHis:   bidResp.AuctionData.PreviewEndHis,
				PreviewTime:     bidResp.AuctionData.PreviewTime,
			},
			BuyName:  userInfo.RealName,
			SignDate: time.Now().Format("2006-01-02"),
		}
		contractData := []string{modelContract.ContractName1, modelContract.ContractName2,
			modelContract.ContractName3, modelContract.ContractName4, modelContract.ContractName5, modelContract.ContractName6}
		//for _, _ = range auctionArtworkUuids {
		//	contractData = append(contractData, []string{modelContract.ContractName5, modelContract.ContractName6}...)
		//}
		url, err = FddContractSign(signReq, contractData)
		if err != nil {
			service.Error(ctx, e.Error, err)
			return
		}
		service.Success(ctx, response.SignContractResp{
			SignType:     "fdd",
			Action:       "signContract",
			FddVerifyUrl: url,
		})
		return
	} else {
		req.UserInfo = &custom_contract.UserInfo{
			Phone:        userInfo.TelNum,
			UserName:     userInfo.UserExtend.RealName,
			Sex:          modelContract.GenderMm[int32(userInfo.Sex)],
			Address:      userInfo.UserExtend.Address,
			CardTypeName: userInfo.UserExtend.IdType,
			CardId:       userInfo.UserExtend.IdNo,
		}
		_, err = service.CustomContractProvider.ProtocolSignOnline(ctx, req)
		if err != nil {
			service.Error(ctx, e.ERROR, err)
			return
		}
		service.Success(ctx, response.SignContractResp{
			SignType:     "custom",
			Action:       "",
			FddVerifyUrl: "",
		})
	}
}

func ContractView(ctx *gin.Context) {
	var (
		req      *custom_contract.ContractViewReq
		viewResp *custom_contract.ContractViewResp
		err      error
	)
	if err = ctx.ShouldBindBodyWith(&req, binding.JSON); err != nil {
		service.Error(ctx, e.InvalidParams, err)
		return
	}
	if req.AuctionArtworkUuid == "" {
		service.Error(ctx, e.Error, errors.New("画作信息为空"))
		return
	}
	userInfoAny, ok := ctx.Get("jwtInfo")
	if config.AppConfig.Service.AppMode == "local" {
		ok = true
		userInfoAny = middleware.LoginInfo{
			ID:       520,
			TelNum:   "18260168418",
			RealName: "张三",
			IdNum:    "6219881987198719",
			Sex:      1,
			UserExtend: &api.UserExtend{
				IsMainland: 1,
				IdType:     "护照",
				IdNo:       "6219881987198719",
				Address:    "浙江省杭州市西湖区",
				RealName:   "张三",
			},
			FddInfo: &api.FddInfo{
				CustomerId: "6C8265142786AAFB3EA29CC65961A655",
			},
		}
	}
	if !ok {
		service.Error(ctx, e.Error, errors.New("请先登录"))
		return
	}
	if req.AuctionArtworkUuid == "" {
		service.Error(ctx, e.Error, errors.New("画作信息为空"))
		return
	}
	userInfo := userInfoAny.(middleware.LoginInfo)
	fmt.Printf("ContractView_userInfo %+v", userInfo)
	logger.Errorf("ContractView_userInfo%+v", userInfo)
	var idType int32
	switch userInfo.UserExtend.IdType {
	case "身份证":
		idType = 1
	case "护照":
		idType = 2
	case "其他":
		idType = 3
	}
	_, _ = service.CustomContractProvider.UpdateUserInfo(context.Background(), &custom_contract.UpdateUserInfoReq{
		UserId:        fmt.Sprint(userInfo.ID),
		FddCustomerId: userInfo.FddInfo.CustomerId,
		IsMainland:    userInfo.UserExtend.IsMainland,
		UserInfo: &custom_contract.UserInfo{
			UserName:      userInfo.RealName,
			Gender:        int32(userInfo.Sex),
			Birthday:      "",
			Address:       userInfo.UserExtend.Address,
			BankName:      "",
			BankNo:        "",
			FddCustomerId: "",
			CardId:        userInfo.UserExtend.IdNo,
			CardType:      idType,
			Sex:           "",
			CardTypeName:  "",
			Phone:         req.Phone,
			CountryCode:   "86",
		},
		RegisterType: modelContract.RegisterTypeOnline,
	})
	req.CountryCode = ""
	req.Phone = userInfo.TelNum
	req.SignOrder = 1
	req.RegisterType = 1
	var auctionUuid string
	// XXX  b51ba082-d993-46ab-807f-bd6b68b3e909  写死杭州的拍卖场次UUID 没有为什么 就这样吧
	if config.AppConfig.Service.AppMode != "prod" {
		auctionUuid = "a95910fc-5f9a-4b8a-b31e-536b49907a32"
	} else {
		auctionUuid = "b51ba082-d993-46ab-807f-bd6b68b3e909"
	}
	if req.AuctionArtworkUuids, err = GetAuctionArtworkUuidsByPhone(userInfo.TelNum, auctionUuid); err != nil {
		service.Error(ctx, e.ERROR, err)
		return
	}
	var auctionArtworkUuids []string
	auctionArtworkUuids = req.AuctionArtworkUuids
	if req.AuctionArtworkUuid != "" {
		auctionArtworkUuids = append(auctionArtworkUuids, req.AuctionArtworkUuid)
	}
	req.AuctionArtworkUuids = stringutils.RemoveDuplicates(auctionArtworkUuids)
	viewResp, err = service.CustomContractProvider.ContractView(context.Background(), req)
	if err != nil {
		service.Error(ctx, e.ERROR, err)
		return
	}
	service.Success(ctx, viewResp)
}

func UserInfo(ctx *gin.Context) {
	var (
		req          *custom_contract.UserInfoReq
		userInfoResp *custom_contract.UserInfoByPhoneResp
		err          error
	)
	if err = ctx.ShouldBindBodyWith(&req, binding.JSON); err != nil {
		service.Error(ctx, e.InvalidParams, err)
		return
	}
	userInfoResp, err = service.CustomContractProvider.UserInfoByPhone(ctx, &custom_contract.UserInfoByPhoneReq{
		Phone: req.Phone,
	})
	if err != nil {
		service.Error(ctx, e.ERROR, err)
		return
	}
	service.Success(ctx, userInfoResp)
}

func ContractViewOffline(ctx *gin.Context) {
	var (
		req      *custom_contract.ContractViewReq
		viewResp *custom_contract.ContractViewResp
		err      error
	)
	if err = ctx.ShouldBindBodyWith(&req, binding.JSON); err != nil {
		service.Error(ctx, e.InvalidParams, err)
		return
	}
	//if req.AuctionArtworkUuid == "" {
	//	service.Error(ctx, e.Error, errors.New("画作信息为空"))
	//	return
	//}
	//req.AuctionUuid = e.DefaultUuid
	// XXX  b51ba082-d993-46ab-807f-bd6b68b3e909  写死杭州的拍卖场次UUID 没有为什么 就这样吧
	var auctionUuid string
	if config.AppConfig.Service.AppMode != "prod" {
		auctionUuid = "a95910fc-5f9a-4b8a-b31e-536b49907a32"
	} else {
		auctionUuid = "b51ba082-d993-46ab-807f-bd6b68b3e909"
	}
	if req.AuctionArtworkUuids, err = GetAuctionArtworkUuidsByPhone(req.Phone, auctionUuid); err != nil {
		service.Error(ctx, e.Error, err)
		return
	}
	if len(req.AuctionArtworkUuids) == 0 {
		service.Error(ctx, e.Error, errors.New("没有拍品信息"))
		return
	}
	if req.AuctionUuid == "" && len(req.AuctionArtworkUuids) == 0 {
		service.Error(ctx, e.Error, errors.New("拍卖场次为空"))
		return
	}
	var auctionArtworkUuids []string
	auctionArtworkUuids = req.AuctionArtworkUuids
	req.AuctionArtworkUuid = req.AuctionArtworkUuids[0]
	if req.AuctionArtworkUuid != "" {
		auctionArtworkUuids = append(auctionArtworkUuids, req.AuctionArtworkUuid)
	}
	req.AuctionArtworkUuids = stringutils.RemoveDuplicates(auctionArtworkUuids)
	viewResp, err = service.CustomContractProvider.ContractView(context.Background(), req)
	if err != nil {
		service.Error(ctx, e.ERROR, err)
		return
	}
	service.Success(ctx, viewResp)
}

func SignView(ctx *gin.Context) {
	var (
		req      *custom_contract.SignViewReq
		viewResp *custom_contract.SignViewResp
		err      error
	)
	if err = ctx.ShouldBindBodyWith(&req, binding.JSON); err != nil {
		service.Error(ctx, e.InvalidParams, err)
		return
	}
	viewResp, err = service.CustomContractProvider.SignView(context.Background(), req)
	if err != nil {
		service.Error(ctx, e.ERROR, err)
		return
	}
	service.Success(ctx, viewResp)
}

func Test(ctx *gin.Context) {
	var (
		req *contract.ContractRequest
		err error
	)
	if err = ctx.ShouldBindBodyWith(&req, binding.JSON); err != nil {
		service.Error(ctx, e.InvalidParams, err)
		return
	}
	ossUrl, err := FddDownload(req.ContractId)
	if err != nil {
		service.Error(ctx, e.ERROR, err)
		return
	}
	service.Success(ctx, ossUrl)
}

func RepairFddContract(ctx *gin.Context) {
	var (
		req *custom_contract.RepairFddContractUrlReq
		err error
	)
	if err = ctx.ShouldBindBodyWith(&req, binding.JSON); err != nil {
		service.Error(ctx, e.InvalidParams, err)
		return
	}
	repairResp, err := service.CustomContractProvider.RepairFddContractUrl(context.Background(), req)
	if err != nil {
		service.Error(ctx, e.ERROR, err)
		return
	}
	for _, v := range repairResp.Data {
		var ossUrl string
		if v.SignType == 1 {
			ossUrl = v.ViewUrl
		} else if v.SignType == 2 {
			ossUrl, err = FddDownload(v.ContractID)
			if err != nil {
				service.Error(ctx, e.ERROR, err)
				return
			}
		}
		if ossUrl != "" {
			_, _ = service.CustomContractProvider.UpdateContract(ctx, &custom_contract.UpdateContractReq{
				ContractId:  v.ContractID,
				DownloadUrl: ossUrl,
			})
		}
	}
	service.Success(ctx, repairResp)
}

func FddDownload(contractId string) (ossUrl string, err error) {
	var (
		resp     *contract.JumpCommonResponse
		fullPath string
	)
	resp, err = service.ContractProvider.DownLoadContractPdf(context.Background(), &contract.ContractRequest{
		ContractId: contractId,
	})
	if err != nil {
		return
	}
	fullPath, err = common.SaveUrlFileDisk(resp.JumpUrl, "./runtime/contract", contractId+".pdf")
	if err != nil {
		return
	}
	// 下载pdf，转存oss
	ossUrl, err = oss.OssUploadLocal(strings.Replace(fullPath, "./runtime", "auction", 1), fullPath)
	return
}

func UserContractCount(ctx *gin.Context) {
	var (
		req  *custom_contract.UserAuctionContractCountReq
		resp *custom_contract.UserAuctionContractCountResp
		err  error
	)
	//if err = ctx.ShouldBindBodyWith(&req, binding.JSON); err != nil {
	//	service.Error(ctx, e.InvalidParams, err)
	//	return
	//}
	req = &custom_contract.UserAuctionContractCountReq{}
	userInfoAny, ok := ctx.Get("jwtInfo")
	if config.AppConfig.Service.AppMode == "local" {
		ok = true
		userInfoAny = middleware.LoginInfo{
			ID:       520,
			TelNum:   "182601684181",
			RealName: "张三",
			IdNum:    "6219881987198719",
			Sex:      1,
			UserExtend: &api.UserExtend{
				IsMainland: 1,
				IdType:     "护照",
				IdNo:       "6219881987198719",
				Address:    "浙江省杭州市西湖区",
				RealName:   "张三",
			},
			FddInfo: &api.FddInfo{
				CustomerId: "6C8265142786AAFB3EA29CC65961A655",
			},
		}
	}
	if !ok {
		service.Error(ctx, e.Error, errors.New("请先登录"))
		return
	}
	var auctionUuid string
	if config.AppConfig.Service.AppMode != "prod" {
		auctionUuid = "a95910fc-5f9a-4b8a-b31e-536b49907a32" //a95910fc-5f9a-4b8a-b31e-536b49907a32
		//auctionUuid = "86180cae-1e07-4b8d-b45e-50d8ce800110" //a95910fc-5f9a-4b8a-b31e-536b49907a32
	} else {
		auctionUuid = "b51ba082-d993-46ab-807f-bd6b68b3e909"
	}
	req.AuctionUuid = auctionUuid
	req.Phone = userInfoAny.(middleware.LoginInfo).TelNum
	resp, err = service.CustomContractProvider.UserAuctionContractCount(context.Background(), req)
	if err != nil {
		service.Error(ctx, e.ERROR, err)
		return
	}
	service.Success(ctx, resp)
	return
}
