package v1

import (
	"context"
	"encoding/hex"
	"errors"
	"fmt"
	"github.com/fonchain_enterprise/client-auction/api/account"
	"github.com/fonchain_enterprise/client-auction/api/contract"
	"github.com/fonchain_enterprise/client-auction/api/custom_contract"
	"github.com/fonchain_enterprise/client-auction/cmd/config"
	"github.com/fonchain_enterprise/client-auction/pkg/e"
	"github.com/fonchain_enterprise/client-auction/pkg/middleware"
	"github.com/fonchain_enterprise/client-auction/pkg/service"
	"github.com/gin-gonic/gin"
	"github.com/gin-gonic/gin/binding"
	"github.com/google/uuid"
	"time"
)

func FddUserRegister(phone, name, cardNo, userId, testReturnHost, testReturnEndPoint, auctionUuid string) (info struct {
	CustomerId    string
	transactionNo string
	url           string
}, err error) {
	openId := hex.EncodeToString([]byte(uuid.NewString()))[:64]
	personRegisterResult, err := service.ContractProvider.RegisterPerson(context.Background(),
		&contract.RegisterPersonRequest{OpenId: openId})
	if err != nil {
		return
	}
	var returnUrl = fmt.Sprintf("%s/api/v1/fenghe/contract/fdd-register-callback?key=%s&time=%d", config.AppConfig.Service.Host, userId, time.Now().Unix())
	//if config.AppConfig.Service.AppMode != "prod" {
	returnUrl = fmt.Sprintf("%s&auctionUuid=%s", returnUrl, auctionUuid)
	//}
	fddReq := contract.PersonVerifyRequest{
		CustomerId:      personRegisterResult.CustomerId,
		VerifiedWay:     "1",
		CustomerName:    name,
		CustomerIdentNo: cardNo,
		Mobile:          phone,
		ReturnUrl:       returnUrl,
		CertType:        "0",
		ResultType:      0,
	}
	verifyResult, err := service.ContractProvider.PersonVerify(context.Background(), &fddReq)
	if err != nil {
		return
	}
	fmt.Println(verifyResult)
	info.CustomerId = personRegisterResult.CustomerId
	info.transactionNo = verifyResult.TransactionNo
	info.url = verifyResult.Url
	return
}

func FddInfo(ctx *gin.Context) {
	req := custom_contract.FddInfoReq{}
	var err error
	if err = ctx.ShouldBindBodyWith(&req, binding.JSON); err != nil {
		service.Error(ctx, e.Failed, errors.New(e.GetMsg(e.InvalidParams)))
		return
	}
	resp, err := service.CustomContractProvider.FddInfo(ctx, &req)
	if err != nil {
		service.Error(ctx, e.Failed, err)
		return
	}
	resp.IsMainland = 1
	service.Success(ctx, resp)
	return
}

// 根据手机号获取用户信息 TODO
func GetAuctionArtworkUuidsByPhone(phone, auctionUuid string) (auctionUuids []string, err error) {
	var userInfo *account.InfoResponse
	fmt.Printf("UserByTelRequest-%+v\n", &account.UserByTelRequest{Tel: phone, Domain: e.MallAuctionDomain})
	userInfo, err = service.AccountProvider.UserByTel(context.Background(), &account.UserByTelRequest{Tel: phone, Domain: e.MallAuctionDomain})
	if err != nil {
		return
	}
	if !userInfo.IsExist {
		err = errors.New("用户不存在")
		return
	}
	if userInfo.Info == nil {
		err = errors.New("用户信息为空")
		return
	}
	userId := fmt.Sprint(userInfo.Info.ID)
	//userId := "123"
	resp, err := service.CustomContractProvider.GetAuctionArtworkUuids(context.Background(),
		&custom_contract.GetAuctionArtworkUuidsReq{Phone: phone, UserID: userId, AuctionUuid: auctionUuid})
	if err != nil {
		return
	}
	auctionUuids = resp.AuctionArtworkUuids
	return
}

func LoginUserInfo(ctx *gin.Context) (userInfo middleware.LoginInfo) {
	userInfoAny, ok := ctx.Get("jwtInfo")
	if config.AppConfig.Service.AppMode == "local" {
		ok = true
		userInfoAny = middleware.LoginInfo{
			ID:       520,
			TelNum:   "************",
			RealName: "张三",
			IdNum:    "****************",
			Sex:      1,
		}
	}
	if ok {
		userInfo = userInfoAny.(middleware.LoginInfo)
	}
	return
}
