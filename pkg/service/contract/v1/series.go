package v1

import (
	"context"
	"errors"
	"fmt"
	"github.com/fonchain_enterprise/client-auction/api/custom_contract"
	"github.com/fonchain_enterprise/client-auction/api/fenghe"
	"github.com/fonchain_enterprise/client-auction/pkg/e"
	"github.com/fonchain_enterprise/client-auction/pkg/service"
	"github.com/gin-gonic/gin"
	"github.com/gin-gonic/gin/binding"
)

func SeriesOrderContractView(ctx *gin.Context) {
	var (
		req      *custom_contract.SeriesOrderContractViewReq
		viewResp *custom_contract.SeriesOrderContractViewResp
		err      error
	)
	if err = ctx.ShouldBindBodyWith(&req, binding.JSON); err != nil {
		service.Error(ctx, e.InvalidParams, err)
		return
	}
	if req.ArtworkUuid == "" {
		service.Error(ctx, e.Error, errors.New("画作信息为空"))
		return
	}
	userInfo := LoginUserInfo(ctx)
	req.Phone = userInfo.TelNum
	req.UserID = fmt.Sprint(userInfo.ID)
	viewResp, err = service.CustomContractProvider.SeriesOrderContractView(context.Background(), req)
	if err != nil {
		service.Error(ctx, e.ERROR, err)
		return
	}
	service.Success(ctx, viewResp)
}

func SeriesOrderContractSign(ctx *gin.Context) {
	var (
		req      *custom_contract.SeriesOrderContractSignReq
		viewResp *custom_contract.SeriesOrderContractSignResp
		err      error
	)
	if err = ctx.ShouldBindBodyWith(&req, binding.JSON); err != nil {
		service.Error(ctx, e.InvalidParams, err)
		return
	}
	if req.ArtworkUuid == "" {
		service.Error(ctx, e.Error, errors.New("画作信息为空"))
		return
	}
	userInfo := LoginUserInfo(ctx)
	req.Phone = userInfo.TelNum
	req.UserID = fmt.Sprint(userInfo.ID)
	viewResp, err = service.CustomContractProvider.SeriesOrderContractSign(context.Background(), req)
	if err != nil {
		service.Error(ctx, e.ERROR, err)
		return
	}
	service.Success(ctx, viewResp)
	//service.FengheProvider
	viewResp.ContractUrls = (viewResp.ContractUrls)
	recordStatus(context.Background(), viewResp, req.ArtworkUuid)

	return
}

// 销售提交支付信息
func recordStatus(c context.Context, resp *custom_contract.SeriesOrderContractSignResp, artworkUid string) {
	realReq := &fenghe.RecordSeriesOrderRequest{
		ArtworkUid:    artworkUid,
		ContractFiles: []string{},
		Files:         []string{},
		ShowStatus:    uint32(e.ShowStatusNeedReap),
	}

	for _, v := range resp.ContractUrls {
		realReq.Files = append(realReq.Files, v)
	}

	service.FengheProvider.RecordSeriesOrder(c, realReq)
	return
}
