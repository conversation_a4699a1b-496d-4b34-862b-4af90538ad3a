package contract

import (
	api "github.com/fonchain_enterprise/client-auction/api/account"
	appconfig "github.com/fonchain_enterprise/client-auction/cmd/config"
	"github.com/fonchain_enterprise/client-auction/pkg/middleware"
	"github.com/gin-gonic/gin"
)

func GetLoginUserInfo(ctx *gin.Context) (userInfo middleware.LoginInfo) {
	if appconfig.AppConfig.Service.AppMode == "local" {
		userInfo = middleware.LoginInfo{
			ID:       520,
			TelNum:   "***********",
			RealName: "张三",
			IdNum:    "****************",
			Sex:      1,
			UserExtend: &api.UserExtend{
				IsMainland: 2,
				IdType:     "护照",
				IdNo:       "****************",
				Address:    "浙江省杭州市西湖区",
				RealName:   "张三",
			},
			FddInfo: &api.FddInfo{
				CustomerId: "6C8265142786AAFB3EA29CC65961A655",
			},
		}
	} else {
		userInfoAny, ok := ctx.Get("jwtInfo")
		if ok {
			userInfo = userInfoAny.(middleware.LoginInfo)
		}
	}
	return
}
