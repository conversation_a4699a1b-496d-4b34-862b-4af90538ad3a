package mobile

import (
	"github.com/fonchain_enterprise/client-auction/api/fenghe"
	"github.com/fonchain_enterprise/client-auction/pkg/e"
	"github.com/fonchain_enterprise/client-auction/pkg/logic"
	"github.com/fonchain_enterprise/client-auction/pkg/model/response"
	"github.com/fonchain_enterprise/client-auction/pkg/service"
	"github.com/gin-gonic/gin"
	"github.com/gin-gonic/gin/binding"
	"github.com/jinzhu/copier"
	"strings"
)

// ArtworkList 绑定的拍品列表
func ArtworkList(c *gin.Context) {

	var req fenghe.AuctionArtworkList

	if err := c.ShouldBindBodyWith(&req, binding.JSON); err != nil {
		service.Error(c, e.InvalidParams, err)
		return
	}

	req.Lang = logic.GetLanguage(c)

	if req.AuctionUuid != "" {
		auctionDetail, err1 := service.FengheProvider.DetailAuction(c.Request.Context(), &fenghe.AuctionDetail{Uuid: req.AuctionUuid})
		if err1 != nil {
			service.Error(c, e.Error, err1)
			return
		}
		req.EndNum = auctionDetail.EndNum
	}

	res, err := service.FengheProvider.ListAuctionArtwork(c.Request.Context(), &req)
	if err != nil {
		service.Error(c, e.Error, err)
		return
	}

	//进行汇率换算
	currency := logic.GetLanguageCurrency(c)

	rate := logic.GetCurrencyExchangeRate(currency, false)
	for k, v := range res.Data { //起拍价换算
		res.Data[k].StartPriceCurrency = currency
		res.Data[k].SoldPriceCurrency = currency
		if currency != "RMB" {
			res.Data[k].StartPrice = logic.GetCurrencyMoneyFromRate(v.StartPrice, rate)
			res.Data[k].SoldPrice = logic.GetCurrencyMoneyFromRate(v.SoldPrice, rate)
		}
	}

	service.Success(c, res)
	return
}

// AdminArtworkDetail 绑定的拍品列表
func AdminArtworkDetail(c *gin.Context) {

	var req fenghe.AuctionArtworkDetail

	if err := c.ShouldBindBodyWith(&req, binding.JSON); err != nil {
		service.Error(c, e.InvalidParams, err)
		return
	}

	res, err := service.FengheProvider.DetailAuctionArtwork(c.Request.Context(), &req)
	if err != nil {
		service.Error(c, e.Error, err)
		return
	}

	service.Success(c, res)
	return
}

// AdminArtworkList 绑定的拍品列表
func AdminArtworkList(c *gin.Context) {

	var req fenghe.AuctionArtworkList

	if err := c.ShouldBindBodyWith(&req, binding.JSON); err != nil {
		service.Error(c, e.InvalidParams, err)
		return
	}

	res, err := service.FengheProvider.ListAuctionArtwork(c, &req)
	if err != nil {
		service.Error(c, e.Error, err)
		return
	}

	for i, _ := range res.Data {

		if res.Data[i].Artwork == nil ||
			res.Data[i].Artwork.HdPic == "" ||
			strings.Contains(res.Data[i].Artwork.HdPic, "?") == true ||
			strings.Contains(res.Data[i].Artwork.HdPic, "https") == false {
			continue
		}

		res.Data[i].Artwork.HdPic = res.Data[i].Artwork.HdPic + "?x-oss-process=image/resize,m_fill,w_100,h_100"
	}

	firstNewRes := response.AuctionArtworkListResponse{}
	copier.Copy(&firstNewRes, res)

	service.Success(c, firstNewRes)
	return
}

// ArtworkLangUpdate 绑定的拍品列表
func ArtworkLangUpdate(c *gin.Context) {

	var req fenghe.ArtworkLangRequest

	if err := c.ShouldBindBodyWith(&req, binding.JSON); err != nil {
		service.Error(c, e.InvalidParams, err)
		return
	}

	infoRes, err := service.FengheProvider.UpdateAuctionArtworkLang(c, &req)

	if err != nil {
		service.Error(c, e.Error, err)
		return
	}

	service.Success(c, infoRes)
	return
}

// ArtworkUpdate 绑定的拍品列表
func ArtworkUpdate(c *gin.Context) {

	var req fenghe.AuctionArtworkRequest

	if err := c.ShouldBindBodyWith(&req, binding.JSON); err != nil {
		service.Error(c, e.InvalidParams, err)
		return
	}

	infoRes, err := service.FengheProvider.DetailAuctionArtwork(c, &fenghe.AuctionArtworkDetail{Uuid: req.Uuid})

	if err != nil {
		service.Error(c, e.Error, err)
		return
	}

	res, err := service.FengheProvider.UpdateAuctionArtwork(c, &req)
	if err != nil {
		service.Error(c, e.Error, err)
		return
	}

	//如果在拍卖中，更新了叫价表 通知粉丝
	if (infoRes.PriceRuleType != req.PriceRuleType || infoRes.PriceRuleType == "diy") && infoRes.IsSelling == 1 {
		_ = logic.SendFansInfo(infoRes.AuctionUuid, e.FanAuctionChannel_Change_Price_Info)
		_ = logic.SendAdminLiveBuys(infoRes.AuctionUuid, req.Uuid)
	}

	logic.SendAdminLiveBuys(infoRes.AuctionUuid, e.SynOpen) //管理系统确认有效

	service.Success(c, res)
	return
}

// ArtworkBaseList 绑定的拍品列表
func ArtworkBaseList(c *gin.Context) {

	var req fenghe.AuctionArtworkList

	if err := c.ShouldBindBodyWith(&req, binding.JSON); err != nil {
		service.Error(c, e.InvalidParams, err)
		return
	}

	res, err := service.FengheProvider.ListAuctionArtwork(c, &req)
	if err != nil {
		service.Error(c, e.Error, err)
		return
	}

	service.Success(c, res)
	return
}
