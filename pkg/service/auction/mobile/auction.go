package mobile

import (
	"context"
	"dubbo.apache.org/dubbo-go/v3/common/logger"
	"encoding/json"
	"errors"
	"fmt"
	"github.com/fonchain_enterprise/client-auction/api/account"
	"github.com/fonchain_enterprise/client-auction/api/chain"
	"github.com/fonchain_enterprise/client-auction/api/fenghe"
	"github.com/fonchain_enterprise/client-auction/api/payment"
	backendSeries "github.com/fonchain_enterprise/client-auction/api/series"
	"github.com/fonchain_enterprise/client-auction/pkg/common/utils"
	"github.com/fonchain_enterprise/client-auction/pkg/e"
	"github.com/fonchain_enterprise/client-auction/pkg/logic"
	"github.com/fonchain_enterprise/client-auction/pkg/logic/live"
	"github.com/fonchain_enterprise/client-auction/pkg/model/query"
	"github.com/fonchain_enterprise/client-auction/pkg/model/vo"
	"github.com/fonchain_enterprise/client-auction/pkg/service"
	"github.com/fonchain_enterprise/client-auction/pkg/service/order"
	"github.com/fonchain_enterprise/client-auction/pkg/utils/secret"
	"github.com/gin-gonic/gin"
	"github.com/gin-gonic/gin/binding"
	"io"
	"math/rand"
	"net/http"
	"strconv"
	"strings"
	"time"
)

// DefaultDetail 用户登录操作
func DefaultDetail(c *gin.Context) {

	var req fenghe.AuctionDetail

	if err := c.ShouldBindBodyWith(&req, binding.JSON); err != nil {
		service.Error(c, e.InvalidParams, err)
		return
	}

	req.Uuid = e.DefaultUuid //deprecated

	res, err := service.FengheProvider.DetailAuction(c, &req)
	if err != nil {
		service.Error(c, e.Error, err)
		return
	}

	service.Success(c, res)
	return
}

// DefaultUpdate
func DefaultUpdate(c *gin.Context) {

	var req fenghe.AuctionRequest

	if err := c.ShouldBindBodyWith(&req, binding.JSON); err != nil {
		service.Error(c, e.InvalidParams, err)
		return
	}
	req.Uuid = e.DefaultUuid //deprecated

	res, err := service.FengheProvider.UpdateAuction(c, &req)
	if err != nil {
		service.Error(c, e.Error, err)
		return
	}

	service.Success(c, res)
	return
}

// GetLiveUrl 获取直播地址加密后的地址
func GetLiveUrl(c *gin.Context) {

	fmt.Println("1------")
	var req fenghe.CommonReq

	if err := c.ShouldBindBodyWith(&req, binding.JSON); err != nil {
		service.Error(c, e.InvalidParams, err)
		return
	}

	res, err := service.FengheProvider.GetPullLiveUrl(c, &req)
	if err != nil {
		service.Error(c, e.Error, err)
		return
	}

	var tempUrl string
	liveRegion := logic.GetLiveRegion(c)
	if _, isOk := res.PullUrlMap[liveRegion]; isOk {
		tempUrl = res.PullUrlMap[liveRegion]
	} else {
		for _, v := range res.PullUrlMap {
			tempUrl = v
			break // 只需要第一个键值对，所以在此处中断循环
		}
	}

	service.Success(c, map[string]interface{}{"expiredAt": time.Now().Add(4 * time.Hour).Unix(), "code": live.CreateCodeUrl(tempUrl), "id": rand.Intn(1000), "name": "日志"})

	return
}

// 支付方式
var (
	PayTypeNoNeed  = 1
	PayTypeOnline  = 2
	PayTypeOffline = 3
)

var (
	PayStatusCancel = 4
	PayStatusPaid   = 3
	PayStatusNoPaid = 2
	PayStatusNoNeed = 1
)

// ReceiveScanSecretInfo 扫码的加密信息
func ReceiveScanSecretInfo(c *gin.Context) {
	var req query.SecretRequest

	if err := c.ShouldBindBodyWith(&req, binding.JSON); err != nil {
		service.Error(c, e.InvalidParams, err)
		return
	}

	if req.Zone != e.ZoneCn && req.Zone != "" {
		req.ToTel = req.Zone + req.ToTel
	}

	re(c, req.ToTel, req.Zone)

	tokenExample, err := secret.GetJwtFromStr(req.Sign)
	if err != nil {
		service.Error(c, e.InvalidParams, err)
		return
	}

	var artworkObj vo.CultureArtworkReq
	err = json.Unmarshal([]byte(tokenExample), &artworkObj)

	if err != nil {
		service.Error(c, e.InvalidParams, err)
		return
	}

	res, err := service.BackendSeriesProvider.CultureArtworkDetail(c, &backendSeries.CultureArtworkDetailReq{Uuid: artworkObj.Uuid, Num: uint32(artworkObj.Num)})

	if err != nil {
		service.Error(c, e.InvalidParams, err)
		return
	}

	infoRes, err := service.AccountProvider.UserByTel(c, &account.UserByTelRequest{Tel: req.ToTel})

	if err != nil {
		service.Error(c, e.InvalidParams, errors.New("没有查到数据"))
		return
	}

	if infoRes.IsExist == false {
		service.Error(c, e.InvalidParams, errors.New("没有查到数据"))
		return
	}

	if infoRes.Info.UserExtend.IsReal != 1 {
		//service.Error(c, e.InvalidParams, errors.New("没有实名"))
		//return
	}

	// 查询 订单
	cultureArtworkOrderList, err := service.BackendSeriesProvider.CultureArtworkOrderList(c, &backendSeries.CultureArtworkOrderListReq{UserId: uint32(infoRes.Info.ID)})

	if err != nil {
		service.Error(c, e.InvalidParams, err)
		return
	}

	hasOrder := false

	if cultureArtworkOrderList.Count > 0 {

		orderDetail := &backendSeries.CultureArtworkOrder{}
		for _, artworkOrder := range cultureArtworkOrderList.List {
			if artworkOrder.SeriesCulturalArtworkUuid == artworkObj.Uuid && artworkOrder.IndexNum == uint32(artworkObj.Num) {
				orderDetail = artworkOrder
				hasOrder = true
				break
			}
		}
		if hasOrder {
			service.Success(c, e.Error15OrderExist, orderDetail)
			return
		}
	}

	if res.IsReceived == 1 {
		service.Error(c, e.InvalidParams, errors.New("已经被领取过"))
		return
	}

	req1 := &backendSeries.DrawCultureArtworkReq{
		CultureArtworkUuid: artworkObj.Uuid,
		IndexNum:           uint32(artworkObj.Num),
		UserId:             uint32(infoRes.Info.ID),
		UserTelNum:         infoRes.Info.TelNum,
		UserName:           infoRes.Info.NickName,
		PayType:            req.PayType,
	}

	drawCultureArtworkRes, err := service.BackendSeriesProvider.DrawCultureArtwork(c, req1)

	if err != nil {
		service.Error(c, e.InvalidParams, err)
		return
	}

	// 如果 选择的是线上支付的 方式 则 创建支付
	var price float64
	var sessionUrl string
	if res.Price != "" {
		price, _ = strconv.ParseFloat(res.Price, 64)
	}

	if price > 0 && req.PayType == uint32(PayTypeOnline) {

		orderDetail, err := service.BackendSeriesProvider.CultureArtworkOrderDetail(c, &backendSeries.CultureArtworkOrderDetailReq{OrderUuid: drawCultureArtworkRes.Uuid})
		if err != nil {
			service.Error(c, e.InvalidParams, err)
			return
		}

		if orderDetail.Currency != "JPY" {
			price = price * 100
		}

		// 调起支付
		sessionUrl, err = stripeCreate(c, res.Name, &payment.CreatePayRequest{
			OutTradeNo: orderDetail.Uuid,
			Amount:     int64(price),
			Currency:   strings.ToLower(orderDetail.Currency),
			QuitUrl:    req.QuitUrl,
			ReturnUrl:  req.ReturnUrl,
		})

		if err != nil {
			// 调起失败 则 更新 订单为 取消支付状态  remark 填写 为 发起支付失败
			updateReq := &backendSeries.UpdateOrderStatusReq{
				OrderUuid: orderDetail.Uuid,
				PayStatus: int32(PayStatusCancel),
				Remark:    "支付发起失败",
			}

			_, updateErr := service.BackendSeriesProvider.UpdateOrderStatus(c.Request.Context(), updateReq)
			if updateErr != nil {
				fmt.Printf("更新订单状态失败: %v\n", updateErr)
			}

			service.Error(c, e.InvalidParams, err)
			return
		}

		OrderTicker(orderDetail.Uuid)
	} else if req.PayType == uint32(PayTypeOffline) {
		OrderTicker(orderDetail.Uuid)
	}

	service.Success(c, sessionUrl)

	return
}

func stripeCreate(c *gin.Context, artworkName string, params *payment.CreatePayRequest) (string, error) {

	params.ClientIp = c.ClientIP()
	params.PostUrl = func() string {
		if c.Request.URL != nil {
			return c.Request.URL.String()
		}
		return ""
	}()
	//params.OutTradeNo,签约的orderUuid，前端要送，TODO
	params.Payee = "Stripe_Japan"
	params.BusinessType = "useLess"
	params.Platform = "stripe"
	params.ChannelType = "stripe"
	params.BusinessType = "useLess"
	params.Domain = "FENGHE_Japan"

	// 商品相关信息填充
	params.ProductName = artworkName              // 产品名称
	params.ProductDescription = params.OutTradeNo // 产品描述，没有的话你送外部流水号就行
	params.ReturnUrl = ""                         // 成功跳转
	params.QuitUrl = ""                           // 失败跳转
	params.Locale = "auto"                        // 国际化

	fmt.Println("支付请求：", params)
	resp, err := service.PaymentProvider.CreatePay(c, params)
	if err != nil {
		fmt.Println("create err:", err)
		return "", err
	}
	fmt.Println("支付报错：", err)
	fmt.Println("支付响应：", resp)

	if resp != nil && resp.CheckoutSessionId != "" {

		updateReq := new(backendSeries.UpdateOrderStatusReq)
		updateReq.OrderUuid = params.OutTradeNo
		updateReq.SessionId = resp.CheckoutSessionId
		_, err := service.BackendSeriesProvider.UpdateOrderStatus(c.Request.Context(), updateReq)
		if err != nil {
			fmt.Println("==== 更新订单的sessionId失败：", err)
			return "", err
		}
		return resp.Url, nil
	}

	return "", err
}

/*
TODO
*/
func re(c *gin.Context, tel, zone string) {

	isIsExist, err := IsRegister(tel)
	if err != nil {
		service.Error(c, e.InvalidParams, err)
		return
	}

	if isIsExist == false { //存在账号使用这个发送
		fmt.Println("不存在账号")
		_, err = RegisterOnlyTel(c, tel, "", zone)
		if err != nil {
			service.Error(c, e.Error, err)
			return
		}
	}

	return
}

// ScanSecretInfo 扫码的加密信息
func ScanSecretInfo(c *gin.Context) {
	scanInfoRes := vo.ScanInfoRes{}
	var req query.ScanSecretRequest

	if err := c.ShouldBindBodyWith(&req, binding.JSON); err != nil {
		service.Error(c, e.InvalidParams, err)
		return
	}
	tokenExample, err := secret.GetJwtFromStr(req.Sign)
	if err != nil {
		service.Error(c, e.InvalidParams, err)
		return
	}

	var artworkObj vo.CultureArtworkReq
	err = json.Unmarshal([]byte(tokenExample), &artworkObj)

	if err != nil {
		service.Error(c, e.InvalidParams, err)
		return
	}

	res, err := service.BackendSeriesProvider.CultureArtworkDetail(c, &backendSeries.CultureArtworkDetailReq{Uuid: artworkObj.Uuid, Num: uint32(artworkObj.Num)})

	if err != nil {
		service.Error(c, e.InvalidParams, err)
		return
	}

	res.Hash = utils.SHA256V([]byte(fmt.Sprintf("%s_%d", res.Hash, artworkObj.Num)))

	scanInfoRes.Num = artworkObj.Num
	scanInfoRes.CultureArtwork = res
	service.Success(c, scanInfoRes)
	return
}

// CultureInfo 扫码的加密信息
func CultureInfo(c *gin.Context) {
	var req backendSeries.CultureArtworkDetailReq

	if err := c.ShouldBindBodyWith(&req, binding.JSON); err != nil {
		service.Error(c, e.InvalidParams, err)
		return
	}

	res, err := service.BackendSeriesProvider.CultureArtworkDetail(c, &req)

	if err != nil {
		service.Error(c, e.InvalidParams, err)
		return
	}
	service.Success(c, res)
	return
}

// Detail 用户登录操作
func Detail(c *gin.Context) {

	var req fenghe.AuctionDetail

	if err := c.ShouldBindBodyWith(&req, binding.JSON); err != nil {
		service.Error(c, e.InvalidParams, err)
		return
	}
	req.Domain = e.MallAuctionDomain //无
	req.Lang = logic.GetLanguage(c)
	if req.Uuid == "" {
		service.Error(c, e.Error, errors.New("没有直播信息"))
		return
	}

	res, err := service.FengheProvider.DetailAuction(c.Request.Context(), &req)
	if err != nil {
		service.Error(c, e.Error, err)
		return
	}
	res.AuctionArtwork = nil

	service.Success(c, res)
	return
}

// UpdateLang
func UpdateLang(c *gin.Context) {

	var req fenghe.AuctionRequest

	if err := c.ShouldBindBodyWith(&req, binding.JSON); err != nil {
		service.Error(c, e.InvalidParams, err)
		return
	}

	res, err := service.FengheProvider.UpdateAuctionLang(c, &req)
	if err != nil {
		service.Error(c, e.Error, err)
		return
	}

	service.Success(c, res)
	return
}

// Update
func Update(c *gin.Context) {

	var req fenghe.AuctionRequest

	if err := c.ShouldBindBodyWith(&req, binding.JSON); err != nil {
		service.Error(c, e.InvalidParams, err)
		return
	}

	res, err := service.FengheProvider.UpdateAuction(c, &req)
	if err != nil {
		service.Error(c, e.Error, err)
		return
	}

	service.Success(c, res)
	return
}

func List(c *gin.Context) {

	var req fenghe.AuctionList

	if err := c.ShouldBindBodyWith(&req, binding.JSON); err != nil {
		service.Error(c, e.InvalidParams, err)
		return
	}

	res, err := service.FengheProvider.ListAuction(c, &req)
	if err != nil {
		service.Error(c, e.Error, err)
		return
	}

	service.Success(c, res)
	return
}

// Create
func Create(c *gin.Context) {

	var req fenghe.AuctionRequest

	if err := c.ShouldBindBodyWith(&req, binding.JSON); err != nil {
		service.Error(c, e.InvalidParams, err)
		return
	}

	res, err := service.FengheProvider.CreateAuction(c, &req)
	if err != nil {
		service.Error(c, e.Error, err)
		return
	}

	req.Uuid = res.Uuid
	res1, err1 := service.FengheProvider.UpdateAuction(c, &req)
	if err1 != nil {
		service.Error(c, e.Error, err1)
		return
	}

	service.Success(c, res1)
	return
}

// Remove
func Remove(c *gin.Context) {

	var req fenghe.AuctionDetail

	if err := c.ShouldBindBodyWith(&req, binding.JSON); err != nil {
		service.Error(c, e.InvalidParams, err)
		return
	}

	res, err := service.FengheProvider.RemoveAuction(c, &req)
	if err != nil {
		service.Error(c, e.Error, err)
		return
	}

	service.Success(c, res)
	return
}

// CreateLive 生成直播连接
func CreateLive(c *gin.Context) {

	var req fenghe.CommonReq

	if err := c.ShouldBindBodyWith(&req, binding.JSON); err != nil {
		service.Error(c, e.InvalidParams, err)
		return
	}

	res, err := service.FengheProvider.CreateLiveUrl(c, &req)
	if err != nil {
		service.Error(c, e.InvalidParams, err)
		return
	}

	service.Success(c, map[string]string{"url": res.PushUrl})
	return
}

// SetLive 开始直播
func SetLive(c *gin.Context) {

	var req fenghe.AuctionRequest

	if err := c.ShouldBindBodyWith(&req, binding.JSON); err != nil {
		service.Error(c, e.InvalidParams, err)
		return
	}

	res, err := service.FengheProvider.UpdateBaseAuction(c, &req)
	if err != nil {
		service.Error(c, e.Error, err)
		return
	}

	// 结束直播
	if req.IsLiving != 1 {
		err := logic.SendFansInfo(req.Uuid, e.FanAuctionChannel_Over)
		if err != nil {
			service.Error(c, e.Error, err)
			return
		}
	}

	service.Success(c, res)
	return
}

// 创建订单
type CreateBuyOrderReq struct {
	BuyUid             string `json:"buyUid"`
	Price              string `json:"price"`
	Currency           string `json:"currency"`
	TestReturnHost     string `json:"testReturnHost"`
	TestReturnEndPoint string `json:"testReturnEndPoint"`
}

func (c *CreateBuyOrderReq) Validate() error {
	if c.BuyUid == "" {
		return errors.New("请填写商品UID")
	}
	return order.ValidateAmount(c.Price)
}

func IsRegister(TelNum string) (bool, error) {
	userByTelReq := account.UserByTelRequest{
		Tel:    TelNum,
		Domain: e.MallAuctionDomain,
	}

	//查询是否有该账号
	userByTelRes, err := service.AccountProvider.UserByTel(context.Background(), &userByTelReq)
	if err != nil {
		return false, err
	}

	return userByTelRes.IsExist, nil
}

func RegisterOnlyTel(c *gin.Context, telNum, code, zone string) (*account.RequestStatus, error) {
	tempNick := telNum

	if len(telNum) <= 4 {
		return nil, errors.New("手机号码格式不对")
	}

	tempNick = telNum[(len(telNum) - 4):]

	//账号服务
	registerRequest := account.RegistRequest{
		NickName: "访客" + tempNick,
		Avatar:   "https://cdn-test.szjixun.cn/fonchain-main/test/image/2076/avatar/6972820e-bee4-46c8-a80e-55d0f96abb51.png", //默认头像
		TelNum:   telNum,
		Password: "Tyfon8899",
		Code:     code,
		Extend:   &account.Extend{},
		Domain:   e.MallAuctionDomain,
		UserExtend: &account.UserExtend{
			Zone: zone,
		},
	}

	res, err := service.AccountProvider.Register(c, &registerRequest)
	fmt.Println("1---------", res, err)
	if err != nil {
		return nil, err
	}

	//1- 获取助记词
	privacyReq := &account.PrivacyInfoRequest{
		ID: res.ID,
	}

	privacyRes, err := service.AccountProvider.PrivacyInfo(c, privacyReq)

	if err != nil {
		return res, err
	}

	//2- 绑定到区块链交易账号
	initReq := &chain.InitAccountRequest{
		Mnemonic: privacyRes.MnemonicWords,
	}

	_, err = service.ChainProvider.InitAccount(c, initReq)

	if err != nil {
		return res, err
	}

	return res, nil

}

func UpdateCultureOrder(c *gin.Context) {
	var req backendSeries.UpdateOrderStatusReq
	if err := c.ShouldBind(&req); err != nil {
		service.Error(c, e.Error, err)
		return
	}

	res, err := service.BackendSeriesProvider.UpdateOrderStatus(c.Request.Context(), &req)
	if err != nil {
		service.Error(c, e.Error, err)
		return
	}
	service.Success(c, res)
	return
}

func StripeCreate(c *gin.Context) {

	params := &payment.CreatePayRequest{}
	if err := c.ShouldBindJSON(params); err != nil {
		service.Error(c, e.Error, err)
		return
	}

	params.ClientIp = c.ClientIP()
	params.PostUrl = func() string {
		if c.Request.URL != nil {
			return c.Request.URL.String()
		}
		return ""
	}()
	//params.OutTradeNo,签约的orderUuid，前端要送，TODO
	params.Payee = "Stripe_Japan"
	params.BusinessType = "useLess"

	// 查询 订单
	orderDetail, err := service.BackendSeriesProvider.CultureArtworkOrderDetail(c, &backendSeries.CultureArtworkOrderDetailReq{OrderUuid: params.OutTradeNo})

	if err != nil {
		service.Error(c, e.InvalidParams, err)
		return
	}

	sessionUrl, err := stripeCreate(c, orderDetail.Name, params)
	if err != nil {
		service.Error(c, e.InvalidParams, err)
		return
	}

	service.Success(c, sessionUrl)

	return
}

func StripeWebhook(c *gin.Context) {

	var req payment.GetCheckoutWebhookRequest
	var err error
	var payloadBytes []byte

	fmt.Println("==============================================")
	fmt.Println("我进来咯！")
	const MaxBodyBytes = int64(65536)
	fmt.Println("11111")
	c.Request.Body = http.MaxBytesReader(c.Writer, c.Request.Body, MaxBodyBytes)
	payloadBytes, err = io.ReadAll(c.Request.Body)
	if err != nil {
		fmt.Println("io.ReadAll error:", payloadBytes)
		service.Error(c, e.Error, err)
		return
	}
	req.Payload = string(payloadBytes)
	req.Signature = c.GetHeader("Stripe-Signature")

	fmt.Println(req.Payload)

	req.PostUrl = func() string {
		if c.Request.URL != nil {
			return c.Request.URL.String()
		}
		return ""
	}()

	resp, err := service.PaymentProvider.StripeJapanWebhook(c, &req)
	if err != nil {
		service.Error(c, e.Error, err)
		return
	}
	fmt.Println("支付报错：", err)
	fmt.Println("支付响应：", resp)

	if resp.PaymentIntentStatus == "paid" {
		updateReq := new(backendSeries.UpdateOrderStatusReq)
		updateReq.OrderUuid = resp.OutTradeNo
		updateReq.PayTime = time.Now().Format("2006-01-02 15:04:05")
		updateReq.PayStatus = 2
		_, err := service.BackendSeriesProvider.UpdateOrderStatus(c.Request.Context(), updateReq)
		if err != nil {
			fmt.Println("==== 更新订单的sessionId失败：", err)
			service.Error(c, e.Error, err)
			return
		}
	}

	service.Success(c, resp)

}

// OrderTicker 实现一个定时器，15分钟后自动取消订单
func OrderTicker(orderUUID string) {
	// 创建定时器，设置时长为15分钟
	timer := time.NewTimer(15 * time.Minute)

	go func() {
		defer timer.Stop()

		// 等待定时器到期
		<-timer.C

		// 时间到了则调用UpdateOrderStatus接口，传入orderUUID，payStatus为PayStatusCancel
		updateReq := &backendSeries.UpdateOrderStatusReq{
			OrderUuid: orderUUID,
			PayStatus: int32(PayStatusCancel),
			Remark:    "订单超时自动取消",
		}

		// 使用背景上下文调用更新订单状态
		ctx := context.Background()
		_, err := service.BackendSeriesProvider.UpdateOrderStatus(ctx, updateReq)
		if err != nil {
			fmt.Printf("订单定时器取消失败，订单UUID: %s, 错误: %v\n", orderUUID, err)
			logger.Infof("订单定时器取消失败，订单UUID: %s, 错误: %v\n", orderUUID, err)
		} else {
			fmt.Printf("订单定时器成功取消订单，订单UUID: %s\n", orderUUID)
			logger.Infof("订单定时器成功取消订单，订单UUID: %s\n", orderUUID)
		}
	}()
}
