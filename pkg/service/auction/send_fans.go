package auction

import (
	"fmt"
	"github.com/fonchain_enterprise/client-auction/pkg/service"
	"github.com/gin-gonic/gin"
)

// SendFansTest 给粉丝发送数据
func SendFansTest(c *gin.Context) {
	//info := c.PostForm("info")

	//fmt.Println(logic.SendFansInfo(info))
	scheme := "http"
	if c.Request.TLS != nil {
		scheme = "https"
	}

	// 获取主机名（包括端口号，如果有）
	host := c.Request.Host
	fmt.Println(scheme + "://" + host)

	service.Success(c, scheme+"://"+host)
	return
}
