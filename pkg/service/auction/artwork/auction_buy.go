package artwork

import (
	"github.com/fonchain_enterprise/client-auction/api/fenghe"
	"github.com/fonchain_enterprise/client-auction/pkg/common/capexe"
	"github.com/fonchain_enterprise/client-auction/pkg/e"
	"github.com/fonchain_enterprise/client-auction/pkg/logic"
	"github.com/fonchain_enterprise/client-auction/pkg/service"
	"github.com/gin-gonic/gin"
	"github.com/gin-gonic/gin/binding"
)

func OutBuyList(c *gin.Context) {

	NewCap := capexe.NewCapExe()
	var req fenghe.AuctionDetail

	if err := c.ShouldBindBodyWith(&req, binding.JSON); err != nil {
		service.Error(c, e.InvalidParams, err)
		return
	}

	//首次获取列表数据
	res, err := service.FengheProvider.NowBiddingBuys(c, &req)
	NewCap.Log("微服务")
	if err != nil {
		service.Error(c, e.Failed, err)
		return
	}

	currency := logic.GetLanguageCurrency(c)
	NewCap.Log("获取语言")
	setBuyList(res, currency)
	NewCap.Log("设置汇率计算")

	service.Success(c, res)
	return

}
