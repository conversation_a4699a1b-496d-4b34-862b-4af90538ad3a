package artwork

import (
	"github.com/fonchain_enterprise/client-auction/api/fenghe"
	"github.com/fonchain_enterprise/client-auction/pkg/common/cache"
	"github.com/fonchain_enterprise/client-auction/pkg/e"
	"github.com/fonchain_enterprise/client-auction/pkg/logic"
	"github.com/fonchain_enterprise/client-auction/pkg/service"
	"github.com/gin-gonic/gin"
	"github.com/gin-gonic/gin/binding"
)

// Detail 用户登录操作
func AuctionArtworkDetail(c *gin.Context) {

	var req fenghe.AuctionArtworkDetail

	if err := c.ShouldBindBodyWith(&req, binding.JSON); err != nil {
		service.Error(c, e.InvalidParams, err)
		return
	}

	res, err := service.FengheProvider.DetailAuctionArtwork(c.Request.Context(), &req)
	if err != nil {
		service.Error(c, e.<PERSON>rro<PERSON>, err)
		return
	}

	currency := logic.GetLanguageCurrency(c)
	res.SoldPriceCurrency = currency
	res.StartPriceCurrency = currency
	res.StartPrice = logic.GetCurrencyMoney(res.StartPrice, currency) //ok
	res.SoldPrice = logic.GetCurrencyMoney(res.StartPrice, currency)  //ok

	service.Success(c, res)
	return
}

// List 用户登录操作
func List(c *gin.Context) {

	var req fenghe.ArtworkList

	if err := c.ShouldBindBodyWith(&req, binding.JSON); err != nil {
		service.Error(c, e.InvalidParams, err)
		return
	}

	res, err := service.FengheProvider.ListArtwork(c.Request.Context(), &req)
	if err != nil {
		service.Error(c, e.Error, err)
		return
	}

	service.Success(c, res)
	return
}

// SellStatus 开拍停拍
func SellStatus(c *gin.Context) {

	var req fenghe.AuctionArtworkRequest

	if err := c.ShouldBindBodyWith(&req, binding.JSON); err != nil {
		service.Error(c, e.InvalidParams, err)
		return
	}

	realReq := fenghe.AuctionArtworkRequest{
		Uuid:      req.Uuid,
		IsSelling: req.IsSelling,
	}

	res, err := service.FengheProvider.UpdateAuctionArtwork(c.Request.Context(), &realReq)
	if err != nil {
		service.Error(c, e.Error, err)
		return
	}

	res1, err := service.FengheProvider.DetailAuctionArtwork(c.Request.Context(), &fenghe.AuctionArtworkDetail{Uuid: realReq.Uuid})
	if err != nil {
		service.Error(c, e.Error, err)
		return
	}
	auctionUuid := res1.AuctionUuid

	if req.IsSelling == 1 { //开拍
		err = logic.SendFansInfo(auctionUuid, e.FanAuctionChannel_NewArtwork)
		cache.RedisClient.Publish(cache.GetScreenChannel(auctionUuid), "open") //done
	} else if req.IsSelling == 2 { //停拍
		err = logic.SendFansInfo(auctionUuid, e.FanAuctionChannel_StopArtwork)
		cache.RedisClient.Publish(cache.GetScreenChannel(auctionUuid), "1") //done
	}

	err = logic.SendAdminLiveBuys(auctionUuid, e.SynOpen) //done 管理系统确认有效

	if err != nil {
		service.Error(c, e.Error, err)
		return
	}

	service.Success(c, res)
	return
}

// ScreenStatus 投屏
func ScreenStatus(c *gin.Context) {

	var req fenghe.AuctionArtworkRequest

	if err := c.ShouldBindBodyWith(&req, binding.JSON); err != nil {
		service.Error(c, e.InvalidParams, err)
		return
	}

	realReq := fenghe.AuctionArtworkRequest{
		Uuid:         req.Uuid,
		ScreenStatus: req.ScreenStatus,
	}

	res, err := service.FengheProvider.UpdateAuctionArtwork(c.Request.Context(), &realReq)
	if err != nil {
		service.Error(c, e.Error, err)
		return
	}

	res1, err := service.FengheProvider.DetailAuctionArtwork(c.Request.Context(), &fenghe.AuctionArtworkDetail{Uuid: realReq.Uuid})
	if err != nil {
		service.Error(c, e.Error, err)
		return
	}

	if req.ScreenStatus != 1 {
		err = logic.SendFansInfo(res1.AuctionUuid, e.FanAuctionChannel_StopArtwork)
	} else {
		err = logic.SendFansInfo(res1.AuctionUuid, e.FanAuctionChannel_First)
	}

	cache.RedisClient.Publish(cache.GetScreenChannel(res1.AuctionUuid), "1") //done

	if err != nil {
		service.Error(c, e.Error, err)
		return
	}

	service.Success(c, res)
	return
}
