package artwork

import (
	"context"
	"encoding/json"
	"fmt"
	"github.com/fonchain_enterprise/client-auction/api/fenghe"
	"github.com/fonchain_enterprise/client-auction/pkg/common/cache"
	"github.com/fonchain_enterprise/client-auction/pkg/common/capexe"
	"github.com/fonchain_enterprise/client-auction/pkg/e"
	"github.com/fonchain_enterprise/client-auction/pkg/logic"
	"github.com/fonchain_enterprise/client-auction/pkg/middleware"
	"github.com/fonchain_enterprise/client-auction/pkg/serializer"
	"github.com/fonchain_enterprise/client-auction/pkg/service"
	"github.com/gin-gonic/gin"
	"github.com/gin-gonic/gin/binding"
	"github.com/go-redis/redis"
	"github.com/gorilla/websocket"
	"net/http"
	"sync"
	"time"
)

// BuyLive 用户直播实时数据
func BuyLive(c *gin.Context) {

	auctionUuid := c.DefaultQuery("auctionUuid", "")
	userInfoAny, _ := c.Get("jwtInfo")
	userInfo := userInfoAny.(middleware.LoginInfo)
	currency := logic.GetLanguageCurrency(c)
	if userInfo.ID == 0 {
		service.Error(c, e.Error, fmt.Errorf("less param userId"))
		return
	}

	// 获取WebSocket连接
	var cstUpgrader = websocket.Upgrader{
		Subprotocols:      []string{"p0", "p1"},
		ReadBufferSize:    1024,
		WriteBufferSize:   1024,
		EnableCompression: true,
		Error: func(w http.ResponseWriter, r *http.Request, status int, reason error) {
			http.Error(w, reason.Error(), status)
		},
		CheckOrigin: func(r *http.Request) bool {
			return true
		},
	}

	ws, err := cstUpgrader.Upgrade(c.Writer, c.Request, http.Header{"Set-Cookie": {"sessionID=1234"}})

	if err != nil {
		panic(err)
	}

	nowDayNum := cache.GetNowDayNum(auctionUuid)        //获取当前天的人次
	liveTotalNum := cache.GetLiveTotalNum(auctionUuid)  //实时人数
	sampleNum := cache.GetSampleBiggestNum(auctionUuid) //获取最高人数
	getNowDayUser := cache.GetNowDayUser(auctionUuid)   //当天人数 dau

	cache.RedisClient.Incr(nowDayNum)                   //人次
	cache.RedisClient.PFAdd(getNowDayUser, userInfo.ID) //人数

	cache.RedisClient.Incr(liveTotalNum) //一分钟之内的人数自增
	minNum, _ := cache.RedisClient.Get(liveTotalNum).Int64()
	biggestNum, _ := cache.RedisClient.Get(sampleNum).Int64()
	if minNum > biggestNum || biggestNum == 0 {
		cache.RedisClient.Set(sampleNum, minNum, 0)
	}

	defer func() {
		fmt.Println(ws.Close())

		//清除数据
		cache.RedisClient.Decr(liveTotalNum) //实时减1

	}()

	if auctionUuid == "" {
		returnWrong(ws, serializer.WsResponse{Code: 1, Msg: "less param auctionUuid"})
		return
	}

	var wg sync.WaitGroup
	wg.Add(1)
	// 处理WebSocket消息
	//go readMessageFromScreen(ws, drDate) //处理客户端主动请求 .初始化
	go activeLive(ws, auctionUuid, &wg, uint32(userInfo.ID), currency)
	//cache.RedisClient.Incr()
	wg.Wait()

	return
}

// activeLive auctionUuid场次的uuid
func activeLive(ws *websocket.Conn, auctionUuid string, wg *sync.WaitGroup, userId uint32, currency string) {

	defer wg.Done()

	// 输出WebSocket消息内容
	firstWs(ws, auctionUuid, currency, userId)

	//订阅频道
	sub := cache.RedisClient.Subscribe(cache.GetFansChannel(auctionUuid)) // done
	defer sub.Close()
	ch := sub.Channel()

	stdoutDone := make(chan struct{})
	go ping(ws, stdoutDone) //存活检测

	//一个协程从客户端拿数据
	for {
		var msg *redis.Message

		select {
		case <-stdoutDone:
			fmt.Println("前端终止连接")
			return
		case v := <-ch: //redis 中 粉丝频道 广播数据通知
			msg = v
			fmt.Println("广播数据", msg)
		}

		//提示
		fmt.Println("频道数据", msg)
		if msg != nil {
			fmt.Println("接受数据频道数据", msg.Payload)
			returnFansWs(ws, auctionUuid, msg.Payload, userId, currency)
		}
		fmt.Println()
		fmt.Println()
		fmt.Println()
	}

}

func returnFansWs(ws *websocket.Conn, auctionUuid string, info string, userId uint32, currency string) {

	publishRes := &logic.FanChannel{}

	req := &fenghe.AuctionDetail{
		Uuid:   auctionUuid,
		UserId: userId,
	}

	wsTempRes := serializer.WsResponse{
		Status: 0,
		Code:   0,
	}

	err := json.Unmarshal([]byte(info), publishRes)

	if err != nil {
		fmt.Println("1---", err)
		wsTempRes.Code = 1
		returnWrong(ws, wsTempRes)
		return
	}

	res, err := service.FengheProvider.NowBiddingArtworkAuction(context.Background(), req)

	fmt.Println("2---", err)
	if err != nil {
		//拉去最新的数据
		wsTempRes.Code = 1
		returnWrong(ws, wsTempRes)
		return
	}
	res.WsType = publishRes.Type
	setBuyList(res.AuctionPriceList, currency)
	setNeedPayBuysList(res.NeedPayBuys, currency)

	res.NowAuctionPrice.Currency = currency
	nowCnPrice := res.NowAuctionPrice.NowPrice
	res.NowAuctionPrice.NowPrice = logic.GetCurrencyMoney(nowCnPrice, currency) //ok
	res.NowAuctionPrice.NextPrice = logic.GetCurrencyMoneyAuction(res.NowAuctionPrice.NextPrice, currency, userId)
	if res.NowAuctionPrice.Status == "done" {
		res.NowAuctionPrice.SuccessPrice = logic.GetCurrencyMoney(res.NowAuctionPrice.SuccessPrice, currency) //ok
	}

	cache.RedisClient.Set(cache.GetPriceNowKey(currency, res.NowAuctionPrice.NowPrice), nowCnPrice, 12*time.Hour)

	// 是tip
	res.Tip = &fenghe.Tip{TipType: publishRes.TipType} //默认类型
	if publishRes.Type == e.FanAuctionChannel_Tip {    //是tip

		if publishRes.TipType == e.FanAuctionChannel_TempArtowkrOver {
			/*
				falling即将落锤
				othersBid已有其他人出价
				successBid竞拍成功
				artworkOver(本拍品结束)
				failBid竞拍失败
				over(竞拍结束)
			*/
			if publishRes.UserId == userId { //成功的订单是谁的
				res.Tip = &fenghe.Tip{TipType: e.FanAuctionChannel_Tip_SuccessBid}
			} else {
				res.Tip = &fenghe.Tip{TipType: e.FanAuctionChannel_Tip_ArtworkOver}
			}
		} else if publishRes.TipType == e.FanAuctionChannel_Tip_OthersBid {
			if publishRes.UserId == userId {
				res.Tip = &fenghe.Tip{TipType: e.FanAuctionChannel_Tip_myBidEffect}
			}
		}
	}

	wsTempRes.Data = res

	returnWrong(ws, wsTempRes)

}

func returnWrong(ws *websocket.Conn, wsTempRes serializer.WsResponse) {

	b, _ := json.Marshal(wsTempRes)

	_ = ws.WriteMessage(1, b) //
	return

}

// firstWs 首次连接的数据
func firstWs(ws *websocket.Conn, auctionUuid, currency string, userId uint32) []byte {

	//首次获取列表数据
	req := &fenghe.AuctionDetail{
		Uuid:   auctionUuid,
		UserId: userId,
	}

	res, err := service.FengheProvider.NowBiddingArtworkAuction(context.Background(), req)
	res.WsType = "first"
	if err != nil {
		returnWrong(ws, serializer.WsResponse{Code: 1, Msg: err.Error()})
		return []byte{}
	}

	res.NowAuctionPrice.Currency = currency
	nowCnPrice := res.NowAuctionPrice.NowPrice
	res.NowAuctionPrice.NowPrice = logic.GetCurrencyMoney(res.NowAuctionPrice.NowPrice, currency)
	res.NowAuctionPrice.NextPrice = logic.GetCurrencyMoneyAuction(res.NowAuctionPrice.NextPrice, currency, userId)
	if res.NowAuctionPrice.Status == "done" {
		res.NowAuctionPrice.SuccessPrice = logic.GetCurrencyMoney(res.NowAuctionPrice.SuccessPrice, currency)
	}

	cache.RedisClient.Set(cache.GetPriceNowKey(currency, res.NowAuctionPrice.NowPrice), nowCnPrice, 12*time.Hour)

	setBuyList(res.AuctionPriceList, currency)
	fmt.Println(res.NeedPayBuys)
	setNeedPayBuysList(res.NeedPayBuys, currency)

	wsRes := serializer.WsResponse{
		Status: 0,
		Code:   0,
		Data:   res,
	}
	if err != nil {
		wsRes.Code = 1
		wsRes.Msg = err.Error()
	}

	b, _ := json.Marshal(wsRes)

	_ = ws.WriteMessage(1, b)

	// 输出WebSocket消息内容
	if res.Artwork != nil && res.Artwork.IsSoled == true { //已经售出
		res.Tip = &fenghe.Tip{TipType: e.FanAuctionChannel_Tip_ArtworkOver}
		res.WsType = "tip"

		wsRes := serializer.WsResponse{
			Status: 0,
			Code:   0,
			Data:   res,
		}
		if err != nil {
			wsRes.Code = 1
			wsRes.Msg = err.Error()
		}

		b, _ := json.Marshal(wsRes)

		_ = ws.WriteMessage(1, b)
	}

	return b
}

func BigScreenTest(c *gin.Context) {

	var req fenghe.WsRtcLive

	if err := c.ShouldBindBodyWith(&req, binding.JSON); err != nil {
		service.Error(c, e.InvalidParams, err)
		return
	}

	cache.RedisClient.Publish(cache.GetFansChannel(req.Auction.Uuid), req.WsType)

	service.Success(c, nil)
	return
}

func setBuyList(list *fenghe.AuctionPriceList, currency string) {

	NewCap := capexe.NewCapExe()
	rate := logic.GetCurrencyExchangeRate(currency, false)
	for k, v := range list.Buys {
		list.Buys[k].BaseMoney = logic.GetCurrencyMoneyFromRate(v.BaseMoney, rate)
		list.Buys[k].BaseCurrency = currency
	}
	NewCap.Log("汇率内部")
}

func setNeedPayBuysList(list []*fenghe.BaseAuctionBuy, currency string) {

	for k, v := range list {

		list[k].LeftCnyPrice = v.LeftPrice
		list[k].LeftPrice = logic.GetCurrencyMoney(v.LeftPrice, currency) //TODO
		list[k].LeftCurrency = currency

		list[k].BaseMoney = logic.GetCurrencyMoney(v.BaseMoney, currency) //TODO
		list[k].BaseCurrency = currency

	}
}
