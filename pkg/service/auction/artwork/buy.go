package artwork

import (
	"context"
	"encoding/json"
	"fmt"
	"github.com/fonchain_enterprise/client-auction/api/account"
	"github.com/fonchain_enterprise/client-auction/api/fenghe"
	"github.com/fonchain_enterprise/client-auction/pkg/common/cache"
	"github.com/fonchain_enterprise/client-auction/pkg/e"
	"github.com/fonchain_enterprise/client-auction/pkg/logic"
	"github.com/fonchain_enterprise/client-auction/pkg/middleware"
	"github.com/fonchain_enterprise/client-auction/pkg/model/login"
	"github.com/fonchain_enterprise/client-auction/pkg/model/query"
	"github.com/fonchain_enterprise/client-auction/pkg/model/vo"
	"github.com/fonchain_enterprise/client-auction/pkg/serializer"
	"github.com/fonchain_enterprise/client-auction/pkg/service"
	"github.com/fonchain_enterprise/client-auction/pkg/service/fenghe/handle"
	"github.com/gin-gonic/gin"
	"github.com/gin-gonic/gin/binding"
	"github.com/go-redis/redis"
	"github.com/google/uuid"
	"github.com/gorilla/websocket"
	"github.com/jinzhu/copier"
	"github.com/opentracing/opentracing-go/log"
	"net/http"
	"strconv"
	"strings"
	"sync"
	"time"
)

// Cancel 成交确认
func Cancel(c *gin.Context) {
	var req fenghe.AuctionBuyDetail

	if err := c.ShouldBindBodyWith(&req, binding.JSON); err != nil {
		service.Error(c, e.InvalidParams, err)
		return
	}

	res, err := service.FengheProvider.CancelAuctionBuy(c, &req)
	if err != nil {
		service.Error(c, e.Error, err)
		return
	}

	info, err1 := service.FengheProvider.DetailAuctionBuy(c, &fenghe.AuctionBuyDetail{Uuid: req.Uuid})
	if err1 != nil {
		service.Error(c, e.Error, err)
		return
	}

	service.Success(c, res)

	err = logic.SendFansTip(info.Uuid, e.FanAuctionChannel_Tip_other, 0) //已有人出价

	logic.SendAdminLiveBuys(info.Uuid, info.AuctionArtworkUuid) //done 粉丝购买

	cache.RedisClient.Publish(cache.GetScreenChannel(info.Uuid), "1")

	return
}

// Sure 成交确认
func Sure(c *gin.Context) {
	fmt.Println("Sure -------------------1")
	var req query.SureReq
	var errs error

	if err := c.ShouldBindBodyWith(&req, binding.JSON); err != nil {
		service.Error(c, e.InvalidParams, err)
		return
	}

	realReq := fenghe.SureAuctionBuyRequest{
		AuctionArtworkUuid: req.Uuid,
		Money:              req.Money,
	}

	res, err := service.FengheProvider.SureAuctionBuy(c, &realReq)
	if err != nil {
		service.Error(c, e.Error, err)
		return
	}

	info, err1 := service.FengheProvider.DetailAuctionBuy(c, &fenghe.AuctionBuyDetail{Uuid: res.Uuid})
	if err1 != nil {
		service.Error(c, e.Error, err)
		return
	}

	err = logic.SendFansTipAndOverArtworkUuid(info.AuctionArtworkInfo.AuctionUuid, res.Uuid, info.UserID) // 当前拍品结束了

	cache.RedisClient.Publish(cache.GetScreenChannel(info.AuctionArtworkInfo.AuctionUuid), "1") //done

	//创建订单
	fmt.Println("sure --------------4 AccountProvider.Info --------------------")
	auctionUuid := ""
	if info.AuctionArtworkInfo != nil {
		auctionUuid = info.AuctionArtworkInfo.AuctionUuid
	}

	/***start 此处线下拍卖根据用户号牌生成用户 ***/
	var userInfo UserInfo
	if info.AuctionType == "local" {
		auctionUserNo, errs := strconv.Atoi(info.UserName)
		if errs != nil {
			service.Error(c, e.Failed, errs, "用户号牌错误")
			return
		}
		fmt.Println("sure --------------4 Sure.GetViewAuctionSessionUserNoList --------------------")
		//将线下用户转换成线上用户
		userInfo, err = GetOrCreateUser(c, auctionUuid, auctionUserNo)
		if err != nil {
			service.Error(c, e.Failed, errs, "生成线上用户失败，请联系管理员")
			return
		}

		info.UserID = uint32(userInfo.UserID)
		// info.UserName 默认是号牌 ，这里可以改为姓名。但是又让改回来。
		//info.UserName = userInfo.UserName

		/*** start 此处线下拍卖根据用户号牌生成用户 ***/
		_, err = service.FengheProvider.UpdateAuctionBuy(c, info)
		if err != nil {
			service.Error(c, e.Failed, err, "关联线上用户失败，请联系管理员")
			return
		}

	} else {

		infoRes, err := service.AccountProvider.UserInfoById(c, &account.InfoRequest{Domain: e.MallAuctionDomain, ID: uint64(info.UserID)})

		if err != nil {
			service.Error(c, e.Failed, err, "确实当前用户信息")
			return
		}

		userInfo = UserInfo{
			UserID:   uint64(info.UserID),
			UserName: infoRes.NickName,
			TelNum:   infoRes.TelNum,
		}
	}

	fmt.Println("sure --------------5 Sure.CreateAuctionOrder --------------------")
	fmt.Println("artwork_uuid :", info.ArtworkUuid)
	fmt.Println("auction_buy_uid :", info.Uuid)
	if info.ArtworkUuid == "" {
		service.Error(c, e.Failed, nil, "竞拍成功，但该商品未绑定画作，请联系管理员")
		return
	}
	errs = handle.CreateAuctionOrder(info.ArtworkUuid, info.Uuid, int64(userInfo.UserID), userInfo.UserName, userInfo.TelNum, info.BuyMoney, info.BuyCurrency, auctionUuid)
	if errs != nil {
		fmt.Println("创建订单失败:", errs.Error())
	}

	go sendMsg(c, userInfo, req.Money, info)

	service.Success(c, res)
	return
}

// SendMsg 价格有效
func sendMsg(c *gin.Context, userInfo UserInfo, price string, buyDetail *fenghe.AuctionBuyRequest) {

	artworkName := "拍卖作品"
	num := 1
	if buyDetail != nil && buyDetail.AuctionArtworkInfo != nil && buyDetail.AuctionArtworkInfo.Artwork != nil {
		artworkName = buyDetail.AuctionArtworkInfo.Artwork.Name
		num = int(buyDetail.AuctionArtworkInfo.Index)
	}

	urlInfo := fmt.Sprintf("%s|Lot25%03d|%s", price, num, artworkName)

	//发动拍卖成功短信
	sendRequest := &account.SendCustomMsgRequest{
		TelNum: userInfo.TelNum,
		Url:    urlInfo,
		ID:     userInfo.UserID,
		MId:    276003,
		SigNo:  231675,
	}

	service.AccountProvider.SendCustomMsg(c, sendRequest)

	return
}

// Effect 价格有效
func Effect(c *gin.Context) {

	var req fenghe.AuctionBuyRequest

	if err := c.ShouldBindBodyWith(&req, binding.JSON); err != nil {
		service.Error(c, e.InvalidParams, err)
		return
	}

	tempBuy, err := service.FengheProvider.DetailAuctionBuy(c, &fenghe.AuctionBuyDetail{Uuid: req.Uuid})
	if err != nil {
		service.Error(c, e.Error, err)
		return
	}

	auctionUuid := tempBuy.AuctionArtworkInfo.AuctionUuid

	realReq := fenghe.AuctionBuyRequest{
		Uuid:          req.Uuid,
		AuctionStatus: 4,
	}

	res, err := service.FengheProvider.UpdateAuctionBuy(c, &realReq)
	if err != nil {
		service.Error(c, e.Error, err)
		return
	}

	err = logic.SendFansTip(auctionUuid, e.FanAuctionChannel_Tip_OthersBid, tempBuy.UserID) //已有人出价

	logic.SendAdminLiveBuys(auctionUuid, tempBuy.AuctionArtworkUuid)    //管理系统确认有效
	cache.RedisClient.Publish(cache.GetScreenChannel(auctionUuid), "1") //done
	//创建订单
	go func() {
		var (
			artworkUid  string = req.ArtworkUuid
			buyUid      string = tempBuy.Uuid
			buyerId     int64  = int64(tempBuy.UserID)
			buyerName   string = tempBuy.UserName
			buyerTelNum string = "无"
			priceTotal  string = tempBuy.BuyMoney
			currency    string = tempBuy.BuyCurrency
			auctionUuid string
		)
		if tempBuy.AuctionArtworkInfo != nil {
			auctionUuid = tempBuy.AuctionArtworkInfo.AuctionUuid
		}
		//查询手机号
		userRes, _ := service.AccountProvider.Info(c, &account.InfoRequest{
			ID:     uint64(tempBuy.UserID),
			Domain: e.AuctionDomain,
		})
		if userRes != nil && userRes.Info != nil {
			buyerTelNum = userRes.Info.TelNum
		}
		err = handle.CreateAuctionOrder(artworkUid, buyUid, buyerId, buyerName, buyerTelNum, priceTotal, currency, auctionUuid)
		if err != nil {
			log.Error(err)
			return
		}
	}()

	service.Success(c, res)
	return
}

// FansBuyCreate 粉丝购买
func FansBuyCreate(c *gin.Context) {

	var req fenghe.FansAuctionBuyRequest
	uuidStr := uuid.NewString()

	if err := c.ShouldBindBodyWith(&req, binding.JSON); err != nil {
		service.Error(c, e.InvalidParams, err)
		return
	}

	userInfoAny, _ := c.Get(e.JwtInfo)
	userInfo := userInfoAny.(middleware.LoginInfo)
	req.UserId = uint32(userInfo.ID)

	req.UserName = userInfo.TelNum
	length := len(userInfo.TelNum)
	if length >= 4 {
		req.UserName = userInfo.TelNum[length-4:]
	}

	currency := logic.GetLanguageCurrency(c)
	fmt.Println("当前的货币币种是", currency)
	fmt.Println("当前的货币币种是", req.BuyMoney)
	fmt.Println("当前语言", c.Request.Header.Get("Accept-Language"))

	buyMoney := cache.RedisClient.Get(cache.GetPriceNowKey(currency, req.BuyMoney)).Val()
	fmt.Println(buyMoney)

	if buyMoney != "" {
		req.BuyMoney = buyMoney
	}

	res1, err := service.FengheProvider.DetailAuctionArtwork(c.Request.Context(), &fenghe.AuctionArtworkDetail{Uuid: req.AuctionArtworkUuid})
	if err != nil {
		service.Error(c, e.Error, err)
		return
	}
	auctionUuid := res1.AuctionUuid

	// 并发限制
	isSetSuccess, err := cache.LockConcurrency(cache.GetLimitSampleArtworkBuy(req.AuctionArtworkUuid, req.BuyMoney), uuidStr, 3*time.Second)

	if !isSetSuccess {
		service.Error(c, e.InvalidParams, fmt.Errorf("当前价格多人竞价中，您提交的时间靠后，请稍候再试"))
		return
	}

	defer func() {
		cache.DeleteLock(cache.GetLimitSampleArtworkBuy(req.AuctionArtworkUuid, req.BuyMoney), uuidStr)
	}()

	fmt.Println(req.BuyMoney)
	req.UserName = userInfo.TelNum

	res, err := service.FengheProvider.CreateFansAuctionBuy(c, &req)
	if err != nil {
		service.Error(c, e.Error, err)
		return
	}

	//提醒更新 广播
	cache.RedisClient.Publish(cache.GetScreenChannel(res1.AuctionUuid), "1")

	_ = logic.SendAdminLiveBuys(auctionUuid, req.AuctionArtworkUuid) //粉丝购买

	service.Success(c, res)
	return
}

// BuyCreate 出价记录
func BuyCreate(c *gin.Context) {

	var req fenghe.AuctionBuyRequest

	if err := c.ShouldBindBodyWith(&req, binding.JSON); err != nil {
		service.Error(c, e.InvalidParams, err)
		return
	}

	res1, err := service.FengheProvider.DetailAuctionArtwork(c.Request.Context(), &fenghe.AuctionArtworkDetail{Uuid: req.AuctionArtworkUuid})
	if err != nil {
		service.Error(c, e.Error, err)
		return
	}
	auctionUuid := res1.AuctionUuid

	if req.UserName == "" {
		service.Error(c, e.InvalidParams, nil, "现场号牌不能为空")
		return
	} else {
		req.UserName = strings.TrimSpace(req.UserName)
		auctionUserNo, err := strconv.Atoi(req.UserName)
		if err != nil {
			service.Error(c, e.InvalidParams, nil, "现场号牌必须是数字")
			return
		}
		check, err := service.FengheProvider.GetViewAuctionSessionUserNoList(c, &fenghe.GetViewAuctionSessionUserNoListRequest{
			Query:    &fenghe.ViewAuctionSessionUserNo{SessionNo: auctionUuid},
			Page:     1,
			PageSize: 1,
			Where:    fmt.Sprintf("user_no = %d", auctionUserNo),
		})
		if err == nil && check.Total == 0 {
			service.Error(c, e.InvalidParams, nil, "现场号牌不存在")
			return
		}
	}
	req.AuctionType = "local"

	req.BaseMoney = req.BuyMoney
	req.BaseCurrency = req.BuyCurrency
	req.AuctionStatus = 4

	res, err := service.FengheProvider.CreateAuctionBuy(c, &req)
	if err != nil {
		service.Error(c, e.Error, err)
		return
	}

	err = logic.SendFansTip(res1.AuctionUuid, e.FanAuctionChannel_Tip_OthersBid, 0) //已有人出价

	logic.SendAdminLiveBuys(auctionUuid, req.AuctionArtworkUuid) //done 粉丝购买

	cache.RedisClient.Publish(cache.GetScreenChannel(auctionUuid), "1")

	service.Success(c, res)
	return
}

// BuyList 出价记录
func BuyList(c *gin.Context) {

	var req fenghe.AuctionBuyList

	if err := c.ShouldBindBodyWith(&req, binding.JSON); err != nil {
		service.Error(c, e.InvalidParams, err)
		return
	}

	if req.AuctionArtworkUuid == "" {
		service.Success(c, nil)
		return
	}

	res, err := service.FengheProvider.ListAuctionBuy(c, &req)
	if err != nil {
		service.Error(c, e.Error, err)
		return
	}
	for k, _ := range res.Data {
		res.Data[k].AuctionArtworkInfo = nil
	}

	service.Success(c, res)
	return
}

// QuickMsg 即将落锤提醒
func QuickMsg(c *gin.Context) {

	var req query.ClientWsReq

	if err := c.ShouldBindBodyWith(&req, binding.JSON); err != nil {
		service.Error(c, e.InvalidParams, err)
		return
	}

	res1, err := service.FengheProvider.DetailAuctionArtwork(c.Request.Context(), &fenghe.AuctionArtworkDetail{Uuid: req.Uuid})
	if err != nil {
		service.Error(c, e.Error, err)
		return
	}

	cache.RedisClient.Publish(cache.GetArtworkChannel(req.Uuid), "ex") // 好像没有坚挺
	err = logic.SendFansTip(res1.AuctionUuid, e.FanAuctionChannel_Tip_Falling, 0)
	if err != nil {
		service.Error(c, e.Error, err)
		return
	}

	service.Success(c, nil)
	return
}

// BuyListLive 出价记录
func BuyListLive(c *gin.Context) {
	userName := ""

	fmt.Println("k1------")
	type AuctionBuyListTemp struct {
		Page               uint64 `form:"page" default:"1"`
		PageSize           uint64 `form:"pageSize" default:"9"`
		AuctionArtworkUuid string `form:"auctionArtworkUuid" default:""`
		AuctionUuid        string `form:"auctionUuid" default:""`
	}

	var tempReq AuctionBuyListTemp

	if err := c.ShouldBindWith(&tempReq, binding.Query); err != nil {
		service.Error(c, e.InvalidParams, err)
		return
	}

	userInfoAny, isOk := c.Get("jwtInfo")
	if isOk {
		userInfo := userInfoAny.(login.Info)
		userName = userInfo.NickName
	}

	req := &fenghe.AuctionBuyList{
		Page:           tempReq.Page,
		PageSize:       tempReq.PageSize,
		NowAuctionUuid: tempReq.AuctionUuid,
	}

	fmt.Println("1--------------", req.PageSize)
	fmt.Println("1--------------", req.Page)
	fmt.Println("1--------------", req.AuctionArtworkUuid)

	// 获取WebSocket连接
	var cstUpgrader = websocket.Upgrader{
		Subprotocols:      []string{"p0", "p1"},
		ReadBufferSize:    1024,
		WriteBufferSize:   1024,
		EnableCompression: true,
		Error: func(w http.ResponseWriter, r *http.Request, status int, reason error) {
			http.Error(w, reason.Error(), status)
		},
		CheckOrigin: func(r *http.Request) bool {
			return true
		},
	}

	ws, err := cstUpgrader.Upgrade(c.Writer, c.Request, http.Header{"Set-Cookie": {"sessionID=1234"}})

	if err != nil {
		panic(err)
	}

	var wg sync.WaitGroup
	wg.Add(1)
	// 处理WebSocket消息
	//go readMessageFromScreen(ws, drDate) //处理客户端主动请求 .初始化
	go buyList(ws, req, &wg, userName)

	wg.Wait()
	err = ws.Close()
	fmt.Println("执行结束", err)
	return
}

// activePushScreen2 服务端主动推送数据 预约病人总数 到场病人总数 已看诊病人总数 排队人数
func buyList(ws *websocket.Conn, req *fenghe.AuctionBuyList, wg *sync.WaitGroup, userName string) {
	uuidStr := uuid.NewString()

	defer wg.Done()

	//首次获取列表数据
	res, err := service.FengheProvider.ListAuctionBuy(context.Background(), req)

	firstNewRes := vo.AuctionBuyListResponse{}
	if err == nil {
		copier.Copy(&firstNewRes, res)
	}

	wsRes := serializer.WsResponse{
		Status: 0,
		Code:   0,
		Data:   firstNewRes,
	}
	if err != nil {
		wsRes.Code = 1
		wsRes.Msg = err.Error()
	}

	b, _ := json.Marshal(wsRes)

	// 输出WebSocket消息内容
	_ = ws.WriteMessage(1, b)

	//订阅频道
	sub := cache.RedisClient.Subscribe(cache.GetSquareChannel(req.NowAuctionUuid))
	defer sub.Close()
	ch := sub.Channel()

	stdoutDone := make(chan struct{})
	//fromClientCh := make(chan *vo.AuctionBuyList)
	go ping(ws, stdoutDone) //存活检测
	//go readMessages(ws, fromClientCh) //存活检测

	//一个协程从客户端拿数据
	for {
		var msg *redis.Message

		select {
		case <-stdoutDone:
			fmt.Println("前端终止连接")
			return
		case v := <-ch: //广播数据通知
			msg = v
			fmt.Println("1广播数据", msg)
			/*case v := <-fromClientCh: //客户端通知切换数据了
			if v == nil {
				fmt.Println("读取前端信息部分终止")
				return
			}

			if v.AuctionArtworkUuid != "" {
				//req.AuctionArtworkUuid = v.AuctionArtworkUuid
			}

			if v.Page != 0 {
				req.Page = v.Page
			}

			if v.PageSize != 0 {
				req.PageSize = v.PageSize
			}
			fmt.Println("2广播数据", msg)
			*/
		}

		timeNow := time.Now()
		newRes := vo.AuctionBuyListResponse{}
		wsTempRes := serializer.WsResponse{Status: 0, Code: 0}

		fmt.Println("list:", userName, ":uuid:", uuidStr, time.Now(), "----", "耗时", timeNow.Sub(time.Now()), "-获取数据1--", msg)
		if msg != nil {
			fmt.Println("接受数据频道数据", msg.Payload)
		}

		listRes, err := service.FengheProvider.ListAuctionBuy(context.Background(), req)
		fmt.Println("list:", userName, ":uuid:", uuidStr, time.Now(), "----", "耗时", timeNow.Sub(time.Now()), "-微服务之后2--", msg)

		if err == nil {
			copier.Copy(&newRes, listRes)
		}

		if msg != nil && msg.Payload == e.SynOpen {
			newRes.WsType = e.SynOpen
		}

		//拉去最新的数据
		wsTempRes.Data = newRes

		if err != nil {
			wsTempRes.Code = 1
			wsTempRes.Msg = err.Error()
		}

		sendWs(ws, wsTempRes)

		fmt.Println("list:", userName, ":uuid:", uuidStr, time.Now(), "----", time.Now(), "耗时", timeNow.Sub(time.Now()), "-推送完毕3--")
		fmt.Println()
		fmt.Println()
		fmt.Println()

	}

	return
}

func sendWs(ws *websocket.Conn, wsTempRes serializer.WsResponse) {

	//拉去最新的数据

	b, _ := json.Marshal(wsTempRes)

	_ = ws.WriteMessage(1, b)
	return

}
