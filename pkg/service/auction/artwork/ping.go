package artwork

import (
	"encoding/json"
	"fmt"
	"github.com/fonchain_enterprise/client-auction/pkg/model/vo"
	"github.com/gorilla/websocket"
	"log"
	"time"
)

type ClientWsMsg struct {
	MsgType string `json:"msgType"`
	Payload string `json:"payload"`
}

func ping(ws *websocket.Conn, done chan struct{}) {
	ticker := time.NewTicker(3 * time.Second)
	defer ticker.Stop()
	for {
		select {
		case <-ticker.C: // 等待 ticker 发出的时间信号
			if err := ws.WriteControl(websocket.PingMessage, []byte{}, time.Now().Add(10*time.Second)); err != nil { //ping
				done <- struct{}{}
				fmt.Println("err------------ping:", err)
				return
			}
			//fmt.Println()
			//fmt.Println("ping:ok", time.Now())
			//fmt.Println()
		case <-done: // 如果有其他地方关闭了 done 通道，退出循环
			return
		}

		/*
			if err := ws.WriteControl(websocket.PingMessage, []byte{}, time.Now().Add(10*time.Second)); err != nil { //ping
				done <- struct{}{}
				fmt.Println("ping:", err)
				return
			}

		*/
	}
}

// readMessages 从 WebSocket 读取消息并在控制台打印
func readMessages(ws *websocket.Conn, msgCh chan *vo.AuctionBuyList) {
	defer close(msgCh)
	info := new(vo.AuctionBuyList)
	for {
		_, message, err := ws.ReadMessage()
		if err != nil {
			log.Printf("读取消息失败: %v", err)
			return
		}

		err = json.Unmarshal(message, info)
		fmt.Println(info, err)

		if err == nil {
			msgCh <- info
		}

	}
}
