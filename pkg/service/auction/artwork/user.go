// Package artwork -----------------------------
// @file      : user.go
// <AUTHOR> JJXu
// @contact   : <EMAIL>
// @time      : 2025/5/21 16:30
// -------------------------------------------
package artwork

import (
	"context"
	"errors"
	"fmt"
	"github.com/fonchain_enterprise/client-auction/api/account"
	"github.com/fonchain_enterprise/client-auction/api/fenghe"
	"github.com/fonchain_enterprise/client-auction/pkg/e"
	"github.com/fonchain_enterprise/client-auction/pkg/service"
	"time"
)

type UserInfo struct {
	UserID   uint64
	UserName string
	TelNum   string
}

func GetOrCreateUser(ctx context.Context, auctionUuid string, auctionUserNo int) (info UserInfo, err error) {
	auctionUserNoList, err2 := service.FengheProvider.GetViewAuctionSessionUserNoList(ctx, &fenghe.GetViewAuctionSessionUserNoListRequest{
		Query:    &fenghe.ViewAuctionSessionUserNo{SessionNo: auctionUuid},
		Page:     1,
		PageSize: 1,
		Where:    fmt.Sprintf("user_no = %d", auctionUserNo),
	})
	if err2 == nil && auctionUserNoList.Total > 0 {
		info.TelNum = auctionUserNoList.List[0].Phone
		info.UserName = auctionUserNoList.List[0].UserName
	} else {
		fmt.Printf("没有找到用户号牌  auctionUid: %s , user_no: %d", auctionUuid, auctionUserNo)
		err = errors.New("没找到用户号牌")
		return
	}
	infoRes, err := service.AccountProvider.List(ctx, &account.ListRequest{Domain: e.MallAuctionDomain, TelNum: info.TelNum})
	if err != nil {
		return
	}
	if infoRes.Count > 0 {
		info.UserID = infoRes.Data[0].ID
	} else {
		registerRequest := account.RegistRequest{
			Domain:    e.MallAuctionDomain,
			NickName:  info.UserName,
			TelNum:    info.TelNum,
			Password:  info.TelNum,
			EnterDate: time.Now().Format(time.DateTime),
			Extend:    &account.Extend{JumpTo: "onsite"}, //origin-老平台  onsite 当前
		}
		var registerResult *account.RequestStatus
		registerResult, err = service.AccountProvider.Register(ctx, &registerRequest)
		if err != nil {
			return
		}
		info.UserID = registerResult.ID
	}
	return
}
