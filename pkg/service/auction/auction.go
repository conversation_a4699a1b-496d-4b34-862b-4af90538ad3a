package auction

import (
	"errors"
	"fmt"
	"github.com/fonchain_enterprise/client-auction/api/fenghe"
	"github.com/fonchain_enterprise/client-auction/pkg/e"
	"github.com/fonchain_enterprise/client-auction/pkg/logic"
	"github.com/fonchain_enterprise/client-auction/pkg/logic/live"
	"github.com/fonchain_enterprise/client-auction/pkg/model/login"
	"github.com/fonchain_enterprise/client-auction/pkg/service"
	"github.com/fonchain_enterprise/client-auction/pkg/service/order"
	"github.com/gin-gonic/gin"
	"github.com/gin-gonic/gin/binding"
	"math/rand"
	"time"
)

// DefaultDetail 用户登录操作
func DefaultDetail(c *gin.Context) {

	var req fenghe.AuctionDetail

	if err := c.ShouldBindBodyWith(&req, binding.JSON); err != nil {
		service.Error(c, e.InvalidParams, err)
		return
	}

	req.Uuid = e.DefaultUuid //deprecated

	res, err := service.FengheProvider.DetailAuction(c, &req)
	if err != nil {
		service.Error(c, e.Error, err)
		return
	}

	service.Success(c, res)
	return
}

// DefaultUpdate
func DefaultUpdate(c *gin.Context) {

	var req fenghe.AuctionRequest

	if err := c.ShouldBindBodyWith(&req, binding.JSON); err != nil {
		service.Error(c, e.InvalidParams, err)
		return
	}
	req.Uuid = e.DefaultUuid //deprecated

	res, err := service.FengheProvider.UpdateAuction(c, &req)
	if err != nil {
		service.Error(c, e.Error, err)
		return
	}

	service.Success(c, res)
	return
}

// GetLiveUrl 获取直播地址加密后的地址
func GetLiveUrl(c *gin.Context) {

	fmt.Println("1------")
	var req fenghe.CommonReq

	if err := c.ShouldBindBodyWith(&req, binding.JSON); err != nil {
		service.Error(c, e.InvalidParams, err)
		return
	}

	res, err := service.FengheProvider.GetPullLiveUrl(c.Request.Context(), &req)
	if err != nil {
		service.Error(c, e.Error, err)
		return
	}

	var tempUrl string
	liveRegion := logic.GetLiveRegion(c)
	if _, isOk := res.PullUrlMap[liveRegion]; isOk {
		tempUrl = res.PullUrlMap[liveRegion]
	} else {
		for _, v := range res.PullUrlMap {
			tempUrl = v
			break // 只需要第一个键值对，所以在此处中断循环
		}
	}

	service.Success(c, map[string]interface{}{"expiredAt": time.Now().Add(4 * time.Hour).Unix(), "code": live.CreateCodeUrl(tempUrl), "id": rand.Intn(1000), "name": "日志"})

	return
}

// FrontDetail 用户登录操作
func FrontDetail(c *gin.Context) {

	fmt.Println("1------")
	var req fenghe.AuctionDetail

	if err := c.ShouldBindBodyWith(&req, binding.JSON); err != nil {
		service.Error(c, e.InvalidParams, err)
		return
	}
	req.Domain = e.MallAuctionDomain //无

	res, err := service.FengheProvider.DetailAuction(c, &req)
	if err != nil {
		service.Error(c, e.Error, err)
		return
	}
	res.AuctionArtwork = nil
	if res.EndNum > 0 && uint32(res.EndNum) < res.TotalNum {
		res.TotalNum = uint32(res.EndNum)
	}

	//res.StartTitle = logic.GetLanguageTime(res.StartTitle, logic.GetLanguage(c))

	service.Success(c, res)
	return
}

// Detail 用户登录操作
func Detail(c *gin.Context) {

	fmt.Println("1------")
	var req fenghe.AuctionDetail

	if err := c.ShouldBindBodyWith(&req, binding.JSON); err != nil {
		service.Error(c, e.InvalidParams, err)
		return
	}
	req.Domain = e.MallAuctionDomain //无

	res, err := service.FengheProvider.DetailAuction(c, &req)
	if err != nil {
		service.Error(c, e.Error, err)
		return
	}
	res.AuctionArtwork = nil

	//res.StartTitle = logic.GetLanguageTime(res.StartTitle, logic.GetLanguage(c))

	service.Success(c, res)
	return
}

// UpdateLang
func UpdateLang(c *gin.Context) {

	var req fenghe.AuctionRequest

	if err := c.ShouldBindBodyWith(&req, binding.JSON); err != nil {
		service.Error(c, e.InvalidParams, err)
		return
	}

	res, err := service.FengheProvider.UpdateAuctionLang(c, &req)
	if err != nil {
		service.Error(c, e.Error, err)
		return
	}

	service.Success(c, res)
	return
}

// Update
func Update(c *gin.Context) {

	var req fenghe.AuctionRequest

	if err := c.ShouldBindBodyWith(&req, binding.JSON); err != nil {
		service.Error(c, e.InvalidParams, err)
		return
	}

	res, err := service.FengheProvider.UpdateAuction(c, &req)
	if err != nil {
		service.Error(c, e.Error, err)
		return
	}

	service.Success(c, res)
	return
}

// Regions
func Regions(c *gin.Context) {

	var res []map[string]interface{}
	res = make([]map[string]interface{}, 0)
	res = append(res, map[string]interface{}{"name": "华东2 (上海)", "liveRegion": "sh"})
	res = append(res, map[string]interface{}{"name": "亚太东北 1(东京)", "liveRegion": "jp"})

	service.Success(c, res)
	return
}

func List(c *gin.Context) {

	var req fenghe.AuctionList

	if err := c.ShouldBindBodyWith(&req, binding.JSON); err != nil {
		service.Error(c, e.InvalidParams, err)
		return
	}

	res, err := service.FengheProvider.ListAuction(c, &req)
	if err != nil {
		service.Error(c, e.Error, err)
		return
	}

	service.Success(c, res)
	return
}

// Create
func Create(c *gin.Context) {

	var req fenghe.AuctionRequest

	if err := c.ShouldBindBodyWith(&req, binding.JSON); err != nil {
		service.Error(c, e.InvalidParams, err)
		return
	}
	info, err := login.GetUserInfoFromCV2(c)
	if err != nil {
		service.Error(c, e.InvalidParams, err)
		return
	}

	req.OperatorName = info.NickName
	res, err := service.FengheProvider.CreateAuction(c, &req)
	if err != nil {
		service.Error(c, e.Error, err)
		return
	}

	req.Uuid = res.Uuid
	res1, err1 := service.FengheProvider.UpdateAuction(c, &req)
	if err1 != nil {
		service.Error(c, e.Error, err1)
		return
	}

	service.Success(c, res1)
	return
}

// UpdateEndNum
func UpdateEndNum(c *gin.Context) {

	var req fenghe.UpdateAuctionEndNumRequest

	if err := c.ShouldBindBodyWith(&req, binding.JSON); err != nil {
		service.Error(c, e.InvalidParams, err)
		return
	}

	res, err := service.FengheProvider.UpdateAuctionEndNum(c, &req)
	if err != nil {
		service.Error(c, e.Error, err)
		return
	}

	service.Success(c, res)
	return
}

// Remove
func Remove(c *gin.Context) {

	var req fenghe.AuctionDetail

	if err := c.ShouldBindBodyWith(&req, binding.JSON); err != nil {
		service.Error(c, e.InvalidParams, err)
		return
	}

	res, err := service.FengheProvider.RemoveAuction(c, &req)
	if err != nil {
		service.Error(c, e.Error, err)
		return
	}

	service.Success(c, res)
	return
}

// CreateLive 生成直播连接
func CreateLive(c *gin.Context) {

	var req fenghe.CommonReq

	if err := c.ShouldBindBodyWith(&req, binding.JSON); err != nil {
		service.Error(c, e.InvalidParams, err)
		return
	}

	res, err := service.FengheProvider.CreateLiveUrl(c, &req)
	if err != nil {
		service.Error(c, e.InvalidParams, err)
		return
	}

	service.Success(c, map[string]string{"url": res.PushUrl})
	return
}

// SetLive 开始直播
func SetLive(c *gin.Context) {

	var req fenghe.AuctionRequest

	if err := c.ShouldBindBodyWith(&req, binding.JSON); err != nil {
		service.Error(c, e.InvalidParams, err)
		return
	}

	res, err := service.FengheProvider.UpdateBaseAuction(c, &req)
	if err != nil {
		service.Error(c, e.Error, err)
		return
	}

	// 结束直播
	if req.IsLiving != 1 {
		err := logic.SendFansInfo(req.Uuid, e.FanAuctionChannel_Over)
		if err != nil {
			service.Error(c, e.Error, err)
			return
		}
	}

	service.Success(c, res)
	return
}

// 创建订单
type CreateBuyOrderReq struct {
	BuyUid             string `json:"buyUid"`
	Price              string `json:"price"`
	Currency           string `json:"currency"`
	TestReturnHost     string `json:"testReturnHost"`
	TestReturnEndPoint string `json:"testReturnEndPoint"`
}

func (c *CreateBuyOrderReq) Validate() error {
	if c.BuyUid == "" {
		return errors.New("请填写商品UID")
	}
	return order.ValidateAmount(c.Price)
}
