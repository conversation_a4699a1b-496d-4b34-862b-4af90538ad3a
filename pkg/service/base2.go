// Package service -----------------------------
// @file      : base2.go
// <AUTHOR> JJXu
// @contact   : <EMAIL>
// @time      : 2025/1/24 17:02
// -------------------------------------------
package service

import (
	"github.com/fonchain_enterprise/client-auction/pkg/e"
	"github.com/fonchain_enterprise/client-auction/pkg/serializer"
	"github.com/gin-gonic/gin"
	"reflect"
)

type ListType struct {
	Data  any   `json:"Data"`
	Total int32 `json:"count"`
}
type ListTypeStat struct {
	Data  any   `json:"Data"`
	Total int32 `json:"count"`
	Stat  any   `json:"stat"`
}
type ResponseOption func(*serializer.Response)

func OptionMsg(msg string) ResponseOption {
	return func(response *serializer.Response) {
		response.Msg = msg
	}
}

func OptionPage[T1 int | int32 | int64 | uint64, T2 int32 | int64 | uint64 | int](page, pageSize T1, total T2) ResponseOption {
	return func(response *serializer.Response) {
		//优化返回值，使其不为空
		//t := reflect.TypeOf(response.Data)
		if response.Data == nil { //|| ((t.Kind() == reflect.Slice || t.Kind() == reflect.Array) && reflect.ValueOf(response.Data).Len() == 0)
			response.Data = ListType{Data: []struct{}{}}
		} else if list, ok := response.Data.(ListType); ok {
			//t := reflect.TypeOf(list)
			//fmt.Println(t.Kind() == reflect.Slice)
			//fmt.Println(t.Kind() == reflect.Array)
			//fmt.Println(reflect.ValueOf(list.Data).Len())
			if list.Data == nil || reflect.ValueOf(list.Data).Len() == 0 {
				response.Data = ListType{Data: []struct{}{}}
			}
		}
		response.Page = &serializer.PageInfo{
			Page:     int32(page),
			PageSize: int32(pageSize),
			Total:    int32(total),
		}
		if list, ok := response.Data.(ListType); ok {
			response.Data = ListType{
				Data:  list.Data,
				Total: int32(total),
			}
		}
	}
}

func List(c *gin.Context, list any, opts ...ResponseOption) {
	var response = serializer.Response{
		Status: e.Ok,
		Code:   e.Ok,
		Data:   ListType{Data: list},
		Msg:    "ok",
		Keys:   []string{"*"},
	}
	if opts != nil {
		for _, opt := range opts {
			opt(&response)
		}
	}
	c.JSON(response.Status, response)
}

type commonNumber interface {
	int | int16 | int32 | int64 | uint32 | uint64
}

func ListStat[T commonNumber](c *gin.Context, list, ext any, page, pageSize, total T) {
	var response = serializer.Response{
		Status: e.Ok,
		Code:   e.Ok,
		Data: ListTypeStat{
			Data:  list,
			Total: int32(total),
			Stat:  ext,
		},
		Msg:       "ok",
		Keys:      []string{"*"},
		Positions: ext,
		Page: &serializer.PageInfo{
			Page:     int32(page),
			PageSize: int32(pageSize),
			Total:    int32(total),
		},
	}
	c.JSON(response.Status, response)
}
