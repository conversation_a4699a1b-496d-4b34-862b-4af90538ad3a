package service

import (
	"dubbo.apache.org/dubbo-go/v3/config"
	_ "dubbo.apache.org/dubbo-go/v3/imports"
	"fmt"
	"github.com/fonchain_enterprise/client-auction/api/account"
	"github.com/fonchain_enterprise/client-auction/api/artist"
	"github.com/fonchain_enterprise/client-auction/api/artwork_query"
	"github.com/fonchain_enterprise/client-auction/api/chain"
	"github.com/fonchain_enterprise/client-auction/api/contract"
	"github.com/fonchain_enterprise/client-auction/api/custom_contract"
	"github.com/fonchain_enterprise/client-auction/api/department"
	"github.com/fonchain_enterprise/client-auction/api/fenghe"
	"github.com/fonchain_enterprise/client-auction/api/order"
	"github.com/fonchain_enterprise/client-auction/api/payment"
	"github.com/fonchain_enterprise/client-auction/api/photoWall"
	"github.com/fonchain_enterprise/client-auction/api/position"
	"github.com/fonchain_enterprise/client-auction/api/rule"
	backendSeries "github.com/fonchain_enterprise/client-auction/api/series"
	"github.com/fonchain_enterprise/client-auction/api/shopbrand"
	"github.com/fonchain_enterprise/client-auction/api/task"
	"os"
)

var PositionProvider = new(position.PositionClientImpl)
var ContractProvider = new(contract.ContractClientImpl)
var AccountProvider = new(account.AccountClientImpl)
var FengheProvider = new(fenghe.FengheProviderClientImpl)
var RuleProvider = new(rule.RuleClientImpl)
var CustomContractProvider = new(custom_contract.CustomContractClientImpl)
var OrderProvider = new(order.OrderClientImpl)
var BackendSeriesProvider = new(backendSeries.SeriesClientImpl) //系列微服务
var BrandProvider = new(shopbrand.BrandClientImpl)              //系列微服务
var TaskProvider = new(task.TaskClientImpl)
var ArtworkQueryProvider = new(artwork_query.ArtworkQueryClientImpl)
var ArtistProvider = new(artist.ArtistClientImpl)
var PaymentProvider = new(payment.PaymentCentClientImpl)
var DepartmentProvider = new(department.DepartmentClientImpl)
var ChainProvider = new(chain.ChainClientImpl)
var PhotoWallProvider = new(photoWall.PhotoWallProviderClientImpl)

func init() {
	fmt.Println("********")
	if os.Getenv("SKIP_REGISTER_MICRO_SERVICE") == "true" {
		return
	}

	init_fenghe()
	init_micro()
	init_auth()
	init_photoWall()

	if err := config.Load(); err != nil {
		panic(err)
	}
}
