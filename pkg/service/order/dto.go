// Package order -----------------------------
// @file      : dto.go
// <AUTHOR> JJXu
// @contact   : <EMAIL>
// @time      : 2025/1/26 11:38
// -------------------------------------------
package order

import (
	"errors"
	"fmt"
	"regexp"
	"strconv"
	"strings"
)

func ValidateAmount(price string) error {
	// 验证 Amount 是否为数字且大于 0、支持两位小数、整数部分最多 9 位
	amountRegex := regexp.MustCompile(`^(0|[1-9]\d{0,8})(\.\d{1,2})?$`) // 匹配整数或最多两位小数的数字
	if !amountRegex.MatchString(price) {
		return errors.New("金额必须大于0且最多支持两位小数")
	}
	// 将 Amount 转换为浮点数进行范围检查
	amount, err := strconv.ParseFloat(price, 64)
	if err != nil {
		return errors.New("金额格式错误")
	}
	if amount <= 0 {
		return errors.New("金额必须大于0")
	}
	if amount >= 1000000000 {
		return errors.New("金额必须小于10亿")
	}
	return nil
}

type PayResultWebParams struct {
	Message string
	Result  string `query:"result"` //success or cancel 、inconfirm（支付确认中）
	//HtmlShowUid string `query:"showUid"`
	HtmlTitle    string
	HtmlOrderNo  string
	HtmlPrice    string
	HtmlCurrency string
	//Domain      string
}

type OrderQueryRequest struct {
	OrderNo string `json:"orderNo"`
}

type ICurrencyValidateRuler interface {
	Validate(value string) error
	Currency() string
	StripePayAmount(currency string, value string) (result float64, err error)
}
type baseCurrencyRule struct {
	currency             string
	minValue             float64
	maxValue             float64
	convStripeValueTimes float64 //转换成stripe 支付价格的倍数
}

func (r *baseCurrencyRule) Currency() string {
	return r.currency
}

func (r *baseCurrencyRule) Validate(value string) error {
	value = strings.TrimSpace(value)
	amount, err := strconv.ParseFloat(value, 64)
	if err != nil {
		return errors.New("金额不是数字")
	}
	if amount < r.minValue {
		return fmt.Errorf("金额低于最小限额:%f", r.minValue)
	}
	if amount > r.maxValue {
		return fmt.Errorf("金额超过最大限额:%f", r.maxValue)
	}
	return nil
}
func (r *baseCurrencyRule) StripePayAmount(currency string, value string) (result float64, err error) {
	amount, err := strconv.ParseFloat(value, 64)
	if err != nil {
		return 0, errors.New("金额不是数字")
	}
	result = amount * r.convStripeValueTimes
	return
}
func newCurrencyRule(currency string, min, max, convTimes float64) ICurrencyValidateRuler {
	return &baseCurrencyRule{
		currency:             currency,
		minValue:             min,
		maxValue:             max,
		convStripeValueTimes: convTimes,
	}
}

// 校验货币money符不符合要求
func ValidateCurrencyValue(currency, value string) error {
	amountRegex := regexp.MustCompile(`^(0|[1-9]\d{0,8})(\.\d{1,2})?$`) // 匹配整数或最多两位小数的数字
	if !amountRegex.MatchString(value) {
		return errors.New("金额必须大于0且最多支持两位小数")
	}
	currency = strings.ToUpper(currency)
	found := false
	for _, v := range currencyValidateRulerList {
		if v.Currency() != currency {
			continue
		}
		found = true
		if err := v.Validate(value); err != nil {
			return err
		}
	}
	if !found {
		return errors.New("不支持的币种")
	}
	return nil
}

// 将价格转换成stripe支付的格式
func ConvertCurrencyToStripePayAmount(currency string, value string) (result float64, err error) {
	currency = strings.ToUpper(currency)
	found := false
	for _, v := range currencyValidateRulerList {
		if v.Currency() != currency {
			continue
		}
		found = true
		if result, err = v.StripePayAmount(currency, value); err != nil {
			return 0, err
		}
	}
	if !found {
		return 0, errors.New("不支持的币种")
	}
	return
}

var currencyValidateRulerList []ICurrencyValidateRuler

func init() {
	currencyValidateRulerList = append(currencyValidateRulerList,
		newCurrencyRule("USD", 0.55, 999999.99, 100), // 美元
		newCurrencyRule("EUR", 0.50, 999999.99, 100), // 欧元
		newCurrencyRule("JPY", 90, 99999999, 1),      // 日币
		newCurrencyRule("CNY", 3.90, 999999.99, 100), // 人民币
		newCurrencyRule("HKD", 4.20, 999999.99, 100), // 港币
	)
}
