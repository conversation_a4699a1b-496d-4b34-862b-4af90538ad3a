// Package order -----------------------------
// @file      : dto_test.go
// <AUTHOR> JJXu
// @contact   : <EMAIL>
// @time      : 2025/3/26 14:29
// -------------------------------------------
package order

import (
	"testing"
)

func TestValidateCurrencyValue(t *testing.T) {

	type args struct {
		currency string
		value    string
	}
	tests := []struct {
		name    string
		args    args
		wantErr bool
	}{
		// 正常情况测试
		{
			name:    "Valid USD amount",
			args:    args{currency: "USD", value: "100"},
			wantErr: false,
		},
		{
			name:    "Valid EUR amount",
			args:    args{currency: "EUR", value: "1000"},
			wantErr: false,
		},
		{
			name:    "Valid JPY amount",
			args:    args{currency: "JPY", value: "100"},
			wantErr: false,
		},
		{
			name:    "Valid CNY amount",
			args:    args{currency: "CNY", value: "1000"},
			wantErr: false,
		},
		{
			name:    "Valid HKD amount",
			args:    args{currency: "HKD", value: "1000"},
			wantErr: false,
		},
		// 错误情况测试
		{
			name:    "USD below minimum",
			args:    args{currency: "USD", value: "54"},
			wantErr: true,
		},
		{
			name:    "EUR below minimum",
			args:    args{currency: "EUR", value: "49"},
			wantErr: true,
		},
		{
			name:    "Invalid currency",
			args:    args{currency: "XXX", value: "100"},
			wantErr: true,
		},
		{
			name:    "Empty value",
			args:    args{currency: "USD", value: ""},
			wantErr: true,
		},
		{
			name:    "Invalid number format",
			args:    args{currency: "USD", value: "abc"},
			wantErr: true,
		},
		{
			name:    "Exceeds maximum value",
			args:    args{currency: "USD", value: "100000000"},
			wantErr: true,
		},
		{
			name:    "Negative value",
			args:    args{currency: "USD", value: "-100"},
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if err := ValidateCurrencyValue(tt.args.currency, tt.args.value); (err != nil) != tt.wantErr {
				t.Errorf("ValidateCurrencyValue() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}
