// Package payment -----------------------------
// @file      : utils.go
// <AUTHOR> JJXu
// @contact   : <EMAIL>
// @time      : 2025/4/27 20:01
// -------------------------------------------
package payment

import (
	"fmt"
	"math/big"
	"math/rand"
	"sync"
	"time"
)

var hundred = new(big.Rat).SetInt64(100)

// 元(字符串)转分(数字)
func StrYuanToFen(yuan string) (fen int64) {
	// 使用 big.Rat 解析金额字符串
	rat := new(big.Rat)
	rat.SetString(yuan)
	// 将金额乘以 100（转换为分）
	result := new(big.Rat).Mul(rat, hundred)
	// 将结果转换为 int64
	fen = result.Num().Int64()
	return
}

// 分(数字)转元(字符串)
func FenToStrYuan(fen int64) (yuan string) {
	rat := new(big.Rat)
	rat.SetInt64(fen)
	result := new(big.Rat).Quo(rat, hundred)
	return result.FloatString(2)
}

var genLocker = sync.Mutex{}

// 生成退款流水号
func GenRefundNo() string {
	genLocker.Lock()
	defer genLocker.Unlock()
	return fmt.Sprintf("%d%03d", time.Now().UnixNano(), rand.Intn(1000))
}
