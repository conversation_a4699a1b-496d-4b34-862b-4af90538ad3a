// Package payment -----------------------------
// @file      : interface.go
// <AUTHOR> JJXu
// @contact   : <EMAIL>
// @time      : 2025/4/14 19:40
// -------------------------------------------
package payment

import (
	"errors"
	"fmt"
	"net/http"
	"strings"
	"time"
)

type IThirdPartyPayment interface {
	Init(config *AllPaymentConfig) error                                             //初始化配置
	CreateTradeAppPay(info *CommonTradeInfo) (result any, tradeNo string, err error) //app支付
	CreateTradeWebPay(info *CommonTradeInfo) (result any, tradeNo string, err error) //web支付
	ParseNotification(httpRequest *http.Request) (data NotifyData, err error)        //解析通知内容
	QueryTradeInfo(outTradeNo string) (data TradeInfo, err error)                    //查询订单信息
	TradeRefund(info *TradeRefundInfo) (result any, refundNo string, err error)      //退款
}

func NewPayment(platForm PayPlatType, config *AllPaymentConfig) (service IThirdPartyPayment, err error) {
	switch platForm {
	case AliPay:
		service = new(AliPayService)
	case WePay:
		service = new(WePayService)
	case Stripe:
		service = new(StripeService)
	case Antom:
		service = new(AntomService)
	default:
		err = errors.New("不支持的支付平台")
		return
	}
	err = service.Init(config)
	return
}

type CommonTradeInfo struct {
	ReturnUrl    string        //页面返回地址,仅h5有效
	NotifyUrl    string        //通知地址
	Subject      string        //标题
	Desc         string        //内容描述
	OutTradeNo   string        //外部交易流水号
	Amount       string        //价格 单位：元
	Currency     string        //货币
	ExpiredAfter time.Duration //订单超时时间
	CallbackData map[string]any
	Goods        []*GoodsDetail //商品信息
}

func (info *CommonTradeInfo) Validate() error {
	if strings.ToLower(info.Currency) != "cny" {
		return fmt.Errorf("只支持人名币支付！")
	}
	if info.OutTradeNo == "" {
		return errors.New("OutTradeNo 不能为空")
	}
	if strings.Contains(info.NotifyUrl, "?") {
		return errors.New("NotifyUrl 不能包含query参数")
	}
	return nil
}

type GoodsDetail struct {
	GoodsId        string
	GoodsName      string
	Quantity       int
	Price          float64
	GoodsCategory  string
	CategoriesTree string
	Body           string
	ShowURL        string
}
type TradeRefundInfo struct {
	OutRefundNo  string //外部退款单号，选填
	RefundAmount string //退款金额

	OutTradeNo string // 外部订单号 与 TradeNo 二选一
	TradeNo    string //
	PaidAmount string // 支付总额
	Reason     string // 退款原因
}

func (t *TradeRefundInfo) Validate() error {
	if t.OutTradeNo == "" && t.TradeNo == "" {
		return errors.New("订单号不能为空")
	}
	if t.OutRefundNo == "" {
		t.OutRefundNo = GenRefundNo()
	}
	if t.Reason == "" {
		t.Reason = "退款"
	}
	return nil
}
