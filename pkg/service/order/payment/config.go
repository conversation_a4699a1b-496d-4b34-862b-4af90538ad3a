// Package payment -----------------------------
// @file      : config.go
// <AUTHOR> JJXu
// @contact   : <EMAIL>
// @time      : 2025/4/16 19:23
// -------------------------------------------
package payment

type AllPaymentConfig struct {
	AliPay    *AliPayConfig    `toml:"aliPay"`
	StripePay *StripeAppConfig `toml:"stripePay"`
	WePay     *WePayConfig     `toml:"wePay"`
	Antom     *AntomConfig     `toml:"antom"`
}
type AliPayConfig struct {
	IsProd          bool   `toml:"isProd"`          //是否生产环境
	AppId           string `toml:"appId"`           //应用ID
	ApiEncryptType  string `toml:"apiEncryptType"`  //接口加密方式 key=密钥;cert=证书
	AppPrivateKey   string `toml:"appPrivateKey"`   //app私钥路径，必填，在"支付宝开放平台密钥工具"中获取
	AppPublicCert   string `toml:"appPublicCert"`   //app公钥证书路径，证书模式必填，在阿里云控制台开发配置中下载
	AliPayRootCert  string `toml:"aliPayRootCert"`  //支付宝根证书路径，证书模式必填，在阿里云控制台开发配置中下载
	AliPayPublicKey string `toml:"aliPayPublicKey"` //支付宝公钥路径 或 支付宝公钥证书路径，在阿里云控制台开发配置中下载
	BodyEncryptKey  string `toml:"bodyEncryptKey"`  //接口内容加密密钥，在阿里云控制台开发配置中复制
}

type StripeAppConfig struct {
	SignatureKey string `toml:"signatureKey"`
	//CallbackHost       string `toml:"callbackHost"`
	CancelWebEndpoint  string `toml:"cancelWebEndpoint"`
	SuccessWebEndpoint string `toml:"successWebEndpoint"`
}

type WePayConfig struct {
	AppId         string `toml:"appId"`         //应用ID
	MchId         string `toml:"mchId"`         //商户号
	SerialNo      string `toml:"serialNo"`      //商户证书序列号
	ApiClientCert string `toml:"apiClientCert"` //商户证书
	ApiV3Key      string `toml:"apiV3Key"`      //商户APIv3密钥
	PrivateKey    string `toml:"privateKey"`    //私钥
}

type AntomConfig struct {
	ChannelCode string `toml:"channelCode"`
	ChannelType string `toml:"channelType"`
}
