// Package payment -----------------------------
// @file      : dto.go
// <AUTHOR> JJXu
// @contact   : <EMAIL>
// @time      : 2025/4/15 18:36
// -------------------------------------------
package payment

import (
	"github.com/fonchain_enterprise/client-auction/api/payment"
	"github.com/smartwalle/alipay/v3"
	"github.com/wechatpay-apiv3/wechatpay-go/core/notify"
)

type PayPlatType int

const (
	WePay  PayPlatType = iota + 1 //微信支付
	AliPay                        //支付宝
	Stripe                        //stripe
	Antom                         //安通环球
)

func (p PayPlatType) String() string {
	switch p {
	case WePay:
		return "wepay"
	case AliPay:
		return "alipay"
	case Stripe:
		return "stripe"
	case Antom:
		return "antom"
	default:
		return "Unknown"
	}
}

type PayMethod int

const (
	H5 PayMethod = iota + 1
	App
)

type NotifyData struct {
	OutTradeNo  string
	TradeNo     string
	Amount      string
	Currency    string
	TradeStatus int32 //1=未完成 2=交易成功 3=交易失败
	NotifyData  string
	AliPay      *alipay.Notification
	WePay       *notify.Request
	Antom       *payment.AntomNotifyPayResponse
}

type TradeInfo struct {
	TradeNo        string
	OutTradeNo     string
	Amount         string
	Currency       string
	TradeStatus    int32 //1=未完成 2=交易成功 3=交易失败
	ThirdPartyInfo any
}

type AntomNotifyRequest struct {
	NotifyType    string `json:"notifyType"`
	PaymentAmount struct {
		Currency string `json:"currency"`
		Value    string `json:"value"`
	} `json:"paymentAmount"`
	PaymentCreateTime string `json:"paymentCreateTime"`
	PaymentId         string `json:"paymentId"`
	PaymentRequestId  string `json:"paymentRequestId"`
	PaymentTime       string `json:"paymentTime"`
	Result            struct {
		ResultCode    string `json:"resultCode"`
		ResultMessage string `json:"resultMessage"`
		ResultStatus  string `json:"resultStatus"`
	} `json:"result"`
}
