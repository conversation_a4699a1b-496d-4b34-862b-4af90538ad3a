// Package payment -----------------------------
// @file      : aliPay.go
// <AUTHOR> JJXu
// @contact   : <EMAIL>
// @time      : 2025/4/15 10:50
// -------------------------------------------
package payment

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"github.com/smartwalle/alipay/v3"
	"log"
	"net/http"
	"net/url"
	"os"
	"strings"
	"time"
)

type AliPayService struct {
	client *alipay.Client
	conf   *AliPayConfig
}

func (a *AliPayService) Init(config *AllPaymentConfig) (err error) {
	if config == nil || config.AliPay == nil {
		err = errors.New("支付宝付款尚未开发，请切换支付方式")
		return
	}
	a.conf = config.AliPay
	//fmt.Printf("conf:%+v\n", a.conf)
	//p, _ := os.Getwd()
	//fmt.Println("cwd", p)
	//fmt.Println("AppPrivateKey", a.conf.AppPrivateKey)
	//fmt.Println("AppPublicCert", a.conf.AppPublicCert)
	//fmt.Println("AliPayRootCert", a.conf.AliPayRootCert)
	//fmt.Println("AliPayPublicKey", a.conf.AliPayPublicKey)
	//fmt.Println("EncryptKey", a.conf.BodyEncryptKey)
	privateKeyBytes, err := os.ReadFile(a.conf.AppPrivateKey)
	if err != nil {
		err = errors.New("读取私钥失败")
		return
	}
	if a.client, err = alipay.New(a.conf.AppId, string(privateKeyBytes), a.conf.IsProd); err != nil {
		log.Println("初始化支付宝失败", err)
		return err
	}
	switch a.conf.ApiEncryptType {
	case "cert":
		if err = a.client.LoadAppCertPublicKeyFromFile(a.conf.AppPublicCert); err != nil {
			log.Println("加载证书发生错误", err)
			return
		}
		if err = a.client.LoadAliPayRootCertFromFile(a.conf.AliPayRootCert); err != nil {
			log.Println("加载证书发生错误", err)
			return
		}
		if err = a.client.LoadAlipayCertPublicKeyFromFile(a.conf.AliPayPublicKey); err != nil {
			log.Println("加载证书发生错误", err)
			return
		}
	case "key":
		publicKeyBytes, errs := os.ReadFile(a.conf.AliPayPublicKey)
		if errs != nil {
			log.Println("加载公钥发生错误", err)
			err = errors.New("读取公钥失败")
			return
		}
		if err = a.client.LoadAliPayPublicKey(string(publicKeyBytes)); err != nil {
			log.Println("加载公钥发生错误", err)
			return
		}
	default:
		log.Println("接口加密方式需要cert或key，但是得到了:", a.conf.ApiEncryptType)
		err = errors.New("不支持的接口加密方式")
		return
	}
	if a.conf.BodyEncryptKey != "" {
		if err = a.client.SetEncryptKey(a.conf.BodyEncryptKey); err != nil {
			log.Println("加载内容加密密钥发生错误", err)
			return
		}
	}
	return
}

// app支付
func (a *AliPayService) CreateTradeAppPay(info *CommonTradeInfo) (result any, tradeNo string, err error) {
	if strings.ToLower(info.Currency) != "cny" {
		err = fmt.Errorf("只支持人名币支付！")
		return
	}
	if err = info.Validate(); err != nil {
		return
	}
	//if info.OutTradeNo == "" {
	//	info.OutTradeNo = "ali" + strings.Replace(uuid.NewString(), "-", "", 4)[:29]
	//}
	tradeNo = info.OutTradeNo
	params := alipay.TradeAppPay{
		Trade: alipay.Trade{
			NotifyURL:      info.NotifyUrl,
			ReturnURL:      info.ReturnUrl,
			Subject:        info.Subject,
			OutTradeNo:     info.OutTradeNo,
			TotalAmount:    info.Amount,
			ProductCode:    "QUICK_APP_PAY",
			Body:           info.Desc,
			GoodsType:      "1",
			PassbackParams: "",
		},
	}
	if info.CallbackData != nil {
		bytesData, _ := json.Marshal(info.CallbackData)
		params.Trade.PassbackParams = url.QueryEscape(string(bytesData))
	}
	if info.ExpiredAfter > 0 {
		params.Trade.TimeExpire = time.Now().Add(info.ExpiredAfter + 3*time.Second).Format("2006-01-02 15:04:05")
	} else {
		params.Trade.TimeExpire = time.Now().Add(30*time.Minute + 3*time.Second).Format("2006-01-02 15:04:05")
	}
	if len(info.Goods) > 0 {
		for _, v := range info.Goods {
			params.Trade.GoodsDetail = append(params.Trade.GoodsDetail, &alipay.GoodsDetail{
				GoodsId:        v.GoodsId,
				GoodsName:      v.GoodsName,
				Quantity:       v.Quantity,
				Price:          v.Price,
				GoodsCategory:  v.GoodsCategory,
				CategoriesTree: v.CategoriesTree,
				Body:           v.Body,
				ShowURL:        v.ShowURL,
			})
		}
	}
	result, err = a.client.TradeAppPay(params)
	if err != nil {
		return
	}
	//todo 支付宝不返回内部订单号，可以通过查询接口获取
	return
}

// H5支付
func (a *AliPayService) CreateTradeWebPay(info *CommonTradeInfo) (result any, tradeNo string, err error) {
	err = errors.New("stripe付款尚未开放，请切换支付方式")
	return
}
func (a *AliPayService) ParseNotification(httpRequest *http.Request) (data NotifyData, err error) {
	err = httpRequest.ParseForm()
	if err != nil {
		log.Println("解析form失败", err)
		return
	}
	data.AliPay, err = a.client.DecodeNotification(httpRequest.Form)
	if err != nil {
		return
	}
	data.TradeNo = data.AliPay.TradeNo
	data.OutTradeNo = data.AliPay.OutTradeNo
	data.Amount = data.AliPay.TotalAmount
	data.TradeStatus = a.translateTradeStatus(data.AliPay.TradeStatus)
	notifyData, _ := json.Marshal(data.AliPay)
	data.NotifyData = string(notifyData)
	return
}

// 查询订单信息
func (a *AliPayService) QueryTradeInfo(outTradeNo string) (data TradeInfo, err error) {
	var result *alipay.TradeQueryRsp
	result, err = a.client.TradeQuery(context.Background(), alipay.TradeQuery{
		OutTradeNo: outTradeNo,
	})
	if err != nil {
		return
	}
	data = TradeInfo{
		TradeNo:        result.TradeNo,
		OutTradeNo:     result.OutTradeNo,
		Amount:         result.TotalAmount,
		Currency:       result.PayCurrency,
		TradeStatus:    a.translateTradeStatus(result.TradeStatus),
		ThirdPartyInfo: result,
	}

	return
}
func (a *AliPayService) translateTradeStatus(thirdPartyStatus alipay.TradeStatus) (tradeStatus int32) {
	switch thirdPartyStatus {
	case alipay.TradeStatusWaitBuyerPay:
	case alipay.TradeStatusSuccess:
		tradeStatus = 2
	case alipay.TradeStatusClosed:
	case alipay.TradeStatusFinished:
	}
	return
}
func (a *AliPayService) TradeRefund(info *TradeRefundInfo) (result any, refundNo string, err error) {
	if err = info.Validate(); err != nil {
		return
	}
	req := alipay.TradeRefund{
		OutTradeNo:   info.OutTradeNo,
		TradeNo:      info.TradeNo,
		RefundAmount: info.RefundAmount,
		OutRequestNo: info.OutRefundNo,
	}
	reqResp, err := a.client.TradeRefund(context.Background(), req)
	refundNo = req.OutRequestNo
	result = reqResp
	return
}
