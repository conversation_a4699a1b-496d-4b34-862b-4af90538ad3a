// Package payment -----------------------------
// @file      : anttom.go
// <AUTHOR> JJXu
// @contact   : <EMAIL>
// @time      : 2025/4/21 13:03
// -------------------------------------------
package payment

import (
	"context"
	"dubbo.apache.org/dubbo-go/v3/common/logger"
	"encoding/json"
	"errors"
	"fmt"
	"github.com/fonchain_enterprise/client-auction/api/payment"
	"github.com/fonchain_enterprise/client-auction/pkg/service"
	"io"
	"net/http"
)

type AntomService struct {
	channelCode string
	channelType string
}

func (a *AntomService) Init(config *AllPaymentConfig) (err error) {
	if service.PaymentProvider == nil {
		err = errors.New("安通支付服务未完成初始化，请更换支付方式")
		return
	}
	if config.Antom == nil {
		err = errors.New("安通支付功能未完成初始化，请更换支付方式")
		return
	}
	a.channelCode = config.Antom.ChannelCode
	a.channelType = config.Antom.ChannelType
	fmt.Println("channelCode", a.channelCode)
	fmt.Println("channelType", a.channelType)
	return
}

func (a *AntomService) CreateTradeAppPay(info *CommonTradeInfo) (result any, tradeNo string, err error) {
	err = errors.New("暂不支持app支付,请更换支付方式")
	return
}
func (a *AntomService) CreateTradeWebPay(info *CommonTradeInfo) (result any, tradeNo string, err error) {
	if err = info.Validate(); err != nil {
		return
	}
	if info.CallbackData != nil {
		logger.Warn("antom不支持回调参数")
	}
	amountInt64 := StrYuanToFen(info.Amount)
	//if amountInt64 <= 100 {
	//	err = errors.New("金额必须大于1")
	//	return
	//}
	fmt.Println("amountInt64", amountInt64)
	var antomReq payment.CreatePayRequest
	antomReq.Platform = "antom"             // 固定不变
	antomReq.ChannelType = a.channelType    // 固定不变
	antomReq.ProductDescription = info.Desc // 产品描述
	antomReq.BusinessType = "useless"       // 固定不变
	antomReq.Domain = "fenghe"              // 区分订单来源，必输
	antomReq.Amount = amountInt64           // 金额，精确到分，1元在这里要送100
	antomReq.Currency = info.Currency       // 币种，cny,usd这种
	antomReq.OutTradeNo = info.OutTradeNo   // 外部流水号，能定位到你业务唯一id
	antomReq.ReturnUrl = info.ReturnUrl     // 成功跳转地址
	antomReq.Payee = a.channelCode
	if len(info.Goods) > 0 {
		goodInfo := "\n"
		for i, v := range info.Goods {
			if i > 0 {
				goodInfo += ","
			}
			goodInfo = v.GoodsName
		}
		antomReq.ProductDescription += goodInfo
	}
	var resp *payment.CreatePayResponse
	resp, err = service.PaymentProvider.CreatePay(context.Background(), &antomReq)
	if err != nil {
		return
	}
	return resp.Url, resp.CheckoutSessionId, nil
}

func (a *AntomService) ParseNotification(httpRequest *http.Request) (result NotifyData, err error) {
	var data AntomNotifyRequest
	//解析httpRequest中的json数据
	var decoder = json.NewDecoder(httpRequest.Body)
	err = decoder.Decode(&data)
	if err != nil {
		if err == io.EOF {
			return result, errors.New("empty request body")
		}
		return result, err
	}
	params := &payment.AntomNotifyPayRequest{
		NotifyType:    data.NotifyType,
		RequestId:     data.PaymentRequestId,
		PaymentId:     data.PaymentId,
		PaymentTime:   data.PaymentTime,
		ResultStatus:  data.Result.ResultStatus,
		ResultMessage: data.Result.ResultMessage,
		ChannelCode:   a.channelCode, // 对应你创建的时候送的Payee
	}
	resp, err := service.PaymentProvider.AntomWebhook(context.Background(), params)
	if err != nil {
		return
	}
	notifyData, _ := json.Marshal(data)
	result = NotifyData{
		OutTradeNo:  resp.OutTradeNo,
		TradeNo:     data.PaymentId,
		Amount:      data.PaymentAmount.Value,
		Currency:    data.PaymentAmount.Currency,
		TradeStatus: a.translateTradeStatus(data.Result.ResultCode),
		NotifyData:  string(notifyData),
		Antom:       resp,
	}
	return
}

func (a *AntomService) QueryTradeInfo(outTradeNo string) (data TradeInfo, err error) {
	var tradeData *payment.PayQueryResponse
	tradeData, err = service.PaymentProvider.QueryPayByOutTradeNo(context.Background(), &payment.PayQueryRequest{
		PayType:    "antom",
		OutTradeNo: outTradeNo,
	})
	if err != nil {
		return
	}

	data = TradeInfo{
		TradeNo:    tradeData.Infos[0].ChannelTradeNo,
		OutTradeNo: tradeData.Infos[0].OutTradeNo,
		Amount:     FenToStrYuan(tradeData.Infos[0].Amount),
		Currency:   tradeData.Infos[0].Currency,
	}
	switch tradeData.Infos[0].Status {
	case "paid":
		data.TradeStatus = 2
	case "pending":
		data.TradeStatus = 1
	case "failed":
		data.TradeStatus = 3
	}
	return
}

// https://docs.antom.com/ac/ams_zh-cn/paymentrn_online
func (a *AntomService) translateTradeStatus(thirdPartyStatus string) (tradeStatus int32) {
	switch thirdPartyStatus {
	case "SUCCESS":
		tradeStatus = 2
	default:
		tradeStatus = 3
	}
	return
}
func (a *AntomService) TradeRefund(info *TradeRefundInfo) (result any, refundNo string, err error) {
	err = errors.New("未实现")
	return
}
