// Package payment -----------------------------
// @file      : wePay.go
// <AUTHOR> JJXu
// @contact   : <EMAIL>
// @time      : 2025/4/15 15:28
// -------------------------------------------
package payment

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"github.com/wechatpay-apiv3/wechatpay-go/core/auth/verifiers"
	"github.com/wechatpay-apiv3/wechatpay-go/core/downloader"
	"github.com/wechatpay-apiv3/wechatpay-go/core/notify"
	"github.com/wechatpay-apiv3/wechatpay-go/services/payments"
	"github.com/wechatpay-apiv3/wechatpay-go/services/payments/app"
	"github.com/wechatpay-apiv3/wechatpay-go/services/refunddomestic"
	"log"
	"net/http"
	"time"

	"github.com/wechatpay-apiv3/wechatpay-go/core"
	"github.com/wechatpay-apiv3/wechatpay-go/core/option"
	"github.com/wechatpay-apiv3/wechatpay-go/utils"
)

type WePayService struct {
	client *core.Client
	conf   *WePayConfig
}

func (a *WePayService) Init(config *AllPaymentConfig) (err error) {
	if config == nil || config.AliPay == nil {
		err = errors.New("微信付款尚未开放，请切换支付方式")
		return
	}
	a.conf = config.WePay
	mchPrivateKey, err := utils.LoadPrivateKeyWithPath(a.conf.PrivateKey)
	if err != nil {
		log.Fatal("load merchant private key error")
		return
	}
	ctx := context.Background()

	// 使用商户私钥等初始化 client，并使它具有自动定时获取微信支付平台证书的能力
	opts := []core.ClientOption{
		option.WithWechatPayAutoAuthCipher(a.conf.MchId, a.conf.SerialNo, mchPrivateKey, a.conf.ApiV3Key),
	}
	a.client, err = core.NewClient(ctx, opts...)
	if err != nil {
		log.Fatalf("new wechat pay client err:%s", err)
	}
	return
}

// CreateTradeAppPay app支付
// 注意：微信app支付不支持真机调试，请打包后在测试
func (a *WePayService) CreateTradeAppPay(info *CommonTradeInfo) (result any, tradeNo string, err error) {
	// 创建 APP 支付订单
	ctx := context.Background()
	svc := app.AppApiService{Client: a.client}
	if err = info.Validate(); err != nil {
		return
	}
	// 发起请求
	var timeExpireAt time.Time
	if info.ExpiredAfter > 0 {
		timeExpireAt = time.Now().Add(info.ExpiredAfter + 3*time.Second)
	} else {
		timeExpireAt = time.Now().Add(30*time.Minute + 3*time.Second)
	}
	amountInt64 := StrYuanToFen(info.Amount)
	var callbackData []byte
	if info.CallbackData != nil {
		callbackData, _ = json.Marshal(info.CallbackData)
	}

	svcRequest := app.PrepayRequest{
		Appid:       core.String(a.conf.AppId),
		Mchid:       core.String(a.conf.MchId),
		Description: core.String(info.Desc),
		OutTradeNo:  core.String(info.OutTradeNo),
		TimeExpire:  &timeExpireAt,
		Attach:      core.String(string(callbackData)), //附加数据说明 core.String("自定义数据说明")
		NotifyUrl:   core.String(info.NotifyUrl),
		//LimitPay:      nil,
		SupportFapiao: nil,
		Amount: &app.Amount{
			Total:    core.Int64(amountInt64),
			Currency: core.String(info.Currency),
		},
		//Detail:     nil,
		//SceneInfo:  nil,
		//SettleInfo: nil,
	}
	if len(info.Goods) > 0 {
		log.Println("微信支付不支持 CommonTradeInfo.Goods参数")
	}
	response, respResult, err := svc.PrepayWithRequestPayment(ctx, svcRequest)
	if err != nil {
		log.Printf("create wechat app pay order error: %s", err)
		return
	}
	fmt.Printf("response: %+v\n", response)
	fmt.Println()
	fmt.Printf("respResult:%+v\n", respResult)
	//// 将响应结果序列化为 JSON 字符串
	//var resultBytes []byte
	//resultBytes, err = json.Marshal(response)
	//if err != nil {
	//	log.Printf("marshal response error: %s", err)
	//	return "", err
	//}

	return response, *response.PrepayId, nil
}
func (a *WePayService) CreateTradeWebPay(info *CommonTradeInfo) (result any, tradeNo string, err error) {
	err = errors.New("该付款方式尚不可用，请切换支付方式")
	return
}
func (a *WePayService) ParseNotification(httpRequest *http.Request) (data NotifyData, err error) {
	certificateVisitor := downloader.MgrInstance().GetCertificateVisitor(a.conf.MchId)
	//handler := notify.NewNotifyHandler(a.conf.ApiV3Key, verifiers.NewSHA256WithRSAVerifier(certificateVisitor))
	handler, err := notify.NewRSANotifyHandler(a.conf.ApiV3Key, verifiers.NewSHA256WithRSAVerifier(certificateVisitor))
	if err != nil {
		fmt.Println(err)
		return
	}
	transaction := new(payments.Transaction)
	data.WePay, err = handler.ParseNotifyRequest(context.Background(), httpRequest, transaction)
	// 如果验签未通过，或者解密失败
	if err != nil {
		fmt.Println(err)
		return
	}
	// 处理通知内容
	fmt.Println("Summary:", data.WePay.Summary)
	fmt.Println("TransactionId", transaction.TransactionId)
	data.OutTradeNo = *transaction.OutTradeNo
	data.TradeNo = *transaction.TransactionId
	amountF64 := float64(*transaction.Amount.PayerTotal) / 100
	data.Amount = fmt.Sprintf("%.2f", amountF64)
	data.Currency = *transaction.Amount.Currency
	notifyBytes, _ := json.Marshal(transaction)
	data.NotifyData = string(notifyBytes)
	fmt.Println("Amount", data.Amount)
	fmt.Println("TradeState", *transaction.TradeState)
	fmt.Println("OutTradeNo", *transaction.OutTradeNo)

	data.TradeStatus = a.translateTradeStatus(*transaction.TradeState)
	return
}

// 查询订单信息
func (a *WePayService) QueryTradeInfo(outTradeNo string) (data TradeInfo, err error) {
	ctx := context.Background()
	svc := app.AppApiService{Client: a.client}
	var (
		resp *payments.Transaction
		//result *core.APIResult
	)
	resp, _, err = svc.QueryOrderByOutTradeNo(ctx, app.QueryOrderByOutTradeNoRequest{
		OutTradeNo: core.String(outTradeNo),
	})
	if err != nil {
		return
	}
	data = TradeInfo{
		TradeNo:        *resp.TransactionId,
		OutTradeNo:     *resp.OutTradeNo,
		Amount:         FenToStrYuan(*resp.Amount.PayerTotal),
		Currency:       *resp.Amount.Currency,
		TradeStatus:    a.translateTradeStatus(*resp.TradeState),
		ThirdPartyInfo: resp,
	}
	return
}
func (a *WePayService) translateTradeStatus(thirdPartyStatus string) (tradeStatus int32) {
	switch thirdPartyStatus {
	case "NOTPAY", "USERPAYING":
	case "SUCCESS":
		tradeStatus = 2
	case "REVOKED", "PAYERROR":
		tradeStatus = 3
	}
	return
}

func (a *WePayService) TradeRefund(info *TradeRefundInfo) (result any, refundNo string, err error) {
	if err = info.Validate(); err != nil {
		return
	}
	refundAmount := StrYuanToFen(info.RefundAmount)
	paidAmount := StrYuanToFen(info.PaidAmount)
	svc := refunddomestic.RefundsApiService{Client: a.client}
	req := refunddomestic.CreateRequest{
		//SubMchid:      core.String(a.conf.MchId),
		OutRefundNo:  core.String(info.OutRefundNo),
		Reason:       core.String(info.Reason),
		FundsAccount: refunddomestic.REQFUNDSACCOUNT_AVAILABLE.Ptr(),
		Amount: &refunddomestic.AmountReq{
			Currency: core.String("CNY"),
			Refund:   core.Int64(refundAmount),
			Total:    core.Int64(paidAmount),
		},
	}
	if info.OutTradeNo != "" {
		req.OutTradeNo = core.String(info.OutTradeNo)
	} else {
		req.TransactionId = core.String(info.TradeNo)
	}
	resp, result, err := svc.Create(context.Background(), req)
	if err != nil {
		return
	}
	return resp, *resp.TransactionId, err
}
