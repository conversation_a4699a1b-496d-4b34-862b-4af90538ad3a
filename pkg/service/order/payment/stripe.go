// Package payment -----------------------------
// @file      : stripe.go
// <AUTHOR> JJXu
// @contact   : <EMAIL>
// @time      : 2025/4/15 15:05
// -------------------------------------------
package payment

import (
	"errors"
	"github.com/smartwalle/alipay/v3"
	"net/http"
)

type StripeService struct {
	client *alipay.Client
	conf   *StripeAppConfig
}

func (a *StripeService) Init(config *AllPaymentConfig) (err error) {
	if config == nil || config.AliPay == nil {
		err = errors.New("stripe付款尚未开放，请切换支付方式")
		return
	}
	a.conf = config.StripePay
	return
}
func (a *StripeService) CreateTradeAppPay(info *CommonTradeInfo) (result any, tradeNo string, err error) {
	err = errors.New("stripe付款尚未开放，请切换支付方式")
	return
}

func (a *StripeService) CreateTradeWebPay(info *CommonTradeInfo) (result any, tradeNo string, err error) {
	err = errors.New("stripe付款尚未开放，请切换支付方式")
	return
}

func (a *StripeService) ParseNotification(httpRequest *http.Request) (data NotifyData, err error) {
	err = errors.New("stripe付款尚未开放，请切换支付方式")
	return
}

// 查询订单信息
func (a *StripeService) QueryTradeInfo(outTradeNo string) (data TradeInfo, err error) {

	return
}

func (a *StripeService) TradeRefund(info *TradeRefundInfo) (result any, refundNo string, err error) {
	err = errors.New("未实现")
	return
}
