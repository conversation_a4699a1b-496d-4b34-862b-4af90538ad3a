// Package order -----------------------------
// @file      : utils.go
// <AUTHOR> JJXu
// @contact   : <EMAIL>
// @time      : 2025/1/26 11:42
// -------------------------------------------
package order

import (
	"encoding/json"
	"errors"
)

// strpie错误列表https://docs.stripe.com/error-codes#account-country-invalid-address
func ParseStripeErr(err error) error {
	content := err.Error()
	errResponse := StripeErrResponse{}
	json.Unmarshal([]byte(content), &errResponse)
	switch errResponse.Code {
	case "amount_too_small":
		return errors.New("金额太小无法创建订单，请联系管理员")
	case "account-closed":
		return errors.New("账户已关闭，请联系管理员")
	case "account_country_invalid_address":
		return errors.New("地址不在账户所在国家，请联系管理员")
	default:
		if errResponse.Code == "" {
			return err
		} else {
			return errors.New(errResponse.Code)
		}
	}
}
func ParseStripeErrToI18nMgsId(err error) string {
	content := err.Error()
	errResponse := StripeErrResponse{}
	json.Unmarshal([]byte(content), &errResponse)
	switch errResponse.Code {
	case "amount_too_small":
		return "ORDER_AMOUNT_TOO_SMALL"
	case "account-closed":
		return "ORDER_ACCOUNT_CLOSED"
	case "account_country_invalid_address":
		return "ORDER_ACCOUNT_COUNTRY_INVALID_ADDRESS"
	default:
		return "ORDER_CREATE_FAILED"
	}
}

type StripeErrResponse struct {
	Code          string `json:"code"`
	DocUrl        string `json:"doc_url"`
	Status        int    `json:"status"`
	Message       string `json:"message"`
	RequestId     string `json:"request_id"`
	RequestLogUrl string `json:"request_log_url"`
	Type          string `json:"type"`
}
