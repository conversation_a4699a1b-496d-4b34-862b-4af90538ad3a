package service

import (
	"fmt"
	"github.com/fonchain_enterprise/client-auction/pkg/logic"
	"github.com/fonchain_enterprise/client-auction/pkg/logic/lang"
	"net/http"
	"reflect"

	"github.com/fonchain_enterprise/client-auction/pkg/e"
	"github.com/fonchain_enterprise/client-auction/pkg/serializer"
	"github.com/gin-gonic/gin"
)

// Success Success(c,someMap)
func Success(c *gin.Context, datas ...interface{}) {
	var data any
	if datas != nil {
		data = datas[0]
	}
	dataFieldStr, _ := c.Get("dataField")
	key := []string{"*"}

	if dataFieldStr != "" && dataFieldStr != nil {
		dataField := dataFieldStr.([]string)
		key = dataField

		if len(dataField) == 1 && dataField[0] == "*" { //不做处理

		} else if len(dataField) >= 1 {
			isList := isList(reflect.TypeOf(data).Elem())

			// 是列表返回
			if isList == false {
				ChangeFromReflect(data, dataField)
			} else {
				ChangeRawFromReflect(data, dataField)
			}
		}
	}

	c.JSON(http.StatusOK, serializer.Response{
		Status: e.Ok,
		Code:   e.SUCCESS,
		Data:   data,
		//	Keys:   dataField,
		Keys: key,
	})
	c.Abort()
}

// SuccessByDataAuth SuccessByDataAuth(c,someMap)
func SuccessByDataAuth(c *gin.Context, data interface{}) {

	dataField, _ := c.Get("dataField")

	c.JSON(http.StatusOK, serializer.Response{
		Status: e.Ok,
		Code:   e.SUCCESS,
		Data:   data,
		Keys:   dataField.([]string),
	})

	c.Abort()
}

// Error 统一错误返回
func Error(c *gin.Context, code int, err error, msg ...string) {

	fmt.Println("err:", err)
	status := e.Failed
	if code == e.NotLogin {
		status = e.NotLogin
	}

	if code == e.NotLoginSqueeze {
		status = e.NotLoginSqueeze
	}

	if code == e.CaptchaWrong {
		status = e.CaptchaWrong
	}

	if code == e.MsgSended {
		status = e.MsgSended
	}
	if code == e.Error14SeriesNotShelf {
		status = code
	}

	var msgStr string
	if msg != nil {
		msgStr = msg[0]
	} else if err != nil {
		msgStr = err.Error()
	}

	msgStr = lang.GetConvertString(msgStr, logic.GetLanguage(c))

	c.JSON(e.Success, serializer.Response{
		Status: status,
		Msg:    msgStr,
		Data:   nil,
	})

	c.Abort()
}

// NotLoginRes 未登录的返回
func NotLoginRes(c *gin.Context, msg string) {

	c.JSON(e.Success, serializer.Response{
		Code:   401,
		Status: 1,
		Msg:    msg,
		Data:   nil,
	})

	c.Abort()
}

func ResponseMsg(c *gin.Context, httpCode int, Resp serializer.Response) {
	if httpCode == 0 {
		httpCode = http.StatusOK
	}

	// 国际化
	if e.VerifyMsgKey(Resp.Msg) {
		Resp.Msg = logic.ConvertOaMsg(c, Resp.Msg)
	}

	c.JSON(httpCode, serializer.Response{
		Status: Resp.Status,
		Code:   Resp.Code,
		Msg:    Resp.Msg,
		Error:  Resp.Error,
		Data:   Resp.Data,
	})
}

func ResponseQuickMsg(c *gin.Context, Status uint8, Msg string, Data interface{}) {
	//var c gin.Context
	c.JSON(http.StatusOK, serializer.Response{
		Status: int(Status),
		Msg:    Msg,
		Data:   Data,
	})
}

func isList(m reflect.Type) bool {
	dataExist := false

	for i := 0; i < m.NumField(); i++ {

		fieldType := m.Field(i)

		if fieldType.Name == "Data" {
			getValue := reflect.ValueOf(fieldType)

			if getValue.Kind() != reflect.Slice {
				dataExist = true
			}

			break
		}
	}

	return dataExist
}

func IsContainString(items []string, item string) bool {
	for _, eachItem := range items {
		if eachItem == item {
			return true
		}
	}
	return false
}

func ChangeFromReflect(data interface{}, dataField []string) {
	m := reflect.TypeOf(data).Elem()
	for i := 0; i < m.NumField(); i++ {
		fieldType := m.Field(i)
		if IsContainString(dataField, fieldType.Name) == false {
			field := reflect.ValueOf(data).Elem().FieldByName(fieldType.Name)

			switch m.Field(i).Type.Name() {
			case "int":
				if field.CanSet() {
					field.SetInt(0)
				}
			case "uint":
				if field.CanSet() {
					field.SetUint(0)
				}
			case "uint64":
				if field.CanSet() {
					field.SetUint(0)
				}
			case "string":
				if field.CanSet() {
					field.SetString("")
				}
			default:
				if reflect.ValueOf(data).Elem().FieldByName(fieldType.Name).CanAddr() {
					if reflect.ValueOf(data).Elem().FieldByName(fieldType.Name).Kind() == reflect.Slice {
						field := reflect.ValueOf(data).Elem().FieldByName(fieldType.Name)

						if field.CanSet() {
							field.Set(reflect.MakeSlice(field.Type(), 0, 0))
						}
					}

					if reflect.ValueOf(data).Elem().FieldByName(fieldType.Name).Kind() == reflect.Struct {
					}

					if reflect.ValueOf(data).Elem().FieldByName(fieldType.Name).Kind() == reflect.Ptr {
						field := reflect.ValueOf(data).Elem().FieldByName(fieldType.Name)
						if !reflect.ValueOf(data).Elem().FieldByName(fieldType.Name).IsNil() {
							fieldType := reflect.ValueOf(data).Elem().FieldByName(fieldType.Name).Elem().Type()

							if field.CanSet() {
								field.Set(reflect.New(fieldType))
							}
						}

					}
				}

			}
		}
	}

}

func ChangeRawFromReflect(data interface{}, dataField []string) {

	sliceContent := reflect.ValueOf(data).Elem().FieldByName("Data")
	for i := 0; i < sliceContent.Len(); i++ {
		sliceContent.Index(i)
		ChangeFromReflect(sliceContent.Index(i).Interface(), dataField)
	}

}
