package auctionSession

import (
	"github.com/gin-gonic/gin"
)

// ========================================================================================
//
//	auction
func InitRouter(g *gin.RouterGroup) {
	var auctionSessionNoAuthRouter = g.Group("sessionUserNo")
	{
		//------------------------------AuctionSessionUserNo
		auctionSessionNoAuthRouter.POST("create", Handler.CreateAuctionSessionUserNo)
		//auctionSessionNoAuthRouter.POST("delete", Handler.DeleteAuctionSessionUserNo)
		auctionSessionNoAuthRouter.POST("update", Handler.UpdateAuctionSessionUserNo)
		//auctionSessionNoAuthRouter.POST("detail", Handler.GetAuctionSessionUserNoDetail)
		//auctionSessionNoAuthRouter.POST("query", Handler.GetAuctionSessionUserNoList)
		auctionSessionNoAuthRouter.POST("check", Handler.CheckAuctionSessionUserNo)
		auctionSessionNoAuthRouter.GET("userInfo", Handler.UserInfo) //获取用户号牌和实名信息

	}

	{
		var admAuthRouter = g.Group("/adm/sessionUserNo")
		admAuthRouter.POST("view", Handler.GetViewAuctionSessionUserNoList)
		admAuthRouter.POST("qrCode/get", Handler.GetQrCode)
		admAuthRouter.POST("checkNo", Handler.CheckIsExistedBySessionNo)
		admAuthRouter.POST("update", Handler.AdmUpdateAuctionSessionUserNo)
		admAuthRouter.POST("download/template/importAuctionUserNo", Handler.DownloadUploadUserTemplate) //下载领号牌模板
		admAuthRouter.POST("import", Handler.UploadAuctionUserData)                                     //导入领号牌数据
		admAuthRouter.POST("export", Handler.ExportAuctionUserNo)                                       //导出领号牌数据
	}
}
