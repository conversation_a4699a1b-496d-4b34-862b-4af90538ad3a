package auctionSession

import (
	"fmt"
	"github.com/fonchain_enterprise/client-auction/api/fenghe"
	"strings"
)

type GetAuctionSessionUserNoListRequest struct {
	Page     int64 `json:"page"`
	PageSize int64 `json:"pageSize"`
	fenghe.AuctionSessionUserNoData
}

type GetViewAuctionSessionUserNoListRequest struct {
	Page      int64  `json:"page"`
	PageSize  int64  `json:"pageSize"`
	SessionNo string `json:"sessionNo"`
	//fenghe.ViewAuctionSessionUserNo
}

type GetQrCodeRequest struct {
	AuctionUid string `json:"auctionUid"`
}
type CheckAuctionSessionUserNoRequest struct {
	Phone     string `json:"phone"`
	SessionNo string `json:"sessionNo"`
	SeriesUid string `json:"seriesUid"`
}
type UserSeriesCheckResp struct {
	*fenghe.ViewAuctionSessionUserNo
	SignStatus int `json:"signStatus"` //1=已签署 2=未签署
}
type CheckIsExistedBySessionNoRequest struct {
	AuctionUserNo string `json:"auctionUserNo"`
	SessionNo     string `json:"sessionNo"`
}
type AdmUpdateAuctionSessionUserNoRequest struct {
	SessionNo     string `json:"sessionNo"`
	AuctionUserNo string `json:"auctionUserNo"`
	Phone         string `json:"phone"`
}
type AuctionUserTemplateData struct {
	CountryCode string   `json:"_"`
	RelName     string   `json:"relName"`
	Phone       string   `json:"ph"`
	CardId      string   `json:"cardId"`
	Errors      []string `json:"-"`
	RowNo       int      `json:"-"`
	Err         string   `json:"err"`
}

func (a *AuctionUserTemplateData) AddErr(errors ...string) {
	a.Errors = append(a.Errors, errors...)
}
func (a *AuctionUserTemplateData) HasErr() bool {
	return len(a.Errors) > 0
}
func (a *AuctionUserTemplateData) GetErr() string {
	builder := strings.Builder{}
	for i, v := range a.Errors {
		builder.WriteString(fmt.Sprintf("%d. %s", i+1, v))
		if i != len(a.Errors)-1 {
			builder.WriteString("\n")
		}
	}
	return builder.String()
}

type UploadAuctionUserDataReq struct {
	SessionNo string `json:"sessionNo" form:"sessionNo"`
}

type UploadAuctionUserDataResp struct {
	SuccessCount  int    `json:"successCount"`
	FailedCount   int    `json:"failedCount"`
	FailedFileUrl string `json:"failedFileUrl"`
}

type ExportAuctionUserFailedData struct {
	Name   string `json:"name"`
	Phone  string `json:"phone"`
	CardId string `json:"cardId"`
	Error  string `json:"error"`
}

type ExportAuctionUserNoData struct {
	AuctionUsrNo string `json:"auctionUsrNo"` //号牌
	Phone        string `json:"phone"`        //手机号
	Name         string `json:"name"`         //姓名
	Gender       string `json:"gender"`       //性别
	BirthDay     string `json:"birthDay"`     //生日
	Address      string `json:"address"`      //家庭住址
	//BankName     string `json:"bankName"`     //所属银行
	//BankNo       string `json:"bankNo"`       //银行卡号码
	CardType  string `json:"cardType"`  //证件类型
	CardId    string `json:"cardId"`    //证件号
	CreatedAt string `json:"createdAt"` //创建时间
}
type ImportAuctionUserTemplateFailedData struct {
	RelName string `json:"relName"`
	Phone   string `json:"phone"`
	CardId  string `json:"cardId"`
	Err     string `json:"err"`
}
type UserInfoReq struct {
	UserId int64 `json:"user_id"`
}
