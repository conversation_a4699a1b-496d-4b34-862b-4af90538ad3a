package auctionSession

import (
	"fmt"
	"github.com/dorlolo/exportToExcel"
	"github.com/fonchain_enterprise/client-auction/api/fenghe"
	"github.com/fonchain_enterprise/client-auction/cmd/config"
	"github.com/fonchain_enterprise/client-auction/pkg/common/download"
	"github.com/fonchain_enterprise/client-auction/pkg/common/utils"
	"github.com/fonchain_enterprise/client-auction/pkg/common/utils/idcard"
	"github.com/fonchain_enterprise/client-auction/pkg/e"
	"github.com/fonchain_enterprise/client-auction/pkg/service"
	"github.com/gin-gonic/gin"
	"github.com/xuri/excelize/v2"
	"net/url"
	"strconv"
	"time"
)

var Handler = &AuctionHandler{
	offlineQrcode: make(map[string]string),
}

type AuctionHandler struct {
	offlineQrcode map[string]string
}

// CreateAuctionSessionUserNo 创建AuctionSessionUserNo
func (a *AuctionHandler) CreateAuctionSessionUserNo(c *gin.Context) {
	var req fenghe.AuctionSessionUserNoData
	if err := c.ShouldBindJSON(&req); err != nil {
		service.Error(c, e.InvalidParams, err, "无效参数")
		return
	}
	if req.Phone == "" {
		service.Error(c, e.InvalidParams, nil, "手机号不能为空")
		return
	}
	if req.SessionNo == "" {
		service.Error(c, e.InvalidParams, nil, "场次号不能为空")
		return
	}
	fmt.Println("生成号牌:", req.Phone)
	resp, err := service.FengheProvider.CreateAuctionSessionUserNo(c, &req)
	fmt.Println("生成号牌结果:", err)
	if err != nil {
		service.Error(c, e.Failed, err, "创建失败")
		return
	}
	fmt.Printf("生成号牌成功:%+v\n", resp)
	data := map[string]any{
		"sessionNo":     resp.Data.SessionNo,
		"auctionUserNo": fmt.Sprintf("%03d", resp.Data.AuctionUserNo),
		"phone":         resp.Data.Phone,
	}
	fmt.Printf("生成号牌返回值:%+v\n", data)
	service.Success(c, data)
}

// DeleteAuctionSessionUserNo 删除AuctionSessionUserNo
func (a *AuctionHandler) DeleteAuctionSessionUserNo(c *gin.Context) {
	var req fenghe.DeleteAuctionSessionUserNoRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		service.Error(c, e.InvalidParams, err, "无效参数")
		return
	}
	_, err := service.FengheProvider.DeleteAuctionSessionUserNo(c, &req)
	if err != nil {
		service.Error(c, e.Failed, err, "删除失败")
		return
	}
	service.Success(c)
}

// UpdateAuctionSessionUserNo 更新AuctionSessionUserNo
func (a *AuctionHandler) UpdateAuctionSessionUserNo(c *gin.Context) {
	var req fenghe.AuctionSessionUserNoData
	if err := c.ShouldBindJSON(&req); err != nil {
		service.Error(c, e.InvalidParams, err, "无效参数")
		return
	}
	_, err := service.FengheProvider.UpdateAuctionSessionUserNo(c, &req)
	if err != nil {
		service.Error(c, e.Failed, err, "更新失败")
		return
	}
	service.Success(c)
}

// GetAuctionSessionUserNoDetail 使用id查询AuctionSessionUserNo
func (a *AuctionHandler) GetAuctionSessionUserNoDetail(c *gin.Context) {
	var req fenghe.GetAuctionSessionUserNoRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		service.Error(c, e.InvalidParams, err, "无效参数")
		return
	}
	resp, err := service.FengheProvider.GetAuctionSessionUserNoDetail(c, &req)
	if err != nil {
		service.Error(c, e.Failed, err, "查询失败")
		return
	}
	service.Success(c, resp)
}
func (a *AuctionHandler) CheckAuctionSessionUserNo(c *gin.Context) {
	var req CheckAuctionSessionUserNoRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		service.Error(c, e.InvalidParams, err, "无效参数")
		return
	}
	resp, err := service.FengheProvider.GetViewAuctionSessionUserNoList(c, &fenghe.GetViewAuctionSessionUserNoListRequest{
		Query: &fenghe.ViewAuctionSessionUserNo{
			Phone:     req.Phone,
			SessionNo: req.SessionNo,
		},
		Page:     1,
		PageSize: 1,
	})
	if err != nil {
		service.Error(c, e.Failed, err, "查询失败")
		return
	}
	exist := true
	var userInfo *fenghe.UserData
	var userInfoResp *UserSeriesCheckResp
	if resp.Total > 0 {
		userInfoResp = &UserSeriesCheckResp{
			ViewAuctionSessionUserNo: resp.List[0],
			SignStatus:               1,
		}
		userInfoRes, errs := service.FengheProvider.GetUserList(c, &fenghe.GetUserListRequest{Query: &fenghe.UserData{Phone: req.Phone}, PageSize: 1, Page: 1})
		if errs != nil || userInfoRes.Total == 0 {
			userInfoResp.SignStatus = 2
		} else {
			userInfo = userInfoRes.List[0]
			contractRes, err1 := service.FengheProvider.GetUserContractList(c, &fenghe.GetUserContractListRequest{
				Page:     1,
				PageSize: -1,
				Query: &fenghe.UserContractData{
					UserID:      userInfo.UUID,
					SeriesUUID:  req.SeriesUid,
					AuctionUUID: req.SessionNo,
				},
			})
			if err1 != nil || contractRes.Total == 0 {
				userInfoResp.SignStatus = 2
			} else {
				for _, v := range contractRes.List {
					if v.DownloadURL == "" {
						userInfoResp.SignStatus = 2
						break
					}
				}
			}
		}
	} else {
		exist = false
	}
	service.Success(c, map[string]any{
		"userInfo": userInfoResp,
		"exist":    exist,
	})
}

// GetAuctionSessionUserNoList 批量查询AuctionSessionUserNo
func (a *AuctionHandler) GetAuctionSessionUserNoList(c *gin.Context) {
	var req GetAuctionSessionUserNoListRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		service.Error(c, e.InvalidParams, err, "无效参数")
		return
	}
	var protoReq = fenghe.GetAuctionSessionUserNoListRequest{Query: &fenghe.AuctionSessionUserNoData{}}
	utils.RequestDataConvert(&req, &protoReq)
	resp, err := service.FengheProvider.GetAuctionSessionUserNoList(c, &protoReq)
	if err != nil {
		service.Error(c, e.Failed, err, "查询失败")
		return
	}
	service.List(c, resp.List, service.OptionPage(resp.Page, resp.PageSize, resp.Total), service.OptionMsg("查询成功"))
}

// GetViewAuctionSessionUserNoList 批量查询AuctionSessionUserNo
func (a *AuctionHandler) GetViewAuctionSessionUserNoList(c *gin.Context) {
	var req GetViewAuctionSessionUserNoListRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		service.Error(c, e.InvalidParams, err, "无效参数")
		return
	}
	fmt.Printf("req: %+v\n", req)
	var protoReq = fenghe.GetViewAuctionSessionUserNoListRequest{Page: req.Page, PageSize: req.PageSize, Query: &fenghe.ViewAuctionSessionUserNo{SessionNo: req.SessionNo}, Order: "auction_user_no asc"}
	//utils.RequestDataConvert(&req, &protoReq)
	fmt.Printf("protoReq: %+v\n", &protoReq)
	resp, err := service.FengheProvider.GetViewAuctionSessionUserNoList(c, &protoReq)
	if err != nil {
		service.Error(c, e.Failed, err, "查询失败")
		return
	}
	for i, v := range resp.List {
		if v.AuctionUserNo == "000" {
			resp.List[i].AuctionUserNo = ""
		}
	}
	service.List(c, resp.List, service.OptionPage(resp.Page, resp.PageSize, resp.Total), service.OptionMsg("查询成功"))
}

func (a *AuctionHandler) GetQrCode(c *gin.Context) {
	var req GetQrCodeRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		service.Error(c, e.InvalidParams, err, "无效参数")
		return
	}
	var data = make(map[string]string)
	data["url"] = config.AppConfig.Service.Host + "/licenseIssuance/personal-Info?number=1&auctionUid=" + req.AuctionUid
	//生成二维码
	bs64Qrcode, ok := a.offlineQrcode[config.AppConfig.Service.AppMode]
	if !ok {
		var err error
		bs64Qrcode, err = utils.GenerateQRCode(data["url"])
		if err != nil {
			service.Error(c, e.Failed, err, "生成二维码失败")
			return
		}
		a.offlineQrcode[config.AppConfig.Service.AppMode] = bs64Qrcode
	}
	data["qrCode"] = bs64Qrcode
	service.Success(c, data)
}
func (a *AuctionHandler) CheckIsExistedBySessionNo(c *gin.Context) {
	var req CheckIsExistedBySessionNoRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		service.Error(c, e.InvalidParams, err, "无效参数")
		return
	}
	num, err := strconv.ParseInt(req.AuctionUserNo, 10, 32)
	fmt.Println("num:", num)
	resp, err := service.FengheProvider.GetViewAuctionSessionUserNoList(c, &fenghe.GetViewAuctionSessionUserNoListRequest{
		Query: &fenghe.ViewAuctionSessionUserNo{
			SessionNo: req.SessionNo,
		},
		Where:    fmt.Sprintf("user_no = %d", num),
		Page:     1,
		PageSize: 1,
	})
	if err != nil {
		service.Error(c, e.Failed, err, "查询失败")
		return
	}
	exist := true
	var userInfo *fenghe.ViewAuctionSessionUserNo
	if resp.Total > 0 {
		userInfo = resp.List[0]
	} else {
		exist = false
	}
	service.Success(c, map[string]any{
		"userInfo": userInfo,
		"exist":    exist,
	})
	return
}

// AdmUpdateAuctionSessionUserNo 更新AuctionSessionUserNo
func (a *AuctionHandler) AdmUpdateAuctionSessionUserNo(c *gin.Context) {
	var req AdmUpdateAuctionSessionUserNoRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		service.Error(c, e.InvalidParams, err, "无效参数")
		return
	}
	protoReq := fenghe.AuctionSessionUserNoData{
		SessionNo: req.SessionNo,
		Phone:     req.Phone,
	}
	auctionUserNo, _ := strconv.ParseInt(req.AuctionUserNo, 10, 32)
	protoReq.AuctionUserNo = uint32(auctionUserNo)
	_, err := service.FengheProvider.UpdateAuctionSessionUserNo(c, &protoReq)
	if err != nil {
		service.Error(c, e.Failed, err, "更新失败")
		return
	}
	service.Success(c)
}
func (a *AuctionHandler) DownloadUploadUserTemplate(c *gin.Context) {
	baseUrl := download.GetErpHostUrl(c)
	if baseUrl.Host == "" {
		return
	}
	fullUrl := baseUrl.ResolveReference(&url.URL{Path: "/api/v1/fenghe/adm/static/template/拍卖用户导入模板.xlsx"})
	service.Success(c, map[string]any{
		"url": fullUrl.String(),
	})
}
func (a *AuctionHandler) UploadAuctionUserData(c *gin.Context) {
	var req UploadAuctionUserDataReq
	if err := c.ShouldBind(&req); err != nil {
		service.Error(c, e.ParamsError, err)
		return
	}
	file, _, err := c.Request.FormFile("file")
	if err != nil {
		service.Error(c, e.ParamsError, err)
		return
	} else {
		defer file.Close()
	}
	//使用excelize读取文件内容
	f, err := excelize.OpenReader(file)
	if err != nil {
		service.Error(c, e.Failed, err)
		return
	}
	//遍历Sheet1中的所有单元格
	rows, err := f.GetRows("Sheet1")
	if err != nil {
		service.Error(c, e.Failed, err)
		return
	}
	//检查行数
	var data []AuctionUserTemplateData
	phoneVd, _ := utils.NewPhoneValidator()
	//检验并整理数据
	for i, row := range rows {
		if i == 0 {
			if len(row) < 3 {
				service.Error(c, e.Failed, err, "字段不符，请重新上传姓名和手机号") //此提示为产品需求设计的，勿改
				return
			}
			if row[0] != "客户姓名" || row[1] != "手机" || row[2] != "身份证号" {
				service.Error(c, e.Failed, err, "字段不符，请重新上传姓名和手机号") //此提示为产品需求设计的，勿改
				return
			}
			continue
		}
		tmp := AuctionUserTemplateData{RowNo: i + 1}
		if len(row) >= 1 {
			tmp.RelName = row[0]
		}
		if len(row) >= 2 {
			tmp.Phone = row[1]
		}
		if len(row) >= 3 {
			tmp.CardId = row[2]
		}
		if len(row) < 3 {
			tmp.AddErr("内容缺失")
		} else {
			//手机号校验
			if !phoneVd.Validate(tmp.Phone) {
				tmp.AddErr("手机号格式错误")
			}
			tmp.CountryCode, tmp.Phone = phoneVd.GetCountryCode(tmp.Phone)
			auctionSessionUserResp, errs := service.FengheProvider.GetAuctionSessionUserNoList(c, &fenghe.GetAuctionSessionUserNoListRequest{
				Query:    &fenghe.AuctionSessionUserNoData{Phone: tmp.Phone, SessionNo: req.SessionNo},
				Page:     1,
				PageSize: 1,
				Order:    "auction_user_no desc",
			})
			if errs != nil {
				tmp.AddErr("号牌信息查询失败")
			} else if auctionSessionUserResp != nil && auctionSessionUserResp.Total > 0 {
				tmp.AddErr("手机号已存在")
			}
			//身份证、实名信息校验
			//if err = utils.AuthenticationTwo(utils.AuthService{
			//	Name:  tmp.RelName,
			//	IDNum: tmp.CardId,
			//}); err != nil {
			//	tmp.AddErr("身份证与姓名不匹配或姓名不存在")
			//}
		}
		data = append(data, tmp)
	}
	//生成数据
	for _, v := range data {
		if v.HasErr() {
			continue
		}
		//查询手机换号是否存在
		auctionUserRes, _ := service.FengheProvider.GetAuctionSessionUserNoList(c, &fenghe.GetAuctionSessionUserNoListRequest{
			Query:    &fenghe.AuctionSessionUserNoData{SessionNo: req.SessionNo, Phone: v.Phone},
			PageSize: 1,
			Page:     1,
		})
		if auctionUserRes.Total > 0 {
			v.AddErr("手机号已绑定号牌")
			continue
		}
		_, err = service.FengheProvider.CreateAuctionSessionUserNo(c, &fenghe.AuctionSessionUserNoData{
			SessionNo: req.SessionNo,
			Phone:     v.Phone,
		})
		if err != nil {
			v.AddErr("号牌创建失败")
			continue
		}
		//查询法大大认证表的用户信息是否存在
		userRes, _ := service.FengheProvider.GetUserList(c, &fenghe.GetUserListRequest{
			Query:    &fenghe.UserData{Phone: v.Phone},
			PageSize: 1,
			Page:     1,
		})
		if userRes != nil && userRes.Total > 0 {
			continue
		}
		user := &fenghe.UserData{
			CountryCode: v.CountryCode,
			Phone:       v.Phone,
			UserName:    v.RelName,
			CardType:    1,
			CardID:      v.CardId,
		}
		if user.CardType == 1 {
			user.Gender = int32(idcard.GetSex(user.CardID))
			switch user.Gender {
			case 0:
				user.Gender = 2
			}
			user.Birthday = idcard.GetBirthDay(user.CardID)
			user.IsMainland = 1
		}
		fmt.Printf("new auction User:%#v\n", user)
		_, err = service.FengheProvider.SaveUser(c, user)
		if err != nil {
			v.AddErr("用户创建失败")
		}
	}
	//分析处理结果
	var resp = UploadAuctionUserDataResp{}
	var failedData []ImportAuctionUserTemplateFailedData
	for _, v := range data {
		if v.HasErr() {
			resp.FailedCount++
			failedData = append(failedData, ImportAuctionUserTemplateFailedData{
				RelName: v.RelName,
				Phone:   v.Phone,
				CardId:  v.CardId,
				Err:     v.GetErr(),
			})
		} else {
			resp.SuccessCount++
		}
	}
	//生成失败数据
	if resp.FailedCount > 0 {
		name := fmt.Sprintf("号牌导入失败数据%d_%d.xlsx", resp.FailedCount, time.Now().Unix())
		ex := exportToExcel.NewExcel("./runtime", name)
		st1 := ex.NewSheet("Sheet1", ImportAuctionUserTemplateFailedData{})
		_ = st1.Title.Gen(
			st1.Title.NewTitleItem(4, "客户姓名", 1, 1),
			st1.Title.NewTitleItem(4, "手机", 1, 2),
			st1.Title.NewTitleItem(4, "身份证号", 1, 3),
			st1.Title.NewTitleItem(4, "错误信息", 1, 4),
		)
		st1.SetFieldSort([]string{"relName", "phone", "cardId", "err"})
		_ = st1.FillData(failedData)
		err = ex.Save()
		if err != nil {
			fmt.Println(err)
		}
		baseUrl := download.GetErpHostUrl(c)
		fullUrl := baseUrl.ResolveReference(&url.URL{
			Path: "/api/v1/fenghe/adm/runtime/" + name})
		resp.FailedFileUrl = fullUrl.String()
	}

	service.Success(c, resp)
}

// ExportAuctionUserNo 批量查询AuctionSessionUserNo
func (a *AuctionHandler) ExportAuctionUserNo(c *gin.Context) {
	var req GetViewAuctionSessionUserNoListRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		service.Error(c, e.InvalidParams, err, "无效参数")
		return
	}
	if config.AppConfig.Service.AppMode != "prod" {
		service.Error(c, e.Failed, nil, "只支持生产环境导出")
		return
	}
	var protoReq = fenghe.GetViewAuctionSessionUserNoListRequest{Page: req.Page, PageSize: -1, Query: &fenghe.ViewAuctionSessionUserNo{SessionNo: req.SessionNo}, Order: "auction_user_no asc"}

	fmt.Printf("protoReq: %+v\n", &protoReq)
	resp, err := service.FengheProvider.GetViewAuctionSessionUserNoList(c, &protoReq)
	if err != nil {
		service.Error(c, e.Failed, err, "查询失败")
		return
	}
	var data []ExportAuctionUserNoData
	for i, v := range resp.List {
		if v.AuctionUserNo == "000" {
			resp.List[i].AuctionUserNo = ""
		}
		gender := "男"
		if v.Gender == 2 {
			gender = "女"
		}
		cardType := ""
		switch resp.List[i].CardType { //1中国身份证，2护照，3其他
		case 1:
			cardType = "中国身份证"
		case 2:
			cardType = "护照"
		default:
			cardType = "其他"
		}
		tmp := ExportAuctionUserNoData{
			AuctionUsrNo: resp.List[i].AuctionUserNo,
			Phone:        resp.List[i].Phone,
			Name:         resp.List[i].UserName,
			Gender:       gender,
			BirthDay:     resp.List[i].Birthday,
			Address:      resp.List[i].Address,
			//BankName:     resp.List[i].BankName,
			//BankNo:       resp.List[i].BankNo,
			CardType:  cardType,
			CardId:    resp.List[i].CardId,
			CreatedAt: resp.List[i].CreatedAt,
		}
		data = append(data, tmp)
	}
	name := fmt.Sprintf("号牌数据导出_%d.xlsx", time.Now().Unix())
	ex := exportToExcel.NewExcel("./runtime", name)
	st1 := ex.NewSheet("Sheet1", ExportAuctionUserNoData{})
	_ = st1.Title.Gen(
		st1.Title.NewTitleItem(4, "号牌", 1, 1),
		st1.Title.NewTitleItem(4, "手机号", 1, 2),
		st1.Title.NewTitleItem(4, "姓名", 1, 3),
		st1.Title.NewTitleItem(4, "性别", 1, 4),
		st1.Title.NewTitleItem(4, "生日", 1, 5),
		st1.Title.NewTitleItem(4, "家庭住址", 1, 6),
		//st1.Title.NewTitleItem(4, "所属银行", 1, 7),
		//st1.Title.NewTitleItem(4, "银行卡号码", 1, 8),
		st1.Title.NewTitleItem(4, "证件类型", 1, 7),
		st1.Title.NewTitleItem(4, "证件号", 1, 8),
		st1.Title.NewTitleItem(4, "创建时间", 1, 9),
	)
	_ = st1.FillData(data)
	err = ex.Save()
	if err != nil {
		fmt.Println(err)
	}
	baseUrl := download.GetErpHostUrl(c)
	fullUrl := baseUrl.ResolveReference(&url.URL{
		Path: "/api/v1/fenghe/adm/runtime/" + name})
	service.Success(c, map[string]any{
		"url": fullUrl.String(),
	})
}
func (a *AuctionHandler) UserInfo(c *gin.Context) {
	var req UserInfoReq
	if err := c.ShouldBindJSON(&req); err != nil {
		service.Error(c, e.InvalidParams, err, "无效参数")
		return
	}

}
