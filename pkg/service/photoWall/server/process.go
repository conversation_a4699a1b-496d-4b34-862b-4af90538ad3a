package server

import (
	"encoding/json"
	"fmt"
	requestWebsocket "github.com/fonchain_enterprise/client-auction/pkg/model/request/websocket"
	responseWebsocket "github.com/fonchain_enterprise/client-auction/pkg/model/response/websocket"
	"sync"
)

type DisposeFunc func(client *Client, cmd string, message []byte) (code uint32, msg string, data interface{})

var (
	handlers        = make(map[string]DisposeFunc)
	handlersRWMutex sync.RWMutex
)

// Register 注册
func Register(key string, value DisposeFunc) {
	handlersRWMutex.Lock()
	defer handlersRWMutex.Unlock()
	handlers[key] = value
	return
}

func getHandlers(key string) (value DisposeFunc, ok bool) {
	handlersRWMutex.RLock()
	defer handlersRWMutex.RUnlock()
	value, ok = handlers[key]
	return
}

func ProcessData(client *Client, message []byte) {
	defer func() {
		if r := recover(); r != nil {
			fmt.Println("处理数据 stop", r)
		}
	}()
	if string(message) == "ping" {
		client.SendMsg([]byte("pong"))
		return
	}
	request := &requestWebsocket.Request{}
	if err := json.Unmarshal(message, request); err != nil {
		client.SendMsg([]byte("数据不合法"))
		return
	}
	requestData, err := json.Marshal(request.Data)
	if err != nil {
		client.SendMsg([]byte("处理数据失败"))
		return
	}
	cmd := request.Cmd
	var (
		code uint32
		msg  string
		data interface{}
	)

	// 采用 map 注册的方式
	if value, ok := getHandlers(cmd); ok {
		code, msg, data = value(client, cmd, requestData)
	} else {
		code = 1
		msg = "未知命令"
	}
	responseHead := responseWebsocket.NewResponseHead(cmd, code, msg, data)
	headByte, err := json.Marshal(responseHead)
	if err != nil {
		return
	}
	client.SendMsg(headByte)
	return
}
