package server

import (
	"encoding/json"
	"fmt"
	"github.com/fonchain_enterprise/client-auction/pkg/common/cache"
	"github.com/fonchain_enterprise/client-auction/pkg/model"
	"github.com/gin-gonic/gin"
	"github.com/gorilla/websocket"
	"net/http"
	"time"
)

var upgrader = websocket.Upgrader{
	CheckOrigin: func(r *http.Request) bool {
		return true
	},
}

var GlobalClientManager = NewClientManager()

func StartChan() {
	GlobalClientManager.Start()
}

func HandleWebSocket(ctx *gin.Context) {
	var conn *websocket.Conn
	var err error
	conn, err = upgrader.Upgrade(ctx.Writer, ctx.Request, nil)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}
	socketKey := ctx.Query("socketKey")
	if socketKey == "" {
		conn.Close()
		return
	}
	userInfoVal := cache.RedisClient.Get(socketKey).Val()
	if userInfoVal == "" {
		conn.Close()
		return
	}
	var userInfo model.LoginInfo
	if err = json.Unmarshal([]byte(userInfoVal), &userInfo); err != nil {
		conn.Close()
		return
	}
	defer cache.RedisClient.Del(socketKey)
	client := NewClient(conn.RemoteAddr().String(), conn, uint64(time.Now().Unix()))
	client.UserID = fmt.Sprint(userInfo.ID)
	go client.read()
	go client.write()
	GlobalClientManager.Register <- client
	GlobalClientManager.Login <- &login{UserID: fmt.Sprint(userInfo.ID), Client: client}
}
