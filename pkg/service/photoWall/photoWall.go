package photoWall

import (
	"context"
	"dubbo.apache.org/dubbo-go/v3/common/logger"
	"encoding/json"
	"errors"
	"fmt"
	"github.com/fonchain_enterprise/client-auction/pkg/common/cache"
	"io"
	"log"
	"net/http"
	"sync"
	"time"

	"github.com/fonchain_enterprise/client-auction/api/photoWall"
	"github.com/fonchain_enterprise/client-auction/pkg/common/oss"
	"github.com/fonchain_enterprise/client-auction/pkg/e"
	"github.com/fonchain_enterprise/client-auction/pkg/logic/blockchain"
	"github.com/fonchain_enterprise/client-auction/pkg/serializer"
	"github.com/fonchain_enterprise/client-auction/pkg/service"

	"github.com/gin-gonic/gin"
	"github.com/gin-gonic/gin/binding"
	"github.com/gorilla/websocket"
)

func SavePhoto(c *gin.Context) {
	var req photoWall.SavePhotoReq
	if err := c.ShouldBindBodyWith(&req, binding.JSON); err != nil {
		service.Error(c, e.InvalidParams, err)
		return
	}

	//file, err := c.FormFile("file")
	//if err != nil {
	//	service.Error(c, e.Error, err)
	//	return
	//}
	//
	//buff := new(bytes.Buffer)
	//var src multipart.File
	//src, err = file.Open()
	//defer src.Close()
	//
	//_, err = io.Copy(buff, src)
	//if err != nil {
	//	service.Error(c, e.Error, err)
	//	return
	//}
	//
	//fileExt := path.Ext(file.Filename)
	//filePath, err := oss.OssUploadToBos(fileExt, buff.Bytes())
	//if err != nil {
	//	service.Error(c, e.Error, err)
	//	return
	//}

	res, err := service.PhotoWallProvider.SavePhoto(c, &req)
	if err != nil {
		service.Error(c, e.Error, err)
		return
	}

	updateReq := photoWall.UpdatePhotoReq{
		Uuid: res.Uuid,
	}

	// 获取 文件流
	imgBase64, err := blockchain.GetBase64FromUrl(req.PhotoUrl)
	if err != nil {
		service.Error(c, e.Error, errors.New("获取文件失败"))
		return
	}

	// 上链
	storeRes, storeResErr := blockchain.CreateStore(&blockchain.StoreReq{Data: imgBase64})
	if storeResErr != nil {
		service.Error(c, e.Error, errors.New("上链失败"))
		return
	}

	go func(txID, UUID, photoUrl string) {
		for {
			time.Sleep(3 * time.Second)
			// 查询
			heightRes, heightResErr := blockchain.QueryStoreHeight(txID)
			if heightResErr != nil {
				return
			}

			if heightRes.Data == 0 {
				continue
			}

			storeDetailRes, storeDetailResErr := blockchain.QueryStoreDetail(heightRes.Data)
			if storeDetailResErr != nil {
				return
			}

			if storeDetailRes.Data == nil {
				continue
			}

			// 生成 证书 证书编号
			//updateReq.PhotoUrl = req.PhotoUrl
			updateReq.CertNum = blockchain.MakeCertNum(txID)
			updateReq.WindUpTime = time.Unix(storeDetailRes.Data.Header.Timestamp, 0).Format("2006-01-02 15:04:05")
			updateReq.BlockHeight = heightRes.Data
			updateReq.BlockHash = storeDetailRes.Data.Header.PreviousHash
			updateReq.TransactionHash = txID
			certUrlBase64 := blockchain.MakeCertUrl(UUID, updateReq.CertNum, txID, photoUrl)

			// 上传 oss
			certUrl, err := oss.OssUploadToBos(".png", certUrlBase64)
			if err != nil {
				logger.Errorf("SavePhoto UploadToBos err %+v", err)
				return
			}

			updateReq.CertUrl = certUrl

			_, updatePhotoErr := service.PhotoWallProvider.UpdatePhoto(c, &updateReq)
			if updatePhotoErr != nil {
				logger.Errorf("SavePhoto UpdatePhoto err %+v", err)
			}

			cache.RedisClient.Publish(cache.GetPhotoWallChannel(), 1)

			return
		}

	}(storeRes.Figure, res.Uuid, req.PhotoUrl)

	service.Success(c, res)
	return
}

type SavePhotoData struct {
	PhotoData string `from:"photoData"`
	PhotoExt  string `from:"photoExt"`
}

func SavePhotoWs(c *gin.Context) {
	var req SavePhotoData
	if err := c.ShouldBindBodyWith(&req, binding.JSON); err != nil {
		service.Error(c, e.InvalidParams, err)
		return
	}

	var PhotoWallUpgrader = websocket.Upgrader{
		Subprotocols:      []string{"p0", "p1"},
		ReadBufferSize:    1024 * 1024 * 10,
		WriteBufferSize:   1024 * 50,
		EnableCompression: true,
		Error: func(w http.ResponseWriter, r *http.Request, status int, reason error) {
			http.Error(w, reason.Error(), status)
		},
		CheckOrigin: func(r *http.Request) bool {
			return true
		},
	}

	ws, err := PhotoWallUpgrader.Upgrade(c.Writer, c.Request, http.Header{"Set-Cookie": {"sessionID=11223344"}})

	if err != nil {
		panic(err)
	}

	var wg sync.WaitGroup
	wg.Add(1)

	go savePhotoHandler(ws, &wg, req)

	wg.Wait()
	err = ws.Close()
	fmt.Println("执行结束", err)
	return
}

func savePhotoHandler(ws *websocket.Conn, wg *sync.WaitGroup, req SavePhotoData) {
	defer wg.Done()

	wsRes := serializer.WsResponse{
		Status: 0,
		Code:   0,
	}

	stdoutDone := make(chan struct{})
	go ping(ws, stdoutDone) //存活检测

	savePhotoReq := photoWall.SavePhotoReq{
		PhotoUrl: req.PhotoData,
	}

	res, err := service.PhotoWallProvider.SavePhoto(context.Background(), &savePhotoReq)
	if err != nil {
		wsRes.Code = 1
		wsRes.Msg = "保存图片失败,请重试"
		sendWs(ws, wsRes)
	}

	updateReq := photoWall.UpdatePhotoReq{
		Uuid: res.Uuid,
	}

	// 获取 文件流
	imgBase64, err := blockchain.GetBase64FromUrl(req.PhotoData)
	if err != nil {
		wsRes.Code = 1
		wsRes.Msg = "保存图片失败,请重试"
		sendWs(ws, wsRes)
	}

	// 上链
	storeRes, storeResErr := blockchain.CreateStore(&blockchain.StoreReq{Data: imgBase64})
	if storeResErr != nil {
		wsRes.Code = 1
		wsRes.Msg = "图片上链失败,请重试"
		sendWs(ws, wsRes)
	}

	for {
		time.Sleep(3 * time.Second)
		// 查询
		heightRes, heightResErr := blockchain.QueryStoreHeight(storeRes.Figure)
		if heightResErr != nil {
			wsRes.Code = 1
			wsRes.Msg = "查询图片上链高度失败,请重试"
			sendWs(ws, wsRes)
		}

		if heightRes.Data == 0 {
			continue
		}

		storeDetailRes, storeDetailResErr := blockchain.QueryStoreDetail(heightRes.Data)
		if storeDetailResErr != nil {
			wsRes.Code = 1
			wsRes.Msg = "查询图片上链详情失败,请重试"
			sendWs(ws, wsRes)
		}

		if storeDetailRes.Data == nil {
			continue
		}

		// 生成 证书 证书编号
		updateReq.CertNum = blockchain.MakeCertNum(storeRes.Figure)
		updateReq.WindUpTime = time.Unix(storeDetailRes.Data.Header.Timestamp, 0).Format("2006-01-02 15:04:05")
		updateReq.BlockHeight = heightRes.Data
		updateReq.BlockHash = storeDetailRes.Data.Header.PreviousHash
		updateReq.TransactionHash = storeRes.Figure
		certUrlBase64 := blockchain.MakeCertUrl(res.Uuid, updateReq.CertNum, storeRes.Figure, req.PhotoData)

		// 上传 oss
		certUrl, err := oss.OssUploadToBos(".png", certUrlBase64)
		if err != nil {
			wsRes.Code = 1
			wsRes.Msg = "上传证书失败,请重试"
			sendWs(ws, wsRes)
		}

		updateReq.CertUrl = certUrl

		_, updatePhotoErr := service.PhotoWallProvider.UpdatePhoto(context.Background(), &updateReq)
		if updatePhotoErr != nil {
			wsRes.Code = 1
			wsRes.Msg = "更新图片上链信息失败,请重试"
			sendWs(ws, wsRes)
		}
	}

}

func UpdatePhoto(c *gin.Context) {
	var req photoWall.UpdatePhotoReq
	if err := c.ShouldBindBodyWith(&req, binding.JSON); err != nil {
		service.Error(c, e.InvalidParams, err)
		return
	}
	res, err := service.PhotoWallProvider.UpdatePhoto(c, &req)
	if err != nil {
		service.Error(c, e.Error, err)
		return
	}

	service.Success(c, res)
	return
}

func QueryPhoto(c *gin.Context) {
	var req photoWall.QueryPhotoSingleReq
	if err := c.ShouldBindBodyWith(&req, binding.JSON); err != nil {
		service.Error(c, e.InvalidParams, err)
		return
	}
	res, err := service.PhotoWallProvider.QueryPhoto(c, &req)
	if err != nil {
		service.Error(c, e.Error, err)
		return
	}

	service.Success(c, res)
	return
}

func QueryPhotoGet(c *gin.Context) {
	uuid := c.Query("uuid")
	if uuid == "" {
		service.Error(c, e.Error, errors.New("uuid不能为空"))
		return
	}
	req := photoWall.QueryPhotoSingleReq{
		Uuid: uuid,
	}
	res, err := service.PhotoWallProvider.QueryPhoto(c, &req)
	if err != nil {
		service.Error(c, e.Error, err)
		return
	}

	service.Success(c, res)
	return
}

func QueryPhotoListWs(c *gin.Context) {
	/*// 1. 从查询参数获取请求参数（WebSocket应该使用GET请求）
	var req photoWall.QueryPhotoListReq

	// 从查询参数解析，设置默认值
	if page := c.Query("page"); page != "" {
		if p, err := strconv.ParseInt(page, 10, 64); err == nil {
			req.Page = p
		}
	}

	if pageSize := c.Query("pageSize"); pageSize != "" {
		if ps, err := strconv.ParseInt(pageSize, 10, 64); err == nil {
			req.PageSize = ps
		}
	}

	req.BlockHash = c.Query("blockHash")
	req.TransactionHash = c.Query("transactionHash")
	req.PhotoUrl = c.Query("photoUrl")
	req.WindUpTime = c.Query("windUpTime")

	if blockHeight := c.Query("blockHeight"); blockHeight != "" {
		if bh, err := strconv.ParseInt(blockHeight, 10, 64); err == nil {
			req.BlockHeight = bh
		}
	}

	var isSend bool = false

	if isSendStr := c.Query("isSend"); isSendStr != "" {
		if isSendReq, err := strconv.ParseBool(isSendStr); err == nil {
			isSend = isSendReq
		}
	}*/

	// 2. 配置 WebSocket 升级器
	var PhotoWallUpgrader = websocket.Upgrader{
		Subprotocols:      []string{"p0", "p1"},
		ReadBufferSize:    1024 * 10,
		WriteBufferSize:   1024 * 50,
		EnableCompression: true,
		Error: func(w http.ResponseWriter, r *http.Request, status int, reason error) {
			http.Error(w, reason.Error(), status)
		},
		CheckOrigin: func(r *http.Request) bool {
			return true
		},
	}

	// 3. 升级为 WebSocket 连接
	ws, err := PhotoWallUpgrader.Upgrade(c.Writer, c.Request, http.Header{
		"Set-Cookie": {"sessionID=121314"},
	})
	if err != nil {
		log.Printf("WebSocket upgrade error: %v", err)
		return
	}
	defer func() {
		if err := ws.Close(); err != nil {
			log.Printf("WebSocket close error: %v", err)
		}
	}()

	// 4. 使用 WaitGroup
	var wg sync.WaitGroup
	wg.Add(1)
	done := make(chan struct{})
	go func() {
		wg.Wait()
		close(done)
	}()

	// 5. 启动处理协程
	go queryPhotoListHandler(ws, &wg)

	// 6. 等待处理完成（无超时）
	<-done
	log.Println("处理正常完成")
}

func queryPhotoListHandler(ws *websocket.Conn, wg *sync.WaitGroup) {
	defer wg.Done()

	wsRes := serializer.WsResponse{
		Status: 0,
		Code:   0,
	}

	stdoutDone := make(chan struct{})
	go ping(ws, stdoutDone) //存活检测

	isSendChan := make(chan bool, 1)

	// Redis订阅频道，监听数据更新
	go func() {
		sub := cache.RedisClient.Subscribe(cache.GetPhotoWallChannel())
		defer sub.Close()
		ch := sub.Channel()

		for {
			select {
			case <-ch:
				// 收到Redis频道消息，触发数据推送
				select {
				case isSendChan <- true:
				default:
				}
			case <-stdoutDone:
				return
			}
		}
	}()

	// 客户端WebSocket消息监听
	go func() {
		for {
			var clientMsg map[string]interface{}
			err := ws.ReadJSON(&clientMsg)
			if err != nil {
				log.Printf("读取客户端消息失败: %v", err)
				close(stdoutDone)
				return
			}

			// 处理客户端发送的isSend信号
			if sendFlag, ok := clientMsg["isSend"]; ok {
				if sendBool, ok := sendFlag.(bool); ok && sendBool {
					select {
					case isSendChan <- true:
					default:
					}
				}
			}

			// 处理客户端发送的参数更新（如果需要的话）
			if newReq, ok := clientMsg["req"]; ok {
				log.Printf("收到客户端请求参数更新: %+v", newReq)
			}
		}
	}()

	for {
		select {
		case <-isSendChan:
			req := new(photoWall.QueryPhotoListReq)
			res, err := service.PhotoWallProvider.QueryPhotoList(context.Background(), req)
			if err != nil {
				wsRes.Code = 1
				wsRes.Msg = "查询图片列表失败,请重试"
				sendWs(ws, wsRes)
				continue
			}

			wsRes.Code = 0
			wsRes.Msg = "success"
			if res.Data != nil && len(res.Data) > 0 {
				wsRes.Data = res.Data[0]
			}
			wsRes.Type = "photoList"

			if err := sendWs(ws, wsRes); err != nil {
				log.Printf("发送消息失败: %v", err)
				return
			}
		case <-stdoutDone:
			log.Println("WebSocket连接关闭，退出处理器")
			return
		}
	}
}

func QueryPhotoListSse(c *gin.Context) {
	var req photoWall.QueryPhotoListReq
	if err := c.ShouldBindBodyWith(&req, binding.JSON); err != nil {
		service.Error(c, e.InvalidParams, err)
		return
	}

	// 创建 SSE 事件源
	c.Header("Content-Type", "text/event-stream")
	c.Header("Cache-Control", "no-cache")
	c.Header("Connection", "keep-alive")
	c.Header("Access-Control-Allow-Origin", "*")
	c.Header("Access-Control-Expose-Headers", "Content-Type")

	// 发送 SSE 事件
	for {
		// 处理连接
		c.Stream(func(w io.Writer) bool {
			res, err := service.PhotoWallProvider.QueryPhotoList(c, &req)
			if err != nil {
				return false
			}

			jsonData, err := json.Marshal(res)
			if err != nil {
				return false
			}

			c.Writer.WriteString(string(jsonData))
			c.Writer.Flush()
			return true

		})
		time.Sleep(3 * time.Second)
	}
}

func QueryPhotoList(c *gin.Context) {
	var req photoWall.QueryPhotoListReq
	if err := c.ShouldBindBodyWith(&req, binding.JSON); err != nil {
		service.Error(c, e.InvalidParams, err)
		return
	}
	res, err := service.PhotoWallProvider.QueryPhotoList(c, &req)
	if err != nil {
		service.Error(c, e.Error, err)
		return
	}

	service.Success(c, res)
	return

}

func sendWs(ws *websocket.Conn, wsTempRes serializer.WsResponse) error {

	//拉取最新的数据
	b, err := json.Marshal(wsTempRes)
	if err != nil {
		return err
	}

	return ws.WriteMessage(1, b)
}

type ClientWsMsg struct {
	MsgType string `json:"msgType"`
	Payload string `json:"payload"`
}

func ping(ws *websocket.Conn, done chan struct{}) {
	ticker := time.NewTicker(3 * time.Second)
	defer ticker.Stop()
	for {
		select {
		case <-ticker.C: // 等待 ticker 发出的时间信号
			if err := ws.WriteControl(websocket.PingMessage, []byte{}, time.Now().Add(10*time.Second)); err != nil { //ping
				done <- struct{}{}
				fmt.Println("err------------ping:", err)
				return
			}
			//fmt.Println()
			//fmt.Println("ping:ok", time.Now())
			//fmt.Println()
		case <-done: // 如果有其他地方关闭了 done 通道，退出循环
			return
		}

		/*
			if err := ws.WriteControl(websocket.PingMessage, []byte{}, time.Now().Add(10*time.Second)); err != nil { //ping
				done <- struct{}{}
				fmt.Println("ping:", err)
				return
			}

		*/
	}
}
