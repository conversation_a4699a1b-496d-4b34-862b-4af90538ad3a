package buy

import (
	"context"
	"encoding/json"
	"fmt"
	"github.com/fonchain_enterprise/client-auction/api/fenghe"
	"github.com/fonchain_enterprise/client-auction/pkg/common/cache"
	"github.com/fonchain_enterprise/client-auction/pkg/e"
	"github.com/fonchain_enterprise/client-auction/pkg/logic"
	"github.com/fonchain_enterprise/client-auction/pkg/serializer"
	"github.com/fonchain_enterprise/client-auction/pkg/service"
	ws2 "github.com/fonchain_enterprise/client-auction/pkg/service/ws"
	"github.com/gin-gonic/gin"
	"github.com/gin-gonic/gin/binding"
	"github.com/go-redis/redis"
	"github.com/google/uuid"
	"github.com/gorilla/websocket"
	"net/http"
	"sync"
	"time"
)

// ScreenInfo 大屏幕数据
func ScreenInfo(c *gin.Context) {

	type AuctionBuyListTemp struct {
		AuctionUuid string `form:"auctionUuid" default:""`
	}

	var tempReq AuctionBuyListTemp

	if err := c.ShouldBindWith(&tempReq, binding.Query); err != nil {
		service.Error(c, e.InvalidParams, err)
		return
	}

	auctionUuid := tempReq.AuctionUuid

	// 获取WebSocket连接
	var cstUpgrader = websocket.Upgrader{
		Subprotocols:      []string{"p0", "p1"},
		ReadBufferSize:    1024,
		WriteBufferSize:   1024,
		EnableCompression: true,
		Error: func(w http.ResponseWriter, r *http.Request, status int, reason error) {
			http.Error(w, reason.Error(), status)
		},
		CheckOrigin: func(r *http.Request) bool {
			return true
		},
	}

	ws, err := cstUpgrader.Upgrade(c.Writer, c.Request, http.Header{"Set-Cookie": {"sessionID=1234"}})

	if err != nil {
		return
	}

	defer func() {
		fmt.Println(ws.Close())
	}()

	var wg sync.WaitGroup
	wg.Add(1)
	// 处理WebSocket消息
	go activePushScreen(ws, auctionUuid, &wg)

	wg.Wait()
	err = ws.Close()
	fmt.Println("执行结束", err)

	return
}

// activePushScreen2 服务端主动推送数据 预约病人总数 到场病人总数 已看诊病人总数 排队人数
func activePushScreen(ws *websocket.Conn, auctionUuid string, wg *sync.WaitGroup) {

	uuidStr := uuid.NewString()
	defer wg.Done()

	//首次获取列表数据
	_ = ws.WriteMessage(1, auctionWs(auctionUuid, "artwork"))

	//订阅频道
	sub := cache.RedisClient.Subscribe(cache.GetScreenChannel(auctionUuid))
	defer sub.Close()
	ch := sub.Channel()

	stdoutDone := make(chan struct{})
	go ws2.Ping(ws, stdoutDone) //存活检测

	//一个协程从客户端拿数据
	for {
		var msg *redis.Message

		select {
		case <-stdoutDone:
			fmt.Println("前端终止连接")
			return
		case v := <-ch: //广播数据通知
			msg = v
			fmt.Println("广播数据", msg)
		}

		fmt.Println("uuid:", uuidStr, time.Now(), "频道数据", msg)
		wsType := "artwork"
		if msg != nil {
			fmt.Println("接受数据频道数据", msg.Payload)
			if msg.Payload == "open" {
				wsType = msg.Payload
			}
		}

		_ = ws.WriteMessage(1, auctionWs(auctionUuid, wsType))
		fmt.Println("uuid:", uuidStr, time.Now(), "结束推送")

		fmt.Println()
		fmt.Println()
		fmt.Println()

	}

	return
}

func auctionWs(auctionUuid string, wsType string) []byte {

	//首次获取列表数据
	req := &fenghe.AuctionDetail{
		Uuid: auctionUuid,
	}
	fmt.Println(req)

	fmt.Println(time.Now(), "微服务前")
	res, err := service.FengheProvider.NowScreenArtworkAuction(context.Background(), req)
	fmt.Println(time.Now(), "微服务后")

	res.WsType = wsType

	wsRes := serializer.WsResponse{
		Status: 0,
		Code:   0,
		Data:   res,
	}
	if err != nil {
		wsRes.Code = 1
		wsRes.Msg = err.Error()
	}
	res.AuctionPriceList = nil

	if res.Auction != nil && res.Auction.EndNum > 0 && res.Auction.TotalNum > uint32(res.Auction.EndNum) {
		res.Auction.TotalNum = uint32(res.Auction.EndNum)
	}

	//计算汇率了
	if res.NowAuctionPrice != nil {
		res.NowAuctionPrice.GlobalPrices = &fenghe.GlobalPrice{
			RMB: logic.GetGlobalCurrencyMoney(res.NowAuctionPrice.NowPrice, "RMB"),
			JPY: logic.GetGlobalCurrencyMoney(res.NowAuctionPrice.NowPrice, "JPY"),
			USD: logic.GetGlobalCurrencyMoney(res.NowAuctionPrice.NowPrice, "USD"),
			EUR: logic.GetGlobalCurrencyMoney(res.NowAuctionPrice.NowPrice, "EUR"),
			HKD: logic.GetGlobalCurrencyMoney(res.NowAuctionPrice.NowPrice, "HKD"),
			TWD: logic.GetGlobalCurrencyMoney(res.NowAuctionPrice.NowPrice, "TWD"),
		}
	}

	b, _ := json.Marshal(res)

	fmt.Println(time.Now(), "微服务完毕")

	// 输出WebSocket消息内容
	return b
}

func firstWs(auctionUuid string) []byte {

	//首次获取列表数据
	req := &fenghe.AuctionDetail{
		Uuid: auctionUuid,
	}

	res, err := service.FengheProvider.NowBiddingArtworkAuction(context.Background(), req)
	res.WsType = "all"

	wsRes := serializer.WsResponse{
		Status: 0,
		Code:   0,
		Data:   res,
	}
	if err != nil {
		wsRes.Code = 1
		wsRes.Msg = err.Error()
	}

	b, _ := json.Marshal(res)

	// 输出WebSocket消息内容

	return b
}
