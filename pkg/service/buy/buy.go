package buy

import (
	"fmt"
	"github.com/fonchain_enterprise/client-auction/api/artwork_query"
	"github.com/fonchain_enterprise/client-auction/api/fenghe"
	"github.com/fonchain_enterprise/client-auction/pkg/e"

	"github.com/fonchain_enterprise/client-auction/pkg/model/request"
	"github.com/fonchain_enterprise/client-auction/pkg/service"
	"github.com/gin-gonic/gin"
	"github.com/gin-gonic/gin/binding"
	"strings"
)

// List 开始直播
func List(c *gin.Context) {
	var req request.OrderRecordReq
	if err := c.ShouldBindJSON(&req); err != nil {
		service.Error(c, e.InvalidParams, err, "无效参数")
		return
	}

	orderListResp, err := service.FengheProvider.GetSeriesOrderList(c, &fenghe.GetSeriesOrderListRequest{
		Query:    &fenghe.SeriesOrderData{UserID: req.UserID},
		Page:     req.Page,
		PageSize: req.PageSize,
	})
	if err != nil {
		service.Error(c, e.Failed, err)
		return
	}
	var artworkUidList []string
	for _, v := range orderListResp.List {
		v := v
		artworkUidList = append(artworkUidList, v.ArtworkUuid)
	}
	artworkListResp, err := service.ArtworkQueryProvider.GetArtworkProfileList(c, &artwork_query.GetArtworkProfileListRequest{
		Page:     1,
		PageSize: int64(len(artworkUidList)),
		Where:    fmt.Sprintf("uuid in ('%s')", strings.Join(artworkUidList, "','")),
		Select:   []string{"uuid", "name", "hd_pic", "tfnum", "artist_name", "artist_uuid", "abstract", "width", "length", "ruler"},
	})
	if err != nil {
		service.Error(c, e.Failed, err)
		return
	}
	var data []request.OrderRecordType
	for _, v := range orderListResp.List {
		v := v
		tmp := request.OrderRecordType{
			OrderNo:    v.OrderNo,
			Amount:     v.Amount,
			Currency:   v.Currency,
			Status:     v.Status,
			ArtworkUid: v.ArtworkUuid,
			PayTime:    v.UpdatedAt,
			ActionCode: v.ActionCode,
		}
		for _, vv := range artworkListResp.List {
			if v.ArtworkUuid == vv.Uuid {
				vv := vv
				tmp.ArtworkName = vv.Name
				tmp.HdPic = vv.HdPic
				tmp.Tfnum = vv.Tfnum
				tmp.ArtistName = vv.ArtistName
				tmp.ArtistUid = vv.ArtistUuid
				tmp.Abstract = vv.Abstract
				tmp.Width = vv.Width
				tmp.Length = vv.Length
				tmp.Ruler = vv.Ruler
				break
			}
		}
		data = append(data, tmp)
	}
	service.List(c, data, service.OptionPage(req.Page, req.PageSize, orderListResp.Total), service.OptionMsg("查询成功"))
}

// PaidList 开始直播
func PaidList(c *gin.Context) {

	var req fenghe.AuctionPayList

	if err := c.ShouldBindBodyWith(&req, binding.JSON); err != nil {
		service.Error(c, e.InvalidParams, err)
		return
	}
	req.Status = 1

	res, err := service.FengheProvider.ListAuctionPay(c, &req)
	if err != nil {
		service.Error(c, e.Error, err)
		return
	}

	service.Success(c, res)
	return
}
