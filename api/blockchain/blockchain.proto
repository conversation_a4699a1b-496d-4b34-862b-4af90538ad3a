/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

syntax = "proto3";
package blockchain;
//import "github.com/mwitkow/go-proto-validators@v0.3.2/validator.proto";

option go_package = "./;blockchain";

service Blockchain {
  rpc FindBlockHeight(BlockHeight)returns(BlockHeightRes){}//获取区块链最新高度
  rpc FindBlockDetail(BlockHeight)returns(GetBlockDetailsResponse){} //获取区块详情
  rpc CreateBlock(OrdinaryDepositReq) returns(OrdinaryDepositRes){} //创建区块
  rpc FindBlockIndex(BlockHeight)returns(StoreQueryRes){}//获取交易索引
  rpc FindStoreQuery(StoreQueryReq) returns(StoreQueryRes){}//查询隐私存证
  rpc FindDealDetail(BlockHeight) returns(FindDealDetailResponse){}//查询交易详情
  rpc FindDealRaw(BlockHeight) returns(FindDealRawResponse){}//查询交易原始数据
  rpc FindBlockHealth(BlockHeight) returns(BlockHeightRes){}//查询系统健康状态
  rpc FindLedgerInfo(BlockHeight)returns(LedgerListResponse){}//获取应用链信息
  rpc FindMemberList(MemberReq)returns(MemberListResponse){}//获取节点列表详情
  // 区块记录接口
  rpc CreateBlockChainRecord(BlockRecordReq)returns(DefaultReturn){}//创建区块记录
  rpc UpdateBlockChainRecord(BlockRecordReq)returns(DefaultReturn){}//更新区块记录
  rpc FindNoBlockInfo(BlockHeight)returns(NoBlockList){}//查询未生成区块信息
  rpc Wechat(WechatRequest) returns (WechatRespond) {}
  //浏览器接口
  rpc FindHomeData(BlockHeight)returns(HomeDataRes){}//获取首页数据
  rpc FindBlockList(BlockListReq) returns(BlockListResp){}//获取区块列表
  rpc FindBlockTxList(BlockTxListReq) returns(BlockTxListResp){}//获取存证列表
  rpc FindBlockInfo(BlockInfoReq)returns(BlockInfoResp){}//查询区块详情
  rpc FindBlockTxInfo(BlockTxInfoReq)returns(BlockTxDataInfoResp){}//查询交易详情
  rpc FindKeepRecordList(KeepRecordListReq)returns(KeepRecordListRes){}//查询交易详情
  rpc FindPersonageInfo(PersonageInfoReq)returns(PersonageInfoRes){} //个人存证概况
}
message NoBlockList{
  repeated NoBlockInfo data = 1;
}
message NoBlockInfo{
  uint64 id = 1;
  string txHash = 2;
  uint64 userId =3;
  uint64 recordType = 4;
  uint64 fileType = 5;
  string content = 6;
  string fileName = 7;
  string certUrl = 8;
}
message PersonageInfoReq{
  uint32 userId = 1;
}
message PersonageInfoRes{
  int64 normal = 1;
  int64 privacy = 2;
}
message KeepRecordListRes{
  int64 count = 1;
  repeated KeepRecordData data = 2;
}
message KeepRecordData{
  string fileName = 1;
  uint32 recordType = 2;
  string fullName = 3;
  string txHash = 4;
  string createdAt = 5;
  string certUrl = 6;
}
message KeepRecordListReq{
  uint64 userId = 1;
  string name = 2;
  string txHash = 3;
  uint32 recordType = 4;
  string startDate = 5;
  string endDate = 6;
  int32 page = 7;
  int32 pageSize = 8;
}
message BlockTxData{
  string hash = 1;
  string createDate = 2;
}
message BlockTxResp{
  int64 count =1;
  repeated BlockTxData data = 2;
}
message BlockDataInfoResp{
  string hash = 1;
  string createDate = 2;
  string tfnum = 3;
}
message BlockInfoResp{
  string blockHash = 1;
  uint64 blockHeight = 2;
  string createDate = 3;
  int64 blockOutTime = 4;
  repeated BlockDataInfoResp txList = 5;
}
message BlockTxDataInfoResp{
  string txsHash = 1;
  string blockHash = 2;
  uint64 height = 3;
  string createdDate = 4;
  string businessId = 5;
  string extData = 6;
  string certUrl = 7;
  string fileUrl = 8;
  string fileHash = 9;
}
message BlockTxInfoResp{
    BlockTxDataInfoResp data = 1;
}
message BlockInfoReq{
  string hash = 1;
  uint64 height = 2;
}
message BlockTxInfoReq{
  uint64 id = 1;
  string hash = 2;
  uint64 height = 3;
}
message BlockTxListData{
  string txHash = 1;
  string createdAt = 2;
  uint32 recordType = 3;
  uint64 consume = 4;
  string orderNum = 5;
}
message BlockTxListResp{
  int64 count =1;
  repeated BlockTxListData data = 2;
}
message BlockTxListReq{
  int32 page = 1;
  int32 pageSize = 2;
  string businessId = 3;
  string txHash = 4;
  uint64 userId = 5;
  string StartDate = 6;
  string EndDate = 7;
}
message BlockListRespInfo{
  string blockHash = 1;
  int64 height = 2;
  string createDate = 3;
  int64 transactionNum = 4;
}
message BlockListResp{
  int64 count = 1;
  repeated BlockListRespInfo data = 2;
}
message BlockListReq{
  int32 page = 1;
  int32 pageSize = 2;
  string searchType = 3;
  string searchStart = 4;
  string searchEnd = 5;
}
message HomeDataRes{
  uint64 total = 1;
  uint64 normal = 2;
  uint64 privacy = 3;
  uint64 deal = 4;
  repeated HomeDataMessageTrend list = 5;
  repeated NodeStatus nodeStatus = 6;
}
message NodeStatus{
  string name = 1;
  bool status = 2;
  string fastest = 3;
}
message HomeDataMessageTrend{
  string date = 1;
  uint64 total = 2;
  uint64 normal = 3;
  uint64 privacy = 4;
}
message DefaultReturn{
  uint64 id = 1;
}
message BlockRecordReq {
  uint64 id = 1 ;
  uint64 userId = 2;
  uint32 recordType = 3;
  uint32 fileType = 4;
  string fileName = 5;
  string hash = 6;
  string saveTime = 7;
  int64 outBlockTime = 8;
  string tradingHash = 9;
  string ledger = 10;
  string orderNum = 11;
  uint64 consume = 12 ;
  string blockId = 13;
  int64 blockHeight = 14;
  string fullName =  15;
  string content=16;
  string cert = 17;
  string consumeUid = 18;
}
message WechatRequest {
}

message WechatRespond {
  string msg = 1 [json_name = "msg"];
}
message MemberReq {
  string ledger = 1;
}
message MemberList{
  repeated Member memberList = 1;
  bool breaked = 2;
}
message Member {
  string id = 1;
  int32 port = 2;
  string shownName = 3;
  string inAddr = 4;
  int32 height = 5;
  string hashType = 6;
  string consensus = 7;
  int64 licOutTime = 8;
}

message MemberListResponse {
  int32 state = 1;
  string message = 2;
  MemberList data = 3;
  bool breaked = 4;
}

message LedgerMember {
  string id = 1;
  int32 port = 2;
  string shownName = 3;
  string inAddr = 4;
}

message Ledger {
  string name = 1;
  repeated LedgerMember memberList = 2;
  string hashType = 3;
  string cons = 4;
  int64 timeStamp = 5;
  string id = 6;
}

message LedgerListResponse {
  int32 state = 1;
  string message = 2;
  repeated Ledger ledgers = 3;
  int32 number = 4;
}
message StoreQueryReq{
  string txId = 1;
  string priKey = 2;
  string password = 3;
}
message StoreQueryRes{
  uint64 state = 1;
  string message = 2;
  string data = 3;
}

message BlockHeightRes{
  uint64 status = 1;
  string message = 2;
  uint64 data = 3;
}
message BlockHeight{
  string BlockId = 1;
}
message PubKeys {
  string pubKeys = 1;
}
message OrdinaryDepositReq{
  string data = 1;
  repeated string pubKeys = 2;
}
message OrdinaryDepositRes{
  uint64 state = 1;
  string message = 2;
  string data = 3;
}

message BlockHeader {
  int32 version = 1;
  int64 height = 2;
  int64 timestamp = 3;
  string blockId = 4;
  string previousId = 5;
  string worldStateRoot = 6;
  string transactionRoot = 7;
}
message DetailsBlockHeader {
  BlockHeader header = 1;
  repeated string txs = 2;
  string extra = 3;
  string raw = 4;
}
message GetBlockDetailsResponse {
  int32 state = 1;
  string message = 2;
  DetailsBlockHeader data = 3;
}
message DealDetailHeader {
  int32 version = 1;
  int32 type = 2;
  int32 subType = 3;
  int64 timestamp = 4;
  string transactionId = 5;
}

message DealDetailStore {
  string storeData = 1;
//  string extra = 2;
}

message FindDealDetailResponse {
  int32 state = 1;
  string message = 2;
  DealDetailHeader header = 3;
  DealDetailStore store = 4;
  string raw = 5;
}


message RawHeader {
  int32 version = 1;
  int32 type = 2;
  int32 subType = 3;
  int64 timestamp = 4;
  string transactionId = 5;
}

message RawTxProof {
  repeated string left = 1;
  repeated string right = 2;
}

message FindDealRawResponse {
  int32 state = 1;
  string message = 2;
  RawHeader header = 3;
  string data = 4;
  string pubkey = 5;
  string sign = 6;
  string raw = 9;
  RawTxProof txproof = 10;
}