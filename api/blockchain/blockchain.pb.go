//
// Licensed to the Apache Software Foundation (ASF) under one or more
// contributor license agreements.  See the NOTICE file distributed with
// this work for additional information regarding copyright ownership.
// The ASF licenses this file to You under the Apache License, Version 2.0
// (the "License"); you may not use this file except in compliance with
// the License.  You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.24.0--rc1
// source: pb/blockchain/blockchain.proto

package blockchain

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type NoBlockList struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Data []*NoBlockInfo `protobuf:"bytes,1,rep,name=data,proto3" json:"data,omitempty"`
}

func (x *NoBlockList) Reset() {
	*x = NoBlockList{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_blockchain_blockchain_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *NoBlockList) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NoBlockList) ProtoMessage() {}

func (x *NoBlockList) ProtoReflect() protoreflect.Message {
	mi := &file_pb_blockchain_blockchain_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NoBlockList.ProtoReflect.Descriptor instead.
func (*NoBlockList) Descriptor() ([]byte, []int) {
	return file_pb_blockchain_blockchain_proto_rawDescGZIP(), []int{0}
}

func (x *NoBlockList) GetData() []*NoBlockInfo {
	if x != nil {
		return x.Data
	}
	return nil
}

type NoBlockInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id         uint64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	TxHash     string `protobuf:"bytes,2,opt,name=txHash,proto3" json:"txHash,omitempty"`
	UserId     uint64 `protobuf:"varint,3,opt,name=userId,proto3" json:"userId,omitempty"`
	RecordType uint64 `protobuf:"varint,4,opt,name=recordType,proto3" json:"recordType,omitempty"`
	FileType   uint64 `protobuf:"varint,5,opt,name=fileType,proto3" json:"fileType,omitempty"`
	Content    string `protobuf:"bytes,6,opt,name=content,proto3" json:"content,omitempty"`
	FileName   string `protobuf:"bytes,7,opt,name=fileName,proto3" json:"fileName,omitempty"`
	CertUrl    string `protobuf:"bytes,8,opt,name=certUrl,proto3" json:"certUrl,omitempty"`
}

func (x *NoBlockInfo) Reset() {
	*x = NoBlockInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_blockchain_blockchain_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *NoBlockInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NoBlockInfo) ProtoMessage() {}

func (x *NoBlockInfo) ProtoReflect() protoreflect.Message {
	mi := &file_pb_blockchain_blockchain_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NoBlockInfo.ProtoReflect.Descriptor instead.
func (*NoBlockInfo) Descriptor() ([]byte, []int) {
	return file_pb_blockchain_blockchain_proto_rawDescGZIP(), []int{1}
}

func (x *NoBlockInfo) GetId() uint64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *NoBlockInfo) GetTxHash() string {
	if x != nil {
		return x.TxHash
	}
	return ""
}

func (x *NoBlockInfo) GetUserId() uint64 {
	if x != nil {
		return x.UserId
	}
	return 0
}

func (x *NoBlockInfo) GetRecordType() uint64 {
	if x != nil {
		return x.RecordType
	}
	return 0
}

func (x *NoBlockInfo) GetFileType() uint64 {
	if x != nil {
		return x.FileType
	}
	return 0
}

func (x *NoBlockInfo) GetContent() string {
	if x != nil {
		return x.Content
	}
	return ""
}

func (x *NoBlockInfo) GetFileName() string {
	if x != nil {
		return x.FileName
	}
	return ""
}

func (x *NoBlockInfo) GetCertUrl() string {
	if x != nil {
		return x.CertUrl
	}
	return ""
}

type PersonageInfoReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UserId uint32 `protobuf:"varint,1,opt,name=userId,proto3" json:"userId,omitempty"`
}

func (x *PersonageInfoReq) Reset() {
	*x = PersonageInfoReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_blockchain_blockchain_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PersonageInfoReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PersonageInfoReq) ProtoMessage() {}

func (x *PersonageInfoReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_blockchain_blockchain_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PersonageInfoReq.ProtoReflect.Descriptor instead.
func (*PersonageInfoReq) Descriptor() ([]byte, []int) {
	return file_pb_blockchain_blockchain_proto_rawDescGZIP(), []int{2}
}

func (x *PersonageInfoReq) GetUserId() uint32 {
	if x != nil {
		return x.UserId
	}
	return 0
}

type PersonageInfoRes struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Normal  int64 `protobuf:"varint,1,opt,name=normal,proto3" json:"normal,omitempty"`
	Privacy int64 `protobuf:"varint,2,opt,name=privacy,proto3" json:"privacy,omitempty"`
}

func (x *PersonageInfoRes) Reset() {
	*x = PersonageInfoRes{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_blockchain_blockchain_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PersonageInfoRes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PersonageInfoRes) ProtoMessage() {}

func (x *PersonageInfoRes) ProtoReflect() protoreflect.Message {
	mi := &file_pb_blockchain_blockchain_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PersonageInfoRes.ProtoReflect.Descriptor instead.
func (*PersonageInfoRes) Descriptor() ([]byte, []int) {
	return file_pb_blockchain_blockchain_proto_rawDescGZIP(), []int{3}
}

func (x *PersonageInfoRes) GetNormal() int64 {
	if x != nil {
		return x.Normal
	}
	return 0
}

func (x *PersonageInfoRes) GetPrivacy() int64 {
	if x != nil {
		return x.Privacy
	}
	return 0
}

type KeepRecordListRes struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Count int64             `protobuf:"varint,1,opt,name=count,proto3" json:"count,omitempty"`
	Data  []*KeepRecordData `protobuf:"bytes,2,rep,name=data,proto3" json:"data,omitempty"`
}

func (x *KeepRecordListRes) Reset() {
	*x = KeepRecordListRes{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_blockchain_blockchain_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *KeepRecordListRes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*KeepRecordListRes) ProtoMessage() {}

func (x *KeepRecordListRes) ProtoReflect() protoreflect.Message {
	mi := &file_pb_blockchain_blockchain_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use KeepRecordListRes.ProtoReflect.Descriptor instead.
func (*KeepRecordListRes) Descriptor() ([]byte, []int) {
	return file_pb_blockchain_blockchain_proto_rawDescGZIP(), []int{4}
}

func (x *KeepRecordListRes) GetCount() int64 {
	if x != nil {
		return x.Count
	}
	return 0
}

func (x *KeepRecordListRes) GetData() []*KeepRecordData {
	if x != nil {
		return x.Data
	}
	return nil
}

type KeepRecordData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	FileName   string `protobuf:"bytes,1,opt,name=fileName,proto3" json:"fileName,omitempty"`
	RecordType uint32 `protobuf:"varint,2,opt,name=recordType,proto3" json:"recordType,omitempty"`
	FullName   string `protobuf:"bytes,3,opt,name=fullName,proto3" json:"fullName,omitempty"`
	TxHash     string `protobuf:"bytes,4,opt,name=txHash,proto3" json:"txHash,omitempty"`
	CreatedAt  string `protobuf:"bytes,5,opt,name=createdAt,proto3" json:"createdAt,omitempty"`
	CertUrl    string `protobuf:"bytes,6,opt,name=certUrl,proto3" json:"certUrl,omitempty"`
}

func (x *KeepRecordData) Reset() {
	*x = KeepRecordData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_blockchain_blockchain_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *KeepRecordData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*KeepRecordData) ProtoMessage() {}

func (x *KeepRecordData) ProtoReflect() protoreflect.Message {
	mi := &file_pb_blockchain_blockchain_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use KeepRecordData.ProtoReflect.Descriptor instead.
func (*KeepRecordData) Descriptor() ([]byte, []int) {
	return file_pb_blockchain_blockchain_proto_rawDescGZIP(), []int{5}
}

func (x *KeepRecordData) GetFileName() string {
	if x != nil {
		return x.FileName
	}
	return ""
}

func (x *KeepRecordData) GetRecordType() uint32 {
	if x != nil {
		return x.RecordType
	}
	return 0
}

func (x *KeepRecordData) GetFullName() string {
	if x != nil {
		return x.FullName
	}
	return ""
}

func (x *KeepRecordData) GetTxHash() string {
	if x != nil {
		return x.TxHash
	}
	return ""
}

func (x *KeepRecordData) GetCreatedAt() string {
	if x != nil {
		return x.CreatedAt
	}
	return ""
}

func (x *KeepRecordData) GetCertUrl() string {
	if x != nil {
		return x.CertUrl
	}
	return ""
}

type KeepRecordListReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UserId     uint64 `protobuf:"varint,1,opt,name=userId,proto3" json:"userId,omitempty"`
	Name       string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	TxHash     string `protobuf:"bytes,3,opt,name=txHash,proto3" json:"txHash,omitempty"`
	RecordType uint32 `protobuf:"varint,4,opt,name=recordType,proto3" json:"recordType,omitempty"`
	StartDate  string `protobuf:"bytes,5,opt,name=startDate,proto3" json:"startDate,omitempty"`
	EndDate    string `protobuf:"bytes,6,opt,name=endDate,proto3" json:"endDate,omitempty"`
	Page       int32  `protobuf:"varint,7,opt,name=page,proto3" json:"page,omitempty"`
	PageSize   int32  `protobuf:"varint,8,opt,name=pageSize,proto3" json:"pageSize,omitempty"`
}

func (x *KeepRecordListReq) Reset() {
	*x = KeepRecordListReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_blockchain_blockchain_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *KeepRecordListReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*KeepRecordListReq) ProtoMessage() {}

func (x *KeepRecordListReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_blockchain_blockchain_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use KeepRecordListReq.ProtoReflect.Descriptor instead.
func (*KeepRecordListReq) Descriptor() ([]byte, []int) {
	return file_pb_blockchain_blockchain_proto_rawDescGZIP(), []int{6}
}

func (x *KeepRecordListReq) GetUserId() uint64 {
	if x != nil {
		return x.UserId
	}
	return 0
}

func (x *KeepRecordListReq) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *KeepRecordListReq) GetTxHash() string {
	if x != nil {
		return x.TxHash
	}
	return ""
}

func (x *KeepRecordListReq) GetRecordType() uint32 {
	if x != nil {
		return x.RecordType
	}
	return 0
}

func (x *KeepRecordListReq) GetStartDate() string {
	if x != nil {
		return x.StartDate
	}
	return ""
}

func (x *KeepRecordListReq) GetEndDate() string {
	if x != nil {
		return x.EndDate
	}
	return ""
}

func (x *KeepRecordListReq) GetPage() int32 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *KeepRecordListReq) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

type BlockTxData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Hash       string `protobuf:"bytes,1,opt,name=hash,proto3" json:"hash,omitempty"`
	CreateDate string `protobuf:"bytes,2,opt,name=createDate,proto3" json:"createDate,omitempty"`
}

func (x *BlockTxData) Reset() {
	*x = BlockTxData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_blockchain_blockchain_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BlockTxData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BlockTxData) ProtoMessage() {}

func (x *BlockTxData) ProtoReflect() protoreflect.Message {
	mi := &file_pb_blockchain_blockchain_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BlockTxData.ProtoReflect.Descriptor instead.
func (*BlockTxData) Descriptor() ([]byte, []int) {
	return file_pb_blockchain_blockchain_proto_rawDescGZIP(), []int{7}
}

func (x *BlockTxData) GetHash() string {
	if x != nil {
		return x.Hash
	}
	return ""
}

func (x *BlockTxData) GetCreateDate() string {
	if x != nil {
		return x.CreateDate
	}
	return ""
}

type BlockTxResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Count int64          `protobuf:"varint,1,opt,name=count,proto3" json:"count,omitempty"`
	Data  []*BlockTxData `protobuf:"bytes,2,rep,name=data,proto3" json:"data,omitempty"`
}

func (x *BlockTxResp) Reset() {
	*x = BlockTxResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_blockchain_blockchain_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BlockTxResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BlockTxResp) ProtoMessage() {}

func (x *BlockTxResp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_blockchain_blockchain_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BlockTxResp.ProtoReflect.Descriptor instead.
func (*BlockTxResp) Descriptor() ([]byte, []int) {
	return file_pb_blockchain_blockchain_proto_rawDescGZIP(), []int{8}
}

func (x *BlockTxResp) GetCount() int64 {
	if x != nil {
		return x.Count
	}
	return 0
}

func (x *BlockTxResp) GetData() []*BlockTxData {
	if x != nil {
		return x.Data
	}
	return nil
}

type BlockDataInfoResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Hash       string `protobuf:"bytes,1,opt,name=hash,proto3" json:"hash,omitempty"`
	CreateDate string `protobuf:"bytes,2,opt,name=createDate,proto3" json:"createDate,omitempty"`
	Tfnum      string `protobuf:"bytes,3,opt,name=tfnum,proto3" json:"tfnum,omitempty"`
}

func (x *BlockDataInfoResp) Reset() {
	*x = BlockDataInfoResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_blockchain_blockchain_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BlockDataInfoResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BlockDataInfoResp) ProtoMessage() {}

func (x *BlockDataInfoResp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_blockchain_blockchain_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BlockDataInfoResp.ProtoReflect.Descriptor instead.
func (*BlockDataInfoResp) Descriptor() ([]byte, []int) {
	return file_pb_blockchain_blockchain_proto_rawDescGZIP(), []int{9}
}

func (x *BlockDataInfoResp) GetHash() string {
	if x != nil {
		return x.Hash
	}
	return ""
}

func (x *BlockDataInfoResp) GetCreateDate() string {
	if x != nil {
		return x.CreateDate
	}
	return ""
}

func (x *BlockDataInfoResp) GetTfnum() string {
	if x != nil {
		return x.Tfnum
	}
	return ""
}

type BlockInfoResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	BlockHash    string               `protobuf:"bytes,1,opt,name=blockHash,proto3" json:"blockHash,omitempty"`
	BlockHeight  uint64               `protobuf:"varint,2,opt,name=blockHeight,proto3" json:"blockHeight,omitempty"`
	CreateDate   string               `protobuf:"bytes,3,opt,name=createDate,proto3" json:"createDate,omitempty"`
	BlockOutTime int64                `protobuf:"varint,4,opt,name=blockOutTime,proto3" json:"blockOutTime,omitempty"`
	TxList       []*BlockDataInfoResp `protobuf:"bytes,5,rep,name=txList,proto3" json:"txList,omitempty"`
}

func (x *BlockInfoResp) Reset() {
	*x = BlockInfoResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_blockchain_blockchain_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BlockInfoResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BlockInfoResp) ProtoMessage() {}

func (x *BlockInfoResp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_blockchain_blockchain_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BlockInfoResp.ProtoReflect.Descriptor instead.
func (*BlockInfoResp) Descriptor() ([]byte, []int) {
	return file_pb_blockchain_blockchain_proto_rawDescGZIP(), []int{10}
}

func (x *BlockInfoResp) GetBlockHash() string {
	if x != nil {
		return x.BlockHash
	}
	return ""
}

func (x *BlockInfoResp) GetBlockHeight() uint64 {
	if x != nil {
		return x.BlockHeight
	}
	return 0
}

func (x *BlockInfoResp) GetCreateDate() string {
	if x != nil {
		return x.CreateDate
	}
	return ""
}

func (x *BlockInfoResp) GetBlockOutTime() int64 {
	if x != nil {
		return x.BlockOutTime
	}
	return 0
}

func (x *BlockInfoResp) GetTxList() []*BlockDataInfoResp {
	if x != nil {
		return x.TxList
	}
	return nil
}

type BlockTxDataInfoResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TxsHash     string `protobuf:"bytes,1,opt,name=txsHash,proto3" json:"txsHash,omitempty"`
	BlockHash   string `protobuf:"bytes,2,opt,name=blockHash,proto3" json:"blockHash,omitempty"`
	Height      uint64 `protobuf:"varint,3,opt,name=height,proto3" json:"height,omitempty"`
	CreatedDate string `protobuf:"bytes,4,opt,name=createdDate,proto3" json:"createdDate,omitempty"`
	BusinessId  string `protobuf:"bytes,5,opt,name=businessId,proto3" json:"businessId,omitempty"`
	ExtData     string `protobuf:"bytes,6,opt,name=extData,proto3" json:"extData,omitempty"`
	CertUrl     string `protobuf:"bytes,7,opt,name=certUrl,proto3" json:"certUrl,omitempty"`
	FileUrl     string `protobuf:"bytes,8,opt,name=fileUrl,proto3" json:"fileUrl,omitempty"`
	FileHash    string `protobuf:"bytes,9,opt,name=fileHash,proto3" json:"fileHash,omitempty"`
}

func (x *BlockTxDataInfoResp) Reset() {
	*x = BlockTxDataInfoResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_blockchain_blockchain_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BlockTxDataInfoResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BlockTxDataInfoResp) ProtoMessage() {}

func (x *BlockTxDataInfoResp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_blockchain_blockchain_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BlockTxDataInfoResp.ProtoReflect.Descriptor instead.
func (*BlockTxDataInfoResp) Descriptor() ([]byte, []int) {
	return file_pb_blockchain_blockchain_proto_rawDescGZIP(), []int{11}
}

func (x *BlockTxDataInfoResp) GetTxsHash() string {
	if x != nil {
		return x.TxsHash
	}
	return ""
}

func (x *BlockTxDataInfoResp) GetBlockHash() string {
	if x != nil {
		return x.BlockHash
	}
	return ""
}

func (x *BlockTxDataInfoResp) GetHeight() uint64 {
	if x != nil {
		return x.Height
	}
	return 0
}

func (x *BlockTxDataInfoResp) GetCreatedDate() string {
	if x != nil {
		return x.CreatedDate
	}
	return ""
}

func (x *BlockTxDataInfoResp) GetBusinessId() string {
	if x != nil {
		return x.BusinessId
	}
	return ""
}

func (x *BlockTxDataInfoResp) GetExtData() string {
	if x != nil {
		return x.ExtData
	}
	return ""
}

func (x *BlockTxDataInfoResp) GetCertUrl() string {
	if x != nil {
		return x.CertUrl
	}
	return ""
}

func (x *BlockTxDataInfoResp) GetFileUrl() string {
	if x != nil {
		return x.FileUrl
	}
	return ""
}

func (x *BlockTxDataInfoResp) GetFileHash() string {
	if x != nil {
		return x.FileHash
	}
	return ""
}

type BlockTxInfoResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Data *BlockTxDataInfoResp `protobuf:"bytes,1,opt,name=data,proto3" json:"data,omitempty"`
}

func (x *BlockTxInfoResp) Reset() {
	*x = BlockTxInfoResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_blockchain_blockchain_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BlockTxInfoResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BlockTxInfoResp) ProtoMessage() {}

func (x *BlockTxInfoResp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_blockchain_blockchain_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BlockTxInfoResp.ProtoReflect.Descriptor instead.
func (*BlockTxInfoResp) Descriptor() ([]byte, []int) {
	return file_pb_blockchain_blockchain_proto_rawDescGZIP(), []int{12}
}

func (x *BlockTxInfoResp) GetData() *BlockTxDataInfoResp {
	if x != nil {
		return x.Data
	}
	return nil
}

type BlockInfoReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Hash   string `protobuf:"bytes,1,opt,name=hash,proto3" json:"hash,omitempty"`
	Height uint64 `protobuf:"varint,2,opt,name=height,proto3" json:"height,omitempty"`
}

func (x *BlockInfoReq) Reset() {
	*x = BlockInfoReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_blockchain_blockchain_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BlockInfoReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BlockInfoReq) ProtoMessage() {}

func (x *BlockInfoReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_blockchain_blockchain_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BlockInfoReq.ProtoReflect.Descriptor instead.
func (*BlockInfoReq) Descriptor() ([]byte, []int) {
	return file_pb_blockchain_blockchain_proto_rawDescGZIP(), []int{13}
}

func (x *BlockInfoReq) GetHash() string {
	if x != nil {
		return x.Hash
	}
	return ""
}

func (x *BlockInfoReq) GetHeight() uint64 {
	if x != nil {
		return x.Height
	}
	return 0
}

type BlockTxInfoReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id     uint64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Hash   string `protobuf:"bytes,2,opt,name=hash,proto3" json:"hash,omitempty"`
	Height uint64 `protobuf:"varint,3,opt,name=height,proto3" json:"height,omitempty"`
}

func (x *BlockTxInfoReq) Reset() {
	*x = BlockTxInfoReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_blockchain_blockchain_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BlockTxInfoReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BlockTxInfoReq) ProtoMessage() {}

func (x *BlockTxInfoReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_blockchain_blockchain_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BlockTxInfoReq.ProtoReflect.Descriptor instead.
func (*BlockTxInfoReq) Descriptor() ([]byte, []int) {
	return file_pb_blockchain_blockchain_proto_rawDescGZIP(), []int{14}
}

func (x *BlockTxInfoReq) GetId() uint64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *BlockTxInfoReq) GetHash() string {
	if x != nil {
		return x.Hash
	}
	return ""
}

func (x *BlockTxInfoReq) GetHeight() uint64 {
	if x != nil {
		return x.Height
	}
	return 0
}

type BlockTxListData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TxHash     string `protobuf:"bytes,1,opt,name=txHash,proto3" json:"txHash,omitempty"`
	CreatedAt  string `protobuf:"bytes,2,opt,name=createdAt,proto3" json:"createdAt,omitempty"`
	RecordType uint32 `protobuf:"varint,3,opt,name=recordType,proto3" json:"recordType,omitempty"`
	Consume    uint64 `protobuf:"varint,4,opt,name=consume,proto3" json:"consume,omitempty"`
	OrderNum   string `protobuf:"bytes,5,opt,name=orderNum,proto3" json:"orderNum,omitempty"`
}

func (x *BlockTxListData) Reset() {
	*x = BlockTxListData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_blockchain_blockchain_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BlockTxListData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BlockTxListData) ProtoMessage() {}

func (x *BlockTxListData) ProtoReflect() protoreflect.Message {
	mi := &file_pb_blockchain_blockchain_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BlockTxListData.ProtoReflect.Descriptor instead.
func (*BlockTxListData) Descriptor() ([]byte, []int) {
	return file_pb_blockchain_blockchain_proto_rawDescGZIP(), []int{15}
}

func (x *BlockTxListData) GetTxHash() string {
	if x != nil {
		return x.TxHash
	}
	return ""
}

func (x *BlockTxListData) GetCreatedAt() string {
	if x != nil {
		return x.CreatedAt
	}
	return ""
}

func (x *BlockTxListData) GetRecordType() uint32 {
	if x != nil {
		return x.RecordType
	}
	return 0
}

func (x *BlockTxListData) GetConsume() uint64 {
	if x != nil {
		return x.Consume
	}
	return 0
}

func (x *BlockTxListData) GetOrderNum() string {
	if x != nil {
		return x.OrderNum
	}
	return ""
}

type BlockTxListResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Count int64              `protobuf:"varint,1,opt,name=count,proto3" json:"count,omitempty"`
	Data  []*BlockTxListData `protobuf:"bytes,2,rep,name=data,proto3" json:"data,omitempty"`
}

func (x *BlockTxListResp) Reset() {
	*x = BlockTxListResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_blockchain_blockchain_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BlockTxListResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BlockTxListResp) ProtoMessage() {}

func (x *BlockTxListResp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_blockchain_blockchain_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BlockTxListResp.ProtoReflect.Descriptor instead.
func (*BlockTxListResp) Descriptor() ([]byte, []int) {
	return file_pb_blockchain_blockchain_proto_rawDescGZIP(), []int{16}
}

func (x *BlockTxListResp) GetCount() int64 {
	if x != nil {
		return x.Count
	}
	return 0
}

func (x *BlockTxListResp) GetData() []*BlockTxListData {
	if x != nil {
		return x.Data
	}
	return nil
}

type BlockTxListReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Page       int32  `protobuf:"varint,1,opt,name=page,proto3" json:"page,omitempty"`
	PageSize   int32  `protobuf:"varint,2,opt,name=pageSize,proto3" json:"pageSize,omitempty"`
	BusinessId string `protobuf:"bytes,3,opt,name=businessId,proto3" json:"businessId,omitempty"`
	TxHash     string `protobuf:"bytes,4,opt,name=txHash,proto3" json:"txHash,omitempty"`
	UserId     uint64 `protobuf:"varint,5,opt,name=userId,proto3" json:"userId,omitempty"`
	StartDate  string `protobuf:"bytes,6,opt,name=StartDate,proto3" json:"StartDate,omitempty"`
	EndDate    string `protobuf:"bytes,7,opt,name=EndDate,proto3" json:"EndDate,omitempty"`
}

func (x *BlockTxListReq) Reset() {
	*x = BlockTxListReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_blockchain_blockchain_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BlockTxListReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BlockTxListReq) ProtoMessage() {}

func (x *BlockTxListReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_blockchain_blockchain_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BlockTxListReq.ProtoReflect.Descriptor instead.
func (*BlockTxListReq) Descriptor() ([]byte, []int) {
	return file_pb_blockchain_blockchain_proto_rawDescGZIP(), []int{17}
}

func (x *BlockTxListReq) GetPage() int32 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *BlockTxListReq) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *BlockTxListReq) GetBusinessId() string {
	if x != nil {
		return x.BusinessId
	}
	return ""
}

func (x *BlockTxListReq) GetTxHash() string {
	if x != nil {
		return x.TxHash
	}
	return ""
}

func (x *BlockTxListReq) GetUserId() uint64 {
	if x != nil {
		return x.UserId
	}
	return 0
}

func (x *BlockTxListReq) GetStartDate() string {
	if x != nil {
		return x.StartDate
	}
	return ""
}

func (x *BlockTxListReq) GetEndDate() string {
	if x != nil {
		return x.EndDate
	}
	return ""
}

type BlockListRespInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	BlockHash      string `protobuf:"bytes,1,opt,name=blockHash,proto3" json:"blockHash,omitempty"`
	Height         int64  `protobuf:"varint,2,opt,name=height,proto3" json:"height,omitempty"`
	CreateDate     string `protobuf:"bytes,3,opt,name=createDate,proto3" json:"createDate,omitempty"`
	TransactionNum int64  `protobuf:"varint,4,opt,name=transactionNum,proto3" json:"transactionNum,omitempty"`
}

func (x *BlockListRespInfo) Reset() {
	*x = BlockListRespInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_blockchain_blockchain_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BlockListRespInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BlockListRespInfo) ProtoMessage() {}

func (x *BlockListRespInfo) ProtoReflect() protoreflect.Message {
	mi := &file_pb_blockchain_blockchain_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BlockListRespInfo.ProtoReflect.Descriptor instead.
func (*BlockListRespInfo) Descriptor() ([]byte, []int) {
	return file_pb_blockchain_blockchain_proto_rawDescGZIP(), []int{18}
}

func (x *BlockListRespInfo) GetBlockHash() string {
	if x != nil {
		return x.BlockHash
	}
	return ""
}

func (x *BlockListRespInfo) GetHeight() int64 {
	if x != nil {
		return x.Height
	}
	return 0
}

func (x *BlockListRespInfo) GetCreateDate() string {
	if x != nil {
		return x.CreateDate
	}
	return ""
}

func (x *BlockListRespInfo) GetTransactionNum() int64 {
	if x != nil {
		return x.TransactionNum
	}
	return 0
}

type BlockListResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Count int64                `protobuf:"varint,1,opt,name=count,proto3" json:"count,omitempty"`
	Data  []*BlockListRespInfo `protobuf:"bytes,2,rep,name=data,proto3" json:"data,omitempty"`
}

func (x *BlockListResp) Reset() {
	*x = BlockListResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_blockchain_blockchain_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BlockListResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BlockListResp) ProtoMessage() {}

func (x *BlockListResp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_blockchain_blockchain_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BlockListResp.ProtoReflect.Descriptor instead.
func (*BlockListResp) Descriptor() ([]byte, []int) {
	return file_pb_blockchain_blockchain_proto_rawDescGZIP(), []int{19}
}

func (x *BlockListResp) GetCount() int64 {
	if x != nil {
		return x.Count
	}
	return 0
}

func (x *BlockListResp) GetData() []*BlockListRespInfo {
	if x != nil {
		return x.Data
	}
	return nil
}

type BlockListReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Page        int32  `protobuf:"varint,1,opt,name=page,proto3" json:"page,omitempty"`
	PageSize    int32  `protobuf:"varint,2,opt,name=pageSize,proto3" json:"pageSize,omitempty"`
	SearchType  string `protobuf:"bytes,3,opt,name=searchType,proto3" json:"searchType,omitempty"`
	SearchStart string `protobuf:"bytes,4,opt,name=searchStart,proto3" json:"searchStart,omitempty"`
	SearchEnd   string `protobuf:"bytes,5,opt,name=searchEnd,proto3" json:"searchEnd,omitempty"`
}

func (x *BlockListReq) Reset() {
	*x = BlockListReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_blockchain_blockchain_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BlockListReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BlockListReq) ProtoMessage() {}

func (x *BlockListReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_blockchain_blockchain_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BlockListReq.ProtoReflect.Descriptor instead.
func (*BlockListReq) Descriptor() ([]byte, []int) {
	return file_pb_blockchain_blockchain_proto_rawDescGZIP(), []int{20}
}

func (x *BlockListReq) GetPage() int32 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *BlockListReq) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *BlockListReq) GetSearchType() string {
	if x != nil {
		return x.SearchType
	}
	return ""
}

func (x *BlockListReq) GetSearchStart() string {
	if x != nil {
		return x.SearchStart
	}
	return ""
}

func (x *BlockListReq) GetSearchEnd() string {
	if x != nil {
		return x.SearchEnd
	}
	return ""
}

type HomeDataRes struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Total      uint64                  `protobuf:"varint,1,opt,name=total,proto3" json:"total,omitempty"`
	Normal     uint64                  `protobuf:"varint,2,opt,name=normal,proto3" json:"normal,omitempty"`
	Privacy    uint64                  `protobuf:"varint,3,opt,name=privacy,proto3" json:"privacy,omitempty"`
	Deal       uint64                  `protobuf:"varint,4,opt,name=deal,proto3" json:"deal,omitempty"`
	List       []*HomeDataMessageTrend `protobuf:"bytes,5,rep,name=list,proto3" json:"list,omitempty"`
	NodeStatus []*NodeStatus           `protobuf:"bytes,6,rep,name=nodeStatus,proto3" json:"nodeStatus,omitempty"`
}

func (x *HomeDataRes) Reset() {
	*x = HomeDataRes{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_blockchain_blockchain_proto_msgTypes[21]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *HomeDataRes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HomeDataRes) ProtoMessage() {}

func (x *HomeDataRes) ProtoReflect() protoreflect.Message {
	mi := &file_pb_blockchain_blockchain_proto_msgTypes[21]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HomeDataRes.ProtoReflect.Descriptor instead.
func (*HomeDataRes) Descriptor() ([]byte, []int) {
	return file_pb_blockchain_blockchain_proto_rawDescGZIP(), []int{21}
}

func (x *HomeDataRes) GetTotal() uint64 {
	if x != nil {
		return x.Total
	}
	return 0
}

func (x *HomeDataRes) GetNormal() uint64 {
	if x != nil {
		return x.Normal
	}
	return 0
}

func (x *HomeDataRes) GetPrivacy() uint64 {
	if x != nil {
		return x.Privacy
	}
	return 0
}

func (x *HomeDataRes) GetDeal() uint64 {
	if x != nil {
		return x.Deal
	}
	return 0
}

func (x *HomeDataRes) GetList() []*HomeDataMessageTrend {
	if x != nil {
		return x.List
	}
	return nil
}

func (x *HomeDataRes) GetNodeStatus() []*NodeStatus {
	if x != nil {
		return x.NodeStatus
	}
	return nil
}

type NodeStatus struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name    string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	Status  bool   `protobuf:"varint,2,opt,name=status,proto3" json:"status,omitempty"`
	Fastest string `protobuf:"bytes,3,opt,name=fastest,proto3" json:"fastest,omitempty"`
}

func (x *NodeStatus) Reset() {
	*x = NodeStatus{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_blockchain_blockchain_proto_msgTypes[22]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *NodeStatus) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NodeStatus) ProtoMessage() {}

func (x *NodeStatus) ProtoReflect() protoreflect.Message {
	mi := &file_pb_blockchain_blockchain_proto_msgTypes[22]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NodeStatus.ProtoReflect.Descriptor instead.
func (*NodeStatus) Descriptor() ([]byte, []int) {
	return file_pb_blockchain_blockchain_proto_rawDescGZIP(), []int{22}
}

func (x *NodeStatus) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *NodeStatus) GetStatus() bool {
	if x != nil {
		return x.Status
	}
	return false
}

func (x *NodeStatus) GetFastest() string {
	if x != nil {
		return x.Fastest
	}
	return ""
}

type HomeDataMessageTrend struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Date    string `protobuf:"bytes,1,opt,name=date,proto3" json:"date,omitempty"`
	Total   uint64 `protobuf:"varint,2,opt,name=total,proto3" json:"total,omitempty"`
	Normal  uint64 `protobuf:"varint,3,opt,name=normal,proto3" json:"normal,omitempty"`
	Privacy uint64 `protobuf:"varint,4,opt,name=privacy,proto3" json:"privacy,omitempty"`
}

func (x *HomeDataMessageTrend) Reset() {
	*x = HomeDataMessageTrend{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_blockchain_blockchain_proto_msgTypes[23]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *HomeDataMessageTrend) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HomeDataMessageTrend) ProtoMessage() {}

func (x *HomeDataMessageTrend) ProtoReflect() protoreflect.Message {
	mi := &file_pb_blockchain_blockchain_proto_msgTypes[23]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HomeDataMessageTrend.ProtoReflect.Descriptor instead.
func (*HomeDataMessageTrend) Descriptor() ([]byte, []int) {
	return file_pb_blockchain_blockchain_proto_rawDescGZIP(), []int{23}
}

func (x *HomeDataMessageTrend) GetDate() string {
	if x != nil {
		return x.Date
	}
	return ""
}

func (x *HomeDataMessageTrend) GetTotal() uint64 {
	if x != nil {
		return x.Total
	}
	return 0
}

func (x *HomeDataMessageTrend) GetNormal() uint64 {
	if x != nil {
		return x.Normal
	}
	return 0
}

func (x *HomeDataMessageTrend) GetPrivacy() uint64 {
	if x != nil {
		return x.Privacy
	}
	return 0
}

type DefaultReturn struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id uint64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
}

func (x *DefaultReturn) Reset() {
	*x = DefaultReturn{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_blockchain_blockchain_proto_msgTypes[24]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DefaultReturn) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DefaultReturn) ProtoMessage() {}

func (x *DefaultReturn) ProtoReflect() protoreflect.Message {
	mi := &file_pb_blockchain_blockchain_proto_msgTypes[24]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DefaultReturn.ProtoReflect.Descriptor instead.
func (*DefaultReturn) Descriptor() ([]byte, []int) {
	return file_pb_blockchain_blockchain_proto_rawDescGZIP(), []int{24}
}

func (x *DefaultReturn) GetId() uint64 {
	if x != nil {
		return x.Id
	}
	return 0
}

type BlockRecordReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id           uint64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	UserId       uint64 `protobuf:"varint,2,opt,name=userId,proto3" json:"userId,omitempty"`
	RecordType   uint32 `protobuf:"varint,3,opt,name=recordType,proto3" json:"recordType,omitempty"`
	FileType     uint32 `protobuf:"varint,4,opt,name=fileType,proto3" json:"fileType,omitempty"`
	FileName     string `protobuf:"bytes,5,opt,name=fileName,proto3" json:"fileName,omitempty"`
	Hash         string `protobuf:"bytes,6,opt,name=hash,proto3" json:"hash,omitempty"`
	SaveTime     string `protobuf:"bytes,7,opt,name=saveTime,proto3" json:"saveTime,omitempty"`
	OutBlockTime int64  `protobuf:"varint,8,opt,name=outBlockTime,proto3" json:"outBlockTime,omitempty"`
	TradingHash  string `protobuf:"bytes,9,opt,name=tradingHash,proto3" json:"tradingHash,omitempty"`
	Ledger       string `protobuf:"bytes,10,opt,name=ledger,proto3" json:"ledger,omitempty"`
	OrderNum     string `protobuf:"bytes,11,opt,name=orderNum,proto3" json:"orderNum,omitempty"`
	Consume      uint64 `protobuf:"varint,12,opt,name=consume,proto3" json:"consume,omitempty"`
	BlockId      string `protobuf:"bytes,13,opt,name=blockId,proto3" json:"blockId,omitempty"`
	BlockHeight  int64  `protobuf:"varint,14,opt,name=blockHeight,proto3" json:"blockHeight,omitempty"`
	FullName     string `protobuf:"bytes,15,opt,name=fullName,proto3" json:"fullName,omitempty"`
	Content      string `protobuf:"bytes,16,opt,name=content,proto3" json:"content,omitempty"`
	Cert         string `protobuf:"bytes,17,opt,name=cert,proto3" json:"cert,omitempty"`
	ConsumeUid   string `protobuf:"bytes,18,opt,name=consumeUid,proto3" json:"consumeUid,omitempty"`
}

func (x *BlockRecordReq) Reset() {
	*x = BlockRecordReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_blockchain_blockchain_proto_msgTypes[25]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BlockRecordReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BlockRecordReq) ProtoMessage() {}

func (x *BlockRecordReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_blockchain_blockchain_proto_msgTypes[25]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BlockRecordReq.ProtoReflect.Descriptor instead.
func (*BlockRecordReq) Descriptor() ([]byte, []int) {
	return file_pb_blockchain_blockchain_proto_rawDescGZIP(), []int{25}
}

func (x *BlockRecordReq) GetId() uint64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *BlockRecordReq) GetUserId() uint64 {
	if x != nil {
		return x.UserId
	}
	return 0
}

func (x *BlockRecordReq) GetRecordType() uint32 {
	if x != nil {
		return x.RecordType
	}
	return 0
}

func (x *BlockRecordReq) GetFileType() uint32 {
	if x != nil {
		return x.FileType
	}
	return 0
}

func (x *BlockRecordReq) GetFileName() string {
	if x != nil {
		return x.FileName
	}
	return ""
}

func (x *BlockRecordReq) GetHash() string {
	if x != nil {
		return x.Hash
	}
	return ""
}

func (x *BlockRecordReq) GetSaveTime() string {
	if x != nil {
		return x.SaveTime
	}
	return ""
}

func (x *BlockRecordReq) GetOutBlockTime() int64 {
	if x != nil {
		return x.OutBlockTime
	}
	return 0
}

func (x *BlockRecordReq) GetTradingHash() string {
	if x != nil {
		return x.TradingHash
	}
	return ""
}

func (x *BlockRecordReq) GetLedger() string {
	if x != nil {
		return x.Ledger
	}
	return ""
}

func (x *BlockRecordReq) GetOrderNum() string {
	if x != nil {
		return x.OrderNum
	}
	return ""
}

func (x *BlockRecordReq) GetConsume() uint64 {
	if x != nil {
		return x.Consume
	}
	return 0
}

func (x *BlockRecordReq) GetBlockId() string {
	if x != nil {
		return x.BlockId
	}
	return ""
}

func (x *BlockRecordReq) GetBlockHeight() int64 {
	if x != nil {
		return x.BlockHeight
	}
	return 0
}

func (x *BlockRecordReq) GetFullName() string {
	if x != nil {
		return x.FullName
	}
	return ""
}

func (x *BlockRecordReq) GetContent() string {
	if x != nil {
		return x.Content
	}
	return ""
}

func (x *BlockRecordReq) GetCert() string {
	if x != nil {
		return x.Cert
	}
	return ""
}

func (x *BlockRecordReq) GetConsumeUid() string {
	if x != nil {
		return x.ConsumeUid
	}
	return ""
}

type WechatRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *WechatRequest) Reset() {
	*x = WechatRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_blockchain_blockchain_proto_msgTypes[26]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *WechatRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WechatRequest) ProtoMessage() {}

func (x *WechatRequest) ProtoReflect() protoreflect.Message {
	mi := &file_pb_blockchain_blockchain_proto_msgTypes[26]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WechatRequest.ProtoReflect.Descriptor instead.
func (*WechatRequest) Descriptor() ([]byte, []int) {
	return file_pb_blockchain_blockchain_proto_rawDescGZIP(), []int{26}
}

type WechatRespond struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Msg string `protobuf:"bytes,1,opt,name=msg,proto3" json:"msg,omitempty"`
}

func (x *WechatRespond) Reset() {
	*x = WechatRespond{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_blockchain_blockchain_proto_msgTypes[27]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *WechatRespond) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WechatRespond) ProtoMessage() {}

func (x *WechatRespond) ProtoReflect() protoreflect.Message {
	mi := &file_pb_blockchain_blockchain_proto_msgTypes[27]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WechatRespond.ProtoReflect.Descriptor instead.
func (*WechatRespond) Descriptor() ([]byte, []int) {
	return file_pb_blockchain_blockchain_proto_rawDescGZIP(), []int{27}
}

func (x *WechatRespond) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

type MemberReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ledger string `protobuf:"bytes,1,opt,name=ledger,proto3" json:"ledger,omitempty"`
}

func (x *MemberReq) Reset() {
	*x = MemberReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_blockchain_blockchain_proto_msgTypes[28]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MemberReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MemberReq) ProtoMessage() {}

func (x *MemberReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_blockchain_blockchain_proto_msgTypes[28]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MemberReq.ProtoReflect.Descriptor instead.
func (*MemberReq) Descriptor() ([]byte, []int) {
	return file_pb_blockchain_blockchain_proto_rawDescGZIP(), []int{28}
}

func (x *MemberReq) GetLedger() string {
	if x != nil {
		return x.Ledger
	}
	return ""
}

type MemberList struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	MemberList []*Member `protobuf:"bytes,1,rep,name=memberList,proto3" json:"memberList,omitempty"`
	Breaked    bool      `protobuf:"varint,2,opt,name=breaked,proto3" json:"breaked,omitempty"`
}

func (x *MemberList) Reset() {
	*x = MemberList{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_blockchain_blockchain_proto_msgTypes[29]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MemberList) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MemberList) ProtoMessage() {}

func (x *MemberList) ProtoReflect() protoreflect.Message {
	mi := &file_pb_blockchain_blockchain_proto_msgTypes[29]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MemberList.ProtoReflect.Descriptor instead.
func (*MemberList) Descriptor() ([]byte, []int) {
	return file_pb_blockchain_blockchain_proto_rawDescGZIP(), []int{29}
}

func (x *MemberList) GetMemberList() []*Member {
	if x != nil {
		return x.MemberList
	}
	return nil
}

func (x *MemberList) GetBreaked() bool {
	if x != nil {
		return x.Breaked
	}
	return false
}

type Member struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id         string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	Port       int32  `protobuf:"varint,2,opt,name=port,proto3" json:"port,omitempty"`
	ShownName  string `protobuf:"bytes,3,opt,name=shownName,proto3" json:"shownName,omitempty"`
	InAddr     string `protobuf:"bytes,4,opt,name=inAddr,proto3" json:"inAddr,omitempty"`
	Height     int32  `protobuf:"varint,5,opt,name=height,proto3" json:"height,omitempty"`
	HashType   string `protobuf:"bytes,6,opt,name=hashType,proto3" json:"hashType,omitempty"`
	Consensus  string `protobuf:"bytes,7,opt,name=consensus,proto3" json:"consensus,omitempty"`
	LicOutTime int64  `protobuf:"varint,8,opt,name=licOutTime,proto3" json:"licOutTime,omitempty"`
}

func (x *Member) Reset() {
	*x = Member{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_blockchain_blockchain_proto_msgTypes[30]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Member) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Member) ProtoMessage() {}

func (x *Member) ProtoReflect() protoreflect.Message {
	mi := &file_pb_blockchain_blockchain_proto_msgTypes[30]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Member.ProtoReflect.Descriptor instead.
func (*Member) Descriptor() ([]byte, []int) {
	return file_pb_blockchain_blockchain_proto_rawDescGZIP(), []int{30}
}

func (x *Member) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *Member) GetPort() int32 {
	if x != nil {
		return x.Port
	}
	return 0
}

func (x *Member) GetShownName() string {
	if x != nil {
		return x.ShownName
	}
	return ""
}

func (x *Member) GetInAddr() string {
	if x != nil {
		return x.InAddr
	}
	return ""
}

func (x *Member) GetHeight() int32 {
	if x != nil {
		return x.Height
	}
	return 0
}

func (x *Member) GetHashType() string {
	if x != nil {
		return x.HashType
	}
	return ""
}

func (x *Member) GetConsensus() string {
	if x != nil {
		return x.Consensus
	}
	return ""
}

func (x *Member) GetLicOutTime() int64 {
	if x != nil {
		return x.LicOutTime
	}
	return 0
}

type MemberListResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	State   int32       `protobuf:"varint,1,opt,name=state,proto3" json:"state,omitempty"`
	Message string      `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	Data    *MemberList `protobuf:"bytes,3,opt,name=data,proto3" json:"data,omitempty"`
	Breaked bool        `protobuf:"varint,4,opt,name=breaked,proto3" json:"breaked,omitempty"`
}

func (x *MemberListResponse) Reset() {
	*x = MemberListResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_blockchain_blockchain_proto_msgTypes[31]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MemberListResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MemberListResponse) ProtoMessage() {}

func (x *MemberListResponse) ProtoReflect() protoreflect.Message {
	mi := &file_pb_blockchain_blockchain_proto_msgTypes[31]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MemberListResponse.ProtoReflect.Descriptor instead.
func (*MemberListResponse) Descriptor() ([]byte, []int) {
	return file_pb_blockchain_blockchain_proto_rawDescGZIP(), []int{31}
}

func (x *MemberListResponse) GetState() int32 {
	if x != nil {
		return x.State
	}
	return 0
}

func (x *MemberListResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *MemberListResponse) GetData() *MemberList {
	if x != nil {
		return x.Data
	}
	return nil
}

func (x *MemberListResponse) GetBreaked() bool {
	if x != nil {
		return x.Breaked
	}
	return false
}

type LedgerMember struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id        string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	Port      int32  `protobuf:"varint,2,opt,name=port,proto3" json:"port,omitempty"`
	ShownName string `protobuf:"bytes,3,opt,name=shownName,proto3" json:"shownName,omitempty"`
	InAddr    string `protobuf:"bytes,4,opt,name=inAddr,proto3" json:"inAddr,omitempty"`
}

func (x *LedgerMember) Reset() {
	*x = LedgerMember{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_blockchain_blockchain_proto_msgTypes[32]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LedgerMember) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LedgerMember) ProtoMessage() {}

func (x *LedgerMember) ProtoReflect() protoreflect.Message {
	mi := &file_pb_blockchain_blockchain_proto_msgTypes[32]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LedgerMember.ProtoReflect.Descriptor instead.
func (*LedgerMember) Descriptor() ([]byte, []int) {
	return file_pb_blockchain_blockchain_proto_rawDescGZIP(), []int{32}
}

func (x *LedgerMember) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *LedgerMember) GetPort() int32 {
	if x != nil {
		return x.Port
	}
	return 0
}

func (x *LedgerMember) GetShownName() string {
	if x != nil {
		return x.ShownName
	}
	return ""
}

func (x *LedgerMember) GetInAddr() string {
	if x != nil {
		return x.InAddr
	}
	return ""
}

type Ledger struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name       string          `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	MemberList []*LedgerMember `protobuf:"bytes,2,rep,name=memberList,proto3" json:"memberList,omitempty"`
	HashType   string          `protobuf:"bytes,3,opt,name=hashType,proto3" json:"hashType,omitempty"`
	Cons       string          `protobuf:"bytes,4,opt,name=cons,proto3" json:"cons,omitempty"`
	TimeStamp  int64           `protobuf:"varint,5,opt,name=timeStamp,proto3" json:"timeStamp,omitempty"`
	Id         string          `protobuf:"bytes,6,opt,name=id,proto3" json:"id,omitempty"`
}

func (x *Ledger) Reset() {
	*x = Ledger{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_blockchain_blockchain_proto_msgTypes[33]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Ledger) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Ledger) ProtoMessage() {}

func (x *Ledger) ProtoReflect() protoreflect.Message {
	mi := &file_pb_blockchain_blockchain_proto_msgTypes[33]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Ledger.ProtoReflect.Descriptor instead.
func (*Ledger) Descriptor() ([]byte, []int) {
	return file_pb_blockchain_blockchain_proto_rawDescGZIP(), []int{33}
}

func (x *Ledger) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *Ledger) GetMemberList() []*LedgerMember {
	if x != nil {
		return x.MemberList
	}
	return nil
}

func (x *Ledger) GetHashType() string {
	if x != nil {
		return x.HashType
	}
	return ""
}

func (x *Ledger) GetCons() string {
	if x != nil {
		return x.Cons
	}
	return ""
}

func (x *Ledger) GetTimeStamp() int64 {
	if x != nil {
		return x.TimeStamp
	}
	return 0
}

func (x *Ledger) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

type LedgerListResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	State   int32     `protobuf:"varint,1,opt,name=state,proto3" json:"state,omitempty"`
	Message string    `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	Ledgers []*Ledger `protobuf:"bytes,3,rep,name=ledgers,proto3" json:"ledgers,omitempty"`
	Number  int32     `protobuf:"varint,4,opt,name=number,proto3" json:"number,omitempty"`
}

func (x *LedgerListResponse) Reset() {
	*x = LedgerListResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_blockchain_blockchain_proto_msgTypes[34]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LedgerListResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LedgerListResponse) ProtoMessage() {}

func (x *LedgerListResponse) ProtoReflect() protoreflect.Message {
	mi := &file_pb_blockchain_blockchain_proto_msgTypes[34]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LedgerListResponse.ProtoReflect.Descriptor instead.
func (*LedgerListResponse) Descriptor() ([]byte, []int) {
	return file_pb_blockchain_blockchain_proto_rawDescGZIP(), []int{34}
}

func (x *LedgerListResponse) GetState() int32 {
	if x != nil {
		return x.State
	}
	return 0
}

func (x *LedgerListResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *LedgerListResponse) GetLedgers() []*Ledger {
	if x != nil {
		return x.Ledgers
	}
	return nil
}

func (x *LedgerListResponse) GetNumber() int32 {
	if x != nil {
		return x.Number
	}
	return 0
}

type StoreQueryReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TxId     string `protobuf:"bytes,1,opt,name=txId,proto3" json:"txId,omitempty"`
	PriKey   string `protobuf:"bytes,2,opt,name=priKey,proto3" json:"priKey,omitempty"`
	Password string `protobuf:"bytes,3,opt,name=password,proto3" json:"password,omitempty"`
}

func (x *StoreQueryReq) Reset() {
	*x = StoreQueryReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_blockchain_blockchain_proto_msgTypes[35]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StoreQueryReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StoreQueryReq) ProtoMessage() {}

func (x *StoreQueryReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_blockchain_blockchain_proto_msgTypes[35]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StoreQueryReq.ProtoReflect.Descriptor instead.
func (*StoreQueryReq) Descriptor() ([]byte, []int) {
	return file_pb_blockchain_blockchain_proto_rawDescGZIP(), []int{35}
}

func (x *StoreQueryReq) GetTxId() string {
	if x != nil {
		return x.TxId
	}
	return ""
}

func (x *StoreQueryReq) GetPriKey() string {
	if x != nil {
		return x.PriKey
	}
	return ""
}

func (x *StoreQueryReq) GetPassword() string {
	if x != nil {
		return x.Password
	}
	return ""
}

type StoreQueryRes struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	State   uint64 `protobuf:"varint,1,opt,name=state,proto3" json:"state,omitempty"`
	Message string `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	Data    string `protobuf:"bytes,3,opt,name=data,proto3" json:"data,omitempty"`
}

func (x *StoreQueryRes) Reset() {
	*x = StoreQueryRes{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_blockchain_blockchain_proto_msgTypes[36]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StoreQueryRes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StoreQueryRes) ProtoMessage() {}

func (x *StoreQueryRes) ProtoReflect() protoreflect.Message {
	mi := &file_pb_blockchain_blockchain_proto_msgTypes[36]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StoreQueryRes.ProtoReflect.Descriptor instead.
func (*StoreQueryRes) Descriptor() ([]byte, []int) {
	return file_pb_blockchain_blockchain_proto_rawDescGZIP(), []int{36}
}

func (x *StoreQueryRes) GetState() uint64 {
	if x != nil {
		return x.State
	}
	return 0
}

func (x *StoreQueryRes) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *StoreQueryRes) GetData() string {
	if x != nil {
		return x.Data
	}
	return ""
}

type BlockHeightRes struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status  uint64 `protobuf:"varint,1,opt,name=status,proto3" json:"status,omitempty"`
	Message string `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	Data    uint64 `protobuf:"varint,3,opt,name=data,proto3" json:"data,omitempty"`
}

func (x *BlockHeightRes) Reset() {
	*x = BlockHeightRes{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_blockchain_blockchain_proto_msgTypes[37]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BlockHeightRes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BlockHeightRes) ProtoMessage() {}

func (x *BlockHeightRes) ProtoReflect() protoreflect.Message {
	mi := &file_pb_blockchain_blockchain_proto_msgTypes[37]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BlockHeightRes.ProtoReflect.Descriptor instead.
func (*BlockHeightRes) Descriptor() ([]byte, []int) {
	return file_pb_blockchain_blockchain_proto_rawDescGZIP(), []int{37}
}

func (x *BlockHeightRes) GetStatus() uint64 {
	if x != nil {
		return x.Status
	}
	return 0
}

func (x *BlockHeightRes) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *BlockHeightRes) GetData() uint64 {
	if x != nil {
		return x.Data
	}
	return 0
}

type BlockHeight struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	BlockId string `protobuf:"bytes,1,opt,name=BlockId,proto3" json:"BlockId,omitempty"`
}

func (x *BlockHeight) Reset() {
	*x = BlockHeight{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_blockchain_blockchain_proto_msgTypes[38]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BlockHeight) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BlockHeight) ProtoMessage() {}

func (x *BlockHeight) ProtoReflect() protoreflect.Message {
	mi := &file_pb_blockchain_blockchain_proto_msgTypes[38]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BlockHeight.ProtoReflect.Descriptor instead.
func (*BlockHeight) Descriptor() ([]byte, []int) {
	return file_pb_blockchain_blockchain_proto_rawDescGZIP(), []int{38}
}

func (x *BlockHeight) GetBlockId() string {
	if x != nil {
		return x.BlockId
	}
	return ""
}

type PubKeys struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PubKeys string `protobuf:"bytes,1,opt,name=pubKeys,proto3" json:"pubKeys,omitempty"`
}

func (x *PubKeys) Reset() {
	*x = PubKeys{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_blockchain_blockchain_proto_msgTypes[39]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PubKeys) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PubKeys) ProtoMessage() {}

func (x *PubKeys) ProtoReflect() protoreflect.Message {
	mi := &file_pb_blockchain_blockchain_proto_msgTypes[39]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PubKeys.ProtoReflect.Descriptor instead.
func (*PubKeys) Descriptor() ([]byte, []int) {
	return file_pb_blockchain_blockchain_proto_rawDescGZIP(), []int{39}
}

func (x *PubKeys) GetPubKeys() string {
	if x != nil {
		return x.PubKeys
	}
	return ""
}

type OrdinaryDepositReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Data    string   `protobuf:"bytes,1,opt,name=data,proto3" json:"data,omitempty"`
	PubKeys []string `protobuf:"bytes,2,rep,name=pubKeys,proto3" json:"pubKeys,omitempty"`
}

func (x *OrdinaryDepositReq) Reset() {
	*x = OrdinaryDepositReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_blockchain_blockchain_proto_msgTypes[40]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *OrdinaryDepositReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OrdinaryDepositReq) ProtoMessage() {}

func (x *OrdinaryDepositReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_blockchain_blockchain_proto_msgTypes[40]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OrdinaryDepositReq.ProtoReflect.Descriptor instead.
func (*OrdinaryDepositReq) Descriptor() ([]byte, []int) {
	return file_pb_blockchain_blockchain_proto_rawDescGZIP(), []int{40}
}

func (x *OrdinaryDepositReq) GetData() string {
	if x != nil {
		return x.Data
	}
	return ""
}

func (x *OrdinaryDepositReq) GetPubKeys() []string {
	if x != nil {
		return x.PubKeys
	}
	return nil
}

type OrdinaryDepositRes struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	State   uint64 `protobuf:"varint,1,opt,name=state,proto3" json:"state,omitempty"`
	Message string `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	Data    string `protobuf:"bytes,3,opt,name=data,proto3" json:"data,omitempty"`
}

func (x *OrdinaryDepositRes) Reset() {
	*x = OrdinaryDepositRes{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_blockchain_blockchain_proto_msgTypes[41]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *OrdinaryDepositRes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OrdinaryDepositRes) ProtoMessage() {}

func (x *OrdinaryDepositRes) ProtoReflect() protoreflect.Message {
	mi := &file_pb_blockchain_blockchain_proto_msgTypes[41]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OrdinaryDepositRes.ProtoReflect.Descriptor instead.
func (*OrdinaryDepositRes) Descriptor() ([]byte, []int) {
	return file_pb_blockchain_blockchain_proto_rawDescGZIP(), []int{41}
}

func (x *OrdinaryDepositRes) GetState() uint64 {
	if x != nil {
		return x.State
	}
	return 0
}

func (x *OrdinaryDepositRes) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *OrdinaryDepositRes) GetData() string {
	if x != nil {
		return x.Data
	}
	return ""
}

type BlockHeader struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Version         int32  `protobuf:"varint,1,opt,name=version,proto3" json:"version,omitempty"`
	Height          int64  `protobuf:"varint,2,opt,name=height,proto3" json:"height,omitempty"`
	Timestamp       int64  `protobuf:"varint,3,opt,name=timestamp,proto3" json:"timestamp,omitempty"`
	BlockId         string `protobuf:"bytes,4,opt,name=blockId,proto3" json:"blockId,omitempty"`
	PreviousId      string `protobuf:"bytes,5,opt,name=previousId,proto3" json:"previousId,omitempty"`
	WorldStateRoot  string `protobuf:"bytes,6,opt,name=worldStateRoot,proto3" json:"worldStateRoot,omitempty"`
	TransactionRoot string `protobuf:"bytes,7,opt,name=transactionRoot,proto3" json:"transactionRoot,omitempty"`
}

func (x *BlockHeader) Reset() {
	*x = BlockHeader{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_blockchain_blockchain_proto_msgTypes[42]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BlockHeader) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BlockHeader) ProtoMessage() {}

func (x *BlockHeader) ProtoReflect() protoreflect.Message {
	mi := &file_pb_blockchain_blockchain_proto_msgTypes[42]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BlockHeader.ProtoReflect.Descriptor instead.
func (*BlockHeader) Descriptor() ([]byte, []int) {
	return file_pb_blockchain_blockchain_proto_rawDescGZIP(), []int{42}
}

func (x *BlockHeader) GetVersion() int32 {
	if x != nil {
		return x.Version
	}
	return 0
}

func (x *BlockHeader) GetHeight() int64 {
	if x != nil {
		return x.Height
	}
	return 0
}

func (x *BlockHeader) GetTimestamp() int64 {
	if x != nil {
		return x.Timestamp
	}
	return 0
}

func (x *BlockHeader) GetBlockId() string {
	if x != nil {
		return x.BlockId
	}
	return ""
}

func (x *BlockHeader) GetPreviousId() string {
	if x != nil {
		return x.PreviousId
	}
	return ""
}

func (x *BlockHeader) GetWorldStateRoot() string {
	if x != nil {
		return x.WorldStateRoot
	}
	return ""
}

func (x *BlockHeader) GetTransactionRoot() string {
	if x != nil {
		return x.TransactionRoot
	}
	return ""
}

type DetailsBlockHeader struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Header *BlockHeader `protobuf:"bytes,1,opt,name=header,proto3" json:"header,omitempty"`
	Txs    []string     `protobuf:"bytes,2,rep,name=txs,proto3" json:"txs,omitempty"`
	Extra  string       `protobuf:"bytes,3,opt,name=extra,proto3" json:"extra,omitempty"`
	Raw    string       `protobuf:"bytes,4,opt,name=raw,proto3" json:"raw,omitempty"`
}

func (x *DetailsBlockHeader) Reset() {
	*x = DetailsBlockHeader{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_blockchain_blockchain_proto_msgTypes[43]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DetailsBlockHeader) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DetailsBlockHeader) ProtoMessage() {}

func (x *DetailsBlockHeader) ProtoReflect() protoreflect.Message {
	mi := &file_pb_blockchain_blockchain_proto_msgTypes[43]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DetailsBlockHeader.ProtoReflect.Descriptor instead.
func (*DetailsBlockHeader) Descriptor() ([]byte, []int) {
	return file_pb_blockchain_blockchain_proto_rawDescGZIP(), []int{43}
}

func (x *DetailsBlockHeader) GetHeader() *BlockHeader {
	if x != nil {
		return x.Header
	}
	return nil
}

func (x *DetailsBlockHeader) GetTxs() []string {
	if x != nil {
		return x.Txs
	}
	return nil
}

func (x *DetailsBlockHeader) GetExtra() string {
	if x != nil {
		return x.Extra
	}
	return ""
}

func (x *DetailsBlockHeader) GetRaw() string {
	if x != nil {
		return x.Raw
	}
	return ""
}

type GetBlockDetailsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	State   int32               `protobuf:"varint,1,opt,name=state,proto3" json:"state,omitempty"`
	Message string              `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	Data    *DetailsBlockHeader `protobuf:"bytes,3,opt,name=data,proto3" json:"data,omitempty"`
}

func (x *GetBlockDetailsResponse) Reset() {
	*x = GetBlockDetailsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_blockchain_blockchain_proto_msgTypes[44]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetBlockDetailsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetBlockDetailsResponse) ProtoMessage() {}

func (x *GetBlockDetailsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_pb_blockchain_blockchain_proto_msgTypes[44]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetBlockDetailsResponse.ProtoReflect.Descriptor instead.
func (*GetBlockDetailsResponse) Descriptor() ([]byte, []int) {
	return file_pb_blockchain_blockchain_proto_rawDescGZIP(), []int{44}
}

func (x *GetBlockDetailsResponse) GetState() int32 {
	if x != nil {
		return x.State
	}
	return 0
}

func (x *GetBlockDetailsResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *GetBlockDetailsResponse) GetData() *DetailsBlockHeader {
	if x != nil {
		return x.Data
	}
	return nil
}

type DealDetailHeader struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Version       int32  `protobuf:"varint,1,opt,name=version,proto3" json:"version,omitempty"`
	Type          int32  `protobuf:"varint,2,opt,name=type,proto3" json:"type,omitempty"`
	SubType       int32  `protobuf:"varint,3,opt,name=subType,proto3" json:"subType,omitempty"`
	Timestamp     int64  `protobuf:"varint,4,opt,name=timestamp,proto3" json:"timestamp,omitempty"`
	TransactionId string `protobuf:"bytes,5,opt,name=transactionId,proto3" json:"transactionId,omitempty"`
}

func (x *DealDetailHeader) Reset() {
	*x = DealDetailHeader{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_blockchain_blockchain_proto_msgTypes[45]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DealDetailHeader) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DealDetailHeader) ProtoMessage() {}

func (x *DealDetailHeader) ProtoReflect() protoreflect.Message {
	mi := &file_pb_blockchain_blockchain_proto_msgTypes[45]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DealDetailHeader.ProtoReflect.Descriptor instead.
func (*DealDetailHeader) Descriptor() ([]byte, []int) {
	return file_pb_blockchain_blockchain_proto_rawDescGZIP(), []int{45}
}

func (x *DealDetailHeader) GetVersion() int32 {
	if x != nil {
		return x.Version
	}
	return 0
}

func (x *DealDetailHeader) GetType() int32 {
	if x != nil {
		return x.Type
	}
	return 0
}

func (x *DealDetailHeader) GetSubType() int32 {
	if x != nil {
		return x.SubType
	}
	return 0
}

func (x *DealDetailHeader) GetTimestamp() int64 {
	if x != nil {
		return x.Timestamp
	}
	return 0
}

func (x *DealDetailHeader) GetTransactionId() string {
	if x != nil {
		return x.TransactionId
	}
	return ""
}

type DealDetailStore struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	StoreData string `protobuf:"bytes,1,opt,name=storeData,proto3" json:"storeData,omitempty"` //  string extra = 2;
}

func (x *DealDetailStore) Reset() {
	*x = DealDetailStore{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_blockchain_blockchain_proto_msgTypes[46]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DealDetailStore) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DealDetailStore) ProtoMessage() {}

func (x *DealDetailStore) ProtoReflect() protoreflect.Message {
	mi := &file_pb_blockchain_blockchain_proto_msgTypes[46]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DealDetailStore.ProtoReflect.Descriptor instead.
func (*DealDetailStore) Descriptor() ([]byte, []int) {
	return file_pb_blockchain_blockchain_proto_rawDescGZIP(), []int{46}
}

func (x *DealDetailStore) GetStoreData() string {
	if x != nil {
		return x.StoreData
	}
	return ""
}

type FindDealDetailResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	State   int32             `protobuf:"varint,1,opt,name=state,proto3" json:"state,omitempty"`
	Message string            `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	Header  *DealDetailHeader `protobuf:"bytes,3,opt,name=header,proto3" json:"header,omitempty"`
	Store   *DealDetailStore  `protobuf:"bytes,4,opt,name=store,proto3" json:"store,omitempty"`
	Raw     string            `protobuf:"bytes,5,opt,name=raw,proto3" json:"raw,omitempty"`
}

func (x *FindDealDetailResponse) Reset() {
	*x = FindDealDetailResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_blockchain_blockchain_proto_msgTypes[47]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FindDealDetailResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FindDealDetailResponse) ProtoMessage() {}

func (x *FindDealDetailResponse) ProtoReflect() protoreflect.Message {
	mi := &file_pb_blockchain_blockchain_proto_msgTypes[47]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FindDealDetailResponse.ProtoReflect.Descriptor instead.
func (*FindDealDetailResponse) Descriptor() ([]byte, []int) {
	return file_pb_blockchain_blockchain_proto_rawDescGZIP(), []int{47}
}

func (x *FindDealDetailResponse) GetState() int32 {
	if x != nil {
		return x.State
	}
	return 0
}

func (x *FindDealDetailResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *FindDealDetailResponse) GetHeader() *DealDetailHeader {
	if x != nil {
		return x.Header
	}
	return nil
}

func (x *FindDealDetailResponse) GetStore() *DealDetailStore {
	if x != nil {
		return x.Store
	}
	return nil
}

func (x *FindDealDetailResponse) GetRaw() string {
	if x != nil {
		return x.Raw
	}
	return ""
}

type RawHeader struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Version       int32  `protobuf:"varint,1,opt,name=version,proto3" json:"version,omitempty"`
	Type          int32  `protobuf:"varint,2,opt,name=type,proto3" json:"type,omitempty"`
	SubType       int32  `protobuf:"varint,3,opt,name=subType,proto3" json:"subType,omitempty"`
	Timestamp     int64  `protobuf:"varint,4,opt,name=timestamp,proto3" json:"timestamp,omitempty"`
	TransactionId string `protobuf:"bytes,5,opt,name=transactionId,proto3" json:"transactionId,omitempty"`
}

func (x *RawHeader) Reset() {
	*x = RawHeader{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_blockchain_blockchain_proto_msgTypes[48]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RawHeader) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RawHeader) ProtoMessage() {}

func (x *RawHeader) ProtoReflect() protoreflect.Message {
	mi := &file_pb_blockchain_blockchain_proto_msgTypes[48]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RawHeader.ProtoReflect.Descriptor instead.
func (*RawHeader) Descriptor() ([]byte, []int) {
	return file_pb_blockchain_blockchain_proto_rawDescGZIP(), []int{48}
}

func (x *RawHeader) GetVersion() int32 {
	if x != nil {
		return x.Version
	}
	return 0
}

func (x *RawHeader) GetType() int32 {
	if x != nil {
		return x.Type
	}
	return 0
}

func (x *RawHeader) GetSubType() int32 {
	if x != nil {
		return x.SubType
	}
	return 0
}

func (x *RawHeader) GetTimestamp() int64 {
	if x != nil {
		return x.Timestamp
	}
	return 0
}

func (x *RawHeader) GetTransactionId() string {
	if x != nil {
		return x.TransactionId
	}
	return ""
}

type RawTxProof struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Left  []string `protobuf:"bytes,1,rep,name=left,proto3" json:"left,omitempty"`
	Right []string `protobuf:"bytes,2,rep,name=right,proto3" json:"right,omitempty"`
}

func (x *RawTxProof) Reset() {
	*x = RawTxProof{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_blockchain_blockchain_proto_msgTypes[49]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RawTxProof) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RawTxProof) ProtoMessage() {}

func (x *RawTxProof) ProtoReflect() protoreflect.Message {
	mi := &file_pb_blockchain_blockchain_proto_msgTypes[49]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RawTxProof.ProtoReflect.Descriptor instead.
func (*RawTxProof) Descriptor() ([]byte, []int) {
	return file_pb_blockchain_blockchain_proto_rawDescGZIP(), []int{49}
}

func (x *RawTxProof) GetLeft() []string {
	if x != nil {
		return x.Left
	}
	return nil
}

func (x *RawTxProof) GetRight() []string {
	if x != nil {
		return x.Right
	}
	return nil
}

type FindDealRawResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	State   int32       `protobuf:"varint,1,opt,name=state,proto3" json:"state,omitempty"`
	Message string      `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	Header  *RawHeader  `protobuf:"bytes,3,opt,name=header,proto3" json:"header,omitempty"`
	Data    string      `protobuf:"bytes,4,opt,name=data,proto3" json:"data,omitempty"`
	Pubkey  string      `protobuf:"bytes,5,opt,name=pubkey,proto3" json:"pubkey,omitempty"`
	Sign    string      `protobuf:"bytes,6,opt,name=sign,proto3" json:"sign,omitempty"`
	Raw     string      `protobuf:"bytes,9,opt,name=raw,proto3" json:"raw,omitempty"`
	Txproof *RawTxProof `protobuf:"bytes,10,opt,name=txproof,proto3" json:"txproof,omitempty"`
}

func (x *FindDealRawResponse) Reset() {
	*x = FindDealRawResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_blockchain_blockchain_proto_msgTypes[50]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FindDealRawResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FindDealRawResponse) ProtoMessage() {}

func (x *FindDealRawResponse) ProtoReflect() protoreflect.Message {
	mi := &file_pb_blockchain_blockchain_proto_msgTypes[50]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FindDealRawResponse.ProtoReflect.Descriptor instead.
func (*FindDealRawResponse) Descriptor() ([]byte, []int) {
	return file_pb_blockchain_blockchain_proto_rawDescGZIP(), []int{50}
}

func (x *FindDealRawResponse) GetState() int32 {
	if x != nil {
		return x.State
	}
	return 0
}

func (x *FindDealRawResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *FindDealRawResponse) GetHeader() *RawHeader {
	if x != nil {
		return x.Header
	}
	return nil
}

func (x *FindDealRawResponse) GetData() string {
	if x != nil {
		return x.Data
	}
	return ""
}

func (x *FindDealRawResponse) GetPubkey() string {
	if x != nil {
		return x.Pubkey
	}
	return ""
}

func (x *FindDealRawResponse) GetSign() string {
	if x != nil {
		return x.Sign
	}
	return ""
}

func (x *FindDealRawResponse) GetRaw() string {
	if x != nil {
		return x.Raw
	}
	return ""
}

func (x *FindDealRawResponse) GetTxproof() *RawTxProof {
	if x != nil {
		return x.Txproof
	}
	return nil
}

var File_pb_blockchain_blockchain_proto protoreflect.FileDescriptor

var file_pb_blockchain_blockchain_proto_rawDesc = []byte{
	0x0a, 0x1e, 0x70, 0x62, 0x2f, 0x62, 0x6c, 0x6f, 0x63, 0x6b, 0x63, 0x68, 0x61, 0x69, 0x6e, 0x2f,
	0x62, 0x6c, 0x6f, 0x63, 0x6b, 0x63, 0x68, 0x61, 0x69, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x12, 0x0a, 0x62, 0x6c, 0x6f, 0x63, 0x6b, 0x63, 0x68, 0x61, 0x69, 0x6e, 0x22, 0x3a, 0x0a, 0x0b,
	0x4e, 0x6f, 0x42, 0x6c, 0x6f, 0x63, 0x6b, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x2b, 0x0a, 0x04, 0x64,
	0x61, 0x74, 0x61, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x62, 0x6c, 0x6f, 0x63,
	0x6b, 0x63, 0x68, 0x61, 0x69, 0x6e, 0x2e, 0x4e, 0x6f, 0x42, 0x6c, 0x6f, 0x63, 0x6b, 0x49, 0x6e,
	0x66, 0x6f, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x22, 0xd9, 0x01, 0x0a, 0x0b, 0x4e, 0x6f, 0x42,
	0x6c, 0x6f, 0x63, 0x6b, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x04, 0x52, 0x02, 0x69, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x74, 0x78, 0x48, 0x61,
	0x73, 0x68, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x74, 0x78, 0x48, 0x61, 0x73, 0x68,
	0x12, 0x16, 0x0a, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x04,
	0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x12, 0x1e, 0x0a, 0x0a, 0x72, 0x65, 0x63, 0x6f,
	0x72, 0x64, 0x54, 0x79, 0x70, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0a, 0x72, 0x65,
	0x63, 0x6f, 0x72, 0x64, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x66, 0x69, 0x6c, 0x65,
	0x54, 0x79, 0x70, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x04, 0x52, 0x08, 0x66, 0x69, 0x6c, 0x65,
	0x54, 0x79, 0x70, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x18,
	0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x12, 0x1a,
	0x0a, 0x08, 0x66, 0x69, 0x6c, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x08, 0x66, 0x69, 0x6c, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x63, 0x65,
	0x72, 0x74, 0x55, 0x72, 0x6c, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x63, 0x65, 0x72,
	0x74, 0x55, 0x72, 0x6c, 0x22, 0x2a, 0x0a, 0x10, 0x50, 0x65, 0x72, 0x73, 0x6f, 0x6e, 0x61, 0x67,
	0x65, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x71, 0x12, 0x16, 0x0a, 0x06, 0x75, 0x73, 0x65, 0x72,
	0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64,
	0x22, 0x44, 0x0a, 0x10, 0x50, 0x65, 0x72, 0x73, 0x6f, 0x6e, 0x61, 0x67, 0x65, 0x49, 0x6e, 0x66,
	0x6f, 0x52, 0x65, 0x73, 0x12, 0x16, 0x0a, 0x06, 0x6e, 0x6f, 0x72, 0x6d, 0x61, 0x6c, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x6e, 0x6f, 0x72, 0x6d, 0x61, 0x6c, 0x12, 0x18, 0x0a, 0x07,
	0x70, 0x72, 0x69, 0x76, 0x61, 0x63, 0x79, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x07, 0x70,
	0x72, 0x69, 0x76, 0x61, 0x63, 0x79, 0x22, 0x59, 0x0a, 0x11, 0x4b, 0x65, 0x65, 0x70, 0x52, 0x65,
	0x63, 0x6f, 0x72, 0x64, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x12, 0x14, 0x0a, 0x05, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x12, 0x2e, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x1a, 0x2e, 0x62, 0x6c, 0x6f, 0x63, 0x6b, 0x63, 0x68, 0x61, 0x69, 0x6e, 0x2e, 0x4b, 0x65, 0x65,
	0x70, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x44, 0x61, 0x74, 0x61, 0x52, 0x04, 0x64, 0x61, 0x74,
	0x61, 0x22, 0xb8, 0x01, 0x0a, 0x0e, 0x4b, 0x65, 0x65, 0x70, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64,
	0x44, 0x61, 0x74, 0x61, 0x12, 0x1a, 0x0a, 0x08, 0x66, 0x69, 0x6c, 0x65, 0x4e, 0x61, 0x6d, 0x65,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x66, 0x69, 0x6c, 0x65, 0x4e, 0x61, 0x6d, 0x65,
	0x12, 0x1e, 0x0a, 0x0a, 0x72, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x54, 0x79, 0x70, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x0d, 0x52, 0x0a, 0x72, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x54, 0x79, 0x70, 0x65,
	0x12, 0x1a, 0x0a, 0x08, 0x66, 0x75, 0x6c, 0x6c, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x08, 0x66, 0x75, 0x6c, 0x6c, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x16, 0x0a, 0x06,
	0x74, 0x78, 0x48, 0x61, 0x73, 0x68, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x74, 0x78,
	0x48, 0x61, 0x73, 0x68, 0x12, 0x1c, 0x0a, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41,
	0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64,
	0x41, 0x74, 0x12, 0x18, 0x0a, 0x07, 0x63, 0x65, 0x72, 0x74, 0x55, 0x72, 0x6c, 0x18, 0x06, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x07, 0x63, 0x65, 0x72, 0x74, 0x55, 0x72, 0x6c, 0x22, 0xdf, 0x01, 0x0a,
	0x11, 0x4b, 0x65, 0x65, 0x70, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x4c, 0x69, 0x73, 0x74, 0x52,
	0x65, 0x71, 0x12, 0x16, 0x0a, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x04, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61,
	0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x16,
	0x0a, 0x06, 0x74, 0x78, 0x48, 0x61, 0x73, 0x68, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06,
	0x74, 0x78, 0x48, 0x61, 0x73, 0x68, 0x12, 0x1e, 0x0a, 0x0a, 0x72, 0x65, 0x63, 0x6f, 0x72, 0x64,
	0x54, 0x79, 0x70, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0a, 0x72, 0x65, 0x63, 0x6f,
	0x72, 0x64, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1c, 0x0a, 0x09, 0x73, 0x74, 0x61, 0x72, 0x74, 0x44,
	0x61, 0x74, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x73, 0x74, 0x61, 0x72, 0x74,
	0x44, 0x61, 0x74, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x65, 0x6e, 0x64, 0x44, 0x61, 0x74, 0x65, 0x18,
	0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x65, 0x6e, 0x64, 0x44, 0x61, 0x74, 0x65, 0x12, 0x12,
	0x0a, 0x04, 0x70, 0x61, 0x67, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x70, 0x61,
	0x67, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x61, 0x67, 0x65, 0x53, 0x69, 0x7a, 0x65, 0x18, 0x08,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x70, 0x61, 0x67, 0x65, 0x53, 0x69, 0x7a, 0x65, 0x22, 0x41,
	0x0a, 0x0b, 0x42, 0x6c, 0x6f, 0x63, 0x6b, 0x54, 0x78, 0x44, 0x61, 0x74, 0x61, 0x12, 0x12, 0x0a,
	0x04, 0x68, 0x61, 0x73, 0x68, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x68, 0x61, 0x73,
	0x68, 0x12, 0x1e, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x44, 0x61, 0x74, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x44, 0x61, 0x74,
	0x65, 0x22, 0x50, 0x0a, 0x0b, 0x42, 0x6c, 0x6f, 0x63, 0x6b, 0x54, 0x78, 0x52, 0x65, 0x73, 0x70,
	0x12, 0x14, 0x0a, 0x05, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x05, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x2b, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x02,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x62, 0x6c, 0x6f, 0x63, 0x6b, 0x63, 0x68, 0x61, 0x69,
	0x6e, 0x2e, 0x42, 0x6c, 0x6f, 0x63, 0x6b, 0x54, 0x78, 0x44, 0x61, 0x74, 0x61, 0x52, 0x04, 0x64,
	0x61, 0x74, 0x61, 0x22, 0x5d, 0x0a, 0x11, 0x42, 0x6c, 0x6f, 0x63, 0x6b, 0x44, 0x61, 0x74, 0x61,
	0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x73, 0x70, 0x12, 0x12, 0x0a, 0x04, 0x68, 0x61, 0x73, 0x68,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x68, 0x61, 0x73, 0x68, 0x12, 0x1e, 0x0a, 0x0a,
	0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x44, 0x61, 0x74, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x44, 0x61, 0x74, 0x65, 0x12, 0x14, 0x0a, 0x05,
	0x74, 0x66, 0x6e, 0x75, 0x6d, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x74, 0x66, 0x6e,
	0x75, 0x6d, 0x22, 0xca, 0x01, 0x0a, 0x0d, 0x42, 0x6c, 0x6f, 0x63, 0x6b, 0x49, 0x6e, 0x66, 0x6f,
	0x52, 0x65, 0x73, 0x70, 0x12, 0x1c, 0x0a, 0x09, 0x62, 0x6c, 0x6f, 0x63, 0x6b, 0x48, 0x61, 0x73,
	0x68, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x62, 0x6c, 0x6f, 0x63, 0x6b, 0x48, 0x61,
	0x73, 0x68, 0x12, 0x20, 0x0a, 0x0b, 0x62, 0x6c, 0x6f, 0x63, 0x6b, 0x48, 0x65, 0x69, 0x67, 0x68,
	0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0b, 0x62, 0x6c, 0x6f, 0x63, 0x6b, 0x48, 0x65,
	0x69, 0x67, 0x68, 0x74, 0x12, 0x1e, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x44, 0x61,
	0x74, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65,
	0x44, 0x61, 0x74, 0x65, 0x12, 0x22, 0x0a, 0x0c, 0x62, 0x6c, 0x6f, 0x63, 0x6b, 0x4f, 0x75, 0x74,
	0x54, 0x69, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0c, 0x62, 0x6c, 0x6f, 0x63,
	0x6b, 0x4f, 0x75, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x35, 0x0a, 0x06, 0x74, 0x78, 0x4c, 0x69,
	0x73, 0x74, 0x18, 0x05, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x62, 0x6c, 0x6f, 0x63, 0x6b,
	0x63, 0x68, 0x61, 0x69, 0x6e, 0x2e, 0x42, 0x6c, 0x6f, 0x63, 0x6b, 0x44, 0x61, 0x74, 0x61, 0x49,
	0x6e, 0x66, 0x6f, 0x52, 0x65, 0x73, 0x70, 0x52, 0x06, 0x74, 0x78, 0x4c, 0x69, 0x73, 0x74, 0x22,
	0x91, 0x02, 0x0a, 0x13, 0x42, 0x6c, 0x6f, 0x63, 0x6b, 0x54, 0x78, 0x44, 0x61, 0x74, 0x61, 0x49,
	0x6e, 0x66, 0x6f, 0x52, 0x65, 0x73, 0x70, 0x12, 0x18, 0x0a, 0x07, 0x74, 0x78, 0x73, 0x48, 0x61,
	0x73, 0x68, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x74, 0x78, 0x73, 0x48, 0x61, 0x73,
	0x68, 0x12, 0x1c, 0x0a, 0x09, 0x62, 0x6c, 0x6f, 0x63, 0x6b, 0x48, 0x61, 0x73, 0x68, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x62, 0x6c, 0x6f, 0x63, 0x6b, 0x48, 0x61, 0x73, 0x68, 0x12,
	0x16, 0x0a, 0x06, 0x68, 0x65, 0x69, 0x67, 0x68, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x04, 0x52,
	0x06, 0x68, 0x65, 0x69, 0x67, 0x68, 0x74, 0x12, 0x20, 0x0a, 0x0b, 0x63, 0x72, 0x65, 0x61, 0x74,
	0x65, 0x64, 0x44, 0x61, 0x74, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x63, 0x72,
	0x65, 0x61, 0x74, 0x65, 0x64, 0x44, 0x61, 0x74, 0x65, 0x12, 0x1e, 0x0a, 0x0a, 0x62, 0x75, 0x73,
	0x69, 0x6e, 0x65, 0x73, 0x73, 0x49, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x62,
	0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x49, 0x64, 0x12, 0x18, 0x0a, 0x07, 0x65, 0x78, 0x74,
	0x44, 0x61, 0x74, 0x61, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x65, 0x78, 0x74, 0x44,
	0x61, 0x74, 0x61, 0x12, 0x18, 0x0a, 0x07, 0x63, 0x65, 0x72, 0x74, 0x55, 0x72, 0x6c, 0x18, 0x07,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x63, 0x65, 0x72, 0x74, 0x55, 0x72, 0x6c, 0x12, 0x18, 0x0a,
	0x07, 0x66, 0x69, 0x6c, 0x65, 0x55, 0x72, 0x6c, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07,
	0x66, 0x69, 0x6c, 0x65, 0x55, 0x72, 0x6c, 0x12, 0x1a, 0x0a, 0x08, 0x66, 0x69, 0x6c, 0x65, 0x48,
	0x61, 0x73, 0x68, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x66, 0x69, 0x6c, 0x65, 0x48,
	0x61, 0x73, 0x68, 0x22, 0x46, 0x0a, 0x0f, 0x42, 0x6c, 0x6f, 0x63, 0x6b, 0x54, 0x78, 0x49, 0x6e,
	0x66, 0x6f, 0x52, 0x65, 0x73, 0x70, 0x12, 0x33, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x62, 0x6c, 0x6f, 0x63, 0x6b, 0x63, 0x68, 0x61, 0x69,
	0x6e, 0x2e, 0x42, 0x6c, 0x6f, 0x63, 0x6b, 0x54, 0x78, 0x44, 0x61, 0x74, 0x61, 0x49, 0x6e, 0x66,
	0x6f, 0x52, 0x65, 0x73, 0x70, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x22, 0x3a, 0x0a, 0x0c, 0x42,
	0x6c, 0x6f, 0x63, 0x6b, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x71, 0x12, 0x12, 0x0a, 0x04, 0x68,
	0x61, 0x73, 0x68, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x68, 0x61, 0x73, 0x68, 0x12,
	0x16, 0x0a, 0x06, 0x68, 0x65, 0x69, 0x67, 0x68, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x04, 0x52,
	0x06, 0x68, 0x65, 0x69, 0x67, 0x68, 0x74, 0x22, 0x4c, 0x0a, 0x0e, 0x42, 0x6c, 0x6f, 0x63, 0x6b,
	0x54, 0x78, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x71, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x02, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x68, 0x61, 0x73,
	0x68, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x68, 0x61, 0x73, 0x68, 0x12, 0x16, 0x0a,
	0x06, 0x68, 0x65, 0x69, 0x67, 0x68, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x04, 0x52, 0x06, 0x68,
	0x65, 0x69, 0x67, 0x68, 0x74, 0x22, 0x9d, 0x01, 0x0a, 0x0f, 0x42, 0x6c, 0x6f, 0x63, 0x6b, 0x54,
	0x78, 0x4c, 0x69, 0x73, 0x74, 0x44, 0x61, 0x74, 0x61, 0x12, 0x16, 0x0a, 0x06, 0x74, 0x78, 0x48,
	0x61, 0x73, 0x68, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x74, 0x78, 0x48, 0x61, 0x73,
	0x68, 0x12, 0x1c, 0x0a, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12,
	0x1e, 0x0a, 0x0a, 0x72, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x54, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x0d, 0x52, 0x0a, 0x72, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x54, 0x79, 0x70, 0x65, 0x12,
	0x18, 0x0a, 0x07, 0x63, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x04,
	0x52, 0x07, 0x63, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x6f, 0x72, 0x64,
	0x65, 0x72, 0x4e, 0x75, 0x6d, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x6f, 0x72, 0x64,
	0x65, 0x72, 0x4e, 0x75, 0x6d, 0x22, 0x58, 0x0a, 0x0f, 0x42, 0x6c, 0x6f, 0x63, 0x6b, 0x54, 0x78,
	0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x70, 0x12, 0x14, 0x0a, 0x05, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x2f,
	0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x62,
	0x6c, 0x6f, 0x63, 0x6b, 0x63, 0x68, 0x61, 0x69, 0x6e, 0x2e, 0x42, 0x6c, 0x6f, 0x63, 0x6b, 0x54,
	0x78, 0x4c, 0x69, 0x73, 0x74, 0x44, 0x61, 0x74, 0x61, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x22,
	0xc8, 0x01, 0x0a, 0x0e, 0x42, 0x6c, 0x6f, 0x63, 0x6b, 0x54, 0x78, 0x4c, 0x69, 0x73, 0x74, 0x52,
	0x65, 0x71, 0x12, 0x12, 0x0a, 0x04, 0x70, 0x61, 0x67, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x04, 0x70, 0x61, 0x67, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x61, 0x67, 0x65, 0x53, 0x69,
	0x7a, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x70, 0x61, 0x67, 0x65, 0x53, 0x69,
	0x7a, 0x65, 0x12, 0x1e, 0x0a, 0x0a, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x49, 0x64,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73,
	0x49, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x74, 0x78, 0x48, 0x61, 0x73, 0x68, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x06, 0x74, 0x78, 0x48, 0x61, 0x73, 0x68, 0x12, 0x16, 0x0a, 0x06, 0x75, 0x73,
	0x65, 0x72, 0x49, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x04, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72,
	0x49, 0x64, 0x12, 0x1c, 0x0a, 0x09, 0x53, 0x74, 0x61, 0x72, 0x74, 0x44, 0x61, 0x74, 0x65, 0x18,
	0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x53, 0x74, 0x61, 0x72, 0x74, 0x44, 0x61, 0x74, 0x65,
	0x12, 0x18, 0x0a, 0x07, 0x45, 0x6e, 0x64, 0x44, 0x61, 0x74, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x07, 0x45, 0x6e, 0x64, 0x44, 0x61, 0x74, 0x65, 0x22, 0x91, 0x01, 0x0a, 0x11, 0x42,
	0x6c, 0x6f, 0x63, 0x6b, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x70, 0x49, 0x6e, 0x66, 0x6f,
	0x12, 0x1c, 0x0a, 0x09, 0x62, 0x6c, 0x6f, 0x63, 0x6b, 0x48, 0x61, 0x73, 0x68, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x09, 0x62, 0x6c, 0x6f, 0x63, 0x6b, 0x48, 0x61, 0x73, 0x68, 0x12, 0x16,
	0x0a, 0x06, 0x68, 0x65, 0x69, 0x67, 0x68, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06,
	0x68, 0x65, 0x69, 0x67, 0x68, 0x74, 0x12, 0x1e, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65,
	0x44, 0x61, 0x74, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x63, 0x72, 0x65, 0x61,
	0x74, 0x65, 0x44, 0x61, 0x74, 0x65, 0x12, 0x26, 0x0a, 0x0e, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61,
	0x63, 0x74, 0x69, 0x6f, 0x6e, 0x4e, 0x75, 0x6d, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0e,
	0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x4e, 0x75, 0x6d, 0x22, 0x58,
	0x0a, 0x0d, 0x42, 0x6c, 0x6f, 0x63, 0x6b, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x70, 0x12,
	0x14, 0x0a, 0x05, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x31, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x02, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x62, 0x6c, 0x6f, 0x63, 0x6b, 0x63, 0x68, 0x61, 0x69, 0x6e,
	0x2e, 0x42, 0x6c, 0x6f, 0x63, 0x6b, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x70, 0x49, 0x6e,
	0x66, 0x6f, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x22, 0x9e, 0x01, 0x0a, 0x0c, 0x42, 0x6c, 0x6f,
	0x63, 0x6b, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x12, 0x12, 0x0a, 0x04, 0x70, 0x61, 0x67,
	0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x70, 0x61, 0x67, 0x65, 0x12, 0x1a, 0x0a,
	0x08, 0x70, 0x61, 0x67, 0x65, 0x53, 0x69, 0x7a, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x08, 0x70, 0x61, 0x67, 0x65, 0x53, 0x69, 0x7a, 0x65, 0x12, 0x1e, 0x0a, 0x0a, 0x73, 0x65, 0x61,
	0x72, 0x63, 0x68, 0x54, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x73,
	0x65, 0x61, 0x72, 0x63, 0x68, 0x54, 0x79, 0x70, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x73, 0x65, 0x61,
	0x72, 0x63, 0x68, 0x53, 0x74, 0x61, 0x72, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b,
	0x73, 0x65, 0x61, 0x72, 0x63, 0x68, 0x53, 0x74, 0x61, 0x72, 0x74, 0x12, 0x1c, 0x0a, 0x09, 0x73,
	0x65, 0x61, 0x72, 0x63, 0x68, 0x45, 0x6e, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09,
	0x73, 0x65, 0x61, 0x72, 0x63, 0x68, 0x45, 0x6e, 0x64, 0x22, 0xd7, 0x01, 0x0a, 0x0b, 0x48, 0x6f,
	0x6d, 0x65, 0x44, 0x61, 0x74, 0x61, 0x52, 0x65, 0x73, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x6f, 0x74,
	0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x12,
	0x16, 0x0a, 0x06, 0x6e, 0x6f, 0x72, 0x6d, 0x61, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x04, 0x52,
	0x06, 0x6e, 0x6f, 0x72, 0x6d, 0x61, 0x6c, 0x12, 0x18, 0x0a, 0x07, 0x70, 0x72, 0x69, 0x76, 0x61,
	0x63, 0x79, 0x18, 0x03, 0x20, 0x01, 0x28, 0x04, 0x52, 0x07, 0x70, 0x72, 0x69, 0x76, 0x61, 0x63,
	0x79, 0x12, 0x12, 0x0a, 0x04, 0x64, 0x65, 0x61, 0x6c, 0x18, 0x04, 0x20, 0x01, 0x28, 0x04, 0x52,
	0x04, 0x64, 0x65, 0x61, 0x6c, 0x12, 0x34, 0x0a, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x05, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x20, 0x2e, 0x62, 0x6c, 0x6f, 0x63, 0x6b, 0x63, 0x68, 0x61, 0x69, 0x6e,
	0x2e, 0x48, 0x6f, 0x6d, 0x65, 0x44, 0x61, 0x74, 0x61, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65,
	0x54, 0x72, 0x65, 0x6e, 0x64, 0x52, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x12, 0x36, 0x0a, 0x0a, 0x6e,
	0x6f, 0x64, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x06, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x16, 0x2e, 0x62, 0x6c, 0x6f, 0x63, 0x6b, 0x63, 0x68, 0x61, 0x69, 0x6e, 0x2e, 0x4e, 0x6f, 0x64,
	0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x0a, 0x6e, 0x6f, 0x64, 0x65, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x22, 0x52, 0x0a, 0x0a, 0x4e, 0x6f, 0x64, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x18, 0x0a,
	0x07, 0x66, 0x61, 0x73, 0x74, 0x65, 0x73, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07,
	0x66, 0x61, 0x73, 0x74, 0x65, 0x73, 0x74, 0x22, 0x72, 0x0a, 0x14, 0x48, 0x6f, 0x6d, 0x65, 0x44,
	0x61, 0x74, 0x61, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x54, 0x72, 0x65, 0x6e, 0x64, 0x12,
	0x12, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x64,
	0x61, 0x74, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x04, 0x52, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x12, 0x16, 0x0a, 0x06, 0x6e, 0x6f, 0x72,
	0x6d, 0x61, 0x6c, 0x18, 0x03, 0x20, 0x01, 0x28, 0x04, 0x52, 0x06, 0x6e, 0x6f, 0x72, 0x6d, 0x61,
	0x6c, 0x12, 0x18, 0x0a, 0x07, 0x70, 0x72, 0x69, 0x76, 0x61, 0x63, 0x79, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x04, 0x52, 0x07, 0x70, 0x72, 0x69, 0x76, 0x61, 0x63, 0x79, 0x22, 0x1f, 0x0a, 0x0d, 0x44,
	0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x52, 0x65, 0x74, 0x75, 0x72, 0x6e, 0x12, 0x0e, 0x0a, 0x02,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x02, 0x69, 0x64, 0x22, 0xfa, 0x03, 0x0a,
	0x0e, 0x42, 0x6c, 0x6f, 0x63, 0x6b, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x52, 0x65, 0x71, 0x12,
	0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x02, 0x69, 0x64, 0x12,
	0x16, 0x0a, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x04, 0x52,
	0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x12, 0x1e, 0x0a, 0x0a, 0x72, 0x65, 0x63, 0x6f, 0x72,
	0x64, 0x54, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0a, 0x72, 0x65, 0x63,
	0x6f, 0x72, 0x64, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x66, 0x69, 0x6c, 0x65, 0x54,
	0x79, 0x70, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x08, 0x66, 0x69, 0x6c, 0x65, 0x54,
	0x79, 0x70, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x66, 0x69, 0x6c, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x66, 0x69, 0x6c, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12,
	0x12, 0x0a, 0x04, 0x68, 0x61, 0x73, 0x68, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x68,
	0x61, 0x73, 0x68, 0x12, 0x1a, 0x0a, 0x08, 0x73, 0x61, 0x76, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x18,
	0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x73, 0x61, 0x76, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x12,
	0x22, 0x0a, 0x0c, 0x6f, 0x75, 0x74, 0x42, 0x6c, 0x6f, 0x63, 0x6b, 0x54, 0x69, 0x6d, 0x65, 0x18,
	0x08, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0c, 0x6f, 0x75, 0x74, 0x42, 0x6c, 0x6f, 0x63, 0x6b, 0x54,
	0x69, 0x6d, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x74, 0x72, 0x61, 0x64, 0x69, 0x6e, 0x67, 0x48, 0x61,
	0x73, 0x68, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x74, 0x72, 0x61, 0x64, 0x69, 0x6e,
	0x67, 0x48, 0x61, 0x73, 0x68, 0x12, 0x16, 0x0a, 0x06, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x72, 0x18,
	0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x72, 0x12, 0x1a, 0x0a,
	0x08, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x4e, 0x75, 0x6d, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x08, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x4e, 0x75, 0x6d, 0x12, 0x18, 0x0a, 0x07, 0x63, 0x6f, 0x6e,
	0x73, 0x75, 0x6d, 0x65, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x04, 0x52, 0x07, 0x63, 0x6f, 0x6e, 0x73,
	0x75, 0x6d, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x62, 0x6c, 0x6f, 0x63, 0x6b, 0x49, 0x64, 0x18, 0x0d,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x62, 0x6c, 0x6f, 0x63, 0x6b, 0x49, 0x64, 0x12, 0x20, 0x0a,
	0x0b, 0x62, 0x6c, 0x6f, 0x63, 0x6b, 0x48, 0x65, 0x69, 0x67, 0x68, 0x74, 0x18, 0x0e, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x0b, 0x62, 0x6c, 0x6f, 0x63, 0x6b, 0x48, 0x65, 0x69, 0x67, 0x68, 0x74, 0x12,
	0x1a, 0x0a, 0x08, 0x66, 0x75, 0x6c, 0x6c, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x0f, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x08, 0x66, 0x75, 0x6c, 0x6c, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x63,
	0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x18, 0x10, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x63, 0x6f,
	0x6e, 0x74, 0x65, 0x6e, 0x74, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x65, 0x72, 0x74, 0x18, 0x11, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x04, 0x63, 0x65, 0x72, 0x74, 0x12, 0x1e, 0x0a, 0x0a, 0x63, 0x6f, 0x6e,
	0x73, 0x75, 0x6d, 0x65, 0x55, 0x69, 0x64, 0x18, 0x12, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x63,
	0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x55, 0x69, 0x64, 0x22, 0x0f, 0x0a, 0x0d, 0x57, 0x65, 0x63,
	0x68, 0x61, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x22, 0x21, 0x0a, 0x0d, 0x57, 0x65,
	0x63, 0x68, 0x61, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x64, 0x12, 0x10, 0x0a, 0x03, 0x6d,
	0x73, 0x67, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6d, 0x73, 0x67, 0x22, 0x23, 0x0a,
	0x09, 0x4d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x52, 0x65, 0x71, 0x12, 0x16, 0x0a, 0x06, 0x6c, 0x65,
	0x64, 0x67, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x6c, 0x65, 0x64, 0x67,
	0x65, 0x72, 0x22, 0x5a, 0x0a, 0x0a, 0x4d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x4c, 0x69, 0x73, 0x74,
	0x12, 0x32, 0x0a, 0x0a, 0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x4c, 0x69, 0x73, 0x74, 0x18, 0x01,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x62, 0x6c, 0x6f, 0x63, 0x6b, 0x63, 0x68, 0x61, 0x69,
	0x6e, 0x2e, 0x4d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x52, 0x0a, 0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72,
	0x4c, 0x69, 0x73, 0x74, 0x12, 0x18, 0x0a, 0x07, 0x62, 0x72, 0x65, 0x61, 0x6b, 0x65, 0x64, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x62, 0x72, 0x65, 0x61, 0x6b, 0x65, 0x64, 0x22, 0xd4,
	0x01, 0x0a, 0x06, 0x4d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x70, 0x6f, 0x72,
	0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x70, 0x6f, 0x72, 0x74, 0x12, 0x1c, 0x0a,
	0x09, 0x73, 0x68, 0x6f, 0x77, 0x6e, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x09, 0x73, 0x68, 0x6f, 0x77, 0x6e, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x69,
	0x6e, 0x41, 0x64, 0x64, 0x72, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x69, 0x6e, 0x41,
	0x64, 0x64, 0x72, 0x12, 0x16, 0x0a, 0x06, 0x68, 0x65, 0x69, 0x67, 0x68, 0x74, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x06, 0x68, 0x65, 0x69, 0x67, 0x68, 0x74, 0x12, 0x1a, 0x0a, 0x08, 0x68,
	0x61, 0x73, 0x68, 0x54, 0x79, 0x70, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x68,
	0x61, 0x73, 0x68, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1c, 0x0a, 0x09, 0x63, 0x6f, 0x6e, 0x73, 0x65,
	0x6e, 0x73, 0x75, 0x73, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x63, 0x6f, 0x6e, 0x73,
	0x65, 0x6e, 0x73, 0x75, 0x73, 0x12, 0x1e, 0x0a, 0x0a, 0x6c, 0x69, 0x63, 0x4f, 0x75, 0x74, 0x54,
	0x69, 0x6d, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x6c, 0x69, 0x63, 0x4f, 0x75,
	0x74, 0x54, 0x69, 0x6d, 0x65, 0x22, 0x8a, 0x01, 0x0a, 0x12, 0x4d, 0x65, 0x6d, 0x62, 0x65, 0x72,
	0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x14, 0x0a, 0x05,
	0x73, 0x74, 0x61, 0x74, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x73, 0x74, 0x61,
	0x74, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x2a, 0x0a, 0x04,
	0x64, 0x61, 0x74, 0x61, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x62, 0x6c, 0x6f,
	0x63, 0x6b, 0x63, 0x68, 0x61, 0x69, 0x6e, 0x2e, 0x4d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x4c, 0x69,
	0x73, 0x74, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x12, 0x18, 0x0a, 0x07, 0x62, 0x72, 0x65, 0x61,
	0x6b, 0x65, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x62, 0x72, 0x65, 0x61, 0x6b,
	0x65, 0x64, 0x22, 0x68, 0x0a, 0x0c, 0x4c, 0x65, 0x64, 0x67, 0x65, 0x72, 0x4d, 0x65, 0x6d, 0x62,
	0x65, 0x72, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02,
	0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x70, 0x6f, 0x72, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x04, 0x70, 0x6f, 0x72, 0x74, 0x12, 0x1c, 0x0a, 0x09, 0x73, 0x68, 0x6f, 0x77, 0x6e, 0x4e,
	0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x73, 0x68, 0x6f, 0x77, 0x6e,
	0x4e, 0x61, 0x6d, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x69, 0x6e, 0x41, 0x64, 0x64, 0x72, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x69, 0x6e, 0x41, 0x64, 0x64, 0x72, 0x22, 0xb4, 0x01, 0x0a,
	0x06, 0x4c, 0x65, 0x64, 0x67, 0x65, 0x72, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x38, 0x0a, 0x0a, 0x6d,
	0x65, 0x6d, 0x62, 0x65, 0x72, 0x4c, 0x69, 0x73, 0x74, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x18, 0x2e, 0x62, 0x6c, 0x6f, 0x63, 0x6b, 0x63, 0x68, 0x61, 0x69, 0x6e, 0x2e, 0x4c, 0x65, 0x64,
	0x67, 0x65, 0x72, 0x4d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x52, 0x0a, 0x6d, 0x65, 0x6d, 0x62, 0x65,
	0x72, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x1a, 0x0a, 0x08, 0x68, 0x61, 0x73, 0x68, 0x54, 0x79, 0x70,
	0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x68, 0x61, 0x73, 0x68, 0x54, 0x79, 0x70,
	0x65, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x6e, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x04, 0x63, 0x6f, 0x6e, 0x73, 0x12, 0x1c, 0x0a, 0x09, 0x74, 0x69, 0x6d, 0x65, 0x53, 0x74, 0x61,
	0x6d, 0x70, 0x18, 0x05, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x74, 0x69, 0x6d, 0x65, 0x53, 0x74,
	0x61, 0x6d, 0x70, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x02, 0x69, 0x64, 0x22, 0x8a, 0x01, 0x0a, 0x12, 0x4c, 0x65, 0x64, 0x67, 0x65, 0x72, 0x4c, 0x69,
	0x73, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x73, 0x74,
	0x61, 0x74, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65,
	0x12, 0x18, 0x0a, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x2c, 0x0a, 0x07, 0x6c, 0x65,
	0x64, 0x67, 0x65, 0x72, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x62, 0x6c,
	0x6f, 0x63, 0x6b, 0x63, 0x68, 0x61, 0x69, 0x6e, 0x2e, 0x4c, 0x65, 0x64, 0x67, 0x65, 0x72, 0x52,
	0x07, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x72, 0x73, 0x12, 0x16, 0x0a, 0x06, 0x6e, 0x75, 0x6d, 0x62,
	0x65, 0x72, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72,
	0x22, 0x57, 0x0a, 0x0d, 0x53, 0x74, 0x6f, 0x72, 0x65, 0x51, 0x75, 0x65, 0x72, 0x79, 0x52, 0x65,
	0x71, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x78, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x04, 0x74, 0x78, 0x49, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x70, 0x72, 0x69, 0x4b, 0x65, 0x79, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x70, 0x72, 0x69, 0x4b, 0x65, 0x79, 0x12, 0x1a, 0x0a,
	0x08, 0x70, 0x61, 0x73, 0x73, 0x77, 0x6f, 0x72, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x08, 0x70, 0x61, 0x73, 0x73, 0x77, 0x6f, 0x72, 0x64, 0x22, 0x53, 0x0a, 0x0d, 0x53, 0x74, 0x6f,
	0x72, 0x65, 0x51, 0x75, 0x65, 0x72, 0x79, 0x52, 0x65, 0x73, 0x12, 0x14, 0x0a, 0x05, 0x73, 0x74,
	0x61, 0x74, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65,
	0x12, 0x18, 0x0a, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x64, 0x61,
	0x74, 0x61, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x22, 0x56,
	0x0a, 0x0e, 0x42, 0x6c, 0x6f, 0x63, 0x6b, 0x48, 0x65, 0x69, 0x67, 0x68, 0x74, 0x52, 0x65, 0x73,
	0x12, 0x16, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04,
	0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x18, 0x0a, 0x07, 0x6d, 0x65, 0x73, 0x73,
	0x61, 0x67, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61,
	0x67, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x03, 0x20, 0x01, 0x28, 0x04,
	0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x22, 0x27, 0x0a, 0x0b, 0x42, 0x6c, 0x6f, 0x63, 0x6b, 0x48,
	0x65, 0x69, 0x67, 0x68, 0x74, 0x12, 0x18, 0x0a, 0x07, 0x42, 0x6c, 0x6f, 0x63, 0x6b, 0x49, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x42, 0x6c, 0x6f, 0x63, 0x6b, 0x49, 0x64, 0x22,
	0x23, 0x0a, 0x07, 0x50, 0x75, 0x62, 0x4b, 0x65, 0x79, 0x73, 0x12, 0x18, 0x0a, 0x07, 0x70, 0x75,
	0x62, 0x4b, 0x65, 0x79, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x70, 0x75, 0x62,
	0x4b, 0x65, 0x79, 0x73, 0x22, 0x42, 0x0a, 0x12, 0x4f, 0x72, 0x64, 0x69, 0x6e, 0x61, 0x72, 0x79,
	0x44, 0x65, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x52, 0x65, 0x71, 0x12, 0x12, 0x0a, 0x04, 0x64, 0x61,
	0x74, 0x61, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x12, 0x18,
	0x0a, 0x07, 0x70, 0x75, 0x62, 0x4b, 0x65, 0x79, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x09, 0x52,
	0x07, 0x70, 0x75, 0x62, 0x4b, 0x65, 0x79, 0x73, 0x22, 0x58, 0x0a, 0x12, 0x4f, 0x72, 0x64, 0x69,
	0x6e, 0x61, 0x72, 0x79, 0x44, 0x65, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x52, 0x65, 0x73, 0x12, 0x14,
	0x0a, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x05, 0x73,
	0x74, 0x61, 0x74, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x12,
	0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x64, 0x61,
	0x74, 0x61, 0x22, 0xe9, 0x01, 0x0a, 0x0b, 0x42, 0x6c, 0x6f, 0x63, 0x6b, 0x48, 0x65, 0x61, 0x64,
	0x65, 0x72, 0x12, 0x18, 0x0a, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x16, 0x0a, 0x06,
	0x68, 0x65, 0x69, 0x67, 0x68, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x68, 0x65,
	0x69, 0x67, 0x68, 0x74, 0x12, 0x1c, 0x0a, 0x09, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d,
	0x70, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61,
	0x6d, 0x70, 0x12, 0x18, 0x0a, 0x07, 0x62, 0x6c, 0x6f, 0x63, 0x6b, 0x49, 0x64, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x07, 0x62, 0x6c, 0x6f, 0x63, 0x6b, 0x49, 0x64, 0x12, 0x1e, 0x0a, 0x0a,
	0x70, 0x72, 0x65, 0x76, 0x69, 0x6f, 0x75, 0x73, 0x49, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0a, 0x70, 0x72, 0x65, 0x76, 0x69, 0x6f, 0x75, 0x73, 0x49, 0x64, 0x12, 0x26, 0x0a, 0x0e,
	0x77, 0x6f, 0x72, 0x6c, 0x64, 0x53, 0x74, 0x61, 0x74, 0x65, 0x52, 0x6f, 0x6f, 0x74, 0x18, 0x06,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x77, 0x6f, 0x72, 0x6c, 0x64, 0x53, 0x74, 0x61, 0x74, 0x65,
	0x52, 0x6f, 0x6f, 0x74, 0x12, 0x28, 0x0a, 0x0f, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x52, 0x6f, 0x6f, 0x74, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x74,
	0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x6f, 0x6f, 0x74, 0x22, 0x7f,
	0x0a, 0x12, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x42, 0x6c, 0x6f, 0x63, 0x6b, 0x48, 0x65,
	0x61, 0x64, 0x65, 0x72, 0x12, 0x2f, 0x0a, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x62, 0x6c, 0x6f, 0x63, 0x6b, 0x63, 0x68, 0x61, 0x69,
	0x6e, 0x2e, 0x42, 0x6c, 0x6f, 0x63, 0x6b, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x06, 0x68,
	0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x10, 0x0a, 0x03, 0x74, 0x78, 0x73, 0x18, 0x02, 0x20, 0x03,
	0x28, 0x09, 0x52, 0x03, 0x74, 0x78, 0x73, 0x12, 0x14, 0x0a, 0x05, 0x65, 0x78, 0x74, 0x72, 0x61,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x65, 0x78, 0x74, 0x72, 0x61, 0x12, 0x10, 0x0a,
	0x03, 0x72, 0x61, 0x77, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x72, 0x61, 0x77, 0x22,
	0x7d, 0x0a, 0x17, 0x47, 0x65, 0x74, 0x42, 0x6c, 0x6f, 0x63, 0x6b, 0x44, 0x65, 0x74, 0x61, 0x69,
	0x6c, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x73, 0x74,
	0x61, 0x74, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65,
	0x12, 0x18, 0x0a, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x32, 0x0a, 0x04, 0x64, 0x61,
	0x74, 0x61, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x62, 0x6c, 0x6f, 0x63, 0x6b,
	0x63, 0x68, 0x61, 0x69, 0x6e, 0x2e, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x42, 0x6c, 0x6f,
	0x63, 0x6b, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x22, 0x9e,
	0x01, 0x0a, 0x10, 0x44, 0x65, 0x61, 0x6c, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x48, 0x65, 0x61,
	0x64, 0x65, 0x72, 0x12, 0x18, 0x0a, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x12, 0x0a,
	0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x74, 0x79, 0x70,
	0x65, 0x12, 0x18, 0x0a, 0x07, 0x73, 0x75, 0x62, 0x54, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x07, 0x73, 0x75, 0x62, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1c, 0x0a, 0x09, 0x74,
	0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09,
	0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x12, 0x24, 0x0a, 0x0d, 0x74, 0x72, 0x61,
	0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0d, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x22,
	0x2f, 0x0a, 0x0f, 0x44, 0x65, 0x61, 0x6c, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x53, 0x74, 0x6f,
	0x72, 0x65, 0x12, 0x1c, 0x0a, 0x09, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x44, 0x61, 0x74, 0x61, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x44, 0x61, 0x74, 0x61,
	0x22, 0xc3, 0x01, 0x0a, 0x16, 0x46, 0x69, 0x6e, 0x64, 0x44, 0x65, 0x61, 0x6c, 0x44, 0x65, 0x74,
	0x61, 0x69, 0x6c, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x73,
	0x74, 0x61, 0x74, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x73, 0x74, 0x61, 0x74,
	0x65, 0x12, 0x18, 0x0a, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x34, 0x0a, 0x06, 0x68,
	0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x62, 0x6c,
	0x6f, 0x63, 0x6b, 0x63, 0x68, 0x61, 0x69, 0x6e, 0x2e, 0x44, 0x65, 0x61, 0x6c, 0x44, 0x65, 0x74,
	0x61, 0x69, 0x6c, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65,
	0x72, 0x12, 0x31, 0x0a, 0x05, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x1b, 0x2e, 0x62, 0x6c, 0x6f, 0x63, 0x6b, 0x63, 0x68, 0x61, 0x69, 0x6e, 0x2e, 0x44, 0x65,
	0x61, 0x6c, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x53, 0x74, 0x6f, 0x72, 0x65, 0x52, 0x05, 0x73,
	0x74, 0x6f, 0x72, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x72, 0x61, 0x77, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x03, 0x72, 0x61, 0x77, 0x22, 0x97, 0x01, 0x0a, 0x09, 0x52, 0x61, 0x77, 0x48, 0x65,
	0x61, 0x64, 0x65, 0x72, 0x12, 0x18, 0x0a, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x12,
	0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x74, 0x79,
	0x70, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x73, 0x75, 0x62, 0x54, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x07, 0x73, 0x75, 0x62, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1c, 0x0a, 0x09,
	0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x09, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x12, 0x24, 0x0a, 0x0d, 0x74, 0x72,
	0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0d, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64,
	0x22, 0x36, 0x0a, 0x0a, 0x52, 0x61, 0x77, 0x54, 0x78, 0x50, 0x72, 0x6f, 0x6f, 0x66, 0x12, 0x12,
	0x0a, 0x04, 0x6c, 0x65, 0x66, 0x74, 0x18, 0x01, 0x20, 0x03, 0x28, 0x09, 0x52, 0x04, 0x6c, 0x65,
	0x66, 0x74, 0x12, 0x14, 0x0a, 0x05, 0x72, 0x69, 0x67, 0x68, 0x74, 0x18, 0x02, 0x20, 0x03, 0x28,
	0x09, 0x52, 0x05, 0x72, 0x69, 0x67, 0x68, 0x74, 0x22, 0xf8, 0x01, 0x0a, 0x13, 0x46, 0x69, 0x6e,
	0x64, 0x44, 0x65, 0x61, 0x6c, 0x52, 0x61, 0x77, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x12, 0x14, 0x0a, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65,
	0x12, 0x2d, 0x0a, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x15, 0x2e, 0x62, 0x6c, 0x6f, 0x63, 0x6b, 0x63, 0x68, 0x61, 0x69, 0x6e, 0x2e, 0x52, 0x61,
	0x77, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12,
	0x12, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x64,
	0x61, 0x74, 0x61, 0x12, 0x16, 0x0a, 0x06, 0x70, 0x75, 0x62, 0x6b, 0x65, 0x79, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x06, 0x70, 0x75, 0x62, 0x6b, 0x65, 0x79, 0x12, 0x12, 0x0a, 0x04, 0x73,
	0x69, 0x67, 0x6e, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x73, 0x69, 0x67, 0x6e, 0x12,
	0x10, 0x0a, 0x03, 0x72, 0x61, 0x77, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x72, 0x61,
	0x77, 0x12, 0x30, 0x0a, 0x07, 0x74, 0x78, 0x70, 0x72, 0x6f, 0x6f, 0x66, 0x18, 0x0a, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x16, 0x2e, 0x62, 0x6c, 0x6f, 0x63, 0x6b, 0x63, 0x68, 0x61, 0x69, 0x6e, 0x2e,
	0x52, 0x61, 0x77, 0x54, 0x78, 0x50, 0x72, 0x6f, 0x6f, 0x66, 0x52, 0x07, 0x74, 0x78, 0x70, 0x72,
	0x6f, 0x6f, 0x66, 0x32, 0xd6, 0x0c, 0x0a, 0x0a, 0x42, 0x6c, 0x6f, 0x63, 0x6b, 0x63, 0x68, 0x61,
	0x69, 0x6e, 0x12, 0x48, 0x0a, 0x0f, 0x46, 0x69, 0x6e, 0x64, 0x42, 0x6c, 0x6f, 0x63, 0x6b, 0x48,
	0x65, 0x69, 0x67, 0x68, 0x74, 0x12, 0x17, 0x2e, 0x62, 0x6c, 0x6f, 0x63, 0x6b, 0x63, 0x68, 0x61,
	0x69, 0x6e, 0x2e, 0x42, 0x6c, 0x6f, 0x63, 0x6b, 0x48, 0x65, 0x69, 0x67, 0x68, 0x74, 0x1a, 0x1a,
	0x2e, 0x62, 0x6c, 0x6f, 0x63, 0x6b, 0x63, 0x68, 0x61, 0x69, 0x6e, 0x2e, 0x42, 0x6c, 0x6f, 0x63,
	0x6b, 0x48, 0x65, 0x69, 0x67, 0x68, 0x74, 0x52, 0x65, 0x73, 0x22, 0x00, 0x12, 0x51, 0x0a, 0x0f,
	0x46, 0x69, 0x6e, 0x64, 0x42, 0x6c, 0x6f, 0x63, 0x6b, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x12,
	0x17, 0x2e, 0x62, 0x6c, 0x6f, 0x63, 0x6b, 0x63, 0x68, 0x61, 0x69, 0x6e, 0x2e, 0x42, 0x6c, 0x6f,
	0x63, 0x6b, 0x48, 0x65, 0x69, 0x67, 0x68, 0x74, 0x1a, 0x23, 0x2e, 0x62, 0x6c, 0x6f, 0x63, 0x6b,
	0x63, 0x68, 0x61, 0x69, 0x6e, 0x2e, 0x47, 0x65, 0x74, 0x42, 0x6c, 0x6f, 0x63, 0x6b, 0x44, 0x65,
	0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12,
	0x4f, 0x0a, 0x0b, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x42, 0x6c, 0x6f, 0x63, 0x6b, 0x12, 0x1e,
	0x2e, 0x62, 0x6c, 0x6f, 0x63, 0x6b, 0x63, 0x68, 0x61, 0x69, 0x6e, 0x2e, 0x4f, 0x72, 0x64, 0x69,
	0x6e, 0x61, 0x72, 0x79, 0x44, 0x65, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x52, 0x65, 0x71, 0x1a, 0x1e,
	0x2e, 0x62, 0x6c, 0x6f, 0x63, 0x6b, 0x63, 0x68, 0x61, 0x69, 0x6e, 0x2e, 0x4f, 0x72, 0x64, 0x69,
	0x6e, 0x61, 0x72, 0x79, 0x44, 0x65, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x52, 0x65, 0x73, 0x22, 0x00,
	0x12, 0x46, 0x0a, 0x0e, 0x46, 0x69, 0x6e, 0x64, 0x42, 0x6c, 0x6f, 0x63, 0x6b, 0x49, 0x6e, 0x64,
	0x65, 0x78, 0x12, 0x17, 0x2e, 0x62, 0x6c, 0x6f, 0x63, 0x6b, 0x63, 0x68, 0x61, 0x69, 0x6e, 0x2e,
	0x42, 0x6c, 0x6f, 0x63, 0x6b, 0x48, 0x65, 0x69, 0x67, 0x68, 0x74, 0x1a, 0x19, 0x2e, 0x62, 0x6c,
	0x6f, 0x63, 0x6b, 0x63, 0x68, 0x61, 0x69, 0x6e, 0x2e, 0x53, 0x74, 0x6f, 0x72, 0x65, 0x51, 0x75,
	0x65, 0x72, 0x79, 0x52, 0x65, 0x73, 0x22, 0x00, 0x12, 0x48, 0x0a, 0x0e, 0x46, 0x69, 0x6e, 0x64,
	0x53, 0x74, 0x6f, 0x72, 0x65, 0x51, 0x75, 0x65, 0x72, 0x79, 0x12, 0x19, 0x2e, 0x62, 0x6c, 0x6f,
	0x63, 0x6b, 0x63, 0x68, 0x61, 0x69, 0x6e, 0x2e, 0x53, 0x74, 0x6f, 0x72, 0x65, 0x51, 0x75, 0x65,
	0x72, 0x79, 0x52, 0x65, 0x71, 0x1a, 0x19, 0x2e, 0x62, 0x6c, 0x6f, 0x63, 0x6b, 0x63, 0x68, 0x61,
	0x69, 0x6e, 0x2e, 0x53, 0x74, 0x6f, 0x72, 0x65, 0x51, 0x75, 0x65, 0x72, 0x79, 0x52, 0x65, 0x73,
	0x22, 0x00, 0x12, 0x4f, 0x0a, 0x0e, 0x46, 0x69, 0x6e, 0x64, 0x44, 0x65, 0x61, 0x6c, 0x44, 0x65,
	0x74, 0x61, 0x69, 0x6c, 0x12, 0x17, 0x2e, 0x62, 0x6c, 0x6f, 0x63, 0x6b, 0x63, 0x68, 0x61, 0x69,
	0x6e, 0x2e, 0x42, 0x6c, 0x6f, 0x63, 0x6b, 0x48, 0x65, 0x69, 0x67, 0x68, 0x74, 0x1a, 0x22, 0x2e,
	0x62, 0x6c, 0x6f, 0x63, 0x6b, 0x63, 0x68, 0x61, 0x69, 0x6e, 0x2e, 0x46, 0x69, 0x6e, 0x64, 0x44,
	0x65, 0x61, 0x6c, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x22, 0x00, 0x12, 0x49, 0x0a, 0x0b, 0x46, 0x69, 0x6e, 0x64, 0x44, 0x65, 0x61, 0x6c, 0x52,
	0x61, 0x77, 0x12, 0x17, 0x2e, 0x62, 0x6c, 0x6f, 0x63, 0x6b, 0x63, 0x68, 0x61, 0x69, 0x6e, 0x2e,
	0x42, 0x6c, 0x6f, 0x63, 0x6b, 0x48, 0x65, 0x69, 0x67, 0x68, 0x74, 0x1a, 0x1f, 0x2e, 0x62, 0x6c,
	0x6f, 0x63, 0x6b, 0x63, 0x68, 0x61, 0x69, 0x6e, 0x2e, 0x46, 0x69, 0x6e, 0x64, 0x44, 0x65, 0x61,
	0x6c, 0x52, 0x61, 0x77, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x48,
	0x0a, 0x0f, 0x46, 0x69, 0x6e, 0x64, 0x42, 0x6c, 0x6f, 0x63, 0x6b, 0x48, 0x65, 0x61, 0x6c, 0x74,
	0x68, 0x12, 0x17, 0x2e, 0x62, 0x6c, 0x6f, 0x63, 0x6b, 0x63, 0x68, 0x61, 0x69, 0x6e, 0x2e, 0x42,
	0x6c, 0x6f, 0x63, 0x6b, 0x48, 0x65, 0x69, 0x67, 0x68, 0x74, 0x1a, 0x1a, 0x2e, 0x62, 0x6c, 0x6f,
	0x63, 0x6b, 0x63, 0x68, 0x61, 0x69, 0x6e, 0x2e, 0x42, 0x6c, 0x6f, 0x63, 0x6b, 0x48, 0x65, 0x69,
	0x67, 0x68, 0x74, 0x52, 0x65, 0x73, 0x22, 0x00, 0x12, 0x4b, 0x0a, 0x0e, 0x46, 0x69, 0x6e, 0x64,
	0x4c, 0x65, 0x64, 0x67, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x17, 0x2e, 0x62, 0x6c, 0x6f,
	0x63, 0x6b, 0x63, 0x68, 0x61, 0x69, 0x6e, 0x2e, 0x42, 0x6c, 0x6f, 0x63, 0x6b, 0x48, 0x65, 0x69,
	0x67, 0x68, 0x74, 0x1a, 0x1e, 0x2e, 0x62, 0x6c, 0x6f, 0x63, 0x6b, 0x63, 0x68, 0x61, 0x69, 0x6e,
	0x2e, 0x4c, 0x65, 0x64, 0x67, 0x65, 0x72, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x49, 0x0a, 0x0e, 0x46, 0x69, 0x6e, 0x64, 0x4d, 0x65, 0x6d,
	0x62, 0x65, 0x72, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x15, 0x2e, 0x62, 0x6c, 0x6f, 0x63, 0x6b, 0x63,
	0x68, 0x61, 0x69, 0x6e, 0x2e, 0x4d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x52, 0x65, 0x71, 0x1a, 0x1e,
	0x2e, 0x62, 0x6c, 0x6f, 0x63, 0x6b, 0x63, 0x68, 0x61, 0x69, 0x6e, 0x2e, 0x4d, 0x65, 0x6d, 0x62,
	0x65, 0x72, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00,
	0x12, 0x51, 0x0a, 0x16, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x42, 0x6c, 0x6f, 0x63, 0x6b, 0x43,
	0x68, 0x61, 0x69, 0x6e, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x12, 0x1a, 0x2e, 0x62, 0x6c, 0x6f,
	0x63, 0x6b, 0x63, 0x68, 0x61, 0x69, 0x6e, 0x2e, 0x42, 0x6c, 0x6f, 0x63, 0x6b, 0x52, 0x65, 0x63,
	0x6f, 0x72, 0x64, 0x52, 0x65, 0x71, 0x1a, 0x19, 0x2e, 0x62, 0x6c, 0x6f, 0x63, 0x6b, 0x63, 0x68,
	0x61, 0x69, 0x6e, 0x2e, 0x44, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x52, 0x65, 0x74, 0x75, 0x72,
	0x6e, 0x22, 0x00, 0x12, 0x51, 0x0a, 0x16, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x42, 0x6c, 0x6f,
	0x63, 0x6b, 0x43, 0x68, 0x61, 0x69, 0x6e, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x12, 0x1a, 0x2e,
	0x62, 0x6c, 0x6f, 0x63, 0x6b, 0x63, 0x68, 0x61, 0x69, 0x6e, 0x2e, 0x42, 0x6c, 0x6f, 0x63, 0x6b,
	0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x52, 0x65, 0x71, 0x1a, 0x19, 0x2e, 0x62, 0x6c, 0x6f, 0x63,
	0x6b, 0x63, 0x68, 0x61, 0x69, 0x6e, 0x2e, 0x44, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x52, 0x65,
	0x74, 0x75, 0x72, 0x6e, 0x22, 0x00, 0x12, 0x45, 0x0a, 0x0f, 0x46, 0x69, 0x6e, 0x64, 0x4e, 0x6f,
	0x42, 0x6c, 0x6f, 0x63, 0x6b, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x17, 0x2e, 0x62, 0x6c, 0x6f, 0x63,
	0x6b, 0x63, 0x68, 0x61, 0x69, 0x6e, 0x2e, 0x42, 0x6c, 0x6f, 0x63, 0x6b, 0x48, 0x65, 0x69, 0x67,
	0x68, 0x74, 0x1a, 0x17, 0x2e, 0x62, 0x6c, 0x6f, 0x63, 0x6b, 0x63, 0x68, 0x61, 0x69, 0x6e, 0x2e,
	0x4e, 0x6f, 0x42, 0x6c, 0x6f, 0x63, 0x6b, 0x4c, 0x69, 0x73, 0x74, 0x22, 0x00, 0x12, 0x40, 0x0a,
	0x06, 0x57, 0x65, 0x63, 0x68, 0x61, 0x74, 0x12, 0x19, 0x2e, 0x62, 0x6c, 0x6f, 0x63, 0x6b, 0x63,
	0x68, 0x61, 0x69, 0x6e, 0x2e, 0x57, 0x65, 0x63, 0x68, 0x61, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x1a, 0x19, 0x2e, 0x62, 0x6c, 0x6f, 0x63, 0x6b, 0x63, 0x68, 0x61, 0x69, 0x6e, 0x2e,
	0x57, 0x65, 0x63, 0x68, 0x61, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x64, 0x22, 0x00, 0x12,
	0x42, 0x0a, 0x0c, 0x46, 0x69, 0x6e, 0x64, 0x48, 0x6f, 0x6d, 0x65, 0x44, 0x61, 0x74, 0x61, 0x12,
	0x17, 0x2e, 0x62, 0x6c, 0x6f, 0x63, 0x6b, 0x63, 0x68, 0x61, 0x69, 0x6e, 0x2e, 0x42, 0x6c, 0x6f,
	0x63, 0x6b, 0x48, 0x65, 0x69, 0x67, 0x68, 0x74, 0x1a, 0x17, 0x2e, 0x62, 0x6c, 0x6f, 0x63, 0x6b,
	0x63, 0x68, 0x61, 0x69, 0x6e, 0x2e, 0x48, 0x6f, 0x6d, 0x65, 0x44, 0x61, 0x74, 0x61, 0x52, 0x65,
	0x73, 0x22, 0x00, 0x12, 0x46, 0x0a, 0x0d, 0x46, 0x69, 0x6e, 0x64, 0x42, 0x6c, 0x6f, 0x63, 0x6b,
	0x4c, 0x69, 0x73, 0x74, 0x12, 0x18, 0x2e, 0x62, 0x6c, 0x6f, 0x63, 0x6b, 0x63, 0x68, 0x61, 0x69,
	0x6e, 0x2e, 0x42, 0x6c, 0x6f, 0x63, 0x6b, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x1a, 0x19,
	0x2e, 0x62, 0x6c, 0x6f, 0x63, 0x6b, 0x63, 0x68, 0x61, 0x69, 0x6e, 0x2e, 0x42, 0x6c, 0x6f, 0x63,
	0x6b, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x70, 0x22, 0x00, 0x12, 0x4c, 0x0a, 0x0f, 0x46,
	0x69, 0x6e, 0x64, 0x42, 0x6c, 0x6f, 0x63, 0x6b, 0x54, 0x78, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x1a,
	0x2e, 0x62, 0x6c, 0x6f, 0x63, 0x6b, 0x63, 0x68, 0x61, 0x69, 0x6e, 0x2e, 0x42, 0x6c, 0x6f, 0x63,
	0x6b, 0x54, 0x78, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x1a, 0x1b, 0x2e, 0x62, 0x6c, 0x6f,
	0x63, 0x6b, 0x63, 0x68, 0x61, 0x69, 0x6e, 0x2e, 0x42, 0x6c, 0x6f, 0x63, 0x6b, 0x54, 0x78, 0x4c,
	0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x70, 0x22, 0x00, 0x12, 0x46, 0x0a, 0x0d, 0x46, 0x69, 0x6e,
	0x64, 0x42, 0x6c, 0x6f, 0x63, 0x6b, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x18, 0x2e, 0x62, 0x6c, 0x6f,
	0x63, 0x6b, 0x63, 0x68, 0x61, 0x69, 0x6e, 0x2e, 0x42, 0x6c, 0x6f, 0x63, 0x6b, 0x49, 0x6e, 0x66,
	0x6f, 0x52, 0x65, 0x71, 0x1a, 0x19, 0x2e, 0x62, 0x6c, 0x6f, 0x63, 0x6b, 0x63, 0x68, 0x61, 0x69,
	0x6e, 0x2e, 0x42, 0x6c, 0x6f, 0x63, 0x6b, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x73, 0x70, 0x22,
	0x00, 0x12, 0x50, 0x0a, 0x0f, 0x46, 0x69, 0x6e, 0x64, 0x42, 0x6c, 0x6f, 0x63, 0x6b, 0x54, 0x78,
	0x49, 0x6e, 0x66, 0x6f, 0x12, 0x1a, 0x2e, 0x62, 0x6c, 0x6f, 0x63, 0x6b, 0x63, 0x68, 0x61, 0x69,
	0x6e, 0x2e, 0x42, 0x6c, 0x6f, 0x63, 0x6b, 0x54, 0x78, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x71,
	0x1a, 0x1f, 0x2e, 0x62, 0x6c, 0x6f, 0x63, 0x6b, 0x63, 0x68, 0x61, 0x69, 0x6e, 0x2e, 0x42, 0x6c,
	0x6f, 0x63, 0x6b, 0x54, 0x78, 0x44, 0x61, 0x74, 0x61, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x73,
	0x70, 0x22, 0x00, 0x12, 0x54, 0x0a, 0x12, 0x46, 0x69, 0x6e, 0x64, 0x4b, 0x65, 0x65, 0x70, 0x52,
	0x65, 0x63, 0x6f, 0x72, 0x64, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x1d, 0x2e, 0x62, 0x6c, 0x6f, 0x63,
	0x6b, 0x63, 0x68, 0x61, 0x69, 0x6e, 0x2e, 0x4b, 0x65, 0x65, 0x70, 0x52, 0x65, 0x63, 0x6f, 0x72,
	0x64, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x1a, 0x1d, 0x2e, 0x62, 0x6c, 0x6f, 0x63, 0x6b,
	0x63, 0x68, 0x61, 0x69, 0x6e, 0x2e, 0x4b, 0x65, 0x65, 0x70, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64,
	0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x22, 0x00, 0x12, 0x51, 0x0a, 0x11, 0x46, 0x69, 0x6e,
	0x64, 0x50, 0x65, 0x72, 0x73, 0x6f, 0x6e, 0x61, 0x67, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x1c,
	0x2e, 0x62, 0x6c, 0x6f, 0x63, 0x6b, 0x63, 0x68, 0x61, 0x69, 0x6e, 0x2e, 0x50, 0x65, 0x72, 0x73,
	0x6f, 0x6e, 0x61, 0x67, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x71, 0x1a, 0x1c, 0x2e, 0x62,
	0x6c, 0x6f, 0x63, 0x6b, 0x63, 0x68, 0x61, 0x69, 0x6e, 0x2e, 0x50, 0x65, 0x72, 0x73, 0x6f, 0x6e,
	0x61, 0x67, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x73, 0x22, 0x00, 0x42, 0x0f, 0x5a, 0x0d,
	0x2e, 0x2f, 0x3b, 0x62, 0x6c, 0x6f, 0x63, 0x6b, 0x63, 0x68, 0x61, 0x69, 0x6e, 0x62, 0x06, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_pb_blockchain_blockchain_proto_rawDescOnce sync.Once
	file_pb_blockchain_blockchain_proto_rawDescData = file_pb_blockchain_blockchain_proto_rawDesc
)

func file_pb_blockchain_blockchain_proto_rawDescGZIP() []byte {
	file_pb_blockchain_blockchain_proto_rawDescOnce.Do(func() {
		file_pb_blockchain_blockchain_proto_rawDescData = protoimpl.X.CompressGZIP(file_pb_blockchain_blockchain_proto_rawDescData)
	})
	return file_pb_blockchain_blockchain_proto_rawDescData
}

var file_pb_blockchain_blockchain_proto_msgTypes = make([]protoimpl.MessageInfo, 51)
var file_pb_blockchain_blockchain_proto_goTypes = []interface{}{
	(*NoBlockList)(nil),             // 0: blockchain.NoBlockList
	(*NoBlockInfo)(nil),             // 1: blockchain.NoBlockInfo
	(*PersonageInfoReq)(nil),        // 2: blockchain.PersonageInfoReq
	(*PersonageInfoRes)(nil),        // 3: blockchain.PersonageInfoRes
	(*KeepRecordListRes)(nil),       // 4: blockchain.KeepRecordListRes
	(*KeepRecordData)(nil),          // 5: blockchain.KeepRecordData
	(*KeepRecordListReq)(nil),       // 6: blockchain.KeepRecordListReq
	(*BlockTxData)(nil),             // 7: blockchain.BlockTxData
	(*BlockTxResp)(nil),             // 8: blockchain.BlockTxResp
	(*BlockDataInfoResp)(nil),       // 9: blockchain.BlockDataInfoResp
	(*BlockInfoResp)(nil),           // 10: blockchain.BlockInfoResp
	(*BlockTxDataInfoResp)(nil),     // 11: blockchain.BlockTxDataInfoResp
	(*BlockTxInfoResp)(nil),         // 12: blockchain.BlockTxInfoResp
	(*BlockInfoReq)(nil),            // 13: blockchain.BlockInfoReq
	(*BlockTxInfoReq)(nil),          // 14: blockchain.BlockTxInfoReq
	(*BlockTxListData)(nil),         // 15: blockchain.BlockTxListData
	(*BlockTxListResp)(nil),         // 16: blockchain.BlockTxListResp
	(*BlockTxListReq)(nil),          // 17: blockchain.BlockTxListReq
	(*BlockListRespInfo)(nil),       // 18: blockchain.BlockListRespInfo
	(*BlockListResp)(nil),           // 19: blockchain.BlockListResp
	(*BlockListReq)(nil),            // 20: blockchain.BlockListReq
	(*HomeDataRes)(nil),             // 21: blockchain.HomeDataRes
	(*NodeStatus)(nil),              // 22: blockchain.NodeStatus
	(*HomeDataMessageTrend)(nil),    // 23: blockchain.HomeDataMessageTrend
	(*DefaultReturn)(nil),           // 24: blockchain.DefaultReturn
	(*BlockRecordReq)(nil),          // 25: blockchain.BlockRecordReq
	(*WechatRequest)(nil),           // 26: blockchain.WechatRequest
	(*WechatRespond)(nil),           // 27: blockchain.WechatRespond
	(*MemberReq)(nil),               // 28: blockchain.MemberReq
	(*MemberList)(nil),              // 29: blockchain.MemberList
	(*Member)(nil),                  // 30: blockchain.Member
	(*MemberListResponse)(nil),      // 31: blockchain.MemberListResponse
	(*LedgerMember)(nil),            // 32: blockchain.LedgerMember
	(*Ledger)(nil),                  // 33: blockchain.Ledger
	(*LedgerListResponse)(nil),      // 34: blockchain.LedgerListResponse
	(*StoreQueryReq)(nil),           // 35: blockchain.StoreQueryReq
	(*StoreQueryRes)(nil),           // 36: blockchain.StoreQueryRes
	(*BlockHeightRes)(nil),          // 37: blockchain.BlockHeightRes
	(*BlockHeight)(nil),             // 38: blockchain.BlockHeight
	(*PubKeys)(nil),                 // 39: blockchain.PubKeys
	(*OrdinaryDepositReq)(nil),      // 40: blockchain.OrdinaryDepositReq
	(*OrdinaryDepositRes)(nil),      // 41: blockchain.OrdinaryDepositRes
	(*BlockHeader)(nil),             // 42: blockchain.BlockHeader
	(*DetailsBlockHeader)(nil),      // 43: blockchain.DetailsBlockHeader
	(*GetBlockDetailsResponse)(nil), // 44: blockchain.GetBlockDetailsResponse
	(*DealDetailHeader)(nil),        // 45: blockchain.DealDetailHeader
	(*DealDetailStore)(nil),         // 46: blockchain.DealDetailStore
	(*FindDealDetailResponse)(nil),  // 47: blockchain.FindDealDetailResponse
	(*RawHeader)(nil),               // 48: blockchain.RawHeader
	(*RawTxProof)(nil),              // 49: blockchain.RawTxProof
	(*FindDealRawResponse)(nil),     // 50: blockchain.FindDealRawResponse
}
var file_pb_blockchain_blockchain_proto_depIdxs = []int32{
	1,  // 0: blockchain.NoBlockList.data:type_name -> blockchain.NoBlockInfo
	5,  // 1: blockchain.KeepRecordListRes.data:type_name -> blockchain.KeepRecordData
	7,  // 2: blockchain.BlockTxResp.data:type_name -> blockchain.BlockTxData
	9,  // 3: blockchain.BlockInfoResp.txList:type_name -> blockchain.BlockDataInfoResp
	11, // 4: blockchain.BlockTxInfoResp.data:type_name -> blockchain.BlockTxDataInfoResp
	15, // 5: blockchain.BlockTxListResp.data:type_name -> blockchain.BlockTxListData
	18, // 6: blockchain.BlockListResp.data:type_name -> blockchain.BlockListRespInfo
	23, // 7: blockchain.HomeDataRes.list:type_name -> blockchain.HomeDataMessageTrend
	22, // 8: blockchain.HomeDataRes.nodeStatus:type_name -> blockchain.NodeStatus
	30, // 9: blockchain.MemberList.memberList:type_name -> blockchain.Member
	29, // 10: blockchain.MemberListResponse.data:type_name -> blockchain.MemberList
	32, // 11: blockchain.Ledger.memberList:type_name -> blockchain.LedgerMember
	33, // 12: blockchain.LedgerListResponse.ledgers:type_name -> blockchain.Ledger
	42, // 13: blockchain.DetailsBlockHeader.header:type_name -> blockchain.BlockHeader
	43, // 14: blockchain.GetBlockDetailsResponse.data:type_name -> blockchain.DetailsBlockHeader
	45, // 15: blockchain.FindDealDetailResponse.header:type_name -> blockchain.DealDetailHeader
	46, // 16: blockchain.FindDealDetailResponse.store:type_name -> blockchain.DealDetailStore
	48, // 17: blockchain.FindDealRawResponse.header:type_name -> blockchain.RawHeader
	49, // 18: blockchain.FindDealRawResponse.txproof:type_name -> blockchain.RawTxProof
	38, // 19: blockchain.Blockchain.FindBlockHeight:input_type -> blockchain.BlockHeight
	38, // 20: blockchain.Blockchain.FindBlockDetail:input_type -> blockchain.BlockHeight
	40, // 21: blockchain.Blockchain.CreateBlock:input_type -> blockchain.OrdinaryDepositReq
	38, // 22: blockchain.Blockchain.FindBlockIndex:input_type -> blockchain.BlockHeight
	35, // 23: blockchain.Blockchain.FindStoreQuery:input_type -> blockchain.StoreQueryReq
	38, // 24: blockchain.Blockchain.FindDealDetail:input_type -> blockchain.BlockHeight
	38, // 25: blockchain.Blockchain.FindDealRaw:input_type -> blockchain.BlockHeight
	38, // 26: blockchain.Blockchain.FindBlockHealth:input_type -> blockchain.BlockHeight
	38, // 27: blockchain.Blockchain.FindLedgerInfo:input_type -> blockchain.BlockHeight
	28, // 28: blockchain.Blockchain.FindMemberList:input_type -> blockchain.MemberReq
	25, // 29: blockchain.Blockchain.CreateBlockChainRecord:input_type -> blockchain.BlockRecordReq
	25, // 30: blockchain.Blockchain.UpdateBlockChainRecord:input_type -> blockchain.BlockRecordReq
	38, // 31: blockchain.Blockchain.FindNoBlockInfo:input_type -> blockchain.BlockHeight
	26, // 32: blockchain.Blockchain.Wechat:input_type -> blockchain.WechatRequest
	38, // 33: blockchain.Blockchain.FindHomeData:input_type -> blockchain.BlockHeight
	20, // 34: blockchain.Blockchain.FindBlockList:input_type -> blockchain.BlockListReq
	17, // 35: blockchain.Blockchain.FindBlockTxList:input_type -> blockchain.BlockTxListReq
	13, // 36: blockchain.Blockchain.FindBlockInfo:input_type -> blockchain.BlockInfoReq
	14, // 37: blockchain.Blockchain.FindBlockTxInfo:input_type -> blockchain.BlockTxInfoReq
	6,  // 38: blockchain.Blockchain.FindKeepRecordList:input_type -> blockchain.KeepRecordListReq
	2,  // 39: blockchain.Blockchain.FindPersonageInfo:input_type -> blockchain.PersonageInfoReq
	37, // 40: blockchain.Blockchain.FindBlockHeight:output_type -> blockchain.BlockHeightRes
	44, // 41: blockchain.Blockchain.FindBlockDetail:output_type -> blockchain.GetBlockDetailsResponse
	41, // 42: blockchain.Blockchain.CreateBlock:output_type -> blockchain.OrdinaryDepositRes
	36, // 43: blockchain.Blockchain.FindBlockIndex:output_type -> blockchain.StoreQueryRes
	36, // 44: blockchain.Blockchain.FindStoreQuery:output_type -> blockchain.StoreQueryRes
	47, // 45: blockchain.Blockchain.FindDealDetail:output_type -> blockchain.FindDealDetailResponse
	50, // 46: blockchain.Blockchain.FindDealRaw:output_type -> blockchain.FindDealRawResponse
	37, // 47: blockchain.Blockchain.FindBlockHealth:output_type -> blockchain.BlockHeightRes
	34, // 48: blockchain.Blockchain.FindLedgerInfo:output_type -> blockchain.LedgerListResponse
	31, // 49: blockchain.Blockchain.FindMemberList:output_type -> blockchain.MemberListResponse
	24, // 50: blockchain.Blockchain.CreateBlockChainRecord:output_type -> blockchain.DefaultReturn
	24, // 51: blockchain.Blockchain.UpdateBlockChainRecord:output_type -> blockchain.DefaultReturn
	0,  // 52: blockchain.Blockchain.FindNoBlockInfo:output_type -> blockchain.NoBlockList
	27, // 53: blockchain.Blockchain.Wechat:output_type -> blockchain.WechatRespond
	21, // 54: blockchain.Blockchain.FindHomeData:output_type -> blockchain.HomeDataRes
	19, // 55: blockchain.Blockchain.FindBlockList:output_type -> blockchain.BlockListResp
	16, // 56: blockchain.Blockchain.FindBlockTxList:output_type -> blockchain.BlockTxListResp
	10, // 57: blockchain.Blockchain.FindBlockInfo:output_type -> blockchain.BlockInfoResp
	11, // 58: blockchain.Blockchain.FindBlockTxInfo:output_type -> blockchain.BlockTxDataInfoResp
	4,  // 59: blockchain.Blockchain.FindKeepRecordList:output_type -> blockchain.KeepRecordListRes
	3,  // 60: blockchain.Blockchain.FindPersonageInfo:output_type -> blockchain.PersonageInfoRes
	40, // [40:61] is the sub-list for method output_type
	19, // [19:40] is the sub-list for method input_type
	19, // [19:19] is the sub-list for extension type_name
	19, // [19:19] is the sub-list for extension extendee
	0,  // [0:19] is the sub-list for field type_name
}

func init() { file_pb_blockchain_blockchain_proto_init() }
func file_pb_blockchain_blockchain_proto_init() {
	if File_pb_blockchain_blockchain_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_pb_blockchain_blockchain_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*NoBlockList); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_blockchain_blockchain_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*NoBlockInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_blockchain_blockchain_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PersonageInfoReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_blockchain_blockchain_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PersonageInfoRes); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_blockchain_blockchain_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*KeepRecordListRes); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_blockchain_blockchain_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*KeepRecordData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_blockchain_blockchain_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*KeepRecordListReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_blockchain_blockchain_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BlockTxData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_blockchain_blockchain_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BlockTxResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_blockchain_blockchain_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BlockDataInfoResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_blockchain_blockchain_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BlockInfoResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_blockchain_blockchain_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BlockTxDataInfoResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_blockchain_blockchain_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BlockTxInfoResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_blockchain_blockchain_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BlockInfoReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_blockchain_blockchain_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BlockTxInfoReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_blockchain_blockchain_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BlockTxListData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_blockchain_blockchain_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BlockTxListResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_blockchain_blockchain_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BlockTxListReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_blockchain_blockchain_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BlockListRespInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_blockchain_blockchain_proto_msgTypes[19].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BlockListResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_blockchain_blockchain_proto_msgTypes[20].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BlockListReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_blockchain_blockchain_proto_msgTypes[21].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*HomeDataRes); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_blockchain_blockchain_proto_msgTypes[22].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*NodeStatus); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_blockchain_blockchain_proto_msgTypes[23].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*HomeDataMessageTrend); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_blockchain_blockchain_proto_msgTypes[24].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DefaultReturn); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_blockchain_blockchain_proto_msgTypes[25].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BlockRecordReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_blockchain_blockchain_proto_msgTypes[26].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*WechatRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_blockchain_blockchain_proto_msgTypes[27].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*WechatRespond); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_blockchain_blockchain_proto_msgTypes[28].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MemberReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_blockchain_blockchain_proto_msgTypes[29].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MemberList); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_blockchain_blockchain_proto_msgTypes[30].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Member); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_blockchain_blockchain_proto_msgTypes[31].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MemberListResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_blockchain_blockchain_proto_msgTypes[32].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LedgerMember); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_blockchain_blockchain_proto_msgTypes[33].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Ledger); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_blockchain_blockchain_proto_msgTypes[34].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LedgerListResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_blockchain_blockchain_proto_msgTypes[35].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*StoreQueryReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_blockchain_blockchain_proto_msgTypes[36].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*StoreQueryRes); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_blockchain_blockchain_proto_msgTypes[37].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BlockHeightRes); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_blockchain_blockchain_proto_msgTypes[38].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BlockHeight); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_blockchain_blockchain_proto_msgTypes[39].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PubKeys); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_blockchain_blockchain_proto_msgTypes[40].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*OrdinaryDepositReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_blockchain_blockchain_proto_msgTypes[41].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*OrdinaryDepositRes); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_blockchain_blockchain_proto_msgTypes[42].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BlockHeader); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_blockchain_blockchain_proto_msgTypes[43].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DetailsBlockHeader); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_blockchain_blockchain_proto_msgTypes[44].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetBlockDetailsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_blockchain_blockchain_proto_msgTypes[45].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DealDetailHeader); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_blockchain_blockchain_proto_msgTypes[46].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DealDetailStore); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_blockchain_blockchain_proto_msgTypes[47].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FindDealDetailResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_blockchain_blockchain_proto_msgTypes[48].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RawHeader); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_blockchain_blockchain_proto_msgTypes[49].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RawTxProof); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_blockchain_blockchain_proto_msgTypes[50].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FindDealRawResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_pb_blockchain_blockchain_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   51,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_pb_blockchain_blockchain_proto_goTypes,
		DependencyIndexes: file_pb_blockchain_blockchain_proto_depIdxs,
		MessageInfos:      file_pb_blockchain_blockchain_proto_msgTypes,
	}.Build()
	File_pb_blockchain_blockchain_proto = out.File
	file_pb_blockchain_blockchain_proto_rawDesc = nil
	file_pb_blockchain_blockchain_proto_goTypes = nil
	file_pb_blockchain_blockchain_proto_depIdxs = nil
}
