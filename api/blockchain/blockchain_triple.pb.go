// Code generated by protoc-gen-go-triple. DO NOT EDIT.
// versions:
// - protoc-gen-go-triple v1.0.8
// - protoc             v4.24.0--rc1
// source: pb/blockchain/blockchain.proto

package blockchain

import (
	context "context"
	protocol "dubbo.apache.org/dubbo-go/v3/protocol"
	dubbo3 "dubbo.apache.org/dubbo-go/v3/protocol/dubbo3"
	invocation "dubbo.apache.org/dubbo-go/v3/protocol/invocation"
	grpc_go "github.com/dubbogo/grpc-go"
	codes "github.com/dubbogo/grpc-go/codes"
	metadata "github.com/dubbogo/grpc-go/metadata"
	status "github.com/dubbogo/grpc-go/status"
	common "github.com/dubbogo/triple/pkg/common"
	constant "github.com/dubbogo/triple/pkg/common/constant"
	triple "github.com/dubbogo/triple/pkg/triple"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc_go.SupportPackageIsVersion7

// BlockchainClient is the client API for Blockchain service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type BlockchainClient interface {
	FindBlockHeight(ctx context.Context, in *BlockHeight, opts ...grpc_go.CallOption) (*BlockHeightRes, common.ErrorWithAttachment)
	FindBlockDetail(ctx context.Context, in *BlockHeight, opts ...grpc_go.CallOption) (*GetBlockDetailsResponse, common.ErrorWithAttachment)
	CreateBlock(ctx context.Context, in *OrdinaryDepositReq, opts ...grpc_go.CallOption) (*OrdinaryDepositRes, common.ErrorWithAttachment)
	FindBlockIndex(ctx context.Context, in *BlockHeight, opts ...grpc_go.CallOption) (*StoreQueryRes, common.ErrorWithAttachment)
	FindStoreQuery(ctx context.Context, in *StoreQueryReq, opts ...grpc_go.CallOption) (*StoreQueryRes, common.ErrorWithAttachment)
	FindDealDetail(ctx context.Context, in *BlockHeight, opts ...grpc_go.CallOption) (*FindDealDetailResponse, common.ErrorWithAttachment)
	FindDealRaw(ctx context.Context, in *BlockHeight, opts ...grpc_go.CallOption) (*FindDealRawResponse, common.ErrorWithAttachment)
	FindBlockHealth(ctx context.Context, in *BlockHeight, opts ...grpc_go.CallOption) (*BlockHeightRes, common.ErrorWithAttachment)
	FindLedgerInfo(ctx context.Context, in *BlockHeight, opts ...grpc_go.CallOption) (*LedgerListResponse, common.ErrorWithAttachment)
	FindMemberList(ctx context.Context, in *MemberReq, opts ...grpc_go.CallOption) (*MemberListResponse, common.ErrorWithAttachment)
	// 区块记录接口
	CreateBlockChainRecord(ctx context.Context, in *BlockRecordReq, opts ...grpc_go.CallOption) (*DefaultReturn, common.ErrorWithAttachment)
	UpdateBlockChainRecord(ctx context.Context, in *BlockRecordReq, opts ...grpc_go.CallOption) (*DefaultReturn, common.ErrorWithAttachment)
	FindNoBlockInfo(ctx context.Context, in *BlockHeight, opts ...grpc_go.CallOption) (*NoBlockList, common.ErrorWithAttachment)
	Wechat(ctx context.Context, in *WechatRequest, opts ...grpc_go.CallOption) (*WechatRespond, common.ErrorWithAttachment)
	//浏览器接口
	FindHomeData(ctx context.Context, in *BlockHeight, opts ...grpc_go.CallOption) (*HomeDataRes, common.ErrorWithAttachment)
	FindBlockList(ctx context.Context, in *BlockListReq, opts ...grpc_go.CallOption) (*BlockListResp, common.ErrorWithAttachment)
	FindBlockTxList(ctx context.Context, in *BlockTxListReq, opts ...grpc_go.CallOption) (*BlockTxListResp, common.ErrorWithAttachment)
	FindBlockInfo(ctx context.Context, in *BlockInfoReq, opts ...grpc_go.CallOption) (*BlockInfoResp, common.ErrorWithAttachment)
	FindBlockTxInfo(ctx context.Context, in *BlockTxInfoReq, opts ...grpc_go.CallOption) (*BlockTxDataInfoResp, common.ErrorWithAttachment)
	FindKeepRecordList(ctx context.Context, in *KeepRecordListReq, opts ...grpc_go.CallOption) (*KeepRecordListRes, common.ErrorWithAttachment)
	FindPersonageInfo(ctx context.Context, in *PersonageInfoReq, opts ...grpc_go.CallOption) (*PersonageInfoRes, common.ErrorWithAttachment)
}

type blockchainClient struct {
	cc *triple.TripleConn
}

type BlockchainClientImpl struct {
	FindBlockHeight        func(ctx context.Context, in *BlockHeight) (*BlockHeightRes, error)
	FindBlockDetail        func(ctx context.Context, in *BlockHeight) (*GetBlockDetailsResponse, error)
	CreateBlock            func(ctx context.Context, in *OrdinaryDepositReq) (*OrdinaryDepositRes, error)
	FindBlockIndex         func(ctx context.Context, in *BlockHeight) (*StoreQueryRes, error)
	FindStoreQuery         func(ctx context.Context, in *StoreQueryReq) (*StoreQueryRes, error)
	FindDealDetail         func(ctx context.Context, in *BlockHeight) (*FindDealDetailResponse, error)
	FindDealRaw            func(ctx context.Context, in *BlockHeight) (*FindDealRawResponse, error)
	FindBlockHealth        func(ctx context.Context, in *BlockHeight) (*BlockHeightRes, error)
	FindLedgerInfo         func(ctx context.Context, in *BlockHeight) (*LedgerListResponse, error)
	FindMemberList         func(ctx context.Context, in *MemberReq) (*MemberListResponse, error)
	CreateBlockChainRecord func(ctx context.Context, in *BlockRecordReq) (*DefaultReturn, error)
	UpdateBlockChainRecord func(ctx context.Context, in *BlockRecordReq) (*DefaultReturn, error)
	FindNoBlockInfo        func(ctx context.Context, in *BlockHeight) (*NoBlockList, error)
	Wechat                 func(ctx context.Context, in *WechatRequest) (*WechatRespond, error)
	FindHomeData           func(ctx context.Context, in *BlockHeight) (*HomeDataRes, error)
	FindBlockList          func(ctx context.Context, in *BlockListReq) (*BlockListResp, error)
	FindBlockTxList        func(ctx context.Context, in *BlockTxListReq) (*BlockTxListResp, error)
	FindBlockInfo          func(ctx context.Context, in *BlockInfoReq) (*BlockInfoResp, error)
	FindBlockTxInfo        func(ctx context.Context, in *BlockTxInfoReq) (*BlockTxDataInfoResp, error)
	FindKeepRecordList     func(ctx context.Context, in *KeepRecordListReq) (*KeepRecordListRes, error)
	FindPersonageInfo      func(ctx context.Context, in *PersonageInfoReq) (*PersonageInfoRes, error)
}

func (c *BlockchainClientImpl) GetDubboStub(cc *triple.TripleConn) BlockchainClient {
	return NewBlockchainClient(cc)
}

func (c *BlockchainClientImpl) XXX_InterfaceName() string {
	return "blockchain.Blockchain"
}

func NewBlockchainClient(cc *triple.TripleConn) BlockchainClient {
	return &blockchainClient{cc}
}

func (c *blockchainClient) FindBlockHeight(ctx context.Context, in *BlockHeight, opts ...grpc_go.CallOption) (*BlockHeightRes, common.ErrorWithAttachment) {
	out := new(BlockHeightRes)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/FindBlockHeight", in, out)
}

func (c *blockchainClient) FindBlockDetail(ctx context.Context, in *BlockHeight, opts ...grpc_go.CallOption) (*GetBlockDetailsResponse, common.ErrorWithAttachment) {
	out := new(GetBlockDetailsResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/FindBlockDetail", in, out)
}

func (c *blockchainClient) CreateBlock(ctx context.Context, in *OrdinaryDepositReq, opts ...grpc_go.CallOption) (*OrdinaryDepositRes, common.ErrorWithAttachment) {
	out := new(OrdinaryDepositRes)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/CreateBlock", in, out)
}

func (c *blockchainClient) FindBlockIndex(ctx context.Context, in *BlockHeight, opts ...grpc_go.CallOption) (*StoreQueryRes, common.ErrorWithAttachment) {
	out := new(StoreQueryRes)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/FindBlockIndex", in, out)
}

func (c *blockchainClient) FindStoreQuery(ctx context.Context, in *StoreQueryReq, opts ...grpc_go.CallOption) (*StoreQueryRes, common.ErrorWithAttachment) {
	out := new(StoreQueryRes)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/FindStoreQuery", in, out)
}

func (c *blockchainClient) FindDealDetail(ctx context.Context, in *BlockHeight, opts ...grpc_go.CallOption) (*FindDealDetailResponse, common.ErrorWithAttachment) {
	out := new(FindDealDetailResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/FindDealDetail", in, out)
}

func (c *blockchainClient) FindDealRaw(ctx context.Context, in *BlockHeight, opts ...grpc_go.CallOption) (*FindDealRawResponse, common.ErrorWithAttachment) {
	out := new(FindDealRawResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/FindDealRaw", in, out)
}

func (c *blockchainClient) FindBlockHealth(ctx context.Context, in *BlockHeight, opts ...grpc_go.CallOption) (*BlockHeightRes, common.ErrorWithAttachment) {
	out := new(BlockHeightRes)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/FindBlockHealth", in, out)
}

func (c *blockchainClient) FindLedgerInfo(ctx context.Context, in *BlockHeight, opts ...grpc_go.CallOption) (*LedgerListResponse, common.ErrorWithAttachment) {
	out := new(LedgerListResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/FindLedgerInfo", in, out)
}

func (c *blockchainClient) FindMemberList(ctx context.Context, in *MemberReq, opts ...grpc_go.CallOption) (*MemberListResponse, common.ErrorWithAttachment) {
	out := new(MemberListResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/FindMemberList", in, out)
}

func (c *blockchainClient) CreateBlockChainRecord(ctx context.Context, in *BlockRecordReq, opts ...grpc_go.CallOption) (*DefaultReturn, common.ErrorWithAttachment) {
	out := new(DefaultReturn)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/CreateBlockChainRecord", in, out)
}

func (c *blockchainClient) UpdateBlockChainRecord(ctx context.Context, in *BlockRecordReq, opts ...grpc_go.CallOption) (*DefaultReturn, common.ErrorWithAttachment) {
	out := new(DefaultReturn)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/UpdateBlockChainRecord", in, out)
}

func (c *blockchainClient) FindNoBlockInfo(ctx context.Context, in *BlockHeight, opts ...grpc_go.CallOption) (*NoBlockList, common.ErrorWithAttachment) {
	out := new(NoBlockList)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/FindNoBlockInfo", in, out)
}

func (c *blockchainClient) Wechat(ctx context.Context, in *WechatRequest, opts ...grpc_go.CallOption) (*WechatRespond, common.ErrorWithAttachment) {
	out := new(WechatRespond)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/Wechat", in, out)
}

func (c *blockchainClient) FindHomeData(ctx context.Context, in *BlockHeight, opts ...grpc_go.CallOption) (*HomeDataRes, common.ErrorWithAttachment) {
	out := new(HomeDataRes)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/FindHomeData", in, out)
}

func (c *blockchainClient) FindBlockList(ctx context.Context, in *BlockListReq, opts ...grpc_go.CallOption) (*BlockListResp, common.ErrorWithAttachment) {
	out := new(BlockListResp)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/FindBlockList", in, out)
}

func (c *blockchainClient) FindBlockTxList(ctx context.Context, in *BlockTxListReq, opts ...grpc_go.CallOption) (*BlockTxListResp, common.ErrorWithAttachment) {
	out := new(BlockTxListResp)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/FindBlockTxList", in, out)
}

func (c *blockchainClient) FindBlockInfo(ctx context.Context, in *BlockInfoReq, opts ...grpc_go.CallOption) (*BlockInfoResp, common.ErrorWithAttachment) {
	out := new(BlockInfoResp)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/FindBlockInfo", in, out)
}

func (c *blockchainClient) FindBlockTxInfo(ctx context.Context, in *BlockTxInfoReq, opts ...grpc_go.CallOption) (*BlockTxDataInfoResp, common.ErrorWithAttachment) {
	out := new(BlockTxDataInfoResp)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/FindBlockTxInfo", in, out)
}

func (c *blockchainClient) FindKeepRecordList(ctx context.Context, in *KeepRecordListReq, opts ...grpc_go.CallOption) (*KeepRecordListRes, common.ErrorWithAttachment) {
	out := new(KeepRecordListRes)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/FindKeepRecordList", in, out)
}

func (c *blockchainClient) FindPersonageInfo(ctx context.Context, in *PersonageInfoReq, opts ...grpc_go.CallOption) (*PersonageInfoRes, common.ErrorWithAttachment) {
	out := new(PersonageInfoRes)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/FindPersonageInfo", in, out)
}

// BlockchainServer is the server API for Blockchain service.
// All implementations must embed UnimplementedBlockchainServer
// for forward compatibility
type BlockchainServer interface {
	FindBlockHeight(context.Context, *BlockHeight) (*BlockHeightRes, error)
	FindBlockDetail(context.Context, *BlockHeight) (*GetBlockDetailsResponse, error)
	CreateBlock(context.Context, *OrdinaryDepositReq) (*OrdinaryDepositRes, error)
	FindBlockIndex(context.Context, *BlockHeight) (*StoreQueryRes, error)
	FindStoreQuery(context.Context, *StoreQueryReq) (*StoreQueryRes, error)
	FindDealDetail(context.Context, *BlockHeight) (*FindDealDetailResponse, error)
	FindDealRaw(context.Context, *BlockHeight) (*FindDealRawResponse, error)
	FindBlockHealth(context.Context, *BlockHeight) (*BlockHeightRes, error)
	FindLedgerInfo(context.Context, *BlockHeight) (*LedgerListResponse, error)
	FindMemberList(context.Context, *MemberReq) (*MemberListResponse, error)
	// 区块记录接口
	CreateBlockChainRecord(context.Context, *BlockRecordReq) (*DefaultReturn, error)
	UpdateBlockChainRecord(context.Context, *BlockRecordReq) (*DefaultReturn, error)
	FindNoBlockInfo(context.Context, *BlockHeight) (*NoBlockList, error)
	Wechat(context.Context, *WechatRequest) (*WechatRespond, error)
	//浏览器接口
	FindHomeData(context.Context, *BlockHeight) (*HomeDataRes, error)
	FindBlockList(context.Context, *BlockListReq) (*BlockListResp, error)
	FindBlockTxList(context.Context, *BlockTxListReq) (*BlockTxListResp, error)
	FindBlockInfo(context.Context, *BlockInfoReq) (*BlockInfoResp, error)
	FindBlockTxInfo(context.Context, *BlockTxInfoReq) (*BlockTxDataInfoResp, error)
	FindKeepRecordList(context.Context, *KeepRecordListReq) (*KeepRecordListRes, error)
	FindPersonageInfo(context.Context, *PersonageInfoReq) (*PersonageInfoRes, error)
	mustEmbedUnimplementedBlockchainServer()
}

// UnimplementedBlockchainServer must be embedded to have forward compatible implementations.
type UnimplementedBlockchainServer struct {
	proxyImpl protocol.Invoker
}

func (UnimplementedBlockchainServer) FindBlockHeight(context.Context, *BlockHeight) (*BlockHeightRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FindBlockHeight not implemented")
}
func (UnimplementedBlockchainServer) FindBlockDetail(context.Context, *BlockHeight) (*GetBlockDetailsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FindBlockDetail not implemented")
}
func (UnimplementedBlockchainServer) CreateBlock(context.Context, *OrdinaryDepositReq) (*OrdinaryDepositRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateBlock not implemented")
}
func (UnimplementedBlockchainServer) FindBlockIndex(context.Context, *BlockHeight) (*StoreQueryRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FindBlockIndex not implemented")
}
func (UnimplementedBlockchainServer) FindStoreQuery(context.Context, *StoreQueryReq) (*StoreQueryRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FindStoreQuery not implemented")
}
func (UnimplementedBlockchainServer) FindDealDetail(context.Context, *BlockHeight) (*FindDealDetailResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FindDealDetail not implemented")
}
func (UnimplementedBlockchainServer) FindDealRaw(context.Context, *BlockHeight) (*FindDealRawResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FindDealRaw not implemented")
}
func (UnimplementedBlockchainServer) FindBlockHealth(context.Context, *BlockHeight) (*BlockHeightRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FindBlockHealth not implemented")
}
func (UnimplementedBlockchainServer) FindLedgerInfo(context.Context, *BlockHeight) (*LedgerListResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FindLedgerInfo not implemented")
}
func (UnimplementedBlockchainServer) FindMemberList(context.Context, *MemberReq) (*MemberListResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FindMemberList not implemented")
}
func (UnimplementedBlockchainServer) CreateBlockChainRecord(context.Context, *BlockRecordReq) (*DefaultReturn, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateBlockChainRecord not implemented")
}
func (UnimplementedBlockchainServer) UpdateBlockChainRecord(context.Context, *BlockRecordReq) (*DefaultReturn, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateBlockChainRecord not implemented")
}
func (UnimplementedBlockchainServer) FindNoBlockInfo(context.Context, *BlockHeight) (*NoBlockList, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FindNoBlockInfo not implemented")
}
func (UnimplementedBlockchainServer) Wechat(context.Context, *WechatRequest) (*WechatRespond, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Wechat not implemented")
}
func (UnimplementedBlockchainServer) FindHomeData(context.Context, *BlockHeight) (*HomeDataRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FindHomeData not implemented")
}
func (UnimplementedBlockchainServer) FindBlockList(context.Context, *BlockListReq) (*BlockListResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FindBlockList not implemented")
}
func (UnimplementedBlockchainServer) FindBlockTxList(context.Context, *BlockTxListReq) (*BlockTxListResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FindBlockTxList not implemented")
}
func (UnimplementedBlockchainServer) FindBlockInfo(context.Context, *BlockInfoReq) (*BlockInfoResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FindBlockInfo not implemented")
}
func (UnimplementedBlockchainServer) FindBlockTxInfo(context.Context, *BlockTxInfoReq) (*BlockTxDataInfoResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FindBlockTxInfo not implemented")
}
func (UnimplementedBlockchainServer) FindKeepRecordList(context.Context, *KeepRecordListReq) (*KeepRecordListRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FindKeepRecordList not implemented")
}
func (UnimplementedBlockchainServer) FindPersonageInfo(context.Context, *PersonageInfoReq) (*PersonageInfoRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FindPersonageInfo not implemented")
}
func (s *UnimplementedBlockchainServer) XXX_SetProxyImpl(impl protocol.Invoker) {
	s.proxyImpl = impl
}

func (s *UnimplementedBlockchainServer) XXX_GetProxyImpl() protocol.Invoker {
	return s.proxyImpl
}

func (s *UnimplementedBlockchainServer) XXX_ServiceDesc() *grpc_go.ServiceDesc {
	return &Blockchain_ServiceDesc
}
func (s *UnimplementedBlockchainServer) XXX_InterfaceName() string {
	return "blockchain.Blockchain"
}

func (UnimplementedBlockchainServer) mustEmbedUnimplementedBlockchainServer() {}

// UnsafeBlockchainServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to BlockchainServer will
// result in compilation errors.
type UnsafeBlockchainServer interface {
	mustEmbedUnimplementedBlockchainServer()
}

func RegisterBlockchainServer(s grpc_go.ServiceRegistrar, srv BlockchainServer) {
	s.RegisterService(&Blockchain_ServiceDesc, srv)
}

func _Blockchain_FindBlockHeight_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(BlockHeight)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("FindBlockHeight", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Blockchain_FindBlockDetail_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(BlockHeight)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("FindBlockDetail", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Blockchain_CreateBlock_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(OrdinaryDepositReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("CreateBlock", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Blockchain_FindBlockIndex_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(BlockHeight)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("FindBlockIndex", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Blockchain_FindStoreQuery_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(StoreQueryReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("FindStoreQuery", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Blockchain_FindDealDetail_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(BlockHeight)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("FindDealDetail", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Blockchain_FindDealRaw_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(BlockHeight)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("FindDealRaw", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Blockchain_FindBlockHealth_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(BlockHeight)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("FindBlockHealth", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Blockchain_FindLedgerInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(BlockHeight)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("FindLedgerInfo", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Blockchain_FindMemberList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(MemberReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("FindMemberList", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Blockchain_CreateBlockChainRecord_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(BlockRecordReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("CreateBlockChainRecord", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Blockchain_UpdateBlockChainRecord_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(BlockRecordReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("UpdateBlockChainRecord", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Blockchain_FindNoBlockInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(BlockHeight)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("FindNoBlockInfo", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Blockchain_Wechat_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(WechatRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("Wechat", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Blockchain_FindHomeData_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(BlockHeight)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("FindHomeData", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Blockchain_FindBlockList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(BlockListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("FindBlockList", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Blockchain_FindBlockTxList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(BlockTxListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("FindBlockTxList", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Blockchain_FindBlockInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(BlockInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("FindBlockInfo", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Blockchain_FindBlockTxInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(BlockTxInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("FindBlockTxInfo", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Blockchain_FindKeepRecordList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(KeepRecordListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("FindKeepRecordList", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Blockchain_FindPersonageInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(PersonageInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("FindPersonageInfo", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

// Blockchain_ServiceDesc is the grpc_go.ServiceDesc for Blockchain service.
// It's only intended for direct use with grpc_go.RegisterService,
// and not to be introspected or modified (even as a copy)
var Blockchain_ServiceDesc = grpc_go.ServiceDesc{
	ServiceName: "blockchain.Blockchain",
	HandlerType: (*BlockchainServer)(nil),
	Methods: []grpc_go.MethodDesc{
		{
			MethodName: "FindBlockHeight",
			Handler:    _Blockchain_FindBlockHeight_Handler,
		},
		{
			MethodName: "FindBlockDetail",
			Handler:    _Blockchain_FindBlockDetail_Handler,
		},
		{
			MethodName: "CreateBlock",
			Handler:    _Blockchain_CreateBlock_Handler,
		},
		{
			MethodName: "FindBlockIndex",
			Handler:    _Blockchain_FindBlockIndex_Handler,
		},
		{
			MethodName: "FindStoreQuery",
			Handler:    _Blockchain_FindStoreQuery_Handler,
		},
		{
			MethodName: "FindDealDetail",
			Handler:    _Blockchain_FindDealDetail_Handler,
		},
		{
			MethodName: "FindDealRaw",
			Handler:    _Blockchain_FindDealRaw_Handler,
		},
		{
			MethodName: "FindBlockHealth",
			Handler:    _Blockchain_FindBlockHealth_Handler,
		},
		{
			MethodName: "FindLedgerInfo",
			Handler:    _Blockchain_FindLedgerInfo_Handler,
		},
		{
			MethodName: "FindMemberList",
			Handler:    _Blockchain_FindMemberList_Handler,
		},
		{
			MethodName: "CreateBlockChainRecord",
			Handler:    _Blockchain_CreateBlockChainRecord_Handler,
		},
		{
			MethodName: "UpdateBlockChainRecord",
			Handler:    _Blockchain_UpdateBlockChainRecord_Handler,
		},
		{
			MethodName: "FindNoBlockInfo",
			Handler:    _Blockchain_FindNoBlockInfo_Handler,
		},
		{
			MethodName: "Wechat",
			Handler:    _Blockchain_Wechat_Handler,
		},
		{
			MethodName: "FindHomeData",
			Handler:    _Blockchain_FindHomeData_Handler,
		},
		{
			MethodName: "FindBlockList",
			Handler:    _Blockchain_FindBlockList_Handler,
		},
		{
			MethodName: "FindBlockTxList",
			Handler:    _Blockchain_FindBlockTxList_Handler,
		},
		{
			MethodName: "FindBlockInfo",
			Handler:    _Blockchain_FindBlockInfo_Handler,
		},
		{
			MethodName: "FindBlockTxInfo",
			Handler:    _Blockchain_FindBlockTxInfo_Handler,
		},
		{
			MethodName: "FindKeepRecordList",
			Handler:    _Blockchain_FindKeepRecordList_Handler,
		},
		{
			MethodName: "FindPersonageInfo",
			Handler:    _Blockchain_FindPersonageInfo_Handler,
		},
	},
	Streams:  []grpc_go.StreamDesc{},
	Metadata: "pb/blockchain/blockchain.proto",
}
