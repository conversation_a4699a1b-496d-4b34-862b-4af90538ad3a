// Code generated by protoc-gen-gogo. DO NOT EDIT.
// source: pb/blockchain/blockchain.proto

package blockchain

import (
	fmt "fmt"
	math "math"
	proto "github.com/golang/protobuf/proto"
	github_com_mwitkow_go_proto_validators "github.com/mwitkow/go-proto-validators"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

func (this *NoBlockList) Validate() error {
	for _, item := range this.Data {
		if item != nil {
			if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(item); err != nil {
				return github_com_mwitkow_go_proto_validators.FieldError("Data", err)
			}
		}
	}
	return nil
}
func (this *NoBlockInfo) Validate() error {
	return nil
}
func (this *PersonageInfoReq) Validate() error {
	return nil
}
func (this *PersonageInfoRes) Validate() error {
	return nil
}
func (this *KeepRecordListRes) Validate() error {
	for _, item := range this.Data {
		if item != nil {
			if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(item); err != nil {
				return github_com_mwitkow_go_proto_validators.FieldError("Data", err)
			}
		}
	}
	return nil
}
func (this *KeepRecordData) Validate() error {
	return nil
}
func (this *KeepRecordListReq) Validate() error {
	return nil
}
func (this *BlockTxData) Validate() error {
	return nil
}
func (this *BlockTxResp) Validate() error {
	for _, item := range this.Data {
		if item != nil {
			if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(item); err != nil {
				return github_com_mwitkow_go_proto_validators.FieldError("Data", err)
			}
		}
	}
	return nil
}
func (this *BlockDataInfoResp) Validate() error {
	return nil
}
func (this *BlockInfoResp) Validate() error {
	for _, item := range this.TxList {
		if item != nil {
			if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(item); err != nil {
				return github_com_mwitkow_go_proto_validators.FieldError("TxList", err)
			}
		}
	}
	return nil
}
func (this *BlockTxDataInfoResp) Validate() error {
	return nil
}
func (this *BlockTxInfoResp) Validate() error {
	if this.Data != nil {
		if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(this.Data); err != nil {
			return github_com_mwitkow_go_proto_validators.FieldError("Data", err)
		}
	}
	return nil
}
func (this *BlockInfoReq) Validate() error {
	return nil
}
func (this *BlockTxInfoReq) Validate() error {
	return nil
}
func (this *BlockTxListData) Validate() error {
	return nil
}
func (this *BlockTxListResp) Validate() error {
	for _, item := range this.Data {
		if item != nil {
			if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(item); err != nil {
				return github_com_mwitkow_go_proto_validators.FieldError("Data", err)
			}
		}
	}
	return nil
}
func (this *BlockTxListReq) Validate() error {
	return nil
}
func (this *BlockListRespInfo) Validate() error {
	return nil
}
func (this *BlockListResp) Validate() error {
	for _, item := range this.Data {
		if item != nil {
			if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(item); err != nil {
				return github_com_mwitkow_go_proto_validators.FieldError("Data", err)
			}
		}
	}
	return nil
}
func (this *BlockListReq) Validate() error {
	return nil
}
func (this *HomeDataRes) Validate() error {
	for _, item := range this.List {
		if item != nil {
			if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(item); err != nil {
				return github_com_mwitkow_go_proto_validators.FieldError("List", err)
			}
		}
	}
	for _, item := range this.NodeStatus {
		if item != nil {
			if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(item); err != nil {
				return github_com_mwitkow_go_proto_validators.FieldError("NodeStatus", err)
			}
		}
	}
	return nil
}
func (this *NodeStatus) Validate() error {
	return nil
}
func (this *HomeDataMessageTrend) Validate() error {
	return nil
}
func (this *DefaultReturn) Validate() error {
	return nil
}
func (this *BlockRecordReq) Validate() error {
	return nil
}
func (this *WechatRequest) Validate() error {
	return nil
}
func (this *WechatRespond) Validate() error {
	return nil
}
func (this *MemberReq) Validate() error {
	return nil
}
func (this *MemberList) Validate() error {
	for _, item := range this.MemberList {
		if item != nil {
			if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(item); err != nil {
				return github_com_mwitkow_go_proto_validators.FieldError("MemberList", err)
			}
		}
	}
	return nil
}
func (this *Member) Validate() error {
	return nil
}
func (this *MemberListResponse) Validate() error {
	if this.Data != nil {
		if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(this.Data); err != nil {
			return github_com_mwitkow_go_proto_validators.FieldError("Data", err)
		}
	}
	return nil
}
func (this *LedgerMember) Validate() error {
	return nil
}
func (this *Ledger) Validate() error {
	for _, item := range this.MemberList {
		if item != nil {
			if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(item); err != nil {
				return github_com_mwitkow_go_proto_validators.FieldError("MemberList", err)
			}
		}
	}
	return nil
}
func (this *LedgerListResponse) Validate() error {
	for _, item := range this.Ledgers {
		if item != nil {
			if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(item); err != nil {
				return github_com_mwitkow_go_proto_validators.FieldError("Ledgers", err)
			}
		}
	}
	return nil
}
func (this *StoreQueryReq) Validate() error {
	return nil
}
func (this *StoreQueryRes) Validate() error {
	return nil
}
func (this *BlockHeightRes) Validate() error {
	return nil
}
func (this *BlockHeight) Validate() error {
	return nil
}
func (this *PubKeys) Validate() error {
	return nil
}
func (this *OrdinaryDepositReq) Validate() error {
	return nil
}
func (this *OrdinaryDepositRes) Validate() error {
	return nil
}
func (this *BlockHeader) Validate() error {
	return nil
}
func (this *DetailsBlockHeader) Validate() error {
	if this.Header != nil {
		if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(this.Header); err != nil {
			return github_com_mwitkow_go_proto_validators.FieldError("Header", err)
		}
	}
	return nil
}
func (this *GetBlockDetailsResponse) Validate() error {
	if this.Data != nil {
		if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(this.Data); err != nil {
			return github_com_mwitkow_go_proto_validators.FieldError("Data", err)
		}
	}
	return nil
}
func (this *DealDetailHeader) Validate() error {
	return nil
}
func (this *DealDetailStore) Validate() error {
	return nil
}
func (this *FindDealDetailResponse) Validate() error {
	if this.Header != nil {
		if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(this.Header); err != nil {
			return github_com_mwitkow_go_proto_validators.FieldError("Header", err)
		}
	}
	if this.Store != nil {
		if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(this.Store); err != nil {
			return github_com_mwitkow_go_proto_validators.FieldError("Store", err)
		}
	}
	return nil
}
func (this *RawHeader) Validate() error {
	return nil
}
func (this *RawTxProof) Validate() error {
	return nil
}
func (this *FindDealRawResponse) Validate() error {
	if this.Header != nil {
		if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(this.Header); err != nil {
			return github_com_mwitkow_go_proto_validators.FieldError("Header", err)
		}
	}
	if this.Txproof != nil {
		if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(this.Txproof); err != nil {
			return github_com_mwitkow_go_proto_validators.FieldError("Txproof", err)
		}
	}
	return nil
}
