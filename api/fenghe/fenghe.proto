syntax = "proto3";
package fenghe;

option go_package = "./;fenghe";

// The auction service definition.
service FengheProvider {
  rpc DetailAuctionTypeAdmin(AuctionTypeAdminDetail) returns (AuctionTypeAdminRequest) {};
  rpc CreateAuctionTypeAdmin(AuctionTypeAdminRequest) returns (AuctionTypeAdminResponse) {};
  rpc UpdateAuctionTypeAdmin(AuctionTypeAdminRequest) returns (AuctionTypeAdminResponse) {};
  rpc RemoveAuctionTypeAdmin(AuctionTypeAdminDetail) returns (AuctionTypeAdminRemove) {};
  rpc ListAuctionTypeAdmin(AuctionTypeAdminList) returns (AuctionTypeAdminListResponse) {};
  rpc DetailAuctionTypeUser(AuctionTypeUserDetail) returns (AuctionTypeUserRequest) {};
  rpc CreateAuctionTypeUser(AuctionTypeUserRequest) returns (AuctionTypeUserResponse) {};
  rpc UpdateAuctionTypeUser(AuctionTypeUserRequest) returns (AuctionTypeUserResponse) {};
  rpc RemoveAuctionTypeUser(AuctionTypeUserDetail) returns (AuctionTypeUserRemove) {};
  rpc ListAuctionTypeUser(AuctionTypeUserList) returns (AuctionTypeUserListResponse) {};
  rpc DetailAuction(AuctionDetail) returns (AuctionRequest) {};
  rpc CreateAuction(AuctionRequest) returns (AuctionResponse) {};
  rpc UpdateAuction(AuctionRequest) returns (AuctionResponse) {};
  rpc UpdateAuctionEndNum(UpdateAuctionEndNumRequest) returns (CommonRes) {};//修改限制条数
  rpc RemoveAuction(AuctionDetail) returns (AuctionRemove) {};
  rpc ListAuction(AuctionList) returns (AuctionListResponse) {};
  rpc UpdateBaseAuction(AuctionRequest) returns (AuctionResponse) {};
  rpc NowBiddingArtworkAuction(AuctionDetail) returns (WsRtcLive) {};
  rpc NowBiddingBuys(AuctionDetail) returns (AuctionPriceList) {};
  rpc UpdateAuctionLang(AuctionRequest) returns (AuctionListResponse) {}; //更新拍卖场次语言
  rpc UpdateAuctionArtworkLang(ArtworkLangRequest) returns (CommonMsg) {};//更新拍卖作品多语言
  rpc DetailAuctionArtworkLang(DetailAuctionArtworkLangReq) returns (ArtworkLangRequest) {};//获取拍卖作品多语言详情

  rpc NowScreenArtworkAuction(AuctionDetail) returns (WsRtcLive) {};
  rpc CreateAuctionArtwork(AuctionArtworkRequest) returns (AuctionArtworkResponse) {};
  rpc UpdateAuctionArtwork(AuctionArtworkRequest) returns (AuctionArtworkResponse) {};
  rpc RemoveAuctionArtwork(AuctionArtworkDetail) returns (AuctionArtworkRemove) {};
  rpc ListAuctionArtwork(AuctionArtworkList) returns (AuctionArtworkListResponse) {};
  rpc DetailAuctionArtwork(AuctionArtworkDetail) returns (AuctionArtworkInfo) {};
  rpc UpdateAuctionBuy(AuctionBuyRequest) returns (AuctionBuyResponse) {};
  rpc RemoveAuctionBuy(AuctionBuyDetail) returns (AuctionBuyRemove) {};
  rpc SureAuctionBuy(SureAuctionBuyRequest) returns (AuctionBuyResponse) {};
  rpc ListAuctionBuy(AuctionBuyList) returns (AuctionBuyListResponse) {};
  rpc AdminListAuctionBuy(AuctionBuyList) returns (AuctionBuyListResponse) {};
  rpc FansListAuctionBuy(AuctionBuyList) returns (AuctionBuyListResponse) {};
  rpc AdminAllListAuctionBuy(AuctionBuyList) returns (AdminListAuctionBuyResponse) {};
  rpc ListAuctionBuyDetail(AuctionBuyList) returns (AuctionBuyListResponse) {};
  rpc DetailAuctionBuy(AuctionBuyDetail) returns (AuctionBuyRequest) {};
  rpc CancelAuctionBuy(AuctionBuyDetail) returns (CommonRes) {};
  rpc CreateAuctionBuy(AuctionBuyRequest) returns (AuctionBuyResponse) {};
  rpc CreateFansAuctionBuy(FansAuctionBuyRequest) returns (AuctionBuyResponse) {};
  rpc ListAuctionPay(AuctionPayList) returns (AuctionPayListResponse) {};
  rpc DetailAuctionPay(AuctionPayDetail) returns (AuctionPayRequest) {};
  rpc CreateAuctionPay(AuctionPayRequest) returns (AuctionPayResponse) {};
  rpc UpdateAuctionPay(AuctionPayRequest) returns (AuctionPayResponse) {};
  rpc RemoveAuctionPay(AuctionPayDetail) returns (AuctionPayRemove) {};
  rpc RemoveArtwork(ArtworkDetail) returns (ArtworkRemove) {};
  rpc ListArtwork(ArtworkList) returns (ArtworkListResponse) {};
  rpc DetailArtwork(ArtworkDetail) returns (ArtworkRequest) {};
  rpc CreateArtwork(ArtworkRequest) returns (ArtworkResponse) {};
  rpc UpdateArtwork(ArtworkRequest) returns (ArtworkResponse) {};
  rpc GetPullLiveUrl(CommonReq) returns (LiveUrlResponse) {};//获取拉流数据
  rpc CreateLiveUrl(CommonReq) returns (LiveUrlResponse) {};//生成推流连接

  rpc GetAuctionArtworkList ( GetAuctionArtworkListRequest )returns( GetAuctionArtworkListResp ){}   //查询AuctionArtwork数据列表
  rpc GetAuctionByWithLotNumber( GetAuctionByWithLotNumberRequest ) returns ( GetAuctionByWithLotNumberResp ) {} //使用Lot编号查询拍卖信息
  rpc CreateOfflinePay ( OfflinePayData )returns( CreateOfflinePayResp ){} //创建OfflinePay
  rpc UpdateOfflinePay ( OfflinePayData )returns( CommonMsg ){} //更新OfflinePay
  rpc SaveOfflinePay ( OfflinePayData )returns( CommonMsg ){} //覆盖OfflinePay
  rpc DeleteOfflinePay ( DeleteOfflinePayRequest )returns( CommonMsg ){}   //删除OfflinePay
  rpc GetOfflinePayDetail ( GetOfflinePayByIdRequest )returns( OfflinePayData ){}   //查询OfflinePay详情
  rpc GetOfflinePayList ( GetOfflinePayListRequest )returns( GetOfflinePayListResp ){}   //查询OfflinePay列表
  rpc AddOfflinePayPrice(AddOfflinePayPriceRequest)returns( OfflinePayData ){}//增加已支付的价格
  rpc GetViewOfflinePayList(GetViewOfflinePayListReq)returns( GetViewOfflinePayListResp ){}//增加已支付的价格

  rpc CreateAuctionSessionUserNo ( AuctionSessionUserNoData )returns( CreateAuctionSessionUserNoResp ){} //创建AuctionSessionUserNo
  rpc UpdateAuctionSessionUserNo ( AuctionSessionUserNoData )returns( CommonMsg ){} //更新AuctionSessionUserNo
  rpc SaveAuctionSessionUserNo ( AuctionSessionUserNoData )returns( CommonMsg ){} //覆盖AuctionSessionUserNo
  rpc DeleteAuctionSessionUserNo ( DeleteAuctionSessionUserNoRequest )returns( CommonMsg ){}   //删除AuctionSessionUserNo
  rpc GetAuctionSessionUserNoDetail ( GetAuctionSessionUserNoRequest )returns( AuctionSessionUserNoData ){}   //查询AuctionSessionUserNo详情
  rpc GetAuctionSessionUserNoList ( GetAuctionSessionUserNoListRequest )returns( GetAuctionSessionUserNoListResp ){}   //查询AuctionSessionUserNo列表
  rpc GetViewAuctionSessionUserNoList ( GetViewAuctionSessionUserNoListRequest )returns( GetViewAuctionSessionUserNoListResp ){}   //查询AuctionSessionUserNo列表

  rpc CreateSeriesArtwork ( SeriesArtworkData )returns( CreateSeriesArtworkResp ){} //创建SeriesArtwork
  rpc UpdateSeriesArtwork ( SeriesArtworkData )returns( CommonMsg ){} //更新SeriesArtwork
  rpc SaveSeriesArtwork ( SeriesArtworkData )returns( CommonMsg ){} //覆盖SeriesArtwork
  rpc DeleteSeriesArtwork ( DeleteSeriesArtworkRequest )returns( CommonMsg ){}   //删除SeriesArtwork
  rpc GetSeriesArtworkDetail ( GetSeriesArtworkByIdRequest )returns( SeriesArtworkData ){}   //查询SeriesArtwork详情
  rpc GetSeriesArtworkList ( GetSeriesArtworkListRequest )returns( GetSeriesArtworkListResp ){}   //查询SeriesArtwork列表
  rpc GetShopSeriesDetail ( GetSeriesArtworkByIdRequest )returns( GetShopSeriesDetailResp ){}   //查询ShopSeries详情

  rpc CreateSeriesArtworkLang ( SeriesArtworkLangData )returns( CreateSeriesArtworkLangResp ){} //创建SeriesArtworkLang
  rpc UpdateSeriesArtworkLang ( SeriesArtworkLangData )returns( CommonMsg ){} //更新SeriesArtworkLang
  rpc SaveSeriesArtworkLang ( SeriesArtworkLangData )returns( CommonMsg ){} //覆盖SeriesArtworkLang
  rpc DeleteSeriesArtworkLang ( DeleteSeriesArtworkLangRequest )returns( CommonMsg ){}   //删除SeriesArtworkLang
  rpc GetSeriesArtworkLangDetail ( GetSeriesArtworkLangByIdRequest )returns( SeriesArtworkLangData ){}   //查询SeriesArtworkLang详情
  rpc GetSeriesArtworkLangList ( GetSeriesArtworkLangListRequest )returns( GetSeriesArtworkLangListResp ){}   //查询SeriesArtworkLang列表

  rpc GetSeriesProfileList(GetSeriesProfileListRequest)returns(GetSeriesProfileListResp){}//查询系列基本信息列表
  rpc InitSeriesProfileCache(CommonReq)returns(CommonMsg){}//初始化系列基本信息缓存索引
  rpc GetSeriesFrontDetail(GetSeriesDetailRequest)returns(GetSeriesDetailResp){}//查询系列详情

  rpc RecordSeriesOrder ( RecordSeriesOrderRequest )returns( CommonMsg ){} // 修改订单状态
  rpc RecordSeriesList ( RecordSeriesOrderRequest )returns( RecordSeriesListResp ){} // 修改订单状态
  rpc UpdateSeriesOrder ( SeriesOrderData )returns( CommonMsg ){} // 修改订单状态
  rpc CreateSeriesOrder ( SeriesOrderData )returns( CreateSeriesOrderResp ){} //创建SeriesOrder
  rpc CreateCultureSeriesOrder ( CreateCultureSeriesOrderRequest )returns( CommonRes ){} //创建SeriesOrder
  rpc SaveSeriesOrder ( SeriesOrderData )returns( CommonMsg ){} //覆盖SeriesOrder
  rpc DeleteSeriesOrder ( DeleteSeriesOrderRequest )returns( CommonMsg ){}   //删除SeriesOrder
  rpc GetSeriesOrderDetail ( GetSeriesOrderByIdRequest )returns( SeriesOrderData ){}   //查询SeriesOrder详情
  rpc GetSeriesOrderList ( GetSeriesOrderListRequest )returns( GetSeriesOrderListResp ){}   //查询SeriesOrder列表
  rpc UpdateTradePayment(UpdateTradePaymentReq)returns(UpdateTradePaymentResp){};//更新交易付款
  rpc CreateSeriesPaymentPprof ( CreateSeriesPaymentPprofReq )returns( CommonMsg ){} //创建交易证明
  rpc CreateSeriesPaymentPprofV2 ( CreateSeriesPaymentPprofReqV2 )returns( CommonMsg ){} //创建交易证明,多了证明类型
  rpc UpdateSeriesPaymentPprof ( SeriesPaymentPprofData )returns( CommonMsg ){} //更新交易证明
  rpc SaveSeriesPaymentPprof ( SeriesPaymentPprofData )returns( CommonMsg ){} //覆盖交易证明
  rpc DeleteSeriesPaymentPprof ( DeleteSeriesPaymentPprofRequest )returns( CommonMsg ){}   //删除交易证明
  rpc GetSeriesPaymentPprofDetail ( GetSeriesPaymentPprofByIdRequest )returns( SeriesPaymentPprofData ){}   //查询交易证明详情
  rpc GetSeriesPaymentPprofList ( GetSeriesPaymentPprofListRequest )returns( GetSeriesPaymentPprofListResp ){}   //查询交易证明列表
  rpc GetExportOrderTypeList ( GetExportOrderTypeListRequest )returns( GetExportOrderTypeListResp ){}   //查询销售情况导出列表

  // 转单
  rpc CreateTransferOrderRecord ( TransferOrderRecordData )returns( CreateTransferOrderRecordResp ){} //创建TransferOrderRecord
  rpc UpdateTransferOrderRecord ( TransferOrderRecordData )returns( CommonMsg ){} //更新TransferOrderRecord
  rpc SaveTransferOrderRecord ( TransferOrderRecordData )returns( CommonMsg ){} //覆盖TransferOrderRecord
  rpc DeleteTransferOrderRecord ( DeleteTransferOrderRecordRequest )returns( CommonMsg ){}   //删除TransferOrderRecord
  rpc GetTransferOrderRecordDetail ( GetTransferOrderRecordByIdRequest )returns( TransferOrderRecordData ){}   //查询TransferOrderRecord详情
  rpc GetTransferOrderRecordList ( GetTransferOrderRecordListRequest )returns( GetTransferOrderRecordListResp ){}   //查询TransferOrderRecord列表

  // 法大大用户实名
  rpc CreateUser ( UserData )returns( CreateUserResp ){} //创建User
  rpc UpdateUser ( UserData )returns( CommonMsg ){} //更新User
  rpc SaveUser ( UserData )returns( CommonMsg ){} //覆盖User
  rpc DeleteUser ( DeleteUserRequest )returns( CommonMsg ){}   //删除User
  rpc GetUserDetail ( GetUserByIdRequest )returns( UserData ){}   //查询User详情
  rpc GetUserList ( GetUserListRequest )returns( GetUserListResp ){}   //查询User列表
  rpc QueryStaffOrdersTotal(QueryStaffOrdersTotalReq)returns(QueryStaffOrdersTotalRes){};
  rpc QueryCustomerOrdersTotal(QueryCustomerOrdersTotalReq)returns(QueryCustomerOrdersTotalRes){};

  // 映射的老戴的用户合同表
//  rpc CreateUserContract ( UserContractData )returns( CreateUserContractResp ){} //创建用户合同
//  rpc UpdateUserContract ( UserContractData )returns( CommonMsg ){} //更新用户合同
//  rpc SaveUserContract ( UserContractData )returns( CommonMsg ){} //覆盖用户合同
//  rpc DeleteUserContract ( DeleteUserContractRequest )returns( CommonMsg ){}   //删除用户合同
  rpc GetUserContractDetail ( GetUserContractByIdRequest )returns( UserContractData ){}   //查询用户合同详情
  rpc GetUserContractList ( GetUserContractListRequest )returns( GetUserContractListResp ){}   //查询用户合同列表
}
message GetShopSeriesDetailResp{
  string brandName = 1;
  string seriesName = 2;
}


message RecordSeriesListResp {
  repeated RecordSeriesOrderRequest list = 1;
}

message RecordSeriesOrderRequest {
   string orderNo = 1;
   repeated string contractFiles = 2;
   repeated string files =3;
   uint32  showStatus = 4;
   uint32  orderId = 5;
   string  artworkUid = 6;
}

message QueryCustomerOrdersTotalRes{
  repeated QueryCustomerOrdersTotalInfo info = 1;
}
message QueryCustomerOrdersTotalReq{
  repeated uint64 customerIds = 1;
  uint64 departmentId = 2;
}

message QueryCustomerOrdersTotalInfo{
  uint64 customerId = 1;
  string amountTotal = 2;
  uint64 orderNum = 3;
}
message QueryStaffOrdersTotalInfo{
  uint64 staffId = 1;
  string amountTotal = 2;
  uint64 orderNum = 3;
}
message QueryStaffOrdersTotalRes{
  repeated QueryStaffOrdersTotalInfo info = 1;
}
message QueryStaffOrdersTotalReq{
  repeated uint64 staffIds = 1;
  uint64 departmentId = 2;
}
message UpdateSeriesOrderShowStatusRequest {
  uint32 ID = 1; //订单ID
  uint32 uuid = 2; //订单ID
  uint32 nextSowStatus = 3; //设置这个玩意 销售专用字段(1-待销售确认收款 2-代后台确认收款 3-代协议 4-代收获 5完毕)
}

message CommonRes {

}

message DetailAuctionArtworkLangReq {
  //string artworkUuid =1;
  string auctionArtworkUuid =2;
  string lang =3;
}

message CommonReq {
  string uuid =1;
}

message LiveUrlResponse {
  string pushUrl=1;
  map<string,string>  pullUrlMap=2;
  string  createdAt=3;
}

message WsRtcLive {
  string wsType                     =1 ;   //tip artwork nowAuctionPrice auctionPriceList first(除了tip)
  Tip tip                           =2 ;
  Artwork artwork                   =3 ;
  NowAuctionPrice nowAuctionPrice   =4;
  AuctionPriceList auctionPriceList =5 ;
  AuctionRequest auction =7 ;//拍卖场次信息
  string errorNo                    =6 ;   //noSelling noAuction
  repeated BaseAuctionBuy needPayBuys =8 ;//拍卖场次信息
}

message BaseAuctionBuy {
  uint32 ID = 1;
  string createdAt = 2;
  string artworkUuid = 3;
  string auctionArtworkUuid = 4;
  uint32 auctionStatus = 5;
  string auctionType = 6;
  string leftPrice = 7;
  string paidPrice = 8;
  string baseCurrency = 9;
  string baseMoney = 11;
  uint32 status = 12;
  string updatedAt = 13;
  uint32 userID = 14;
  string userName = 15;
  string uuid = 16;
  string leftCurrency = 21;
  string userCreatedAt = 24;
  string leftCnyPrice = 25;
}

message Artwork {
  uint32    index              = 1;
  string    uuid = 2;
  string    name               = 3;
  bool      isSelling          = 4;
  bool      isSoled            = 5;
  string    hdPic           = 7;
  uint32    totalNum           = 8;
  string    artistName           = 9;
  uint32    length           = 10;
  uint32    width           = 11;
  uint32    ruler           = 12;
  string    abstract        = 13;
  AuctionBuyRequest   buyInfo= 14;
  string    startPrice        = 15;
}

message Tip {
  string tipType = 1; //提示类型 falling即将落锤  othersBid已有其他人出价 successBid竞拍成功 artworkOver(本拍品结束) failBid竞拍失败 over(竞拍结束)
}

message NowAuctionPrice {
  string    currency  = 1;
  string    nowPrice  = 2;
  string    nextPrice = 3;
  GlobalPrice globalPrices = 4;
  string    auctionType = 5;
  string    status = 8;//状态 done 成交了 doing 进行中
  string    successPrice = 6;//竞拍成功的价格
  uint32    successUserId = 7;//竞拍成功的用户id
  string    successBuyUuid = 9;//竞拍成功的用户名
}

message GlobalPrice {
  string  RMB = 1;
  string  JPY = 2;
  string  USD = 3;
  string  EUR = 4;
  string  HKD = 5;
  string  TWD = 6;
}

message AuctionPriceList {
  repeated Buy buys = 1;
}

message Buy  {
  string   statusCode    = 1 ; //(领先 head) (出局 out)
  string   auctionType   = 2 ; //类型  online  scene
  string   createdAt     = 3 ; //下单时间
  uint32   userId        = 5 ; //金额
  string   isMy          = 6 ; //是否是我
  string   buyMoney         = 4 ; //金额
  string   buyCurrency         = 7 ; //金额
  string   baseMoney         = 8 ; //金额
  string   baseCurrency         = 9 ; //金额
  string   uuid         = 10 ; //金额
}

message AuctionTypeAdminListResponse {
  uint64 count = 1;
  repeated AuctionTypeAdminRequest data = 2;
}

message AuctionTypeAdminRequest {
  string action = 1;
  string createdAt = 2;
  uint32 deletedAt = 3;
  uint32 ID = 4;
  string updatedAt = 5;
  uint32 userID = 6;
  string userName = 7;
}

message AuctionTypeAdminDetail {
  uint32 ID = 1;
  string domain = 2;
}

message AuctionTypeAdminResponse {
  uint32 ID = 1;
  bool success = 2;
}

message AuctionTypeAdminRemove {
  bool success = 1;
}

message AuctionTypeAdminList {
  uint64 page = 1;
  uint64 pageSize = 2;
}

message AuctionTypeUserListResponse {
  uint64 count = 1;
  repeated AuctionTypeUserRequest data = 2;
}

message AuctionTypeUserRequest {
  string action = 1;
  string createdAt = 2;
  uint32 deletedAt = 3;
  uint32 ID = 4;
  string updatedAt = 5;
  uint32 userID = 6;
  string userName = 7;
}

message AuctionTypeUserDetail {
  uint32 ID = 1;
  string domain = 2;
}

message AuctionTypeUserResponse {
  uint32 ID = 1;
  bool success = 2;
}

message AuctionTypeUserRemove {
  bool success = 1;
}

message AuctionTypeUserList {
  uint64 page = 1;
  uint64 pageSize = 2;
}


message AuctionLang {
  string address = 1;
  string image = 5;
  string info = 6;
  string startDate = 7;
  string title = 8;
  string uuid = 10;
  string startTitle = 16;
}

message UpdateAuctionEndNumRequest {
  string uuid = 1;
  int32 endNum = 2;
}

message AuctionRequest {
  string address = 1;
  string image = 5;
  string info = 6;
  string startDate = 7;
  string title = 8;
  string startTitle = 16;
  string lang = 18;
  /*上边是多语言*/

  string createdAt = 2;
  uint32 deletedAt = 3;
  uint32 ID = 4;
  string updatedAt = 9;
  string uuid = 10;
  repeated AuctionArtworkRequest auctionArtwork = 14;
  uint32 totalNum = 11;
  uint32 isLiving = 13;
  bool isExistLivingArtwork = 15; //是否存在正在拍卖的作品
  string operatorName = 17;

  string liveRegion = 19; //直播中心
  string startPreviewDate = 20; //预览开始时间
  string endPreviewDate = 21; //预览结束时间
  int32 endNum = 22; //预览结束时间
  int32 offlineRegister=23;//允许线下报名 1=启用 2=禁用
}

message AuctionDetail {
  uint32 ID = 1;
  string domain = 2;
  string uuid = 3;
  uint32 userId = 4;
  string lang = 5;
}

message AuctionResponse {
  uint32 ID = 1;
  bool success = 2;
  string uuid = 3;
}

message AuctionRemove {
  bool success = 1;
}

message AuctionList {
  uint64 page = 1;
  uint64 pageSize = 2;
  string title = 3;
  string startStartDate = 4;
  string endStartDate = 5;
}

message AuctionListResponse {
  uint64 count = 1;
  repeated AuctionRequest data = 2;
}

message AuctionArtworkList {
  uint64 page = 1;
  uint64 pageSize = 2;
  string auctionUuid = 3;
  bool isBase = 10;
  string lang = 4;
  int32 endNum = 5;
}

message AuctionArtworkListResponse {
  uint64 count = 1;
  repeated AuctionArtworkInfo data = 2;
}

message AuctionArtworkInfo {
  string artworkTitle = 1;
  string artworkUuid = 2;
  string createdAt = 3;
  uint32 deletedAt = 4;
  uint32 ID = 5;
  uint32 index = 6;
  uint32 isSelling = 7; //是否正在拍卖  开拍停拍
  string priceRuleImage = 8;
  string priceRuleInfo = 9;
  string priceRuleType = 10; //拍卖类型价格类型
  uint32 screenStatus = 11;
  string startPrice = 12;   //起拍价
  string soldPrice = 18;   //起拍价
  string updatedAt = 13;
  string uuid = 14;
  repeated PriceRule priceRules = 15;
  ArtworkRequest artwork = 16; //作品名字 缩略图 作者名字
  bool isSold = 17;//是否售出
  string auctionUuid = 19;
  string startPriceCurrency = 20;   //起拍价货币 仅仅展示使用
  string soldPriceCurrency = 21;   //售价货币 仅仅展示使用
  string priceRuleAdd = 22;   //售价货币 仅仅展示使用
  uint32 userId = 23;
}

message AuctionArtworkRequest {
  string artworkTitle = 1;
  string artworkUuid = 2;
  string createdAt = 3;
  uint32 deletedAt = 4;
  uint32 ID = 5;
  uint32 index = 6;
  uint32 isSelling = 7;
  string priceRuleImage = 8;
  string priceRuleInfo = 9;
  string priceRuleType = 10;
  uint32 screenStatus = 11;
  string startPrice = 12;
  string updatedAt = 13;
  string uuid = 14;
  repeated PriceRule priceRules = 15;
  bool isSold = 16;//是否售出
  string priceRuleAdd = 17;
}

//竞价表
message PriceRule {
  string uuid = 1;
  string price = 2;
  uint32 index = 3;
  uint32 intPrice = 4;
}

message AuctionArtworkDetail {
  uint32 ID = 1;
  string domain = 2;
  string uuid = 3;
  string lang = 4 ;
}

message AuctionArtworkResponse {
  uint32 ID = 1;
  bool success = 2;
}

message AuctionArtworkRemove {
  bool success = 1;
}

message AuctionBuyList {
  uint64 page = 1;
  uint64 pageSize = 2;
  string auctionArtworkUuid = 3;
  uint32 userId = 4;
  uint32 auctionStatus = 5;

  string tnum = 6; //画家编号
  string artistName = 7; //画家姓名
  string tfnum = 8; //画作编号
  string name = 9; //画作名称
  string userName = 10; //成交人
  string fromCode = 11; //邀请码
  string auctionType = 12; //类型 online local
  uint32 status = 13; //支付状态
  string nowAuctionUuid = 14; //当前拍卖的藏品uuid
  repeated uint32 auctionStatuses = 15;
}

message AuctionBuyListResponse {
  uint64 count = 1;
  repeated AuctionBuyRequest data = 2;
  AuctionArtworkBase auctionArtworkBase = 3;//仅仅当auctionArtworkUuid有值时返回
  NowAuctionPrice nowAuctionPrice = 4;//仅仅当auctionArtworkUuid有值时返回
}

message AuctionArtworkBase {
  string   uuid        =1;
  uint32   index       =2;
  string   artistName  =3;
  string   name        =4;
}

message AdminListAuctionBuyResponse {
  uint64 count = 1;
  repeated AuctionBuyRequest data = 2;
  uint32 totalArtworkNum = 3; //数量
  string totalArtworkMoney = 4; //成交总价
}

message FansAuctionBuyRequest {
  string auctionArtworkUuid = 1;
  uint32 userId             = 3;
  string userName           = 4;
  string buyCurrency           = 5;
  string buyMoney              = 2;
  string fromCode              = 6;
}

message AuctionBuyRequest {
  string artworkUuid = 1;
  string auctionArtworkUuid = 2;
  uint32 auctionStatus = 3;
  string auctionType = 4;
  string createdAt = 5;
  uint32 deletedAt = 7;
  uint32 ID = 8;
  string leftPrice = 9;
  string paidPrice = 10;
  string baseCurrency = 6;
  string baseMoney = 11;
  uint32 status = 12;
  string updatedAt = 13;
  uint32 userID = 14;
  string userName = 15;
  string uuid = 16;
  AuctionArtworkInfo auctionArtworkInfo = 18;
  string buyCurrency = 19;
  string buyMoney = 20;
  string leftCurrency = 21;
  uint32 userAuctionStatus = 22;
  uint32 adminAuctionStatus = 23;
  string userCreatedAt = 24;
  string fromCode = 25;
  ArtworkRequest artwork = 26; //作品名字 缩略图 作者名字
  string leftCnyPrice = 27;
}

message ArtworkBaseInfo {
  string uuid = 1;
  string Name = 2;
  string startPrice = 3;
  string artistName = 4;
  string hdPic = 5;
}

message DetailSureAuctionBuyRequest {
  string auctionArtworkUuid = 1;
}

message SureAuctionBuyRequest {
  string auctionArtworkUuid = 1;
  string money = 2;
}

message AuctionBuyDetail {
  uint32 ID = 1;
  string domain = 2;
  string uuid = 3;
}

message AuctionBuyResponse {
  uint32 ID = 1;
  bool success = 2;
  string uuid = 3;
}

message AuctionBuyRemove {
  bool success = 1;
}

message AuctionPayDetail {
  uint32 ID = 1;
  string domain = 2;
  string uuid=3;
}

message AuctionPayResponse {
  uint32 ID = 1;
  bool success = 2;
}

message AuctionPayRemove {
  bool success = 1;
}

message AuctionPayList {
  uint64 page = 1;
  uint64 pageSize = 2;
  string auctionBuyUuid= 3;
  uint32 offlinePayId=4;
  string qrUid=5;
  int32 status=6;
  string orderNo=7;
}

message AuctionPayListResponse {
  uint64 count = 1;
  repeated AuctionPayRequest data = 2;
}

message AuctionPayRequest {
  string artworkUuid = 1;
  string auctionArtworkUuid = 2;
  string auctionBuyUuid = 3;
  string createdAt = 4;
  uint32 deletedAt = 5;
  uint32 ID = 6;
  string money = 7;
  uint32 status = 8;
  string updatedAt = 9;
  uint32 userID = 10;
  string userName = 11;
  string uuid = 12;
  string currency = 13;
//  uint32 offlinePayId=14;
  string paySessionId=15;
  string seriesUid=16;
  string orderNo=17;
  repeated SeriesPaymentPprofData pprofList =18;
  string remark=19;
  int64 optUserId=20;
  string optUserName=21;

  string  ArrivalDate=22; //到账日期
  string  PaymentType=23; //支付方式 对应枚举 PaymentType
  string  BankAccount=24; //入账银行
  string  AgentIncome=25; //代理收入
  string  AfterTax=26; //税后收入
  string  IncomeCompany=27; //收入计公司
  string  ServiceFee=28;//服务费
}

message ArtworkDetail {
  uint32 ID = 1;
  string uuid =2;

}

message ArtworkResponse {
  uint32 ID = 1;
  bool success = 2;
}

message ArtworkRemove {
  bool success = 1;
}

message ArtworkList {
  uint64 page = 1;
  uint64 pageSize = 2;
  string artistName = 3;
  string tnum = 4;
  string tfnum = 5;
  string name = 6;
  string auctionUuid = 7;
  string seriesUuid = 8;
  repeated string uidIn=9;
}

message ArtworkListResponse {
  uint64 count = 1;
  repeated ArtworkRequest data = 2;
}

message ArtworkLangRequest {
  string abstract = 1;
  string artistName = 2;
  uint32 ruler = 9;
  uint32 length = 7;
  string hdPic = 5;
  string name = 8;
  string lang = 15;
  /**
   * 上边是多语言
   */

  string createdAt = 3;
  uint32 deletedAt = 4;
  uint32 ID = 6;
  string tfnum = 10;
  string tnum = 11;
  string updatedAt = 12;
  string uuid = 13;
  uint32 width = 14;
  string auctionArtworkUuid = 16;
}

message ArtworkRequest {
  string abstract = 1;
  string artistName = 2;
  string createdAt = 3;
  uint32 deletedAt = 4;
  string hdPic = 5;
  uint32 ID = 6;
  uint32 length = 7;
  string name = 8;
  uint32 ruler = 9;
  string tfnum = 10;
  string tnum = 11;
  string updatedAt = 12;
  string uuid = 13;
  uint32 width = 14;
  string lang = 15;
}

message GetAuctionArtworkListRequest{
  AuctionArtworkInfo query =1;
  int64 page=2;
  int64 pageSize=3;
  string where=4;
  string order=5;
}
message GetAuctionArtworkListResp{
  repeated AuctionArtworkInfo list=1;
  int64 page=2;
  int64 pageSize=3;
  int64 Total=4;
}
message GetAuctionByWithLotNumberRequest{
  uint32 lotNo=1;
}
message GetAuctionByWithLotNumberResp{
  uint32 logNo=1;
  string auctionBuyUuid=2;
  string auctionArtworkUuid=3;
  string artworkUid=4;
  string hdPic=5; //画作图片
  string artworkName=6; //作品名称
  string buyMoney=7;//总金额
  string buyCurrency=8;//总金额货币单位
  string leftPrice=9;//剩余金额
  string leftCurrency=10;//剩余金额货币单位
  uint32 auctionBuyPayStatus=11;//拍卖商品的支付结果
  uint32 qrcodeTotal=12;//商品拥有的二维码总数
}
message CommonMsg{
  string msg = 1;
}

message OfflinePayData{
  uint32  ID=1;
  string createdAt=2;
  string updatedAt=3;
  int64  deletedAt=4;
  uint32 userId = 5; //用户id
  string userName = 6; //用户名字
  string auctionBuyUuid = 7; //拍卖uuid，总订单号
  string auctionArtworkUuid = 8; //拍卖画作表uid
  string artworkUid=9; //画作uid
  string price = 10; //价格
  string currency = 11; //货币单位
  string paidPrice = 12; //已支付金额
  string paidCurrency = 13; //已支付货币单位
  int32  payStatus = 14; //支付状态 1=未支付  2=已付款 4=部分支付
  string qrUid=15; //二维码uid
  uint32 lotNo=16; //拍品编号
  string remark=17;//备注
}
message OfflinePayListData{
  uint32  ID=1;
  string createdAt=2;
  string updatedAt=3;
  int64  deletedAt=4;
  uint64 userId = 5; //用户id
  string userName = 6; //用户名字
  //  string auctionBuyUuid = 7; //拍卖uuid，总订单号
  //  string auctionArtworkUuid = 8; //画作uid
  string price = 9; //价格
  string currency = 10; //货币单位
  string paidPrice = 11; //已支付金额
  string paidCurrency = 12; //已支付货币单位
  int32  payStatus = 13; //支付状态 1=未支付  2=已付款 4=部分支付
  string qrUid=14; //二维码uid
  uint32 lotNo=15; //拍品编号
  string hdPic=16; //画作图片
  string artworkName=17;//画作名
  string artistName=18;//画家名字
  string remark=19;//备注
}
message CreateOfflinePayResp{
  OfflinePayData data=1;
  string msg=2;
}
message DeleteOfflinePayRequest{
  uint32 id=1; //二选一，数据id
  repeated uint32 ids=2;//二选一，数据id列表
  string qrUid=3;
}
message GetOfflinePayByIdRequest{
  uint32 id=1; //数据id
  string qrUid=2;
}
message GetOfflinePayListRequest{
  OfflinePayData query =1;
  int64 page=2;
  int64 pageSize=3;
  string where=4;
  string order=5;
  string payTimeBegin=6;
  string payTimeEnd=7;
}
message GetOfflinePayListResp{
  repeated OfflinePayListData list=1;
  int64 page=2;
  int64 pageSize=3;
  int64 Total=4;
}
message AddOfflinePayPriceRequest{
  string qrUid=1;
  uint32 offlinePayId=2; //qrUid和offlinePayId 为主键，二选一
  string  price=3;
  string currency=4;
}
message ViewOfflinePayData {
  string qrUid = 1;
  string createdAt = 2;
  string lotNo = 3;
  int64  userId = 4;
  string userName = 5;
  string auctionBuyUuid = 6;
  string auctionArtworkUuid = 7;
  string artworkUid = 8;
  string price = 9;
  string currency = 10;
  string paidPrice = 11;
  string paidCurrency = 12;
  int32  payStatus = 13;
  string latestPayTime = 14;
  string artworkName = 15;
  string hdPic = 16;
  string artistName=17;
  string tfnum=18;
  int64 payCount=19;
  int64 paySuccessCount=20;
}
message GetViewOfflinePayListReq{
  ViewOfflinePayData query =1;
  int64 page=2;
  int64 pageSize=3;
  string where=4;
  string order=5;
}
message GetViewOfflinePayListResp{
  repeated ViewOfflinePayData list=1;
  int64 page=2;
  int64 pageSize=3;
  int64 Total=4;
}

message AuctionSessionUserNoData{
  int64 ID=1;
  string createdAt=2;
  string updatedAt=3;
  int64  deletedAt=4;
  string sessionNo = 5; //场次号
  uint32 auctionUserNo = 6; //拍卖号牌(目前仅线下使用)
  string phone = 7; //用户手机号
}
message CreateAuctionSessionUserNoResp{
  AuctionSessionUserNoData data=1;
  string msg=2;
}
message DeleteAuctionSessionUserNoRequest{
  int64 id=1; //二选一，数据id
  repeated int64 ids=2;//二选一，数据id列表
}
message GetAuctionSessionUserNoRequest{
  uint32 id=1; //数据id
  string phone=2;//手机号
}
message GetAuctionSessionUserNoListRequest{
  AuctionSessionUserNoData query =1;
  int64 page=2;
  int64 pageSize=3;
  string where=4;
  string order=5;
}
message GetAuctionSessionUserNoListResp{
  repeated AuctionSessionUserNoData list=1;
  int64 page=2;
  int64 pageSize=3;
  int64 Total=4;
}

message ViewAuctionSessionUserNo {
  uint32 ID = 1; // 主键ID
  string created_at = 2; // 创建时间
  string updated_at = 3; // 更新时间
  int32 deleted_at = 4; // 软删除时间戳，0表示未删除
  string session_no = 5; // 会话编号
  string auction_user_no = 6; // 拍卖用户编号
  string phone = 7; // 用户手机号，作为主键之一
  string user_name = 8; // 用户名称
  int32 gender = 9; // 性别：1男，2女，3其他
  string birthday = 10; // 生日
  string address = 11; // 地址
  string bank_name = 12; // 银行名称
  string bank_no = 13; // 银行卡号
  int32 card_type = 14; // 证件类型：1中国身份证，2护照，3其他
  string card_id = 15; // 证件号码
  int32 fdd_register_status = 16; // 法大大注册状态 1=成功 2=失败
}

message GetViewAuctionSessionUserNoListRequest{
  ViewAuctionSessionUserNo query =1;
  int64 page=2;
  int64 pageSize=3;
  string where=4;
  string order=5;
}
message GetViewAuctionSessionUserNoListResp{
  repeated ViewAuctionSessionUserNo list=1;
  int64 page=2;
  int64 pageSize=3;
  int64 Total=4;
}

message SeriesArtworkData {
  uint32 ID = 1;
  string createdAt = 2;
  string updatedAt = 3;
  int64 deletedAt = 4;
  int64 artworkId = 5;
  string artworkUid = 6;
  string tfnum = 7;
  string artworkName = 8;
  string hdPic = 9;
  int32 ruler = 10;
  int32 length = 11;
  int32 width = 12;
  string artistName = 13;
  string artistUid = 14;
  string abstract = 15;
  string tnum=16;
  repeated SeriesArtworkLangData lang=17;
  string price=18;
  string currency=19;
}

message SeriesArtworkLangData {
  uint32 ID = 1;
  string createdAt = 2;
  string updatedAt = 3;
  int64 deletedAt = 4;
  string lang = 5;
  int64 seriesArtworkId = 6;
  string artworkUid = 7;
  string artworkName = 8;
  string artistName = 9;
  string abstract = 10;
  string hdPic=11;
  int32 ruler=12;
  int32 length=13;
  int32 width=14;
}
message CreateSeriesArtworkResp{
  SeriesArtworkData data=1;
  string msg=2;
}
message DeleteSeriesArtworkRequest{
  int64 id=1; //二选一，数据id
  repeated int64 ids=2;//二选一，数据id列表
}
message GetSeriesArtworkByIdRequest{
  int64 id=1; //数据id
  string artworkUid=2;//id和artworkUid 二选一
  string lang=3; //选择语言 默认中文
  string seriesUid=4;//系列uid，如果需要查询价格，此字段必填。
}
message GetSeriesArtworkListRequest{
  SeriesArtworkData query =1;
  int64 page=2;
  int64 pageSize=3;
  string where=4;
  string order=5;
  string lang=6;
  string seriesUid=7;//系列uid，如果需要查询价格，此字段必填。
  string actionCode=8;//系列类型 show=画展 auction=拍卖
}
message GetSeriesArtworkListResp{
  repeated SeriesArtworkData list=1;
  int64 page=2;
  int64 pageSize=3;
  int64 Total=4;
}
message CreateSeriesArtworkLangResp{
  SeriesArtworkLangData data=1;
  string msg=2;
}
message DeleteSeriesArtworkLangRequest{
  int64 id=1; //二选一，数据id
  repeated int64 ids=2;//二选一，数据id列表
}
message GetSeriesArtworkLangByIdRequest{
  int64 id=1; //数据id
}
message GetSeriesArtworkLangListRequest{
  SeriesArtworkLangData query =1;
  int64 page=2;
  int64 pageSize=3;
  string where=4;
  string order=5;
}
message GetSeriesArtworkLangListResp{
  repeated SeriesArtworkLangData list=1;
  int64 page=2;
  int64 pageSize=3;
  int64 Total=4;
}
message SeriesProfileData{
  uint32 id = 1;
  string createdAt = 2;
  string updatedAt = 3;
  int64  deletedAt = 4;
  string seriesUuid = 5;
  string coverImg=6;
  string seriesName=7;
  string actionCode=8;
  string salesTotal=9;
  int32 shelfState=10;
  string onShelfTime=11;
  string brandName=12;
}
message GetSeriesProfileListRequest{
  int64 page=2;
  int64 pageSize=3;
  string where=4;
  string order=5;
  string lang=6;
  string keywords=7;
  string actionCode=8;
  int32  shelfState=9;
  repeated string omitFields =10;
}
message GetSeriesProfileListResp{
  repeated SeriesProfileData list=1;
  int64 page=2;
  int64 pageSize=3;
  int64 Total=4;
}

message GetSeriesDetailRequest{
  string seriesUuid=1;
}
message GetSeriesDetailResp{

}

message SeriesOrderData{
  int64 ID=1;
  string createdAt=2;
  string updatedAt=3;
  int64  deletedAt=4;
  string orderNo = 5; //订单号
  string seriesUid = 6; //系列uid
  string actionCode = 7; //系列类型 show=画展 auction=拍卖
  string artworkUuid = 8; //作品的uuid
  string auctionBuyUuid = 9; //拍卖uuid，总订单号
  string amount = 10; //金额
  string currency = 11; //出价
  string leftPrice = 12; //剩余金额
  string paidPrice = 13; //已经支付金额
  int32 status = 14; //支付状态 1 未支付  2 支付成功 3 支付失败 4部分支付 5支付过期  default:1
  int32 userID = 15; //购买人id
  string userName = 16; //购买人名字
  string telNum = 17; //购买人手机号
  uint32 receivingStatus=18;//收货状态 1=未收货  2=已收货
  uint32 companyId=21;//公司Id
  uint32 staffId=22;//销售员id
  string companyName=23;//缓存的公司名
  string staffName=24;//缓存的员工名
  uint32 showStatus=25;//缓存的员工名
  uint32 signStatus=26;//协议签署状态 1=未签署 2=已签署
  string auctionArtworkUuid=27;
  string cultureAddition = 28; // 文创类型的附加信息

  string  arrivalDate=29; //到账日期
  string  paymentType=30; //支付方式 对应枚举 PaymentType
  string  bankAccount=31; //入账银行
  string  agentIncome=32; //代理收入
  string  afterTax=33; //税后收入
  string  incomeCompany=34; //收入计公司
  string  serviceFee=35;//服务费
  string  receivingTime=36;//确认收货时间
}
message CreateSeriesOrderResp{
  SeriesOrderData data=1;
  string msg=2;
}
message DeleteSeriesOrderRequest{
  int64 id=1; //二选一，数据id
  repeated int64 ids=2;//二选一，数据id列表
}
message GetSeriesOrderByIdRequest{
  int64 id=1; //数据id
  string orderNo=2;
}
message GetSeriesOrderListRequest{
  SeriesOrderData query =1;
  int64 page=2;
  int64 pageSize=3;
  string where=4;
  string order=5;
}
message GetSeriesOrderListResp{
  repeated SeriesOrderData list=1;
  int64 page=2;
  int64 pageSize=3;
  int64 Total=4;
}
message UpdateTradePaymentReq{
  string tradeNo=1; //交易流水号
  string thirdPartyTradeNo=2;//第三方的交易流水号
  int32 tradeStatus=3;//1 未支付 2=支付成功 3=支付失败
  string callbackBody=4;//回调信息
  string amount=5;//价格
}
message UpdateTradePaymentResp{
  string msg=1;
}
message SeriesPaymentPprofData{
  int64 ID = 1; //
  string createdAt = 2; //
  string updatedAt = 3; //
  int64 deletedAt = 4; //
  int32 auctionPayId = 5; //
  string orderNo = 6; //
  string picUrl = 7; //
  string payTime=8;
  int64 PprofType=9;//证明类型: 1=付款证明 2=收款证明
}

message CreateSeriesPaymentPprofResp{
  SeriesPaymentPprofData data=1;
  string msg=2;
}
message DeleteSeriesPaymentPprofRequest{
  int64 id=1; //二选一，数据id
  repeated int64 ids=2;//二选一，数据id列表
}
message GetSeriesPaymentPprofByIdRequest{
  int64 id=1; //数据id
}
message GetSeriesPaymentPprofListRequest{
  SeriesPaymentPprofData query =1;
  int64 page=2;
  int64 pageSize=3;
  string where=4;
  string order=5;
}
message GetSeriesPaymentPprofListResp{
  repeated SeriesPaymentPprofData list=1;
  int64 page=2;
  int64 pageSize=3;
  int64 Total=4;
}
message CreateSeriesPaymentPprofReq{
  string orderNo=1;
  repeated string pprofUrls=2;
  int64 optUserId=3;
  string optUserName=4;
  string remark=5;
  string payTime=6;
}

message ExportOrderTypeData{
  string lotNo = 1; //Lot号
  string artworkName = 2; //画作名称
  string tfnum = 3; //画作编号
  string amount = 4; //金额
  string nickName = 5; //购买人名字
  string phone = 6; //购买人手机号
  string auctionUserNo = 7; //号牌
  string buyTime = 8; //购买时间
  string status = 9; //订单状态
  string receivingStatus = 10; //确认收货状态
  string payTime=11; //支付时间
  string orderNo=12; //订单号
  string artworkMask=13; //画作类型
  string copyrightPrice=14; //版权费用
  string signTime=15; //协议签署时间
  string receivingTime=16; //确认收货时间
  string companyName=17; //站点名称
  string solderName=18; //销售员名称
  string paymentType=19;//支付方式
  string bankAccount=20;//银行账号
  string agentIncome=21; //代理收入
  string afterTax=22; //税后收入
  string incomeCompany=23; //收入计公司
  string serviceFee=24;//服务费
  string soldCopyright=25;//是否卖版权
  string arrivalDate=26;//到账日期
  string artworkType=27;//画作类型
  string createdAt=28;//订单创建时间
  string signStatus=29;//协议签署状态

}
message GetExportOrderTypeListRequest{
  string seriesUid=1;
}
message GetExportOrderTypeListResp{
  repeated ExportOrderTypeData list=1;
  int64 page=2;
  int64 pageSize=3;
  int64 Total=4;
}

message TransferOrderRecordData{
  int64 ID = 1; //
  string createdAt = 2; //
  string updatedAt = 3; //
  int64 deletedAt = 4; //
  string orderNo = 5; //订单号
  string seriesUid = 6; //系列号
  string auctionBuyUuid = 7; //拍卖商品号
  int32 originUserId = 8; //原始用户ID
  string originTelNum = 9; //原始手机号
  string targetTelNum = 10; //目标手机号
  int32 targetUserId = 11; //原始用户ID

}


message CreateTransferOrderRecordResp{
  TransferOrderRecordData data=1;
  string msg=2;
}
message DeleteTransferOrderRecordRequest{
  int64 id=1; //二选一，数据id
  repeated int64 ids=2;//二选一，数据id列表
}
message GetTransferOrderRecordByIdRequest{
  int64 id=1; //数据id
}
message GetTransferOrderRecordListRequest{
  TransferOrderRecordData query =1;
  int64 page=2;
  int64 pageSize=3;
  string where=4;
  string order=5;
}
message GetTransferOrderRecordListResp{
  repeated TransferOrderRecordData list=1;
  int64 page=2;
  int64 pageSize=3;
  int64 Total=4;
}

message UserData{
  string uUID = 1; //用户ID
  string userID = 2; //用户ID
  string countryCode = 3; //国家码
  string phone = 4; //手机号
  string userName = 5; //用户名
  int32 gender = 6; //性别 1男 2女 3未知
  string birthday = 7; //生日
  string address = 8; //地址
  string bankName = 9; //银行名称
  string bankNo = 10; //银行卡号
  string fddCustomID = 11; //法大大自定义id
  int32 isMainland = 12; //是否为内地用户 0 否 1 是
  int32 fddRegisterStatus = 13; //法大大注册状态 2 正常 4 失败
  int32 status = 14; //状态
  int32 registerType = 15; //注册类型
  string transactionNo = 16; //法大大注册交易号
  string fddInfo = 17; //法大大注册信息
  int32 cardType = 18; //身份证号类型
  string cardID = 19; //身份证号
  int32 createdAt = 20; //创建时间
  int32 updatedAt = 21; //更新时间
  int64 deletedAt = 22; //删除时间
  int32 specialType = 23; //特殊类型

}

message CreateUserResp{
  UserData data=1;
  string msg=2;
}
message DeleteUserRequest{
  int64 id=1; //二选一，数据id
  repeated int64 ids=2;//二选一，数据id列表
}
message GetUserByIdRequest{
  int64 id=1; //数据id
}
message GetUserListRequest{
  UserData query =1;
  int64 page=2;
  int64 pageSize=3;
  string where=4;
  string order=5;
}
message GetUserListResp{
  repeated UserData list=1;
  int64 page=2;
  int64 pageSize=3;
  int64 Total=4;
}
enum PaymentType{
  None              = 0;
  Alipay            = 1;//支付宝
  BankTransfer      = 2;//转账
  PosMachine        = 3;//Pos机支付
}
message CreateSeriesPaymentPprofReqV2{
  message Pprof{
    string picUrl=1;//证明图片
    int64 pprofType=2;//证明类型: 1=付款证明 2=收款证明
  }
  string orderNo=1; //订单号
  repeated Pprof pprofUrls=2;//收付款证明
  int64 optUserId=3; // 收款操作人ID
  string optUserName=4;//收款操作人名
  string remark=5;//备注
  string payTime=6;//支付时间

  string  arrivalDate=7; //到账日期
  string  paymentType=8; //支付方式 对应枚举 PaymentType
  string  bankAccount=9; //入账银行
  string  agentIncome=10; //代理收入
  string  afterTax=11; //税后收入
  string  incomeCompany=12; //收入计公司
  string  serviceFee=13;//服务费
}

message UserContractData{
  string uUID = 1; //
  string userID = 2; //用户ID
  string contractID = 3; //合同ID
  string contractName = 4; //合同名称
  string templateID = 5; //模板ID
  int32 signType = 6; //签名类型 0未知 1自定义 2fdd
  int32 signOrder = 7; //签名次序 1 第一次 2 第一次签名
  int32 status = 8; //状态 0未知 1进行中 2完成
  string batchNo = 9; //批次号
  string fddInfo = 10; //fdd信息
  string viewURL = 11; //查看合同url
  string phone = 12; //手机号
  int32 lineType = 13; //
  string shortName = 14; //合同简称
  int32 createdAt = 15; //创建时间
  int32 updatedAt = 16; //更新时间
  int64 deletedAt = 17; //
  string auctionArtworkUUID = 18; //拍卖uuid
  string seriesUUID = 19; //系列uuid
  string auctionUUID = 20; //拍卖uuid
  string downloadURL = 21; //下载合同url

}

message CreateUserContractResp{
  UserContractData data=1;
  string msg=2;
}
message DeleteUserContractRequest{
  int64 id=1; //二选一，数据id
  repeated int64 ids=2;//二选一，数据id列表
}
message GetUserContractByIdRequest{
  int64 id=1; //数据id
}
message GetUserContractListRequest{
  UserContractData query =1;
  int64 page=2;
  int64 pageSize=3;
  string where=4;
  string order=5;
}
message GetUserContractListResp{
  repeated UserContractData list=1;
  int64 page=2;
  int64 pageSize=3;
  int64 Total=4;
}


message CreateCultureSeriesOrderRequest{
  message CommodityInfo{
    string price = 1;// 商品单价
    uint32 count = 2;// 商品数量
    string name = 3;// 商品名
    string currency = 4;// 币种
    string briefImage = 5;//缩略图
  }
  string paymentAmount = 1; // 应付金额总数
  string currency = 2;
  string actualPaymentAmount = 3; //实际支付金额
  string actualPaymentcurrency = 4; //实际支付货币
  uint32 payType = 5; // 支付方式 1：现金 2：刷卡 3：扫码
  repeated CommodityInfo commodityInfo = 6; // 商品信息列表
  string orderNo = 7; // 订单号
  uint32 status = 8; // 付款状态
  string operator = 9; // 操作人
  string operatorTel = 10; // 操作人手机号
  uint32 operatorId = 11; // 操作人Id
  string seriesUid = 12; // 系列uuid

}
