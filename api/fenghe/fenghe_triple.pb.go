// Code generated by protoc-gen-go-triple. DO NOT EDIT.
// versions:
// - protoc-gen-go-triple v1.0.8
// - protoc             v4.22.0--rc2
// source: fenghe.proto

package fenghe

import (
	context "context"
	protocol "dubbo.apache.org/dubbo-go/v3/protocol"
	dubbo3 "dubbo.apache.org/dubbo-go/v3/protocol/dubbo3"
	invocation "dubbo.apache.org/dubbo-go/v3/protocol/invocation"
	grpc_go "github.com/dubbogo/grpc-go"
	codes "github.com/dubbogo/grpc-go/codes"
	metadata "github.com/dubbogo/grpc-go/metadata"
	status "github.com/dubbogo/grpc-go/status"
	common "github.com/dubbogo/triple/pkg/common"
	constant "github.com/dubbogo/triple/pkg/common/constant"
	triple "github.com/dubbogo/triple/pkg/triple"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc_go.SupportPackageIsVersion7

// FengheProviderClient is the client API for FengheProvider service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type FengheProviderClient interface {
	DetailAuctionTypeAdmin(ctx context.Context, in *AuctionTypeAdminDetail, opts ...grpc_go.CallOption) (*AuctionTypeAdminRequest, common.ErrorWithAttachment)
	CreateAuctionTypeAdmin(ctx context.Context, in *AuctionTypeAdminRequest, opts ...grpc_go.CallOption) (*AuctionTypeAdminResponse, common.ErrorWithAttachment)
	UpdateAuctionTypeAdmin(ctx context.Context, in *AuctionTypeAdminRequest, opts ...grpc_go.CallOption) (*AuctionTypeAdminResponse, common.ErrorWithAttachment)
	RemoveAuctionTypeAdmin(ctx context.Context, in *AuctionTypeAdminDetail, opts ...grpc_go.CallOption) (*AuctionTypeAdminRemove, common.ErrorWithAttachment)
	ListAuctionTypeAdmin(ctx context.Context, in *AuctionTypeAdminList, opts ...grpc_go.CallOption) (*AuctionTypeAdminListResponse, common.ErrorWithAttachment)
	DetailAuctionTypeUser(ctx context.Context, in *AuctionTypeUserDetail, opts ...grpc_go.CallOption) (*AuctionTypeUserRequest, common.ErrorWithAttachment)
	CreateAuctionTypeUser(ctx context.Context, in *AuctionTypeUserRequest, opts ...grpc_go.CallOption) (*AuctionTypeUserResponse, common.ErrorWithAttachment)
	UpdateAuctionTypeUser(ctx context.Context, in *AuctionTypeUserRequest, opts ...grpc_go.CallOption) (*AuctionTypeUserResponse, common.ErrorWithAttachment)
	RemoveAuctionTypeUser(ctx context.Context, in *AuctionTypeUserDetail, opts ...grpc_go.CallOption) (*AuctionTypeUserRemove, common.ErrorWithAttachment)
	ListAuctionTypeUser(ctx context.Context, in *AuctionTypeUserList, opts ...grpc_go.CallOption) (*AuctionTypeUserListResponse, common.ErrorWithAttachment)
	DetailAuction(ctx context.Context, in *AuctionDetail, opts ...grpc_go.CallOption) (*AuctionRequest, common.ErrorWithAttachment)
	CreateAuction(ctx context.Context, in *AuctionRequest, opts ...grpc_go.CallOption) (*AuctionResponse, common.ErrorWithAttachment)
	UpdateAuction(ctx context.Context, in *AuctionRequest, opts ...grpc_go.CallOption) (*AuctionResponse, common.ErrorWithAttachment)
	UpdateAuctionEndNum(ctx context.Context, in *UpdateAuctionEndNumRequest, opts ...grpc_go.CallOption) (*CommonRes, common.ErrorWithAttachment)
	RemoveAuction(ctx context.Context, in *AuctionDetail, opts ...grpc_go.CallOption) (*AuctionRemove, common.ErrorWithAttachment)
	ListAuction(ctx context.Context, in *AuctionList, opts ...grpc_go.CallOption) (*AuctionListResponse, common.ErrorWithAttachment)
	UpdateBaseAuction(ctx context.Context, in *AuctionRequest, opts ...grpc_go.CallOption) (*AuctionResponse, common.ErrorWithAttachment)
	NowBiddingArtworkAuction(ctx context.Context, in *AuctionDetail, opts ...grpc_go.CallOption) (*WsRtcLive, common.ErrorWithAttachment)
	NowBiddingBuys(ctx context.Context, in *AuctionDetail, opts ...grpc_go.CallOption) (*AuctionPriceList, common.ErrorWithAttachment)
	UpdateAuctionLang(ctx context.Context, in *AuctionRequest, opts ...grpc_go.CallOption) (*AuctionListResponse, common.ErrorWithAttachment)
	UpdateAuctionArtworkLang(ctx context.Context, in *ArtworkLangRequest, opts ...grpc_go.CallOption) (*CommonMsg, common.ErrorWithAttachment)
	DetailAuctionArtworkLang(ctx context.Context, in *DetailAuctionArtworkLangReq, opts ...grpc_go.CallOption) (*ArtworkLangRequest, common.ErrorWithAttachment)
	NowScreenArtworkAuction(ctx context.Context, in *AuctionDetail, opts ...grpc_go.CallOption) (*WsRtcLive, common.ErrorWithAttachment)
	CreateAuctionArtwork(ctx context.Context, in *AuctionArtworkRequest, opts ...grpc_go.CallOption) (*AuctionArtworkResponse, common.ErrorWithAttachment)
	UpdateAuctionArtwork(ctx context.Context, in *AuctionArtworkRequest, opts ...grpc_go.CallOption) (*AuctionArtworkResponse, common.ErrorWithAttachment)
	RemoveAuctionArtwork(ctx context.Context, in *AuctionArtworkDetail, opts ...grpc_go.CallOption) (*AuctionArtworkRemove, common.ErrorWithAttachment)
	ListAuctionArtwork(ctx context.Context, in *AuctionArtworkList, opts ...grpc_go.CallOption) (*AuctionArtworkListResponse, common.ErrorWithAttachment)
	DetailAuctionArtwork(ctx context.Context, in *AuctionArtworkDetail, opts ...grpc_go.CallOption) (*AuctionArtworkInfo, common.ErrorWithAttachment)
	UpdateAuctionBuy(ctx context.Context, in *AuctionBuyRequest, opts ...grpc_go.CallOption) (*AuctionBuyResponse, common.ErrorWithAttachment)
	RemoveAuctionBuy(ctx context.Context, in *AuctionBuyDetail, opts ...grpc_go.CallOption) (*AuctionBuyRemove, common.ErrorWithAttachment)
	SureAuctionBuy(ctx context.Context, in *SureAuctionBuyRequest, opts ...grpc_go.CallOption) (*AuctionBuyResponse, common.ErrorWithAttachment)
	ListAuctionBuy(ctx context.Context, in *AuctionBuyList, opts ...grpc_go.CallOption) (*AuctionBuyListResponse, common.ErrorWithAttachment)
	AdminListAuctionBuy(ctx context.Context, in *AuctionBuyList, opts ...grpc_go.CallOption) (*AuctionBuyListResponse, common.ErrorWithAttachment)
	FansListAuctionBuy(ctx context.Context, in *AuctionBuyList, opts ...grpc_go.CallOption) (*AuctionBuyListResponse, common.ErrorWithAttachment)
	AdminAllListAuctionBuy(ctx context.Context, in *AuctionBuyList, opts ...grpc_go.CallOption) (*AdminListAuctionBuyResponse, common.ErrorWithAttachment)
	ListAuctionBuyDetail(ctx context.Context, in *AuctionBuyList, opts ...grpc_go.CallOption) (*AuctionBuyListResponse, common.ErrorWithAttachment)
	DetailAuctionBuy(ctx context.Context, in *AuctionBuyDetail, opts ...grpc_go.CallOption) (*AuctionBuyRequest, common.ErrorWithAttachment)
	CancelAuctionBuy(ctx context.Context, in *AuctionBuyDetail, opts ...grpc_go.CallOption) (*CommonRes, common.ErrorWithAttachment)
	CreateAuctionBuy(ctx context.Context, in *AuctionBuyRequest, opts ...grpc_go.CallOption) (*AuctionBuyResponse, common.ErrorWithAttachment)
	CreateFansAuctionBuy(ctx context.Context, in *FansAuctionBuyRequest, opts ...grpc_go.CallOption) (*AuctionBuyResponse, common.ErrorWithAttachment)
	ListAuctionPay(ctx context.Context, in *AuctionPayList, opts ...grpc_go.CallOption) (*AuctionPayListResponse, common.ErrorWithAttachment)
	DetailAuctionPay(ctx context.Context, in *AuctionPayDetail, opts ...grpc_go.CallOption) (*AuctionPayRequest, common.ErrorWithAttachment)
	CreateAuctionPay(ctx context.Context, in *AuctionPayRequest, opts ...grpc_go.CallOption) (*AuctionPayResponse, common.ErrorWithAttachment)
	UpdateAuctionPay(ctx context.Context, in *AuctionPayRequest, opts ...grpc_go.CallOption) (*AuctionPayResponse, common.ErrorWithAttachment)
	RemoveAuctionPay(ctx context.Context, in *AuctionPayDetail, opts ...grpc_go.CallOption) (*AuctionPayRemove, common.ErrorWithAttachment)
	RemoveArtwork(ctx context.Context, in *ArtworkDetail, opts ...grpc_go.CallOption) (*ArtworkRemove, common.ErrorWithAttachment)
	ListArtwork(ctx context.Context, in *ArtworkList, opts ...grpc_go.CallOption) (*ArtworkListResponse, common.ErrorWithAttachment)
	DetailArtwork(ctx context.Context, in *ArtworkDetail, opts ...grpc_go.CallOption) (*ArtworkRequest, common.ErrorWithAttachment)
	CreateArtwork(ctx context.Context, in *ArtworkRequest, opts ...grpc_go.CallOption) (*ArtworkResponse, common.ErrorWithAttachment)
	UpdateArtwork(ctx context.Context, in *ArtworkRequest, opts ...grpc_go.CallOption) (*ArtworkResponse, common.ErrorWithAttachment)
	GetPullLiveUrl(ctx context.Context, in *CommonReq, opts ...grpc_go.CallOption) (*LiveUrlResponse, common.ErrorWithAttachment)
	CreateLiveUrl(ctx context.Context, in *CommonReq, opts ...grpc_go.CallOption) (*LiveUrlResponse, common.ErrorWithAttachment)
	GetAuctionArtworkList(ctx context.Context, in *GetAuctionArtworkListRequest, opts ...grpc_go.CallOption) (*GetAuctionArtworkListResp, common.ErrorWithAttachment)
	GetAuctionByWithLotNumber(ctx context.Context, in *GetAuctionByWithLotNumberRequest, opts ...grpc_go.CallOption) (*GetAuctionByWithLotNumberResp, common.ErrorWithAttachment)
	CreateOfflinePay(ctx context.Context, in *OfflinePayData, opts ...grpc_go.CallOption) (*CreateOfflinePayResp, common.ErrorWithAttachment)
	UpdateOfflinePay(ctx context.Context, in *OfflinePayData, opts ...grpc_go.CallOption) (*CommonMsg, common.ErrorWithAttachment)
	SaveOfflinePay(ctx context.Context, in *OfflinePayData, opts ...grpc_go.CallOption) (*CommonMsg, common.ErrorWithAttachment)
	DeleteOfflinePay(ctx context.Context, in *DeleteOfflinePayRequest, opts ...grpc_go.CallOption) (*CommonMsg, common.ErrorWithAttachment)
	GetOfflinePayDetail(ctx context.Context, in *GetOfflinePayByIdRequest, opts ...grpc_go.CallOption) (*OfflinePayData, common.ErrorWithAttachment)
	GetOfflinePayList(ctx context.Context, in *GetOfflinePayListRequest, opts ...grpc_go.CallOption) (*GetOfflinePayListResp, common.ErrorWithAttachment)
	AddOfflinePayPrice(ctx context.Context, in *AddOfflinePayPriceRequest, opts ...grpc_go.CallOption) (*OfflinePayData, common.ErrorWithAttachment)
	GetViewOfflinePayList(ctx context.Context, in *GetViewOfflinePayListReq, opts ...grpc_go.CallOption) (*GetViewOfflinePayListResp, common.ErrorWithAttachment)
	CreateAuctionSessionUserNo(ctx context.Context, in *AuctionSessionUserNoData, opts ...grpc_go.CallOption) (*CreateAuctionSessionUserNoResp, common.ErrorWithAttachment)
	UpdateAuctionSessionUserNo(ctx context.Context, in *AuctionSessionUserNoData, opts ...grpc_go.CallOption) (*CommonMsg, common.ErrorWithAttachment)
	SaveAuctionSessionUserNo(ctx context.Context, in *AuctionSessionUserNoData, opts ...grpc_go.CallOption) (*CommonMsg, common.ErrorWithAttachment)
	DeleteAuctionSessionUserNo(ctx context.Context, in *DeleteAuctionSessionUserNoRequest, opts ...grpc_go.CallOption) (*CommonMsg, common.ErrorWithAttachment)
	GetAuctionSessionUserNoDetail(ctx context.Context, in *GetAuctionSessionUserNoRequest, opts ...grpc_go.CallOption) (*AuctionSessionUserNoData, common.ErrorWithAttachment)
	GetAuctionSessionUserNoList(ctx context.Context, in *GetAuctionSessionUserNoListRequest, opts ...grpc_go.CallOption) (*GetAuctionSessionUserNoListResp, common.ErrorWithAttachment)
	GetViewAuctionSessionUserNoList(ctx context.Context, in *GetViewAuctionSessionUserNoListRequest, opts ...grpc_go.CallOption) (*GetViewAuctionSessionUserNoListResp, common.ErrorWithAttachment)
	CreateSeriesArtwork(ctx context.Context, in *SeriesArtworkData, opts ...grpc_go.CallOption) (*CreateSeriesArtworkResp, common.ErrorWithAttachment)
	UpdateSeriesArtwork(ctx context.Context, in *SeriesArtworkData, opts ...grpc_go.CallOption) (*CommonMsg, common.ErrorWithAttachment)
	SaveSeriesArtwork(ctx context.Context, in *SeriesArtworkData, opts ...grpc_go.CallOption) (*CommonMsg, common.ErrorWithAttachment)
	DeleteSeriesArtwork(ctx context.Context, in *DeleteSeriesArtworkRequest, opts ...grpc_go.CallOption) (*CommonMsg, common.ErrorWithAttachment)
	GetSeriesArtworkDetail(ctx context.Context, in *GetSeriesArtworkByIdRequest, opts ...grpc_go.CallOption) (*SeriesArtworkData, common.ErrorWithAttachment)
	GetSeriesArtworkList(ctx context.Context, in *GetSeriesArtworkListRequest, opts ...grpc_go.CallOption) (*GetSeriesArtworkListResp, common.ErrorWithAttachment)
	GetShopSeriesDetail(ctx context.Context, in *GetSeriesArtworkByIdRequest, opts ...grpc_go.CallOption) (*GetShopSeriesDetailResp, common.ErrorWithAttachment)
	CreateSeriesArtworkLang(ctx context.Context, in *SeriesArtworkLangData, opts ...grpc_go.CallOption) (*CreateSeriesArtworkLangResp, common.ErrorWithAttachment)
	UpdateSeriesArtworkLang(ctx context.Context, in *SeriesArtworkLangData, opts ...grpc_go.CallOption) (*CommonMsg, common.ErrorWithAttachment)
	SaveSeriesArtworkLang(ctx context.Context, in *SeriesArtworkLangData, opts ...grpc_go.CallOption) (*CommonMsg, common.ErrorWithAttachment)
	DeleteSeriesArtworkLang(ctx context.Context, in *DeleteSeriesArtworkLangRequest, opts ...grpc_go.CallOption) (*CommonMsg, common.ErrorWithAttachment)
	GetSeriesArtworkLangDetail(ctx context.Context, in *GetSeriesArtworkLangByIdRequest, opts ...grpc_go.CallOption) (*SeriesArtworkLangData, common.ErrorWithAttachment)
	GetSeriesArtworkLangList(ctx context.Context, in *GetSeriesArtworkLangListRequest, opts ...grpc_go.CallOption) (*GetSeriesArtworkLangListResp, common.ErrorWithAttachment)
	GetSeriesProfileList(ctx context.Context, in *GetSeriesProfileListRequest, opts ...grpc_go.CallOption) (*GetSeriesProfileListResp, common.ErrorWithAttachment)
	InitSeriesProfileCache(ctx context.Context, in *CommonReq, opts ...grpc_go.CallOption) (*CommonMsg, common.ErrorWithAttachment)
	GetSeriesFrontDetail(ctx context.Context, in *GetSeriesDetailRequest, opts ...grpc_go.CallOption) (*GetSeriesDetailResp, common.ErrorWithAttachment)
	RecordSeriesOrder(ctx context.Context, in *RecordSeriesOrderRequest, opts ...grpc_go.CallOption) (*CommonMsg, common.ErrorWithAttachment)
	RecordSeriesList(ctx context.Context, in *RecordSeriesOrderRequest, opts ...grpc_go.CallOption) (*RecordSeriesListResp, common.ErrorWithAttachment)
	UpdateSeriesOrder(ctx context.Context, in *SeriesOrderData, opts ...grpc_go.CallOption) (*CommonMsg, common.ErrorWithAttachment)
	CreateSeriesOrder(ctx context.Context, in *SeriesOrderData, opts ...grpc_go.CallOption) (*CreateSeriesOrderResp, common.ErrorWithAttachment)
	CreateCultureSeriesOrder(ctx context.Context, in *CreateCultureSeriesOrderRequest, opts ...grpc_go.CallOption) (*CommonRes, common.ErrorWithAttachment)
	SaveSeriesOrder(ctx context.Context, in *SeriesOrderData, opts ...grpc_go.CallOption) (*CommonMsg, common.ErrorWithAttachment)
	DeleteSeriesOrder(ctx context.Context, in *DeleteSeriesOrderRequest, opts ...grpc_go.CallOption) (*CommonMsg, common.ErrorWithAttachment)
	GetSeriesOrderDetail(ctx context.Context, in *GetSeriesOrderByIdRequest, opts ...grpc_go.CallOption) (*SeriesOrderData, common.ErrorWithAttachment)
	GetSeriesOrderList(ctx context.Context, in *GetSeriesOrderListRequest, opts ...grpc_go.CallOption) (*GetSeriesOrderListResp, common.ErrorWithAttachment)
	UpdateTradePayment(ctx context.Context, in *UpdateTradePaymentReq, opts ...grpc_go.CallOption) (*UpdateTradePaymentResp, common.ErrorWithAttachment)
	CreateSeriesPaymentPprof(ctx context.Context, in *CreateSeriesPaymentPprofReq, opts ...grpc_go.CallOption) (*CommonMsg, common.ErrorWithAttachment)
	CreateSeriesPaymentPprofV2(ctx context.Context, in *CreateSeriesPaymentPprofReqV2, opts ...grpc_go.CallOption) (*CommonMsg, common.ErrorWithAttachment)
	UpdateSeriesPaymentPprof(ctx context.Context, in *SeriesPaymentPprofData, opts ...grpc_go.CallOption) (*CommonMsg, common.ErrorWithAttachment)
	SaveSeriesPaymentPprof(ctx context.Context, in *SeriesPaymentPprofData, opts ...grpc_go.CallOption) (*CommonMsg, common.ErrorWithAttachment)
	DeleteSeriesPaymentPprof(ctx context.Context, in *DeleteSeriesPaymentPprofRequest, opts ...grpc_go.CallOption) (*CommonMsg, common.ErrorWithAttachment)
	GetSeriesPaymentPprofDetail(ctx context.Context, in *GetSeriesPaymentPprofByIdRequest, opts ...grpc_go.CallOption) (*SeriesPaymentPprofData, common.ErrorWithAttachment)
	GetSeriesPaymentPprofList(ctx context.Context, in *GetSeriesPaymentPprofListRequest, opts ...grpc_go.CallOption) (*GetSeriesPaymentPprofListResp, common.ErrorWithAttachment)
	GetExportOrderTypeList(ctx context.Context, in *GetExportOrderTypeListRequest, opts ...grpc_go.CallOption) (*GetExportOrderTypeListResp, common.ErrorWithAttachment)
	// 转单
	CreateTransferOrderRecord(ctx context.Context, in *TransferOrderRecordData, opts ...grpc_go.CallOption) (*CreateTransferOrderRecordResp, common.ErrorWithAttachment)
	UpdateTransferOrderRecord(ctx context.Context, in *TransferOrderRecordData, opts ...grpc_go.CallOption) (*CommonMsg, common.ErrorWithAttachment)
	SaveTransferOrderRecord(ctx context.Context, in *TransferOrderRecordData, opts ...grpc_go.CallOption) (*CommonMsg, common.ErrorWithAttachment)
	DeleteTransferOrderRecord(ctx context.Context, in *DeleteTransferOrderRecordRequest, opts ...grpc_go.CallOption) (*CommonMsg, common.ErrorWithAttachment)
	GetTransferOrderRecordDetail(ctx context.Context, in *GetTransferOrderRecordByIdRequest, opts ...grpc_go.CallOption) (*TransferOrderRecordData, common.ErrorWithAttachment)
	GetTransferOrderRecordList(ctx context.Context, in *GetTransferOrderRecordListRequest, opts ...grpc_go.CallOption) (*GetTransferOrderRecordListResp, common.ErrorWithAttachment)
	// 法大大用户实名
	CreateUser(ctx context.Context, in *UserData, opts ...grpc_go.CallOption) (*CreateUserResp, common.ErrorWithAttachment)
	UpdateUser(ctx context.Context, in *UserData, opts ...grpc_go.CallOption) (*CommonMsg, common.ErrorWithAttachment)
	SaveUser(ctx context.Context, in *UserData, opts ...grpc_go.CallOption) (*CommonMsg, common.ErrorWithAttachment)
	DeleteUser(ctx context.Context, in *DeleteUserRequest, opts ...grpc_go.CallOption) (*CommonMsg, common.ErrorWithAttachment)
	GetUserDetail(ctx context.Context, in *GetUserByIdRequest, opts ...grpc_go.CallOption) (*UserData, common.ErrorWithAttachment)
	GetUserList(ctx context.Context, in *GetUserListRequest, opts ...grpc_go.CallOption) (*GetUserListResp, common.ErrorWithAttachment)
	QueryStaffOrdersTotal(ctx context.Context, in *QueryStaffOrdersTotalReq, opts ...grpc_go.CallOption) (*QueryStaffOrdersTotalRes, common.ErrorWithAttachment)
	QueryCustomerOrdersTotal(ctx context.Context, in *QueryCustomerOrdersTotalReq, opts ...grpc_go.CallOption) (*QueryCustomerOrdersTotalRes, common.ErrorWithAttachment)
	// 映射的老戴的用户合同表
	//
	//	rpc CreateUserContract ( UserContractData )returns( CreateUserContractResp ){} //创建用户合同
	//	rpc UpdateUserContract ( UserContractData )returns( CommonMsg ){} //更新用户合同
	//	rpc SaveUserContract ( UserContractData )returns( CommonMsg ){} //覆盖用户合同
	//	rpc DeleteUserContract ( DeleteUserContractRequest )returns( CommonMsg ){}   //删除用户合同
	GetUserContractDetail(ctx context.Context, in *GetUserContractByIdRequest, opts ...grpc_go.CallOption) (*UserContractData, common.ErrorWithAttachment)
	GetUserContractList(ctx context.Context, in *GetUserContractListRequest, opts ...grpc_go.CallOption) (*GetUserContractListResp, common.ErrorWithAttachment)
}

type fengheProviderClient struct {
	cc *triple.TripleConn
}

type FengheProviderClientImpl struct {
	DetailAuctionTypeAdmin          func(ctx context.Context, in *AuctionTypeAdminDetail) (*AuctionTypeAdminRequest, error)
	CreateAuctionTypeAdmin          func(ctx context.Context, in *AuctionTypeAdminRequest) (*AuctionTypeAdminResponse, error)
	UpdateAuctionTypeAdmin          func(ctx context.Context, in *AuctionTypeAdminRequest) (*AuctionTypeAdminResponse, error)
	RemoveAuctionTypeAdmin          func(ctx context.Context, in *AuctionTypeAdminDetail) (*AuctionTypeAdminRemove, error)
	ListAuctionTypeAdmin            func(ctx context.Context, in *AuctionTypeAdminList) (*AuctionTypeAdminListResponse, error)
	DetailAuctionTypeUser           func(ctx context.Context, in *AuctionTypeUserDetail) (*AuctionTypeUserRequest, error)
	CreateAuctionTypeUser           func(ctx context.Context, in *AuctionTypeUserRequest) (*AuctionTypeUserResponse, error)
	UpdateAuctionTypeUser           func(ctx context.Context, in *AuctionTypeUserRequest) (*AuctionTypeUserResponse, error)
	RemoveAuctionTypeUser           func(ctx context.Context, in *AuctionTypeUserDetail) (*AuctionTypeUserRemove, error)
	ListAuctionTypeUser             func(ctx context.Context, in *AuctionTypeUserList) (*AuctionTypeUserListResponse, error)
	DetailAuction                   func(ctx context.Context, in *AuctionDetail) (*AuctionRequest, error)
	CreateAuction                   func(ctx context.Context, in *AuctionRequest) (*AuctionResponse, error)
	UpdateAuction                   func(ctx context.Context, in *AuctionRequest) (*AuctionResponse, error)
	UpdateAuctionEndNum             func(ctx context.Context, in *UpdateAuctionEndNumRequest) (*CommonRes, error)
	RemoveAuction                   func(ctx context.Context, in *AuctionDetail) (*AuctionRemove, error)
	ListAuction                     func(ctx context.Context, in *AuctionList) (*AuctionListResponse, error)
	UpdateBaseAuction               func(ctx context.Context, in *AuctionRequest) (*AuctionResponse, error)
	NowBiddingArtworkAuction        func(ctx context.Context, in *AuctionDetail) (*WsRtcLive, error)
	NowBiddingBuys                  func(ctx context.Context, in *AuctionDetail) (*AuctionPriceList, error)
	UpdateAuctionLang               func(ctx context.Context, in *AuctionRequest) (*AuctionListResponse, error)
	UpdateAuctionArtworkLang        func(ctx context.Context, in *ArtworkLangRequest) (*CommonMsg, error)
	DetailAuctionArtworkLang        func(ctx context.Context, in *DetailAuctionArtworkLangReq) (*ArtworkLangRequest, error)
	NowScreenArtworkAuction         func(ctx context.Context, in *AuctionDetail) (*WsRtcLive, error)
	CreateAuctionArtwork            func(ctx context.Context, in *AuctionArtworkRequest) (*AuctionArtworkResponse, error)
	UpdateAuctionArtwork            func(ctx context.Context, in *AuctionArtworkRequest) (*AuctionArtworkResponse, error)
	RemoveAuctionArtwork            func(ctx context.Context, in *AuctionArtworkDetail) (*AuctionArtworkRemove, error)
	ListAuctionArtwork              func(ctx context.Context, in *AuctionArtworkList) (*AuctionArtworkListResponse, error)
	DetailAuctionArtwork            func(ctx context.Context, in *AuctionArtworkDetail) (*AuctionArtworkInfo, error)
	UpdateAuctionBuy                func(ctx context.Context, in *AuctionBuyRequest) (*AuctionBuyResponse, error)
	RemoveAuctionBuy                func(ctx context.Context, in *AuctionBuyDetail) (*AuctionBuyRemove, error)
	SureAuctionBuy                  func(ctx context.Context, in *SureAuctionBuyRequest) (*AuctionBuyResponse, error)
	ListAuctionBuy                  func(ctx context.Context, in *AuctionBuyList) (*AuctionBuyListResponse, error)
	AdminListAuctionBuy             func(ctx context.Context, in *AuctionBuyList) (*AuctionBuyListResponse, error)
	FansListAuctionBuy              func(ctx context.Context, in *AuctionBuyList) (*AuctionBuyListResponse, error)
	AdminAllListAuctionBuy          func(ctx context.Context, in *AuctionBuyList) (*AdminListAuctionBuyResponse, error)
	ListAuctionBuyDetail            func(ctx context.Context, in *AuctionBuyList) (*AuctionBuyListResponse, error)
	DetailAuctionBuy                func(ctx context.Context, in *AuctionBuyDetail) (*AuctionBuyRequest, error)
	CancelAuctionBuy                func(ctx context.Context, in *AuctionBuyDetail) (*CommonRes, error)
	CreateAuctionBuy                func(ctx context.Context, in *AuctionBuyRequest) (*AuctionBuyResponse, error)
	CreateFansAuctionBuy            func(ctx context.Context, in *FansAuctionBuyRequest) (*AuctionBuyResponse, error)
	ListAuctionPay                  func(ctx context.Context, in *AuctionPayList) (*AuctionPayListResponse, error)
	DetailAuctionPay                func(ctx context.Context, in *AuctionPayDetail) (*AuctionPayRequest, error)
	CreateAuctionPay                func(ctx context.Context, in *AuctionPayRequest) (*AuctionPayResponse, error)
	UpdateAuctionPay                func(ctx context.Context, in *AuctionPayRequest) (*AuctionPayResponse, error)
	RemoveAuctionPay                func(ctx context.Context, in *AuctionPayDetail) (*AuctionPayRemove, error)
	RemoveArtwork                   func(ctx context.Context, in *ArtworkDetail) (*ArtworkRemove, error)
	ListArtwork                     func(ctx context.Context, in *ArtworkList) (*ArtworkListResponse, error)
	DetailArtwork                   func(ctx context.Context, in *ArtworkDetail) (*ArtworkRequest, error)
	CreateArtwork                   func(ctx context.Context, in *ArtworkRequest) (*ArtworkResponse, error)
	UpdateArtwork                   func(ctx context.Context, in *ArtworkRequest) (*ArtworkResponse, error)
	GetPullLiveUrl                  func(ctx context.Context, in *CommonReq) (*LiveUrlResponse, error)
	CreateLiveUrl                   func(ctx context.Context, in *CommonReq) (*LiveUrlResponse, error)
	GetAuctionArtworkList           func(ctx context.Context, in *GetAuctionArtworkListRequest) (*GetAuctionArtworkListResp, error)
	GetAuctionByWithLotNumber       func(ctx context.Context, in *GetAuctionByWithLotNumberRequest) (*GetAuctionByWithLotNumberResp, error)
	CreateOfflinePay                func(ctx context.Context, in *OfflinePayData) (*CreateOfflinePayResp, error)
	UpdateOfflinePay                func(ctx context.Context, in *OfflinePayData) (*CommonMsg, error)
	SaveOfflinePay                  func(ctx context.Context, in *OfflinePayData) (*CommonMsg, error)
	DeleteOfflinePay                func(ctx context.Context, in *DeleteOfflinePayRequest) (*CommonMsg, error)
	GetOfflinePayDetail             func(ctx context.Context, in *GetOfflinePayByIdRequest) (*OfflinePayData, error)
	GetOfflinePayList               func(ctx context.Context, in *GetOfflinePayListRequest) (*GetOfflinePayListResp, error)
	AddOfflinePayPrice              func(ctx context.Context, in *AddOfflinePayPriceRequest) (*OfflinePayData, error)
	GetViewOfflinePayList           func(ctx context.Context, in *GetViewOfflinePayListReq) (*GetViewOfflinePayListResp, error)
	CreateAuctionSessionUserNo      func(ctx context.Context, in *AuctionSessionUserNoData) (*CreateAuctionSessionUserNoResp, error)
	UpdateAuctionSessionUserNo      func(ctx context.Context, in *AuctionSessionUserNoData) (*CommonMsg, error)
	SaveAuctionSessionUserNo        func(ctx context.Context, in *AuctionSessionUserNoData) (*CommonMsg, error)
	DeleteAuctionSessionUserNo      func(ctx context.Context, in *DeleteAuctionSessionUserNoRequest) (*CommonMsg, error)
	GetAuctionSessionUserNoDetail   func(ctx context.Context, in *GetAuctionSessionUserNoRequest) (*AuctionSessionUserNoData, error)
	GetAuctionSessionUserNoList     func(ctx context.Context, in *GetAuctionSessionUserNoListRequest) (*GetAuctionSessionUserNoListResp, error)
	GetViewAuctionSessionUserNoList func(ctx context.Context, in *GetViewAuctionSessionUserNoListRequest) (*GetViewAuctionSessionUserNoListResp, error)
	CreateSeriesArtwork             func(ctx context.Context, in *SeriesArtworkData) (*CreateSeriesArtworkResp, error)
	UpdateSeriesArtwork             func(ctx context.Context, in *SeriesArtworkData) (*CommonMsg, error)
	SaveSeriesArtwork               func(ctx context.Context, in *SeriesArtworkData) (*CommonMsg, error)
	DeleteSeriesArtwork             func(ctx context.Context, in *DeleteSeriesArtworkRequest) (*CommonMsg, error)
	GetSeriesArtworkDetail          func(ctx context.Context, in *GetSeriesArtworkByIdRequest) (*SeriesArtworkData, error)
	GetSeriesArtworkList            func(ctx context.Context, in *GetSeriesArtworkListRequest) (*GetSeriesArtworkListResp, error)
	GetShopSeriesDetail             func(ctx context.Context, in *GetSeriesArtworkByIdRequest) (*GetShopSeriesDetailResp, error)
	CreateSeriesArtworkLang         func(ctx context.Context, in *SeriesArtworkLangData) (*CreateSeriesArtworkLangResp, error)
	UpdateSeriesArtworkLang         func(ctx context.Context, in *SeriesArtworkLangData) (*CommonMsg, error)
	SaveSeriesArtworkLang           func(ctx context.Context, in *SeriesArtworkLangData) (*CommonMsg, error)
	DeleteSeriesArtworkLang         func(ctx context.Context, in *DeleteSeriesArtworkLangRequest) (*CommonMsg, error)
	GetSeriesArtworkLangDetail      func(ctx context.Context, in *GetSeriesArtworkLangByIdRequest) (*SeriesArtworkLangData, error)
	GetSeriesArtworkLangList        func(ctx context.Context, in *GetSeriesArtworkLangListRequest) (*GetSeriesArtworkLangListResp, error)
	GetSeriesProfileList            func(ctx context.Context, in *GetSeriesProfileListRequest) (*GetSeriesProfileListResp, error)
	InitSeriesProfileCache          func(ctx context.Context, in *CommonReq) (*CommonMsg, error)
	GetSeriesFrontDetail            func(ctx context.Context, in *GetSeriesDetailRequest) (*GetSeriesDetailResp, error)
	RecordSeriesOrder               func(ctx context.Context, in *RecordSeriesOrderRequest) (*CommonMsg, error)
	RecordSeriesList                func(ctx context.Context, in *RecordSeriesOrderRequest) (*RecordSeriesListResp, error)
	UpdateSeriesOrder               func(ctx context.Context, in *SeriesOrderData) (*CommonMsg, error)
	CreateSeriesOrder               func(ctx context.Context, in *SeriesOrderData) (*CreateSeriesOrderResp, error)
	CreateCultureSeriesOrder        func(ctx context.Context, in *CreateCultureSeriesOrderRequest) (*CommonRes, error)
	SaveSeriesOrder                 func(ctx context.Context, in *SeriesOrderData) (*CommonMsg, error)
	DeleteSeriesOrder               func(ctx context.Context, in *DeleteSeriesOrderRequest) (*CommonMsg, error)
	GetSeriesOrderDetail            func(ctx context.Context, in *GetSeriesOrderByIdRequest) (*SeriesOrderData, error)
	GetSeriesOrderList              func(ctx context.Context, in *GetSeriesOrderListRequest) (*GetSeriesOrderListResp, error)
	UpdateTradePayment              func(ctx context.Context, in *UpdateTradePaymentReq) (*UpdateTradePaymentResp, error)
	CreateSeriesPaymentPprof        func(ctx context.Context, in *CreateSeriesPaymentPprofReq) (*CommonMsg, error)
	CreateSeriesPaymentPprofV2      func(ctx context.Context, in *CreateSeriesPaymentPprofReqV2) (*CommonMsg, error)
	UpdateSeriesPaymentPprof        func(ctx context.Context, in *SeriesPaymentPprofData) (*CommonMsg, error)
	SaveSeriesPaymentPprof          func(ctx context.Context, in *SeriesPaymentPprofData) (*CommonMsg, error)
	DeleteSeriesPaymentPprof        func(ctx context.Context, in *DeleteSeriesPaymentPprofRequest) (*CommonMsg, error)
	GetSeriesPaymentPprofDetail     func(ctx context.Context, in *GetSeriesPaymentPprofByIdRequest) (*SeriesPaymentPprofData, error)
	GetSeriesPaymentPprofList       func(ctx context.Context, in *GetSeriesPaymentPprofListRequest) (*GetSeriesPaymentPprofListResp, error)
	GetExportOrderTypeList          func(ctx context.Context, in *GetExportOrderTypeListRequest) (*GetExportOrderTypeListResp, error)
	CreateTransferOrderRecord       func(ctx context.Context, in *TransferOrderRecordData) (*CreateTransferOrderRecordResp, error)
	UpdateTransferOrderRecord       func(ctx context.Context, in *TransferOrderRecordData) (*CommonMsg, error)
	SaveTransferOrderRecord         func(ctx context.Context, in *TransferOrderRecordData) (*CommonMsg, error)
	DeleteTransferOrderRecord       func(ctx context.Context, in *DeleteTransferOrderRecordRequest) (*CommonMsg, error)
	GetTransferOrderRecordDetail    func(ctx context.Context, in *GetTransferOrderRecordByIdRequest) (*TransferOrderRecordData, error)
	GetTransferOrderRecordList      func(ctx context.Context, in *GetTransferOrderRecordListRequest) (*GetTransferOrderRecordListResp, error)
	CreateUser                      func(ctx context.Context, in *UserData) (*CreateUserResp, error)
	UpdateUser                      func(ctx context.Context, in *UserData) (*CommonMsg, error)
	SaveUser                        func(ctx context.Context, in *UserData) (*CommonMsg, error)
	DeleteUser                      func(ctx context.Context, in *DeleteUserRequest) (*CommonMsg, error)
	GetUserDetail                   func(ctx context.Context, in *GetUserByIdRequest) (*UserData, error)
	GetUserList                     func(ctx context.Context, in *GetUserListRequest) (*GetUserListResp, error)
	QueryStaffOrdersTotal           func(ctx context.Context, in *QueryStaffOrdersTotalReq) (*QueryStaffOrdersTotalRes, error)
	QueryCustomerOrdersTotal        func(ctx context.Context, in *QueryCustomerOrdersTotalReq) (*QueryCustomerOrdersTotalRes, error)
	GetUserContractDetail           func(ctx context.Context, in *GetUserContractByIdRequest) (*UserContractData, error)
	GetUserContractList             func(ctx context.Context, in *GetUserContractListRequest) (*GetUserContractListResp, error)
}

func (c *FengheProviderClientImpl) GetDubboStub(cc *triple.TripleConn) FengheProviderClient {
	return NewFengheProviderClient(cc)
}

func (c *FengheProviderClientImpl) XXX_InterfaceName() string {
	return "fenghe.FengheProvider"
}

func NewFengheProviderClient(cc *triple.TripleConn) FengheProviderClient {
	return &fengheProviderClient{cc}
}

func (c *fengheProviderClient) DetailAuctionTypeAdmin(ctx context.Context, in *AuctionTypeAdminDetail, opts ...grpc_go.CallOption) (*AuctionTypeAdminRequest, common.ErrorWithAttachment) {
	out := new(AuctionTypeAdminRequest)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/DetailAuctionTypeAdmin", in, out)
}

func (c *fengheProviderClient) CreateAuctionTypeAdmin(ctx context.Context, in *AuctionTypeAdminRequest, opts ...grpc_go.CallOption) (*AuctionTypeAdminResponse, common.ErrorWithAttachment) {
	out := new(AuctionTypeAdminResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/CreateAuctionTypeAdmin", in, out)
}

func (c *fengheProviderClient) UpdateAuctionTypeAdmin(ctx context.Context, in *AuctionTypeAdminRequest, opts ...grpc_go.CallOption) (*AuctionTypeAdminResponse, common.ErrorWithAttachment) {
	out := new(AuctionTypeAdminResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/UpdateAuctionTypeAdmin", in, out)
}

func (c *fengheProviderClient) RemoveAuctionTypeAdmin(ctx context.Context, in *AuctionTypeAdminDetail, opts ...grpc_go.CallOption) (*AuctionTypeAdminRemove, common.ErrorWithAttachment) {
	out := new(AuctionTypeAdminRemove)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/RemoveAuctionTypeAdmin", in, out)
}

func (c *fengheProviderClient) ListAuctionTypeAdmin(ctx context.Context, in *AuctionTypeAdminList, opts ...grpc_go.CallOption) (*AuctionTypeAdminListResponse, common.ErrorWithAttachment) {
	out := new(AuctionTypeAdminListResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/ListAuctionTypeAdmin", in, out)
}

func (c *fengheProviderClient) DetailAuctionTypeUser(ctx context.Context, in *AuctionTypeUserDetail, opts ...grpc_go.CallOption) (*AuctionTypeUserRequest, common.ErrorWithAttachment) {
	out := new(AuctionTypeUserRequest)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/DetailAuctionTypeUser", in, out)
}

func (c *fengheProviderClient) CreateAuctionTypeUser(ctx context.Context, in *AuctionTypeUserRequest, opts ...grpc_go.CallOption) (*AuctionTypeUserResponse, common.ErrorWithAttachment) {
	out := new(AuctionTypeUserResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/CreateAuctionTypeUser", in, out)
}

func (c *fengheProviderClient) UpdateAuctionTypeUser(ctx context.Context, in *AuctionTypeUserRequest, opts ...grpc_go.CallOption) (*AuctionTypeUserResponse, common.ErrorWithAttachment) {
	out := new(AuctionTypeUserResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/UpdateAuctionTypeUser", in, out)
}

func (c *fengheProviderClient) RemoveAuctionTypeUser(ctx context.Context, in *AuctionTypeUserDetail, opts ...grpc_go.CallOption) (*AuctionTypeUserRemove, common.ErrorWithAttachment) {
	out := new(AuctionTypeUserRemove)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/RemoveAuctionTypeUser", in, out)
}

func (c *fengheProviderClient) ListAuctionTypeUser(ctx context.Context, in *AuctionTypeUserList, opts ...grpc_go.CallOption) (*AuctionTypeUserListResponse, common.ErrorWithAttachment) {
	out := new(AuctionTypeUserListResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/ListAuctionTypeUser", in, out)
}

func (c *fengheProviderClient) DetailAuction(ctx context.Context, in *AuctionDetail, opts ...grpc_go.CallOption) (*AuctionRequest, common.ErrorWithAttachment) {
	out := new(AuctionRequest)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/DetailAuction", in, out)
}

func (c *fengheProviderClient) CreateAuction(ctx context.Context, in *AuctionRequest, opts ...grpc_go.CallOption) (*AuctionResponse, common.ErrorWithAttachment) {
	out := new(AuctionResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/CreateAuction", in, out)
}

func (c *fengheProviderClient) UpdateAuction(ctx context.Context, in *AuctionRequest, opts ...grpc_go.CallOption) (*AuctionResponse, common.ErrorWithAttachment) {
	out := new(AuctionResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/UpdateAuction", in, out)
}

func (c *fengheProviderClient) UpdateAuctionEndNum(ctx context.Context, in *UpdateAuctionEndNumRequest, opts ...grpc_go.CallOption) (*CommonRes, common.ErrorWithAttachment) {
	out := new(CommonRes)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/UpdateAuctionEndNum", in, out)
}

func (c *fengheProviderClient) RemoveAuction(ctx context.Context, in *AuctionDetail, opts ...grpc_go.CallOption) (*AuctionRemove, common.ErrorWithAttachment) {
	out := new(AuctionRemove)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/RemoveAuction", in, out)
}

func (c *fengheProviderClient) ListAuction(ctx context.Context, in *AuctionList, opts ...grpc_go.CallOption) (*AuctionListResponse, common.ErrorWithAttachment) {
	out := new(AuctionListResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/ListAuction", in, out)
}

func (c *fengheProviderClient) UpdateBaseAuction(ctx context.Context, in *AuctionRequest, opts ...grpc_go.CallOption) (*AuctionResponse, common.ErrorWithAttachment) {
	out := new(AuctionResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/UpdateBaseAuction", in, out)
}

func (c *fengheProviderClient) NowBiddingArtworkAuction(ctx context.Context, in *AuctionDetail, opts ...grpc_go.CallOption) (*WsRtcLive, common.ErrorWithAttachment) {
	out := new(WsRtcLive)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/NowBiddingArtworkAuction", in, out)
}

func (c *fengheProviderClient) NowBiddingBuys(ctx context.Context, in *AuctionDetail, opts ...grpc_go.CallOption) (*AuctionPriceList, common.ErrorWithAttachment) {
	out := new(AuctionPriceList)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/NowBiddingBuys", in, out)
}

func (c *fengheProviderClient) UpdateAuctionLang(ctx context.Context, in *AuctionRequest, opts ...grpc_go.CallOption) (*AuctionListResponse, common.ErrorWithAttachment) {
	out := new(AuctionListResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/UpdateAuctionLang", in, out)
}

func (c *fengheProviderClient) UpdateAuctionArtworkLang(ctx context.Context, in *ArtworkLangRequest, opts ...grpc_go.CallOption) (*CommonMsg, common.ErrorWithAttachment) {
	out := new(CommonMsg)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/UpdateAuctionArtworkLang", in, out)
}

func (c *fengheProviderClient) DetailAuctionArtworkLang(ctx context.Context, in *DetailAuctionArtworkLangReq, opts ...grpc_go.CallOption) (*ArtworkLangRequest, common.ErrorWithAttachment) {
	out := new(ArtworkLangRequest)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/DetailAuctionArtworkLang", in, out)
}

func (c *fengheProviderClient) NowScreenArtworkAuction(ctx context.Context, in *AuctionDetail, opts ...grpc_go.CallOption) (*WsRtcLive, common.ErrorWithAttachment) {
	out := new(WsRtcLive)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/NowScreenArtworkAuction", in, out)
}

func (c *fengheProviderClient) CreateAuctionArtwork(ctx context.Context, in *AuctionArtworkRequest, opts ...grpc_go.CallOption) (*AuctionArtworkResponse, common.ErrorWithAttachment) {
	out := new(AuctionArtworkResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/CreateAuctionArtwork", in, out)
}

func (c *fengheProviderClient) UpdateAuctionArtwork(ctx context.Context, in *AuctionArtworkRequest, opts ...grpc_go.CallOption) (*AuctionArtworkResponse, common.ErrorWithAttachment) {
	out := new(AuctionArtworkResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/UpdateAuctionArtwork", in, out)
}

func (c *fengheProviderClient) RemoveAuctionArtwork(ctx context.Context, in *AuctionArtworkDetail, opts ...grpc_go.CallOption) (*AuctionArtworkRemove, common.ErrorWithAttachment) {
	out := new(AuctionArtworkRemove)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/RemoveAuctionArtwork", in, out)
}

func (c *fengheProviderClient) ListAuctionArtwork(ctx context.Context, in *AuctionArtworkList, opts ...grpc_go.CallOption) (*AuctionArtworkListResponse, common.ErrorWithAttachment) {
	out := new(AuctionArtworkListResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/ListAuctionArtwork", in, out)
}

func (c *fengheProviderClient) DetailAuctionArtwork(ctx context.Context, in *AuctionArtworkDetail, opts ...grpc_go.CallOption) (*AuctionArtworkInfo, common.ErrorWithAttachment) {
	out := new(AuctionArtworkInfo)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/DetailAuctionArtwork", in, out)
}

func (c *fengheProviderClient) UpdateAuctionBuy(ctx context.Context, in *AuctionBuyRequest, opts ...grpc_go.CallOption) (*AuctionBuyResponse, common.ErrorWithAttachment) {
	out := new(AuctionBuyResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/UpdateAuctionBuy", in, out)
}

func (c *fengheProviderClient) RemoveAuctionBuy(ctx context.Context, in *AuctionBuyDetail, opts ...grpc_go.CallOption) (*AuctionBuyRemove, common.ErrorWithAttachment) {
	out := new(AuctionBuyRemove)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/RemoveAuctionBuy", in, out)
}

func (c *fengheProviderClient) SureAuctionBuy(ctx context.Context, in *SureAuctionBuyRequest, opts ...grpc_go.CallOption) (*AuctionBuyResponse, common.ErrorWithAttachment) {
	out := new(AuctionBuyResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/SureAuctionBuy", in, out)
}

func (c *fengheProviderClient) ListAuctionBuy(ctx context.Context, in *AuctionBuyList, opts ...grpc_go.CallOption) (*AuctionBuyListResponse, common.ErrorWithAttachment) {
	out := new(AuctionBuyListResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/ListAuctionBuy", in, out)
}

func (c *fengheProviderClient) AdminListAuctionBuy(ctx context.Context, in *AuctionBuyList, opts ...grpc_go.CallOption) (*AuctionBuyListResponse, common.ErrorWithAttachment) {
	out := new(AuctionBuyListResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/AdminListAuctionBuy", in, out)
}

func (c *fengheProviderClient) FansListAuctionBuy(ctx context.Context, in *AuctionBuyList, opts ...grpc_go.CallOption) (*AuctionBuyListResponse, common.ErrorWithAttachment) {
	out := new(AuctionBuyListResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/FansListAuctionBuy", in, out)
}

func (c *fengheProviderClient) AdminAllListAuctionBuy(ctx context.Context, in *AuctionBuyList, opts ...grpc_go.CallOption) (*AdminListAuctionBuyResponse, common.ErrorWithAttachment) {
	out := new(AdminListAuctionBuyResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/AdminAllListAuctionBuy", in, out)
}

func (c *fengheProviderClient) ListAuctionBuyDetail(ctx context.Context, in *AuctionBuyList, opts ...grpc_go.CallOption) (*AuctionBuyListResponse, common.ErrorWithAttachment) {
	out := new(AuctionBuyListResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/ListAuctionBuyDetail", in, out)
}

func (c *fengheProviderClient) DetailAuctionBuy(ctx context.Context, in *AuctionBuyDetail, opts ...grpc_go.CallOption) (*AuctionBuyRequest, common.ErrorWithAttachment) {
	out := new(AuctionBuyRequest)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/DetailAuctionBuy", in, out)
}

func (c *fengheProviderClient) CancelAuctionBuy(ctx context.Context, in *AuctionBuyDetail, opts ...grpc_go.CallOption) (*CommonRes, common.ErrorWithAttachment) {
	out := new(CommonRes)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/CancelAuctionBuy", in, out)
}

func (c *fengheProviderClient) CreateAuctionBuy(ctx context.Context, in *AuctionBuyRequest, opts ...grpc_go.CallOption) (*AuctionBuyResponse, common.ErrorWithAttachment) {
	out := new(AuctionBuyResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/CreateAuctionBuy", in, out)
}

func (c *fengheProviderClient) CreateFansAuctionBuy(ctx context.Context, in *FansAuctionBuyRequest, opts ...grpc_go.CallOption) (*AuctionBuyResponse, common.ErrorWithAttachment) {
	out := new(AuctionBuyResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/CreateFansAuctionBuy", in, out)
}

func (c *fengheProviderClient) ListAuctionPay(ctx context.Context, in *AuctionPayList, opts ...grpc_go.CallOption) (*AuctionPayListResponse, common.ErrorWithAttachment) {
	out := new(AuctionPayListResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/ListAuctionPay", in, out)
}

func (c *fengheProviderClient) DetailAuctionPay(ctx context.Context, in *AuctionPayDetail, opts ...grpc_go.CallOption) (*AuctionPayRequest, common.ErrorWithAttachment) {
	out := new(AuctionPayRequest)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/DetailAuctionPay", in, out)
}

func (c *fengheProviderClient) CreateAuctionPay(ctx context.Context, in *AuctionPayRequest, opts ...grpc_go.CallOption) (*AuctionPayResponse, common.ErrorWithAttachment) {
	out := new(AuctionPayResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/CreateAuctionPay", in, out)
}

func (c *fengheProviderClient) UpdateAuctionPay(ctx context.Context, in *AuctionPayRequest, opts ...grpc_go.CallOption) (*AuctionPayResponse, common.ErrorWithAttachment) {
	out := new(AuctionPayResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/UpdateAuctionPay", in, out)
}

func (c *fengheProviderClient) RemoveAuctionPay(ctx context.Context, in *AuctionPayDetail, opts ...grpc_go.CallOption) (*AuctionPayRemove, common.ErrorWithAttachment) {
	out := new(AuctionPayRemove)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/RemoveAuctionPay", in, out)
}

func (c *fengheProviderClient) RemoveArtwork(ctx context.Context, in *ArtworkDetail, opts ...grpc_go.CallOption) (*ArtworkRemove, common.ErrorWithAttachment) {
	out := new(ArtworkRemove)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/RemoveArtwork", in, out)
}

func (c *fengheProviderClient) ListArtwork(ctx context.Context, in *ArtworkList, opts ...grpc_go.CallOption) (*ArtworkListResponse, common.ErrorWithAttachment) {
	out := new(ArtworkListResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/ListArtwork", in, out)
}

func (c *fengheProviderClient) DetailArtwork(ctx context.Context, in *ArtworkDetail, opts ...grpc_go.CallOption) (*ArtworkRequest, common.ErrorWithAttachment) {
	out := new(ArtworkRequest)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/DetailArtwork", in, out)
}

func (c *fengheProviderClient) CreateArtwork(ctx context.Context, in *ArtworkRequest, opts ...grpc_go.CallOption) (*ArtworkResponse, common.ErrorWithAttachment) {
	out := new(ArtworkResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/CreateArtwork", in, out)
}

func (c *fengheProviderClient) UpdateArtwork(ctx context.Context, in *ArtworkRequest, opts ...grpc_go.CallOption) (*ArtworkResponse, common.ErrorWithAttachment) {
	out := new(ArtworkResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/UpdateArtwork", in, out)
}

func (c *fengheProviderClient) GetPullLiveUrl(ctx context.Context, in *CommonReq, opts ...grpc_go.CallOption) (*LiveUrlResponse, common.ErrorWithAttachment) {
	out := new(LiveUrlResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/GetPullLiveUrl", in, out)
}

func (c *fengheProviderClient) CreateLiveUrl(ctx context.Context, in *CommonReq, opts ...grpc_go.CallOption) (*LiveUrlResponse, common.ErrorWithAttachment) {
	out := new(LiveUrlResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/CreateLiveUrl", in, out)
}

func (c *fengheProviderClient) GetAuctionArtworkList(ctx context.Context, in *GetAuctionArtworkListRequest, opts ...grpc_go.CallOption) (*GetAuctionArtworkListResp, common.ErrorWithAttachment) {
	out := new(GetAuctionArtworkListResp)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/GetAuctionArtworkList", in, out)
}

func (c *fengheProviderClient) GetAuctionByWithLotNumber(ctx context.Context, in *GetAuctionByWithLotNumberRequest, opts ...grpc_go.CallOption) (*GetAuctionByWithLotNumberResp, common.ErrorWithAttachment) {
	out := new(GetAuctionByWithLotNumberResp)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/GetAuctionByWithLotNumber", in, out)
}

func (c *fengheProviderClient) CreateOfflinePay(ctx context.Context, in *OfflinePayData, opts ...grpc_go.CallOption) (*CreateOfflinePayResp, common.ErrorWithAttachment) {
	out := new(CreateOfflinePayResp)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/CreateOfflinePay", in, out)
}

func (c *fengheProviderClient) UpdateOfflinePay(ctx context.Context, in *OfflinePayData, opts ...grpc_go.CallOption) (*CommonMsg, common.ErrorWithAttachment) {
	out := new(CommonMsg)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/UpdateOfflinePay", in, out)
}

func (c *fengheProviderClient) SaveOfflinePay(ctx context.Context, in *OfflinePayData, opts ...grpc_go.CallOption) (*CommonMsg, common.ErrorWithAttachment) {
	out := new(CommonMsg)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/SaveOfflinePay", in, out)
}

func (c *fengheProviderClient) DeleteOfflinePay(ctx context.Context, in *DeleteOfflinePayRequest, opts ...grpc_go.CallOption) (*CommonMsg, common.ErrorWithAttachment) {
	out := new(CommonMsg)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/DeleteOfflinePay", in, out)
}

func (c *fengheProviderClient) GetOfflinePayDetail(ctx context.Context, in *GetOfflinePayByIdRequest, opts ...grpc_go.CallOption) (*OfflinePayData, common.ErrorWithAttachment) {
	out := new(OfflinePayData)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/GetOfflinePayDetail", in, out)
}

func (c *fengheProviderClient) GetOfflinePayList(ctx context.Context, in *GetOfflinePayListRequest, opts ...grpc_go.CallOption) (*GetOfflinePayListResp, common.ErrorWithAttachment) {
	out := new(GetOfflinePayListResp)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/GetOfflinePayList", in, out)
}

func (c *fengheProviderClient) AddOfflinePayPrice(ctx context.Context, in *AddOfflinePayPriceRequest, opts ...grpc_go.CallOption) (*OfflinePayData, common.ErrorWithAttachment) {
	out := new(OfflinePayData)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/AddOfflinePayPrice", in, out)
}

func (c *fengheProviderClient) GetViewOfflinePayList(ctx context.Context, in *GetViewOfflinePayListReq, opts ...grpc_go.CallOption) (*GetViewOfflinePayListResp, common.ErrorWithAttachment) {
	out := new(GetViewOfflinePayListResp)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/GetViewOfflinePayList", in, out)
}

func (c *fengheProviderClient) CreateAuctionSessionUserNo(ctx context.Context, in *AuctionSessionUserNoData, opts ...grpc_go.CallOption) (*CreateAuctionSessionUserNoResp, common.ErrorWithAttachment) {
	out := new(CreateAuctionSessionUserNoResp)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/CreateAuctionSessionUserNo", in, out)
}

func (c *fengheProviderClient) UpdateAuctionSessionUserNo(ctx context.Context, in *AuctionSessionUserNoData, opts ...grpc_go.CallOption) (*CommonMsg, common.ErrorWithAttachment) {
	out := new(CommonMsg)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/UpdateAuctionSessionUserNo", in, out)
}

func (c *fengheProviderClient) SaveAuctionSessionUserNo(ctx context.Context, in *AuctionSessionUserNoData, opts ...grpc_go.CallOption) (*CommonMsg, common.ErrorWithAttachment) {
	out := new(CommonMsg)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/SaveAuctionSessionUserNo", in, out)
}

func (c *fengheProviderClient) DeleteAuctionSessionUserNo(ctx context.Context, in *DeleteAuctionSessionUserNoRequest, opts ...grpc_go.CallOption) (*CommonMsg, common.ErrorWithAttachment) {
	out := new(CommonMsg)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/DeleteAuctionSessionUserNo", in, out)
}

func (c *fengheProviderClient) GetAuctionSessionUserNoDetail(ctx context.Context, in *GetAuctionSessionUserNoRequest, opts ...grpc_go.CallOption) (*AuctionSessionUserNoData, common.ErrorWithAttachment) {
	out := new(AuctionSessionUserNoData)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/GetAuctionSessionUserNoDetail", in, out)
}

func (c *fengheProviderClient) GetAuctionSessionUserNoList(ctx context.Context, in *GetAuctionSessionUserNoListRequest, opts ...grpc_go.CallOption) (*GetAuctionSessionUserNoListResp, common.ErrorWithAttachment) {
	out := new(GetAuctionSessionUserNoListResp)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/GetAuctionSessionUserNoList", in, out)
}

func (c *fengheProviderClient) GetViewAuctionSessionUserNoList(ctx context.Context, in *GetViewAuctionSessionUserNoListRequest, opts ...grpc_go.CallOption) (*GetViewAuctionSessionUserNoListResp, common.ErrorWithAttachment) {
	out := new(GetViewAuctionSessionUserNoListResp)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/GetViewAuctionSessionUserNoList", in, out)
}

func (c *fengheProviderClient) CreateSeriesArtwork(ctx context.Context, in *SeriesArtworkData, opts ...grpc_go.CallOption) (*CreateSeriesArtworkResp, common.ErrorWithAttachment) {
	out := new(CreateSeriesArtworkResp)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/CreateSeriesArtwork", in, out)
}

func (c *fengheProviderClient) UpdateSeriesArtwork(ctx context.Context, in *SeriesArtworkData, opts ...grpc_go.CallOption) (*CommonMsg, common.ErrorWithAttachment) {
	out := new(CommonMsg)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/UpdateSeriesArtwork", in, out)
}

func (c *fengheProviderClient) SaveSeriesArtwork(ctx context.Context, in *SeriesArtworkData, opts ...grpc_go.CallOption) (*CommonMsg, common.ErrorWithAttachment) {
	out := new(CommonMsg)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/SaveSeriesArtwork", in, out)
}

func (c *fengheProviderClient) DeleteSeriesArtwork(ctx context.Context, in *DeleteSeriesArtworkRequest, opts ...grpc_go.CallOption) (*CommonMsg, common.ErrorWithAttachment) {
	out := new(CommonMsg)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/DeleteSeriesArtwork", in, out)
}

func (c *fengheProviderClient) GetSeriesArtworkDetail(ctx context.Context, in *GetSeriesArtworkByIdRequest, opts ...grpc_go.CallOption) (*SeriesArtworkData, common.ErrorWithAttachment) {
	out := new(SeriesArtworkData)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/GetSeriesArtworkDetail", in, out)
}

func (c *fengheProviderClient) GetSeriesArtworkList(ctx context.Context, in *GetSeriesArtworkListRequest, opts ...grpc_go.CallOption) (*GetSeriesArtworkListResp, common.ErrorWithAttachment) {
	out := new(GetSeriesArtworkListResp)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/GetSeriesArtworkList", in, out)
}

func (c *fengheProviderClient) GetShopSeriesDetail(ctx context.Context, in *GetSeriesArtworkByIdRequest, opts ...grpc_go.CallOption) (*GetShopSeriesDetailResp, common.ErrorWithAttachment) {
	out := new(GetShopSeriesDetailResp)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/GetShopSeriesDetail", in, out)
}

func (c *fengheProviderClient) CreateSeriesArtworkLang(ctx context.Context, in *SeriesArtworkLangData, opts ...grpc_go.CallOption) (*CreateSeriesArtworkLangResp, common.ErrorWithAttachment) {
	out := new(CreateSeriesArtworkLangResp)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/CreateSeriesArtworkLang", in, out)
}

func (c *fengheProviderClient) UpdateSeriesArtworkLang(ctx context.Context, in *SeriesArtworkLangData, opts ...grpc_go.CallOption) (*CommonMsg, common.ErrorWithAttachment) {
	out := new(CommonMsg)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/UpdateSeriesArtworkLang", in, out)
}

func (c *fengheProviderClient) SaveSeriesArtworkLang(ctx context.Context, in *SeriesArtworkLangData, opts ...grpc_go.CallOption) (*CommonMsg, common.ErrorWithAttachment) {
	out := new(CommonMsg)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/SaveSeriesArtworkLang", in, out)
}

func (c *fengheProviderClient) DeleteSeriesArtworkLang(ctx context.Context, in *DeleteSeriesArtworkLangRequest, opts ...grpc_go.CallOption) (*CommonMsg, common.ErrorWithAttachment) {
	out := new(CommonMsg)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/DeleteSeriesArtworkLang", in, out)
}

func (c *fengheProviderClient) GetSeriesArtworkLangDetail(ctx context.Context, in *GetSeriesArtworkLangByIdRequest, opts ...grpc_go.CallOption) (*SeriesArtworkLangData, common.ErrorWithAttachment) {
	out := new(SeriesArtworkLangData)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/GetSeriesArtworkLangDetail", in, out)
}

func (c *fengheProviderClient) GetSeriesArtworkLangList(ctx context.Context, in *GetSeriesArtworkLangListRequest, opts ...grpc_go.CallOption) (*GetSeriesArtworkLangListResp, common.ErrorWithAttachment) {
	out := new(GetSeriesArtworkLangListResp)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/GetSeriesArtworkLangList", in, out)
}

func (c *fengheProviderClient) GetSeriesProfileList(ctx context.Context, in *GetSeriesProfileListRequest, opts ...grpc_go.CallOption) (*GetSeriesProfileListResp, common.ErrorWithAttachment) {
	out := new(GetSeriesProfileListResp)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/GetSeriesProfileList", in, out)
}

func (c *fengheProviderClient) InitSeriesProfileCache(ctx context.Context, in *CommonReq, opts ...grpc_go.CallOption) (*CommonMsg, common.ErrorWithAttachment) {
	out := new(CommonMsg)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/InitSeriesProfileCache", in, out)
}

func (c *fengheProviderClient) GetSeriesFrontDetail(ctx context.Context, in *GetSeriesDetailRequest, opts ...grpc_go.CallOption) (*GetSeriesDetailResp, common.ErrorWithAttachment) {
	out := new(GetSeriesDetailResp)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/GetSeriesFrontDetail", in, out)
}

func (c *fengheProviderClient) RecordSeriesOrder(ctx context.Context, in *RecordSeriesOrderRequest, opts ...grpc_go.CallOption) (*CommonMsg, common.ErrorWithAttachment) {
	out := new(CommonMsg)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/RecordSeriesOrder", in, out)
}

func (c *fengheProviderClient) RecordSeriesList(ctx context.Context, in *RecordSeriesOrderRequest, opts ...grpc_go.CallOption) (*RecordSeriesListResp, common.ErrorWithAttachment) {
	out := new(RecordSeriesListResp)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/RecordSeriesList", in, out)
}

func (c *fengheProviderClient) UpdateSeriesOrder(ctx context.Context, in *SeriesOrderData, opts ...grpc_go.CallOption) (*CommonMsg, common.ErrorWithAttachment) {
	out := new(CommonMsg)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/UpdateSeriesOrder", in, out)
}

func (c *fengheProviderClient) CreateSeriesOrder(ctx context.Context, in *SeriesOrderData, opts ...grpc_go.CallOption) (*CreateSeriesOrderResp, common.ErrorWithAttachment) {
	out := new(CreateSeriesOrderResp)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/CreateSeriesOrder", in, out)
}

func (c *fengheProviderClient) CreateCultureSeriesOrder(ctx context.Context, in *CreateCultureSeriesOrderRequest, opts ...grpc_go.CallOption) (*CommonRes, common.ErrorWithAttachment) {
	out := new(CommonRes)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/CreateCultureSeriesOrder", in, out)
}

func (c *fengheProviderClient) SaveSeriesOrder(ctx context.Context, in *SeriesOrderData, opts ...grpc_go.CallOption) (*CommonMsg, common.ErrorWithAttachment) {
	out := new(CommonMsg)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/SaveSeriesOrder", in, out)
}

func (c *fengheProviderClient) DeleteSeriesOrder(ctx context.Context, in *DeleteSeriesOrderRequest, opts ...grpc_go.CallOption) (*CommonMsg, common.ErrorWithAttachment) {
	out := new(CommonMsg)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/DeleteSeriesOrder", in, out)
}

func (c *fengheProviderClient) GetSeriesOrderDetail(ctx context.Context, in *GetSeriesOrderByIdRequest, opts ...grpc_go.CallOption) (*SeriesOrderData, common.ErrorWithAttachment) {
	out := new(SeriesOrderData)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/GetSeriesOrderDetail", in, out)
}

func (c *fengheProviderClient) GetSeriesOrderList(ctx context.Context, in *GetSeriesOrderListRequest, opts ...grpc_go.CallOption) (*GetSeriesOrderListResp, common.ErrorWithAttachment) {
	out := new(GetSeriesOrderListResp)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/GetSeriesOrderList", in, out)
}

func (c *fengheProviderClient) UpdateTradePayment(ctx context.Context, in *UpdateTradePaymentReq, opts ...grpc_go.CallOption) (*UpdateTradePaymentResp, common.ErrorWithAttachment) {
	out := new(UpdateTradePaymentResp)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/UpdateTradePayment", in, out)
}

func (c *fengheProviderClient) CreateSeriesPaymentPprof(ctx context.Context, in *CreateSeriesPaymentPprofReq, opts ...grpc_go.CallOption) (*CommonMsg, common.ErrorWithAttachment) {
	out := new(CommonMsg)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/CreateSeriesPaymentPprof", in, out)
}

func (c *fengheProviderClient) CreateSeriesPaymentPprofV2(ctx context.Context, in *CreateSeriesPaymentPprofReqV2, opts ...grpc_go.CallOption) (*CommonMsg, common.ErrorWithAttachment) {
	out := new(CommonMsg)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/CreateSeriesPaymentPprofV2", in, out)
}

func (c *fengheProviderClient) UpdateSeriesPaymentPprof(ctx context.Context, in *SeriesPaymentPprofData, opts ...grpc_go.CallOption) (*CommonMsg, common.ErrorWithAttachment) {
	out := new(CommonMsg)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/UpdateSeriesPaymentPprof", in, out)
}

func (c *fengheProviderClient) SaveSeriesPaymentPprof(ctx context.Context, in *SeriesPaymentPprofData, opts ...grpc_go.CallOption) (*CommonMsg, common.ErrorWithAttachment) {
	out := new(CommonMsg)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/SaveSeriesPaymentPprof", in, out)
}

func (c *fengheProviderClient) DeleteSeriesPaymentPprof(ctx context.Context, in *DeleteSeriesPaymentPprofRequest, opts ...grpc_go.CallOption) (*CommonMsg, common.ErrorWithAttachment) {
	out := new(CommonMsg)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/DeleteSeriesPaymentPprof", in, out)
}

func (c *fengheProviderClient) GetSeriesPaymentPprofDetail(ctx context.Context, in *GetSeriesPaymentPprofByIdRequest, opts ...grpc_go.CallOption) (*SeriesPaymentPprofData, common.ErrorWithAttachment) {
	out := new(SeriesPaymentPprofData)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/GetSeriesPaymentPprofDetail", in, out)
}

func (c *fengheProviderClient) GetSeriesPaymentPprofList(ctx context.Context, in *GetSeriesPaymentPprofListRequest, opts ...grpc_go.CallOption) (*GetSeriesPaymentPprofListResp, common.ErrorWithAttachment) {
	out := new(GetSeriesPaymentPprofListResp)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/GetSeriesPaymentPprofList", in, out)
}

func (c *fengheProviderClient) GetExportOrderTypeList(ctx context.Context, in *GetExportOrderTypeListRequest, opts ...grpc_go.CallOption) (*GetExportOrderTypeListResp, common.ErrorWithAttachment) {
	out := new(GetExportOrderTypeListResp)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/GetExportOrderTypeList", in, out)
}

func (c *fengheProviderClient) CreateTransferOrderRecord(ctx context.Context, in *TransferOrderRecordData, opts ...grpc_go.CallOption) (*CreateTransferOrderRecordResp, common.ErrorWithAttachment) {
	out := new(CreateTransferOrderRecordResp)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/CreateTransferOrderRecord", in, out)
}

func (c *fengheProviderClient) UpdateTransferOrderRecord(ctx context.Context, in *TransferOrderRecordData, opts ...grpc_go.CallOption) (*CommonMsg, common.ErrorWithAttachment) {
	out := new(CommonMsg)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/UpdateTransferOrderRecord", in, out)
}

func (c *fengheProviderClient) SaveTransferOrderRecord(ctx context.Context, in *TransferOrderRecordData, opts ...grpc_go.CallOption) (*CommonMsg, common.ErrorWithAttachment) {
	out := new(CommonMsg)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/SaveTransferOrderRecord", in, out)
}

func (c *fengheProviderClient) DeleteTransferOrderRecord(ctx context.Context, in *DeleteTransferOrderRecordRequest, opts ...grpc_go.CallOption) (*CommonMsg, common.ErrorWithAttachment) {
	out := new(CommonMsg)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/DeleteTransferOrderRecord", in, out)
}

func (c *fengheProviderClient) GetTransferOrderRecordDetail(ctx context.Context, in *GetTransferOrderRecordByIdRequest, opts ...grpc_go.CallOption) (*TransferOrderRecordData, common.ErrorWithAttachment) {
	out := new(TransferOrderRecordData)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/GetTransferOrderRecordDetail", in, out)
}

func (c *fengheProviderClient) GetTransferOrderRecordList(ctx context.Context, in *GetTransferOrderRecordListRequest, opts ...grpc_go.CallOption) (*GetTransferOrderRecordListResp, common.ErrorWithAttachment) {
	out := new(GetTransferOrderRecordListResp)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/GetTransferOrderRecordList", in, out)
}

func (c *fengheProviderClient) CreateUser(ctx context.Context, in *UserData, opts ...grpc_go.CallOption) (*CreateUserResp, common.ErrorWithAttachment) {
	out := new(CreateUserResp)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/CreateUser", in, out)
}

func (c *fengheProviderClient) UpdateUser(ctx context.Context, in *UserData, opts ...grpc_go.CallOption) (*CommonMsg, common.ErrorWithAttachment) {
	out := new(CommonMsg)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/UpdateUser", in, out)
}

func (c *fengheProviderClient) SaveUser(ctx context.Context, in *UserData, opts ...grpc_go.CallOption) (*CommonMsg, common.ErrorWithAttachment) {
	out := new(CommonMsg)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/SaveUser", in, out)
}

func (c *fengheProviderClient) DeleteUser(ctx context.Context, in *DeleteUserRequest, opts ...grpc_go.CallOption) (*CommonMsg, common.ErrorWithAttachment) {
	out := new(CommonMsg)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/DeleteUser", in, out)
}

func (c *fengheProviderClient) GetUserDetail(ctx context.Context, in *GetUserByIdRequest, opts ...grpc_go.CallOption) (*UserData, common.ErrorWithAttachment) {
	out := new(UserData)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/GetUserDetail", in, out)
}

func (c *fengheProviderClient) GetUserList(ctx context.Context, in *GetUserListRequest, opts ...grpc_go.CallOption) (*GetUserListResp, common.ErrorWithAttachment) {
	out := new(GetUserListResp)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/GetUserList", in, out)
}

func (c *fengheProviderClient) QueryStaffOrdersTotal(ctx context.Context, in *QueryStaffOrdersTotalReq, opts ...grpc_go.CallOption) (*QueryStaffOrdersTotalRes, common.ErrorWithAttachment) {
	out := new(QueryStaffOrdersTotalRes)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/QueryStaffOrdersTotal", in, out)
}

func (c *fengheProviderClient) QueryCustomerOrdersTotal(ctx context.Context, in *QueryCustomerOrdersTotalReq, opts ...grpc_go.CallOption) (*QueryCustomerOrdersTotalRes, common.ErrorWithAttachment) {
	out := new(QueryCustomerOrdersTotalRes)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/QueryCustomerOrdersTotal", in, out)
}

func (c *fengheProviderClient) GetUserContractDetail(ctx context.Context, in *GetUserContractByIdRequest, opts ...grpc_go.CallOption) (*UserContractData, common.ErrorWithAttachment) {
	out := new(UserContractData)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/GetUserContractDetail", in, out)
}

func (c *fengheProviderClient) GetUserContractList(ctx context.Context, in *GetUserContractListRequest, opts ...grpc_go.CallOption) (*GetUserContractListResp, common.ErrorWithAttachment) {
	out := new(GetUserContractListResp)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/GetUserContractList", in, out)
}

// FengheProviderServer is the server API for FengheProvider service.
// All implementations must embed UnimplementedFengheProviderServer
// for forward compatibility
type FengheProviderServer interface {
	DetailAuctionTypeAdmin(context.Context, *AuctionTypeAdminDetail) (*AuctionTypeAdminRequest, error)
	CreateAuctionTypeAdmin(context.Context, *AuctionTypeAdminRequest) (*AuctionTypeAdminResponse, error)
	UpdateAuctionTypeAdmin(context.Context, *AuctionTypeAdminRequest) (*AuctionTypeAdminResponse, error)
	RemoveAuctionTypeAdmin(context.Context, *AuctionTypeAdminDetail) (*AuctionTypeAdminRemove, error)
	ListAuctionTypeAdmin(context.Context, *AuctionTypeAdminList) (*AuctionTypeAdminListResponse, error)
	DetailAuctionTypeUser(context.Context, *AuctionTypeUserDetail) (*AuctionTypeUserRequest, error)
	CreateAuctionTypeUser(context.Context, *AuctionTypeUserRequest) (*AuctionTypeUserResponse, error)
	UpdateAuctionTypeUser(context.Context, *AuctionTypeUserRequest) (*AuctionTypeUserResponse, error)
	RemoveAuctionTypeUser(context.Context, *AuctionTypeUserDetail) (*AuctionTypeUserRemove, error)
	ListAuctionTypeUser(context.Context, *AuctionTypeUserList) (*AuctionTypeUserListResponse, error)
	DetailAuction(context.Context, *AuctionDetail) (*AuctionRequest, error)
	CreateAuction(context.Context, *AuctionRequest) (*AuctionResponse, error)
	UpdateAuction(context.Context, *AuctionRequest) (*AuctionResponse, error)
	UpdateAuctionEndNum(context.Context, *UpdateAuctionEndNumRequest) (*CommonRes, error)
	RemoveAuction(context.Context, *AuctionDetail) (*AuctionRemove, error)
	ListAuction(context.Context, *AuctionList) (*AuctionListResponse, error)
	UpdateBaseAuction(context.Context, *AuctionRequest) (*AuctionResponse, error)
	NowBiddingArtworkAuction(context.Context, *AuctionDetail) (*WsRtcLive, error)
	NowBiddingBuys(context.Context, *AuctionDetail) (*AuctionPriceList, error)
	UpdateAuctionLang(context.Context, *AuctionRequest) (*AuctionListResponse, error)
	UpdateAuctionArtworkLang(context.Context, *ArtworkLangRequest) (*CommonMsg, error)
	DetailAuctionArtworkLang(context.Context, *DetailAuctionArtworkLangReq) (*ArtworkLangRequest, error)
	NowScreenArtworkAuction(context.Context, *AuctionDetail) (*WsRtcLive, error)
	CreateAuctionArtwork(context.Context, *AuctionArtworkRequest) (*AuctionArtworkResponse, error)
	UpdateAuctionArtwork(context.Context, *AuctionArtworkRequest) (*AuctionArtworkResponse, error)
	RemoveAuctionArtwork(context.Context, *AuctionArtworkDetail) (*AuctionArtworkRemove, error)
	ListAuctionArtwork(context.Context, *AuctionArtworkList) (*AuctionArtworkListResponse, error)
	DetailAuctionArtwork(context.Context, *AuctionArtworkDetail) (*AuctionArtworkInfo, error)
	UpdateAuctionBuy(context.Context, *AuctionBuyRequest) (*AuctionBuyResponse, error)
	RemoveAuctionBuy(context.Context, *AuctionBuyDetail) (*AuctionBuyRemove, error)
	SureAuctionBuy(context.Context, *SureAuctionBuyRequest) (*AuctionBuyResponse, error)
	ListAuctionBuy(context.Context, *AuctionBuyList) (*AuctionBuyListResponse, error)
	AdminListAuctionBuy(context.Context, *AuctionBuyList) (*AuctionBuyListResponse, error)
	FansListAuctionBuy(context.Context, *AuctionBuyList) (*AuctionBuyListResponse, error)
	AdminAllListAuctionBuy(context.Context, *AuctionBuyList) (*AdminListAuctionBuyResponse, error)
	ListAuctionBuyDetail(context.Context, *AuctionBuyList) (*AuctionBuyListResponse, error)
	DetailAuctionBuy(context.Context, *AuctionBuyDetail) (*AuctionBuyRequest, error)
	CancelAuctionBuy(context.Context, *AuctionBuyDetail) (*CommonRes, error)
	CreateAuctionBuy(context.Context, *AuctionBuyRequest) (*AuctionBuyResponse, error)
	CreateFansAuctionBuy(context.Context, *FansAuctionBuyRequest) (*AuctionBuyResponse, error)
	ListAuctionPay(context.Context, *AuctionPayList) (*AuctionPayListResponse, error)
	DetailAuctionPay(context.Context, *AuctionPayDetail) (*AuctionPayRequest, error)
	CreateAuctionPay(context.Context, *AuctionPayRequest) (*AuctionPayResponse, error)
	UpdateAuctionPay(context.Context, *AuctionPayRequest) (*AuctionPayResponse, error)
	RemoveAuctionPay(context.Context, *AuctionPayDetail) (*AuctionPayRemove, error)
	RemoveArtwork(context.Context, *ArtworkDetail) (*ArtworkRemove, error)
	ListArtwork(context.Context, *ArtworkList) (*ArtworkListResponse, error)
	DetailArtwork(context.Context, *ArtworkDetail) (*ArtworkRequest, error)
	CreateArtwork(context.Context, *ArtworkRequest) (*ArtworkResponse, error)
	UpdateArtwork(context.Context, *ArtworkRequest) (*ArtworkResponse, error)
	GetPullLiveUrl(context.Context, *CommonReq) (*LiveUrlResponse, error)
	CreateLiveUrl(context.Context, *CommonReq) (*LiveUrlResponse, error)
	GetAuctionArtworkList(context.Context, *GetAuctionArtworkListRequest) (*GetAuctionArtworkListResp, error)
	GetAuctionByWithLotNumber(context.Context, *GetAuctionByWithLotNumberRequest) (*GetAuctionByWithLotNumberResp, error)
	CreateOfflinePay(context.Context, *OfflinePayData) (*CreateOfflinePayResp, error)
	UpdateOfflinePay(context.Context, *OfflinePayData) (*CommonMsg, error)
	SaveOfflinePay(context.Context, *OfflinePayData) (*CommonMsg, error)
	DeleteOfflinePay(context.Context, *DeleteOfflinePayRequest) (*CommonMsg, error)
	GetOfflinePayDetail(context.Context, *GetOfflinePayByIdRequest) (*OfflinePayData, error)
	GetOfflinePayList(context.Context, *GetOfflinePayListRequest) (*GetOfflinePayListResp, error)
	AddOfflinePayPrice(context.Context, *AddOfflinePayPriceRequest) (*OfflinePayData, error)
	GetViewOfflinePayList(context.Context, *GetViewOfflinePayListReq) (*GetViewOfflinePayListResp, error)
	CreateAuctionSessionUserNo(context.Context, *AuctionSessionUserNoData) (*CreateAuctionSessionUserNoResp, error)
	UpdateAuctionSessionUserNo(context.Context, *AuctionSessionUserNoData) (*CommonMsg, error)
	SaveAuctionSessionUserNo(context.Context, *AuctionSessionUserNoData) (*CommonMsg, error)
	DeleteAuctionSessionUserNo(context.Context, *DeleteAuctionSessionUserNoRequest) (*CommonMsg, error)
	GetAuctionSessionUserNoDetail(context.Context, *GetAuctionSessionUserNoRequest) (*AuctionSessionUserNoData, error)
	GetAuctionSessionUserNoList(context.Context, *GetAuctionSessionUserNoListRequest) (*GetAuctionSessionUserNoListResp, error)
	GetViewAuctionSessionUserNoList(context.Context, *GetViewAuctionSessionUserNoListRequest) (*GetViewAuctionSessionUserNoListResp, error)
	CreateSeriesArtwork(context.Context, *SeriesArtworkData) (*CreateSeriesArtworkResp, error)
	UpdateSeriesArtwork(context.Context, *SeriesArtworkData) (*CommonMsg, error)
	SaveSeriesArtwork(context.Context, *SeriesArtworkData) (*CommonMsg, error)
	DeleteSeriesArtwork(context.Context, *DeleteSeriesArtworkRequest) (*CommonMsg, error)
	GetSeriesArtworkDetail(context.Context, *GetSeriesArtworkByIdRequest) (*SeriesArtworkData, error)
	GetSeriesArtworkList(context.Context, *GetSeriesArtworkListRequest) (*GetSeriesArtworkListResp, error)
	GetShopSeriesDetail(context.Context, *GetSeriesArtworkByIdRequest) (*GetShopSeriesDetailResp, error)
	CreateSeriesArtworkLang(context.Context, *SeriesArtworkLangData) (*CreateSeriesArtworkLangResp, error)
	UpdateSeriesArtworkLang(context.Context, *SeriesArtworkLangData) (*CommonMsg, error)
	SaveSeriesArtworkLang(context.Context, *SeriesArtworkLangData) (*CommonMsg, error)
	DeleteSeriesArtworkLang(context.Context, *DeleteSeriesArtworkLangRequest) (*CommonMsg, error)
	GetSeriesArtworkLangDetail(context.Context, *GetSeriesArtworkLangByIdRequest) (*SeriesArtworkLangData, error)
	GetSeriesArtworkLangList(context.Context, *GetSeriesArtworkLangListRequest) (*GetSeriesArtworkLangListResp, error)
	GetSeriesProfileList(context.Context, *GetSeriesProfileListRequest) (*GetSeriesProfileListResp, error)
	InitSeriesProfileCache(context.Context, *CommonReq) (*CommonMsg, error)
	GetSeriesFrontDetail(context.Context, *GetSeriesDetailRequest) (*GetSeriesDetailResp, error)
	RecordSeriesOrder(context.Context, *RecordSeriesOrderRequest) (*CommonMsg, error)
	RecordSeriesList(context.Context, *RecordSeriesOrderRequest) (*RecordSeriesListResp, error)
	UpdateSeriesOrder(context.Context, *SeriesOrderData) (*CommonMsg, error)
	CreateSeriesOrder(context.Context, *SeriesOrderData) (*CreateSeriesOrderResp, error)
	CreateCultureSeriesOrder(context.Context, *CreateCultureSeriesOrderRequest) (*CommonRes, error)
	SaveSeriesOrder(context.Context, *SeriesOrderData) (*CommonMsg, error)
	DeleteSeriesOrder(context.Context, *DeleteSeriesOrderRequest) (*CommonMsg, error)
	GetSeriesOrderDetail(context.Context, *GetSeriesOrderByIdRequest) (*SeriesOrderData, error)
	GetSeriesOrderList(context.Context, *GetSeriesOrderListRequest) (*GetSeriesOrderListResp, error)
	UpdateTradePayment(context.Context, *UpdateTradePaymentReq) (*UpdateTradePaymentResp, error)
	CreateSeriesPaymentPprof(context.Context, *CreateSeriesPaymentPprofReq) (*CommonMsg, error)
	CreateSeriesPaymentPprofV2(context.Context, *CreateSeriesPaymentPprofReqV2) (*CommonMsg, error)
	UpdateSeriesPaymentPprof(context.Context, *SeriesPaymentPprofData) (*CommonMsg, error)
	SaveSeriesPaymentPprof(context.Context, *SeriesPaymentPprofData) (*CommonMsg, error)
	DeleteSeriesPaymentPprof(context.Context, *DeleteSeriesPaymentPprofRequest) (*CommonMsg, error)
	GetSeriesPaymentPprofDetail(context.Context, *GetSeriesPaymentPprofByIdRequest) (*SeriesPaymentPprofData, error)
	GetSeriesPaymentPprofList(context.Context, *GetSeriesPaymentPprofListRequest) (*GetSeriesPaymentPprofListResp, error)
	GetExportOrderTypeList(context.Context, *GetExportOrderTypeListRequest) (*GetExportOrderTypeListResp, error)
	// 转单
	CreateTransferOrderRecord(context.Context, *TransferOrderRecordData) (*CreateTransferOrderRecordResp, error)
	UpdateTransferOrderRecord(context.Context, *TransferOrderRecordData) (*CommonMsg, error)
	SaveTransferOrderRecord(context.Context, *TransferOrderRecordData) (*CommonMsg, error)
	DeleteTransferOrderRecord(context.Context, *DeleteTransferOrderRecordRequest) (*CommonMsg, error)
	GetTransferOrderRecordDetail(context.Context, *GetTransferOrderRecordByIdRequest) (*TransferOrderRecordData, error)
	GetTransferOrderRecordList(context.Context, *GetTransferOrderRecordListRequest) (*GetTransferOrderRecordListResp, error)
	// 法大大用户实名
	CreateUser(context.Context, *UserData) (*CreateUserResp, error)
	UpdateUser(context.Context, *UserData) (*CommonMsg, error)
	SaveUser(context.Context, *UserData) (*CommonMsg, error)
	DeleteUser(context.Context, *DeleteUserRequest) (*CommonMsg, error)
	GetUserDetail(context.Context, *GetUserByIdRequest) (*UserData, error)
	GetUserList(context.Context, *GetUserListRequest) (*GetUserListResp, error)
	QueryStaffOrdersTotal(context.Context, *QueryStaffOrdersTotalReq) (*QueryStaffOrdersTotalRes, error)
	QueryCustomerOrdersTotal(context.Context, *QueryCustomerOrdersTotalReq) (*QueryCustomerOrdersTotalRes, error)
	// 映射的老戴的用户合同表
	//
	//	rpc CreateUserContract ( UserContractData )returns( CreateUserContractResp ){} //创建用户合同
	//	rpc UpdateUserContract ( UserContractData )returns( CommonMsg ){} //更新用户合同
	//	rpc SaveUserContract ( UserContractData )returns( CommonMsg ){} //覆盖用户合同
	//	rpc DeleteUserContract ( DeleteUserContractRequest )returns( CommonMsg ){}   //删除用户合同
	GetUserContractDetail(context.Context, *GetUserContractByIdRequest) (*UserContractData, error)
	GetUserContractList(context.Context, *GetUserContractListRequest) (*GetUserContractListResp, error)
	mustEmbedUnimplementedFengheProviderServer()
}

// UnimplementedFengheProviderServer must be embedded to have forward compatible implementations.
type UnimplementedFengheProviderServer struct {
	proxyImpl protocol.Invoker
}

func (UnimplementedFengheProviderServer) DetailAuctionTypeAdmin(context.Context, *AuctionTypeAdminDetail) (*AuctionTypeAdminRequest, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DetailAuctionTypeAdmin not implemented")
}
func (UnimplementedFengheProviderServer) CreateAuctionTypeAdmin(context.Context, *AuctionTypeAdminRequest) (*AuctionTypeAdminResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateAuctionTypeAdmin not implemented")
}
func (UnimplementedFengheProviderServer) UpdateAuctionTypeAdmin(context.Context, *AuctionTypeAdminRequest) (*AuctionTypeAdminResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateAuctionTypeAdmin not implemented")
}
func (UnimplementedFengheProviderServer) RemoveAuctionTypeAdmin(context.Context, *AuctionTypeAdminDetail) (*AuctionTypeAdminRemove, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RemoveAuctionTypeAdmin not implemented")
}
func (UnimplementedFengheProviderServer) ListAuctionTypeAdmin(context.Context, *AuctionTypeAdminList) (*AuctionTypeAdminListResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListAuctionTypeAdmin not implemented")
}
func (UnimplementedFengheProviderServer) DetailAuctionTypeUser(context.Context, *AuctionTypeUserDetail) (*AuctionTypeUserRequest, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DetailAuctionTypeUser not implemented")
}
func (UnimplementedFengheProviderServer) CreateAuctionTypeUser(context.Context, *AuctionTypeUserRequest) (*AuctionTypeUserResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateAuctionTypeUser not implemented")
}
func (UnimplementedFengheProviderServer) UpdateAuctionTypeUser(context.Context, *AuctionTypeUserRequest) (*AuctionTypeUserResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateAuctionTypeUser not implemented")
}
func (UnimplementedFengheProviderServer) RemoveAuctionTypeUser(context.Context, *AuctionTypeUserDetail) (*AuctionTypeUserRemove, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RemoveAuctionTypeUser not implemented")
}
func (UnimplementedFengheProviderServer) ListAuctionTypeUser(context.Context, *AuctionTypeUserList) (*AuctionTypeUserListResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListAuctionTypeUser not implemented")
}
func (UnimplementedFengheProviderServer) DetailAuction(context.Context, *AuctionDetail) (*AuctionRequest, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DetailAuction not implemented")
}
func (UnimplementedFengheProviderServer) CreateAuction(context.Context, *AuctionRequest) (*AuctionResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateAuction not implemented")
}
func (UnimplementedFengheProviderServer) UpdateAuction(context.Context, *AuctionRequest) (*AuctionResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateAuction not implemented")
}
func (UnimplementedFengheProviderServer) UpdateAuctionEndNum(context.Context, *UpdateAuctionEndNumRequest) (*CommonRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateAuctionEndNum not implemented")
}
func (UnimplementedFengheProviderServer) RemoveAuction(context.Context, *AuctionDetail) (*AuctionRemove, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RemoveAuction not implemented")
}
func (UnimplementedFengheProviderServer) ListAuction(context.Context, *AuctionList) (*AuctionListResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListAuction not implemented")
}
func (UnimplementedFengheProviderServer) UpdateBaseAuction(context.Context, *AuctionRequest) (*AuctionResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateBaseAuction not implemented")
}
func (UnimplementedFengheProviderServer) NowBiddingArtworkAuction(context.Context, *AuctionDetail) (*WsRtcLive, error) {
	return nil, status.Errorf(codes.Unimplemented, "method NowBiddingArtworkAuction not implemented")
}
func (UnimplementedFengheProviderServer) NowBiddingBuys(context.Context, *AuctionDetail) (*AuctionPriceList, error) {
	return nil, status.Errorf(codes.Unimplemented, "method NowBiddingBuys not implemented")
}
func (UnimplementedFengheProviderServer) UpdateAuctionLang(context.Context, *AuctionRequest) (*AuctionListResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateAuctionLang not implemented")
}
func (UnimplementedFengheProviderServer) UpdateAuctionArtworkLang(context.Context, *ArtworkLangRequest) (*CommonMsg, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateAuctionArtworkLang not implemented")
}
func (UnimplementedFengheProviderServer) DetailAuctionArtworkLang(context.Context, *DetailAuctionArtworkLangReq) (*ArtworkLangRequest, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DetailAuctionArtworkLang not implemented")
}
func (UnimplementedFengheProviderServer) NowScreenArtworkAuction(context.Context, *AuctionDetail) (*WsRtcLive, error) {
	return nil, status.Errorf(codes.Unimplemented, "method NowScreenArtworkAuction not implemented")
}
func (UnimplementedFengheProviderServer) CreateAuctionArtwork(context.Context, *AuctionArtworkRequest) (*AuctionArtworkResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateAuctionArtwork not implemented")
}
func (UnimplementedFengheProviderServer) UpdateAuctionArtwork(context.Context, *AuctionArtworkRequest) (*AuctionArtworkResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateAuctionArtwork not implemented")
}
func (UnimplementedFengheProviderServer) RemoveAuctionArtwork(context.Context, *AuctionArtworkDetail) (*AuctionArtworkRemove, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RemoveAuctionArtwork not implemented")
}
func (UnimplementedFengheProviderServer) ListAuctionArtwork(context.Context, *AuctionArtworkList) (*AuctionArtworkListResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListAuctionArtwork not implemented")
}
func (UnimplementedFengheProviderServer) DetailAuctionArtwork(context.Context, *AuctionArtworkDetail) (*AuctionArtworkInfo, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DetailAuctionArtwork not implemented")
}
func (UnimplementedFengheProviderServer) UpdateAuctionBuy(context.Context, *AuctionBuyRequest) (*AuctionBuyResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateAuctionBuy not implemented")
}
func (UnimplementedFengheProviderServer) RemoveAuctionBuy(context.Context, *AuctionBuyDetail) (*AuctionBuyRemove, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RemoveAuctionBuy not implemented")
}
func (UnimplementedFengheProviderServer) SureAuctionBuy(context.Context, *SureAuctionBuyRequest) (*AuctionBuyResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SureAuctionBuy not implemented")
}
func (UnimplementedFengheProviderServer) ListAuctionBuy(context.Context, *AuctionBuyList) (*AuctionBuyListResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListAuctionBuy not implemented")
}
func (UnimplementedFengheProviderServer) AdminListAuctionBuy(context.Context, *AuctionBuyList) (*AuctionBuyListResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AdminListAuctionBuy not implemented")
}
func (UnimplementedFengheProviderServer) FansListAuctionBuy(context.Context, *AuctionBuyList) (*AuctionBuyListResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FansListAuctionBuy not implemented")
}
func (UnimplementedFengheProviderServer) AdminAllListAuctionBuy(context.Context, *AuctionBuyList) (*AdminListAuctionBuyResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AdminAllListAuctionBuy not implemented")
}
func (UnimplementedFengheProviderServer) ListAuctionBuyDetail(context.Context, *AuctionBuyList) (*AuctionBuyListResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListAuctionBuyDetail not implemented")
}
func (UnimplementedFengheProviderServer) DetailAuctionBuy(context.Context, *AuctionBuyDetail) (*AuctionBuyRequest, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DetailAuctionBuy not implemented")
}
func (UnimplementedFengheProviderServer) CancelAuctionBuy(context.Context, *AuctionBuyDetail) (*CommonRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CancelAuctionBuy not implemented")
}
func (UnimplementedFengheProviderServer) CreateAuctionBuy(context.Context, *AuctionBuyRequest) (*AuctionBuyResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateAuctionBuy not implemented")
}
func (UnimplementedFengheProviderServer) CreateFansAuctionBuy(context.Context, *FansAuctionBuyRequest) (*AuctionBuyResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateFansAuctionBuy not implemented")
}
func (UnimplementedFengheProviderServer) ListAuctionPay(context.Context, *AuctionPayList) (*AuctionPayListResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListAuctionPay not implemented")
}
func (UnimplementedFengheProviderServer) DetailAuctionPay(context.Context, *AuctionPayDetail) (*AuctionPayRequest, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DetailAuctionPay not implemented")
}
func (UnimplementedFengheProviderServer) CreateAuctionPay(context.Context, *AuctionPayRequest) (*AuctionPayResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateAuctionPay not implemented")
}
func (UnimplementedFengheProviderServer) UpdateAuctionPay(context.Context, *AuctionPayRequest) (*AuctionPayResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateAuctionPay not implemented")
}
func (UnimplementedFengheProviderServer) RemoveAuctionPay(context.Context, *AuctionPayDetail) (*AuctionPayRemove, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RemoveAuctionPay not implemented")
}
func (UnimplementedFengheProviderServer) RemoveArtwork(context.Context, *ArtworkDetail) (*ArtworkRemove, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RemoveArtwork not implemented")
}
func (UnimplementedFengheProviderServer) ListArtwork(context.Context, *ArtworkList) (*ArtworkListResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListArtwork not implemented")
}
func (UnimplementedFengheProviderServer) DetailArtwork(context.Context, *ArtworkDetail) (*ArtworkRequest, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DetailArtwork not implemented")
}
func (UnimplementedFengheProviderServer) CreateArtwork(context.Context, *ArtworkRequest) (*ArtworkResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateArtwork not implemented")
}
func (UnimplementedFengheProviderServer) UpdateArtwork(context.Context, *ArtworkRequest) (*ArtworkResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateArtwork not implemented")
}
func (UnimplementedFengheProviderServer) GetPullLiveUrl(context.Context, *CommonReq) (*LiveUrlResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetPullLiveUrl not implemented")
}
func (UnimplementedFengheProviderServer) CreateLiveUrl(context.Context, *CommonReq) (*LiveUrlResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateLiveUrl not implemented")
}
func (UnimplementedFengheProviderServer) GetAuctionArtworkList(context.Context, *GetAuctionArtworkListRequest) (*GetAuctionArtworkListResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetAuctionArtworkList not implemented")
}
func (UnimplementedFengheProviderServer) GetAuctionByWithLotNumber(context.Context, *GetAuctionByWithLotNumberRequest) (*GetAuctionByWithLotNumberResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetAuctionByWithLotNumber not implemented")
}
func (UnimplementedFengheProviderServer) CreateOfflinePay(context.Context, *OfflinePayData) (*CreateOfflinePayResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateOfflinePay not implemented")
}
func (UnimplementedFengheProviderServer) UpdateOfflinePay(context.Context, *OfflinePayData) (*CommonMsg, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateOfflinePay not implemented")
}
func (UnimplementedFengheProviderServer) SaveOfflinePay(context.Context, *OfflinePayData) (*CommonMsg, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SaveOfflinePay not implemented")
}
func (UnimplementedFengheProviderServer) DeleteOfflinePay(context.Context, *DeleteOfflinePayRequest) (*CommonMsg, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteOfflinePay not implemented")
}
func (UnimplementedFengheProviderServer) GetOfflinePayDetail(context.Context, *GetOfflinePayByIdRequest) (*OfflinePayData, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetOfflinePayDetail not implemented")
}
func (UnimplementedFengheProviderServer) GetOfflinePayList(context.Context, *GetOfflinePayListRequest) (*GetOfflinePayListResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetOfflinePayList not implemented")
}
func (UnimplementedFengheProviderServer) AddOfflinePayPrice(context.Context, *AddOfflinePayPriceRequest) (*OfflinePayData, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AddOfflinePayPrice not implemented")
}
func (UnimplementedFengheProviderServer) GetViewOfflinePayList(context.Context, *GetViewOfflinePayListReq) (*GetViewOfflinePayListResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetViewOfflinePayList not implemented")
}
func (UnimplementedFengheProviderServer) CreateAuctionSessionUserNo(context.Context, *AuctionSessionUserNoData) (*CreateAuctionSessionUserNoResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateAuctionSessionUserNo not implemented")
}
func (UnimplementedFengheProviderServer) UpdateAuctionSessionUserNo(context.Context, *AuctionSessionUserNoData) (*CommonMsg, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateAuctionSessionUserNo not implemented")
}
func (UnimplementedFengheProviderServer) SaveAuctionSessionUserNo(context.Context, *AuctionSessionUserNoData) (*CommonMsg, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SaveAuctionSessionUserNo not implemented")
}
func (UnimplementedFengheProviderServer) DeleteAuctionSessionUserNo(context.Context, *DeleteAuctionSessionUserNoRequest) (*CommonMsg, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteAuctionSessionUserNo not implemented")
}
func (UnimplementedFengheProviderServer) GetAuctionSessionUserNoDetail(context.Context, *GetAuctionSessionUserNoRequest) (*AuctionSessionUserNoData, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetAuctionSessionUserNoDetail not implemented")
}
func (UnimplementedFengheProviderServer) GetAuctionSessionUserNoList(context.Context, *GetAuctionSessionUserNoListRequest) (*GetAuctionSessionUserNoListResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetAuctionSessionUserNoList not implemented")
}
func (UnimplementedFengheProviderServer) GetViewAuctionSessionUserNoList(context.Context, *GetViewAuctionSessionUserNoListRequest) (*GetViewAuctionSessionUserNoListResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetViewAuctionSessionUserNoList not implemented")
}
func (UnimplementedFengheProviderServer) CreateSeriesArtwork(context.Context, *SeriesArtworkData) (*CreateSeriesArtworkResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateSeriesArtwork not implemented")
}
func (UnimplementedFengheProviderServer) UpdateSeriesArtwork(context.Context, *SeriesArtworkData) (*CommonMsg, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateSeriesArtwork not implemented")
}
func (UnimplementedFengheProviderServer) SaveSeriesArtwork(context.Context, *SeriesArtworkData) (*CommonMsg, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SaveSeriesArtwork not implemented")
}
func (UnimplementedFengheProviderServer) DeleteSeriesArtwork(context.Context, *DeleteSeriesArtworkRequest) (*CommonMsg, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteSeriesArtwork not implemented")
}
func (UnimplementedFengheProviderServer) GetSeriesArtworkDetail(context.Context, *GetSeriesArtworkByIdRequest) (*SeriesArtworkData, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetSeriesArtworkDetail not implemented")
}
func (UnimplementedFengheProviderServer) GetSeriesArtworkList(context.Context, *GetSeriesArtworkListRequest) (*GetSeriesArtworkListResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetSeriesArtworkList not implemented")
}
func (UnimplementedFengheProviderServer) GetShopSeriesDetail(context.Context, *GetSeriesArtworkByIdRequest) (*GetShopSeriesDetailResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetShopSeriesDetail not implemented")
}
func (UnimplementedFengheProviderServer) CreateSeriesArtworkLang(context.Context, *SeriesArtworkLangData) (*CreateSeriesArtworkLangResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateSeriesArtworkLang not implemented")
}
func (UnimplementedFengheProviderServer) UpdateSeriesArtworkLang(context.Context, *SeriesArtworkLangData) (*CommonMsg, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateSeriesArtworkLang not implemented")
}
func (UnimplementedFengheProviderServer) SaveSeriesArtworkLang(context.Context, *SeriesArtworkLangData) (*CommonMsg, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SaveSeriesArtworkLang not implemented")
}
func (UnimplementedFengheProviderServer) DeleteSeriesArtworkLang(context.Context, *DeleteSeriesArtworkLangRequest) (*CommonMsg, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteSeriesArtworkLang not implemented")
}
func (UnimplementedFengheProviderServer) GetSeriesArtworkLangDetail(context.Context, *GetSeriesArtworkLangByIdRequest) (*SeriesArtworkLangData, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetSeriesArtworkLangDetail not implemented")
}
func (UnimplementedFengheProviderServer) GetSeriesArtworkLangList(context.Context, *GetSeriesArtworkLangListRequest) (*GetSeriesArtworkLangListResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetSeriesArtworkLangList not implemented")
}
func (UnimplementedFengheProviderServer) GetSeriesProfileList(context.Context, *GetSeriesProfileListRequest) (*GetSeriesProfileListResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetSeriesProfileList not implemented")
}
func (UnimplementedFengheProviderServer) InitSeriesProfileCache(context.Context, *CommonReq) (*CommonMsg, error) {
	return nil, status.Errorf(codes.Unimplemented, "method InitSeriesProfileCache not implemented")
}
func (UnimplementedFengheProviderServer) GetSeriesFrontDetail(context.Context, *GetSeriesDetailRequest) (*GetSeriesDetailResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetSeriesFrontDetail not implemented")
}
func (UnimplementedFengheProviderServer) RecordSeriesOrder(context.Context, *RecordSeriesOrderRequest) (*CommonMsg, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RecordSeriesOrder not implemented")
}
func (UnimplementedFengheProviderServer) RecordSeriesList(context.Context, *RecordSeriesOrderRequest) (*RecordSeriesListResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RecordSeriesList not implemented")
}
func (UnimplementedFengheProviderServer) UpdateSeriesOrder(context.Context, *SeriesOrderData) (*CommonMsg, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateSeriesOrder not implemented")
}
func (UnimplementedFengheProviderServer) CreateSeriesOrder(context.Context, *SeriesOrderData) (*CreateSeriesOrderResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateSeriesOrder not implemented")
}
func (UnimplementedFengheProviderServer) CreateCultureSeriesOrder(context.Context, *CreateCultureSeriesOrderRequest) (*CommonRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateCultureSeriesOrder not implemented")
}
func (UnimplementedFengheProviderServer) SaveSeriesOrder(context.Context, *SeriesOrderData) (*CommonMsg, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SaveSeriesOrder not implemented")
}
func (UnimplementedFengheProviderServer) DeleteSeriesOrder(context.Context, *DeleteSeriesOrderRequest) (*CommonMsg, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteSeriesOrder not implemented")
}
func (UnimplementedFengheProviderServer) GetSeriesOrderDetail(context.Context, *GetSeriesOrderByIdRequest) (*SeriesOrderData, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetSeriesOrderDetail not implemented")
}
func (UnimplementedFengheProviderServer) GetSeriesOrderList(context.Context, *GetSeriesOrderListRequest) (*GetSeriesOrderListResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetSeriesOrderList not implemented")
}
func (UnimplementedFengheProviderServer) UpdateTradePayment(context.Context, *UpdateTradePaymentReq) (*UpdateTradePaymentResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateTradePayment not implemented")
}
func (UnimplementedFengheProviderServer) CreateSeriesPaymentPprof(context.Context, *CreateSeriesPaymentPprofReq) (*CommonMsg, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateSeriesPaymentPprof not implemented")
}
func (UnimplementedFengheProviderServer) CreateSeriesPaymentPprofV2(context.Context, *CreateSeriesPaymentPprofReqV2) (*CommonMsg, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateSeriesPaymentPprofV2 not implemented")
}
func (UnimplementedFengheProviderServer) UpdateSeriesPaymentPprof(context.Context, *SeriesPaymentPprofData) (*CommonMsg, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateSeriesPaymentPprof not implemented")
}
func (UnimplementedFengheProviderServer) SaveSeriesPaymentPprof(context.Context, *SeriesPaymentPprofData) (*CommonMsg, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SaveSeriesPaymentPprof not implemented")
}
func (UnimplementedFengheProviderServer) DeleteSeriesPaymentPprof(context.Context, *DeleteSeriesPaymentPprofRequest) (*CommonMsg, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteSeriesPaymentPprof not implemented")
}
func (UnimplementedFengheProviderServer) GetSeriesPaymentPprofDetail(context.Context, *GetSeriesPaymentPprofByIdRequest) (*SeriesPaymentPprofData, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetSeriesPaymentPprofDetail not implemented")
}
func (UnimplementedFengheProviderServer) GetSeriesPaymentPprofList(context.Context, *GetSeriesPaymentPprofListRequest) (*GetSeriesPaymentPprofListResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetSeriesPaymentPprofList not implemented")
}
func (UnimplementedFengheProviderServer) GetExportOrderTypeList(context.Context, *GetExportOrderTypeListRequest) (*GetExportOrderTypeListResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetExportOrderTypeList not implemented")
}
func (UnimplementedFengheProviderServer) CreateTransferOrderRecord(context.Context, *TransferOrderRecordData) (*CreateTransferOrderRecordResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateTransferOrderRecord not implemented")
}
func (UnimplementedFengheProviderServer) UpdateTransferOrderRecord(context.Context, *TransferOrderRecordData) (*CommonMsg, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateTransferOrderRecord not implemented")
}
func (UnimplementedFengheProviderServer) SaveTransferOrderRecord(context.Context, *TransferOrderRecordData) (*CommonMsg, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SaveTransferOrderRecord not implemented")
}
func (UnimplementedFengheProviderServer) DeleteTransferOrderRecord(context.Context, *DeleteTransferOrderRecordRequest) (*CommonMsg, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteTransferOrderRecord not implemented")
}
func (UnimplementedFengheProviderServer) GetTransferOrderRecordDetail(context.Context, *GetTransferOrderRecordByIdRequest) (*TransferOrderRecordData, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetTransferOrderRecordDetail not implemented")
}
func (UnimplementedFengheProviderServer) GetTransferOrderRecordList(context.Context, *GetTransferOrderRecordListRequest) (*GetTransferOrderRecordListResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetTransferOrderRecordList not implemented")
}
func (UnimplementedFengheProviderServer) CreateUser(context.Context, *UserData) (*CreateUserResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateUser not implemented")
}
func (UnimplementedFengheProviderServer) UpdateUser(context.Context, *UserData) (*CommonMsg, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateUser not implemented")
}
func (UnimplementedFengheProviderServer) SaveUser(context.Context, *UserData) (*CommonMsg, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SaveUser not implemented")
}
func (UnimplementedFengheProviderServer) DeleteUser(context.Context, *DeleteUserRequest) (*CommonMsg, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteUser not implemented")
}
func (UnimplementedFengheProviderServer) GetUserDetail(context.Context, *GetUserByIdRequest) (*UserData, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetUserDetail not implemented")
}
func (UnimplementedFengheProviderServer) GetUserList(context.Context, *GetUserListRequest) (*GetUserListResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetUserList not implemented")
}
func (UnimplementedFengheProviderServer) QueryStaffOrdersTotal(context.Context, *QueryStaffOrdersTotalReq) (*QueryStaffOrdersTotalRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method QueryStaffOrdersTotal not implemented")
}
func (UnimplementedFengheProviderServer) QueryCustomerOrdersTotal(context.Context, *QueryCustomerOrdersTotalReq) (*QueryCustomerOrdersTotalRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method QueryCustomerOrdersTotal not implemented")
}
func (UnimplementedFengheProviderServer) GetUserContractDetail(context.Context, *GetUserContractByIdRequest) (*UserContractData, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetUserContractDetail not implemented")
}
func (UnimplementedFengheProviderServer) GetUserContractList(context.Context, *GetUserContractListRequest) (*GetUserContractListResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetUserContractList not implemented")
}
func (s *UnimplementedFengheProviderServer) XXX_SetProxyImpl(impl protocol.Invoker) {
	s.proxyImpl = impl
}

func (s *UnimplementedFengheProviderServer) XXX_GetProxyImpl() protocol.Invoker {
	return s.proxyImpl
}

func (s *UnimplementedFengheProviderServer) XXX_ServiceDesc() *grpc_go.ServiceDesc {
	return &FengheProvider_ServiceDesc
}
func (s *UnimplementedFengheProviderServer) XXX_InterfaceName() string {
	return "fenghe.FengheProvider"
}

func (UnimplementedFengheProviderServer) mustEmbedUnimplementedFengheProviderServer() {}

// UnsafeFengheProviderServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to FengheProviderServer will
// result in compilation errors.
type UnsafeFengheProviderServer interface {
	mustEmbedUnimplementedFengheProviderServer()
}

func RegisterFengheProviderServer(s grpc_go.ServiceRegistrar, srv FengheProviderServer) {
	s.RegisterService(&FengheProvider_ServiceDesc, srv)
}

func _FengheProvider_DetailAuctionTypeAdmin_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(AuctionTypeAdminDetail)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("DetailAuctionTypeAdmin", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _FengheProvider_CreateAuctionTypeAdmin_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(AuctionTypeAdminRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("CreateAuctionTypeAdmin", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _FengheProvider_UpdateAuctionTypeAdmin_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(AuctionTypeAdminRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("UpdateAuctionTypeAdmin", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _FengheProvider_RemoveAuctionTypeAdmin_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(AuctionTypeAdminDetail)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("RemoveAuctionTypeAdmin", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _FengheProvider_ListAuctionTypeAdmin_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(AuctionTypeAdminList)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("ListAuctionTypeAdmin", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _FengheProvider_DetailAuctionTypeUser_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(AuctionTypeUserDetail)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("DetailAuctionTypeUser", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _FengheProvider_CreateAuctionTypeUser_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(AuctionTypeUserRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("CreateAuctionTypeUser", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _FengheProvider_UpdateAuctionTypeUser_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(AuctionTypeUserRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("UpdateAuctionTypeUser", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _FengheProvider_RemoveAuctionTypeUser_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(AuctionTypeUserDetail)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("RemoveAuctionTypeUser", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _FengheProvider_ListAuctionTypeUser_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(AuctionTypeUserList)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("ListAuctionTypeUser", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _FengheProvider_DetailAuction_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(AuctionDetail)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("DetailAuction", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _FengheProvider_CreateAuction_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(AuctionRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("CreateAuction", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _FengheProvider_UpdateAuction_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(AuctionRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("UpdateAuction", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _FengheProvider_UpdateAuctionEndNum_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateAuctionEndNumRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("UpdateAuctionEndNum", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _FengheProvider_RemoveAuction_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(AuctionDetail)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("RemoveAuction", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _FengheProvider_ListAuction_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(AuctionList)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("ListAuction", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _FengheProvider_UpdateBaseAuction_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(AuctionRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("UpdateBaseAuction", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _FengheProvider_NowBiddingArtworkAuction_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(AuctionDetail)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("NowBiddingArtworkAuction", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _FengheProvider_NowBiddingBuys_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(AuctionDetail)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("NowBiddingBuys", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _FengheProvider_UpdateAuctionLang_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(AuctionRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("UpdateAuctionLang", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _FengheProvider_UpdateAuctionArtworkLang_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(ArtworkLangRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("UpdateAuctionArtworkLang", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _FengheProvider_DetailAuctionArtworkLang_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(DetailAuctionArtworkLangReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("DetailAuctionArtworkLang", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _FengheProvider_NowScreenArtworkAuction_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(AuctionDetail)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("NowScreenArtworkAuction", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _FengheProvider_CreateAuctionArtwork_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(AuctionArtworkRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("CreateAuctionArtwork", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _FengheProvider_UpdateAuctionArtwork_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(AuctionArtworkRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("UpdateAuctionArtwork", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _FengheProvider_RemoveAuctionArtwork_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(AuctionArtworkDetail)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("RemoveAuctionArtwork", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _FengheProvider_ListAuctionArtwork_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(AuctionArtworkList)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("ListAuctionArtwork", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _FengheProvider_DetailAuctionArtwork_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(AuctionArtworkDetail)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("DetailAuctionArtwork", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _FengheProvider_UpdateAuctionBuy_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(AuctionBuyRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("UpdateAuctionBuy", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _FengheProvider_RemoveAuctionBuy_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(AuctionBuyDetail)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("RemoveAuctionBuy", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _FengheProvider_SureAuctionBuy_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(SureAuctionBuyRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("SureAuctionBuy", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _FengheProvider_ListAuctionBuy_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(AuctionBuyList)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("ListAuctionBuy", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _FengheProvider_AdminListAuctionBuy_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(AuctionBuyList)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("AdminListAuctionBuy", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _FengheProvider_FansListAuctionBuy_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(AuctionBuyList)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("FansListAuctionBuy", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _FengheProvider_AdminAllListAuctionBuy_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(AuctionBuyList)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("AdminAllListAuctionBuy", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _FengheProvider_ListAuctionBuyDetail_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(AuctionBuyList)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("ListAuctionBuyDetail", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _FengheProvider_DetailAuctionBuy_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(AuctionBuyDetail)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("DetailAuctionBuy", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _FengheProvider_CancelAuctionBuy_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(AuctionBuyDetail)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("CancelAuctionBuy", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _FengheProvider_CreateAuctionBuy_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(AuctionBuyRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("CreateAuctionBuy", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _FengheProvider_CreateFansAuctionBuy_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(FansAuctionBuyRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("CreateFansAuctionBuy", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _FengheProvider_ListAuctionPay_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(AuctionPayList)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("ListAuctionPay", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _FengheProvider_DetailAuctionPay_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(AuctionPayDetail)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("DetailAuctionPay", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _FengheProvider_CreateAuctionPay_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(AuctionPayRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("CreateAuctionPay", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _FengheProvider_UpdateAuctionPay_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(AuctionPayRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("UpdateAuctionPay", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _FengheProvider_RemoveAuctionPay_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(AuctionPayDetail)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("RemoveAuctionPay", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _FengheProvider_RemoveArtwork_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(ArtworkDetail)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("RemoveArtwork", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _FengheProvider_ListArtwork_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(ArtworkList)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("ListArtwork", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _FengheProvider_DetailArtwork_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(ArtworkDetail)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("DetailArtwork", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _FengheProvider_CreateArtwork_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(ArtworkRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("CreateArtwork", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _FengheProvider_UpdateArtwork_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(ArtworkRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("UpdateArtwork", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _FengheProvider_GetPullLiveUrl_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(CommonReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("GetPullLiveUrl", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _FengheProvider_CreateLiveUrl_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(CommonReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("CreateLiveUrl", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _FengheProvider_GetAuctionArtworkList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAuctionArtworkListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("GetAuctionArtworkList", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _FengheProvider_GetAuctionByWithLotNumber_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAuctionByWithLotNumberRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("GetAuctionByWithLotNumber", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _FengheProvider_CreateOfflinePay_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(OfflinePayData)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("CreateOfflinePay", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _FengheProvider_UpdateOfflinePay_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(OfflinePayData)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("UpdateOfflinePay", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _FengheProvider_SaveOfflinePay_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(OfflinePayData)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("SaveOfflinePay", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _FengheProvider_DeleteOfflinePay_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteOfflinePayRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("DeleteOfflinePay", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _FengheProvider_GetOfflinePayDetail_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetOfflinePayByIdRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("GetOfflinePayDetail", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _FengheProvider_GetOfflinePayList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetOfflinePayListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("GetOfflinePayList", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _FengheProvider_AddOfflinePayPrice_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddOfflinePayPriceRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("AddOfflinePayPrice", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _FengheProvider_GetViewOfflinePayList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetViewOfflinePayListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("GetViewOfflinePayList", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _FengheProvider_CreateAuctionSessionUserNo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(AuctionSessionUserNoData)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("CreateAuctionSessionUserNo", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _FengheProvider_UpdateAuctionSessionUserNo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(AuctionSessionUserNoData)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("UpdateAuctionSessionUserNo", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _FengheProvider_SaveAuctionSessionUserNo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(AuctionSessionUserNoData)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("SaveAuctionSessionUserNo", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _FengheProvider_DeleteAuctionSessionUserNo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteAuctionSessionUserNoRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("DeleteAuctionSessionUserNo", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _FengheProvider_GetAuctionSessionUserNoDetail_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAuctionSessionUserNoRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("GetAuctionSessionUserNoDetail", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _FengheProvider_GetAuctionSessionUserNoList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAuctionSessionUserNoListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("GetAuctionSessionUserNoList", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _FengheProvider_GetViewAuctionSessionUserNoList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetViewAuctionSessionUserNoListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("GetViewAuctionSessionUserNoList", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _FengheProvider_CreateSeriesArtwork_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(SeriesArtworkData)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("CreateSeriesArtwork", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _FengheProvider_UpdateSeriesArtwork_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(SeriesArtworkData)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("UpdateSeriesArtwork", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _FengheProvider_SaveSeriesArtwork_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(SeriesArtworkData)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("SaveSeriesArtwork", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _FengheProvider_DeleteSeriesArtwork_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteSeriesArtworkRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("DeleteSeriesArtwork", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _FengheProvider_GetSeriesArtworkDetail_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetSeriesArtworkByIdRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("GetSeriesArtworkDetail", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _FengheProvider_GetSeriesArtworkList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetSeriesArtworkListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("GetSeriesArtworkList", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _FengheProvider_GetShopSeriesDetail_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetSeriesArtworkByIdRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("GetShopSeriesDetail", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _FengheProvider_CreateSeriesArtworkLang_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(SeriesArtworkLangData)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("CreateSeriesArtworkLang", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _FengheProvider_UpdateSeriesArtworkLang_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(SeriesArtworkLangData)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("UpdateSeriesArtworkLang", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _FengheProvider_SaveSeriesArtworkLang_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(SeriesArtworkLangData)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("SaveSeriesArtworkLang", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _FengheProvider_DeleteSeriesArtworkLang_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteSeriesArtworkLangRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("DeleteSeriesArtworkLang", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _FengheProvider_GetSeriesArtworkLangDetail_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetSeriesArtworkLangByIdRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("GetSeriesArtworkLangDetail", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _FengheProvider_GetSeriesArtworkLangList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetSeriesArtworkLangListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("GetSeriesArtworkLangList", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _FengheProvider_GetSeriesProfileList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetSeriesProfileListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("GetSeriesProfileList", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _FengheProvider_InitSeriesProfileCache_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(CommonReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("InitSeriesProfileCache", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _FengheProvider_GetSeriesFrontDetail_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetSeriesDetailRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("GetSeriesFrontDetail", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _FengheProvider_RecordSeriesOrder_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(RecordSeriesOrderRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("RecordSeriesOrder", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _FengheProvider_RecordSeriesList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(RecordSeriesOrderRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("RecordSeriesList", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _FengheProvider_UpdateSeriesOrder_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(SeriesOrderData)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("UpdateSeriesOrder", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _FengheProvider_CreateSeriesOrder_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(SeriesOrderData)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("CreateSeriesOrder", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _FengheProvider_CreateCultureSeriesOrder_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateCultureSeriesOrderRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("CreateCultureSeriesOrder", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _FengheProvider_SaveSeriesOrder_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(SeriesOrderData)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("SaveSeriesOrder", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _FengheProvider_DeleteSeriesOrder_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteSeriesOrderRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("DeleteSeriesOrder", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _FengheProvider_GetSeriesOrderDetail_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetSeriesOrderByIdRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("GetSeriesOrderDetail", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _FengheProvider_GetSeriesOrderList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetSeriesOrderListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("GetSeriesOrderList", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _FengheProvider_UpdateTradePayment_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateTradePaymentReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("UpdateTradePayment", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _FengheProvider_CreateSeriesPaymentPprof_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateSeriesPaymentPprofReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("CreateSeriesPaymentPprof", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _FengheProvider_CreateSeriesPaymentPprofV2_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateSeriesPaymentPprofReqV2)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("CreateSeriesPaymentPprofV2", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _FengheProvider_UpdateSeriesPaymentPprof_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(SeriesPaymentPprofData)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("UpdateSeriesPaymentPprof", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _FengheProvider_SaveSeriesPaymentPprof_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(SeriesPaymentPprofData)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("SaveSeriesPaymentPprof", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _FengheProvider_DeleteSeriesPaymentPprof_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteSeriesPaymentPprofRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("DeleteSeriesPaymentPprof", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _FengheProvider_GetSeriesPaymentPprofDetail_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetSeriesPaymentPprofByIdRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("GetSeriesPaymentPprofDetail", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _FengheProvider_GetSeriesPaymentPprofList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetSeriesPaymentPprofListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("GetSeriesPaymentPprofList", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _FengheProvider_GetExportOrderTypeList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetExportOrderTypeListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("GetExportOrderTypeList", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _FengheProvider_CreateTransferOrderRecord_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(TransferOrderRecordData)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("CreateTransferOrderRecord", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _FengheProvider_UpdateTransferOrderRecord_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(TransferOrderRecordData)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("UpdateTransferOrderRecord", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _FengheProvider_SaveTransferOrderRecord_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(TransferOrderRecordData)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("SaveTransferOrderRecord", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _FengheProvider_DeleteTransferOrderRecord_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteTransferOrderRecordRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("DeleteTransferOrderRecord", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _FengheProvider_GetTransferOrderRecordDetail_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetTransferOrderRecordByIdRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("GetTransferOrderRecordDetail", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _FengheProvider_GetTransferOrderRecordList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetTransferOrderRecordListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("GetTransferOrderRecordList", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _FengheProvider_CreateUser_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(UserData)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("CreateUser", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _FengheProvider_UpdateUser_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(UserData)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("UpdateUser", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _FengheProvider_SaveUser_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(UserData)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("SaveUser", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _FengheProvider_DeleteUser_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteUserRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("DeleteUser", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _FengheProvider_GetUserDetail_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUserByIdRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("GetUserDetail", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _FengheProvider_GetUserList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUserListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("GetUserList", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _FengheProvider_QueryStaffOrdersTotal_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(QueryStaffOrdersTotalReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("QueryStaffOrdersTotal", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _FengheProvider_QueryCustomerOrdersTotal_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(QueryCustomerOrdersTotalReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("QueryCustomerOrdersTotal", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _FengheProvider_GetUserContractDetail_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUserContractByIdRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("GetUserContractDetail", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _FengheProvider_GetUserContractList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUserContractListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("GetUserContractList", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

// FengheProvider_ServiceDesc is the grpc_go.ServiceDesc for FengheProvider service.
// It's only intended for direct use with grpc_go.RegisterService,
// and not to be introspected or modified (even as a copy)
var FengheProvider_ServiceDesc = grpc_go.ServiceDesc{
	ServiceName: "fenghe.FengheProvider",
	HandlerType: (*FengheProviderServer)(nil),
	Methods: []grpc_go.MethodDesc{
		{
			MethodName: "DetailAuctionTypeAdmin",
			Handler:    _FengheProvider_DetailAuctionTypeAdmin_Handler,
		},
		{
			MethodName: "CreateAuctionTypeAdmin",
			Handler:    _FengheProvider_CreateAuctionTypeAdmin_Handler,
		},
		{
			MethodName: "UpdateAuctionTypeAdmin",
			Handler:    _FengheProvider_UpdateAuctionTypeAdmin_Handler,
		},
		{
			MethodName: "RemoveAuctionTypeAdmin",
			Handler:    _FengheProvider_RemoveAuctionTypeAdmin_Handler,
		},
		{
			MethodName: "ListAuctionTypeAdmin",
			Handler:    _FengheProvider_ListAuctionTypeAdmin_Handler,
		},
		{
			MethodName: "DetailAuctionTypeUser",
			Handler:    _FengheProvider_DetailAuctionTypeUser_Handler,
		},
		{
			MethodName: "CreateAuctionTypeUser",
			Handler:    _FengheProvider_CreateAuctionTypeUser_Handler,
		},
		{
			MethodName: "UpdateAuctionTypeUser",
			Handler:    _FengheProvider_UpdateAuctionTypeUser_Handler,
		},
		{
			MethodName: "RemoveAuctionTypeUser",
			Handler:    _FengheProvider_RemoveAuctionTypeUser_Handler,
		},
		{
			MethodName: "ListAuctionTypeUser",
			Handler:    _FengheProvider_ListAuctionTypeUser_Handler,
		},
		{
			MethodName: "DetailAuction",
			Handler:    _FengheProvider_DetailAuction_Handler,
		},
		{
			MethodName: "CreateAuction",
			Handler:    _FengheProvider_CreateAuction_Handler,
		},
		{
			MethodName: "UpdateAuction",
			Handler:    _FengheProvider_UpdateAuction_Handler,
		},
		{
			MethodName: "UpdateAuctionEndNum",
			Handler:    _FengheProvider_UpdateAuctionEndNum_Handler,
		},
		{
			MethodName: "RemoveAuction",
			Handler:    _FengheProvider_RemoveAuction_Handler,
		},
		{
			MethodName: "ListAuction",
			Handler:    _FengheProvider_ListAuction_Handler,
		},
		{
			MethodName: "UpdateBaseAuction",
			Handler:    _FengheProvider_UpdateBaseAuction_Handler,
		},
		{
			MethodName: "NowBiddingArtworkAuction",
			Handler:    _FengheProvider_NowBiddingArtworkAuction_Handler,
		},
		{
			MethodName: "NowBiddingBuys",
			Handler:    _FengheProvider_NowBiddingBuys_Handler,
		},
		{
			MethodName: "UpdateAuctionLang",
			Handler:    _FengheProvider_UpdateAuctionLang_Handler,
		},
		{
			MethodName: "UpdateAuctionArtworkLang",
			Handler:    _FengheProvider_UpdateAuctionArtworkLang_Handler,
		},
		{
			MethodName: "DetailAuctionArtworkLang",
			Handler:    _FengheProvider_DetailAuctionArtworkLang_Handler,
		},
		{
			MethodName: "NowScreenArtworkAuction",
			Handler:    _FengheProvider_NowScreenArtworkAuction_Handler,
		},
		{
			MethodName: "CreateAuctionArtwork",
			Handler:    _FengheProvider_CreateAuctionArtwork_Handler,
		},
		{
			MethodName: "UpdateAuctionArtwork",
			Handler:    _FengheProvider_UpdateAuctionArtwork_Handler,
		},
		{
			MethodName: "RemoveAuctionArtwork",
			Handler:    _FengheProvider_RemoveAuctionArtwork_Handler,
		},
		{
			MethodName: "ListAuctionArtwork",
			Handler:    _FengheProvider_ListAuctionArtwork_Handler,
		},
		{
			MethodName: "DetailAuctionArtwork",
			Handler:    _FengheProvider_DetailAuctionArtwork_Handler,
		},
		{
			MethodName: "UpdateAuctionBuy",
			Handler:    _FengheProvider_UpdateAuctionBuy_Handler,
		},
		{
			MethodName: "RemoveAuctionBuy",
			Handler:    _FengheProvider_RemoveAuctionBuy_Handler,
		},
		{
			MethodName: "SureAuctionBuy",
			Handler:    _FengheProvider_SureAuctionBuy_Handler,
		},
		{
			MethodName: "ListAuctionBuy",
			Handler:    _FengheProvider_ListAuctionBuy_Handler,
		},
		{
			MethodName: "AdminListAuctionBuy",
			Handler:    _FengheProvider_AdminListAuctionBuy_Handler,
		},
		{
			MethodName: "FansListAuctionBuy",
			Handler:    _FengheProvider_FansListAuctionBuy_Handler,
		},
		{
			MethodName: "AdminAllListAuctionBuy",
			Handler:    _FengheProvider_AdminAllListAuctionBuy_Handler,
		},
		{
			MethodName: "ListAuctionBuyDetail",
			Handler:    _FengheProvider_ListAuctionBuyDetail_Handler,
		},
		{
			MethodName: "DetailAuctionBuy",
			Handler:    _FengheProvider_DetailAuctionBuy_Handler,
		},
		{
			MethodName: "CancelAuctionBuy",
			Handler:    _FengheProvider_CancelAuctionBuy_Handler,
		},
		{
			MethodName: "CreateAuctionBuy",
			Handler:    _FengheProvider_CreateAuctionBuy_Handler,
		},
		{
			MethodName: "CreateFansAuctionBuy",
			Handler:    _FengheProvider_CreateFansAuctionBuy_Handler,
		},
		{
			MethodName: "ListAuctionPay",
			Handler:    _FengheProvider_ListAuctionPay_Handler,
		},
		{
			MethodName: "DetailAuctionPay",
			Handler:    _FengheProvider_DetailAuctionPay_Handler,
		},
		{
			MethodName: "CreateAuctionPay",
			Handler:    _FengheProvider_CreateAuctionPay_Handler,
		},
		{
			MethodName: "UpdateAuctionPay",
			Handler:    _FengheProvider_UpdateAuctionPay_Handler,
		},
		{
			MethodName: "RemoveAuctionPay",
			Handler:    _FengheProvider_RemoveAuctionPay_Handler,
		},
		{
			MethodName: "RemoveArtwork",
			Handler:    _FengheProvider_RemoveArtwork_Handler,
		},
		{
			MethodName: "ListArtwork",
			Handler:    _FengheProvider_ListArtwork_Handler,
		},
		{
			MethodName: "DetailArtwork",
			Handler:    _FengheProvider_DetailArtwork_Handler,
		},
		{
			MethodName: "CreateArtwork",
			Handler:    _FengheProvider_CreateArtwork_Handler,
		},
		{
			MethodName: "UpdateArtwork",
			Handler:    _FengheProvider_UpdateArtwork_Handler,
		},
		{
			MethodName: "GetPullLiveUrl",
			Handler:    _FengheProvider_GetPullLiveUrl_Handler,
		},
		{
			MethodName: "CreateLiveUrl",
			Handler:    _FengheProvider_CreateLiveUrl_Handler,
		},
		{
			MethodName: "GetAuctionArtworkList",
			Handler:    _FengheProvider_GetAuctionArtworkList_Handler,
		},
		{
			MethodName: "GetAuctionByWithLotNumber",
			Handler:    _FengheProvider_GetAuctionByWithLotNumber_Handler,
		},
		{
			MethodName: "CreateOfflinePay",
			Handler:    _FengheProvider_CreateOfflinePay_Handler,
		},
		{
			MethodName: "UpdateOfflinePay",
			Handler:    _FengheProvider_UpdateOfflinePay_Handler,
		},
		{
			MethodName: "SaveOfflinePay",
			Handler:    _FengheProvider_SaveOfflinePay_Handler,
		},
		{
			MethodName: "DeleteOfflinePay",
			Handler:    _FengheProvider_DeleteOfflinePay_Handler,
		},
		{
			MethodName: "GetOfflinePayDetail",
			Handler:    _FengheProvider_GetOfflinePayDetail_Handler,
		},
		{
			MethodName: "GetOfflinePayList",
			Handler:    _FengheProvider_GetOfflinePayList_Handler,
		},
		{
			MethodName: "AddOfflinePayPrice",
			Handler:    _FengheProvider_AddOfflinePayPrice_Handler,
		},
		{
			MethodName: "GetViewOfflinePayList",
			Handler:    _FengheProvider_GetViewOfflinePayList_Handler,
		},
		{
			MethodName: "CreateAuctionSessionUserNo",
			Handler:    _FengheProvider_CreateAuctionSessionUserNo_Handler,
		},
		{
			MethodName: "UpdateAuctionSessionUserNo",
			Handler:    _FengheProvider_UpdateAuctionSessionUserNo_Handler,
		},
		{
			MethodName: "SaveAuctionSessionUserNo",
			Handler:    _FengheProvider_SaveAuctionSessionUserNo_Handler,
		},
		{
			MethodName: "DeleteAuctionSessionUserNo",
			Handler:    _FengheProvider_DeleteAuctionSessionUserNo_Handler,
		},
		{
			MethodName: "GetAuctionSessionUserNoDetail",
			Handler:    _FengheProvider_GetAuctionSessionUserNoDetail_Handler,
		},
		{
			MethodName: "GetAuctionSessionUserNoList",
			Handler:    _FengheProvider_GetAuctionSessionUserNoList_Handler,
		},
		{
			MethodName: "GetViewAuctionSessionUserNoList",
			Handler:    _FengheProvider_GetViewAuctionSessionUserNoList_Handler,
		},
		{
			MethodName: "CreateSeriesArtwork",
			Handler:    _FengheProvider_CreateSeriesArtwork_Handler,
		},
		{
			MethodName: "UpdateSeriesArtwork",
			Handler:    _FengheProvider_UpdateSeriesArtwork_Handler,
		},
		{
			MethodName: "SaveSeriesArtwork",
			Handler:    _FengheProvider_SaveSeriesArtwork_Handler,
		},
		{
			MethodName: "DeleteSeriesArtwork",
			Handler:    _FengheProvider_DeleteSeriesArtwork_Handler,
		},
		{
			MethodName: "GetSeriesArtworkDetail",
			Handler:    _FengheProvider_GetSeriesArtworkDetail_Handler,
		},
		{
			MethodName: "GetSeriesArtworkList",
			Handler:    _FengheProvider_GetSeriesArtworkList_Handler,
		},
		{
			MethodName: "GetShopSeriesDetail",
			Handler:    _FengheProvider_GetShopSeriesDetail_Handler,
		},
		{
			MethodName: "CreateSeriesArtworkLang",
			Handler:    _FengheProvider_CreateSeriesArtworkLang_Handler,
		},
		{
			MethodName: "UpdateSeriesArtworkLang",
			Handler:    _FengheProvider_UpdateSeriesArtworkLang_Handler,
		},
		{
			MethodName: "SaveSeriesArtworkLang",
			Handler:    _FengheProvider_SaveSeriesArtworkLang_Handler,
		},
		{
			MethodName: "DeleteSeriesArtworkLang",
			Handler:    _FengheProvider_DeleteSeriesArtworkLang_Handler,
		},
		{
			MethodName: "GetSeriesArtworkLangDetail",
			Handler:    _FengheProvider_GetSeriesArtworkLangDetail_Handler,
		},
		{
			MethodName: "GetSeriesArtworkLangList",
			Handler:    _FengheProvider_GetSeriesArtworkLangList_Handler,
		},
		{
			MethodName: "GetSeriesProfileList",
			Handler:    _FengheProvider_GetSeriesProfileList_Handler,
		},
		{
			MethodName: "InitSeriesProfileCache",
			Handler:    _FengheProvider_InitSeriesProfileCache_Handler,
		},
		{
			MethodName: "GetSeriesFrontDetail",
			Handler:    _FengheProvider_GetSeriesFrontDetail_Handler,
		},
		{
			MethodName: "RecordSeriesOrder",
			Handler:    _FengheProvider_RecordSeriesOrder_Handler,
		},
		{
			MethodName: "RecordSeriesList",
			Handler:    _FengheProvider_RecordSeriesList_Handler,
		},
		{
			MethodName: "UpdateSeriesOrder",
			Handler:    _FengheProvider_UpdateSeriesOrder_Handler,
		},
		{
			MethodName: "CreateSeriesOrder",
			Handler:    _FengheProvider_CreateSeriesOrder_Handler,
		},
		{
			MethodName: "CreateCultureSeriesOrder",
			Handler:    _FengheProvider_CreateCultureSeriesOrder_Handler,
		},
		{
			MethodName: "SaveSeriesOrder",
			Handler:    _FengheProvider_SaveSeriesOrder_Handler,
		},
		{
			MethodName: "DeleteSeriesOrder",
			Handler:    _FengheProvider_DeleteSeriesOrder_Handler,
		},
		{
			MethodName: "GetSeriesOrderDetail",
			Handler:    _FengheProvider_GetSeriesOrderDetail_Handler,
		},
		{
			MethodName: "GetSeriesOrderList",
			Handler:    _FengheProvider_GetSeriesOrderList_Handler,
		},
		{
			MethodName: "UpdateTradePayment",
			Handler:    _FengheProvider_UpdateTradePayment_Handler,
		},
		{
			MethodName: "CreateSeriesPaymentPprof",
			Handler:    _FengheProvider_CreateSeriesPaymentPprof_Handler,
		},
		{
			MethodName: "CreateSeriesPaymentPprofV2",
			Handler:    _FengheProvider_CreateSeriesPaymentPprofV2_Handler,
		},
		{
			MethodName: "UpdateSeriesPaymentPprof",
			Handler:    _FengheProvider_UpdateSeriesPaymentPprof_Handler,
		},
		{
			MethodName: "SaveSeriesPaymentPprof",
			Handler:    _FengheProvider_SaveSeriesPaymentPprof_Handler,
		},
		{
			MethodName: "DeleteSeriesPaymentPprof",
			Handler:    _FengheProvider_DeleteSeriesPaymentPprof_Handler,
		},
		{
			MethodName: "GetSeriesPaymentPprofDetail",
			Handler:    _FengheProvider_GetSeriesPaymentPprofDetail_Handler,
		},
		{
			MethodName: "GetSeriesPaymentPprofList",
			Handler:    _FengheProvider_GetSeriesPaymentPprofList_Handler,
		},
		{
			MethodName: "GetExportOrderTypeList",
			Handler:    _FengheProvider_GetExportOrderTypeList_Handler,
		},
		{
			MethodName: "CreateTransferOrderRecord",
			Handler:    _FengheProvider_CreateTransferOrderRecord_Handler,
		},
		{
			MethodName: "UpdateTransferOrderRecord",
			Handler:    _FengheProvider_UpdateTransferOrderRecord_Handler,
		},
		{
			MethodName: "SaveTransferOrderRecord",
			Handler:    _FengheProvider_SaveTransferOrderRecord_Handler,
		},
		{
			MethodName: "DeleteTransferOrderRecord",
			Handler:    _FengheProvider_DeleteTransferOrderRecord_Handler,
		},
		{
			MethodName: "GetTransferOrderRecordDetail",
			Handler:    _FengheProvider_GetTransferOrderRecordDetail_Handler,
		},
		{
			MethodName: "GetTransferOrderRecordList",
			Handler:    _FengheProvider_GetTransferOrderRecordList_Handler,
		},
		{
			MethodName: "CreateUser",
			Handler:    _FengheProvider_CreateUser_Handler,
		},
		{
			MethodName: "UpdateUser",
			Handler:    _FengheProvider_UpdateUser_Handler,
		},
		{
			MethodName: "SaveUser",
			Handler:    _FengheProvider_SaveUser_Handler,
		},
		{
			MethodName: "DeleteUser",
			Handler:    _FengheProvider_DeleteUser_Handler,
		},
		{
			MethodName: "GetUserDetail",
			Handler:    _FengheProvider_GetUserDetail_Handler,
		},
		{
			MethodName: "GetUserList",
			Handler:    _FengheProvider_GetUserList_Handler,
		},
		{
			MethodName: "QueryStaffOrdersTotal",
			Handler:    _FengheProvider_QueryStaffOrdersTotal_Handler,
		},
		{
			MethodName: "QueryCustomerOrdersTotal",
			Handler:    _FengheProvider_QueryCustomerOrdersTotal_Handler,
		},
		{
			MethodName: "GetUserContractDetail",
			Handler:    _FengheProvider_GetUserContractDetail_Handler,
		},
		{
			MethodName: "GetUserContractList",
			Handler:    _FengheProvider_GetUserContractList_Handler,
		},
	},
	Streams:  []grpc_go.StreamDesc{},
	Metadata: "fenghe.proto",
}
