// Code generated by protoc-gen-gogo. DO NOT EDIT.
// source: fenghe.proto

package fenghe

import (
	fmt "fmt"
	math "math"
	proto "github.com/golang/protobuf/proto"
	github_com_mwitkow_go_proto_validators "github.com/mwitkow/go-proto-validators"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

func (this *GetShopSeriesDetailResp) Validate() error {
	return nil
}
func (this *RecordSeriesListResp) Validate() error {
	for _, item := range this.List {
		if item != nil {
			if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(item); err != nil {
				return github_com_mwitkow_go_proto_validators.FieldError("List", err)
			}
		}
	}
	return nil
}
func (this *RecordSeriesOrderRequest) Validate() error {
	return nil
}
func (this *QueryCustomerOrdersTotalRes) Validate() error {
	for _, item := range this.Info {
		if item != nil {
			if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(item); err != nil {
				return github_com_mwitkow_go_proto_validators.FieldError("Info", err)
			}
		}
	}
	return nil
}
func (this *QueryCustomerOrdersTotalReq) Validate() error {
	return nil
}
func (this *QueryCustomerOrdersTotalInfo) Validate() error {
	return nil
}
func (this *QueryStaffOrdersTotalInfo) Validate() error {
	return nil
}
func (this *QueryStaffOrdersTotalRes) Validate() error {
	for _, item := range this.Info {
		if item != nil {
			if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(item); err != nil {
				return github_com_mwitkow_go_proto_validators.FieldError("Info", err)
			}
		}
	}
	return nil
}
func (this *QueryStaffOrdersTotalReq) Validate() error {
	return nil
}
func (this *UpdateSeriesOrderShowStatusRequest) Validate() error {
	return nil
}
func (this *CommonRes) Validate() error {
	return nil
}
func (this *DetailAuctionArtworkLangReq) Validate() error {
	return nil
}
func (this *CommonReq) Validate() error {
	return nil
}
func (this *LiveUrlResponse) Validate() error {
	// Validation of proto3 map<> fields is unsupported.
	return nil
}
func (this *WsRtcLive) Validate() error {
	if this.Tip != nil {
		if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(this.Tip); err != nil {
			return github_com_mwitkow_go_proto_validators.FieldError("Tip", err)
		}
	}
	if this.Artwork != nil {
		if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(this.Artwork); err != nil {
			return github_com_mwitkow_go_proto_validators.FieldError("Artwork", err)
		}
	}
	if this.NowAuctionPrice != nil {
		if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(this.NowAuctionPrice); err != nil {
			return github_com_mwitkow_go_proto_validators.FieldError("NowAuctionPrice", err)
		}
	}
	if this.AuctionPriceList != nil {
		if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(this.AuctionPriceList); err != nil {
			return github_com_mwitkow_go_proto_validators.FieldError("AuctionPriceList", err)
		}
	}
	if this.Auction != nil {
		if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(this.Auction); err != nil {
			return github_com_mwitkow_go_proto_validators.FieldError("Auction", err)
		}
	}
	for _, item := range this.NeedPayBuys {
		if item != nil {
			if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(item); err != nil {
				return github_com_mwitkow_go_proto_validators.FieldError("NeedPayBuys", err)
			}
		}
	}
	return nil
}
func (this *BaseAuctionBuy) Validate() error {
	return nil
}
func (this *Artwork) Validate() error {
	if this.BuyInfo != nil {
		if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(this.BuyInfo); err != nil {
			return github_com_mwitkow_go_proto_validators.FieldError("BuyInfo", err)
		}
	}
	return nil
}
func (this *Tip) Validate() error {
	return nil
}
func (this *NowAuctionPrice) Validate() error {
	if this.GlobalPrices != nil {
		if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(this.GlobalPrices); err != nil {
			return github_com_mwitkow_go_proto_validators.FieldError("GlobalPrices", err)
		}
	}
	return nil
}
func (this *GlobalPrice) Validate() error {
	return nil
}
func (this *AuctionPriceList) Validate() error {
	for _, item := range this.Buys {
		if item != nil {
			if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(item); err != nil {
				return github_com_mwitkow_go_proto_validators.FieldError("Buys", err)
			}
		}
	}
	return nil
}
func (this *Buy) Validate() error {
	return nil
}
func (this *AuctionTypeAdminListResponse) Validate() error {
	for _, item := range this.Data {
		if item != nil {
			if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(item); err != nil {
				return github_com_mwitkow_go_proto_validators.FieldError("Data", err)
			}
		}
	}
	return nil
}
func (this *AuctionTypeAdminRequest) Validate() error {
	return nil
}
func (this *AuctionTypeAdminDetail) Validate() error {
	return nil
}
func (this *AuctionTypeAdminResponse) Validate() error {
	return nil
}
func (this *AuctionTypeAdminRemove) Validate() error {
	return nil
}
func (this *AuctionTypeAdminList) Validate() error {
	return nil
}
func (this *AuctionTypeUserListResponse) Validate() error {
	for _, item := range this.Data {
		if item != nil {
			if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(item); err != nil {
				return github_com_mwitkow_go_proto_validators.FieldError("Data", err)
			}
		}
	}
	return nil
}
func (this *AuctionTypeUserRequest) Validate() error {
	return nil
}
func (this *AuctionTypeUserDetail) Validate() error {
	return nil
}
func (this *AuctionTypeUserResponse) Validate() error {
	return nil
}
func (this *AuctionTypeUserRemove) Validate() error {
	return nil
}
func (this *AuctionTypeUserList) Validate() error {
	return nil
}
func (this *AuctionLang) Validate() error {
	return nil
}
func (this *UpdateAuctionEndNumRequest) Validate() error {
	return nil
}
func (this *AuctionRequest) Validate() error {
	for _, item := range this.AuctionArtwork {
		if item != nil {
			if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(item); err != nil {
				return github_com_mwitkow_go_proto_validators.FieldError("AuctionArtwork", err)
			}
		}
	}
	return nil
}
func (this *AuctionDetail) Validate() error {
	return nil
}
func (this *AuctionResponse) Validate() error {
	return nil
}
func (this *AuctionRemove) Validate() error {
	return nil
}
func (this *AuctionList) Validate() error {
	return nil
}
func (this *AuctionListResponse) Validate() error {
	for _, item := range this.Data {
		if item != nil {
			if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(item); err != nil {
				return github_com_mwitkow_go_proto_validators.FieldError("Data", err)
			}
		}
	}
	return nil
}
func (this *AuctionArtworkList) Validate() error {
	return nil
}
func (this *AuctionArtworkListResponse) Validate() error {
	for _, item := range this.Data {
		if item != nil {
			if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(item); err != nil {
				return github_com_mwitkow_go_proto_validators.FieldError("Data", err)
			}
		}
	}
	return nil
}
func (this *AuctionArtworkInfo) Validate() error {
	for _, item := range this.PriceRules {
		if item != nil {
			if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(item); err != nil {
				return github_com_mwitkow_go_proto_validators.FieldError("PriceRules", err)
			}
		}
	}
	if this.Artwork != nil {
		if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(this.Artwork); err != nil {
			return github_com_mwitkow_go_proto_validators.FieldError("Artwork", err)
		}
	}
	return nil
}
func (this *AuctionArtworkRequest) Validate() error {
	for _, item := range this.PriceRules {
		if item != nil {
			if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(item); err != nil {
				return github_com_mwitkow_go_proto_validators.FieldError("PriceRules", err)
			}
		}
	}
	return nil
}
func (this *PriceRule) Validate() error {
	return nil
}
func (this *AuctionArtworkDetail) Validate() error {
	return nil
}
func (this *AuctionArtworkResponse) Validate() error {
	return nil
}
func (this *AuctionArtworkRemove) Validate() error {
	return nil
}
func (this *AuctionBuyList) Validate() error {
	return nil
}
func (this *AuctionBuyListResponse) Validate() error {
	for _, item := range this.Data {
		if item != nil {
			if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(item); err != nil {
				return github_com_mwitkow_go_proto_validators.FieldError("Data", err)
			}
		}
	}
	if this.AuctionArtworkBase != nil {
		if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(this.AuctionArtworkBase); err != nil {
			return github_com_mwitkow_go_proto_validators.FieldError("AuctionArtworkBase", err)
		}
	}
	if this.NowAuctionPrice != nil {
		if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(this.NowAuctionPrice); err != nil {
			return github_com_mwitkow_go_proto_validators.FieldError("NowAuctionPrice", err)
		}
	}
	return nil
}
func (this *AuctionArtworkBase) Validate() error {
	return nil
}
func (this *AdminListAuctionBuyResponse) Validate() error {
	for _, item := range this.Data {
		if item != nil {
			if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(item); err != nil {
				return github_com_mwitkow_go_proto_validators.FieldError("Data", err)
			}
		}
	}
	return nil
}
func (this *FansAuctionBuyRequest) Validate() error {
	return nil
}
func (this *AuctionBuyRequest) Validate() error {
	if this.AuctionArtworkInfo != nil {
		if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(this.AuctionArtworkInfo); err != nil {
			return github_com_mwitkow_go_proto_validators.FieldError("AuctionArtworkInfo", err)
		}
	}
	if this.Artwork != nil {
		if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(this.Artwork); err != nil {
			return github_com_mwitkow_go_proto_validators.FieldError("Artwork", err)
		}
	}
	return nil
}
func (this *ArtworkBaseInfo) Validate() error {
	return nil
}
func (this *DetailSureAuctionBuyRequest) Validate() error {
	return nil
}
func (this *SureAuctionBuyRequest) Validate() error {
	return nil
}
func (this *AuctionBuyDetail) Validate() error {
	return nil
}
func (this *AuctionBuyResponse) Validate() error {
	return nil
}
func (this *AuctionBuyRemove) Validate() error {
	return nil
}
func (this *AuctionPayDetail) Validate() error {
	return nil
}
func (this *AuctionPayResponse) Validate() error {
	return nil
}
func (this *AuctionPayRemove) Validate() error {
	return nil
}
func (this *AuctionPayList) Validate() error {
	return nil
}
func (this *AuctionPayListResponse) Validate() error {
	for _, item := range this.Data {
		if item != nil {
			if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(item); err != nil {
				return github_com_mwitkow_go_proto_validators.FieldError("Data", err)
			}
		}
	}
	return nil
}
func (this *AuctionPayRequest) Validate() error {
	for _, item := range this.PprofList {
		if item != nil {
			if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(item); err != nil {
				return github_com_mwitkow_go_proto_validators.FieldError("PprofList", err)
			}
		}
	}
	return nil
}
func (this *ArtworkDetail) Validate() error {
	return nil
}
func (this *ArtworkResponse) Validate() error {
	return nil
}
func (this *ArtworkRemove) Validate() error {
	return nil
}
func (this *ArtworkList) Validate() error {
	return nil
}
func (this *ArtworkListResponse) Validate() error {
	for _, item := range this.Data {
		if item != nil {
			if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(item); err != nil {
				return github_com_mwitkow_go_proto_validators.FieldError("Data", err)
			}
		}
	}
	return nil
}
func (this *ArtworkLangRequest) Validate() error {
	return nil
}
func (this *ArtworkRequest) Validate() error {
	return nil
}
func (this *GetAuctionArtworkListRequest) Validate() error {
	if this.Query != nil {
		if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(this.Query); err != nil {
			return github_com_mwitkow_go_proto_validators.FieldError("Query", err)
		}
	}
	return nil
}
func (this *GetAuctionArtworkListResp) Validate() error {
	for _, item := range this.List {
		if item != nil {
			if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(item); err != nil {
				return github_com_mwitkow_go_proto_validators.FieldError("List", err)
			}
		}
	}
	return nil
}
func (this *GetAuctionByWithLotNumberRequest) Validate() error {
	return nil
}
func (this *GetAuctionByWithLotNumberResp) Validate() error {
	return nil
}
func (this *CommonMsg) Validate() error {
	return nil
}
func (this *OfflinePayData) Validate() error {
	return nil
}
func (this *OfflinePayListData) Validate() error {
	return nil
}
func (this *CreateOfflinePayResp) Validate() error {
	if this.Data != nil {
		if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(this.Data); err != nil {
			return github_com_mwitkow_go_proto_validators.FieldError("Data", err)
		}
	}
	return nil
}
func (this *DeleteOfflinePayRequest) Validate() error {
	return nil
}
func (this *GetOfflinePayByIdRequest) Validate() error {
	return nil
}
func (this *GetOfflinePayListRequest) Validate() error {
	if this.Query != nil {
		if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(this.Query); err != nil {
			return github_com_mwitkow_go_proto_validators.FieldError("Query", err)
		}
	}
	return nil
}
func (this *GetOfflinePayListResp) Validate() error {
	for _, item := range this.List {
		if item != nil {
			if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(item); err != nil {
				return github_com_mwitkow_go_proto_validators.FieldError("List", err)
			}
		}
	}
	return nil
}
func (this *AddOfflinePayPriceRequest) Validate() error {
	return nil
}
func (this *ViewOfflinePayData) Validate() error {
	return nil
}
func (this *GetViewOfflinePayListReq) Validate() error {
	if this.Query != nil {
		if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(this.Query); err != nil {
			return github_com_mwitkow_go_proto_validators.FieldError("Query", err)
		}
	}
	return nil
}
func (this *GetViewOfflinePayListResp) Validate() error {
	for _, item := range this.List {
		if item != nil {
			if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(item); err != nil {
				return github_com_mwitkow_go_proto_validators.FieldError("List", err)
			}
		}
	}
	return nil
}
func (this *AuctionSessionUserNoData) Validate() error {
	return nil
}
func (this *CreateAuctionSessionUserNoResp) Validate() error {
	if this.Data != nil {
		if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(this.Data); err != nil {
			return github_com_mwitkow_go_proto_validators.FieldError("Data", err)
		}
	}
	return nil
}
func (this *DeleteAuctionSessionUserNoRequest) Validate() error {
	return nil
}
func (this *GetAuctionSessionUserNoRequest) Validate() error {
	return nil
}
func (this *GetAuctionSessionUserNoListRequest) Validate() error {
	if this.Query != nil {
		if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(this.Query); err != nil {
			return github_com_mwitkow_go_proto_validators.FieldError("Query", err)
		}
	}
	return nil
}
func (this *GetAuctionSessionUserNoListResp) Validate() error {
	for _, item := range this.List {
		if item != nil {
			if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(item); err != nil {
				return github_com_mwitkow_go_proto_validators.FieldError("List", err)
			}
		}
	}
	return nil
}
func (this *ViewAuctionSessionUserNo) Validate() error {
	return nil
}
func (this *GetViewAuctionSessionUserNoListRequest) Validate() error {
	if this.Query != nil {
		if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(this.Query); err != nil {
			return github_com_mwitkow_go_proto_validators.FieldError("Query", err)
		}
	}
	return nil
}
func (this *GetViewAuctionSessionUserNoListResp) Validate() error {
	for _, item := range this.List {
		if item != nil {
			if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(item); err != nil {
				return github_com_mwitkow_go_proto_validators.FieldError("List", err)
			}
		}
	}
	return nil
}
func (this *SeriesArtworkData) Validate() error {
	for _, item := range this.Lang {
		if item != nil {
			if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(item); err != nil {
				return github_com_mwitkow_go_proto_validators.FieldError("Lang", err)
			}
		}
	}
	return nil
}
func (this *SeriesArtworkLangData) Validate() error {
	return nil
}
func (this *CreateSeriesArtworkResp) Validate() error {
	if this.Data != nil {
		if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(this.Data); err != nil {
			return github_com_mwitkow_go_proto_validators.FieldError("Data", err)
		}
	}
	return nil
}
func (this *DeleteSeriesArtworkRequest) Validate() error {
	return nil
}
func (this *GetSeriesArtworkByIdRequest) Validate() error {
	return nil
}
func (this *GetSeriesArtworkListRequest) Validate() error {
	if this.Query != nil {
		if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(this.Query); err != nil {
			return github_com_mwitkow_go_proto_validators.FieldError("Query", err)
		}
	}
	return nil
}
func (this *GetSeriesArtworkListResp) Validate() error {
	for _, item := range this.List {
		if item != nil {
			if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(item); err != nil {
				return github_com_mwitkow_go_proto_validators.FieldError("List", err)
			}
		}
	}
	return nil
}
func (this *CreateSeriesArtworkLangResp) Validate() error {
	if this.Data != nil {
		if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(this.Data); err != nil {
			return github_com_mwitkow_go_proto_validators.FieldError("Data", err)
		}
	}
	return nil
}
func (this *DeleteSeriesArtworkLangRequest) Validate() error {
	return nil
}
func (this *GetSeriesArtworkLangByIdRequest) Validate() error {
	return nil
}
func (this *GetSeriesArtworkLangListRequest) Validate() error {
	if this.Query != nil {
		if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(this.Query); err != nil {
			return github_com_mwitkow_go_proto_validators.FieldError("Query", err)
		}
	}
	return nil
}
func (this *GetSeriesArtworkLangListResp) Validate() error {
	for _, item := range this.List {
		if item != nil {
			if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(item); err != nil {
				return github_com_mwitkow_go_proto_validators.FieldError("List", err)
			}
		}
	}
	return nil
}
func (this *SeriesProfileData) Validate() error {
	return nil
}
func (this *GetSeriesProfileListRequest) Validate() error {
	return nil
}
func (this *GetSeriesProfileListResp) Validate() error {
	for _, item := range this.List {
		if item != nil {
			if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(item); err != nil {
				return github_com_mwitkow_go_proto_validators.FieldError("List", err)
			}
		}
	}
	return nil
}
func (this *GetSeriesDetailRequest) Validate() error {
	return nil
}
func (this *GetSeriesDetailResp) Validate() error {
	return nil
}
func (this *SeriesOrderData) Validate() error {
	return nil
}
func (this *CreateSeriesOrderResp) Validate() error {
	if this.Data != nil {
		if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(this.Data); err != nil {
			return github_com_mwitkow_go_proto_validators.FieldError("Data", err)
		}
	}
	return nil
}
func (this *DeleteSeriesOrderRequest) Validate() error {
	return nil
}
func (this *GetSeriesOrderByIdRequest) Validate() error {
	return nil
}
func (this *GetSeriesOrderListRequest) Validate() error {
	if this.Query != nil {
		if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(this.Query); err != nil {
			return github_com_mwitkow_go_proto_validators.FieldError("Query", err)
		}
	}
	return nil
}
func (this *GetSeriesOrderListResp) Validate() error {
	for _, item := range this.List {
		if item != nil {
			if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(item); err != nil {
				return github_com_mwitkow_go_proto_validators.FieldError("List", err)
			}
		}
	}
	return nil
}
func (this *UpdateTradePaymentReq) Validate() error {
	return nil
}
func (this *UpdateTradePaymentResp) Validate() error {
	return nil
}
func (this *SeriesPaymentPprofData) Validate() error {
	return nil
}
func (this *CreateSeriesPaymentPprofResp) Validate() error {
	if this.Data != nil {
		if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(this.Data); err != nil {
			return github_com_mwitkow_go_proto_validators.FieldError("Data", err)
		}
	}
	return nil
}
func (this *DeleteSeriesPaymentPprofRequest) Validate() error {
	return nil
}
func (this *GetSeriesPaymentPprofByIdRequest) Validate() error {
	return nil
}
func (this *GetSeriesPaymentPprofListRequest) Validate() error {
	if this.Query != nil {
		if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(this.Query); err != nil {
			return github_com_mwitkow_go_proto_validators.FieldError("Query", err)
		}
	}
	return nil
}
func (this *GetSeriesPaymentPprofListResp) Validate() error {
	for _, item := range this.List {
		if item != nil {
			if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(item); err != nil {
				return github_com_mwitkow_go_proto_validators.FieldError("List", err)
			}
		}
	}
	return nil
}
func (this *CreateSeriesPaymentPprofReq) Validate() error {
	return nil
}
func (this *ExportOrderTypeData) Validate() error {
	return nil
}
func (this *GetExportOrderTypeListRequest) Validate() error {
	return nil
}
func (this *GetExportOrderTypeListResp) Validate() error {
	for _, item := range this.List {
		if item != nil {
			if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(item); err != nil {
				return github_com_mwitkow_go_proto_validators.FieldError("List", err)
			}
		}
	}
	return nil
}
func (this *TransferOrderRecordData) Validate() error {
	return nil
}
func (this *CreateTransferOrderRecordResp) Validate() error {
	if this.Data != nil {
		if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(this.Data); err != nil {
			return github_com_mwitkow_go_proto_validators.FieldError("Data", err)
		}
	}
	return nil
}
func (this *DeleteTransferOrderRecordRequest) Validate() error {
	return nil
}
func (this *GetTransferOrderRecordByIdRequest) Validate() error {
	return nil
}
func (this *GetTransferOrderRecordListRequest) Validate() error {
	if this.Query != nil {
		if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(this.Query); err != nil {
			return github_com_mwitkow_go_proto_validators.FieldError("Query", err)
		}
	}
	return nil
}
func (this *GetTransferOrderRecordListResp) Validate() error {
	for _, item := range this.List {
		if item != nil {
			if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(item); err != nil {
				return github_com_mwitkow_go_proto_validators.FieldError("List", err)
			}
		}
	}
	return nil
}
func (this *UserData) Validate() error {
	return nil
}
func (this *CreateUserResp) Validate() error {
	if this.Data != nil {
		if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(this.Data); err != nil {
			return github_com_mwitkow_go_proto_validators.FieldError("Data", err)
		}
	}
	return nil
}
func (this *DeleteUserRequest) Validate() error {
	return nil
}
func (this *GetUserByIdRequest) Validate() error {
	return nil
}
func (this *GetUserListRequest) Validate() error {
	if this.Query != nil {
		if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(this.Query); err != nil {
			return github_com_mwitkow_go_proto_validators.FieldError("Query", err)
		}
	}
	return nil
}
func (this *GetUserListResp) Validate() error {
	for _, item := range this.List {
		if item != nil {
			if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(item); err != nil {
				return github_com_mwitkow_go_proto_validators.FieldError("List", err)
			}
		}
	}
	return nil
}
func (this *CreateSeriesPaymentPprofReqV2) Validate() error {
	for _, item := range this.PprofUrls {
		if item != nil {
			if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(item); err != nil {
				return github_com_mwitkow_go_proto_validators.FieldError("PprofUrls", err)
			}
		}
	}
	return nil
}
func (this *CreateSeriesPaymentPprofReqV2_Pprof) Validate() error {
	return nil
}
func (this *UserContractData) Validate() error {
	return nil
}
func (this *CreateUserContractResp) Validate() error {
	if this.Data != nil {
		if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(this.Data); err != nil {
			return github_com_mwitkow_go_proto_validators.FieldError("Data", err)
		}
	}
	return nil
}
func (this *DeleteUserContractRequest) Validate() error {
	return nil
}
func (this *GetUserContractByIdRequest) Validate() error {
	return nil
}
func (this *GetUserContractListRequest) Validate() error {
	if this.Query != nil {
		if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(this.Query); err != nil {
			return github_com_mwitkow_go_proto_validators.FieldError("Query", err)
		}
	}
	return nil
}
func (this *GetUserContractListResp) Validate() error {
	for _, item := range this.List {
		if item != nil {
			if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(item); err != nil {
				return github_com_mwitkow_go_proto_validators.FieldError("List", err)
			}
		}
	}
	return nil
}
func (this *CreateCultureSeriesOrderRequest) Validate() error {
	for _, item := range this.CommodityInfo {
		if item != nil {
			if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(item); err != nil {
				return github_com_mwitkow_go_proto_validators.FieldError("CommodityInfo", err)
			}
		}
	}
	return nil
}
func (this *CreateCultureSeriesOrderRequest_CommodityInfo) Validate() error {
	return nil
}
