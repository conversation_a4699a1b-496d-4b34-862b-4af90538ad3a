// Code generated by protoc-gen-go-triple. DO NOT EDIT.
// versions:
// - protoc-gen-go-triple v1.0.8
// - protoc             v6.30.0--rc2
// source: api/photoWall/photoWall.proto

package photoWall

import (
	context "context"
	protocol "dubbo.apache.org/dubbo-go/v3/protocol"
	dubbo3 "dubbo.apache.org/dubbo-go/v3/protocol/dubbo3"
	invocation "dubbo.apache.org/dubbo-go/v3/protocol/invocation"
	grpc_go "github.com/dubbogo/grpc-go"
	codes "github.com/dubbogo/grpc-go/codes"
	metadata "github.com/dubbogo/grpc-go/metadata"
	status "github.com/dubbogo/grpc-go/status"
	common "github.com/dubbogo/triple/pkg/common"
	constant "github.com/dubbogo/triple/pkg/common/constant"
	triple "github.com/dubbogo/triple/pkg/triple"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc_go.SupportPackageIsVersion7

// PhotoWallProviderClient is the client API for PhotoWallProvider service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type PhotoWallProviderClient interface {
	SavePhoto(ctx context.Context, in *SavePhotoReq, opts ...grpc_go.CallOption) (*CommonPhotoResp, common.ErrorWithAttachment)
	UpdatePhoto(ctx context.Context, in *UpdatePhotoReq, opts ...grpc_go.CallOption) (*CommonPhotoResp, common.ErrorWithAttachment)
	QueryPhoto(ctx context.Context, in *QueryPhotoSingleReq, opts ...grpc_go.CallOption) (*QueryPhotoSingleResp, common.ErrorWithAttachment)
	QueryPhotoList(ctx context.Context, in *QueryPhotoListReq, opts ...grpc_go.CallOption) (*QueryPhotoListResp, common.ErrorWithAttachment)
}

type photoWallProviderClient struct {
	cc *triple.TripleConn
}

type PhotoWallProviderClientImpl struct {
	SavePhoto      func(ctx context.Context, in *SavePhotoReq) (*CommonPhotoResp, error)
	UpdatePhoto    func(ctx context.Context, in *UpdatePhotoReq) (*CommonPhotoResp, error)
	QueryPhoto     func(ctx context.Context, in *QueryPhotoSingleReq) (*QueryPhotoSingleResp, error)
	QueryPhotoList func(ctx context.Context, in *QueryPhotoListReq) (*QueryPhotoListResp, error)
}

func (c *PhotoWallProviderClientImpl) GetDubboStub(cc *triple.TripleConn) PhotoWallProviderClient {
	return NewPhotoWallProviderClient(cc)
}

func (c *PhotoWallProviderClientImpl) XXX_InterfaceName() string {
	return "photoWall.PhotoWallProvider"
}

func NewPhotoWallProviderClient(cc *triple.TripleConn) PhotoWallProviderClient {
	return &photoWallProviderClient{cc}
}

func (c *photoWallProviderClient) SavePhoto(ctx context.Context, in *SavePhotoReq, opts ...grpc_go.CallOption) (*CommonPhotoResp, common.ErrorWithAttachment) {
	out := new(CommonPhotoResp)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/SavePhoto", in, out)
}

func (c *photoWallProviderClient) UpdatePhoto(ctx context.Context, in *UpdatePhotoReq, opts ...grpc_go.CallOption) (*CommonPhotoResp, common.ErrorWithAttachment) {
	out := new(CommonPhotoResp)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/UpdatePhoto", in, out)
}

func (c *photoWallProviderClient) QueryPhoto(ctx context.Context, in *QueryPhotoSingleReq, opts ...grpc_go.CallOption) (*QueryPhotoSingleResp, common.ErrorWithAttachment) {
	out := new(QueryPhotoSingleResp)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/QueryPhoto", in, out)
}

func (c *photoWallProviderClient) QueryPhotoList(ctx context.Context, in *QueryPhotoListReq, opts ...grpc_go.CallOption) (*QueryPhotoListResp, common.ErrorWithAttachment) {
	out := new(QueryPhotoListResp)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/QueryPhotoList", in, out)
}

// PhotoWallProviderServer is the server API for PhotoWallProvider service.
// All implementations must embed UnimplementedPhotoWallProviderServer
// for forward compatibility
type PhotoWallProviderServer interface {
	SavePhoto(context.Context, *SavePhotoReq) (*CommonPhotoResp, error)
	UpdatePhoto(context.Context, *UpdatePhotoReq) (*CommonPhotoResp, error)
	QueryPhoto(context.Context, *QueryPhotoSingleReq) (*QueryPhotoSingleResp, error)
	QueryPhotoList(context.Context, *QueryPhotoListReq) (*QueryPhotoListResp, error)
	mustEmbedUnimplementedPhotoWallProviderServer()
}

// UnimplementedPhotoWallProviderServer must be embedded to have forward compatible implementations.
type UnimplementedPhotoWallProviderServer struct {
	proxyImpl protocol.Invoker
}

func (UnimplementedPhotoWallProviderServer) SavePhoto(context.Context, *SavePhotoReq) (*CommonPhotoResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SavePhoto not implemented")
}
func (UnimplementedPhotoWallProviderServer) UpdatePhoto(context.Context, *UpdatePhotoReq) (*CommonPhotoResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdatePhoto not implemented")
}
func (UnimplementedPhotoWallProviderServer) QueryPhoto(context.Context, *QueryPhotoSingleReq) (*QueryPhotoSingleResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method QueryPhoto not implemented")
}
func (UnimplementedPhotoWallProviderServer) QueryPhotoList(context.Context, *QueryPhotoListReq) (*QueryPhotoListResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method QueryPhotoList not implemented")
}
func (s *UnimplementedPhotoWallProviderServer) XXX_SetProxyImpl(impl protocol.Invoker) {
	s.proxyImpl = impl
}

func (s *UnimplementedPhotoWallProviderServer) XXX_GetProxyImpl() protocol.Invoker {
	return s.proxyImpl
}

func (s *UnimplementedPhotoWallProviderServer) XXX_ServiceDesc() *grpc_go.ServiceDesc {
	return &PhotoWallProvider_ServiceDesc
}
func (s *UnimplementedPhotoWallProviderServer) XXX_InterfaceName() string {
	return "photoWall.PhotoWallProvider"
}

func (UnimplementedPhotoWallProviderServer) mustEmbedUnimplementedPhotoWallProviderServer() {}

// UnsafePhotoWallProviderServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to PhotoWallProviderServer will
// result in compilation errors.
type UnsafePhotoWallProviderServer interface {
	mustEmbedUnimplementedPhotoWallProviderServer()
}

func RegisterPhotoWallProviderServer(s grpc_go.ServiceRegistrar, srv PhotoWallProviderServer) {
	s.RegisterService(&PhotoWallProvider_ServiceDesc, srv)
}

func _PhotoWallProvider_SavePhoto_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(SavePhotoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("SavePhoto", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _PhotoWallProvider_UpdatePhoto_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdatePhotoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("UpdatePhoto", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _PhotoWallProvider_QueryPhoto_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(QueryPhotoSingleReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("QueryPhoto", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _PhotoWallProvider_QueryPhotoList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(QueryPhotoListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("QueryPhotoList", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

// PhotoWallProvider_ServiceDesc is the grpc_go.ServiceDesc for PhotoWallProvider service.
// It's only intended for direct use with grpc_go.RegisterService,
// and not to be introspected or modified (even as a copy)
var PhotoWallProvider_ServiceDesc = grpc_go.ServiceDesc{
	ServiceName: "photoWall.PhotoWallProvider",
	HandlerType: (*PhotoWallProviderServer)(nil),
	Methods: []grpc_go.MethodDesc{
		{
			MethodName: "SavePhoto",
			Handler:    _PhotoWallProvider_SavePhoto_Handler,
		},
		{
			MethodName: "UpdatePhoto",
			Handler:    _PhotoWallProvider_UpdatePhoto_Handler,
		},
		{
			MethodName: "QueryPhoto",
			Handler:    _PhotoWallProvider_QueryPhoto_Handler,
		},
		{
			MethodName: "QueryPhotoList",
			Handler:    _PhotoWallProvider_QueryPhotoList_Handler,
		},
	},
	Streams:  []grpc_go.StreamDesc{},
	Metadata: "api/photoWall/photoWall.proto",
}
