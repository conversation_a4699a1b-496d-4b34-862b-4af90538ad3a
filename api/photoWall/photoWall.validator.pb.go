// Code generated by protoc-gen-gogo. DO NOT EDIT.
// source: api/photoWall/photoWall.proto

package photoWall

import (
	fmt "fmt"
	math "math"
	proto "github.com/golang/protobuf/proto"
	github_com_mwitkow_go_proto_validators "github.com/mwitkow/go-proto-validators"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

func (this *SavePhotoReq) Validate() error {
	return nil
}
func (this *CommonPhotoResp) Validate() error {
	return nil
}
func (this *UpdatePhotoReq) Validate() error {
	return nil
}
func (this *QueryPhotoSingleReq) Validate() error {
	return nil
}
func (this *QueryPhotoSingleResp) Validate() error {
	if this.Data != nil {
		if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(this.Data); err != nil {
			return github_com_mwitkow_go_proto_validators.FieldError("Data", err)
		}
	}
	return nil
}
func (this *QueryPhotoListReq) Validate() error {
	return nil
}
func (this *QueryPhotoListResp) Validate() error {
	for _, item := range this.Data {
		if item != nil {
			if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(item); err != nil {
				return github_com_mwitkow_go_proto_validators.FieldError("Data", err)
			}
		}
	}
	return nil
}
func (this *Photo) Validate() error {
	return nil
}
