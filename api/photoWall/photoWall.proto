syntax = "proto3";
package photoWall;

option go_package = "./;photoWall";

service PhotoWallProvider {
  rpc  SavePhoto (SavePhotoReq) returns (CommonPhotoResp) {}
  rpc  UpdatePhoto (UpdatePhotoReq) returns (CommonPhotoResp) {}
  rpc  QueryPhoto (QueryPhotoSingleReq) returns (QueryPhotoSingleResp) {}
  rpc  QueryPhotoList (QueryPhotoListReq) returns (QueryPhotoListResp) {}
}

message SavePhotoReq{
  string photoUrl = 1;
  string blockHash = 2;
  string transactionHash = 3;
  int64  blockHeight = 4;
  string windUpTime = 5;
  string certNum = 6;
  string certUrl = 7;
}

message CommonPhotoResp{
  string uuid = 1 ;
}

message UpdatePhotoReq{
  string uuid = 1;
  string photoUrl = 2;
  string blockHash = 3;
  string transactionHash = 4;
  int64  blockHeight = 5;
  string windUpTime = 6;
  string certNum = 7;
  string certUrl = 8;
}

message QueryPhotoSingleReq{
  string uuid = 1;
  string blockHash = 2;
  string transactionHash = 3;
  int64  blockHeight = 4;
  string windUpTime = 5;
  string photoUrl = 6;
}

message QueryPhotoSingleResp{
  Photo data = 1;
}

message QueryPhotoListReq{
  int64 page = 1;
  int64 pageSize = 2;
  string blockHash = 3;
  string transactionHash = 4;
  int64  blockHeight = 5;
  string windUpTime = 6;
  string photoUrl = 7;
}

message QueryPhotoListResp{
  repeated Photo data = 1;
  int64 page = 2;
  int64 pageSize = 3;
  int64 Total = 4;
}

message Photo{
  string uuid = 1;
  string photoUrl = 2;
  string blockHash = 3;
  string transactionHash = 4;
  int64  blockHeight = 5;
  string windUpTime = 6;
  string certNum = 7;
  string certUrl = 8;
}
