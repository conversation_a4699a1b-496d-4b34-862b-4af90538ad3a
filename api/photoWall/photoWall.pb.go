// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v6.30.0--rc2
// source: api/photoWall/photoWall.proto

package photoWall

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type SavePhotoReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PhotoUrl        string `protobuf:"bytes,1,opt,name=photoUrl,proto3" json:"photoUrl,omitempty"`
	BlockHash       string `protobuf:"bytes,2,opt,name=blockHash,proto3" json:"blockHash,omitempty"`
	TransactionHash string `protobuf:"bytes,3,opt,name=transactionHash,proto3" json:"transactionHash,omitempty"`
	BlockHeight     int64  `protobuf:"varint,4,opt,name=blockHeight,proto3" json:"blockHeight,omitempty"`
	WindUpTime      string `protobuf:"bytes,5,opt,name=windUpTime,proto3" json:"windUpTime,omitempty"`
	CertNum         string `protobuf:"bytes,6,opt,name=certNum,proto3" json:"certNum,omitempty"`
	CertUrl         string `protobuf:"bytes,7,opt,name=certUrl,proto3" json:"certUrl,omitempty"`
}

func (x *SavePhotoReq) Reset() {
	*x = SavePhotoReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_photoWall_photoWall_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SavePhotoReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SavePhotoReq) ProtoMessage() {}

func (x *SavePhotoReq) ProtoReflect() protoreflect.Message {
	mi := &file_api_photoWall_photoWall_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SavePhotoReq.ProtoReflect.Descriptor instead.
func (*SavePhotoReq) Descriptor() ([]byte, []int) {
	return file_api_photoWall_photoWall_proto_rawDescGZIP(), []int{0}
}

func (x *SavePhotoReq) GetPhotoUrl() string {
	if x != nil {
		return x.PhotoUrl
	}
	return ""
}

func (x *SavePhotoReq) GetBlockHash() string {
	if x != nil {
		return x.BlockHash
	}
	return ""
}

func (x *SavePhotoReq) GetTransactionHash() string {
	if x != nil {
		return x.TransactionHash
	}
	return ""
}

func (x *SavePhotoReq) GetBlockHeight() int64 {
	if x != nil {
		return x.BlockHeight
	}
	return 0
}

func (x *SavePhotoReq) GetWindUpTime() string {
	if x != nil {
		return x.WindUpTime
	}
	return ""
}

func (x *SavePhotoReq) GetCertNum() string {
	if x != nil {
		return x.CertNum
	}
	return ""
}

func (x *SavePhotoReq) GetCertUrl() string {
	if x != nil {
		return x.CertUrl
	}
	return ""
}

type CommonPhotoResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Uuid string `protobuf:"bytes,1,opt,name=uuid,proto3" json:"uuid,omitempty"`
}

func (x *CommonPhotoResp) Reset() {
	*x = CommonPhotoResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_photoWall_photoWall_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CommonPhotoResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CommonPhotoResp) ProtoMessage() {}

func (x *CommonPhotoResp) ProtoReflect() protoreflect.Message {
	mi := &file_api_photoWall_photoWall_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CommonPhotoResp.ProtoReflect.Descriptor instead.
func (*CommonPhotoResp) Descriptor() ([]byte, []int) {
	return file_api_photoWall_photoWall_proto_rawDescGZIP(), []int{1}
}

func (x *CommonPhotoResp) GetUuid() string {
	if x != nil {
		return x.Uuid
	}
	return ""
}

type UpdatePhotoReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Uuid            string `protobuf:"bytes,1,opt,name=uuid,proto3" json:"uuid,omitempty"`
	PhotoUrl        string `protobuf:"bytes,2,opt,name=photoUrl,proto3" json:"photoUrl,omitempty"`
	BlockHash       string `protobuf:"bytes,3,opt,name=blockHash,proto3" json:"blockHash,omitempty"`
	TransactionHash string `protobuf:"bytes,4,opt,name=transactionHash,proto3" json:"transactionHash,omitempty"`
	BlockHeight     int64  `protobuf:"varint,5,opt,name=blockHeight,proto3" json:"blockHeight,omitempty"`
	WindUpTime      string `protobuf:"bytes,6,opt,name=windUpTime,proto3" json:"windUpTime,omitempty"`
	CertNum         string `protobuf:"bytes,7,opt,name=certNum,proto3" json:"certNum,omitempty"`
	CertUrl         string `protobuf:"bytes,8,opt,name=certUrl,proto3" json:"certUrl,omitempty"`
}

func (x *UpdatePhotoReq) Reset() {
	*x = UpdatePhotoReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_photoWall_photoWall_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdatePhotoReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdatePhotoReq) ProtoMessage() {}

func (x *UpdatePhotoReq) ProtoReflect() protoreflect.Message {
	mi := &file_api_photoWall_photoWall_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdatePhotoReq.ProtoReflect.Descriptor instead.
func (*UpdatePhotoReq) Descriptor() ([]byte, []int) {
	return file_api_photoWall_photoWall_proto_rawDescGZIP(), []int{2}
}

func (x *UpdatePhotoReq) GetUuid() string {
	if x != nil {
		return x.Uuid
	}
	return ""
}

func (x *UpdatePhotoReq) GetPhotoUrl() string {
	if x != nil {
		return x.PhotoUrl
	}
	return ""
}

func (x *UpdatePhotoReq) GetBlockHash() string {
	if x != nil {
		return x.BlockHash
	}
	return ""
}

func (x *UpdatePhotoReq) GetTransactionHash() string {
	if x != nil {
		return x.TransactionHash
	}
	return ""
}

func (x *UpdatePhotoReq) GetBlockHeight() int64 {
	if x != nil {
		return x.BlockHeight
	}
	return 0
}

func (x *UpdatePhotoReq) GetWindUpTime() string {
	if x != nil {
		return x.WindUpTime
	}
	return ""
}

func (x *UpdatePhotoReq) GetCertNum() string {
	if x != nil {
		return x.CertNum
	}
	return ""
}

func (x *UpdatePhotoReq) GetCertUrl() string {
	if x != nil {
		return x.CertUrl
	}
	return ""
}

type QueryPhotoSingleReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Uuid            string `protobuf:"bytes,1,opt,name=uuid,proto3" json:"uuid,omitempty"`
	BlockHash       string `protobuf:"bytes,2,opt,name=blockHash,proto3" json:"blockHash,omitempty"`
	TransactionHash string `protobuf:"bytes,3,opt,name=transactionHash,proto3" json:"transactionHash,omitempty"`
	BlockHeight     int64  `protobuf:"varint,4,opt,name=blockHeight,proto3" json:"blockHeight,omitempty"`
	WindUpTime      string `protobuf:"bytes,5,opt,name=windUpTime,proto3" json:"windUpTime,omitempty"`
	PhotoUrl        string `protobuf:"bytes,6,opt,name=photoUrl,proto3" json:"photoUrl,omitempty"`
}

func (x *QueryPhotoSingleReq) Reset() {
	*x = QueryPhotoSingleReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_photoWall_photoWall_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *QueryPhotoSingleReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QueryPhotoSingleReq) ProtoMessage() {}

func (x *QueryPhotoSingleReq) ProtoReflect() protoreflect.Message {
	mi := &file_api_photoWall_photoWall_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QueryPhotoSingleReq.ProtoReflect.Descriptor instead.
func (*QueryPhotoSingleReq) Descriptor() ([]byte, []int) {
	return file_api_photoWall_photoWall_proto_rawDescGZIP(), []int{3}
}

func (x *QueryPhotoSingleReq) GetUuid() string {
	if x != nil {
		return x.Uuid
	}
	return ""
}

func (x *QueryPhotoSingleReq) GetBlockHash() string {
	if x != nil {
		return x.BlockHash
	}
	return ""
}

func (x *QueryPhotoSingleReq) GetTransactionHash() string {
	if x != nil {
		return x.TransactionHash
	}
	return ""
}

func (x *QueryPhotoSingleReq) GetBlockHeight() int64 {
	if x != nil {
		return x.BlockHeight
	}
	return 0
}

func (x *QueryPhotoSingleReq) GetWindUpTime() string {
	if x != nil {
		return x.WindUpTime
	}
	return ""
}

func (x *QueryPhotoSingleReq) GetPhotoUrl() string {
	if x != nil {
		return x.PhotoUrl
	}
	return ""
}

type QueryPhotoSingleResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Data *Photo `protobuf:"bytes,1,opt,name=data,proto3" json:"data,omitempty"`
}

func (x *QueryPhotoSingleResp) Reset() {
	*x = QueryPhotoSingleResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_photoWall_photoWall_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *QueryPhotoSingleResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QueryPhotoSingleResp) ProtoMessage() {}

func (x *QueryPhotoSingleResp) ProtoReflect() protoreflect.Message {
	mi := &file_api_photoWall_photoWall_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QueryPhotoSingleResp.ProtoReflect.Descriptor instead.
func (*QueryPhotoSingleResp) Descriptor() ([]byte, []int) {
	return file_api_photoWall_photoWall_proto_rawDescGZIP(), []int{4}
}

func (x *QueryPhotoSingleResp) GetData() *Photo {
	if x != nil {
		return x.Data
	}
	return nil
}

type QueryPhotoListReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Page            int64  `protobuf:"varint,1,opt,name=page,proto3" json:"page,omitempty"`
	PageSize        int64  `protobuf:"varint,2,opt,name=pageSize,proto3" json:"pageSize,omitempty"`
	BlockHash       string `protobuf:"bytes,3,opt,name=blockHash,proto3" json:"blockHash,omitempty"`
	TransactionHash string `protobuf:"bytes,4,opt,name=transactionHash,proto3" json:"transactionHash,omitempty"`
	BlockHeight     int64  `protobuf:"varint,5,opt,name=blockHeight,proto3" json:"blockHeight,omitempty"`
	WindUpTime      string `protobuf:"bytes,6,opt,name=windUpTime,proto3" json:"windUpTime,omitempty"`
	PhotoUrl        string `protobuf:"bytes,7,opt,name=photoUrl,proto3" json:"photoUrl,omitempty"`
}

func (x *QueryPhotoListReq) Reset() {
	*x = QueryPhotoListReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_photoWall_photoWall_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *QueryPhotoListReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QueryPhotoListReq) ProtoMessage() {}

func (x *QueryPhotoListReq) ProtoReflect() protoreflect.Message {
	mi := &file_api_photoWall_photoWall_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QueryPhotoListReq.ProtoReflect.Descriptor instead.
func (*QueryPhotoListReq) Descriptor() ([]byte, []int) {
	return file_api_photoWall_photoWall_proto_rawDescGZIP(), []int{5}
}

func (x *QueryPhotoListReq) GetPage() int64 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *QueryPhotoListReq) GetPageSize() int64 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *QueryPhotoListReq) GetBlockHash() string {
	if x != nil {
		return x.BlockHash
	}
	return ""
}

func (x *QueryPhotoListReq) GetTransactionHash() string {
	if x != nil {
		return x.TransactionHash
	}
	return ""
}

func (x *QueryPhotoListReq) GetBlockHeight() int64 {
	if x != nil {
		return x.BlockHeight
	}
	return 0
}

func (x *QueryPhotoListReq) GetWindUpTime() string {
	if x != nil {
		return x.WindUpTime
	}
	return ""
}

func (x *QueryPhotoListReq) GetPhotoUrl() string {
	if x != nil {
		return x.PhotoUrl
	}
	return ""
}

type QueryPhotoListResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Data     []*Photo `protobuf:"bytes,1,rep,name=data,proto3" json:"data,omitempty"`
	Page     int64    `protobuf:"varint,2,opt,name=page,proto3" json:"page,omitempty"`
	PageSize int64    `protobuf:"varint,3,opt,name=pageSize,proto3" json:"pageSize,omitempty"`
	Total    int64    `protobuf:"varint,4,opt,name=Total,proto3" json:"Total,omitempty"`
}

func (x *QueryPhotoListResp) Reset() {
	*x = QueryPhotoListResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_photoWall_photoWall_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *QueryPhotoListResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QueryPhotoListResp) ProtoMessage() {}

func (x *QueryPhotoListResp) ProtoReflect() protoreflect.Message {
	mi := &file_api_photoWall_photoWall_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QueryPhotoListResp.ProtoReflect.Descriptor instead.
func (*QueryPhotoListResp) Descriptor() ([]byte, []int) {
	return file_api_photoWall_photoWall_proto_rawDescGZIP(), []int{6}
}

func (x *QueryPhotoListResp) GetData() []*Photo {
	if x != nil {
		return x.Data
	}
	return nil
}

func (x *QueryPhotoListResp) GetPage() int64 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *QueryPhotoListResp) GetPageSize() int64 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *QueryPhotoListResp) GetTotal() int64 {
	if x != nil {
		return x.Total
	}
	return 0
}

type Photo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Uuid            string `protobuf:"bytes,1,opt,name=uuid,proto3" json:"uuid,omitempty"`
	PhotoUrl        string `protobuf:"bytes,2,opt,name=photoUrl,proto3" json:"photoUrl,omitempty"`
	BlockHash       string `protobuf:"bytes,3,opt,name=blockHash,proto3" json:"blockHash,omitempty"`
	TransactionHash string `protobuf:"bytes,4,opt,name=transactionHash,proto3" json:"transactionHash,omitempty"`
	BlockHeight     int64  `protobuf:"varint,5,opt,name=blockHeight,proto3" json:"blockHeight,omitempty"`
	WindUpTime      string `protobuf:"bytes,6,opt,name=windUpTime,proto3" json:"windUpTime,omitempty"`
	CertNum         string `protobuf:"bytes,7,opt,name=certNum,proto3" json:"certNum,omitempty"`
	CertUrl         string `protobuf:"bytes,8,opt,name=certUrl,proto3" json:"certUrl,omitempty"`
}

func (x *Photo) Reset() {
	*x = Photo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_photoWall_photoWall_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Photo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Photo) ProtoMessage() {}

func (x *Photo) ProtoReflect() protoreflect.Message {
	mi := &file_api_photoWall_photoWall_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Photo.ProtoReflect.Descriptor instead.
func (*Photo) Descriptor() ([]byte, []int) {
	return file_api_photoWall_photoWall_proto_rawDescGZIP(), []int{7}
}

func (x *Photo) GetUuid() string {
	if x != nil {
		return x.Uuid
	}
	return ""
}

func (x *Photo) GetPhotoUrl() string {
	if x != nil {
		return x.PhotoUrl
	}
	return ""
}

func (x *Photo) GetBlockHash() string {
	if x != nil {
		return x.BlockHash
	}
	return ""
}

func (x *Photo) GetTransactionHash() string {
	if x != nil {
		return x.TransactionHash
	}
	return ""
}

func (x *Photo) GetBlockHeight() int64 {
	if x != nil {
		return x.BlockHeight
	}
	return 0
}

func (x *Photo) GetWindUpTime() string {
	if x != nil {
		return x.WindUpTime
	}
	return ""
}

func (x *Photo) GetCertNum() string {
	if x != nil {
		return x.CertNum
	}
	return ""
}

func (x *Photo) GetCertUrl() string {
	if x != nil {
		return x.CertUrl
	}
	return ""
}

var File_api_photoWall_photoWall_proto protoreflect.FileDescriptor

var file_api_photoWall_photoWall_proto_rawDesc = []byte{
	0x0a, 0x1d, 0x61, 0x70, 0x69, 0x2f, 0x70, 0x68, 0x6f, 0x74, 0x6f, 0x57, 0x61, 0x6c, 0x6c, 0x2f,
	0x70, 0x68, 0x6f, 0x74, 0x6f, 0x57, 0x61, 0x6c, 0x6c, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12,
	0x09, 0x70, 0x68, 0x6f, 0x74, 0x6f, 0x57, 0x61, 0x6c, 0x6c, 0x22, 0xe8, 0x01, 0x0a, 0x0c, 0x53,
	0x61, 0x76, 0x65, 0x50, 0x68, 0x6f, 0x74, 0x6f, 0x52, 0x65, 0x71, 0x12, 0x1a, 0x0a, 0x08, 0x70,
	0x68, 0x6f, 0x74, 0x6f, 0x55, 0x72, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x70,
	0x68, 0x6f, 0x74, 0x6f, 0x55, 0x72, 0x6c, 0x12, 0x1c, 0x0a, 0x09, 0x62, 0x6c, 0x6f, 0x63, 0x6b,
	0x48, 0x61, 0x73, 0x68, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x62, 0x6c, 0x6f, 0x63,
	0x6b, 0x48, 0x61, 0x73, 0x68, 0x12, 0x28, 0x0a, 0x0f, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63,
	0x74, 0x69, 0x6f, 0x6e, 0x48, 0x61, 0x73, 0x68, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f,
	0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x48, 0x61, 0x73, 0x68, 0x12,
	0x20, 0x0a, 0x0b, 0x62, 0x6c, 0x6f, 0x63, 0x6b, 0x48, 0x65, 0x69, 0x67, 0x68, 0x74, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x0b, 0x62, 0x6c, 0x6f, 0x63, 0x6b, 0x48, 0x65, 0x69, 0x67, 0x68,
	0x74, 0x12, 0x1e, 0x0a, 0x0a, 0x77, 0x69, 0x6e, 0x64, 0x55, 0x70, 0x54, 0x69, 0x6d, 0x65, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x77, 0x69, 0x6e, 0x64, 0x55, 0x70, 0x54, 0x69, 0x6d,
	0x65, 0x12, 0x18, 0x0a, 0x07, 0x63, 0x65, 0x72, 0x74, 0x4e, 0x75, 0x6d, 0x18, 0x06, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x07, 0x63, 0x65, 0x72, 0x74, 0x4e, 0x75, 0x6d, 0x12, 0x18, 0x0a, 0x07, 0x63,
	0x65, 0x72, 0x74, 0x55, 0x72, 0x6c, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x63, 0x65,
	0x72, 0x74, 0x55, 0x72, 0x6c, 0x22, 0x25, 0x0a, 0x0f, 0x43, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x50,
	0x68, 0x6f, 0x74, 0x6f, 0x52, 0x65, 0x73, 0x70, 0x12, 0x12, 0x0a, 0x04, 0x75, 0x75, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x75, 0x75, 0x69, 0x64, 0x22, 0xfe, 0x01, 0x0a,
	0x0e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x50, 0x68, 0x6f, 0x74, 0x6f, 0x52, 0x65, 0x71, 0x12,
	0x12, 0x0a, 0x04, 0x75, 0x75, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x75,
	0x75, 0x69, 0x64, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x68, 0x6f, 0x74, 0x6f, 0x55, 0x72, 0x6c, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x70, 0x68, 0x6f, 0x74, 0x6f, 0x55, 0x72, 0x6c, 0x12,
	0x1c, 0x0a, 0x09, 0x62, 0x6c, 0x6f, 0x63, 0x6b, 0x48, 0x61, 0x73, 0x68, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x09, 0x62, 0x6c, 0x6f, 0x63, 0x6b, 0x48, 0x61, 0x73, 0x68, 0x12, 0x28, 0x0a,
	0x0f, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x48, 0x61, 0x73, 0x68,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x48, 0x61, 0x73, 0x68, 0x12, 0x20, 0x0a, 0x0b, 0x62, 0x6c, 0x6f, 0x63, 0x6b,
	0x48, 0x65, 0x69, 0x67, 0x68, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0b, 0x62, 0x6c,
	0x6f, 0x63, 0x6b, 0x48, 0x65, 0x69, 0x67, 0x68, 0x74, 0x12, 0x1e, 0x0a, 0x0a, 0x77, 0x69, 0x6e,
	0x64, 0x55, 0x70, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x77,
	0x69, 0x6e, 0x64, 0x55, 0x70, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x63, 0x65, 0x72,
	0x74, 0x4e, 0x75, 0x6d, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x63, 0x65, 0x72, 0x74,
	0x4e, 0x75, 0x6d, 0x12, 0x18, 0x0a, 0x07, 0x63, 0x65, 0x72, 0x74, 0x55, 0x72, 0x6c, 0x18, 0x08,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x63, 0x65, 0x72, 0x74, 0x55, 0x72, 0x6c, 0x22, 0xcf, 0x01,
	0x0a, 0x13, 0x51, 0x75, 0x65, 0x72, 0x79, 0x50, 0x68, 0x6f, 0x74, 0x6f, 0x53, 0x69, 0x6e, 0x67,
	0x6c, 0x65, 0x52, 0x65, 0x71, 0x12, 0x12, 0x0a, 0x04, 0x75, 0x75, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x04, 0x75, 0x75, 0x69, 0x64, 0x12, 0x1c, 0x0a, 0x09, 0x62, 0x6c, 0x6f,
	0x63, 0x6b, 0x48, 0x61, 0x73, 0x68, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x62, 0x6c,
	0x6f, 0x63, 0x6b, 0x48, 0x61, 0x73, 0x68, 0x12, 0x28, 0x0a, 0x0f, 0x74, 0x72, 0x61, 0x6e, 0x73,
	0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x48, 0x61, 0x73, 0x68, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0f, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x48, 0x61, 0x73,
	0x68, 0x12, 0x20, 0x0a, 0x0b, 0x62, 0x6c, 0x6f, 0x63, 0x6b, 0x48, 0x65, 0x69, 0x67, 0x68, 0x74,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0b, 0x62, 0x6c, 0x6f, 0x63, 0x6b, 0x48, 0x65, 0x69,
	0x67, 0x68, 0x74, 0x12, 0x1e, 0x0a, 0x0a, 0x77, 0x69, 0x6e, 0x64, 0x55, 0x70, 0x54, 0x69, 0x6d,
	0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x77, 0x69, 0x6e, 0x64, 0x55, 0x70, 0x54,
	0x69, 0x6d, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x68, 0x6f, 0x74, 0x6f, 0x55, 0x72, 0x6c, 0x18,
	0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x70, 0x68, 0x6f, 0x74, 0x6f, 0x55, 0x72, 0x6c, 0x22,
	0x3c, 0x0a, 0x14, 0x51, 0x75, 0x65, 0x72, 0x79, 0x50, 0x68, 0x6f, 0x74, 0x6f, 0x53, 0x69, 0x6e,
	0x67, 0x6c, 0x65, 0x52, 0x65, 0x73, 0x70, 0x12, 0x24, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x10, 0x2e, 0x70, 0x68, 0x6f, 0x74, 0x6f, 0x57, 0x61, 0x6c,
	0x6c, 0x2e, 0x50, 0x68, 0x6f, 0x74, 0x6f, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x22, 0xe9, 0x01,
	0x0a, 0x11, 0x51, 0x75, 0x65, 0x72, 0x79, 0x50, 0x68, 0x6f, 0x74, 0x6f, 0x4c, 0x69, 0x73, 0x74,
	0x52, 0x65, 0x71, 0x12, 0x12, 0x0a, 0x04, 0x70, 0x61, 0x67, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x04, 0x70, 0x61, 0x67, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x61, 0x67, 0x65, 0x53,
	0x69, 0x7a, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x70, 0x61, 0x67, 0x65, 0x53,
	0x69, 0x7a, 0x65, 0x12, 0x1c, 0x0a, 0x09, 0x62, 0x6c, 0x6f, 0x63, 0x6b, 0x48, 0x61, 0x73, 0x68,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x62, 0x6c, 0x6f, 0x63, 0x6b, 0x48, 0x61, 0x73,
	0x68, 0x12, 0x28, 0x0a, 0x0f, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e,
	0x48, 0x61, 0x73, 0x68, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x74, 0x72, 0x61, 0x6e,
	0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x48, 0x61, 0x73, 0x68, 0x12, 0x20, 0x0a, 0x0b, 0x62,
	0x6c, 0x6f, 0x63, 0x6b, 0x48, 0x65, 0x69, 0x67, 0x68, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x0b, 0x62, 0x6c, 0x6f, 0x63, 0x6b, 0x48, 0x65, 0x69, 0x67, 0x68, 0x74, 0x12, 0x1e, 0x0a,
	0x0a, 0x77, 0x69, 0x6e, 0x64, 0x55, 0x70, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0a, 0x77, 0x69, 0x6e, 0x64, 0x55, 0x70, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x1a, 0x0a,
	0x08, 0x70, 0x68, 0x6f, 0x74, 0x6f, 0x55, 0x72, 0x6c, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x08, 0x70, 0x68, 0x6f, 0x74, 0x6f, 0x55, 0x72, 0x6c, 0x22, 0x80, 0x01, 0x0a, 0x12, 0x51, 0x75,
	0x65, 0x72, 0x79, 0x50, 0x68, 0x6f, 0x74, 0x6f, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x70,
	0x12, 0x24, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x10,
	0x2e, 0x70, 0x68, 0x6f, 0x74, 0x6f, 0x57, 0x61, 0x6c, 0x6c, 0x2e, 0x50, 0x68, 0x6f, 0x74, 0x6f,
	0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x12, 0x12, 0x0a, 0x04, 0x70, 0x61, 0x67, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x04, 0x70, 0x61, 0x67, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x61,
	0x67, 0x65, 0x53, 0x69, 0x7a, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x70, 0x61,
	0x67, 0x65, 0x53, 0x69, 0x7a, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x54, 0x6f, 0x74, 0x61, 0x6c, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x54, 0x6f, 0x74, 0x61, 0x6c, 0x22, 0xf5, 0x01, 0x0a,
	0x05, 0x50, 0x68, 0x6f, 0x74, 0x6f, 0x12, 0x12, 0x0a, 0x04, 0x75, 0x75, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x75, 0x75, 0x69, 0x64, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x68,
	0x6f, 0x74, 0x6f, 0x55, 0x72, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x70, 0x68,
	0x6f, 0x74, 0x6f, 0x55, 0x72, 0x6c, 0x12, 0x1c, 0x0a, 0x09, 0x62, 0x6c, 0x6f, 0x63, 0x6b, 0x48,
	0x61, 0x73, 0x68, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x62, 0x6c, 0x6f, 0x63, 0x6b,
	0x48, 0x61, 0x73, 0x68, 0x12, 0x28, 0x0a, 0x0f, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x48, 0x61, 0x73, 0x68, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x74,
	0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x48, 0x61, 0x73, 0x68, 0x12, 0x20,
	0x0a, 0x0b, 0x62, 0x6c, 0x6f, 0x63, 0x6b, 0x48, 0x65, 0x69, 0x67, 0x68, 0x74, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x0b, 0x62, 0x6c, 0x6f, 0x63, 0x6b, 0x48, 0x65, 0x69, 0x67, 0x68, 0x74,
	0x12, 0x1e, 0x0a, 0x0a, 0x77, 0x69, 0x6e, 0x64, 0x55, 0x70, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x06,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x77, 0x69, 0x6e, 0x64, 0x55, 0x70, 0x54, 0x69, 0x6d, 0x65,
	0x12, 0x18, 0x0a, 0x07, 0x63, 0x65, 0x72, 0x74, 0x4e, 0x75, 0x6d, 0x18, 0x07, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x07, 0x63, 0x65, 0x72, 0x74, 0x4e, 0x75, 0x6d, 0x12, 0x18, 0x0a, 0x07, 0x63, 0x65,
	0x72, 0x74, 0x55, 0x72, 0x6c, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x63, 0x65, 0x72,
	0x74, 0x55, 0x72, 0x6c, 0x32, 0xc1, 0x02, 0x0a, 0x11, 0x50, 0x68, 0x6f, 0x74, 0x6f, 0x57, 0x61,
	0x6c, 0x6c, 0x50, 0x72, 0x6f, 0x76, 0x69, 0x64, 0x65, 0x72, 0x12, 0x42, 0x0a, 0x09, 0x53, 0x61,
	0x76, 0x65, 0x50, 0x68, 0x6f, 0x74, 0x6f, 0x12, 0x17, 0x2e, 0x70, 0x68, 0x6f, 0x74, 0x6f, 0x57,
	0x61, 0x6c, 0x6c, 0x2e, 0x53, 0x61, 0x76, 0x65, 0x50, 0x68, 0x6f, 0x74, 0x6f, 0x52, 0x65, 0x71,
	0x1a, 0x1a, 0x2e, 0x70, 0x68, 0x6f, 0x74, 0x6f, 0x57, 0x61, 0x6c, 0x6c, 0x2e, 0x43, 0x6f, 0x6d,
	0x6d, 0x6f, 0x6e, 0x50, 0x68, 0x6f, 0x74, 0x6f, 0x52, 0x65, 0x73, 0x70, 0x22, 0x00, 0x12, 0x46,
	0x0a, 0x0b, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x50, 0x68, 0x6f, 0x74, 0x6f, 0x12, 0x19, 0x2e,
	0x70, 0x68, 0x6f, 0x74, 0x6f, 0x57, 0x61, 0x6c, 0x6c, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x50, 0x68, 0x6f, 0x74, 0x6f, 0x52, 0x65, 0x71, 0x1a, 0x1a, 0x2e, 0x70, 0x68, 0x6f, 0x74, 0x6f,
	0x57, 0x61, 0x6c, 0x6c, 0x2e, 0x43, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x50, 0x68, 0x6f, 0x74, 0x6f,
	0x52, 0x65, 0x73, 0x70, 0x22, 0x00, 0x12, 0x4f, 0x0a, 0x0a, 0x51, 0x75, 0x65, 0x72, 0x79, 0x50,
	0x68, 0x6f, 0x74, 0x6f, 0x12, 0x1e, 0x2e, 0x70, 0x68, 0x6f, 0x74, 0x6f, 0x57, 0x61, 0x6c, 0x6c,
	0x2e, 0x51, 0x75, 0x65, 0x72, 0x79, 0x50, 0x68, 0x6f, 0x74, 0x6f, 0x53, 0x69, 0x6e, 0x67, 0x6c,
	0x65, 0x52, 0x65, 0x71, 0x1a, 0x1f, 0x2e, 0x70, 0x68, 0x6f, 0x74, 0x6f, 0x57, 0x61, 0x6c, 0x6c,
	0x2e, 0x51, 0x75, 0x65, 0x72, 0x79, 0x50, 0x68, 0x6f, 0x74, 0x6f, 0x53, 0x69, 0x6e, 0x67, 0x6c,
	0x65, 0x52, 0x65, 0x73, 0x70, 0x22, 0x00, 0x12, 0x4f, 0x0a, 0x0e, 0x51, 0x75, 0x65, 0x72, 0x79,
	0x50, 0x68, 0x6f, 0x74, 0x6f, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x1c, 0x2e, 0x70, 0x68, 0x6f, 0x74,
	0x6f, 0x57, 0x61, 0x6c, 0x6c, 0x2e, 0x51, 0x75, 0x65, 0x72, 0x79, 0x50, 0x68, 0x6f, 0x74, 0x6f,
	0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x1a, 0x1d, 0x2e, 0x70, 0x68, 0x6f, 0x74, 0x6f, 0x57,
	0x61, 0x6c, 0x6c, 0x2e, 0x51, 0x75, 0x65, 0x72, 0x79, 0x50, 0x68, 0x6f, 0x74, 0x6f, 0x4c, 0x69,
	0x73, 0x74, 0x52, 0x65, 0x73, 0x70, 0x22, 0x00, 0x42, 0x0e, 0x5a, 0x0c, 0x2e, 0x2f, 0x3b, 0x70,
	0x68, 0x6f, 0x74, 0x6f, 0x57, 0x61, 0x6c, 0x6c, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_photoWall_photoWall_proto_rawDescOnce sync.Once
	file_api_photoWall_photoWall_proto_rawDescData = file_api_photoWall_photoWall_proto_rawDesc
)

func file_api_photoWall_photoWall_proto_rawDescGZIP() []byte {
	file_api_photoWall_photoWall_proto_rawDescOnce.Do(func() {
		file_api_photoWall_photoWall_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_photoWall_photoWall_proto_rawDescData)
	})
	return file_api_photoWall_photoWall_proto_rawDescData
}

var file_api_photoWall_photoWall_proto_msgTypes = make([]protoimpl.MessageInfo, 8)
var file_api_photoWall_photoWall_proto_goTypes = []interface{}{
	(*SavePhotoReq)(nil),         // 0: photoWall.SavePhotoReq
	(*CommonPhotoResp)(nil),      // 1: photoWall.CommonPhotoResp
	(*UpdatePhotoReq)(nil),       // 2: photoWall.UpdatePhotoReq
	(*QueryPhotoSingleReq)(nil),  // 3: photoWall.QueryPhotoSingleReq
	(*QueryPhotoSingleResp)(nil), // 4: photoWall.QueryPhotoSingleResp
	(*QueryPhotoListReq)(nil),    // 5: photoWall.QueryPhotoListReq
	(*QueryPhotoListResp)(nil),   // 6: photoWall.QueryPhotoListResp
	(*Photo)(nil),                // 7: photoWall.Photo
}
var file_api_photoWall_photoWall_proto_depIdxs = []int32{
	7, // 0: photoWall.QueryPhotoSingleResp.data:type_name -> photoWall.Photo
	7, // 1: photoWall.QueryPhotoListResp.data:type_name -> photoWall.Photo
	0, // 2: photoWall.PhotoWallProvider.SavePhoto:input_type -> photoWall.SavePhotoReq
	2, // 3: photoWall.PhotoWallProvider.UpdatePhoto:input_type -> photoWall.UpdatePhotoReq
	3, // 4: photoWall.PhotoWallProvider.QueryPhoto:input_type -> photoWall.QueryPhotoSingleReq
	5, // 5: photoWall.PhotoWallProvider.QueryPhotoList:input_type -> photoWall.QueryPhotoListReq
	1, // 6: photoWall.PhotoWallProvider.SavePhoto:output_type -> photoWall.CommonPhotoResp
	1, // 7: photoWall.PhotoWallProvider.UpdatePhoto:output_type -> photoWall.CommonPhotoResp
	4, // 8: photoWall.PhotoWallProvider.QueryPhoto:output_type -> photoWall.QueryPhotoSingleResp
	6, // 9: photoWall.PhotoWallProvider.QueryPhotoList:output_type -> photoWall.QueryPhotoListResp
	6, // [6:10] is the sub-list for method output_type
	2, // [2:6] is the sub-list for method input_type
	2, // [2:2] is the sub-list for extension type_name
	2, // [2:2] is the sub-list for extension extendee
	0, // [0:2] is the sub-list for field type_name
}

func init() { file_api_photoWall_photoWall_proto_init() }
func file_api_photoWall_photoWall_proto_init() {
	if File_api_photoWall_photoWall_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_api_photoWall_photoWall_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SavePhotoReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_photoWall_photoWall_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CommonPhotoResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_photoWall_photoWall_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdatePhotoReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_photoWall_photoWall_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*QueryPhotoSingleReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_photoWall_photoWall_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*QueryPhotoSingleResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_photoWall_photoWall_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*QueryPhotoListReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_photoWall_photoWall_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*QueryPhotoListResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_photoWall_photoWall_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Photo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_photoWall_photoWall_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   8,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_api_photoWall_photoWall_proto_goTypes,
		DependencyIndexes: file_api_photoWall_photoWall_proto_depIdxs,
		MessageInfos:      file_api_photoWall_photoWall_proto_msgTypes,
	}.Build()
	File_api_photoWall_photoWall_proto = out.File
	file_api_photoWall_photoWall_proto_rawDesc = nil
	file_api_photoWall_photoWall_proto_goTypes = nil
	file_api_photoWall_photoWall_proto_depIdxs = nil
}
