// Code generated by protoc-gen-go-triple. DO NOT EDIT.
// versions:
// - protoc-gen-go-triple v1.0.8
// - protoc             v3.21.1
// source: pb/artwork_query.proto

package artwork_query

import (
	context "context"
	protocol "dubbo.apache.org/dubbo-go/v3/protocol"
	dubbo3 "dubbo.apache.org/dubbo-go/v3/protocol/dubbo3"
	invocation "dubbo.apache.org/dubbo-go/v3/protocol/invocation"
	grpc_go "github.com/dubbogo/grpc-go"
	codes "github.com/dubbogo/grpc-go/codes"
	metadata "github.com/dubbogo/grpc-go/metadata"
	status "github.com/dubbogo/grpc-go/status"
	common "github.com/dubbogo/triple/pkg/common"
	constant "github.com/dubbogo/triple/pkg/common/constant"
	triple "github.com/dubbogo/triple/pkg/triple"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc_go.SupportPackageIsVersion7

// ArtworkQueryClient is the client API for ArtworkQuery service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type ArtworkQueryClient interface {
	ArtworkList(ctx context.Context, in *ArtworkListRequest, opts ...grpc_go.CallOption) (*ArtworkListResponse, common.ErrorWithAttachment)
	DelArtwork(ctx context.Context, in *DelAwRequest, opts ...grpc_go.CallOption) (*DelAwResponse, common.ErrorWithAttachment)
	DelAuthData(ctx context.Context, in *DelAuthDataRequest, opts ...grpc_go.CallOption) (*DelAuthDataResponse, common.ErrorWithAttachment)
	DelMarketData(ctx context.Context, in *DelMarketDataRequest, opts ...grpc_go.CallOption) (*DelMarketDataResponse, common.ErrorWithAttachment)
	DelStorageData(ctx context.Context, in *DelStorageDataRequest, opts ...grpc_go.CallOption) (*DelStorageDataResponse, common.ErrorWithAttachment)
	TagsList(ctx context.Context, in *TagsListRequest, opts ...grpc_go.CallOption) (*TagsListResponse, common.ErrorWithAttachment)
	CatList(ctx context.Context, in *CatListRequest, opts ...grpc_go.CallOption) (*CatListResponse, common.ErrorWithAttachment)
	ImgMatchByUuid(ctx context.Context, in *ImgMatchRequest, opts ...grpc_go.CallOption) (*ImgMatchResponse, common.ErrorWithAttachment)
	BatchBitMap(ctx context.Context, in *BatchBitMapRequest, opts ...grpc_go.CallOption) (*BatchBitMapResponse, common.ErrorWithAttachment)
	CheckArtworkName(ctx context.Context, in *CheckArtworkNameRequest, opts ...grpc_go.CallOption) (*CheckArtworkNameResponse, common.ErrorWithAttachment)
	CheckArtworkTfnum(ctx context.Context, in *CheckArtworkTfnumRequest, opts ...grpc_go.CallOption) (*CheckArtworkTfnumResponse, common.ErrorWithAttachment)
	UpdateThirdParty(ctx context.Context, in *UpdateThirdPartyRequest, opts ...grpc_go.CallOption) (*UpdateThirdPartyResponse, common.ErrorWithAttachment)
	DelThirdParty(ctx context.Context, in *DelThirdPartyRequest, opts ...grpc_go.CallOption) (*DelThirdPartyResponse, common.ErrorWithAttachment)
	ThirdPartyList(ctx context.Context, in *ThirdPartyListRequest, opts ...grpc_go.CallOption) (*ThirdPartyListResponse, common.ErrorWithAttachment)
	UpdateAwStockStatus(ctx context.Context, in *UpdateAwStockStatusRequest, opts ...grpc_go.CallOption) (*UpdateAwStockStatusResponse, common.ErrorWithAttachment)
	SyncArtShowId(ctx context.Context, in *SyncArtShowIdRequest, opts ...grpc_go.CallOption) (*SyncArtShowIdResponse, common.ErrorWithAttachment)
	ShelfList(ctx context.Context, in *ShelfListRequest, opts ...grpc_go.CallOption) (*ShelfListResponse, common.ErrorWithAttachment)
	UpdateCopyrightHash(ctx context.Context, in *UpdateCopyrightHashRequest, opts ...grpc_go.CallOption) (*UpdateCopyrightHashResponse, common.ErrorWithAttachment)
	ExportArtwork(ctx context.Context, in *ExportArtworkRequest, opts ...grpc_go.CallOption) (*ExportArtworkResponse, common.ErrorWithAttachment)
	TagIdKvList(ctx context.Context, in *TagIdKvListRequest, opts ...grpc_go.CallOption) (*TagIdKvListResponse, common.ErrorWithAttachment)
	ExportFieldList(ctx context.Context, in *ExportFieldListRequest, opts ...grpc_go.CallOption) (*ExportFieldListResponse, common.ErrorWithAttachment)
	ArtworkDataByShowId(ctx context.Context, in *ArtworkDataByShowIdRequest, opts ...grpc_go.CallOption) (*ArtworkDataByShowIdResponse, common.ErrorWithAttachment)
	MyAwList(ctx context.Context, in *MyAwListReq, opts ...grpc_go.CallOption) (*MyAwListResp, common.ErrorWithAttachment)
	ArtworkPreviewList(ctx context.Context, in *ArtworkPreviewListRequest, opts ...grpc_go.CallOption) (*ArtworkPreviewListResponse, common.ErrorWithAttachment)
	VerifyList(ctx context.Context, in *VerifyListReq, opts ...grpc_go.CallOption) (*VerifyListResp, common.ErrorWithAttachment)
	ArtshowList(ctx context.Context, in *ArtshowListReq, opts ...grpc_go.CallOption) (*ArtshowListResp, common.ErrorWithAttachment)
	CountVerifySimilar(ctx context.Context, in *CountVerifySimilarReq, opts ...grpc_go.CallOption) (*CountVerifySimilarResp, common.ErrorWithAttachment)
	OneQuery(ctx context.Context, in *OneQueryReq, opts ...grpc_go.CallOption) (*OneQueryResp, common.ErrorWithAttachment)
	GetPassArtist(ctx context.Context, in *GetPassArtistReq, opts ...grpc_go.CallOption) (*GetPassArtistResp, common.ErrorWithAttachment)
	FilterAwList(ctx context.Context, in *FilterAwListReq, opts ...grpc_go.CallOption) (*FilterAwListResp, common.ErrorWithAttachment)
	ChainInfoByHash(ctx context.Context, in *ChainInfoByHashReq, opts ...grpc_go.CallOption) (*ChainInfoByHashResp, common.ErrorWithAttachment)
	StockOutArtist(ctx context.Context, in *StockOutArtistReq, opts ...grpc_go.CallOption) (*StockOutArtistResp, common.ErrorWithAttachment)
	ChainList(ctx context.Context, in *ChainListReq, opts ...grpc_go.CallOption) (*ChainListResp, common.ErrorWithAttachment)
	TotalTransfer(ctx context.Context, in *emptypb.Empty, opts ...grpc_go.CallOption) (*TotalTransferResp, common.ErrorWithAttachment)
	GetArtworkProfileList(ctx context.Context, in *GetArtworkProfileListRequest, opts ...grpc_go.CallOption) (*GetArtworkProfileListResp, common.ErrorWithAttachment)
	ArtistBindArtworkList(ctx context.Context, in *ArtistBindArtworkListReq, opts ...grpc_go.CallOption) (*ArtistBindArtworkListResp, common.ErrorWithAttachment)
	GetArtworkCopyrightList(ctx context.Context, in *GetArtworkCopyrightListRequest, opts ...grpc_go.CallOption) (*GetArtworkCopyrightListResp, common.ErrorWithAttachment)
	GetArtworkDataBatch(ctx context.Context, in *GetArtworkDataBatchReq, opts ...grpc_go.CallOption) (*GetArtworkDataBatchResp, common.ErrorWithAttachment)
	BatchDciList(ctx context.Context, in *BatchDciListReq, opts ...grpc_go.CallOption) (*BatchDciListResp, common.ErrorWithAttachment)
	ArtworkArtistList(ctx context.Context, in *ArtworkArtistListReq, opts ...grpc_go.CallOption) (*ArtworkArtistListResp, common.ErrorWithAttachment)
	StorageList(ctx context.Context, in *StorageListReq, opts ...grpc_go.CallOption) (*StorageListResp, common.ErrorWithAttachment)
	SecondArtworkList(ctx context.Context, in *SecondArtworkListReq, opts ...grpc_go.CallOption) (*SecondArtworkListResp, common.ErrorWithAttachment)
	ArtistsDataByUuids(ctx context.Context, in *ArtistsDataByUuidsReq, opts ...grpc_go.CallOption) (*ArtistsDataByUuidsResp, common.ErrorWithAttachment)
}

type artworkQueryClient struct {
	cc *triple.TripleConn
}

type ArtworkQueryClientImpl struct {
	ArtworkList             func(ctx context.Context, in *ArtworkListRequest) (*ArtworkListResponse, error)
	DelArtwork              func(ctx context.Context, in *DelAwRequest) (*DelAwResponse, error)
	DelAuthData             func(ctx context.Context, in *DelAuthDataRequest) (*DelAuthDataResponse, error)
	DelMarketData           func(ctx context.Context, in *DelMarketDataRequest) (*DelMarketDataResponse, error)
	DelStorageData          func(ctx context.Context, in *DelStorageDataRequest) (*DelStorageDataResponse, error)
	TagsList                func(ctx context.Context, in *TagsListRequest) (*TagsListResponse, error)
	CatList                 func(ctx context.Context, in *CatListRequest) (*CatListResponse, error)
	ImgMatchByUuid          func(ctx context.Context, in *ImgMatchRequest) (*ImgMatchResponse, error)
	BatchBitMap             func(ctx context.Context, in *BatchBitMapRequest) (*BatchBitMapResponse, error)
	CheckArtworkName        func(ctx context.Context, in *CheckArtworkNameRequest) (*CheckArtworkNameResponse, error)
	CheckArtworkTfnum       func(ctx context.Context, in *CheckArtworkTfnumRequest) (*CheckArtworkTfnumResponse, error)
	UpdateThirdParty        func(ctx context.Context, in *UpdateThirdPartyRequest) (*UpdateThirdPartyResponse, error)
	DelThirdParty           func(ctx context.Context, in *DelThirdPartyRequest) (*DelThirdPartyResponse, error)
	ThirdPartyList          func(ctx context.Context, in *ThirdPartyListRequest) (*ThirdPartyListResponse, error)
	UpdateAwStockStatus     func(ctx context.Context, in *UpdateAwStockStatusRequest) (*UpdateAwStockStatusResponse, error)
	SyncArtShowId           func(ctx context.Context, in *SyncArtShowIdRequest) (*SyncArtShowIdResponse, error)
	ShelfList               func(ctx context.Context, in *ShelfListRequest) (*ShelfListResponse, error)
	UpdateCopyrightHash     func(ctx context.Context, in *UpdateCopyrightHashRequest) (*UpdateCopyrightHashResponse, error)
	ExportArtwork           func(ctx context.Context, in *ExportArtworkRequest) (*ExportArtworkResponse, error)
	TagIdKvList             func(ctx context.Context, in *TagIdKvListRequest) (*TagIdKvListResponse, error)
	ExportFieldList         func(ctx context.Context, in *ExportFieldListRequest) (*ExportFieldListResponse, error)
	ArtworkDataByShowId     func(ctx context.Context, in *ArtworkDataByShowIdRequest) (*ArtworkDataByShowIdResponse, error)
	MyAwList                func(ctx context.Context, in *MyAwListReq) (*MyAwListResp, error)
	ArtworkPreviewList      func(ctx context.Context, in *ArtworkPreviewListRequest) (*ArtworkPreviewListResponse, error)
	VerifyList              func(ctx context.Context, in *VerifyListReq) (*VerifyListResp, error)
	ArtshowList             func(ctx context.Context, in *ArtshowListReq) (*ArtshowListResp, error)
	CountVerifySimilar      func(ctx context.Context, in *CountVerifySimilarReq) (*CountVerifySimilarResp, error)
	OneQuery                func(ctx context.Context, in *OneQueryReq) (*OneQueryResp, error)
	GetPassArtist           func(ctx context.Context, in *GetPassArtistReq) (*GetPassArtistResp, error)
	FilterAwList            func(ctx context.Context, in *FilterAwListReq) (*FilterAwListResp, error)
	ChainInfoByHash         func(ctx context.Context, in *ChainInfoByHashReq) (*ChainInfoByHashResp, error)
	StockOutArtist          func(ctx context.Context, in *StockOutArtistReq) (*StockOutArtistResp, error)
	ChainList               func(ctx context.Context, in *ChainListReq) (*ChainListResp, error)
	TotalTransfer           func(ctx context.Context, in *emptypb.Empty) (*TotalTransferResp, error)
	GetArtworkProfileList   func(ctx context.Context, in *GetArtworkProfileListRequest) (*GetArtworkProfileListResp, error)
	ArtistBindArtworkList   func(ctx context.Context, in *ArtistBindArtworkListReq) (*ArtistBindArtworkListResp, error)
	GetArtworkCopyrightList func(ctx context.Context, in *GetArtworkCopyrightListRequest) (*GetArtworkCopyrightListResp, error)
	GetArtworkDataBatch     func(ctx context.Context, in *GetArtworkDataBatchReq) (*GetArtworkDataBatchResp, error)
	BatchDciList            func(ctx context.Context, in *BatchDciListReq) (*BatchDciListResp, error)
	ArtworkArtistList       func(ctx context.Context, in *ArtworkArtistListReq) (*ArtworkArtistListResp, error)
	StorageList             func(ctx context.Context, in *StorageListReq) (*StorageListResp, error)
	SecondArtworkList       func(ctx context.Context, in *SecondArtworkListReq) (*SecondArtworkListResp, error)
	ArtistsDataByUuids      func(ctx context.Context, in *ArtistsDataByUuidsReq) (*ArtistsDataByUuidsResp, error)
}

func (c *ArtworkQueryClientImpl) GetDubboStub(cc *triple.TripleConn) ArtworkQueryClient {
	return NewArtworkQueryClient(cc)
}

func (c *ArtworkQueryClientImpl) XXX_InterfaceName() string {
	return "Artwork.ArtworkQuery"
}

func NewArtworkQueryClient(cc *triple.TripleConn) ArtworkQueryClient {
	return &artworkQueryClient{cc}
}

func (c *artworkQueryClient) ArtworkList(ctx context.Context, in *ArtworkListRequest, opts ...grpc_go.CallOption) (*ArtworkListResponse, common.ErrorWithAttachment) {
	out := new(ArtworkListResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/ArtworkList", in, out)
}

func (c *artworkQueryClient) DelArtwork(ctx context.Context, in *DelAwRequest, opts ...grpc_go.CallOption) (*DelAwResponse, common.ErrorWithAttachment) {
	out := new(DelAwResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/DelArtwork", in, out)
}

func (c *artworkQueryClient) DelAuthData(ctx context.Context, in *DelAuthDataRequest, opts ...grpc_go.CallOption) (*DelAuthDataResponse, common.ErrorWithAttachment) {
	out := new(DelAuthDataResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/DelAuthData", in, out)
}

func (c *artworkQueryClient) DelMarketData(ctx context.Context, in *DelMarketDataRequest, opts ...grpc_go.CallOption) (*DelMarketDataResponse, common.ErrorWithAttachment) {
	out := new(DelMarketDataResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/DelMarketData", in, out)
}

func (c *artworkQueryClient) DelStorageData(ctx context.Context, in *DelStorageDataRequest, opts ...grpc_go.CallOption) (*DelStorageDataResponse, common.ErrorWithAttachment) {
	out := new(DelStorageDataResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/DelStorageData", in, out)
}

func (c *artworkQueryClient) TagsList(ctx context.Context, in *TagsListRequest, opts ...grpc_go.CallOption) (*TagsListResponse, common.ErrorWithAttachment) {
	out := new(TagsListResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/TagsList", in, out)
}

func (c *artworkQueryClient) CatList(ctx context.Context, in *CatListRequest, opts ...grpc_go.CallOption) (*CatListResponse, common.ErrorWithAttachment) {
	out := new(CatListResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/CatList", in, out)
}

func (c *artworkQueryClient) ImgMatchByUuid(ctx context.Context, in *ImgMatchRequest, opts ...grpc_go.CallOption) (*ImgMatchResponse, common.ErrorWithAttachment) {
	out := new(ImgMatchResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/ImgMatchByUuid", in, out)
}

func (c *artworkQueryClient) BatchBitMap(ctx context.Context, in *BatchBitMapRequest, opts ...grpc_go.CallOption) (*BatchBitMapResponse, common.ErrorWithAttachment) {
	out := new(BatchBitMapResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/BatchBitMap", in, out)
}

func (c *artworkQueryClient) CheckArtworkName(ctx context.Context, in *CheckArtworkNameRequest, opts ...grpc_go.CallOption) (*CheckArtworkNameResponse, common.ErrorWithAttachment) {
	out := new(CheckArtworkNameResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/CheckArtworkName", in, out)
}

func (c *artworkQueryClient) CheckArtworkTfnum(ctx context.Context, in *CheckArtworkTfnumRequest, opts ...grpc_go.CallOption) (*CheckArtworkTfnumResponse, common.ErrorWithAttachment) {
	out := new(CheckArtworkTfnumResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/CheckArtworkTfnum", in, out)
}

func (c *artworkQueryClient) UpdateThirdParty(ctx context.Context, in *UpdateThirdPartyRequest, opts ...grpc_go.CallOption) (*UpdateThirdPartyResponse, common.ErrorWithAttachment) {
	out := new(UpdateThirdPartyResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/UpdateThirdParty", in, out)
}

func (c *artworkQueryClient) DelThirdParty(ctx context.Context, in *DelThirdPartyRequest, opts ...grpc_go.CallOption) (*DelThirdPartyResponse, common.ErrorWithAttachment) {
	out := new(DelThirdPartyResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/DelThirdParty", in, out)
}

func (c *artworkQueryClient) ThirdPartyList(ctx context.Context, in *ThirdPartyListRequest, opts ...grpc_go.CallOption) (*ThirdPartyListResponse, common.ErrorWithAttachment) {
	out := new(ThirdPartyListResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/ThirdPartyList", in, out)
}

func (c *artworkQueryClient) UpdateAwStockStatus(ctx context.Context, in *UpdateAwStockStatusRequest, opts ...grpc_go.CallOption) (*UpdateAwStockStatusResponse, common.ErrorWithAttachment) {
	out := new(UpdateAwStockStatusResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/UpdateAwStockStatus", in, out)
}

func (c *artworkQueryClient) SyncArtShowId(ctx context.Context, in *SyncArtShowIdRequest, opts ...grpc_go.CallOption) (*SyncArtShowIdResponse, common.ErrorWithAttachment) {
	out := new(SyncArtShowIdResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/SyncArtShowId", in, out)
}

func (c *artworkQueryClient) ShelfList(ctx context.Context, in *ShelfListRequest, opts ...grpc_go.CallOption) (*ShelfListResponse, common.ErrorWithAttachment) {
	out := new(ShelfListResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/ShelfList", in, out)
}

func (c *artworkQueryClient) UpdateCopyrightHash(ctx context.Context, in *UpdateCopyrightHashRequest, opts ...grpc_go.CallOption) (*UpdateCopyrightHashResponse, common.ErrorWithAttachment) {
	out := new(UpdateCopyrightHashResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/UpdateCopyrightHash", in, out)
}

func (c *artworkQueryClient) ExportArtwork(ctx context.Context, in *ExportArtworkRequest, opts ...grpc_go.CallOption) (*ExportArtworkResponse, common.ErrorWithAttachment) {
	out := new(ExportArtworkResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/ExportArtwork", in, out)
}

func (c *artworkQueryClient) TagIdKvList(ctx context.Context, in *TagIdKvListRequest, opts ...grpc_go.CallOption) (*TagIdKvListResponse, common.ErrorWithAttachment) {
	out := new(TagIdKvListResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/TagIdKvList", in, out)
}

func (c *artworkQueryClient) ExportFieldList(ctx context.Context, in *ExportFieldListRequest, opts ...grpc_go.CallOption) (*ExportFieldListResponse, common.ErrorWithAttachment) {
	out := new(ExportFieldListResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/ExportFieldList", in, out)
}

func (c *artworkQueryClient) ArtworkDataByShowId(ctx context.Context, in *ArtworkDataByShowIdRequest, opts ...grpc_go.CallOption) (*ArtworkDataByShowIdResponse, common.ErrorWithAttachment) {
	out := new(ArtworkDataByShowIdResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/ArtworkDataByShowId", in, out)
}

func (c *artworkQueryClient) MyAwList(ctx context.Context, in *MyAwListReq, opts ...grpc_go.CallOption) (*MyAwListResp, common.ErrorWithAttachment) {
	out := new(MyAwListResp)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/MyAwList", in, out)
}

func (c *artworkQueryClient) ArtworkPreviewList(ctx context.Context, in *ArtworkPreviewListRequest, opts ...grpc_go.CallOption) (*ArtworkPreviewListResponse, common.ErrorWithAttachment) {
	out := new(ArtworkPreviewListResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/ArtworkPreviewList", in, out)
}

func (c *artworkQueryClient) VerifyList(ctx context.Context, in *VerifyListReq, opts ...grpc_go.CallOption) (*VerifyListResp, common.ErrorWithAttachment) {
	out := new(VerifyListResp)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/VerifyList", in, out)
}

func (c *artworkQueryClient) ArtshowList(ctx context.Context, in *ArtshowListReq, opts ...grpc_go.CallOption) (*ArtshowListResp, common.ErrorWithAttachment) {
	out := new(ArtshowListResp)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/ArtshowList", in, out)
}

func (c *artworkQueryClient) CountVerifySimilar(ctx context.Context, in *CountVerifySimilarReq, opts ...grpc_go.CallOption) (*CountVerifySimilarResp, common.ErrorWithAttachment) {
	out := new(CountVerifySimilarResp)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/CountVerifySimilar", in, out)
}

func (c *artworkQueryClient) OneQuery(ctx context.Context, in *OneQueryReq, opts ...grpc_go.CallOption) (*OneQueryResp, common.ErrorWithAttachment) {
	out := new(OneQueryResp)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/OneQuery", in, out)
}

func (c *artworkQueryClient) GetPassArtist(ctx context.Context, in *GetPassArtistReq, opts ...grpc_go.CallOption) (*GetPassArtistResp, common.ErrorWithAttachment) {
	out := new(GetPassArtistResp)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/GetPassArtist", in, out)
}

func (c *artworkQueryClient) FilterAwList(ctx context.Context, in *FilterAwListReq, opts ...grpc_go.CallOption) (*FilterAwListResp, common.ErrorWithAttachment) {
	out := new(FilterAwListResp)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/FilterAwList", in, out)
}

func (c *artworkQueryClient) ChainInfoByHash(ctx context.Context, in *ChainInfoByHashReq, opts ...grpc_go.CallOption) (*ChainInfoByHashResp, common.ErrorWithAttachment) {
	out := new(ChainInfoByHashResp)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/ChainInfoByHash", in, out)
}

func (c *artworkQueryClient) StockOutArtist(ctx context.Context, in *StockOutArtistReq, opts ...grpc_go.CallOption) (*StockOutArtistResp, common.ErrorWithAttachment) {
	out := new(StockOutArtistResp)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/StockOutArtist", in, out)
}

func (c *artworkQueryClient) ChainList(ctx context.Context, in *ChainListReq, opts ...grpc_go.CallOption) (*ChainListResp, common.ErrorWithAttachment) {
	out := new(ChainListResp)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/ChainList", in, out)
}

func (c *artworkQueryClient) TotalTransfer(ctx context.Context, in *emptypb.Empty, opts ...grpc_go.CallOption) (*TotalTransferResp, common.ErrorWithAttachment) {
	out := new(TotalTransferResp)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/TotalTransfer", in, out)
}

func (c *artworkQueryClient) GetArtworkProfileList(ctx context.Context, in *GetArtworkProfileListRequest, opts ...grpc_go.CallOption) (*GetArtworkProfileListResp, common.ErrorWithAttachment) {
	out := new(GetArtworkProfileListResp)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/GetArtworkProfileList", in, out)
}

func (c *artworkQueryClient) ArtistBindArtworkList(ctx context.Context, in *ArtistBindArtworkListReq, opts ...grpc_go.CallOption) (*ArtistBindArtworkListResp, common.ErrorWithAttachment) {
	out := new(ArtistBindArtworkListResp)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/ArtistBindArtworkList", in, out)
}

func (c *artworkQueryClient) GetArtworkCopyrightList(ctx context.Context, in *GetArtworkCopyrightListRequest, opts ...grpc_go.CallOption) (*GetArtworkCopyrightListResp, common.ErrorWithAttachment) {
	out := new(GetArtworkCopyrightListResp)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/GetArtworkCopyrightList", in, out)
}

func (c *artworkQueryClient) GetArtworkDataBatch(ctx context.Context, in *GetArtworkDataBatchReq, opts ...grpc_go.CallOption) (*GetArtworkDataBatchResp, common.ErrorWithAttachment) {
	out := new(GetArtworkDataBatchResp)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/GetArtworkDataBatch", in, out)
}

func (c *artworkQueryClient) BatchDciList(ctx context.Context, in *BatchDciListReq, opts ...grpc_go.CallOption) (*BatchDciListResp, common.ErrorWithAttachment) {
	out := new(BatchDciListResp)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/BatchDciList", in, out)
}

func (c *artworkQueryClient) ArtworkArtistList(ctx context.Context, in *ArtworkArtistListReq, opts ...grpc_go.CallOption) (*ArtworkArtistListResp, common.ErrorWithAttachment) {
	out := new(ArtworkArtistListResp)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/ArtworkArtistList", in, out)
}

func (c *artworkQueryClient) StorageList(ctx context.Context, in *StorageListReq, opts ...grpc_go.CallOption) (*StorageListResp, common.ErrorWithAttachment) {
	out := new(StorageListResp)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/StorageList", in, out)
}

func (c *artworkQueryClient) SecondArtworkList(ctx context.Context, in *SecondArtworkListReq, opts ...grpc_go.CallOption) (*SecondArtworkListResp, common.ErrorWithAttachment) {
	out := new(SecondArtworkListResp)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/SecondArtworkList", in, out)
}

func (c *artworkQueryClient) ArtistsDataByUuids(ctx context.Context, in *ArtistsDataByUuidsReq, opts ...grpc_go.CallOption) (*ArtistsDataByUuidsResp, common.ErrorWithAttachment) {
	out := new(ArtistsDataByUuidsResp)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/ArtistsDataByUuids", in, out)
}

// ArtworkQueryServer is the server API for ArtworkQuery service.
// All implementations must embed UnimplementedArtworkQueryServer
// for forward compatibility
type ArtworkQueryServer interface {
	ArtworkList(context.Context, *ArtworkListRequest) (*ArtworkListResponse, error)
	DelArtwork(context.Context, *DelAwRequest) (*DelAwResponse, error)
	DelAuthData(context.Context, *DelAuthDataRequest) (*DelAuthDataResponse, error)
	DelMarketData(context.Context, *DelMarketDataRequest) (*DelMarketDataResponse, error)
	DelStorageData(context.Context, *DelStorageDataRequest) (*DelStorageDataResponse, error)
	TagsList(context.Context, *TagsListRequest) (*TagsListResponse, error)
	CatList(context.Context, *CatListRequest) (*CatListResponse, error)
	ImgMatchByUuid(context.Context, *ImgMatchRequest) (*ImgMatchResponse, error)
	BatchBitMap(context.Context, *BatchBitMapRequest) (*BatchBitMapResponse, error)
	CheckArtworkName(context.Context, *CheckArtworkNameRequest) (*CheckArtworkNameResponse, error)
	CheckArtworkTfnum(context.Context, *CheckArtworkTfnumRequest) (*CheckArtworkTfnumResponse, error)
	UpdateThirdParty(context.Context, *UpdateThirdPartyRequest) (*UpdateThirdPartyResponse, error)
	DelThirdParty(context.Context, *DelThirdPartyRequest) (*DelThirdPartyResponse, error)
	ThirdPartyList(context.Context, *ThirdPartyListRequest) (*ThirdPartyListResponse, error)
	UpdateAwStockStatus(context.Context, *UpdateAwStockStatusRequest) (*UpdateAwStockStatusResponse, error)
	SyncArtShowId(context.Context, *SyncArtShowIdRequest) (*SyncArtShowIdResponse, error)
	ShelfList(context.Context, *ShelfListRequest) (*ShelfListResponse, error)
	UpdateCopyrightHash(context.Context, *UpdateCopyrightHashRequest) (*UpdateCopyrightHashResponse, error)
	ExportArtwork(context.Context, *ExportArtworkRequest) (*ExportArtworkResponse, error)
	TagIdKvList(context.Context, *TagIdKvListRequest) (*TagIdKvListResponse, error)
	ExportFieldList(context.Context, *ExportFieldListRequest) (*ExportFieldListResponse, error)
	ArtworkDataByShowId(context.Context, *ArtworkDataByShowIdRequest) (*ArtworkDataByShowIdResponse, error)
	MyAwList(context.Context, *MyAwListReq) (*MyAwListResp, error)
	ArtworkPreviewList(context.Context, *ArtworkPreviewListRequest) (*ArtworkPreviewListResponse, error)
	VerifyList(context.Context, *VerifyListReq) (*VerifyListResp, error)
	ArtshowList(context.Context, *ArtshowListReq) (*ArtshowListResp, error)
	CountVerifySimilar(context.Context, *CountVerifySimilarReq) (*CountVerifySimilarResp, error)
	OneQuery(context.Context, *OneQueryReq) (*OneQueryResp, error)
	GetPassArtist(context.Context, *GetPassArtistReq) (*GetPassArtistResp, error)
	FilterAwList(context.Context, *FilterAwListReq) (*FilterAwListResp, error)
	ChainInfoByHash(context.Context, *ChainInfoByHashReq) (*ChainInfoByHashResp, error)
	StockOutArtist(context.Context, *StockOutArtistReq) (*StockOutArtistResp, error)
	ChainList(context.Context, *ChainListReq) (*ChainListResp, error)
	TotalTransfer(context.Context, *emptypb.Empty) (*TotalTransferResp, error)
	GetArtworkProfileList(context.Context, *GetArtworkProfileListRequest) (*GetArtworkProfileListResp, error)
	ArtistBindArtworkList(context.Context, *ArtistBindArtworkListReq) (*ArtistBindArtworkListResp, error)
	GetArtworkCopyrightList(context.Context, *GetArtworkCopyrightListRequest) (*GetArtworkCopyrightListResp, error)
	GetArtworkDataBatch(context.Context, *GetArtworkDataBatchReq) (*GetArtworkDataBatchResp, error)
	BatchDciList(context.Context, *BatchDciListReq) (*BatchDciListResp, error)
	ArtworkArtistList(context.Context, *ArtworkArtistListReq) (*ArtworkArtistListResp, error)
	StorageList(context.Context, *StorageListReq) (*StorageListResp, error)
	SecondArtworkList(context.Context, *SecondArtworkListReq) (*SecondArtworkListResp, error)
	ArtistsDataByUuids(context.Context, *ArtistsDataByUuidsReq) (*ArtistsDataByUuidsResp, error)
	mustEmbedUnimplementedArtworkQueryServer()
}

// UnimplementedArtworkQueryServer must be embedded to have forward compatible implementations.
type UnimplementedArtworkQueryServer struct {
	proxyImpl protocol.Invoker
}

func (UnimplementedArtworkQueryServer) ArtworkList(context.Context, *ArtworkListRequest) (*ArtworkListResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ArtworkList not implemented")
}
func (UnimplementedArtworkQueryServer) DelArtwork(context.Context, *DelAwRequest) (*DelAwResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DelArtwork not implemented")
}
func (UnimplementedArtworkQueryServer) DelAuthData(context.Context, *DelAuthDataRequest) (*DelAuthDataResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DelAuthData not implemented")
}
func (UnimplementedArtworkQueryServer) DelMarketData(context.Context, *DelMarketDataRequest) (*DelMarketDataResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DelMarketData not implemented")
}
func (UnimplementedArtworkQueryServer) DelStorageData(context.Context, *DelStorageDataRequest) (*DelStorageDataResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DelStorageData not implemented")
}
func (UnimplementedArtworkQueryServer) TagsList(context.Context, *TagsListRequest) (*TagsListResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method TagsList not implemented")
}
func (UnimplementedArtworkQueryServer) CatList(context.Context, *CatListRequest) (*CatListResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CatList not implemented")
}
func (UnimplementedArtworkQueryServer) ImgMatchByUuid(context.Context, *ImgMatchRequest) (*ImgMatchResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ImgMatchByUuid not implemented")
}
func (UnimplementedArtworkQueryServer) BatchBitMap(context.Context, *BatchBitMapRequest) (*BatchBitMapResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method BatchBitMap not implemented")
}
func (UnimplementedArtworkQueryServer) CheckArtworkName(context.Context, *CheckArtworkNameRequest) (*CheckArtworkNameResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CheckArtworkName not implemented")
}
func (UnimplementedArtworkQueryServer) CheckArtworkTfnum(context.Context, *CheckArtworkTfnumRequest) (*CheckArtworkTfnumResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CheckArtworkTfnum not implemented")
}
func (UnimplementedArtworkQueryServer) UpdateThirdParty(context.Context, *UpdateThirdPartyRequest) (*UpdateThirdPartyResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateThirdParty not implemented")
}
func (UnimplementedArtworkQueryServer) DelThirdParty(context.Context, *DelThirdPartyRequest) (*DelThirdPartyResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DelThirdParty not implemented")
}
func (UnimplementedArtworkQueryServer) ThirdPartyList(context.Context, *ThirdPartyListRequest) (*ThirdPartyListResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ThirdPartyList not implemented")
}
func (UnimplementedArtworkQueryServer) UpdateAwStockStatus(context.Context, *UpdateAwStockStatusRequest) (*UpdateAwStockStatusResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateAwStockStatus not implemented")
}
func (UnimplementedArtworkQueryServer) SyncArtShowId(context.Context, *SyncArtShowIdRequest) (*SyncArtShowIdResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SyncArtShowId not implemented")
}
func (UnimplementedArtworkQueryServer) ShelfList(context.Context, *ShelfListRequest) (*ShelfListResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ShelfList not implemented")
}
func (UnimplementedArtworkQueryServer) UpdateCopyrightHash(context.Context, *UpdateCopyrightHashRequest) (*UpdateCopyrightHashResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateCopyrightHash not implemented")
}
func (UnimplementedArtworkQueryServer) ExportArtwork(context.Context, *ExportArtworkRequest) (*ExportArtworkResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ExportArtwork not implemented")
}
func (UnimplementedArtworkQueryServer) TagIdKvList(context.Context, *TagIdKvListRequest) (*TagIdKvListResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method TagIdKvList not implemented")
}
func (UnimplementedArtworkQueryServer) ExportFieldList(context.Context, *ExportFieldListRequest) (*ExportFieldListResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ExportFieldList not implemented")
}
func (UnimplementedArtworkQueryServer) ArtworkDataByShowId(context.Context, *ArtworkDataByShowIdRequest) (*ArtworkDataByShowIdResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ArtworkDataByShowId not implemented")
}
func (UnimplementedArtworkQueryServer) MyAwList(context.Context, *MyAwListReq) (*MyAwListResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method MyAwList not implemented")
}
func (UnimplementedArtworkQueryServer) ArtworkPreviewList(context.Context, *ArtworkPreviewListRequest) (*ArtworkPreviewListResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ArtworkPreviewList not implemented")
}
func (UnimplementedArtworkQueryServer) VerifyList(context.Context, *VerifyListReq) (*VerifyListResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method VerifyList not implemented")
}
func (UnimplementedArtworkQueryServer) ArtshowList(context.Context, *ArtshowListReq) (*ArtshowListResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ArtshowList not implemented")
}
func (UnimplementedArtworkQueryServer) CountVerifySimilar(context.Context, *CountVerifySimilarReq) (*CountVerifySimilarResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CountVerifySimilar not implemented")
}
func (UnimplementedArtworkQueryServer) OneQuery(context.Context, *OneQueryReq) (*OneQueryResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method OneQuery not implemented")
}
func (UnimplementedArtworkQueryServer) GetPassArtist(context.Context, *GetPassArtistReq) (*GetPassArtistResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetPassArtist not implemented")
}
func (UnimplementedArtworkQueryServer) FilterAwList(context.Context, *FilterAwListReq) (*FilterAwListResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FilterAwList not implemented")
}
func (UnimplementedArtworkQueryServer) ChainInfoByHash(context.Context, *ChainInfoByHashReq) (*ChainInfoByHashResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ChainInfoByHash not implemented")
}
func (UnimplementedArtworkQueryServer) StockOutArtist(context.Context, *StockOutArtistReq) (*StockOutArtistResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method StockOutArtist not implemented")
}
func (UnimplementedArtworkQueryServer) ChainList(context.Context, *ChainListReq) (*ChainListResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ChainList not implemented")
}
func (UnimplementedArtworkQueryServer) TotalTransfer(context.Context, *emptypb.Empty) (*TotalTransferResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method TotalTransfer not implemented")
}
func (UnimplementedArtworkQueryServer) GetArtworkProfileList(context.Context, *GetArtworkProfileListRequest) (*GetArtworkProfileListResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetArtworkProfileList not implemented")
}
func (UnimplementedArtworkQueryServer) ArtistBindArtworkList(context.Context, *ArtistBindArtworkListReq) (*ArtistBindArtworkListResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ArtistBindArtworkList not implemented")
}
func (UnimplementedArtworkQueryServer) GetArtworkCopyrightList(context.Context, *GetArtworkCopyrightListRequest) (*GetArtworkCopyrightListResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetArtworkCopyrightList not implemented")
}
func (UnimplementedArtworkQueryServer) GetArtworkDataBatch(context.Context, *GetArtworkDataBatchReq) (*GetArtworkDataBatchResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetArtworkDataBatch not implemented")
}
func (UnimplementedArtworkQueryServer) BatchDciList(context.Context, *BatchDciListReq) (*BatchDciListResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method BatchDciList not implemented")
}
func (UnimplementedArtworkQueryServer) ArtworkArtistList(context.Context, *ArtworkArtistListReq) (*ArtworkArtistListResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ArtworkArtistList not implemented")
}
func (UnimplementedArtworkQueryServer) StorageList(context.Context, *StorageListReq) (*StorageListResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method StorageList not implemented")
}
func (UnimplementedArtworkQueryServer) SecondArtworkList(context.Context, *SecondArtworkListReq) (*SecondArtworkListResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SecondArtworkList not implemented")
}
func (UnimplementedArtworkQueryServer) ArtistsDataByUuids(context.Context, *ArtistsDataByUuidsReq) (*ArtistsDataByUuidsResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ArtistsDataByUuids not implemented")
}
func (s *UnimplementedArtworkQueryServer) XXX_SetProxyImpl(impl protocol.Invoker) {
	s.proxyImpl = impl
}

func (s *UnimplementedArtworkQueryServer) XXX_GetProxyImpl() protocol.Invoker {
	return s.proxyImpl
}

func (s *UnimplementedArtworkQueryServer) XXX_ServiceDesc() *grpc_go.ServiceDesc {
	return &ArtworkQuery_ServiceDesc
}
func (s *UnimplementedArtworkQueryServer) XXX_InterfaceName() string {
	return "Artwork.ArtworkQuery"
}

func (UnimplementedArtworkQueryServer) mustEmbedUnimplementedArtworkQueryServer() {}

// UnsafeArtworkQueryServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to ArtworkQueryServer will
// result in compilation errors.
type UnsafeArtworkQueryServer interface {
	mustEmbedUnimplementedArtworkQueryServer()
}

func RegisterArtworkQueryServer(s grpc_go.ServiceRegistrar, srv ArtworkQueryServer) {
	s.RegisterService(&ArtworkQuery_ServiceDesc, srv)
}

func _ArtworkQuery_ArtworkList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(ArtworkListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("ArtworkList", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _ArtworkQuery_DelArtwork_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(DelAwRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("DelArtwork", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _ArtworkQuery_DelAuthData_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(DelAuthDataRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("DelAuthData", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _ArtworkQuery_DelMarketData_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(DelMarketDataRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("DelMarketData", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _ArtworkQuery_DelStorageData_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(DelStorageDataRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("DelStorageData", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _ArtworkQuery_TagsList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(TagsListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("TagsList", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _ArtworkQuery_CatList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(CatListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("CatList", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _ArtworkQuery_ImgMatchByUuid_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(ImgMatchRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("ImgMatchByUuid", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _ArtworkQuery_BatchBitMap_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchBitMapRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("BatchBitMap", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _ArtworkQuery_CheckArtworkName_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(CheckArtworkNameRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("CheckArtworkName", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _ArtworkQuery_CheckArtworkTfnum_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(CheckArtworkTfnumRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("CheckArtworkTfnum", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _ArtworkQuery_UpdateThirdParty_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateThirdPartyRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("UpdateThirdParty", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _ArtworkQuery_DelThirdParty_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(DelThirdPartyRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("DelThirdParty", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _ArtworkQuery_ThirdPartyList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(ThirdPartyListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("ThirdPartyList", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _ArtworkQuery_UpdateAwStockStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateAwStockStatusRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("UpdateAwStockStatus", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _ArtworkQuery_SyncArtShowId_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(SyncArtShowIdRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("SyncArtShowId", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _ArtworkQuery_ShelfList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(ShelfListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("ShelfList", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _ArtworkQuery_UpdateCopyrightHash_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateCopyrightHashRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("UpdateCopyrightHash", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _ArtworkQuery_ExportArtwork_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(ExportArtworkRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("ExportArtwork", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _ArtworkQuery_TagIdKvList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(TagIdKvListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("TagIdKvList", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _ArtworkQuery_ExportFieldList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(ExportFieldListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("ExportFieldList", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _ArtworkQuery_ArtworkDataByShowId_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(ArtworkDataByShowIdRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("ArtworkDataByShowId", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _ArtworkQuery_MyAwList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(MyAwListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("MyAwList", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _ArtworkQuery_ArtworkPreviewList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(ArtworkPreviewListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("ArtworkPreviewList", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _ArtworkQuery_VerifyList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(VerifyListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("VerifyList", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _ArtworkQuery_ArtshowList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(ArtshowListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("ArtshowList", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _ArtworkQuery_CountVerifySimilar_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(CountVerifySimilarReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("CountVerifySimilar", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _ArtworkQuery_OneQuery_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(OneQueryReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("OneQuery", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _ArtworkQuery_GetPassArtist_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetPassArtistReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("GetPassArtist", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _ArtworkQuery_FilterAwList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(FilterAwListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("FilterAwList", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _ArtworkQuery_ChainInfoByHash_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(ChainInfoByHashReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("ChainInfoByHash", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _ArtworkQuery_StockOutArtist_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(StockOutArtistReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("StockOutArtist", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _ArtworkQuery_ChainList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(ChainListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("ChainList", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _ArtworkQuery_TotalTransfer_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(emptypb.Empty)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("TotalTransfer", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _ArtworkQuery_GetArtworkProfileList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetArtworkProfileListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("GetArtworkProfileList", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _ArtworkQuery_ArtistBindArtworkList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(ArtistBindArtworkListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("ArtistBindArtworkList", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _ArtworkQuery_GetArtworkCopyrightList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetArtworkCopyrightListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("GetArtworkCopyrightList", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _ArtworkQuery_GetArtworkDataBatch_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetArtworkDataBatchReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("GetArtworkDataBatch", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _ArtworkQuery_BatchDciList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchDciListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("BatchDciList", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _ArtworkQuery_ArtworkArtistList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(ArtworkArtistListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("ArtworkArtistList", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _ArtworkQuery_StorageList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(StorageListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("StorageList", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _ArtworkQuery_SecondArtworkList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(SecondArtworkListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("SecondArtworkList", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _ArtworkQuery_ArtistsDataByUuids_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(ArtistsDataByUuidsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("ArtistsDataByUuids", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

// ArtworkQuery_ServiceDesc is the grpc_go.ServiceDesc for ArtworkQuery service.
// It's only intended for direct use with grpc_go.RegisterService,
// and not to be introspected or modified (even as a copy)
var ArtworkQuery_ServiceDesc = grpc_go.ServiceDesc{
	ServiceName: "Artwork.ArtworkQuery",
	HandlerType: (*ArtworkQueryServer)(nil),
	Methods: []grpc_go.MethodDesc{
		{
			MethodName: "ArtworkList",
			Handler:    _ArtworkQuery_ArtworkList_Handler,
		},
		{
			MethodName: "DelArtwork",
			Handler:    _ArtworkQuery_DelArtwork_Handler,
		},
		{
			MethodName: "DelAuthData",
			Handler:    _ArtworkQuery_DelAuthData_Handler,
		},
		{
			MethodName: "DelMarketData",
			Handler:    _ArtworkQuery_DelMarketData_Handler,
		},
		{
			MethodName: "DelStorageData",
			Handler:    _ArtworkQuery_DelStorageData_Handler,
		},
		{
			MethodName: "TagsList",
			Handler:    _ArtworkQuery_TagsList_Handler,
		},
		{
			MethodName: "CatList",
			Handler:    _ArtworkQuery_CatList_Handler,
		},
		{
			MethodName: "ImgMatchByUuid",
			Handler:    _ArtworkQuery_ImgMatchByUuid_Handler,
		},
		{
			MethodName: "BatchBitMap",
			Handler:    _ArtworkQuery_BatchBitMap_Handler,
		},
		{
			MethodName: "CheckArtworkName",
			Handler:    _ArtworkQuery_CheckArtworkName_Handler,
		},
		{
			MethodName: "CheckArtworkTfnum",
			Handler:    _ArtworkQuery_CheckArtworkTfnum_Handler,
		},
		{
			MethodName: "UpdateThirdParty",
			Handler:    _ArtworkQuery_UpdateThirdParty_Handler,
		},
		{
			MethodName: "DelThirdParty",
			Handler:    _ArtworkQuery_DelThirdParty_Handler,
		},
		{
			MethodName: "ThirdPartyList",
			Handler:    _ArtworkQuery_ThirdPartyList_Handler,
		},
		{
			MethodName: "UpdateAwStockStatus",
			Handler:    _ArtworkQuery_UpdateAwStockStatus_Handler,
		},
		{
			MethodName: "SyncArtShowId",
			Handler:    _ArtworkQuery_SyncArtShowId_Handler,
		},
		{
			MethodName: "ShelfList",
			Handler:    _ArtworkQuery_ShelfList_Handler,
		},
		{
			MethodName: "UpdateCopyrightHash",
			Handler:    _ArtworkQuery_UpdateCopyrightHash_Handler,
		},
		{
			MethodName: "ExportArtwork",
			Handler:    _ArtworkQuery_ExportArtwork_Handler,
		},
		{
			MethodName: "TagIdKvList",
			Handler:    _ArtworkQuery_TagIdKvList_Handler,
		},
		{
			MethodName: "ExportFieldList",
			Handler:    _ArtworkQuery_ExportFieldList_Handler,
		},
		{
			MethodName: "ArtworkDataByShowId",
			Handler:    _ArtworkQuery_ArtworkDataByShowId_Handler,
		},
		{
			MethodName: "MyAwList",
			Handler:    _ArtworkQuery_MyAwList_Handler,
		},
		{
			MethodName: "ArtworkPreviewList",
			Handler:    _ArtworkQuery_ArtworkPreviewList_Handler,
		},
		{
			MethodName: "VerifyList",
			Handler:    _ArtworkQuery_VerifyList_Handler,
		},
		{
			MethodName: "ArtshowList",
			Handler:    _ArtworkQuery_ArtshowList_Handler,
		},
		{
			MethodName: "CountVerifySimilar",
			Handler:    _ArtworkQuery_CountVerifySimilar_Handler,
		},
		{
			MethodName: "OneQuery",
			Handler:    _ArtworkQuery_OneQuery_Handler,
		},
		{
			MethodName: "GetPassArtist",
			Handler:    _ArtworkQuery_GetPassArtist_Handler,
		},
		{
			MethodName: "FilterAwList",
			Handler:    _ArtworkQuery_FilterAwList_Handler,
		},
		{
			MethodName: "ChainInfoByHash",
			Handler:    _ArtworkQuery_ChainInfoByHash_Handler,
		},
		{
			MethodName: "StockOutArtist",
			Handler:    _ArtworkQuery_StockOutArtist_Handler,
		},
		{
			MethodName: "ChainList",
			Handler:    _ArtworkQuery_ChainList_Handler,
		},
		{
			MethodName: "TotalTransfer",
			Handler:    _ArtworkQuery_TotalTransfer_Handler,
		},
		{
			MethodName: "GetArtworkProfileList",
			Handler:    _ArtworkQuery_GetArtworkProfileList_Handler,
		},
		{
			MethodName: "ArtistBindArtworkList",
			Handler:    _ArtworkQuery_ArtistBindArtworkList_Handler,
		},
		{
			MethodName: "GetArtworkCopyrightList",
			Handler:    _ArtworkQuery_GetArtworkCopyrightList_Handler,
		},
		{
			MethodName: "GetArtworkDataBatch",
			Handler:    _ArtworkQuery_GetArtworkDataBatch_Handler,
		},
		{
			MethodName: "BatchDciList",
			Handler:    _ArtworkQuery_BatchDciList_Handler,
		},
		{
			MethodName: "ArtworkArtistList",
			Handler:    _ArtworkQuery_ArtworkArtistList_Handler,
		},
		{
			MethodName: "StorageList",
			Handler:    _ArtworkQuery_StorageList_Handler,
		},
		{
			MethodName: "SecondArtworkList",
			Handler:    _ArtworkQuery_SecondArtworkList_Handler,
		},
		{
			MethodName: "ArtistsDataByUuids",
			Handler:    _ArtworkQuery_ArtistsDataByUuids_Handler,
		},
	},
	Streams:  []grpc_go.StreamDesc{},
	Metadata: "pb/artwork_query.proto",
}
