// Code generated by protoc-gen-gogo. DO NOT EDIT.
// source: pb/artwork_query.proto

//protoc --proto_path=. --go_out=. --go-triple_out=. --validate_out="lang=go:." ./pb/artwork_query.proto

package artwork_query

import (
	fmt "fmt"
	math "math"
	proto "github.com/golang/protobuf/proto"
	_ "github.com/mwitkow/go-proto-validators"
	_ "google.golang.org/protobuf/types/known/emptypb"
	_ "google.golang.org/protobuf/types/descriptorpb"
	_ "google.golang.org/protobuf/types/known/wrapperspb"
	github_com_mwitkow_go_proto_validators "github.com/mwitkow/go-proto-validators"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

func (this *ArtworkListRequest) Validate() error {
	if this.StorageStatus != nil {
		if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(this.StorageStatus); err != nil {
			return github_com_mwitkow_go_proto_validators.FieldError("StorageStatus", err)
		}
	}
	if this.SaleStatus != nil {
		if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(this.SaleStatus); err != nil {
			return github_com_mwitkow_go_proto_validators.FieldError("SaleStatus", err)
		}
	}
	if this.FilterStatus != nil {
		if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(this.FilterStatus); err != nil {
			return github_com_mwitkow_go_proto_validators.FieldError("FilterStatus", err)
		}
	}
	return nil
}
func (this *ArtworkListResponse) Validate() error {
	for _, item := range this.Data {
		if item != nil {
			if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(item); err != nil {
				return github_com_mwitkow_go_proto_validators.FieldError("Data", err)
			}
		}
	}
	return nil
}
func (this *ArtworkListResponse_Info) Validate() error {
	return nil
}
func (this *DelAwRequest) Validate() error {
	if this.ArtworkUuid == "" {
		return github_com_mwitkow_go_proto_validators.FieldError("ArtworkUuid", fmt.Errorf(`请选择画作`))
	}
	return nil
}
func (this *DelAwResponse) Validate() error {
	return nil
}
func (this *DelAuthDataRequest) Validate() error {
	return nil
}
func (this *DelAuthDataResponse) Validate() error {
	return nil
}
func (this *DelMarketDataRequest) Validate() error {
	return nil
}
func (this *DelMarketDataResponse) Validate() error {
	return nil
}
func (this *DelStorageDataRequest) Validate() error {
	return nil
}
func (this *DelStorageDataResponse) Validate() error {
	return nil
}
func (this *TagsListRequest) Validate() error {
	return nil
}
func (this *TagsData) Validate() error {
	if this.TagsFirst != nil {
		if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(this.TagsFirst); err != nil {
			return github_com_mwitkow_go_proto_validators.FieldError("TagsFirst", err)
		}
	}
	for _, item := range this.List {
		if item != nil {
			if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(item); err != nil {
				return github_com_mwitkow_go_proto_validators.FieldError("List", err)
			}
		}
	}
	return nil
}
func (this *TagsData_TagsInfo) Validate() error {
	return nil
}
func (this *TagsListResponse) Validate() error {
	for _, item := range this.TagsData {
		if item != nil {
			if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(item); err != nil {
				return github_com_mwitkow_go_proto_validators.FieldError("TagsData", err)
			}
		}
	}
	return nil
}
func (this *CatListRequest) Validate() error {
	return nil
}
func (this *CatListResponse) Validate() error {
	for _, item := range this.Data {
		if item != nil {
			if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(item); err != nil {
				return github_com_mwitkow_go_proto_validators.FieldError("Data", err)
			}
		}
	}
	return nil
}
func (this *CatListResponse_CatInfo) Validate() error {
	return nil
}
func (this *ImgMatchRequest) Validate() error {
	if this.ImgUrl == "" {
		return github_com_mwitkow_go_proto_validators.FieldError("ImgUrl", fmt.Errorf(`图片不能为空`))
	}
	if !(this.UseType > 0) {
		return github_com_mwitkow_go_proto_validators.FieldError("UseType", fmt.Errorf(`用途不能为空`))
	}
	return nil
}
func (this *ImgMatchResponse) Validate() error {
	return nil
}
func (this *BatchBitMapRequest) Validate() error {
	for _, item := range this.BitData {
		if item != nil {
			if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(item); err != nil {
				return github_com_mwitkow_go_proto_validators.FieldError("BitData", err)
			}
		}
	}
	return nil
}
func (this *BatchBitMapRequest_BitInfo) Validate() error {
	return nil
}
func (this *BatchBitMapResponse) Validate() error {
	return nil
}
func (this *CheckArtworkNameRequest) Validate() error {
	if this.ArtworkName == "" {
		return github_com_mwitkow_go_proto_validators.FieldError("ArtworkName", fmt.Errorf(`画作名不能为空`))
	}
	return nil
}
func (this *CheckArtworkNameResponse) Validate() error {
	return nil
}
func (this *CheckArtworkTfnumRequest) Validate() error {
	if this.Tfnum == "" {
		return github_com_mwitkow_go_proto_validators.FieldError("Tfnum", fmt.Errorf(`编号不能为空`))
	}
	return nil
}
func (this *CheckArtworkTfnumResponse) Validate() error {
	return nil
}
func (this *UpdateThirdPartyRequest) Validate() error {
	if this.ArtworkUuid == "" {
		return github_com_mwitkow_go_proto_validators.FieldError("ArtworkUuid", fmt.Errorf(`请选择画作`))
	}
	if this.ThirdComment == "" {
		return github_com_mwitkow_go_proto_validators.FieldError("ThirdComment", fmt.Errorf(`属性值不能为空`))
	}
	return nil
}
func (this *UpdateThirdPartyResponse) Validate() error {
	return nil
}
func (this *DelThirdPartyRequest) Validate() error {
	return nil
}
func (this *DelThirdPartyResponse) Validate() error {
	return nil
}
func (this *ThirdPartyListRequest) Validate() error {
	if this.ArtworkUuid == "" {
		return github_com_mwitkow_go_proto_validators.FieldError("ArtworkUuid", fmt.Errorf(`请选择画作`))
	}
	return nil
}
func (this *ThirdPartyListResponse) Validate() error {
	return nil
}
func (this *UpdateAwStockStatusRequest) Validate() error {
	return nil
}
func (this *UpdateAwStockStatusResponse) Validate() error {
	return nil
}
func (this *SyncArtShowIdRequest) Validate() error {
	for _, item := range this.Data {
		if item != nil {
			if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(item); err != nil {
				return github_com_mwitkow_go_proto_validators.FieldError("Data", err)
			}
		}
	}
	return nil
}
func (this *SyncArtShowIdRequestInfo) Validate() error {
	return nil
}
func (this *SyncArtShowIdResponse) Validate() error {
	return nil
}
func (this *ShelfListRequest) Validate() error {
	return nil
}
func (this *ShelfListResponse) Validate() error {
	for _, item := range this.Data {
		if item != nil {
			if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(item); err != nil {
				return github_com_mwitkow_go_proto_validators.FieldError("Data", err)
			}
		}
	}
	return nil
}
func (this *ShelfListResponse_ShelfInfo) Validate() error {
	return nil
}
func (this *UpdateCopyrightHashRequest) Validate() error {
	if this.ArtworkUuid == "" {
		return github_com_mwitkow_go_proto_validators.FieldError("ArtworkUuid", fmt.Errorf(`请选择画家`))
	}
	if this.CopyrightPath == "" {
		return github_com_mwitkow_go_proto_validators.FieldError("CopyrightPath", fmt.Errorf(`版权图路径不能为空`))
	}
	return nil
}
func (this *UpdateCopyrightHashResponse) Validate() error {
	return nil
}
func (this *ExportArtworkRequest) Validate() error {
	if this.ColumnId == "" {
		return github_com_mwitkow_go_proto_validators.FieldError("ColumnId", fmt.Errorf(`请选择字段`))
	}
	return nil
}
func (this *ExportArtworkResponse) Validate() error {
	for _, item := range this.Data {
		if item != nil {
			if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(item); err != nil {
				return github_com_mwitkow_go_proto_validators.FieldError("Data", err)
			}
		}
	}
	return nil
}
func (this *ExportArtworkResponse_Info) Validate() error {
	return nil
}
func (this *TagIdKvListRequest) Validate() error {
	return nil
}
func (this *TagIdKvListResponse) Validate() error {
	// Validation of proto3 map<> fields is unsupported.
	return nil
}
func (this *ExportFieldListRequest) Validate() error {
	if !(this.ExportType > 0) {
		return github_com_mwitkow_go_proto_validators.FieldError("ExportType", fmt.Errorf(`导出类型必须大于0`))
	}
	return nil
}
func (this *ExportFieldListResponse) Validate() error {
	for _, item := range this.Data {
		if item != nil {
			if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(item); err != nil {
				return github_com_mwitkow_go_proto_validators.FieldError("Data", err)
			}
		}
	}
	return nil
}
func (this *ExportFieldListResponse_Info) Validate() error {
	return nil
}
func (this *ArtworkDataByShowIdRequest) Validate() error {
	return nil
}
func (this *ArtworkDataByShowIdResponse) Validate() error {
	for _, item := range this.Data {
		if item != nil {
			if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(item); err != nil {
				return github_com_mwitkow_go_proto_validators.FieldError("Data", err)
			}
		}
	}
	return nil
}
func (this *ArtworkDataByShowIdResponse_Info) Validate() error {
	return nil
}
func (this *MyAwListReq) Validate() error {
	return nil
}
func (this *MyAwListResp) Validate() error {
	for _, item := range this.Data {
		if item != nil {
			if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(item); err != nil {
				return github_com_mwitkow_go_proto_validators.FieldError("Data", err)
			}
		}
	}
	return nil
}
func (this *MyAwListResp_Info) Validate() error {
	return nil
}
func (this *PageInfo) Validate() error {
	return nil
}
func (this *ArtworkPreviewListRequest) Validate() error {
	return nil
}
func (this *ArtworkPreviewListResponse) Validate() error {
	for _, item := range this.Data {
		if item != nil {
			if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(item); err != nil {
				return github_com_mwitkow_go_proto_validators.FieldError("Data", err)
			}
		}
	}
	if this.Page != nil {
		if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(this.Page); err != nil {
			return github_com_mwitkow_go_proto_validators.FieldError("Page", err)
		}
	}
	return nil
}
func (this *ArtworkPreviewResponse) Validate() error {
	return nil
}
func (this *VerifyListReq) Validate() error {
	return nil
}
func (this *VerifyListResp) Validate() error {
	// Validation of proto3 map<> fields is unsupported.
	return nil
}
func (this *VerifyListResp_RateImg) Validate() error {
	for _, item := range this.Data {
		if item != nil {
			if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(item); err != nil {
				return github_com_mwitkow_go_proto_validators.FieldError("Data", err)
			}
		}
	}
	return nil
}
func (this *VerifyListResp_RateImg_Info) Validate() error {
	return nil
}
func (this *VerifyListResp_Info) Validate() error {
	// Validation of proto3 map<> fields is unsupported.
	return nil
}
func (this *ArtshowListReq) Validate() error {
	return nil
}
func (this *ArtshowListResp) Validate() error {
	for _, item := range this.Data {
		if item != nil {
			if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(item); err != nil {
				return github_com_mwitkow_go_proto_validators.FieldError("Data", err)
			}
		}
	}
	return nil
}
func (this *ArtshowListResp_Info) Validate() error {
	for _, item := range this.InviteData {
		if item != nil {
			if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(item); err != nil {
				return github_com_mwitkow_go_proto_validators.FieldError("InviteData", err)
			}
		}
	}
	return nil
}
func (this *ArtshowListResp_Info_InviteInfo) Validate() error {
	return nil
}
func (this *CountVerifySimilarReq) Validate() error {
	return nil
}
func (this *CountVerifySimilarResp) Validate() error {
	return nil
}
func (this *OneQueryReq) Validate() error {
	return nil
}
func (this *OneQueryResp) Validate() error {
	for _, item := range this.Data {
		if item != nil {
			if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(item); err != nil {
				return github_com_mwitkow_go_proto_validators.FieldError("Data", err)
			}
		}
	}
	return nil
}
func (this *OneQueryResp_Info) Validate() error {
	return nil
}
func (this *GetPassArtistReq) Validate() error {
	return nil
}
func (this *GetPassArtistResp) Validate() error {
	return nil
}
func (this *FilterAwListReq) Validate() error {
	if this.ArtistUuid == "" {
		return github_com_mwitkow_go_proto_validators.FieldError("ArtistUuid", fmt.Errorf(`请选择画家`))
	}
	return nil
}
func (this *FilterAwListResp) Validate() error {
	for _, item := range this.Data {
		if item != nil {
			if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(item); err != nil {
				return github_com_mwitkow_go_proto_validators.FieldError("Data", err)
			}
		}
	}
	return nil
}
func (this *FilterAwListResp_Info) Validate() error {
	return nil
}
func (this *ChainInfoByHashReq) Validate() error {
	return nil
}
func (this *ChainInfoByHashResp) Validate() error {
	for _, item := range this.Data {
		if item != nil {
			if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(item); err != nil {
				return github_com_mwitkow_go_proto_validators.FieldError("Data", err)
			}
		}
	}
	return nil
}
func (this *ChainInfoByHashResp_Info) Validate() error {
	return nil
}
func (this *StockOutArtistReq) Validate() error {
	return nil
}
func (this *StockOutArtistResp) Validate() error {
	return nil
}
func (this *ChainListReq) Validate() error {
	return nil
}
func (this *ChainListResp) Validate() error {
	for _, item := range this.Data {
		if item != nil {
			if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(item); err != nil {
				return github_com_mwitkow_go_proto_validators.FieldError("Data", err)
			}
		}
	}
	return nil
}
func (this *ChainListResp_Info) Validate() error {
	return nil
}
func (this *TotalTransferResp) Validate() error {
	return nil
}
func (this *ArtworkProfileData) Validate() error {
	return nil
}
func (this *GetArtworkProfileListRequest) Validate() error {
	if this.Query != nil {
		if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(this.Query); err != nil {
			return github_com_mwitkow_go_proto_validators.FieldError("Query", err)
		}
	}
	return nil
}
func (this *GetArtworkProfileListResp) Validate() error {
	for _, item := range this.List {
		if item != nil {
			if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(item); err != nil {
				return github_com_mwitkow_go_proto_validators.FieldError("List", err)
			}
		}
	}
	return nil
}
func (this *ArtistBindArtworkListReq) Validate() error {
	return nil
}
func (this *ArtistBindArtworkListResp) Validate() error {
	for _, item := range this.Data {
		if item != nil {
			if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(item); err != nil {
				return github_com_mwitkow_go_proto_validators.FieldError("Data", err)
			}
		}
	}
	return nil
}
func (this *ArtistBindArtworkListResp_Info) Validate() error {
	return nil
}
func (this *ArtworkCopyrightData) Validate() error {
	return nil
}
func (this *GetArtworkCopyrightListRequest) Validate() error {
	if this.Query != nil {
		if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(this.Query); err != nil {
			return github_com_mwitkow_go_proto_validators.FieldError("Query", err)
		}
	}
	return nil
}
func (this *GetArtworkCopyrightListResp) Validate() error {
	for _, item := range this.List {
		if item != nil {
			if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(item); err != nil {
				return github_com_mwitkow_go_proto_validators.FieldError("List", err)
			}
		}
	}
	return nil
}
func (this *GetArtworkDataBatchReq) Validate() error {
	return nil
}
func (this *GetArtworkDataBatchResp) Validate() error {
	for _, item := range this.Data {
		if item != nil {
			if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(item); err != nil {
				return github_com_mwitkow_go_proto_validators.FieldError("Data", err)
			}
		}
	}
	return nil
}
func (this *GetArtworkDataBatchResp_ArtworkInfo) Validate() error {
	return nil
}
func (this *BatchDciListReq) Validate() error {
	return nil
}
func (this *BatchDciListResp) Validate() error {
	for _, item := range this.Data {
		if item != nil {
			if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(item); err != nil {
				return github_com_mwitkow_go_proto_validators.FieldError("Data", err)
			}
		}
	}
	return nil
}
func (this *BatchDciListResp_Info) Validate() error {
	return nil
}
func (this *ArtworkArtistListReq) Validate() error {
	return nil
}
func (this *ArtworkArtistListResp) Validate() error {
	for _, item := range this.Data {
		if item != nil {
			if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(item); err != nil {
				return github_com_mwitkow_go_proto_validators.FieldError("Data", err)
			}
		}
	}
	return nil
}
func (this *ArtworkArtistListResp_Info) Validate() error {
	return nil
}
func (this *StorageListReq) Validate() error {
	return nil
}
func (this *StorageListResp) Validate() error {
	for _, item := range this.Data {
		if item != nil {
			if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(item); err != nil {
				return github_com_mwitkow_go_proto_validators.FieldError("Data", err)
			}
		}
	}
	return nil
}
func (this *StorageListResp_Info) Validate() error {
	return nil
}
func (this *SecondArtworkListReq) Validate() error {
	return nil
}
func (this *SecondArtworkListResp) Validate() error {
	for _, item := range this.Data {
		if item != nil {
			if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(item); err != nil {
				return github_com_mwitkow_go_proto_validators.FieldError("Data", err)
			}
		}
	}
	return nil
}
func (this *SecondArtworkListResp_Info) Validate() error {
	return nil
}
func (this *ArtistsDataByUuidsReq) Validate() error {
	return nil
}
func (this *ArtistsDataByUuidsResp) Validate() error {
	for _, item := range this.Data {
		if item != nil {
			if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(item); err != nil {
				return github_com_mwitkow_go_proto_validators.FieldError("Data", err)
			}
		}
	}
	return nil
}
func (this *ArtistsDataByUuidsResp_Info) Validate() error {
	return nil
}
