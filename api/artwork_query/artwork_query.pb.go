// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.35.2
// 	protoc        v3.21.1
// source: pb/artwork_query.proto

//protoc --proto_path=. --go_out=. --go-triple_out=. --validate_out="lang=go:." ./pb/artwork_query.proto

package artwork_query

import (
	_ "github.com/mwitkow/go-proto-validators"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	_ "google.golang.org/protobuf/types/descriptorpb"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
	wrapperspb "google.golang.org/protobuf/types/known/wrapperspb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// ArtworkList
type ArtworkListRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Keyword  string `protobuf:"bytes,1,opt,name=Keyword,json=keyword,proto3" json:"Keyword"`
	Page     int32  `protobuf:"varint,2,opt,name=Page,json=page,proto3" json:"Page"`
	PageSize int32  `protobuf:"varint,3,opt,name=PageSize,json=page_size,proto3" json:"PageSize"`
	// int32 StorageStatus                = 4 [json_name = "storage_status"];
	StorageStatus   *wrapperspb.Int32Value `protobuf:"bytes,4,opt,name=StorageStatus,json=storage_status,proto3" json:"StorageStatus"`
	IsOver          int32                  `protobuf:"varint,5,opt,name=IsOver,json=is_over,proto3" json:"IsOver"`
	AdminId         int32                  `protobuf:"varint,6,opt,name=AdminId,json=admin_id,proto3" json:"AdminId"`
	ArtistUid       string                 `protobuf:"bytes,7,opt,name=ArtistUid,json=artist_uid,proto3" json:"ArtistUid"`
	InArtShow       int32                  `protobuf:"varint,8,opt,name=InArtShow,json=in_artshow,proto3" json:"InArtShow"`
	SaleStatus      *wrapperspb.Int32Value `protobuf:"bytes,9,opt,name=SaleStatus,json=sale_status,proto3" json:"SaleStatus"`
	Mask            int32                  `protobuf:"varint,10,opt,name=Mask,json=mask,proto3" json:"Mask"`
	SaleStatusMul   []int32                `protobuf:"varint,11,rep,packed,name=SaleStatusMul,json=sale_status_mul,proto3" json:"SaleStatusMul"`
	ArtworkType     []int32                `protobuf:"varint,12,rep,packed,name=ArtworkType,json=artwork_type,proto3" json:"ArtworkType"`
	SigndateOver    int32                  `protobuf:"varint,13,opt,name=SigndateOver,json=signdate_over,proto3" json:"SigndateOver"`
	Flag            int32                  `protobuf:"varint,14,opt,name=Flag,json=flag,proto3" json:"Flag"` // 1 画作系统 2 二手画系统
	ArtworkUuids    []string               `protobuf:"bytes,15,rep,name=ArtworkUuids,proto3" json:"ArtworkUuids"`
	InStorageStart  string                 `protobuf:"bytes,16,opt,name=InStorageStart,proto3" json:"InStorageStart"`
	InStorageEnd    string                 `protobuf:"bytes,17,opt,name=InStorageEnd,proto3" json:"InStorageEnd"`
	FilterStatus    *wrapperspb.Int32Value `protobuf:"bytes,18,opt,name=FilterStatus,proto3" json:"FilterStatus"`
	RecheckState    int32                  `protobuf:"varint,19,opt,name=RecheckState,proto3" json:"RecheckState"`
	OrderBy         string                 `protobuf:"bytes,20,opt,name=OrderBy,proto3" json:"OrderBy"`
	Sort            string                 `protobuf:"bytes,21,opt,name=Sort,proto3" json:"Sort"`
	StartRuler      int32                  `protobuf:"varint,22,opt,name=StartRuler,proto3" json:"StartRuler"`
	EndRuler        int32                  `protobuf:"varint,23,opt,name=EndRuler,proto3" json:"EndRuler"`
	CopyrightStatus string                 `protobuf:"bytes,24,opt,name=CopyrightStatus,proto3" json:"CopyrightStatus"`
	SaleDateStart   string                 `protobuf:"bytes,25,opt,name=SaleDateStart,proto3" json:"SaleDateStart"`
	SaleDateEnd     string                 `protobuf:"bytes,26,opt,name=SaleDateEnd,proto3" json:"SaleDateEnd"`
	Tfnum           string                 `protobuf:"bytes,27,opt,name=Tfnum,proto3" json:"Tfnum"`
	ArtworkName     string                 `protobuf:"bytes,28,opt,name=ArtworkName,proto3" json:"ArtworkName"`
}

func (x *ArtworkListRequest) Reset() {
	*x = ArtworkListRequest{}
	mi := &file_pb_artwork_query_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ArtworkListRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ArtworkListRequest) ProtoMessage() {}

func (x *ArtworkListRequest) ProtoReflect() protoreflect.Message {
	mi := &file_pb_artwork_query_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ArtworkListRequest.ProtoReflect.Descriptor instead.
func (*ArtworkListRequest) Descriptor() ([]byte, []int) {
	return file_pb_artwork_query_proto_rawDescGZIP(), []int{0}
}

func (x *ArtworkListRequest) GetKeyword() string {
	if x != nil {
		return x.Keyword
	}
	return ""
}

func (x *ArtworkListRequest) GetPage() int32 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *ArtworkListRequest) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *ArtworkListRequest) GetStorageStatus() *wrapperspb.Int32Value {
	if x != nil {
		return x.StorageStatus
	}
	return nil
}

func (x *ArtworkListRequest) GetIsOver() int32 {
	if x != nil {
		return x.IsOver
	}
	return 0
}

func (x *ArtworkListRequest) GetAdminId() int32 {
	if x != nil {
		return x.AdminId
	}
	return 0
}

func (x *ArtworkListRequest) GetArtistUid() string {
	if x != nil {
		return x.ArtistUid
	}
	return ""
}

func (x *ArtworkListRequest) GetInArtShow() int32 {
	if x != nil {
		return x.InArtShow
	}
	return 0
}

func (x *ArtworkListRequest) GetSaleStatus() *wrapperspb.Int32Value {
	if x != nil {
		return x.SaleStatus
	}
	return nil
}

func (x *ArtworkListRequest) GetMask() int32 {
	if x != nil {
		return x.Mask
	}
	return 0
}

func (x *ArtworkListRequest) GetSaleStatusMul() []int32 {
	if x != nil {
		return x.SaleStatusMul
	}
	return nil
}

func (x *ArtworkListRequest) GetArtworkType() []int32 {
	if x != nil {
		return x.ArtworkType
	}
	return nil
}

func (x *ArtworkListRequest) GetSigndateOver() int32 {
	if x != nil {
		return x.SigndateOver
	}
	return 0
}

func (x *ArtworkListRequest) GetFlag() int32 {
	if x != nil {
		return x.Flag
	}
	return 0
}

func (x *ArtworkListRequest) GetArtworkUuids() []string {
	if x != nil {
		return x.ArtworkUuids
	}
	return nil
}

func (x *ArtworkListRequest) GetInStorageStart() string {
	if x != nil {
		return x.InStorageStart
	}
	return ""
}

func (x *ArtworkListRequest) GetInStorageEnd() string {
	if x != nil {
		return x.InStorageEnd
	}
	return ""
}

func (x *ArtworkListRequest) GetFilterStatus() *wrapperspb.Int32Value {
	if x != nil {
		return x.FilterStatus
	}
	return nil
}

func (x *ArtworkListRequest) GetRecheckState() int32 {
	if x != nil {
		return x.RecheckState
	}
	return 0
}

func (x *ArtworkListRequest) GetOrderBy() string {
	if x != nil {
		return x.OrderBy
	}
	return ""
}

func (x *ArtworkListRequest) GetSort() string {
	if x != nil {
		return x.Sort
	}
	return ""
}

func (x *ArtworkListRequest) GetStartRuler() int32 {
	if x != nil {
		return x.StartRuler
	}
	return 0
}

func (x *ArtworkListRequest) GetEndRuler() int32 {
	if x != nil {
		return x.EndRuler
	}
	return 0
}

func (x *ArtworkListRequest) GetCopyrightStatus() string {
	if x != nil {
		return x.CopyrightStatus
	}
	return ""
}

func (x *ArtworkListRequest) GetSaleDateStart() string {
	if x != nil {
		return x.SaleDateStart
	}
	return ""
}

func (x *ArtworkListRequest) GetSaleDateEnd() string {
	if x != nil {
		return x.SaleDateEnd
	}
	return ""
}

func (x *ArtworkListRequest) GetTfnum() string {
	if x != nil {
		return x.Tfnum
	}
	return ""
}

func (x *ArtworkListRequest) GetArtworkName() string {
	if x != nil {
		return x.ArtworkName
	}
	return ""
}

type ArtworkListResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Data     []*ArtworkListResponse_Info `protobuf:"bytes,1,rep,name=Data,json=data,proto3" json:"Data"`
	Count    int32                       `protobuf:"varint,2,opt,name=Count,json=count,proto3" json:"Count"`
	Page     int32                       `protobuf:"varint,3,opt,name=Page,json=page,proto3" json:"Page"`
	PageSize int32                       `protobuf:"varint,4,opt,name=PageSize,json=page_size,proto3" json:"PageSize"`
	Msg      string                      `protobuf:"bytes,5,opt,name=Msg,json=message,proto3" json:"Msg"`
}

func (x *ArtworkListResponse) Reset() {
	*x = ArtworkListResponse{}
	mi := &file_pb_artwork_query_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ArtworkListResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ArtworkListResponse) ProtoMessage() {}

func (x *ArtworkListResponse) ProtoReflect() protoreflect.Message {
	mi := &file_pb_artwork_query_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ArtworkListResponse.ProtoReflect.Descriptor instead.
func (*ArtworkListResponse) Descriptor() ([]byte, []int) {
	return file_pb_artwork_query_proto_rawDescGZIP(), []int{1}
}

func (x *ArtworkListResponse) GetData() []*ArtworkListResponse_Info {
	if x != nil {
		return x.Data
	}
	return nil
}

func (x *ArtworkListResponse) GetCount() int32 {
	if x != nil {
		return x.Count
	}
	return 0
}

func (x *ArtworkListResponse) GetPage() int32 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *ArtworkListResponse) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *ArtworkListResponse) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

// DelArtwork
type DelAwRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ArtworkUuid string `protobuf:"bytes,1,opt,name=ArtworkUuid,json=artwork_uuid,proto3" json:"ArtworkUuid"`
}

func (x *DelAwRequest) Reset() {
	*x = DelAwRequest{}
	mi := &file_pb_artwork_query_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DelAwRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DelAwRequest) ProtoMessage() {}

func (x *DelAwRequest) ProtoReflect() protoreflect.Message {
	mi := &file_pb_artwork_query_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DelAwRequest.ProtoReflect.Descriptor instead.
func (*DelAwRequest) Descriptor() ([]byte, []int) {
	return file_pb_artwork_query_proto_rawDescGZIP(), []int{2}
}

func (x *DelAwRequest) GetArtworkUuid() string {
	if x != nil {
		return x.ArtworkUuid
	}
	return ""
}

type DelAwResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Msg string `protobuf:"bytes,1,opt,name=Msg,json=msg,proto3" json:"Msg"`
}

func (x *DelAwResponse) Reset() {
	*x = DelAwResponse{}
	mi := &file_pb_artwork_query_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DelAwResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DelAwResponse) ProtoMessage() {}

func (x *DelAwResponse) ProtoReflect() protoreflect.Message {
	mi := &file_pb_artwork_query_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DelAwResponse.ProtoReflect.Descriptor instead.
func (*DelAwResponse) Descriptor() ([]byte, []int) {
	return file_pb_artwork_query_proto_rawDescGZIP(), []int{3}
}

func (x *DelAwResponse) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

// DelAuthData
type DelAuthDataRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ids []int32 `protobuf:"varint,1,rep,packed,name=Ids,json=ids,proto3" json:"Ids"`
}

func (x *DelAuthDataRequest) Reset() {
	*x = DelAuthDataRequest{}
	mi := &file_pb_artwork_query_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DelAuthDataRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DelAuthDataRequest) ProtoMessage() {}

func (x *DelAuthDataRequest) ProtoReflect() protoreflect.Message {
	mi := &file_pb_artwork_query_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DelAuthDataRequest.ProtoReflect.Descriptor instead.
func (*DelAuthDataRequest) Descriptor() ([]byte, []int) {
	return file_pb_artwork_query_proto_rawDescGZIP(), []int{4}
}

func (x *DelAuthDataRequest) GetIds() []int32 {
	if x != nil {
		return x.Ids
	}
	return nil
}

type DelAuthDataResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Msg string `protobuf:"bytes,1,opt,name=Msg,json=msg,proto3" json:"Msg"`
}

func (x *DelAuthDataResponse) Reset() {
	*x = DelAuthDataResponse{}
	mi := &file_pb_artwork_query_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DelAuthDataResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DelAuthDataResponse) ProtoMessage() {}

func (x *DelAuthDataResponse) ProtoReflect() protoreflect.Message {
	mi := &file_pb_artwork_query_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DelAuthDataResponse.ProtoReflect.Descriptor instead.
func (*DelAuthDataResponse) Descriptor() ([]byte, []int) {
	return file_pb_artwork_query_proto_rawDescGZIP(), []int{5}
}

func (x *DelAuthDataResponse) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

// DelAuthData
type DelMarketDataRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	MarketIds []int32 `protobuf:"varint,1,rep,packed,name=MarketIds,json=market_ids,proto3" json:"MarketIds"`
}

func (x *DelMarketDataRequest) Reset() {
	*x = DelMarketDataRequest{}
	mi := &file_pb_artwork_query_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DelMarketDataRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DelMarketDataRequest) ProtoMessage() {}

func (x *DelMarketDataRequest) ProtoReflect() protoreflect.Message {
	mi := &file_pb_artwork_query_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DelMarketDataRequest.ProtoReflect.Descriptor instead.
func (*DelMarketDataRequest) Descriptor() ([]byte, []int) {
	return file_pb_artwork_query_proto_rawDescGZIP(), []int{6}
}

func (x *DelMarketDataRequest) GetMarketIds() []int32 {
	if x != nil {
		return x.MarketIds
	}
	return nil
}

type DelMarketDataResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Msg string `protobuf:"bytes,1,opt,name=Msg,json=msg,proto3" json:"Msg"`
}

func (x *DelMarketDataResponse) Reset() {
	*x = DelMarketDataResponse{}
	mi := &file_pb_artwork_query_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DelMarketDataResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DelMarketDataResponse) ProtoMessage() {}

func (x *DelMarketDataResponse) ProtoReflect() protoreflect.Message {
	mi := &file_pb_artwork_query_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DelMarketDataResponse.ProtoReflect.Descriptor instead.
func (*DelMarketDataResponse) Descriptor() ([]byte, []int) {
	return file_pb_artwork_query_proto_rawDescGZIP(), []int{7}
}

func (x *DelMarketDataResponse) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

// DelStorageData
type DelStorageDataRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ids []int32 `protobuf:"varint,1,rep,packed,name=Ids,json=ids,proto3" json:"Ids"`
}

func (x *DelStorageDataRequest) Reset() {
	*x = DelStorageDataRequest{}
	mi := &file_pb_artwork_query_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DelStorageDataRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DelStorageDataRequest) ProtoMessage() {}

func (x *DelStorageDataRequest) ProtoReflect() protoreflect.Message {
	mi := &file_pb_artwork_query_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DelStorageDataRequest.ProtoReflect.Descriptor instead.
func (*DelStorageDataRequest) Descriptor() ([]byte, []int) {
	return file_pb_artwork_query_proto_rawDescGZIP(), []int{8}
}

func (x *DelStorageDataRequest) GetIds() []int32 {
	if x != nil {
		return x.Ids
	}
	return nil
}

type DelStorageDataResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Msg string `protobuf:"bytes,1,opt,name=Msg,json=msg,proto3" json:"Msg"`
}

func (x *DelStorageDataResponse) Reset() {
	*x = DelStorageDataResponse{}
	mi := &file_pb_artwork_query_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DelStorageDataResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DelStorageDataResponse) ProtoMessage() {}

func (x *DelStorageDataResponse) ProtoReflect() protoreflect.Message {
	mi := &file_pb_artwork_query_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DelStorageDataResponse.ProtoReflect.Descriptor instead.
func (*DelStorageDataResponse) Descriptor() ([]byte, []int) {
	return file_pb_artwork_query_proto_rawDescGZIP(), []int{9}
}

func (x *DelStorageDataResponse) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

// TagsList
type TagsListRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *TagsListRequest) Reset() {
	*x = TagsListRequest{}
	mi := &file_pb_artwork_query_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TagsListRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TagsListRequest) ProtoMessage() {}

func (x *TagsListRequest) ProtoReflect() protoreflect.Message {
	mi := &file_pb_artwork_query_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TagsListRequest.ProtoReflect.Descriptor instead.
func (*TagsListRequest) Descriptor() ([]byte, []int) {
	return file_pb_artwork_query_proto_rawDescGZIP(), []int{10}
}

type TagsData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TagsFirst *TagsData_TagsInfo   `protobuf:"bytes,1,opt,name=TagsFirst,json=tags_top,proto3" json:"TagsFirst"`
	List      []*TagsData_TagsInfo `protobuf:"bytes,2,rep,name=List,json=list,proto3" json:"List"`
}

func (x *TagsData) Reset() {
	*x = TagsData{}
	mi := &file_pb_artwork_query_proto_msgTypes[11]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TagsData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TagsData) ProtoMessage() {}

func (x *TagsData) ProtoReflect() protoreflect.Message {
	mi := &file_pb_artwork_query_proto_msgTypes[11]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TagsData.ProtoReflect.Descriptor instead.
func (*TagsData) Descriptor() ([]byte, []int) {
	return file_pb_artwork_query_proto_rawDescGZIP(), []int{11}
}

func (x *TagsData) GetTagsFirst() *TagsData_TagsInfo {
	if x != nil {
		return x.TagsFirst
	}
	return nil
}

func (x *TagsData) GetList() []*TagsData_TagsInfo {
	if x != nil {
		return x.List
	}
	return nil
}

type TagsListResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TagsData []*TagsData `protobuf:"bytes,1,rep,name=TagsData,json=data,proto3" json:"TagsData"`
	Msg      string      `protobuf:"bytes,2,opt,name=Msg,json=msg,proto3" json:"Msg"`
}

func (x *TagsListResponse) Reset() {
	*x = TagsListResponse{}
	mi := &file_pb_artwork_query_proto_msgTypes[12]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TagsListResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TagsListResponse) ProtoMessage() {}

func (x *TagsListResponse) ProtoReflect() protoreflect.Message {
	mi := &file_pb_artwork_query_proto_msgTypes[12]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TagsListResponse.ProtoReflect.Descriptor instead.
func (*TagsListResponse) Descriptor() ([]byte, []int) {
	return file_pb_artwork_query_proto_rawDescGZIP(), []int{12}
}

func (x *TagsListResponse) GetTagsData() []*TagsData {
	if x != nil {
		return x.TagsData
	}
	return nil
}

func (x *TagsListResponse) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

// CatList
type CatListRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Pid  int32   `protobuf:"varint,1,opt,name=Pid,proto3" json:"Pid"`
	Pids []int32 `protobuf:"varint,2,rep,packed,name=Pids,proto3" json:"Pids"`
}

func (x *CatListRequest) Reset() {
	*x = CatListRequest{}
	mi := &file_pb_artwork_query_proto_msgTypes[13]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CatListRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CatListRequest) ProtoMessage() {}

func (x *CatListRequest) ProtoReflect() protoreflect.Message {
	mi := &file_pb_artwork_query_proto_msgTypes[13]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CatListRequest.ProtoReflect.Descriptor instead.
func (*CatListRequest) Descriptor() ([]byte, []int) {
	return file_pb_artwork_query_proto_rawDescGZIP(), []int{13}
}

func (x *CatListRequest) GetPid() int32 {
	if x != nil {
		return x.Pid
	}
	return 0
}

func (x *CatListRequest) GetPids() []int32 {
	if x != nil {
		return x.Pids
	}
	return nil
}

type CatListResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Data []*CatListResponse_CatInfo `protobuf:"bytes,1,rep,name=Data,json=data,proto3" json:"Data"`
	Msg  string                     `protobuf:"bytes,2,opt,name=Msg,json=msg,proto3" json:"Msg"`
}

func (x *CatListResponse) Reset() {
	*x = CatListResponse{}
	mi := &file_pb_artwork_query_proto_msgTypes[14]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CatListResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CatListResponse) ProtoMessage() {}

func (x *CatListResponse) ProtoReflect() protoreflect.Message {
	mi := &file_pb_artwork_query_proto_msgTypes[14]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CatListResponse.ProtoReflect.Descriptor instead.
func (*CatListResponse) Descriptor() ([]byte, []int) {
	return file_pb_artwork_query_proto_rawDescGZIP(), []int{14}
}

func (x *CatListResponse) GetData() []*CatListResponse_CatInfo {
	if x != nil {
		return x.Data
	}
	return nil
}

func (x *CatListResponse) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

// ImgMatchByName
type ImgMatchRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ArtworkName string `protobuf:"bytes,1,opt,name=ArtworkName,json=artwork_name,proto3" json:"ArtworkName"`
	ImgUrl      string `protobuf:"bytes,2,opt,name=ImgUrl,json=img_url,proto3" json:"ImgUrl"`
	UseType     int32  `protobuf:"varint,3,opt,name=UseType,json=use_type,proto3" json:"UseType"`
	ArtworkUuid string `protobuf:"bytes,4,opt,name=ArtworkUuid,json=artwork_uuid,proto3" json:"ArtworkUuid"`
}

func (x *ImgMatchRequest) Reset() {
	*x = ImgMatchRequest{}
	mi := &file_pb_artwork_query_proto_msgTypes[15]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ImgMatchRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ImgMatchRequest) ProtoMessage() {}

func (x *ImgMatchRequest) ProtoReflect() protoreflect.Message {
	mi := &file_pb_artwork_query_proto_msgTypes[15]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ImgMatchRequest.ProtoReflect.Descriptor instead.
func (*ImgMatchRequest) Descriptor() ([]byte, []int) {
	return file_pb_artwork_query_proto_rawDescGZIP(), []int{15}
}

func (x *ImgMatchRequest) GetArtworkName() string {
	if x != nil {
		return x.ArtworkName
	}
	return ""
}

func (x *ImgMatchRequest) GetImgUrl() string {
	if x != nil {
		return x.ImgUrl
	}
	return ""
}

func (x *ImgMatchRequest) GetUseType() int32 {
	if x != nil {
		return x.UseType
	}
	return 0
}

func (x *ImgMatchRequest) GetArtworkUuid() string {
	if x != nil {
		return x.ArtworkUuid
	}
	return ""
}

type ImgMatchResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Msg string `protobuf:"bytes,1,opt,name=Msg,json=msg,proto3" json:"Msg"`
}

func (x *ImgMatchResponse) Reset() {
	*x = ImgMatchResponse{}
	mi := &file_pb_artwork_query_proto_msgTypes[16]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ImgMatchResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ImgMatchResponse) ProtoMessage() {}

func (x *ImgMatchResponse) ProtoReflect() protoreflect.Message {
	mi := &file_pb_artwork_query_proto_msgTypes[16]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ImgMatchResponse.ProtoReflect.Descriptor instead.
func (*ImgMatchResponse) Descriptor() ([]byte, []int) {
	return file_pb_artwork_query_proto_rawDescGZIP(), []int{16}
}

func (x *ImgMatchResponse) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

type BatchBitMapRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	BitData     []*BatchBitMapRequest_BitInfo `protobuf:"bytes,1,rep,name=BitData,proto3" json:"BitData"`
	ArtworkUuid string                        `protobuf:"bytes,2,opt,name=ArtworkUuid,json=artwork_uuid,proto3" json:"ArtworkUuid"`
}

func (x *BatchBitMapRequest) Reset() {
	*x = BatchBitMapRequest{}
	mi := &file_pb_artwork_query_proto_msgTypes[17]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BatchBitMapRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BatchBitMapRequest) ProtoMessage() {}

func (x *BatchBitMapRequest) ProtoReflect() protoreflect.Message {
	mi := &file_pb_artwork_query_proto_msgTypes[17]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BatchBitMapRequest.ProtoReflect.Descriptor instead.
func (*BatchBitMapRequest) Descriptor() ([]byte, []int) {
	return file_pb_artwork_query_proto_rawDescGZIP(), []int{17}
}

func (x *BatchBitMapRequest) GetBitData() []*BatchBitMapRequest_BitInfo {
	if x != nil {
		return x.BitData
	}
	return nil
}

func (x *BatchBitMapRequest) GetArtworkUuid() string {
	if x != nil {
		return x.ArtworkUuid
	}
	return ""
}

type BatchBitMapResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Msg string `protobuf:"bytes,1,opt,name=Msg,json=msg,proto3" json:"Msg"`
}

func (x *BatchBitMapResponse) Reset() {
	*x = BatchBitMapResponse{}
	mi := &file_pb_artwork_query_proto_msgTypes[18]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BatchBitMapResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BatchBitMapResponse) ProtoMessage() {}

func (x *BatchBitMapResponse) ProtoReflect() protoreflect.Message {
	mi := &file_pb_artwork_query_proto_msgTypes[18]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BatchBitMapResponse.ProtoReflect.Descriptor instead.
func (*BatchBitMapResponse) Descriptor() ([]byte, []int) {
	return file_pb_artwork_query_proto_rawDescGZIP(), []int{18}
}

func (x *BatchBitMapResponse) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

// CheckArtworkName
type CheckArtworkNameRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ArtworkName string `protobuf:"bytes,1,opt,name=ArtworkName,json=artwork_name,proto3" json:"ArtworkName"`
}

func (x *CheckArtworkNameRequest) Reset() {
	*x = CheckArtworkNameRequest{}
	mi := &file_pb_artwork_query_proto_msgTypes[19]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CheckArtworkNameRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CheckArtworkNameRequest) ProtoMessage() {}

func (x *CheckArtworkNameRequest) ProtoReflect() protoreflect.Message {
	mi := &file_pb_artwork_query_proto_msgTypes[19]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CheckArtworkNameRequest.ProtoReflect.Descriptor instead.
func (*CheckArtworkNameRequest) Descriptor() ([]byte, []int) {
	return file_pb_artwork_query_proto_rawDescGZIP(), []int{19}
}

func (x *CheckArtworkNameRequest) GetArtworkName() string {
	if x != nil {
		return x.ArtworkName
	}
	return ""
}

type CheckArtworkNameResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ArtworkUuid string `protobuf:"bytes,1,opt,name=ArtworkUuid,json=artwork_uuid,proto3" json:"ArtworkUuid"`
	ArtworkId   int32  `protobuf:"varint,2,opt,name=ArtworkId,json=artwork_id,proto3" json:"ArtworkId"`
	Msg         string `protobuf:"bytes,3,opt,name=Msg,json=msg,proto3" json:"Msg"`
}

func (x *CheckArtworkNameResponse) Reset() {
	*x = CheckArtworkNameResponse{}
	mi := &file_pb_artwork_query_proto_msgTypes[20]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CheckArtworkNameResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CheckArtworkNameResponse) ProtoMessage() {}

func (x *CheckArtworkNameResponse) ProtoReflect() protoreflect.Message {
	mi := &file_pb_artwork_query_proto_msgTypes[20]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CheckArtworkNameResponse.ProtoReflect.Descriptor instead.
func (*CheckArtworkNameResponse) Descriptor() ([]byte, []int) {
	return file_pb_artwork_query_proto_rawDescGZIP(), []int{20}
}

func (x *CheckArtworkNameResponse) GetArtworkUuid() string {
	if x != nil {
		return x.ArtworkUuid
	}
	return ""
}

func (x *CheckArtworkNameResponse) GetArtworkId() int32 {
	if x != nil {
		return x.ArtworkId
	}
	return 0
}

func (x *CheckArtworkNameResponse) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

// CheckArtworkTfnum
type CheckArtworkTfnumRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Tfnum string `protobuf:"bytes,1,opt,name=Tfnum,json=tfnum,proto3" json:"Tfnum"`
}

func (x *CheckArtworkTfnumRequest) Reset() {
	*x = CheckArtworkTfnumRequest{}
	mi := &file_pb_artwork_query_proto_msgTypes[21]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CheckArtworkTfnumRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CheckArtworkTfnumRequest) ProtoMessage() {}

func (x *CheckArtworkTfnumRequest) ProtoReflect() protoreflect.Message {
	mi := &file_pb_artwork_query_proto_msgTypes[21]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CheckArtworkTfnumRequest.ProtoReflect.Descriptor instead.
func (*CheckArtworkTfnumRequest) Descriptor() ([]byte, []int) {
	return file_pb_artwork_query_proto_rawDescGZIP(), []int{21}
}

func (x *CheckArtworkTfnumRequest) GetTfnum() string {
	if x != nil {
		return x.Tfnum
	}
	return ""
}

type CheckArtworkTfnumResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ArtworkUuid string `protobuf:"bytes,1,opt,name=ArtworkUuid,json=artwork_uuid,proto3" json:"ArtworkUuid"`
	ArtworkId   int32  `protobuf:"varint,2,opt,name=ArtworkId,json=artwork_id,proto3" json:"ArtworkId"`
	Msg         string `protobuf:"bytes,3,opt,name=Msg,json=msg,proto3" json:"Msg"`
	ArtistUuid  string `protobuf:"bytes,4,opt,name=ArtistUuid,proto3" json:"ArtistUuid"`
}

func (x *CheckArtworkTfnumResponse) Reset() {
	*x = CheckArtworkTfnumResponse{}
	mi := &file_pb_artwork_query_proto_msgTypes[22]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CheckArtworkTfnumResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CheckArtworkTfnumResponse) ProtoMessage() {}

func (x *CheckArtworkTfnumResponse) ProtoReflect() protoreflect.Message {
	mi := &file_pb_artwork_query_proto_msgTypes[22]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CheckArtworkTfnumResponse.ProtoReflect.Descriptor instead.
func (*CheckArtworkTfnumResponse) Descriptor() ([]byte, []int) {
	return file_pb_artwork_query_proto_rawDescGZIP(), []int{22}
}

func (x *CheckArtworkTfnumResponse) GetArtworkUuid() string {
	if x != nil {
		return x.ArtworkUuid
	}
	return ""
}

func (x *CheckArtworkTfnumResponse) GetArtworkId() int32 {
	if x != nil {
		return x.ArtworkId
	}
	return 0
}

func (x *CheckArtworkTfnumResponse) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *CheckArtworkTfnumResponse) GetArtistUuid() string {
	if x != nil {
		return x.ArtistUuid
	}
	return ""
}

// UpdateThirdParty
type UpdateThirdPartyRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ThirdId      int32  `protobuf:"varint,1,opt,name=ThirdId,json=third_id,proto3" json:"ThirdId"`
	ArtworkUuid  string `protobuf:"bytes,2,opt,name=ArtworkUuid,json=artwork_uuid,proto3" json:"ArtworkUuid"`
	ThirdComment string `protobuf:"bytes,3,opt,name=ThirdComment,json=class_value,proto3" json:"ThirdComment"`
}

func (x *UpdateThirdPartyRequest) Reset() {
	*x = UpdateThirdPartyRequest{}
	mi := &file_pb_artwork_query_proto_msgTypes[23]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateThirdPartyRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateThirdPartyRequest) ProtoMessage() {}

func (x *UpdateThirdPartyRequest) ProtoReflect() protoreflect.Message {
	mi := &file_pb_artwork_query_proto_msgTypes[23]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateThirdPartyRequest.ProtoReflect.Descriptor instead.
func (*UpdateThirdPartyRequest) Descriptor() ([]byte, []int) {
	return file_pb_artwork_query_proto_rawDescGZIP(), []int{23}
}

func (x *UpdateThirdPartyRequest) GetThirdId() int32 {
	if x != nil {
		return x.ThirdId
	}
	return 0
}

func (x *UpdateThirdPartyRequest) GetArtworkUuid() string {
	if x != nil {
		return x.ArtworkUuid
	}
	return ""
}

func (x *UpdateThirdPartyRequest) GetThirdComment() string {
	if x != nil {
		return x.ThirdComment
	}
	return ""
}

type UpdateThirdPartyResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Msg string `protobuf:"bytes,1,opt,name=Msg,json=msg,proto3" json:"Msg"`
}

func (x *UpdateThirdPartyResponse) Reset() {
	*x = UpdateThirdPartyResponse{}
	mi := &file_pb_artwork_query_proto_msgTypes[24]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateThirdPartyResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateThirdPartyResponse) ProtoMessage() {}

func (x *UpdateThirdPartyResponse) ProtoReflect() protoreflect.Message {
	mi := &file_pb_artwork_query_proto_msgTypes[24]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateThirdPartyResponse.ProtoReflect.Descriptor instead.
func (*UpdateThirdPartyResponse) Descriptor() ([]byte, []int) {
	return file_pb_artwork_query_proto_rawDescGZIP(), []int{24}
}

func (x *UpdateThirdPartyResponse) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

// DelThirdParty
type DelThirdPartyRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ThirdIds []int32 `protobuf:"varint,1,rep,packed,name=ThirdIds,json=third_ids,proto3" json:"ThirdIds"`
}

func (x *DelThirdPartyRequest) Reset() {
	*x = DelThirdPartyRequest{}
	mi := &file_pb_artwork_query_proto_msgTypes[25]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DelThirdPartyRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DelThirdPartyRequest) ProtoMessage() {}

func (x *DelThirdPartyRequest) ProtoReflect() protoreflect.Message {
	mi := &file_pb_artwork_query_proto_msgTypes[25]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DelThirdPartyRequest.ProtoReflect.Descriptor instead.
func (*DelThirdPartyRequest) Descriptor() ([]byte, []int) {
	return file_pb_artwork_query_proto_rawDescGZIP(), []int{25}
}

func (x *DelThirdPartyRequest) GetThirdIds() []int32 {
	if x != nil {
		return x.ThirdIds
	}
	return nil
}

type DelThirdPartyResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Msg string `protobuf:"bytes,1,opt,name=Msg,json=msg,proto3" json:"Msg"`
}

func (x *DelThirdPartyResponse) Reset() {
	*x = DelThirdPartyResponse{}
	mi := &file_pb_artwork_query_proto_msgTypes[26]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DelThirdPartyResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DelThirdPartyResponse) ProtoMessage() {}

func (x *DelThirdPartyResponse) ProtoReflect() protoreflect.Message {
	mi := &file_pb_artwork_query_proto_msgTypes[26]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DelThirdPartyResponse.ProtoReflect.Descriptor instead.
func (*DelThirdPartyResponse) Descriptor() ([]byte, []int) {
	return file_pb_artwork_query_proto_rawDescGZIP(), []int{26}
}

func (x *DelThirdPartyResponse) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

// DelThirdPartyList
type ThirdPartyListRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ArtworkUuid string `protobuf:"bytes,1,opt,name=ArtworkUuid,json=artwork_uuid,proto3" json:"ArtworkUuid"`
}

func (x *ThirdPartyListRequest) Reset() {
	*x = ThirdPartyListRequest{}
	mi := &file_pb_artwork_query_proto_msgTypes[27]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ThirdPartyListRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ThirdPartyListRequest) ProtoMessage() {}

func (x *ThirdPartyListRequest) ProtoReflect() protoreflect.Message {
	mi := &file_pb_artwork_query_proto_msgTypes[27]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ThirdPartyListRequest.ProtoReflect.Descriptor instead.
func (*ThirdPartyListRequest) Descriptor() ([]byte, []int) {
	return file_pb_artwork_query_proto_rawDescGZIP(), []int{27}
}

func (x *ThirdPartyListRequest) GetArtworkUuid() string {
	if x != nil {
		return x.ArtworkUuid
	}
	return ""
}

type ThirdPartyListResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	//	message Info{
	//	        int32 Id = 1 [json_name = "id"];
	//	        string ArtworkUuid = 2 [json_name = "artwork_uuid"];
	//	        string ThirdComment = 3 [json_name = "third_comment"];
	//	}
	Data []string `protobuf:"bytes,2,rep,name=Data,json=data,proto3" json:"Data"`
	Msg  string   `protobuf:"bytes,1,opt,name=Msg,json=msg,proto3" json:"Msg"`
}

func (x *ThirdPartyListResponse) Reset() {
	*x = ThirdPartyListResponse{}
	mi := &file_pb_artwork_query_proto_msgTypes[28]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ThirdPartyListResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ThirdPartyListResponse) ProtoMessage() {}

func (x *ThirdPartyListResponse) ProtoReflect() protoreflect.Message {
	mi := &file_pb_artwork_query_proto_msgTypes[28]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ThirdPartyListResponse.ProtoReflect.Descriptor instead.
func (*ThirdPartyListResponse) Descriptor() ([]byte, []int) {
	return file_pb_artwork_query_proto_rawDescGZIP(), []int{28}
}

func (x *ThirdPartyListResponse) GetData() []string {
	if x != nil {
		return x.Data
	}
	return nil
}

func (x *ThirdPartyListResponse) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

// UpdateAwStatus
type UpdateAwStockStatusRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	//	enum SaleStatus {
	//	    StorageStatusNotIn = 0;
	//	    StorageStatusIn = 1;
	//	    StorageStatusDigiIng = 2;
	//	    StorageStatusDigiDone = 3;
	//	    StorageStatusAuthIng = 4;
	//	    StorageStatusAuthDone = 5;
	//	    StorageStatusForSale = 6;
	//	    StorageStatusOut = 7;
	//	}
	ArtworkUuid string  `protobuf:"bytes,1,opt,name=ArtworkUuid,json=artwork_uuid,proto3" json:"ArtworkUuid"`
	ActionType  int32   `protobuf:"varint,2,opt,name=ActionType,json=action_type,proto3" json:"ActionType"`
	AdminId     int32   `protobuf:"varint,3,opt,name=AdminId,json=admin_id,proto3" json:"AdminId"`
	DepartName  string  `protobuf:"bytes,4,opt,name=DepartName,json=depart_name,proto3" json:"DepartName"`
	ArtworkIds  []int32 `protobuf:"varint,5,rep,packed,name=ArtworkIds,json=artwork_ids,proto3" json:"ArtworkIds"`
	AllotUids   []int32 `protobuf:"varint,6,rep,packed,name=AllotUids,json=allot_uids,proto3" json:"AllotUids"`
	ReceiveDate string  `protobuf:"bytes,7,opt,name=ReceiveDate,json=receive_date,proto3" json:"ReceiveDate"`
	PostName    string  `protobuf:"bytes,8,opt,name=PostName,json=post_name,proto3" json:"PostName"`
}

func (x *UpdateAwStockStatusRequest) Reset() {
	*x = UpdateAwStockStatusRequest{}
	mi := &file_pb_artwork_query_proto_msgTypes[29]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateAwStockStatusRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateAwStockStatusRequest) ProtoMessage() {}

func (x *UpdateAwStockStatusRequest) ProtoReflect() protoreflect.Message {
	mi := &file_pb_artwork_query_proto_msgTypes[29]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateAwStockStatusRequest.ProtoReflect.Descriptor instead.
func (*UpdateAwStockStatusRequest) Descriptor() ([]byte, []int) {
	return file_pb_artwork_query_proto_rawDescGZIP(), []int{29}
}

func (x *UpdateAwStockStatusRequest) GetArtworkUuid() string {
	if x != nil {
		return x.ArtworkUuid
	}
	return ""
}

func (x *UpdateAwStockStatusRequest) GetActionType() int32 {
	if x != nil {
		return x.ActionType
	}
	return 0
}

func (x *UpdateAwStockStatusRequest) GetAdminId() int32 {
	if x != nil {
		return x.AdminId
	}
	return 0
}

func (x *UpdateAwStockStatusRequest) GetDepartName() string {
	if x != nil {
		return x.DepartName
	}
	return ""
}

func (x *UpdateAwStockStatusRequest) GetArtworkIds() []int32 {
	if x != nil {
		return x.ArtworkIds
	}
	return nil
}

func (x *UpdateAwStockStatusRequest) GetAllotUids() []int32 {
	if x != nil {
		return x.AllotUids
	}
	return nil
}

func (x *UpdateAwStockStatusRequest) GetReceiveDate() string {
	if x != nil {
		return x.ReceiveDate
	}
	return ""
}

func (x *UpdateAwStockStatusRequest) GetPostName() string {
	if x != nil {
		return x.PostName
	}
	return ""
}

type UpdateAwStockStatusResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Msg string `protobuf:"bytes,1,opt,name=Msg,json=msg,proto3" json:"Msg"`
}

func (x *UpdateAwStockStatusResponse) Reset() {
	*x = UpdateAwStockStatusResponse{}
	mi := &file_pb_artwork_query_proto_msgTypes[30]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateAwStockStatusResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateAwStockStatusResponse) ProtoMessage() {}

func (x *UpdateAwStockStatusResponse) ProtoReflect() protoreflect.Message {
	mi := &file_pb_artwork_query_proto_msgTypes[30]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateAwStockStatusResponse.ProtoReflect.Descriptor instead.
func (*UpdateAwStockStatusResponse) Descriptor() ([]byte, []int) {
	return file_pb_artwork_query_proto_rawDescGZIP(), []int{30}
}

func (x *UpdateAwStockStatusResponse) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

type SyncArtShowIdRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Data         []*SyncArtShowIdRequestInfo `protobuf:"bytes,1,rep,name=Data,json=data,proto3" json:"Data"`
	ArtShowUuids []string                    `protobuf:"bytes,2,rep,name=ArtShowUuids,json=artshow_uuids,proto3" json:"ArtShowUuids"`
}

func (x *SyncArtShowIdRequest) Reset() {
	*x = SyncArtShowIdRequest{}
	mi := &file_pb_artwork_query_proto_msgTypes[31]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SyncArtShowIdRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SyncArtShowIdRequest) ProtoMessage() {}

func (x *SyncArtShowIdRequest) ProtoReflect() protoreflect.Message {
	mi := &file_pb_artwork_query_proto_msgTypes[31]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SyncArtShowIdRequest.ProtoReflect.Descriptor instead.
func (*SyncArtShowIdRequest) Descriptor() ([]byte, []int) {
	return file_pb_artwork_query_proto_rawDescGZIP(), []int{31}
}

func (x *SyncArtShowIdRequest) GetData() []*SyncArtShowIdRequestInfo {
	if x != nil {
		return x.Data
	}
	return nil
}

func (x *SyncArtShowIdRequest) GetArtShowUuids() []string {
	if x != nil {
		return x.ArtShowUuids
	}
	return nil
}

type SyncArtShowIdResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Msg string `protobuf:"bytes,1,opt,name=Msg,json=msg,proto3" json:"Msg"`
}

func (x *SyncArtShowIdResponse) Reset() {
	*x = SyncArtShowIdResponse{}
	mi := &file_pb_artwork_query_proto_msgTypes[32]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SyncArtShowIdResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SyncArtShowIdResponse) ProtoMessage() {}

func (x *SyncArtShowIdResponse) ProtoReflect() protoreflect.Message {
	mi := &file_pb_artwork_query_proto_msgTypes[32]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SyncArtShowIdResponse.ProtoReflect.Descriptor instead.
func (*SyncArtShowIdResponse) Descriptor() ([]byte, []int) {
	return file_pb_artwork_query_proto_rawDescGZIP(), []int{32}
}

func (x *SyncArtShowIdResponse) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

// ShelfList
type ShelfListRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *ShelfListRequest) Reset() {
	*x = ShelfListRequest{}
	mi := &file_pb_artwork_query_proto_msgTypes[33]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ShelfListRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ShelfListRequest) ProtoMessage() {}

func (x *ShelfListRequest) ProtoReflect() protoreflect.Message {
	mi := &file_pb_artwork_query_proto_msgTypes[33]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ShelfListRequest.ProtoReflect.Descriptor instead.
func (*ShelfListRequest) Descriptor() ([]byte, []int) {
	return file_pb_artwork_query_proto_rawDescGZIP(), []int{33}
}

type ShelfListResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Data []*ShelfListResponse_ShelfInfo `protobuf:"bytes,1,rep,name=Data,proto3" json:"Data"`
	Msg  string                         `protobuf:"bytes,2,opt,name=Msg,proto3" json:"Msg"`
}

func (x *ShelfListResponse) Reset() {
	*x = ShelfListResponse{}
	mi := &file_pb_artwork_query_proto_msgTypes[34]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ShelfListResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ShelfListResponse) ProtoMessage() {}

func (x *ShelfListResponse) ProtoReflect() protoreflect.Message {
	mi := &file_pb_artwork_query_proto_msgTypes[34]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ShelfListResponse.ProtoReflect.Descriptor instead.
func (*ShelfListResponse) Descriptor() ([]byte, []int) {
	return file_pb_artwork_query_proto_rawDescGZIP(), []int{34}
}

func (x *ShelfListResponse) GetData() []*ShelfListResponse_ShelfInfo {
	if x != nil {
		return x.Data
	}
	return nil
}

func (x *ShelfListResponse) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

// UpdateCopyrightHash
type UpdateCopyrightHashRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ArtworkUuid   string `protobuf:"bytes,1,opt,name=ArtworkUuid,json=artwork_uuid,proto3" json:"ArtworkUuid"`
	CopyrightHash string `protobuf:"bytes,2,opt,name=CopyrightHash,json=copyrightHash,proto3" json:"CopyrightHash"`
	CopyrightPath string `protobuf:"bytes,3,opt,name=CopyrightPath,json=copyrightPath,proto3" json:"CopyrightPath"`
}

func (x *UpdateCopyrightHashRequest) Reset() {
	*x = UpdateCopyrightHashRequest{}
	mi := &file_pb_artwork_query_proto_msgTypes[35]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateCopyrightHashRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateCopyrightHashRequest) ProtoMessage() {}

func (x *UpdateCopyrightHashRequest) ProtoReflect() protoreflect.Message {
	mi := &file_pb_artwork_query_proto_msgTypes[35]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateCopyrightHashRequest.ProtoReflect.Descriptor instead.
func (*UpdateCopyrightHashRequest) Descriptor() ([]byte, []int) {
	return file_pb_artwork_query_proto_rawDescGZIP(), []int{35}
}

func (x *UpdateCopyrightHashRequest) GetArtworkUuid() string {
	if x != nil {
		return x.ArtworkUuid
	}
	return ""
}

func (x *UpdateCopyrightHashRequest) GetCopyrightHash() string {
	if x != nil {
		return x.CopyrightHash
	}
	return ""
}

func (x *UpdateCopyrightHashRequest) GetCopyrightPath() string {
	if x != nil {
		return x.CopyrightPath
	}
	return ""
}

type UpdateCopyrightHashResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Msg string `protobuf:"bytes,1,opt,name=Msg,json=msg,proto3" json:"Msg"`
}

func (x *UpdateCopyrightHashResponse) Reset() {
	*x = UpdateCopyrightHashResponse{}
	mi := &file_pb_artwork_query_proto_msgTypes[36]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateCopyrightHashResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateCopyrightHashResponse) ProtoMessage() {}

func (x *UpdateCopyrightHashResponse) ProtoReflect() protoreflect.Message {
	mi := &file_pb_artwork_query_proto_msgTypes[36]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateCopyrightHashResponse.ProtoReflect.Descriptor instead.
func (*UpdateCopyrightHashResponse) Descriptor() ([]byte, []int) {
	return file_pb_artwork_query_proto_rawDescGZIP(), []int{36}
}

func (x *UpdateCopyrightHashResponse) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

// ExportArtwork
type ExportArtworkRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Keyword      string   `protobuf:"bytes,1,opt,name=Keyword,json=keyword,proto3" json:"Keyword"`
	Page         int32    `protobuf:"varint,2,opt,name=Page,json=page,proto3" json:"Page"`
	PageSize     int32    `protobuf:"varint,3,opt,name=PageSize,json=page_size,proto3" json:"PageSize"`
	ColumnId     string   `protobuf:"bytes,4,opt,name=ColumnId,json=column_id,proto3" json:"ColumnId"`
	ColumnName   string   `protobuf:"bytes,5,opt,name=ColumnName,json=column_name,proto3" json:"ColumnName"`
	ArtworkUuids []string `protobuf:"bytes,6,rep,name=ArtworkUuids,json=artwork_uuids,proto3" json:"ArtworkUuids"`
}

func (x *ExportArtworkRequest) Reset() {
	*x = ExportArtworkRequest{}
	mi := &file_pb_artwork_query_proto_msgTypes[37]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ExportArtworkRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ExportArtworkRequest) ProtoMessage() {}

func (x *ExportArtworkRequest) ProtoReflect() protoreflect.Message {
	mi := &file_pb_artwork_query_proto_msgTypes[37]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ExportArtworkRequest.ProtoReflect.Descriptor instead.
func (*ExportArtworkRequest) Descriptor() ([]byte, []int) {
	return file_pb_artwork_query_proto_rawDescGZIP(), []int{37}
}

func (x *ExportArtworkRequest) GetKeyword() string {
	if x != nil {
		return x.Keyword
	}
	return ""
}

func (x *ExportArtworkRequest) GetPage() int32 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *ExportArtworkRequest) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *ExportArtworkRequest) GetColumnId() string {
	if x != nil {
		return x.ColumnId
	}
	return ""
}

func (x *ExportArtworkRequest) GetColumnName() string {
	if x != nil {
		return x.ColumnName
	}
	return ""
}

func (x *ExportArtworkRequest) GetArtworkUuids() []string {
	if x != nil {
		return x.ArtworkUuids
	}
	return nil
}

type ExportArtworkResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Data       []*ExportArtworkResponse_Info `protobuf:"bytes,1,rep,name=Data,json=data,proto3" json:"Data"`
	StructName string                        `protobuf:"bytes,2,opt,name=StructName,json=struct_name,proto3" json:"StructName"`
	ColumnDesc string                        `protobuf:"bytes,3,opt,name=ColumnDesc,json=column_desc,proto3" json:"ColumnDesc"`
	Msg        string                        `protobuf:"bytes,4,opt,name=Msg,json=msg,proto3" json:"Msg"`
}

func (x *ExportArtworkResponse) Reset() {
	*x = ExportArtworkResponse{}
	mi := &file_pb_artwork_query_proto_msgTypes[38]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ExportArtworkResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ExportArtworkResponse) ProtoMessage() {}

func (x *ExportArtworkResponse) ProtoReflect() protoreflect.Message {
	mi := &file_pb_artwork_query_proto_msgTypes[38]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ExportArtworkResponse.ProtoReflect.Descriptor instead.
func (*ExportArtworkResponse) Descriptor() ([]byte, []int) {
	return file_pb_artwork_query_proto_rawDescGZIP(), []int{38}
}

func (x *ExportArtworkResponse) GetData() []*ExportArtworkResponse_Info {
	if x != nil {
		return x.Data
	}
	return nil
}

func (x *ExportArtworkResponse) GetStructName() string {
	if x != nil {
		return x.StructName
	}
	return ""
}

func (x *ExportArtworkResponse) GetColumnDesc() string {
	if x != nil {
		return x.ColumnDesc
	}
	return ""
}

func (x *ExportArtworkResponse) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

// TagIdKvList
type TagIdKvListRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Pids string `protobuf:"bytes,1,opt,name=Pids,json=pids,proto3" json:"Pids"`
}

func (x *TagIdKvListRequest) Reset() {
	*x = TagIdKvListRequest{}
	mi := &file_pb_artwork_query_proto_msgTypes[39]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TagIdKvListRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TagIdKvListRequest) ProtoMessage() {}

func (x *TagIdKvListRequest) ProtoReflect() protoreflect.Message {
	mi := &file_pb_artwork_query_proto_msgTypes[39]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TagIdKvListRequest.ProtoReflect.Descriptor instead.
func (*TagIdKvListRequest) Descriptor() ([]byte, []int) {
	return file_pb_artwork_query_proto_rawDescGZIP(), []int{39}
}

func (x *TagIdKvListRequest) GetPids() string {
	if x != nil {
		return x.Pids
	}
	return ""
}

type TagIdKvListResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Info map[int32]string `protobuf:"bytes,1,rep,name=Info,json=info,proto3" json:"Info" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	Msg  string           `protobuf:"bytes,2,opt,name=Msg,json=msg,proto3" json:"Msg"`
}

func (x *TagIdKvListResponse) Reset() {
	*x = TagIdKvListResponse{}
	mi := &file_pb_artwork_query_proto_msgTypes[40]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TagIdKvListResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TagIdKvListResponse) ProtoMessage() {}

func (x *TagIdKvListResponse) ProtoReflect() protoreflect.Message {
	mi := &file_pb_artwork_query_proto_msgTypes[40]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TagIdKvListResponse.ProtoReflect.Descriptor instead.
func (*TagIdKvListResponse) Descriptor() ([]byte, []int) {
	return file_pb_artwork_query_proto_rawDescGZIP(), []int{40}
}

func (x *TagIdKvListResponse) GetInfo() map[int32]string {
	if x != nil {
		return x.Info
	}
	return nil
}

func (x *TagIdKvListResponse) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

// ExportFieldList
type ExportFieldListRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ExportType int32 `protobuf:"varint,1,opt,name=ExportType,json=export_type,proto3" json:"ExportType"`
}

func (x *ExportFieldListRequest) Reset() {
	*x = ExportFieldListRequest{}
	mi := &file_pb_artwork_query_proto_msgTypes[41]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ExportFieldListRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ExportFieldListRequest) ProtoMessage() {}

func (x *ExportFieldListRequest) ProtoReflect() protoreflect.Message {
	mi := &file_pb_artwork_query_proto_msgTypes[41]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ExportFieldListRequest.ProtoReflect.Descriptor instead.
func (*ExportFieldListRequest) Descriptor() ([]byte, []int) {
	return file_pb_artwork_query_proto_rawDescGZIP(), []int{41}
}

func (x *ExportFieldListRequest) GetExportType() int32 {
	if x != nil {
		return x.ExportType
	}
	return 0
}

type ExportFieldListResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Data []*ExportFieldListResponse_Info `protobuf:"bytes,1,rep,name=Data,json=data,proto3" json:"Data"`
	Msg  string                          `protobuf:"bytes,2,opt,name=Msg,json=msg,proto3" json:"Msg"`
}

func (x *ExportFieldListResponse) Reset() {
	*x = ExportFieldListResponse{}
	mi := &file_pb_artwork_query_proto_msgTypes[42]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ExportFieldListResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ExportFieldListResponse) ProtoMessage() {}

func (x *ExportFieldListResponse) ProtoReflect() protoreflect.Message {
	mi := &file_pb_artwork_query_proto_msgTypes[42]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ExportFieldListResponse.ProtoReflect.Descriptor instead.
func (*ExportFieldListResponse) Descriptor() ([]byte, []int) {
	return file_pb_artwork_query_proto_rawDescGZIP(), []int{42}
}

func (x *ExportFieldListResponse) GetData() []*ExportFieldListResponse_Info {
	if x != nil {
		return x.Data
	}
	return nil
}

func (x *ExportFieldListResponse) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

// ArtworkDataByShowId
type ArtworkDataByShowIdRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ArtworkShowIds []string `protobuf:"bytes,1,rep,name=ArtworkShowIds,json=data,proto3" json:"ArtworkShowIds"`
}

func (x *ArtworkDataByShowIdRequest) Reset() {
	*x = ArtworkDataByShowIdRequest{}
	mi := &file_pb_artwork_query_proto_msgTypes[43]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ArtworkDataByShowIdRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ArtworkDataByShowIdRequest) ProtoMessage() {}

func (x *ArtworkDataByShowIdRequest) ProtoReflect() protoreflect.Message {
	mi := &file_pb_artwork_query_proto_msgTypes[43]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ArtworkDataByShowIdRequest.ProtoReflect.Descriptor instead.
func (*ArtworkDataByShowIdRequest) Descriptor() ([]byte, []int) {
	return file_pb_artwork_query_proto_rawDescGZIP(), []int{43}
}

func (x *ArtworkDataByShowIdRequest) GetArtworkShowIds() []string {
	if x != nil {
		return x.ArtworkShowIds
	}
	return nil
}

type ArtworkDataByShowIdResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Data []*ArtworkDataByShowIdResponse_Info `protobuf:"bytes,1,rep,name=Data,proto3" json:"Data"`
	Msg  string                              `protobuf:"bytes,2,opt,name=Msg,json=msg,proto3" json:"Msg"`
}

func (x *ArtworkDataByShowIdResponse) Reset() {
	*x = ArtworkDataByShowIdResponse{}
	mi := &file_pb_artwork_query_proto_msgTypes[44]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ArtworkDataByShowIdResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ArtworkDataByShowIdResponse) ProtoMessage() {}

func (x *ArtworkDataByShowIdResponse) ProtoReflect() protoreflect.Message {
	mi := &file_pb_artwork_query_proto_msgTypes[44]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ArtworkDataByShowIdResponse.ProtoReflect.Descriptor instead.
func (*ArtworkDataByShowIdResponse) Descriptor() ([]byte, []int) {
	return file_pb_artwork_query_proto_rawDescGZIP(), []int{44}
}

func (x *ArtworkDataByShowIdResponse) GetData() []*ArtworkDataByShowIdResponse_Info {
	if x != nil {
		return x.Data
	}
	return nil
}

func (x *ArtworkDataByShowIdResponse) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

type MyAwListReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Keyword    string   `protobuf:"bytes,1,opt,name=Keyword,json=keyword,proto3" json:"Keyword"`
	Page       int32    `protobuf:"varint,2,opt,name=Page,json=page,proto3" json:"Page"`
	PageSize   int32    `protobuf:"varint,3,opt,name=PageSize,json=page_size,proto3" json:"PageSize"`
	IsOver     int32    `protobuf:"varint,4,opt,name=IsOver,json=is_over,proto3" json:"IsOver"`
	AdminId    int32    `protobuf:"varint,5,opt,name=AdminId,json=admin_id,proto3" json:"AdminId"`
	ChainState int32    `protobuf:"varint,6,opt,name=ChainState,json=chain_state,proto3" json:"ChainState"`
	RulesUrl   []string `protobuf:"bytes,7,rep,name=RulesUrl,json=rules_url,proto3" json:"RulesUrl"`
	Source     int32    `protobuf:"varint,8,opt,name=Source,json=source,proto3" json:"Source"`
}

func (x *MyAwListReq) Reset() {
	*x = MyAwListReq{}
	mi := &file_pb_artwork_query_proto_msgTypes[45]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *MyAwListReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MyAwListReq) ProtoMessage() {}

func (x *MyAwListReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_artwork_query_proto_msgTypes[45]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MyAwListReq.ProtoReflect.Descriptor instead.
func (*MyAwListReq) Descriptor() ([]byte, []int) {
	return file_pb_artwork_query_proto_rawDescGZIP(), []int{45}
}

func (x *MyAwListReq) GetKeyword() string {
	if x != nil {
		return x.Keyword
	}
	return ""
}

func (x *MyAwListReq) GetPage() int32 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *MyAwListReq) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *MyAwListReq) GetIsOver() int32 {
	if x != nil {
		return x.IsOver
	}
	return 0
}

func (x *MyAwListReq) GetAdminId() int32 {
	if x != nil {
		return x.AdminId
	}
	return 0
}

func (x *MyAwListReq) GetChainState() int32 {
	if x != nil {
		return x.ChainState
	}
	return 0
}

func (x *MyAwListReq) GetRulesUrl() []string {
	if x != nil {
		return x.RulesUrl
	}
	return nil
}

func (x *MyAwListReq) GetSource() int32 {
	if x != nil {
		return x.Source
	}
	return 0
}

type MyAwListResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Data      []*MyAwListResp_Info `protobuf:"bytes,1,rep,name=Data,json=data,proto3" json:"Data"`
	Count     int32                `protobuf:"varint,2,opt,name=Count,json=count,proto3" json:"Count"`
	InCount   int32                `protobuf:"varint,3,opt,name=inCount,json=in_count,proto3" json:"inCount"`
	DoneCount int32                `protobuf:"varint,4,opt,name=doneCount,json=done_count,proto3" json:"doneCount"`
	Page      int32                `protobuf:"varint,5,opt,name=Page,json=page,proto3" json:"Page"`
	PageSize  int32                `protobuf:"varint,6,opt,name=PageSize,json=page_size,proto3" json:"PageSize"`
	Msg       string               `protobuf:"bytes,7,opt,name=Msg,json=message,proto3" json:"Msg"`
}

func (x *MyAwListResp) Reset() {
	*x = MyAwListResp{}
	mi := &file_pb_artwork_query_proto_msgTypes[46]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *MyAwListResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MyAwListResp) ProtoMessage() {}

func (x *MyAwListResp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_artwork_query_proto_msgTypes[46]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MyAwListResp.ProtoReflect.Descriptor instead.
func (*MyAwListResp) Descriptor() ([]byte, []int) {
	return file_pb_artwork_query_proto_rawDescGZIP(), []int{46}
}

func (x *MyAwListResp) GetData() []*MyAwListResp_Info {
	if x != nil {
		return x.Data
	}
	return nil
}

func (x *MyAwListResp) GetCount() int32 {
	if x != nil {
		return x.Count
	}
	return 0
}

func (x *MyAwListResp) GetInCount() int32 {
	if x != nil {
		return x.InCount
	}
	return 0
}

func (x *MyAwListResp) GetDoneCount() int32 {
	if x != nil {
		return x.DoneCount
	}
	return 0
}

func (x *MyAwListResp) GetPage() int32 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *MyAwListResp) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *MyAwListResp) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

type PageInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Page     int32 `protobuf:"varint,1,opt,name=page,proto3" json:"page"`
	PageSize int32 `protobuf:"varint,2,opt,name=pageSize,proto3" json:"pageSize"`
	Total    int32 `protobuf:"varint,3,opt,name=total,proto3" json:"total"`
}

func (x *PageInfo) Reset() {
	*x = PageInfo{}
	mi := &file_pb_artwork_query_proto_msgTypes[47]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PageInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PageInfo) ProtoMessage() {}

func (x *PageInfo) ProtoReflect() protoreflect.Message {
	mi := &file_pb_artwork_query_proto_msgTypes[47]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PageInfo.ProtoReflect.Descriptor instead.
func (*PageInfo) Descriptor() ([]byte, []int) {
	return file_pb_artwork_query_proto_rawDescGZIP(), []int{47}
}

func (x *PageInfo) GetPage() int32 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *PageInfo) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *PageInfo) GetTotal() int32 {
	if x != nil {
		return x.Total
	}
	return 0
}

type ArtworkPreviewListRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Page     int32 `protobuf:"varint,1,opt,name=page,proto3" json:"page"`
	PageSize int32 `protobuf:"varint,2,opt,name=pageSize,proto3" json:"pageSize"`
	// string keyword=3;
	// int32 storageStatus=4;
	// int32 isOver=5;
	// int32 adminId=6;
	ArtistUid   string   `protobuf:"bytes,7,opt,name=artistUid,proto3" json:"artistUid"`
	InArtShow   int32    `protobuf:"varint,8,opt,name=inArtShow,proto3" json:"inArtShow"`
	ArtworkUids []string `protobuf:"bytes,9,rep,name=artworkUids,proto3" json:"artworkUids"`  //画作uid列表 选填
	ArtworkName string   `protobuf:"bytes,10,opt,name=artworkName,proto3" json:"artworkName"` //画作名称
	Tfnum       string   `protobuf:"bytes,11,opt,name=tfnum,proto3" json:"tfnum"`
}

func (x *ArtworkPreviewListRequest) Reset() {
	*x = ArtworkPreviewListRequest{}
	mi := &file_pb_artwork_query_proto_msgTypes[48]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ArtworkPreviewListRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ArtworkPreviewListRequest) ProtoMessage() {}

func (x *ArtworkPreviewListRequest) ProtoReflect() protoreflect.Message {
	mi := &file_pb_artwork_query_proto_msgTypes[48]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ArtworkPreviewListRequest.ProtoReflect.Descriptor instead.
func (*ArtworkPreviewListRequest) Descriptor() ([]byte, []int) {
	return file_pb_artwork_query_proto_rawDescGZIP(), []int{48}
}

func (x *ArtworkPreviewListRequest) GetPage() int32 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *ArtworkPreviewListRequest) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *ArtworkPreviewListRequest) GetArtistUid() string {
	if x != nil {
		return x.ArtistUid
	}
	return ""
}

func (x *ArtworkPreviewListRequest) GetInArtShow() int32 {
	if x != nil {
		return x.InArtShow
	}
	return 0
}

func (x *ArtworkPreviewListRequest) GetArtworkUids() []string {
	if x != nil {
		return x.ArtworkUids
	}
	return nil
}

func (x *ArtworkPreviewListRequest) GetArtworkName() string {
	if x != nil {
		return x.ArtworkName
	}
	return ""
}

func (x *ArtworkPreviewListRequest) GetTfnum() string {
	if x != nil {
		return x.Tfnum
	}
	return ""
}

type ArtworkPreviewListResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Data []*ArtworkPreviewResponse `protobuf:"bytes,1,rep,name=data,proto3" json:"data"`
	Page *PageInfo                 `protobuf:"bytes,2,opt,name=page,proto3" json:"page"`
}

func (x *ArtworkPreviewListResponse) Reset() {
	*x = ArtworkPreviewListResponse{}
	mi := &file_pb_artwork_query_proto_msgTypes[49]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ArtworkPreviewListResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ArtworkPreviewListResponse) ProtoMessage() {}

func (x *ArtworkPreviewListResponse) ProtoReflect() protoreflect.Message {
	mi := &file_pb_artwork_query_proto_msgTypes[49]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ArtworkPreviewListResponse.ProtoReflect.Descriptor instead.
func (*ArtworkPreviewListResponse) Descriptor() ([]byte, []int) {
	return file_pb_artwork_query_proto_rawDescGZIP(), []int{49}
}

func (x *ArtworkPreviewListResponse) GetData() []*ArtworkPreviewResponse {
	if x != nil {
		return x.Data
	}
	return nil
}

func (x *ArtworkPreviewListResponse) GetPage() *PageInfo {
	if x != nil {
		return x.Page
	}
	return nil
}

type ArtworkPreviewResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ArtistUuid     string `protobuf:"bytes,1,opt,name=artistUuid,proto3" json:"artistUuid"`
	ArtworkName    string `protobuf:"bytes,2,opt,name=artworkName,proto3" json:"artworkName"`
	Length         int32  `protobuf:"varint,3,opt,name=length,proto3" json:"length"`
	Width          int32  `protobuf:"varint,4,opt,name=width,proto3" json:"width"`
	Ruler          int32  `protobuf:"varint,5,opt,name=ruler,proto3" json:"ruler"`
	CreatedAddress string `protobuf:"bytes,6,opt,name=createdAddress,proto3" json:"createdAddress"`
	ArtistPhoto    string `protobuf:"bytes,7,opt,name=artistPhoto,proto3" json:"artistPhoto"`
	HdPic          string `protobuf:"bytes,8,opt,name=hdPic,proto3" json:"hdPic"`
	ArtworkUid     string `protobuf:"bytes,9,opt,name=artworkUid,proto3" json:"artworkUid"`
	CreateDate     string `protobuf:"bytes,10,opt,name=createDate,proto3" json:"createDate"`
	InSource       int32  `protobuf:"varint,11,opt,name=inSource,proto3" json:"inSource"`
	DigiArtImg     string `protobuf:"bytes,12,opt,name=digiArtImg,proto3" json:"digiArtImg"`
	PhotoPic       string `protobuf:"bytes,13,opt,name=photoPic,proto3" json:"photoPic"`
	Tfnum          string `protobuf:"bytes,14,opt,name=tfnum,proto3" json:"tfnum"`
	ArtworkType    int32  `protobuf:"varint,15,opt,name=artworkType,proto3" json:"artworkType"`
}

func (x *ArtworkPreviewResponse) Reset() {
	*x = ArtworkPreviewResponse{}
	mi := &file_pb_artwork_query_proto_msgTypes[50]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ArtworkPreviewResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ArtworkPreviewResponse) ProtoMessage() {}

func (x *ArtworkPreviewResponse) ProtoReflect() protoreflect.Message {
	mi := &file_pb_artwork_query_proto_msgTypes[50]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ArtworkPreviewResponse.ProtoReflect.Descriptor instead.
func (*ArtworkPreviewResponse) Descriptor() ([]byte, []int) {
	return file_pb_artwork_query_proto_rawDescGZIP(), []int{50}
}

func (x *ArtworkPreviewResponse) GetArtistUuid() string {
	if x != nil {
		return x.ArtistUuid
	}
	return ""
}

func (x *ArtworkPreviewResponse) GetArtworkName() string {
	if x != nil {
		return x.ArtworkName
	}
	return ""
}

func (x *ArtworkPreviewResponse) GetLength() int32 {
	if x != nil {
		return x.Length
	}
	return 0
}

func (x *ArtworkPreviewResponse) GetWidth() int32 {
	if x != nil {
		return x.Width
	}
	return 0
}

func (x *ArtworkPreviewResponse) GetRuler() int32 {
	if x != nil {
		return x.Ruler
	}
	return 0
}

func (x *ArtworkPreviewResponse) GetCreatedAddress() string {
	if x != nil {
		return x.CreatedAddress
	}
	return ""
}

func (x *ArtworkPreviewResponse) GetArtistPhoto() string {
	if x != nil {
		return x.ArtistPhoto
	}
	return ""
}

func (x *ArtworkPreviewResponse) GetHdPic() string {
	if x != nil {
		return x.HdPic
	}
	return ""
}

func (x *ArtworkPreviewResponse) GetArtworkUid() string {
	if x != nil {
		return x.ArtworkUid
	}
	return ""
}

func (x *ArtworkPreviewResponse) GetCreateDate() string {
	if x != nil {
		return x.CreateDate
	}
	return ""
}

func (x *ArtworkPreviewResponse) GetInSource() int32 {
	if x != nil {
		return x.InSource
	}
	return 0
}

func (x *ArtworkPreviewResponse) GetDigiArtImg() string {
	if x != nil {
		return x.DigiArtImg
	}
	return ""
}

func (x *ArtworkPreviewResponse) GetPhotoPic() string {
	if x != nil {
		return x.PhotoPic
	}
	return ""
}

func (x *ArtworkPreviewResponse) GetTfnum() string {
	if x != nil {
		return x.Tfnum
	}
	return ""
}

func (x *ArtworkPreviewResponse) GetArtworkType() int32 {
	if x != nil {
		return x.ArtworkType
	}
	return 0
}

type VerifyListReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ArtworkUuid string `protobuf:"bytes,1,opt,name=ArtworkUuid,proto3" json:"ArtworkUuid"`
}

func (x *VerifyListReq) Reset() {
	*x = VerifyListReq{}
	mi := &file_pb_artwork_query_proto_msgTypes[51]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *VerifyListReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VerifyListReq) ProtoMessage() {}

func (x *VerifyListReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_artwork_query_proto_msgTypes[51]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VerifyListReq.ProtoReflect.Descriptor instead.
func (*VerifyListReq) Descriptor() ([]byte, []int) {
	return file_pb_artwork_query_proto_rawDescGZIP(), []int{51}
}

func (x *VerifyListReq) GetArtworkUuid() string {
	if x != nil {
		return x.ArtworkUuid
	}
	return ""
}

type VerifyListResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Data    map[string]*VerifyListResp_Info `protobuf:"bytes,6,rep,name=Data,proto3" json:"Data" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	AuthImg string                          `protobuf:"bytes,7,opt,name=AuthImg,proto3" json:"AuthImg"`
	Msg     string                          `protobuf:"bytes,8,opt,name=Msg,proto3" json:"Msg"`
}

func (x *VerifyListResp) Reset() {
	*x = VerifyListResp{}
	mi := &file_pb_artwork_query_proto_msgTypes[52]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *VerifyListResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VerifyListResp) ProtoMessage() {}

func (x *VerifyListResp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_artwork_query_proto_msgTypes[52]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VerifyListResp.ProtoReflect.Descriptor instead.
func (*VerifyListResp) Descriptor() ([]byte, []int) {
	return file_pb_artwork_query_proto_rawDescGZIP(), []int{52}
}

func (x *VerifyListResp) GetData() map[string]*VerifyListResp_Info {
	if x != nil {
		return x.Data
	}
	return nil
}

func (x *VerifyListResp) GetAuthImg() string {
	if x != nil {
		return x.AuthImg
	}
	return ""
}

func (x *VerifyListResp) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

type ArtshowListReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Keyword  string `protobuf:"bytes,1,opt,name=Keyword,proto3" json:"Keyword"`
	Page     int32  `protobuf:"varint,2,opt,name=Page,json=page,proto3" json:"Page"`
	PageSize int32  `protobuf:"varint,3,opt,name=PageSize,json=page_size,proto3" json:"PageSize"`
}

func (x *ArtshowListReq) Reset() {
	*x = ArtshowListReq{}
	mi := &file_pb_artwork_query_proto_msgTypes[53]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ArtshowListReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ArtshowListReq) ProtoMessage() {}

func (x *ArtshowListReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_artwork_query_proto_msgTypes[53]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ArtshowListReq.ProtoReflect.Descriptor instead.
func (*ArtshowListReq) Descriptor() ([]byte, []int) {
	return file_pb_artwork_query_proto_rawDescGZIP(), []int{53}
}

func (x *ArtshowListReq) GetKeyword() string {
	if x != nil {
		return x.Keyword
	}
	return ""
}

func (x *ArtshowListReq) GetPage() int32 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *ArtshowListReq) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

type ArtshowListResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Data     []*ArtshowListResp_Info `protobuf:"bytes,1,rep,name=Data,json=data,proto3" json:"Data"`
	Count    int32                   `protobuf:"varint,2,opt,name=Count,json=count,proto3" json:"Count"`
	Page     int32                   `protobuf:"varint,3,opt,name=Page,json=page,proto3" json:"Page"`
	PageSize int32                   `protobuf:"varint,4,opt,name=PageSize,json=page_size,proto3" json:"PageSize"`
	Msg      string                  `protobuf:"bytes,5,opt,name=Msg,proto3" json:"Msg"`
}

func (x *ArtshowListResp) Reset() {
	*x = ArtshowListResp{}
	mi := &file_pb_artwork_query_proto_msgTypes[54]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ArtshowListResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ArtshowListResp) ProtoMessage() {}

func (x *ArtshowListResp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_artwork_query_proto_msgTypes[54]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ArtshowListResp.ProtoReflect.Descriptor instead.
func (*ArtshowListResp) Descriptor() ([]byte, []int) {
	return file_pb_artwork_query_proto_rawDescGZIP(), []int{54}
}

func (x *ArtshowListResp) GetData() []*ArtshowListResp_Info {
	if x != nil {
		return x.Data
	}
	return nil
}

func (x *ArtshowListResp) GetCount() int32 {
	if x != nil {
		return x.Count
	}
	return 0
}

func (x *ArtshowListResp) GetPage() int32 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *ArtshowListResp) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *ArtshowListResp) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

type CountVerifySimilarReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ArtworkUuids []string `protobuf:"bytes,1,rep,name=ArtworkUuids,proto3" json:"ArtworkUuids"`
	BitMapIndex  int32    `protobuf:"varint,2,opt,name=BitMapIndex,proto3" json:"BitMapIndex"`
}

func (x *CountVerifySimilarReq) Reset() {
	*x = CountVerifySimilarReq{}
	mi := &file_pb_artwork_query_proto_msgTypes[55]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CountVerifySimilarReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CountVerifySimilarReq) ProtoMessage() {}

func (x *CountVerifySimilarReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_artwork_query_proto_msgTypes[55]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CountVerifySimilarReq.ProtoReflect.Descriptor instead.
func (*CountVerifySimilarReq) Descriptor() ([]byte, []int) {
	return file_pb_artwork_query_proto_rawDescGZIP(), []int{55}
}

func (x *CountVerifySimilarReq) GetArtworkUuids() []string {
	if x != nil {
		return x.ArtworkUuids
	}
	return nil
}

func (x *CountVerifySimilarReq) GetBitMapIndex() int32 {
	if x != nil {
		return x.BitMapIndex
	}
	return 0
}

type CountVerifySimilarResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Msg string `protobuf:"bytes,1,opt,name=Msg,proto3" json:"Msg"`
}

func (x *CountVerifySimilarResp) Reset() {
	*x = CountVerifySimilarResp{}
	mi := &file_pb_artwork_query_proto_msgTypes[56]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CountVerifySimilarResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CountVerifySimilarResp) ProtoMessage() {}

func (x *CountVerifySimilarResp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_artwork_query_proto_msgTypes[56]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CountVerifySimilarResp.ProtoReflect.Descriptor instead.
func (*CountVerifySimilarResp) Descriptor() ([]byte, []int) {
	return file_pb_artwork_query_proto_rawDescGZIP(), []int{56}
}

func (x *CountVerifySimilarResp) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

// OneQuick
type OneQueryReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Keyword            string   `protobuf:"bytes,1,opt,name=Keyword,proto3" json:"Keyword"`
	Page               int32    `protobuf:"varint,2,opt,name=Page,json=page,proto3" json:"Page"`
	PageSize           int32    `protobuf:"varint,3,opt,name=PageSize,json=page_size,proto3" json:"PageSize"`
	Permission         string   `protobuf:"bytes,4,opt,name=Permission,json=permission,proto3" json:"Permission"`
	SearchArtworkUuids []string `protobuf:"bytes,5,rep,name=SearchArtworkUuids,json=search_artwork_uuids,proto3" json:"SearchArtworkUuids"`
	CheckStatus        int32    `protobuf:"varint,6,opt,name=CheckStatus,json=check_status,proto3" json:"CheckStatus"`
	BlackListArtistUid []string `protobuf:"bytes,7,rep,name=BlackListArtistUid,json=black_list_artist_uid,proto3" json:"BlackListArtistUid"`
}

func (x *OneQueryReq) Reset() {
	*x = OneQueryReq{}
	mi := &file_pb_artwork_query_proto_msgTypes[57]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *OneQueryReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OneQueryReq) ProtoMessage() {}

func (x *OneQueryReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_artwork_query_proto_msgTypes[57]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OneQueryReq.ProtoReflect.Descriptor instead.
func (*OneQueryReq) Descriptor() ([]byte, []int) {
	return file_pb_artwork_query_proto_rawDescGZIP(), []int{57}
}

func (x *OneQueryReq) GetKeyword() string {
	if x != nil {
		return x.Keyword
	}
	return ""
}

func (x *OneQueryReq) GetPage() int32 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *OneQueryReq) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *OneQueryReq) GetPermission() string {
	if x != nil {
		return x.Permission
	}
	return ""
}

func (x *OneQueryReq) GetSearchArtworkUuids() []string {
	if x != nil {
		return x.SearchArtworkUuids
	}
	return nil
}

func (x *OneQueryReq) GetCheckStatus() int32 {
	if x != nil {
		return x.CheckStatus
	}
	return 0
}

func (x *OneQueryReq) GetBlackListArtistUid() []string {
	if x != nil {
		return x.BlackListArtistUid
	}
	return nil
}

type OneQueryResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Data     []*OneQueryResp_Info `protobuf:"bytes,1,rep,name=Data,json=data,proto3" json:"Data"`
	Count    int32                `protobuf:"varint,2,opt,name=Count,json=count,proto3" json:"Count"`
	Page     int32                `protobuf:"varint,3,opt,name=Page,json=page,proto3" json:"Page"`
	PageSize int32                `protobuf:"varint,4,opt,name=PageSize,json=page_size,proto3" json:"PageSize"`
	Msg      string               `protobuf:"bytes,5,opt,name=Msg,proto3" json:"Msg"`
}

func (x *OneQueryResp) Reset() {
	*x = OneQueryResp{}
	mi := &file_pb_artwork_query_proto_msgTypes[58]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *OneQueryResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OneQueryResp) ProtoMessage() {}

func (x *OneQueryResp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_artwork_query_proto_msgTypes[58]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OneQueryResp.ProtoReflect.Descriptor instead.
func (*OneQueryResp) Descriptor() ([]byte, []int) {
	return file_pb_artwork_query_proto_rawDescGZIP(), []int{58}
}

func (x *OneQueryResp) GetData() []*OneQueryResp_Info {
	if x != nil {
		return x.Data
	}
	return nil
}

func (x *OneQueryResp) GetCount() int32 {
	if x != nil {
		return x.Count
	}
	return 0
}

func (x *OneQueryResp) GetPage() int32 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *OneQueryResp) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *OneQueryResp) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

type GetPassArtistReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ArtistUuid string `protobuf:"bytes,1,opt,name=ArtistUuid,proto3" json:"ArtistUuid"`
}

func (x *GetPassArtistReq) Reset() {
	*x = GetPassArtistReq{}
	mi := &file_pb_artwork_query_proto_msgTypes[59]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetPassArtistReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetPassArtistReq) ProtoMessage() {}

func (x *GetPassArtistReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_artwork_query_proto_msgTypes[59]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetPassArtistReq.ProtoReflect.Descriptor instead.
func (*GetPassArtistReq) Descriptor() ([]byte, []int) {
	return file_pb_artwork_query_proto_rawDescGZIP(), []int{59}
}

func (x *GetPassArtistReq) GetArtistUuid() string {
	if x != nil {
		return x.ArtistUuid
	}
	return ""
}

type GetPassArtistResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ArtistUuids []string `protobuf:"bytes,1,rep,name=ArtistUuids,proto3" json:"ArtistUuids"`
	Msg         string   `protobuf:"bytes,2,opt,name=Msg,proto3" json:"Msg"`
}

func (x *GetPassArtistResp) Reset() {
	*x = GetPassArtistResp{}
	mi := &file_pb_artwork_query_proto_msgTypes[60]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetPassArtistResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetPassArtistResp) ProtoMessage() {}

func (x *GetPassArtistResp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_artwork_query_proto_msgTypes[60]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetPassArtistResp.ProtoReflect.Descriptor instead.
func (*GetPassArtistResp) Descriptor() ([]byte, []int) {
	return file_pb_artwork_query_proto_rawDescGZIP(), []int{60}
}

func (x *GetPassArtistResp) GetArtistUuids() []string {
	if x != nil {
		return x.ArtistUuids
	}
	return nil
}

func (x *GetPassArtistResp) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

type FilterAwListReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ArtistUuid string `protobuf:"bytes,1,opt,name=ArtistUuid,json=artist_uuid,proto3" json:"ArtistUuid"`
}

func (x *FilterAwListReq) Reset() {
	*x = FilterAwListReq{}
	mi := &file_pb_artwork_query_proto_msgTypes[61]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *FilterAwListReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FilterAwListReq) ProtoMessage() {}

func (x *FilterAwListReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_artwork_query_proto_msgTypes[61]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FilterAwListReq.ProtoReflect.Descriptor instead.
func (*FilterAwListReq) Descriptor() ([]byte, []int) {
	return file_pb_artwork_query_proto_rawDescGZIP(), []int{61}
}

func (x *FilterAwListReq) GetArtistUuid() string {
	if x != nil {
		return x.ArtistUuid
	}
	return ""
}

type FilterAwListResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Data []*FilterAwListResp_Info `protobuf:"bytes,1,rep,name=Data,proto3" json:"Data"`
	Msg  string                   `protobuf:"bytes,2,opt,name=Msg,proto3" json:"Msg"`
}

func (x *FilterAwListResp) Reset() {
	*x = FilterAwListResp{}
	mi := &file_pb_artwork_query_proto_msgTypes[62]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *FilterAwListResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FilterAwListResp) ProtoMessage() {}

func (x *FilterAwListResp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_artwork_query_proto_msgTypes[62]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FilterAwListResp.ProtoReflect.Descriptor instead.
func (*FilterAwListResp) Descriptor() ([]byte, []int) {
	return file_pb_artwork_query_proto_rawDescGZIP(), []int{62}
}

func (x *FilterAwListResp) GetData() []*FilterAwListResp_Info {
	if x != nil {
		return x.Data
	}
	return nil
}

func (x *FilterAwListResp) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

// ChainInfoByHash
type ChainInfoByHashReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	WtTransactionHash    []string `protobuf:"bytes,1,rep,name=WtTransactionHash,proto3" json:"WtTransactionHash"`
	BaiduTransactionHash []string `protobuf:"bytes,2,rep,name=BaiduTransactionHash,proto3" json:"BaiduTransactionHash"`
	Changtransactionhash []string `protobuf:"bytes,3,rep,name=Changtransactionhash,proto3" json:"Changtransactionhash"`
}

func (x *ChainInfoByHashReq) Reset() {
	*x = ChainInfoByHashReq{}
	mi := &file_pb_artwork_query_proto_msgTypes[63]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ChainInfoByHashReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ChainInfoByHashReq) ProtoMessage() {}

func (x *ChainInfoByHashReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_artwork_query_proto_msgTypes[63]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ChainInfoByHashReq.ProtoReflect.Descriptor instead.
func (*ChainInfoByHashReq) Descriptor() ([]byte, []int) {
	return file_pb_artwork_query_proto_rawDescGZIP(), []int{63}
}

func (x *ChainInfoByHashReq) GetWtTransactionHash() []string {
	if x != nil {
		return x.WtTransactionHash
	}
	return nil
}

func (x *ChainInfoByHashReq) GetBaiduTransactionHash() []string {
	if x != nil {
		return x.BaiduTransactionHash
	}
	return nil
}

func (x *ChainInfoByHashReq) GetChangtransactionhash() []string {
	if x != nil {
		return x.Changtransactionhash
	}
	return nil
}

type ChainInfoByHashResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Data []*ChainInfoByHashResp_Info `protobuf:"bytes,1,rep,name=Data,proto3" json:"Data"`
}

func (x *ChainInfoByHashResp) Reset() {
	*x = ChainInfoByHashResp{}
	mi := &file_pb_artwork_query_proto_msgTypes[64]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ChainInfoByHashResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ChainInfoByHashResp) ProtoMessage() {}

func (x *ChainInfoByHashResp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_artwork_query_proto_msgTypes[64]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ChainInfoByHashResp.ProtoReflect.Descriptor instead.
func (*ChainInfoByHashResp) Descriptor() ([]byte, []int) {
	return file_pb_artwork_query_proto_rawDescGZIP(), []int{64}
}

func (x *ChainInfoByHashResp) GetData() []*ChainInfoByHashResp_Info {
	if x != nil {
		return x.Data
	}
	return nil
}

// StockOutArtist
type StockOutArtistReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	StockOutTime string `protobuf:"bytes,1,opt,name=StockOutTime,proto3" json:"StockOutTime"`
}

func (x *StockOutArtistReq) Reset() {
	*x = StockOutArtistReq{}
	mi := &file_pb_artwork_query_proto_msgTypes[65]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *StockOutArtistReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StockOutArtistReq) ProtoMessage() {}

func (x *StockOutArtistReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_artwork_query_proto_msgTypes[65]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StockOutArtistReq.ProtoReflect.Descriptor instead.
func (*StockOutArtistReq) Descriptor() ([]byte, []int) {
	return file_pb_artwork_query_proto_rawDescGZIP(), []int{65}
}

func (x *StockOutArtistReq) GetStockOutTime() string {
	if x != nil {
		return x.StockOutTime
	}
	return ""
}

type StockOutArtistResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ArtistUuids []string `protobuf:"bytes,1,rep,name=ArtistUuids,proto3" json:"ArtistUuids"`
}

func (x *StockOutArtistResp) Reset() {
	*x = StockOutArtistResp{}
	mi := &file_pb_artwork_query_proto_msgTypes[66]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *StockOutArtistResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StockOutArtistResp) ProtoMessage() {}

func (x *StockOutArtistResp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_artwork_query_proto_msgTypes[66]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StockOutArtistResp.ProtoReflect.Descriptor instead.
func (*StockOutArtistResp) Descriptor() ([]byte, []int) {
	return file_pb_artwork_query_proto_rawDescGZIP(), []int{66}
}

func (x *StockOutArtistResp) GetArtistUuids() []string {
	if x != nil {
		return x.ArtistUuids
	}
	return nil
}

// ChainList
type ChainListReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ChainType string `protobuf:"bytes,1,opt,name=ChainType,proto3" json:"ChainType"`
	Page      int32  `protobuf:"varint,2,opt,name=Page,proto3" json:"Page"`
	PageSize  int32  `protobuf:"varint,3,opt,name=PageSize,proto3" json:"PageSize"`
	Tfnum     string `protobuf:"bytes,4,opt,name=Tfnum,proto3" json:"Tfnum"`
}

func (x *ChainListReq) Reset() {
	*x = ChainListReq{}
	mi := &file_pb_artwork_query_proto_msgTypes[67]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ChainListReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ChainListReq) ProtoMessage() {}

func (x *ChainListReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_artwork_query_proto_msgTypes[67]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ChainListReq.ProtoReflect.Descriptor instead.
func (*ChainListReq) Descriptor() ([]byte, []int) {
	return file_pb_artwork_query_proto_rawDescGZIP(), []int{67}
}

func (x *ChainListReq) GetChainType() string {
	if x != nil {
		return x.ChainType
	}
	return ""
}

func (x *ChainListReq) GetPage() int32 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *ChainListReq) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *ChainListReq) GetTfnum() string {
	if x != nil {
		return x.Tfnum
	}
	return ""
}

type ChainListResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Data  []*ChainListResp_Info `protobuf:"bytes,1,rep,name=Data,proto3" json:"Data"`
	Count int64                 `protobuf:"varint,2,opt,name=Count,proto3" json:"Count"`
}

func (x *ChainListResp) Reset() {
	*x = ChainListResp{}
	mi := &file_pb_artwork_query_proto_msgTypes[68]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ChainListResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ChainListResp) ProtoMessage() {}

func (x *ChainListResp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_artwork_query_proto_msgTypes[68]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ChainListResp.ProtoReflect.Descriptor instead.
func (*ChainListResp) Descriptor() ([]byte, []int) {
	return file_pb_artwork_query_proto_rawDescGZIP(), []int{68}
}

func (x *ChainListResp) GetData() []*ChainListResp_Info {
	if x != nil {
		return x.Data
	}
	return nil
}

func (x *ChainListResp) GetCount() int64 {
	if x != nil {
		return x.Count
	}
	return 0
}

type TotalTransferResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TotalNum                 int64 `protobuf:"varint,1,opt,name=TotalNum,proto3" json:"TotalNum"`
	TotalOfflineCopyrightNum int64 `protobuf:"varint,2,opt,name=TotalOfflineCopyrightNum,proto3" json:"TotalOfflineCopyrightNum"`
}

func (x *TotalTransferResp) Reset() {
	*x = TotalTransferResp{}
	mi := &file_pb_artwork_query_proto_msgTypes[69]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TotalTransferResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TotalTransferResp) ProtoMessage() {}

func (x *TotalTransferResp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_artwork_query_proto_msgTypes[69]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TotalTransferResp.ProtoReflect.Descriptor instead.
func (*TotalTransferResp) Descriptor() ([]byte, []int) {
	return file_pb_artwork_query_proto_rawDescGZIP(), []int{69}
}

func (x *TotalTransferResp) GetTotalNum() int64 {
	if x != nil {
		return x.TotalNum
	}
	return 0
}

func (x *TotalTransferResp) GetTotalOfflineCopyrightNum() int64 {
	if x != nil {
		return x.TotalOfflineCopyrightNum
	}
	return 0
}

type ArtworkProfileData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id                   int32   `protobuf:"varint,5,opt,name=id,proto3" json:"id"`                                     //type:*int32       comment:序号
	Uuid                 string  `protobuf:"bytes,6,opt,name=uuid,proto3" json:"uuid"`                                  //
	Seqnum               string  `protobuf:"bytes,7,opt,name=seqnum,proto3" json:"seqnum"`                              //
	Tfnum                string  `protobuf:"bytes,8,opt,name=tfnum,proto3" json:"tfnum"`                                //
	Num                  int32   `protobuf:"varint,9,opt,name=num,proto3" json:"num"`                                   //type:*int32       comment:画作序号; 从1开始
	ArtistName           string  `protobuf:"bytes,10,opt,name=artistName,proto3" json:"artistName"`                     //
	ArtistUuid           string  `protobuf:"bytes,11,opt,name=artistUuid,proto3" json:"artistUuid"`                     //
	Name                 string  `protobuf:"bytes,12,opt,name=name,proto3" json:"name"`                                 //
	Belong               int32   `protobuf:"varint,13,opt,name=belong,proto3" json:"belong"`                            //
	ArtistPhoto          string  `protobuf:"bytes,14,opt,name=artistPhoto,proto3" json:"artistPhoto"`                   //
	PhotoPic             string  `protobuf:"bytes,15,opt,name=photoPic,proto3" json:"photoPic"`                         //
	IsSign               int32   `protobuf:"varint,16,opt,name=isSign,proto3" json:"isSign"`                            //
	IsSeal               int32   `protobuf:"varint,17,opt,name=isSeal,proto3" json:"isSeal"`                            //
	ArtQuality           int32   `protobuf:"varint,18,opt,name=artQuality,proto3" json:"artQuality"`                    //
	IncompletePic        string  `protobuf:"bytes,19,opt,name=incompletePic,proto3" json:"incompletePic"`               //
	CopyrightPic         string  `protobuf:"bytes,20,opt,name=copyrightPic,proto3" json:"copyrightPic"`                 //
	Length               int32   `protobuf:"varint,21,opt,name=length,proto3" json:"length"`                            //type:*int32       comment:画作长度;厘米
	Width                int32   `protobuf:"varint,22,opt,name=width,proto3" json:"width"`                              //type:*int32       comment:画作宽度;厘米
	Ruler                int32   `protobuf:"varint,23,opt,name=ruler,proto3" json:"ruler"`                              //type:*int32       comment:画作平尺数
	ModelYear            string  `protobuf:"bytes,24,opt,name=modelYear,proto3" json:"modelYear"`                       //
	ArtworkState         int32   `protobuf:"varint,25,opt,name=artworkState,proto3" json:"artworkState"`                //
	ArtworkPic           string  `protobuf:"bytes,26,opt,name=artworkPic,proto3" json:"artworkPic"`                     //
	IsExcellent          int32   `protobuf:"varint,27,opt,name=isExcellent,proto3" json:"isExcellent"`                  //
	ScreenNum            int32   `protobuf:"varint,28,opt,name=screenNum,proto3" json:"screenNum"`                      //type:*int32       comment:条屏数量
	NetworkTrace         string  `protobuf:"bytes,29,opt,name=networkTrace,proto3" json:"networkTrace"`                 //
	PhotoState           string  `protobuf:"bytes,30,opt,name=photoState,proto3" json:"photoState"`                     //
	Hash                 string  `protobuf:"bytes,31,opt,name=hash,proto3" json:"hash"`                                 //
	Copyright            string  `protobuf:"bytes,32,opt,name=copyright,proto3" json:"copyright"`                       //
	Abstract             string  `protobuf:"bytes,33,opt,name=abstract,proto3" json:"abstract"`                         //简介
	Mountmode            int32   `protobuf:"varint,34,opt,name=mountmode,proto3" json:"mountmode"`                      //
	Material             int32   `protobuf:"varint,35,opt,name=material,proto3" json:"material"`                        //
	Sealpic              string  `protobuf:"bytes,36,opt,name=sealpic,proto3" json:"sealpic"`                           //
	SealpicPhoto         string  `protobuf:"bytes,37,opt,name=sealpicPhoto,proto3" json:"sealpicPhoto"`                 //手机拍摄人名章图
	Signpic              string  `protobuf:"bytes,38,opt,name=signpic,proto3" json:"signpic"`                           //
	InscribeDate         string  `protobuf:"bytes,39,opt,name=inscribeDate,proto3" json:"inscribeDate"`                 //
	Signdate             string  `protobuf:"bytes,40,opt,name=signdate,proto3" json:"signdate"`                         //
	CreatedDate          string  `protobuf:"bytes,41,opt,name=createdDate,proto3" json:"createdDate"`                   //
	CreatedAddress       string  `protobuf:"bytes,42,opt,name=createdAddress,proto3" json:"createdAddress"`             //
	CreateAddressCode    string  `protobuf:"bytes,43,opt,name=createAddressCode,proto3" json:"createAddressCode"`       //创作地址的编码
	ArriveTime           string  `protobuf:"bytes,44,opt,name=arriveTime,proto3" json:"arriveTime"`                     //
	ArtworkType          int32   `protobuf:"varint,45,opt,name=artworkType,proto3" json:"artworkType"`                  //
	GiftInfo             string  `protobuf:"bytes,46,opt,name=giftInfo,proto3" json:"giftInfo"`                         //
	Scroll               string  `protobuf:"bytes,47,opt,name=scroll,proto3" json:"scroll"`                             //
	Comment              string  `protobuf:"bytes,48,opt,name=comment,proto3" json:"comment"`                           //画作备注
	ArtMeansOfExpression string  `protobuf:"bytes,49,opt,name=artMeansOfExpression,proto3" json:"artMeansOfExpression"` //
	Size                 int32   `protobuf:"varint,50,opt,name=size,proto3" json:"size"`                                //
	ArtHorizontal        int32   `protobuf:"varint,51,opt,name=artHorizontal,proto3" json:"artHorizontal"`              //
	FlowState            int32   `protobuf:"varint,52,opt,name=flowState,proto3" json:"flowState"`                      //
	HdPic                string  `protobuf:"bytes,53,opt,name=hdPic,proto3" json:"hdPic"`                               //
	ArtCondition         int32   `protobuf:"varint,54,opt,name=artCondition,proto3" json:"artCondition"`                //
	Status               int32   `protobuf:"varint,55,opt,name=status,proto3" json:"status"`                            //
	PriceRuler           float32 `protobuf:"fixed32,56,opt,name=priceRuler,proto3" json:"priceRuler"`                   //
	PriceCopyright       float32 `protobuf:"fixed32,57,opt,name=priceCopyright,proto3" json:"priceCopyright"`           //
	PriceArtwork         float32 `protobuf:"fixed32,58,opt,name=priceArtwork,proto3" json:"priceArtwork"`               //
	PriceMarket          float32 `protobuf:"fixed32,59,opt,name=priceMarket,proto3" json:"priceMarket"`                 //
	PriceRun             float32 `protobuf:"fixed32,60,opt,name=priceRun,proto3" json:"priceRun"`                       //润格
	FilterState          int32   `protobuf:"varint,61,opt,name=filterState,proto3" json:"filterState"`                  //筛选状态 1 通过 2 不通过
	CreateSource         int32   `protobuf:"varint,62,opt,name=createSource,proto3" json:"createSource"`                //来源 1 后台 2 画家宝
	CreateDoneDate       string  `protobuf:"bytes,63,opt,name=createDoneDate,proto3" json:"createDoneDate"`             //创作完成时间
	Mask                 int32   `protobuf:"varint,64,opt,name=mask,proto3" json:"mask"`                                //画作标记  1 一手画  2二手画
	InSource             int32   `protobuf:"varint,65,opt,name=inSource,proto3" json:"inSource"`                        //在哪个系统  1 管理 2 画家宝 3 管理和画家包
	CreatedAt            int32   `protobuf:"varint,66,opt,name=createdAt,proto3" json:"createdAt"`                      //
	UpdatedAt            int32   `protobuf:"varint,67,opt,name=updatedAt,proto3" json:"updatedAt"`                      //
	DeletedAt            int32   `protobuf:"varint,68,opt,name=deletedAt,proto3" json:"deletedAt"`                      //
}

func (x *ArtworkProfileData) Reset() {
	*x = ArtworkProfileData{}
	mi := &file_pb_artwork_query_proto_msgTypes[70]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ArtworkProfileData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ArtworkProfileData) ProtoMessage() {}

func (x *ArtworkProfileData) ProtoReflect() protoreflect.Message {
	mi := &file_pb_artwork_query_proto_msgTypes[70]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ArtworkProfileData.ProtoReflect.Descriptor instead.
func (*ArtworkProfileData) Descriptor() ([]byte, []int) {
	return file_pb_artwork_query_proto_rawDescGZIP(), []int{70}
}

func (x *ArtworkProfileData) GetId() int32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *ArtworkProfileData) GetUuid() string {
	if x != nil {
		return x.Uuid
	}
	return ""
}

func (x *ArtworkProfileData) GetSeqnum() string {
	if x != nil {
		return x.Seqnum
	}
	return ""
}

func (x *ArtworkProfileData) GetTfnum() string {
	if x != nil {
		return x.Tfnum
	}
	return ""
}

func (x *ArtworkProfileData) GetNum() int32 {
	if x != nil {
		return x.Num
	}
	return 0
}

func (x *ArtworkProfileData) GetArtistName() string {
	if x != nil {
		return x.ArtistName
	}
	return ""
}

func (x *ArtworkProfileData) GetArtistUuid() string {
	if x != nil {
		return x.ArtistUuid
	}
	return ""
}

func (x *ArtworkProfileData) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *ArtworkProfileData) GetBelong() int32 {
	if x != nil {
		return x.Belong
	}
	return 0
}

func (x *ArtworkProfileData) GetArtistPhoto() string {
	if x != nil {
		return x.ArtistPhoto
	}
	return ""
}

func (x *ArtworkProfileData) GetPhotoPic() string {
	if x != nil {
		return x.PhotoPic
	}
	return ""
}

func (x *ArtworkProfileData) GetIsSign() int32 {
	if x != nil {
		return x.IsSign
	}
	return 0
}

func (x *ArtworkProfileData) GetIsSeal() int32 {
	if x != nil {
		return x.IsSeal
	}
	return 0
}

func (x *ArtworkProfileData) GetArtQuality() int32 {
	if x != nil {
		return x.ArtQuality
	}
	return 0
}

func (x *ArtworkProfileData) GetIncompletePic() string {
	if x != nil {
		return x.IncompletePic
	}
	return ""
}

func (x *ArtworkProfileData) GetCopyrightPic() string {
	if x != nil {
		return x.CopyrightPic
	}
	return ""
}

func (x *ArtworkProfileData) GetLength() int32 {
	if x != nil {
		return x.Length
	}
	return 0
}

func (x *ArtworkProfileData) GetWidth() int32 {
	if x != nil {
		return x.Width
	}
	return 0
}

func (x *ArtworkProfileData) GetRuler() int32 {
	if x != nil {
		return x.Ruler
	}
	return 0
}

func (x *ArtworkProfileData) GetModelYear() string {
	if x != nil {
		return x.ModelYear
	}
	return ""
}

func (x *ArtworkProfileData) GetArtworkState() int32 {
	if x != nil {
		return x.ArtworkState
	}
	return 0
}

func (x *ArtworkProfileData) GetArtworkPic() string {
	if x != nil {
		return x.ArtworkPic
	}
	return ""
}

func (x *ArtworkProfileData) GetIsExcellent() int32 {
	if x != nil {
		return x.IsExcellent
	}
	return 0
}

func (x *ArtworkProfileData) GetScreenNum() int32 {
	if x != nil {
		return x.ScreenNum
	}
	return 0
}

func (x *ArtworkProfileData) GetNetworkTrace() string {
	if x != nil {
		return x.NetworkTrace
	}
	return ""
}

func (x *ArtworkProfileData) GetPhotoState() string {
	if x != nil {
		return x.PhotoState
	}
	return ""
}

func (x *ArtworkProfileData) GetHash() string {
	if x != nil {
		return x.Hash
	}
	return ""
}

func (x *ArtworkProfileData) GetCopyright() string {
	if x != nil {
		return x.Copyright
	}
	return ""
}

func (x *ArtworkProfileData) GetAbstract() string {
	if x != nil {
		return x.Abstract
	}
	return ""
}

func (x *ArtworkProfileData) GetMountmode() int32 {
	if x != nil {
		return x.Mountmode
	}
	return 0
}

func (x *ArtworkProfileData) GetMaterial() int32 {
	if x != nil {
		return x.Material
	}
	return 0
}

func (x *ArtworkProfileData) GetSealpic() string {
	if x != nil {
		return x.Sealpic
	}
	return ""
}

func (x *ArtworkProfileData) GetSealpicPhoto() string {
	if x != nil {
		return x.SealpicPhoto
	}
	return ""
}

func (x *ArtworkProfileData) GetSignpic() string {
	if x != nil {
		return x.Signpic
	}
	return ""
}

func (x *ArtworkProfileData) GetInscribeDate() string {
	if x != nil {
		return x.InscribeDate
	}
	return ""
}

func (x *ArtworkProfileData) GetSigndate() string {
	if x != nil {
		return x.Signdate
	}
	return ""
}

func (x *ArtworkProfileData) GetCreatedDate() string {
	if x != nil {
		return x.CreatedDate
	}
	return ""
}

func (x *ArtworkProfileData) GetCreatedAddress() string {
	if x != nil {
		return x.CreatedAddress
	}
	return ""
}

func (x *ArtworkProfileData) GetCreateAddressCode() string {
	if x != nil {
		return x.CreateAddressCode
	}
	return ""
}

func (x *ArtworkProfileData) GetArriveTime() string {
	if x != nil {
		return x.ArriveTime
	}
	return ""
}

func (x *ArtworkProfileData) GetArtworkType() int32 {
	if x != nil {
		return x.ArtworkType
	}
	return 0
}

func (x *ArtworkProfileData) GetGiftInfo() string {
	if x != nil {
		return x.GiftInfo
	}
	return ""
}

func (x *ArtworkProfileData) GetScroll() string {
	if x != nil {
		return x.Scroll
	}
	return ""
}

func (x *ArtworkProfileData) GetComment() string {
	if x != nil {
		return x.Comment
	}
	return ""
}

func (x *ArtworkProfileData) GetArtMeansOfExpression() string {
	if x != nil {
		return x.ArtMeansOfExpression
	}
	return ""
}

func (x *ArtworkProfileData) GetSize() int32 {
	if x != nil {
		return x.Size
	}
	return 0
}

func (x *ArtworkProfileData) GetArtHorizontal() int32 {
	if x != nil {
		return x.ArtHorizontal
	}
	return 0
}

func (x *ArtworkProfileData) GetFlowState() int32 {
	if x != nil {
		return x.FlowState
	}
	return 0
}

func (x *ArtworkProfileData) GetHdPic() string {
	if x != nil {
		return x.HdPic
	}
	return ""
}

func (x *ArtworkProfileData) GetArtCondition() int32 {
	if x != nil {
		return x.ArtCondition
	}
	return 0
}

func (x *ArtworkProfileData) GetStatus() int32 {
	if x != nil {
		return x.Status
	}
	return 0
}

func (x *ArtworkProfileData) GetPriceRuler() float32 {
	if x != nil {
		return x.PriceRuler
	}
	return 0
}

func (x *ArtworkProfileData) GetPriceCopyright() float32 {
	if x != nil {
		return x.PriceCopyright
	}
	return 0
}

func (x *ArtworkProfileData) GetPriceArtwork() float32 {
	if x != nil {
		return x.PriceArtwork
	}
	return 0
}

func (x *ArtworkProfileData) GetPriceMarket() float32 {
	if x != nil {
		return x.PriceMarket
	}
	return 0
}

func (x *ArtworkProfileData) GetPriceRun() float32 {
	if x != nil {
		return x.PriceRun
	}
	return 0
}

func (x *ArtworkProfileData) GetFilterState() int32 {
	if x != nil {
		return x.FilterState
	}
	return 0
}

func (x *ArtworkProfileData) GetCreateSource() int32 {
	if x != nil {
		return x.CreateSource
	}
	return 0
}

func (x *ArtworkProfileData) GetCreateDoneDate() string {
	if x != nil {
		return x.CreateDoneDate
	}
	return ""
}

func (x *ArtworkProfileData) GetMask() int32 {
	if x != nil {
		return x.Mask
	}
	return 0
}

func (x *ArtworkProfileData) GetInSource() int32 {
	if x != nil {
		return x.InSource
	}
	return 0
}

func (x *ArtworkProfileData) GetCreatedAt() int32 {
	if x != nil {
		return x.CreatedAt
	}
	return 0
}

func (x *ArtworkProfileData) GetUpdatedAt() int32 {
	if x != nil {
		return x.UpdatedAt
	}
	return 0
}

func (x *ArtworkProfileData) GetDeletedAt() int32 {
	if x != nil {
		return x.DeletedAt
	}
	return 0
}

type GetArtworkProfileListRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Query    *ArtworkProfileData `protobuf:"bytes,1,opt,name=query,proto3" json:"query"`
	Page     int64               `protobuf:"varint,2,opt,name=page,proto3" json:"page"`
	PageSize int64               `protobuf:"varint,3,opt,name=pageSize,proto3" json:"pageSize"`
	Where    string              `protobuf:"bytes,4,opt,name=where,proto3" json:"where"`
	Order    string              `protobuf:"bytes,5,opt,name=order,proto3" json:"order"`
	Select   []string            `protobuf:"bytes,6,rep,name=select,proto3" json:"select"`
}

func (x *GetArtworkProfileListRequest) Reset() {
	*x = GetArtworkProfileListRequest{}
	mi := &file_pb_artwork_query_proto_msgTypes[71]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetArtworkProfileListRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetArtworkProfileListRequest) ProtoMessage() {}

func (x *GetArtworkProfileListRequest) ProtoReflect() protoreflect.Message {
	mi := &file_pb_artwork_query_proto_msgTypes[71]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetArtworkProfileListRequest.ProtoReflect.Descriptor instead.
func (*GetArtworkProfileListRequest) Descriptor() ([]byte, []int) {
	return file_pb_artwork_query_proto_rawDescGZIP(), []int{71}
}

func (x *GetArtworkProfileListRequest) GetQuery() *ArtworkProfileData {
	if x != nil {
		return x.Query
	}
	return nil
}

func (x *GetArtworkProfileListRequest) GetPage() int64 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *GetArtworkProfileListRequest) GetPageSize() int64 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *GetArtworkProfileListRequest) GetWhere() string {
	if x != nil {
		return x.Where
	}
	return ""
}

func (x *GetArtworkProfileListRequest) GetOrder() string {
	if x != nil {
		return x.Order
	}
	return ""
}

func (x *GetArtworkProfileListRequest) GetSelect() []string {
	if x != nil {
		return x.Select
	}
	return nil
}

type GetArtworkProfileListResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	List     []*ArtworkProfileData `protobuf:"bytes,1,rep,name=list,proto3" json:"list"`
	Page     int64                 `protobuf:"varint,2,opt,name=page,proto3" json:"page"`
	PageSize int64                 `protobuf:"varint,3,opt,name=pageSize,proto3" json:"pageSize"`
	Total    int64                 `protobuf:"varint,4,opt,name=Total,proto3" json:"Total"`
}

func (x *GetArtworkProfileListResp) Reset() {
	*x = GetArtworkProfileListResp{}
	mi := &file_pb_artwork_query_proto_msgTypes[72]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetArtworkProfileListResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetArtworkProfileListResp) ProtoMessage() {}

func (x *GetArtworkProfileListResp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_artwork_query_proto_msgTypes[72]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetArtworkProfileListResp.ProtoReflect.Descriptor instead.
func (*GetArtworkProfileListResp) Descriptor() ([]byte, []int) {
	return file_pb_artwork_query_proto_rawDescGZIP(), []int{72}
}

func (x *GetArtworkProfileListResp) GetList() []*ArtworkProfileData {
	if x != nil {
		return x.List
	}
	return nil
}

func (x *GetArtworkProfileListResp) GetPage() int64 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *GetArtworkProfileListResp) GetPageSize() int64 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *GetArtworkProfileListResp) GetTotal() int64 {
	if x != nil {
		return x.Total
	}
	return 0
}

// ArtistBindArtworkList
type ArtistBindArtworkListReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ArtistUuid string `protobuf:"bytes,1,opt,name=ArtistUuid,proto3" json:"ArtistUuid"`
	Page       int32  `protobuf:"varint,2,opt,name=Page,proto3" json:"Page"`
	PageSize   int32  `protobuf:"varint,3,opt,name=PageSize,proto3" json:"PageSize"`
	Action     int32  `protobuf:"varint,4,opt,name=Action,proto3" json:"Action"`
}

func (x *ArtistBindArtworkListReq) Reset() {
	*x = ArtistBindArtworkListReq{}
	mi := &file_pb_artwork_query_proto_msgTypes[73]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ArtistBindArtworkListReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ArtistBindArtworkListReq) ProtoMessage() {}

func (x *ArtistBindArtworkListReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_artwork_query_proto_msgTypes[73]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ArtistBindArtworkListReq.ProtoReflect.Descriptor instead.
func (*ArtistBindArtworkListReq) Descriptor() ([]byte, []int) {
	return file_pb_artwork_query_proto_rawDescGZIP(), []int{73}
}

func (x *ArtistBindArtworkListReq) GetArtistUuid() string {
	if x != nil {
		return x.ArtistUuid
	}
	return ""
}

func (x *ArtistBindArtworkListReq) GetPage() int32 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *ArtistBindArtworkListReq) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *ArtistBindArtworkListReq) GetAction() int32 {
	if x != nil {
		return x.Action
	}
	return 0
}

type ArtistBindArtworkListResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Data  []*ArtistBindArtworkListResp_Info `protobuf:"bytes,1,rep,name=Data,proto3" json:"Data"`
	Count int32                             `protobuf:"varint,2,opt,name=Count,proto3" json:"Count"`
}

func (x *ArtistBindArtworkListResp) Reset() {
	*x = ArtistBindArtworkListResp{}
	mi := &file_pb_artwork_query_proto_msgTypes[74]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ArtistBindArtworkListResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ArtistBindArtworkListResp) ProtoMessage() {}

func (x *ArtistBindArtworkListResp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_artwork_query_proto_msgTypes[74]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ArtistBindArtworkListResp.ProtoReflect.Descriptor instead.
func (*ArtistBindArtworkListResp) Descriptor() ([]byte, []int) {
	return file_pb_artwork_query_proto_rawDescGZIP(), []int{74}
}

func (x *ArtistBindArtworkListResp) GetData() []*ArtistBindArtworkListResp_Info {
	if x != nil {
		return x.Data
	}
	return nil
}

func (x *ArtistBindArtworkListResp) GetCount() int32 {
	if x != nil {
		return x.Count
	}
	return 0
}

type ArtworkCopyrightData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ID                    int64  `protobuf:"varint,1,opt,name=ID,proto3" json:"ID"`
	CreatedAt             int32  `protobuf:"varint,2,opt,name=createdAt,proto3" json:"createdAt"`
	UpdatedAt             int32  `protobuf:"varint,3,opt,name=updatedAt,proto3" json:"updatedAt"`
	DeletedAt             int64  `protobuf:"varint,4,opt,name=deletedAt,proto3" json:"deletedAt"`
	ArtworkUuid           string `protobuf:"bytes,6,opt,name=artworkUuid,proto3" json:"artworkUuid"`                      //画作uuid
	Number                string `protobuf:"bytes,7,opt,name=number,proto3" json:"number"`                                //版权编号
	SerialNumber          string `protobuf:"bytes,8,opt,name=serialNumber,proto3" json:"serialNumber"`                    //著作权登记流水号
	ApplyTime             string `protobuf:"bytes,9,opt,name=applyTime,proto3" json:"applyTime"`                          //申请时间
	RegisterNumber        string `protobuf:"bytes,10,opt,name=registerNumber,proto3" json:"registerNumber"`               //作品登记号
	CertDigi              string `protobuf:"bytes,11,opt,name=certDigi,proto3" json:"certDigi"`                           //证书电子样本
	CertRegisterTime      string `protobuf:"bytes,12,opt,name=certRegisterTime,proto3" json:"certRegisterTime"`           //证书登记日期
	AgentRegisterContract string `protobuf:"bytes,13,opt,name=agentRegisterContract,proto3" json:"agentRegisterContract"` //代理登记合同
	PromiseLetterUrl      string `protobuf:"bytes,14,opt,name=promiseLetterUrl,proto3" json:"promiseLetterUrl"`           //承诺书
	EntrustLetterUrl      string `protobuf:"bytes,15,opt,name=entrustLetterUrl,proto3" json:"entrustLetterUrl"`           //委托书
	LicenseAgreementUrl   string `protobuf:"bytes,16,opt,name=licenseAgreementUrl,proto3" json:"licenseAgreementUrl"`     //画作授权协议
	DownloadAgreementUrl  string `protobuf:"bytes,17,opt,name=downloadAgreementUrl,proto3" json:"downloadAgreementUrl"`   //画作授权协议下载地址
	CopyrightStatus       string `protobuf:"bytes,18,opt,name=copyrightStatus,proto3" json:"copyrightStatus"`             //版权状态
	CopyrightSource       int32  `protobuf:"varint,19,opt,name=copyrightSource,proto3" json:"copyrightSource"`            //版权来源
}

func (x *ArtworkCopyrightData) Reset() {
	*x = ArtworkCopyrightData{}
	mi := &file_pb_artwork_query_proto_msgTypes[75]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ArtworkCopyrightData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ArtworkCopyrightData) ProtoMessage() {}

func (x *ArtworkCopyrightData) ProtoReflect() protoreflect.Message {
	mi := &file_pb_artwork_query_proto_msgTypes[75]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ArtworkCopyrightData.ProtoReflect.Descriptor instead.
func (*ArtworkCopyrightData) Descriptor() ([]byte, []int) {
	return file_pb_artwork_query_proto_rawDescGZIP(), []int{75}
}

func (x *ArtworkCopyrightData) GetID() int64 {
	if x != nil {
		return x.ID
	}
	return 0
}

func (x *ArtworkCopyrightData) GetCreatedAt() int32 {
	if x != nil {
		return x.CreatedAt
	}
	return 0
}

func (x *ArtworkCopyrightData) GetUpdatedAt() int32 {
	if x != nil {
		return x.UpdatedAt
	}
	return 0
}

func (x *ArtworkCopyrightData) GetDeletedAt() int64 {
	if x != nil {
		return x.DeletedAt
	}
	return 0
}

func (x *ArtworkCopyrightData) GetArtworkUuid() string {
	if x != nil {
		return x.ArtworkUuid
	}
	return ""
}

func (x *ArtworkCopyrightData) GetNumber() string {
	if x != nil {
		return x.Number
	}
	return ""
}

func (x *ArtworkCopyrightData) GetSerialNumber() string {
	if x != nil {
		return x.SerialNumber
	}
	return ""
}

func (x *ArtworkCopyrightData) GetApplyTime() string {
	if x != nil {
		return x.ApplyTime
	}
	return ""
}

func (x *ArtworkCopyrightData) GetRegisterNumber() string {
	if x != nil {
		return x.RegisterNumber
	}
	return ""
}

func (x *ArtworkCopyrightData) GetCertDigi() string {
	if x != nil {
		return x.CertDigi
	}
	return ""
}

func (x *ArtworkCopyrightData) GetCertRegisterTime() string {
	if x != nil {
		return x.CertRegisterTime
	}
	return ""
}

func (x *ArtworkCopyrightData) GetAgentRegisterContract() string {
	if x != nil {
		return x.AgentRegisterContract
	}
	return ""
}

func (x *ArtworkCopyrightData) GetPromiseLetterUrl() string {
	if x != nil {
		return x.PromiseLetterUrl
	}
	return ""
}

func (x *ArtworkCopyrightData) GetEntrustLetterUrl() string {
	if x != nil {
		return x.EntrustLetterUrl
	}
	return ""
}

func (x *ArtworkCopyrightData) GetLicenseAgreementUrl() string {
	if x != nil {
		return x.LicenseAgreementUrl
	}
	return ""
}

func (x *ArtworkCopyrightData) GetDownloadAgreementUrl() string {
	if x != nil {
		return x.DownloadAgreementUrl
	}
	return ""
}

func (x *ArtworkCopyrightData) GetCopyrightStatus() string {
	if x != nil {
		return x.CopyrightStatus
	}
	return ""
}

func (x *ArtworkCopyrightData) GetCopyrightSource() int32 {
	if x != nil {
		return x.CopyrightSource
	}
	return 0
}

type GetArtworkCopyrightListRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Query    *ArtworkCopyrightData `protobuf:"bytes,1,opt,name=query,proto3" json:"query"`
	Page     int64                 `protobuf:"varint,2,opt,name=page,proto3" json:"page"`
	PageSize int64                 `protobuf:"varint,3,opt,name=pageSize,proto3" json:"pageSize"`
	Where    string                `protobuf:"bytes,4,opt,name=where,proto3" json:"where"`
	Order    string                `protobuf:"bytes,5,opt,name=order,proto3" json:"order"`
}

func (x *GetArtworkCopyrightListRequest) Reset() {
	*x = GetArtworkCopyrightListRequest{}
	mi := &file_pb_artwork_query_proto_msgTypes[76]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetArtworkCopyrightListRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetArtworkCopyrightListRequest) ProtoMessage() {}

func (x *GetArtworkCopyrightListRequest) ProtoReflect() protoreflect.Message {
	mi := &file_pb_artwork_query_proto_msgTypes[76]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetArtworkCopyrightListRequest.ProtoReflect.Descriptor instead.
func (*GetArtworkCopyrightListRequest) Descriptor() ([]byte, []int) {
	return file_pb_artwork_query_proto_rawDescGZIP(), []int{76}
}

func (x *GetArtworkCopyrightListRequest) GetQuery() *ArtworkCopyrightData {
	if x != nil {
		return x.Query
	}
	return nil
}

func (x *GetArtworkCopyrightListRequest) GetPage() int64 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *GetArtworkCopyrightListRequest) GetPageSize() int64 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *GetArtworkCopyrightListRequest) GetWhere() string {
	if x != nil {
		return x.Where
	}
	return ""
}

func (x *GetArtworkCopyrightListRequest) GetOrder() string {
	if x != nil {
		return x.Order
	}
	return ""
}

type GetArtworkCopyrightListResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	List     []*ArtworkCopyrightData `protobuf:"bytes,1,rep,name=list,proto3" json:"list"`
	Page     int64                   `protobuf:"varint,2,opt,name=page,proto3" json:"page"`
	PageSize int64                   `protobuf:"varint,3,opt,name=pageSize,proto3" json:"pageSize"`
	Total    int64                   `protobuf:"varint,4,opt,name=Total,proto3" json:"Total"`
}

func (x *GetArtworkCopyrightListResp) Reset() {
	*x = GetArtworkCopyrightListResp{}
	mi := &file_pb_artwork_query_proto_msgTypes[77]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetArtworkCopyrightListResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetArtworkCopyrightListResp) ProtoMessage() {}

func (x *GetArtworkCopyrightListResp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_artwork_query_proto_msgTypes[77]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetArtworkCopyrightListResp.ProtoReflect.Descriptor instead.
func (*GetArtworkCopyrightListResp) Descriptor() ([]byte, []int) {
	return file_pb_artwork_query_proto_rawDescGZIP(), []int{77}
}

func (x *GetArtworkCopyrightListResp) GetList() []*ArtworkCopyrightData {
	if x != nil {
		return x.List
	}
	return nil
}

func (x *GetArtworkCopyrightListResp) GetPage() int64 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *GetArtworkCopyrightListResp) GetPageSize() int64 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *GetArtworkCopyrightListResp) GetTotal() int64 {
	if x != nil {
		return x.Total
	}
	return 0
}

// GetArtworkDataBatch
type GetArtworkDataBatchReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ArtworkUuids []string `protobuf:"bytes,1,rep,name=ArtworkUuids,proto3" json:"ArtworkUuids"`
}

func (x *GetArtworkDataBatchReq) Reset() {
	*x = GetArtworkDataBatchReq{}
	mi := &file_pb_artwork_query_proto_msgTypes[78]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetArtworkDataBatchReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetArtworkDataBatchReq) ProtoMessage() {}

func (x *GetArtworkDataBatchReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_artwork_query_proto_msgTypes[78]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetArtworkDataBatchReq.ProtoReflect.Descriptor instead.
func (*GetArtworkDataBatchReq) Descriptor() ([]byte, []int) {
	return file_pb_artwork_query_proto_rawDescGZIP(), []int{78}
}

func (x *GetArtworkDataBatchReq) GetArtworkUuids() []string {
	if x != nil {
		return x.ArtworkUuids
	}
	return nil
}

type GetArtworkDataBatchResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Data []*GetArtworkDataBatchResp_ArtworkInfo `protobuf:"bytes,1,rep,name=Data,proto3" json:"Data"`
}

func (x *GetArtworkDataBatchResp) Reset() {
	*x = GetArtworkDataBatchResp{}
	mi := &file_pb_artwork_query_proto_msgTypes[79]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetArtworkDataBatchResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetArtworkDataBatchResp) ProtoMessage() {}

func (x *GetArtworkDataBatchResp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_artwork_query_proto_msgTypes[79]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetArtworkDataBatchResp.ProtoReflect.Descriptor instead.
func (*GetArtworkDataBatchResp) Descriptor() ([]byte, []int) {
	return file_pb_artwork_query_proto_rawDescGZIP(), []int{79}
}

func (x *GetArtworkDataBatchResp) GetData() []*GetArtworkDataBatchResp_ArtworkInfo {
	if x != nil {
		return x.Data
	}
	return nil
}

type BatchDciListReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ArtistName        string   `protobuf:"bytes,1,opt,name=ArtistName,proto3" json:"ArtistName"`
	ArtworkName       string   `protobuf:"bytes,2,opt,name=ArtworkName,proto3" json:"ArtworkName"`
	Page              int32    `protobuf:"varint,3,opt,name=Page,proto3" json:"Page"`
	PageSize          int32    `protobuf:"varint,4,opt,name=PageSize,proto3" json:"PageSize"`
	NotInArtworkUuids []string `protobuf:"bytes,5,rep,name=NotInArtworkUuids,proto3" json:"NotInArtworkUuids"`
}

func (x *BatchDciListReq) Reset() {
	*x = BatchDciListReq{}
	mi := &file_pb_artwork_query_proto_msgTypes[80]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BatchDciListReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BatchDciListReq) ProtoMessage() {}

func (x *BatchDciListReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_artwork_query_proto_msgTypes[80]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BatchDciListReq.ProtoReflect.Descriptor instead.
func (*BatchDciListReq) Descriptor() ([]byte, []int) {
	return file_pb_artwork_query_proto_rawDescGZIP(), []int{80}
}

func (x *BatchDciListReq) GetArtistName() string {
	if x != nil {
		return x.ArtistName
	}
	return ""
}

func (x *BatchDciListReq) GetArtworkName() string {
	if x != nil {
		return x.ArtworkName
	}
	return ""
}

func (x *BatchDciListReq) GetPage() int32 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *BatchDciListReq) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *BatchDciListReq) GetNotInArtworkUuids() []string {
	if x != nil {
		return x.NotInArtworkUuids
	}
	return nil
}

type BatchDciListResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Data  []*BatchDciListResp_Info `protobuf:"bytes,1,rep,name=Data,proto3" json:"Data"`
	Count int32                    `protobuf:"varint,2,opt,name=Count,proto3" json:"Count"`
}

func (x *BatchDciListResp) Reset() {
	*x = BatchDciListResp{}
	mi := &file_pb_artwork_query_proto_msgTypes[81]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BatchDciListResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BatchDciListResp) ProtoMessage() {}

func (x *BatchDciListResp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_artwork_query_proto_msgTypes[81]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BatchDciListResp.ProtoReflect.Descriptor instead.
func (*BatchDciListResp) Descriptor() ([]byte, []int) {
	return file_pb_artwork_query_proto_rawDescGZIP(), []int{81}
}

func (x *BatchDciListResp) GetData() []*BatchDciListResp_Info {
	if x != nil {
		return x.Data
	}
	return nil
}

func (x *BatchDciListResp) GetCount() int32 {
	if x != nil {
		return x.Count
	}
	return 0
}

type ArtworkArtistListReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ArtistName string `protobuf:"bytes,1,opt,name=ArtistName,proto3" json:"ArtistName"`
	ArtistUuid string `protobuf:"bytes,2,opt,name=ArtistUuid,proto3" json:"ArtistUuid"`
}

func (x *ArtworkArtistListReq) Reset() {
	*x = ArtworkArtistListReq{}
	mi := &file_pb_artwork_query_proto_msgTypes[82]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ArtworkArtistListReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ArtworkArtistListReq) ProtoMessage() {}

func (x *ArtworkArtistListReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_artwork_query_proto_msgTypes[82]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ArtworkArtistListReq.ProtoReflect.Descriptor instead.
func (*ArtworkArtistListReq) Descriptor() ([]byte, []int) {
	return file_pb_artwork_query_proto_rawDescGZIP(), []int{82}
}

func (x *ArtworkArtistListReq) GetArtistName() string {
	if x != nil {
		return x.ArtistName
	}
	return ""
}

func (x *ArtworkArtistListReq) GetArtistUuid() string {
	if x != nil {
		return x.ArtistUuid
	}
	return ""
}

type ArtworkArtistListResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Data []*ArtworkArtistListResp_Info `protobuf:"bytes,1,rep,name=Data,proto3" json:"Data"`
}

func (x *ArtworkArtistListResp) Reset() {
	*x = ArtworkArtistListResp{}
	mi := &file_pb_artwork_query_proto_msgTypes[83]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ArtworkArtistListResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ArtworkArtistListResp) ProtoMessage() {}

func (x *ArtworkArtistListResp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_artwork_query_proto_msgTypes[83]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ArtworkArtistListResp.ProtoReflect.Descriptor instead.
func (*ArtworkArtistListResp) Descriptor() ([]byte, []int) {
	return file_pb_artwork_query_proto_rawDescGZIP(), []int{83}
}

func (x *ArtworkArtistListResp) GetData() []*ArtworkArtistListResp_Info {
	if x != nil {
		return x.Data
	}
	return nil
}

type StorageListReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Page               int32    `protobuf:"varint,1,opt,name=Page,proto3" json:"Page"`
	PageSize           int32    `protobuf:"varint,2,opt,name=PageSize,proto3" json:"PageSize"`
	Flag               int32    `protobuf:"varint,3,opt,name=Flag,proto3" json:"Flag"`
	ArtistName         string   `protobuf:"bytes,4,opt,name=ArtistName,proto3" json:"ArtistName"`
	ArtworkName        string   `protobuf:"bytes,5,opt,name=ArtworkName,proto3" json:"ArtworkName"`
	Tfnum              string   `protobuf:"bytes,6,opt,name=Tfnum,proto3" json:"Tfnum"`
	Tnum               string   `protobuf:"bytes,7,opt,name=Tnum,proto3" json:"Tnum"`
	FilterArtworkUuids []string `protobuf:"bytes,8,rep,name=FilterArtworkUuids,proto3" json:"FilterArtworkUuids"`
	ArtistUuids        []string `protobuf:"bytes,9,rep,name=ArtistUuids,proto3" json:"ArtistUuids"`
	Keywords           string   `protobuf:"bytes,10,opt,name=Keywords,proto3" json:"Keywords"`
}

func (x *StorageListReq) Reset() {
	*x = StorageListReq{}
	mi := &file_pb_artwork_query_proto_msgTypes[84]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *StorageListReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StorageListReq) ProtoMessage() {}

func (x *StorageListReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_artwork_query_proto_msgTypes[84]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StorageListReq.ProtoReflect.Descriptor instead.
func (*StorageListReq) Descriptor() ([]byte, []int) {
	return file_pb_artwork_query_proto_rawDescGZIP(), []int{84}
}

func (x *StorageListReq) GetPage() int32 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *StorageListReq) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *StorageListReq) GetFlag() int32 {
	if x != nil {
		return x.Flag
	}
	return 0
}

func (x *StorageListReq) GetArtistName() string {
	if x != nil {
		return x.ArtistName
	}
	return ""
}

func (x *StorageListReq) GetArtworkName() string {
	if x != nil {
		return x.ArtworkName
	}
	return ""
}

func (x *StorageListReq) GetTfnum() string {
	if x != nil {
		return x.Tfnum
	}
	return ""
}

func (x *StorageListReq) GetTnum() string {
	if x != nil {
		return x.Tnum
	}
	return ""
}

func (x *StorageListReq) GetFilterArtworkUuids() []string {
	if x != nil {
		return x.FilterArtworkUuids
	}
	return nil
}

func (x *StorageListReq) GetArtistUuids() []string {
	if x != nil {
		return x.ArtistUuids
	}
	return nil
}

func (x *StorageListReq) GetKeywords() string {
	if x != nil {
		return x.Keywords
	}
	return ""
}

type StorageListResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Data  []*StorageListResp_Info `protobuf:"bytes,1,rep,name=Data,proto3" json:"Data"`
	Count int32                   `protobuf:"varint,2,opt,name=Count,proto3" json:"Count"`
}

func (x *StorageListResp) Reset() {
	*x = StorageListResp{}
	mi := &file_pb_artwork_query_proto_msgTypes[85]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *StorageListResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StorageListResp) ProtoMessage() {}

func (x *StorageListResp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_artwork_query_proto_msgTypes[85]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StorageListResp.ProtoReflect.Descriptor instead.
func (*StorageListResp) Descriptor() ([]byte, []int) {
	return file_pb_artwork_query_proto_rawDescGZIP(), []int{85}
}

func (x *StorageListResp) GetData() []*StorageListResp_Info {
	if x != nil {
		return x.Data
	}
	return nil
}

func (x *StorageListResp) GetCount() int32 {
	if x != nil {
		return x.Count
	}
	return 0
}

type SecondArtworkListReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Page     int32  `protobuf:"varint,1,opt,name=Page,proto3" json:"Page"`
	PageSize int32  `protobuf:"varint,2,opt,name=PageSize,proto3" json:"PageSize"`
	Keyword  string `protobuf:"bytes,3,opt,name=Keyword,proto3" json:"Keyword"`
}

func (x *SecondArtworkListReq) Reset() {
	*x = SecondArtworkListReq{}
	mi := &file_pb_artwork_query_proto_msgTypes[86]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SecondArtworkListReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SecondArtworkListReq) ProtoMessage() {}

func (x *SecondArtworkListReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_artwork_query_proto_msgTypes[86]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SecondArtworkListReq.ProtoReflect.Descriptor instead.
func (*SecondArtworkListReq) Descriptor() ([]byte, []int) {
	return file_pb_artwork_query_proto_rawDescGZIP(), []int{86}
}

func (x *SecondArtworkListReq) GetPage() int32 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *SecondArtworkListReq) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *SecondArtworkListReq) GetKeyword() string {
	if x != nil {
		return x.Keyword
	}
	return ""
}

type SecondArtworkListResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Data  []*SecondArtworkListResp_Info `protobuf:"bytes,1,rep,name=Data,proto3" json:"Data"`
	Count int32                         `protobuf:"varint,2,opt,name=Count,proto3" json:"Count"`
}

func (x *SecondArtworkListResp) Reset() {
	*x = SecondArtworkListResp{}
	mi := &file_pb_artwork_query_proto_msgTypes[87]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SecondArtworkListResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SecondArtworkListResp) ProtoMessage() {}

func (x *SecondArtworkListResp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_artwork_query_proto_msgTypes[87]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SecondArtworkListResp.ProtoReflect.Descriptor instead.
func (*SecondArtworkListResp) Descriptor() ([]byte, []int) {
	return file_pb_artwork_query_proto_rawDescGZIP(), []int{87}
}

func (x *SecondArtworkListResp) GetData() []*SecondArtworkListResp_Info {
	if x != nil {
		return x.Data
	}
	return nil
}

func (x *SecondArtworkListResp) GetCount() int32 {
	if x != nil {
		return x.Count
	}
	return 0
}

type ArtistsDataByUuidsReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ArtworkUuids []string `protobuf:"bytes,1,rep,name=ArtworkUuids,proto3" json:"ArtworkUuids"`
}

func (x *ArtistsDataByUuidsReq) Reset() {
	*x = ArtistsDataByUuidsReq{}
	mi := &file_pb_artwork_query_proto_msgTypes[88]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ArtistsDataByUuidsReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ArtistsDataByUuidsReq) ProtoMessage() {}

func (x *ArtistsDataByUuidsReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_artwork_query_proto_msgTypes[88]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ArtistsDataByUuidsReq.ProtoReflect.Descriptor instead.
func (*ArtistsDataByUuidsReq) Descriptor() ([]byte, []int) {
	return file_pb_artwork_query_proto_rawDescGZIP(), []int{88}
}

func (x *ArtistsDataByUuidsReq) GetArtworkUuids() []string {
	if x != nil {
		return x.ArtworkUuids
	}
	return nil
}

type ArtistsDataByUuidsResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Data []*ArtistsDataByUuidsResp_Info `protobuf:"bytes,1,rep,name=Data,proto3" json:"Data"`
}

func (x *ArtistsDataByUuidsResp) Reset() {
	*x = ArtistsDataByUuidsResp{}
	mi := &file_pb_artwork_query_proto_msgTypes[89]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ArtistsDataByUuidsResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ArtistsDataByUuidsResp) ProtoMessage() {}

func (x *ArtistsDataByUuidsResp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_artwork_query_proto_msgTypes[89]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ArtistsDataByUuidsResp.ProtoReflect.Descriptor instead.
func (*ArtistsDataByUuidsResp) Descriptor() ([]byte, []int) {
	return file_pb_artwork_query_proto_rawDescGZIP(), []int{89}
}

func (x *ArtistsDataByUuidsResp) GetData() []*ArtistsDataByUuidsResp_Info {
	if x != nil {
		return x.Data
	}
	return nil
}

type ArtworkListResponse_Info struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id                  int32   `protobuf:"varint,1,opt,name=Id,json=id,proto3" json:"Id"`
	ArtworkUuid         string  `protobuf:"bytes,2,opt,name=ArtworkUuid,json=artwork_uuid,proto3" json:"ArtworkUuid"`
	ArtistName          string  `protobuf:"bytes,3,opt,name=ArtistName,json=artist_name,proto3" json:"ArtistName"`
	ArtworkName         string  `protobuf:"bytes,4,opt,name=ArtworkName,json=artwork_name,proto3" json:"ArtworkName"`
	Length              int32   `protobuf:"varint,5,opt,name=Length,json=length,proto3" json:"Length"`
	Width               int32   `protobuf:"varint,6,opt,name=Width,json=width,proto3" json:"Width"`
	Ruler               int32   `protobuf:"varint,7,opt,name=Ruler,json=ruler,proto3" json:"Ruler"`
	Num                 string  `protobuf:"bytes,8,opt,name=Num,json=num,proto3" json:"Num"`
	HdPic               string  `protobuf:"bytes,9,opt,name=HdPic,json=hd_pic,proto3" json:"HdPic"`
	StorageStatus       int32   `protobuf:"varint,10,opt,name=StorageStatus,json=storage_status,proto3" json:"StorageStatus"`
	SaleStatus          int32   `protobuf:"varint,11,opt,name=SaleStatus,json=sale_status,proto3" json:"SaleStatus"`
	InStorageTime       string  `protobuf:"bytes,12,opt,name=InStorageTime,json=in_storage_time,proto3" json:"InStorageTime"`
	Tfnum               string  `protobuf:"bytes,16,opt,name=Tfnum,json=tfnum,proto3" json:"Tfnum"`
	DigiArtImg          string  `protobuf:"bytes,17,opt,name=DigiArtImg,json=digi_art_img,proto3" json:"DigiArtImg"`
	PhotoPic            string  `protobuf:"bytes,18,opt,name=PhotoPic,json=photo_pic,proto3" json:"PhotoPic"`
	PriceRun            float32 `protobuf:"fixed32,19,opt,name=PriceRun,json=price_run,proto3" json:"PriceRun"`
	ArtworkType         float32 `protobuf:"fixed32,20,opt,name=ArtworkType,json=artwork_type,proto3" json:"ArtworkType"`
	Signdate            string  `protobuf:"bytes,21,opt,name=Signdate,json=signdate,proto3" json:"Signdate"`
	SigndateOver        bool    `protobuf:"varint,22,opt,name=SigndateOver,json=sign_date_over,proto3" json:"SigndateOver"`
	AuthImg             string  `protobuf:"bytes,23,opt,name=AuthImg,json=auth_img,proto3" json:"AuthImg"`
	SaleId              int32   `protobuf:"varint,24,opt,name=SaleId,json=sale_id,proto3" json:"SaleId"`
	SaleAddress         string  `protobuf:"bytes,25,opt,name=SaleAddress,json=sale_address,proto3" json:"SaleAddress"`
	ArtShowStatus       int32   `protobuf:"varint,26,opt,name=ArtShowStatus,json=artshow_status,proto3" json:"ArtShowStatus"`
	FilterStatus        int32   `protobuf:"varint,27,opt,name=FilterStatus,json=filter_status,proto3" json:"FilterStatus"`
	RecheckState        int32   `protobuf:"varint,28,opt,name=RecheckState,proto3" json:"RecheckState"`
	ArtistShowCount     string  `protobuf:"bytes,29,opt,name=artistShowCount,proto3" json:"artistShowCount"`
	ArtistUuid          string  `protobuf:"bytes,30,opt,name=ArtistUuid,json=artistUuid,proto3" json:"ArtistUuid"`
	ArtshowId           string  `protobuf:"bytes,31,opt,name=ArtshowId,proto3" json:"ArtshowId"`
	CopyrightStatus     string  `protobuf:"bytes,32,opt,name=CopyrightStatus,proto3" json:"CopyrightStatus"`
	LicenseAgreementUrl string  `protobuf:"bytes,33,opt,name=LicenseAgreementUrl,proto3" json:"LicenseAgreementUrl"`
	BlackListStatus     int32   `protobuf:"varint,34,opt,name=BlackListStatus,proto3" json:"BlackListStatus"`
	Tnum                string  `protobuf:"bytes,35,opt,name=Tnum,proto3" json:"Tnum"`
}

func (x *ArtworkListResponse_Info) Reset() {
	*x = ArtworkListResponse_Info{}
	mi := &file_pb_artwork_query_proto_msgTypes[90]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ArtworkListResponse_Info) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ArtworkListResponse_Info) ProtoMessage() {}

func (x *ArtworkListResponse_Info) ProtoReflect() protoreflect.Message {
	mi := &file_pb_artwork_query_proto_msgTypes[90]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ArtworkListResponse_Info.ProtoReflect.Descriptor instead.
func (*ArtworkListResponse_Info) Descriptor() ([]byte, []int) {
	return file_pb_artwork_query_proto_rawDescGZIP(), []int{1, 0}
}

func (x *ArtworkListResponse_Info) GetId() int32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *ArtworkListResponse_Info) GetArtworkUuid() string {
	if x != nil {
		return x.ArtworkUuid
	}
	return ""
}

func (x *ArtworkListResponse_Info) GetArtistName() string {
	if x != nil {
		return x.ArtistName
	}
	return ""
}

func (x *ArtworkListResponse_Info) GetArtworkName() string {
	if x != nil {
		return x.ArtworkName
	}
	return ""
}

func (x *ArtworkListResponse_Info) GetLength() int32 {
	if x != nil {
		return x.Length
	}
	return 0
}

func (x *ArtworkListResponse_Info) GetWidth() int32 {
	if x != nil {
		return x.Width
	}
	return 0
}

func (x *ArtworkListResponse_Info) GetRuler() int32 {
	if x != nil {
		return x.Ruler
	}
	return 0
}

func (x *ArtworkListResponse_Info) GetNum() string {
	if x != nil {
		return x.Num
	}
	return ""
}

func (x *ArtworkListResponse_Info) GetHdPic() string {
	if x != nil {
		return x.HdPic
	}
	return ""
}

func (x *ArtworkListResponse_Info) GetStorageStatus() int32 {
	if x != nil {
		return x.StorageStatus
	}
	return 0
}

func (x *ArtworkListResponse_Info) GetSaleStatus() int32 {
	if x != nil {
		return x.SaleStatus
	}
	return 0
}

func (x *ArtworkListResponse_Info) GetInStorageTime() string {
	if x != nil {
		return x.InStorageTime
	}
	return ""
}

func (x *ArtworkListResponse_Info) GetTfnum() string {
	if x != nil {
		return x.Tfnum
	}
	return ""
}

func (x *ArtworkListResponse_Info) GetDigiArtImg() string {
	if x != nil {
		return x.DigiArtImg
	}
	return ""
}

func (x *ArtworkListResponse_Info) GetPhotoPic() string {
	if x != nil {
		return x.PhotoPic
	}
	return ""
}

func (x *ArtworkListResponse_Info) GetPriceRun() float32 {
	if x != nil {
		return x.PriceRun
	}
	return 0
}

func (x *ArtworkListResponse_Info) GetArtworkType() float32 {
	if x != nil {
		return x.ArtworkType
	}
	return 0
}

func (x *ArtworkListResponse_Info) GetSigndate() string {
	if x != nil {
		return x.Signdate
	}
	return ""
}

func (x *ArtworkListResponse_Info) GetSigndateOver() bool {
	if x != nil {
		return x.SigndateOver
	}
	return false
}

func (x *ArtworkListResponse_Info) GetAuthImg() string {
	if x != nil {
		return x.AuthImg
	}
	return ""
}

func (x *ArtworkListResponse_Info) GetSaleId() int32 {
	if x != nil {
		return x.SaleId
	}
	return 0
}

func (x *ArtworkListResponse_Info) GetSaleAddress() string {
	if x != nil {
		return x.SaleAddress
	}
	return ""
}

func (x *ArtworkListResponse_Info) GetArtShowStatus() int32 {
	if x != nil {
		return x.ArtShowStatus
	}
	return 0
}

func (x *ArtworkListResponse_Info) GetFilterStatus() int32 {
	if x != nil {
		return x.FilterStatus
	}
	return 0
}

func (x *ArtworkListResponse_Info) GetRecheckState() int32 {
	if x != nil {
		return x.RecheckState
	}
	return 0
}

func (x *ArtworkListResponse_Info) GetArtistShowCount() string {
	if x != nil {
		return x.ArtistShowCount
	}
	return ""
}

func (x *ArtworkListResponse_Info) GetArtistUuid() string {
	if x != nil {
		return x.ArtistUuid
	}
	return ""
}

func (x *ArtworkListResponse_Info) GetArtshowId() string {
	if x != nil {
		return x.ArtshowId
	}
	return ""
}

func (x *ArtworkListResponse_Info) GetCopyrightStatus() string {
	if x != nil {
		return x.CopyrightStatus
	}
	return ""
}

func (x *ArtworkListResponse_Info) GetLicenseAgreementUrl() string {
	if x != nil {
		return x.LicenseAgreementUrl
	}
	return ""
}

func (x *ArtworkListResponse_Info) GetBlackListStatus() int32 {
	if x != nil {
		return x.BlackListStatus
	}
	return 0
}

func (x *ArtworkListResponse_Info) GetTnum() string {
	if x != nil {
		return x.Tnum
	}
	return ""
}

type TagsData_TagsInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id      int32  `protobuf:"varint,1,opt,name=Id,json=id,proto3" json:"Id"`
	CatName string `protobuf:"bytes,2,opt,name=CatName,json=cat_name,proto3" json:"CatName"`
}

func (x *TagsData_TagsInfo) Reset() {
	*x = TagsData_TagsInfo{}
	mi := &file_pb_artwork_query_proto_msgTypes[91]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TagsData_TagsInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TagsData_TagsInfo) ProtoMessage() {}

func (x *TagsData_TagsInfo) ProtoReflect() protoreflect.Message {
	mi := &file_pb_artwork_query_proto_msgTypes[91]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TagsData_TagsInfo.ProtoReflect.Descriptor instead.
func (*TagsData_TagsInfo) Descriptor() ([]byte, []int) {
	return file_pb_artwork_query_proto_rawDescGZIP(), []int{11, 0}
}

func (x *TagsData_TagsInfo) GetId() int32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *TagsData_TagsInfo) GetCatName() string {
	if x != nil {
		return x.CatName
	}
	return ""
}

type CatListResponse_CatInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id      int32  `protobuf:"varint,1,opt,name=Id,json=id,proto3" json:"Id"`
	CatName string `protobuf:"bytes,2,opt,name=CatName,json=cat_name,proto3" json:"CatName"`
	Pid     int32  `protobuf:"varint,3,opt,name=Pid,proto3" json:"Pid"`
}

func (x *CatListResponse_CatInfo) Reset() {
	*x = CatListResponse_CatInfo{}
	mi := &file_pb_artwork_query_proto_msgTypes[92]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CatListResponse_CatInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CatListResponse_CatInfo) ProtoMessage() {}

func (x *CatListResponse_CatInfo) ProtoReflect() protoreflect.Message {
	mi := &file_pb_artwork_query_proto_msgTypes[92]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CatListResponse_CatInfo.ProtoReflect.Descriptor instead.
func (*CatListResponse_CatInfo) Descriptor() ([]byte, []int) {
	return file_pb_artwork_query_proto_rawDescGZIP(), []int{14, 0}
}

func (x *CatListResponse_CatInfo) GetId() int32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *CatListResponse_CatInfo) GetCatName() string {
	if x != nil {
		return x.CatName
	}
	return ""
}

func (x *CatListResponse_CatInfo) GetPid() int32 {
	if x != nil {
		return x.Pid
	}
	return 0
}

type BatchBitMapRequest_BitInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	BitIndex  string `protobuf:"bytes,1,opt,name=BitIndex,proto3" json:"BitIndex"`
	ImgOssUrl string `protobuf:"bytes,2,opt,name=ImgOssUrl,proto3" json:"ImgOssUrl"`
	BitName   string `protobuf:"bytes,3,opt,name=BitName,proto3" json:"BitName"`
}

func (x *BatchBitMapRequest_BitInfo) Reset() {
	*x = BatchBitMapRequest_BitInfo{}
	mi := &file_pb_artwork_query_proto_msgTypes[93]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BatchBitMapRequest_BitInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BatchBitMapRequest_BitInfo) ProtoMessage() {}

func (x *BatchBitMapRequest_BitInfo) ProtoReflect() protoreflect.Message {
	mi := &file_pb_artwork_query_proto_msgTypes[93]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BatchBitMapRequest_BitInfo.ProtoReflect.Descriptor instead.
func (*BatchBitMapRequest_BitInfo) Descriptor() ([]byte, []int) {
	return file_pb_artwork_query_proto_rawDescGZIP(), []int{17, 0}
}

func (x *BatchBitMapRequest_BitInfo) GetBitIndex() string {
	if x != nil {
		return x.BitIndex
	}
	return ""
}

func (x *BatchBitMapRequest_BitInfo) GetImgOssUrl() string {
	if x != nil {
		return x.ImgOssUrl
	}
	return ""
}

func (x *BatchBitMapRequest_BitInfo) GetBitName() string {
	if x != nil {
		return x.BitName
	}
	return ""
}

type SyncArtShowIdRequestInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ArtShowUuid  string   `protobuf:"bytes,2,opt,name=ArtShowUuid,json=artshow_uuid,proto3" json:"ArtShowUuid"`
	ArtworkUuids []string `protobuf:"bytes,1,rep,name=ArtworkUuids,json=artwork_uuids,proto3" json:"ArtworkUuids"`
}

func (x *SyncArtShowIdRequestInfo) Reset() {
	*x = SyncArtShowIdRequestInfo{}
	mi := &file_pb_artwork_query_proto_msgTypes[94]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SyncArtShowIdRequestInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SyncArtShowIdRequestInfo) ProtoMessage() {}

func (x *SyncArtShowIdRequestInfo) ProtoReflect() protoreflect.Message {
	mi := &file_pb_artwork_query_proto_msgTypes[94]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SyncArtShowIdRequestInfo.ProtoReflect.Descriptor instead.
func (*SyncArtShowIdRequestInfo) Descriptor() ([]byte, []int) {
	return file_pb_artwork_query_proto_rawDescGZIP(), []int{31, 0}
}

func (x *SyncArtShowIdRequestInfo) GetArtShowUuid() string {
	if x != nil {
		return x.ArtShowUuid
	}
	return ""
}

func (x *SyncArtShowIdRequestInfo) GetArtworkUuids() []string {
	if x != nil {
		return x.ArtworkUuids
	}
	return nil
}

type ShelfListResponse_ShelfInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ShelfId int32  `protobuf:"varint,1,opt,name=ShelfId,json=shelf_id,proto3" json:"ShelfId"`
	ShelfNo string `protobuf:"bytes,2,opt,name=ShelfNo,json=msg,proto3" json:"ShelfNo"`
}

func (x *ShelfListResponse_ShelfInfo) Reset() {
	*x = ShelfListResponse_ShelfInfo{}
	mi := &file_pb_artwork_query_proto_msgTypes[95]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ShelfListResponse_ShelfInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ShelfListResponse_ShelfInfo) ProtoMessage() {}

func (x *ShelfListResponse_ShelfInfo) ProtoReflect() protoreflect.Message {
	mi := &file_pb_artwork_query_proto_msgTypes[95]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ShelfListResponse_ShelfInfo.ProtoReflect.Descriptor instead.
func (*ShelfListResponse_ShelfInfo) Descriptor() ([]byte, []int) {
	return file_pb_artwork_query_proto_rawDescGZIP(), []int{34, 0}
}

func (x *ShelfListResponse_ShelfInfo) GetShelfId() int32 {
	if x != nil {
		return x.ShelfId
	}
	return 0
}

func (x *ShelfListResponse_ShelfInfo) GetShelfNo() string {
	if x != nil {
		return x.ShelfNo
	}
	return ""
}

type ExportArtworkResponse_Info struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ArtworkName         string  `protobuf:"bytes,1,opt,name=ArtworkName,json=artwork_name,proto3" json:"ArtworkName"`
	ArtistName          string  `protobuf:"bytes,2,opt,name=ArtistName,json=artist_name,proto3" json:"ArtistName"`
	ArtCondition        int32   `protobuf:"varint,3,opt,name=ArtCondition,json=art_condition,proto3" json:"ArtCondition"`
	Mountmode           int32   `protobuf:"varint,4,opt,name=Mountmode,json=mountmode,proto3" json:"Mountmode"`
	ArtHorizontal       int32   `protobuf:"varint,5,opt,name=ArtHorizontal,json=art_horizontal,proto3" json:"ArtHorizontal"`
	Size                int32   `protobuf:"varint,6,opt,name=Size,json=size,proto3" json:"Size"`
	Length              int32   `protobuf:"varint,7,opt,name=Length,json=length,proto3" json:"Length"`
	Width               int32   `protobuf:"varint,8,opt,name=Width,json=width,proto3" json:"Width"`
	Ruler               int32   `protobuf:"varint,9,opt,name=Ruler,json=ruler,proto3" json:"Ruler"`
	InscribeDate        string  `protobuf:"bytes,10,opt,name=InscribeDate,json=inscribe_date,proto3" json:"InscribeDate"`
	CreatedDate         string  `protobuf:"bytes,11,opt,name=CreatedDate,json=created_date,proto3" json:"CreatedDate"`
	CreatedAddress      string  `protobuf:"bytes,12,opt,name=CreatedAddress,json=created_address,proto3" json:"CreatedAddress"`
	Abstract            string  `protobuf:"bytes,13,opt,name=Abstract,json=abstract,proto3" json:"Abstract"`
	PriceRuler          float64 `protobuf:"fixed64,14,opt,name=PriceRuler,json=price_ruler,proto3" json:"PriceRuler"`
	PriceCopyright      float64 `protobuf:"fixed64,15,opt,name=PriceCopyright,json=price_copyright,proto3" json:"PriceCopyright"`
	PriceArtwork        float64 `protobuf:"fixed64,16,opt,name=PriceArtwork,json=price_artwork,proto3" json:"PriceArtwork"`
	PriceMarket         float64 `protobuf:"fixed64,17,opt,name=PriceMarket,json=price_market,proto3" json:"PriceMarket"`
	Belong              int32   `protobuf:"varint,18,opt,name=Belong,json=belong,proto3" json:"Belong"`
	FlowState           int32   `protobuf:"varint,19,opt,name=FlowState,json=flow_state,proto3" json:"FlowState"`
	ArtQuality          int32   `protobuf:"varint,20,opt,name=ArtQuality,json=art_quality,proto3" json:"ArtQuality"`
	IncompletePic       string  `protobuf:"bytes,21,opt,name=IncompletePic,json=incomplete_pic,proto3" json:"IncompletePic"`
	Signpic             string  `protobuf:"bytes,22,opt,name=Signpic,json=signpic,proto3" json:"Signpic"`
	Sealpic             string  `protobuf:"bytes,23,opt,name=Sealpic,json=sealpic,proto3" json:"Sealpic"`
	ArtistPhoto         string  `protobuf:"bytes,24,opt,name=ArtistPhoto,json=artist_photo,proto3" json:"ArtistPhoto"`
	PhotoPic            string  `protobuf:"bytes,25,opt,name=PhotoPic,json=photo_pic,proto3" json:"PhotoPic"`
	HdPic               string  `protobuf:"bytes,26,opt,name=HdPic,json=hd_pic,proto3" json:"HdPic"`
	Material            int32   `protobuf:"varint,27,opt,name=Material,json=material,proto3" json:"Material"`
	ArtworkUuid         string  `protobuf:"bytes,28,opt,name=ArtworkUuid,json=artwork_uuid,proto3" json:"ArtworkUuid"`
	ArtistUuid          string  `protobuf:"bytes,29,opt,name=ArtistUuid,json=artist_uuid,proto3" json:"ArtistUuid"`
	ArtworkType         int32   `protobuf:"varint,30,opt,name=ArtworkType,json=artwork_type,proto3" json:"ArtworkType"`
	ArtType             int32   `protobuf:"varint,31,opt,name=ArtType,json=art_type,proto3" json:"ArtType"`
	ArtTitle            int32   `protobuf:"varint,32,opt,name=ArtTitle,json=art_title,proto3" json:"ArtTitle"`
	ArtStyle            int32   `protobuf:"varint,33,opt,name=ArtStyle,json=art_style,proto3" json:"ArtStyle"`
	Color               int32   `protobuf:"varint,34,opt,name=Color,json=color,proto3" json:"Color"`
	PenTechniques       string  `protobuf:"bytes,35,opt,name=PenTechniques,json=pen_techniques,proto3" json:"PenTechniques"`
	ArtIdea             string  `protobuf:"bytes,36,opt,name=ArtIdea,json=art_idea,proto3" json:"ArtIdea"`
	ExpressIdea         string  `protobuf:"bytes,37,opt,name=ExpressIdea,json=express_idea,proto3" json:"ExpressIdea"`
	ArtStory            string  `protobuf:"bytes,38,opt,name=ArtStory,json=art_story,proto3" json:"ArtStory"`
	FirstPublish        string  `protobuf:"bytes,39,opt,name=FirstPublish,json=first_publish,proto3" json:"FirstPublish"`
	FirstPublishImg     string  `protobuf:"bytes,40,opt,name=FirstPublish_img,json=first_publish_img,proto3" json:"FirstPublish_img"`
	FirstName           string  `protobuf:"bytes,41,opt,name=FirstName,json=first_name,proto3" json:"FirstName"`
	FirstNameImg        string  `protobuf:"bytes,42,opt,name=FirstName_img,json=first_name_img,proto3" json:"FirstName_img"`
	ThirdComment        string  `protobuf:"bytes,43,opt,name=ThirdComment,json=third_comment,proto3" json:"ThirdComment"`
	SprayPosition       string  `protobuf:"bytes,44,opt,name=SprayPosition,json=spray_position,proto3" json:"SprayPosition"`
	SprayRemark         string  `protobuf:"bytes,45,opt,name=SprayRemark,json=spray_remark,proto3" json:"SprayRemark"`
	DigiShootDate       string  `protobuf:"bytes,46,opt,name=DigiShootDate,json=digi_shoot_date,proto3" json:"DigiShootDate"`
	DigiMakeDate        string  `protobuf:"bytes,47,opt,name=DigiMakeDate,json=digi_make_date,proto3" json:"DigiMakeDate"`
	DigiArtImg          string  `protobuf:"bytes,48,opt,name=DigiArtImg,json=digi_art_img,proto3" json:"DigiArtImg"`
	DigiArtCopyrightImg string  `protobuf:"bytes,49,opt,name=DigiArtCopyrightImg,json=digi_art_copyright_img,proto3" json:"DigiArtCopyrightImg"`
	CopyrightHash       string  `protobuf:"bytes,50,opt,name=CopyrightHash,json=copyright_hash,proto3" json:"CopyrightHash"`
	RealrightHash       string  `protobuf:"bytes,51,opt,name=RealrightHash,json=realright_hash,proto3" json:"RealrightHash"`
	AuthDataHash        string  `protobuf:"bytes,52,opt,name=AuthDataHash,json=auth_data_hash,proto3" json:"AuthDataHash"`
	WtRealHash          string  `protobuf:"bytes,53,opt,name=WtRealHash,json=wt_real_hash,proto3" json:"WtRealHash"`
	CxRealHash          string  `protobuf:"bytes,54,opt,name=CxRealHash,json=cx_real_hash,proto3" json:"CxRealHash"`
	BaiduRealHash       string  `protobuf:"bytes,55,opt,name=BaiduRealHash,json=baidu_real_hash,proto3" json:"BaiduRealHash"`
	DigiCopyrightInfo   string  `protobuf:"bytes,56,opt,name=DigiCopyrightInfo,json=digi_copyright_info,proto3" json:"DigiCopyrightInfo"`
	DigiCopyrightFile   string  `protobuf:"bytes,57,opt,name=DigiCopyrightFile,json=digi_copyright_file,proto3" json:"DigiCopyrightFile"`
	Tfnum               string  `protobuf:"bytes,58,opt,name=Tfnum,json=tfnum,proto3" json:"Tfnum"`
	Seqnum              string  `protobuf:"bytes,59,opt,name=Seqnum,json=seqnum,proto3" json:"Seqnum"`
	Uuid                string  `protobuf:"bytes,60,opt,name=Uuid,json=uuid,proto3" json:"Uuid"`
	StorageStatus       int32   `protobuf:"varint,61,opt,name=StorageStatus,json=storage_status,proto3" json:"StorageStatus"`
	SaleStatus          int32   `protobuf:"varint,62,opt,name=SaleStatus,json=sale_status,proto3" json:"SaleStatus"`
	PriceRun            float32 `protobuf:"fixed32,63,opt,name=PriceRun,json=price_run,proto3" json:"PriceRun"`
}

func (x *ExportArtworkResponse_Info) Reset() {
	*x = ExportArtworkResponse_Info{}
	mi := &file_pb_artwork_query_proto_msgTypes[96]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ExportArtworkResponse_Info) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ExportArtworkResponse_Info) ProtoMessage() {}

func (x *ExportArtworkResponse_Info) ProtoReflect() protoreflect.Message {
	mi := &file_pb_artwork_query_proto_msgTypes[96]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ExportArtworkResponse_Info.ProtoReflect.Descriptor instead.
func (*ExportArtworkResponse_Info) Descriptor() ([]byte, []int) {
	return file_pb_artwork_query_proto_rawDescGZIP(), []int{38, 0}
}

func (x *ExportArtworkResponse_Info) GetArtworkName() string {
	if x != nil {
		return x.ArtworkName
	}
	return ""
}

func (x *ExportArtworkResponse_Info) GetArtistName() string {
	if x != nil {
		return x.ArtistName
	}
	return ""
}

func (x *ExportArtworkResponse_Info) GetArtCondition() int32 {
	if x != nil {
		return x.ArtCondition
	}
	return 0
}

func (x *ExportArtworkResponse_Info) GetMountmode() int32 {
	if x != nil {
		return x.Mountmode
	}
	return 0
}

func (x *ExportArtworkResponse_Info) GetArtHorizontal() int32 {
	if x != nil {
		return x.ArtHorizontal
	}
	return 0
}

func (x *ExportArtworkResponse_Info) GetSize() int32 {
	if x != nil {
		return x.Size
	}
	return 0
}

func (x *ExportArtworkResponse_Info) GetLength() int32 {
	if x != nil {
		return x.Length
	}
	return 0
}

func (x *ExportArtworkResponse_Info) GetWidth() int32 {
	if x != nil {
		return x.Width
	}
	return 0
}

func (x *ExportArtworkResponse_Info) GetRuler() int32 {
	if x != nil {
		return x.Ruler
	}
	return 0
}

func (x *ExportArtworkResponse_Info) GetInscribeDate() string {
	if x != nil {
		return x.InscribeDate
	}
	return ""
}

func (x *ExportArtworkResponse_Info) GetCreatedDate() string {
	if x != nil {
		return x.CreatedDate
	}
	return ""
}

func (x *ExportArtworkResponse_Info) GetCreatedAddress() string {
	if x != nil {
		return x.CreatedAddress
	}
	return ""
}

func (x *ExportArtworkResponse_Info) GetAbstract() string {
	if x != nil {
		return x.Abstract
	}
	return ""
}

func (x *ExportArtworkResponse_Info) GetPriceRuler() float64 {
	if x != nil {
		return x.PriceRuler
	}
	return 0
}

func (x *ExportArtworkResponse_Info) GetPriceCopyright() float64 {
	if x != nil {
		return x.PriceCopyright
	}
	return 0
}

func (x *ExportArtworkResponse_Info) GetPriceArtwork() float64 {
	if x != nil {
		return x.PriceArtwork
	}
	return 0
}

func (x *ExportArtworkResponse_Info) GetPriceMarket() float64 {
	if x != nil {
		return x.PriceMarket
	}
	return 0
}

func (x *ExportArtworkResponse_Info) GetBelong() int32 {
	if x != nil {
		return x.Belong
	}
	return 0
}

func (x *ExportArtworkResponse_Info) GetFlowState() int32 {
	if x != nil {
		return x.FlowState
	}
	return 0
}

func (x *ExportArtworkResponse_Info) GetArtQuality() int32 {
	if x != nil {
		return x.ArtQuality
	}
	return 0
}

func (x *ExportArtworkResponse_Info) GetIncompletePic() string {
	if x != nil {
		return x.IncompletePic
	}
	return ""
}

func (x *ExportArtworkResponse_Info) GetSignpic() string {
	if x != nil {
		return x.Signpic
	}
	return ""
}

func (x *ExportArtworkResponse_Info) GetSealpic() string {
	if x != nil {
		return x.Sealpic
	}
	return ""
}

func (x *ExportArtworkResponse_Info) GetArtistPhoto() string {
	if x != nil {
		return x.ArtistPhoto
	}
	return ""
}

func (x *ExportArtworkResponse_Info) GetPhotoPic() string {
	if x != nil {
		return x.PhotoPic
	}
	return ""
}

func (x *ExportArtworkResponse_Info) GetHdPic() string {
	if x != nil {
		return x.HdPic
	}
	return ""
}

func (x *ExportArtworkResponse_Info) GetMaterial() int32 {
	if x != nil {
		return x.Material
	}
	return 0
}

func (x *ExportArtworkResponse_Info) GetArtworkUuid() string {
	if x != nil {
		return x.ArtworkUuid
	}
	return ""
}

func (x *ExportArtworkResponse_Info) GetArtistUuid() string {
	if x != nil {
		return x.ArtistUuid
	}
	return ""
}

func (x *ExportArtworkResponse_Info) GetArtworkType() int32 {
	if x != nil {
		return x.ArtworkType
	}
	return 0
}

func (x *ExportArtworkResponse_Info) GetArtType() int32 {
	if x != nil {
		return x.ArtType
	}
	return 0
}

func (x *ExportArtworkResponse_Info) GetArtTitle() int32 {
	if x != nil {
		return x.ArtTitle
	}
	return 0
}

func (x *ExportArtworkResponse_Info) GetArtStyle() int32 {
	if x != nil {
		return x.ArtStyle
	}
	return 0
}

func (x *ExportArtworkResponse_Info) GetColor() int32 {
	if x != nil {
		return x.Color
	}
	return 0
}

func (x *ExportArtworkResponse_Info) GetPenTechniques() string {
	if x != nil {
		return x.PenTechniques
	}
	return ""
}

func (x *ExportArtworkResponse_Info) GetArtIdea() string {
	if x != nil {
		return x.ArtIdea
	}
	return ""
}

func (x *ExportArtworkResponse_Info) GetExpressIdea() string {
	if x != nil {
		return x.ExpressIdea
	}
	return ""
}

func (x *ExportArtworkResponse_Info) GetArtStory() string {
	if x != nil {
		return x.ArtStory
	}
	return ""
}

func (x *ExportArtworkResponse_Info) GetFirstPublish() string {
	if x != nil {
		return x.FirstPublish
	}
	return ""
}

func (x *ExportArtworkResponse_Info) GetFirstPublishImg() string {
	if x != nil {
		return x.FirstPublishImg
	}
	return ""
}

func (x *ExportArtworkResponse_Info) GetFirstName() string {
	if x != nil {
		return x.FirstName
	}
	return ""
}

func (x *ExportArtworkResponse_Info) GetFirstNameImg() string {
	if x != nil {
		return x.FirstNameImg
	}
	return ""
}

func (x *ExportArtworkResponse_Info) GetThirdComment() string {
	if x != nil {
		return x.ThirdComment
	}
	return ""
}

func (x *ExportArtworkResponse_Info) GetSprayPosition() string {
	if x != nil {
		return x.SprayPosition
	}
	return ""
}

func (x *ExportArtworkResponse_Info) GetSprayRemark() string {
	if x != nil {
		return x.SprayRemark
	}
	return ""
}

func (x *ExportArtworkResponse_Info) GetDigiShootDate() string {
	if x != nil {
		return x.DigiShootDate
	}
	return ""
}

func (x *ExportArtworkResponse_Info) GetDigiMakeDate() string {
	if x != nil {
		return x.DigiMakeDate
	}
	return ""
}

func (x *ExportArtworkResponse_Info) GetDigiArtImg() string {
	if x != nil {
		return x.DigiArtImg
	}
	return ""
}

func (x *ExportArtworkResponse_Info) GetDigiArtCopyrightImg() string {
	if x != nil {
		return x.DigiArtCopyrightImg
	}
	return ""
}

func (x *ExportArtworkResponse_Info) GetCopyrightHash() string {
	if x != nil {
		return x.CopyrightHash
	}
	return ""
}

func (x *ExportArtworkResponse_Info) GetRealrightHash() string {
	if x != nil {
		return x.RealrightHash
	}
	return ""
}

func (x *ExportArtworkResponse_Info) GetAuthDataHash() string {
	if x != nil {
		return x.AuthDataHash
	}
	return ""
}

func (x *ExportArtworkResponse_Info) GetWtRealHash() string {
	if x != nil {
		return x.WtRealHash
	}
	return ""
}

func (x *ExportArtworkResponse_Info) GetCxRealHash() string {
	if x != nil {
		return x.CxRealHash
	}
	return ""
}

func (x *ExportArtworkResponse_Info) GetBaiduRealHash() string {
	if x != nil {
		return x.BaiduRealHash
	}
	return ""
}

func (x *ExportArtworkResponse_Info) GetDigiCopyrightInfo() string {
	if x != nil {
		return x.DigiCopyrightInfo
	}
	return ""
}

func (x *ExportArtworkResponse_Info) GetDigiCopyrightFile() string {
	if x != nil {
		return x.DigiCopyrightFile
	}
	return ""
}

func (x *ExportArtworkResponse_Info) GetTfnum() string {
	if x != nil {
		return x.Tfnum
	}
	return ""
}

func (x *ExportArtworkResponse_Info) GetSeqnum() string {
	if x != nil {
		return x.Seqnum
	}
	return ""
}

func (x *ExportArtworkResponse_Info) GetUuid() string {
	if x != nil {
		return x.Uuid
	}
	return ""
}

func (x *ExportArtworkResponse_Info) GetStorageStatus() int32 {
	if x != nil {
		return x.StorageStatus
	}
	return 0
}

func (x *ExportArtworkResponse_Info) GetSaleStatus() int32 {
	if x != nil {
		return x.SaleStatus
	}
	return 0
}

func (x *ExportArtworkResponse_Info) GetPriceRun() float32 {
	if x != nil {
		return x.PriceRun
	}
	return 0
}

type ExportFieldListResponse_Info struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id         int32  `protobuf:"varint,1,opt,name=Id,json=id,proto3" json:"Id"`
	ColumnDesc string `protobuf:"bytes,2,opt,name=ColumnDesc,json=column_desc,proto3" json:"ColumnDesc"`
}

func (x *ExportFieldListResponse_Info) Reset() {
	*x = ExportFieldListResponse_Info{}
	mi := &file_pb_artwork_query_proto_msgTypes[98]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ExportFieldListResponse_Info) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ExportFieldListResponse_Info) ProtoMessage() {}

func (x *ExportFieldListResponse_Info) ProtoReflect() protoreflect.Message {
	mi := &file_pb_artwork_query_proto_msgTypes[98]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ExportFieldListResponse_Info.ProtoReflect.Descriptor instead.
func (*ExportFieldListResponse_Info) Descriptor() ([]byte, []int) {
	return file_pb_artwork_query_proto_rawDescGZIP(), []int{42, 0}
}

func (x *ExportFieldListResponse_Info) GetId() int32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *ExportFieldListResponse_Info) GetColumnDesc() string {
	if x != nil {
		return x.ColumnDesc
	}
	return ""
}

type ArtworkDataByShowIdResponse_Info struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ArtShowId   string `protobuf:"bytes,1,opt,name=ArtShowId,json=show_id,proto3" json:"ArtShowId"`
	ArtworkUuid string `protobuf:"bytes,2,opt,name=ArtworkUuid,json=artwork_uuid,proto3" json:"ArtworkUuid"`
	Tfnum       string `protobuf:"bytes,3,opt,name=Tfnum,json=tfnum,proto3" json:"Tfnum"`
}

func (x *ArtworkDataByShowIdResponse_Info) Reset() {
	*x = ArtworkDataByShowIdResponse_Info{}
	mi := &file_pb_artwork_query_proto_msgTypes[99]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ArtworkDataByShowIdResponse_Info) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ArtworkDataByShowIdResponse_Info) ProtoMessage() {}

func (x *ArtworkDataByShowIdResponse_Info) ProtoReflect() protoreflect.Message {
	mi := &file_pb_artwork_query_proto_msgTypes[99]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ArtworkDataByShowIdResponse_Info.ProtoReflect.Descriptor instead.
func (*ArtworkDataByShowIdResponse_Info) Descriptor() ([]byte, []int) {
	return file_pb_artwork_query_proto_rawDescGZIP(), []int{44, 0}
}

func (x *ArtworkDataByShowIdResponse_Info) GetArtShowId() string {
	if x != nil {
		return x.ArtShowId
	}
	return ""
}

func (x *ArtworkDataByShowIdResponse_Info) GetArtworkUuid() string {
	if x != nil {
		return x.ArtworkUuid
	}
	return ""
}

func (x *ArtworkDataByShowIdResponse_Info) GetTfnum() string {
	if x != nil {
		return x.Tfnum
	}
	return ""
}

type MyAwListResp_Info struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id               int32   `protobuf:"varint,1,opt,name=Id,proto3" json:"Id"`
	ArtworkUuid      string  `protobuf:"bytes,2,opt,name=ArtworkUuid,json=artwork_uuid,proto3" json:"ArtworkUuid"`
	ArtistName       string  `protobuf:"bytes,3,opt,name=ArtistName,json=artist_name,proto3" json:"ArtistName"`
	ArtworkName      string  `protobuf:"bytes,4,opt,name=ArtworkName,json=artwork_name,proto3" json:"ArtworkName"`
	Length           int32   `protobuf:"varint,5,opt,name=Length,json=length,proto3" json:"Length"`
	Width            int32   `protobuf:"varint,6,opt,name=Width,json=width,proto3" json:"Width"`
	Ruler            int32   `protobuf:"varint,7,opt,name=Ruler,json=ruler,proto3" json:"Ruler"`
	HdPic            string  `protobuf:"bytes,9,opt,name=HdPic,json=hd_pic,proto3" json:"HdPic"`
	WtState          int32   `protobuf:"varint,10,opt,name=WtState,json=wtstate,proto3" json:"WtState"`
	Changchainstate  int32   `protobuf:"varint,11,opt,name=Changchainstate,json=changchainstate,proto3" json:"Changchainstate"`
	BaiduState       int32   `protobuf:"varint,12,opt,name=BaiduState,json=baidustate,proto3" json:"BaiduState"`
	Tfnum            string  `protobuf:"bytes,13,opt,name=Tfnum,json=tfnum,proto3" json:"Tfnum"`
	DigiArtImg       string  `protobuf:"bytes,14,opt,name=DigiArtImg,json=digi_art_img,proto3" json:"DigiArtImg"`
	PhotoPic         string  `protobuf:"bytes,15,opt,name=PhotoPic,json=photo_pic,proto3" json:"PhotoPic"`
	PriceRun         float32 `protobuf:"fixed32,16,opt,name=PriceRun,json=price_run,proto3" json:"PriceRun"`
	BitMapNum        int32   `protobuf:"varint,17,opt,name=BitMapNum,json=bit_map_num,proto3" json:"BitMapNum"`
	BmSixtyNum       int32   `protobuf:"varint,18,opt,name=BmSixtyNum,json=bm_sixty_num,proto3" json:"BmSixtyNum"`
	BmTwoHundredNum  int32   `protobuf:"varint,19,opt,name=BmTwoHundredNum,json=bm_twohoudred_num,proto3" json:"BmTwoHundredNum"`
	BmSixHundredNum  int32   `protobuf:"varint,20,opt,name=BmSixHundredNum,json=bm_sixhoudred_num,proto3" json:"BmSixHundredNum"`
	BmTwoThousandNum int32   `protobuf:"varint,21,opt,name=BmTwoThousandNum,json=bm_twothoundsand_num,proto3" json:"BmTwoThousandNum"`
	Signpic          string  `protobuf:"bytes,22,opt,name=Signpic,json=sign_pic,proto3" json:"Signpic"`
	Sealpic          string  `protobuf:"bytes,23,opt,name=Sealpic,json=seal_pic,proto3" json:"Sealpic"`
	ScheduleTime     string  `protobuf:"bytes,24,opt,name=ScheduleTime,json=schedule_time,proto3" json:"ScheduleTime"`
	State            int32   `protobuf:"varint,25,opt,name=State,json=state,proto3" json:"State"`
	ArtistUuid       string  `protobuf:"bytes,26,opt,name=ArtistUuid,proto3" json:"ArtistUuid"`
	ArtistShowCount  string  `protobuf:"bytes,27,opt,name=artistShowCount,proto3" json:"artistShowCount"`
	BlackListStatus  int32   `protobuf:"varint,28,opt,name=BlackListStatus,proto3" json:"BlackListStatus"`
}

func (x *MyAwListResp_Info) Reset() {
	*x = MyAwListResp_Info{}
	mi := &file_pb_artwork_query_proto_msgTypes[100]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *MyAwListResp_Info) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MyAwListResp_Info) ProtoMessage() {}

func (x *MyAwListResp_Info) ProtoReflect() protoreflect.Message {
	mi := &file_pb_artwork_query_proto_msgTypes[100]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MyAwListResp_Info.ProtoReflect.Descriptor instead.
func (*MyAwListResp_Info) Descriptor() ([]byte, []int) {
	return file_pb_artwork_query_proto_rawDescGZIP(), []int{46, 0}
}

func (x *MyAwListResp_Info) GetId() int32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *MyAwListResp_Info) GetArtworkUuid() string {
	if x != nil {
		return x.ArtworkUuid
	}
	return ""
}

func (x *MyAwListResp_Info) GetArtistName() string {
	if x != nil {
		return x.ArtistName
	}
	return ""
}

func (x *MyAwListResp_Info) GetArtworkName() string {
	if x != nil {
		return x.ArtworkName
	}
	return ""
}

func (x *MyAwListResp_Info) GetLength() int32 {
	if x != nil {
		return x.Length
	}
	return 0
}

func (x *MyAwListResp_Info) GetWidth() int32 {
	if x != nil {
		return x.Width
	}
	return 0
}

func (x *MyAwListResp_Info) GetRuler() int32 {
	if x != nil {
		return x.Ruler
	}
	return 0
}

func (x *MyAwListResp_Info) GetHdPic() string {
	if x != nil {
		return x.HdPic
	}
	return ""
}

func (x *MyAwListResp_Info) GetWtState() int32 {
	if x != nil {
		return x.WtState
	}
	return 0
}

func (x *MyAwListResp_Info) GetChangchainstate() int32 {
	if x != nil {
		return x.Changchainstate
	}
	return 0
}

func (x *MyAwListResp_Info) GetBaiduState() int32 {
	if x != nil {
		return x.BaiduState
	}
	return 0
}

func (x *MyAwListResp_Info) GetTfnum() string {
	if x != nil {
		return x.Tfnum
	}
	return ""
}

func (x *MyAwListResp_Info) GetDigiArtImg() string {
	if x != nil {
		return x.DigiArtImg
	}
	return ""
}

func (x *MyAwListResp_Info) GetPhotoPic() string {
	if x != nil {
		return x.PhotoPic
	}
	return ""
}

func (x *MyAwListResp_Info) GetPriceRun() float32 {
	if x != nil {
		return x.PriceRun
	}
	return 0
}

func (x *MyAwListResp_Info) GetBitMapNum() int32 {
	if x != nil {
		return x.BitMapNum
	}
	return 0
}

func (x *MyAwListResp_Info) GetBmSixtyNum() int32 {
	if x != nil {
		return x.BmSixtyNum
	}
	return 0
}

func (x *MyAwListResp_Info) GetBmTwoHundredNum() int32 {
	if x != nil {
		return x.BmTwoHundredNum
	}
	return 0
}

func (x *MyAwListResp_Info) GetBmSixHundredNum() int32 {
	if x != nil {
		return x.BmSixHundredNum
	}
	return 0
}

func (x *MyAwListResp_Info) GetBmTwoThousandNum() int32 {
	if x != nil {
		return x.BmTwoThousandNum
	}
	return 0
}

func (x *MyAwListResp_Info) GetSignpic() string {
	if x != nil {
		return x.Signpic
	}
	return ""
}

func (x *MyAwListResp_Info) GetSealpic() string {
	if x != nil {
		return x.Sealpic
	}
	return ""
}

func (x *MyAwListResp_Info) GetScheduleTime() string {
	if x != nil {
		return x.ScheduleTime
	}
	return ""
}

func (x *MyAwListResp_Info) GetState() int32 {
	if x != nil {
		return x.State
	}
	return 0
}

func (x *MyAwListResp_Info) GetArtistUuid() string {
	if x != nil {
		return x.ArtistUuid
	}
	return ""
}

func (x *MyAwListResp_Info) GetArtistShowCount() string {
	if x != nil {
		return x.ArtistShowCount
	}
	return ""
}

func (x *MyAwListResp_Info) GetBlackListStatus() int32 {
	if x != nil {
		return x.BlackListStatus
	}
	return 0
}

type VerifyListResp_RateImg struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Data []*VerifyListResp_RateImg_Info `protobuf:"bytes,1,rep,name=Data,proto3" json:"Data"`
}

func (x *VerifyListResp_RateImg) Reset() {
	*x = VerifyListResp_RateImg{}
	mi := &file_pb_artwork_query_proto_msgTypes[101]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *VerifyListResp_RateImg) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VerifyListResp_RateImg) ProtoMessage() {}

func (x *VerifyListResp_RateImg) ProtoReflect() protoreflect.Message {
	mi := &file_pb_artwork_query_proto_msgTypes[101]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VerifyListResp_RateImg.ProtoReflect.Descriptor instead.
func (*VerifyListResp_RateImg) Descriptor() ([]byte, []int) {
	return file_pb_artwork_query_proto_rawDescGZIP(), []int{52, 0}
}

func (x *VerifyListResp_RateImg) GetData() []*VerifyListResp_RateImg_Info {
	if x != nil {
		return x.Data
	}
	return nil
}

type VerifyListResp_Info struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	BitMapIndex int32                              `protobuf:"varint,1,opt,name=BitMapIndex,proto3" json:"BitMapIndex"`
	BitmapImg   string                             `protobuf:"bytes,2,opt,name=BitmapImg,proto3" json:"BitmapImg"`
	BmImgs      map[string]*VerifyListResp_RateImg `protobuf:"bytes,3,rep,name=BmImgs,proto3" json:"BmImgs" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

func (x *VerifyListResp_Info) Reset() {
	*x = VerifyListResp_Info{}
	mi := &file_pb_artwork_query_proto_msgTypes[102]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *VerifyListResp_Info) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VerifyListResp_Info) ProtoMessage() {}

func (x *VerifyListResp_Info) ProtoReflect() protoreflect.Message {
	mi := &file_pb_artwork_query_proto_msgTypes[102]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VerifyListResp_Info.ProtoReflect.Descriptor instead.
func (*VerifyListResp_Info) Descriptor() ([]byte, []int) {
	return file_pb_artwork_query_proto_rawDescGZIP(), []int{52, 1}
}

func (x *VerifyListResp_Info) GetBitMapIndex() int32 {
	if x != nil {
		return x.BitMapIndex
	}
	return 0
}

func (x *VerifyListResp_Info) GetBitmapImg() string {
	if x != nil {
		return x.BitmapImg
	}
	return ""
}

func (x *VerifyListResp_Info) GetBmImgs() map[string]*VerifyListResp_RateImg {
	if x != nil {
		return x.BmImgs
	}
	return nil
}

type VerifyListResp_RateImg_Info struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RateUrl     string  `protobuf:"bytes,1,opt,name=RateUrl,proto3" json:"RateUrl"`
	RateSimilar float32 `protobuf:"fixed32,2,opt,name=RateSimilar,proto3" json:"RateSimilar"`
	Uuid        string  `protobuf:"bytes,3,opt,name=Uuid,proto3" json:"Uuid"`
	RateImgTime string  `protobuf:"bytes,4,opt,name=RateImgTime,proto3" json:"RateImgTime"`
}

func (x *VerifyListResp_RateImg_Info) Reset() {
	*x = VerifyListResp_RateImg_Info{}
	mi := &file_pb_artwork_query_proto_msgTypes[104]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *VerifyListResp_RateImg_Info) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VerifyListResp_RateImg_Info) ProtoMessage() {}

func (x *VerifyListResp_RateImg_Info) ProtoReflect() protoreflect.Message {
	mi := &file_pb_artwork_query_proto_msgTypes[104]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VerifyListResp_RateImg_Info.ProtoReflect.Descriptor instead.
func (*VerifyListResp_RateImg_Info) Descriptor() ([]byte, []int) {
	return file_pb_artwork_query_proto_rawDescGZIP(), []int{52, 0, 0}
}

func (x *VerifyListResp_RateImg_Info) GetRateUrl() string {
	if x != nil {
		return x.RateUrl
	}
	return ""
}

func (x *VerifyListResp_RateImg_Info) GetRateSimilar() float32 {
	if x != nil {
		return x.RateSimilar
	}
	return 0
}

func (x *VerifyListResp_RateImg_Info) GetUuid() string {
	if x != nil {
		return x.Uuid
	}
	return ""
}

func (x *VerifyListResp_RateImg_Info) GetRateImgTime() string {
	if x != nil {
		return x.RateImgTime
	}
	return ""
}

type ArtshowListResp_Info struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ArtistName     string `protobuf:"bytes,1,opt,name=ArtistName,proto3" json:"ArtistName"`
	Sex            string `protobuf:"bytes,2,opt,name=Sex,proto3" json:"Sex"`
	Age            int32  `protobuf:"varint,3,opt,name=Age,proto3" json:"Age"`
	Phone          string `protobuf:"bytes,4,opt,name=Phone,proto3" json:"Phone"`
	ArtshowExtTime string `protobuf:"bytes,5,opt,name=ArtshowExtTime,proto3" json:"ArtshowExtTime"`
	// string InviteUsers = 6;
	// string InviteUserId = 7;
	RecommendUser int32                              `protobuf:"varint,8,opt,name=RecommendUser,proto3" json:"RecommendUser"`
	ArtshowId     string                             `protobuf:"bytes,9,opt,name=ArtshowId,proto3" json:"ArtshowId"`
	ArtistUuid    string                             `protobuf:"bytes,10,opt,name=ArtistUuid,proto3" json:"ArtistUuid"`
	InviteData    []*ArtshowListResp_Info_InviteInfo `protobuf:"bytes,11,rep,name=InviteData,proto3" json:"InviteData"`
}

func (x *ArtshowListResp_Info) Reset() {
	*x = ArtshowListResp_Info{}
	mi := &file_pb_artwork_query_proto_msgTypes[106]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ArtshowListResp_Info) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ArtshowListResp_Info) ProtoMessage() {}

func (x *ArtshowListResp_Info) ProtoReflect() protoreflect.Message {
	mi := &file_pb_artwork_query_proto_msgTypes[106]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ArtshowListResp_Info.ProtoReflect.Descriptor instead.
func (*ArtshowListResp_Info) Descriptor() ([]byte, []int) {
	return file_pb_artwork_query_proto_rawDescGZIP(), []int{54, 0}
}

func (x *ArtshowListResp_Info) GetArtistName() string {
	if x != nil {
		return x.ArtistName
	}
	return ""
}

func (x *ArtshowListResp_Info) GetSex() string {
	if x != nil {
		return x.Sex
	}
	return ""
}

func (x *ArtshowListResp_Info) GetAge() int32 {
	if x != nil {
		return x.Age
	}
	return 0
}

func (x *ArtshowListResp_Info) GetPhone() string {
	if x != nil {
		return x.Phone
	}
	return ""
}

func (x *ArtshowListResp_Info) GetArtshowExtTime() string {
	if x != nil {
		return x.ArtshowExtTime
	}
	return ""
}

func (x *ArtshowListResp_Info) GetRecommendUser() int32 {
	if x != nil {
		return x.RecommendUser
	}
	return 0
}

func (x *ArtshowListResp_Info) GetArtshowId() string {
	if x != nil {
		return x.ArtshowId
	}
	return ""
}

func (x *ArtshowListResp_Info) GetArtistUuid() string {
	if x != nil {
		return x.ArtistUuid
	}
	return ""
}

func (x *ArtshowListResp_Info) GetInviteData() []*ArtshowListResp_Info_InviteInfo {
	if x != nil {
		return x.InviteData
	}
	return nil
}

type ArtshowListResp_Info_InviteInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UserId   int32  `protobuf:"varint,1,opt,name=UserId,proto3" json:"UserId"`
	Realname string `protobuf:"bytes,2,opt,name=Realname,proto3" json:"Realname"`
}

func (x *ArtshowListResp_Info_InviteInfo) Reset() {
	*x = ArtshowListResp_Info_InviteInfo{}
	mi := &file_pb_artwork_query_proto_msgTypes[107]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ArtshowListResp_Info_InviteInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ArtshowListResp_Info_InviteInfo) ProtoMessage() {}

func (x *ArtshowListResp_Info_InviteInfo) ProtoReflect() protoreflect.Message {
	mi := &file_pb_artwork_query_proto_msgTypes[107]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ArtshowListResp_Info_InviteInfo.ProtoReflect.Descriptor instead.
func (*ArtshowListResp_Info_InviteInfo) Descriptor() ([]byte, []int) {
	return file_pb_artwork_query_proto_rawDescGZIP(), []int{54, 0, 0}
}

func (x *ArtshowListResp_Info_InviteInfo) GetUserId() int32 {
	if x != nil {
		return x.UserId
	}
	return 0
}

func (x *ArtshowListResp_Info_InviteInfo) GetRealname() string {
	if x != nil {
		return x.Realname
	}
	return ""
}

type OneQueryResp_Info struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ArtworkName     string `protobuf:"bytes,1,opt,name=ArtworkName,proto3" json:"ArtworkName"`
	ArtistName      string `protobuf:"bytes,2,opt,name=ArtistName,proto3" json:"ArtistName"`
	Length          int32  `protobuf:"varint,3,opt,name=Length,proto3" json:"Length"`
	Width           int32  `protobuf:"varint,4,opt,name=Width,proto3" json:"Width"`
	Ruler           int32  `protobuf:"varint,5,opt,name=Ruler,proto3" json:"Ruler"`
	Abstract        string `protobuf:"bytes,6,opt,name=Abstract,proto3" json:"Abstract"`
	Comment         string `protobuf:"bytes,7,opt,name=Comment,proto3" json:"Comment"`
	ArtworkUuid     string `protobuf:"bytes,8,opt,name=ArtworkUuid,proto3" json:"ArtworkUuid"`
	DigiArtImg      string `protobuf:"bytes,9,opt,name=DigiArtImg,proto3" json:"DigiArtImg"`
	EduEvaluation   string `protobuf:"bytes,10,opt,name=EduEvaluation,proto3" json:"EduEvaluation"`
	CheckStatus     int32  `protobuf:"varint,11,opt,name=CheckStatus,proto3" json:"CheckStatus"`
	ArtistShowCount string `protobuf:"bytes,12,opt,name=artistShowCount,proto3" json:"artistShowCount"`
	ArtistUuid      string `protobuf:"bytes,13,opt,name=artistUuid,proto3" json:"artistUuid"`
}

func (x *OneQueryResp_Info) Reset() {
	*x = OneQueryResp_Info{}
	mi := &file_pb_artwork_query_proto_msgTypes[108]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *OneQueryResp_Info) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OneQueryResp_Info) ProtoMessage() {}

func (x *OneQueryResp_Info) ProtoReflect() protoreflect.Message {
	mi := &file_pb_artwork_query_proto_msgTypes[108]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OneQueryResp_Info.ProtoReflect.Descriptor instead.
func (*OneQueryResp_Info) Descriptor() ([]byte, []int) {
	return file_pb_artwork_query_proto_rawDescGZIP(), []int{58, 0}
}

func (x *OneQueryResp_Info) GetArtworkName() string {
	if x != nil {
		return x.ArtworkName
	}
	return ""
}

func (x *OneQueryResp_Info) GetArtistName() string {
	if x != nil {
		return x.ArtistName
	}
	return ""
}

func (x *OneQueryResp_Info) GetLength() int32 {
	if x != nil {
		return x.Length
	}
	return 0
}

func (x *OneQueryResp_Info) GetWidth() int32 {
	if x != nil {
		return x.Width
	}
	return 0
}

func (x *OneQueryResp_Info) GetRuler() int32 {
	if x != nil {
		return x.Ruler
	}
	return 0
}

func (x *OneQueryResp_Info) GetAbstract() string {
	if x != nil {
		return x.Abstract
	}
	return ""
}

func (x *OneQueryResp_Info) GetComment() string {
	if x != nil {
		return x.Comment
	}
	return ""
}

func (x *OneQueryResp_Info) GetArtworkUuid() string {
	if x != nil {
		return x.ArtworkUuid
	}
	return ""
}

func (x *OneQueryResp_Info) GetDigiArtImg() string {
	if x != nil {
		return x.DigiArtImg
	}
	return ""
}

func (x *OneQueryResp_Info) GetEduEvaluation() string {
	if x != nil {
		return x.EduEvaluation
	}
	return ""
}

func (x *OneQueryResp_Info) GetCheckStatus() int32 {
	if x != nil {
		return x.CheckStatus
	}
	return 0
}

func (x *OneQueryResp_Info) GetArtistShowCount() string {
	if x != nil {
		return x.ArtistShowCount
	}
	return ""
}

func (x *OneQueryResp_Info) GetArtistUuid() string {
	if x != nil {
		return x.ArtistUuid
	}
	return ""
}

type FilterAwListResp_Info struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ArtworkUuid  string `protobuf:"bytes,1,opt,name=ArtworkUuid,proto3" json:"ArtworkUuid"`
	ArtworkName  string `protobuf:"bytes,2,opt,name=ArtworkName,proto3" json:"ArtworkName"`
	HdPic        string `protobuf:"bytes,3,opt,name=HdPic,proto3" json:"HdPic"`
	ArtistUuid   string `protobuf:"bytes,4,opt,name=ArtistUuid,proto3" json:"ArtistUuid"`
	Length       int32  `protobuf:"varint,5,opt,name=Length,proto3" json:"Length"`
	Width        int32  `protobuf:"varint,6,opt,name=Width,proto3" json:"Width"`
	Ruler        int32  `protobuf:"varint,7,opt,name=Ruler,proto3" json:"Ruler"`
	FilterStatus int32  `protobuf:"varint,8,opt,name=FilterStatus,proto3" json:"FilterStatus"`
	DigiArtImg   string `protobuf:"bytes,9,opt,name=DigiArtImg,proto3" json:"DigiArtImg"`
}

func (x *FilterAwListResp_Info) Reset() {
	*x = FilterAwListResp_Info{}
	mi := &file_pb_artwork_query_proto_msgTypes[109]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *FilterAwListResp_Info) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FilterAwListResp_Info) ProtoMessage() {}

func (x *FilterAwListResp_Info) ProtoReflect() protoreflect.Message {
	mi := &file_pb_artwork_query_proto_msgTypes[109]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FilterAwListResp_Info.ProtoReflect.Descriptor instead.
func (*FilterAwListResp_Info) Descriptor() ([]byte, []int) {
	return file_pb_artwork_query_proto_rawDescGZIP(), []int{62, 0}
}

func (x *FilterAwListResp_Info) GetArtworkUuid() string {
	if x != nil {
		return x.ArtworkUuid
	}
	return ""
}

func (x *FilterAwListResp_Info) GetArtworkName() string {
	if x != nil {
		return x.ArtworkName
	}
	return ""
}

func (x *FilterAwListResp_Info) GetHdPic() string {
	if x != nil {
		return x.HdPic
	}
	return ""
}

func (x *FilterAwListResp_Info) GetArtistUuid() string {
	if x != nil {
		return x.ArtistUuid
	}
	return ""
}

func (x *FilterAwListResp_Info) GetLength() int32 {
	if x != nil {
		return x.Length
	}
	return 0
}

func (x *FilterAwListResp_Info) GetWidth() int32 {
	if x != nil {
		return x.Width
	}
	return 0
}

func (x *FilterAwListResp_Info) GetRuler() int32 {
	if x != nil {
		return x.Ruler
	}
	return 0
}

func (x *FilterAwListResp_Info) GetFilterStatus() int32 {
	if x != nil {
		return x.FilterStatus
	}
	return 0
}

func (x *FilterAwListResp_Info) GetDigiArtImg() string {
	if x != nil {
		return x.DigiArtImg
	}
	return ""
}

type ChainInfoByHashResp_Info struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Basicinfoofartwork   string `protobuf:"bytes,1,opt,name=Basicinfoofartwork,proto3" json:"Basicinfoofartwork"`
	RequestId1           string `protobuf:"bytes,2,opt,name=RequestId1,proto3" json:"RequestId1"`
	ArtworkUuid          string `protobuf:"bytes,3,opt,name=ArtworkUuid,proto3" json:"ArtworkUuid"`
	Tfnum                string `protobuf:"bytes,4,opt,name=Tfnum,proto3" json:"Tfnum"`
	AuthImg              string `protobuf:"bytes,5,opt,name=AuthImg,proto3" json:"AuthImg"`
	Certtmpicossaddr     string `protobuf:"bytes,6,opt,name=Certtmpicossaddr,proto3" json:"Certtmpicossaddr"`
	Winduptime           string `protobuf:"bytes,7,opt,name=Winduptime,proto3" json:"Winduptime"`
	WtTransactionHash    string `protobuf:"bytes,8,opt,name=WtTransactionHash,proto3" json:"WtTransactionHash"`
	Transactionhash      string `protobuf:"bytes,9,opt,name=Transactionhash,proto3" json:"Transactionhash"`
	Changtransactionhash string `protobuf:"bytes,10,opt,name=Changtransactionhash,proto3" json:"Changtransactionhash"`
	BaiduHeight          int64  `protobuf:"varint,11,opt,name=BaiduHeight,proto3" json:"BaiduHeight"`
}

func (x *ChainInfoByHashResp_Info) Reset() {
	*x = ChainInfoByHashResp_Info{}
	mi := &file_pb_artwork_query_proto_msgTypes[110]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ChainInfoByHashResp_Info) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ChainInfoByHashResp_Info) ProtoMessage() {}

func (x *ChainInfoByHashResp_Info) ProtoReflect() protoreflect.Message {
	mi := &file_pb_artwork_query_proto_msgTypes[110]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ChainInfoByHashResp_Info.ProtoReflect.Descriptor instead.
func (*ChainInfoByHashResp_Info) Descriptor() ([]byte, []int) {
	return file_pb_artwork_query_proto_rawDescGZIP(), []int{64, 0}
}

func (x *ChainInfoByHashResp_Info) GetBasicinfoofartwork() string {
	if x != nil {
		return x.Basicinfoofartwork
	}
	return ""
}

func (x *ChainInfoByHashResp_Info) GetRequestId1() string {
	if x != nil {
		return x.RequestId1
	}
	return ""
}

func (x *ChainInfoByHashResp_Info) GetArtworkUuid() string {
	if x != nil {
		return x.ArtworkUuid
	}
	return ""
}

func (x *ChainInfoByHashResp_Info) GetTfnum() string {
	if x != nil {
		return x.Tfnum
	}
	return ""
}

func (x *ChainInfoByHashResp_Info) GetAuthImg() string {
	if x != nil {
		return x.AuthImg
	}
	return ""
}

func (x *ChainInfoByHashResp_Info) GetCerttmpicossaddr() string {
	if x != nil {
		return x.Certtmpicossaddr
	}
	return ""
}

func (x *ChainInfoByHashResp_Info) GetWinduptime() string {
	if x != nil {
		return x.Winduptime
	}
	return ""
}

func (x *ChainInfoByHashResp_Info) GetWtTransactionHash() string {
	if x != nil {
		return x.WtTransactionHash
	}
	return ""
}

func (x *ChainInfoByHashResp_Info) GetTransactionhash() string {
	if x != nil {
		return x.Transactionhash
	}
	return ""
}

func (x *ChainInfoByHashResp_Info) GetChangtransactionhash() string {
	if x != nil {
		return x.Changtransactionhash
	}
	return ""
}

func (x *ChainInfoByHashResp_Info) GetBaiduHeight() int64 {
	if x != nil {
		return x.BaiduHeight
	}
	return 0
}

type ChainListResp_Info struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Hash       string `protobuf:"bytes,1,opt,name=Hash,proto3" json:"Hash"`
	CreateDate string `protobuf:"bytes,2,opt,name=CreateDate,proto3" json:"CreateDate"`
}

func (x *ChainListResp_Info) Reset() {
	*x = ChainListResp_Info{}
	mi := &file_pb_artwork_query_proto_msgTypes[111]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ChainListResp_Info) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ChainListResp_Info) ProtoMessage() {}

func (x *ChainListResp_Info) ProtoReflect() protoreflect.Message {
	mi := &file_pb_artwork_query_proto_msgTypes[111]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ChainListResp_Info.ProtoReflect.Descriptor instead.
func (*ChainListResp_Info) Descriptor() ([]byte, []int) {
	return file_pb_artwork_query_proto_rawDescGZIP(), []int{68, 0}
}

func (x *ChainListResp_Info) GetHash() string {
	if x != nil {
		return x.Hash
	}
	return ""
}

func (x *ChainListResp_Info) GetCreateDate() string {
	if x != nil {
		return x.CreateDate
	}
	return ""
}

type ArtistBindArtworkListResp_Info struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ArtworkUuid   string `protobuf:"bytes,1,opt,name=ArtworkUuid,proto3" json:"ArtworkUuid"`
	Tfnum         string `protobuf:"bytes,2,opt,name=Tfnum,proto3" json:"Tfnum"`
	Length        int32  `protobuf:"varint,3,opt,name=Length,proto3" json:"Length"`
	Width         int32  `protobuf:"varint,4,opt,name=Width,proto3" json:"Width"`
	Ruler         int32  `protobuf:"varint,5,opt,name=Ruler,proto3" json:"Ruler"`
	SaleStatus    int32  `protobuf:"varint,6,opt,name=SaleStatus,proto3" json:"SaleStatus"`
	StorageStatus int32  `protobuf:"varint,7,opt,name=StorageStatus,proto3" json:"StorageStatus"`
	HdPic         string `protobuf:"bytes,8,opt,name=HdPic,proto3" json:"HdPic"`
}

func (x *ArtistBindArtworkListResp_Info) Reset() {
	*x = ArtistBindArtworkListResp_Info{}
	mi := &file_pb_artwork_query_proto_msgTypes[112]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ArtistBindArtworkListResp_Info) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ArtistBindArtworkListResp_Info) ProtoMessage() {}

func (x *ArtistBindArtworkListResp_Info) ProtoReflect() protoreflect.Message {
	mi := &file_pb_artwork_query_proto_msgTypes[112]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ArtistBindArtworkListResp_Info.ProtoReflect.Descriptor instead.
func (*ArtistBindArtworkListResp_Info) Descriptor() ([]byte, []int) {
	return file_pb_artwork_query_proto_rawDescGZIP(), []int{74, 0}
}

func (x *ArtistBindArtworkListResp_Info) GetArtworkUuid() string {
	if x != nil {
		return x.ArtworkUuid
	}
	return ""
}

func (x *ArtistBindArtworkListResp_Info) GetTfnum() string {
	if x != nil {
		return x.Tfnum
	}
	return ""
}

func (x *ArtistBindArtworkListResp_Info) GetLength() int32 {
	if x != nil {
		return x.Length
	}
	return 0
}

func (x *ArtistBindArtworkListResp_Info) GetWidth() int32 {
	if x != nil {
		return x.Width
	}
	return 0
}

func (x *ArtistBindArtworkListResp_Info) GetRuler() int32 {
	if x != nil {
		return x.Ruler
	}
	return 0
}

func (x *ArtistBindArtworkListResp_Info) GetSaleStatus() int32 {
	if x != nil {
		return x.SaleStatus
	}
	return 0
}

func (x *ArtistBindArtworkListResp_Info) GetStorageStatus() int32 {
	if x != nil {
		return x.StorageStatus
	}
	return 0
}

func (x *ArtistBindArtworkListResp_Info) GetHdPic() string {
	if x != nil {
		return x.HdPic
	}
	return ""
}

type GetArtworkDataBatchResp_ArtworkInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ArtworkName    string  `protobuf:"bytes,1,opt,name=ArtworkName,proto3" json:"ArtworkName"`                              //画作名字
	Tfnum          string  `protobuf:"bytes,2,opt,name=Tfnum,proto3" json:"Tfnum"`                                          //画作编号
	Length         int32   `protobuf:"varint,3,opt,name=Length,proto3" json:"Length"`                                       //长度
	Width          int32   `protobuf:"varint,4,opt,name=Width,proto3" json:"Width"`                                         //宽度
	ArtworkType    int32   `protobuf:"varint,5,opt,name=ArtworkType,proto3" json:"ArtworkType"`                             //画作类型 1-优秀画作，2-赠画，3-卷轴，4-普通画作
	SaleStatus     int32   `protobuf:"varint,6,opt,name=SaleStatus,proto3" json:"SaleStatus"`                               //销售状态  0不在仓库 1在仓库 2数字化中 3鉴证中 4数字化完成 5鉴证完成 6待销售 7上架 8已出售 9司机主管确认  司机主管确认在司机扫码之后
	StorageStatus  int32   `protobuf:"varint,7,opt,name=StorageStatus,proto3" json:"StorageStatus"`                         //库存状态  0  未入库  1仓库  2 数字化中 3 鉴证中 4出库
	HdPic          string  `protobuf:"bytes,8,opt,name=HdPic,proto3" json:"HdPic"`                                          //高清图
	ArtworkUuid    string  `protobuf:"bytes,9,opt,name=ArtworkUuid,proto3" json:"ArtworkUuid"`                              // 画作UUid
	ArtistName     string  `protobuf:"bytes,10,opt,name=ArtistName,json=artist_name,proto3" json:"ArtistName"`              //画家名字
	Material       int32   `protobuf:"varint,11,opt,name=Material,json=material,proto3" json:"Material"`                    //画作材质  1 宣纸  2 绢  3 水彩纸
	ArtCondition   int32   `protobuf:"varint,12,opt,name=ArtCondition,json=art_condition,proto3" json:"ArtCondition"`       //画作品相 1 完好 2 破损 3 残缺
	Mountmode      int32   `protobuf:"varint,13,opt,name=Mountmode,json=mountmode,proto3" json:"Mountmode"`                 //装裱方式 1 画心
	ArtHorizontal  int32   `protobuf:"varint,14,opt,name=ArtHorizontal,json=art_horizontal,proto3" json:"ArtHorizontal"`    //幅式 请求 catlist 接口
	Size           int32   `protobuf:"varint,15,opt,name=Size,json=size,proto3" json:"Size"`                                //尺寸 1 大 2中  3小
	Ruler          int32   `protobuf:"varint,16,opt,name=Ruler,json=ruler,proto3" json:"Ruler"`                             //平尺
	InscribeDate   string  `protobuf:"bytes,17,opt,name=InscribeDate,json=inscribe_date,proto3" json:"InscribeDate"`        //落款时间
	CreatedDate    string  `protobuf:"bytes,18,opt,name=CreatedDate,json=created_date,proto3" json:"CreatedDate"`           //创建日期
	CreatedAddress string  `protobuf:"bytes,19,opt,name=CreatedAddress,json=created_address,proto3" json:"CreatedAddress"`  //创建地址
	CreateDoneDate string  `protobuf:"bytes,20,opt,name=CreateDoneDate,json=create_done_date,proto3" json:"CreateDoneDate"` //创建完成时间
	FilterState    int32   `protobuf:"varint,21,opt,name=FilterState,json=filter_state,proto3" json:"FilterState"`          // 筛选状态1 通过 2 不通过
	PriceRun       float32 `protobuf:"fixed32,22,opt,name=PriceRun,json=price_run,proto3" json:"PriceRun"`                  //润格
	Signdate       string  `protobuf:"bytes,23,opt,name=Signdate,json=sign_date,proto3" json:"Signdate"`                    //签约时间
	Comment        string  `protobuf:"bytes,24,opt,name=Comment,json=comment,proto3" json:"Comment"`                        //评价 备注
	Abstract       string  `protobuf:"bytes,25,opt,name=Abstract,json=abstract,proto3" json:"Abstract"`                     //画作简介
	EduEvaluation  string  `protobuf:"bytes,26,opt,name=EduEvaluation,proto3" json:"EduEvaluation"`                         // 评价2
	ArtQuality     int32   `protobuf:"varint,27,opt,name=ArtQuality,json=art_quality,proto3" json:"ArtQuality"`             //画作状态 1 完好   2 破损  3 残缺
	Signpic        string  `protobuf:"bytes,28,opt,name=Signpic,json=signpic,proto3" json:"Signpic"`                        //落款图
	Sealpic        string  `protobuf:"bytes,29,opt,name=Sealpic,json=sealpic,proto3" json:"Sealpic"`                        //人名章图
	ArtistPhoto    string  `protobuf:"bytes,30,opt,name=ArtistPhoto,json=artist_photo,proto3" json:"ArtistPhoto"`           //画家画作合照
	PhotoPic       string  `protobuf:"bytes,31,opt,name=PhotoPic,json=photo_pic,proto3" json:"PhotoPic"`                    //手机拍摄
	SealpicPhoto   string  `protobuf:"bytes,32,opt,name=SealpicPhoto,json=sealpic_photo,proto3" json:"SealpicPhoto"`        //手机拍摄人名章图
}

func (x *GetArtworkDataBatchResp_ArtworkInfo) Reset() {
	*x = GetArtworkDataBatchResp_ArtworkInfo{}
	mi := &file_pb_artwork_query_proto_msgTypes[113]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetArtworkDataBatchResp_ArtworkInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetArtworkDataBatchResp_ArtworkInfo) ProtoMessage() {}

func (x *GetArtworkDataBatchResp_ArtworkInfo) ProtoReflect() protoreflect.Message {
	mi := &file_pb_artwork_query_proto_msgTypes[113]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetArtworkDataBatchResp_ArtworkInfo.ProtoReflect.Descriptor instead.
func (*GetArtworkDataBatchResp_ArtworkInfo) Descriptor() ([]byte, []int) {
	return file_pb_artwork_query_proto_rawDescGZIP(), []int{79, 0}
}

func (x *GetArtworkDataBatchResp_ArtworkInfo) GetArtworkName() string {
	if x != nil {
		return x.ArtworkName
	}
	return ""
}

func (x *GetArtworkDataBatchResp_ArtworkInfo) GetTfnum() string {
	if x != nil {
		return x.Tfnum
	}
	return ""
}

func (x *GetArtworkDataBatchResp_ArtworkInfo) GetLength() int32 {
	if x != nil {
		return x.Length
	}
	return 0
}

func (x *GetArtworkDataBatchResp_ArtworkInfo) GetWidth() int32 {
	if x != nil {
		return x.Width
	}
	return 0
}

func (x *GetArtworkDataBatchResp_ArtworkInfo) GetArtworkType() int32 {
	if x != nil {
		return x.ArtworkType
	}
	return 0
}

func (x *GetArtworkDataBatchResp_ArtworkInfo) GetSaleStatus() int32 {
	if x != nil {
		return x.SaleStatus
	}
	return 0
}

func (x *GetArtworkDataBatchResp_ArtworkInfo) GetStorageStatus() int32 {
	if x != nil {
		return x.StorageStatus
	}
	return 0
}

func (x *GetArtworkDataBatchResp_ArtworkInfo) GetHdPic() string {
	if x != nil {
		return x.HdPic
	}
	return ""
}

func (x *GetArtworkDataBatchResp_ArtworkInfo) GetArtworkUuid() string {
	if x != nil {
		return x.ArtworkUuid
	}
	return ""
}

func (x *GetArtworkDataBatchResp_ArtworkInfo) GetArtistName() string {
	if x != nil {
		return x.ArtistName
	}
	return ""
}

func (x *GetArtworkDataBatchResp_ArtworkInfo) GetMaterial() int32 {
	if x != nil {
		return x.Material
	}
	return 0
}

func (x *GetArtworkDataBatchResp_ArtworkInfo) GetArtCondition() int32 {
	if x != nil {
		return x.ArtCondition
	}
	return 0
}

func (x *GetArtworkDataBatchResp_ArtworkInfo) GetMountmode() int32 {
	if x != nil {
		return x.Mountmode
	}
	return 0
}

func (x *GetArtworkDataBatchResp_ArtworkInfo) GetArtHorizontal() int32 {
	if x != nil {
		return x.ArtHorizontal
	}
	return 0
}

func (x *GetArtworkDataBatchResp_ArtworkInfo) GetSize() int32 {
	if x != nil {
		return x.Size
	}
	return 0
}

func (x *GetArtworkDataBatchResp_ArtworkInfo) GetRuler() int32 {
	if x != nil {
		return x.Ruler
	}
	return 0
}

func (x *GetArtworkDataBatchResp_ArtworkInfo) GetInscribeDate() string {
	if x != nil {
		return x.InscribeDate
	}
	return ""
}

func (x *GetArtworkDataBatchResp_ArtworkInfo) GetCreatedDate() string {
	if x != nil {
		return x.CreatedDate
	}
	return ""
}

func (x *GetArtworkDataBatchResp_ArtworkInfo) GetCreatedAddress() string {
	if x != nil {
		return x.CreatedAddress
	}
	return ""
}

func (x *GetArtworkDataBatchResp_ArtworkInfo) GetCreateDoneDate() string {
	if x != nil {
		return x.CreateDoneDate
	}
	return ""
}

func (x *GetArtworkDataBatchResp_ArtworkInfo) GetFilterState() int32 {
	if x != nil {
		return x.FilterState
	}
	return 0
}

func (x *GetArtworkDataBatchResp_ArtworkInfo) GetPriceRun() float32 {
	if x != nil {
		return x.PriceRun
	}
	return 0
}

func (x *GetArtworkDataBatchResp_ArtworkInfo) GetSigndate() string {
	if x != nil {
		return x.Signdate
	}
	return ""
}

func (x *GetArtworkDataBatchResp_ArtworkInfo) GetComment() string {
	if x != nil {
		return x.Comment
	}
	return ""
}

func (x *GetArtworkDataBatchResp_ArtworkInfo) GetAbstract() string {
	if x != nil {
		return x.Abstract
	}
	return ""
}

func (x *GetArtworkDataBatchResp_ArtworkInfo) GetEduEvaluation() string {
	if x != nil {
		return x.EduEvaluation
	}
	return ""
}

func (x *GetArtworkDataBatchResp_ArtworkInfo) GetArtQuality() int32 {
	if x != nil {
		return x.ArtQuality
	}
	return 0
}

func (x *GetArtworkDataBatchResp_ArtworkInfo) GetSignpic() string {
	if x != nil {
		return x.Signpic
	}
	return ""
}

func (x *GetArtworkDataBatchResp_ArtworkInfo) GetSealpic() string {
	if x != nil {
		return x.Sealpic
	}
	return ""
}

func (x *GetArtworkDataBatchResp_ArtworkInfo) GetArtistPhoto() string {
	if x != nil {
		return x.ArtistPhoto
	}
	return ""
}

func (x *GetArtworkDataBatchResp_ArtworkInfo) GetPhotoPic() string {
	if x != nil {
		return x.PhotoPic
	}
	return ""
}

func (x *GetArtworkDataBatchResp_ArtworkInfo) GetSealpicPhoto() string {
	if x != nil {
		return x.SealpicPhoto
	}
	return ""
}

type BatchDciListResp_Info struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ArtworkUuid string `protobuf:"bytes,1,opt,name=ArtworkUuid,proto3" json:"ArtworkUuid"`
	ArtistUuid  string `protobuf:"bytes,2,opt,name=ArtistUuid,proto3" json:"ArtistUuid"`
	Tfnum       string `protobuf:"bytes,3,opt,name=Tfnum,proto3" json:"Tfnum"`
	ArtistName  string `protobuf:"bytes,4,opt,name=ArtistName,proto3" json:"ArtistName"`
	ArtworkName string `protobuf:"bytes,5,opt,name=ArtworkName,proto3" json:"ArtworkName"`
	WorkFileUrl string `protobuf:"bytes,6,opt,name=WorkFileUrl,proto3" json:"WorkFileUrl"`
	ArtworkType int32  `protobuf:"varint,7,opt,name=ArtworkType,proto3" json:"ArtworkType"`
}

func (x *BatchDciListResp_Info) Reset() {
	*x = BatchDciListResp_Info{}
	mi := &file_pb_artwork_query_proto_msgTypes[114]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BatchDciListResp_Info) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BatchDciListResp_Info) ProtoMessage() {}

func (x *BatchDciListResp_Info) ProtoReflect() protoreflect.Message {
	mi := &file_pb_artwork_query_proto_msgTypes[114]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BatchDciListResp_Info.ProtoReflect.Descriptor instead.
func (*BatchDciListResp_Info) Descriptor() ([]byte, []int) {
	return file_pb_artwork_query_proto_rawDescGZIP(), []int{81, 0}
}

func (x *BatchDciListResp_Info) GetArtworkUuid() string {
	if x != nil {
		return x.ArtworkUuid
	}
	return ""
}

func (x *BatchDciListResp_Info) GetArtistUuid() string {
	if x != nil {
		return x.ArtistUuid
	}
	return ""
}

func (x *BatchDciListResp_Info) GetTfnum() string {
	if x != nil {
		return x.Tfnum
	}
	return ""
}

func (x *BatchDciListResp_Info) GetArtistName() string {
	if x != nil {
		return x.ArtistName
	}
	return ""
}

func (x *BatchDciListResp_Info) GetArtworkName() string {
	if x != nil {
		return x.ArtworkName
	}
	return ""
}

func (x *BatchDciListResp_Info) GetWorkFileUrl() string {
	if x != nil {
		return x.WorkFileUrl
	}
	return ""
}

func (x *BatchDciListResp_Info) GetArtworkType() int32 {
	if x != nil {
		return x.ArtworkType
	}
	return 0
}

type ArtworkArtistListResp_Info struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ArtworkNum int32  `protobuf:"varint,1,opt,name=ArtworkNum,proto3" json:"ArtworkNum"`
	ArtistUuid string `protobuf:"bytes,2,opt,name=ArtistUuid,proto3" json:"ArtistUuid"`
	ArtistName string `protobuf:"bytes,3,opt,name=ArtistName,proto3" json:"ArtistName"`
}

func (x *ArtworkArtistListResp_Info) Reset() {
	*x = ArtworkArtistListResp_Info{}
	mi := &file_pb_artwork_query_proto_msgTypes[115]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ArtworkArtistListResp_Info) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ArtworkArtistListResp_Info) ProtoMessage() {}

func (x *ArtworkArtistListResp_Info) ProtoReflect() protoreflect.Message {
	mi := &file_pb_artwork_query_proto_msgTypes[115]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ArtworkArtistListResp_Info.ProtoReflect.Descriptor instead.
func (*ArtworkArtistListResp_Info) Descriptor() ([]byte, []int) {
	return file_pb_artwork_query_proto_rawDescGZIP(), []int{83, 0}
}

func (x *ArtworkArtistListResp_Info) GetArtworkNum() int32 {
	if x != nil {
		return x.ArtworkNum
	}
	return 0
}

func (x *ArtworkArtistListResp_Info) GetArtistUuid() string {
	if x != nil {
		return x.ArtistUuid
	}
	return ""
}

func (x *ArtworkArtistListResp_Info) GetArtistName() string {
	if x != nil {
		return x.ArtistName
	}
	return ""
}

type StorageListResp_Info struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ArtworkUuid string `protobuf:"bytes,1,opt,name=ArtworkUuid,proto3" json:"ArtworkUuid"`
	ArtistUuid  string `protobuf:"bytes,2,opt,name=ArtistUuid,proto3" json:"ArtistUuid"`
	Tfnum       string `protobuf:"bytes,3,opt,name=Tfnum,proto3" json:"Tfnum"`
	Tnum        string `protobuf:"bytes,4,opt,name=Tnum,proto3" json:"Tnum"`
	ArtworkName string `protobuf:"bytes,5,opt,name=ArtworkName,proto3" json:"ArtworkName"`
	ArtistName  string `protobuf:"bytes,6,opt,name=ArtistName,proto3" json:"ArtistName"`
	Ruler       int32  `protobuf:"varint,7,opt,name=Ruler,proto3" json:"Ruler"`
	HdPic       string `protobuf:"bytes,8,opt,name=HdPic,proto3" json:"HdPic"`
}

func (x *StorageListResp_Info) Reset() {
	*x = StorageListResp_Info{}
	mi := &file_pb_artwork_query_proto_msgTypes[116]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *StorageListResp_Info) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StorageListResp_Info) ProtoMessage() {}

func (x *StorageListResp_Info) ProtoReflect() protoreflect.Message {
	mi := &file_pb_artwork_query_proto_msgTypes[116]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StorageListResp_Info.ProtoReflect.Descriptor instead.
func (*StorageListResp_Info) Descriptor() ([]byte, []int) {
	return file_pb_artwork_query_proto_rawDescGZIP(), []int{85, 0}
}

func (x *StorageListResp_Info) GetArtworkUuid() string {
	if x != nil {
		return x.ArtworkUuid
	}
	return ""
}

func (x *StorageListResp_Info) GetArtistUuid() string {
	if x != nil {
		return x.ArtistUuid
	}
	return ""
}

func (x *StorageListResp_Info) GetTfnum() string {
	if x != nil {
		return x.Tfnum
	}
	return ""
}

func (x *StorageListResp_Info) GetTnum() string {
	if x != nil {
		return x.Tnum
	}
	return ""
}

func (x *StorageListResp_Info) GetArtworkName() string {
	if x != nil {
		return x.ArtworkName
	}
	return ""
}

func (x *StorageListResp_Info) GetArtistName() string {
	if x != nil {
		return x.ArtistName
	}
	return ""
}

func (x *StorageListResp_Info) GetRuler() int32 {
	if x != nil {
		return x.Ruler
	}
	return 0
}

func (x *StorageListResp_Info) GetHdPic() string {
	if x != nil {
		return x.HdPic
	}
	return ""
}

type SecondArtworkListResp_Info struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ArtworkUuid     string `protobuf:"bytes,1,opt,name=ArtworkUuid,proto3" json:"ArtworkUuid"`
	ArtistName      string `protobuf:"bytes,2,opt,name=ArtistName,proto3" json:"ArtistName"`
	Length          int32  `protobuf:"varint,3,opt,name=Length,proto3" json:"Length"`
	Width           int32  `protobuf:"varint,4,opt,name=Width,proto3" json:"Width"`
	Ruler           int32  `protobuf:"varint,5,opt,name=Ruler,proto3" json:"Ruler"`
	Tfnum           string `protobuf:"bytes,6,opt,name=Tfnum,proto3" json:"Tfnum"`
	ArtistShowCount string `protobuf:"bytes,7,opt,name=artistShowCount,proto3" json:"artistShowCount"`
	ArtworkType     int32  `protobuf:"varint,8,opt,name=ArtworkType,proto3" json:"ArtworkType"`
	AuthImg         string `protobuf:"bytes,9,opt,name=AuthImg,proto3" json:"AuthImg"`
	DigiArtImg      string `protobuf:"bytes,10,opt,name=DigiArtImg,proto3" json:"DigiArtImg"`
	HdPic           string `protobuf:"bytes,11,opt,name=HdPic,proto3" json:"HdPic"`
	WtState         int32  `protobuf:"varint,12,opt,name=WtState,proto3" json:"WtState"`
	Changchainstate int32  `protobuf:"varint,13,opt,name=Changchainstate,proto3" json:"Changchainstate"`
	BaiduState      int32  `protobuf:"varint,14,opt,name=BaiduState,proto3" json:"BaiduState"`
	ChainState      int32  `protobuf:"varint,15,opt,name=ChainState,proto3" json:"ChainState"`
	ArtistUuid      string `protobuf:"bytes,16,opt,name=ArtistUuid,proto3" json:"ArtistUuid"`
	BlackListStatus int32  `protobuf:"varint,17,opt,name=BlackListStatus,proto3" json:"BlackListStatus"`
	ArtworkName     string `protobuf:"bytes,18,opt,name=ArtworkName,proto3" json:"ArtworkName"`
}

func (x *SecondArtworkListResp_Info) Reset() {
	*x = SecondArtworkListResp_Info{}
	mi := &file_pb_artwork_query_proto_msgTypes[117]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SecondArtworkListResp_Info) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SecondArtworkListResp_Info) ProtoMessage() {}

func (x *SecondArtworkListResp_Info) ProtoReflect() protoreflect.Message {
	mi := &file_pb_artwork_query_proto_msgTypes[117]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SecondArtworkListResp_Info.ProtoReflect.Descriptor instead.
func (*SecondArtworkListResp_Info) Descriptor() ([]byte, []int) {
	return file_pb_artwork_query_proto_rawDescGZIP(), []int{87, 0}
}

func (x *SecondArtworkListResp_Info) GetArtworkUuid() string {
	if x != nil {
		return x.ArtworkUuid
	}
	return ""
}

func (x *SecondArtworkListResp_Info) GetArtistName() string {
	if x != nil {
		return x.ArtistName
	}
	return ""
}

func (x *SecondArtworkListResp_Info) GetLength() int32 {
	if x != nil {
		return x.Length
	}
	return 0
}

func (x *SecondArtworkListResp_Info) GetWidth() int32 {
	if x != nil {
		return x.Width
	}
	return 0
}

func (x *SecondArtworkListResp_Info) GetRuler() int32 {
	if x != nil {
		return x.Ruler
	}
	return 0
}

func (x *SecondArtworkListResp_Info) GetTfnum() string {
	if x != nil {
		return x.Tfnum
	}
	return ""
}

func (x *SecondArtworkListResp_Info) GetArtistShowCount() string {
	if x != nil {
		return x.ArtistShowCount
	}
	return ""
}

func (x *SecondArtworkListResp_Info) GetArtworkType() int32 {
	if x != nil {
		return x.ArtworkType
	}
	return 0
}

func (x *SecondArtworkListResp_Info) GetAuthImg() string {
	if x != nil {
		return x.AuthImg
	}
	return ""
}

func (x *SecondArtworkListResp_Info) GetDigiArtImg() string {
	if x != nil {
		return x.DigiArtImg
	}
	return ""
}

func (x *SecondArtworkListResp_Info) GetHdPic() string {
	if x != nil {
		return x.HdPic
	}
	return ""
}

func (x *SecondArtworkListResp_Info) GetWtState() int32 {
	if x != nil {
		return x.WtState
	}
	return 0
}

func (x *SecondArtworkListResp_Info) GetChangchainstate() int32 {
	if x != nil {
		return x.Changchainstate
	}
	return 0
}

func (x *SecondArtworkListResp_Info) GetBaiduState() int32 {
	if x != nil {
		return x.BaiduState
	}
	return 0
}

func (x *SecondArtworkListResp_Info) GetChainState() int32 {
	if x != nil {
		return x.ChainState
	}
	return 0
}

func (x *SecondArtworkListResp_Info) GetArtistUuid() string {
	if x != nil {
		return x.ArtistUuid
	}
	return ""
}

func (x *SecondArtworkListResp_Info) GetBlackListStatus() int32 {
	if x != nil {
		return x.BlackListStatus
	}
	return 0
}

func (x *SecondArtworkListResp_Info) GetArtworkName() string {
	if x != nil {
		return x.ArtworkName
	}
	return ""
}

type ArtistsDataByUuidsResp_Info struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ArtistUuid  string `protobuf:"bytes,1,opt,name=ArtistUuid,proto3" json:"ArtistUuid"`
	ArtistName  string `protobuf:"bytes,2,opt,name=ArtistName,proto3" json:"ArtistName"`
	ArtworkUuid string `protobuf:"bytes,3,opt,name=ArtworkUuid,proto3" json:"ArtworkUuid"`
}

func (x *ArtistsDataByUuidsResp_Info) Reset() {
	*x = ArtistsDataByUuidsResp_Info{}
	mi := &file_pb_artwork_query_proto_msgTypes[118]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ArtistsDataByUuidsResp_Info) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ArtistsDataByUuidsResp_Info) ProtoMessage() {}

func (x *ArtistsDataByUuidsResp_Info) ProtoReflect() protoreflect.Message {
	mi := &file_pb_artwork_query_proto_msgTypes[118]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ArtistsDataByUuidsResp_Info.ProtoReflect.Descriptor instead.
func (*ArtistsDataByUuidsResp_Info) Descriptor() ([]byte, []int) {
	return file_pb_artwork_query_proto_rawDescGZIP(), []int{89, 0}
}

func (x *ArtistsDataByUuidsResp_Info) GetArtistUuid() string {
	if x != nil {
		return x.ArtistUuid
	}
	return ""
}

func (x *ArtistsDataByUuidsResp_Info) GetArtistName() string {
	if x != nil {
		return x.ArtistName
	}
	return ""
}

func (x *ArtistsDataByUuidsResp_Info) GetArtworkUuid() string {
	if x != nil {
		return x.ArtworkUuid
	}
	return ""
}

var File_pb_artwork_query_proto protoreflect.FileDescriptor

var file_pb_artwork_query_proto_rawDesc = []byte{
	0x0a, 0x16, 0x70, 0x62, 0x2f, 0x61, 0x72, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x5f, 0x71, 0x75, 0x65,
	0x72, 0x79, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x07, 0x41, 0x72, 0x74, 0x77, 0x6f, 0x72,
	0x6b, 0x1a, 0x16, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61,
	0x74, 0x6f, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1b, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x65, 0x6d, 0x70, 0x74, 0x79,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x20, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74,
	0x6f, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x77, 0x72, 0x61, 0x70, 0x70, 0x65,
	0x72, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xd4, 0x07, 0x0a, 0x12, 0x41, 0x72, 0x74,
	0x77, 0x6f, 0x72, 0x6b, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12,
	0x18, 0x0a, 0x07, 0x4b, 0x65, 0x79, 0x77, 0x6f, 0x72, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x07, 0x6b, 0x65, 0x79, 0x77, 0x6f, 0x72, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x50, 0x61, 0x67,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x70, 0x61, 0x67, 0x65, 0x12, 0x1b, 0x0a,
	0x08, 0x50, 0x61, 0x67, 0x65, 0x53, 0x69, 0x7a, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x09, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x73, 0x69, 0x7a, 0x65, 0x12, 0x42, 0x0a, 0x0d, 0x53, 0x74,
	0x6f, 0x72, 0x61, 0x67, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x1b, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x62, 0x75, 0x66, 0x2e, 0x49, 0x6e, 0x74, 0x33, 0x32, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x0e,
	0x73, 0x74, 0x6f, 0x72, 0x61, 0x67, 0x65, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x17,
	0x0a, 0x06, 0x49, 0x73, 0x4f, 0x76, 0x65, 0x72, 0x18, 0x05, 0x20, 0x01, 0x28, 0x05, 0x52, 0x07,
	0x69, 0x73, 0x5f, 0x6f, 0x76, 0x65, 0x72, 0x12, 0x19, 0x0a, 0x07, 0x41, 0x64, 0x6d, 0x69, 0x6e,
	0x49, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x5f,
	0x69, 0x64, 0x12, 0x1d, 0x0a, 0x09, 0x41, 0x72, 0x74, 0x69, 0x73, 0x74, 0x55, 0x69, 0x64, 0x18,
	0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x61, 0x72, 0x74, 0x69, 0x73, 0x74, 0x5f, 0x75, 0x69,
	0x64, 0x12, 0x1d, 0x0a, 0x09, 0x49, 0x6e, 0x41, 0x72, 0x74, 0x53, 0x68, 0x6f, 0x77, 0x18, 0x08,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x0a, 0x69, 0x6e, 0x5f, 0x61, 0x72, 0x74, 0x73, 0x68, 0x6f, 0x77,
	0x12, 0x3c, 0x0a, 0x0a, 0x53, 0x61, 0x6c, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x09,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x49, 0x6e, 0x74, 0x33, 0x32, 0x56, 0x61, 0x6c, 0x75,
	0x65, 0x52, 0x0b, 0x73, 0x61, 0x6c, 0x65, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x12,
	0x0a, 0x04, 0x4d, 0x61, 0x73, 0x6b, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x6d, 0x61,
	0x73, 0x6b, 0x12, 0x26, 0x0a, 0x0d, 0x53, 0x61, 0x6c, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x4d, 0x75, 0x6c, 0x18, 0x0b, 0x20, 0x03, 0x28, 0x05, 0x52, 0x0f, 0x73, 0x61, 0x6c, 0x65, 0x5f,
	0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x5f, 0x6d, 0x75, 0x6c, 0x12, 0x21, 0x0a, 0x0b, 0x41, 0x72,
	0x74, 0x77, 0x6f, 0x72, 0x6b, 0x54, 0x79, 0x70, 0x65, 0x18, 0x0c, 0x20, 0x03, 0x28, 0x05, 0x52,
	0x0c, 0x61, 0x72, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x12, 0x23, 0x0a,
	0x0c, 0x53, 0x69, 0x67, 0x6e, 0x64, 0x61, 0x74, 0x65, 0x4f, 0x76, 0x65, 0x72, 0x18, 0x0d, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x0d, 0x73, 0x69, 0x67, 0x6e, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x6f, 0x76,
	0x65, 0x72, 0x12, 0x12, 0x0a, 0x04, 0x46, 0x6c, 0x61, 0x67, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x04, 0x66, 0x6c, 0x61, 0x67, 0x12, 0x22, 0x0a, 0x0c, 0x41, 0x72, 0x74, 0x77, 0x6f, 0x72,
	0x6b, 0x55, 0x75, 0x69, 0x64, 0x73, 0x18, 0x0f, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0c, 0x41, 0x72,
	0x74, 0x77, 0x6f, 0x72, 0x6b, 0x55, 0x75, 0x69, 0x64, 0x73, 0x12, 0x26, 0x0a, 0x0e, 0x49, 0x6e,
	0x53, 0x74, 0x6f, 0x72, 0x61, 0x67, 0x65, 0x53, 0x74, 0x61, 0x72, 0x74, 0x18, 0x10, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0e, 0x49, 0x6e, 0x53, 0x74, 0x6f, 0x72, 0x61, 0x67, 0x65, 0x53, 0x74, 0x61,
	0x72, 0x74, 0x12, 0x22, 0x0a, 0x0c, 0x49, 0x6e, 0x53, 0x74, 0x6f, 0x72, 0x61, 0x67, 0x65, 0x45,
	0x6e, 0x64, 0x18, 0x11, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x49, 0x6e, 0x53, 0x74, 0x6f, 0x72,
	0x61, 0x67, 0x65, 0x45, 0x6e, 0x64, 0x12, 0x3f, 0x0a, 0x0c, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72,
	0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x12, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x49,
	0x6e, 0x74, 0x33, 0x32, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x0c, 0x46, 0x69, 0x6c, 0x74, 0x65,
	0x72, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x22, 0x0a, 0x0c, 0x52, 0x65, 0x63, 0x68, 0x65,
	0x63, 0x6b, 0x53, 0x74, 0x61, 0x74, 0x65, 0x18, 0x13, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0c, 0x52,
	0x65, 0x63, 0x68, 0x65, 0x63, 0x6b, 0x53, 0x74, 0x61, 0x74, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x4f,
	0x72, 0x64, 0x65, 0x72, 0x42, 0x79, 0x18, 0x14, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x4f, 0x72,
	0x64, 0x65, 0x72, 0x42, 0x79, 0x12, 0x12, 0x0a, 0x04, 0x53, 0x6f, 0x72, 0x74, 0x18, 0x15, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x04, 0x53, 0x6f, 0x72, 0x74, 0x12, 0x1e, 0x0a, 0x0a, 0x53, 0x74, 0x61,
	0x72, 0x74, 0x52, 0x75, 0x6c, 0x65, 0x72, 0x18, 0x16, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0a, 0x53,
	0x74, 0x61, 0x72, 0x74, 0x52, 0x75, 0x6c, 0x65, 0x72, 0x12, 0x1a, 0x0a, 0x08, 0x45, 0x6e, 0x64,
	0x52, 0x75, 0x6c, 0x65, 0x72, 0x18, 0x17, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x45, 0x6e, 0x64,
	0x52, 0x75, 0x6c, 0x65, 0x72, 0x12, 0x28, 0x0a, 0x0f, 0x43, 0x6f, 0x70, 0x79, 0x72, 0x69, 0x67,
	0x68, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x18, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f,
	0x43, 0x6f, 0x70, 0x79, 0x72, 0x69, 0x67, 0x68, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12,
	0x24, 0x0a, 0x0d, 0x53, 0x61, 0x6c, 0x65, 0x44, 0x61, 0x74, 0x65, 0x53, 0x74, 0x61, 0x72, 0x74,
	0x18, 0x19, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x53, 0x61, 0x6c, 0x65, 0x44, 0x61, 0x74, 0x65,
	0x53, 0x74, 0x61, 0x72, 0x74, 0x12, 0x20, 0x0a, 0x0b, 0x53, 0x61, 0x6c, 0x65, 0x44, 0x61, 0x74,
	0x65, 0x45, 0x6e, 0x64, 0x18, 0x1a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x53, 0x61, 0x6c, 0x65,
	0x44, 0x61, 0x74, 0x65, 0x45, 0x6e, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x54, 0x66, 0x6e, 0x75, 0x6d,
	0x18, 0x1b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x54, 0x66, 0x6e, 0x75, 0x6d, 0x12, 0x20, 0x0a,
	0x0b, 0x41, 0x72, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x1c, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0b, 0x41, 0x72, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x4e, 0x61, 0x6d, 0x65, 0x22,
	0xa6, 0x09, 0x0a, 0x13, 0x41, 0x72, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x4c, 0x69, 0x73, 0x74, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x35, 0x0a, 0x04, 0x44, 0x61, 0x74, 0x61, 0x18,
	0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x41, 0x72, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2e,
	0x41, 0x72, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x2e, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x12, 0x14,
	0x0a, 0x05, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x12, 0x12, 0x0a, 0x04, 0x50, 0x61, 0x67, 0x65, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x04, 0x70, 0x61, 0x67, 0x65, 0x12, 0x1b, 0x0a, 0x08, 0x50, 0x61, 0x67, 0x65,
	0x53, 0x69, 0x7a, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x09, 0x70, 0x61, 0x67, 0x65,
	0x5f, 0x73, 0x69, 0x7a, 0x65, 0x12, 0x14, 0x0a, 0x03, 0x4d, 0x73, 0x67, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x1a, 0xfa, 0x07, 0x0a, 0x04,
	0x49, 0x6e, 0x66, 0x6f, 0x12, 0x0e, 0x0a, 0x02, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x02, 0x69, 0x64, 0x12, 0x21, 0x0a, 0x0b, 0x41, 0x72, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x55,
	0x75, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x61, 0x72, 0x74, 0x77, 0x6f,
	0x72, 0x6b, 0x5f, 0x75, 0x75, 0x69, 0x64, 0x12, 0x1f, 0x0a, 0x0a, 0x41, 0x72, 0x74, 0x69, 0x73,
	0x74, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x61, 0x72, 0x74,
	0x69, 0x73, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x21, 0x0a, 0x0b, 0x41, 0x72, 0x74, 0x77,
	0x6f, 0x72, 0x6b, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x61,
	0x72, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x4c,
	0x65, 0x6e, 0x67, 0x74, 0x68, 0x18, 0x05, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x6c, 0x65, 0x6e,
	0x67, 0x74, 0x68, 0x12, 0x14, 0x0a, 0x05, 0x57, 0x69, 0x64, 0x74, 0x68, 0x18, 0x06, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x05, 0x77, 0x69, 0x64, 0x74, 0x68, 0x12, 0x14, 0x0a, 0x05, 0x52, 0x75, 0x6c,
	0x65, 0x72, 0x18, 0x07, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x72, 0x75, 0x6c, 0x65, 0x72, 0x12,
	0x10, 0x0a, 0x03, 0x4e, 0x75, 0x6d, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6e, 0x75,
	0x6d, 0x12, 0x15, 0x0a, 0x05, 0x48, 0x64, 0x50, 0x69, 0x63, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x06, 0x68, 0x64, 0x5f, 0x70, 0x69, 0x63, 0x12, 0x25, 0x0a, 0x0d, 0x53, 0x74, 0x6f, 0x72,
	0x61, 0x67, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x0e, 0x73, 0x74, 0x6f, 0x72, 0x61, 0x67, 0x65, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12,
	0x1f, 0x0a, 0x0a, 0x53, 0x61, 0x6c, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x0b, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x0b, 0x73, 0x61, 0x6c, 0x65, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x12, 0x26, 0x0a, 0x0d, 0x49, 0x6e, 0x53, 0x74, 0x6f, 0x72, 0x61, 0x67, 0x65, 0x54, 0x69, 0x6d,
	0x65, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x69, 0x6e, 0x5f, 0x73, 0x74, 0x6f, 0x72,
	0x61, 0x67, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x54, 0x66, 0x6e, 0x75,
	0x6d, 0x18, 0x10, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x74, 0x66, 0x6e, 0x75, 0x6d, 0x12, 0x20,
	0x0a, 0x0a, 0x44, 0x69, 0x67, 0x69, 0x41, 0x72, 0x74, 0x49, 0x6d, 0x67, 0x18, 0x11, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0c, 0x64, 0x69, 0x67, 0x69, 0x5f, 0x61, 0x72, 0x74, 0x5f, 0x69, 0x6d, 0x67,
	0x12, 0x1b, 0x0a, 0x08, 0x50, 0x68, 0x6f, 0x74, 0x6f, 0x50, 0x69, 0x63, 0x18, 0x12, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x09, 0x70, 0x68, 0x6f, 0x74, 0x6f, 0x5f, 0x70, 0x69, 0x63, 0x12, 0x1b, 0x0a,
	0x08, 0x50, 0x72, 0x69, 0x63, 0x65, 0x52, 0x75, 0x6e, 0x18, 0x13, 0x20, 0x01, 0x28, 0x02, 0x52,
	0x09, 0x70, 0x72, 0x69, 0x63, 0x65, 0x5f, 0x72, 0x75, 0x6e, 0x12, 0x21, 0x0a, 0x0b, 0x41, 0x72,
	0x74, 0x77, 0x6f, 0x72, 0x6b, 0x54, 0x79, 0x70, 0x65, 0x18, 0x14, 0x20, 0x01, 0x28, 0x02, 0x52,
	0x0c, 0x61, 0x72, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x12, 0x1a, 0x0a,
	0x08, 0x53, 0x69, 0x67, 0x6e, 0x64, 0x61, 0x74, 0x65, 0x18, 0x15, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x08, 0x73, 0x69, 0x67, 0x6e, 0x64, 0x61, 0x74, 0x65, 0x12, 0x24, 0x0a, 0x0c, 0x53, 0x69, 0x67,
	0x6e, 0x64, 0x61, 0x74, 0x65, 0x4f, 0x76, 0x65, 0x72, 0x18, 0x16, 0x20, 0x01, 0x28, 0x08, 0x52,
	0x0e, 0x73, 0x69, 0x67, 0x6e, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x6f, 0x76, 0x65, 0x72, 0x12,
	0x19, 0x0a, 0x07, 0x41, 0x75, 0x74, 0x68, 0x49, 0x6d, 0x67, 0x18, 0x17, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x08, 0x61, 0x75, 0x74, 0x68, 0x5f, 0x69, 0x6d, 0x67, 0x12, 0x17, 0x0a, 0x06, 0x53, 0x61,
	0x6c, 0x65, 0x49, 0x64, 0x18, 0x18, 0x20, 0x01, 0x28, 0x05, 0x52, 0x07, 0x73, 0x61, 0x6c, 0x65,
	0x5f, 0x69, 0x64, 0x12, 0x21, 0x0a, 0x0b, 0x53, 0x61, 0x6c, 0x65, 0x41, 0x64, 0x64, 0x72, 0x65,
	0x73, 0x73, 0x18, 0x19, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x73, 0x61, 0x6c, 0x65, 0x5f, 0x61,
	0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x12, 0x25, 0x0a, 0x0d, 0x41, 0x72, 0x74, 0x53, 0x68, 0x6f,
	0x77, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x1a, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0e, 0x61,
	0x72, 0x74, 0x73, 0x68, 0x6f, 0x77, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x23, 0x0a,
	0x0c, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x1b, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x0d, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x5f, 0x73, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x12, 0x22, 0x0a, 0x0c, 0x52, 0x65, 0x63, 0x68, 0x65, 0x63, 0x6b, 0x53, 0x74, 0x61,
	0x74, 0x65, 0x18, 0x1c, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0c, 0x52, 0x65, 0x63, 0x68, 0x65, 0x63,
	0x6b, 0x53, 0x74, 0x61, 0x74, 0x65, 0x12, 0x28, 0x0a, 0x0f, 0x61, 0x72, 0x74, 0x69, 0x73, 0x74,
	0x53, 0x68, 0x6f, 0x77, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x1d, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0f, 0x61, 0x72, 0x74, 0x69, 0x73, 0x74, 0x53, 0x68, 0x6f, 0x77, 0x43, 0x6f, 0x75, 0x6e, 0x74,
	0x12, 0x1e, 0x0a, 0x0a, 0x41, 0x72, 0x74, 0x69, 0x73, 0x74, 0x55, 0x75, 0x69, 0x64, 0x18, 0x1e,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x61, 0x72, 0x74, 0x69, 0x73, 0x74, 0x55, 0x75, 0x69, 0x64,
	0x12, 0x1c, 0x0a, 0x09, 0x41, 0x72, 0x74, 0x73, 0x68, 0x6f, 0x77, 0x49, 0x64, 0x18, 0x1f, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x09, 0x41, 0x72, 0x74, 0x73, 0x68, 0x6f, 0x77, 0x49, 0x64, 0x12, 0x28,
	0x0a, 0x0f, 0x43, 0x6f, 0x70, 0x79, 0x72, 0x69, 0x67, 0x68, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x18, 0x20, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x43, 0x6f, 0x70, 0x79, 0x72, 0x69, 0x67,
	0x68, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x30, 0x0a, 0x13, 0x4c, 0x69, 0x63, 0x65,
	0x6e, 0x73, 0x65, 0x41, 0x67, 0x72, 0x65, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x55, 0x72, 0x6c, 0x18,
	0x21, 0x20, 0x01, 0x28, 0x09, 0x52, 0x13, 0x4c, 0x69, 0x63, 0x65, 0x6e, 0x73, 0x65, 0x41, 0x67,
	0x72, 0x65, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x55, 0x72, 0x6c, 0x12, 0x28, 0x0a, 0x0f, 0x42, 0x6c,
	0x61, 0x63, 0x6b, 0x4c, 0x69, 0x73, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x22, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x0f, 0x42, 0x6c, 0x61, 0x63, 0x6b, 0x4c, 0x69, 0x73, 0x74, 0x53, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x12, 0x12, 0x0a, 0x04, 0x54, 0x6e, 0x75, 0x6d, 0x18, 0x23, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x04, 0x54, 0x6e, 0x75, 0x6d, 0x22, 0x4a, 0x0a, 0x0c, 0x44, 0x65, 0x6c, 0x41,
	0x77, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x3a, 0x0a, 0x0b, 0x41, 0x72, 0x74, 0x77,
	0x6f, 0x72, 0x6b, 0x55, 0x75, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x17, 0xe2,
	0xdf, 0x1f, 0x13, 0x2a, 0x0f, 0xe8, 0xaf, 0xb7, 0xe9, 0x80, 0x89, 0xe6, 0x8b, 0xa9, 0xe7, 0x94,
	0xbb, 0xe4, 0xbd, 0x9c, 0x58, 0x01, 0x52, 0x0c, 0x61, 0x72, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x5f,
	0x75, 0x75, 0x69, 0x64, 0x22, 0x21, 0x0a, 0x0d, 0x44, 0x65, 0x6c, 0x41, 0x77, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x4d, 0x73, 0x67, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x03, 0x6d, 0x73, 0x67, 0x22, 0x26, 0x0a, 0x12, 0x44, 0x65, 0x6c, 0x41, 0x75,
	0x74, 0x68, 0x44, 0x61, 0x74, 0x61, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x10, 0x0a,
	0x03, 0x49, 0x64, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x05, 0x52, 0x03, 0x69, 0x64, 0x73, 0x22,
	0x27, 0x0a, 0x13, 0x44, 0x65, 0x6c, 0x41, 0x75, 0x74, 0x68, 0x44, 0x61, 0x74, 0x61, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x4d, 0x73, 0x67, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x03, 0x6d, 0x73, 0x67, 0x22, 0x35, 0x0a, 0x14, 0x44, 0x65, 0x6c, 0x4d,
	0x61, 0x72, 0x6b, 0x65, 0x74, 0x44, 0x61, 0x74, 0x61, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x12, 0x1d, 0x0a, 0x09, 0x4d, 0x61, 0x72, 0x6b, 0x65, 0x74, 0x49, 0x64, 0x73, 0x18, 0x01, 0x20,
	0x03, 0x28, 0x05, 0x52, 0x0a, 0x6d, 0x61, 0x72, 0x6b, 0x65, 0x74, 0x5f, 0x69, 0x64, 0x73, 0x22,
	0x29, 0x0a, 0x15, 0x44, 0x65, 0x6c, 0x4d, 0x61, 0x72, 0x6b, 0x65, 0x74, 0x44, 0x61, 0x74, 0x61,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x4d, 0x73, 0x67, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6d, 0x73, 0x67, 0x22, 0x29, 0x0a, 0x15, 0x44, 0x65,
	0x6c, 0x53, 0x74, 0x6f, 0x72, 0x61, 0x67, 0x65, 0x44, 0x61, 0x74, 0x61, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x12, 0x10, 0x0a, 0x03, 0x49, 0x64, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x05,
	0x52, 0x03, 0x69, 0x64, 0x73, 0x22, 0x2a, 0x0a, 0x16, 0x44, 0x65, 0x6c, 0x53, 0x74, 0x6f, 0x72,
	0x61, 0x67, 0x65, 0x44, 0x61, 0x74, 0x61, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12,
	0x10, 0x0a, 0x03, 0x4d, 0x73, 0x67, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6d, 0x73,
	0x67, 0x22, 0x11, 0x0a, 0x0f, 0x54, 0x61, 0x67, 0x73, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x22, 0xaa, 0x01, 0x0a, 0x08, 0x54, 0x61, 0x67, 0x73, 0x44, 0x61, 0x74,
	0x61, 0x12, 0x37, 0x0a, 0x09, 0x54, 0x61, 0x67, 0x73, 0x46, 0x69, 0x72, 0x73, 0x74, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x41, 0x72, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2e, 0x54,
	0x61, 0x67, 0x73, 0x44, 0x61, 0x74, 0x61, 0x2e, 0x54, 0x61, 0x67, 0x73, 0x49, 0x6e, 0x66, 0x6f,
	0x52, 0x08, 0x74, 0x61, 0x67, 0x73, 0x5f, 0x74, 0x6f, 0x70, 0x12, 0x2e, 0x0a, 0x04, 0x4c, 0x69,
	0x73, 0x74, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x41, 0x72, 0x74, 0x77, 0x6f,
	0x72, 0x6b, 0x2e, 0x54, 0x61, 0x67, 0x73, 0x44, 0x61, 0x74, 0x61, 0x2e, 0x54, 0x61, 0x67, 0x73,
	0x49, 0x6e, 0x66, 0x6f, 0x52, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x1a, 0x35, 0x0a, 0x08, 0x54, 0x61,
	0x67, 0x73, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x0e, 0x0a, 0x02, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x02, 0x69, 0x64, 0x12, 0x19, 0x0a, 0x07, 0x43, 0x61, 0x74, 0x4e, 0x61, 0x6d,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x63, 0x61, 0x74, 0x5f, 0x6e, 0x61, 0x6d,
	0x65, 0x22, 0x4f, 0x0a, 0x10, 0x54, 0x61, 0x67, 0x73, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x29, 0x0a, 0x08, 0x54, 0x61, 0x67, 0x73, 0x44, 0x61, 0x74,
	0x61, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x41, 0x72, 0x74, 0x77, 0x6f, 0x72,
	0x6b, 0x2e, 0x54, 0x61, 0x67, 0x73, 0x44, 0x61, 0x74, 0x61, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61,
	0x12, 0x10, 0x0a, 0x03, 0x4d, 0x73, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6d,
	0x73, 0x67, 0x22, 0x36, 0x0a, 0x0e, 0x43, 0x61, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x12, 0x10, 0x0a, 0x03, 0x50, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x03, 0x50, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x50, 0x69, 0x64, 0x73, 0x18, 0x02,
	0x20, 0x03, 0x28, 0x05, 0x52, 0x04, 0x50, 0x69, 0x64, 0x73, 0x22, 0xa1, 0x01, 0x0a, 0x0f, 0x43,
	0x61, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x34,
	0x0a, 0x04, 0x44, 0x61, 0x74, 0x61, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x20, 0x2e, 0x41,
	0x72, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2e, 0x43, 0x61, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x43, 0x61, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x04,
	0x64, 0x61, 0x74, 0x61, 0x12, 0x10, 0x0a, 0x03, 0x4d, 0x73, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x03, 0x6d, 0x73, 0x67, 0x1a, 0x46, 0x0a, 0x07, 0x43, 0x61, 0x74, 0x49, 0x6e, 0x66,
	0x6f, 0x12, 0x0e, 0x0a, 0x02, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x02, 0x69,
	0x64, 0x12, 0x19, 0x0a, 0x07, 0x43, 0x61, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x08, 0x63, 0x61, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x10, 0x0a, 0x03,
	0x50, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x03, 0x50, 0x69, 0x64, 0x22, 0xe5,
	0x01, 0x0a, 0x0f, 0x49, 0x6d, 0x67, 0x4d, 0x61, 0x74, 0x63, 0x68, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x12, 0x21, 0x0a, 0x0b, 0x41, 0x72, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x4e, 0x61, 0x6d,
	0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x61, 0x72, 0x74, 0x77, 0x6f, 0x72, 0x6b,
	0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x33, 0x0a, 0x06, 0x49, 0x6d, 0x67, 0x55, 0x72, 0x6c, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x1a, 0xe2, 0xdf, 0x1f, 0x16, 0x2a, 0x12, 0xe5, 0x9b, 0xbe,
	0xe7, 0x89, 0x87, 0xe4, 0xb8, 0x8d, 0xe8, 0x83, 0xbd, 0xe4, 0xb8, 0xba, 0xe7, 0xa9, 0xba, 0x58,
	0x01, 0x52, 0x07, 0x69, 0x6d, 0x67, 0x5f, 0x75, 0x72, 0x6c, 0x12, 0x35, 0x0a, 0x07, 0x55, 0x73,
	0x65, 0x54, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x42, 0x1a, 0xe2, 0xdf, 0x1f,
	0x16, 0x10, 0x00, 0x2a, 0x12, 0xe7, 0x94, 0xa8, 0xe9, 0x80, 0x94, 0xe4, 0xb8, 0x8d, 0xe8, 0x83,
	0xbd, 0xe4, 0xb8, 0xba, 0xe7, 0xa9, 0xba, 0x52, 0x08, 0x75, 0x73, 0x65, 0x5f, 0x74, 0x79, 0x70,
	0x65, 0x12, 0x43, 0x0a, 0x0b, 0x41, 0x72, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x55, 0x75, 0x69, 0x64,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x42, 0x20, 0xe2, 0xdf, 0x1f, 0x1c, 0x10, 0x00, 0x2a, 0x18,
	0xe7, 0x94, 0xbb, 0xe4, 0xbd, 0x9c, 0xe5, 0x90, 0x8d, 0xe5, 0xad, 0x97, 0xe8, 0x8e, 0xb7, 0xe5,
	0x8f, 0x96, 0xe9, 0x94, 0x99, 0xe8, 0xaf, 0xaf, 0x52, 0x0c, 0x61, 0x72, 0x74, 0x77, 0x6f, 0x72,
	0x6b, 0x5f, 0x75, 0x75, 0x69, 0x64, 0x22, 0x24, 0x0a, 0x10, 0x49, 0x6d, 0x67, 0x4d, 0x61, 0x74,
	0x63, 0x68, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x4d, 0x73,
	0x67, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6d, 0x73, 0x67, 0x22, 0xf7, 0x01, 0x0a,
	0x12, 0x42, 0x61, 0x74, 0x63, 0x68, 0x42, 0x69, 0x74, 0x4d, 0x61, 0x70, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x12, 0x3d, 0x0a, 0x07, 0x42, 0x69, 0x74, 0x44, 0x61, 0x74, 0x61, 0x18, 0x01,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x23, 0x2e, 0x41, 0x72, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2e, 0x42,
	0x61, 0x74, 0x63, 0x68, 0x42, 0x69, 0x74, 0x4d, 0x61, 0x70, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x2e, 0x42, 0x69, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x07, 0x42, 0x69, 0x74, 0x44, 0x61,
	0x74, 0x61, 0x12, 0x43, 0x0a, 0x0b, 0x41, 0x72, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x55, 0x75, 0x69,
	0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x20, 0xe2, 0xdf, 0x1f, 0x1c, 0x10, 0x00, 0x2a,
	0x18, 0xe7, 0x94, 0xbb, 0xe4, 0xbd, 0x9c, 0xe5, 0x90, 0x8d, 0xe5, 0xad, 0x97, 0xe8, 0x8e, 0xb7,
	0xe5, 0x8f, 0x96, 0xe9, 0x94, 0x99, 0xe8, 0xaf, 0xaf, 0x52, 0x0c, 0x61, 0x72, 0x74, 0x77, 0x6f,
	0x72, 0x6b, 0x5f, 0x75, 0x75, 0x69, 0x64, 0x1a, 0x5d, 0x0a, 0x07, 0x42, 0x69, 0x74, 0x49, 0x6e,
	0x66, 0x6f, 0x12, 0x1a, 0x0a, 0x08, 0x42, 0x69, 0x74, 0x49, 0x6e, 0x64, 0x65, 0x78, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x42, 0x69, 0x74, 0x49, 0x6e, 0x64, 0x65, 0x78, 0x12, 0x1c,
	0x0a, 0x09, 0x49, 0x6d, 0x67, 0x4f, 0x73, 0x73, 0x55, 0x72, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x09, 0x49, 0x6d, 0x67, 0x4f, 0x73, 0x73, 0x55, 0x72, 0x6c, 0x12, 0x18, 0x0a, 0x07,
	0x42, 0x69, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x42,
	0x69, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x22, 0x27, 0x0a, 0x13, 0x42, 0x61, 0x74, 0x63, 0x68, 0x42,
	0x69, 0x74, 0x4d, 0x61, 0x70, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x10, 0x0a,
	0x03, 0x4d, 0x73, 0x67, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6d, 0x73, 0x67, 0x22,
	0x5b, 0x0a, 0x17, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x41, 0x72, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x4e,
	0x61, 0x6d, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x40, 0x0a, 0x0b, 0x41, 0x72,
	0x74, 0x77, 0x6f, 0x72, 0x6b, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42,
	0x1d, 0xe2, 0xdf, 0x1f, 0x19, 0x2a, 0x15, 0xe7, 0x94, 0xbb, 0xe4, 0xbd, 0x9c, 0xe5, 0x90, 0x8d,
	0xe4, 0xb8, 0x8d, 0xe8, 0x83, 0xbd, 0xe4, 0xb8, 0xba, 0xe7, 0xa9, 0xba, 0x58, 0x01, 0x52, 0x0c,
	0x61, 0x72, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x22, 0x6e, 0x0a, 0x18,
	0x43, 0x68, 0x65, 0x63, 0x6b, 0x41, 0x72, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x4e, 0x61, 0x6d, 0x65,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x21, 0x0a, 0x0b, 0x41, 0x72, 0x74, 0x77,
	0x6f, 0x72, 0x6b, 0x55, 0x75, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x61,
	0x72, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x5f, 0x75, 0x75, 0x69, 0x64, 0x12, 0x1d, 0x0a, 0x09, 0x41,
	0x72, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x49, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0a,
	0x61, 0x72, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x5f, 0x69, 0x64, 0x12, 0x10, 0x0a, 0x03, 0x4d, 0x73,
	0x67, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6d, 0x73, 0x67, 0x22, 0x4c, 0x0a, 0x18,
	0x43, 0x68, 0x65, 0x63, 0x6b, 0x41, 0x72, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x54, 0x66, 0x6e, 0x75,
	0x6d, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x30, 0x0a, 0x05, 0x54, 0x66, 0x6e, 0x75,
	0x6d, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x1a, 0xe2, 0xdf, 0x1f, 0x16, 0x2a, 0x12, 0xe7,
	0xbc, 0x96, 0xe5, 0x8f, 0xb7, 0xe4, 0xb8, 0x8d, 0xe8, 0x83, 0xbd, 0xe4, 0xb8, 0xba, 0xe7, 0xa9,
	0xba, 0x58, 0x01, 0x52, 0x05, 0x74, 0x66, 0x6e, 0x75, 0x6d, 0x22, 0x8f, 0x01, 0x0a, 0x19, 0x43,
	0x68, 0x65, 0x63, 0x6b, 0x41, 0x72, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x54, 0x66, 0x6e, 0x75, 0x6d,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x21, 0x0a, 0x0b, 0x41, 0x72, 0x74, 0x77,
	0x6f, 0x72, 0x6b, 0x55, 0x75, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x61,
	0x72, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x5f, 0x75, 0x75, 0x69, 0x64, 0x12, 0x1d, 0x0a, 0x09, 0x41,
	0x72, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x49, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0a,
	0x61, 0x72, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x5f, 0x69, 0x64, 0x12, 0x10, 0x0a, 0x03, 0x4d, 0x73,
	0x67, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6d, 0x73, 0x67, 0x12, 0x1e, 0x0a, 0x0a,
	0x41, 0x72, 0x74, 0x69, 0x73, 0x74, 0x55, 0x75, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0a, 0x41, 0x72, 0x74, 0x69, 0x73, 0x74, 0x55, 0x75, 0x69, 0x64, 0x22, 0xb2, 0x01, 0x0a,
	0x17, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x54, 0x68, 0x69, 0x72, 0x64, 0x50, 0x61, 0x72, 0x74,
	0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x19, 0x0a, 0x07, 0x54, 0x68, 0x69, 0x72,
	0x64, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x74, 0x68, 0x69, 0x72, 0x64,
	0x5f, 0x69, 0x64, 0x12, 0x3a, 0x0a, 0x0b, 0x41, 0x72, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x55, 0x75,
	0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x17, 0xe2, 0xdf, 0x1f, 0x13, 0x2a, 0x0f,
	0xe8, 0xaf, 0xb7, 0xe9, 0x80, 0x89, 0xe6, 0x8b, 0xa9, 0xe7, 0x94, 0xbb, 0xe4, 0xbd, 0x9c, 0x58,
	0x01, 0x52, 0x0c, 0x61, 0x72, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x5f, 0x75, 0x75, 0x69, 0x64, 0x12,
	0x40, 0x0a, 0x0c, 0x54, 0x68, 0x69, 0x72, 0x64, 0x43, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x09, 0x42, 0x1d, 0xe2, 0xdf, 0x1f, 0x19, 0x2a, 0x15, 0xe5, 0xb1, 0x9e,
	0xe6, 0x80, 0xa7, 0xe5, 0x80, 0xbc, 0xe4, 0xb8, 0x8d, 0xe8, 0x83, 0xbd, 0xe4, 0xb8, 0xba, 0xe7,
	0xa9, 0xba, 0x58, 0x01, 0x52, 0x0b, 0x63, 0x6c, 0x61, 0x73, 0x73, 0x5f, 0x76, 0x61, 0x6c, 0x75,
	0x65, 0x22, 0x2c, 0x0a, 0x18, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x54, 0x68, 0x69, 0x72, 0x64,
	0x50, 0x61, 0x72, 0x74, 0x79, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x10, 0x0a,
	0x03, 0x4d, 0x73, 0x67, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6d, 0x73, 0x67, 0x22,
	0x33, 0x0a, 0x14, 0x44, 0x65, 0x6c, 0x54, 0x68, 0x69, 0x72, 0x64, 0x50, 0x61, 0x72, 0x74, 0x79,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1b, 0x0a, 0x08, 0x54, 0x68, 0x69, 0x72, 0x64,
	0x49, 0x64, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x05, 0x52, 0x09, 0x74, 0x68, 0x69, 0x72, 0x64,
	0x5f, 0x69, 0x64, 0x73, 0x22, 0x29, 0x0a, 0x15, 0x44, 0x65, 0x6c, 0x54, 0x68, 0x69, 0x72, 0x64,
	0x50, 0x61, 0x72, 0x74, 0x79, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x10, 0x0a,
	0x03, 0x4d, 0x73, 0x67, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6d, 0x73, 0x67, 0x22,
	0x53, 0x0a, 0x15, 0x54, 0x68, 0x69, 0x72, 0x64, 0x50, 0x61, 0x72, 0x74, 0x79, 0x4c, 0x69, 0x73,
	0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x3a, 0x0a, 0x0b, 0x41, 0x72, 0x74, 0x77,
	0x6f, 0x72, 0x6b, 0x55, 0x75, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x17, 0xe2,
	0xdf, 0x1f, 0x13, 0x2a, 0x0f, 0xe8, 0xaf, 0xb7, 0xe9, 0x80, 0x89, 0xe6, 0x8b, 0xa9, 0xe7, 0x94,
	0xbb, 0xe4, 0xbd, 0x9c, 0x58, 0x01, 0x52, 0x0c, 0x61, 0x72, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x5f,
	0x75, 0x75, 0x69, 0x64, 0x22, 0x3e, 0x0a, 0x16, 0x54, 0x68, 0x69, 0x72, 0x64, 0x50, 0x61, 0x72,
	0x74, 0x79, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x12,
	0x0a, 0x04, 0x44, 0x61, 0x74, 0x61, 0x18, 0x02, 0x20, 0x03, 0x28, 0x09, 0x52, 0x04, 0x64, 0x61,
	0x74, 0x61, 0x12, 0x10, 0x0a, 0x03, 0x4d, 0x73, 0x67, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x03, 0x6d, 0x73, 0x67, 0x22, 0x9c, 0x02, 0x0a, 0x1a, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x41,
	0x77, 0x53, 0x74, 0x6f, 0x63, 0x6b, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x12, 0x21, 0x0a, 0x0b, 0x41, 0x72, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x55, 0x75,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x61, 0x72, 0x74, 0x77, 0x6f, 0x72,
	0x6b, 0x5f, 0x75, 0x75, 0x69, 0x64, 0x12, 0x1f, 0x0a, 0x0a, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e,
	0x54, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0b, 0x61, 0x63, 0x74, 0x69,
	0x6f, 0x6e, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x12, 0x19, 0x0a, 0x07, 0x41, 0x64, 0x6d, 0x69, 0x6e,
	0x49, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x5f,
	0x69, 0x64, 0x12, 0x1f, 0x0a, 0x0a, 0x44, 0x65, 0x70, 0x61, 0x72, 0x74, 0x4e, 0x61, 0x6d, 0x65,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x65, 0x70, 0x61, 0x72, 0x74, 0x5f, 0x6e,
	0x61, 0x6d, 0x65, 0x12, 0x1f, 0x0a, 0x0a, 0x41, 0x72, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x49, 0x64,
	0x73, 0x18, 0x05, 0x20, 0x03, 0x28, 0x05, 0x52, 0x0b, 0x61, 0x72, 0x74, 0x77, 0x6f, 0x72, 0x6b,
	0x5f, 0x69, 0x64, 0x73, 0x12, 0x1d, 0x0a, 0x09, 0x41, 0x6c, 0x6c, 0x6f, 0x74, 0x55, 0x69, 0x64,
	0x73, 0x18, 0x06, 0x20, 0x03, 0x28, 0x05, 0x52, 0x0a, 0x61, 0x6c, 0x6c, 0x6f, 0x74, 0x5f, 0x75,
	0x69, 0x64, 0x73, 0x12, 0x21, 0x0a, 0x0b, 0x52, 0x65, 0x63, 0x65, 0x69, 0x76, 0x65, 0x44, 0x61,
	0x74, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x72, 0x65, 0x63, 0x65, 0x69, 0x76,
	0x65, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x12, 0x1b, 0x0a, 0x08, 0x50, 0x6f, 0x73, 0x74, 0x4e, 0x61,
	0x6d, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x70, 0x6f, 0x73, 0x74, 0x5f, 0x6e,
	0x61, 0x6d, 0x65, 0x22, 0x2f, 0x0a, 0x1b, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x41, 0x77, 0x53,
	0x74, 0x6f, 0x63, 0x6b, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x4d, 0x73, 0x67, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x03, 0x6d, 0x73, 0x67, 0x22, 0xc3, 0x01, 0x0a, 0x14, 0x53, 0x79, 0x6e, 0x63, 0x41, 0x72, 0x74,
	0x53, 0x68, 0x6f, 0x77, 0x49, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x36, 0x0a,
	0x04, 0x44, 0x61, 0x74, 0x61, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x41, 0x72,
	0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2e, 0x53, 0x79, 0x6e, 0x63, 0x41, 0x72, 0x74, 0x53, 0x68, 0x6f,
	0x77, 0x49, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x69, 0x6e, 0x66, 0x6f, 0x52,
	0x04, 0x64, 0x61, 0x74, 0x61, 0x12, 0x23, 0x0a, 0x0c, 0x41, 0x72, 0x74, 0x53, 0x68, 0x6f, 0x77,
	0x55, 0x75, 0x69, 0x64, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0d, 0x61, 0x72, 0x74,
	0x73, 0x68, 0x6f, 0x77, 0x5f, 0x75, 0x75, 0x69, 0x64, 0x73, 0x1a, 0x4e, 0x0a, 0x04, 0x69, 0x6e,
	0x66, 0x6f, 0x12, 0x21, 0x0a, 0x0b, 0x41, 0x72, 0x74, 0x53, 0x68, 0x6f, 0x77, 0x55, 0x75, 0x69,
	0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x61, 0x72, 0x74, 0x73, 0x68, 0x6f, 0x77,
	0x5f, 0x75, 0x75, 0x69, 0x64, 0x12, 0x23, 0x0a, 0x0c, 0x41, 0x72, 0x74, 0x77, 0x6f, 0x72, 0x6b,
	0x55, 0x75, 0x69, 0x64, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0d, 0x61, 0x72, 0x74,
	0x77, 0x6f, 0x72, 0x6b, 0x5f, 0x75, 0x75, 0x69, 0x64, 0x73, 0x22, 0x29, 0x0a, 0x15, 0x53, 0x79,
	0x6e, 0x63, 0x41, 0x72, 0x74, 0x53, 0x68, 0x6f, 0x77, 0x49, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x4d, 0x73, 0x67, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x03, 0x6d, 0x73, 0x67, 0x22, 0x12, 0x0a, 0x10, 0x53, 0x68, 0x65, 0x6c, 0x66, 0x4c, 0x69,
	0x73, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x22, 0x9d, 0x01, 0x0a, 0x11, 0x53, 0x68,
	0x65, 0x6c, 0x66, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12,
	0x38, 0x0a, 0x04, 0x44, 0x61, 0x74, 0x61, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x24, 0x2e,
	0x41, 0x72, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2e, 0x53, 0x68, 0x65, 0x6c, 0x66, 0x4c, 0x69, 0x73,
	0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x53, 0x68, 0x65, 0x6c, 0x66, 0x49,
	0x6e, 0x66, 0x6f, 0x52, 0x04, 0x44, 0x61, 0x74, 0x61, 0x12, 0x10, 0x0a, 0x03, 0x4d, 0x73, 0x67,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x4d, 0x73, 0x67, 0x1a, 0x3c, 0x0a, 0x09, 0x53,
	0x68, 0x65, 0x6c, 0x66, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x19, 0x0a, 0x07, 0x53, 0x68, 0x65, 0x6c,
	0x66, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x73, 0x68, 0x65, 0x6c, 0x66,
	0x5f, 0x69, 0x64, 0x12, 0x14, 0x0a, 0x07, 0x53, 0x68, 0x65, 0x6c, 0x66, 0x4e, 0x6f, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6d, 0x73, 0x67, 0x22, 0xc9, 0x01, 0x0a, 0x1a, 0x55, 0x70,
	0x64, 0x61, 0x74, 0x65, 0x43, 0x6f, 0x70, 0x79, 0x72, 0x69, 0x67, 0x68, 0x74, 0x48, 0x61, 0x73,
	0x68, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x3a, 0x0a, 0x0b, 0x41, 0x72, 0x74, 0x77,
	0x6f, 0x72, 0x6b, 0x55, 0x75, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x17, 0xe2,
	0xdf, 0x1f, 0x13, 0x2a, 0x0f, 0xe8, 0xaf, 0xb7, 0xe9, 0x80, 0x89, 0xe6, 0x8b, 0xa9, 0xe7, 0x94,
	0xbb, 0xe5, 0xae, 0xb6, 0x58, 0x01, 0x52, 0x0c, 0x61, 0x72, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x5f,
	0x75, 0x75, 0x69, 0x64, 0x12, 0x24, 0x0a, 0x0d, 0x43, 0x6f, 0x70, 0x79, 0x72, 0x69, 0x67, 0x68,
	0x74, 0x48, 0x61, 0x73, 0x68, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x63, 0x6f, 0x70,
	0x79, 0x72, 0x69, 0x67, 0x68, 0x74, 0x48, 0x61, 0x73, 0x68, 0x12, 0x49, 0x0a, 0x0d, 0x43, 0x6f,
	0x70, 0x79, 0x72, 0x69, 0x67, 0x68, 0x74, 0x50, 0x61, 0x74, 0x68, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x09, 0x42, 0x23, 0xe2, 0xdf, 0x1f, 0x1f, 0x2a, 0x1b, 0xe7, 0x89, 0x88, 0xe6, 0x9d, 0x83, 0xe5,
	0x9b, 0xbe, 0xe8, 0xb7, 0xaf, 0xe5, 0xbe, 0x84, 0xe4, 0xb8, 0x8d, 0xe8, 0x83, 0xbd, 0xe4, 0xb8,
	0xba, 0xe7, 0xa9, 0xba, 0x58, 0x01, 0x52, 0x0d, 0x63, 0x6f, 0x70, 0x79, 0x72, 0x69, 0x67, 0x68,
	0x74, 0x50, 0x61, 0x74, 0x68, 0x22, 0x2f, 0x0a, 0x1b, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x43,
	0x6f, 0x70, 0x79, 0x72, 0x69, 0x67, 0x68, 0x74, 0x48, 0x61, 0x73, 0x68, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x4d, 0x73, 0x67, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x03, 0x6d, 0x73, 0x67, 0x22, 0xdd, 0x01, 0x0a, 0x14, 0x45, 0x78, 0x70, 0x6f, 0x72,
	0x74, 0x41, 0x72, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12,
	0x18, 0x0a, 0x07, 0x4b, 0x65, 0x79, 0x77, 0x6f, 0x72, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x07, 0x6b, 0x65, 0x79, 0x77, 0x6f, 0x72, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x50, 0x61, 0x67,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x70, 0x61, 0x67, 0x65, 0x12, 0x1b, 0x0a,
	0x08, 0x50, 0x61, 0x67, 0x65, 0x53, 0x69, 0x7a, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x09, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x73, 0x69, 0x7a, 0x65, 0x12, 0x34, 0x0a, 0x08, 0x43, 0x6f,
	0x6c, 0x75, 0x6d, 0x6e, 0x49, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x42, 0x17, 0xe2, 0xdf,
	0x1f, 0x13, 0x2a, 0x0f, 0xe8, 0xaf, 0xb7, 0xe9, 0x80, 0x89, 0xe6, 0x8b, 0xa9, 0xe5, 0xad, 0x97,
	0xe6, 0xae, 0xb5, 0x58, 0x01, 0x52, 0x09, 0x63, 0x6f, 0x6c, 0x75, 0x6d, 0x6e, 0x5f, 0x69, 0x64,
	0x12, 0x1f, 0x0a, 0x0a, 0x43, 0x6f, 0x6c, 0x75, 0x6d, 0x6e, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x63, 0x6f, 0x6c, 0x75, 0x6d, 0x6e, 0x5f, 0x6e, 0x61, 0x6d,
	0x65, 0x12, 0x23, 0x0a, 0x0c, 0x41, 0x72, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x55, 0x75, 0x69, 0x64,
	0x73, 0x18, 0x06, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0d, 0x61, 0x72, 0x74, 0x77, 0x6f, 0x72, 0x6b,
	0x5f, 0x75, 0x75, 0x69, 0x64, 0x73, 0x22, 0xdf, 0x11, 0x0a, 0x15, 0x45, 0x78, 0x70, 0x6f, 0x72,
	0x74, 0x41, 0x72, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x12, 0x37, 0x0a, 0x04, 0x44, 0x61, 0x74, 0x61, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x23,
	0x2e, 0x41, 0x72, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2e, 0x45, 0x78, 0x70, 0x6f, 0x72, 0x74, 0x41,
	0x72, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x49,
	0x6e, 0x66, 0x6f, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x12, 0x1f, 0x0a, 0x0a, 0x53, 0x74, 0x72,
	0x75, 0x63, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x73,
	0x74, 0x72, 0x75, 0x63, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x1f, 0x0a, 0x0a, 0x43, 0x6f,
	0x6c, 0x75, 0x6d, 0x6e, 0x44, 0x65, 0x73, 0x63, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b,
	0x63, 0x6f, 0x6c, 0x75, 0x6d, 0x6e, 0x5f, 0x64, 0x65, 0x73, 0x63, 0x12, 0x10, 0x0a, 0x03, 0x4d,
	0x73, 0x67, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6d, 0x73, 0x67, 0x1a, 0xb8, 0x10,
	0x0a, 0x04, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x21, 0x0a, 0x0b, 0x41, 0x72, 0x74, 0x77, 0x6f, 0x72,
	0x6b, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x61, 0x72, 0x74,
	0x77, 0x6f, 0x72, 0x6b, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x1f, 0x0a, 0x0a, 0x41, 0x72, 0x74,
	0x69, 0x73, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x61,
	0x72, 0x74, 0x69, 0x73, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x23, 0x0a, 0x0c, 0x41, 0x72,
	0x74, 0x43, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x0d, 0x61, 0x72, 0x74, 0x5f, 0x63, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x12,
	0x1c, 0x0a, 0x09, 0x4d, 0x6f, 0x75, 0x6e, 0x74, 0x6d, 0x6f, 0x64, 0x65, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x09, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x6d, 0x6f, 0x64, 0x65, 0x12, 0x25, 0x0a,
	0x0d, 0x41, 0x72, 0x74, 0x48, 0x6f, 0x72, 0x69, 0x7a, 0x6f, 0x6e, 0x74, 0x61, 0x6c, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x0e, 0x61, 0x72, 0x74, 0x5f, 0x68, 0x6f, 0x72, 0x69, 0x7a, 0x6f,
	0x6e, 0x74, 0x61, 0x6c, 0x12, 0x12, 0x0a, 0x04, 0x53, 0x69, 0x7a, 0x65, 0x18, 0x06, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x04, 0x73, 0x69, 0x7a, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x4c, 0x65, 0x6e, 0x67,
	0x74, 0x68, 0x18, 0x07, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x6c, 0x65, 0x6e, 0x67, 0x74, 0x68,
	0x12, 0x14, 0x0a, 0x05, 0x57, 0x69, 0x64, 0x74, 0x68, 0x18, 0x08, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x05, 0x77, 0x69, 0x64, 0x74, 0x68, 0x12, 0x14, 0x0a, 0x05, 0x52, 0x75, 0x6c, 0x65, 0x72, 0x18,
	0x09, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x72, 0x75, 0x6c, 0x65, 0x72, 0x12, 0x23, 0x0a, 0x0c,
	0x49, 0x6e, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x44, 0x61, 0x74, 0x65, 0x18, 0x0a, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0d, 0x69, 0x6e, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x5f, 0x64, 0x61, 0x74,
	0x65, 0x12, 0x21, 0x0a, 0x0b, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x44, 0x61, 0x74, 0x65,
	0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f,
	0x64, 0x61, 0x74, 0x65, 0x12, 0x27, 0x0a, 0x0e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41,
	0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x63, 0x72,
	0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x12, 0x1a, 0x0a,
	0x08, 0x41, 0x62, 0x73, 0x74, 0x72, 0x61, 0x63, 0x74, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x08, 0x61, 0x62, 0x73, 0x74, 0x72, 0x61, 0x63, 0x74, 0x12, 0x1f, 0x0a, 0x0a, 0x50, 0x72, 0x69,
	0x63, 0x65, 0x52, 0x75, 0x6c, 0x65, 0x72, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x01, 0x52, 0x0b, 0x70,
	0x72, 0x69, 0x63, 0x65, 0x5f, 0x72, 0x75, 0x6c, 0x65, 0x72, 0x12, 0x27, 0x0a, 0x0e, 0x50, 0x72,
	0x69, 0x63, 0x65, 0x43, 0x6f, 0x70, 0x79, 0x72, 0x69, 0x67, 0x68, 0x74, 0x18, 0x0f, 0x20, 0x01,
	0x28, 0x01, 0x52, 0x0f, 0x70, 0x72, 0x69, 0x63, 0x65, 0x5f, 0x63, 0x6f, 0x70, 0x79, 0x72, 0x69,
	0x67, 0x68, 0x74, 0x12, 0x23, 0x0a, 0x0c, 0x50, 0x72, 0x69, 0x63, 0x65, 0x41, 0x72, 0x74, 0x77,
	0x6f, 0x72, 0x6b, 0x18, 0x10, 0x20, 0x01, 0x28, 0x01, 0x52, 0x0d, 0x70, 0x72, 0x69, 0x63, 0x65,
	0x5f, 0x61, 0x72, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x12, 0x21, 0x0a, 0x0b, 0x50, 0x72, 0x69, 0x63,
	0x65, 0x4d, 0x61, 0x72, 0x6b, 0x65, 0x74, 0x18, 0x11, 0x20, 0x01, 0x28, 0x01, 0x52, 0x0c, 0x70,
	0x72, 0x69, 0x63, 0x65, 0x5f, 0x6d, 0x61, 0x72, 0x6b, 0x65, 0x74, 0x12, 0x16, 0x0a, 0x06, 0x42,
	0x65, 0x6c, 0x6f, 0x6e, 0x67, 0x18, 0x12, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x62, 0x65, 0x6c,
	0x6f, 0x6e, 0x67, 0x12, 0x1d, 0x0a, 0x09, 0x46, 0x6c, 0x6f, 0x77, 0x53, 0x74, 0x61, 0x74, 0x65,
	0x18, 0x13, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0a, 0x66, 0x6c, 0x6f, 0x77, 0x5f, 0x73, 0x74, 0x61,
	0x74, 0x65, 0x12, 0x1f, 0x0a, 0x0a, 0x41, 0x72, 0x74, 0x51, 0x75, 0x61, 0x6c, 0x69, 0x74, 0x79,
	0x18, 0x14, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0b, 0x61, 0x72, 0x74, 0x5f, 0x71, 0x75, 0x61, 0x6c,
	0x69, 0x74, 0x79, 0x12, 0x25, 0x0a, 0x0d, 0x49, 0x6e, 0x63, 0x6f, 0x6d, 0x70, 0x6c, 0x65, 0x74,
	0x65, 0x50, 0x69, 0x63, 0x18, 0x15, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x69, 0x6e, 0x63, 0x6f,
	0x6d, 0x70, 0x6c, 0x65, 0x74, 0x65, 0x5f, 0x70, 0x69, 0x63, 0x12, 0x18, 0x0a, 0x07, 0x53, 0x69,
	0x67, 0x6e, 0x70, 0x69, 0x63, 0x18, 0x16, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x73, 0x69, 0x67,
	0x6e, 0x70, 0x69, 0x63, 0x12, 0x18, 0x0a, 0x07, 0x53, 0x65, 0x61, 0x6c, 0x70, 0x69, 0x63, 0x18,
	0x17, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x73, 0x65, 0x61, 0x6c, 0x70, 0x69, 0x63, 0x12, 0x21,
	0x0a, 0x0b, 0x41, 0x72, 0x74, 0x69, 0x73, 0x74, 0x50, 0x68, 0x6f, 0x74, 0x6f, 0x18, 0x18, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0c, 0x61, 0x72, 0x74, 0x69, 0x73, 0x74, 0x5f, 0x70, 0x68, 0x6f, 0x74,
	0x6f, 0x12, 0x1b, 0x0a, 0x08, 0x50, 0x68, 0x6f, 0x74, 0x6f, 0x50, 0x69, 0x63, 0x18, 0x19, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x09, 0x70, 0x68, 0x6f, 0x74, 0x6f, 0x5f, 0x70, 0x69, 0x63, 0x12, 0x15,
	0x0a, 0x05, 0x48, 0x64, 0x50, 0x69, 0x63, 0x18, 0x1a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x68,
	0x64, 0x5f, 0x70, 0x69, 0x63, 0x12, 0x1a, 0x0a, 0x08, 0x4d, 0x61, 0x74, 0x65, 0x72, 0x69, 0x61,
	0x6c, 0x18, 0x1b, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x6d, 0x61, 0x74, 0x65, 0x72, 0x69, 0x61,
	0x6c, 0x12, 0x21, 0x0a, 0x0b, 0x41, 0x72, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x55, 0x75, 0x69, 0x64,
	0x18, 0x1c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x61, 0x72, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x5f,
	0x75, 0x75, 0x69, 0x64, 0x12, 0x1f, 0x0a, 0x0a, 0x41, 0x72, 0x74, 0x69, 0x73, 0x74, 0x55, 0x75,
	0x69, 0x64, 0x18, 0x1d, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x61, 0x72, 0x74, 0x69, 0x73, 0x74,
	0x5f, 0x75, 0x75, 0x69, 0x64, 0x12, 0x21, 0x0a, 0x0b, 0x41, 0x72, 0x74, 0x77, 0x6f, 0x72, 0x6b,
	0x54, 0x79, 0x70, 0x65, 0x18, 0x1e, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0c, 0x61, 0x72, 0x74, 0x77,
	0x6f, 0x72, 0x6b, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x12, 0x19, 0x0a, 0x07, 0x41, 0x72, 0x74, 0x54,
	0x79, 0x70, 0x65, 0x18, 0x1f, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x61, 0x72, 0x74, 0x5f, 0x74,
	0x79, 0x70, 0x65, 0x12, 0x1b, 0x0a, 0x08, 0x41, 0x72, 0x74, 0x54, 0x69, 0x74, 0x6c, 0x65, 0x18,
	0x20, 0x20, 0x01, 0x28, 0x05, 0x52, 0x09, 0x61, 0x72, 0x74, 0x5f, 0x74, 0x69, 0x74, 0x6c, 0x65,
	0x12, 0x1b, 0x0a, 0x08, 0x41, 0x72, 0x74, 0x53, 0x74, 0x79, 0x6c, 0x65, 0x18, 0x21, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x09, 0x61, 0x72, 0x74, 0x5f, 0x73, 0x74, 0x79, 0x6c, 0x65, 0x12, 0x14, 0x0a,
	0x05, 0x43, 0x6f, 0x6c, 0x6f, 0x72, 0x18, 0x22, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x63, 0x6f,
	0x6c, 0x6f, 0x72, 0x12, 0x25, 0x0a, 0x0d, 0x50, 0x65, 0x6e, 0x54, 0x65, 0x63, 0x68, 0x6e, 0x69,
	0x71, 0x75, 0x65, 0x73, 0x18, 0x23, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x70, 0x65, 0x6e, 0x5f,
	0x74, 0x65, 0x63, 0x68, 0x6e, 0x69, 0x71, 0x75, 0x65, 0x73, 0x12, 0x19, 0x0a, 0x07, 0x41, 0x72,
	0x74, 0x49, 0x64, 0x65, 0x61, 0x18, 0x24, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x61, 0x72, 0x74,
	0x5f, 0x69, 0x64, 0x65, 0x61, 0x12, 0x21, 0x0a, 0x0b, 0x45, 0x78, 0x70, 0x72, 0x65, 0x73, 0x73,
	0x49, 0x64, 0x65, 0x61, 0x18, 0x25, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x65, 0x78, 0x70, 0x72,
	0x65, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x65, 0x61, 0x12, 0x1b, 0x0a, 0x08, 0x41, 0x72, 0x74, 0x53,
	0x74, 0x6f, 0x72, 0x79, 0x18, 0x26, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x61, 0x72, 0x74, 0x5f,
	0x73, 0x74, 0x6f, 0x72, 0x79, 0x12, 0x23, 0x0a, 0x0c, 0x46, 0x69, 0x72, 0x73, 0x74, 0x50, 0x75,
	0x62, 0x6c, 0x69, 0x73, 0x68, 0x18, 0x27, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x66, 0x69, 0x72,
	0x73, 0x74, 0x5f, 0x70, 0x75, 0x62, 0x6c, 0x69, 0x73, 0x68, 0x12, 0x2b, 0x0a, 0x10, 0x46, 0x69,
	0x72, 0x73, 0x74, 0x50, 0x75, 0x62, 0x6c, 0x69, 0x73, 0x68, 0x5f, 0x69, 0x6d, 0x67, 0x18, 0x28,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x11, 0x66, 0x69, 0x72, 0x73, 0x74, 0x5f, 0x70, 0x75, 0x62, 0x6c,
	0x69, 0x73, 0x68, 0x5f, 0x69, 0x6d, 0x67, 0x12, 0x1d, 0x0a, 0x09, 0x46, 0x69, 0x72, 0x73, 0x74,
	0x4e, 0x61, 0x6d, 0x65, 0x18, 0x29, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x66, 0x69, 0x72, 0x73,
	0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x25, 0x0a, 0x0d, 0x46, 0x69, 0x72, 0x73, 0x74, 0x4e,
	0x61, 0x6d, 0x65, 0x5f, 0x69, 0x6d, 0x67, 0x18, 0x2a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x66,
	0x69, 0x72, 0x73, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x5f, 0x69, 0x6d, 0x67, 0x12, 0x23, 0x0a,
	0x0c, 0x54, 0x68, 0x69, 0x72, 0x64, 0x43, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x18, 0x2b, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0d, 0x74, 0x68, 0x69, 0x72, 0x64, 0x5f, 0x63, 0x6f, 0x6d, 0x6d, 0x65,
	0x6e, 0x74, 0x12, 0x25, 0x0a, 0x0d, 0x53, 0x70, 0x72, 0x61, 0x79, 0x50, 0x6f, 0x73, 0x69, 0x74,
	0x69, 0x6f, 0x6e, 0x18, 0x2c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x73, 0x70, 0x72, 0x61, 0x79,
	0x5f, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x21, 0x0a, 0x0b, 0x53, 0x70, 0x72,
	0x61, 0x79, 0x52, 0x65, 0x6d, 0x61, 0x72, 0x6b, 0x18, 0x2d, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c,
	0x73, 0x70, 0x72, 0x61, 0x79, 0x5f, 0x72, 0x65, 0x6d, 0x61, 0x72, 0x6b, 0x12, 0x26, 0x0a, 0x0d,
	0x44, 0x69, 0x67, 0x69, 0x53, 0x68, 0x6f, 0x6f, 0x74, 0x44, 0x61, 0x74, 0x65, 0x18, 0x2e, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0f, 0x64, 0x69, 0x67, 0x69, 0x5f, 0x73, 0x68, 0x6f, 0x6f, 0x74, 0x5f,
	0x64, 0x61, 0x74, 0x65, 0x12, 0x24, 0x0a, 0x0c, 0x44, 0x69, 0x67, 0x69, 0x4d, 0x61, 0x6b, 0x65,
	0x44, 0x61, 0x74, 0x65, 0x18, 0x2f, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x64, 0x69, 0x67, 0x69,
	0x5f, 0x6d, 0x61, 0x6b, 0x65, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x12, 0x20, 0x0a, 0x0a, 0x44, 0x69,
	0x67, 0x69, 0x41, 0x72, 0x74, 0x49, 0x6d, 0x67, 0x18, 0x30, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c,
	0x64, 0x69, 0x67, 0x69, 0x5f, 0x61, 0x72, 0x74, 0x5f, 0x69, 0x6d, 0x67, 0x12, 0x33, 0x0a, 0x13,
	0x44, 0x69, 0x67, 0x69, 0x41, 0x72, 0x74, 0x43, 0x6f, 0x70, 0x79, 0x72, 0x69, 0x67, 0x68, 0x74,
	0x49, 0x6d, 0x67, 0x18, 0x31, 0x20, 0x01, 0x28, 0x09, 0x52, 0x16, 0x64, 0x69, 0x67, 0x69, 0x5f,
	0x61, 0x72, 0x74, 0x5f, 0x63, 0x6f, 0x70, 0x79, 0x72, 0x69, 0x67, 0x68, 0x74, 0x5f, 0x69, 0x6d,
	0x67, 0x12, 0x25, 0x0a, 0x0d, 0x43, 0x6f, 0x70, 0x79, 0x72, 0x69, 0x67, 0x68, 0x74, 0x48, 0x61,
	0x73, 0x68, 0x18, 0x32, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x63, 0x6f, 0x70, 0x79, 0x72, 0x69,
	0x67, 0x68, 0x74, 0x5f, 0x68, 0x61, 0x73, 0x68, 0x12, 0x25, 0x0a, 0x0d, 0x52, 0x65, 0x61, 0x6c,
	0x72, 0x69, 0x67, 0x68, 0x74, 0x48, 0x61, 0x73, 0x68, 0x18, 0x33, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0e, 0x72, 0x65, 0x61, 0x6c, 0x72, 0x69, 0x67, 0x68, 0x74, 0x5f, 0x68, 0x61, 0x73, 0x68, 0x12,
	0x24, 0x0a, 0x0c, 0x41, 0x75, 0x74, 0x68, 0x44, 0x61, 0x74, 0x61, 0x48, 0x61, 0x73, 0x68, 0x18,
	0x34, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x61, 0x75, 0x74, 0x68, 0x5f, 0x64, 0x61, 0x74, 0x61,
	0x5f, 0x68, 0x61, 0x73, 0x68, 0x12, 0x20, 0x0a, 0x0a, 0x57, 0x74, 0x52, 0x65, 0x61, 0x6c, 0x48,
	0x61, 0x73, 0x68, 0x18, 0x35, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x77, 0x74, 0x5f, 0x72, 0x65,
	0x61, 0x6c, 0x5f, 0x68, 0x61, 0x73, 0x68, 0x12, 0x20, 0x0a, 0x0a, 0x43, 0x78, 0x52, 0x65, 0x61,
	0x6c, 0x48, 0x61, 0x73, 0x68, 0x18, 0x36, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x63, 0x78, 0x5f,
	0x72, 0x65, 0x61, 0x6c, 0x5f, 0x68, 0x61, 0x73, 0x68, 0x12, 0x26, 0x0a, 0x0d, 0x42, 0x61, 0x69,
	0x64, 0x75, 0x52, 0x65, 0x61, 0x6c, 0x48, 0x61, 0x73, 0x68, 0x18, 0x37, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0f, 0x62, 0x61, 0x69, 0x64, 0x75, 0x5f, 0x72, 0x65, 0x61, 0x6c, 0x5f, 0x68, 0x61, 0x73,
	0x68, 0x12, 0x2e, 0x0a, 0x11, 0x44, 0x69, 0x67, 0x69, 0x43, 0x6f, 0x70, 0x79, 0x72, 0x69, 0x67,
	0x68, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x18, 0x38, 0x20, 0x01, 0x28, 0x09, 0x52, 0x13, 0x64, 0x69,
	0x67, 0x69, 0x5f, 0x63, 0x6f, 0x70, 0x79, 0x72, 0x69, 0x67, 0x68, 0x74, 0x5f, 0x69, 0x6e, 0x66,
	0x6f, 0x12, 0x2e, 0x0a, 0x11, 0x44, 0x69, 0x67, 0x69, 0x43, 0x6f, 0x70, 0x79, 0x72, 0x69, 0x67,
	0x68, 0x74, 0x46, 0x69, 0x6c, 0x65, 0x18, 0x39, 0x20, 0x01, 0x28, 0x09, 0x52, 0x13, 0x64, 0x69,
	0x67, 0x69, 0x5f, 0x63, 0x6f, 0x70, 0x79, 0x72, 0x69, 0x67, 0x68, 0x74, 0x5f, 0x66, 0x69, 0x6c,
	0x65, 0x12, 0x14, 0x0a, 0x05, 0x54, 0x66, 0x6e, 0x75, 0x6d, 0x18, 0x3a, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x05, 0x74, 0x66, 0x6e, 0x75, 0x6d, 0x12, 0x16, 0x0a, 0x06, 0x53, 0x65, 0x71, 0x6e, 0x75,
	0x6d, 0x18, 0x3b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x73, 0x65, 0x71, 0x6e, 0x75, 0x6d, 0x12,
	0x12, 0x0a, 0x04, 0x55, 0x75, 0x69, 0x64, 0x18, 0x3c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x75,
	0x75, 0x69, 0x64, 0x12, 0x25, 0x0a, 0x0d, 0x53, 0x74, 0x6f, 0x72, 0x61, 0x67, 0x65, 0x53, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x18, 0x3d, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0e, 0x73, 0x74, 0x6f, 0x72,
	0x61, 0x67, 0x65, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x1f, 0x0a, 0x0a, 0x53, 0x61,
	0x6c, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x3e, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0b,
	0x73, 0x61, 0x6c, 0x65, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x1b, 0x0a, 0x08, 0x50,
	0x72, 0x69, 0x63, 0x65, 0x52, 0x75, 0x6e, 0x18, 0x3f, 0x20, 0x01, 0x28, 0x02, 0x52, 0x09, 0x70,
	0x72, 0x69, 0x63, 0x65, 0x5f, 0x72, 0x75, 0x6e, 0x22, 0x28, 0x0a, 0x12, 0x54, 0x61, 0x67, 0x49,
	0x64, 0x4b, 0x76, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x12,
	0x0a, 0x04, 0x50, 0x69, 0x64, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x70, 0x69,
	0x64, 0x73, 0x22, 0x9c, 0x01, 0x0a, 0x13, 0x54, 0x61, 0x67, 0x49, 0x64, 0x4b, 0x76, 0x4c, 0x69,
	0x73, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x3a, 0x0a, 0x04, 0x49, 0x6e,
	0x66, 0x6f, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x26, 0x2e, 0x41, 0x72, 0x74, 0x77, 0x6f,
	0x72, 0x6b, 0x2e, 0x54, 0x61, 0x67, 0x49, 0x64, 0x4b, 0x76, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x49, 0x6e, 0x66, 0x6f, 0x45, 0x6e, 0x74, 0x72, 0x79,
	0x52, 0x04, 0x69, 0x6e, 0x66, 0x6f, 0x12, 0x10, 0x0a, 0x03, 0x4d, 0x73, 0x67, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x03, 0x6d, 0x73, 0x67, 0x1a, 0x37, 0x0a, 0x09, 0x49, 0x6e, 0x66, 0x6f,
	0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38,
	0x01, 0x22, 0x5c, 0x0a, 0x16, 0x45, 0x78, 0x70, 0x6f, 0x72, 0x74, 0x46, 0x69, 0x65, 0x6c, 0x64,
	0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x42, 0x0a, 0x0a, 0x45,
	0x78, 0x70, 0x6f, 0x72, 0x74, 0x54, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x42,
	0x21, 0xe2, 0xdf, 0x1f, 0x1d, 0x10, 0x00, 0x2a, 0x19, 0xe5, 0xaf, 0xbc, 0xe5, 0x87, 0xba, 0xe7,
	0xb1, 0xbb, 0xe5, 0x9e, 0x8b, 0xe5, 0xbf, 0x85, 0xe9, 0xa1, 0xbb, 0xe5, 0xa4, 0xa7, 0xe4, 0xba,
	0x8e, 0x30, 0x52, 0x0b, 0x65, 0x78, 0x70, 0x6f, 0x72, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x22,
	0x9f, 0x01, 0x0a, 0x17, 0x45, 0x78, 0x70, 0x6f, 0x72, 0x74, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x4c,
	0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x39, 0x0a, 0x04, 0x44,
	0x61, 0x74, 0x61, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x25, 0x2e, 0x41, 0x72, 0x74, 0x77,
	0x6f, 0x72, 0x6b, 0x2e, 0x45, 0x78, 0x70, 0x6f, 0x72, 0x74, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x4c,
	0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x49, 0x6e, 0x66, 0x6f,
	0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x12, 0x10, 0x0a, 0x03, 0x4d, 0x73, 0x67, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x03, 0x6d, 0x73, 0x67, 0x1a, 0x37, 0x0a, 0x04, 0x49, 0x6e, 0x66, 0x6f,
	0x12, 0x0e, 0x0a, 0x02, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x02, 0x69, 0x64,
	0x12, 0x1f, 0x0a, 0x0a, 0x43, 0x6f, 0x6c, 0x75, 0x6d, 0x6e, 0x44, 0x65, 0x73, 0x63, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x63, 0x6f, 0x6c, 0x75, 0x6d, 0x6e, 0x5f, 0x64, 0x65, 0x73,
	0x63, 0x22, 0x3a, 0x0a, 0x1a, 0x41, 0x72, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x44, 0x61, 0x74, 0x61,
	0x42, 0x79, 0x53, 0x68, 0x6f, 0x77, 0x49, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12,
	0x1c, 0x0a, 0x0e, 0x41, 0x72, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x53, 0x68, 0x6f, 0x77, 0x49, 0x64,
	0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x09, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x22, 0xcb, 0x01,
	0x0a, 0x1b, 0x41, 0x72, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x44, 0x61, 0x74, 0x61, 0x42, 0x79, 0x53,
	0x68, 0x6f, 0x77, 0x49, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x3d, 0x0a,
	0x04, 0x44, 0x61, 0x74, 0x61, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x29, 0x2e, 0x41, 0x72,
	0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2e, 0x41, 0x72, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x44, 0x61, 0x74,
	0x61, 0x42, 0x79, 0x53, 0x68, 0x6f, 0x77, 0x49, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x2e, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x04, 0x44, 0x61, 0x74, 0x61, 0x12, 0x10, 0x0a, 0x03,
	0x4d, 0x73, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6d, 0x73, 0x67, 0x1a, 0x5b,
	0x0a, 0x04, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x1a, 0x0a, 0x09, 0x41, 0x72, 0x74, 0x53, 0x68, 0x6f,
	0x77, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x73, 0x68, 0x6f, 0x77, 0x5f,
	0x69, 0x64, 0x12, 0x21, 0x0a, 0x0b, 0x41, 0x72, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x55, 0x75, 0x69,
	0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x61, 0x72, 0x74, 0x77, 0x6f, 0x72, 0x6b,
	0x5f, 0x75, 0x75, 0x69, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x54, 0x66, 0x6e, 0x75, 0x6d, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x74, 0x66, 0x6e, 0x75, 0x6d, 0x22, 0xe2, 0x01, 0x0a, 0x0b,
	0x4d, 0x79, 0x41, 0x77, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x12, 0x18, 0x0a, 0x07, 0x4b,
	0x65, 0x79, 0x77, 0x6f, 0x72, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6b, 0x65,
	0x79, 0x77, 0x6f, 0x72, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x50, 0x61, 0x67, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x04, 0x70, 0x61, 0x67, 0x65, 0x12, 0x1b, 0x0a, 0x08, 0x50, 0x61, 0x67,
	0x65, 0x53, 0x69, 0x7a, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x09, 0x70, 0x61, 0x67,
	0x65, 0x5f, 0x73, 0x69, 0x7a, 0x65, 0x12, 0x17, 0x0a, 0x06, 0x49, 0x73, 0x4f, 0x76, 0x65, 0x72,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x07, 0x69, 0x73, 0x5f, 0x6f, 0x76, 0x65, 0x72, 0x12,
	0x19, 0x0a, 0x07, 0x41, 0x64, 0x6d, 0x69, 0x6e, 0x49, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x08, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x5f, 0x69, 0x64, 0x12, 0x1f, 0x0a, 0x0a, 0x43, 0x68,
	0x61, 0x69, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0b,
	0x63, 0x68, 0x61, 0x69, 0x6e, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x65, 0x12, 0x1b, 0x0a, 0x08, 0x52,
	0x75, 0x6c, 0x65, 0x73, 0x55, 0x72, 0x6c, 0x18, 0x07, 0x20, 0x03, 0x28, 0x09, 0x52, 0x09, 0x72,
	0x75, 0x6c, 0x65, 0x73, 0x5f, 0x75, 0x72, 0x6c, 0x12, 0x16, 0x0a, 0x06, 0x53, 0x6f, 0x75, 0x72,
	0x63, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65,
	0x22, 0xb5, 0x08, 0x0a, 0x0c, 0x4d, 0x79, 0x41, 0x77, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73,
	0x70, 0x12, 0x2e, 0x0a, 0x04, 0x44, 0x61, 0x74, 0x61, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x1a, 0x2e, 0x41, 0x72, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2e, 0x4d, 0x79, 0x41, 0x77, 0x4c, 0x69,
	0x73, 0x74, 0x52, 0x65, 0x73, 0x70, 0x2e, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x04, 0x64, 0x61, 0x74,
	0x61, 0x12, 0x14, 0x0a, 0x05, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x05, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x19, 0x0a, 0x07, 0x69, 0x6e, 0x43, 0x6f, 0x75,
	0x6e, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x69, 0x6e, 0x5f, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x12, 0x1d, 0x0a, 0x09, 0x64, 0x6f, 0x6e, 0x65, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0a, 0x64, 0x6f, 0x6e, 0x65, 0x5f, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x12, 0x12, 0x0a, 0x04, 0x50, 0x61, 0x67, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x04, 0x70, 0x61, 0x67, 0x65, 0x12, 0x1b, 0x0a, 0x08, 0x50, 0x61, 0x67, 0x65, 0x53, 0x69, 0x7a,
	0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x05, 0x52, 0x09, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x73, 0x69,
	0x7a, 0x65, 0x12, 0x14, 0x0a, 0x03, 0x4d, 0x73, 0x67, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x1a, 0xdd, 0x06, 0x0a, 0x04, 0x49, 0x6e, 0x66,
	0x6f, 0x12, 0x0e, 0x0a, 0x02, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x02, 0x49,
	0x64, 0x12, 0x21, 0x0a, 0x0b, 0x41, 0x72, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x55, 0x75, 0x69, 0x64,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x61, 0x72, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x5f,
	0x75, 0x75, 0x69, 0x64, 0x12, 0x1f, 0x0a, 0x0a, 0x41, 0x72, 0x74, 0x69, 0x73, 0x74, 0x4e, 0x61,
	0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x61, 0x72, 0x74, 0x69, 0x73, 0x74,
	0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x21, 0x0a, 0x0b, 0x41, 0x72, 0x74, 0x77, 0x6f, 0x72, 0x6b,
	0x4e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x61, 0x72, 0x74, 0x77,
	0x6f, 0x72, 0x6b, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x4c, 0x65, 0x6e, 0x67,
	0x74, 0x68, 0x18, 0x05, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x6c, 0x65, 0x6e, 0x67, 0x74, 0x68,
	0x12, 0x14, 0x0a, 0x05, 0x57, 0x69, 0x64, 0x74, 0x68, 0x18, 0x06, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x05, 0x77, 0x69, 0x64, 0x74, 0x68, 0x12, 0x14, 0x0a, 0x05, 0x52, 0x75, 0x6c, 0x65, 0x72, 0x18,
	0x07, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x72, 0x75, 0x6c, 0x65, 0x72, 0x12, 0x15, 0x0a, 0x05,
	0x48, 0x64, 0x50, 0x69, 0x63, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x68, 0x64, 0x5f,
	0x70, 0x69, 0x63, 0x12, 0x18, 0x0a, 0x07, 0x57, 0x74, 0x53, 0x74, 0x61, 0x74, 0x65, 0x18, 0x0a,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x07, 0x77, 0x74, 0x73, 0x74, 0x61, 0x74, 0x65, 0x12, 0x28, 0x0a,
	0x0f, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x63, 0x68, 0x61, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x74, 0x65,
	0x18, 0x0b, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0f, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x63, 0x68, 0x61,
	0x69, 0x6e, 0x73, 0x74, 0x61, 0x74, 0x65, 0x12, 0x1e, 0x0a, 0x0a, 0x42, 0x61, 0x69, 0x64, 0x75,
	0x53, 0x74, 0x61, 0x74, 0x65, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0a, 0x62, 0x61, 0x69,
	0x64, 0x75, 0x73, 0x74, 0x61, 0x74, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x54, 0x66, 0x6e, 0x75, 0x6d,
	0x18, 0x0d, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x74, 0x66, 0x6e, 0x75, 0x6d, 0x12, 0x20, 0x0a,
	0x0a, 0x44, 0x69, 0x67, 0x69, 0x41, 0x72, 0x74, 0x49, 0x6d, 0x67, 0x18, 0x0e, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0c, 0x64, 0x69, 0x67, 0x69, 0x5f, 0x61, 0x72, 0x74, 0x5f, 0x69, 0x6d, 0x67, 0x12,
	0x1b, 0x0a, 0x08, 0x50, 0x68, 0x6f, 0x74, 0x6f, 0x50, 0x69, 0x63, 0x18, 0x0f, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x09, 0x70, 0x68, 0x6f, 0x74, 0x6f, 0x5f, 0x70, 0x69, 0x63, 0x12, 0x1b, 0x0a, 0x08,
	0x50, 0x72, 0x69, 0x63, 0x65, 0x52, 0x75, 0x6e, 0x18, 0x10, 0x20, 0x01, 0x28, 0x02, 0x52, 0x09,
	0x70, 0x72, 0x69, 0x63, 0x65, 0x5f, 0x72, 0x75, 0x6e, 0x12, 0x1e, 0x0a, 0x09, 0x42, 0x69, 0x74,
	0x4d, 0x61, 0x70, 0x4e, 0x75, 0x6d, 0x18, 0x11, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0b, 0x62, 0x69,
	0x74, 0x5f, 0x6d, 0x61, 0x70, 0x5f, 0x6e, 0x75, 0x6d, 0x12, 0x20, 0x0a, 0x0a, 0x42, 0x6d, 0x53,
	0x69, 0x78, 0x74, 0x79, 0x4e, 0x75, 0x6d, 0x18, 0x12, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0c, 0x62,
	0x6d, 0x5f, 0x73, 0x69, 0x78, 0x74, 0x79, 0x5f, 0x6e, 0x75, 0x6d, 0x12, 0x2a, 0x0a, 0x0f, 0x42,
	0x6d, 0x54, 0x77, 0x6f, 0x48, 0x75, 0x6e, 0x64, 0x72, 0x65, 0x64, 0x4e, 0x75, 0x6d, 0x18, 0x13,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x11, 0x62, 0x6d, 0x5f, 0x74, 0x77, 0x6f, 0x68, 0x6f, 0x75, 0x64,
	0x72, 0x65, 0x64, 0x5f, 0x6e, 0x75, 0x6d, 0x12, 0x2a, 0x0a, 0x0f, 0x42, 0x6d, 0x53, 0x69, 0x78,
	0x48, 0x75, 0x6e, 0x64, 0x72, 0x65, 0x64, 0x4e, 0x75, 0x6d, 0x18, 0x14, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x11, 0x62, 0x6d, 0x5f, 0x73, 0x69, 0x78, 0x68, 0x6f, 0x75, 0x64, 0x72, 0x65, 0x64, 0x5f,
	0x6e, 0x75, 0x6d, 0x12, 0x2e, 0x0a, 0x10, 0x42, 0x6d, 0x54, 0x77, 0x6f, 0x54, 0x68, 0x6f, 0x75,
	0x73, 0x61, 0x6e, 0x64, 0x4e, 0x75, 0x6d, 0x18, 0x15, 0x20, 0x01, 0x28, 0x05, 0x52, 0x14, 0x62,
	0x6d, 0x5f, 0x74, 0x77, 0x6f, 0x74, 0x68, 0x6f, 0x75, 0x6e, 0x64, 0x73, 0x61, 0x6e, 0x64, 0x5f,
	0x6e, 0x75, 0x6d, 0x12, 0x19, 0x0a, 0x07, 0x53, 0x69, 0x67, 0x6e, 0x70, 0x69, 0x63, 0x18, 0x16,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x73, 0x69, 0x67, 0x6e, 0x5f, 0x70, 0x69, 0x63, 0x12, 0x19,
	0x0a, 0x07, 0x53, 0x65, 0x61, 0x6c, 0x70, 0x69, 0x63, 0x18, 0x17, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x08, 0x73, 0x65, 0x61, 0x6c, 0x5f, 0x70, 0x69, 0x63, 0x12, 0x23, 0x0a, 0x0c, 0x53, 0x63, 0x68,
	0x65, 0x64, 0x75, 0x6c, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x18, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0d, 0x73, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x12, 0x14,
	0x0a, 0x05, 0x53, 0x74, 0x61, 0x74, 0x65, 0x18, 0x19, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x73,
	0x74, 0x61, 0x74, 0x65, 0x12, 0x1e, 0x0a, 0x0a, 0x41, 0x72, 0x74, 0x69, 0x73, 0x74, 0x55, 0x75,
	0x69, 0x64, 0x18, 0x1a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x41, 0x72, 0x74, 0x69, 0x73, 0x74,
	0x55, 0x75, 0x69, 0x64, 0x12, 0x28, 0x0a, 0x0f, 0x61, 0x72, 0x74, 0x69, 0x73, 0x74, 0x53, 0x68,
	0x6f, 0x77, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x1b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x61,
	0x72, 0x74, 0x69, 0x73, 0x74, 0x53, 0x68, 0x6f, 0x77, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x28,
	0x0a, 0x0f, 0x42, 0x6c, 0x61, 0x63, 0x6b, 0x4c, 0x69, 0x73, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x18, 0x1c, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0f, 0x42, 0x6c, 0x61, 0x63, 0x6b, 0x4c, 0x69,
	0x73, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x22, 0x50, 0x0a, 0x08, 0x50, 0x61, 0x67, 0x65,
	0x49, 0x6e, 0x66, 0x6f, 0x12, 0x12, 0x0a, 0x04, 0x70, 0x61, 0x67, 0x65, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x04, 0x70, 0x61, 0x67, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x61, 0x67, 0x65,
	0x53, 0x69, 0x7a, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x70, 0x61, 0x67, 0x65,
	0x53, 0x69, 0x7a, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x22, 0xe1, 0x01, 0x0a, 0x19, 0x41,
	0x72, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x50, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x4c, 0x69, 0x73,
	0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x12, 0x0a, 0x04, 0x70, 0x61, 0x67, 0x65,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x70, 0x61, 0x67, 0x65, 0x12, 0x1a, 0x0a, 0x08,
	0x70, 0x61, 0x67, 0x65, 0x53, 0x69, 0x7a, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08,
	0x70, 0x61, 0x67, 0x65, 0x53, 0x69, 0x7a, 0x65, 0x12, 0x1c, 0x0a, 0x09, 0x61, 0x72, 0x74, 0x69,
	0x73, 0x74, 0x55, 0x69, 0x64, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x61, 0x72, 0x74,
	0x69, 0x73, 0x74, 0x55, 0x69, 0x64, 0x12, 0x1c, 0x0a, 0x09, 0x69, 0x6e, 0x41, 0x72, 0x74, 0x53,
	0x68, 0x6f, 0x77, 0x18, 0x08, 0x20, 0x01, 0x28, 0x05, 0x52, 0x09, 0x69, 0x6e, 0x41, 0x72, 0x74,
	0x53, 0x68, 0x6f, 0x77, 0x12, 0x20, 0x0a, 0x0b, 0x61, 0x72, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x55,
	0x69, 0x64, 0x73, 0x18, 0x09, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0b, 0x61, 0x72, 0x74, 0x77, 0x6f,
	0x72, 0x6b, 0x55, 0x69, 0x64, 0x73, 0x12, 0x20, 0x0a, 0x0b, 0x61, 0x72, 0x74, 0x77, 0x6f, 0x72,
	0x6b, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x61, 0x72, 0x74,
	0x77, 0x6f, 0x72, 0x6b, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x66, 0x6e, 0x75,
	0x6d, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x74, 0x66, 0x6e, 0x75, 0x6d, 0x22, 0x78,
	0x0a, 0x1a, 0x41, 0x72, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x50, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77,
	0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x33, 0x0a, 0x04,
	0x64, 0x61, 0x74, 0x61, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x41, 0x72, 0x74,
	0x77, 0x6f, 0x72, 0x6b, 0x2e, 0x41, 0x72, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x50, 0x72, 0x65, 0x76,
	0x69, 0x65, 0x77, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x52, 0x04, 0x64, 0x61, 0x74,
	0x61, 0x12, 0x25, 0x0a, 0x04, 0x70, 0x61, 0x67, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x11, 0x2e, 0x41, 0x72, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2e, 0x50, 0x61, 0x67, 0x65, 0x49, 0x6e,
	0x66, 0x6f, 0x52, 0x04, 0x70, 0x61, 0x67, 0x65, 0x22, 0xce, 0x03, 0x0a, 0x16, 0x41, 0x72, 0x74,
	0x77, 0x6f, 0x72, 0x6b, 0x50, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x12, 0x1e, 0x0a, 0x0a, 0x61, 0x72, 0x74, 0x69, 0x73, 0x74, 0x55, 0x75, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x61, 0x72, 0x74, 0x69, 0x73, 0x74, 0x55,
	0x75, 0x69, 0x64, 0x12, 0x20, 0x0a, 0x0b, 0x61, 0x72, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x4e, 0x61,
	0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x61, 0x72, 0x74, 0x77, 0x6f, 0x72,
	0x6b, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x6c, 0x65, 0x6e, 0x67, 0x74, 0x68, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x6c, 0x65, 0x6e, 0x67, 0x74, 0x68, 0x12, 0x14, 0x0a,
	0x05, 0x77, 0x69, 0x64, 0x74, 0x68, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x77, 0x69,
	0x64, 0x74, 0x68, 0x12, 0x14, 0x0a, 0x05, 0x72, 0x75, 0x6c, 0x65, 0x72, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x05, 0x72, 0x75, 0x6c, 0x65, 0x72, 0x12, 0x26, 0x0a, 0x0e, 0x63, 0x72, 0x65,
	0x61, 0x74, 0x65, 0x64, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x18, 0x06, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0e, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73,
	0x73, 0x12, 0x20, 0x0a, 0x0b, 0x61, 0x72, 0x74, 0x69, 0x73, 0x74, 0x50, 0x68, 0x6f, 0x74, 0x6f,
	0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x61, 0x72, 0x74, 0x69, 0x73, 0x74, 0x50, 0x68,
	0x6f, 0x74, 0x6f, 0x12, 0x14, 0x0a, 0x05, 0x68, 0x64, 0x50, 0x69, 0x63, 0x18, 0x08, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x05, 0x68, 0x64, 0x50, 0x69, 0x63, 0x12, 0x1e, 0x0a, 0x0a, 0x61, 0x72, 0x74,
	0x77, 0x6f, 0x72, 0x6b, 0x55, 0x69, 0x64, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x61,
	0x72, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x55, 0x69, 0x64, 0x12, 0x1e, 0x0a, 0x0a, 0x63, 0x72, 0x65,
	0x61, 0x74, 0x65, 0x44, 0x61, 0x74, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x63,
	0x72, 0x65, 0x61, 0x74, 0x65, 0x44, 0x61, 0x74, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x69, 0x6e, 0x53,
	0x6f, 0x75, 0x72, 0x63, 0x65, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x69, 0x6e, 0x53,
	0x6f, 0x75, 0x72, 0x63, 0x65, 0x12, 0x1e, 0x0a, 0x0a, 0x64, 0x69, 0x67, 0x69, 0x41, 0x72, 0x74,
	0x49, 0x6d, 0x67, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x64, 0x69, 0x67, 0x69, 0x41,
	0x72, 0x74, 0x49, 0x6d, 0x67, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x68, 0x6f, 0x74, 0x6f, 0x50, 0x69,
	0x63, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x70, 0x68, 0x6f, 0x74, 0x6f, 0x50, 0x69,
	0x63, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x66, 0x6e, 0x75, 0x6d, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x05, 0x74, 0x66, 0x6e, 0x75, 0x6d, 0x12, 0x20, 0x0a, 0x0b, 0x61, 0x72, 0x74, 0x77, 0x6f,
	0x72, 0x6b, 0x54, 0x79, 0x70, 0x65, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0b, 0x61, 0x72,
	0x74, 0x77, 0x6f, 0x72, 0x6b, 0x54, 0x79, 0x70, 0x65, 0x22, 0x31, 0x0a, 0x0d, 0x56, 0x65, 0x72,
	0x69, 0x66, 0x79, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x12, 0x20, 0x0a, 0x0b, 0x41, 0x72,
	0x74, 0x77, 0x6f, 0x72, 0x6b, 0x55, 0x75, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0b, 0x41, 0x72, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x55, 0x75, 0x69, 0x64, 0x22, 0xf1, 0x04, 0x0a,
	0x0e, 0x56, 0x65, 0x72, 0x69, 0x66, 0x79, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x70, 0x12,
	0x35, 0x0a, 0x04, 0x44, 0x61, 0x74, 0x61, 0x18, 0x06, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x21, 0x2e,
	0x41, 0x72, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2e, 0x56, 0x65, 0x72, 0x69, 0x66, 0x79, 0x4c, 0x69,
	0x73, 0x74, 0x52, 0x65, 0x73, 0x70, 0x2e, 0x44, 0x61, 0x74, 0x61, 0x45, 0x6e, 0x74, 0x72, 0x79,
	0x52, 0x04, 0x44, 0x61, 0x74, 0x61, 0x12, 0x18, 0x0a, 0x07, 0x41, 0x75, 0x74, 0x68, 0x49, 0x6d,
	0x67, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x41, 0x75, 0x74, 0x68, 0x49, 0x6d, 0x67,
	0x12, 0x10, 0x0a, 0x03, 0x4d, 0x73, 0x67, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x4d,
	0x73, 0x67, 0x1a, 0xbd, 0x01, 0x0a, 0x07, 0x52, 0x61, 0x74, 0x65, 0x49, 0x6d, 0x67, 0x12, 0x38,
	0x0a, 0x04, 0x44, 0x61, 0x74, 0x61, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x24, 0x2e, 0x41,
	0x72, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2e, 0x56, 0x65, 0x72, 0x69, 0x66, 0x79, 0x4c, 0x69, 0x73,
	0x74, 0x52, 0x65, 0x73, 0x70, 0x2e, 0x52, 0x61, 0x74, 0x65, 0x49, 0x6d, 0x67, 0x2e, 0x49, 0x6e,
	0x66, 0x6f, 0x52, 0x04, 0x44, 0x61, 0x74, 0x61, 0x1a, 0x78, 0x0a, 0x04, 0x49, 0x6e, 0x66, 0x6f,
	0x12, 0x18, 0x0a, 0x07, 0x52, 0x61, 0x74, 0x65, 0x55, 0x72, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x07, 0x52, 0x61, 0x74, 0x65, 0x55, 0x72, 0x6c, 0x12, 0x20, 0x0a, 0x0b, 0x52, 0x61,
	0x74, 0x65, 0x53, 0x69, 0x6d, 0x69, 0x6c, 0x61, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x02, 0x52,
	0x0b, 0x52, 0x61, 0x74, 0x65, 0x53, 0x69, 0x6d, 0x69, 0x6c, 0x61, 0x72, 0x12, 0x12, 0x0a, 0x04,
	0x55, 0x75, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x55, 0x75, 0x69, 0x64,
	0x12, 0x20, 0x0a, 0x0b, 0x52, 0x61, 0x74, 0x65, 0x49, 0x6d, 0x67, 0x54, 0x69, 0x6d, 0x65, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x52, 0x61, 0x74, 0x65, 0x49, 0x6d, 0x67, 0x54, 0x69,
	0x6d, 0x65, 0x1a, 0xe4, 0x01, 0x0a, 0x04, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x20, 0x0a, 0x0b, 0x42,
	0x69, 0x74, 0x4d, 0x61, 0x70, 0x49, 0x6e, 0x64, 0x65, 0x78, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x0b, 0x42, 0x69, 0x74, 0x4d, 0x61, 0x70, 0x49, 0x6e, 0x64, 0x65, 0x78, 0x12, 0x1c, 0x0a,
	0x09, 0x42, 0x69, 0x74, 0x6d, 0x61, 0x70, 0x49, 0x6d, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x09, 0x42, 0x69, 0x74, 0x6d, 0x61, 0x70, 0x49, 0x6d, 0x67, 0x12, 0x40, 0x0a, 0x06, 0x42,
	0x6d, 0x49, 0x6d, 0x67, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x28, 0x2e, 0x41, 0x72,
	0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2e, 0x56, 0x65, 0x72, 0x69, 0x66, 0x79, 0x4c, 0x69, 0x73, 0x74,
	0x52, 0x65, 0x73, 0x70, 0x2e, 0x49, 0x6e, 0x66, 0x6f, 0x2e, 0x42, 0x6d, 0x49, 0x6d, 0x67, 0x73,
	0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x06, 0x42, 0x6d, 0x49, 0x6d, 0x67, 0x73, 0x1a, 0x5a, 0x0a,
	0x0b, 0x42, 0x6d, 0x49, 0x6d, 0x67, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03,
	0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x35,
	0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e,
	0x41, 0x72, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2e, 0x56, 0x65, 0x72, 0x69, 0x66, 0x79, 0x4c, 0x69,
	0x73, 0x74, 0x52, 0x65, 0x73, 0x70, 0x2e, 0x52, 0x61, 0x74, 0x65, 0x49, 0x6d, 0x67, 0x52, 0x05,
	0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x1a, 0x55, 0x0a, 0x09, 0x44, 0x61, 0x74,
	0x61, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x32, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x41, 0x72, 0x74, 0x77, 0x6f, 0x72,
	0x6b, 0x2e, 0x56, 0x65, 0x72, 0x69, 0x66, 0x79, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x70,
	0x2e, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01,
	0x22, 0x5b, 0x0a, 0x0e, 0x41, 0x72, 0x74, 0x73, 0x68, 0x6f, 0x77, 0x4c, 0x69, 0x73, 0x74, 0x52,
	0x65, 0x71, 0x12, 0x18, 0x0a, 0x07, 0x4b, 0x65, 0x79, 0x77, 0x6f, 0x72, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x07, 0x4b, 0x65, 0x79, 0x77, 0x6f, 0x72, 0x64, 0x12, 0x12, 0x0a, 0x04,
	0x50, 0x61, 0x67, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x70, 0x61, 0x67, 0x65,
	0x12, 0x1b, 0x0a, 0x08, 0x50, 0x61, 0x67, 0x65, 0x53, 0x69, 0x7a, 0x65, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x09, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x73, 0x69, 0x7a, 0x65, 0x22, 0x98, 0x04,
	0x0a, 0x0f, 0x41, 0x72, 0x74, 0x73, 0x68, 0x6f, 0x77, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73,
	0x70, 0x12, 0x31, 0x0a, 0x04, 0x44, 0x61, 0x74, 0x61, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x1d, 0x2e, 0x41, 0x72, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2e, 0x41, 0x72, 0x74, 0x73, 0x68, 0x6f,
	0x77, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x70, 0x2e, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x04,
	0x64, 0x61, 0x74, 0x61, 0x12, 0x14, 0x0a, 0x05, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x05, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x12, 0x0a, 0x04, 0x50, 0x61,
	0x67, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x70, 0x61, 0x67, 0x65, 0x12, 0x1b,
	0x0a, 0x08, 0x50, 0x61, 0x67, 0x65, 0x53, 0x69, 0x7a, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x09, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x73, 0x69, 0x7a, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x4d,
	0x73, 0x67, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x4d, 0x73, 0x67, 0x1a, 0xf8, 0x02,
	0x0a, 0x04, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x1e, 0x0a, 0x0a, 0x41, 0x72, 0x74, 0x69, 0x73, 0x74,
	0x4e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x41, 0x72, 0x74, 0x69,
	0x73, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x53, 0x65, 0x78, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x03, 0x53, 0x65, 0x78, 0x12, 0x10, 0x0a, 0x03, 0x41, 0x67, 0x65, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x03, 0x41, 0x67, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x50, 0x68,
	0x6f, 0x6e, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x50, 0x68, 0x6f, 0x6e, 0x65,
	0x12, 0x26, 0x0a, 0x0e, 0x41, 0x72, 0x74, 0x73, 0x68, 0x6f, 0x77, 0x45, 0x78, 0x74, 0x54, 0x69,
	0x6d, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x41, 0x72, 0x74, 0x73, 0x68, 0x6f,
	0x77, 0x45, 0x78, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x24, 0x0a, 0x0d, 0x52, 0x65, 0x63, 0x6f,
	0x6d, 0x6d, 0x65, 0x6e, 0x64, 0x55, 0x73, 0x65, 0x72, 0x18, 0x08, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x0d, 0x52, 0x65, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x64, 0x55, 0x73, 0x65, 0x72, 0x12, 0x1c,
	0x0a, 0x09, 0x41, 0x72, 0x74, 0x73, 0x68, 0x6f, 0x77, 0x49, 0x64, 0x18, 0x09, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x09, 0x41, 0x72, 0x74, 0x73, 0x68, 0x6f, 0x77, 0x49, 0x64, 0x12, 0x1e, 0x0a, 0x0a,
	0x41, 0x72, 0x74, 0x69, 0x73, 0x74, 0x55, 0x75, 0x69, 0x64, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0a, 0x41, 0x72, 0x74, 0x69, 0x73, 0x74, 0x55, 0x75, 0x69, 0x64, 0x12, 0x48, 0x0a, 0x0a,
	0x49, 0x6e, 0x76, 0x69, 0x74, 0x65, 0x44, 0x61, 0x74, 0x61, 0x18, 0x0b, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x28, 0x2e, 0x41, 0x72, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2e, 0x41, 0x72, 0x74, 0x73, 0x68,
	0x6f, 0x77, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x70, 0x2e, 0x49, 0x6e, 0x66, 0x6f, 0x2e,
	0x49, 0x6e, 0x76, 0x69, 0x74, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x0a, 0x49, 0x6e, 0x76, 0x69,
	0x74, 0x65, 0x44, 0x61, 0x74, 0x61, 0x1a, 0x40, 0x0a, 0x0a, 0x49, 0x6e, 0x76, 0x69, 0x74, 0x65,
	0x49, 0x6e, 0x66, 0x6f, 0x12, 0x16, 0x0a, 0x06, 0x55, 0x73, 0x65, 0x72, 0x49, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x55, 0x73, 0x65, 0x72, 0x49, 0x64, 0x12, 0x1a, 0x0a, 0x08,
	0x52, 0x65, 0x61, 0x6c, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08,
	0x52, 0x65, 0x61, 0x6c, 0x6e, 0x61, 0x6d, 0x65, 0x22, 0x5d, 0x0a, 0x15, 0x43, 0x6f, 0x75, 0x6e,
	0x74, 0x56, 0x65, 0x72, 0x69, 0x66, 0x79, 0x53, 0x69, 0x6d, 0x69, 0x6c, 0x61, 0x72, 0x52, 0x65,
	0x71, 0x12, 0x22, 0x0a, 0x0c, 0x41, 0x72, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x55, 0x75, 0x69, 0x64,
	0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0c, 0x41, 0x72, 0x74, 0x77, 0x6f, 0x72, 0x6b,
	0x55, 0x75, 0x69, 0x64, 0x73, 0x12, 0x20, 0x0a, 0x0b, 0x42, 0x69, 0x74, 0x4d, 0x61, 0x70, 0x49,
	0x6e, 0x64, 0x65, 0x78, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0b, 0x42, 0x69, 0x74, 0x4d,
	0x61, 0x70, 0x49, 0x6e, 0x64, 0x65, 0x78, 0x22, 0x2a, 0x0a, 0x16, 0x43, 0x6f, 0x75, 0x6e, 0x74,
	0x56, 0x65, 0x72, 0x69, 0x66, 0x79, 0x53, 0x69, 0x6d, 0x69, 0x6c, 0x61, 0x72, 0x52, 0x65, 0x73,
	0x70, 0x12, 0x10, 0x0a, 0x03, 0x4d, 0x73, 0x67, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03,
	0x4d, 0x73, 0x67, 0x22, 0x80, 0x02, 0x0a, 0x0b, 0x4f, 0x6e, 0x65, 0x51, 0x75, 0x65, 0x72, 0x79,
	0x52, 0x65, 0x71, 0x12, 0x18, 0x0a, 0x07, 0x4b, 0x65, 0x79, 0x77, 0x6f, 0x72, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x4b, 0x65, 0x79, 0x77, 0x6f, 0x72, 0x64, 0x12, 0x12, 0x0a,
	0x04, 0x50, 0x61, 0x67, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x70, 0x61, 0x67,
	0x65, 0x12, 0x1b, 0x0a, 0x08, 0x50, 0x61, 0x67, 0x65, 0x53, 0x69, 0x7a, 0x65, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x09, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x73, 0x69, 0x7a, 0x65, 0x12, 0x1e,
	0x0a, 0x0a, 0x50, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0a, 0x70, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x30,
	0x0a, 0x12, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x41, 0x72, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x55,
	0x75, 0x69, 0x64, 0x73, 0x18, 0x05, 0x20, 0x03, 0x28, 0x09, 0x52, 0x14, 0x73, 0x65, 0x61, 0x72,
	0x63, 0x68, 0x5f, 0x61, 0x72, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x5f, 0x75, 0x75, 0x69, 0x64, 0x73,
	0x12, 0x21, 0x0a, 0x0b, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18,
	0x06, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0c, 0x63, 0x68, 0x65, 0x63, 0x6b, 0x5f, 0x73, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x12, 0x31, 0x0a, 0x12, 0x42, 0x6c, 0x61, 0x63, 0x6b, 0x4c, 0x69, 0x73, 0x74,
	0x41, 0x72, 0x74, 0x69, 0x73, 0x74, 0x55, 0x69, 0x64, 0x18, 0x07, 0x20, 0x03, 0x28, 0x09, 0x52,
	0x15, 0x62, 0x6c, 0x61, 0x63, 0x6b, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x5f, 0x61, 0x72, 0x74, 0x69,
	0x73, 0x74, 0x5f, 0x75, 0x69, 0x64, 0x22, 0xb0, 0x04, 0x0a, 0x0c, 0x4f, 0x6e, 0x65, 0x51, 0x75,
	0x65, 0x72, 0x79, 0x52, 0x65, 0x73, 0x70, 0x12, 0x2e, 0x0a, 0x04, 0x44, 0x61, 0x74, 0x61, 0x18,
	0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x41, 0x72, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2e,
	0x4f, 0x6e, 0x65, 0x51, 0x75, 0x65, 0x72, 0x79, 0x52, 0x65, 0x73, 0x70, 0x2e, 0x49, 0x6e, 0x66,
	0x6f, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x12, 0x14, 0x0a, 0x05, 0x43, 0x6f, 0x75, 0x6e, 0x74,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x12, 0x0a,
	0x04, 0x50, 0x61, 0x67, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x70, 0x61, 0x67,
	0x65, 0x12, 0x1b, 0x0a, 0x08, 0x50, 0x61, 0x67, 0x65, 0x53, 0x69, 0x7a, 0x65, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x09, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x73, 0x69, 0x7a, 0x65, 0x12, 0x10,
	0x0a, 0x03, 0x4d, 0x73, 0x67, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x4d, 0x73, 0x67,
	0x1a, 0x96, 0x03, 0x0a, 0x04, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x20, 0x0a, 0x0b, 0x41, 0x72, 0x74,
	0x77, 0x6f, 0x72, 0x6b, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b,
	0x41, 0x72, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1e, 0x0a, 0x0a, 0x41,
	0x72, 0x74, 0x69, 0x73, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0a, 0x41, 0x72, 0x74, 0x69, 0x73, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x4c,
	0x65, 0x6e, 0x67, 0x74, 0x68, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x4c, 0x65, 0x6e,
	0x67, 0x74, 0x68, 0x12, 0x14, 0x0a, 0x05, 0x57, 0x69, 0x64, 0x74, 0x68, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x05, 0x57, 0x69, 0x64, 0x74, 0x68, 0x12, 0x14, 0x0a, 0x05, 0x52, 0x75, 0x6c,
	0x65, 0x72, 0x18, 0x05, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x52, 0x75, 0x6c, 0x65, 0x72, 0x12,
	0x1a, 0x0a, 0x08, 0x41, 0x62, 0x73, 0x74, 0x72, 0x61, 0x63, 0x74, 0x18, 0x06, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x08, 0x41, 0x62, 0x73, 0x74, 0x72, 0x61, 0x63, 0x74, 0x12, 0x18, 0x0a, 0x07, 0x43,
	0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x43, 0x6f,
	0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x12, 0x20, 0x0a, 0x0b, 0x41, 0x72, 0x74, 0x77, 0x6f, 0x72, 0x6b,
	0x55, 0x75, 0x69, 0x64, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x41, 0x72, 0x74, 0x77,
	0x6f, 0x72, 0x6b, 0x55, 0x75, 0x69, 0x64, 0x12, 0x1e, 0x0a, 0x0a, 0x44, 0x69, 0x67, 0x69, 0x41,
	0x72, 0x74, 0x49, 0x6d, 0x67, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x44, 0x69, 0x67,
	0x69, 0x41, 0x72, 0x74, 0x49, 0x6d, 0x67, 0x12, 0x24, 0x0a, 0x0d, 0x45, 0x64, 0x75, 0x45, 0x76,
	0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d,
	0x45, 0x64, 0x75, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x20, 0x0a,
	0x0b, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x0b, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x0b, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12,
	0x28, 0x0a, 0x0f, 0x61, 0x72, 0x74, 0x69, 0x73, 0x74, 0x53, 0x68, 0x6f, 0x77, 0x43, 0x6f, 0x75,
	0x6e, 0x74, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x61, 0x72, 0x74, 0x69, 0x73, 0x74,
	0x53, 0x68, 0x6f, 0x77, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x1e, 0x0a, 0x0a, 0x61, 0x72, 0x74,
	0x69, 0x73, 0x74, 0x55, 0x75, 0x69, 0x64, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x61,
	0x72, 0x74, 0x69, 0x73, 0x74, 0x55, 0x75, 0x69, 0x64, 0x22, 0x32, 0x0a, 0x10, 0x47, 0x65, 0x74,
	0x50, 0x61, 0x73, 0x73, 0x41, 0x72, 0x74, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x12, 0x1e, 0x0a,
	0x0a, 0x41, 0x72, 0x74, 0x69, 0x73, 0x74, 0x55, 0x75, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0a, 0x41, 0x72, 0x74, 0x69, 0x73, 0x74, 0x55, 0x75, 0x69, 0x64, 0x22, 0x47, 0x0a,
	0x11, 0x47, 0x65, 0x74, 0x50, 0x61, 0x73, 0x73, 0x41, 0x72, 0x74, 0x69, 0x73, 0x74, 0x52, 0x65,
	0x73, 0x70, 0x12, 0x20, 0x0a, 0x0b, 0x41, 0x72, 0x74, 0x69, 0x73, 0x74, 0x55, 0x75, 0x69, 0x64,
	0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0b, 0x41, 0x72, 0x74, 0x69, 0x73, 0x74, 0x55,
	0x75, 0x69, 0x64, 0x73, 0x12, 0x10, 0x0a, 0x03, 0x4d, 0x73, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x03, 0x4d, 0x73, 0x67, 0x22, 0x4b, 0x0a, 0x0f, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72,
	0x41, 0x77, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x12, 0x38, 0x0a, 0x0a, 0x41, 0x72, 0x74,
	0x69, 0x73, 0x74, 0x55, 0x75, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x17, 0xe2,
	0xdf, 0x1f, 0x13, 0x2a, 0x0f, 0xe8, 0xaf, 0xb7, 0xe9, 0x80, 0x89, 0xe6, 0x8b, 0xa9, 0xe7, 0x94,
	0xbb, 0xe5, 0xae, 0xb6, 0x58, 0x01, 0x52, 0x0b, 0x61, 0x72, 0x74, 0x69, 0x73, 0x74, 0x5f, 0x75,
	0x75, 0x69, 0x64, 0x22, 0xe3, 0x02, 0x0a, 0x10, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x41, 0x77,
	0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x70, 0x12, 0x32, 0x0a, 0x04, 0x44, 0x61, 0x74, 0x61,
	0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x41, 0x72, 0x74, 0x77, 0x6f, 0x72, 0x6b,
	0x2e, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x41, 0x77, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73,
	0x70, 0x2e, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x04, 0x44, 0x61, 0x74, 0x61, 0x12, 0x10, 0x0a, 0x03,
	0x4d, 0x73, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x4d, 0x73, 0x67, 0x1a, 0x88,
	0x02, 0x0a, 0x04, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x20, 0x0a, 0x0b, 0x41, 0x72, 0x74, 0x77, 0x6f,
	0x72, 0x6b, 0x55, 0x75, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x41, 0x72,
	0x74, 0x77, 0x6f, 0x72, 0x6b, 0x55, 0x75, 0x69, 0x64, 0x12, 0x20, 0x0a, 0x0b, 0x41, 0x72, 0x74,
	0x77, 0x6f, 0x72, 0x6b, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b,
	0x41, 0x72, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x48,
	0x64, 0x50, 0x69, 0x63, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x48, 0x64, 0x50, 0x69,
	0x63, 0x12, 0x1e, 0x0a, 0x0a, 0x41, 0x72, 0x74, 0x69, 0x73, 0x74, 0x55, 0x75, 0x69, 0x64, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x41, 0x72, 0x74, 0x69, 0x73, 0x74, 0x55, 0x75, 0x69,
	0x64, 0x12, 0x16, 0x0a, 0x06, 0x4c, 0x65, 0x6e, 0x67, 0x74, 0x68, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x06, 0x4c, 0x65, 0x6e, 0x67, 0x74, 0x68, 0x12, 0x14, 0x0a, 0x05, 0x57, 0x69, 0x64,
	0x74, 0x68, 0x18, 0x06, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x57, 0x69, 0x64, 0x74, 0x68, 0x12,
	0x14, 0x0a, 0x05, 0x52, 0x75, 0x6c, 0x65, 0x72, 0x18, 0x07, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05,
	0x52, 0x75, 0x6c, 0x65, 0x72, 0x12, 0x22, 0x0a, 0x0c, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x53,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x08, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0c, 0x46, 0x69, 0x6c,
	0x74, 0x65, 0x72, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x1e, 0x0a, 0x0a, 0x44, 0x69, 0x67,
	0x69, 0x41, 0x72, 0x74, 0x49, 0x6d, 0x67, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x44,
	0x69, 0x67, 0x69, 0x41, 0x72, 0x74, 0x49, 0x6d, 0x67, 0x22, 0xaa, 0x01, 0x0a, 0x12, 0x43, 0x68,
	0x61, 0x69, 0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x42, 0x79, 0x48, 0x61, 0x73, 0x68, 0x52, 0x65, 0x71,
	0x12, 0x2c, 0x0a, 0x11, 0x57, 0x74, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x48, 0x61, 0x73, 0x68, 0x18, 0x01, 0x20, 0x03, 0x28, 0x09, 0x52, 0x11, 0x57, 0x74, 0x54,
	0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x48, 0x61, 0x73, 0x68, 0x12, 0x32,
	0x0a, 0x14, 0x42, 0x61, 0x69, 0x64, 0x75, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69,
	0x6f, 0x6e, 0x48, 0x61, 0x73, 0x68, 0x18, 0x02, 0x20, 0x03, 0x28, 0x09, 0x52, 0x14, 0x42, 0x61,
	0x69, 0x64, 0x75, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x48, 0x61,
	0x73, 0x68, 0x12, 0x32, 0x0a, 0x14, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x74, 0x72, 0x61, 0x6e, 0x73,
	0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x68, 0x61, 0x73, 0x68, 0x18, 0x03, 0x20, 0x03, 0x28, 0x09,
	0x52, 0x14, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69,
	0x6f, 0x6e, 0x68, 0x61, 0x73, 0x68, 0x22, 0xf1, 0x03, 0x0a, 0x13, 0x43, 0x68, 0x61, 0x69, 0x6e,
	0x49, 0x6e, 0x66, 0x6f, 0x42, 0x79, 0x48, 0x61, 0x73, 0x68, 0x52, 0x65, 0x73, 0x70, 0x12, 0x35,
	0x0a, 0x04, 0x44, 0x61, 0x74, 0x61, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x41,
	0x72, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2e, 0x43, 0x68, 0x61, 0x69, 0x6e, 0x49, 0x6e, 0x66, 0x6f,
	0x42, 0x79, 0x48, 0x61, 0x73, 0x68, 0x52, 0x65, 0x73, 0x70, 0x2e, 0x49, 0x6e, 0x66, 0x6f, 0x52,
	0x04, 0x44, 0x61, 0x74, 0x61, 0x1a, 0xa2, 0x03, 0x0a, 0x04, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x2e,
	0x0a, 0x12, 0x42, 0x61, 0x73, 0x69, 0x63, 0x69, 0x6e, 0x66, 0x6f, 0x6f, 0x66, 0x61, 0x72, 0x74,
	0x77, 0x6f, 0x72, 0x6b, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x12, 0x42, 0x61, 0x73, 0x69,
	0x63, 0x69, 0x6e, 0x66, 0x6f, 0x6f, 0x66, 0x61, 0x72, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x12, 0x1e,
	0x0a, 0x0a, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x49, 0x64, 0x31, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0a, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x49, 0x64, 0x31, 0x12, 0x20,
	0x0a, 0x0b, 0x41, 0x72, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x55, 0x75, 0x69, 0x64, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0b, 0x41, 0x72, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x55, 0x75, 0x69, 0x64,
	0x12, 0x14, 0x0a, 0x05, 0x54, 0x66, 0x6e, 0x75, 0x6d, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x05, 0x54, 0x66, 0x6e, 0x75, 0x6d, 0x12, 0x18, 0x0a, 0x07, 0x41, 0x75, 0x74, 0x68, 0x49, 0x6d,
	0x67, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x41, 0x75, 0x74, 0x68, 0x49, 0x6d, 0x67,
	0x12, 0x2a, 0x0a, 0x10, 0x43, 0x65, 0x72, 0x74, 0x74, 0x6d, 0x70, 0x69, 0x63, 0x6f, 0x73, 0x73,
	0x61, 0x64, 0x64, 0x72, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x43, 0x65, 0x72, 0x74,
	0x74, 0x6d, 0x70, 0x69, 0x63, 0x6f, 0x73, 0x73, 0x61, 0x64, 0x64, 0x72, 0x12, 0x1e, 0x0a, 0x0a,
	0x57, 0x69, 0x6e, 0x64, 0x75, 0x70, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0a, 0x57, 0x69, 0x6e, 0x64, 0x75, 0x70, 0x74, 0x69, 0x6d, 0x65, 0x12, 0x2c, 0x0a, 0x11,
	0x57, 0x74, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x48, 0x61, 0x73,
	0x68, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x11, 0x57, 0x74, 0x54, 0x72, 0x61, 0x6e, 0x73,
	0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x48, 0x61, 0x73, 0x68, 0x12, 0x28, 0x0a, 0x0f, 0x54, 0x72,
	0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x68, 0x61, 0x73, 0x68, 0x18, 0x09, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0f, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e,
	0x68, 0x61, 0x73, 0x68, 0x12, 0x32, 0x0a, 0x14, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x74, 0x72, 0x61,
	0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x68, 0x61, 0x73, 0x68, 0x18, 0x0a, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x14, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63,
	0x74, 0x69, 0x6f, 0x6e, 0x68, 0x61, 0x73, 0x68, 0x12, 0x20, 0x0a, 0x0b, 0x42, 0x61, 0x69, 0x64,
	0x75, 0x48, 0x65, 0x69, 0x67, 0x68, 0x74, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0b, 0x42,
	0x61, 0x69, 0x64, 0x75, 0x48, 0x65, 0x69, 0x67, 0x68, 0x74, 0x22, 0x37, 0x0a, 0x11, 0x53, 0x74,
	0x6f, 0x63, 0x6b, 0x4f, 0x75, 0x74, 0x41, 0x72, 0x74, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x12,
	0x22, 0x0a, 0x0c, 0x53, 0x74, 0x6f, 0x63, 0x6b, 0x4f, 0x75, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x53, 0x74, 0x6f, 0x63, 0x6b, 0x4f, 0x75, 0x74, 0x54,
	0x69, 0x6d, 0x65, 0x22, 0x36, 0x0a, 0x12, 0x53, 0x74, 0x6f, 0x63, 0x6b, 0x4f, 0x75, 0x74, 0x41,
	0x72, 0x74, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x70, 0x12, 0x20, 0x0a, 0x0b, 0x41, 0x72, 0x74,
	0x69, 0x73, 0x74, 0x55, 0x75, 0x69, 0x64, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0b,
	0x41, 0x72, 0x74, 0x69, 0x73, 0x74, 0x55, 0x75, 0x69, 0x64, 0x73, 0x22, 0x72, 0x0a, 0x0c, 0x43,
	0x68, 0x61, 0x69, 0x6e, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x12, 0x1c, 0x0a, 0x09, 0x43,
	0x68, 0x61, 0x69, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09,
	0x43, 0x68, 0x61, 0x69, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x50, 0x61, 0x67,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x50, 0x61, 0x67, 0x65, 0x12, 0x1a, 0x0a,
	0x08, 0x50, 0x61, 0x67, 0x65, 0x53, 0x69, 0x7a, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x08, 0x50, 0x61, 0x67, 0x65, 0x53, 0x69, 0x7a, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x54, 0x66, 0x6e,
	0x75, 0x6d, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x54, 0x66, 0x6e, 0x75, 0x6d, 0x22,
	0x92, 0x01, 0x0a, 0x0d, 0x43, 0x68, 0x61, 0x69, 0x6e, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73,
	0x70, 0x12, 0x2f, 0x0a, 0x04, 0x44, 0x61, 0x74, 0x61, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x1b, 0x2e, 0x41, 0x72, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2e, 0x43, 0x68, 0x61, 0x69, 0x6e, 0x4c,
	0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x70, 0x2e, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x04, 0x44, 0x61,
	0x74, 0x61, 0x12, 0x14, 0x0a, 0x05, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x05, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x1a, 0x3a, 0x0a, 0x04, 0x49, 0x6e, 0x66, 0x6f,
	0x12, 0x12, 0x0a, 0x04, 0x48, 0x61, 0x73, 0x68, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04,
	0x48, 0x61, 0x73, 0x68, 0x12, 0x1e, 0x0a, 0x0a, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x44, 0x61,
	0x74, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65,
	0x44, 0x61, 0x74, 0x65, 0x22, 0x6b, 0x0a, 0x11, 0x54, 0x6f, 0x74, 0x61, 0x6c, 0x54, 0x72, 0x61,
	0x6e, 0x73, 0x66, 0x65, 0x72, 0x52, 0x65, 0x73, 0x70, 0x12, 0x1a, 0x0a, 0x08, 0x54, 0x6f, 0x74,
	0x61, 0x6c, 0x4e, 0x75, 0x6d, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x54, 0x6f, 0x74,
	0x61, 0x6c, 0x4e, 0x75, 0x6d, 0x12, 0x3a, 0x0a, 0x18, 0x54, 0x6f, 0x74, 0x61, 0x6c, 0x4f, 0x66,
	0x66, 0x6c, 0x69, 0x6e, 0x65, 0x43, 0x6f, 0x70, 0x79, 0x72, 0x69, 0x67, 0x68, 0x74, 0x4e, 0x75,
	0x6d, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x18, 0x54, 0x6f, 0x74, 0x61, 0x6c, 0x4f, 0x66,
	0x66, 0x6c, 0x69, 0x6e, 0x65, 0x43, 0x6f, 0x70, 0x79, 0x72, 0x69, 0x67, 0x68, 0x74, 0x4e, 0x75,
	0x6d, 0x22, 0x8e, 0x0f, 0x0a, 0x12, 0x41, 0x72, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x50, 0x72, 0x6f,
	0x66, 0x69, 0x6c, 0x65, 0x44, 0x61, 0x74, 0x61, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x02, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x75, 0x75, 0x69, 0x64,
	0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x75, 0x75, 0x69, 0x64, 0x12, 0x16, 0x0a, 0x06,
	0x73, 0x65, 0x71, 0x6e, 0x75, 0x6d, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x73, 0x65,
	0x71, 0x6e, 0x75, 0x6d, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x66, 0x6e, 0x75, 0x6d, 0x18, 0x08, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x05, 0x74, 0x66, 0x6e, 0x75, 0x6d, 0x12, 0x10, 0x0a, 0x03, 0x6e, 0x75,
	0x6d, 0x18, 0x09, 0x20, 0x01, 0x28, 0x05, 0x52, 0x03, 0x6e, 0x75, 0x6d, 0x12, 0x1e, 0x0a, 0x0a,
	0x61, 0x72, 0x74, 0x69, 0x73, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0a, 0x61, 0x72, 0x74, 0x69, 0x73, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1e, 0x0a, 0x0a,
	0x61, 0x72, 0x74, 0x69, 0x73, 0x74, 0x55, 0x75, 0x69, 0x64, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0a, 0x61, 0x72, 0x74, 0x69, 0x73, 0x74, 0x55, 0x75, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04,
	0x6e, 0x61, 0x6d, 0x65, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65,
	0x12, 0x16, 0x0a, 0x06, 0x62, 0x65, 0x6c, 0x6f, 0x6e, 0x67, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x06, 0x62, 0x65, 0x6c, 0x6f, 0x6e, 0x67, 0x12, 0x20, 0x0a, 0x0b, 0x61, 0x72, 0x74, 0x69,
	0x73, 0x74, 0x50, 0x68, 0x6f, 0x74, 0x6f, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x61,
	0x72, 0x74, 0x69, 0x73, 0x74, 0x50, 0x68, 0x6f, 0x74, 0x6f, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x68,
	0x6f, 0x74, 0x6f, 0x50, 0x69, 0x63, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x70, 0x68,
	0x6f, 0x74, 0x6f, 0x50, 0x69, 0x63, 0x12, 0x16, 0x0a, 0x06, 0x69, 0x73, 0x53, 0x69, 0x67, 0x6e,
	0x18, 0x10, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x69, 0x73, 0x53, 0x69, 0x67, 0x6e, 0x12, 0x16,
	0x0a, 0x06, 0x69, 0x73, 0x53, 0x65, 0x61, 0x6c, 0x18, 0x11, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06,
	0x69, 0x73, 0x53, 0x65, 0x61, 0x6c, 0x12, 0x1e, 0x0a, 0x0a, 0x61, 0x72, 0x74, 0x51, 0x75, 0x61,
	0x6c, 0x69, 0x74, 0x79, 0x18, 0x12, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0a, 0x61, 0x72, 0x74, 0x51,
	0x75, 0x61, 0x6c, 0x69, 0x74, 0x79, 0x12, 0x24, 0x0a, 0x0d, 0x69, 0x6e, 0x63, 0x6f, 0x6d, 0x70,
	0x6c, 0x65, 0x74, 0x65, 0x50, 0x69, 0x63, 0x18, 0x13, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x69,
	0x6e, 0x63, 0x6f, 0x6d, 0x70, 0x6c, 0x65, 0x74, 0x65, 0x50, 0x69, 0x63, 0x12, 0x22, 0x0a, 0x0c,
	0x63, 0x6f, 0x70, 0x79, 0x72, 0x69, 0x67, 0x68, 0x74, 0x50, 0x69, 0x63, 0x18, 0x14, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0c, 0x63, 0x6f, 0x70, 0x79, 0x72, 0x69, 0x67, 0x68, 0x74, 0x50, 0x69, 0x63,
	0x12, 0x16, 0x0a, 0x06, 0x6c, 0x65, 0x6e, 0x67, 0x74, 0x68, 0x18, 0x15, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x06, 0x6c, 0x65, 0x6e, 0x67, 0x74, 0x68, 0x12, 0x14, 0x0a, 0x05, 0x77, 0x69, 0x64, 0x74,
	0x68, 0x18, 0x16, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x77, 0x69, 0x64, 0x74, 0x68, 0x12, 0x14,
	0x0a, 0x05, 0x72, 0x75, 0x6c, 0x65, 0x72, 0x18, 0x17, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x72,
	0x75, 0x6c, 0x65, 0x72, 0x12, 0x1c, 0x0a, 0x09, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x59, 0x65, 0x61,
	0x72, 0x18, 0x18, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x59, 0x65,
	0x61, 0x72, 0x12, 0x22, 0x0a, 0x0c, 0x61, 0x72, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x53, 0x74, 0x61,
	0x74, 0x65, 0x18, 0x19, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0c, 0x61, 0x72, 0x74, 0x77, 0x6f, 0x72,
	0x6b, 0x53, 0x74, 0x61, 0x74, 0x65, 0x12, 0x1e, 0x0a, 0x0a, 0x61, 0x72, 0x74, 0x77, 0x6f, 0x72,
	0x6b, 0x50, 0x69, 0x63, 0x18, 0x1a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x61, 0x72, 0x74, 0x77,
	0x6f, 0x72, 0x6b, 0x50, 0x69, 0x63, 0x12, 0x20, 0x0a, 0x0b, 0x69, 0x73, 0x45, 0x78, 0x63, 0x65,
	0x6c, 0x6c, 0x65, 0x6e, 0x74, 0x18, 0x1b, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0b, 0x69, 0x73, 0x45,
	0x78, 0x63, 0x65, 0x6c, 0x6c, 0x65, 0x6e, 0x74, 0x12, 0x1c, 0x0a, 0x09, 0x73, 0x63, 0x72, 0x65,
	0x65, 0x6e, 0x4e, 0x75, 0x6d, 0x18, 0x1c, 0x20, 0x01, 0x28, 0x05, 0x52, 0x09, 0x73, 0x63, 0x72,
	0x65, 0x65, 0x6e, 0x4e, 0x75, 0x6d, 0x12, 0x22, 0x0a, 0x0c, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72,
	0x6b, 0x54, 0x72, 0x61, 0x63, 0x65, 0x18, 0x1d, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x6e, 0x65,
	0x74, 0x77, 0x6f, 0x72, 0x6b, 0x54, 0x72, 0x61, 0x63, 0x65, 0x12, 0x1e, 0x0a, 0x0a, 0x70, 0x68,
	0x6f, 0x74, 0x6f, 0x53, 0x74, 0x61, 0x74, 0x65, 0x18, 0x1e, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a,
	0x70, 0x68, 0x6f, 0x74, 0x6f, 0x53, 0x74, 0x61, 0x74, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x68, 0x61,
	0x73, 0x68, 0x18, 0x1f, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x68, 0x61, 0x73, 0x68, 0x12, 0x1c,
	0x0a, 0x09, 0x63, 0x6f, 0x70, 0x79, 0x72, 0x69, 0x67, 0x68, 0x74, 0x18, 0x20, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x09, 0x63, 0x6f, 0x70, 0x79, 0x72, 0x69, 0x67, 0x68, 0x74, 0x12, 0x1a, 0x0a, 0x08,
	0x61, 0x62, 0x73, 0x74, 0x72, 0x61, 0x63, 0x74, 0x18, 0x21, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08,
	0x61, 0x62, 0x73, 0x74, 0x72, 0x61, 0x63, 0x74, 0x12, 0x1c, 0x0a, 0x09, 0x6d, 0x6f, 0x75, 0x6e,
	0x74, 0x6d, 0x6f, 0x64, 0x65, 0x18, 0x22, 0x20, 0x01, 0x28, 0x05, 0x52, 0x09, 0x6d, 0x6f, 0x75,
	0x6e, 0x74, 0x6d, 0x6f, 0x64, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x6d, 0x61, 0x74, 0x65, 0x72, 0x69,
	0x61, 0x6c, 0x18, 0x23, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x6d, 0x61, 0x74, 0x65, 0x72, 0x69,
	0x61, 0x6c, 0x12, 0x18, 0x0a, 0x07, 0x73, 0x65, 0x61, 0x6c, 0x70, 0x69, 0x63, 0x18, 0x24, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x07, 0x73, 0x65, 0x61, 0x6c, 0x70, 0x69, 0x63, 0x12, 0x22, 0x0a, 0x0c,
	0x73, 0x65, 0x61, 0x6c, 0x70, 0x69, 0x63, 0x50, 0x68, 0x6f, 0x74, 0x6f, 0x18, 0x25, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0c, 0x73, 0x65, 0x61, 0x6c, 0x70, 0x69, 0x63, 0x50, 0x68, 0x6f, 0x74, 0x6f,
	0x12, 0x18, 0x0a, 0x07, 0x73, 0x69, 0x67, 0x6e, 0x70, 0x69, 0x63, 0x18, 0x26, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x07, 0x73, 0x69, 0x67, 0x6e, 0x70, 0x69, 0x63, 0x12, 0x22, 0x0a, 0x0c, 0x69, 0x6e,
	0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x44, 0x61, 0x74, 0x65, 0x18, 0x27, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0c, 0x69, 0x6e, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x44, 0x61, 0x74, 0x65, 0x12, 0x1a,
	0x0a, 0x08, 0x73, 0x69, 0x67, 0x6e, 0x64, 0x61, 0x74, 0x65, 0x18, 0x28, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x08, 0x73, 0x69, 0x67, 0x6e, 0x64, 0x61, 0x74, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x63, 0x72,
	0x65, 0x61, 0x74, 0x65, 0x64, 0x44, 0x61, 0x74, 0x65, 0x18, 0x29, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0b, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x44, 0x61, 0x74, 0x65, 0x12, 0x26, 0x0a, 0x0e,
	0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x18, 0x2a,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x64, 0x64,
	0x72, 0x65, 0x73, 0x73, 0x12, 0x2c, 0x0a, 0x11, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x41, 0x64,
	0x64, 0x72, 0x65, 0x73, 0x73, 0x43, 0x6f, 0x64, 0x65, 0x18, 0x2b, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x11, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x43, 0x6f,
	0x64, 0x65, 0x12, 0x1e, 0x0a, 0x0a, 0x61, 0x72, 0x72, 0x69, 0x76, 0x65, 0x54, 0x69, 0x6d, 0x65,
	0x18, 0x2c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x61, 0x72, 0x72, 0x69, 0x76, 0x65, 0x54, 0x69,
	0x6d, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x61, 0x72, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x54, 0x79, 0x70,
	0x65, 0x18, 0x2d, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0b, 0x61, 0x72, 0x74, 0x77, 0x6f, 0x72, 0x6b,
	0x54, 0x79, 0x70, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x67, 0x69, 0x66, 0x74, 0x49, 0x6e, 0x66, 0x6f,
	0x18, 0x2e, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x67, 0x69, 0x66, 0x74, 0x49, 0x6e, 0x66, 0x6f,
	0x12, 0x16, 0x0a, 0x06, 0x73, 0x63, 0x72, 0x6f, 0x6c, 0x6c, 0x18, 0x2f, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x06, 0x73, 0x63, 0x72, 0x6f, 0x6c, 0x6c, 0x12, 0x18, 0x0a, 0x07, 0x63, 0x6f, 0x6d, 0x6d,
	0x65, 0x6e, 0x74, 0x18, 0x30, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x63, 0x6f, 0x6d, 0x6d, 0x65,
	0x6e, 0x74, 0x12, 0x32, 0x0a, 0x14, 0x61, 0x72, 0x74, 0x4d, 0x65, 0x61, 0x6e, 0x73, 0x4f, 0x66,
	0x45, 0x78, 0x70, 0x72, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x31, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x14, 0x61, 0x72, 0x74, 0x4d, 0x65, 0x61, 0x6e, 0x73, 0x4f, 0x66, 0x45, 0x78, 0x70, 0x72,
	0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x12, 0x0a, 0x04, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x32,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x73, 0x69, 0x7a, 0x65, 0x12, 0x24, 0x0a, 0x0d, 0x61, 0x72,
	0x74, 0x48, 0x6f, 0x72, 0x69, 0x7a, 0x6f, 0x6e, 0x74, 0x61, 0x6c, 0x18, 0x33, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x0d, 0x61, 0x72, 0x74, 0x48, 0x6f, 0x72, 0x69, 0x7a, 0x6f, 0x6e, 0x74, 0x61, 0x6c,
	0x12, 0x1c, 0x0a, 0x09, 0x66, 0x6c, 0x6f, 0x77, 0x53, 0x74, 0x61, 0x74, 0x65, 0x18, 0x34, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x09, 0x66, 0x6c, 0x6f, 0x77, 0x53, 0x74, 0x61, 0x74, 0x65, 0x12, 0x14,
	0x0a, 0x05, 0x68, 0x64, 0x50, 0x69, 0x63, 0x18, 0x35, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x68,
	0x64, 0x50, 0x69, 0x63, 0x12, 0x22, 0x0a, 0x0c, 0x61, 0x72, 0x74, 0x43, 0x6f, 0x6e, 0x64, 0x69,
	0x74, 0x69, 0x6f, 0x6e, 0x18, 0x36, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0c, 0x61, 0x72, 0x74, 0x43,
	0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x18, 0x37, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x12, 0x1e, 0x0a, 0x0a, 0x70, 0x72, 0x69, 0x63, 0x65, 0x52, 0x75, 0x6c, 0x65, 0x72, 0x18, 0x38,
	0x20, 0x01, 0x28, 0x02, 0x52, 0x0a, 0x70, 0x72, 0x69, 0x63, 0x65, 0x52, 0x75, 0x6c, 0x65, 0x72,
	0x12, 0x26, 0x0a, 0x0e, 0x70, 0x72, 0x69, 0x63, 0x65, 0x43, 0x6f, 0x70, 0x79, 0x72, 0x69, 0x67,
	0x68, 0x74, 0x18, 0x39, 0x20, 0x01, 0x28, 0x02, 0x52, 0x0e, 0x70, 0x72, 0x69, 0x63, 0x65, 0x43,
	0x6f, 0x70, 0x79, 0x72, 0x69, 0x67, 0x68, 0x74, 0x12, 0x22, 0x0a, 0x0c, 0x70, 0x72, 0x69, 0x63,
	0x65, 0x41, 0x72, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x18, 0x3a, 0x20, 0x01, 0x28, 0x02, 0x52, 0x0c,
	0x70, 0x72, 0x69, 0x63, 0x65, 0x41, 0x72, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x12, 0x20, 0x0a, 0x0b,
	0x70, 0x72, 0x69, 0x63, 0x65, 0x4d, 0x61, 0x72, 0x6b, 0x65, 0x74, 0x18, 0x3b, 0x20, 0x01, 0x28,
	0x02, 0x52, 0x0b, 0x70, 0x72, 0x69, 0x63, 0x65, 0x4d, 0x61, 0x72, 0x6b, 0x65, 0x74, 0x12, 0x1a,
	0x0a, 0x08, 0x70, 0x72, 0x69, 0x63, 0x65, 0x52, 0x75, 0x6e, 0x18, 0x3c, 0x20, 0x01, 0x28, 0x02,
	0x52, 0x08, 0x70, 0x72, 0x69, 0x63, 0x65, 0x52, 0x75, 0x6e, 0x12, 0x20, 0x0a, 0x0b, 0x66, 0x69,
	0x6c, 0x74, 0x65, 0x72, 0x53, 0x74, 0x61, 0x74, 0x65, 0x18, 0x3d, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x0b, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x53, 0x74, 0x61, 0x74, 0x65, 0x12, 0x22, 0x0a, 0x0c,
	0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x18, 0x3e, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x0c, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65,
	0x12, 0x26, 0x0a, 0x0e, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x44, 0x6f, 0x6e, 0x65, 0x44, 0x61,
	0x74, 0x65, 0x18, 0x3f, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65,
	0x44, 0x6f, 0x6e, 0x65, 0x44, 0x61, 0x74, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x6d, 0x61, 0x73, 0x6b,
	0x18, 0x40, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x6d, 0x61, 0x73, 0x6b, 0x12, 0x1a, 0x0a, 0x08,
	0x69, 0x6e, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x18, 0x41, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08,
	0x69, 0x6e, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x12, 0x1c, 0x0a, 0x09, 0x63, 0x72, 0x65, 0x61,
	0x74, 0x65, 0x64, 0x41, 0x74, 0x18, 0x42, 0x20, 0x01, 0x28, 0x05, 0x52, 0x09, 0x63, 0x72, 0x65,
	0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x1c, 0x0a, 0x09, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x64, 0x41, 0x74, 0x18, 0x43, 0x20, 0x01, 0x28, 0x05, 0x52, 0x09, 0x75, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x64, 0x41, 0x74, 0x12, 0x1c, 0x0a, 0x09, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x64, 0x41,
	0x74, 0x18, 0x44, 0x20, 0x01, 0x28, 0x05, 0x52, 0x09, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x64,
	0x41, 0x74, 0x22, 0xc5, 0x01, 0x0a, 0x1c, 0x47, 0x65, 0x74, 0x41, 0x72, 0x74, 0x77, 0x6f, 0x72,
	0x6b, 0x50, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x12, 0x31, 0x0a, 0x05, 0x71, 0x75, 0x65, 0x72, 0x79, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x41, 0x72, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2e, 0x41, 0x72, 0x74,
	0x77, 0x6f, 0x72, 0x6b, 0x50, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x44, 0x61, 0x74, 0x61, 0x52,
	0x05, 0x71, 0x75, 0x65, 0x72, 0x79, 0x12, 0x12, 0x0a, 0x04, 0x70, 0x61, 0x67, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x04, 0x70, 0x61, 0x67, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x61,
	0x67, 0x65, 0x53, 0x69, 0x7a, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x70, 0x61,
	0x67, 0x65, 0x53, 0x69, 0x7a, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x77, 0x68, 0x65, 0x72, 0x65, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x77, 0x68, 0x65, 0x72, 0x65, 0x12, 0x14, 0x0a, 0x05,
	0x6f, 0x72, 0x64, 0x65, 0x72, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x6f, 0x72, 0x64,
	0x65, 0x72, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x65, 0x6c, 0x65, 0x63, 0x74, 0x18, 0x06, 0x20, 0x03,
	0x28, 0x09, 0x52, 0x06, 0x73, 0x65, 0x6c, 0x65, 0x63, 0x74, 0x22, 0x92, 0x01, 0x0a, 0x19, 0x47,
	0x65, 0x74, 0x41, 0x72, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x50, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65,
	0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x70, 0x12, 0x2f, 0x0a, 0x04, 0x6c, 0x69, 0x73, 0x74,
	0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x41, 0x72, 0x74, 0x77, 0x6f, 0x72, 0x6b,
	0x2e, 0x41, 0x72, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x50, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x44,
	0x61, 0x74, 0x61, 0x52, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x12, 0x12, 0x0a, 0x04, 0x70, 0x61, 0x67,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x04, 0x70, 0x61, 0x67, 0x65, 0x12, 0x1a, 0x0a,
	0x08, 0x70, 0x61, 0x67, 0x65, 0x53, 0x69, 0x7a, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x08, 0x70, 0x61, 0x67, 0x65, 0x53, 0x69, 0x7a, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x54, 0x6f, 0x74,
	0x61, 0x6c, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x54, 0x6f, 0x74, 0x61, 0x6c, 0x22,
	0x82, 0x01, 0x0a, 0x18, 0x41, 0x72, 0x74, 0x69, 0x73, 0x74, 0x42, 0x69, 0x6e, 0x64, 0x41, 0x72,
	0x74, 0x77, 0x6f, 0x72, 0x6b, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x12, 0x1e, 0x0a, 0x0a,
	0x41, 0x72, 0x74, 0x69, 0x73, 0x74, 0x55, 0x75, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0a, 0x41, 0x72, 0x74, 0x69, 0x73, 0x74, 0x55, 0x75, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04,
	0x50, 0x61, 0x67, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x50, 0x61, 0x67, 0x65,
	0x12, 0x1a, 0x0a, 0x08, 0x50, 0x61, 0x67, 0x65, 0x53, 0x69, 0x7a, 0x65, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x08, 0x50, 0x61, 0x67, 0x65, 0x53, 0x69, 0x7a, 0x65, 0x12, 0x16, 0x0a, 0x06,
	0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x41, 0x63,
	0x74, 0x69, 0x6f, 0x6e, 0x22, 0xcf, 0x02, 0x0a, 0x19, 0x41, 0x72, 0x74, 0x69, 0x73, 0x74, 0x42,
	0x69, 0x6e, 0x64, 0x41, 0x72, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65,
	0x73, 0x70, 0x12, 0x3b, 0x0a, 0x04, 0x44, 0x61, 0x74, 0x61, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x27, 0x2e, 0x41, 0x72, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2e, 0x41, 0x72, 0x74, 0x69, 0x73,
	0x74, 0x42, 0x69, 0x6e, 0x64, 0x41, 0x72, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x4c, 0x69, 0x73, 0x74,
	0x52, 0x65, 0x73, 0x70, 0x2e, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x04, 0x44, 0x61, 0x74, 0x61, 0x12,
	0x14, 0x0a, 0x05, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05,
	0x43, 0x6f, 0x75, 0x6e, 0x74, 0x1a, 0xde, 0x01, 0x0a, 0x04, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x20,
	0x0a, 0x0b, 0x41, 0x72, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x55, 0x75, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0b, 0x41, 0x72, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x55, 0x75, 0x69, 0x64,
	0x12, 0x14, 0x0a, 0x05, 0x54, 0x66, 0x6e, 0x75, 0x6d, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x05, 0x54, 0x66, 0x6e, 0x75, 0x6d, 0x12, 0x16, 0x0a, 0x06, 0x4c, 0x65, 0x6e, 0x67, 0x74, 0x68,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x4c, 0x65, 0x6e, 0x67, 0x74, 0x68, 0x12, 0x14,
	0x0a, 0x05, 0x57, 0x69, 0x64, 0x74, 0x68, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x57,
	0x69, 0x64, 0x74, 0x68, 0x12, 0x14, 0x0a, 0x05, 0x52, 0x75, 0x6c, 0x65, 0x72, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x05, 0x52, 0x75, 0x6c, 0x65, 0x72, 0x12, 0x1e, 0x0a, 0x0a, 0x53, 0x61,
	0x6c, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x06, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0a,
	0x53, 0x61, 0x6c, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x24, 0x0a, 0x0d, 0x53, 0x74,
	0x6f, 0x72, 0x61, 0x67, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x07, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x0d, 0x53, 0x74, 0x6f, 0x72, 0x61, 0x67, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x12, 0x14, 0x0a, 0x05, 0x48, 0x64, 0x50, 0x69, 0x63, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x05, 0x48, 0x64, 0x50, 0x69, 0x63, 0x22, 0xb4, 0x05, 0x0a, 0x14, 0x41, 0x72, 0x74, 0x77, 0x6f,
	0x72, 0x6b, 0x43, 0x6f, 0x70, 0x79, 0x72, 0x69, 0x67, 0x68, 0x74, 0x44, 0x61, 0x74, 0x61, 0x12,
	0x0e, 0x0a, 0x02, 0x49, 0x44, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x49, 0x44, 0x12,
	0x1c, 0x0a, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x1c, 0x0a,
	0x09, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x09, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x1c, 0x0a, 0x09, 0x64,
	0x65, 0x6c, 0x65, 0x74, 0x65, 0x64, 0x41, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09,
	0x64, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x20, 0x0a, 0x0b, 0x61, 0x72, 0x74,
	0x77, 0x6f, 0x72, 0x6b, 0x55, 0x75, 0x69, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b,
	0x61, 0x72, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x55, 0x75, 0x69, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x6e,
	0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x6e, 0x75, 0x6d,
	0x62, 0x65, 0x72, 0x12, 0x22, 0x0a, 0x0c, 0x73, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x4e, 0x75, 0x6d,
	0x62, 0x65, 0x72, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x73, 0x65, 0x72, 0x69, 0x61,
	0x6c, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x1c, 0x0a, 0x09, 0x61, 0x70, 0x70, 0x6c, 0x79,
	0x54, 0x69, 0x6d, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x61, 0x70, 0x70, 0x6c,
	0x79, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x26, 0x0a, 0x0e, 0x72, 0x65, 0x67, 0x69, 0x73, 0x74, 0x65,
	0x72, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x72,
	0x65, 0x67, 0x69, 0x73, 0x74, 0x65, 0x72, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x1a, 0x0a,
	0x08, 0x63, 0x65, 0x72, 0x74, 0x44, 0x69, 0x67, 0x69, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x08, 0x63, 0x65, 0x72, 0x74, 0x44, 0x69, 0x67, 0x69, 0x12, 0x2a, 0x0a, 0x10, 0x63, 0x65, 0x72,
	0x74, 0x52, 0x65, 0x67, 0x69, 0x73, 0x74, 0x65, 0x72, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x0c, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x10, 0x63, 0x65, 0x72, 0x74, 0x52, 0x65, 0x67, 0x69, 0x73, 0x74, 0x65,
	0x72, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x34, 0x0a, 0x15, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x52, 0x65,
	0x67, 0x69, 0x73, 0x74, 0x65, 0x72, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x61, 0x63, 0x74, 0x18, 0x0d,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x15, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x67, 0x69, 0x73,
	0x74, 0x65, 0x72, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x61, 0x63, 0x74, 0x12, 0x2a, 0x0a, 0x10, 0x70,
	0x72, 0x6f, 0x6d, 0x69, 0x73, 0x65, 0x4c, 0x65, 0x74, 0x74, 0x65, 0x72, 0x55, 0x72, 0x6c, 0x18,
	0x0e, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x70, 0x72, 0x6f, 0x6d, 0x69, 0x73, 0x65, 0x4c, 0x65,
	0x74, 0x74, 0x65, 0x72, 0x55, 0x72, 0x6c, 0x12, 0x2a, 0x0a, 0x10, 0x65, 0x6e, 0x74, 0x72, 0x75,
	0x73, 0x74, 0x4c, 0x65, 0x74, 0x74, 0x65, 0x72, 0x55, 0x72, 0x6c, 0x18, 0x0f, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x10, 0x65, 0x6e, 0x74, 0x72, 0x75, 0x73, 0x74, 0x4c, 0x65, 0x74, 0x74, 0x65, 0x72,
	0x55, 0x72, 0x6c, 0x12, 0x30, 0x0a, 0x13, 0x6c, 0x69, 0x63, 0x65, 0x6e, 0x73, 0x65, 0x41, 0x67,
	0x72, 0x65, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x55, 0x72, 0x6c, 0x18, 0x10, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x13, 0x6c, 0x69, 0x63, 0x65, 0x6e, 0x73, 0x65, 0x41, 0x67, 0x72, 0x65, 0x65, 0x6d, 0x65,
	0x6e, 0x74, 0x55, 0x72, 0x6c, 0x12, 0x32, 0x0a, 0x14, 0x64, 0x6f, 0x77, 0x6e, 0x6c, 0x6f, 0x61,
	0x64, 0x41, 0x67, 0x72, 0x65, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x55, 0x72, 0x6c, 0x18, 0x11, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x14, 0x64, 0x6f, 0x77, 0x6e, 0x6c, 0x6f, 0x61, 0x64, 0x41, 0x67, 0x72,
	0x65, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x55, 0x72, 0x6c, 0x12, 0x28, 0x0a, 0x0f, 0x63, 0x6f, 0x70,
	0x79, 0x72, 0x69, 0x67, 0x68, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x12, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0f, 0x63, 0x6f, 0x70, 0x79, 0x72, 0x69, 0x67, 0x68, 0x74, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x12, 0x28, 0x0a, 0x0f, 0x63, 0x6f, 0x70, 0x79, 0x72, 0x69, 0x67, 0x68, 0x74,
	0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x18, 0x13, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0f, 0x63, 0x6f,
	0x70, 0x79, 0x72, 0x69, 0x67, 0x68, 0x74, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x22, 0xb1, 0x01,
	0x0a, 0x1e, 0x47, 0x65, 0x74, 0x41, 0x72, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x43, 0x6f, 0x70, 0x79,
	0x72, 0x69, 0x67, 0x68, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x12, 0x33, 0x0a, 0x05, 0x71, 0x75, 0x65, 0x72, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x1d, 0x2e, 0x41, 0x72, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2e, 0x41, 0x72, 0x74, 0x77, 0x6f, 0x72,
	0x6b, 0x43, 0x6f, 0x70, 0x79, 0x72, 0x69, 0x67, 0x68, 0x74, 0x44, 0x61, 0x74, 0x61, 0x52, 0x05,
	0x71, 0x75, 0x65, 0x72, 0x79, 0x12, 0x12, 0x0a, 0x04, 0x70, 0x61, 0x67, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x04, 0x70, 0x61, 0x67, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x61, 0x67,
	0x65, 0x53, 0x69, 0x7a, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x70, 0x61, 0x67,
	0x65, 0x53, 0x69, 0x7a, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x77, 0x68, 0x65, 0x72, 0x65, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x77, 0x68, 0x65, 0x72, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x6f,
	0x72, 0x64, 0x65, 0x72, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x6f, 0x72, 0x64, 0x65,
	0x72, 0x22, 0x96, 0x01, 0x0a, 0x1b, 0x47, 0x65, 0x74, 0x41, 0x72, 0x74, 0x77, 0x6f, 0x72, 0x6b,
	0x43, 0x6f, 0x70, 0x79, 0x72, 0x69, 0x67, 0x68, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73,
	0x70, 0x12, 0x31, 0x0a, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x1d, 0x2e, 0x41, 0x72, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2e, 0x41, 0x72, 0x74, 0x77, 0x6f, 0x72,
	0x6b, 0x43, 0x6f, 0x70, 0x79, 0x72, 0x69, 0x67, 0x68, 0x74, 0x44, 0x61, 0x74, 0x61, 0x52, 0x04,
	0x6c, 0x69, 0x73, 0x74, 0x12, 0x12, 0x0a, 0x04, 0x70, 0x61, 0x67, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x04, 0x70, 0x61, 0x67, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x61, 0x67, 0x65,
	0x53, 0x69, 0x7a, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x70, 0x61, 0x67, 0x65,
	0x53, 0x69, 0x7a, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x54, 0x6f, 0x74, 0x61, 0x6c, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x05, 0x54, 0x6f, 0x74, 0x61, 0x6c, 0x22, 0x3c, 0x0a, 0x16, 0x47, 0x65,
	0x74, 0x41, 0x72, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x44, 0x61, 0x74, 0x61, 0x42, 0x61, 0x74, 0x63,
	0x68, 0x52, 0x65, 0x71, 0x12, 0x22, 0x0a, 0x0c, 0x41, 0x72, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x55,
	0x75, 0x69, 0x64, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0c, 0x41, 0x72, 0x74, 0x77,
	0x6f, 0x72, 0x6b, 0x55, 0x75, 0x69, 0x64, 0x73, 0x22, 0xd0, 0x08, 0x0a, 0x17, 0x47, 0x65, 0x74,
	0x41, 0x72, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x44, 0x61, 0x74, 0x61, 0x42, 0x61, 0x74, 0x63, 0x68,
	0x52, 0x65, 0x73, 0x70, 0x12, 0x40, 0x0a, 0x04, 0x44, 0x61, 0x74, 0x61, 0x18, 0x01, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x2c, 0x2e, 0x41, 0x72, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2e, 0x47, 0x65, 0x74,
	0x41, 0x72, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x44, 0x61, 0x74, 0x61, 0x42, 0x61, 0x74, 0x63, 0x68,
	0x52, 0x65, 0x73, 0x70, 0x2e, 0x41, 0x72, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x49, 0x6e, 0x66, 0x6f,
	0x52, 0x04, 0x44, 0x61, 0x74, 0x61, 0x1a, 0xf2, 0x07, 0x0a, 0x0b, 0x41, 0x72, 0x74, 0x77, 0x6f,
	0x72, 0x6b, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x20, 0x0a, 0x0b, 0x41, 0x72, 0x74, 0x77, 0x6f, 0x72,
	0x6b, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x41, 0x72, 0x74,
	0x77, 0x6f, 0x72, 0x6b, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x54, 0x66, 0x6e, 0x75,
	0x6d, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x54, 0x66, 0x6e, 0x75, 0x6d, 0x12, 0x16,
	0x0a, 0x06, 0x4c, 0x65, 0x6e, 0x67, 0x74, 0x68, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06,
	0x4c, 0x65, 0x6e, 0x67, 0x74, 0x68, 0x12, 0x14, 0x0a, 0x05, 0x57, 0x69, 0x64, 0x74, 0x68, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x57, 0x69, 0x64, 0x74, 0x68, 0x12, 0x20, 0x0a, 0x0b,
	0x41, 0x72, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x54, 0x79, 0x70, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x0b, 0x41, 0x72, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1e,
	0x0a, 0x0a, 0x53, 0x61, 0x6c, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x06, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x0a, 0x53, 0x61, 0x6c, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x24,
	0x0a, 0x0d, 0x53, 0x74, 0x6f, 0x72, 0x61, 0x67, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18,
	0x07, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0d, 0x53, 0x74, 0x6f, 0x72, 0x61, 0x67, 0x65, 0x53, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x12, 0x14, 0x0a, 0x05, 0x48, 0x64, 0x50, 0x69, 0x63, 0x18, 0x08, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x05, 0x48, 0x64, 0x50, 0x69, 0x63, 0x12, 0x20, 0x0a, 0x0b, 0x41, 0x72,
	0x74, 0x77, 0x6f, 0x72, 0x6b, 0x55, 0x75, 0x69, 0x64, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0b, 0x41, 0x72, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x55, 0x75, 0x69, 0x64, 0x12, 0x1f, 0x0a, 0x0a,
	0x41, 0x72, 0x74, 0x69, 0x73, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0b, 0x61, 0x72, 0x74, 0x69, 0x73, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x1a, 0x0a,
	0x08, 0x4d, 0x61, 0x74, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x08, 0x6d, 0x61, 0x74, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x12, 0x23, 0x0a, 0x0c, 0x41, 0x72, 0x74,
	0x43, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x0d, 0x61, 0x72, 0x74, 0x5f, 0x63, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x1c,
	0x0a, 0x09, 0x4d, 0x6f, 0x75, 0x6e, 0x74, 0x6d, 0x6f, 0x64, 0x65, 0x18, 0x0d, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x09, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x6d, 0x6f, 0x64, 0x65, 0x12, 0x25, 0x0a, 0x0d,
	0x41, 0x72, 0x74, 0x48, 0x6f, 0x72, 0x69, 0x7a, 0x6f, 0x6e, 0x74, 0x61, 0x6c, 0x18, 0x0e, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x0e, 0x61, 0x72, 0x74, 0x5f, 0x68, 0x6f, 0x72, 0x69, 0x7a, 0x6f, 0x6e,
	0x74, 0x61, 0x6c, 0x12, 0x12, 0x0a, 0x04, 0x53, 0x69, 0x7a, 0x65, 0x18, 0x0f, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x04, 0x73, 0x69, 0x7a, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x52, 0x75, 0x6c, 0x65, 0x72,
	0x18, 0x10, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x72, 0x75, 0x6c, 0x65, 0x72, 0x12, 0x23, 0x0a,
	0x0c, 0x49, 0x6e, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x44, 0x61, 0x74, 0x65, 0x18, 0x11, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0d, 0x69, 0x6e, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x5f, 0x64, 0x61,
	0x74, 0x65, 0x12, 0x21, 0x0a, 0x0b, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x44, 0x61, 0x74,
	0x65, 0x18, 0x12, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64,
	0x5f, 0x64, 0x61, 0x74, 0x65, 0x12, 0x27, 0x0a, 0x0e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64,
	0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x18, 0x13, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x63,
	0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x12, 0x28,
	0x0a, 0x0e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x44, 0x6f, 0x6e, 0x65, 0x44, 0x61, 0x74, 0x65,
	0x18, 0x14, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x5f, 0x64,
	0x6f, 0x6e, 0x65, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x12, 0x21, 0x0a, 0x0b, 0x46, 0x69, 0x6c, 0x74,
	0x65, 0x72, 0x53, 0x74, 0x61, 0x74, 0x65, 0x18, 0x15, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0c, 0x66,
	0x69, 0x6c, 0x74, 0x65, 0x72, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x65, 0x12, 0x1b, 0x0a, 0x08, 0x50,
	0x72, 0x69, 0x63, 0x65, 0x52, 0x75, 0x6e, 0x18, 0x16, 0x20, 0x01, 0x28, 0x02, 0x52, 0x09, 0x70,
	0x72, 0x69, 0x63, 0x65, 0x5f, 0x72, 0x75, 0x6e, 0x12, 0x1b, 0x0a, 0x08, 0x53, 0x69, 0x67, 0x6e,
	0x64, 0x61, 0x74, 0x65, 0x18, 0x17, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x73, 0x69, 0x67, 0x6e,
	0x5f, 0x64, 0x61, 0x74, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x43, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x74,
	0x18, 0x18, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x12,
	0x1a, 0x0a, 0x08, 0x41, 0x62, 0x73, 0x74, 0x72, 0x61, 0x63, 0x74, 0x18, 0x19, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x08, 0x61, 0x62, 0x73, 0x74, 0x72, 0x61, 0x63, 0x74, 0x12, 0x24, 0x0a, 0x0d, 0x45,
	0x64, 0x75, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x1a, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0d, 0x45, 0x64, 0x75, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x12, 0x1f, 0x0a, 0x0a, 0x41, 0x72, 0x74, 0x51, 0x75, 0x61, 0x6c, 0x69, 0x74, 0x79, 0x18,
	0x1b, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0b, 0x61, 0x72, 0x74, 0x5f, 0x71, 0x75, 0x61, 0x6c, 0x69,
	0x74, 0x79, 0x12, 0x18, 0x0a, 0x07, 0x53, 0x69, 0x67, 0x6e, 0x70, 0x69, 0x63, 0x18, 0x1c, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x07, 0x73, 0x69, 0x67, 0x6e, 0x70, 0x69, 0x63, 0x12, 0x18, 0x0a, 0x07,
	0x53, 0x65, 0x61, 0x6c, 0x70, 0x69, 0x63, 0x18, 0x1d, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x73,
	0x65, 0x61, 0x6c, 0x70, 0x69, 0x63, 0x12, 0x21, 0x0a, 0x0b, 0x41, 0x72, 0x74, 0x69, 0x73, 0x74,
	0x50, 0x68, 0x6f, 0x74, 0x6f, 0x18, 0x1e, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x61, 0x72, 0x74,
	0x69, 0x73, 0x74, 0x5f, 0x70, 0x68, 0x6f, 0x74, 0x6f, 0x12, 0x1b, 0x0a, 0x08, 0x50, 0x68, 0x6f,
	0x74, 0x6f, 0x50, 0x69, 0x63, 0x18, 0x1f, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x70, 0x68, 0x6f,
	0x74, 0x6f, 0x5f, 0x70, 0x69, 0x63, 0x12, 0x23, 0x0a, 0x0c, 0x53, 0x65, 0x61, 0x6c, 0x70, 0x69,
	0x63, 0x50, 0x68, 0x6f, 0x74, 0x6f, 0x18, 0x20, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x73, 0x65,
	0x61, 0x6c, 0x70, 0x69, 0x63, 0x5f, 0x70, 0x68, 0x6f, 0x74, 0x6f, 0x22, 0xb1, 0x01, 0x0a, 0x0f,
	0x42, 0x61, 0x74, 0x63, 0x68, 0x44, 0x63, 0x69, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x12,
	0x1e, 0x0a, 0x0a, 0x41, 0x72, 0x74, 0x69, 0x73, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0a, 0x41, 0x72, 0x74, 0x69, 0x73, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x12,
	0x20, 0x0a, 0x0b, 0x41, 0x72, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x41, 0x72, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x4e, 0x61, 0x6d,
	0x65, 0x12, 0x12, 0x0a, 0x04, 0x50, 0x61, 0x67, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x04, 0x50, 0x61, 0x67, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x50, 0x61, 0x67, 0x65, 0x53, 0x69, 0x7a,
	0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x50, 0x61, 0x67, 0x65, 0x53, 0x69, 0x7a,
	0x65, 0x12, 0x2c, 0x0a, 0x11, 0x4e, 0x6f, 0x74, 0x49, 0x6e, 0x41, 0x72, 0x74, 0x77, 0x6f, 0x72,
	0x6b, 0x55, 0x75, 0x69, 0x64, 0x73, 0x18, 0x05, 0x20, 0x03, 0x28, 0x09, 0x52, 0x11, 0x4e, 0x6f,
	0x74, 0x49, 0x6e, 0x41, 0x72, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x55, 0x75, 0x69, 0x64, 0x73, 0x22,
	0xc3, 0x02, 0x0a, 0x10, 0x42, 0x61, 0x74, 0x63, 0x68, 0x44, 0x63, 0x69, 0x4c, 0x69, 0x73, 0x74,
	0x52, 0x65, 0x73, 0x70, 0x12, 0x32, 0x0a, 0x04, 0x44, 0x61, 0x74, 0x61, 0x18, 0x01, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x41, 0x72, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2e, 0x42, 0x61, 0x74,
	0x63, 0x68, 0x44, 0x63, 0x69, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x70, 0x2e, 0x49, 0x6e,
	0x66, 0x6f, 0x52, 0x04, 0x44, 0x61, 0x74, 0x61, 0x12, 0x14, 0x0a, 0x05, 0x43, 0x6f, 0x75, 0x6e,
	0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x1a, 0xe4,
	0x01, 0x0a, 0x04, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x20, 0x0a, 0x0b, 0x41, 0x72, 0x74, 0x77, 0x6f,
	0x72, 0x6b, 0x55, 0x75, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x41, 0x72,
	0x74, 0x77, 0x6f, 0x72, 0x6b, 0x55, 0x75, 0x69, 0x64, 0x12, 0x1e, 0x0a, 0x0a, 0x41, 0x72, 0x74,
	0x69, 0x73, 0x74, 0x55, 0x75, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x41,
	0x72, 0x74, 0x69, 0x73, 0x74, 0x55, 0x75, 0x69, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x54, 0x66, 0x6e,
	0x75, 0x6d, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x54, 0x66, 0x6e, 0x75, 0x6d, 0x12,
	0x1e, 0x0a, 0x0a, 0x41, 0x72, 0x74, 0x69, 0x73, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0a, 0x41, 0x72, 0x74, 0x69, 0x73, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x12,
	0x20, 0x0a, 0x0b, 0x41, 0x72, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x41, 0x72, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x4e, 0x61, 0x6d,
	0x65, 0x12, 0x20, 0x0a, 0x0b, 0x57, 0x6f, 0x72, 0x6b, 0x46, 0x69, 0x6c, 0x65, 0x55, 0x72, 0x6c,
	0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x57, 0x6f, 0x72, 0x6b, 0x46, 0x69, 0x6c, 0x65,
	0x55, 0x72, 0x6c, 0x12, 0x20, 0x0a, 0x0b, 0x41, 0x72, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x54, 0x79,
	0x70, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0b, 0x41, 0x72, 0x74, 0x77, 0x6f, 0x72,
	0x6b, 0x54, 0x79, 0x70, 0x65, 0x22, 0x56, 0x0a, 0x14, 0x41, 0x72, 0x74, 0x77, 0x6f, 0x72, 0x6b,
	0x41, 0x72, 0x74, 0x69, 0x73, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x12, 0x1e, 0x0a,
	0x0a, 0x41, 0x72, 0x74, 0x69, 0x73, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0a, 0x41, 0x72, 0x74, 0x69, 0x73, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1e, 0x0a,
	0x0a, 0x41, 0x72, 0x74, 0x69, 0x73, 0x74, 0x55, 0x75, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0a, 0x41, 0x72, 0x74, 0x69, 0x73, 0x74, 0x55, 0x75, 0x69, 0x64, 0x22, 0xb8, 0x01,
	0x0a, 0x15, 0x41, 0x72, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x41, 0x72, 0x74, 0x69, 0x73, 0x74, 0x4c,
	0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x70, 0x12, 0x37, 0x0a, 0x04, 0x44, 0x61, 0x74, 0x61, 0x18,
	0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x23, 0x2e, 0x41, 0x72, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2e,
	0x41, 0x72, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x41, 0x72, 0x74, 0x69, 0x73, 0x74, 0x4c, 0x69, 0x73,
	0x74, 0x52, 0x65, 0x73, 0x70, 0x2e, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x04, 0x44, 0x61, 0x74, 0x61,
	0x1a, 0x66, 0x0a, 0x04, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x1e, 0x0a, 0x0a, 0x41, 0x72, 0x74, 0x77,
	0x6f, 0x72, 0x6b, 0x4e, 0x75, 0x6d, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0a, 0x41, 0x72,
	0x74, 0x77, 0x6f, 0x72, 0x6b, 0x4e, 0x75, 0x6d, 0x12, 0x1e, 0x0a, 0x0a, 0x41, 0x72, 0x74, 0x69,
	0x73, 0x74, 0x55, 0x75, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x41, 0x72,
	0x74, 0x69, 0x73, 0x74, 0x55, 0x75, 0x69, 0x64, 0x12, 0x1e, 0x0a, 0x0a, 0x41, 0x72, 0x74, 0x69,
	0x73, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x41, 0x72,
	0x74, 0x69, 0x73, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x22, 0xae, 0x02, 0x0a, 0x0e, 0x53, 0x74, 0x6f,
	0x72, 0x61, 0x67, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x12, 0x12, 0x0a, 0x04, 0x50,
	0x61, 0x67, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x50, 0x61, 0x67, 0x65, 0x12,
	0x1a, 0x0a, 0x08, 0x50, 0x61, 0x67, 0x65, 0x53, 0x69, 0x7a, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x08, 0x50, 0x61, 0x67, 0x65, 0x53, 0x69, 0x7a, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x46,
	0x6c, 0x61, 0x67, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x46, 0x6c, 0x61, 0x67, 0x12,
	0x1e, 0x0a, 0x0a, 0x41, 0x72, 0x74, 0x69, 0x73, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0a, 0x41, 0x72, 0x74, 0x69, 0x73, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x12,
	0x20, 0x0a, 0x0b, 0x41, 0x72, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x41, 0x72, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x4e, 0x61, 0x6d,
	0x65, 0x12, 0x14, 0x0a, 0x05, 0x54, 0x66, 0x6e, 0x75, 0x6d, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x05, 0x54, 0x66, 0x6e, 0x75, 0x6d, 0x12, 0x12, 0x0a, 0x04, 0x54, 0x6e, 0x75, 0x6d, 0x18,
	0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x54, 0x6e, 0x75, 0x6d, 0x12, 0x2e, 0x0a, 0x12, 0x46,
	0x69, 0x6c, 0x74, 0x65, 0x72, 0x41, 0x72, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x55, 0x75, 0x69, 0x64,
	0x73, 0x18, 0x08, 0x20, 0x03, 0x28, 0x09, 0x52, 0x12, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x41,
	0x72, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x55, 0x75, 0x69, 0x64, 0x73, 0x12, 0x20, 0x0a, 0x0b, 0x41,
	0x72, 0x74, 0x69, 0x73, 0x74, 0x55, 0x75, 0x69, 0x64, 0x73, 0x18, 0x09, 0x20, 0x03, 0x28, 0x09,
	0x52, 0x0b, 0x41, 0x72, 0x74, 0x69, 0x73, 0x74, 0x55, 0x75, 0x69, 0x64, 0x73, 0x12, 0x1a, 0x0a,
	0x08, 0x4b, 0x65, 0x79, 0x77, 0x6f, 0x72, 0x64, 0x73, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x08, 0x4b, 0x65, 0x79, 0x77, 0x6f, 0x72, 0x64, 0x73, 0x22, 0xbd, 0x02, 0x0a, 0x0f, 0x53, 0x74,
	0x6f, 0x72, 0x61, 0x67, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x70, 0x12, 0x31, 0x0a,
	0x04, 0x44, 0x61, 0x74, 0x61, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x41, 0x72,
	0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2e, 0x53, 0x74, 0x6f, 0x72, 0x61, 0x67, 0x65, 0x4c, 0x69, 0x73,
	0x74, 0x52, 0x65, 0x73, 0x70, 0x2e, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x04, 0x44, 0x61, 0x74, 0x61,
	0x12, 0x14, 0x0a, 0x05, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x05, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x1a, 0xe0, 0x01, 0x0a, 0x04, 0x49, 0x6e, 0x66, 0x6f, 0x12,
	0x20, 0x0a, 0x0b, 0x41, 0x72, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x55, 0x75, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x41, 0x72, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x55, 0x75, 0x69,
	0x64, 0x12, 0x1e, 0x0a, 0x0a, 0x41, 0x72, 0x74, 0x69, 0x73, 0x74, 0x55, 0x75, 0x69, 0x64, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x41, 0x72, 0x74, 0x69, 0x73, 0x74, 0x55, 0x75, 0x69,
	0x64, 0x12, 0x14, 0x0a, 0x05, 0x54, 0x66, 0x6e, 0x75, 0x6d, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x05, 0x54, 0x66, 0x6e, 0x75, 0x6d, 0x12, 0x12, 0x0a, 0x04, 0x54, 0x6e, 0x75, 0x6d, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x54, 0x6e, 0x75, 0x6d, 0x12, 0x20, 0x0a, 0x0b, 0x41,
	0x72, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0b, 0x41, 0x72, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1e, 0x0a,
	0x0a, 0x41, 0x72, 0x74, 0x69, 0x73, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0a, 0x41, 0x72, 0x74, 0x69, 0x73, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x14, 0x0a,
	0x05, 0x52, 0x75, 0x6c, 0x65, 0x72, 0x18, 0x07, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x52, 0x75,
	0x6c, 0x65, 0x72, 0x12, 0x14, 0x0a, 0x05, 0x48, 0x64, 0x50, 0x69, 0x63, 0x18, 0x08, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x05, 0x48, 0x64, 0x50, 0x69, 0x63, 0x22, 0x60, 0x0a, 0x14, 0x53, 0x65, 0x63,
	0x6f, 0x6e, 0x64, 0x41, 0x72, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65,
	0x71, 0x12, 0x12, 0x0a, 0x04, 0x50, 0x61, 0x67, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x04, 0x50, 0x61, 0x67, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x50, 0x61, 0x67, 0x65, 0x53, 0x69, 0x7a,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x50, 0x61, 0x67, 0x65, 0x53, 0x69, 0x7a,
	0x65, 0x12, 0x18, 0x0a, 0x07, 0x4b, 0x65, 0x79, 0x77, 0x6f, 0x72, 0x64, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x07, 0x4b, 0x65, 0x79, 0x77, 0x6f, 0x72, 0x64, 0x22, 0x97, 0x05, 0x0a, 0x15,
	0x53, 0x65, 0x63, 0x6f, 0x6e, 0x64, 0x41, 0x72, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x4c, 0x69, 0x73,
	0x74, 0x52, 0x65, 0x73, 0x70, 0x12, 0x37, 0x0a, 0x04, 0x44, 0x61, 0x74, 0x61, 0x18, 0x01, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x23, 0x2e, 0x41, 0x72, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2e, 0x53, 0x65,
	0x63, 0x6f, 0x6e, 0x64, 0x41, 0x72, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x4c, 0x69, 0x73, 0x74, 0x52,
	0x65, 0x73, 0x70, 0x2e, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x04, 0x44, 0x61, 0x74, 0x61, 0x12, 0x14,
	0x0a, 0x05, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x43,
	0x6f, 0x75, 0x6e, 0x74, 0x1a, 0xae, 0x04, 0x0a, 0x04, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x20, 0x0a,
	0x0b, 0x41, 0x72, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x55, 0x75, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0b, 0x41, 0x72, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x55, 0x75, 0x69, 0x64, 0x12,
	0x1e, 0x0a, 0x0a, 0x41, 0x72, 0x74, 0x69, 0x73, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0a, 0x41, 0x72, 0x74, 0x69, 0x73, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x12,
	0x16, 0x0a, 0x06, 0x4c, 0x65, 0x6e, 0x67, 0x74, 0x68, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x06, 0x4c, 0x65, 0x6e, 0x67, 0x74, 0x68, 0x12, 0x14, 0x0a, 0x05, 0x57, 0x69, 0x64, 0x74, 0x68,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x57, 0x69, 0x64, 0x74, 0x68, 0x12, 0x14, 0x0a,
	0x05, 0x52, 0x75, 0x6c, 0x65, 0x72, 0x18, 0x05, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x52, 0x75,
	0x6c, 0x65, 0x72, 0x12, 0x14, 0x0a, 0x05, 0x54, 0x66, 0x6e, 0x75, 0x6d, 0x18, 0x06, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x05, 0x54, 0x66, 0x6e, 0x75, 0x6d, 0x12, 0x28, 0x0a, 0x0f, 0x61, 0x72, 0x74,
	0x69, 0x73, 0x74, 0x53, 0x68, 0x6f, 0x77, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x07, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0f, 0x61, 0x72, 0x74, 0x69, 0x73, 0x74, 0x53, 0x68, 0x6f, 0x77, 0x43, 0x6f,
	0x75, 0x6e, 0x74, 0x12, 0x20, 0x0a, 0x0b, 0x41, 0x72, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x54, 0x79,
	0x70, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0b, 0x41, 0x72, 0x74, 0x77, 0x6f, 0x72,
	0x6b, 0x54, 0x79, 0x70, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x41, 0x75, 0x74, 0x68, 0x49, 0x6d, 0x67,
	0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x41, 0x75, 0x74, 0x68, 0x49, 0x6d, 0x67, 0x12,
	0x1e, 0x0a, 0x0a, 0x44, 0x69, 0x67, 0x69, 0x41, 0x72, 0x74, 0x49, 0x6d, 0x67, 0x18, 0x0a, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0a, 0x44, 0x69, 0x67, 0x69, 0x41, 0x72, 0x74, 0x49, 0x6d, 0x67, 0x12,
	0x14, 0x0a, 0x05, 0x48, 0x64, 0x50, 0x69, 0x63, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05,
	0x48, 0x64, 0x50, 0x69, 0x63, 0x12, 0x18, 0x0a, 0x07, 0x57, 0x74, 0x53, 0x74, 0x61, 0x74, 0x65,
	0x18, 0x0c, 0x20, 0x01, 0x28, 0x05, 0x52, 0x07, 0x57, 0x74, 0x53, 0x74, 0x61, 0x74, 0x65, 0x12,
	0x28, 0x0a, 0x0f, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x63, 0x68, 0x61, 0x69, 0x6e, 0x73, 0x74, 0x61,
	0x74, 0x65, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0f, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x63,
	0x68, 0x61, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x74, 0x65, 0x12, 0x1e, 0x0a, 0x0a, 0x42, 0x61, 0x69,
	0x64, 0x75, 0x53, 0x74, 0x61, 0x74, 0x65, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0a, 0x42,
	0x61, 0x69, 0x64, 0x75, 0x53, 0x74, 0x61, 0x74, 0x65, 0x12, 0x1e, 0x0a, 0x0a, 0x43, 0x68, 0x61,
	0x69, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x65, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0a, 0x43,
	0x68, 0x61, 0x69, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x65, 0x12, 0x1e, 0x0a, 0x0a, 0x41, 0x72, 0x74,
	0x69, 0x73, 0x74, 0x55, 0x75, 0x69, 0x64, 0x18, 0x10, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x41,
	0x72, 0x74, 0x69, 0x73, 0x74, 0x55, 0x75, 0x69, 0x64, 0x12, 0x28, 0x0a, 0x0f, 0x42, 0x6c, 0x61,
	0x63, 0x6b, 0x4c, 0x69, 0x73, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x11, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x0f, 0x42, 0x6c, 0x61, 0x63, 0x6b, 0x4c, 0x69, 0x73, 0x74, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x12, 0x20, 0x0a, 0x0b, 0x41, 0x72, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x4e, 0x61,
	0x6d, 0x65, 0x18, 0x12, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x41, 0x72, 0x74, 0x77, 0x6f, 0x72,
	0x6b, 0x4e, 0x61, 0x6d, 0x65, 0x22, 0x3b, 0x0a, 0x15, 0x41, 0x72, 0x74, 0x69, 0x73, 0x74, 0x73,
	0x44, 0x61, 0x74, 0x61, 0x42, 0x79, 0x55, 0x75, 0x69, 0x64, 0x73, 0x52, 0x65, 0x71, 0x12, 0x22,
	0x0a, 0x0c, 0x41, 0x72, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x55, 0x75, 0x69, 0x64, 0x73, 0x18, 0x01,
	0x20, 0x03, 0x28, 0x09, 0x52, 0x0c, 0x41, 0x72, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x55, 0x75, 0x69,
	0x64, 0x73, 0x22, 0xbc, 0x01, 0x0a, 0x16, 0x41, 0x72, 0x74, 0x69, 0x73, 0x74, 0x73, 0x44, 0x61,
	0x74, 0x61, 0x42, 0x79, 0x55, 0x75, 0x69, 0x64, 0x73, 0x52, 0x65, 0x73, 0x70, 0x12, 0x38, 0x0a,
	0x04, 0x44, 0x61, 0x74, 0x61, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x24, 0x2e, 0x41, 0x72,
	0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2e, 0x41, 0x72, 0x74, 0x69, 0x73, 0x74, 0x73, 0x44, 0x61, 0x74,
	0x61, 0x42, 0x79, 0x55, 0x75, 0x69, 0x64, 0x73, 0x52, 0x65, 0x73, 0x70, 0x2e, 0x49, 0x6e, 0x66,
	0x6f, 0x52, 0x04, 0x44, 0x61, 0x74, 0x61, 0x1a, 0x68, 0x0a, 0x04, 0x49, 0x6e, 0x66, 0x6f, 0x12,
	0x1e, 0x0a, 0x0a, 0x41, 0x72, 0x74, 0x69, 0x73, 0x74, 0x55, 0x75, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0a, 0x41, 0x72, 0x74, 0x69, 0x73, 0x74, 0x55, 0x75, 0x69, 0x64, 0x12,
	0x1e, 0x0a, 0x0a, 0x41, 0x72, 0x74, 0x69, 0x73, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0a, 0x41, 0x72, 0x74, 0x69, 0x73, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x12,
	0x20, 0x0a, 0x0b, 0x41, 0x72, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x55, 0x75, 0x69, 0x64, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x41, 0x72, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x55, 0x75, 0x69,
	0x64, 0x32, 0xa1, 0x1b, 0x0a, 0x0c, 0x41, 0x72, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x51, 0x75, 0x65,
	0x72, 0x79, 0x12, 0x4a, 0x0a, 0x0b, 0x41, 0x72, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x4c, 0x69, 0x73,
	0x74, 0x12, 0x1b, 0x2e, 0x41, 0x72, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2e, 0x41, 0x72, 0x74, 0x77,
	0x6f, 0x72, 0x6b, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1c,
	0x2e, 0x41, 0x72, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2e, 0x41, 0x72, 0x74, 0x77, 0x6f, 0x72, 0x6b,
	0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x3d,
	0x0a, 0x0a, 0x44, 0x65, 0x6c, 0x41, 0x72, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x12, 0x15, 0x2e, 0x41,
	0x72, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2e, 0x44, 0x65, 0x6c, 0x41, 0x77, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x1a, 0x16, 0x2e, 0x41, 0x72, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2e, 0x44, 0x65,
	0x6c, 0x41, 0x77, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x4a, 0x0a,
	0x0b, 0x44, 0x65, 0x6c, 0x41, 0x75, 0x74, 0x68, 0x44, 0x61, 0x74, 0x61, 0x12, 0x1b, 0x2e, 0x41,
	0x72, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2e, 0x44, 0x65, 0x6c, 0x41, 0x75, 0x74, 0x68, 0x44, 0x61,
	0x74, 0x61, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1c, 0x2e, 0x41, 0x72, 0x74, 0x77,
	0x6f, 0x72, 0x6b, 0x2e, 0x44, 0x65, 0x6c, 0x41, 0x75, 0x74, 0x68, 0x44, 0x61, 0x74, 0x61, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x50, 0x0a, 0x0d, 0x44, 0x65, 0x6c,
	0x4d, 0x61, 0x72, 0x6b, 0x65, 0x74, 0x44, 0x61, 0x74, 0x61, 0x12, 0x1d, 0x2e, 0x41, 0x72, 0x74,
	0x77, 0x6f, 0x72, 0x6b, 0x2e, 0x44, 0x65, 0x6c, 0x4d, 0x61, 0x72, 0x6b, 0x65, 0x74, 0x44, 0x61,
	0x74, 0x61, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1e, 0x2e, 0x41, 0x72, 0x74, 0x77,
	0x6f, 0x72, 0x6b, 0x2e, 0x44, 0x65, 0x6c, 0x4d, 0x61, 0x72, 0x6b, 0x65, 0x74, 0x44, 0x61, 0x74,
	0x61, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x53, 0x0a, 0x0e, 0x44,
	0x65, 0x6c, 0x53, 0x74, 0x6f, 0x72, 0x61, 0x67, 0x65, 0x44, 0x61, 0x74, 0x61, 0x12, 0x1e, 0x2e,
	0x41, 0x72, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2e, 0x44, 0x65, 0x6c, 0x53, 0x74, 0x6f, 0x72, 0x61,
	0x67, 0x65, 0x44, 0x61, 0x74, 0x61, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1f, 0x2e,
	0x41, 0x72, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2e, 0x44, 0x65, 0x6c, 0x53, 0x74, 0x6f, 0x72, 0x61,
	0x67, 0x65, 0x44, 0x61, 0x74, 0x61, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00,
	0x12, 0x41, 0x0a, 0x08, 0x54, 0x61, 0x67, 0x73, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x18, 0x2e, 0x41,
	0x72, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2e, 0x54, 0x61, 0x67, 0x73, 0x4c, 0x69, 0x73, 0x74, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x19, 0x2e, 0x41, 0x72, 0x74, 0x77, 0x6f, 0x72, 0x6b,
	0x2e, 0x54, 0x61, 0x67, 0x73, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x22, 0x00, 0x12, 0x3e, 0x0a, 0x07, 0x43, 0x61, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x17,
	0x2e, 0x41, 0x72, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2e, 0x43, 0x61, 0x74, 0x4c, 0x69, 0x73, 0x74,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x18, 0x2e, 0x41, 0x72, 0x74, 0x77, 0x6f, 0x72,
	0x6b, 0x2e, 0x43, 0x61, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x22, 0x00, 0x12, 0x47, 0x0a, 0x0e, 0x49, 0x6d, 0x67, 0x4d, 0x61, 0x74, 0x63, 0x68, 0x42,
	0x79, 0x55, 0x75, 0x69, 0x64, 0x12, 0x18, 0x2e, 0x41, 0x72, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2e,
	0x49, 0x6d, 0x67, 0x4d, 0x61, 0x74, 0x63, 0x68, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a,
	0x19, 0x2e, 0x41, 0x72, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2e, 0x49, 0x6d, 0x67, 0x4d, 0x61, 0x74,
	0x63, 0x68, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x4a, 0x0a, 0x0b,
	0x42, 0x61, 0x74, 0x63, 0x68, 0x42, 0x69, 0x74, 0x4d, 0x61, 0x70, 0x12, 0x1b, 0x2e, 0x41, 0x72,
	0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2e, 0x42, 0x61, 0x74, 0x63, 0x68, 0x42, 0x69, 0x74, 0x4d, 0x61,
	0x70, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1c, 0x2e, 0x41, 0x72, 0x74, 0x77, 0x6f,
	0x72, 0x6b, 0x2e, 0x42, 0x61, 0x74, 0x63, 0x68, 0x42, 0x69, 0x74, 0x4d, 0x61, 0x70, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x59, 0x0a, 0x10, 0x43, 0x68, 0x65, 0x63,
	0x6b, 0x41, 0x72, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x20, 0x2e, 0x41,
	0x72, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2e, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x41, 0x72, 0x74, 0x77,
	0x6f, 0x72, 0x6b, 0x4e, 0x61, 0x6d, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x21,
	0x2e, 0x41, 0x72, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2e, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x41, 0x72,
	0x74, 0x77, 0x6f, 0x72, 0x6b, 0x4e, 0x61, 0x6d, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x22, 0x00, 0x12, 0x5c, 0x0a, 0x11, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x41, 0x72, 0x74, 0x77,
	0x6f, 0x72, 0x6b, 0x54, 0x66, 0x6e, 0x75, 0x6d, 0x12, 0x21, 0x2e, 0x41, 0x72, 0x74, 0x77, 0x6f,
	0x72, 0x6b, 0x2e, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x41, 0x72, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x54,
	0x66, 0x6e, 0x75, 0x6d, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x22, 0x2e, 0x41, 0x72,
	0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2e, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x41, 0x72, 0x74, 0x77, 0x6f,
	0x72, 0x6b, 0x54, 0x66, 0x6e, 0x75, 0x6d, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22,
	0x00, 0x12, 0x59, 0x0a, 0x10, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x54, 0x68, 0x69, 0x72, 0x64,
	0x50, 0x61, 0x72, 0x74, 0x79, 0x12, 0x20, 0x2e, 0x41, 0x72, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2e,
	0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x54, 0x68, 0x69, 0x72, 0x64, 0x50, 0x61, 0x72, 0x74, 0x79,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x21, 0x2e, 0x41, 0x72, 0x74, 0x77, 0x6f, 0x72,
	0x6b, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x54, 0x68, 0x69, 0x72, 0x64, 0x50, 0x61, 0x72,
	0x74, 0x79, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x50, 0x0a, 0x0d,
	0x44, 0x65, 0x6c, 0x54, 0x68, 0x69, 0x72, 0x64, 0x50, 0x61, 0x72, 0x74, 0x79, 0x12, 0x1d, 0x2e,
	0x41, 0x72, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2e, 0x44, 0x65, 0x6c, 0x54, 0x68, 0x69, 0x72, 0x64,
	0x50, 0x61, 0x72, 0x74, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1e, 0x2e, 0x41,
	0x72, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2e, 0x44, 0x65, 0x6c, 0x54, 0x68, 0x69, 0x72, 0x64, 0x50,
	0x61, 0x72, 0x74, 0x79, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x53,
	0x0a, 0x0e, 0x54, 0x68, 0x69, 0x72, 0x64, 0x50, 0x61, 0x72, 0x74, 0x79, 0x4c, 0x69, 0x73, 0x74,
	0x12, 0x1e, 0x2e, 0x41, 0x72, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2e, 0x54, 0x68, 0x69, 0x72, 0x64,
	0x50, 0x61, 0x72, 0x74, 0x79, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x1a, 0x1f, 0x2e, 0x41, 0x72, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2e, 0x54, 0x68, 0x69, 0x72, 0x64,
	0x50, 0x61, 0x72, 0x74, 0x79, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x22, 0x00, 0x12, 0x62, 0x0a, 0x13, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x41, 0x77, 0x53,
	0x74, 0x6f, 0x63, 0x6b, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x23, 0x2e, 0x41, 0x72, 0x74,
	0x77, 0x6f, 0x72, 0x6b, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x41, 0x77, 0x53, 0x74, 0x6f,
	0x63, 0x6b, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a,
	0x24, 0x2e, 0x41, 0x72, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x41, 0x77, 0x53, 0x74, 0x6f, 0x63, 0x6b, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x50, 0x0a, 0x0d, 0x53, 0x79, 0x6e, 0x63, 0x41,
	0x72, 0x74, 0x53, 0x68, 0x6f, 0x77, 0x49, 0x64, 0x12, 0x1d, 0x2e, 0x41, 0x72, 0x74, 0x77, 0x6f,
	0x72, 0x6b, 0x2e, 0x53, 0x79, 0x6e, 0x63, 0x41, 0x72, 0x74, 0x53, 0x68, 0x6f, 0x77, 0x49, 0x64,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1e, 0x2e, 0x41, 0x72, 0x74, 0x77, 0x6f, 0x72,
	0x6b, 0x2e, 0x53, 0x79, 0x6e, 0x63, 0x41, 0x72, 0x74, 0x53, 0x68, 0x6f, 0x77, 0x49, 0x64, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x44, 0x0a, 0x09, 0x53, 0x68, 0x65,
	0x6c, 0x66, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x19, 0x2e, 0x41, 0x72, 0x74, 0x77, 0x6f, 0x72, 0x6b,
	0x2e, 0x53, 0x68, 0x65, 0x6c, 0x66, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x1a, 0x1a, 0x2e, 0x41, 0x72, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2e, 0x53, 0x68, 0x65, 0x6c,
	0x66, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12,
	0x62, 0x0a, 0x13, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x43, 0x6f, 0x70, 0x79, 0x72, 0x69, 0x67,
	0x68, 0x74, 0x48, 0x61, 0x73, 0x68, 0x12, 0x23, 0x2e, 0x41, 0x72, 0x74, 0x77, 0x6f, 0x72, 0x6b,
	0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x43, 0x6f, 0x70, 0x79, 0x72, 0x69, 0x67, 0x68, 0x74,
	0x48, 0x61, 0x73, 0x68, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x24, 0x2e, 0x41, 0x72,
	0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x43, 0x6f, 0x70, 0x79,
	0x72, 0x69, 0x67, 0x68, 0x74, 0x48, 0x61, 0x73, 0x68, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x22, 0x00, 0x12, 0x50, 0x0a, 0x0d, 0x45, 0x78, 0x70, 0x6f, 0x72, 0x74, 0x41, 0x72, 0x74,
	0x77, 0x6f, 0x72, 0x6b, 0x12, 0x1d, 0x2e, 0x41, 0x72, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2e, 0x45,
	0x78, 0x70, 0x6f, 0x72, 0x74, 0x41, 0x72, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x1a, 0x1e, 0x2e, 0x41, 0x72, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2e, 0x45, 0x78,
	0x70, 0x6f, 0x72, 0x74, 0x41, 0x72, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x4a, 0x0a, 0x0b, 0x54, 0x61, 0x67, 0x49, 0x64, 0x4b, 0x76,
	0x4c, 0x69, 0x73, 0x74, 0x12, 0x1b, 0x2e, 0x41, 0x72, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2e, 0x54,
	0x61, 0x67, 0x49, 0x64, 0x4b, 0x76, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x1a, 0x1c, 0x2e, 0x41, 0x72, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2e, 0x54, 0x61, 0x67, 0x49,
	0x64, 0x4b, 0x76, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22,
	0x00, 0x12, 0x56, 0x0a, 0x0f, 0x45, 0x78, 0x70, 0x6f, 0x72, 0x74, 0x46, 0x69, 0x65, 0x6c, 0x64,
	0x4c, 0x69, 0x73, 0x74, 0x12, 0x1f, 0x2e, 0x41, 0x72, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2e, 0x45,
	0x78, 0x70, 0x6f, 0x72, 0x74, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x20, 0x2e, 0x41, 0x72, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2e,
	0x45, 0x78, 0x70, 0x6f, 0x72, 0x74, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x4c, 0x69, 0x73, 0x74, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x62, 0x0a, 0x13, 0x41, 0x72, 0x74,
	0x77, 0x6f, 0x72, 0x6b, 0x44, 0x61, 0x74, 0x61, 0x42, 0x79, 0x53, 0x68, 0x6f, 0x77, 0x49, 0x64,
	0x12, 0x23, 0x2e, 0x41, 0x72, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2e, 0x41, 0x72, 0x74, 0x77, 0x6f,
	0x72, 0x6b, 0x44, 0x61, 0x74, 0x61, 0x42, 0x79, 0x53, 0x68, 0x6f, 0x77, 0x49, 0x64, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x24, 0x2e, 0x41, 0x72, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2e,
	0x41, 0x72, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x44, 0x61, 0x74, 0x61, 0x42, 0x79, 0x53, 0x68, 0x6f,
	0x77, 0x49, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x39, 0x0a,
	0x08, 0x4d, 0x79, 0x41, 0x77, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x14, 0x2e, 0x41, 0x72, 0x74, 0x77,
	0x6f, 0x72, 0x6b, 0x2e, 0x4d, 0x79, 0x41, 0x77, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x1a,
	0x15, 0x2e, 0x41, 0x72, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2e, 0x4d, 0x79, 0x41, 0x77, 0x4c, 0x69,
	0x73, 0x74, 0x52, 0x65, 0x73, 0x70, 0x22, 0x00, 0x12, 0x5f, 0x0a, 0x12, 0x41, 0x72, 0x74, 0x77,
	0x6f, 0x72, 0x6b, 0x50, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x22,
	0x2e, 0x41, 0x72, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2e, 0x41, 0x72, 0x74, 0x77, 0x6f, 0x72, 0x6b,
	0x50, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x1a, 0x23, 0x2e, 0x41, 0x72, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2e, 0x41, 0x72, 0x74,
	0x77, 0x6f, 0x72, 0x6b, 0x50, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x4c, 0x69, 0x73, 0x74, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x3f, 0x0a, 0x0a, 0x56, 0x65, 0x72,
	0x69, 0x66, 0x79, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x16, 0x2e, 0x41, 0x72, 0x74, 0x77, 0x6f, 0x72,
	0x6b, 0x2e, 0x56, 0x65, 0x72, 0x69, 0x66, 0x79, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x1a,
	0x17, 0x2e, 0x41, 0x72, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2e, 0x56, 0x65, 0x72, 0x69, 0x66, 0x79,
	0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x70, 0x22, 0x00, 0x12, 0x42, 0x0a, 0x0b, 0x41, 0x72,
	0x74, 0x73, 0x68, 0x6f, 0x77, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x17, 0x2e, 0x41, 0x72, 0x74, 0x77,
	0x6f, 0x72, 0x6b, 0x2e, 0x41, 0x72, 0x74, 0x73, 0x68, 0x6f, 0x77, 0x4c, 0x69, 0x73, 0x74, 0x52,
	0x65, 0x71, 0x1a, 0x18, 0x2e, 0x41, 0x72, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2e, 0x41, 0x72, 0x74,
	0x73, 0x68, 0x6f, 0x77, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x70, 0x22, 0x00, 0x12, 0x57,
	0x0a, 0x12, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x56, 0x65, 0x72, 0x69, 0x66, 0x79, 0x53, 0x69, 0x6d,
	0x69, 0x6c, 0x61, 0x72, 0x12, 0x1e, 0x2e, 0x41, 0x72, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2e, 0x43,
	0x6f, 0x75, 0x6e, 0x74, 0x56, 0x65, 0x72, 0x69, 0x66, 0x79, 0x53, 0x69, 0x6d, 0x69, 0x6c, 0x61,
	0x72, 0x52, 0x65, 0x71, 0x1a, 0x1f, 0x2e, 0x41, 0x72, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2e, 0x43,
	0x6f, 0x75, 0x6e, 0x74, 0x56, 0x65, 0x72, 0x69, 0x66, 0x79, 0x53, 0x69, 0x6d, 0x69, 0x6c, 0x61,
	0x72, 0x52, 0x65, 0x73, 0x70, 0x22, 0x00, 0x12, 0x39, 0x0a, 0x08, 0x4f, 0x6e, 0x65, 0x51, 0x75,
	0x65, 0x72, 0x79, 0x12, 0x14, 0x2e, 0x41, 0x72, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2e, 0x4f, 0x6e,
	0x65, 0x51, 0x75, 0x65, 0x72, 0x79, 0x52, 0x65, 0x71, 0x1a, 0x15, 0x2e, 0x41, 0x72, 0x74, 0x77,
	0x6f, 0x72, 0x6b, 0x2e, 0x4f, 0x6e, 0x65, 0x51, 0x75, 0x65, 0x72, 0x79, 0x52, 0x65, 0x73, 0x70,
	0x22, 0x00, 0x12, 0x48, 0x0a, 0x0d, 0x47, 0x65, 0x74, 0x50, 0x61, 0x73, 0x73, 0x41, 0x72, 0x74,
	0x69, 0x73, 0x74, 0x12, 0x19, 0x2e, 0x41, 0x72, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2e, 0x47, 0x65,
	0x74, 0x50, 0x61, 0x73, 0x73, 0x41, 0x72, 0x74, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x1a, 0x1a,
	0x2e, 0x41, 0x72, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2e, 0x47, 0x65, 0x74, 0x50, 0x61, 0x73, 0x73,
	0x41, 0x72, 0x74, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x70, 0x22, 0x00, 0x12, 0x45, 0x0a, 0x0c,
	0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x41, 0x77, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x18, 0x2e, 0x41,
	0x72, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2e, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x41, 0x77, 0x4c,
	0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x1a, 0x19, 0x2e, 0x41, 0x72, 0x74, 0x77, 0x6f, 0x72, 0x6b,
	0x2e, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x41, 0x77, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73,
	0x70, 0x22, 0x00, 0x12, 0x4e, 0x0a, 0x0f, 0x43, 0x68, 0x61, 0x69, 0x6e, 0x49, 0x6e, 0x66, 0x6f,
	0x42, 0x79, 0x48, 0x61, 0x73, 0x68, 0x12, 0x1b, 0x2e, 0x41, 0x72, 0x74, 0x77, 0x6f, 0x72, 0x6b,
	0x2e, 0x43, 0x68, 0x61, 0x69, 0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x42, 0x79, 0x48, 0x61, 0x73, 0x68,
	0x52, 0x65, 0x71, 0x1a, 0x1c, 0x2e, 0x41, 0x72, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2e, 0x43, 0x68,
	0x61, 0x69, 0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x42, 0x79, 0x48, 0x61, 0x73, 0x68, 0x52, 0x65, 0x73,
	0x70, 0x22, 0x00, 0x12, 0x4b, 0x0a, 0x0e, 0x53, 0x74, 0x6f, 0x63, 0x6b, 0x4f, 0x75, 0x74, 0x41,
	0x72, 0x74, 0x69, 0x73, 0x74, 0x12, 0x1a, 0x2e, 0x41, 0x72, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2e,
	0x53, 0x74, 0x6f, 0x63, 0x6b, 0x4f, 0x75, 0x74, 0x41, 0x72, 0x74, 0x69, 0x73, 0x74, 0x52, 0x65,
	0x71, 0x1a, 0x1b, 0x2e, 0x41, 0x72, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2e, 0x53, 0x74, 0x6f, 0x63,
	0x6b, 0x4f, 0x75, 0x74, 0x41, 0x72, 0x74, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x70, 0x22, 0x00,
	0x12, 0x3c, 0x0a, 0x09, 0x43, 0x68, 0x61, 0x69, 0x6e, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x15, 0x2e,
	0x41, 0x72, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2e, 0x43, 0x68, 0x61, 0x69, 0x6e, 0x4c, 0x69, 0x73,
	0x74, 0x52, 0x65, 0x71, 0x1a, 0x16, 0x2e, 0x41, 0x72, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2e, 0x43,
	0x68, 0x61, 0x69, 0x6e, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x70, 0x22, 0x00, 0x12, 0x45,
	0x0a, 0x0d, 0x54, 0x6f, 0x74, 0x61, 0x6c, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x66, 0x65, 0x72, 0x12,
	0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75,
	0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x1a, 0x1a, 0x2e, 0x41, 0x72, 0x74, 0x77, 0x6f, 0x72,
	0x6b, 0x2e, 0x54, 0x6f, 0x74, 0x61, 0x6c, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x66, 0x65, 0x72, 0x52,
	0x65, 0x73, 0x70, 0x22, 0x00, 0x12, 0x64, 0x0a, 0x15, 0x47, 0x65, 0x74, 0x41, 0x72, 0x74, 0x77,
	0x6f, 0x72, 0x6b, 0x50, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x25,
	0x2e, 0x41, 0x72, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2e, 0x47, 0x65, 0x74, 0x41, 0x72, 0x74, 0x77,
	0x6f, 0x72, 0x6b, 0x50, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x22, 0x2e, 0x41, 0x72, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2e,
	0x47, 0x65, 0x74, 0x41, 0x72, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x50, 0x72, 0x6f, 0x66, 0x69, 0x6c,
	0x65, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x70, 0x22, 0x00, 0x12, 0x60, 0x0a, 0x15, 0x41,
	0x72, 0x74, 0x69, 0x73, 0x74, 0x42, 0x69, 0x6e, 0x64, 0x41, 0x72, 0x74, 0x77, 0x6f, 0x72, 0x6b,
	0x4c, 0x69, 0x73, 0x74, 0x12, 0x21, 0x2e, 0x41, 0x72, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2e, 0x41,
	0x72, 0x74, 0x69, 0x73, 0x74, 0x42, 0x69, 0x6e, 0x64, 0x41, 0x72, 0x74, 0x77, 0x6f, 0x72, 0x6b,
	0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x1a, 0x22, 0x2e, 0x41, 0x72, 0x74, 0x77, 0x6f, 0x72,
	0x6b, 0x2e, 0x41, 0x72, 0x74, 0x69, 0x73, 0x74, 0x42, 0x69, 0x6e, 0x64, 0x41, 0x72, 0x74, 0x77,
	0x6f, 0x72, 0x6b, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x70, 0x22, 0x00, 0x12, 0x6a, 0x0a,
	0x17, 0x47, 0x65, 0x74, 0x41, 0x72, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x43, 0x6f, 0x70, 0x79, 0x72,
	0x69, 0x67, 0x68, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x27, 0x2e, 0x41, 0x72, 0x74, 0x77, 0x6f,
	0x72, 0x6b, 0x2e, 0x47, 0x65, 0x74, 0x41, 0x72, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x43, 0x6f, 0x70,
	0x79, 0x72, 0x69, 0x67, 0x68, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x1a, 0x24, 0x2e, 0x41, 0x72, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2e, 0x47, 0x65, 0x74, 0x41,
	0x72, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x43, 0x6f, 0x70, 0x79, 0x72, 0x69, 0x67, 0x68, 0x74, 0x4c,
	0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x70, 0x22, 0x00, 0x12, 0x5a, 0x0a, 0x13, 0x47, 0x65, 0x74,
	0x41, 0x72, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x44, 0x61, 0x74, 0x61, 0x42, 0x61, 0x74, 0x63, 0x68,
	0x12, 0x1f, 0x2e, 0x41, 0x72, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2e, 0x47, 0x65, 0x74, 0x41, 0x72,
	0x74, 0x77, 0x6f, 0x72, 0x6b, 0x44, 0x61, 0x74, 0x61, 0x42, 0x61, 0x74, 0x63, 0x68, 0x52, 0x65,
	0x71, 0x1a, 0x20, 0x2e, 0x41, 0x72, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2e, 0x47, 0x65, 0x74, 0x41,
	0x72, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x44, 0x61, 0x74, 0x61, 0x42, 0x61, 0x74, 0x63, 0x68, 0x52,
	0x65, 0x73, 0x70, 0x22, 0x00, 0x12, 0x45, 0x0a, 0x0c, 0x42, 0x61, 0x74, 0x63, 0x68, 0x44, 0x63,
	0x69, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x18, 0x2e, 0x41, 0x72, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2e,
	0x42, 0x61, 0x74, 0x63, 0x68, 0x44, 0x63, 0x69, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x1a,
	0x19, 0x2e, 0x41, 0x72, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2e, 0x42, 0x61, 0x74, 0x63, 0x68, 0x44,
	0x63, 0x69, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x70, 0x22, 0x00, 0x12, 0x54, 0x0a, 0x11,
	0x41, 0x72, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x41, 0x72, 0x74, 0x69, 0x73, 0x74, 0x4c, 0x69, 0x73,
	0x74, 0x12, 0x1d, 0x2e, 0x41, 0x72, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2e, 0x41, 0x72, 0x74, 0x77,
	0x6f, 0x72, 0x6b, 0x41, 0x72, 0x74, 0x69, 0x73, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71,
	0x1a, 0x1e, 0x2e, 0x41, 0x72, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2e, 0x41, 0x72, 0x74, 0x77, 0x6f,
	0x72, 0x6b, 0x41, 0x72, 0x74, 0x69, 0x73, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x70,
	0x22, 0x00, 0x12, 0x42, 0x0a, 0x0b, 0x53, 0x74, 0x6f, 0x72, 0x61, 0x67, 0x65, 0x4c, 0x69, 0x73,
	0x74, 0x12, 0x17, 0x2e, 0x41, 0x72, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2e, 0x53, 0x74, 0x6f, 0x72,
	0x61, 0x67, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x1a, 0x18, 0x2e, 0x41, 0x72, 0x74,
	0x77, 0x6f, 0x72, 0x6b, 0x2e, 0x53, 0x74, 0x6f, 0x72, 0x61, 0x67, 0x65, 0x4c, 0x69, 0x73, 0x74,
	0x52, 0x65, 0x73, 0x70, 0x22, 0x00, 0x12, 0x54, 0x0a, 0x11, 0x53, 0x65, 0x63, 0x6f, 0x6e, 0x64,
	0x41, 0x72, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x1d, 0x2e, 0x41, 0x72,
	0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2e, 0x53, 0x65, 0x63, 0x6f, 0x6e, 0x64, 0x41, 0x72, 0x74, 0x77,
	0x6f, 0x72, 0x6b, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x1a, 0x1e, 0x2e, 0x41, 0x72, 0x74,
	0x77, 0x6f, 0x72, 0x6b, 0x2e, 0x53, 0x65, 0x63, 0x6f, 0x6e, 0x64, 0x41, 0x72, 0x74, 0x77, 0x6f,
	0x72, 0x6b, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x70, 0x22, 0x00, 0x12, 0x57, 0x0a, 0x12,
	0x41, 0x72, 0x74, 0x69, 0x73, 0x74, 0x73, 0x44, 0x61, 0x74, 0x61, 0x42, 0x79, 0x55, 0x75, 0x69,
	0x64, 0x73, 0x12, 0x1e, 0x2e, 0x41, 0x72, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2e, 0x41, 0x72, 0x74,
	0x69, 0x73, 0x74, 0x73, 0x44, 0x61, 0x74, 0x61, 0x42, 0x79, 0x55, 0x75, 0x69, 0x64, 0x73, 0x52,
	0x65, 0x71, 0x1a, 0x1f, 0x2e, 0x41, 0x72, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2e, 0x41, 0x72, 0x74,
	0x69, 0x73, 0x74, 0x73, 0x44, 0x61, 0x74, 0x61, 0x42, 0x79, 0x55, 0x75, 0x69, 0x64, 0x73, 0x52,
	0x65, 0x73, 0x70, 0x22, 0x00, 0x42, 0x1f, 0x5a, 0x1d, 0x2e, 0x2f, 0x61, 0x72, 0x74, 0x77, 0x6f,
	0x72, 0x6b, 0x5f, 0x71, 0x75, 0x65, 0x72, 0x79, 0x3b, 0x61, 0x72, 0x74, 0x77, 0x6f, 0x72, 0x6b,
	0x5f, 0x71, 0x75, 0x65, 0x72, 0x79, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_pb_artwork_query_proto_rawDescOnce sync.Once
	file_pb_artwork_query_proto_rawDescData = file_pb_artwork_query_proto_rawDesc
)

func file_pb_artwork_query_proto_rawDescGZIP() []byte {
	file_pb_artwork_query_proto_rawDescOnce.Do(func() {
		file_pb_artwork_query_proto_rawDescData = protoimpl.X.CompressGZIP(file_pb_artwork_query_proto_rawDescData)
	})
	return file_pb_artwork_query_proto_rawDescData
}

var file_pb_artwork_query_proto_msgTypes = make([]protoimpl.MessageInfo, 119)
var file_pb_artwork_query_proto_goTypes = []any{
	(*ArtworkListRequest)(nil),                  // 0: Artwork.ArtworkListRequest
	(*ArtworkListResponse)(nil),                 // 1: Artwork.ArtworkListResponse
	(*DelAwRequest)(nil),                        // 2: Artwork.DelAwRequest
	(*DelAwResponse)(nil),                       // 3: Artwork.DelAwResponse
	(*DelAuthDataRequest)(nil),                  // 4: Artwork.DelAuthDataRequest
	(*DelAuthDataResponse)(nil),                 // 5: Artwork.DelAuthDataResponse
	(*DelMarketDataRequest)(nil),                // 6: Artwork.DelMarketDataRequest
	(*DelMarketDataResponse)(nil),               // 7: Artwork.DelMarketDataResponse
	(*DelStorageDataRequest)(nil),               // 8: Artwork.DelStorageDataRequest
	(*DelStorageDataResponse)(nil),              // 9: Artwork.DelStorageDataResponse
	(*TagsListRequest)(nil),                     // 10: Artwork.TagsListRequest
	(*TagsData)(nil),                            // 11: Artwork.TagsData
	(*TagsListResponse)(nil),                    // 12: Artwork.TagsListResponse
	(*CatListRequest)(nil),                      // 13: Artwork.CatListRequest
	(*CatListResponse)(nil),                     // 14: Artwork.CatListResponse
	(*ImgMatchRequest)(nil),                     // 15: Artwork.ImgMatchRequest
	(*ImgMatchResponse)(nil),                    // 16: Artwork.ImgMatchResponse
	(*BatchBitMapRequest)(nil),                  // 17: Artwork.BatchBitMapRequest
	(*BatchBitMapResponse)(nil),                 // 18: Artwork.BatchBitMapResponse
	(*CheckArtworkNameRequest)(nil),             // 19: Artwork.CheckArtworkNameRequest
	(*CheckArtworkNameResponse)(nil),            // 20: Artwork.CheckArtworkNameResponse
	(*CheckArtworkTfnumRequest)(nil),            // 21: Artwork.CheckArtworkTfnumRequest
	(*CheckArtworkTfnumResponse)(nil),           // 22: Artwork.CheckArtworkTfnumResponse
	(*UpdateThirdPartyRequest)(nil),             // 23: Artwork.UpdateThirdPartyRequest
	(*UpdateThirdPartyResponse)(nil),            // 24: Artwork.UpdateThirdPartyResponse
	(*DelThirdPartyRequest)(nil),                // 25: Artwork.DelThirdPartyRequest
	(*DelThirdPartyResponse)(nil),               // 26: Artwork.DelThirdPartyResponse
	(*ThirdPartyListRequest)(nil),               // 27: Artwork.ThirdPartyListRequest
	(*ThirdPartyListResponse)(nil),              // 28: Artwork.ThirdPartyListResponse
	(*UpdateAwStockStatusRequest)(nil),          // 29: Artwork.UpdateAwStockStatusRequest
	(*UpdateAwStockStatusResponse)(nil),         // 30: Artwork.UpdateAwStockStatusResponse
	(*SyncArtShowIdRequest)(nil),                // 31: Artwork.SyncArtShowIdRequest
	(*SyncArtShowIdResponse)(nil),               // 32: Artwork.SyncArtShowIdResponse
	(*ShelfListRequest)(nil),                    // 33: Artwork.ShelfListRequest
	(*ShelfListResponse)(nil),                   // 34: Artwork.ShelfListResponse
	(*UpdateCopyrightHashRequest)(nil),          // 35: Artwork.UpdateCopyrightHashRequest
	(*UpdateCopyrightHashResponse)(nil),         // 36: Artwork.UpdateCopyrightHashResponse
	(*ExportArtworkRequest)(nil),                // 37: Artwork.ExportArtworkRequest
	(*ExportArtworkResponse)(nil),               // 38: Artwork.ExportArtworkResponse
	(*TagIdKvListRequest)(nil),                  // 39: Artwork.TagIdKvListRequest
	(*TagIdKvListResponse)(nil),                 // 40: Artwork.TagIdKvListResponse
	(*ExportFieldListRequest)(nil),              // 41: Artwork.ExportFieldListRequest
	(*ExportFieldListResponse)(nil),             // 42: Artwork.ExportFieldListResponse
	(*ArtworkDataByShowIdRequest)(nil),          // 43: Artwork.ArtworkDataByShowIdRequest
	(*ArtworkDataByShowIdResponse)(nil),         // 44: Artwork.ArtworkDataByShowIdResponse
	(*MyAwListReq)(nil),                         // 45: Artwork.MyAwListReq
	(*MyAwListResp)(nil),                        // 46: Artwork.MyAwListResp
	(*PageInfo)(nil),                            // 47: Artwork.PageInfo
	(*ArtworkPreviewListRequest)(nil),           // 48: Artwork.ArtworkPreviewListRequest
	(*ArtworkPreviewListResponse)(nil),          // 49: Artwork.ArtworkPreviewListResponse
	(*ArtworkPreviewResponse)(nil),              // 50: Artwork.ArtworkPreviewResponse
	(*VerifyListReq)(nil),                       // 51: Artwork.VerifyListReq
	(*VerifyListResp)(nil),                      // 52: Artwork.VerifyListResp
	(*ArtshowListReq)(nil),                      // 53: Artwork.ArtshowListReq
	(*ArtshowListResp)(nil),                     // 54: Artwork.ArtshowListResp
	(*CountVerifySimilarReq)(nil),               // 55: Artwork.CountVerifySimilarReq
	(*CountVerifySimilarResp)(nil),              // 56: Artwork.CountVerifySimilarResp
	(*OneQueryReq)(nil),                         // 57: Artwork.OneQueryReq
	(*OneQueryResp)(nil),                        // 58: Artwork.OneQueryResp
	(*GetPassArtistReq)(nil),                    // 59: Artwork.GetPassArtistReq
	(*GetPassArtistResp)(nil),                   // 60: Artwork.GetPassArtistResp
	(*FilterAwListReq)(nil),                     // 61: Artwork.FilterAwListReq
	(*FilterAwListResp)(nil),                    // 62: Artwork.FilterAwListResp
	(*ChainInfoByHashReq)(nil),                  // 63: Artwork.ChainInfoByHashReq
	(*ChainInfoByHashResp)(nil),                 // 64: Artwork.ChainInfoByHashResp
	(*StockOutArtistReq)(nil),                   // 65: Artwork.StockOutArtistReq
	(*StockOutArtistResp)(nil),                  // 66: Artwork.StockOutArtistResp
	(*ChainListReq)(nil),                        // 67: Artwork.ChainListReq
	(*ChainListResp)(nil),                       // 68: Artwork.ChainListResp
	(*TotalTransferResp)(nil),                   // 69: Artwork.TotalTransferResp
	(*ArtworkProfileData)(nil),                  // 70: Artwork.ArtworkProfileData
	(*GetArtworkProfileListRequest)(nil),        // 71: Artwork.GetArtworkProfileListRequest
	(*GetArtworkProfileListResp)(nil),           // 72: Artwork.GetArtworkProfileListResp
	(*ArtistBindArtworkListReq)(nil),            // 73: Artwork.ArtistBindArtworkListReq
	(*ArtistBindArtworkListResp)(nil),           // 74: Artwork.ArtistBindArtworkListResp
	(*ArtworkCopyrightData)(nil),                // 75: Artwork.ArtworkCopyrightData
	(*GetArtworkCopyrightListRequest)(nil),      // 76: Artwork.GetArtworkCopyrightListRequest
	(*GetArtworkCopyrightListResp)(nil),         // 77: Artwork.GetArtworkCopyrightListResp
	(*GetArtworkDataBatchReq)(nil),              // 78: Artwork.GetArtworkDataBatchReq
	(*GetArtworkDataBatchResp)(nil),             // 79: Artwork.GetArtworkDataBatchResp
	(*BatchDciListReq)(nil),                     // 80: Artwork.BatchDciListReq
	(*BatchDciListResp)(nil),                    // 81: Artwork.BatchDciListResp
	(*ArtworkArtistListReq)(nil),                // 82: Artwork.ArtworkArtistListReq
	(*ArtworkArtistListResp)(nil),               // 83: Artwork.ArtworkArtistListResp
	(*StorageListReq)(nil),                      // 84: Artwork.StorageListReq
	(*StorageListResp)(nil),                     // 85: Artwork.StorageListResp
	(*SecondArtworkListReq)(nil),                // 86: Artwork.SecondArtworkListReq
	(*SecondArtworkListResp)(nil),               // 87: Artwork.SecondArtworkListResp
	(*ArtistsDataByUuidsReq)(nil),               // 88: Artwork.ArtistsDataByUuidsReq
	(*ArtistsDataByUuidsResp)(nil),              // 89: Artwork.ArtistsDataByUuidsResp
	(*ArtworkListResponse_Info)(nil),            // 90: Artwork.ArtworkListResponse.Info
	(*TagsData_TagsInfo)(nil),                   // 91: Artwork.TagsData.TagsInfo
	(*CatListResponse_CatInfo)(nil),             // 92: Artwork.CatListResponse.CatInfo
	(*BatchBitMapRequest_BitInfo)(nil),          // 93: Artwork.BatchBitMapRequest.BitInfo
	(*SyncArtShowIdRequestInfo)(nil),            // 94: Artwork.SyncArtShowIdRequest.info
	(*ShelfListResponse_ShelfInfo)(nil),         // 95: Artwork.ShelfListResponse.ShelfInfo
	(*ExportArtworkResponse_Info)(nil),          // 96: Artwork.ExportArtworkResponse.Info
	nil,                                         // 97: Artwork.TagIdKvListResponse.InfoEntry
	(*ExportFieldListResponse_Info)(nil),        // 98: Artwork.ExportFieldListResponse.Info
	(*ArtworkDataByShowIdResponse_Info)(nil),    // 99: Artwork.ArtworkDataByShowIdResponse.Info
	(*MyAwListResp_Info)(nil),                   // 100: Artwork.MyAwListResp.Info
	(*VerifyListResp_RateImg)(nil),              // 101: Artwork.VerifyListResp.RateImg
	(*VerifyListResp_Info)(nil),                 // 102: Artwork.VerifyListResp.Info
	nil,                                         // 103: Artwork.VerifyListResp.DataEntry
	(*VerifyListResp_RateImg_Info)(nil),         // 104: Artwork.VerifyListResp.RateImg.Info
	nil,                                         // 105: Artwork.VerifyListResp.Info.BmImgsEntry
	(*ArtshowListResp_Info)(nil),                // 106: Artwork.ArtshowListResp.Info
	(*ArtshowListResp_Info_InviteInfo)(nil),     // 107: Artwork.ArtshowListResp.Info.InviteInfo
	(*OneQueryResp_Info)(nil),                   // 108: Artwork.OneQueryResp.Info
	(*FilterAwListResp_Info)(nil),               // 109: Artwork.FilterAwListResp.Info
	(*ChainInfoByHashResp_Info)(nil),            // 110: Artwork.ChainInfoByHashResp.Info
	(*ChainListResp_Info)(nil),                  // 111: Artwork.ChainListResp.Info
	(*ArtistBindArtworkListResp_Info)(nil),      // 112: Artwork.ArtistBindArtworkListResp.Info
	(*GetArtworkDataBatchResp_ArtworkInfo)(nil), // 113: Artwork.GetArtworkDataBatchResp.ArtworkInfo
	(*BatchDciListResp_Info)(nil),               // 114: Artwork.BatchDciListResp.Info
	(*ArtworkArtistListResp_Info)(nil),          // 115: Artwork.ArtworkArtistListResp.Info
	(*StorageListResp_Info)(nil),                // 116: Artwork.StorageListResp.Info
	(*SecondArtworkListResp_Info)(nil),          // 117: Artwork.SecondArtworkListResp.Info
	(*ArtistsDataByUuidsResp_Info)(nil),         // 118: Artwork.ArtistsDataByUuidsResp.Info
	(*wrapperspb.Int32Value)(nil),               // 119: google.protobuf.Int32Value
	(*emptypb.Empty)(nil),                       // 120: google.protobuf.Empty
}
var file_pb_artwork_query_proto_depIdxs = []int32{
	119, // 0: Artwork.ArtworkListRequest.StorageStatus:type_name -> google.protobuf.Int32Value
	119, // 1: Artwork.ArtworkListRequest.SaleStatus:type_name -> google.protobuf.Int32Value
	119, // 2: Artwork.ArtworkListRequest.FilterStatus:type_name -> google.protobuf.Int32Value
	90,  // 3: Artwork.ArtworkListResponse.Data:type_name -> Artwork.ArtworkListResponse.Info
	91,  // 4: Artwork.TagsData.TagsFirst:type_name -> Artwork.TagsData.TagsInfo
	91,  // 5: Artwork.TagsData.List:type_name -> Artwork.TagsData.TagsInfo
	11,  // 6: Artwork.TagsListResponse.TagsData:type_name -> Artwork.TagsData
	92,  // 7: Artwork.CatListResponse.Data:type_name -> Artwork.CatListResponse.CatInfo
	93,  // 8: Artwork.BatchBitMapRequest.BitData:type_name -> Artwork.BatchBitMapRequest.BitInfo
	94,  // 9: Artwork.SyncArtShowIdRequest.Data:type_name -> Artwork.SyncArtShowIdRequest.info
	95,  // 10: Artwork.ShelfListResponse.Data:type_name -> Artwork.ShelfListResponse.ShelfInfo
	96,  // 11: Artwork.ExportArtworkResponse.Data:type_name -> Artwork.ExportArtworkResponse.Info
	97,  // 12: Artwork.TagIdKvListResponse.Info:type_name -> Artwork.TagIdKvListResponse.InfoEntry
	98,  // 13: Artwork.ExportFieldListResponse.Data:type_name -> Artwork.ExportFieldListResponse.Info
	99,  // 14: Artwork.ArtworkDataByShowIdResponse.Data:type_name -> Artwork.ArtworkDataByShowIdResponse.Info
	100, // 15: Artwork.MyAwListResp.Data:type_name -> Artwork.MyAwListResp.Info
	50,  // 16: Artwork.ArtworkPreviewListResponse.data:type_name -> Artwork.ArtworkPreviewResponse
	47,  // 17: Artwork.ArtworkPreviewListResponse.page:type_name -> Artwork.PageInfo
	103, // 18: Artwork.VerifyListResp.Data:type_name -> Artwork.VerifyListResp.DataEntry
	106, // 19: Artwork.ArtshowListResp.Data:type_name -> Artwork.ArtshowListResp.Info
	108, // 20: Artwork.OneQueryResp.Data:type_name -> Artwork.OneQueryResp.Info
	109, // 21: Artwork.FilterAwListResp.Data:type_name -> Artwork.FilterAwListResp.Info
	110, // 22: Artwork.ChainInfoByHashResp.Data:type_name -> Artwork.ChainInfoByHashResp.Info
	111, // 23: Artwork.ChainListResp.Data:type_name -> Artwork.ChainListResp.Info
	70,  // 24: Artwork.GetArtworkProfileListRequest.query:type_name -> Artwork.ArtworkProfileData
	70,  // 25: Artwork.GetArtworkProfileListResp.list:type_name -> Artwork.ArtworkProfileData
	112, // 26: Artwork.ArtistBindArtworkListResp.Data:type_name -> Artwork.ArtistBindArtworkListResp.Info
	75,  // 27: Artwork.GetArtworkCopyrightListRequest.query:type_name -> Artwork.ArtworkCopyrightData
	75,  // 28: Artwork.GetArtworkCopyrightListResp.list:type_name -> Artwork.ArtworkCopyrightData
	113, // 29: Artwork.GetArtworkDataBatchResp.Data:type_name -> Artwork.GetArtworkDataBatchResp.ArtworkInfo
	114, // 30: Artwork.BatchDciListResp.Data:type_name -> Artwork.BatchDciListResp.Info
	115, // 31: Artwork.ArtworkArtistListResp.Data:type_name -> Artwork.ArtworkArtistListResp.Info
	116, // 32: Artwork.StorageListResp.Data:type_name -> Artwork.StorageListResp.Info
	117, // 33: Artwork.SecondArtworkListResp.Data:type_name -> Artwork.SecondArtworkListResp.Info
	118, // 34: Artwork.ArtistsDataByUuidsResp.Data:type_name -> Artwork.ArtistsDataByUuidsResp.Info
	104, // 35: Artwork.VerifyListResp.RateImg.Data:type_name -> Artwork.VerifyListResp.RateImg.Info
	105, // 36: Artwork.VerifyListResp.Info.BmImgs:type_name -> Artwork.VerifyListResp.Info.BmImgsEntry
	102, // 37: Artwork.VerifyListResp.DataEntry.value:type_name -> Artwork.VerifyListResp.Info
	101, // 38: Artwork.VerifyListResp.Info.BmImgsEntry.value:type_name -> Artwork.VerifyListResp.RateImg
	107, // 39: Artwork.ArtshowListResp.Info.InviteData:type_name -> Artwork.ArtshowListResp.Info.InviteInfo
	0,   // 40: Artwork.ArtworkQuery.ArtworkList:input_type -> Artwork.ArtworkListRequest
	2,   // 41: Artwork.ArtworkQuery.DelArtwork:input_type -> Artwork.DelAwRequest
	4,   // 42: Artwork.ArtworkQuery.DelAuthData:input_type -> Artwork.DelAuthDataRequest
	6,   // 43: Artwork.ArtworkQuery.DelMarketData:input_type -> Artwork.DelMarketDataRequest
	8,   // 44: Artwork.ArtworkQuery.DelStorageData:input_type -> Artwork.DelStorageDataRequest
	10,  // 45: Artwork.ArtworkQuery.TagsList:input_type -> Artwork.TagsListRequest
	13,  // 46: Artwork.ArtworkQuery.CatList:input_type -> Artwork.CatListRequest
	15,  // 47: Artwork.ArtworkQuery.ImgMatchByUuid:input_type -> Artwork.ImgMatchRequest
	17,  // 48: Artwork.ArtworkQuery.BatchBitMap:input_type -> Artwork.BatchBitMapRequest
	19,  // 49: Artwork.ArtworkQuery.CheckArtworkName:input_type -> Artwork.CheckArtworkNameRequest
	21,  // 50: Artwork.ArtworkQuery.CheckArtworkTfnum:input_type -> Artwork.CheckArtworkTfnumRequest
	23,  // 51: Artwork.ArtworkQuery.UpdateThirdParty:input_type -> Artwork.UpdateThirdPartyRequest
	25,  // 52: Artwork.ArtworkQuery.DelThirdParty:input_type -> Artwork.DelThirdPartyRequest
	27,  // 53: Artwork.ArtworkQuery.ThirdPartyList:input_type -> Artwork.ThirdPartyListRequest
	29,  // 54: Artwork.ArtworkQuery.UpdateAwStockStatus:input_type -> Artwork.UpdateAwStockStatusRequest
	31,  // 55: Artwork.ArtworkQuery.SyncArtShowId:input_type -> Artwork.SyncArtShowIdRequest
	33,  // 56: Artwork.ArtworkQuery.ShelfList:input_type -> Artwork.ShelfListRequest
	35,  // 57: Artwork.ArtworkQuery.UpdateCopyrightHash:input_type -> Artwork.UpdateCopyrightHashRequest
	37,  // 58: Artwork.ArtworkQuery.ExportArtwork:input_type -> Artwork.ExportArtworkRequest
	39,  // 59: Artwork.ArtworkQuery.TagIdKvList:input_type -> Artwork.TagIdKvListRequest
	41,  // 60: Artwork.ArtworkQuery.ExportFieldList:input_type -> Artwork.ExportFieldListRequest
	43,  // 61: Artwork.ArtworkQuery.ArtworkDataByShowId:input_type -> Artwork.ArtworkDataByShowIdRequest
	45,  // 62: Artwork.ArtworkQuery.MyAwList:input_type -> Artwork.MyAwListReq
	48,  // 63: Artwork.ArtworkQuery.ArtworkPreviewList:input_type -> Artwork.ArtworkPreviewListRequest
	51,  // 64: Artwork.ArtworkQuery.VerifyList:input_type -> Artwork.VerifyListReq
	53,  // 65: Artwork.ArtworkQuery.ArtshowList:input_type -> Artwork.ArtshowListReq
	55,  // 66: Artwork.ArtworkQuery.CountVerifySimilar:input_type -> Artwork.CountVerifySimilarReq
	57,  // 67: Artwork.ArtworkQuery.OneQuery:input_type -> Artwork.OneQueryReq
	59,  // 68: Artwork.ArtworkQuery.GetPassArtist:input_type -> Artwork.GetPassArtistReq
	61,  // 69: Artwork.ArtworkQuery.FilterAwList:input_type -> Artwork.FilterAwListReq
	63,  // 70: Artwork.ArtworkQuery.ChainInfoByHash:input_type -> Artwork.ChainInfoByHashReq
	65,  // 71: Artwork.ArtworkQuery.StockOutArtist:input_type -> Artwork.StockOutArtistReq
	67,  // 72: Artwork.ArtworkQuery.ChainList:input_type -> Artwork.ChainListReq
	120, // 73: Artwork.ArtworkQuery.TotalTransfer:input_type -> google.protobuf.Empty
	71,  // 74: Artwork.ArtworkQuery.GetArtworkProfileList:input_type -> Artwork.GetArtworkProfileListRequest
	73,  // 75: Artwork.ArtworkQuery.ArtistBindArtworkList:input_type -> Artwork.ArtistBindArtworkListReq
	76,  // 76: Artwork.ArtworkQuery.GetArtworkCopyrightList:input_type -> Artwork.GetArtworkCopyrightListRequest
	78,  // 77: Artwork.ArtworkQuery.GetArtworkDataBatch:input_type -> Artwork.GetArtworkDataBatchReq
	80,  // 78: Artwork.ArtworkQuery.BatchDciList:input_type -> Artwork.BatchDciListReq
	82,  // 79: Artwork.ArtworkQuery.ArtworkArtistList:input_type -> Artwork.ArtworkArtistListReq
	84,  // 80: Artwork.ArtworkQuery.StorageList:input_type -> Artwork.StorageListReq
	86,  // 81: Artwork.ArtworkQuery.SecondArtworkList:input_type -> Artwork.SecondArtworkListReq
	88,  // 82: Artwork.ArtworkQuery.ArtistsDataByUuids:input_type -> Artwork.ArtistsDataByUuidsReq
	1,   // 83: Artwork.ArtworkQuery.ArtworkList:output_type -> Artwork.ArtworkListResponse
	3,   // 84: Artwork.ArtworkQuery.DelArtwork:output_type -> Artwork.DelAwResponse
	5,   // 85: Artwork.ArtworkQuery.DelAuthData:output_type -> Artwork.DelAuthDataResponse
	7,   // 86: Artwork.ArtworkQuery.DelMarketData:output_type -> Artwork.DelMarketDataResponse
	9,   // 87: Artwork.ArtworkQuery.DelStorageData:output_type -> Artwork.DelStorageDataResponse
	12,  // 88: Artwork.ArtworkQuery.TagsList:output_type -> Artwork.TagsListResponse
	14,  // 89: Artwork.ArtworkQuery.CatList:output_type -> Artwork.CatListResponse
	16,  // 90: Artwork.ArtworkQuery.ImgMatchByUuid:output_type -> Artwork.ImgMatchResponse
	18,  // 91: Artwork.ArtworkQuery.BatchBitMap:output_type -> Artwork.BatchBitMapResponse
	20,  // 92: Artwork.ArtworkQuery.CheckArtworkName:output_type -> Artwork.CheckArtworkNameResponse
	22,  // 93: Artwork.ArtworkQuery.CheckArtworkTfnum:output_type -> Artwork.CheckArtworkTfnumResponse
	24,  // 94: Artwork.ArtworkQuery.UpdateThirdParty:output_type -> Artwork.UpdateThirdPartyResponse
	26,  // 95: Artwork.ArtworkQuery.DelThirdParty:output_type -> Artwork.DelThirdPartyResponse
	28,  // 96: Artwork.ArtworkQuery.ThirdPartyList:output_type -> Artwork.ThirdPartyListResponse
	30,  // 97: Artwork.ArtworkQuery.UpdateAwStockStatus:output_type -> Artwork.UpdateAwStockStatusResponse
	32,  // 98: Artwork.ArtworkQuery.SyncArtShowId:output_type -> Artwork.SyncArtShowIdResponse
	34,  // 99: Artwork.ArtworkQuery.ShelfList:output_type -> Artwork.ShelfListResponse
	36,  // 100: Artwork.ArtworkQuery.UpdateCopyrightHash:output_type -> Artwork.UpdateCopyrightHashResponse
	38,  // 101: Artwork.ArtworkQuery.ExportArtwork:output_type -> Artwork.ExportArtworkResponse
	40,  // 102: Artwork.ArtworkQuery.TagIdKvList:output_type -> Artwork.TagIdKvListResponse
	42,  // 103: Artwork.ArtworkQuery.ExportFieldList:output_type -> Artwork.ExportFieldListResponse
	44,  // 104: Artwork.ArtworkQuery.ArtworkDataByShowId:output_type -> Artwork.ArtworkDataByShowIdResponse
	46,  // 105: Artwork.ArtworkQuery.MyAwList:output_type -> Artwork.MyAwListResp
	49,  // 106: Artwork.ArtworkQuery.ArtworkPreviewList:output_type -> Artwork.ArtworkPreviewListResponse
	52,  // 107: Artwork.ArtworkQuery.VerifyList:output_type -> Artwork.VerifyListResp
	54,  // 108: Artwork.ArtworkQuery.ArtshowList:output_type -> Artwork.ArtshowListResp
	56,  // 109: Artwork.ArtworkQuery.CountVerifySimilar:output_type -> Artwork.CountVerifySimilarResp
	58,  // 110: Artwork.ArtworkQuery.OneQuery:output_type -> Artwork.OneQueryResp
	60,  // 111: Artwork.ArtworkQuery.GetPassArtist:output_type -> Artwork.GetPassArtistResp
	62,  // 112: Artwork.ArtworkQuery.FilterAwList:output_type -> Artwork.FilterAwListResp
	64,  // 113: Artwork.ArtworkQuery.ChainInfoByHash:output_type -> Artwork.ChainInfoByHashResp
	66,  // 114: Artwork.ArtworkQuery.StockOutArtist:output_type -> Artwork.StockOutArtistResp
	68,  // 115: Artwork.ArtworkQuery.ChainList:output_type -> Artwork.ChainListResp
	69,  // 116: Artwork.ArtworkQuery.TotalTransfer:output_type -> Artwork.TotalTransferResp
	72,  // 117: Artwork.ArtworkQuery.GetArtworkProfileList:output_type -> Artwork.GetArtworkProfileListResp
	74,  // 118: Artwork.ArtworkQuery.ArtistBindArtworkList:output_type -> Artwork.ArtistBindArtworkListResp
	77,  // 119: Artwork.ArtworkQuery.GetArtworkCopyrightList:output_type -> Artwork.GetArtworkCopyrightListResp
	79,  // 120: Artwork.ArtworkQuery.GetArtworkDataBatch:output_type -> Artwork.GetArtworkDataBatchResp
	81,  // 121: Artwork.ArtworkQuery.BatchDciList:output_type -> Artwork.BatchDciListResp
	83,  // 122: Artwork.ArtworkQuery.ArtworkArtistList:output_type -> Artwork.ArtworkArtistListResp
	85,  // 123: Artwork.ArtworkQuery.StorageList:output_type -> Artwork.StorageListResp
	87,  // 124: Artwork.ArtworkQuery.SecondArtworkList:output_type -> Artwork.SecondArtworkListResp
	89,  // 125: Artwork.ArtworkQuery.ArtistsDataByUuids:output_type -> Artwork.ArtistsDataByUuidsResp
	83,  // [83:126] is the sub-list for method output_type
	40,  // [40:83] is the sub-list for method input_type
	40,  // [40:40] is the sub-list for extension type_name
	40,  // [40:40] is the sub-list for extension extendee
	0,   // [0:40] is the sub-list for field type_name
}

func init() { file_pb_artwork_query_proto_init() }
func file_pb_artwork_query_proto_init() {
	if File_pb_artwork_query_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_pb_artwork_query_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   119,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_pb_artwork_query_proto_goTypes,
		DependencyIndexes: file_pb_artwork_query_proto_depIdxs,
		MessageInfos:      file_pb_artwork_query_proto_msgTypes,
	}.Build()
	File_pb_artwork_query_proto = out.File
	file_pb_artwork_query_proto_rawDesc = nil
	file_pb_artwork_query_proto_goTypes = nil
	file_pb_artwork_query_proto_depIdxs = nil
}
