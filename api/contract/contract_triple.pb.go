// Code generated by protoc-gen-go-triple. DO NOT EDIT.
// versions:
// - protoc-gen-go-triple v1.0.8
// - protoc             v3.21.1
// source: contract.proto

package contract

import (
	context "context"
	protocol "dubbo.apache.org/dubbo-go/v3/protocol"
	dubbo3 "dubbo.apache.org/dubbo-go/v3/protocol/dubbo3"
	invocation "dubbo.apache.org/dubbo-go/v3/protocol/invocation"
	grpc_go "github.com/dubbogo/grpc-go"
	codes "github.com/dubbogo/grpc-go/codes"
	metadata "github.com/dubbogo/grpc-go/metadata"
	status "github.com/dubbogo/grpc-go/status"
	common "github.com/dubbogo/triple/pkg/common"
	constant "github.com/dubbogo/triple/pkg/common/constant"
	triple "github.com/dubbogo/triple/pkg/triple"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc_go.SupportPackageIsVersion7

// ContractClient is the client API for Contract service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type ContractClient interface {
	RegisterPerson(ctx context.Context, in *RegisterPersonRequest, opts ...grpc_go.CallOption) (*RegisterResponse, common.ErrorWithAttachment)
	PersonVerify(ctx context.Context, in *PersonVerifyRequest, opts ...grpc_go.CallOption) (*PersonVerifyResponse, common.ErrorWithAttachment)
	CheckPersonVerifySing(ctx context.Context, in *CheckPersonVerifySingRequest, opts ...grpc_go.CallOption) (*CommonResponse, common.ErrorWithAttachment)
	RegisterOrg(ctx context.Context, in *RegisterOrgRequest, opts ...grpc_go.CallOption) (*RegisterResponse, common.ErrorWithAttachment)
	CompanyVerify(ctx context.Context, in *CompanyVerifyRequest, opts ...grpc_go.CallOption) (*PersonVerifyResponse, common.ErrorWithAttachment)
	FindPersonCertInfo(ctx context.Context, in *FindCertInfo, opts ...grpc_go.CallOption) (*PersonCertInfoResponse, common.ErrorWithAttachment)
	FindCompanyCertInfo(ctx context.Context, in *FindCertInfo, opts ...grpc_go.CallOption) (*CompanyCertInfoResponse, common.ErrorWithAttachment)
	ApplyCert(ctx context.Context, in *ApplyCertRequest, opts ...grpc_go.CallOption) (*CommonResponse, common.ErrorWithAttachment)
	AddSignature(ctx context.Context, in *AddSignRequest, opts ...grpc_go.CallOption) (*AddSignResponse, common.ErrorWithAttachment)
	CustomSign(ctx context.Context, in *CustomSignRequest, opts ...grpc_go.CallOption) (*CustomSignResponse, common.ErrorWithAttachment)
	UploadTemplate(ctx context.Context, in *UploadTemplateRequest, opts ...grpc_go.CallOption) (*CommonResponse, common.ErrorWithAttachment)
	UploadDocs(ctx context.Context, in *UploadDocsRequest, opts ...grpc_go.CallOption) (*CommonResponse, common.ErrorWithAttachment)
	GenerateContract(ctx context.Context, in *GenerateContractRequest, opts ...grpc_go.CallOption) (*ViewCommonResponse, common.ErrorWithAttachment)
	ExtSignAuto(ctx context.Context, in *ExtSignAutoRequest, opts ...grpc_go.CallOption) (*ViewCommonResponse, common.ErrorWithAttachment)
	BeforeAuthSign(ctx context.Context, in *BeforeAuthSignRequest, opts ...grpc_go.CallOption) (*JumpCommonResponse, common.ErrorWithAttachment)
	ExtSign(ctx context.Context, in *ExtSignRequest, opts ...grpc_go.CallOption) (*JumpCommonResponse, common.ErrorWithAttachment)
	ViewContract(ctx context.Context, in *ContractRequest, opts ...grpc_go.CallOption) (*JumpCommonResponse, common.ErrorWithAttachment)
	DownLoadContract(ctx context.Context, in *ContractRequest, opts ...grpc_go.CallOption) (*JumpCommonResponse, common.ErrorWithAttachment)
	ContractFiling(ctx context.Context, in *ContractRequest, opts ...grpc_go.CallOption) (*CommonResponse, common.ErrorWithAttachment)
	GetPdfTemplateKeys(ctx context.Context, in *PdfTemplateKeysRequest, opts ...grpc_go.CallOption) (*PdfTemplateKeysResponse, common.ErrorWithAttachment)
	AuthStatus(ctx context.Context, in *CustomerIdRequest, opts ...grpc_go.CallOption) (*AuthStatusResponse, common.ErrorWithAttachment)
	CancelExtSignAuto(ctx context.Context, in *CancelExtSignAutoRequest, opts ...grpc_go.CallOption) (*JumpCommonResponse, common.ErrorWithAttachment)
	GenerateContractInBatches(ctx context.Context, in *GenerateContractInBatchesRequest, opts ...grpc_go.CallOption) (*GenerateContractInBatchesResponse, common.ErrorWithAttachment)
	DownLoadContractPdf(ctx context.Context, in *ContractRequest, opts ...grpc_go.CallOption) (*JumpCommonResponse, common.ErrorWithAttachment)
}

type contractClient struct {
	cc *triple.TripleConn
}

type ContractClientImpl struct {
	RegisterPerson            func(ctx context.Context, in *RegisterPersonRequest) (*RegisterResponse, error)
	PersonVerify              func(ctx context.Context, in *PersonVerifyRequest) (*PersonVerifyResponse, error)
	CheckPersonVerifySing     func(ctx context.Context, in *CheckPersonVerifySingRequest) (*CommonResponse, error)
	RegisterOrg               func(ctx context.Context, in *RegisterOrgRequest) (*RegisterResponse, error)
	CompanyVerify             func(ctx context.Context, in *CompanyVerifyRequest) (*PersonVerifyResponse, error)
	FindPersonCertInfo        func(ctx context.Context, in *FindCertInfo) (*PersonCertInfoResponse, error)
	FindCompanyCertInfo       func(ctx context.Context, in *FindCertInfo) (*CompanyCertInfoResponse, error)
	ApplyCert                 func(ctx context.Context, in *ApplyCertRequest) (*CommonResponse, error)
	AddSignature              func(ctx context.Context, in *AddSignRequest) (*AddSignResponse, error)
	CustomSign                func(ctx context.Context, in *CustomSignRequest) (*CustomSignResponse, error)
	UploadTemplate            func(ctx context.Context, in *UploadTemplateRequest) (*CommonResponse, error)
	UploadDocs                func(ctx context.Context, in *UploadDocsRequest) (*CommonResponse, error)
	GenerateContract          func(ctx context.Context, in *GenerateContractRequest) (*ViewCommonResponse, error)
	ExtSignAuto               func(ctx context.Context, in *ExtSignAutoRequest) (*ViewCommonResponse, error)
	BeforeAuthSign            func(ctx context.Context, in *BeforeAuthSignRequest) (*JumpCommonResponse, error)
	ExtSign                   func(ctx context.Context, in *ExtSignRequest) (*JumpCommonResponse, error)
	ViewContract              func(ctx context.Context, in *ContractRequest) (*JumpCommonResponse, error)
	DownLoadContract          func(ctx context.Context, in *ContractRequest) (*JumpCommonResponse, error)
	ContractFiling            func(ctx context.Context, in *ContractRequest) (*CommonResponse, error)
	GetPdfTemplateKeys        func(ctx context.Context, in *PdfTemplateKeysRequest) (*PdfTemplateKeysResponse, error)
	AuthStatus                func(ctx context.Context, in *CustomerIdRequest) (*AuthStatusResponse, error)
	CancelExtSignAuto         func(ctx context.Context, in *CancelExtSignAutoRequest) (*JumpCommonResponse, error)
	GenerateContractInBatches func(ctx context.Context, in *GenerateContractInBatchesRequest) (*GenerateContractInBatchesResponse, error)
	DownLoadContractPdf       func(ctx context.Context, in *ContractRequest) (*JumpCommonResponse, error)
}

func (c *ContractClientImpl) GetDubboStub(cc *triple.TripleConn) ContractClient {
	return NewContractClient(cc)
}

func (c *ContractClientImpl) XXX_InterfaceName() string {
	return "contract.Contract"
}

func NewContractClient(cc *triple.TripleConn) ContractClient {
	return &contractClient{cc}
}

func (c *contractClient) RegisterPerson(ctx context.Context, in *RegisterPersonRequest, opts ...grpc_go.CallOption) (*RegisterResponse, common.ErrorWithAttachment) {
	out := new(RegisterResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/RegisterPerson", in, out)
}

func (c *contractClient) PersonVerify(ctx context.Context, in *PersonVerifyRequest, opts ...grpc_go.CallOption) (*PersonVerifyResponse, common.ErrorWithAttachment) {
	out := new(PersonVerifyResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/PersonVerify", in, out)
}

func (c *contractClient) CheckPersonVerifySing(ctx context.Context, in *CheckPersonVerifySingRequest, opts ...grpc_go.CallOption) (*CommonResponse, common.ErrorWithAttachment) {
	out := new(CommonResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/CheckPersonVerifySing", in, out)
}

func (c *contractClient) RegisterOrg(ctx context.Context, in *RegisterOrgRequest, opts ...grpc_go.CallOption) (*RegisterResponse, common.ErrorWithAttachment) {
	out := new(RegisterResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/RegisterOrg", in, out)
}

func (c *contractClient) CompanyVerify(ctx context.Context, in *CompanyVerifyRequest, opts ...grpc_go.CallOption) (*PersonVerifyResponse, common.ErrorWithAttachment) {
	out := new(PersonVerifyResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/CompanyVerify", in, out)
}

func (c *contractClient) FindPersonCertInfo(ctx context.Context, in *FindCertInfo, opts ...grpc_go.CallOption) (*PersonCertInfoResponse, common.ErrorWithAttachment) {
	out := new(PersonCertInfoResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/FindPersonCertInfo", in, out)
}

func (c *contractClient) FindCompanyCertInfo(ctx context.Context, in *FindCertInfo, opts ...grpc_go.CallOption) (*CompanyCertInfoResponse, common.ErrorWithAttachment) {
	out := new(CompanyCertInfoResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/FindCompanyCertInfo", in, out)
}

func (c *contractClient) ApplyCert(ctx context.Context, in *ApplyCertRequest, opts ...grpc_go.CallOption) (*CommonResponse, common.ErrorWithAttachment) {
	out := new(CommonResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/ApplyCert", in, out)
}

func (c *contractClient) AddSignature(ctx context.Context, in *AddSignRequest, opts ...grpc_go.CallOption) (*AddSignResponse, common.ErrorWithAttachment) {
	out := new(AddSignResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/AddSignature", in, out)
}

func (c *contractClient) CustomSign(ctx context.Context, in *CustomSignRequest, opts ...grpc_go.CallOption) (*CustomSignResponse, common.ErrorWithAttachment) {
	out := new(CustomSignResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/CustomSign", in, out)
}

func (c *contractClient) UploadTemplate(ctx context.Context, in *UploadTemplateRequest, opts ...grpc_go.CallOption) (*CommonResponse, common.ErrorWithAttachment) {
	out := new(CommonResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/UploadTemplate", in, out)
}

func (c *contractClient) UploadDocs(ctx context.Context, in *UploadDocsRequest, opts ...grpc_go.CallOption) (*CommonResponse, common.ErrorWithAttachment) {
	out := new(CommonResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/UploadDocs", in, out)
}

func (c *contractClient) GenerateContract(ctx context.Context, in *GenerateContractRequest, opts ...grpc_go.CallOption) (*ViewCommonResponse, common.ErrorWithAttachment) {
	out := new(ViewCommonResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/GenerateContract", in, out)
}

func (c *contractClient) ExtSignAuto(ctx context.Context, in *ExtSignAutoRequest, opts ...grpc_go.CallOption) (*ViewCommonResponse, common.ErrorWithAttachment) {
	out := new(ViewCommonResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/ExtSignAuto", in, out)
}

func (c *contractClient) BeforeAuthSign(ctx context.Context, in *BeforeAuthSignRequest, opts ...grpc_go.CallOption) (*JumpCommonResponse, common.ErrorWithAttachment) {
	out := new(JumpCommonResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/BeforeAuthSign", in, out)
}

func (c *contractClient) ExtSign(ctx context.Context, in *ExtSignRequest, opts ...grpc_go.CallOption) (*JumpCommonResponse, common.ErrorWithAttachment) {
	out := new(JumpCommonResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/ExtSign", in, out)
}

func (c *contractClient) ViewContract(ctx context.Context, in *ContractRequest, opts ...grpc_go.CallOption) (*JumpCommonResponse, common.ErrorWithAttachment) {
	out := new(JumpCommonResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/ViewContract", in, out)
}

func (c *contractClient) DownLoadContract(ctx context.Context, in *ContractRequest, opts ...grpc_go.CallOption) (*JumpCommonResponse, common.ErrorWithAttachment) {
	out := new(JumpCommonResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/DownLoadContract", in, out)
}

func (c *contractClient) ContractFiling(ctx context.Context, in *ContractRequest, opts ...grpc_go.CallOption) (*CommonResponse, common.ErrorWithAttachment) {
	out := new(CommonResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/ContractFiling", in, out)
}

func (c *contractClient) GetPdfTemplateKeys(ctx context.Context, in *PdfTemplateKeysRequest, opts ...grpc_go.CallOption) (*PdfTemplateKeysResponse, common.ErrorWithAttachment) {
	out := new(PdfTemplateKeysResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/GetPdfTemplateKeys", in, out)
}

func (c *contractClient) AuthStatus(ctx context.Context, in *CustomerIdRequest, opts ...grpc_go.CallOption) (*AuthStatusResponse, common.ErrorWithAttachment) {
	out := new(AuthStatusResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/AuthStatus", in, out)
}

func (c *contractClient) CancelExtSignAuto(ctx context.Context, in *CancelExtSignAutoRequest, opts ...grpc_go.CallOption) (*JumpCommonResponse, common.ErrorWithAttachment) {
	out := new(JumpCommonResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/CancelExtSignAuto", in, out)
}

func (c *contractClient) GenerateContractInBatches(ctx context.Context, in *GenerateContractInBatchesRequest, opts ...grpc_go.CallOption) (*GenerateContractInBatchesResponse, common.ErrorWithAttachment) {
	out := new(GenerateContractInBatchesResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/GenerateContractInBatches", in, out)
}

func (c *contractClient) DownLoadContractPdf(ctx context.Context, in *ContractRequest, opts ...grpc_go.CallOption) (*JumpCommonResponse, common.ErrorWithAttachment) {
	out := new(JumpCommonResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/DownLoadContractPdf", in, out)
}

// ContractServer is the server API for Contract service.
// All implementations must embed UnimplementedContractServer
// for forward compatibility
type ContractServer interface {
	RegisterPerson(context.Context, *RegisterPersonRequest) (*RegisterResponse, error)
	PersonVerify(context.Context, *PersonVerifyRequest) (*PersonVerifyResponse, error)
	CheckPersonVerifySing(context.Context, *CheckPersonVerifySingRequest) (*CommonResponse, error)
	RegisterOrg(context.Context, *RegisterOrgRequest) (*RegisterResponse, error)
	CompanyVerify(context.Context, *CompanyVerifyRequest) (*PersonVerifyResponse, error)
	FindPersonCertInfo(context.Context, *FindCertInfo) (*PersonCertInfoResponse, error)
	FindCompanyCertInfo(context.Context, *FindCertInfo) (*CompanyCertInfoResponse, error)
	ApplyCert(context.Context, *ApplyCertRequest) (*CommonResponse, error)
	AddSignature(context.Context, *AddSignRequest) (*AddSignResponse, error)
	CustomSign(context.Context, *CustomSignRequest) (*CustomSignResponse, error)
	UploadTemplate(context.Context, *UploadTemplateRequest) (*CommonResponse, error)
	UploadDocs(context.Context, *UploadDocsRequest) (*CommonResponse, error)
	GenerateContract(context.Context, *GenerateContractRequest) (*ViewCommonResponse, error)
	ExtSignAuto(context.Context, *ExtSignAutoRequest) (*ViewCommonResponse, error)
	BeforeAuthSign(context.Context, *BeforeAuthSignRequest) (*JumpCommonResponse, error)
	ExtSign(context.Context, *ExtSignRequest) (*JumpCommonResponse, error)
	ViewContract(context.Context, *ContractRequest) (*JumpCommonResponse, error)
	DownLoadContract(context.Context, *ContractRequest) (*JumpCommonResponse, error)
	ContractFiling(context.Context, *ContractRequest) (*CommonResponse, error)
	GetPdfTemplateKeys(context.Context, *PdfTemplateKeysRequest) (*PdfTemplateKeysResponse, error)
	AuthStatus(context.Context, *CustomerIdRequest) (*AuthStatusResponse, error)
	CancelExtSignAuto(context.Context, *CancelExtSignAutoRequest) (*JumpCommonResponse, error)
	GenerateContractInBatches(context.Context, *GenerateContractInBatchesRequest) (*GenerateContractInBatchesResponse, error)
	DownLoadContractPdf(context.Context, *ContractRequest) (*JumpCommonResponse, error)
	mustEmbedUnimplementedContractServer()
}

// UnimplementedContractServer must be embedded to have forward compatible implementations.
type UnimplementedContractServer struct {
	proxyImpl protocol.Invoker
}

func (UnimplementedContractServer) RegisterPerson(context.Context, *RegisterPersonRequest) (*RegisterResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RegisterPerson not implemented")
}
func (UnimplementedContractServer) PersonVerify(context.Context, *PersonVerifyRequest) (*PersonVerifyResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PersonVerify not implemented")
}
func (UnimplementedContractServer) CheckPersonVerifySing(context.Context, *CheckPersonVerifySingRequest) (*CommonResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CheckPersonVerifySing not implemented")
}
func (UnimplementedContractServer) RegisterOrg(context.Context, *RegisterOrgRequest) (*RegisterResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RegisterOrg not implemented")
}
func (UnimplementedContractServer) CompanyVerify(context.Context, *CompanyVerifyRequest) (*PersonVerifyResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CompanyVerify not implemented")
}
func (UnimplementedContractServer) FindPersonCertInfo(context.Context, *FindCertInfo) (*PersonCertInfoResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FindPersonCertInfo not implemented")
}
func (UnimplementedContractServer) FindCompanyCertInfo(context.Context, *FindCertInfo) (*CompanyCertInfoResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FindCompanyCertInfo not implemented")
}
func (UnimplementedContractServer) ApplyCert(context.Context, *ApplyCertRequest) (*CommonResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ApplyCert not implemented")
}
func (UnimplementedContractServer) AddSignature(context.Context, *AddSignRequest) (*AddSignResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AddSignature not implemented")
}
func (UnimplementedContractServer) CustomSign(context.Context, *CustomSignRequest) (*CustomSignResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CustomSign not implemented")
}
func (UnimplementedContractServer) UploadTemplate(context.Context, *UploadTemplateRequest) (*CommonResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UploadTemplate not implemented")
}
func (UnimplementedContractServer) UploadDocs(context.Context, *UploadDocsRequest) (*CommonResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UploadDocs not implemented")
}
func (UnimplementedContractServer) GenerateContract(context.Context, *GenerateContractRequest) (*ViewCommonResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GenerateContract not implemented")
}
func (UnimplementedContractServer) ExtSignAuto(context.Context, *ExtSignAutoRequest) (*ViewCommonResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ExtSignAuto not implemented")
}
func (UnimplementedContractServer) BeforeAuthSign(context.Context, *BeforeAuthSignRequest) (*JumpCommonResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method BeforeAuthSign not implemented")
}
func (UnimplementedContractServer) ExtSign(context.Context, *ExtSignRequest) (*JumpCommonResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ExtSign not implemented")
}
func (UnimplementedContractServer) ViewContract(context.Context, *ContractRequest) (*JumpCommonResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ViewContract not implemented")
}
func (UnimplementedContractServer) DownLoadContract(context.Context, *ContractRequest) (*JumpCommonResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DownLoadContract not implemented")
}
func (UnimplementedContractServer) ContractFiling(context.Context, *ContractRequest) (*CommonResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ContractFiling not implemented")
}
func (UnimplementedContractServer) GetPdfTemplateKeys(context.Context, *PdfTemplateKeysRequest) (*PdfTemplateKeysResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetPdfTemplateKeys not implemented")
}
func (UnimplementedContractServer) AuthStatus(context.Context, *CustomerIdRequest) (*AuthStatusResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AuthStatus not implemented")
}
func (UnimplementedContractServer) CancelExtSignAuto(context.Context, *CancelExtSignAutoRequest) (*JumpCommonResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CancelExtSignAuto not implemented")
}
func (UnimplementedContractServer) GenerateContractInBatches(context.Context, *GenerateContractInBatchesRequest) (*GenerateContractInBatchesResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GenerateContractInBatches not implemented")
}
func (UnimplementedContractServer) DownLoadContractPdf(context.Context, *ContractRequest) (*JumpCommonResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DownLoadContractPdf not implemented")
}
func (s *UnimplementedContractServer) XXX_SetProxyImpl(impl protocol.Invoker) {
	s.proxyImpl = impl
}

func (s *UnimplementedContractServer) XXX_GetProxyImpl() protocol.Invoker {
	return s.proxyImpl
}

func (s *UnimplementedContractServer) XXX_ServiceDesc() *grpc_go.ServiceDesc {
	return &Contract_ServiceDesc
}
func (s *UnimplementedContractServer) XXX_InterfaceName() string {
	return "contract.Contract"
}

func (UnimplementedContractServer) mustEmbedUnimplementedContractServer() {}

// UnsafeContractServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to ContractServer will
// result in compilation errors.
type UnsafeContractServer interface {
	mustEmbedUnimplementedContractServer()
}

func RegisterContractServer(s grpc_go.ServiceRegistrar, srv ContractServer) {
	s.RegisterService(&Contract_ServiceDesc, srv)
}

func _Contract_RegisterPerson_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(RegisterPersonRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("RegisterPerson", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Contract_PersonVerify_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(PersonVerifyRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("PersonVerify", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Contract_CheckPersonVerifySing_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(CheckPersonVerifySingRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("CheckPersonVerifySing", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Contract_RegisterOrg_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(RegisterOrgRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("RegisterOrg", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Contract_CompanyVerify_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(CompanyVerifyRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("CompanyVerify", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Contract_FindPersonCertInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(FindCertInfo)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("FindPersonCertInfo", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Contract_FindCompanyCertInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(FindCertInfo)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("FindCompanyCertInfo", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Contract_ApplyCert_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(ApplyCertRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("ApplyCert", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Contract_AddSignature_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddSignRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("AddSignature", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Contract_CustomSign_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(CustomSignRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("CustomSign", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Contract_UploadTemplate_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(UploadTemplateRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("UploadTemplate", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Contract_UploadDocs_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(UploadDocsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("UploadDocs", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Contract_GenerateContract_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(GenerateContractRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("GenerateContract", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Contract_ExtSignAuto_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(ExtSignAutoRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("ExtSignAuto", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Contract_BeforeAuthSign_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(BeforeAuthSignRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("BeforeAuthSign", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Contract_ExtSign_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(ExtSignRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("ExtSign", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Contract_ViewContract_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(ContractRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("ViewContract", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Contract_DownLoadContract_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(ContractRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("DownLoadContract", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Contract_ContractFiling_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(ContractRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("ContractFiling", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Contract_GetPdfTemplateKeys_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(PdfTemplateKeysRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("GetPdfTemplateKeys", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Contract_AuthStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(CustomerIdRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("AuthStatus", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Contract_CancelExtSignAuto_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(CancelExtSignAutoRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("CancelExtSignAuto", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Contract_GenerateContractInBatches_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(GenerateContractInBatchesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("GenerateContractInBatches", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Contract_DownLoadContractPdf_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(ContractRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("DownLoadContractPdf", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

// Contract_ServiceDesc is the grpc_go.ServiceDesc for Contract service.
// It's only intended for direct use with grpc_go.RegisterService,
// and not to be introspected or modified (even as a copy)
var Contract_ServiceDesc = grpc_go.ServiceDesc{
	ServiceName: "contract.Contract",
	HandlerType: (*ContractServer)(nil),
	Methods: []grpc_go.MethodDesc{
		{
			MethodName: "RegisterPerson",
			Handler:    _Contract_RegisterPerson_Handler,
		},
		{
			MethodName: "PersonVerify",
			Handler:    _Contract_PersonVerify_Handler,
		},
		{
			MethodName: "CheckPersonVerifySing",
			Handler:    _Contract_CheckPersonVerifySing_Handler,
		},
		{
			MethodName: "RegisterOrg",
			Handler:    _Contract_RegisterOrg_Handler,
		},
		{
			MethodName: "CompanyVerify",
			Handler:    _Contract_CompanyVerify_Handler,
		},
		{
			MethodName: "FindPersonCertInfo",
			Handler:    _Contract_FindPersonCertInfo_Handler,
		},
		{
			MethodName: "FindCompanyCertInfo",
			Handler:    _Contract_FindCompanyCertInfo_Handler,
		},
		{
			MethodName: "ApplyCert",
			Handler:    _Contract_ApplyCert_Handler,
		},
		{
			MethodName: "AddSignature",
			Handler:    _Contract_AddSignature_Handler,
		},
		{
			MethodName: "CustomSign",
			Handler:    _Contract_CustomSign_Handler,
		},
		{
			MethodName: "UploadTemplate",
			Handler:    _Contract_UploadTemplate_Handler,
		},
		{
			MethodName: "UploadDocs",
			Handler:    _Contract_UploadDocs_Handler,
		},
		{
			MethodName: "GenerateContract",
			Handler:    _Contract_GenerateContract_Handler,
		},
		{
			MethodName: "ExtSignAuto",
			Handler:    _Contract_ExtSignAuto_Handler,
		},
		{
			MethodName: "BeforeAuthSign",
			Handler:    _Contract_BeforeAuthSign_Handler,
		},
		{
			MethodName: "ExtSign",
			Handler:    _Contract_ExtSign_Handler,
		},
		{
			MethodName: "ViewContract",
			Handler:    _Contract_ViewContract_Handler,
		},
		{
			MethodName: "DownLoadContract",
			Handler:    _Contract_DownLoadContract_Handler,
		},
		{
			MethodName: "ContractFiling",
			Handler:    _Contract_ContractFiling_Handler,
		},
		{
			MethodName: "GetPdfTemplateKeys",
			Handler:    _Contract_GetPdfTemplateKeys_Handler,
		},
		{
			MethodName: "AuthStatus",
			Handler:    _Contract_AuthStatus_Handler,
		},
		{
			MethodName: "CancelExtSignAuto",
			Handler:    _Contract_CancelExtSignAuto_Handler,
		},
		{
			MethodName: "GenerateContractInBatches",
			Handler:    _Contract_GenerateContractInBatches_Handler,
		},
		{
			MethodName: "DownLoadContractPdf",
			Handler:    _Contract_DownLoadContractPdf_Handler,
		},
	},
	Streams:  []grpc_go.StreamDesc{},
	Metadata: "contract.proto",
}
