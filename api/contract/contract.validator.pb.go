// Code generated by protoc-gen-gogo. DO NOT EDIT.
// source: contract.proto

package contract

import (
	fmt "fmt"
	math "math"
	proto "github.com/golang/protobuf/proto"
	github_com_mwitkow_go_proto_validators "github.com/mwitkow/go-proto-validators"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

func (this *Fdd) Validate() error {
	return nil
}
func (this *CheckPersonVerifySingRequest) Validate() error {
	if this.Fdd != nil {
		if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(this.Fdd); err != nil {
			return github_com_mwitkow_go_proto_validators.FieldError("Fdd", err)
		}
	}
	return nil
}
func (this *CancelExtSignAutoRequest) Validate() error {
	if this.Fdd != nil {
		if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(this.Fdd); err != nil {
			return github_com_mwitkow_go_proto_validators.FieldError("Fdd", err)
		}
	}
	return nil
}
func (this *Person) Validate() error {
	return nil
}
func (this *PersonCertInfoResponse) Validate() error {
	if this.Person != nil {
		if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(this.Person); err != nil {
			return github_com_mwitkow_go_proto_validators.FieldError("Person", err)
		}
	}
	return nil
}
func (this *FindCertInfo) Validate() error {
	if this.Fdd != nil {
		if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(this.Fdd); err != nil {
			return github_com_mwitkow_go_proto_validators.FieldError("Fdd", err)
		}
	}
	return nil
}
func (this *Company) Validate() error {
	return nil
}
func (this *Manager) Validate() error {
	return nil
}
func (this *CompanyCertInfoResponse) Validate() error {
	if this.Company != nil {
		if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(this.Company); err != nil {
			return github_com_mwitkow_go_proto_validators.FieldError("Company", err)
		}
	}
	if this.Manager != nil {
		if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(this.Manager); err != nil {
			return github_com_mwitkow_go_proto_validators.FieldError("Manager", err)
		}
	}
	return nil
}
func (this *AuthStatusResponse) Validate() error {
	return nil
}
func (this *ApplyCertRequest) Validate() error {
	if this.Fdd != nil {
		if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(this.Fdd); err != nil {
			return github_com_mwitkow_go_proto_validators.FieldError("Fdd", err)
		}
	}
	return nil
}
func (this *ViewCommonResponse) Validate() error {
	return nil
}
func (this *PdfTemplateKeysRequest) Validate() error {
	if this.Fdd != nil {
		if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(this.Fdd); err != nil {
			return github_com_mwitkow_go_proto_validators.FieldError("Fdd", err)
		}
	}
	return nil
}
func (this *PdfTemplateKeysResponse) Validate() error {
	return nil
}
func (this *JumpCommonResponse) Validate() error {
	return nil
}
func (this *ContractRequest) Validate() error {
	if this.Fdd != nil {
		if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(this.Fdd); err != nil {
			return github_com_mwitkow_go_proto_validators.FieldError("Fdd", err)
		}
	}
	return nil
}
func (this *ExtSignRequest) Validate() error {
	if this.Fdd != nil {
		if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(this.Fdd); err != nil {
			return github_com_mwitkow_go_proto_validators.FieldError("Fdd", err)
		}
	}
	return nil
}
func (this *BeforeAuthSignRequest) Validate() error {
	if this.Fdd != nil {
		if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(this.Fdd); err != nil {
			return github_com_mwitkow_go_proto_validators.FieldError("Fdd", err)
		}
	}
	return nil
}
func (this *UploadDocsRequest) Validate() error {
	if this.Fdd != nil {
		if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(this.Fdd); err != nil {
			return github_com_mwitkow_go_proto_validators.FieldError("Fdd", err)
		}
	}
	return nil
}
func (this *CompanyVerifyRequest) Validate() error {
	if this.Fdd != nil {
		if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(this.Fdd); err != nil {
			return github_com_mwitkow_go_proto_validators.FieldError("Fdd", err)
		}
	}
	return nil
}
func (this *UploadTemplateResponse) Validate() error {
	return nil
}
func (this *PersonVerifyRequest) Validate() error {
	if this.Fdd != nil {
		if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(this.Fdd); err != nil {
			return github_com_mwitkow_go_proto_validators.FieldError("Fdd", err)
		}
	}
	return nil
}
func (this *PersonVerifyResponse) Validate() error {
	return nil
}
func (this *RegisterResponse) Validate() error {
	return nil
}
func (this *CustomerIdRequest) Validate() error {
	if this.Fdd != nil {
		if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(this.Fdd); err != nil {
			return github_com_mwitkow_go_proto_validators.FieldError("Fdd", err)
		}
	}
	return nil
}
func (this *CommonResponse) Validate() error {
	return nil
}
func (this *RegisterOrgRequest) Validate() error {
	if this.Fdd != nil {
		if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(this.Fdd); err != nil {
			return github_com_mwitkow_go_proto_validators.FieldError("Fdd", err)
		}
	}
	return nil
}
func (this *RegisterPersonRequest) Validate() error {
	if this.Fdd != nil {
		if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(this.Fdd); err != nil {
			return github_com_mwitkow_go_proto_validators.FieldError("Fdd", err)
		}
	}
	return nil
}
func (this *AddSignRequest) Validate() error {
	if this.Fdd != nil {
		if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(this.Fdd); err != nil {
			return github_com_mwitkow_go_proto_validators.FieldError("Fdd", err)
		}
	}
	return nil
}
func (this *AddSignResponse) Validate() error {
	return nil
}
func (this *CustomSignRequest) Validate() error {
	if this.Fdd != nil {
		if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(this.Fdd); err != nil {
			return github_com_mwitkow_go_proto_validators.FieldError("Fdd", err)
		}
	}
	return nil
}
func (this *CustomSignResponse) Validate() error {
	return nil
}
func (this *UploadTemplateRequest) Validate() error {
	if this.Fdd != nil {
		if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(this.Fdd); err != nil {
			return github_com_mwitkow_go_proto_validators.FieldError("Fdd", err)
		}
	}
	return nil
}
func (this *ExtSignAutoRequest) Validate() error {
	if this.Fdd != nil {
		if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(this.Fdd); err != nil {
			return github_com_mwitkow_go_proto_validators.FieldError("Fdd", err)
		}
	}
	return nil
}
func (this *GenerateContractRequest) Validate() error {
	if this.Fdd != nil {
		if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(this.Fdd); err != nil {
			return github_com_mwitkow_go_proto_validators.FieldError("Fdd", err)
		}
	}
	return nil
}
func (this *SignaturePosition) Validate() error {
	return nil
}
func (this *SignData) Validate() error {
	for _, item := range this.SignaturePositions {
		if item != nil {
			if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(item); err != nil {
				return github_com_mwitkow_go_proto_validators.FieldError("SignaturePositions", err)
			}
		}
	}
	return nil
}
func (this *GenerateContractInBatchesRequest) Validate() error {
	for _, item := range this.SignData {
		if item != nil {
			if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(item); err != nil {
				return github_com_mwitkow_go_proto_validators.FieldError("SignData", err)
			}
		}
	}
	if this.Fdd != nil {
		if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(this.Fdd); err != nil {
			return github_com_mwitkow_go_proto_validators.FieldError("Fdd", err)
		}
	}
	return nil
}
func (this *GenerateContractInBatchesResponse) Validate() error {
	return nil
}
