//
// Licensed to the Apache Software Foundation (ASF) under one or more
// contributor license agreements.  See the NOTICE file distributed with
// this work for additional information regarding copyright ownership.
// The ASF licenses this file to You under the Apache License, Version 2.0
// (the "License"); you may not use this file except in compliance with
// the License.  You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.35.2
// 	protoc        v3.21.1
// source: contract.proto

package contract

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type FddResultType int32

const (
	FddResultType_NoResultType            FddResultType = 0 //法大大默认值为2
	FddResultType_SuccessReturnFailedKeep FddResultType = 1 //认证通过直接跳转到return_url；认证不通过停留在原因页面
	FddResultType_UseReturnButton         FddResultType = 2 //无论是否认证通过均停留在法大大页面。需要用户点击“完成认证”或“退出认证”后跳转到return_url或法大大指定页面
	FddResultType_AlwaysReturn            FddResultType = 3 //无论是否认证通过均跳转到return_url。
)

// Enum value maps for FddResultType.
var (
	FddResultType_name = map[int32]string{
		0: "NoResultType",
		1: "SuccessReturnFailedKeep",
		2: "UseReturnButton",
		3: "AlwaysReturn",
	}
	FddResultType_value = map[string]int32{
		"NoResultType":            0,
		"SuccessReturnFailedKeep": 1,
		"UseReturnButton":         2,
		"AlwaysReturn":            3,
	}
)

func (x FddResultType) Enum() *FddResultType {
	p := new(FddResultType)
	*p = x
	return p
}

func (x FddResultType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (FddResultType) Descriptor() protoreflect.EnumDescriptor {
	return file_contract_proto_enumTypes[0].Descriptor()
}

func (FddResultType) Type() protoreflect.EnumType {
	return &file_contract_proto_enumTypes[0]
}

func (x FddResultType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use FddResultType.Descriptor instead.
func (FddResultType) EnumDescriptor() ([]byte, []int) {
	return file_contract_proto_rawDescGZIP(), []int{0}
}

type Fdd struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Url         string `protobuf:"bytes,1,opt,name=url,proto3" json:"url,omitempty"`
	AppId       string `protobuf:"bytes,2,opt,name=appId,proto3" json:"appId,omitempty"`
	AppSecret   string `protobuf:"bytes,3,opt,name=appSecret,proto3" json:"appSecret,omitempty"`
	ContentType string `protobuf:"bytes,4,opt,name=contentType,proto3" json:"contentType,omitempty"`
}

func (x *Fdd) Reset() {
	*x = Fdd{}
	mi := &file_contract_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Fdd) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Fdd) ProtoMessage() {}

func (x *Fdd) ProtoReflect() protoreflect.Message {
	mi := &file_contract_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Fdd.ProtoReflect.Descriptor instead.
func (*Fdd) Descriptor() ([]byte, []int) {
	return file_contract_proto_rawDescGZIP(), []int{0}
}

func (x *Fdd) GetUrl() string {
	if x != nil {
		return x.Url
	}
	return ""
}

func (x *Fdd) GetAppId() string {
	if x != nil {
		return x.AppId
	}
	return ""
}

func (x *Fdd) GetAppSecret() string {
	if x != nil {
		return x.AppSecret
	}
	return ""
}

func (x *Fdd) GetContentType() string {
	if x != nil {
		return x.ContentType
	}
	return ""
}

type CheckPersonVerifySingRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TransactionNo      string `protobuf:"bytes,1,opt,name=transactionNo,proto3" json:"transactionNo,omitempty"`
	PersonName         string `protobuf:"bytes,2,opt,name=personName,proto3" json:"personName,omitempty"`
	Status             string `protobuf:"bytes,3,opt,name=status,proto3" json:"status,omitempty"`
	CompanyName        string `protobuf:"bytes,4,opt,name=companyName,proto3" json:"companyName,omitempty"`
	AuthenticationType string `protobuf:"bytes,5,opt,name=authenticationType,proto3" json:"authenticationType,omitempty"`
	Sign               string `protobuf:"bytes,6,opt,name=sign,proto3" json:"sign,omitempty"`
	Fdd                *Fdd   `protobuf:"bytes,13,opt,name=fdd,proto3" json:"fdd,omitempty"` //法大大的配置,有责使用，无则默认
}

func (x *CheckPersonVerifySingRequest) Reset() {
	*x = CheckPersonVerifySingRequest{}
	mi := &file_contract_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CheckPersonVerifySingRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CheckPersonVerifySingRequest) ProtoMessage() {}

func (x *CheckPersonVerifySingRequest) ProtoReflect() protoreflect.Message {
	mi := &file_contract_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CheckPersonVerifySingRequest.ProtoReflect.Descriptor instead.
func (*CheckPersonVerifySingRequest) Descriptor() ([]byte, []int) {
	return file_contract_proto_rawDescGZIP(), []int{1}
}

func (x *CheckPersonVerifySingRequest) GetTransactionNo() string {
	if x != nil {
		return x.TransactionNo
	}
	return ""
}

func (x *CheckPersonVerifySingRequest) GetPersonName() string {
	if x != nil {
		return x.PersonName
	}
	return ""
}

func (x *CheckPersonVerifySingRequest) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

func (x *CheckPersonVerifySingRequest) GetCompanyName() string {
	if x != nil {
		return x.CompanyName
	}
	return ""
}

func (x *CheckPersonVerifySingRequest) GetAuthenticationType() string {
	if x != nil {
		return x.AuthenticationType
	}
	return ""
}

func (x *CheckPersonVerifySingRequest) GetSign() string {
	if x != nil {
		return x.Sign
	}
	return ""
}

func (x *CheckPersonVerifySingRequest) GetFdd() *Fdd {
	if x != nil {
		return x.Fdd
	}
	return nil
}

type CancelExtSignAutoRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CustomerId string `protobuf:"bytes,1,opt,name=CustomerId,proto3" json:"CustomerId,omitempty"`
	NotifyUrl  string `protobuf:"bytes,2,opt,name=NotifyUrl,proto3" json:"NotifyUrl,omitempty"`
	ReturnUrl  string `protobuf:"bytes,3,opt,name=returnUrl,proto3" json:"returnUrl,omitempty"`
	Fdd        *Fdd   `protobuf:"bytes,4,opt,name=fdd,proto3" json:"fdd,omitempty"` //法大大的配置,有责使用，无则默认
}

func (x *CancelExtSignAutoRequest) Reset() {
	*x = CancelExtSignAutoRequest{}
	mi := &file_contract_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CancelExtSignAutoRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CancelExtSignAutoRequest) ProtoMessage() {}

func (x *CancelExtSignAutoRequest) ProtoReflect() protoreflect.Message {
	mi := &file_contract_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CancelExtSignAutoRequest.ProtoReflect.Descriptor instead.
func (*CancelExtSignAutoRequest) Descriptor() ([]byte, []int) {
	return file_contract_proto_rawDescGZIP(), []int{2}
}

func (x *CancelExtSignAutoRequest) GetCustomerId() string {
	if x != nil {
		return x.CustomerId
	}
	return ""
}

func (x *CancelExtSignAutoRequest) GetNotifyUrl() string {
	if x != nil {
		return x.NotifyUrl
	}
	return ""
}

func (x *CancelExtSignAutoRequest) GetReturnUrl() string {
	if x != nil {
		return x.ReturnUrl
	}
	return ""
}

func (x *CancelExtSignAutoRequest) GetFdd() *Fdd {
	if x != nil {
		return x.Fdd
	}
	return nil
}

type Person struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AreaCode             string  `protobuf:"bytes,1,opt,name=AreaCode,proto3" json:"AreaCode,omitempty"`
	AuditFailReason      string  `protobuf:"bytes,2,opt,name=AuditFailReason,proto3" json:"AuditFailReason,omitempty"`
	AuditorTime          string  `protobuf:"bytes,3,opt,name=AuditorTime,proto3" json:"AuditorTime,omitempty"`
	BackgroundIdCardPath string  `protobuf:"bytes,4,opt,name=BackgroundIdCardPath,proto3" json:"BackgroundIdCardPath,omitempty"`
	BankCardNo           string  `protobuf:"bytes,5,opt,name=BankCardNo,proto3" json:"BankCardNo,omitempty"`
	Birthday             string  `protobuf:"bytes,6,opt,name=Birthday,proto3" json:"Birthday,omitempty"`
	CertType             string  `protobuf:"bytes,7,opt,name=CertType,proto3" json:"CertType,omitempty"`
	ExpiresDate          string  `protobuf:"bytes,8,opt,name=ExpiresDate,proto3" json:"ExpiresDate,omitempty"`
	Fork                 string  `protobuf:"bytes,9,opt,name=Fork,proto3" json:"Fork,omitempty"`
	GesturesPhotoPath    string  `protobuf:"bytes,10,opt,name=GesturesPhotoPath,proto3" json:"GesturesPhotoPath,omitempty"`
	HeadPhotoPath        string  `protobuf:"bytes,11,opt,name=HeadPhotoPath,proto3" json:"HeadPhotoPath,omitempty"`
	IdCard               string  `protobuf:"bytes,12,opt,name=IdCard,proto3" json:"IdCard,omitempty"`
	IsLongTerm           string  `protobuf:"bytes,13,opt,name=IsLongTerm,proto3" json:"IsLongTerm,omitempty"`
	IsPassFourElement    float64 `protobuf:"fixed64,14,opt,name=IsPassFourElement,proto3" json:"IsPassFourElement,omitempty"`
	IsPassThreeElement   float64 `protobuf:"fixed64,15,opt,name=IsPassThreeElement,proto3" json:"IsPassThreeElement,omitempty"`
	IssueAuthority       string  `protobuf:"bytes,16,opt,name=IssueAuthority,proto3" json:"IssueAuthority,omitempty"`
	Mobile               string  `protobuf:"bytes,17,opt,name=Mobile,proto3" json:"Mobile,omitempty"`
	PersonName           string  `protobuf:"bytes,18,opt,name=PersonName,proto3" json:"PersonName,omitempty"`
	PhotoUuid            string  `protobuf:"bytes,19,opt,name=PhotoUuid,proto3" json:"PhotoUuid,omitempty"`
	Sex                  string  `protobuf:"bytes,20,opt,name=Sex,proto3" json:"Sex,omitempty"`
	StartDate            string  `protobuf:"bytes,21,opt,name=StartDate,proto3" json:"StartDate,omitempty"`
	Status               string  `protobuf:"bytes,22,opt,name=Status,proto3" json:"Status,omitempty"`
	Type                 string  `protobuf:"bytes,23,opt,name=Type,proto3" json:"Type,omitempty"`
	VerifyType           string  `protobuf:"bytes,24,opt,name=VerifyType,proto3" json:"VerifyType,omitempty"`
	Address              string  `protobuf:"bytes,25,opt,name=Address,proto3" json:"Address,omitempty"`
}

func (x *Person) Reset() {
	*x = Person{}
	mi := &file_contract_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Person) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Person) ProtoMessage() {}

func (x *Person) ProtoReflect() protoreflect.Message {
	mi := &file_contract_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Person.ProtoReflect.Descriptor instead.
func (*Person) Descriptor() ([]byte, []int) {
	return file_contract_proto_rawDescGZIP(), []int{3}
}

func (x *Person) GetAreaCode() string {
	if x != nil {
		return x.AreaCode
	}
	return ""
}

func (x *Person) GetAuditFailReason() string {
	if x != nil {
		return x.AuditFailReason
	}
	return ""
}

func (x *Person) GetAuditorTime() string {
	if x != nil {
		return x.AuditorTime
	}
	return ""
}

func (x *Person) GetBackgroundIdCardPath() string {
	if x != nil {
		return x.BackgroundIdCardPath
	}
	return ""
}

func (x *Person) GetBankCardNo() string {
	if x != nil {
		return x.BankCardNo
	}
	return ""
}

func (x *Person) GetBirthday() string {
	if x != nil {
		return x.Birthday
	}
	return ""
}

func (x *Person) GetCertType() string {
	if x != nil {
		return x.CertType
	}
	return ""
}

func (x *Person) GetExpiresDate() string {
	if x != nil {
		return x.ExpiresDate
	}
	return ""
}

func (x *Person) GetFork() string {
	if x != nil {
		return x.Fork
	}
	return ""
}

func (x *Person) GetGesturesPhotoPath() string {
	if x != nil {
		return x.GesturesPhotoPath
	}
	return ""
}

func (x *Person) GetHeadPhotoPath() string {
	if x != nil {
		return x.HeadPhotoPath
	}
	return ""
}

func (x *Person) GetIdCard() string {
	if x != nil {
		return x.IdCard
	}
	return ""
}

func (x *Person) GetIsLongTerm() string {
	if x != nil {
		return x.IsLongTerm
	}
	return ""
}

func (x *Person) GetIsPassFourElement() float64 {
	if x != nil {
		return x.IsPassFourElement
	}
	return 0
}

func (x *Person) GetIsPassThreeElement() float64 {
	if x != nil {
		return x.IsPassThreeElement
	}
	return 0
}

func (x *Person) GetIssueAuthority() string {
	if x != nil {
		return x.IssueAuthority
	}
	return ""
}

func (x *Person) GetMobile() string {
	if x != nil {
		return x.Mobile
	}
	return ""
}

func (x *Person) GetPersonName() string {
	if x != nil {
		return x.PersonName
	}
	return ""
}

func (x *Person) GetPhotoUuid() string {
	if x != nil {
		return x.PhotoUuid
	}
	return ""
}

func (x *Person) GetSex() string {
	if x != nil {
		return x.Sex
	}
	return ""
}

func (x *Person) GetStartDate() string {
	if x != nil {
		return x.StartDate
	}
	return ""
}

func (x *Person) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

func (x *Person) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

func (x *Person) GetVerifyType() string {
	if x != nil {
		return x.VerifyType
	}
	return ""
}

func (x *Person) GetAddress() string {
	if x != nil {
		return x.Address
	}
	return ""
}

type PersonCertInfoResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Person                   *Person `protobuf:"bytes,1,opt,name=Person,json=person,proto3" json:"Person,omitempty"`
	AuthenticationSubmitTime string  `protobuf:"bytes,2,opt,name=AuthenticationSubmitTime,json=authenticationSubmitTime,proto3" json:"AuthenticationSubmitTime,omitempty"`
	PassTime                 string  `protobuf:"bytes,3,opt,name=PassTime,json=passTime,proto3" json:"PassTime,omitempty"`
	TransactionNo            string  `protobuf:"bytes,4,opt,name=TransactionNo,json=transactionNo,proto3" json:"TransactionNo,omitempty"`
	Type                     string  `protobuf:"bytes,5,opt,name=Type,json=type,proto3" json:"Type,omitempty"`
}

func (x *PersonCertInfoResponse) Reset() {
	*x = PersonCertInfoResponse{}
	mi := &file_contract_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PersonCertInfoResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PersonCertInfoResponse) ProtoMessage() {}

func (x *PersonCertInfoResponse) ProtoReflect() protoreflect.Message {
	mi := &file_contract_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PersonCertInfoResponse.ProtoReflect.Descriptor instead.
func (*PersonCertInfoResponse) Descriptor() ([]byte, []int) {
	return file_contract_proto_rawDescGZIP(), []int{4}
}

func (x *PersonCertInfoResponse) GetPerson() *Person {
	if x != nil {
		return x.Person
	}
	return nil
}

func (x *PersonCertInfoResponse) GetAuthenticationSubmitTime() string {
	if x != nil {
		return x.AuthenticationSubmitTime
	}
	return ""
}

func (x *PersonCertInfoResponse) GetPassTime() string {
	if x != nil {
		return x.PassTime
	}
	return ""
}

func (x *PersonCertInfoResponse) GetTransactionNo() string {
	if x != nil {
		return x.TransactionNo
	}
	return ""
}

func (x *PersonCertInfoResponse) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

type FindCertInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	VerifiedSerialno string `protobuf:"bytes,1,opt,name=VerifiedSerialno,json=verifiedSerialno,proto3" json:"VerifiedSerialno,omitempty"`
	Fdd              *Fdd   `protobuf:"bytes,2,opt,name=fdd,proto3" json:"fdd,omitempty"` //法大大的配置,有责使用，无则默认
}

func (x *FindCertInfo) Reset() {
	*x = FindCertInfo{}
	mi := &file_contract_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *FindCertInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FindCertInfo) ProtoMessage() {}

func (x *FindCertInfo) ProtoReflect() protoreflect.Message {
	mi := &file_contract_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FindCertInfo.ProtoReflect.Descriptor instead.
func (*FindCertInfo) Descriptor() ([]byte, []int) {
	return file_contract_proto_rawDescGZIP(), []int{5}
}

func (x *FindCertInfo) GetVerifiedSerialno() string {
	if x != nil {
		return x.VerifiedSerialno
	}
	return ""
}

func (x *FindCertInfo) GetFdd() *Fdd {
	if x != nil {
		return x.Fdd
	}
	return nil
}

type Company struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AuditFailReason      string `protobuf:"bytes,1,opt,name=AuditFailReason,proto3" json:"AuditFailReason,omitempty"`
	AuditorTime          string `protobuf:"bytes,2,opt,name=AuditorTime,proto3" json:"AuditorTime,omitempty"`
	CertificatesType     string `protobuf:"bytes,3,opt,name=CertificatesType,proto3" json:"CertificatesType,omitempty"`
	CompanyEmail         string `protobuf:"bytes,4,opt,name=CompanyEmail,proto3" json:"CompanyEmail,omitempty"`
	CompanyName          string `protobuf:"bytes,5,opt,name=CompanyName,proto3" json:"CompanyName,omitempty"`
	Hasagent             string `protobuf:"bytes,6,opt,name=Hasagent,proto3" json:"Hasagent,omitempty"`
	Legal                string `protobuf:"bytes,7,opt,name=Legal,proto3" json:"Legal,omitempty"`
	LegalMobile          string `protobuf:"bytes,8,opt,name=LegalMobile,proto3" json:"LegalMobile,omitempty"`
	LegalName            string `protobuf:"bytes,9,opt,name=LegalName,proto3" json:"LegalName,omitempty"`
	Organization         string `protobuf:"bytes,10,opt,name=Organization,proto3" json:"Organization,omitempty"`
	OrganizationPath     string `protobuf:"bytes,11,opt,name=OrganizationPath,proto3" json:"OrganizationPath,omitempty"`
	OrganizationType     string `protobuf:"bytes,12,opt,name=OrganizationType,proto3" json:"OrganizationType,omitempty"`
	RegFormPath          string `protobuf:"bytes,13,opt,name=RegFormPath,proto3" json:"RegFormPath,omitempty"`
	RelatedTransactionNo string `protobuf:"bytes,14,opt,name=RelatedTransactionNo,proto3" json:"RelatedTransactionNo,omitempty"`
	Status               string `protobuf:"bytes,15,opt,name=Status,proto3" json:"Status,omitempty"`
	VerifyType           string `protobuf:"bytes,16,opt,name=VerifyType,proto3" json:"VerifyType,omitempty"`
}

func (x *Company) Reset() {
	*x = Company{}
	mi := &file_contract_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Company) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Company) ProtoMessage() {}

func (x *Company) ProtoReflect() protoreflect.Message {
	mi := &file_contract_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Company.ProtoReflect.Descriptor instead.
func (*Company) Descriptor() ([]byte, []int) {
	return file_contract_proto_rawDescGZIP(), []int{6}
}

func (x *Company) GetAuditFailReason() string {
	if x != nil {
		return x.AuditFailReason
	}
	return ""
}

func (x *Company) GetAuditorTime() string {
	if x != nil {
		return x.AuditorTime
	}
	return ""
}

func (x *Company) GetCertificatesType() string {
	if x != nil {
		return x.CertificatesType
	}
	return ""
}

func (x *Company) GetCompanyEmail() string {
	if x != nil {
		return x.CompanyEmail
	}
	return ""
}

func (x *Company) GetCompanyName() string {
	if x != nil {
		return x.CompanyName
	}
	return ""
}

func (x *Company) GetHasagent() string {
	if x != nil {
		return x.Hasagent
	}
	return ""
}

func (x *Company) GetLegal() string {
	if x != nil {
		return x.Legal
	}
	return ""
}

func (x *Company) GetLegalMobile() string {
	if x != nil {
		return x.LegalMobile
	}
	return ""
}

func (x *Company) GetLegalName() string {
	if x != nil {
		return x.LegalName
	}
	return ""
}

func (x *Company) GetOrganization() string {
	if x != nil {
		return x.Organization
	}
	return ""
}

func (x *Company) GetOrganizationPath() string {
	if x != nil {
		return x.OrganizationPath
	}
	return ""
}

func (x *Company) GetOrganizationType() string {
	if x != nil {
		return x.OrganizationType
	}
	return ""
}

func (x *Company) GetRegFormPath() string {
	if x != nil {
		return x.RegFormPath
	}
	return ""
}

func (x *Company) GetRelatedTransactionNo() string {
	if x != nil {
		return x.RelatedTransactionNo
	}
	return ""
}

func (x *Company) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

func (x *Company) GetVerifyType() string {
	if x != nil {
		return x.VerifyType
	}
	return ""
}

type Manager struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AreaCode             string `protobuf:"bytes,1,opt,name=AreaCode,proto3" json:"AreaCode,omitempty"`
	AuditFailReason      string `protobuf:"bytes,2,opt,name=AuditFailReason,proto3" json:"AuditFailReason,omitempty"`
	AuditorTime          string `protobuf:"bytes,3,opt,name=AuditorTime,proto3" json:"AuditorTime,omitempty"`
	BackgroundIdCardPath string `protobuf:"bytes,4,opt,name=BackgroundIdCardPath,proto3" json:"BackgroundIdCardPath,omitempty"`
	Birthday             string `protobuf:"bytes,5,opt,name=Birthday,proto3" json:"Birthday,omitempty"`
	ExpiresDate          string `protobuf:"bytes,6,opt,name=ExpiresDate,proto3" json:"ExpiresDate,omitempty"`
	Fork                 string `protobuf:"bytes,7,opt,name=Fork,proto3" json:"Fork,omitempty"`
	HeadPhotoPath        string `protobuf:"bytes,8,opt,name=HeadPhotoPath,proto3" json:"HeadPhotoPath,omitempty"`
	IdCard               string `protobuf:"bytes,9,opt,name=IdCard,proto3" json:"IdCard,omitempty"`
	IsLongTerm           string `protobuf:"bytes,10,opt,name=IsLongTerm,proto3" json:"IsLongTerm,omitempty"`
	IssueAuthority       string `protobuf:"bytes,11,opt,name=IssueAuthority,proto3" json:"IssueAuthority,omitempty"`
	Mobile               string `protobuf:"bytes,12,opt,name=Mobile,proto3" json:"Mobile,omitempty"`
	PersonName           string `protobuf:"bytes,13,opt,name=PersonName,proto3" json:"PersonName,omitempty"`
	PhotoUuid            string `protobuf:"bytes,14,opt,name=PhotoUuid,proto3" json:"PhotoUuid,omitempty"`
	Sex                  string `protobuf:"bytes,15,opt,name=Sex,proto3" json:"Sex,omitempty"`
	StartDate            string `protobuf:"bytes,16,opt,name=StartDate,proto3" json:"StartDate,omitempty"`
	Status               string `protobuf:"bytes,17,opt,name=Status,proto3" json:"Status,omitempty"`
	Type                 string `protobuf:"bytes,18,opt,name=Type,proto3" json:"Type,omitempty"`
	VerifyType           string `protobuf:"bytes,19,opt,name=VerifyType,proto3" json:"VerifyType,omitempty"`
	Address              string `protobuf:"bytes,20,opt,name=Address,proto3" json:"Address,omitempty"`
}

func (x *Manager) Reset() {
	*x = Manager{}
	mi := &file_contract_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Manager) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Manager) ProtoMessage() {}

func (x *Manager) ProtoReflect() protoreflect.Message {
	mi := &file_contract_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Manager.ProtoReflect.Descriptor instead.
func (*Manager) Descriptor() ([]byte, []int) {
	return file_contract_proto_rawDescGZIP(), []int{7}
}

func (x *Manager) GetAreaCode() string {
	if x != nil {
		return x.AreaCode
	}
	return ""
}

func (x *Manager) GetAuditFailReason() string {
	if x != nil {
		return x.AuditFailReason
	}
	return ""
}

func (x *Manager) GetAuditorTime() string {
	if x != nil {
		return x.AuditorTime
	}
	return ""
}

func (x *Manager) GetBackgroundIdCardPath() string {
	if x != nil {
		return x.BackgroundIdCardPath
	}
	return ""
}

func (x *Manager) GetBirthday() string {
	if x != nil {
		return x.Birthday
	}
	return ""
}

func (x *Manager) GetExpiresDate() string {
	if x != nil {
		return x.ExpiresDate
	}
	return ""
}

func (x *Manager) GetFork() string {
	if x != nil {
		return x.Fork
	}
	return ""
}

func (x *Manager) GetHeadPhotoPath() string {
	if x != nil {
		return x.HeadPhotoPath
	}
	return ""
}

func (x *Manager) GetIdCard() string {
	if x != nil {
		return x.IdCard
	}
	return ""
}

func (x *Manager) GetIsLongTerm() string {
	if x != nil {
		return x.IsLongTerm
	}
	return ""
}

func (x *Manager) GetIssueAuthority() string {
	if x != nil {
		return x.IssueAuthority
	}
	return ""
}

func (x *Manager) GetMobile() string {
	if x != nil {
		return x.Mobile
	}
	return ""
}

func (x *Manager) GetPersonName() string {
	if x != nil {
		return x.PersonName
	}
	return ""
}

func (x *Manager) GetPhotoUuid() string {
	if x != nil {
		return x.PhotoUuid
	}
	return ""
}

func (x *Manager) GetSex() string {
	if x != nil {
		return x.Sex
	}
	return ""
}

func (x *Manager) GetStartDate() string {
	if x != nil {
		return x.StartDate
	}
	return ""
}

func (x *Manager) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

func (x *Manager) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

func (x *Manager) GetVerifyType() string {
	if x != nil {
		return x.VerifyType
	}
	return ""
}

func (x *Manager) GetAddress() string {
	if x != nil {
		return x.Address
	}
	return ""
}

type CompanyCertInfoResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Company                  *Company `protobuf:"bytes,1,opt,name=Company,proto3" json:"Company,omitempty"`
	Manager                  *Manager `protobuf:"bytes,2,opt,name=Manager,json=manager,proto3" json:"Manager,omitempty"`
	AuthenticationSubmitTime string   `protobuf:"bytes,3,opt,name=AuthenticationSubmitTime,json=authenticationSubmitTime,proto3" json:"AuthenticationSubmitTime,omitempty"`
	PassTime                 string   `protobuf:"bytes,4,opt,name=PassTime,json=passTime,proto3" json:"PassTime,omitempty"`
	TransactionNo            string   `protobuf:"bytes,5,opt,name=TransactionNo,json=transactionNo,proto3" json:"TransactionNo,omitempty"`
	Type                     string   `protobuf:"bytes,6,opt,name=Type,json=type,proto3" json:"Type,omitempty"`
}

func (x *CompanyCertInfoResponse) Reset() {
	*x = CompanyCertInfoResponse{}
	mi := &file_contract_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CompanyCertInfoResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CompanyCertInfoResponse) ProtoMessage() {}

func (x *CompanyCertInfoResponse) ProtoReflect() protoreflect.Message {
	mi := &file_contract_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CompanyCertInfoResponse.ProtoReflect.Descriptor instead.
func (*CompanyCertInfoResponse) Descriptor() ([]byte, []int) {
	return file_contract_proto_rawDescGZIP(), []int{8}
}

func (x *CompanyCertInfoResponse) GetCompany() *Company {
	if x != nil {
		return x.Company
	}
	return nil
}

func (x *CompanyCertInfoResponse) GetManager() *Manager {
	if x != nil {
		return x.Manager
	}
	return nil
}

func (x *CompanyCertInfoResponse) GetAuthenticationSubmitTime() string {
	if x != nil {
		return x.AuthenticationSubmitTime
	}
	return ""
}

func (x *CompanyCertInfoResponse) GetPassTime() string {
	if x != nil {
		return x.PassTime
	}
	return ""
}

func (x *CompanyCertInfoResponse) GetTransactionNo() string {
	if x != nil {
		return x.TransactionNo
	}
	return ""
}

func (x *CompanyCertInfoResponse) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

type AuthStatusResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AuthType      uint64 `protobuf:"varint,1,opt,name=AuthType,json=authType,proto3" json:"AuthType,omitempty"`
	ContractId    string `protobuf:"bytes,2,opt,name=ContractId,json=contractId,proto3" json:"ContractId,omitempty"`
	Status        uint64 `protobuf:"varint,3,opt,name=Status,json=status,proto3" json:"Status,omitempty"`
	TransactionId string `protobuf:"bytes,4,opt,name=TransactionId,json=transactionId,proto3" json:"TransactionId,omitempty"`
}

func (x *AuthStatusResponse) Reset() {
	*x = AuthStatusResponse{}
	mi := &file_contract_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AuthStatusResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AuthStatusResponse) ProtoMessage() {}

func (x *AuthStatusResponse) ProtoReflect() protoreflect.Message {
	mi := &file_contract_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AuthStatusResponse.ProtoReflect.Descriptor instead.
func (*AuthStatusResponse) Descriptor() ([]byte, []int) {
	return file_contract_proto_rawDescGZIP(), []int{9}
}

func (x *AuthStatusResponse) GetAuthType() uint64 {
	if x != nil {
		return x.AuthType
	}
	return 0
}

func (x *AuthStatusResponse) GetContractId() string {
	if x != nil {
		return x.ContractId
	}
	return ""
}

func (x *AuthStatusResponse) GetStatus() uint64 {
	if x != nil {
		return x.Status
	}
	return 0
}

func (x *AuthStatusResponse) GetTransactionId() string {
	if x != nil {
		return x.TransactionId
	}
	return ""
}

type ApplyCertRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CustomerId       string `protobuf:"bytes,1,opt,name=CustomerId,json=customerId,proto3" json:"CustomerId,omitempty"`
	VerifiedSerialno string `protobuf:"bytes,2,opt,name=VerifiedSerialno,json=verifiedSerialno,proto3" json:"VerifiedSerialno,omitempty"`
	Fdd              *Fdd   `protobuf:"bytes,3,opt,name=fdd,proto3" json:"fdd,omitempty"` //法大大的配置,有责使用，无则默认
}

func (x *ApplyCertRequest) Reset() {
	*x = ApplyCertRequest{}
	mi := &file_contract_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ApplyCertRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ApplyCertRequest) ProtoMessage() {}

func (x *ApplyCertRequest) ProtoReflect() protoreflect.Message {
	mi := &file_contract_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ApplyCertRequest.ProtoReflect.Descriptor instead.
func (*ApplyCertRequest) Descriptor() ([]byte, []int) {
	return file_contract_proto_rawDescGZIP(), []int{10}
}

func (x *ApplyCertRequest) GetCustomerId() string {
	if x != nil {
		return x.CustomerId
	}
	return ""
}

func (x *ApplyCertRequest) GetVerifiedSerialno() string {
	if x != nil {
		return x.VerifiedSerialno
	}
	return ""
}

func (x *ApplyCertRequest) GetFdd() *Fdd {
	if x != nil {
		return x.Fdd
	}
	return nil
}

type ViewCommonResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	DownloadUrl string `protobuf:"bytes,1,opt,name=DownloadUrl,json=downloadUrl,proto3" json:"DownloadUrl,omitempty"`
	ViewPdfUrl  string `protobuf:"bytes,2,opt,name=ViewPdfUrl,json=viewPdfUrl,proto3" json:"ViewPdfUrl,omitempty"`
}

func (x *ViewCommonResponse) Reset() {
	*x = ViewCommonResponse{}
	mi := &file_contract_proto_msgTypes[11]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ViewCommonResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ViewCommonResponse) ProtoMessage() {}

func (x *ViewCommonResponse) ProtoReflect() protoreflect.Message {
	mi := &file_contract_proto_msgTypes[11]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ViewCommonResponse.ProtoReflect.Descriptor instead.
func (*ViewCommonResponse) Descriptor() ([]byte, []int) {
	return file_contract_proto_rawDescGZIP(), []int{11}
}

func (x *ViewCommonResponse) GetDownloadUrl() string {
	if x != nil {
		return x.DownloadUrl
	}
	return ""
}

func (x *ViewCommonResponse) GetViewPdfUrl() string {
	if x != nil {
		return x.ViewPdfUrl
	}
	return ""
}

type PdfTemplateKeysRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TemplateId string `protobuf:"bytes,1,opt,name=TemplateId,json=templateId,proto3" json:"TemplateId,omitempty"`
	Fdd        *Fdd   `protobuf:"bytes,2,opt,name=fdd,proto3" json:"fdd,omitempty"` //法大大的配置,有责使用，无则默认
}

func (x *PdfTemplateKeysRequest) Reset() {
	*x = PdfTemplateKeysRequest{}
	mi := &file_contract_proto_msgTypes[12]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PdfTemplateKeysRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PdfTemplateKeysRequest) ProtoMessage() {}

func (x *PdfTemplateKeysRequest) ProtoReflect() protoreflect.Message {
	mi := &file_contract_proto_msgTypes[12]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PdfTemplateKeysRequest.ProtoReflect.Descriptor instead.
func (*PdfTemplateKeysRequest) Descriptor() ([]byte, []int) {
	return file_contract_proto_rawDescGZIP(), []int{12}
}

func (x *PdfTemplateKeysRequest) GetTemplateId() string {
	if x != nil {
		return x.TemplateId
	}
	return ""
}

func (x *PdfTemplateKeysRequest) GetFdd() *Fdd {
	if x != nil {
		return x.Fdd
	}
	return nil
}

type PdfTemplateKeysResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Keys []string `protobuf:"bytes,1,rep,name=Keys,proto3" json:"Keys,omitempty"`
}

func (x *PdfTemplateKeysResponse) Reset() {
	*x = PdfTemplateKeysResponse{}
	mi := &file_contract_proto_msgTypes[13]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PdfTemplateKeysResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PdfTemplateKeysResponse) ProtoMessage() {}

func (x *PdfTemplateKeysResponse) ProtoReflect() protoreflect.Message {
	mi := &file_contract_proto_msgTypes[13]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PdfTemplateKeysResponse.ProtoReflect.Descriptor instead.
func (*PdfTemplateKeysResponse) Descriptor() ([]byte, []int) {
	return file_contract_proto_rawDescGZIP(), []int{13}
}

func (x *PdfTemplateKeysResponse) GetKeys() []string {
	if x != nil {
		return x.Keys
	}
	return nil
}

type JumpCommonResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	JumpUrl string `protobuf:"bytes,1,opt,name=JumpUrl,json=jumpUrl,proto3" json:"JumpUrl,omitempty"`
}

func (x *JumpCommonResponse) Reset() {
	*x = JumpCommonResponse{}
	mi := &file_contract_proto_msgTypes[14]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *JumpCommonResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*JumpCommonResponse) ProtoMessage() {}

func (x *JumpCommonResponse) ProtoReflect() protoreflect.Message {
	mi := &file_contract_proto_msgTypes[14]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use JumpCommonResponse.ProtoReflect.Descriptor instead.
func (*JumpCommonResponse) Descriptor() ([]byte, []int) {
	return file_contract_proto_rawDescGZIP(), []int{14}
}

func (x *JumpCommonResponse) GetJumpUrl() string {
	if x != nil {
		return x.JumpUrl
	}
	return ""
}

type ContractRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ContractId string `protobuf:"bytes,1,opt,name=ContractId,json=contractId,proto3" json:"ContractId,omitempty"`
	Fdd        *Fdd   `protobuf:"bytes,13,opt,name=fdd,proto3" json:"fdd,omitempty"` //法大大的配置,有责使用，无则默认
}

func (x *ContractRequest) Reset() {
	*x = ContractRequest{}
	mi := &file_contract_proto_msgTypes[15]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ContractRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ContractRequest) ProtoMessage() {}

func (x *ContractRequest) ProtoReflect() protoreflect.Message {
	mi := &file_contract_proto_msgTypes[15]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ContractRequest.ProtoReflect.Descriptor instead.
func (*ContractRequest) Descriptor() ([]byte, []int) {
	return file_contract_proto_rawDescGZIP(), []int{15}
}

func (x *ContractRequest) GetContractId() string {
	if x != nil {
		return x.ContractId
	}
	return ""
}

func (x *ContractRequest) GetFdd() *Fdd {
	if x != nil {
		return x.Fdd
	}
	return nil
}

type ExtSignRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TransactionId     string `protobuf:"bytes,1,opt,name=TransactionId,json=transactionId,proto3" json:"TransactionId,omitempty"`
	ContractId        string `protobuf:"bytes,2,opt,name=ContractId,json=contractId,proto3" json:"ContractId,omitempty"`
	CustomerId        string `protobuf:"bytes,3,opt,name=CustomerId,json=customerId,proto3" json:"CustomerId,omitempty"`
	ReturnUrl         string `protobuf:"bytes,4,opt,name=ReturnUrl,json=returnUrl,proto3" json:"ReturnUrl,omitempty"`
	DocTitle          string `protobuf:"bytes,5,opt,name=DocTitle,json=docTitle,proto3" json:"DocTitle,omitempty"`
	OpenEnvironment   string `protobuf:"bytes,6,opt,name=OpenEnvironment,json=openEnvironment,proto3" json:"OpenEnvironment,omitempty"`
	MobileSignType    string `protobuf:"bytes,7,opt,name=MobileSignType,json=mobileSignType,proto3" json:"MobileSignType,omitempty"`
	SignKeyword       string `protobuf:"bytes,8,opt,name=SignKeyword,json=signKeyword,proto3" json:"SignKeyword,omitempty"`
	Keyx              string `protobuf:"bytes,9,opt,name=Keyx,json=keyx,proto3" json:"Keyx,omitempty"`
	Keyy              string `protobuf:"bytes,10,opt,name=Keyy,json=keyy,proto3" json:"Keyy,omitempty"`
	SignatureShowTime string `protobuf:"bytes,11,opt,name=SignatureShowTime,json=signatureShowTime,proto3" json:"SignatureShowTime,omitempty"`
	PcHandSignature   string `protobuf:"bytes,12,opt,name=PcHandSignature,proto3" json:"PcHandSignature,omitempty"`
	Fdd               *Fdd   `protobuf:"bytes,13,opt,name=fdd,proto3" json:"fdd,omitempty"` //法大大的配置,有责使用，无则默认
}

func (x *ExtSignRequest) Reset() {
	*x = ExtSignRequest{}
	mi := &file_contract_proto_msgTypes[16]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ExtSignRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ExtSignRequest) ProtoMessage() {}

func (x *ExtSignRequest) ProtoReflect() protoreflect.Message {
	mi := &file_contract_proto_msgTypes[16]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ExtSignRequest.ProtoReflect.Descriptor instead.
func (*ExtSignRequest) Descriptor() ([]byte, []int) {
	return file_contract_proto_rawDescGZIP(), []int{16}
}

func (x *ExtSignRequest) GetTransactionId() string {
	if x != nil {
		return x.TransactionId
	}
	return ""
}

func (x *ExtSignRequest) GetContractId() string {
	if x != nil {
		return x.ContractId
	}
	return ""
}

func (x *ExtSignRequest) GetCustomerId() string {
	if x != nil {
		return x.CustomerId
	}
	return ""
}

func (x *ExtSignRequest) GetReturnUrl() string {
	if x != nil {
		return x.ReturnUrl
	}
	return ""
}

func (x *ExtSignRequest) GetDocTitle() string {
	if x != nil {
		return x.DocTitle
	}
	return ""
}

func (x *ExtSignRequest) GetOpenEnvironment() string {
	if x != nil {
		return x.OpenEnvironment
	}
	return ""
}

func (x *ExtSignRequest) GetMobileSignType() string {
	if x != nil {
		return x.MobileSignType
	}
	return ""
}

func (x *ExtSignRequest) GetSignKeyword() string {
	if x != nil {
		return x.SignKeyword
	}
	return ""
}

func (x *ExtSignRequest) GetKeyx() string {
	if x != nil {
		return x.Keyx
	}
	return ""
}

func (x *ExtSignRequest) GetKeyy() string {
	if x != nil {
		return x.Keyy
	}
	return ""
}

func (x *ExtSignRequest) GetSignatureShowTime() string {
	if x != nil {
		return x.SignatureShowTime
	}
	return ""
}

func (x *ExtSignRequest) GetPcHandSignature() string {
	if x != nil {
		return x.PcHandSignature
	}
	return ""
}

func (x *ExtSignRequest) GetFdd() *Fdd {
	if x != nil {
		return x.Fdd
	}
	return nil
}

type BeforeAuthSignRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TransactionId string `protobuf:"bytes,1,opt,name=TransactionId,json=transactionId,proto3" json:"TransactionId,omitempty"`
	ContractId    string `protobuf:"bytes,2,opt,name=ContractId,json=contractId,proto3" json:"ContractId,omitempty"`
	CustomerId    string `protobuf:"bytes,3,opt,name=CustomerId,json=customerId,proto3" json:"CustomerId,omitempty"`
	ReturnUrl     string `protobuf:"bytes,4,opt,name=ReturnUrl,json=returnUrl,proto3" json:"ReturnUrl,omitempty"`
	NotifyUrl     string `protobuf:"bytes,5,opt,name=NotifyUrl,json=notifyUrl,proto3" json:"NotifyUrl,omitempty"`
	Fdd           *Fdd   `protobuf:"bytes,6,opt,name=fdd,proto3" json:"fdd,omitempty"` //法大大的配置,有责使用，无则默认
}

func (x *BeforeAuthSignRequest) Reset() {
	*x = BeforeAuthSignRequest{}
	mi := &file_contract_proto_msgTypes[17]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BeforeAuthSignRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BeforeAuthSignRequest) ProtoMessage() {}

func (x *BeforeAuthSignRequest) ProtoReflect() protoreflect.Message {
	mi := &file_contract_proto_msgTypes[17]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BeforeAuthSignRequest.ProtoReflect.Descriptor instead.
func (*BeforeAuthSignRequest) Descriptor() ([]byte, []int) {
	return file_contract_proto_rawDescGZIP(), []int{17}
}

func (x *BeforeAuthSignRequest) GetTransactionId() string {
	if x != nil {
		return x.TransactionId
	}
	return ""
}

func (x *BeforeAuthSignRequest) GetContractId() string {
	if x != nil {
		return x.ContractId
	}
	return ""
}

func (x *BeforeAuthSignRequest) GetCustomerId() string {
	if x != nil {
		return x.CustomerId
	}
	return ""
}

func (x *BeforeAuthSignRequest) GetReturnUrl() string {
	if x != nil {
		return x.ReturnUrl
	}
	return ""
}

func (x *BeforeAuthSignRequest) GetNotifyUrl() string {
	if x != nil {
		return x.NotifyUrl
	}
	return ""
}

func (x *BeforeAuthSignRequest) GetFdd() *Fdd {
	if x != nil {
		return x.Fdd
	}
	return nil
}

type UploadDocsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ContractId string `protobuf:"bytes,1,opt,name=ContractId,json=customerId,proto3" json:"ContractId,omitempty"`
	DocTitle   string `protobuf:"bytes,2,opt,name=DocTitle,json=docTitle,proto3" json:"DocTitle,omitempty"`
	DocUrl     string `protobuf:"bytes,3,opt,name=DocUrl,json=docUrl,proto3" json:"DocUrl,omitempty"`
	File       string `protobuf:"bytes,4,opt,name=File,json=file,proto3" json:"File,omitempty"`
	// string  doc_type        = 1     [json_name = "customerId"];
	Fdd *Fdd `protobuf:"bytes,6,opt,name=fdd,proto3" json:"fdd,omitempty"` //法大大的配置,有责使用，无则默认
}

func (x *UploadDocsRequest) Reset() {
	*x = UploadDocsRequest{}
	mi := &file_contract_proto_msgTypes[18]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UploadDocsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UploadDocsRequest) ProtoMessage() {}

func (x *UploadDocsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_contract_proto_msgTypes[18]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UploadDocsRequest.ProtoReflect.Descriptor instead.
func (*UploadDocsRequest) Descriptor() ([]byte, []int) {
	return file_contract_proto_rawDescGZIP(), []int{18}
}

func (x *UploadDocsRequest) GetContractId() string {
	if x != nil {
		return x.ContractId
	}
	return ""
}

func (x *UploadDocsRequest) GetDocTitle() string {
	if x != nil {
		return x.DocTitle
	}
	return ""
}

func (x *UploadDocsRequest) GetDocUrl() string {
	if x != nil {
		return x.DocUrl
	}
	return ""
}

func (x *UploadDocsRequest) GetFile() string {
	if x != nil {
		return x.File
	}
	return ""
}

func (x *UploadDocsRequest) GetFdd() *Fdd {
	if x != nil {
		return x.Fdd
	}
	return nil
}

type CompanyVerifyRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CustomerId string `protobuf:"bytes,1,opt,name=CustomerId,json=customerId,proto3" json:"CustomerId,omitempty"`
	Fdd        *Fdd   `protobuf:"bytes,2,opt,name=fdd,proto3" json:"fdd,omitempty"` //法大大的配置,有责使用，无则默认
}

func (x *CompanyVerifyRequest) Reset() {
	*x = CompanyVerifyRequest{}
	mi := &file_contract_proto_msgTypes[19]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CompanyVerifyRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CompanyVerifyRequest) ProtoMessage() {}

func (x *CompanyVerifyRequest) ProtoReflect() protoreflect.Message {
	mi := &file_contract_proto_msgTypes[19]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CompanyVerifyRequest.ProtoReflect.Descriptor instead.
func (*CompanyVerifyRequest) Descriptor() ([]byte, []int) {
	return file_contract_proto_rawDescGZIP(), []int{19}
}

func (x *CompanyVerifyRequest) GetCustomerId() string {
	if x != nil {
		return x.CustomerId
	}
	return ""
}

func (x *CompanyVerifyRequest) GetFdd() *Fdd {
	if x != nil {
		return x.Fdd
	}
	return nil
}

type UploadTemplateResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CustomerId string `protobuf:"bytes,1,opt,name=CustomerId,json=customerId,proto3" json:"CustomerId,omitempty"`
}

func (x *UploadTemplateResponse) Reset() {
	*x = UploadTemplateResponse{}
	mi := &file_contract_proto_msgTypes[20]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UploadTemplateResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UploadTemplateResponse) ProtoMessage() {}

func (x *UploadTemplateResponse) ProtoReflect() protoreflect.Message {
	mi := &file_contract_proto_msgTypes[20]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UploadTemplateResponse.ProtoReflect.Descriptor instead.
func (*UploadTemplateResponse) Descriptor() ([]byte, []int) {
	return file_contract_proto_rawDescGZIP(), []int{20}
}

func (x *UploadTemplateResponse) GetCustomerId() string {
	if x != nil {
		return x.CustomerId
	}
	return ""
}

type PersonVerifyRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CustomerId      string        `protobuf:"bytes,1,opt,name=CustomerId,json=customerId,proto3" json:"CustomerId,omitempty"`
	VerifiedWay     string        `protobuf:"bytes,2,opt,name=VerifiedWay,json=verifiedWay,proto3" json:"VerifiedWay,omitempty"`
	CustomerName    string        `protobuf:"bytes,3,opt,name=CustomerName,json=customerName,proto3" json:"CustomerName,omitempty"`
	CustomerIdentNo string        `protobuf:"bytes,4,opt,name=CustomerIdentNo,json=customerIdentNo,proto3" json:"CustomerIdentNo,omitempty"`
	Mobile          string        `protobuf:"bytes,5,opt,name=Mobile,json=mobile,proto3" json:"Mobile,omitempty"`
	ReturnUrl       string        `protobuf:"bytes,6,opt,name=ReturnUrl,json=returnUrl,proto3" json:"ReturnUrl,omitempty"`
	CertType        string        `protobuf:"bytes,7,opt,name=certType,proto3" json:"certType,omitempty"`
	ApiUrl          string        `protobuf:"bytes,8,opt,name=apiUrl,proto3" json:"apiUrl,omitempty"`
	IsMini          string        `protobuf:"bytes,9,opt,name=isMini,proto3" json:"isMini,omitempty"`
	Fdd             *Fdd          `protobuf:"bytes,10,opt,name=fdd,proto3" json:"fdd,omitempty"` //法大大的配置,有责使用，无则默认
	ResultType      FddResultType `protobuf:"varint,11,opt,name=resultType,proto3,enum=contract.FddResultType" json:"resultType,omitempty"`
	NotifyUrl       string        `protobuf:"bytes,12,opt,name=notifyUrl,proto3" json:"notifyUrl,omitempty"`
}

func (x *PersonVerifyRequest) Reset() {
	*x = PersonVerifyRequest{}
	mi := &file_contract_proto_msgTypes[21]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PersonVerifyRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PersonVerifyRequest) ProtoMessage() {}

func (x *PersonVerifyRequest) ProtoReflect() protoreflect.Message {
	mi := &file_contract_proto_msgTypes[21]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PersonVerifyRequest.ProtoReflect.Descriptor instead.
func (*PersonVerifyRequest) Descriptor() ([]byte, []int) {
	return file_contract_proto_rawDescGZIP(), []int{21}
}

func (x *PersonVerifyRequest) GetCustomerId() string {
	if x != nil {
		return x.CustomerId
	}
	return ""
}

func (x *PersonVerifyRequest) GetVerifiedWay() string {
	if x != nil {
		return x.VerifiedWay
	}
	return ""
}

func (x *PersonVerifyRequest) GetCustomerName() string {
	if x != nil {
		return x.CustomerName
	}
	return ""
}

func (x *PersonVerifyRequest) GetCustomerIdentNo() string {
	if x != nil {
		return x.CustomerIdentNo
	}
	return ""
}

func (x *PersonVerifyRequest) GetMobile() string {
	if x != nil {
		return x.Mobile
	}
	return ""
}

func (x *PersonVerifyRequest) GetReturnUrl() string {
	if x != nil {
		return x.ReturnUrl
	}
	return ""
}

func (x *PersonVerifyRequest) GetCertType() string {
	if x != nil {
		return x.CertType
	}
	return ""
}

func (x *PersonVerifyRequest) GetApiUrl() string {
	if x != nil {
		return x.ApiUrl
	}
	return ""
}

func (x *PersonVerifyRequest) GetIsMini() string {
	if x != nil {
		return x.IsMini
	}
	return ""
}

func (x *PersonVerifyRequest) GetFdd() *Fdd {
	if x != nil {
		return x.Fdd
	}
	return nil
}

func (x *PersonVerifyRequest) GetResultType() FddResultType {
	if x != nil {
		return x.ResultType
	}
	return FddResultType_NoResultType
}

func (x *PersonVerifyRequest) GetNotifyUrl() string {
	if x != nil {
		return x.NotifyUrl
	}
	return ""
}

type PersonVerifyResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TransactionNo string `protobuf:"bytes,1,opt,name=TransactionNo,proto3" json:"TransactionNo,omitempty"`
	Url           string `protobuf:"bytes,2,opt,name=Url,proto3" json:"Url,omitempty"`
}

func (x *PersonVerifyResponse) Reset() {
	*x = PersonVerifyResponse{}
	mi := &file_contract_proto_msgTypes[22]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PersonVerifyResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PersonVerifyResponse) ProtoMessage() {}

func (x *PersonVerifyResponse) ProtoReflect() protoreflect.Message {
	mi := &file_contract_proto_msgTypes[22]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PersonVerifyResponse.ProtoReflect.Descriptor instead.
func (*PersonVerifyResponse) Descriptor() ([]byte, []int) {
	return file_contract_proto_rawDescGZIP(), []int{22}
}

func (x *PersonVerifyResponse) GetTransactionNo() string {
	if x != nil {
		return x.TransactionNo
	}
	return ""
}

func (x *PersonVerifyResponse) GetUrl() string {
	if x != nil {
		return x.Url
	}
	return ""
}

type RegisterResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CustomerId string `protobuf:"bytes,1,opt,name=CustomerId,json=customerId,proto3" json:"CustomerId,omitempty"`
}

func (x *RegisterResponse) Reset() {
	*x = RegisterResponse{}
	mi := &file_contract_proto_msgTypes[23]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RegisterResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RegisterResponse) ProtoMessage() {}

func (x *RegisterResponse) ProtoReflect() protoreflect.Message {
	mi := &file_contract_proto_msgTypes[23]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RegisterResponse.ProtoReflect.Descriptor instead.
func (*RegisterResponse) Descriptor() ([]byte, []int) {
	return file_contract_proto_rawDescGZIP(), []int{23}
}

func (x *RegisterResponse) GetCustomerId() string {
	if x != nil {
		return x.CustomerId
	}
	return ""
}

type CustomerIdRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CustomerId string `protobuf:"bytes,1,opt,name=CustomerId,json=customerId,proto3" json:"CustomerId,omitempty"`
	Fdd        *Fdd   `protobuf:"bytes,2,opt,name=fdd,proto3" json:"fdd,omitempty"` //法大大的配置,有责使用，无则默认
}

func (x *CustomerIdRequest) Reset() {
	*x = CustomerIdRequest{}
	mi := &file_contract_proto_msgTypes[24]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CustomerIdRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CustomerIdRequest) ProtoMessage() {}

func (x *CustomerIdRequest) ProtoReflect() protoreflect.Message {
	mi := &file_contract_proto_msgTypes[24]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CustomerIdRequest.ProtoReflect.Descriptor instead.
func (*CustomerIdRequest) Descriptor() ([]byte, []int) {
	return file_contract_proto_rawDescGZIP(), []int{24}
}

func (x *CustomerIdRequest) GetCustomerId() string {
	if x != nil {
		return x.CustomerId
	}
	return ""
}

func (x *CustomerIdRequest) GetFdd() *Fdd {
	if x != nil {
		return x.Fdd
	}
	return nil
}

type CommonResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *CommonResponse) Reset() {
	*x = CommonResponse{}
	mi := &file_contract_proto_msgTypes[25]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CommonResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CommonResponse) ProtoMessage() {}

func (x *CommonResponse) ProtoReflect() protoreflect.Message {
	mi := &file_contract_proto_msgTypes[25]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CommonResponse.ProtoReflect.Descriptor instead.
func (*CommonResponse) Descriptor() ([]byte, []int) {
	return file_contract_proto_rawDescGZIP(), []int{25}
}

type RegisterOrgRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	OpenId string `protobuf:"bytes,1,opt,name=OpenId,json=openId,proto3" json:"OpenId,omitempty"`
	Fdd    *Fdd   `protobuf:"bytes,2,opt,name=fdd,proto3" json:"fdd,omitempty"` //法大大的配置,有责使用，无则默认
}

func (x *RegisterOrgRequest) Reset() {
	*x = RegisterOrgRequest{}
	mi := &file_contract_proto_msgTypes[26]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RegisterOrgRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RegisterOrgRequest) ProtoMessage() {}

func (x *RegisterOrgRequest) ProtoReflect() protoreflect.Message {
	mi := &file_contract_proto_msgTypes[26]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RegisterOrgRequest.ProtoReflect.Descriptor instead.
func (*RegisterOrgRequest) Descriptor() ([]byte, []int) {
	return file_contract_proto_rawDescGZIP(), []int{26}
}

func (x *RegisterOrgRequest) GetOpenId() string {
	if x != nil {
		return x.OpenId
	}
	return ""
}

func (x *RegisterOrgRequest) GetFdd() *Fdd {
	if x != nil {
		return x.Fdd
	}
	return nil
}

type RegisterPersonRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	OpenId string `protobuf:"bytes,1,opt,name=OpenId,json=openId,proto3" json:"OpenId,omitempty"`
	Fdd    *Fdd   `protobuf:"bytes,11,opt,name=fdd,proto3" json:"fdd,omitempty"` //法大大的配置,有责使用，无则默认
}

func (x *RegisterPersonRequest) Reset() {
	*x = RegisterPersonRequest{}
	mi := &file_contract_proto_msgTypes[27]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RegisterPersonRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RegisterPersonRequest) ProtoMessage() {}

func (x *RegisterPersonRequest) ProtoReflect() protoreflect.Message {
	mi := &file_contract_proto_msgTypes[27]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RegisterPersonRequest.ProtoReflect.Descriptor instead.
func (*RegisterPersonRequest) Descriptor() ([]byte, []int) {
	return file_contract_proto_rawDescGZIP(), []int{27}
}

func (x *RegisterPersonRequest) GetOpenId() string {
	if x != nil {
		return x.OpenId
	}
	return ""
}

func (x *RegisterPersonRequest) GetFdd() *Fdd {
	if x != nil {
		return x.Fdd
	}
	return nil
}

type AddSignRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CustomerId  string `protobuf:"bytes,1,opt,name=CustomerId,json=customerId,proto3" json:"CustomerId,omitempty"`
	ReadImgByte []byte `protobuf:"bytes,2,opt,name=ReadImgByte,json=readImgByte,proto3" json:"ReadImgByte,omitempty"`
	Fdd         *Fdd   `protobuf:"bytes,3,opt,name=fdd,proto3" json:"fdd,omitempty"` //法大大的配置,有责使用，无则默认
}

func (x *AddSignRequest) Reset() {
	*x = AddSignRequest{}
	mi := &file_contract_proto_msgTypes[28]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AddSignRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddSignRequest) ProtoMessage() {}

func (x *AddSignRequest) ProtoReflect() protoreflect.Message {
	mi := &file_contract_proto_msgTypes[28]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddSignRequest.ProtoReflect.Descriptor instead.
func (*AddSignRequest) Descriptor() ([]byte, []int) {
	return file_contract_proto_rawDescGZIP(), []int{28}
}

func (x *AddSignRequest) GetCustomerId() string {
	if x != nil {
		return x.CustomerId
	}
	return ""
}

func (x *AddSignRequest) GetReadImgByte() []byte {
	if x != nil {
		return x.ReadImgByte
	}
	return nil
}

func (x *AddSignRequest) GetFdd() *Fdd {
	if x != nil {
		return x.Fdd
	}
	return nil
}

type AddSignResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	SignatureId string `protobuf:"bytes,1,opt,name=SignatureId,json=signatureId,proto3" json:"SignatureId,omitempty"`
}

func (x *AddSignResponse) Reset() {
	*x = AddSignResponse{}
	mi := &file_contract_proto_msgTypes[29]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AddSignResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddSignResponse) ProtoMessage() {}

func (x *AddSignResponse) ProtoReflect() protoreflect.Message {
	mi := &file_contract_proto_msgTypes[29]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddSignResponse.ProtoReflect.Descriptor instead.
func (*AddSignResponse) Descriptor() ([]byte, []int) {
	return file_contract_proto_rawDescGZIP(), []int{29}
}

func (x *AddSignResponse) GetSignatureId() string {
	if x != nil {
		return x.SignatureId
	}
	return ""
}

type CustomSignRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CustomerId string `protobuf:"bytes,1,opt,name=CustomerId,json=customerId,proto3" json:"CustomerId,omitempty"`
	Content    string `protobuf:"bytes,2,opt,name=Content,json=content,proto3" json:"Content,omitempty"`
	Fdd        *Fdd   `protobuf:"bytes,3,opt,name=fdd,proto3" json:"fdd,omitempty"` //法大大的配置,有责使用，无则默认
}

func (x *CustomSignRequest) Reset() {
	*x = CustomSignRequest{}
	mi := &file_contract_proto_msgTypes[30]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CustomSignRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CustomSignRequest) ProtoMessage() {}

func (x *CustomSignRequest) ProtoReflect() protoreflect.Message {
	mi := &file_contract_proto_msgTypes[30]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CustomSignRequest.ProtoReflect.Descriptor instead.
func (*CustomSignRequest) Descriptor() ([]byte, []int) {
	return file_contract_proto_rawDescGZIP(), []int{30}
}

func (x *CustomSignRequest) GetCustomerId() string {
	if x != nil {
		return x.CustomerId
	}
	return ""
}

func (x *CustomSignRequest) GetContent() string {
	if x != nil {
		return x.Content
	}
	return ""
}

func (x *CustomSignRequest) GetFdd() *Fdd {
	if x != nil {
		return x.Fdd
	}
	return nil
}

type CustomSignResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	SignatureImgBase64 string `protobuf:"bytes,1,opt,name=SignatureImgBase64,json=signatureImgBase64,proto3" json:"SignatureImgBase64,omitempty"`
}

func (x *CustomSignResponse) Reset() {
	*x = CustomSignResponse{}
	mi := &file_contract_proto_msgTypes[31]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CustomSignResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CustomSignResponse) ProtoMessage() {}

func (x *CustomSignResponse) ProtoReflect() protoreflect.Message {
	mi := &file_contract_proto_msgTypes[31]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CustomSignResponse.ProtoReflect.Descriptor instead.
func (*CustomSignResponse) Descriptor() ([]byte, []int) {
	return file_contract_proto_rawDescGZIP(), []int{31}
}

func (x *CustomSignResponse) GetSignatureImgBase64() string {
	if x != nil {
		return x.SignatureImgBase64
	}
	return ""
}

type UploadTemplateRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TemplateId string `protobuf:"bytes,1,opt,name=TemplateId,json=templateId,proto3" json:"TemplateId,omitempty"`
	DocUrl     string `protobuf:"bytes,2,opt,name=DocUrl,json=docUrl,proto3" json:"DocUrl,omitempty"`
	Fdd        *Fdd   `protobuf:"bytes,3,opt,name=fdd,proto3" json:"fdd,omitempty"` //法大大的配置,有责使用，无则默认
}

func (x *UploadTemplateRequest) Reset() {
	*x = UploadTemplateRequest{}
	mi := &file_contract_proto_msgTypes[32]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UploadTemplateRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UploadTemplateRequest) ProtoMessage() {}

func (x *UploadTemplateRequest) ProtoReflect() protoreflect.Message {
	mi := &file_contract_proto_msgTypes[32]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UploadTemplateRequest.ProtoReflect.Descriptor instead.
func (*UploadTemplateRequest) Descriptor() ([]byte, []int) {
	return file_contract_proto_rawDescGZIP(), []int{32}
}

func (x *UploadTemplateRequest) GetTemplateId() string {
	if x != nil {
		return x.TemplateId
	}
	return ""
}

func (x *UploadTemplateRequest) GetDocUrl() string {
	if x != nil {
		return x.DocUrl
	}
	return ""
}

func (x *UploadTemplateRequest) GetFdd() *Fdd {
	if x != nil {
		return x.Fdd
	}
	return nil
}

type ExtSignAutoRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TransactionId string `protobuf:"bytes,1,opt,name=TransactionId,json=transactionId,proto3" json:"TransactionId,omitempty"`
	ContractId    string `protobuf:"bytes,2,opt,name=ContractId,json=contractId,proto3" json:"ContractId,omitempty"`
	CustomerId    string `protobuf:"bytes,3,opt,name=CustomerId,json=customerId,proto3" json:"CustomerId,omitempty"`
	ClientRole    string `protobuf:"bytes,4,opt,name=ClientRole,json=clientRole,proto3" json:"ClientRole,omitempty"`
	DocTitle      string `protobuf:"bytes,5,opt,name=DocTitle,json=docTitle,proto3" json:"DocTitle,omitempty"`
	SignKeyword   string `protobuf:"bytes,6,opt,name=SignKeyword,json=signKeyword,proto3" json:"SignKeyword,omitempty"`
	SignatureId   string `protobuf:"bytes,7,opt,name=SignatureId,json=signatureId,proto3" json:"SignatureId,omitempty"`
	KeyX          string `protobuf:"bytes,9,opt,name=KeyX,json=keyX,proto3" json:"KeyX,omitempty"`
	KeyY          string `protobuf:"bytes,10,opt,name=KeyY,json=keyY,proto3" json:"KeyY,omitempty"`
	Fdd           *Fdd   `protobuf:"bytes,11,opt,name=fdd,proto3" json:"fdd,omitempty"` //法大大的配置,有责使用，无则默认
}

func (x *ExtSignAutoRequest) Reset() {
	*x = ExtSignAutoRequest{}
	mi := &file_contract_proto_msgTypes[33]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ExtSignAutoRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ExtSignAutoRequest) ProtoMessage() {}

func (x *ExtSignAutoRequest) ProtoReflect() protoreflect.Message {
	mi := &file_contract_proto_msgTypes[33]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ExtSignAutoRequest.ProtoReflect.Descriptor instead.
func (*ExtSignAutoRequest) Descriptor() ([]byte, []int) {
	return file_contract_proto_rawDescGZIP(), []int{33}
}

func (x *ExtSignAutoRequest) GetTransactionId() string {
	if x != nil {
		return x.TransactionId
	}
	return ""
}

func (x *ExtSignAutoRequest) GetContractId() string {
	if x != nil {
		return x.ContractId
	}
	return ""
}

func (x *ExtSignAutoRequest) GetCustomerId() string {
	if x != nil {
		return x.CustomerId
	}
	return ""
}

func (x *ExtSignAutoRequest) GetClientRole() string {
	if x != nil {
		return x.ClientRole
	}
	return ""
}

func (x *ExtSignAutoRequest) GetDocTitle() string {
	if x != nil {
		return x.DocTitle
	}
	return ""
}

func (x *ExtSignAutoRequest) GetSignKeyword() string {
	if x != nil {
		return x.SignKeyword
	}
	return ""
}

func (x *ExtSignAutoRequest) GetSignatureId() string {
	if x != nil {
		return x.SignatureId
	}
	return ""
}

func (x *ExtSignAutoRequest) GetKeyX() string {
	if x != nil {
		return x.KeyX
	}
	return ""
}

func (x *ExtSignAutoRequest) GetKeyY() string {
	if x != nil {
		return x.KeyY
	}
	return ""
}

func (x *ExtSignAutoRequest) GetFdd() *Fdd {
	if x != nil {
		return x.Fdd
	}
	return nil
}

type GenerateContractRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TemplateId    string `protobuf:"bytes,1,opt,name=TemplateId,json=templateId,proto3" json:"TemplateId,omitempty"`
	ContractId    string `protobuf:"bytes,2,opt,name=ContractId,json=contractId,proto3" json:"ContractId,omitempty"`
	ParameterMap  string `protobuf:"bytes,3,opt,name=ParameterMap,proto3" json:"ParameterMap,omitempty"`
	DynamicTables string `protobuf:"bytes,4,opt,name=DynamicTables,json=dynamicTables,proto3" json:"DynamicTables,omitempty"`
	DocTitle      string `protobuf:"bytes,5,opt,name=DocTitle,json=docTitle,proto3" json:"DocTitle,omitempty"`
	ApiHost       string `protobuf:"bytes,6,opt,name=apiHost,proto3" json:"apiHost,omitempty"`
	Fdd           *Fdd   `protobuf:"bytes,7,opt,name=fdd,proto3" json:"fdd,omitempty"` //法大大的配置,有责使用，无则默认
}

func (x *GenerateContractRequest) Reset() {
	*x = GenerateContractRequest{}
	mi := &file_contract_proto_msgTypes[34]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GenerateContractRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GenerateContractRequest) ProtoMessage() {}

func (x *GenerateContractRequest) ProtoReflect() protoreflect.Message {
	mi := &file_contract_proto_msgTypes[34]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GenerateContractRequest.ProtoReflect.Descriptor instead.
func (*GenerateContractRequest) Descriptor() ([]byte, []int) {
	return file_contract_proto_rawDescGZIP(), []int{34}
}

func (x *GenerateContractRequest) GetTemplateId() string {
	if x != nil {
		return x.TemplateId
	}
	return ""
}

func (x *GenerateContractRequest) GetContractId() string {
	if x != nil {
		return x.ContractId
	}
	return ""
}

func (x *GenerateContractRequest) GetParameterMap() string {
	if x != nil {
		return x.ParameterMap
	}
	return ""
}

func (x *GenerateContractRequest) GetDynamicTables() string {
	if x != nil {
		return x.DynamicTables
	}
	return ""
}

func (x *GenerateContractRequest) GetDocTitle() string {
	if x != nil {
		return x.DocTitle
	}
	return ""
}

func (x *GenerateContractRequest) GetApiHost() string {
	if x != nil {
		return x.ApiHost
	}
	return ""
}

func (x *GenerateContractRequest) GetFdd() *Fdd {
	if x != nil {
		return x.Fdd
	}
	return nil
}

type SignaturePosition struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Pagenum string `protobuf:"bytes,1,opt,name=pagenum,proto3" json:"pagenum,omitempty"` //需要保留json的omit值
	X       string `protobuf:"bytes,2,opt,name=x,proto3" json:"x,omitempty"`             //需要保留json的omit值
	Y       string `protobuf:"bytes,3,opt,name=y,proto3" json:"y,omitempty"`             //需要保留json的omit值
}

func (x *SignaturePosition) Reset() {
	*x = SignaturePosition{}
	mi := &file_contract_proto_msgTypes[35]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SignaturePosition) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SignaturePosition) ProtoMessage() {}

func (x *SignaturePosition) ProtoReflect() protoreflect.Message {
	mi := &file_contract_proto_msgTypes[35]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SignaturePosition.ProtoReflect.Descriptor instead.
func (*SignaturePosition) Descriptor() ([]byte, []int) {
	return file_contract_proto_rawDescGZIP(), []int{35}
}

func (x *SignaturePosition) GetPagenum() string {
	if x != nil {
		return x.Pagenum
	}
	return ""
}

func (x *SignaturePosition) GetX() string {
	if x != nil {
		return x.X
	}
	return ""
}

func (x *SignaturePosition) GetY() string {
	if x != nil {
		return x.Y
	}
	return ""
}

type SignData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ContractId         string               `protobuf:"bytes,1,opt,name=ContractId,proto3" json:"ContractId,omitempty"`                 //需要保留json的omit值
	TransactionId      string               `protobuf:"bytes,2,opt,name=TransactionId,proto3" json:"TransactionId,omitempty"`           //需要保留json的omit值
	SignKeyword        string               `protobuf:"bytes,3,opt,name=signKeyword,proto3" json:"signKeyword,omitempty"`               //需要保留json的omit值
	SignaturePositions []*SignaturePosition `protobuf:"bytes,4,rep,name=signaturePositions,proto3" json:"signaturePositions,omitempty"` //需要保留json的omit值
	Keyx               string               `protobuf:"bytes,5,opt,name=keyx,proto3" json:"keyx,omitempty"`
	Keyy               string               `protobuf:"bytes,6,opt,name=keyy,proto3" json:"keyy,omitempty"`
}

func (x *SignData) Reset() {
	*x = SignData{}
	mi := &file_contract_proto_msgTypes[36]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SignData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SignData) ProtoMessage() {}

func (x *SignData) ProtoReflect() protoreflect.Message {
	mi := &file_contract_proto_msgTypes[36]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SignData.ProtoReflect.Descriptor instead.
func (*SignData) Descriptor() ([]byte, []int) {
	return file_contract_proto_rawDescGZIP(), []int{36}
}

func (x *SignData) GetContractId() string {
	if x != nil {
		return x.ContractId
	}
	return ""
}

func (x *SignData) GetTransactionId() string {
	if x != nil {
		return x.TransactionId
	}
	return ""
}

func (x *SignData) GetSignKeyword() string {
	if x != nil {
		return x.SignKeyword
	}
	return ""
}

func (x *SignData) GetSignaturePositions() []*SignaturePosition {
	if x != nil {
		return x.SignaturePositions
	}
	return nil
}

func (x *SignData) GetKeyx() string {
	if x != nil {
		return x.Keyx
	}
	return ""
}

func (x *SignData) GetKeyy() string {
	if x != nil {
		return x.Keyy
	}
	return ""
}

type GenerateContractInBatchesRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	BatchId    string      `protobuf:"bytes,1,opt,name=batchId,json=batch_id,proto3" json:"batchId,omitempty"`          //必填 批次号（流水号）
	SignData   []*SignData `protobuf:"bytes,2,rep,name=signData,json=sign_data,proto3" json:"signData,omitempty"`       //必填 签章数据
	CustomerId string      `protobuf:"bytes,3,opt,name=customerId,json=customer_id,proto3" json:"customerId,omitempty"` //必填 客户编号 JsonArray[sign_data] 需要URLEncoder，编码UTF-8
	// string outhCustomerId =4 [json_name="outh_customer_id"];//选填 代理人客户编号
	BatchTitle     string `protobuf:"bytes,5,opt,name=batchTitle,json=batch_title,proto3" json:"batchTitle,omitempty"`              //必填 批量请求标题需要 如 xx 批量签署” 需要URLEncoder
	MobileSignType string `protobuf:"bytes,6,opt,name=mobileSignType,json=mobile_sign_type,proto3" json:"mobileSignType,omitempty"` //必填 签章类型
	ReturnUrl      string `protobuf:"bytes,7,opt,name=returnUrl,json=return_url,proto3" json:"returnUrl,omitempty"`                 //必填 页面跳转 URL,长度限制500 需要URLEncoder//  string NotifyUrl =8 [json_name="notify_url"];
	Fdd            *Fdd   `protobuf:"bytes,8,opt,name=fdd,proto3" json:"fdd,omitempty"`                                             //法大大的配置,有责使用，无则默认
}

func (x *GenerateContractInBatchesRequest) Reset() {
	*x = GenerateContractInBatchesRequest{}
	mi := &file_contract_proto_msgTypes[37]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GenerateContractInBatchesRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GenerateContractInBatchesRequest) ProtoMessage() {}

func (x *GenerateContractInBatchesRequest) ProtoReflect() protoreflect.Message {
	mi := &file_contract_proto_msgTypes[37]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GenerateContractInBatchesRequest.ProtoReflect.Descriptor instead.
func (*GenerateContractInBatchesRequest) Descriptor() ([]byte, []int) {
	return file_contract_proto_rawDescGZIP(), []int{37}
}

func (x *GenerateContractInBatchesRequest) GetBatchId() string {
	if x != nil {
		return x.BatchId
	}
	return ""
}

func (x *GenerateContractInBatchesRequest) GetSignData() []*SignData {
	if x != nil {
		return x.SignData
	}
	return nil
}

func (x *GenerateContractInBatchesRequest) GetCustomerId() string {
	if x != nil {
		return x.CustomerId
	}
	return ""
}

func (x *GenerateContractInBatchesRequest) GetBatchTitle() string {
	if x != nil {
		return x.BatchTitle
	}
	return ""
}

func (x *GenerateContractInBatchesRequest) GetMobileSignType() string {
	if x != nil {
		return x.MobileSignType
	}
	return ""
}

func (x *GenerateContractInBatchesRequest) GetReturnUrl() string {
	if x != nil {
		return x.ReturnUrl
	}
	return ""
}

func (x *GenerateContractInBatchesRequest) GetFdd() *Fdd {
	if x != nil {
		return x.Fdd
	}
	return nil
}

type GenerateContractInBatchesResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	JumpUrl string `protobuf:"bytes,3,opt,name=jumpUrl,proto3" json:"jumpUrl,omitempty"`
}

func (x *GenerateContractInBatchesResponse) Reset() {
	*x = GenerateContractInBatchesResponse{}
	mi := &file_contract_proto_msgTypes[38]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GenerateContractInBatchesResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GenerateContractInBatchesResponse) ProtoMessage() {}

func (x *GenerateContractInBatchesResponse) ProtoReflect() protoreflect.Message {
	mi := &file_contract_proto_msgTypes[38]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GenerateContractInBatchesResponse.ProtoReflect.Descriptor instead.
func (*GenerateContractInBatchesResponse) Descriptor() ([]byte, []int) {
	return file_contract_proto_rawDescGZIP(), []int{38}
}

func (x *GenerateContractInBatchesResponse) GetJumpUrl() string {
	if x != nil {
		return x.JumpUrl
	}
	return ""
}

var File_contract_proto protoreflect.FileDescriptor

var file_contract_proto_rawDesc = []byte{
	0x0a, 0x0e, 0x63, 0x6f, 0x6e, 0x74, 0x72, 0x61, 0x63, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x12, 0x08, 0x63, 0x6f, 0x6e, 0x74, 0x72, 0x61, 0x63, 0x74, 0x22, 0x6d, 0x0a, 0x03, 0x46, 0x64,
	0x64, 0x12, 0x10, 0x0a, 0x03, 0x75, 0x72, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03,
	0x75, 0x72, 0x6c, 0x12, 0x14, 0x0a, 0x05, 0x61, 0x70, 0x70, 0x49, 0x64, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x05, 0x61, 0x70, 0x70, 0x49, 0x64, 0x12, 0x1c, 0x0a, 0x09, 0x61, 0x70, 0x70,
	0x53, 0x65, 0x63, 0x72, 0x65, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x61, 0x70,
	0x70, 0x53, 0x65, 0x63, 0x72, 0x65, 0x74, 0x12, 0x20, 0x0a, 0x0b, 0x63, 0x6f, 0x6e, 0x74, 0x65,
	0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x63, 0x6f,
	0x6e, 0x74, 0x65, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x22, 0x83, 0x02, 0x0a, 0x1c, 0x43, 0x68,
	0x65, 0x63, 0x6b, 0x50, 0x65, 0x72, 0x73, 0x6f, 0x6e, 0x56, 0x65, 0x72, 0x69, 0x66, 0x79, 0x53,
	0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x24, 0x0a, 0x0d, 0x74, 0x72,
	0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x4e, 0x6f, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0d, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x4e, 0x6f,
	0x12, 0x1e, 0x0a, 0x0a, 0x70, 0x65, 0x72, 0x73, 0x6f, 0x6e, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x70, 0x65, 0x72, 0x73, 0x6f, 0x6e, 0x4e, 0x61, 0x6d, 0x65,
	0x12, 0x16, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x20, 0x0a, 0x0b, 0x63, 0x6f, 0x6d, 0x70,
	0x61, 0x6e, 0x79, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x63,
	0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x2e, 0x0a, 0x12, 0x61, 0x75,
	0x74, 0x68, 0x65, 0x6e, 0x74, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65,
	0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x12, 0x61, 0x75, 0x74, 0x68, 0x65, 0x6e, 0x74, 0x69,
	0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x73, 0x69,
	0x67, 0x6e, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x73, 0x69, 0x67, 0x6e, 0x12, 0x1f,
	0x0a, 0x03, 0x66, 0x64, 0x64, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0d, 0x2e, 0x63, 0x6f,
	0x6e, 0x74, 0x72, 0x61, 0x63, 0x74, 0x2e, 0x46, 0x64, 0x64, 0x52, 0x03, 0x66, 0x64, 0x64, 0x22,
	0x97, 0x01, 0x0a, 0x18, 0x43, 0x61, 0x6e, 0x63, 0x65, 0x6c, 0x45, 0x78, 0x74, 0x53, 0x69, 0x67,
	0x6e, 0x41, 0x75, 0x74, 0x6f, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1e, 0x0a, 0x0a,
	0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0a, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x49, 0x64, 0x12, 0x1c, 0x0a, 0x09,
	0x4e, 0x6f, 0x74, 0x69, 0x66, 0x79, 0x55, 0x72, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x09, 0x4e, 0x6f, 0x74, 0x69, 0x66, 0x79, 0x55, 0x72, 0x6c, 0x12, 0x1c, 0x0a, 0x09, 0x72, 0x65,
	0x74, 0x75, 0x72, 0x6e, 0x55, 0x72, 0x6c, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x72,
	0x65, 0x74, 0x75, 0x72, 0x6e, 0x55, 0x72, 0x6c, 0x12, 0x1f, 0x0a, 0x03, 0x66, 0x64, 0x64, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0d, 0x2e, 0x63, 0x6f, 0x6e, 0x74, 0x72, 0x61, 0x63, 0x74,
	0x2e, 0x46, 0x64, 0x64, 0x52, 0x03, 0x66, 0x64, 0x64, 0x22, 0xb0, 0x06, 0x0a, 0x06, 0x50, 0x65,
	0x72, 0x73, 0x6f, 0x6e, 0x12, 0x1a, 0x0a, 0x08, 0x41, 0x72, 0x65, 0x61, 0x43, 0x6f, 0x64, 0x65,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x41, 0x72, 0x65, 0x61, 0x43, 0x6f, 0x64, 0x65,
	0x12, 0x28, 0x0a, 0x0f, 0x41, 0x75, 0x64, 0x69, 0x74, 0x46, 0x61, 0x69, 0x6c, 0x52, 0x65, 0x61,
	0x73, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x41, 0x75, 0x64, 0x69, 0x74,
	0x46, 0x61, 0x69, 0x6c, 0x52, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x12, 0x20, 0x0a, 0x0b, 0x41, 0x75,
	0x64, 0x69, 0x74, 0x6f, 0x72, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0b, 0x41, 0x75, 0x64, 0x69, 0x74, 0x6f, 0x72, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x32, 0x0a, 0x14,
	0x42, 0x61, 0x63, 0x6b, 0x67, 0x72, 0x6f, 0x75, 0x6e, 0x64, 0x49, 0x64, 0x43, 0x61, 0x72, 0x64,
	0x50, 0x61, 0x74, 0x68, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x14, 0x42, 0x61, 0x63, 0x6b,
	0x67, 0x72, 0x6f, 0x75, 0x6e, 0x64, 0x49, 0x64, 0x43, 0x61, 0x72, 0x64, 0x50, 0x61, 0x74, 0x68,
	0x12, 0x1e, 0x0a, 0x0a, 0x42, 0x61, 0x6e, 0x6b, 0x43, 0x61, 0x72, 0x64, 0x4e, 0x6f, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x42, 0x61, 0x6e, 0x6b, 0x43, 0x61, 0x72, 0x64, 0x4e, 0x6f,
	0x12, 0x1a, 0x0a, 0x08, 0x42, 0x69, 0x72, 0x74, 0x68, 0x64, 0x61, 0x79, 0x18, 0x06, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x08, 0x42, 0x69, 0x72, 0x74, 0x68, 0x64, 0x61, 0x79, 0x12, 0x1a, 0x0a, 0x08,
	0x43, 0x65, 0x72, 0x74, 0x54, 0x79, 0x70, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08,
	0x43, 0x65, 0x72, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x45, 0x78, 0x70, 0x69,
	0x72, 0x65, 0x73, 0x44, 0x61, 0x74, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x45,
	0x78, 0x70, 0x69, 0x72, 0x65, 0x73, 0x44, 0x61, 0x74, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x46, 0x6f,
	0x72, 0x6b, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x46, 0x6f, 0x72, 0x6b, 0x12, 0x2c,
	0x0a, 0x11, 0x47, 0x65, 0x73, 0x74, 0x75, 0x72, 0x65, 0x73, 0x50, 0x68, 0x6f, 0x74, 0x6f, 0x50,
	0x61, 0x74, 0x68, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x11, 0x47, 0x65, 0x73, 0x74, 0x75,
	0x72, 0x65, 0x73, 0x50, 0x68, 0x6f, 0x74, 0x6f, 0x50, 0x61, 0x74, 0x68, 0x12, 0x24, 0x0a, 0x0d,
	0x48, 0x65, 0x61, 0x64, 0x50, 0x68, 0x6f, 0x74, 0x6f, 0x50, 0x61, 0x74, 0x68, 0x18, 0x0b, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0d, 0x48, 0x65, 0x61, 0x64, 0x50, 0x68, 0x6f, 0x74, 0x6f, 0x50, 0x61,
	0x74, 0x68, 0x12, 0x16, 0x0a, 0x06, 0x49, 0x64, 0x43, 0x61, 0x72, 0x64, 0x18, 0x0c, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x06, 0x49, 0x64, 0x43, 0x61, 0x72, 0x64, 0x12, 0x1e, 0x0a, 0x0a, 0x49, 0x73,
	0x4c, 0x6f, 0x6e, 0x67, 0x54, 0x65, 0x72, 0x6d, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a,
	0x49, 0x73, 0x4c, 0x6f, 0x6e, 0x67, 0x54, 0x65, 0x72, 0x6d, 0x12, 0x2c, 0x0a, 0x11, 0x49, 0x73,
	0x50, 0x61, 0x73, 0x73, 0x46, 0x6f, 0x75, 0x72, 0x45, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x18,
	0x0e, 0x20, 0x01, 0x28, 0x01, 0x52, 0x11, 0x49, 0x73, 0x50, 0x61, 0x73, 0x73, 0x46, 0x6f, 0x75,
	0x72, 0x45, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x12, 0x2e, 0x0a, 0x12, 0x49, 0x73, 0x50, 0x61,
	0x73, 0x73, 0x54, 0x68, 0x72, 0x65, 0x65, 0x45, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x18, 0x0f,
	0x20, 0x01, 0x28, 0x01, 0x52, 0x12, 0x49, 0x73, 0x50, 0x61, 0x73, 0x73, 0x54, 0x68, 0x72, 0x65,
	0x65, 0x45, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x12, 0x26, 0x0a, 0x0e, 0x49, 0x73, 0x73, 0x75,
	0x65, 0x41, 0x75, 0x74, 0x68, 0x6f, 0x72, 0x69, 0x74, 0x79, 0x18, 0x10, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0e, 0x49, 0x73, 0x73, 0x75, 0x65, 0x41, 0x75, 0x74, 0x68, 0x6f, 0x72, 0x69, 0x74, 0x79,
	0x12, 0x16, 0x0a, 0x06, 0x4d, 0x6f, 0x62, 0x69, 0x6c, 0x65, 0x18, 0x11, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x06, 0x4d, 0x6f, 0x62, 0x69, 0x6c, 0x65, 0x12, 0x1e, 0x0a, 0x0a, 0x50, 0x65, 0x72, 0x73,
	0x6f, 0x6e, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x12, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x50, 0x65,
	0x72, 0x73, 0x6f, 0x6e, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1c, 0x0a, 0x09, 0x50, 0x68, 0x6f, 0x74,
	0x6f, 0x55, 0x75, 0x69, 0x64, 0x18, 0x13, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x50, 0x68, 0x6f,
	0x74, 0x6f, 0x55, 0x75, 0x69, 0x64, 0x12, 0x10, 0x0a, 0x03, 0x53, 0x65, 0x78, 0x18, 0x14, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x03, 0x53, 0x65, 0x78, 0x12, 0x1c, 0x0a, 0x09, 0x53, 0x74, 0x61, 0x72,
	0x74, 0x44, 0x61, 0x74, 0x65, 0x18, 0x15, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x53, 0x74, 0x61,
	0x72, 0x74, 0x44, 0x61, 0x74, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x18, 0x16, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x12,
	0x0a, 0x04, 0x54, 0x79, 0x70, 0x65, 0x18, 0x17, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x54, 0x79,
	0x70, 0x65, 0x12, 0x1e, 0x0a, 0x0a, 0x56, 0x65, 0x72, 0x69, 0x66, 0x79, 0x54, 0x79, 0x70, 0x65,
	0x18, 0x18, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x56, 0x65, 0x72, 0x69, 0x66, 0x79, 0x54, 0x79,
	0x70, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x18, 0x19, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x07, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x22, 0xd4, 0x01, 0x0a,
	0x16, 0x50, 0x65, 0x72, 0x73, 0x6f, 0x6e, 0x43, 0x65, 0x72, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x28, 0x0a, 0x06, 0x50, 0x65, 0x72, 0x73, 0x6f,
	0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x10, 0x2e, 0x63, 0x6f, 0x6e, 0x74, 0x72, 0x61,
	0x63, 0x74, 0x2e, 0x50, 0x65, 0x72, 0x73, 0x6f, 0x6e, 0x52, 0x06, 0x70, 0x65, 0x72, 0x73, 0x6f,
	0x6e, 0x12, 0x3a, 0x0a, 0x18, 0x41, 0x75, 0x74, 0x68, 0x65, 0x6e, 0x74, 0x69, 0x63, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x53, 0x75, 0x62, 0x6d, 0x69, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x18, 0x61, 0x75, 0x74, 0x68, 0x65, 0x6e, 0x74, 0x69, 0x63, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x53, 0x75, 0x62, 0x6d, 0x69, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x1a, 0x0a,
	0x08, 0x50, 0x61, 0x73, 0x73, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x08, 0x70, 0x61, 0x73, 0x73, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x24, 0x0a, 0x0d, 0x54, 0x72, 0x61,
	0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x4e, 0x6f, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0d, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x4e, 0x6f, 0x12,
	0x12, 0x0a, 0x04, 0x54, 0x79, 0x70, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x74,
	0x79, 0x70, 0x65, 0x22, 0x5b, 0x0a, 0x0c, 0x46, 0x69, 0x6e, 0x64, 0x43, 0x65, 0x72, 0x74, 0x49,
	0x6e, 0x66, 0x6f, 0x12, 0x2a, 0x0a, 0x10, 0x56, 0x65, 0x72, 0x69, 0x66, 0x69, 0x65, 0x64, 0x53,
	0x65, 0x72, 0x69, 0x61, 0x6c, 0x6e, 0x6f, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x76,
	0x65, 0x72, 0x69, 0x66, 0x69, 0x65, 0x64, 0x53, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x6e, 0x6f, 0x12,
	0x1f, 0x0a, 0x03, 0x66, 0x64, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0d, 0x2e, 0x63,
	0x6f, 0x6e, 0x74, 0x72, 0x61, 0x63, 0x74, 0x2e, 0x46, 0x64, 0x64, 0x52, 0x03, 0x66, 0x64, 0x64,
	0x22, 0xc3, 0x04, 0x0a, 0x07, 0x43, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x12, 0x28, 0x0a, 0x0f,
	0x41, 0x75, 0x64, 0x69, 0x74, 0x46, 0x61, 0x69, 0x6c, 0x52, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x41, 0x75, 0x64, 0x69, 0x74, 0x46, 0x61, 0x69, 0x6c,
	0x52, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x12, 0x20, 0x0a, 0x0b, 0x41, 0x75, 0x64, 0x69, 0x74, 0x6f,
	0x72, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x41, 0x75, 0x64,
	0x69, 0x74, 0x6f, 0x72, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x2a, 0x0a, 0x10, 0x43, 0x65, 0x72, 0x74,
	0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x65, 0x73, 0x54, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x10, 0x43, 0x65, 0x72, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x65, 0x73,
	0x54, 0x79, 0x70, 0x65, 0x12, 0x22, 0x0a, 0x0c, 0x43, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x45,
	0x6d, 0x61, 0x69, 0x6c, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x43, 0x6f, 0x6d, 0x70,
	0x61, 0x6e, 0x79, 0x45, 0x6d, 0x61, 0x69, 0x6c, 0x12, 0x20, 0x0a, 0x0b, 0x43, 0x6f, 0x6d, 0x70,
	0x61, 0x6e, 0x79, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x43,
	0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x48, 0x61,
	0x73, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x48, 0x61,
	0x73, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x12, 0x14, 0x0a, 0x05, 0x4c, 0x65, 0x67, 0x61, 0x6c, 0x18,
	0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x4c, 0x65, 0x67, 0x61, 0x6c, 0x12, 0x20, 0x0a, 0x0b,
	0x4c, 0x65, 0x67, 0x61, 0x6c, 0x4d, 0x6f, 0x62, 0x69, 0x6c, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0b, 0x4c, 0x65, 0x67, 0x61, 0x6c, 0x4d, 0x6f, 0x62, 0x69, 0x6c, 0x65, 0x12, 0x1c,
	0x0a, 0x09, 0x4c, 0x65, 0x67, 0x61, 0x6c, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x09, 0x4c, 0x65, 0x67, 0x61, 0x6c, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x22, 0x0a, 0x0c,
	0x4f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x0a, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0c, 0x4f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x12, 0x2a, 0x0a, 0x10, 0x4f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x50, 0x61, 0x74, 0x68, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x4f, 0x72, 0x67, 0x61,
	0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x50, 0x61, 0x74, 0x68, 0x12, 0x2a, 0x0a, 0x10,
	0x4f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65,
	0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x4f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x52, 0x65, 0x67, 0x46,
	0x6f, 0x72, 0x6d, 0x50, 0x61, 0x74, 0x68, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x52,
	0x65, 0x67, 0x46, 0x6f, 0x72, 0x6d, 0x50, 0x61, 0x74, 0x68, 0x12, 0x32, 0x0a, 0x14, 0x52, 0x65,
	0x6c, 0x61, 0x74, 0x65, 0x64, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e,
	0x4e, 0x6f, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x09, 0x52, 0x14, 0x52, 0x65, 0x6c, 0x61, 0x74, 0x65,
	0x64, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x4e, 0x6f, 0x12, 0x16,
	0x0a, 0x06, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06,
	0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x1e, 0x0a, 0x0a, 0x56, 0x65, 0x72, 0x69, 0x66, 0x79,
	0x54, 0x79, 0x70, 0x65, 0x18, 0x10, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x56, 0x65, 0x72, 0x69,
	0x66, 0x79, 0x54, 0x79, 0x70, 0x65, 0x22, 0xe9, 0x04, 0x0a, 0x07, 0x4d, 0x61, 0x6e, 0x61, 0x67,
	0x65, 0x72, 0x12, 0x1a, 0x0a, 0x08, 0x41, 0x72, 0x65, 0x61, 0x43, 0x6f, 0x64, 0x65, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x41, 0x72, 0x65, 0x61, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x28,
	0x0a, 0x0f, 0x41, 0x75, 0x64, 0x69, 0x74, 0x46, 0x61, 0x69, 0x6c, 0x52, 0x65, 0x61, 0x73, 0x6f,
	0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x41, 0x75, 0x64, 0x69, 0x74, 0x46, 0x61,
	0x69, 0x6c, 0x52, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x12, 0x20, 0x0a, 0x0b, 0x41, 0x75, 0x64, 0x69,
	0x74, 0x6f, 0x72, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x41,
	0x75, 0x64, 0x69, 0x74, 0x6f, 0x72, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x32, 0x0a, 0x14, 0x42, 0x61,
	0x63, 0x6b, 0x67, 0x72, 0x6f, 0x75, 0x6e, 0x64, 0x49, 0x64, 0x43, 0x61, 0x72, 0x64, 0x50, 0x61,
	0x74, 0x68, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x14, 0x42, 0x61, 0x63, 0x6b, 0x67, 0x72,
	0x6f, 0x75, 0x6e, 0x64, 0x49, 0x64, 0x43, 0x61, 0x72, 0x64, 0x50, 0x61, 0x74, 0x68, 0x12, 0x1a,
	0x0a, 0x08, 0x42, 0x69, 0x72, 0x74, 0x68, 0x64, 0x61, 0x79, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x08, 0x42, 0x69, 0x72, 0x74, 0x68, 0x64, 0x61, 0x79, 0x12, 0x20, 0x0a, 0x0b, 0x45, 0x78,
	0x70, 0x69, 0x72, 0x65, 0x73, 0x44, 0x61, 0x74, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0b, 0x45, 0x78, 0x70, 0x69, 0x72, 0x65, 0x73, 0x44, 0x61, 0x74, 0x65, 0x12, 0x12, 0x0a, 0x04,
	0x46, 0x6f, 0x72, 0x6b, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x46, 0x6f, 0x72, 0x6b,
	0x12, 0x24, 0x0a, 0x0d, 0x48, 0x65, 0x61, 0x64, 0x50, 0x68, 0x6f, 0x74, 0x6f, 0x50, 0x61, 0x74,
	0x68, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x48, 0x65, 0x61, 0x64, 0x50, 0x68, 0x6f,
	0x74, 0x6f, 0x50, 0x61, 0x74, 0x68, 0x12, 0x16, 0x0a, 0x06, 0x49, 0x64, 0x43, 0x61, 0x72, 0x64,
	0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x49, 0x64, 0x43, 0x61, 0x72, 0x64, 0x12, 0x1e,
	0x0a, 0x0a, 0x49, 0x73, 0x4c, 0x6f, 0x6e, 0x67, 0x54, 0x65, 0x72, 0x6d, 0x18, 0x0a, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0a, 0x49, 0x73, 0x4c, 0x6f, 0x6e, 0x67, 0x54, 0x65, 0x72, 0x6d, 0x12, 0x26,
	0x0a, 0x0e, 0x49, 0x73, 0x73, 0x75, 0x65, 0x41, 0x75, 0x74, 0x68, 0x6f, 0x72, 0x69, 0x74, 0x79,
	0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x49, 0x73, 0x73, 0x75, 0x65, 0x41, 0x75, 0x74,
	0x68, 0x6f, 0x72, 0x69, 0x74, 0x79, 0x12, 0x16, 0x0a, 0x06, 0x4d, 0x6f, 0x62, 0x69, 0x6c, 0x65,
	0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x4d, 0x6f, 0x62, 0x69, 0x6c, 0x65, 0x12, 0x1e,
	0x0a, 0x0a, 0x50, 0x65, 0x72, 0x73, 0x6f, 0x6e, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x0d, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0a, 0x50, 0x65, 0x72, 0x73, 0x6f, 0x6e, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1c,
	0x0a, 0x09, 0x50, 0x68, 0x6f, 0x74, 0x6f, 0x55, 0x75, 0x69, 0x64, 0x18, 0x0e, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x09, 0x50, 0x68, 0x6f, 0x74, 0x6f, 0x55, 0x75, 0x69, 0x64, 0x12, 0x10, 0x0a, 0x03,
	0x53, 0x65, 0x78, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x53, 0x65, 0x78, 0x12, 0x1c,
	0x0a, 0x09, 0x53, 0x74, 0x61, 0x72, 0x74, 0x44, 0x61, 0x74, 0x65, 0x18, 0x10, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x09, 0x53, 0x74, 0x61, 0x72, 0x74, 0x44, 0x61, 0x74, 0x65, 0x12, 0x16, 0x0a, 0x06,
	0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x11, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x53, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x12, 0x12, 0x0a, 0x04, 0x54, 0x79, 0x70, 0x65, 0x18, 0x12, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x04, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1e, 0x0a, 0x0a, 0x56, 0x65, 0x72, 0x69,
	0x66, 0x79, 0x54, 0x79, 0x70, 0x65, 0x18, 0x13, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x56, 0x65,
	0x72, 0x69, 0x66, 0x79, 0x54, 0x79, 0x70, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x41, 0x64, 0x64, 0x72,
	0x65, 0x73, 0x73, 0x18, 0x14, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x41, 0x64, 0x64, 0x72, 0x65,
	0x73, 0x73, 0x22, 0x85, 0x02, 0x0a, 0x17, 0x43, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x43, 0x65,
	0x72, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x2b,
	0x0a, 0x07, 0x43, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x11, 0x2e, 0x63, 0x6f, 0x6e, 0x74, 0x72, 0x61, 0x63, 0x74, 0x2e, 0x43, 0x6f, 0x6d, 0x70, 0x61,
	0x6e, 0x79, 0x52, 0x07, 0x43, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x12, 0x2b, 0x0a, 0x07, 0x4d,
	0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x63,
	0x6f, 0x6e, 0x74, 0x72, 0x61, 0x63, 0x74, 0x2e, 0x4d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x52,
	0x07, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x12, 0x3a, 0x0a, 0x18, 0x41, 0x75, 0x74, 0x68,
	0x65, 0x6e, 0x74, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x75, 0x62, 0x6d, 0x69, 0x74,
	0x54, 0x69, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x18, 0x61, 0x75, 0x74, 0x68,
	0x65, 0x6e, 0x74, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x75, 0x62, 0x6d, 0x69, 0x74,
	0x54, 0x69, 0x6d, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x50, 0x61, 0x73, 0x73, 0x54, 0x69, 0x6d, 0x65,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x70, 0x61, 0x73, 0x73, 0x54, 0x69, 0x6d, 0x65,
	0x12, 0x24, 0x0a, 0x0d, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x4e,
	0x6f, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63,
	0x74, 0x69, 0x6f, 0x6e, 0x4e, 0x6f, 0x12, 0x12, 0x0a, 0x04, 0x54, 0x79, 0x70, 0x65, 0x18, 0x06,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x22, 0x8e, 0x01, 0x0a, 0x12, 0x41,
	0x75, 0x74, 0x68, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x12, 0x1a, 0x0a, 0x08, 0x41, 0x75, 0x74, 0x68, 0x54, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x04, 0x52, 0x08, 0x61, 0x75, 0x74, 0x68, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1e, 0x0a,
	0x0a, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x61, 0x63, 0x74, 0x49, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0a, 0x63, 0x6f, 0x6e, 0x74, 0x72, 0x61, 0x63, 0x74, 0x49, 0x64, 0x12, 0x16, 0x0a,
	0x06, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x04, 0x52, 0x06, 0x73,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x24, 0x0a, 0x0d, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63,
	0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x74, 0x72,
	0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x22, 0x7f, 0x0a, 0x10, 0x41,
	0x70, 0x70, 0x6c, 0x79, 0x43, 0x65, 0x72, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12,
	0x1e, 0x0a, 0x0a, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x49, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0a, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x49, 0x64, 0x12,
	0x2a, 0x0a, 0x10, 0x56, 0x65, 0x72, 0x69, 0x66, 0x69, 0x65, 0x64, 0x53, 0x65, 0x72, 0x69, 0x61,
	0x6c, 0x6e, 0x6f, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x76, 0x65, 0x72, 0x69, 0x66,
	0x69, 0x65, 0x64, 0x53, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x6e, 0x6f, 0x12, 0x1f, 0x0a, 0x03, 0x66,
	0x64, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0d, 0x2e, 0x63, 0x6f, 0x6e, 0x74, 0x72,
	0x61, 0x63, 0x74, 0x2e, 0x46, 0x64, 0x64, 0x52, 0x03, 0x66, 0x64, 0x64, 0x22, 0x56, 0x0a, 0x12,
	0x56, 0x69, 0x65, 0x77, 0x43, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x44, 0x6f, 0x77, 0x6e, 0x6c, 0x6f, 0x61, 0x64, 0x55, 0x72,
	0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x6f, 0x77, 0x6e, 0x6c, 0x6f, 0x61,
	0x64, 0x55, 0x72, 0x6c, 0x12, 0x1e, 0x0a, 0x0a, 0x56, 0x69, 0x65, 0x77, 0x50, 0x64, 0x66, 0x55,
	0x72, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x76, 0x69, 0x65, 0x77, 0x50, 0x64,
	0x66, 0x55, 0x72, 0x6c, 0x22, 0x59, 0x0a, 0x16, 0x50, 0x64, 0x66, 0x54, 0x65, 0x6d, 0x70, 0x6c,
	0x61, 0x74, 0x65, 0x4b, 0x65, 0x79, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1e,
	0x0a, 0x0a, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0a, 0x74, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x49, 0x64, 0x12, 0x1f,
	0x0a, 0x03, 0x66, 0x64, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0d, 0x2e, 0x63, 0x6f,
	0x6e, 0x74, 0x72, 0x61, 0x63, 0x74, 0x2e, 0x46, 0x64, 0x64, 0x52, 0x03, 0x66, 0x64, 0x64, 0x22,
	0x2d, 0x0a, 0x17, 0x50, 0x64, 0x66, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x4b, 0x65,
	0x79, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x4b, 0x65,
	0x79, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x09, 0x52, 0x04, 0x4b, 0x65, 0x79, 0x73, 0x22, 0x2e,
	0x0a, 0x12, 0x4a, 0x75, 0x6d, 0x70, 0x43, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x4a, 0x75, 0x6d, 0x70, 0x55, 0x72, 0x6c, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6a, 0x75, 0x6d, 0x70, 0x55, 0x72, 0x6c, 0x22, 0x52,
	0x0a, 0x0f, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x61, 0x63, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x12, 0x1e, 0x0a, 0x0a, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x61, 0x63, 0x74, 0x49, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x63, 0x6f, 0x6e, 0x74, 0x72, 0x61, 0x63, 0x74, 0x49,
	0x64, 0x12, 0x1f, 0x0a, 0x03, 0x66, 0x64, 0x64, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0d,
	0x2e, 0x63, 0x6f, 0x6e, 0x74, 0x72, 0x61, 0x63, 0x74, 0x2e, 0x46, 0x64, 0x64, 0x52, 0x03, 0x66,
	0x64, 0x64, 0x22, 0xc5, 0x03, 0x0a, 0x0e, 0x45, 0x78, 0x74, 0x53, 0x69, 0x67, 0x6e, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x24, 0x0a, 0x0d, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63,
	0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x74, 0x72,
	0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x12, 0x1e, 0x0a, 0x0a, 0x43,
	0x6f, 0x6e, 0x74, 0x72, 0x61, 0x63, 0x74, 0x49, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0a, 0x63, 0x6f, 0x6e, 0x74, 0x72, 0x61, 0x63, 0x74, 0x49, 0x64, 0x12, 0x1e, 0x0a, 0x0a, 0x43,
	0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x49, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0a, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x49, 0x64, 0x12, 0x1c, 0x0a, 0x09, 0x52,
	0x65, 0x74, 0x75, 0x72, 0x6e, 0x55, 0x72, 0x6c, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09,
	0x72, 0x65, 0x74, 0x75, 0x72, 0x6e, 0x55, 0x72, 0x6c, 0x12, 0x1a, 0x0a, 0x08, 0x44, 0x6f, 0x63,
	0x54, 0x69, 0x74, 0x6c, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x64, 0x6f, 0x63,
	0x54, 0x69, 0x74, 0x6c, 0x65, 0x12, 0x28, 0x0a, 0x0f, 0x4f, 0x70, 0x65, 0x6e, 0x45, 0x6e, 0x76,
	0x69, 0x72, 0x6f, 0x6e, 0x6d, 0x65, 0x6e, 0x74, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f,
	0x6f, 0x70, 0x65, 0x6e, 0x45, 0x6e, 0x76, 0x69, 0x72, 0x6f, 0x6e, 0x6d, 0x65, 0x6e, 0x74, 0x12,
	0x26, 0x0a, 0x0e, 0x4d, 0x6f, 0x62, 0x69, 0x6c, 0x65, 0x53, 0x69, 0x67, 0x6e, 0x54, 0x79, 0x70,
	0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x6d, 0x6f, 0x62, 0x69, 0x6c, 0x65, 0x53,
	0x69, 0x67, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x53, 0x69, 0x67, 0x6e, 0x4b,
	0x65, 0x79, 0x77, 0x6f, 0x72, 0x64, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x73, 0x69,
	0x67, 0x6e, 0x4b, 0x65, 0x79, 0x77, 0x6f, 0x72, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x4b, 0x65, 0x79,
	0x78, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6b, 0x65, 0x79, 0x78, 0x12, 0x12, 0x0a,
	0x04, 0x4b, 0x65, 0x79, 0x79, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6b, 0x65, 0x79,
	0x79, 0x12, 0x2c, 0x0a, 0x11, 0x53, 0x69, 0x67, 0x6e, 0x61, 0x74, 0x75, 0x72, 0x65, 0x53, 0x68,
	0x6f, 0x77, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x11, 0x73, 0x69,
	0x67, 0x6e, 0x61, 0x74, 0x75, 0x72, 0x65, 0x53, 0x68, 0x6f, 0x77, 0x54, 0x69, 0x6d, 0x65, 0x12,
	0x28, 0x0a, 0x0f, 0x50, 0x63, 0x48, 0x61, 0x6e, 0x64, 0x53, 0x69, 0x67, 0x6e, 0x61, 0x74, 0x75,
	0x72, 0x65, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x50, 0x63, 0x48, 0x61, 0x6e, 0x64,
	0x53, 0x69, 0x67, 0x6e, 0x61, 0x74, 0x75, 0x72, 0x65, 0x12, 0x1f, 0x0a, 0x03, 0x66, 0x64, 0x64,
	0x18, 0x0d, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0d, 0x2e, 0x63, 0x6f, 0x6e, 0x74, 0x72, 0x61, 0x63,
	0x74, 0x2e, 0x46, 0x64, 0x64, 0x52, 0x03, 0x66, 0x64, 0x64, 0x22, 0xda, 0x01, 0x0a, 0x15, 0x42,
	0x65, 0x66, 0x6f, 0x72, 0x65, 0x41, 0x75, 0x74, 0x68, 0x53, 0x69, 0x67, 0x6e, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x12, 0x24, 0x0a, 0x0d, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x74, 0x72, 0x61,
	0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x12, 0x1e, 0x0a, 0x0a, 0x43, 0x6f,
	0x6e, 0x74, 0x72, 0x61, 0x63, 0x74, 0x49, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a,
	0x63, 0x6f, 0x6e, 0x74, 0x72, 0x61, 0x63, 0x74, 0x49, 0x64, 0x12, 0x1e, 0x0a, 0x0a, 0x43, 0x75,
	0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x49, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a,
	0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x49, 0x64, 0x12, 0x1c, 0x0a, 0x09, 0x52, 0x65,
	0x74, 0x75, 0x72, 0x6e, 0x55, 0x72, 0x6c, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x72,
	0x65, 0x74, 0x75, 0x72, 0x6e, 0x55, 0x72, 0x6c, 0x12, 0x1c, 0x0a, 0x09, 0x4e, 0x6f, 0x74, 0x69,
	0x66, 0x79, 0x55, 0x72, 0x6c, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x6e, 0x6f, 0x74,
	0x69, 0x66, 0x79, 0x55, 0x72, 0x6c, 0x12, 0x1f, 0x0a, 0x03, 0x66, 0x64, 0x64, 0x18, 0x06, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x0d, 0x2e, 0x63, 0x6f, 0x6e, 0x74, 0x72, 0x61, 0x63, 0x74, 0x2e, 0x46,
	0x64, 0x64, 0x52, 0x03, 0x66, 0x64, 0x64, 0x22, 0x9c, 0x01, 0x0a, 0x11, 0x55, 0x70, 0x6c, 0x6f,
	0x61, 0x64, 0x44, 0x6f, 0x63, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1e, 0x0a,
	0x0a, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x61, 0x63, 0x74, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0a, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x49, 0x64, 0x12, 0x1a, 0x0a,
	0x08, 0x44, 0x6f, 0x63, 0x54, 0x69, 0x74, 0x6c, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x08, 0x64, 0x6f, 0x63, 0x54, 0x69, 0x74, 0x6c, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x44, 0x6f, 0x63,
	0x55, 0x72, 0x6c, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x64, 0x6f, 0x63, 0x55, 0x72,
	0x6c, 0x12, 0x12, 0x0a, 0x04, 0x46, 0x69, 0x6c, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x04, 0x66, 0x69, 0x6c, 0x65, 0x12, 0x1f, 0x0a, 0x03, 0x66, 0x64, 0x64, 0x18, 0x06, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x0d, 0x2e, 0x63, 0x6f, 0x6e, 0x74, 0x72, 0x61, 0x63, 0x74, 0x2e, 0x46, 0x64,
	0x64, 0x52, 0x03, 0x66, 0x64, 0x64, 0x22, 0x57, 0x0a, 0x14, 0x43, 0x6f, 0x6d, 0x70, 0x61, 0x6e,
	0x79, 0x56, 0x65, 0x72, 0x69, 0x66, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1e,
	0x0a, 0x0a, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0a, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x49, 0x64, 0x12, 0x1f,
	0x0a, 0x03, 0x66, 0x64, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0d, 0x2e, 0x63, 0x6f,
	0x6e, 0x74, 0x72, 0x61, 0x63, 0x74, 0x2e, 0x46, 0x64, 0x64, 0x52, 0x03, 0x66, 0x64, 0x64, 0x22,
	0x38, 0x0a, 0x16, 0x55, 0x70, 0x6c, 0x6f, 0x61, 0x64, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74,
	0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x1e, 0x0a, 0x0a, 0x43, 0x75, 0x73,
	0x74, 0x6f, 0x6d, 0x65, 0x72, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x63,
	0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x49, 0x64, 0x22, 0x9f, 0x03, 0x0a, 0x13, 0x50, 0x65,
	0x72, 0x73, 0x6f, 0x6e, 0x56, 0x65, 0x72, 0x69, 0x66, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x12, 0x1e, 0x0a, 0x0a, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x49, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x49,
	0x64, 0x12, 0x20, 0x0a, 0x0b, 0x56, 0x65, 0x72, 0x69, 0x66, 0x69, 0x65, 0x64, 0x57, 0x61, 0x79,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x76, 0x65, 0x72, 0x69, 0x66, 0x69, 0x65, 0x64,
	0x57, 0x61, 0x79, 0x12, 0x22, 0x0a, 0x0c, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x4e,
	0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x63, 0x75, 0x73, 0x74, 0x6f,
	0x6d, 0x65, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x28, 0x0a, 0x0f, 0x43, 0x75, 0x73, 0x74, 0x6f,
	0x6d, 0x65, 0x72, 0x49, 0x64, 0x65, 0x6e, 0x74, 0x4e, 0x6f, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x49, 0x64, 0x65, 0x6e, 0x74, 0x4e,
	0x6f, 0x12, 0x16, 0x0a, 0x06, 0x4d, 0x6f, 0x62, 0x69, 0x6c, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x06, 0x6d, 0x6f, 0x62, 0x69, 0x6c, 0x65, 0x12, 0x1c, 0x0a, 0x09, 0x52, 0x65, 0x74,
	0x75, 0x72, 0x6e, 0x55, 0x72, 0x6c, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x72, 0x65,
	0x74, 0x75, 0x72, 0x6e, 0x55, 0x72, 0x6c, 0x12, 0x1a, 0x0a, 0x08, 0x63, 0x65, 0x72, 0x74, 0x54,
	0x79, 0x70, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x63, 0x65, 0x72, 0x74, 0x54,
	0x79, 0x70, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x61, 0x70, 0x69, 0x55, 0x72, 0x6c, 0x18, 0x08, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x06, 0x61, 0x70, 0x69, 0x55, 0x72, 0x6c, 0x12, 0x16, 0x0a, 0x06, 0x69,
	0x73, 0x4d, 0x69, 0x6e, 0x69, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x69, 0x73, 0x4d,
	0x69, 0x6e, 0x69, 0x12, 0x1f, 0x0a, 0x03, 0x66, 0x64, 0x64, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x0d, 0x2e, 0x63, 0x6f, 0x6e, 0x74, 0x72, 0x61, 0x63, 0x74, 0x2e, 0x46, 0x64, 0x64, 0x52,
	0x03, 0x66, 0x64, 0x64, 0x12, 0x37, 0x0a, 0x0a, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x54, 0x79,
	0x70, 0x65, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x17, 0x2e, 0x63, 0x6f, 0x6e, 0x74, 0x72,
	0x61, 0x63, 0x74, 0x2e, 0x46, 0x64, 0x64, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x54, 0x79, 0x70,
	0x65, 0x52, 0x0a, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1c, 0x0a,
	0x09, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x79, 0x55, 0x72, 0x6c, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x09, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x79, 0x55, 0x72, 0x6c, 0x22, 0x4e, 0x0a, 0x14, 0x50,
	0x65, 0x72, 0x73, 0x6f, 0x6e, 0x56, 0x65, 0x72, 0x69, 0x66, 0x79, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x12, 0x24, 0x0a, 0x0d, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69,
	0x6f, 0x6e, 0x4e, 0x6f, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x54, 0x72, 0x61, 0x6e,
	0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x4e, 0x6f, 0x12, 0x10, 0x0a, 0x03, 0x55, 0x72, 0x6c,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x55, 0x72, 0x6c, 0x22, 0x32, 0x0a, 0x10, 0x52,
	0x65, 0x67, 0x69, 0x73, 0x74, 0x65, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12,
	0x1e, 0x0a, 0x0a, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x49, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0a, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x49, 0x64, 0x22,
	0x54, 0x0a, 0x11, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x49, 0x64, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x12, 0x1e, 0x0a, 0x0a, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72,
	0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d,
	0x65, 0x72, 0x49, 0x64, 0x12, 0x1f, 0x0a, 0x03, 0x66, 0x64, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x0d, 0x2e, 0x63, 0x6f, 0x6e, 0x74, 0x72, 0x61, 0x63, 0x74, 0x2e, 0x46, 0x64, 0x64,
	0x52, 0x03, 0x66, 0x64, 0x64, 0x22, 0x10, 0x0a, 0x0e, 0x43, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x4d, 0x0a, 0x12, 0x52, 0x65, 0x67, 0x69, 0x73,
	0x74, 0x65, 0x72, 0x4f, 0x72, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x16, 0x0a,
	0x06, 0x4f, 0x70, 0x65, 0x6e, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x6f,
	0x70, 0x65, 0x6e, 0x49, 0x64, 0x12, 0x1f, 0x0a, 0x03, 0x66, 0x64, 0x64, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x0d, 0x2e, 0x63, 0x6f, 0x6e, 0x74, 0x72, 0x61, 0x63, 0x74, 0x2e, 0x46, 0x64,
	0x64, 0x52, 0x03, 0x66, 0x64, 0x64, 0x22, 0x50, 0x0a, 0x15, 0x52, 0x65, 0x67, 0x69, 0x73, 0x74,
	0x65, 0x72, 0x50, 0x65, 0x72, 0x73, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12,
	0x16, 0x0a, 0x06, 0x4f, 0x70, 0x65, 0x6e, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x06, 0x6f, 0x70, 0x65, 0x6e, 0x49, 0x64, 0x12, 0x1f, 0x0a, 0x03, 0x66, 0x64, 0x64, 0x18, 0x0b,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x0d, 0x2e, 0x63, 0x6f, 0x6e, 0x74, 0x72, 0x61, 0x63, 0x74, 0x2e,
	0x46, 0x64, 0x64, 0x52, 0x03, 0x66, 0x64, 0x64, 0x22, 0x73, 0x0a, 0x0e, 0x41, 0x64, 0x64, 0x53,
	0x69, 0x67, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1e, 0x0a, 0x0a, 0x43, 0x75,
	0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a,
	0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x49, 0x64, 0x12, 0x20, 0x0a, 0x0b, 0x52, 0x65,
	0x61, 0x64, 0x49, 0x6d, 0x67, 0x42, 0x79, 0x74, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0c, 0x52,
	0x0b, 0x72, 0x65, 0x61, 0x64, 0x49, 0x6d, 0x67, 0x42, 0x79, 0x74, 0x65, 0x12, 0x1f, 0x0a, 0x03,
	0x66, 0x64, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0d, 0x2e, 0x63, 0x6f, 0x6e, 0x74,
	0x72, 0x61, 0x63, 0x74, 0x2e, 0x46, 0x64, 0x64, 0x52, 0x03, 0x66, 0x64, 0x64, 0x22, 0x33, 0x0a,
	0x0f, 0x41, 0x64, 0x64, 0x53, 0x69, 0x67, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x12, 0x20, 0x0a, 0x0b, 0x53, 0x69, 0x67, 0x6e, 0x61, 0x74, 0x75, 0x72, 0x65, 0x49, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x73, 0x69, 0x67, 0x6e, 0x61, 0x74, 0x75, 0x72, 0x65,
	0x49, 0x64, 0x22, 0x6e, 0x0a, 0x11, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x53, 0x69, 0x67, 0x6e,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1e, 0x0a, 0x0a, 0x43, 0x75, 0x73, 0x74, 0x6f,
	0x6d, 0x65, 0x72, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x63, 0x75, 0x73,
	0x74, 0x6f, 0x6d, 0x65, 0x72, 0x49, 0x64, 0x12, 0x18, 0x0a, 0x07, 0x43, 0x6f, 0x6e, 0x74, 0x65,
	0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e,
	0x74, 0x12, 0x1f, 0x0a, 0x03, 0x66, 0x64, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0d,
	0x2e, 0x63, 0x6f, 0x6e, 0x74, 0x72, 0x61, 0x63, 0x74, 0x2e, 0x46, 0x64, 0x64, 0x52, 0x03, 0x66,
	0x64, 0x64, 0x22, 0x44, 0x0a, 0x12, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x53, 0x69, 0x67, 0x6e,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x2e, 0x0a, 0x12, 0x53, 0x69, 0x67, 0x6e,
	0x61, 0x74, 0x75, 0x72, 0x65, 0x49, 0x6d, 0x67, 0x42, 0x61, 0x73, 0x65, 0x36, 0x34, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x12, 0x73, 0x69, 0x67, 0x6e, 0x61, 0x74, 0x75, 0x72, 0x65, 0x49,
	0x6d, 0x67, 0x42, 0x61, 0x73, 0x65, 0x36, 0x34, 0x22, 0x70, 0x0a, 0x15, 0x55, 0x70, 0x6c, 0x6f,
	0x61, 0x64, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x12, 0x1e, 0x0a, 0x0a, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x49, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x74, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x49,
	0x64, 0x12, 0x16, 0x0a, 0x06, 0x44, 0x6f, 0x63, 0x55, 0x72, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x06, 0x64, 0x6f, 0x63, 0x55, 0x72, 0x6c, 0x12, 0x1f, 0x0a, 0x03, 0x66, 0x64, 0x64,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0d, 0x2e, 0x63, 0x6f, 0x6e, 0x74, 0x72, 0x61, 0x63,
	0x74, 0x2e, 0x46, 0x64, 0x64, 0x52, 0x03, 0x66, 0x64, 0x64, 0x22, 0xc3, 0x02, 0x0a, 0x12, 0x45,
	0x78, 0x74, 0x53, 0x69, 0x67, 0x6e, 0x41, 0x75, 0x74, 0x6f, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x12, 0x24, 0x0a, 0x0d, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e,
	0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61,
	0x63, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x12, 0x1e, 0x0a, 0x0a, 0x43, 0x6f, 0x6e, 0x74, 0x72,
	0x61, 0x63, 0x74, 0x49, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x63, 0x6f, 0x6e,
	0x74, 0x72, 0x61, 0x63, 0x74, 0x49, 0x64, 0x12, 0x1e, 0x0a, 0x0a, 0x43, 0x75, 0x73, 0x74, 0x6f,
	0x6d, 0x65, 0x72, 0x49, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x63, 0x75, 0x73,
	0x74, 0x6f, 0x6d, 0x65, 0x72, 0x49, 0x64, 0x12, 0x1e, 0x0a, 0x0a, 0x43, 0x6c, 0x69, 0x65, 0x6e,
	0x74, 0x52, 0x6f, 0x6c, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x63, 0x6c, 0x69,
	0x65, 0x6e, 0x74, 0x52, 0x6f, 0x6c, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x44, 0x6f, 0x63, 0x54, 0x69,
	0x74, 0x6c, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x64, 0x6f, 0x63, 0x54, 0x69,
	0x74, 0x6c, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x53, 0x69, 0x67, 0x6e, 0x4b, 0x65, 0x79, 0x77, 0x6f,
	0x72, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x73, 0x69, 0x67, 0x6e, 0x4b, 0x65,
	0x79, 0x77, 0x6f, 0x72, 0x64, 0x12, 0x20, 0x0a, 0x0b, 0x53, 0x69, 0x67, 0x6e, 0x61, 0x74, 0x75,
	0x72, 0x65, 0x49, 0x64, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x73, 0x69, 0x67, 0x6e,
	0x61, 0x74, 0x75, 0x72, 0x65, 0x49, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x4b, 0x65, 0x79, 0x58, 0x18,
	0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6b, 0x65, 0x79, 0x58, 0x12, 0x12, 0x0a, 0x04, 0x4b,
	0x65, 0x79, 0x59, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6b, 0x65, 0x79, 0x59, 0x12,
	0x1f, 0x0a, 0x03, 0x66, 0x64, 0x64, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0d, 0x2e, 0x63,
	0x6f, 0x6e, 0x74, 0x72, 0x61, 0x63, 0x74, 0x2e, 0x46, 0x64, 0x64, 0x52, 0x03, 0x66, 0x64, 0x64,
	0x22, 0xfa, 0x01, 0x0a, 0x17, 0x47, 0x65, 0x6e, 0x65, 0x72, 0x61, 0x74, 0x65, 0x43, 0x6f, 0x6e,
	0x74, 0x72, 0x61, 0x63, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1e, 0x0a, 0x0a,
	0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0a, 0x74, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x49, 0x64, 0x12, 0x1e, 0x0a, 0x0a,
	0x43, 0x6f, 0x6e, 0x74, 0x72, 0x61, 0x63, 0x74, 0x49, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0a, 0x63, 0x6f, 0x6e, 0x74, 0x72, 0x61, 0x63, 0x74, 0x49, 0x64, 0x12, 0x22, 0x0a, 0x0c,
	0x50, 0x61, 0x72, 0x61, 0x6d, 0x65, 0x74, 0x65, 0x72, 0x4d, 0x61, 0x70, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0c, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x65, 0x74, 0x65, 0x72, 0x4d, 0x61, 0x70,
	0x12, 0x24, 0x0a, 0x0d, 0x44, 0x79, 0x6e, 0x61, 0x6d, 0x69, 0x63, 0x54, 0x61, 0x62, 0x6c, 0x65,
	0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x64, 0x79, 0x6e, 0x61, 0x6d, 0x69, 0x63,
	0x54, 0x61, 0x62, 0x6c, 0x65, 0x73, 0x12, 0x1a, 0x0a, 0x08, 0x44, 0x6f, 0x63, 0x54, 0x69, 0x74,
	0x6c, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x64, 0x6f, 0x63, 0x54, 0x69, 0x74,
	0x6c, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x61, 0x70, 0x69, 0x48, 0x6f, 0x73, 0x74, 0x18, 0x06, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x70, 0x69, 0x48, 0x6f, 0x73, 0x74, 0x12, 0x1f, 0x0a, 0x03,
	0x66, 0x64, 0x64, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0d, 0x2e, 0x63, 0x6f, 0x6e, 0x74,
	0x72, 0x61, 0x63, 0x74, 0x2e, 0x46, 0x64, 0x64, 0x52, 0x03, 0x66, 0x64, 0x64, 0x22, 0x49, 0x0a,
	0x11, 0x53, 0x69, 0x67, 0x6e, 0x61, 0x74, 0x75, 0x72, 0x65, 0x50, 0x6f, 0x73, 0x69, 0x74, 0x69,
	0x6f, 0x6e, 0x12, 0x18, 0x0a, 0x07, 0x70, 0x61, 0x67, 0x65, 0x6e, 0x75, 0x6d, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x07, 0x70, 0x61, 0x67, 0x65, 0x6e, 0x75, 0x6d, 0x12, 0x0c, 0x0a, 0x01,
	0x78, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x01, 0x78, 0x12, 0x0c, 0x0a, 0x01, 0x79, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x01, 0x79, 0x22, 0xe7, 0x01, 0x0a, 0x08, 0x53, 0x69, 0x67,
	0x6e, 0x44, 0x61, 0x74, 0x61, 0x12, 0x1e, 0x0a, 0x0a, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x61, 0x63,
	0x74, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x43, 0x6f, 0x6e, 0x74, 0x72,
	0x61, 0x63, 0x74, 0x49, 0x64, 0x12, 0x24, 0x0a, 0x0d, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63,
	0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x54, 0x72,
	0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x12, 0x20, 0x0a, 0x0b, 0x73,
	0x69, 0x67, 0x6e, 0x4b, 0x65, 0x79, 0x77, 0x6f, 0x72, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0b, 0x73, 0x69, 0x67, 0x6e, 0x4b, 0x65, 0x79, 0x77, 0x6f, 0x72, 0x64, 0x12, 0x4b, 0x0a,
	0x12, 0x73, 0x69, 0x67, 0x6e, 0x61, 0x74, 0x75, 0x72, 0x65, 0x50, 0x6f, 0x73, 0x69, 0x74, 0x69,
	0x6f, 0x6e, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x63, 0x6f, 0x6e, 0x74,
	0x72, 0x61, 0x63, 0x74, 0x2e, 0x53, 0x69, 0x67, 0x6e, 0x61, 0x74, 0x75, 0x72, 0x65, 0x50, 0x6f,
	0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x12, 0x73, 0x69, 0x67, 0x6e, 0x61, 0x74, 0x75, 0x72,
	0x65, 0x50, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x12, 0x0a, 0x04, 0x6b, 0x65,
	0x79, 0x78, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6b, 0x65, 0x79, 0x78, 0x12, 0x12,
	0x0a, 0x04, 0x6b, 0x65, 0x79, 0x79, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6b, 0x65,
	0x79, 0x79, 0x22, 0x9a, 0x02, 0x0a, 0x20, 0x47, 0x65, 0x6e, 0x65, 0x72, 0x61, 0x74, 0x65, 0x43,
	0x6f, 0x6e, 0x74, 0x72, 0x61, 0x63, 0x74, 0x49, 0x6e, 0x42, 0x61, 0x74, 0x63, 0x68, 0x65, 0x73,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x19, 0x0a, 0x07, 0x62, 0x61, 0x74, 0x63, 0x68,
	0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x62, 0x61, 0x74, 0x63, 0x68, 0x5f,
	0x69, 0x64, 0x12, 0x2f, 0x0a, 0x08, 0x73, 0x69, 0x67, 0x6e, 0x44, 0x61, 0x74, 0x61, 0x18, 0x02,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x63, 0x6f, 0x6e, 0x74, 0x72, 0x61, 0x63, 0x74, 0x2e,
	0x53, 0x69, 0x67, 0x6e, 0x44, 0x61, 0x74, 0x61, 0x52, 0x09, 0x73, 0x69, 0x67, 0x6e, 0x5f, 0x64,
	0x61, 0x74, 0x61, 0x12, 0x1f, 0x0a, 0x0a, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x49,
	0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65,
	0x72, 0x5f, 0x69, 0x64, 0x12, 0x1f, 0x0a, 0x0a, 0x62, 0x61, 0x74, 0x63, 0x68, 0x54, 0x69, 0x74,
	0x6c, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x62, 0x61, 0x74, 0x63, 0x68, 0x5f,
	0x74, 0x69, 0x74, 0x6c, 0x65, 0x12, 0x28, 0x0a, 0x0e, 0x6d, 0x6f, 0x62, 0x69, 0x6c, 0x65, 0x53,
	0x69, 0x67, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x6d,
	0x6f, 0x62, 0x69, 0x6c, 0x65, 0x5f, 0x73, 0x69, 0x67, 0x6e, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x12,
	0x1d, 0x0a, 0x09, 0x72, 0x65, 0x74, 0x75, 0x72, 0x6e, 0x55, 0x72, 0x6c, 0x18, 0x07, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0a, 0x72, 0x65, 0x74, 0x75, 0x72, 0x6e, 0x5f, 0x75, 0x72, 0x6c, 0x12, 0x1f,
	0x0a, 0x03, 0x66, 0x64, 0x64, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0d, 0x2e, 0x63, 0x6f,
	0x6e, 0x74, 0x72, 0x61, 0x63, 0x74, 0x2e, 0x46, 0x64, 0x64, 0x52, 0x03, 0x66, 0x64, 0x64, 0x22,
	0x3d, 0x0a, 0x21, 0x47, 0x65, 0x6e, 0x65, 0x72, 0x61, 0x74, 0x65, 0x43, 0x6f, 0x6e, 0x74, 0x72,
	0x61, 0x63, 0x74, 0x49, 0x6e, 0x42, 0x61, 0x74, 0x63, 0x68, 0x65, 0x73, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x6a, 0x75, 0x6d, 0x70, 0x55, 0x72, 0x6c, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6a, 0x75, 0x6d, 0x70, 0x55, 0x72, 0x6c, 0x2a, 0x65,
	0x0a, 0x0d, 0x46, 0x64, 0x64, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12,
	0x10, 0x0a, 0x0c, 0x4e, 0x6f, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x54, 0x79, 0x70, 0x65, 0x10,
	0x00, 0x12, 0x1b, 0x0a, 0x17, 0x53, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x52, 0x65, 0x74, 0x75,
	0x72, 0x6e, 0x46, 0x61, 0x69, 0x6c, 0x65, 0x64, 0x4b, 0x65, 0x65, 0x70, 0x10, 0x01, 0x12, 0x13,
	0x0a, 0x0f, 0x55, 0x73, 0x65, 0x52, 0x65, 0x74, 0x75, 0x72, 0x6e, 0x42, 0x75, 0x74, 0x74, 0x6f,
	0x6e, 0x10, 0x02, 0x12, 0x10, 0x0a, 0x0c, 0x41, 0x6c, 0x77, 0x61, 0x79, 0x73, 0x52, 0x65, 0x74,
	0x75, 0x72, 0x6e, 0x10, 0x03, 0x32, 0xa4, 0x0f, 0x0a, 0x08, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x61,
	0x63, 0x74, 0x12, 0x4f, 0x0a, 0x0e, 0x52, 0x65, 0x67, 0x69, 0x73, 0x74, 0x65, 0x72, 0x50, 0x65,
	0x72, 0x73, 0x6f, 0x6e, 0x12, 0x1f, 0x2e, 0x63, 0x6f, 0x6e, 0x74, 0x72, 0x61, 0x63, 0x74, 0x2e,
	0x52, 0x65, 0x67, 0x69, 0x73, 0x74, 0x65, 0x72, 0x50, 0x65, 0x72, 0x73, 0x6f, 0x6e, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1a, 0x2e, 0x63, 0x6f, 0x6e, 0x74, 0x72, 0x61, 0x63, 0x74,
	0x2e, 0x52, 0x65, 0x67, 0x69, 0x73, 0x74, 0x65, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x22, 0x00, 0x12, 0x4f, 0x0a, 0x0c, 0x50, 0x65, 0x72, 0x73, 0x6f, 0x6e, 0x56, 0x65, 0x72,
	0x69, 0x66, 0x79, 0x12, 0x1d, 0x2e, 0x63, 0x6f, 0x6e, 0x74, 0x72, 0x61, 0x63, 0x74, 0x2e, 0x50,
	0x65, 0x72, 0x73, 0x6f, 0x6e, 0x56, 0x65, 0x72, 0x69, 0x66, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x1a, 0x1e, 0x2e, 0x63, 0x6f, 0x6e, 0x74, 0x72, 0x61, 0x63, 0x74, 0x2e, 0x50, 0x65,
	0x72, 0x73, 0x6f, 0x6e, 0x56, 0x65, 0x72, 0x69, 0x66, 0x79, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x22, 0x00, 0x12, 0x5b, 0x0a, 0x15, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x50, 0x65, 0x72,
	0x73, 0x6f, 0x6e, 0x56, 0x65, 0x72, 0x69, 0x66, 0x79, 0x53, 0x69, 0x6e, 0x67, 0x12, 0x26, 0x2e,
	0x63, 0x6f, 0x6e, 0x74, 0x72, 0x61, 0x63, 0x74, 0x2e, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x50, 0x65,
	0x72, 0x73, 0x6f, 0x6e, 0x56, 0x65, 0x72, 0x69, 0x66, 0x79, 0x53, 0x69, 0x6e, 0x67, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x18, 0x2e, 0x63, 0x6f, 0x6e, 0x74, 0x72, 0x61, 0x63, 0x74,
	0x2e, 0x43, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22,
	0x00, 0x12, 0x49, 0x0a, 0x0b, 0x52, 0x65, 0x67, 0x69, 0x73, 0x74, 0x65, 0x72, 0x4f, 0x72, 0x67,
	0x12, 0x1c, 0x2e, 0x63, 0x6f, 0x6e, 0x74, 0x72, 0x61, 0x63, 0x74, 0x2e, 0x52, 0x65, 0x67, 0x69,
	0x73, 0x74, 0x65, 0x72, 0x4f, 0x72, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1a,
	0x2e, 0x63, 0x6f, 0x6e, 0x74, 0x72, 0x61, 0x63, 0x74, 0x2e, 0x52, 0x65, 0x67, 0x69, 0x73, 0x74,
	0x65, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x51, 0x0a, 0x0d,
	0x43, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x56, 0x65, 0x72, 0x69, 0x66, 0x79, 0x12, 0x1e, 0x2e,
	0x63, 0x6f, 0x6e, 0x74, 0x72, 0x61, 0x63, 0x74, 0x2e, 0x43, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79,
	0x56, 0x65, 0x72, 0x69, 0x66, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1e, 0x2e,
	0x63, 0x6f, 0x6e, 0x74, 0x72, 0x61, 0x63, 0x74, 0x2e, 0x50, 0x65, 0x72, 0x73, 0x6f, 0x6e, 0x56,
	0x65, 0x72, 0x69, 0x66, 0x79, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12,
	0x50, 0x0a, 0x12, 0x46, 0x69, 0x6e, 0x64, 0x50, 0x65, 0x72, 0x73, 0x6f, 0x6e, 0x43, 0x65, 0x72,
	0x74, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x16, 0x2e, 0x63, 0x6f, 0x6e, 0x74, 0x72, 0x61, 0x63, 0x74,
	0x2e, 0x46, 0x69, 0x6e, 0x64, 0x43, 0x65, 0x72, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x1a, 0x20, 0x2e,
	0x63, 0x6f, 0x6e, 0x74, 0x72, 0x61, 0x63, 0x74, 0x2e, 0x50, 0x65, 0x72, 0x73, 0x6f, 0x6e, 0x43,
	0x65, 0x72, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22,
	0x00, 0x12, 0x52, 0x0a, 0x13, 0x46, 0x69, 0x6e, 0x64, 0x43, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79,
	0x43, 0x65, 0x72, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x16, 0x2e, 0x63, 0x6f, 0x6e, 0x74, 0x72,
	0x61, 0x63, 0x74, 0x2e, 0x46, 0x69, 0x6e, 0x64, 0x43, 0x65, 0x72, 0x74, 0x49, 0x6e, 0x66, 0x6f,
	0x1a, 0x21, 0x2e, 0x63, 0x6f, 0x6e, 0x74, 0x72, 0x61, 0x63, 0x74, 0x2e, 0x43, 0x6f, 0x6d, 0x70,
	0x61, 0x6e, 0x79, 0x43, 0x65, 0x72, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x43, 0x0a, 0x09, 0x41, 0x70, 0x70, 0x6c, 0x79, 0x43, 0x65,
	0x72, 0x74, 0x12, 0x1a, 0x2e, 0x63, 0x6f, 0x6e, 0x74, 0x72, 0x61, 0x63, 0x74, 0x2e, 0x41, 0x70,
	0x70, 0x6c, 0x79, 0x43, 0x65, 0x72, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x18,
	0x2e, 0x63, 0x6f, 0x6e, 0x74, 0x72, 0x61, 0x63, 0x74, 0x2e, 0x43, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x45, 0x0a, 0x0c, 0x41, 0x64,
	0x64, 0x53, 0x69, 0x67, 0x6e, 0x61, 0x74, 0x75, 0x72, 0x65, 0x12, 0x18, 0x2e, 0x63, 0x6f, 0x6e,
	0x74, 0x72, 0x61, 0x63, 0x74, 0x2e, 0x41, 0x64, 0x64, 0x53, 0x69, 0x67, 0x6e, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x1a, 0x19, 0x2e, 0x63, 0x6f, 0x6e, 0x74, 0x72, 0x61, 0x63, 0x74, 0x2e,
	0x41, 0x64, 0x64, 0x53, 0x69, 0x67, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22,
	0x00, 0x12, 0x49, 0x0a, 0x0a, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x53, 0x69, 0x67, 0x6e, 0x12,
	0x1b, 0x2e, 0x63, 0x6f, 0x6e, 0x74, 0x72, 0x61, 0x63, 0x74, 0x2e, 0x43, 0x75, 0x73, 0x74, 0x6f,
	0x6d, 0x53, 0x69, 0x67, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1c, 0x2e, 0x63,
	0x6f, 0x6e, 0x74, 0x72, 0x61, 0x63, 0x74, 0x2e, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x53, 0x69,
	0x67, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x4d, 0x0a, 0x0e,
	0x55, 0x70, 0x6c, 0x6f, 0x61, 0x64, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x12, 0x1f,
	0x2e, 0x63, 0x6f, 0x6e, 0x74, 0x72, 0x61, 0x63, 0x74, 0x2e, 0x55, 0x70, 0x6c, 0x6f, 0x61, 0x64,
	0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a,
	0x18, 0x2e, 0x63, 0x6f, 0x6e, 0x74, 0x72, 0x61, 0x63, 0x74, 0x2e, 0x43, 0x6f, 0x6d, 0x6d, 0x6f,
	0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x45, 0x0a, 0x0a, 0x55,
	0x70, 0x6c, 0x6f, 0x61, 0x64, 0x44, 0x6f, 0x63, 0x73, 0x12, 0x1b, 0x2e, 0x63, 0x6f, 0x6e, 0x74,
	0x72, 0x61, 0x63, 0x74, 0x2e, 0x55, 0x70, 0x6c, 0x6f, 0x61, 0x64, 0x44, 0x6f, 0x63, 0x73, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x18, 0x2e, 0x63, 0x6f, 0x6e, 0x74, 0x72, 0x61, 0x63,
	0x74, 0x2e, 0x43, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x22, 0x00, 0x12, 0x55, 0x0a, 0x10, 0x47, 0x65, 0x6e, 0x65, 0x72, 0x61, 0x74, 0x65, 0x43, 0x6f,
	0x6e, 0x74, 0x72, 0x61, 0x63, 0x74, 0x12, 0x21, 0x2e, 0x63, 0x6f, 0x6e, 0x74, 0x72, 0x61, 0x63,
	0x74, 0x2e, 0x47, 0x65, 0x6e, 0x65, 0x72, 0x61, 0x74, 0x65, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x61,
	0x63, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1c, 0x2e, 0x63, 0x6f, 0x6e, 0x74,
	0x72, 0x61, 0x63, 0x74, 0x2e, 0x56, 0x69, 0x65, 0x77, 0x43, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x4b, 0x0a, 0x0b, 0x45, 0x78, 0x74,
	0x53, 0x69, 0x67, 0x6e, 0x41, 0x75, 0x74, 0x6f, 0x12, 0x1c, 0x2e, 0x63, 0x6f, 0x6e, 0x74, 0x72,
	0x61, 0x63, 0x74, 0x2e, 0x45, 0x78, 0x74, 0x53, 0x69, 0x67, 0x6e, 0x41, 0x75, 0x74, 0x6f, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1c, 0x2e, 0x63, 0x6f, 0x6e, 0x74, 0x72, 0x61, 0x63,
	0x74, 0x2e, 0x56, 0x69, 0x65, 0x77, 0x43, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x51, 0x0a, 0x0e, 0x42, 0x65, 0x66, 0x6f, 0x72, 0x65,
	0x41, 0x75, 0x74, 0x68, 0x53, 0x69, 0x67, 0x6e, 0x12, 0x1f, 0x2e, 0x63, 0x6f, 0x6e, 0x74, 0x72,
	0x61, 0x63, 0x74, 0x2e, 0x42, 0x65, 0x66, 0x6f, 0x72, 0x65, 0x41, 0x75, 0x74, 0x68, 0x53, 0x69,
	0x67, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1c, 0x2e, 0x63, 0x6f, 0x6e, 0x74,
	0x72, 0x61, 0x63, 0x74, 0x2e, 0x4a, 0x75, 0x6d, 0x70, 0x43, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x43, 0x0a, 0x07, 0x45, 0x78, 0x74,
	0x53, 0x69, 0x67, 0x6e, 0x12, 0x18, 0x2e, 0x63, 0x6f, 0x6e, 0x74, 0x72, 0x61, 0x63, 0x74, 0x2e,
	0x45, 0x78, 0x74, 0x53, 0x69, 0x67, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1c,
	0x2e, 0x63, 0x6f, 0x6e, 0x74, 0x72, 0x61, 0x63, 0x74, 0x2e, 0x4a, 0x75, 0x6d, 0x70, 0x43, 0x6f,
	0x6d, 0x6d, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x49,
	0x0a, 0x0c, 0x56, 0x69, 0x65, 0x77, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x61, 0x63, 0x74, 0x12, 0x19,
	0x2e, 0x63, 0x6f, 0x6e, 0x74, 0x72, 0x61, 0x63, 0x74, 0x2e, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x61,
	0x63, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1c, 0x2e, 0x63, 0x6f, 0x6e, 0x74,
	0x72, 0x61, 0x63, 0x74, 0x2e, 0x4a, 0x75, 0x6d, 0x70, 0x43, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x4d, 0x0a, 0x10, 0x44, 0x6f, 0x77,
	0x6e, 0x4c, 0x6f, 0x61, 0x64, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x61, 0x63, 0x74, 0x12, 0x19, 0x2e,
	0x63, 0x6f, 0x6e, 0x74, 0x72, 0x61, 0x63, 0x74, 0x2e, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x61, 0x63,
	0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1c, 0x2e, 0x63, 0x6f, 0x6e, 0x74, 0x72,
	0x61, 0x63, 0x74, 0x2e, 0x4a, 0x75, 0x6d, 0x70, 0x43, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x47, 0x0a, 0x0e, 0x43, 0x6f, 0x6e, 0x74,
	0x72, 0x61, 0x63, 0x74, 0x46, 0x69, 0x6c, 0x69, 0x6e, 0x67, 0x12, 0x19, 0x2e, 0x63, 0x6f, 0x6e,
	0x74, 0x72, 0x61, 0x63, 0x74, 0x2e, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x61, 0x63, 0x74, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x18, 0x2e, 0x63, 0x6f, 0x6e, 0x74, 0x72, 0x61, 0x63, 0x74,
	0x2e, 0x43, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22,
	0x00, 0x12, 0x5b, 0x0a, 0x12, 0x47, 0x65, 0x74, 0x50, 0x64, 0x66, 0x54, 0x65, 0x6d, 0x70, 0x6c,
	0x61, 0x74, 0x65, 0x4b, 0x65, 0x79, 0x73, 0x12, 0x20, 0x2e, 0x63, 0x6f, 0x6e, 0x74, 0x72, 0x61,
	0x63, 0x74, 0x2e, 0x50, 0x64, 0x66, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x4b, 0x65,
	0x79, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x21, 0x2e, 0x63, 0x6f, 0x6e, 0x74,
	0x72, 0x61, 0x63, 0x74, 0x2e, 0x50, 0x64, 0x66, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65,
	0x4b, 0x65, 0x79, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x49,
	0x0a, 0x0a, 0x41, 0x75, 0x74, 0x68, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x1b, 0x2e, 0x63,
	0x6f, 0x6e, 0x74, 0x72, 0x61, 0x63, 0x74, 0x2e, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72,
	0x49, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1c, 0x2e, 0x63, 0x6f, 0x6e, 0x74,
	0x72, 0x61, 0x63, 0x74, 0x2e, 0x41, 0x75, 0x74, 0x68, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x57, 0x0a, 0x11, 0x43, 0x61, 0x6e,
	0x63, 0x65, 0x6c, 0x45, 0x78, 0x74, 0x53, 0x69, 0x67, 0x6e, 0x41, 0x75, 0x74, 0x6f, 0x12, 0x22,
	0x2e, 0x63, 0x6f, 0x6e, 0x74, 0x72, 0x61, 0x63, 0x74, 0x2e, 0x43, 0x61, 0x6e, 0x63, 0x65, 0x6c,
	0x45, 0x78, 0x74, 0x53, 0x69, 0x67, 0x6e, 0x41, 0x75, 0x74, 0x6f, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x1a, 0x1c, 0x2e, 0x63, 0x6f, 0x6e, 0x74, 0x72, 0x61, 0x63, 0x74, 0x2e, 0x4a, 0x75,
	0x6d, 0x70, 0x43, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x22, 0x00, 0x12, 0x76, 0x0a, 0x19, 0x47, 0x65, 0x6e, 0x65, 0x72, 0x61, 0x74, 0x65, 0x43, 0x6f,
	0x6e, 0x74, 0x72, 0x61, 0x63, 0x74, 0x49, 0x6e, 0x42, 0x61, 0x74, 0x63, 0x68, 0x65, 0x73, 0x12,
	0x2a, 0x2e, 0x63, 0x6f, 0x6e, 0x74, 0x72, 0x61, 0x63, 0x74, 0x2e, 0x47, 0x65, 0x6e, 0x65, 0x72,
	0x61, 0x74, 0x65, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x61, 0x63, 0x74, 0x49, 0x6e, 0x42, 0x61, 0x74,
	0x63, 0x68, 0x65, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2b, 0x2e, 0x63, 0x6f,
	0x6e, 0x74, 0x72, 0x61, 0x63, 0x74, 0x2e, 0x47, 0x65, 0x6e, 0x65, 0x72, 0x61, 0x74, 0x65, 0x43,
	0x6f, 0x6e, 0x74, 0x72, 0x61, 0x63, 0x74, 0x49, 0x6e, 0x42, 0x61, 0x74, 0x63, 0x68, 0x65, 0x73,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x50, 0x0a, 0x13, 0x44, 0x6f,
	0x77, 0x6e, 0x4c, 0x6f, 0x61, 0x64, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x61, 0x63, 0x74, 0x50, 0x64,
	0x66, 0x12, 0x19, 0x2e, 0x63, 0x6f, 0x6e, 0x74, 0x72, 0x61, 0x63, 0x74, 0x2e, 0x43, 0x6f, 0x6e,
	0x74, 0x72, 0x61, 0x63, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1c, 0x2e, 0x63,
	0x6f, 0x6e, 0x74, 0x72, 0x61, 0x63, 0x74, 0x2e, 0x4a, 0x75, 0x6d, 0x70, 0x43, 0x6f, 0x6d, 0x6d,
	0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x42, 0x0d, 0x5a, 0x0b,
	0x2e, 0x2f, 0x3b, 0x63, 0x6f, 0x6e, 0x74, 0x72, 0x61, 0x63, 0x74, 0x62, 0x06, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x33,
}

var (
	file_contract_proto_rawDescOnce sync.Once
	file_contract_proto_rawDescData = file_contract_proto_rawDesc
)

func file_contract_proto_rawDescGZIP() []byte {
	file_contract_proto_rawDescOnce.Do(func() {
		file_contract_proto_rawDescData = protoimpl.X.CompressGZIP(file_contract_proto_rawDescData)
	})
	return file_contract_proto_rawDescData
}

var file_contract_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_contract_proto_msgTypes = make([]protoimpl.MessageInfo, 39)
var file_contract_proto_goTypes = []any{
	(FddResultType)(0),                        // 0: contract.FddResultType
	(*Fdd)(nil),                               // 1: contract.Fdd
	(*CheckPersonVerifySingRequest)(nil),      // 2: contract.CheckPersonVerifySingRequest
	(*CancelExtSignAutoRequest)(nil),          // 3: contract.CancelExtSignAutoRequest
	(*Person)(nil),                            // 4: contract.Person
	(*PersonCertInfoResponse)(nil),            // 5: contract.PersonCertInfoResponse
	(*FindCertInfo)(nil),                      // 6: contract.FindCertInfo
	(*Company)(nil),                           // 7: contract.Company
	(*Manager)(nil),                           // 8: contract.Manager
	(*CompanyCertInfoResponse)(nil),           // 9: contract.CompanyCertInfoResponse
	(*AuthStatusResponse)(nil),                // 10: contract.AuthStatusResponse
	(*ApplyCertRequest)(nil),                  // 11: contract.ApplyCertRequest
	(*ViewCommonResponse)(nil),                // 12: contract.ViewCommonResponse
	(*PdfTemplateKeysRequest)(nil),            // 13: contract.PdfTemplateKeysRequest
	(*PdfTemplateKeysResponse)(nil),           // 14: contract.PdfTemplateKeysResponse
	(*JumpCommonResponse)(nil),                // 15: contract.JumpCommonResponse
	(*ContractRequest)(nil),                   // 16: contract.ContractRequest
	(*ExtSignRequest)(nil),                    // 17: contract.ExtSignRequest
	(*BeforeAuthSignRequest)(nil),             // 18: contract.BeforeAuthSignRequest
	(*UploadDocsRequest)(nil),                 // 19: contract.UploadDocsRequest
	(*CompanyVerifyRequest)(nil),              // 20: contract.CompanyVerifyRequest
	(*UploadTemplateResponse)(nil),            // 21: contract.UploadTemplateResponse
	(*PersonVerifyRequest)(nil),               // 22: contract.PersonVerifyRequest
	(*PersonVerifyResponse)(nil),              // 23: contract.PersonVerifyResponse
	(*RegisterResponse)(nil),                  // 24: contract.RegisterResponse
	(*CustomerIdRequest)(nil),                 // 25: contract.CustomerIdRequest
	(*CommonResponse)(nil),                    // 26: contract.CommonResponse
	(*RegisterOrgRequest)(nil),                // 27: contract.RegisterOrgRequest
	(*RegisterPersonRequest)(nil),             // 28: contract.RegisterPersonRequest
	(*AddSignRequest)(nil),                    // 29: contract.AddSignRequest
	(*AddSignResponse)(nil),                   // 30: contract.AddSignResponse
	(*CustomSignRequest)(nil),                 // 31: contract.CustomSignRequest
	(*CustomSignResponse)(nil),                // 32: contract.CustomSignResponse
	(*UploadTemplateRequest)(nil),             // 33: contract.UploadTemplateRequest
	(*ExtSignAutoRequest)(nil),                // 34: contract.ExtSignAutoRequest
	(*GenerateContractRequest)(nil),           // 35: contract.GenerateContractRequest
	(*SignaturePosition)(nil),                 // 36: contract.SignaturePosition
	(*SignData)(nil),                          // 37: contract.SignData
	(*GenerateContractInBatchesRequest)(nil),  // 38: contract.GenerateContractInBatchesRequest
	(*GenerateContractInBatchesResponse)(nil), // 39: contract.GenerateContractInBatchesResponse
}
var file_contract_proto_depIdxs = []int32{
	1,  // 0: contract.CheckPersonVerifySingRequest.fdd:type_name -> contract.Fdd
	1,  // 1: contract.CancelExtSignAutoRequest.fdd:type_name -> contract.Fdd
	4,  // 2: contract.PersonCertInfoResponse.Person:type_name -> contract.Person
	1,  // 3: contract.FindCertInfo.fdd:type_name -> contract.Fdd
	7,  // 4: contract.CompanyCertInfoResponse.Company:type_name -> contract.Company
	8,  // 5: contract.CompanyCertInfoResponse.Manager:type_name -> contract.Manager
	1,  // 6: contract.ApplyCertRequest.fdd:type_name -> contract.Fdd
	1,  // 7: contract.PdfTemplateKeysRequest.fdd:type_name -> contract.Fdd
	1,  // 8: contract.ContractRequest.fdd:type_name -> contract.Fdd
	1,  // 9: contract.ExtSignRequest.fdd:type_name -> contract.Fdd
	1,  // 10: contract.BeforeAuthSignRequest.fdd:type_name -> contract.Fdd
	1,  // 11: contract.UploadDocsRequest.fdd:type_name -> contract.Fdd
	1,  // 12: contract.CompanyVerifyRequest.fdd:type_name -> contract.Fdd
	1,  // 13: contract.PersonVerifyRequest.fdd:type_name -> contract.Fdd
	0,  // 14: contract.PersonVerifyRequest.resultType:type_name -> contract.FddResultType
	1,  // 15: contract.CustomerIdRequest.fdd:type_name -> contract.Fdd
	1,  // 16: contract.RegisterOrgRequest.fdd:type_name -> contract.Fdd
	1,  // 17: contract.RegisterPersonRequest.fdd:type_name -> contract.Fdd
	1,  // 18: contract.AddSignRequest.fdd:type_name -> contract.Fdd
	1,  // 19: contract.CustomSignRequest.fdd:type_name -> contract.Fdd
	1,  // 20: contract.UploadTemplateRequest.fdd:type_name -> contract.Fdd
	1,  // 21: contract.ExtSignAutoRequest.fdd:type_name -> contract.Fdd
	1,  // 22: contract.GenerateContractRequest.fdd:type_name -> contract.Fdd
	36, // 23: contract.SignData.signaturePositions:type_name -> contract.SignaturePosition
	37, // 24: contract.GenerateContractInBatchesRequest.signData:type_name -> contract.SignData
	1,  // 25: contract.GenerateContractInBatchesRequest.fdd:type_name -> contract.Fdd
	28, // 26: contract.Contract.RegisterPerson:input_type -> contract.RegisterPersonRequest
	22, // 27: contract.Contract.PersonVerify:input_type -> contract.PersonVerifyRequest
	2,  // 28: contract.Contract.CheckPersonVerifySing:input_type -> contract.CheckPersonVerifySingRequest
	27, // 29: contract.Contract.RegisterOrg:input_type -> contract.RegisterOrgRequest
	20, // 30: contract.Contract.CompanyVerify:input_type -> contract.CompanyVerifyRequest
	6,  // 31: contract.Contract.FindPersonCertInfo:input_type -> contract.FindCertInfo
	6,  // 32: contract.Contract.FindCompanyCertInfo:input_type -> contract.FindCertInfo
	11, // 33: contract.Contract.ApplyCert:input_type -> contract.ApplyCertRequest
	29, // 34: contract.Contract.AddSignature:input_type -> contract.AddSignRequest
	31, // 35: contract.Contract.CustomSign:input_type -> contract.CustomSignRequest
	33, // 36: contract.Contract.UploadTemplate:input_type -> contract.UploadTemplateRequest
	19, // 37: contract.Contract.UploadDocs:input_type -> contract.UploadDocsRequest
	35, // 38: contract.Contract.GenerateContract:input_type -> contract.GenerateContractRequest
	34, // 39: contract.Contract.ExtSignAuto:input_type -> contract.ExtSignAutoRequest
	18, // 40: contract.Contract.BeforeAuthSign:input_type -> contract.BeforeAuthSignRequest
	17, // 41: contract.Contract.ExtSign:input_type -> contract.ExtSignRequest
	16, // 42: contract.Contract.ViewContract:input_type -> contract.ContractRequest
	16, // 43: contract.Contract.DownLoadContract:input_type -> contract.ContractRequest
	16, // 44: contract.Contract.ContractFiling:input_type -> contract.ContractRequest
	13, // 45: contract.Contract.GetPdfTemplateKeys:input_type -> contract.PdfTemplateKeysRequest
	25, // 46: contract.Contract.AuthStatus:input_type -> contract.CustomerIdRequest
	3,  // 47: contract.Contract.CancelExtSignAuto:input_type -> contract.CancelExtSignAutoRequest
	38, // 48: contract.Contract.GenerateContractInBatches:input_type -> contract.GenerateContractInBatchesRequest
	16, // 49: contract.Contract.DownLoadContractPdf:input_type -> contract.ContractRequest
	24, // 50: contract.Contract.RegisterPerson:output_type -> contract.RegisterResponse
	23, // 51: contract.Contract.PersonVerify:output_type -> contract.PersonVerifyResponse
	26, // 52: contract.Contract.CheckPersonVerifySing:output_type -> contract.CommonResponse
	24, // 53: contract.Contract.RegisterOrg:output_type -> contract.RegisterResponse
	23, // 54: contract.Contract.CompanyVerify:output_type -> contract.PersonVerifyResponse
	5,  // 55: contract.Contract.FindPersonCertInfo:output_type -> contract.PersonCertInfoResponse
	9,  // 56: contract.Contract.FindCompanyCertInfo:output_type -> contract.CompanyCertInfoResponse
	26, // 57: contract.Contract.ApplyCert:output_type -> contract.CommonResponse
	30, // 58: contract.Contract.AddSignature:output_type -> contract.AddSignResponse
	32, // 59: contract.Contract.CustomSign:output_type -> contract.CustomSignResponse
	26, // 60: contract.Contract.UploadTemplate:output_type -> contract.CommonResponse
	26, // 61: contract.Contract.UploadDocs:output_type -> contract.CommonResponse
	12, // 62: contract.Contract.GenerateContract:output_type -> contract.ViewCommonResponse
	12, // 63: contract.Contract.ExtSignAuto:output_type -> contract.ViewCommonResponse
	15, // 64: contract.Contract.BeforeAuthSign:output_type -> contract.JumpCommonResponse
	15, // 65: contract.Contract.ExtSign:output_type -> contract.JumpCommonResponse
	15, // 66: contract.Contract.ViewContract:output_type -> contract.JumpCommonResponse
	15, // 67: contract.Contract.DownLoadContract:output_type -> contract.JumpCommonResponse
	26, // 68: contract.Contract.ContractFiling:output_type -> contract.CommonResponse
	14, // 69: contract.Contract.GetPdfTemplateKeys:output_type -> contract.PdfTemplateKeysResponse
	10, // 70: contract.Contract.AuthStatus:output_type -> contract.AuthStatusResponse
	15, // 71: contract.Contract.CancelExtSignAuto:output_type -> contract.JumpCommonResponse
	39, // 72: contract.Contract.GenerateContractInBatches:output_type -> contract.GenerateContractInBatchesResponse
	15, // 73: contract.Contract.DownLoadContractPdf:output_type -> contract.JumpCommonResponse
	50, // [50:74] is the sub-list for method output_type
	26, // [26:50] is the sub-list for method input_type
	26, // [26:26] is the sub-list for extension type_name
	26, // [26:26] is the sub-list for extension extendee
	0,  // [0:26] is the sub-list for field type_name
}

func init() { file_contract_proto_init() }
func file_contract_proto_init() {
	if File_contract_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_contract_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   39,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_contract_proto_goTypes,
		DependencyIndexes: file_contract_proto_depIdxs,
		EnumInfos:         file_contract_proto_enumTypes,
		MessageInfos:      file_contract_proto_msgTypes,
	}.Build()
	File_contract_proto = out.File
	file_contract_proto_rawDesc = nil
	file_contract_proto_goTypes = nil
	file_contract_proto_depIdxs = nil
}
