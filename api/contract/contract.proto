/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

syntax = "proto3";
package contract;
//import "github.com/mwitkow/go-proto-validators/validator.proto";
//protoc --proto_path=./ --go_out=./api/contract --go-triple_out=./api/contract  ./api/contract/contract.proto
option go_package = "./;contract";

service Contract {
  rpc RegisterPerson (RegisterPersonRequest) returns (RegisterResponse) {} //个人注册
  rpc PersonVerify (PersonVerifyRequest) returns (PersonVerifyResponse) {} //个人身份校验
  rpc CheckPersonVerifySing (CheckPersonVerifySingRequest) returns (CommonResponse) {} //个人身份校验

  rpc RegisterOrg (RegisterOrgRequest) returns (RegisterResponse) {} //主体注册
  rpc CompanyVerify (CompanyVerifyRequest) returns (PersonVerifyResponse) {} //主体身份校验

  rpc FindPersonCertInfo (FindCertInfo) returns (PersonCertInfoResponse) {} //主体身份校验
  rpc FindCompanyCertInfo (FindCertInfo) returns (CompanyCertInfoResponse) {} //主体身份校验

  rpc ApplyCert (ApplyCertRequest) returns (CommonResponse) {} //颁发证书

  rpc AddSignature (AddSignRequest) returns (AddSignResponse) {} //实名证书申请
  rpc CustomSign (CustomSignRequest) returns (CustomSignResponse) {} //自定义印章
  rpc UploadTemplate (UploadTemplateRequest) returns (CommonResponse) {} //上传文本
  rpc UploadDocs (UploadDocsRequest) returns (CommonResponse) {} //上传文本
  rpc GenerateContract (GenerateContractRequest) returns (ViewCommonResponse) {} //模板填充
  rpc ExtSignAuto (ExtSignAutoRequest) returns (ViewCommonResponse) {} //自动签章
  rpc BeforeAuthSign (BeforeAuthSignRequest) returns (JumpCommonResponse) {} //自动签章 授权
  rpc ExtSign (ExtSignRequest) returns (JumpCommonResponse) {} //授权
  rpc ViewContract (ContractRequest) returns (JumpCommonResponse) {} //查看合同
  rpc DownLoadContract (ContractRequest) returns (JumpCommonResponse) {} //查看合同
  rpc ContractFiling (ContractRequest) returns (CommonResponse) {} //合同归档
  rpc GetPdfTemplateKeys (PdfTemplateKeysRequest) returns (PdfTemplateKeysResponse) {} //获取合同模板的key值

  rpc AuthStatus (CustomerIdRequest) returns (AuthStatusResponse) {} //获取合同模板的key值
  rpc CancelExtSignAuto (CancelExtSignAutoRequest) returns (JumpCommonResponse) {} //取消授权签协议接口

  rpc GenerateContractInBatches(GenerateContractInBatchesRequest)returns(GenerateContractInBatchesResponse){}//批量生成合同模板，用于一键签署
  rpc DownLoadContractPdf(ContractRequest)returns(JumpCommonResponse){} //下载合同pdf
}

message Fdd {
  string url = 1;
  string appId   =2;
  string appSecret =3;
  string contentType =4;
}
message CheckPersonVerifySingRequest {
  string transactionNo = 1;
  string personName = 2;
  string status = 3;
  string companyName = 4;
  string authenticationType = 5;
  string sign = 6;
  Fdd fdd = 13; //法大大的配置,有责使用，无则默认
}


message CancelExtSignAutoRequest {
  string CustomerId = 1;
  string NotifyUrl = 2;
  string returnUrl = 3;
  Fdd fdd = 4; //法大大的配置,有责使用，无则默认
}

message Person {
  string AreaCode = 1;
  string AuditFailReason = 2;
  string AuditorTime = 3;
  string BackgroundIdCardPath = 4;
  string BankCardNo = 5;
  string Birthday = 6;
  string CertType = 7;
  string ExpiresDate = 8;
  string Fork = 9;
  string GesturesPhotoPath = 10;
  string HeadPhotoPath = 11;
  string IdCard = 12;
  string IsLongTerm = 13;
  double IsPassFourElement = 14;
  double IsPassThreeElement = 15;
  string IssueAuthority = 16;
  string Mobile = 17;
  string PersonName = 18;
  string PhotoUuid = 19;
  string Sex = 20;
  string StartDate = 21;
  string Status = 22;
  string Type = 23;
  string VerifyType = 24;
  string Address = 25;
}

message PersonCertInfoResponse {
  Person Person  = 1 [json_name = "person"];
  string AuthenticationSubmitTime  = 2 [json_name = "authenticationSubmitTime"];
  string PassTime  = 3 [json_name = "passTime"];
  string TransactionNo  = 4 [json_name = "transactionNo"];
  string Type  = 5 [json_name = "type"];
}

message FindCertInfo {
  string VerifiedSerialno  = 1 [json_name = "verifiedSerialno"];
  Fdd fdd = 2; //法大大的配置,有责使用，无则默认
}
message Company{
   string AuditFailReason = 1;
   string AuditorTime = 2;
   string CertificatesType = 3;
   string CompanyEmail = 4;
   string CompanyName = 5;
   string Hasagent = 6;
   string Legal = 7;
   string LegalMobile = 8;
   string LegalName = 9;
   string Organization = 10;
   string OrganizationPath = 11;
   string OrganizationType = 12;
   string RegFormPath = 13;
   string RelatedTransactionNo = 14;
   string Status = 15;
   string VerifyType = 16;
}

message Manager {
  string AreaCode = 1;
  string AuditFailReason = 2;
  string AuditorTime = 3;
  string BackgroundIdCardPath = 4;
  string Birthday = 5;
  string ExpiresDate = 6;
  string Fork = 7;
  string HeadPhotoPath = 8;
  string IdCard = 9;
  string IsLongTerm = 10;
  string IssueAuthority = 11;
  string Mobile = 12;
  string PersonName = 13;
  string PhotoUuid = 14;
  string Sex = 15;
  string StartDate = 16;
  string Status = 17;
  string Type = 18;
  string VerifyType = 19;
  string Address = 20;
}

message CompanyCertInfoResponse {
  Company Company  = 1 [json_name = "Company"];
  Manager Manager  = 2 [json_name = "manager"];
  string AuthenticationSubmitTime  = 3 [json_name = "authenticationSubmitTime"];
  string PassTime  = 4 [json_name = "passTime"];
  string TransactionNo  = 5 [json_name = "transactionNo"];
  string Type  = 6 [json_name = "type"];
}

message AuthStatusResponse {
  uint64 AuthType  = 1 [json_name = "authType"];
  string ContractId  = 2 [json_name = "contractId"];
  uint64 Status  = 3 [json_name = "status"];
  string TransactionId  = 4 [json_name = "transactionId"];
}

message ApplyCertRequest {
  string CustomerId = 1 [json_name = "customerId"];
  string VerifiedSerialno  = 2 [json_name = "verifiedSerialno"];
  Fdd fdd = 3; //法大大的配置,有责使用，无则默认
}

message ViewCommonResponse {
  string DownloadUrl = 1 [json_name = "downloadUrl"];
  string ViewPdfUrl  = 2 [json_name = "viewPdfUrl"];
}


message PdfTemplateKeysRequest {
  string TemplateId = 1 [json_name = "templateId"];
  Fdd fdd = 2; //法大大的配置,有责使用，无则默认
}

message PdfTemplateKeysResponse {
  repeated string Keys = 1 [json_name = "Keys"];
}

message JumpCommonResponse {
  string JumpUrl = 1 [json_name = "jumpUrl"];
}

message ContractRequest {
  string ContractId = 1 [json_name = "contractId"];
  Fdd fdd = 13; //法大大的配置,有责使用，无则默认
}

message ExtSignRequest {
  string TransactionId = 1 [json_name = "transactionId"];
  string ContractId  = 2 [json_name = "contractId"];
  string CustomerId  = 3 [json_name = "customerId"];
  string ReturnUrl  = 4 [json_name = "returnUrl"];
  string DocTitle  = 5 [json_name = "docTitle"];
  string OpenEnvironment  = 6 [json_name = "openEnvironment"];
  string MobileSignType  = 7 [json_name = "mobileSignType"];
  string SignKeyword  = 8 [json_name = "signKeyword"];
  string Keyx  = 9 [json_name = "keyx"];
  string Keyy  = 10 [json_name = "keyy"];
  string SignatureShowTime  = 11 [json_name = "signatureShowTime"];
  string PcHandSignature  = 12 [json_name = "PcHandSignature"];
  Fdd fdd = 13; //法大大的配置,有责使用，无则默认
}

message BeforeAuthSignRequest {
  string TransactionId = 1 [json_name = "transactionId"];
  string ContractId  = 2 [json_name = "contractId"];
  string CustomerId  = 3 [json_name = "customerId"];
  string ReturnUrl  = 4 [json_name = "returnUrl"];
  string NotifyUrl  = 5 [json_name = "notifyUrl"];
  Fdd fdd = 6; //法大大的配置,有责使用，无则默认
}


message UploadDocsRequest {
  string  ContractId        = 1     [json_name = "customerId"];
  string  DocTitle          = 2     [json_name = "docTitle"];
  string  DocUrl            = 3     [json_name = "docUrl"];
  string  File              = 4     [json_name = "file"];
  //string  doc_type        = 1     [json_name = "customerId"];
  Fdd fdd = 6; //法大大的配置,有责使用，无则默认
}

message CompanyVerifyRequest {
  string  CustomerId        = 1     [json_name = "customerId"];
  Fdd fdd = 2; //法大大的配置,有责使用，无则默认
}

message UploadTemplateResponse {
  string  CustomerId        = 1     [json_name = "customerId"];
}

enum FddResultType{
  NoResultType=0;           //法大大默认值为2
  SuccessReturnFailedKeep=1;//认证通过直接跳转到return_url；认证不通过停留在原因页面
  UseReturnButton=2;        //无论是否认证通过均停留在法大大页面。需要用户点击“完成认证”或“退出认证”后跳转到return_url或法大大指定页面
  AlwaysReturn=3;           //无论是否认证通过均跳转到return_url。
}
message PersonVerifyRequest {
    string CustomerId       = 1     [json_name = "customerId"];
    string VerifiedWay      = 2     [json_name = "verifiedWay"];
    string CustomerName     = 3     [json_name = "customerName"];
    string CustomerIdentNo  = 4     [json_name = "customerIdentNo"];
    string Mobile           = 5     [json_name = "mobile"];
    string ReturnUrl        = 6     [json_name = "returnUrl"];
    string certType         = 7     ;
    string apiUrl           = 8 ;
    string isMini           = 9 ;
    Fdd fdd = 10; //法大大的配置,有责使用，无则默认
    FddResultType resultType =11;
    string notifyUrl=12;
}

message PersonVerifyResponse {
  string TransactionNo =1;
  string Url           =2;
}

message RegisterResponse {
  string CustomerId = 1     [json_name = "customerId"];
}

message CustomerIdRequest {
  string CustomerId = 1     [json_name = "customerId"];
  Fdd fdd = 2; //法大大的配置,有责使用，无则默认
}

message CommonResponse {
}

message RegisterOrgRequest {
  string                  OpenId        = 1     [json_name = "openId"];
  Fdd fdd = 2; //法大大的配置,有责使用，无则默认
}

message RegisterPersonRequest {
  string                 OpenId            = 1     [json_name = "openId"];
  Fdd fdd = 11; //法大大的配置,有责使用，无则默认
}

message AddSignRequest {
  string    CustomerId            = 1     [json_name = "customerId"];
  bytes     ReadImgByte           = 2     [json_name = "readImgByte"];
  Fdd fdd = 3; //法大大的配置,有责使用，无则默认
}

message AddSignResponse {
  string SignatureId  = 1     [json_name = "signatureId"];
}

message CustomSignRequest {
  string CustomerId = 1     [json_name = "customerId"];
  string Content  = 2     [json_name = "content"];
  Fdd fdd = 3; //法大大的配置,有责使用，无则默认
}

message CustomSignResponse {
  string SignatureImgBase64 = 1     [json_name = "signatureImgBase64"];
}


message UploadTemplateRequest {
  string TemplateId = 1     [json_name = "templateId"];
  string DocUrl = 2     [json_name = "docUrl"];
  Fdd fdd = 3; //法大大的配置,有责使用，无则默认
}

message ExtSignAutoRequest {
  string    TransactionId = 1     [json_name = "transactionId"];
  string    ContractId    = 2     [json_name = "contractId"];
  string    CustomerId    = 3     [json_name = "customerId"];
  string    ClientRole    = 4     [json_name = "clientRole"];
  string    DocTitle      = 5     [json_name = "docTitle"];
  string    SignKeyword   = 6     [json_name = "signKeyword"];
  string    SignatureId   = 7     [json_name = "signatureId"];
  string    KeyX   = 9     [json_name = "keyX"];
  string    KeyY   = 10     [json_name = "keyY"];
  Fdd fdd = 11; //法大大的配置,有责使用，无则默认
}

message GenerateContractRequest {
    string TemplateId     = 1 [json_name="templateId"];
    string ContractId     = 2 [json_name="contractId"];
    string ParameterMap   = 3 [json_name="ParameterMap"];
    string DynamicTables = 4 [json_name="dynamicTables"];
    string DocTitle =5 [json_name="docTitle"];
    string apiHost = 6 ;
    Fdd fdd = 7; //法大大的配置,有责使用，无则默认
}

message SignaturePosition{
    string pagenum =1;//需要保留json的omit值
    string x=2;//需要保留json的omit值
    string y=3;//需要保留json的omit值
}
message SignData{
  string ContractId=1;//需要保留json的omit值
  string TransactionId=2;//需要保留json的omit值
  string signKeyword=3;//需要保留json的omit值
  repeated SignaturePosition signaturePositions=4;//需要保留json的omit值
  string keyx=5;
  string keyy=6;
}
message GenerateContractInBatchesRequest{
  string batchId =1 [json_name="batch_id"];//必填 批次号（流水号）
  repeated SignData signData =2 [json_name="sign_data"];//必填 签章数据
  string customerId =3 [json_name="customer_id"];//必填 客户编号 JsonArray[sign_data] 需要URLEncoder，编码UTF-8
//  string outhCustomerId =4 [json_name="outh_customer_id"];//选填 代理人客户编号
  string batchTitle =5 [json_name="batch_title"];//必填 批量请求标题需要 如 xx 批量签署” 需要URLEncoder
  string mobileSignType =6 [json_name="mobile_sign_type"];//必填 签章类型
  string returnUrl =7 [json_name="return_url"];//必填 页面跳转 URL,长度限制500 需要URLEncoder//  string NotifyUrl =8 [json_name="notify_url"];
  Fdd fdd = 8; //法大大的配置,有责使用，无则默认
//  string VerificationType =9 [json_name="verification_type"];
//  string SignatureId =10 [json_name="signature_id"];
//  string AcrossSignatureId =11 [json_name="across_signature_id"];
//  string SignatureShowTime =12 [json_name="signature_show_time"];
//  string SaveHandSignature =13 [json_name="save_hand_signature"];
//  string PositionType =14 [json_name="position_type"];
//  string WritingTrack =15 [json_name="writing_track"];
//  string OpenEnvironment =16 [json_name="open_environment"];
//  string PcHandSignature =17 [json_name="pc_hand_signature"];
//  string ReadTime =18 [json_name="read_time"];
//  string BatchSignReadFlag =19 [json_name="batch_sign_read_flag"];
//  string AudioContent =20 [json_name="audio_content"];
//  string QuestionId =21 [json_name="question_id"];
}
message GenerateContractInBatchesResponse{
    string jumpUrl=3;
}
