syntax = "proto3";
package chain;
import "github.com/mwitkow/go-proto-validators@v0.3.2/validator.proto";

option go_package = "./;chain";

// The approval service definition.
service Chain {
  rpc CoBaiduChain(BaiduReq) returns (CoBaiduChainResponse) {};//百度上链
  rpc CoBaiduCertify(BaiduReq) returns (CoBaiduChainResponse) {};//百度上链
  rpc BaiduReqInfo(BaiduReqInfoRequest) returns (BaiduReqInfoResponse) {};//百度上链
  rpc BaiduInfoByTxtId(BaiduInfoByTxtIdRequest) returns (BaiduInfoByTxtIdResponse) {};//百度上链
  rpc CoChengChain(CoChengChainRequest) returns (CoChengChainResponse) {};//澄上链
  rpc CoWtChain(CoChengChainRequest) returns (CoChainResponse) {};//梧桐上链

  rpc InitAccount(InitAccountRequest) returns (InitAccountResponse) {};//铸造NFT
  rpc CreateAccount(CreateAccountRequest) returns (InitAccountResponse) {};//创建账号
  rpc CastNFT(CastNFTRequest) returns (CastNFTResponse) {};//铸造NFT
  rpc NFTByHash(GetNFTRequest) returns (CastNFTRequest) {};//查询
  rpc SearchNFT(GetNFTRequest) returns (NftHashList) {};//查询
  rpc NFTList(NFTListRequest) returns (NFTListResponse) {};//获取NFT列表
  rpc BurnNFT(BurnNFTRequest) returns (BurnNFTResponse) {};//销毁token

  rpc Transfer(TransferRequest) returns (TransferResponse) {};//转让成功自动解锁
  rpc TransferAutoLock(TransferRequest) returns (TransferResponse) {};//转让,自动加锁解锁
  rpc Lock(LockRequest) returns (LockResponse) {};//锁定,无法二次锁定
  rpc UnLock(LockRequest) returns (LockResponse) {};//解锁

  rpc ECert(Identification) returns (UploadFileResponse) {};//生成电子证书
  rpc DbECert(Identification) returns (UploadFileResponse) {};//生成电子证书
}

message BaiduInfoByTxtIdResponse{

}

message GetNFTRequest{
  string DciID         =1 [json_name = "dciID"];
  string Hash         =2 [json_name = "hash"];
}

message NftHashList{
  repeated string Hashs         =2 [json_name = "hashs"];
}

message BaiduReqInfoRequest{
  string RequestId         =1;
}

message BaiduInfoByTxtIdRequest{
  string txtId         =1;
}

message BaiduReqInfoResponse{
  string TxId         =1;
  string BlockId      =2;
  string CertUrl      =3;
  int64  CreateTime   =4;
  uint64 Height       =5;
}

message CreateAccountRequest{
}

message InitAccountRequest{
  string Mnemonic         =1;
  FonContract FonContract =2;
}

message InitAccountResponse{
  string Mnemonic         =1;
  string Address         =2;
}

message LockRequest{
  string BuyAddress =1;
  Seller Seller     =2;
  repeated string Hash = 3;
  FonContract FonContract = 4;
}

message Seller{
  string Address  = 1;
  string Mnemonic  = 2;
}

message LockResponse{


}

message UnLockResponse{

}

message IdentArtworkInfo{
  string ArtiscticName = 1;   //`json:"艺术品名称"`
  string ArtworkNum = 2;         //`json:"艺术品编号"`
  string Author = 3;             //`json:"标注作者"`
  uint64  AgeOfCreation = 4;         //`json:"标注创作年份"` //创作年代
  string  ModelAddress  =5 ;//`json:"标注创作地点"`
  string TextureOfMaterial =6; //`json:"材质"` //材质
  uint64  Width            =7;    //`json:"艺术品尺寸(宽)"`
  uint64  Length           =8;    //`json:"艺术品尺寸(长)"`
}

message Identification {
  string Uid                            =1;                         //`json:"uid"`
  IdentArtworkInfo IdentArtworkInfoPro  =2;
  Artist Artist                         =3;
  string   ArtistBaiduchainHash         =4;
  string   BaiduTransactionHash         =5;
  string   WindupTime                   =6;
  string   Pic2000Src                   =7;   //直接是2000的可访问地址
}

message Artist{
  string Uid   = 1;
  string Photo  = 2; //整个可访问的
  string  Address  =3 ;//`json:"标注创作地点"`
}

message UploadFileResponse {
  string Src = 1;
}

message CoChainRequest {
  int64  Status = 1;
  int64  UserId = 2;
}

message CoChengChainRequest {
  string  Data = 1;
  string  BusinessId = 2;
}

message BaiduReq {
  string FileHash = 1 [json_name = "fileHash"];
  string FileName = 2 [json_name = "fileName"];
  string Content = 3 [json_name = "content"];
  string UserName = 4 [json_name = "userName"];
  string UploadData = 5 [json_name = "uploadData"];
  //string Eid = 2 [json_name = "eid"];
  string Address = 6 [json_name = "address"];
  string EvidenceType = 7 [json_name = "EvidenceType"];
  string CallbackUrl = 8 [json_name = "callbackUrl"];
  string AppId = 9 [json_name = "appId"];
  string CallbackConfig = 10 [json_name = "callback_config"];
}

message CoChengChainResponse{
  string TxId                   = 1;
  string ConfirmationLetterUrl  = 2;
}

message CoBaiduChainResponse {
  string RequestId = 1;
}

message CoChainResponse {
  string Figure = 1;
}

message CastNFTRequest {
  //map<string, string> Args = 2 [json_name = "args"];
  NFT         ArtWork     = 1 [json_name = "nft"];
  FonContract FonContract = 3;
}

message CastNFTResponse {
  string Hash = 1[json_name = "hash"];
}

message FonContract{
  string Node = 1;
  string ContractType =2;
  string ContractName =3;
}

message NFTListRequest {
  string Address = 1;
  FonContract FonContract = 2;
}


message TxReceipt {
  string SerialNum       =1 [json_name = "serialNum"];
  string PaymentPlatform =2 [json_name = "paymentPlatform"];
  uint64 Price           =3 [json_name = "price"];
}

message NFT {
   string              DciID             =1  [json_name = "dciid"]; //作品ID
   string              DciHash           =2  [json_name = "dciHash",(validator.field) = {string_not_empty: true,human_error: "hash必须要有"} ]; //作品hash
   string              DciName           =3  [json_name = "dciName"]; //作品名
   string              DciMaker          =4  [json_name = "dciMaker"]; //作者
   string              DciUrl            =5  [json_name = "dciUrl"]; //作品图片路径
   string              DciOwner          =6  [json_name = "dciOwner"]; //作品拥有者
   int64               DciType           =7  [json_name = "dciType"]; //作品类型
   int64               DciCreateProperty =8  [json_name = "dciCreateProperty"]; //作品属性
   repeated string     DciFrom           =9  [json_name = "dciFrom"]; //基于那个作品的衍生品
   int64               DciAttribute      =10 [json_name = "dciAttribute"]; //作品属性 0-版权，1-物权
   string              LastTransaction   =11 ;//最后一笔交易
   uint64              AverageTxPrice    =12 ;//成交均价
   uint64              CurTxPrice        =13 [json_name = "curTxPrice"]; //当前标价
   repeated TxReceipt  TReceipt          =14 ;//链下付款凭证&&成交价格
   string              ContractNum       =15 [json_name = "contractNum"]; //合同编号
   string              CertificateNum    =16 [json_name = "certificateNum"]; //著作权登记编号
   string              Introduction      =17 [json_name = "introduction"];
   int64               Length            =18 [json_name = "length"];
   int64               Width             =19 [json_name = "width"];
   string              Url               =20 [json_name = "url"];
   string              CollectionName    =21 [json_name = "collectionName"]; //合辑名
   string              FolderImg         =22 [json_name = "folderImg"]; //文件夹图片
   string              PriceJson         =23 [json_name = "priceJson"];
}

message NFTListResponse {
  repeated NFT data = 1;
}

message BurnNFTRequest {
  string Address =1;
  string Hash =2;
  string Mnemonic=3;
  FonContract FonContract = 4;
}

message BurnNFTResponse {
}

message TransferRequest {
  string BuyAddress       =1;
  Seller seller           =2;
  string Hash             =3;
  FonContract FonContract =4;
  string ContractNum      =6;
  int64 Platform          =7;
  string SerialNum        =8;
  string BuyMnemonic      =9;
}

message TransferResponse {
}

message ECertRequest {
}

message ECertResponse {
}