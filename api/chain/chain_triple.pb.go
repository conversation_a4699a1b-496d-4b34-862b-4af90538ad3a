// Code generated by protoc-gen-go-triple. DO NOT EDIT.
// versions:
// - protoc-gen-go-triple v1.0.8
// - protoc             v3.21.12
// source: api/chain/chain.proto

package chain

import (
	context "context"
	protocol "dubbo.apache.org/dubbo-go/v3/protocol"
	dubbo3 "dubbo.apache.org/dubbo-go/v3/protocol/dubbo3"
	invocation "dubbo.apache.org/dubbo-go/v3/protocol/invocation"
	grpc_go "github.com/dubbogo/grpc-go"
	codes "github.com/dubbogo/grpc-go/codes"
	metadata "github.com/dubbogo/grpc-go/metadata"
	status "github.com/dubbogo/grpc-go/status"
	common "github.com/dubbogo/triple/pkg/common"
	constant "github.com/dubbogo/triple/pkg/common/constant"
	triple "github.com/dubbogo/triple/pkg/triple"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc_go.SupportPackageIsVersion7

// ChainClient is the client API for Chain service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type ChainClient interface {
	CoBaiduChain(ctx context.Context, in *BaiduReq, opts ...grpc_go.CallOption) (*CoBaiduChainResponse, common.ErrorWithAttachment)
	CoBaiduCertify(ctx context.Context, in *BaiduReq, opts ...grpc_go.CallOption) (*CoBaiduChainResponse, common.ErrorWithAttachment)
	BaiduReqInfo(ctx context.Context, in *BaiduReqInfoRequest, opts ...grpc_go.CallOption) (*BaiduReqInfoResponse, common.ErrorWithAttachment)
	BaiduInfoByTxtId(ctx context.Context, in *BaiduInfoByTxtIdRequest, opts ...grpc_go.CallOption) (*BaiduInfoByTxtIdResponse, common.ErrorWithAttachment)
	CoChengChain(ctx context.Context, in *CoChengChainRequest, opts ...grpc_go.CallOption) (*CoChengChainResponse, common.ErrorWithAttachment)
	CoWtChain(ctx context.Context, in *CoChengChainRequest, opts ...grpc_go.CallOption) (*CoChainResponse, common.ErrorWithAttachment)
	InitAccount(ctx context.Context, in *InitAccountRequest, opts ...grpc_go.CallOption) (*InitAccountResponse, common.ErrorWithAttachment)
	CreateAccount(ctx context.Context, in *CreateAccountRequest, opts ...grpc_go.CallOption) (*InitAccountResponse, common.ErrorWithAttachment)
	CastNFT(ctx context.Context, in *CastNFTRequest, opts ...grpc_go.CallOption) (*CastNFTResponse, common.ErrorWithAttachment)
	NFTByHash(ctx context.Context, in *GetNFTRequest, opts ...grpc_go.CallOption) (*CastNFTRequest, common.ErrorWithAttachment)
	SearchNFT(ctx context.Context, in *GetNFTRequest, opts ...grpc_go.CallOption) (*NftHashList, common.ErrorWithAttachment)
	NFTList(ctx context.Context, in *NFTListRequest, opts ...grpc_go.CallOption) (*NFTListResponse, common.ErrorWithAttachment)
	BurnNFT(ctx context.Context, in *BurnNFTRequest, opts ...grpc_go.CallOption) (*BurnNFTResponse, common.ErrorWithAttachment)
	Transfer(ctx context.Context, in *TransferRequest, opts ...grpc_go.CallOption) (*TransferResponse, common.ErrorWithAttachment)
	TransferAutoLock(ctx context.Context, in *TransferRequest, opts ...grpc_go.CallOption) (*TransferResponse, common.ErrorWithAttachment)
	Lock(ctx context.Context, in *LockRequest, opts ...grpc_go.CallOption) (*LockResponse, common.ErrorWithAttachment)
	UnLock(ctx context.Context, in *LockRequest, opts ...grpc_go.CallOption) (*LockResponse, common.ErrorWithAttachment)
	ECert(ctx context.Context, in *Identification, opts ...grpc_go.CallOption) (*UploadFileResponse, common.ErrorWithAttachment)
	DbECert(ctx context.Context, in *Identification, opts ...grpc_go.CallOption) (*UploadFileResponse, common.ErrorWithAttachment)
}

type chainClient struct {
	cc *triple.TripleConn
}

type ChainClientImpl struct {
	CoBaiduChain     func(ctx context.Context, in *BaiduReq) (*CoBaiduChainResponse, error)
	CoBaiduCertify   func(ctx context.Context, in *BaiduReq) (*CoBaiduChainResponse, error)
	BaiduReqInfo     func(ctx context.Context, in *BaiduReqInfoRequest) (*BaiduReqInfoResponse, error)
	BaiduInfoByTxtId func(ctx context.Context, in *BaiduInfoByTxtIdRequest) (*BaiduInfoByTxtIdResponse, error)
	CoChengChain     func(ctx context.Context, in *CoChengChainRequest) (*CoChengChainResponse, error)
	CoWtChain        func(ctx context.Context, in *CoChengChainRequest) (*CoChainResponse, error)
	InitAccount      func(ctx context.Context, in *InitAccountRequest) (*InitAccountResponse, error)
	CreateAccount    func(ctx context.Context, in *CreateAccountRequest) (*InitAccountResponse, error)
	CastNFT          func(ctx context.Context, in *CastNFTRequest) (*CastNFTResponse, error)
	NFTByHash        func(ctx context.Context, in *GetNFTRequest) (*CastNFTRequest, error)
	SearchNFT        func(ctx context.Context, in *GetNFTRequest) (*NftHashList, error)
	NFTList          func(ctx context.Context, in *NFTListRequest) (*NFTListResponse, error)
	BurnNFT          func(ctx context.Context, in *BurnNFTRequest) (*BurnNFTResponse, error)
	Transfer         func(ctx context.Context, in *TransferRequest) (*TransferResponse, error)
	TransferAutoLock func(ctx context.Context, in *TransferRequest) (*TransferResponse, error)
	Lock             func(ctx context.Context, in *LockRequest) (*LockResponse, error)
	UnLock           func(ctx context.Context, in *LockRequest) (*LockResponse, error)
	ECert            func(ctx context.Context, in *Identification) (*UploadFileResponse, error)
	DbECert          func(ctx context.Context, in *Identification) (*UploadFileResponse, error)
}

func (c *ChainClientImpl) GetDubboStub(cc *triple.TripleConn) ChainClient {
	return NewChainClient(cc)
}

func (c *ChainClientImpl) XXX_InterfaceName() string {
	return "chain.Chain"
}

func NewChainClient(cc *triple.TripleConn) ChainClient {
	return &chainClient{cc}
}

func (c *chainClient) CoBaiduChain(ctx context.Context, in *BaiduReq, opts ...grpc_go.CallOption) (*CoBaiduChainResponse, common.ErrorWithAttachment) {
	out := new(CoBaiduChainResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/CoBaiduChain", in, out)
}

func (c *chainClient) CoBaiduCertify(ctx context.Context, in *BaiduReq, opts ...grpc_go.CallOption) (*CoBaiduChainResponse, common.ErrorWithAttachment) {
	out := new(CoBaiduChainResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/CoBaiduCertify", in, out)
}

func (c *chainClient) BaiduReqInfo(ctx context.Context, in *BaiduReqInfoRequest, opts ...grpc_go.CallOption) (*BaiduReqInfoResponse, common.ErrorWithAttachment) {
	out := new(BaiduReqInfoResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/BaiduReqInfo", in, out)
}

func (c *chainClient) BaiduInfoByTxtId(ctx context.Context, in *BaiduInfoByTxtIdRequest, opts ...grpc_go.CallOption) (*BaiduInfoByTxtIdResponse, common.ErrorWithAttachment) {
	out := new(BaiduInfoByTxtIdResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/BaiduInfoByTxtId", in, out)
}

func (c *chainClient) CoChengChain(ctx context.Context, in *CoChengChainRequest, opts ...grpc_go.CallOption) (*CoChengChainResponse, common.ErrorWithAttachment) {
	out := new(CoChengChainResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/CoChengChain", in, out)
}

func (c *chainClient) CoWtChain(ctx context.Context, in *CoChengChainRequest, opts ...grpc_go.CallOption) (*CoChainResponse, common.ErrorWithAttachment) {
	out := new(CoChainResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/CoWtChain", in, out)
}

func (c *chainClient) InitAccount(ctx context.Context, in *InitAccountRequest, opts ...grpc_go.CallOption) (*InitAccountResponse, common.ErrorWithAttachment) {
	out := new(InitAccountResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/InitAccount", in, out)
}

func (c *chainClient) CreateAccount(ctx context.Context, in *CreateAccountRequest, opts ...grpc_go.CallOption) (*InitAccountResponse, common.ErrorWithAttachment) {
	out := new(InitAccountResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/CreateAccount", in, out)
}

func (c *chainClient) CastNFT(ctx context.Context, in *CastNFTRequest, opts ...grpc_go.CallOption) (*CastNFTResponse, common.ErrorWithAttachment) {
	out := new(CastNFTResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/CastNFT", in, out)
}

func (c *chainClient) NFTByHash(ctx context.Context, in *GetNFTRequest, opts ...grpc_go.CallOption) (*CastNFTRequest, common.ErrorWithAttachment) {
	out := new(CastNFTRequest)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/NFTByHash", in, out)
}

func (c *chainClient) SearchNFT(ctx context.Context, in *GetNFTRequest, opts ...grpc_go.CallOption) (*NftHashList, common.ErrorWithAttachment) {
	out := new(NftHashList)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/SearchNFT", in, out)
}

func (c *chainClient) NFTList(ctx context.Context, in *NFTListRequest, opts ...grpc_go.CallOption) (*NFTListResponse, common.ErrorWithAttachment) {
	out := new(NFTListResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/NFTList", in, out)
}

func (c *chainClient) BurnNFT(ctx context.Context, in *BurnNFTRequest, opts ...grpc_go.CallOption) (*BurnNFTResponse, common.ErrorWithAttachment) {
	out := new(BurnNFTResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/BurnNFT", in, out)
}

func (c *chainClient) Transfer(ctx context.Context, in *TransferRequest, opts ...grpc_go.CallOption) (*TransferResponse, common.ErrorWithAttachment) {
	out := new(TransferResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/Transfer", in, out)
}

func (c *chainClient) TransferAutoLock(ctx context.Context, in *TransferRequest, opts ...grpc_go.CallOption) (*TransferResponse, common.ErrorWithAttachment) {
	out := new(TransferResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/TransferAutoLock", in, out)
}

func (c *chainClient) Lock(ctx context.Context, in *LockRequest, opts ...grpc_go.CallOption) (*LockResponse, common.ErrorWithAttachment) {
	out := new(LockResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/Lock", in, out)
}

func (c *chainClient) UnLock(ctx context.Context, in *LockRequest, opts ...grpc_go.CallOption) (*LockResponse, common.ErrorWithAttachment) {
	out := new(LockResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/UnLock", in, out)
}

func (c *chainClient) ECert(ctx context.Context, in *Identification, opts ...grpc_go.CallOption) (*UploadFileResponse, common.ErrorWithAttachment) {
	out := new(UploadFileResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/ECert", in, out)
}

func (c *chainClient) DbECert(ctx context.Context, in *Identification, opts ...grpc_go.CallOption) (*UploadFileResponse, common.ErrorWithAttachment) {
	out := new(UploadFileResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/DbECert", in, out)
}

// ChainServer is the server API for Chain service.
// All implementations must embed UnimplementedChainServer
// for forward compatibility
type ChainServer interface {
	CoBaiduChain(context.Context, *BaiduReq) (*CoBaiduChainResponse, error)
	CoBaiduCertify(context.Context, *BaiduReq) (*CoBaiduChainResponse, error)
	BaiduReqInfo(context.Context, *BaiduReqInfoRequest) (*BaiduReqInfoResponse, error)
	BaiduInfoByTxtId(context.Context, *BaiduInfoByTxtIdRequest) (*BaiduInfoByTxtIdResponse, error)
	CoChengChain(context.Context, *CoChengChainRequest) (*CoChengChainResponse, error)
	CoWtChain(context.Context, *CoChengChainRequest) (*CoChainResponse, error)
	InitAccount(context.Context, *InitAccountRequest) (*InitAccountResponse, error)
	CreateAccount(context.Context, *CreateAccountRequest) (*InitAccountResponse, error)
	CastNFT(context.Context, *CastNFTRequest) (*CastNFTResponse, error)
	NFTByHash(context.Context, *GetNFTRequest) (*CastNFTRequest, error)
	SearchNFT(context.Context, *GetNFTRequest) (*NftHashList, error)
	NFTList(context.Context, *NFTListRequest) (*NFTListResponse, error)
	BurnNFT(context.Context, *BurnNFTRequest) (*BurnNFTResponse, error)
	Transfer(context.Context, *TransferRequest) (*TransferResponse, error)
	TransferAutoLock(context.Context, *TransferRequest) (*TransferResponse, error)
	Lock(context.Context, *LockRequest) (*LockResponse, error)
	UnLock(context.Context, *LockRequest) (*LockResponse, error)
	ECert(context.Context, *Identification) (*UploadFileResponse, error)
	DbECert(context.Context, *Identification) (*UploadFileResponse, error)
	mustEmbedUnimplementedChainServer()
}

// UnimplementedChainServer must be embedded to have forward compatible implementations.
type UnimplementedChainServer struct {
	proxyImpl protocol.Invoker
}

func (UnimplementedChainServer) CoBaiduChain(context.Context, *BaiduReq) (*CoBaiduChainResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CoBaiduChain not implemented")
}
func (UnimplementedChainServer) CoBaiduCertify(context.Context, *BaiduReq) (*CoBaiduChainResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CoBaiduCertify not implemented")
}
func (UnimplementedChainServer) BaiduReqInfo(context.Context, *BaiduReqInfoRequest) (*BaiduReqInfoResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method BaiduReqInfo not implemented")
}
func (UnimplementedChainServer) BaiduInfoByTxtId(context.Context, *BaiduInfoByTxtIdRequest) (*BaiduInfoByTxtIdResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method BaiduInfoByTxtId not implemented")
}
func (UnimplementedChainServer) CoChengChain(context.Context, *CoChengChainRequest) (*CoChengChainResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CoChengChain not implemented")
}
func (UnimplementedChainServer) CoWtChain(context.Context, *CoChengChainRequest) (*CoChainResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CoWtChain not implemented")
}
func (UnimplementedChainServer) InitAccount(context.Context, *InitAccountRequest) (*InitAccountResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method InitAccount not implemented")
}
func (UnimplementedChainServer) CreateAccount(context.Context, *CreateAccountRequest) (*InitAccountResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateAccount not implemented")
}
func (UnimplementedChainServer) CastNFT(context.Context, *CastNFTRequest) (*CastNFTResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CastNFT not implemented")
}
func (UnimplementedChainServer) NFTByHash(context.Context, *GetNFTRequest) (*CastNFTRequest, error) {
	return nil, status.Errorf(codes.Unimplemented, "method NFTByHash not implemented")
}
func (UnimplementedChainServer) SearchNFT(context.Context, *GetNFTRequest) (*NftHashList, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SearchNFT not implemented")
}
func (UnimplementedChainServer) NFTList(context.Context, *NFTListRequest) (*NFTListResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method NFTList not implemented")
}
func (UnimplementedChainServer) BurnNFT(context.Context, *BurnNFTRequest) (*BurnNFTResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method BurnNFT not implemented")
}
func (UnimplementedChainServer) Transfer(context.Context, *TransferRequest) (*TransferResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Transfer not implemented")
}
func (UnimplementedChainServer) TransferAutoLock(context.Context, *TransferRequest) (*TransferResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method TransferAutoLock not implemented")
}
func (UnimplementedChainServer) Lock(context.Context, *LockRequest) (*LockResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Lock not implemented")
}
func (UnimplementedChainServer) UnLock(context.Context, *LockRequest) (*LockResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UnLock not implemented")
}
func (UnimplementedChainServer) ECert(context.Context, *Identification) (*UploadFileResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ECert not implemented")
}
func (UnimplementedChainServer) DbECert(context.Context, *Identification) (*UploadFileResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DbECert not implemented")
}
func (s *UnimplementedChainServer) XXX_SetProxyImpl(impl protocol.Invoker) {
	s.proxyImpl = impl
}

func (s *UnimplementedChainServer) XXX_GetProxyImpl() protocol.Invoker {
	return s.proxyImpl
}

func (s *UnimplementedChainServer) XXX_ServiceDesc() *grpc_go.ServiceDesc {
	return &Chain_ServiceDesc
}
func (s *UnimplementedChainServer) XXX_InterfaceName() string {
	return "chain.Chain"
}

func (UnimplementedChainServer) mustEmbedUnimplementedChainServer() {}

// UnsafeChainServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to ChainServer will
// result in compilation errors.
type UnsafeChainServer interface {
	mustEmbedUnimplementedChainServer()
}

func RegisterChainServer(s grpc_go.ServiceRegistrar, srv ChainServer) {
	s.RegisterService(&Chain_ServiceDesc, srv)
}

func _Chain_CoBaiduChain_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(BaiduReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("CoBaiduChain", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Chain_CoBaiduCertify_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(BaiduReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("CoBaiduCertify", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Chain_BaiduReqInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(BaiduReqInfoRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("BaiduReqInfo", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Chain_BaiduInfoByTxtId_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(BaiduInfoByTxtIdRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("BaiduInfoByTxtId", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Chain_CoChengChain_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(CoChengChainRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("CoChengChain", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Chain_CoWtChain_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(CoChengChainRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("CoWtChain", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Chain_InitAccount_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(InitAccountRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("InitAccount", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Chain_CreateAccount_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateAccountRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("CreateAccount", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Chain_CastNFT_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(CastNFTRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("CastNFT", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Chain_NFTByHash_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetNFTRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("NFTByHash", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Chain_SearchNFT_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetNFTRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("SearchNFT", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Chain_NFTList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(NFTListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("NFTList", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Chain_BurnNFT_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(BurnNFTRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("BurnNFT", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Chain_Transfer_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(TransferRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("Transfer", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Chain_TransferAutoLock_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(TransferRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("TransferAutoLock", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Chain_Lock_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(LockRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("Lock", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Chain_UnLock_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(LockRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("UnLock", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Chain_ECert_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(Identification)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("ECert", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Chain_DbECert_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(Identification)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("DbECert", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

// Chain_ServiceDesc is the grpc_go.ServiceDesc for Chain service.
// It's only intended for direct use with grpc_go.RegisterService,
// and not to be introspected or modified (even as a copy)
var Chain_ServiceDesc = grpc_go.ServiceDesc{
	ServiceName: "chain.Chain",
	HandlerType: (*ChainServer)(nil),
	Methods: []grpc_go.MethodDesc{
		{
			MethodName: "CoBaiduChain",
			Handler:    _Chain_CoBaiduChain_Handler,
		},
		{
			MethodName: "CoBaiduCertify",
			Handler:    _Chain_CoBaiduCertify_Handler,
		},
		{
			MethodName: "BaiduReqInfo",
			Handler:    _Chain_BaiduReqInfo_Handler,
		},
		{
			MethodName: "BaiduInfoByTxtId",
			Handler:    _Chain_BaiduInfoByTxtId_Handler,
		},
		{
			MethodName: "CoChengChain",
			Handler:    _Chain_CoChengChain_Handler,
		},
		{
			MethodName: "CoWtChain",
			Handler:    _Chain_CoWtChain_Handler,
		},
		{
			MethodName: "InitAccount",
			Handler:    _Chain_InitAccount_Handler,
		},
		{
			MethodName: "CreateAccount",
			Handler:    _Chain_CreateAccount_Handler,
		},
		{
			MethodName: "CastNFT",
			Handler:    _Chain_CastNFT_Handler,
		},
		{
			MethodName: "NFTByHash",
			Handler:    _Chain_NFTByHash_Handler,
		},
		{
			MethodName: "SearchNFT",
			Handler:    _Chain_SearchNFT_Handler,
		},
		{
			MethodName: "NFTList",
			Handler:    _Chain_NFTList_Handler,
		},
		{
			MethodName: "BurnNFT",
			Handler:    _Chain_BurnNFT_Handler,
		},
		{
			MethodName: "Transfer",
			Handler:    _Chain_Transfer_Handler,
		},
		{
			MethodName: "TransferAutoLock",
			Handler:    _Chain_TransferAutoLock_Handler,
		},
		{
			MethodName: "Lock",
			Handler:    _Chain_Lock_Handler,
		},
		{
			MethodName: "UnLock",
			Handler:    _Chain_UnLock_Handler,
		},
		{
			MethodName: "ECert",
			Handler:    _Chain_ECert_Handler,
		},
		{
			MethodName: "DbECert",
			Handler:    _Chain_DbECert_Handler,
		},
	},
	Streams:  []grpc_go.StreamDesc{},
	Metadata: "api/chain/chain.proto",
}
