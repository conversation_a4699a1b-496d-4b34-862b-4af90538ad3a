// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.26.0
// 	protoc        v3.21.12
// source: api/chain/chain.proto

package chain

import (
	_ "github.com/mwitkow/go-proto-validators"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type BaiduInfoByTxtIdResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *BaiduInfoByTxtIdResponse) Reset() {
	*x = BaiduInfoByTxtIdResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_chain_chain_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BaiduInfoByTxtIdResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BaiduInfoByTxtIdResponse) ProtoMessage() {}

func (x *BaiduInfoByTxtIdResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_chain_chain_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BaiduInfoByTxtIdResponse.ProtoReflect.Descriptor instead.
func (*BaiduInfoByTxtIdResponse) Descriptor() ([]byte, []int) {
	return file_api_chain_chain_proto_rawDescGZIP(), []int{0}
}

type GetNFTRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	DciID string `protobuf:"bytes,1,opt,name=DciID,json=dciID,proto3" json:"DciID,omitempty"`
	Hash  string `protobuf:"bytes,2,opt,name=Hash,json=hash,proto3" json:"Hash,omitempty"`
}

func (x *GetNFTRequest) Reset() {
	*x = GetNFTRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_chain_chain_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetNFTRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetNFTRequest) ProtoMessage() {}

func (x *GetNFTRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_chain_chain_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetNFTRequest.ProtoReflect.Descriptor instead.
func (*GetNFTRequest) Descriptor() ([]byte, []int) {
	return file_api_chain_chain_proto_rawDescGZIP(), []int{1}
}

func (x *GetNFTRequest) GetDciID() string {
	if x != nil {
		return x.DciID
	}
	return ""
}

func (x *GetNFTRequest) GetHash() string {
	if x != nil {
		return x.Hash
	}
	return ""
}

type NftHashList struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Hashs []string `protobuf:"bytes,2,rep,name=Hashs,json=hashs,proto3" json:"Hashs,omitempty"`
}

func (x *NftHashList) Reset() {
	*x = NftHashList{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_chain_chain_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *NftHashList) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NftHashList) ProtoMessage() {}

func (x *NftHashList) ProtoReflect() protoreflect.Message {
	mi := &file_api_chain_chain_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NftHashList.ProtoReflect.Descriptor instead.
func (*NftHashList) Descriptor() ([]byte, []int) {
	return file_api_chain_chain_proto_rawDescGZIP(), []int{2}
}

func (x *NftHashList) GetHashs() []string {
	if x != nil {
		return x.Hashs
	}
	return nil
}

type BaiduReqInfoRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RequestId string `protobuf:"bytes,1,opt,name=RequestId,proto3" json:"RequestId,omitempty"`
}

func (x *BaiduReqInfoRequest) Reset() {
	*x = BaiduReqInfoRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_chain_chain_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BaiduReqInfoRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BaiduReqInfoRequest) ProtoMessage() {}

func (x *BaiduReqInfoRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_chain_chain_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BaiduReqInfoRequest.ProtoReflect.Descriptor instead.
func (*BaiduReqInfoRequest) Descriptor() ([]byte, []int) {
	return file_api_chain_chain_proto_rawDescGZIP(), []int{3}
}

func (x *BaiduReqInfoRequest) GetRequestId() string {
	if x != nil {
		return x.RequestId
	}
	return ""
}

type BaiduInfoByTxtIdRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TxtId string `protobuf:"bytes,1,opt,name=txtId,proto3" json:"txtId,omitempty"`
}

func (x *BaiduInfoByTxtIdRequest) Reset() {
	*x = BaiduInfoByTxtIdRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_chain_chain_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BaiduInfoByTxtIdRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BaiduInfoByTxtIdRequest) ProtoMessage() {}

func (x *BaiduInfoByTxtIdRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_chain_chain_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BaiduInfoByTxtIdRequest.ProtoReflect.Descriptor instead.
func (*BaiduInfoByTxtIdRequest) Descriptor() ([]byte, []int) {
	return file_api_chain_chain_proto_rawDescGZIP(), []int{4}
}

func (x *BaiduInfoByTxtIdRequest) GetTxtId() string {
	if x != nil {
		return x.TxtId
	}
	return ""
}

type BaiduReqInfoResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TxId       string `protobuf:"bytes,1,opt,name=TxId,proto3" json:"TxId,omitempty"`
	BlockId    string `protobuf:"bytes,2,opt,name=BlockId,proto3" json:"BlockId,omitempty"`
	CertUrl    string `protobuf:"bytes,3,opt,name=CertUrl,proto3" json:"CertUrl,omitempty"`
	CreateTime int64  `protobuf:"varint,4,opt,name=CreateTime,proto3" json:"CreateTime,omitempty"`
	Height     uint64 `protobuf:"varint,5,opt,name=Height,proto3" json:"Height,omitempty"`
}

func (x *BaiduReqInfoResponse) Reset() {
	*x = BaiduReqInfoResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_chain_chain_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BaiduReqInfoResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BaiduReqInfoResponse) ProtoMessage() {}

func (x *BaiduReqInfoResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_chain_chain_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BaiduReqInfoResponse.ProtoReflect.Descriptor instead.
func (*BaiduReqInfoResponse) Descriptor() ([]byte, []int) {
	return file_api_chain_chain_proto_rawDescGZIP(), []int{5}
}

func (x *BaiduReqInfoResponse) GetTxId() string {
	if x != nil {
		return x.TxId
	}
	return ""
}

func (x *BaiduReqInfoResponse) GetBlockId() string {
	if x != nil {
		return x.BlockId
	}
	return ""
}

func (x *BaiduReqInfoResponse) GetCertUrl() string {
	if x != nil {
		return x.CertUrl
	}
	return ""
}

func (x *BaiduReqInfoResponse) GetCreateTime() int64 {
	if x != nil {
		return x.CreateTime
	}
	return 0
}

func (x *BaiduReqInfoResponse) GetHeight() uint64 {
	if x != nil {
		return x.Height
	}
	return 0
}

type CreateAccountRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *CreateAccountRequest) Reset() {
	*x = CreateAccountRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_chain_chain_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateAccountRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateAccountRequest) ProtoMessage() {}

func (x *CreateAccountRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_chain_chain_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateAccountRequest.ProtoReflect.Descriptor instead.
func (*CreateAccountRequest) Descriptor() ([]byte, []int) {
	return file_api_chain_chain_proto_rawDescGZIP(), []int{6}
}

type InitAccountRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Mnemonic    string       `protobuf:"bytes,1,opt,name=Mnemonic,proto3" json:"Mnemonic,omitempty"`
	FonContract *FonContract `protobuf:"bytes,2,opt,name=FonContract,proto3" json:"FonContract,omitempty"`
}

func (x *InitAccountRequest) Reset() {
	*x = InitAccountRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_chain_chain_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *InitAccountRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InitAccountRequest) ProtoMessage() {}

func (x *InitAccountRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_chain_chain_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InitAccountRequest.ProtoReflect.Descriptor instead.
func (*InitAccountRequest) Descriptor() ([]byte, []int) {
	return file_api_chain_chain_proto_rawDescGZIP(), []int{7}
}

func (x *InitAccountRequest) GetMnemonic() string {
	if x != nil {
		return x.Mnemonic
	}
	return ""
}

func (x *InitAccountRequest) GetFonContract() *FonContract {
	if x != nil {
		return x.FonContract
	}
	return nil
}

type InitAccountResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Mnemonic string `protobuf:"bytes,1,opt,name=Mnemonic,proto3" json:"Mnemonic,omitempty"`
	Address  string `protobuf:"bytes,2,opt,name=Address,proto3" json:"Address,omitempty"`
}

func (x *InitAccountResponse) Reset() {
	*x = InitAccountResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_chain_chain_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *InitAccountResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InitAccountResponse) ProtoMessage() {}

func (x *InitAccountResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_chain_chain_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InitAccountResponse.ProtoReflect.Descriptor instead.
func (*InitAccountResponse) Descriptor() ([]byte, []int) {
	return file_api_chain_chain_proto_rawDescGZIP(), []int{8}
}

func (x *InitAccountResponse) GetMnemonic() string {
	if x != nil {
		return x.Mnemonic
	}
	return ""
}

func (x *InitAccountResponse) GetAddress() string {
	if x != nil {
		return x.Address
	}
	return ""
}

type LockRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	BuyAddress  string       `protobuf:"bytes,1,opt,name=BuyAddress,proto3" json:"BuyAddress,omitempty"`
	Seller      *Seller      `protobuf:"bytes,2,opt,name=Seller,proto3" json:"Seller,omitempty"`
	Hash        []string     `protobuf:"bytes,3,rep,name=Hash,proto3" json:"Hash,omitempty"`
	FonContract *FonContract `protobuf:"bytes,4,opt,name=FonContract,proto3" json:"FonContract,omitempty"`
}

func (x *LockRequest) Reset() {
	*x = LockRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_chain_chain_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LockRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LockRequest) ProtoMessage() {}

func (x *LockRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_chain_chain_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LockRequest.ProtoReflect.Descriptor instead.
func (*LockRequest) Descriptor() ([]byte, []int) {
	return file_api_chain_chain_proto_rawDescGZIP(), []int{9}
}

func (x *LockRequest) GetBuyAddress() string {
	if x != nil {
		return x.BuyAddress
	}
	return ""
}

func (x *LockRequest) GetSeller() *Seller {
	if x != nil {
		return x.Seller
	}
	return nil
}

func (x *LockRequest) GetHash() []string {
	if x != nil {
		return x.Hash
	}
	return nil
}

func (x *LockRequest) GetFonContract() *FonContract {
	if x != nil {
		return x.FonContract
	}
	return nil
}

type Seller struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Address  string `protobuf:"bytes,1,opt,name=Address,proto3" json:"Address,omitempty"`
	Mnemonic string `protobuf:"bytes,2,opt,name=Mnemonic,proto3" json:"Mnemonic,omitempty"`
}

func (x *Seller) Reset() {
	*x = Seller{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_chain_chain_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Seller) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Seller) ProtoMessage() {}

func (x *Seller) ProtoReflect() protoreflect.Message {
	mi := &file_api_chain_chain_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Seller.ProtoReflect.Descriptor instead.
func (*Seller) Descriptor() ([]byte, []int) {
	return file_api_chain_chain_proto_rawDescGZIP(), []int{10}
}

func (x *Seller) GetAddress() string {
	if x != nil {
		return x.Address
	}
	return ""
}

func (x *Seller) GetMnemonic() string {
	if x != nil {
		return x.Mnemonic
	}
	return ""
}

type LockResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *LockResponse) Reset() {
	*x = LockResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_chain_chain_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LockResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LockResponse) ProtoMessage() {}

func (x *LockResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_chain_chain_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LockResponse.ProtoReflect.Descriptor instead.
func (*LockResponse) Descriptor() ([]byte, []int) {
	return file_api_chain_chain_proto_rawDescGZIP(), []int{11}
}

type UnLockResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *UnLockResponse) Reset() {
	*x = UnLockResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_chain_chain_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UnLockResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UnLockResponse) ProtoMessage() {}

func (x *UnLockResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_chain_chain_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UnLockResponse.ProtoReflect.Descriptor instead.
func (*UnLockResponse) Descriptor() ([]byte, []int) {
	return file_api_chain_chain_proto_rawDescGZIP(), []int{12}
}

type IdentArtworkInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ArtiscticName     string `protobuf:"bytes,1,opt,name=ArtiscticName,proto3" json:"ArtiscticName,omitempty"`         //`json:"艺术品名称"`
	ArtworkNum        string `protobuf:"bytes,2,opt,name=ArtworkNum,proto3" json:"ArtworkNum,omitempty"`               //`json:"艺术品编号"`
	Author            string `protobuf:"bytes,3,opt,name=Author,proto3" json:"Author,omitempty"`                       //`json:"标注作者"`
	AgeOfCreation     uint64 `protobuf:"varint,4,opt,name=AgeOfCreation,proto3" json:"AgeOfCreation,omitempty"`        //`json:"标注创作年份"` //创作年代
	ModelAddress      string `protobuf:"bytes,5,opt,name=ModelAddress,proto3" json:"ModelAddress,omitempty"`           //`json:"标注创作地点"`
	TextureOfMaterial string `protobuf:"bytes,6,opt,name=TextureOfMaterial,proto3" json:"TextureOfMaterial,omitempty"` //`json:"材质"` //材质
	Width             uint64 `protobuf:"varint,7,opt,name=Width,proto3" json:"Width,omitempty"`                        //`json:"艺术品尺寸(宽)"`
	Length            uint64 `protobuf:"varint,8,opt,name=Length,proto3" json:"Length,omitempty"`                      //`json:"艺术品尺寸(长)"`
}

func (x *IdentArtworkInfo) Reset() {
	*x = IdentArtworkInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_chain_chain_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *IdentArtworkInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*IdentArtworkInfo) ProtoMessage() {}

func (x *IdentArtworkInfo) ProtoReflect() protoreflect.Message {
	mi := &file_api_chain_chain_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use IdentArtworkInfo.ProtoReflect.Descriptor instead.
func (*IdentArtworkInfo) Descriptor() ([]byte, []int) {
	return file_api_chain_chain_proto_rawDescGZIP(), []int{13}
}

func (x *IdentArtworkInfo) GetArtiscticName() string {
	if x != nil {
		return x.ArtiscticName
	}
	return ""
}

func (x *IdentArtworkInfo) GetArtworkNum() string {
	if x != nil {
		return x.ArtworkNum
	}
	return ""
}

func (x *IdentArtworkInfo) GetAuthor() string {
	if x != nil {
		return x.Author
	}
	return ""
}

func (x *IdentArtworkInfo) GetAgeOfCreation() uint64 {
	if x != nil {
		return x.AgeOfCreation
	}
	return 0
}

func (x *IdentArtworkInfo) GetModelAddress() string {
	if x != nil {
		return x.ModelAddress
	}
	return ""
}

func (x *IdentArtworkInfo) GetTextureOfMaterial() string {
	if x != nil {
		return x.TextureOfMaterial
	}
	return ""
}

func (x *IdentArtworkInfo) GetWidth() uint64 {
	if x != nil {
		return x.Width
	}
	return 0
}

func (x *IdentArtworkInfo) GetLength() uint64 {
	if x != nil {
		return x.Length
	}
	return 0
}

type Identification struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Uid                  string            `protobuf:"bytes,1,opt,name=Uid,proto3" json:"Uid,omitempty"` //`json:"uid"`
	IdentArtworkInfoPro  *IdentArtworkInfo `protobuf:"bytes,2,opt,name=IdentArtworkInfoPro,proto3" json:"IdentArtworkInfoPro,omitempty"`
	Artist               *Artist           `protobuf:"bytes,3,opt,name=Artist,proto3" json:"Artist,omitempty"`
	ArtistBaiduchainHash string            `protobuf:"bytes,4,opt,name=ArtistBaiduchainHash,proto3" json:"ArtistBaiduchainHash,omitempty"`
	BaiduTransactionHash string            `protobuf:"bytes,5,opt,name=BaiduTransactionHash,proto3" json:"BaiduTransactionHash,omitempty"`
	WindupTime           string            `protobuf:"bytes,6,opt,name=WindupTime,proto3" json:"WindupTime,omitempty"`
	Pic2000Src           string            `protobuf:"bytes,7,opt,name=Pic2000Src,proto3" json:"Pic2000Src,omitempty"` //直接是2000的可访问地址
}

func (x *Identification) Reset() {
	*x = Identification{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_chain_chain_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Identification) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Identification) ProtoMessage() {}

func (x *Identification) ProtoReflect() protoreflect.Message {
	mi := &file_api_chain_chain_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Identification.ProtoReflect.Descriptor instead.
func (*Identification) Descriptor() ([]byte, []int) {
	return file_api_chain_chain_proto_rawDescGZIP(), []int{14}
}

func (x *Identification) GetUid() string {
	if x != nil {
		return x.Uid
	}
	return ""
}

func (x *Identification) GetIdentArtworkInfoPro() *IdentArtworkInfo {
	if x != nil {
		return x.IdentArtworkInfoPro
	}
	return nil
}

func (x *Identification) GetArtist() *Artist {
	if x != nil {
		return x.Artist
	}
	return nil
}

func (x *Identification) GetArtistBaiduchainHash() string {
	if x != nil {
		return x.ArtistBaiduchainHash
	}
	return ""
}

func (x *Identification) GetBaiduTransactionHash() string {
	if x != nil {
		return x.BaiduTransactionHash
	}
	return ""
}

func (x *Identification) GetWindupTime() string {
	if x != nil {
		return x.WindupTime
	}
	return ""
}

func (x *Identification) GetPic2000Src() string {
	if x != nil {
		return x.Pic2000Src
	}
	return ""
}

type Artist struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Uid     string `protobuf:"bytes,1,opt,name=Uid,proto3" json:"Uid,omitempty"`
	Photo   string `protobuf:"bytes,2,opt,name=Photo,proto3" json:"Photo,omitempty"`     //整个可访问的
	Address string `protobuf:"bytes,3,opt,name=Address,proto3" json:"Address,omitempty"` //`json:"标注创作地点"`
}

func (x *Artist) Reset() {
	*x = Artist{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_chain_chain_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Artist) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Artist) ProtoMessage() {}

func (x *Artist) ProtoReflect() protoreflect.Message {
	mi := &file_api_chain_chain_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Artist.ProtoReflect.Descriptor instead.
func (*Artist) Descriptor() ([]byte, []int) {
	return file_api_chain_chain_proto_rawDescGZIP(), []int{15}
}

func (x *Artist) GetUid() string {
	if x != nil {
		return x.Uid
	}
	return ""
}

func (x *Artist) GetPhoto() string {
	if x != nil {
		return x.Photo
	}
	return ""
}

func (x *Artist) GetAddress() string {
	if x != nil {
		return x.Address
	}
	return ""
}

type UploadFileResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Src string `protobuf:"bytes,1,opt,name=Src,proto3" json:"Src,omitempty"`
}

func (x *UploadFileResponse) Reset() {
	*x = UploadFileResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_chain_chain_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UploadFileResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UploadFileResponse) ProtoMessage() {}

func (x *UploadFileResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_chain_chain_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UploadFileResponse.ProtoReflect.Descriptor instead.
func (*UploadFileResponse) Descriptor() ([]byte, []int) {
	return file_api_chain_chain_proto_rawDescGZIP(), []int{16}
}

func (x *UploadFileResponse) GetSrc() string {
	if x != nil {
		return x.Src
	}
	return ""
}

type CoChainRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status int64 `protobuf:"varint,1,opt,name=Status,proto3" json:"Status,omitempty"`
	UserId int64 `protobuf:"varint,2,opt,name=UserId,proto3" json:"UserId,omitempty"`
}

func (x *CoChainRequest) Reset() {
	*x = CoChainRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_chain_chain_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CoChainRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CoChainRequest) ProtoMessage() {}

func (x *CoChainRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_chain_chain_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CoChainRequest.ProtoReflect.Descriptor instead.
func (*CoChainRequest) Descriptor() ([]byte, []int) {
	return file_api_chain_chain_proto_rawDescGZIP(), []int{17}
}

func (x *CoChainRequest) GetStatus() int64 {
	if x != nil {
		return x.Status
	}
	return 0
}

func (x *CoChainRequest) GetUserId() int64 {
	if x != nil {
		return x.UserId
	}
	return 0
}

type CoChengChainRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Data       string `protobuf:"bytes,1,opt,name=Data,proto3" json:"Data,omitempty"`
	BusinessId string `protobuf:"bytes,2,opt,name=BusinessId,proto3" json:"BusinessId,omitempty"`
}

func (x *CoChengChainRequest) Reset() {
	*x = CoChengChainRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_chain_chain_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CoChengChainRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CoChengChainRequest) ProtoMessage() {}

func (x *CoChengChainRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_chain_chain_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CoChengChainRequest.ProtoReflect.Descriptor instead.
func (*CoChengChainRequest) Descriptor() ([]byte, []int) {
	return file_api_chain_chain_proto_rawDescGZIP(), []int{18}
}

func (x *CoChengChainRequest) GetData() string {
	if x != nil {
		return x.Data
	}
	return ""
}

func (x *CoChengChainRequest) GetBusinessId() string {
	if x != nil {
		return x.BusinessId
	}
	return ""
}

type BaiduReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	FileHash   string `protobuf:"bytes,1,opt,name=FileHash,json=fileHash,proto3" json:"FileHash,omitempty"`
	FileName   string `protobuf:"bytes,2,opt,name=FileName,json=fileName,proto3" json:"FileName,omitempty"`
	Content    string `protobuf:"bytes,3,opt,name=Content,json=content,proto3" json:"Content,omitempty"`
	UserName   string `protobuf:"bytes,4,opt,name=UserName,json=userName,proto3" json:"UserName,omitempty"`
	UploadData string `protobuf:"bytes,5,opt,name=UploadData,json=uploadData,proto3" json:"UploadData,omitempty"`
	//string Eid = 2 [json_name = "eid"];
	Address        string `protobuf:"bytes,6,opt,name=Address,json=address,proto3" json:"Address,omitempty"`
	EvidenceType   string `protobuf:"bytes,7,opt,name=EvidenceType,proto3" json:"EvidenceType,omitempty"`
	CallbackUrl    string `protobuf:"bytes,8,opt,name=CallbackUrl,json=callbackUrl,proto3" json:"CallbackUrl,omitempty"`
	AppId          string `protobuf:"bytes,9,opt,name=AppId,json=appId,proto3" json:"AppId,omitempty"`
	CallbackConfig string `protobuf:"bytes,10,opt,name=CallbackConfig,json=callback_config,proto3" json:"CallbackConfig,omitempty"`
}

func (x *BaiduReq) Reset() {
	*x = BaiduReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_chain_chain_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BaiduReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BaiduReq) ProtoMessage() {}

func (x *BaiduReq) ProtoReflect() protoreflect.Message {
	mi := &file_api_chain_chain_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BaiduReq.ProtoReflect.Descriptor instead.
func (*BaiduReq) Descriptor() ([]byte, []int) {
	return file_api_chain_chain_proto_rawDescGZIP(), []int{19}
}

func (x *BaiduReq) GetFileHash() string {
	if x != nil {
		return x.FileHash
	}
	return ""
}

func (x *BaiduReq) GetFileName() string {
	if x != nil {
		return x.FileName
	}
	return ""
}

func (x *BaiduReq) GetContent() string {
	if x != nil {
		return x.Content
	}
	return ""
}

func (x *BaiduReq) GetUserName() string {
	if x != nil {
		return x.UserName
	}
	return ""
}

func (x *BaiduReq) GetUploadData() string {
	if x != nil {
		return x.UploadData
	}
	return ""
}

func (x *BaiduReq) GetAddress() string {
	if x != nil {
		return x.Address
	}
	return ""
}

func (x *BaiduReq) GetEvidenceType() string {
	if x != nil {
		return x.EvidenceType
	}
	return ""
}

func (x *BaiduReq) GetCallbackUrl() string {
	if x != nil {
		return x.CallbackUrl
	}
	return ""
}

func (x *BaiduReq) GetAppId() string {
	if x != nil {
		return x.AppId
	}
	return ""
}

func (x *BaiduReq) GetCallbackConfig() string {
	if x != nil {
		return x.CallbackConfig
	}
	return ""
}

type CoChengChainResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TxId                  string `protobuf:"bytes,1,opt,name=TxId,proto3" json:"TxId,omitempty"`
	ConfirmationLetterUrl string `protobuf:"bytes,2,opt,name=ConfirmationLetterUrl,proto3" json:"ConfirmationLetterUrl,omitempty"`
}

func (x *CoChengChainResponse) Reset() {
	*x = CoChengChainResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_chain_chain_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CoChengChainResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CoChengChainResponse) ProtoMessage() {}

func (x *CoChengChainResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_chain_chain_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CoChengChainResponse.ProtoReflect.Descriptor instead.
func (*CoChengChainResponse) Descriptor() ([]byte, []int) {
	return file_api_chain_chain_proto_rawDescGZIP(), []int{20}
}

func (x *CoChengChainResponse) GetTxId() string {
	if x != nil {
		return x.TxId
	}
	return ""
}

func (x *CoChengChainResponse) GetConfirmationLetterUrl() string {
	if x != nil {
		return x.ConfirmationLetterUrl
	}
	return ""
}

type CoBaiduChainResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RequestId string `protobuf:"bytes,1,opt,name=RequestId,proto3" json:"RequestId,omitempty"`
}

func (x *CoBaiduChainResponse) Reset() {
	*x = CoBaiduChainResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_chain_chain_proto_msgTypes[21]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CoBaiduChainResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CoBaiduChainResponse) ProtoMessage() {}

func (x *CoBaiduChainResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_chain_chain_proto_msgTypes[21]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CoBaiduChainResponse.ProtoReflect.Descriptor instead.
func (*CoBaiduChainResponse) Descriptor() ([]byte, []int) {
	return file_api_chain_chain_proto_rawDescGZIP(), []int{21}
}

func (x *CoBaiduChainResponse) GetRequestId() string {
	if x != nil {
		return x.RequestId
	}
	return ""
}

type CoChainResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Figure string `protobuf:"bytes,1,opt,name=Figure,proto3" json:"Figure,omitempty"`
}

func (x *CoChainResponse) Reset() {
	*x = CoChainResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_chain_chain_proto_msgTypes[22]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CoChainResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CoChainResponse) ProtoMessage() {}

func (x *CoChainResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_chain_chain_proto_msgTypes[22]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CoChainResponse.ProtoReflect.Descriptor instead.
func (*CoChainResponse) Descriptor() ([]byte, []int) {
	return file_api_chain_chain_proto_rawDescGZIP(), []int{22}
}

func (x *CoChainResponse) GetFigure() string {
	if x != nil {
		return x.Figure
	}
	return ""
}

type CastNFTRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	//map<string, string> Args = 2 [json_name = "args"];
	ArtWork     *NFT         `protobuf:"bytes,1,opt,name=ArtWork,json=nft,proto3" json:"ArtWork,omitempty"`
	FonContract *FonContract `protobuf:"bytes,3,opt,name=FonContract,proto3" json:"FonContract,omitempty"`
}

func (x *CastNFTRequest) Reset() {
	*x = CastNFTRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_chain_chain_proto_msgTypes[23]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CastNFTRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CastNFTRequest) ProtoMessage() {}

func (x *CastNFTRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_chain_chain_proto_msgTypes[23]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CastNFTRequest.ProtoReflect.Descriptor instead.
func (*CastNFTRequest) Descriptor() ([]byte, []int) {
	return file_api_chain_chain_proto_rawDescGZIP(), []int{23}
}

func (x *CastNFTRequest) GetArtWork() *NFT {
	if x != nil {
		return x.ArtWork
	}
	return nil
}

func (x *CastNFTRequest) GetFonContract() *FonContract {
	if x != nil {
		return x.FonContract
	}
	return nil
}

type CastNFTResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Hash string `protobuf:"bytes,1,opt,name=Hash,json=hash,proto3" json:"Hash,omitempty"`
}

func (x *CastNFTResponse) Reset() {
	*x = CastNFTResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_chain_chain_proto_msgTypes[24]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CastNFTResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CastNFTResponse) ProtoMessage() {}

func (x *CastNFTResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_chain_chain_proto_msgTypes[24]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CastNFTResponse.ProtoReflect.Descriptor instead.
func (*CastNFTResponse) Descriptor() ([]byte, []int) {
	return file_api_chain_chain_proto_rawDescGZIP(), []int{24}
}

func (x *CastNFTResponse) GetHash() string {
	if x != nil {
		return x.Hash
	}
	return ""
}

type FonContract struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Node         string `protobuf:"bytes,1,opt,name=Node,proto3" json:"Node,omitempty"`
	ContractType string `protobuf:"bytes,2,opt,name=ContractType,proto3" json:"ContractType,omitempty"`
	ContractName string `protobuf:"bytes,3,opt,name=ContractName,proto3" json:"ContractName,omitempty"`
}

func (x *FonContract) Reset() {
	*x = FonContract{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_chain_chain_proto_msgTypes[25]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FonContract) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FonContract) ProtoMessage() {}

func (x *FonContract) ProtoReflect() protoreflect.Message {
	mi := &file_api_chain_chain_proto_msgTypes[25]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FonContract.ProtoReflect.Descriptor instead.
func (*FonContract) Descriptor() ([]byte, []int) {
	return file_api_chain_chain_proto_rawDescGZIP(), []int{25}
}

func (x *FonContract) GetNode() string {
	if x != nil {
		return x.Node
	}
	return ""
}

func (x *FonContract) GetContractType() string {
	if x != nil {
		return x.ContractType
	}
	return ""
}

func (x *FonContract) GetContractName() string {
	if x != nil {
		return x.ContractName
	}
	return ""
}

type NFTListRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Address     string       `protobuf:"bytes,1,opt,name=Address,proto3" json:"Address,omitempty"`
	FonContract *FonContract `protobuf:"bytes,2,opt,name=FonContract,proto3" json:"FonContract,omitempty"`
}

func (x *NFTListRequest) Reset() {
	*x = NFTListRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_chain_chain_proto_msgTypes[26]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *NFTListRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NFTListRequest) ProtoMessage() {}

func (x *NFTListRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_chain_chain_proto_msgTypes[26]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NFTListRequest.ProtoReflect.Descriptor instead.
func (*NFTListRequest) Descriptor() ([]byte, []int) {
	return file_api_chain_chain_proto_rawDescGZIP(), []int{26}
}

func (x *NFTListRequest) GetAddress() string {
	if x != nil {
		return x.Address
	}
	return ""
}

func (x *NFTListRequest) GetFonContract() *FonContract {
	if x != nil {
		return x.FonContract
	}
	return nil
}

type TxReceipt struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	SerialNum       string `protobuf:"bytes,1,opt,name=SerialNum,json=serialNum,proto3" json:"SerialNum,omitempty"`
	PaymentPlatform string `protobuf:"bytes,2,opt,name=PaymentPlatform,json=paymentPlatform,proto3" json:"PaymentPlatform,omitempty"`
	Price           uint64 `protobuf:"varint,3,opt,name=Price,json=price,proto3" json:"Price,omitempty"`
}

func (x *TxReceipt) Reset() {
	*x = TxReceipt{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_chain_chain_proto_msgTypes[27]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TxReceipt) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TxReceipt) ProtoMessage() {}

func (x *TxReceipt) ProtoReflect() protoreflect.Message {
	mi := &file_api_chain_chain_proto_msgTypes[27]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TxReceipt.ProtoReflect.Descriptor instead.
func (*TxReceipt) Descriptor() ([]byte, []int) {
	return file_api_chain_chain_proto_rawDescGZIP(), []int{27}
}

func (x *TxReceipt) GetSerialNum() string {
	if x != nil {
		return x.SerialNum
	}
	return ""
}

func (x *TxReceipt) GetPaymentPlatform() string {
	if x != nil {
		return x.PaymentPlatform
	}
	return ""
}

func (x *TxReceipt) GetPrice() uint64 {
	if x != nil {
		return x.Price
	}
	return 0
}

type NFT struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	DciID             string       `protobuf:"bytes,1,opt,name=DciID,json=dciid,proto3" json:"DciID,omitempty"`                                      //作品ID
	DciHash           string       `protobuf:"bytes,2,opt,name=DciHash,json=dciHash,proto3" json:"DciHash,omitempty"`                                //作品hash
	DciName           string       `protobuf:"bytes,3,opt,name=DciName,json=dciName,proto3" json:"DciName,omitempty"`                                //作品名
	DciMaker          string       `protobuf:"bytes,4,opt,name=DciMaker,json=dciMaker,proto3" json:"DciMaker,omitempty"`                             //作者
	DciUrl            string       `protobuf:"bytes,5,opt,name=DciUrl,json=dciUrl,proto3" json:"DciUrl,omitempty"`                                   //作品图片路径
	DciOwner          string       `protobuf:"bytes,6,opt,name=DciOwner,json=dciOwner,proto3" json:"DciOwner,omitempty"`                             //作品拥有者
	DciType           int64        `protobuf:"varint,7,opt,name=DciType,json=dciType,proto3" json:"DciType,omitempty"`                               //作品类型
	DciCreateProperty int64        `protobuf:"varint,8,opt,name=DciCreateProperty,json=dciCreateProperty,proto3" json:"DciCreateProperty,omitempty"` //作品属性
	DciFrom           []string     `protobuf:"bytes,9,rep,name=DciFrom,json=dciFrom,proto3" json:"DciFrom,omitempty"`                                //基于那个作品的衍生品
	DciAttribute      int64        `protobuf:"varint,10,opt,name=DciAttribute,json=dciAttribute,proto3" json:"DciAttribute,omitempty"`               //作品属性 0-版权，1-物权
	LastTransaction   string       `protobuf:"bytes,11,opt,name=LastTransaction,proto3" json:"LastTransaction,omitempty"`                            //最后一笔交易
	AverageTxPrice    uint64       `protobuf:"varint,12,opt,name=AverageTxPrice,proto3" json:"AverageTxPrice,omitempty"`                             //成交均价
	CurTxPrice        uint64       `protobuf:"varint,13,opt,name=CurTxPrice,json=curTxPrice,proto3" json:"CurTxPrice,omitempty"`                     //当前标价
	TReceipt          []*TxReceipt `protobuf:"bytes,14,rep,name=TReceipt,proto3" json:"TReceipt,omitempty"`                                          //链下付款凭证&&成交价格
	ContractNum       string       `protobuf:"bytes,15,opt,name=ContractNum,json=contractNum,proto3" json:"ContractNum,omitempty"`                   //合同编号
	CertificateNum    string       `protobuf:"bytes,16,opt,name=CertificateNum,json=certificateNum,proto3" json:"CertificateNum,omitempty"`          //著作权登记编号
	Introduction      string       `protobuf:"bytes,17,opt,name=Introduction,json=introduction,proto3" json:"Introduction,omitempty"`
	Length            int64        `protobuf:"varint,18,opt,name=Length,json=length,proto3" json:"Length,omitempty"`
	Width             int64        `protobuf:"varint,19,opt,name=Width,json=width,proto3" json:"Width,omitempty"`
	Url               string       `protobuf:"bytes,20,opt,name=Url,json=url,proto3" json:"Url,omitempty"`
	CollectionName    string       `protobuf:"bytes,21,opt,name=CollectionName,json=collectionName,proto3" json:"CollectionName,omitempty"` //合辑名
	FolderImg         string       `protobuf:"bytes,22,opt,name=FolderImg,json=folderImg,proto3" json:"FolderImg,omitempty"`                //文件夹图片
	PriceJson         string       `protobuf:"bytes,23,opt,name=PriceJson,json=priceJson,proto3" json:"PriceJson,omitempty"`
}

func (x *NFT) Reset() {
	*x = NFT{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_chain_chain_proto_msgTypes[28]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *NFT) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NFT) ProtoMessage() {}

func (x *NFT) ProtoReflect() protoreflect.Message {
	mi := &file_api_chain_chain_proto_msgTypes[28]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NFT.ProtoReflect.Descriptor instead.
func (*NFT) Descriptor() ([]byte, []int) {
	return file_api_chain_chain_proto_rawDescGZIP(), []int{28}
}

func (x *NFT) GetDciID() string {
	if x != nil {
		return x.DciID
	}
	return ""
}

func (x *NFT) GetDciHash() string {
	if x != nil {
		return x.DciHash
	}
	return ""
}

func (x *NFT) GetDciName() string {
	if x != nil {
		return x.DciName
	}
	return ""
}

func (x *NFT) GetDciMaker() string {
	if x != nil {
		return x.DciMaker
	}
	return ""
}

func (x *NFT) GetDciUrl() string {
	if x != nil {
		return x.DciUrl
	}
	return ""
}

func (x *NFT) GetDciOwner() string {
	if x != nil {
		return x.DciOwner
	}
	return ""
}

func (x *NFT) GetDciType() int64 {
	if x != nil {
		return x.DciType
	}
	return 0
}

func (x *NFT) GetDciCreateProperty() int64 {
	if x != nil {
		return x.DciCreateProperty
	}
	return 0
}

func (x *NFT) GetDciFrom() []string {
	if x != nil {
		return x.DciFrom
	}
	return nil
}

func (x *NFT) GetDciAttribute() int64 {
	if x != nil {
		return x.DciAttribute
	}
	return 0
}

func (x *NFT) GetLastTransaction() string {
	if x != nil {
		return x.LastTransaction
	}
	return ""
}

func (x *NFT) GetAverageTxPrice() uint64 {
	if x != nil {
		return x.AverageTxPrice
	}
	return 0
}

func (x *NFT) GetCurTxPrice() uint64 {
	if x != nil {
		return x.CurTxPrice
	}
	return 0
}

func (x *NFT) GetTReceipt() []*TxReceipt {
	if x != nil {
		return x.TReceipt
	}
	return nil
}

func (x *NFT) GetContractNum() string {
	if x != nil {
		return x.ContractNum
	}
	return ""
}

func (x *NFT) GetCertificateNum() string {
	if x != nil {
		return x.CertificateNum
	}
	return ""
}

func (x *NFT) GetIntroduction() string {
	if x != nil {
		return x.Introduction
	}
	return ""
}

func (x *NFT) GetLength() int64 {
	if x != nil {
		return x.Length
	}
	return 0
}

func (x *NFT) GetWidth() int64 {
	if x != nil {
		return x.Width
	}
	return 0
}

func (x *NFT) GetUrl() string {
	if x != nil {
		return x.Url
	}
	return ""
}

func (x *NFT) GetCollectionName() string {
	if x != nil {
		return x.CollectionName
	}
	return ""
}

func (x *NFT) GetFolderImg() string {
	if x != nil {
		return x.FolderImg
	}
	return ""
}

func (x *NFT) GetPriceJson() string {
	if x != nil {
		return x.PriceJson
	}
	return ""
}

type NFTListResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Data []*NFT `protobuf:"bytes,1,rep,name=data,proto3" json:"data,omitempty"`
}

func (x *NFTListResponse) Reset() {
	*x = NFTListResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_chain_chain_proto_msgTypes[29]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *NFTListResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NFTListResponse) ProtoMessage() {}

func (x *NFTListResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_chain_chain_proto_msgTypes[29]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NFTListResponse.ProtoReflect.Descriptor instead.
func (*NFTListResponse) Descriptor() ([]byte, []int) {
	return file_api_chain_chain_proto_rawDescGZIP(), []int{29}
}

func (x *NFTListResponse) GetData() []*NFT {
	if x != nil {
		return x.Data
	}
	return nil
}

type BurnNFTRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Address     string       `protobuf:"bytes,1,opt,name=Address,proto3" json:"Address,omitempty"`
	Hash        string       `protobuf:"bytes,2,opt,name=Hash,proto3" json:"Hash,omitempty"`
	Mnemonic    string       `protobuf:"bytes,3,opt,name=Mnemonic,proto3" json:"Mnemonic,omitempty"`
	FonContract *FonContract `protobuf:"bytes,4,opt,name=FonContract,proto3" json:"FonContract,omitempty"`
}

func (x *BurnNFTRequest) Reset() {
	*x = BurnNFTRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_chain_chain_proto_msgTypes[30]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BurnNFTRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BurnNFTRequest) ProtoMessage() {}

func (x *BurnNFTRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_chain_chain_proto_msgTypes[30]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BurnNFTRequest.ProtoReflect.Descriptor instead.
func (*BurnNFTRequest) Descriptor() ([]byte, []int) {
	return file_api_chain_chain_proto_rawDescGZIP(), []int{30}
}

func (x *BurnNFTRequest) GetAddress() string {
	if x != nil {
		return x.Address
	}
	return ""
}

func (x *BurnNFTRequest) GetHash() string {
	if x != nil {
		return x.Hash
	}
	return ""
}

func (x *BurnNFTRequest) GetMnemonic() string {
	if x != nil {
		return x.Mnemonic
	}
	return ""
}

func (x *BurnNFTRequest) GetFonContract() *FonContract {
	if x != nil {
		return x.FonContract
	}
	return nil
}

type BurnNFTResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *BurnNFTResponse) Reset() {
	*x = BurnNFTResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_chain_chain_proto_msgTypes[31]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BurnNFTResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BurnNFTResponse) ProtoMessage() {}

func (x *BurnNFTResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_chain_chain_proto_msgTypes[31]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BurnNFTResponse.ProtoReflect.Descriptor instead.
func (*BurnNFTResponse) Descriptor() ([]byte, []int) {
	return file_api_chain_chain_proto_rawDescGZIP(), []int{31}
}

type TransferRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	BuyAddress  string       `protobuf:"bytes,1,opt,name=BuyAddress,proto3" json:"BuyAddress,omitempty"`
	Seller      *Seller      `protobuf:"bytes,2,opt,name=seller,proto3" json:"seller,omitempty"`
	Hash        string       `protobuf:"bytes,3,opt,name=Hash,proto3" json:"Hash,omitempty"`
	FonContract *FonContract `protobuf:"bytes,4,opt,name=FonContract,proto3" json:"FonContract,omitempty"`
	ContractNum string       `protobuf:"bytes,6,opt,name=ContractNum,proto3" json:"ContractNum,omitempty"`
	Platform    int64        `protobuf:"varint,7,opt,name=Platform,proto3" json:"Platform,omitempty"`
	SerialNum   string       `protobuf:"bytes,8,opt,name=SerialNum,proto3" json:"SerialNum,omitempty"`
	BuyMnemonic string       `protobuf:"bytes,9,opt,name=BuyMnemonic,proto3" json:"BuyMnemonic,omitempty"`
}

func (x *TransferRequest) Reset() {
	*x = TransferRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_chain_chain_proto_msgTypes[32]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TransferRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TransferRequest) ProtoMessage() {}

func (x *TransferRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_chain_chain_proto_msgTypes[32]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TransferRequest.ProtoReflect.Descriptor instead.
func (*TransferRequest) Descriptor() ([]byte, []int) {
	return file_api_chain_chain_proto_rawDescGZIP(), []int{32}
}

func (x *TransferRequest) GetBuyAddress() string {
	if x != nil {
		return x.BuyAddress
	}
	return ""
}

func (x *TransferRequest) GetSeller() *Seller {
	if x != nil {
		return x.Seller
	}
	return nil
}

func (x *TransferRequest) GetHash() string {
	if x != nil {
		return x.Hash
	}
	return ""
}

func (x *TransferRequest) GetFonContract() *FonContract {
	if x != nil {
		return x.FonContract
	}
	return nil
}

func (x *TransferRequest) GetContractNum() string {
	if x != nil {
		return x.ContractNum
	}
	return ""
}

func (x *TransferRequest) GetPlatform() int64 {
	if x != nil {
		return x.Platform
	}
	return 0
}

func (x *TransferRequest) GetSerialNum() string {
	if x != nil {
		return x.SerialNum
	}
	return ""
}

func (x *TransferRequest) GetBuyMnemonic() string {
	if x != nil {
		return x.BuyMnemonic
	}
	return ""
}

type TransferResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *TransferResponse) Reset() {
	*x = TransferResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_chain_chain_proto_msgTypes[33]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TransferResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TransferResponse) ProtoMessage() {}

func (x *TransferResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_chain_chain_proto_msgTypes[33]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TransferResponse.ProtoReflect.Descriptor instead.
func (*TransferResponse) Descriptor() ([]byte, []int) {
	return file_api_chain_chain_proto_rawDescGZIP(), []int{33}
}

type ECertRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *ECertRequest) Reset() {
	*x = ECertRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_chain_chain_proto_msgTypes[34]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ECertRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ECertRequest) ProtoMessage() {}

func (x *ECertRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_chain_chain_proto_msgTypes[34]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ECertRequest.ProtoReflect.Descriptor instead.
func (*ECertRequest) Descriptor() ([]byte, []int) {
	return file_api_chain_chain_proto_rawDescGZIP(), []int{34}
}

type ECertResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *ECertResponse) Reset() {
	*x = ECertResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_chain_chain_proto_msgTypes[35]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ECertResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ECertResponse) ProtoMessage() {}

func (x *ECertResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_chain_chain_proto_msgTypes[35]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ECertResponse.ProtoReflect.Descriptor instead.
func (*ECertResponse) Descriptor() ([]byte, []int) {
	return file_api_chain_chain_proto_rawDescGZIP(), []int{35}
}

var File_api_chain_chain_proto protoreflect.FileDescriptor

var file_api_chain_chain_proto_rawDesc = []byte{
	0x0a, 0x15, 0x61, 0x70, 0x69, 0x2f, 0x63, 0x68, 0x61, 0x69, 0x6e, 0x2f, 0x63, 0x68, 0x61, 0x69,
	0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x05, 0x63, 0x68, 0x61, 0x69, 0x6e, 0x1a, 0x3d,
	0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x6d, 0x77, 0x69, 0x74, 0x6b,
	0x6f, 0x77, 0x2f, 0x67, 0x6f, 0x2d, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2d, 0x76, 0x61, 0x6c, 0x69,
	0x64, 0x61, 0x74, 0x6f, 0x72, 0x73, 0x40, 0x76, 0x30, 0x2e, 0x33, 0x2e, 0x32, 0x2f, 0x76, 0x61,
	0x6c, 0x69, 0x64, 0x61, 0x74, 0x6f, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x1a, 0x0a,
	0x18, 0x42, 0x61, 0x69, 0x64, 0x75, 0x49, 0x6e, 0x66, 0x6f, 0x42, 0x79, 0x54, 0x78, 0x74, 0x49,
	0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x39, 0x0a, 0x0d, 0x47, 0x65, 0x74,
	0x4e, 0x46, 0x54, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x14, 0x0a, 0x05, 0x44, 0x63,
	0x69, 0x49, 0x44, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x64, 0x63, 0x69, 0x49, 0x44,
	0x12, 0x12, 0x0a, 0x04, 0x48, 0x61, 0x73, 0x68, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04,
	0x68, 0x61, 0x73, 0x68, 0x22, 0x23, 0x0a, 0x0b, 0x4e, 0x66, 0x74, 0x48, 0x61, 0x73, 0x68, 0x4c,
	0x69, 0x73, 0x74, 0x12, 0x14, 0x0a, 0x05, 0x48, 0x61, 0x73, 0x68, 0x73, 0x18, 0x02, 0x20, 0x03,
	0x28, 0x09, 0x52, 0x05, 0x68, 0x61, 0x73, 0x68, 0x73, 0x22, 0x33, 0x0a, 0x13, 0x42, 0x61, 0x69,
	0x64, 0x75, 0x52, 0x65, 0x71, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x12, 0x1c, 0x0a, 0x09, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x49, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x09, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x49, 0x64, 0x22, 0x2f,
	0x0a, 0x17, 0x42, 0x61, 0x69, 0x64, 0x75, 0x49, 0x6e, 0x66, 0x6f, 0x42, 0x79, 0x54, 0x78, 0x74,
	0x49, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x78, 0x74,
	0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x74, 0x78, 0x74, 0x49, 0x64, 0x22,
	0x96, 0x01, 0x0a, 0x14, 0x42, 0x61, 0x69, 0x64, 0x75, 0x52, 0x65, 0x71, 0x49, 0x6e, 0x66, 0x6f,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x54, 0x78, 0x49, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x54, 0x78, 0x49, 0x64, 0x12, 0x18, 0x0a, 0x07,
	0x42, 0x6c, 0x6f, 0x63, 0x6b, 0x49, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x42,
	0x6c, 0x6f, 0x63, 0x6b, 0x49, 0x64, 0x12, 0x18, 0x0a, 0x07, 0x43, 0x65, 0x72, 0x74, 0x55, 0x72,
	0x6c, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x43, 0x65, 0x72, 0x74, 0x55, 0x72, 0x6c,
	0x12, 0x1e, 0x0a, 0x0a, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65,
	0x12, 0x16, 0x0a, 0x06, 0x48, 0x65, 0x69, 0x67, 0x68, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x04,
	0x52, 0x06, 0x48, 0x65, 0x69, 0x67, 0x68, 0x74, 0x22, 0x16, 0x0a, 0x14, 0x43, 0x72, 0x65, 0x61,
	0x74, 0x65, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x22, 0x66, 0x0a, 0x12, 0x49, 0x6e, 0x69, 0x74, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1a, 0x0a, 0x08, 0x4d, 0x6e, 0x65, 0x6d, 0x6f, 0x6e,
	0x69, 0x63, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x4d, 0x6e, 0x65, 0x6d, 0x6f, 0x6e,
	0x69, 0x63, 0x12, 0x34, 0x0a, 0x0b, 0x46, 0x6f, 0x6e, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x61, 0x63,
	0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x63, 0x68, 0x61, 0x69, 0x6e, 0x2e,
	0x46, 0x6f, 0x6e, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x61, 0x63, 0x74, 0x52, 0x0b, 0x46, 0x6f, 0x6e,
	0x43, 0x6f, 0x6e, 0x74, 0x72, 0x61, 0x63, 0x74, 0x22, 0x4b, 0x0a, 0x13, 0x49, 0x6e, 0x69, 0x74,
	0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12,
	0x1a, 0x0a, 0x08, 0x4d, 0x6e, 0x65, 0x6d, 0x6f, 0x6e, 0x69, 0x63, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x08, 0x4d, 0x6e, 0x65, 0x6d, 0x6f, 0x6e, 0x69, 0x63, 0x12, 0x18, 0x0a, 0x07, 0x41,
	0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x41, 0x64,
	0x64, 0x72, 0x65, 0x73, 0x73, 0x22, 0x9e, 0x01, 0x0a, 0x0b, 0x4c, 0x6f, 0x63, 0x6b, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1e, 0x0a, 0x0a, 0x42, 0x75, 0x79, 0x41, 0x64, 0x64, 0x72,
	0x65, 0x73, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x42, 0x75, 0x79, 0x41, 0x64,
	0x64, 0x72, 0x65, 0x73, 0x73, 0x12, 0x25, 0x0a, 0x06, 0x53, 0x65, 0x6c, 0x6c, 0x65, 0x72, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0d, 0x2e, 0x63, 0x68, 0x61, 0x69, 0x6e, 0x2e, 0x53, 0x65,
	0x6c, 0x6c, 0x65, 0x72, 0x52, 0x06, 0x53, 0x65, 0x6c, 0x6c, 0x65, 0x72, 0x12, 0x12, 0x0a, 0x04,
	0x48, 0x61, 0x73, 0x68, 0x18, 0x03, 0x20, 0x03, 0x28, 0x09, 0x52, 0x04, 0x48, 0x61, 0x73, 0x68,
	0x12, 0x34, 0x0a, 0x0b, 0x46, 0x6f, 0x6e, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x61, 0x63, 0x74, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x63, 0x68, 0x61, 0x69, 0x6e, 0x2e, 0x46, 0x6f,
	0x6e, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x61, 0x63, 0x74, 0x52, 0x0b, 0x46, 0x6f, 0x6e, 0x43, 0x6f,
	0x6e, 0x74, 0x72, 0x61, 0x63, 0x74, 0x22, 0x3e, 0x0a, 0x06, 0x53, 0x65, 0x6c, 0x6c, 0x65, 0x72,
	0x12, 0x18, 0x0a, 0x07, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x07, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x12, 0x1a, 0x0a, 0x08, 0x4d, 0x6e,
	0x65, 0x6d, 0x6f, 0x6e, 0x69, 0x63, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x4d, 0x6e,
	0x65, 0x6d, 0x6f, 0x6e, 0x69, 0x63, 0x22, 0x0e, 0x0a, 0x0c, 0x4c, 0x6f, 0x63, 0x6b, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x10, 0x0a, 0x0e, 0x55, 0x6e, 0x4c, 0x6f, 0x63, 0x6b,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x96, 0x02, 0x0a, 0x10, 0x49, 0x64, 0x65,
	0x6e, 0x74, 0x41, 0x72, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x24, 0x0a,
	0x0d, 0x41, 0x72, 0x74, 0x69, 0x73, 0x63, 0x74, 0x69, 0x63, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x41, 0x72, 0x74, 0x69, 0x73, 0x63, 0x74, 0x69, 0x63, 0x4e,
	0x61, 0x6d, 0x65, 0x12, 0x1e, 0x0a, 0x0a, 0x41, 0x72, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x4e, 0x75,
	0x6d, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x41, 0x72, 0x74, 0x77, 0x6f, 0x72, 0x6b,
	0x4e, 0x75, 0x6d, 0x12, 0x16, 0x0a, 0x06, 0x41, 0x75, 0x74, 0x68, 0x6f, 0x72, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x06, 0x41, 0x75, 0x74, 0x68, 0x6f, 0x72, 0x12, 0x24, 0x0a, 0x0d, 0x41,
	0x67, 0x65, 0x4f, 0x66, 0x43, 0x72, 0x65, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x04, 0x52, 0x0d, 0x41, 0x67, 0x65, 0x4f, 0x66, 0x43, 0x72, 0x65, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x12, 0x22, 0x0a, 0x0c, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73,
	0x73, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x41, 0x64,
	0x64, 0x72, 0x65, 0x73, 0x73, 0x12, 0x2c, 0x0a, 0x11, 0x54, 0x65, 0x78, 0x74, 0x75, 0x72, 0x65,
	0x4f, 0x66, 0x4d, 0x61, 0x74, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x11, 0x54, 0x65, 0x78, 0x74, 0x75, 0x72, 0x65, 0x4f, 0x66, 0x4d, 0x61, 0x74, 0x65, 0x72,
	0x69, 0x61, 0x6c, 0x12, 0x14, 0x0a, 0x05, 0x57, 0x69, 0x64, 0x74, 0x68, 0x18, 0x07, 0x20, 0x01,
	0x28, 0x04, 0x52, 0x05, 0x57, 0x69, 0x64, 0x74, 0x68, 0x12, 0x16, 0x0a, 0x06, 0x4c, 0x65, 0x6e,
	0x67, 0x74, 0x68, 0x18, 0x08, 0x20, 0x01, 0x28, 0x04, 0x52, 0x06, 0x4c, 0x65, 0x6e, 0x67, 0x74,
	0x68, 0x22, 0xbc, 0x02, 0x0a, 0x0e, 0x49, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x12, 0x10, 0x0a, 0x03, 0x55, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x03, 0x55, 0x69, 0x64, 0x12, 0x49, 0x0a, 0x13, 0x49, 0x64, 0x65, 0x6e, 0x74, 0x41,
	0x72, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x49, 0x6e, 0x66, 0x6f, 0x50, 0x72, 0x6f, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x63, 0x68, 0x61, 0x69, 0x6e, 0x2e, 0x49, 0x64, 0x65, 0x6e,
	0x74, 0x41, 0x72, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x13, 0x49, 0x64,
	0x65, 0x6e, 0x74, 0x41, 0x72, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x49, 0x6e, 0x66, 0x6f, 0x50, 0x72,
	0x6f, 0x12, 0x25, 0x0a, 0x06, 0x41, 0x72, 0x74, 0x69, 0x73, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x0d, 0x2e, 0x63, 0x68, 0x61, 0x69, 0x6e, 0x2e, 0x41, 0x72, 0x74, 0x69, 0x73, 0x74,
	0x52, 0x06, 0x41, 0x72, 0x74, 0x69, 0x73, 0x74, 0x12, 0x32, 0x0a, 0x14, 0x41, 0x72, 0x74, 0x69,
	0x73, 0x74, 0x42, 0x61, 0x69, 0x64, 0x75, 0x63, 0x68, 0x61, 0x69, 0x6e, 0x48, 0x61, 0x73, 0x68,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x14, 0x41, 0x72, 0x74, 0x69, 0x73, 0x74, 0x42, 0x61,
	0x69, 0x64, 0x75, 0x63, 0x68, 0x61, 0x69, 0x6e, 0x48, 0x61, 0x73, 0x68, 0x12, 0x32, 0x0a, 0x14,
	0x42, 0x61, 0x69, 0x64, 0x75, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e,
	0x48, 0x61, 0x73, 0x68, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x14, 0x42, 0x61, 0x69, 0x64,
	0x75, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x48, 0x61, 0x73, 0x68,
	0x12, 0x1e, 0x0a, 0x0a, 0x57, 0x69, 0x6e, 0x64, 0x75, 0x70, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x06,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x57, 0x69, 0x6e, 0x64, 0x75, 0x70, 0x54, 0x69, 0x6d, 0x65,
	0x12, 0x1e, 0x0a, 0x0a, 0x50, 0x69, 0x63, 0x32, 0x30, 0x30, 0x30, 0x53, 0x72, 0x63, 0x18, 0x07,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x50, 0x69, 0x63, 0x32, 0x30, 0x30, 0x30, 0x53, 0x72, 0x63,
	0x22, 0x4a, 0x0a, 0x06, 0x41, 0x72, 0x74, 0x69, 0x73, 0x74, 0x12, 0x10, 0x0a, 0x03, 0x55, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x55, 0x69, 0x64, 0x12, 0x14, 0x0a, 0x05,
	0x50, 0x68, 0x6f, 0x74, 0x6f, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x50, 0x68, 0x6f,
	0x74, 0x6f, 0x12, 0x18, 0x0a, 0x07, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x07, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x22, 0x26, 0x0a, 0x12,
	0x55, 0x70, 0x6c, 0x6f, 0x61, 0x64, 0x46, 0x69, 0x6c, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x53, 0x72, 0x63, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x03, 0x53, 0x72, 0x63, 0x22, 0x40, 0x0a, 0x0e, 0x43, 0x6f, 0x43, 0x68, 0x61, 0x69, 0x6e, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x16, 0x0a, 0x06, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x16,
	0x0a, 0x06, 0x55, 0x73, 0x65, 0x72, 0x49, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06,
	0x55, 0x73, 0x65, 0x72, 0x49, 0x64, 0x22, 0x49, 0x0a, 0x13, 0x43, 0x6f, 0x43, 0x68, 0x65, 0x6e,
	0x67, 0x43, 0x68, 0x61, 0x69, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x12, 0x0a,
	0x04, 0x44, 0x61, 0x74, 0x61, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x44, 0x61, 0x74,
	0x61, 0x12, 0x1e, 0x0a, 0x0a, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x49, 0x64, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x49,
	0x64, 0x22, 0xb7, 0x02, 0x0a, 0x08, 0x42, 0x61, 0x69, 0x64, 0x75, 0x52, 0x65, 0x71, 0x12, 0x1a,
	0x0a, 0x08, 0x46, 0x69, 0x6c, 0x65, 0x48, 0x61, 0x73, 0x68, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x08, 0x66, 0x69, 0x6c, 0x65, 0x48, 0x61, 0x73, 0x68, 0x12, 0x1a, 0x0a, 0x08, 0x46, 0x69,
	0x6c, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x66, 0x69,
	0x6c, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e,
	0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74,
	0x12, 0x1a, 0x0a, 0x08, 0x55, 0x73, 0x65, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x08, 0x75, 0x73, 0x65, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1e, 0x0a, 0x0a,
	0x55, 0x70, 0x6c, 0x6f, 0x61, 0x64, 0x44, 0x61, 0x74, 0x61, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0a, 0x75, 0x70, 0x6c, 0x6f, 0x61, 0x64, 0x44, 0x61, 0x74, 0x61, 0x12, 0x18, 0x0a, 0x07,
	0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61,
	0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x12, 0x22, 0x0a, 0x0c, 0x45, 0x76, 0x69, 0x64, 0x65, 0x6e,
	0x63, 0x65, 0x54, 0x79, 0x70, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x45, 0x76,
	0x69, 0x64, 0x65, 0x6e, 0x63, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x43, 0x61,
	0x6c, 0x6c, 0x62, 0x61, 0x63, 0x6b, 0x55, 0x72, 0x6c, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0b, 0x63, 0x61, 0x6c, 0x6c, 0x62, 0x61, 0x63, 0x6b, 0x55, 0x72, 0x6c, 0x12, 0x14, 0x0a, 0x05,
	0x41, 0x70, 0x70, 0x49, 0x64, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x61, 0x70, 0x70,
	0x49, 0x64, 0x12, 0x27, 0x0a, 0x0e, 0x43, 0x61, 0x6c, 0x6c, 0x62, 0x61, 0x63, 0x6b, 0x43, 0x6f,
	0x6e, 0x66, 0x69, 0x67, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x63, 0x61, 0x6c, 0x6c,
	0x62, 0x61, 0x63, 0x6b, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x22, 0x60, 0x0a, 0x14, 0x43,
	0x6f, 0x43, 0x68, 0x65, 0x6e, 0x67, 0x43, 0x68, 0x61, 0x69, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x54, 0x78, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x04, 0x54, 0x78, 0x49, 0x64, 0x12, 0x34, 0x0a, 0x15, 0x43, 0x6f, 0x6e, 0x66, 0x69,
	0x72, 0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4c, 0x65, 0x74, 0x74, 0x65, 0x72, 0x55, 0x72, 0x6c,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x15, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x72, 0x6d, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x4c, 0x65, 0x74, 0x74, 0x65, 0x72, 0x55, 0x72, 0x6c, 0x22, 0x34, 0x0a,
	0x14, 0x43, 0x6f, 0x42, 0x61, 0x69, 0x64, 0x75, 0x43, 0x68, 0x61, 0x69, 0x6e, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x1c, 0x0a, 0x09, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x49, 0x64, 0x22, 0x29, 0x0a, 0x0f, 0x43, 0x6f, 0x43, 0x68, 0x61, 0x69, 0x6e, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x46, 0x69, 0x67, 0x75, 0x72, 0x65,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x46, 0x69, 0x67, 0x75, 0x72, 0x65, 0x22, 0x68,
	0x0a, 0x0e, 0x43, 0x61, 0x73, 0x74, 0x4e, 0x46, 0x54, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x12, 0x20, 0x0a, 0x07, 0x41, 0x72, 0x74, 0x57, 0x6f, 0x72, 0x6b, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x0a, 0x2e, 0x63, 0x68, 0x61, 0x69, 0x6e, 0x2e, 0x4e, 0x46, 0x54, 0x52, 0x03, 0x6e,
	0x66, 0x74, 0x12, 0x34, 0x0a, 0x0b, 0x46, 0x6f, 0x6e, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x61, 0x63,
	0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x63, 0x68, 0x61, 0x69, 0x6e, 0x2e,
	0x46, 0x6f, 0x6e, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x61, 0x63, 0x74, 0x52, 0x0b, 0x46, 0x6f, 0x6e,
	0x43, 0x6f, 0x6e, 0x74, 0x72, 0x61, 0x63, 0x74, 0x22, 0x25, 0x0a, 0x0f, 0x43, 0x61, 0x73, 0x74,
	0x4e, 0x46, 0x54, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x48,
	0x61, 0x73, 0x68, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x68, 0x61, 0x73, 0x68, 0x22,
	0x69, 0x0a, 0x0b, 0x46, 0x6f, 0x6e, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x61, 0x63, 0x74, 0x12, 0x12,
	0x0a, 0x04, 0x4e, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x4e, 0x6f,
	0x64, 0x65, 0x12, 0x22, 0x0a, 0x0c, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x61, 0x63, 0x74, 0x54, 0x79,
	0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x61,
	0x63, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x22, 0x0a, 0x0c, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x61,
	0x63, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x43, 0x6f,
	0x6e, 0x74, 0x72, 0x61, 0x63, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x22, 0x60, 0x0a, 0x0e, 0x4e, 0x46,
	0x54, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x18, 0x0a, 0x07,
	0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x41,
	0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x12, 0x34, 0x0a, 0x0b, 0x46, 0x6f, 0x6e, 0x43, 0x6f, 0x6e,
	0x74, 0x72, 0x61, 0x63, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x63, 0x68,
	0x61, 0x69, 0x6e, 0x2e, 0x46, 0x6f, 0x6e, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x61, 0x63, 0x74, 0x52,
	0x0b, 0x46, 0x6f, 0x6e, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x61, 0x63, 0x74, 0x22, 0x69, 0x0a, 0x09,
	0x54, 0x78, 0x52, 0x65, 0x63, 0x65, 0x69, 0x70, 0x74, 0x12, 0x1c, 0x0a, 0x09, 0x53, 0x65, 0x72,
	0x69, 0x61, 0x6c, 0x4e, 0x75, 0x6d, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x73, 0x65,
	0x72, 0x69, 0x61, 0x6c, 0x4e, 0x75, 0x6d, 0x12, 0x28, 0x0a, 0x0f, 0x50, 0x61, 0x79, 0x6d, 0x65,
	0x6e, 0x74, 0x50, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0f, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x50, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72,
	0x6d, 0x12, 0x14, 0x0a, 0x05, 0x50, 0x72, 0x69, 0x63, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x04,
	0x52, 0x05, 0x70, 0x72, 0x69, 0x63, 0x65, 0x22, 0xf1, 0x05, 0x0a, 0x03, 0x4e, 0x46, 0x54, 0x12,
	0x14, 0x0a, 0x05, 0x44, 0x63, 0x69, 0x49, 0x44, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05,
	0x64, 0x63, 0x69, 0x69, 0x64, 0x12, 0x32, 0x0a, 0x07, 0x44, 0x63, 0x69, 0x48, 0x61, 0x73, 0x68,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x18, 0xe2, 0xdf, 0x1f, 0x14, 0x2a, 0x10, 0x68, 0x61,
	0x73, 0x68, 0xe5, 0xbf, 0x85, 0xe9, 0xa1, 0xbb, 0xe8, 0xa6, 0x81, 0xe6, 0x9c, 0x89, 0x58, 0x01,
	0x52, 0x07, 0x64, 0x63, 0x69, 0x48, 0x61, 0x73, 0x68, 0x12, 0x18, 0x0a, 0x07, 0x44, 0x63, 0x69,
	0x4e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x64, 0x63, 0x69, 0x4e,
	0x61, 0x6d, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x44, 0x63, 0x69, 0x4d, 0x61, 0x6b, 0x65, 0x72, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x64, 0x63, 0x69, 0x4d, 0x61, 0x6b, 0x65, 0x72, 0x12,
	0x16, 0x0a, 0x06, 0x44, 0x63, 0x69, 0x55, 0x72, 0x6c, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x06, 0x64, 0x63, 0x69, 0x55, 0x72, 0x6c, 0x12, 0x1a, 0x0a, 0x08, 0x44, 0x63, 0x69, 0x4f, 0x77,
	0x6e, 0x65, 0x72, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x64, 0x63, 0x69, 0x4f, 0x77,
	0x6e, 0x65, 0x72, 0x12, 0x18, 0x0a, 0x07, 0x44, 0x63, 0x69, 0x54, 0x79, 0x70, 0x65, 0x18, 0x07,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x07, 0x64, 0x63, 0x69, 0x54, 0x79, 0x70, 0x65, 0x12, 0x2c, 0x0a,
	0x11, 0x44, 0x63, 0x69, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x50, 0x72, 0x6f, 0x70, 0x65, 0x72,
	0x74, 0x79, 0x18, 0x08, 0x20, 0x01, 0x28, 0x03, 0x52, 0x11, 0x64, 0x63, 0x69, 0x43, 0x72, 0x65,
	0x61, 0x74, 0x65, 0x50, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x79, 0x12, 0x18, 0x0a, 0x07, 0x44,
	0x63, 0x69, 0x46, 0x72, 0x6f, 0x6d, 0x18, 0x09, 0x20, 0x03, 0x28, 0x09, 0x52, 0x07, 0x64, 0x63,
	0x69, 0x46, 0x72, 0x6f, 0x6d, 0x12, 0x22, 0x0a, 0x0c, 0x44, 0x63, 0x69, 0x41, 0x74, 0x74, 0x72,
	0x69, 0x62, 0x75, 0x74, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0c, 0x64, 0x63, 0x69,
	0x41, 0x74, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x12, 0x28, 0x0a, 0x0f, 0x4c, 0x61, 0x73,
	0x74, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x0b, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0f, 0x4c, 0x61, 0x73, 0x74, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x12, 0x26, 0x0a, 0x0e, 0x41, 0x76, 0x65, 0x72, 0x61, 0x67, 0x65, 0x54, 0x78,
	0x50, 0x72, 0x69, 0x63, 0x65, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0e, 0x41, 0x76, 0x65,
	0x72, 0x61, 0x67, 0x65, 0x54, 0x78, 0x50, 0x72, 0x69, 0x63, 0x65, 0x12, 0x1e, 0x0a, 0x0a, 0x43,
	0x75, 0x72, 0x54, 0x78, 0x50, 0x72, 0x69, 0x63, 0x65, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x04, 0x52,
	0x0a, 0x63, 0x75, 0x72, 0x54, 0x78, 0x50, 0x72, 0x69, 0x63, 0x65, 0x12, 0x2c, 0x0a, 0x08, 0x54,
	0x52, 0x65, 0x63, 0x65, 0x69, 0x70, 0x74, 0x18, 0x0e, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x10, 0x2e,
	0x63, 0x68, 0x61, 0x69, 0x6e, 0x2e, 0x54, 0x78, 0x52, 0x65, 0x63, 0x65, 0x69, 0x70, 0x74, 0x52,
	0x08, 0x54, 0x52, 0x65, 0x63, 0x65, 0x69, 0x70, 0x74, 0x12, 0x20, 0x0a, 0x0b, 0x43, 0x6f, 0x6e,
	0x74, 0x72, 0x61, 0x63, 0x74, 0x4e, 0x75, 0x6d, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b,
	0x63, 0x6f, 0x6e, 0x74, 0x72, 0x61, 0x63, 0x74, 0x4e, 0x75, 0x6d, 0x12, 0x26, 0x0a, 0x0e, 0x43,
	0x65, 0x72, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x65, 0x4e, 0x75, 0x6d, 0x18, 0x10, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0e, 0x63, 0x65, 0x72, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x65,
	0x4e, 0x75, 0x6d, 0x12, 0x22, 0x0a, 0x0c, 0x49, 0x6e, 0x74, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x18, 0x11, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x69, 0x6e, 0x74, 0x72, 0x6f,
	0x64, 0x75, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x16, 0x0a, 0x06, 0x4c, 0x65, 0x6e, 0x67, 0x74,
	0x68, 0x18, 0x12, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x6c, 0x65, 0x6e, 0x67, 0x74, 0x68, 0x12,
	0x14, 0x0a, 0x05, 0x57, 0x69, 0x64, 0x74, 0x68, 0x18, 0x13, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05,
	0x77, 0x69, 0x64, 0x74, 0x68, 0x12, 0x10, 0x0a, 0x03, 0x55, 0x72, 0x6c, 0x18, 0x14, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x03, 0x75, 0x72, 0x6c, 0x12, 0x26, 0x0a, 0x0e, 0x43, 0x6f, 0x6c, 0x6c, 0x65,
	0x63, 0x74, 0x69, 0x6f, 0x6e, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x15, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0e, 0x63, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x4e, 0x61, 0x6d, 0x65, 0x12,
	0x1c, 0x0a, 0x09, 0x46, 0x6f, 0x6c, 0x64, 0x65, 0x72, 0x49, 0x6d, 0x67, 0x18, 0x16, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x09, 0x66, 0x6f, 0x6c, 0x64, 0x65, 0x72, 0x49, 0x6d, 0x67, 0x12, 0x1c, 0x0a,
	0x09, 0x50, 0x72, 0x69, 0x63, 0x65, 0x4a, 0x73, 0x6f, 0x6e, 0x18, 0x17, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x09, 0x70, 0x72, 0x69, 0x63, 0x65, 0x4a, 0x73, 0x6f, 0x6e, 0x22, 0x31, 0x0a, 0x0f, 0x4e,
	0x46, 0x54, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x1e,
	0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x0a, 0x2e, 0x63,
	0x68, 0x61, 0x69, 0x6e, 0x2e, 0x4e, 0x46, 0x54, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x22, 0x90,
	0x01, 0x0a, 0x0e, 0x42, 0x75, 0x72, 0x6e, 0x4e, 0x46, 0x54, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x12, 0x18, 0x0a, 0x07, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x07, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x12, 0x12, 0x0a, 0x04, 0x48,
	0x61, 0x73, 0x68, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x48, 0x61, 0x73, 0x68, 0x12,
	0x1a, 0x0a, 0x08, 0x4d, 0x6e, 0x65, 0x6d, 0x6f, 0x6e, 0x69, 0x63, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x08, 0x4d, 0x6e, 0x65, 0x6d, 0x6f, 0x6e, 0x69, 0x63, 0x12, 0x34, 0x0a, 0x0b, 0x46,
	0x6f, 0x6e, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x61, 0x63, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x12, 0x2e, 0x63, 0x68, 0x61, 0x69, 0x6e, 0x2e, 0x46, 0x6f, 0x6e, 0x43, 0x6f, 0x6e, 0x74,
	0x72, 0x61, 0x63, 0x74, 0x52, 0x0b, 0x46, 0x6f, 0x6e, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x61, 0x63,
	0x74, 0x22, 0x11, 0x0a, 0x0f, 0x42, 0x75, 0x72, 0x6e, 0x4e, 0x46, 0x54, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x22, 0xa0, 0x02, 0x0a, 0x0f, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x66, 0x65,
	0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1e, 0x0a, 0x0a, 0x42, 0x75, 0x79, 0x41,
	0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x42, 0x75,
	0x79, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x12, 0x25, 0x0a, 0x06, 0x73, 0x65, 0x6c, 0x6c,
	0x65, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0d, 0x2e, 0x63, 0x68, 0x61, 0x69, 0x6e,
	0x2e, 0x53, 0x65, 0x6c, 0x6c, 0x65, 0x72, 0x52, 0x06, 0x73, 0x65, 0x6c, 0x6c, 0x65, 0x72, 0x12,
	0x12, 0x0a, 0x04, 0x48, 0x61, 0x73, 0x68, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x48,
	0x61, 0x73, 0x68, 0x12, 0x34, 0x0a, 0x0b, 0x46, 0x6f, 0x6e, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x61,
	0x63, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x63, 0x68, 0x61, 0x69, 0x6e,
	0x2e, 0x46, 0x6f, 0x6e, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x61, 0x63, 0x74, 0x52, 0x0b, 0x46, 0x6f,
	0x6e, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x61, 0x63, 0x74, 0x12, 0x20, 0x0a, 0x0b, 0x43, 0x6f, 0x6e,
	0x74, 0x72, 0x61, 0x63, 0x74, 0x4e, 0x75, 0x6d, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b,
	0x43, 0x6f, 0x6e, 0x74, 0x72, 0x61, 0x63, 0x74, 0x4e, 0x75, 0x6d, 0x12, 0x1a, 0x0a, 0x08, 0x50,
	0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x18, 0x07, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x50,
	0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x12, 0x1c, 0x0a, 0x09, 0x53, 0x65, 0x72, 0x69, 0x61,
	0x6c, 0x4e, 0x75, 0x6d, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x53, 0x65, 0x72, 0x69,
	0x61, 0x6c, 0x4e, 0x75, 0x6d, 0x12, 0x20, 0x0a, 0x0b, 0x42, 0x75, 0x79, 0x4d, 0x6e, 0x65, 0x6d,
	0x6f, 0x6e, 0x69, 0x63, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x42, 0x75, 0x79, 0x4d,
	0x6e, 0x65, 0x6d, 0x6f, 0x6e, 0x69, 0x63, 0x22, 0x12, 0x0a, 0x10, 0x54, 0x72, 0x61, 0x6e, 0x73,
	0x66, 0x65, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x0e, 0x0a, 0x0c, 0x45,
	0x43, 0x65, 0x72, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x22, 0x0f, 0x0a, 0x0d, 0x45,
	0x43, 0x65, 0x72, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x32, 0xe0, 0x09, 0x0a,
	0x05, 0x43, 0x68, 0x61, 0x69, 0x6e, 0x12, 0x3e, 0x0a, 0x0c, 0x43, 0x6f, 0x42, 0x61, 0x69, 0x64,
	0x75, 0x43, 0x68, 0x61, 0x69, 0x6e, 0x12, 0x0f, 0x2e, 0x63, 0x68, 0x61, 0x69, 0x6e, 0x2e, 0x42,
	0x61, 0x69, 0x64, 0x75, 0x52, 0x65, 0x71, 0x1a, 0x1b, 0x2e, 0x63, 0x68, 0x61, 0x69, 0x6e, 0x2e,
	0x43, 0x6f, 0x42, 0x61, 0x69, 0x64, 0x75, 0x43, 0x68, 0x61, 0x69, 0x6e, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x40, 0x0a, 0x0e, 0x43, 0x6f, 0x42, 0x61, 0x69, 0x64,
	0x75, 0x43, 0x65, 0x72, 0x74, 0x69, 0x66, 0x79, 0x12, 0x0f, 0x2e, 0x63, 0x68, 0x61, 0x69, 0x6e,
	0x2e, 0x42, 0x61, 0x69, 0x64, 0x75, 0x52, 0x65, 0x71, 0x1a, 0x1b, 0x2e, 0x63, 0x68, 0x61, 0x69,
	0x6e, 0x2e, 0x43, 0x6f, 0x42, 0x61, 0x69, 0x64, 0x75, 0x43, 0x68, 0x61, 0x69, 0x6e, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x49, 0x0a, 0x0c, 0x42, 0x61, 0x69, 0x64,
	0x75, 0x52, 0x65, 0x71, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x1a, 0x2e, 0x63, 0x68, 0x61, 0x69, 0x6e,
	0x2e, 0x42, 0x61, 0x69, 0x64, 0x75, 0x52, 0x65, 0x71, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x1a, 0x1b, 0x2e, 0x63, 0x68, 0x61, 0x69, 0x6e, 0x2e, 0x42, 0x61, 0x69,
	0x64, 0x75, 0x52, 0x65, 0x71, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x22, 0x00, 0x12, 0x55, 0x0a, 0x10, 0x42, 0x61, 0x69, 0x64, 0x75, 0x49, 0x6e, 0x66, 0x6f,
	0x42, 0x79, 0x54, 0x78, 0x74, 0x49, 0x64, 0x12, 0x1e, 0x2e, 0x63, 0x68, 0x61, 0x69, 0x6e, 0x2e,
	0x42, 0x61, 0x69, 0x64, 0x75, 0x49, 0x6e, 0x66, 0x6f, 0x42, 0x79, 0x54, 0x78, 0x74, 0x49, 0x64,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1f, 0x2e, 0x63, 0x68, 0x61, 0x69, 0x6e, 0x2e,
	0x42, 0x61, 0x69, 0x64, 0x75, 0x49, 0x6e, 0x66, 0x6f, 0x42, 0x79, 0x54, 0x78, 0x74, 0x49, 0x64,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x49, 0x0a, 0x0c, 0x43, 0x6f,
	0x43, 0x68, 0x65, 0x6e, 0x67, 0x43, 0x68, 0x61, 0x69, 0x6e, 0x12, 0x1a, 0x2e, 0x63, 0x68, 0x61,
	0x69, 0x6e, 0x2e, 0x43, 0x6f, 0x43, 0x68, 0x65, 0x6e, 0x67, 0x43, 0x68, 0x61, 0x69, 0x6e, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1b, 0x2e, 0x63, 0x68, 0x61, 0x69, 0x6e, 0x2e, 0x43,
	0x6f, 0x43, 0x68, 0x65, 0x6e, 0x67, 0x43, 0x68, 0x61, 0x69, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x41, 0x0a, 0x09, 0x43, 0x6f, 0x57, 0x74, 0x43, 0x68, 0x61,
	0x69, 0x6e, 0x12, 0x1a, 0x2e, 0x63, 0x68, 0x61, 0x69, 0x6e, 0x2e, 0x43, 0x6f, 0x43, 0x68, 0x65,
	0x6e, 0x67, 0x43, 0x68, 0x61, 0x69, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x16,
	0x2e, 0x63, 0x68, 0x61, 0x69, 0x6e, 0x2e, 0x43, 0x6f, 0x43, 0x68, 0x61, 0x69, 0x6e, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x46, 0x0a, 0x0b, 0x49, 0x6e, 0x69, 0x74,
	0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x19, 0x2e, 0x63, 0x68, 0x61, 0x69, 0x6e, 0x2e,
	0x49, 0x6e, 0x69, 0x74, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x1a, 0x1a, 0x2e, 0x63, 0x68, 0x61, 0x69, 0x6e, 0x2e, 0x49, 0x6e, 0x69, 0x74, 0x41,
	0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00,
	0x12, 0x4a, 0x0a, 0x0d, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x12, 0x1b, 0x2e, 0x63, 0x68, 0x61, 0x69, 0x6e, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65,
	0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1a,
	0x2e, 0x63, 0x68, 0x61, 0x69, 0x6e, 0x2e, 0x49, 0x6e, 0x69, 0x74, 0x41, 0x63, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x3a, 0x0a, 0x07,
	0x43, 0x61, 0x73, 0x74, 0x4e, 0x46, 0x54, 0x12, 0x15, 0x2e, 0x63, 0x68, 0x61, 0x69, 0x6e, 0x2e,
	0x43, 0x61, 0x73, 0x74, 0x4e, 0x46, 0x54, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x16,
	0x2e, 0x63, 0x68, 0x61, 0x69, 0x6e, 0x2e, 0x43, 0x61, 0x73, 0x74, 0x4e, 0x46, 0x54, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x3a, 0x0a, 0x09, 0x4e, 0x46, 0x54, 0x42,
	0x79, 0x48, 0x61, 0x73, 0x68, 0x12, 0x14, 0x2e, 0x63, 0x68, 0x61, 0x69, 0x6e, 0x2e, 0x47, 0x65,
	0x74, 0x4e, 0x46, 0x54, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x15, 0x2e, 0x63, 0x68,
	0x61, 0x69, 0x6e, 0x2e, 0x43, 0x61, 0x73, 0x74, 0x4e, 0x46, 0x54, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x22, 0x00, 0x12, 0x37, 0x0a, 0x09, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x4e, 0x46,
	0x54, 0x12, 0x14, 0x2e, 0x63, 0x68, 0x61, 0x69, 0x6e, 0x2e, 0x47, 0x65, 0x74, 0x4e, 0x46, 0x54,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x12, 0x2e, 0x63, 0x68, 0x61, 0x69, 0x6e, 0x2e,
	0x4e, 0x66, 0x74, 0x48, 0x61, 0x73, 0x68, 0x4c, 0x69, 0x73, 0x74, 0x22, 0x00, 0x12, 0x3a, 0x0a,
	0x07, 0x4e, 0x46, 0x54, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x15, 0x2e, 0x63, 0x68, 0x61, 0x69, 0x6e,
	0x2e, 0x4e, 0x46, 0x54, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a,
	0x16, 0x2e, 0x63, 0x68, 0x61, 0x69, 0x6e, 0x2e, 0x4e, 0x46, 0x54, 0x4c, 0x69, 0x73, 0x74, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x3a, 0x0a, 0x07, 0x42, 0x75, 0x72,
	0x6e, 0x4e, 0x46, 0x54, 0x12, 0x15, 0x2e, 0x63, 0x68, 0x61, 0x69, 0x6e, 0x2e, 0x42, 0x75, 0x72,
	0x6e, 0x4e, 0x46, 0x54, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x16, 0x2e, 0x63, 0x68,
	0x61, 0x69, 0x6e, 0x2e, 0x42, 0x75, 0x72, 0x6e, 0x4e, 0x46, 0x54, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x3d, 0x0a, 0x08, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x66, 0x65,
	0x72, 0x12, 0x16, 0x2e, 0x63, 0x68, 0x61, 0x69, 0x6e, 0x2e, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x66,
	0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x17, 0x2e, 0x63, 0x68, 0x61, 0x69,
	0x6e, 0x2e, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x66, 0x65, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x22, 0x00, 0x12, 0x45, 0x0a, 0x10, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x66, 0x65, 0x72,
	0x41, 0x75, 0x74, 0x6f, 0x4c, 0x6f, 0x63, 0x6b, 0x12, 0x16, 0x2e, 0x63, 0x68, 0x61, 0x69, 0x6e,
	0x2e, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x66, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x1a, 0x17, 0x2e, 0x63, 0x68, 0x61, 0x69, 0x6e, 0x2e, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x66, 0x65,
	0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x31, 0x0a, 0x04, 0x4c,
	0x6f, 0x63, 0x6b, 0x12, 0x12, 0x2e, 0x63, 0x68, 0x61, 0x69, 0x6e, 0x2e, 0x4c, 0x6f, 0x63, 0x6b,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x13, 0x2e, 0x63, 0x68, 0x61, 0x69, 0x6e, 0x2e,
	0x4c, 0x6f, 0x63, 0x6b, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x33,
	0x0a, 0x06, 0x55, 0x6e, 0x4c, 0x6f, 0x63, 0x6b, 0x12, 0x12, 0x2e, 0x63, 0x68, 0x61, 0x69, 0x6e,
	0x2e, 0x4c, 0x6f, 0x63, 0x6b, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x13, 0x2e, 0x63,
	0x68, 0x61, 0x69, 0x6e, 0x2e, 0x4c, 0x6f, 0x63, 0x6b, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x22, 0x00, 0x12, 0x3b, 0x0a, 0x05, 0x45, 0x43, 0x65, 0x72, 0x74, 0x12, 0x15, 0x2e, 0x63,
	0x68, 0x61, 0x69, 0x6e, 0x2e, 0x49, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x1a, 0x19, 0x2e, 0x63, 0x68, 0x61, 0x69, 0x6e, 0x2e, 0x55, 0x70, 0x6c, 0x6f,
	0x61, 0x64, 0x46, 0x69, 0x6c, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00,
	0x12, 0x3d, 0x0a, 0x07, 0x44, 0x62, 0x45, 0x43, 0x65, 0x72, 0x74, 0x12, 0x15, 0x2e, 0x63, 0x68,
	0x61, 0x69, 0x6e, 0x2e, 0x49, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x1a, 0x19, 0x2e, 0x63, 0x68, 0x61, 0x69, 0x6e, 0x2e, 0x55, 0x70, 0x6c, 0x6f, 0x61,
	0x64, 0x46, 0x69, 0x6c, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x42,
	0x0a, 0x5a, 0x08, 0x2e, 0x2f, 0x3b, 0x63, 0x68, 0x61, 0x69, 0x6e, 0x62, 0x06, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x33,
}

var (
	file_api_chain_chain_proto_rawDescOnce sync.Once
	file_api_chain_chain_proto_rawDescData = file_api_chain_chain_proto_rawDesc
)

func file_api_chain_chain_proto_rawDescGZIP() []byte {
	file_api_chain_chain_proto_rawDescOnce.Do(func() {
		file_api_chain_chain_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_chain_chain_proto_rawDescData)
	})
	return file_api_chain_chain_proto_rawDescData
}

var file_api_chain_chain_proto_msgTypes = make([]protoimpl.MessageInfo, 36)
var file_api_chain_chain_proto_goTypes = []interface{}{
	(*BaiduInfoByTxtIdResponse)(nil), // 0: chain.BaiduInfoByTxtIdResponse
	(*GetNFTRequest)(nil),            // 1: chain.GetNFTRequest
	(*NftHashList)(nil),              // 2: chain.NftHashList
	(*BaiduReqInfoRequest)(nil),      // 3: chain.BaiduReqInfoRequest
	(*BaiduInfoByTxtIdRequest)(nil),  // 4: chain.BaiduInfoByTxtIdRequest
	(*BaiduReqInfoResponse)(nil),     // 5: chain.BaiduReqInfoResponse
	(*CreateAccountRequest)(nil),     // 6: chain.CreateAccountRequest
	(*InitAccountRequest)(nil),       // 7: chain.InitAccountRequest
	(*InitAccountResponse)(nil),      // 8: chain.InitAccountResponse
	(*LockRequest)(nil),              // 9: chain.LockRequest
	(*Seller)(nil),                   // 10: chain.Seller
	(*LockResponse)(nil),             // 11: chain.LockResponse
	(*UnLockResponse)(nil),           // 12: chain.UnLockResponse
	(*IdentArtworkInfo)(nil),         // 13: chain.IdentArtworkInfo
	(*Identification)(nil),           // 14: chain.Identification
	(*Artist)(nil),                   // 15: chain.Artist
	(*UploadFileResponse)(nil),       // 16: chain.UploadFileResponse
	(*CoChainRequest)(nil),           // 17: chain.CoChainRequest
	(*CoChengChainRequest)(nil),      // 18: chain.CoChengChainRequest
	(*BaiduReq)(nil),                 // 19: chain.BaiduReq
	(*CoChengChainResponse)(nil),     // 20: chain.CoChengChainResponse
	(*CoBaiduChainResponse)(nil),     // 21: chain.CoBaiduChainResponse
	(*CoChainResponse)(nil),          // 22: chain.CoChainResponse
	(*CastNFTRequest)(nil),           // 23: chain.CastNFTRequest
	(*CastNFTResponse)(nil),          // 24: chain.CastNFTResponse
	(*FonContract)(nil),              // 25: chain.FonContract
	(*NFTListRequest)(nil),           // 26: chain.NFTListRequest
	(*TxReceipt)(nil),                // 27: chain.TxReceipt
	(*NFT)(nil),                      // 28: chain.NFT
	(*NFTListResponse)(nil),          // 29: chain.NFTListResponse
	(*BurnNFTRequest)(nil),           // 30: chain.BurnNFTRequest
	(*BurnNFTResponse)(nil),          // 31: chain.BurnNFTResponse
	(*TransferRequest)(nil),          // 32: chain.TransferRequest
	(*TransferResponse)(nil),         // 33: chain.TransferResponse
	(*ECertRequest)(nil),             // 34: chain.ECertRequest
	(*ECertResponse)(nil),            // 35: chain.ECertResponse
}
var file_api_chain_chain_proto_depIdxs = []int32{
	25, // 0: chain.InitAccountRequest.FonContract:type_name -> chain.FonContract
	10, // 1: chain.LockRequest.Seller:type_name -> chain.Seller
	25, // 2: chain.LockRequest.FonContract:type_name -> chain.FonContract
	13, // 3: chain.Identification.IdentArtworkInfoPro:type_name -> chain.IdentArtworkInfo
	15, // 4: chain.Identification.Artist:type_name -> chain.Artist
	28, // 5: chain.CastNFTRequest.ArtWork:type_name -> chain.NFT
	25, // 6: chain.CastNFTRequest.FonContract:type_name -> chain.FonContract
	25, // 7: chain.NFTListRequest.FonContract:type_name -> chain.FonContract
	27, // 8: chain.NFT.TReceipt:type_name -> chain.TxReceipt
	28, // 9: chain.NFTListResponse.data:type_name -> chain.NFT
	25, // 10: chain.BurnNFTRequest.FonContract:type_name -> chain.FonContract
	10, // 11: chain.TransferRequest.seller:type_name -> chain.Seller
	25, // 12: chain.TransferRequest.FonContract:type_name -> chain.FonContract
	19, // 13: chain.Chain.CoBaiduChain:input_type -> chain.BaiduReq
	19, // 14: chain.Chain.CoBaiduCertify:input_type -> chain.BaiduReq
	3,  // 15: chain.Chain.BaiduReqInfo:input_type -> chain.BaiduReqInfoRequest
	4,  // 16: chain.Chain.BaiduInfoByTxtId:input_type -> chain.BaiduInfoByTxtIdRequest
	18, // 17: chain.Chain.CoChengChain:input_type -> chain.CoChengChainRequest
	18, // 18: chain.Chain.CoWtChain:input_type -> chain.CoChengChainRequest
	7,  // 19: chain.Chain.InitAccount:input_type -> chain.InitAccountRequest
	6,  // 20: chain.Chain.CreateAccount:input_type -> chain.CreateAccountRequest
	23, // 21: chain.Chain.CastNFT:input_type -> chain.CastNFTRequest
	1,  // 22: chain.Chain.NFTByHash:input_type -> chain.GetNFTRequest
	1,  // 23: chain.Chain.SearchNFT:input_type -> chain.GetNFTRequest
	26, // 24: chain.Chain.NFTList:input_type -> chain.NFTListRequest
	30, // 25: chain.Chain.BurnNFT:input_type -> chain.BurnNFTRequest
	32, // 26: chain.Chain.Transfer:input_type -> chain.TransferRequest
	32, // 27: chain.Chain.TransferAutoLock:input_type -> chain.TransferRequest
	9,  // 28: chain.Chain.Lock:input_type -> chain.LockRequest
	9,  // 29: chain.Chain.UnLock:input_type -> chain.LockRequest
	14, // 30: chain.Chain.ECert:input_type -> chain.Identification
	14, // 31: chain.Chain.DbECert:input_type -> chain.Identification
	21, // 32: chain.Chain.CoBaiduChain:output_type -> chain.CoBaiduChainResponse
	21, // 33: chain.Chain.CoBaiduCertify:output_type -> chain.CoBaiduChainResponse
	5,  // 34: chain.Chain.BaiduReqInfo:output_type -> chain.BaiduReqInfoResponse
	0,  // 35: chain.Chain.BaiduInfoByTxtId:output_type -> chain.BaiduInfoByTxtIdResponse
	20, // 36: chain.Chain.CoChengChain:output_type -> chain.CoChengChainResponse
	22, // 37: chain.Chain.CoWtChain:output_type -> chain.CoChainResponse
	8,  // 38: chain.Chain.InitAccount:output_type -> chain.InitAccountResponse
	8,  // 39: chain.Chain.CreateAccount:output_type -> chain.InitAccountResponse
	24, // 40: chain.Chain.CastNFT:output_type -> chain.CastNFTResponse
	23, // 41: chain.Chain.NFTByHash:output_type -> chain.CastNFTRequest
	2,  // 42: chain.Chain.SearchNFT:output_type -> chain.NftHashList
	29, // 43: chain.Chain.NFTList:output_type -> chain.NFTListResponse
	31, // 44: chain.Chain.BurnNFT:output_type -> chain.BurnNFTResponse
	33, // 45: chain.Chain.Transfer:output_type -> chain.TransferResponse
	33, // 46: chain.Chain.TransferAutoLock:output_type -> chain.TransferResponse
	11, // 47: chain.Chain.Lock:output_type -> chain.LockResponse
	11, // 48: chain.Chain.UnLock:output_type -> chain.LockResponse
	16, // 49: chain.Chain.ECert:output_type -> chain.UploadFileResponse
	16, // 50: chain.Chain.DbECert:output_type -> chain.UploadFileResponse
	32, // [32:51] is the sub-list for method output_type
	13, // [13:32] is the sub-list for method input_type
	13, // [13:13] is the sub-list for extension type_name
	13, // [13:13] is the sub-list for extension extendee
	0,  // [0:13] is the sub-list for field type_name
}

func init() { file_api_chain_chain_proto_init() }
func file_api_chain_chain_proto_init() {
	if File_api_chain_chain_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_api_chain_chain_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BaiduInfoByTxtIdResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_chain_chain_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetNFTRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_chain_chain_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*NftHashList); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_chain_chain_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BaiduReqInfoRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_chain_chain_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BaiduInfoByTxtIdRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_chain_chain_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BaiduReqInfoResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_chain_chain_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateAccountRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_chain_chain_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*InitAccountRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_chain_chain_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*InitAccountResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_chain_chain_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LockRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_chain_chain_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Seller); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_chain_chain_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LockResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_chain_chain_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UnLockResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_chain_chain_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*IdentArtworkInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_chain_chain_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Identification); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_chain_chain_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Artist); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_chain_chain_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UploadFileResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_chain_chain_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CoChainRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_chain_chain_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CoChengChainRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_chain_chain_proto_msgTypes[19].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BaiduReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_chain_chain_proto_msgTypes[20].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CoChengChainResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_chain_chain_proto_msgTypes[21].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CoBaiduChainResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_chain_chain_proto_msgTypes[22].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CoChainResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_chain_chain_proto_msgTypes[23].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CastNFTRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_chain_chain_proto_msgTypes[24].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CastNFTResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_chain_chain_proto_msgTypes[25].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FonContract); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_chain_chain_proto_msgTypes[26].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*NFTListRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_chain_chain_proto_msgTypes[27].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TxReceipt); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_chain_chain_proto_msgTypes[28].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*NFT); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_chain_chain_proto_msgTypes[29].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*NFTListResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_chain_chain_proto_msgTypes[30].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BurnNFTRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_chain_chain_proto_msgTypes[31].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BurnNFTResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_chain_chain_proto_msgTypes[32].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TransferRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_chain_chain_proto_msgTypes[33].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TransferResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_chain_chain_proto_msgTypes[34].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ECertRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_chain_chain_proto_msgTypes[35].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ECertResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_chain_chain_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   36,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_api_chain_chain_proto_goTypes,
		DependencyIndexes: file_api_chain_chain_proto_depIdxs,
		MessageInfos:      file_api_chain_chain_proto_msgTypes,
	}.Build()
	File_api_chain_chain_proto = out.File
	file_api_chain_chain_proto_rawDesc = nil
	file_api_chain_chain_proto_goTypes = nil
	file_api_chain_chain_proto_depIdxs = nil
}
