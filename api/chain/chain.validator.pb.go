// Code generated by protoc-gen-gogo. DO NOT EDIT.
// source: api/chain/chain.proto

package chain

import (
	fmt "fmt"
	math "math"
	proto "github.com/golang/protobuf/proto"
	_ "github.com/mwitkow/go-proto-validators"
	github_com_mwitkow_go_proto_validators "github.com/mwitkow/go-proto-validators"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

func (this *BaiduInfoByTxtIdResponse) Validate() error {
	return nil
}
func (this *GetNFTRequest) Validate() error {
	return nil
}
func (this *NftHashList) Validate() error {
	return nil
}
func (this *BaiduReqInfoRequest) Validate() error {
	return nil
}
func (this *BaiduInfoByTxtIdRequest) Validate() error {
	return nil
}
func (this *BaiduReqInfoResponse) Validate() error {
	return nil
}
func (this *CreateAccountRequest) Validate() error {
	return nil
}
func (this *InitAccountRequest) Validate() error {
	if this.FonContract != nil {
		if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(this.FonContract); err != nil {
			return github_com_mwitkow_go_proto_validators.FieldError("FonContract", err)
		}
	}
	return nil
}
func (this *InitAccountResponse) Validate() error {
	return nil
}
func (this *LockRequest) Validate() error {
	if this.Seller != nil {
		if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(this.Seller); err != nil {
			return github_com_mwitkow_go_proto_validators.FieldError("Seller", err)
		}
	}
	if this.FonContract != nil {
		if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(this.FonContract); err != nil {
			return github_com_mwitkow_go_proto_validators.FieldError("FonContract", err)
		}
	}
	return nil
}
func (this *Seller) Validate() error {
	return nil
}
func (this *LockResponse) Validate() error {
	return nil
}
func (this *UnLockResponse) Validate() error {
	return nil
}
func (this *IdentArtworkInfo) Validate() error {
	return nil
}
func (this *Identification) Validate() error {
	if this.IdentArtworkInfoPro != nil {
		if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(this.IdentArtworkInfoPro); err != nil {
			return github_com_mwitkow_go_proto_validators.FieldError("IdentArtworkInfoPro", err)
		}
	}
	if this.Artist != nil {
		if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(this.Artist); err != nil {
			return github_com_mwitkow_go_proto_validators.FieldError("Artist", err)
		}
	}
	return nil
}
func (this *Artist) Validate() error {
	return nil
}
func (this *UploadFileResponse) Validate() error {
	return nil
}
func (this *CoChainRequest) Validate() error {
	return nil
}
func (this *CoChengChainRequest) Validate() error {
	return nil
}
func (this *BaiduReq) Validate() error {
	return nil
}
func (this *CoChengChainResponse) Validate() error {
	return nil
}
func (this *CoBaiduChainResponse) Validate() error {
	return nil
}
func (this *CoChainResponse) Validate() error {
	return nil
}
func (this *CastNFTRequest) Validate() error {
	if this.ArtWork != nil {
		if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(this.ArtWork); err != nil {
			return github_com_mwitkow_go_proto_validators.FieldError("ArtWork", err)
		}
	}
	if this.FonContract != nil {
		if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(this.FonContract); err != nil {
			return github_com_mwitkow_go_proto_validators.FieldError("FonContract", err)
		}
	}
	return nil
}
func (this *CastNFTResponse) Validate() error {
	return nil
}
func (this *FonContract) Validate() error {
	return nil
}
func (this *NFTListRequest) Validate() error {
	if this.FonContract != nil {
		if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(this.FonContract); err != nil {
			return github_com_mwitkow_go_proto_validators.FieldError("FonContract", err)
		}
	}
	return nil
}
func (this *TxReceipt) Validate() error {
	return nil
}
func (this *NFT) Validate() error {
	if this.DciHash == "" {
		return github_com_mwitkow_go_proto_validators.FieldError("DciHash", fmt.Errorf(`hash必须要有`))
	}
	for _, item := range this.TReceipt {
		if item != nil {
			if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(item); err != nil {
				return github_com_mwitkow_go_proto_validators.FieldError("TReceipt", err)
			}
		}
	}
	return nil
}
func (this *NFTListResponse) Validate() error {
	for _, item := range this.Data {
		if item != nil {
			if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(item); err != nil {
				return github_com_mwitkow_go_proto_validators.FieldError("Data", err)
			}
		}
	}
	return nil
}
func (this *BurnNFTRequest) Validate() error {
	if this.FonContract != nil {
		if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(this.FonContract); err != nil {
			return github_com_mwitkow_go_proto_validators.FieldError("FonContract", err)
		}
	}
	return nil
}
func (this *BurnNFTResponse) Validate() error {
	return nil
}
func (this *TransferRequest) Validate() error {
	if this.Seller != nil {
		if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(this.Seller); err != nil {
			return github_com_mwitkow_go_proto_validators.FieldError("Seller", err)
		}
	}
	if this.FonContract != nil {
		if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(this.FonContract); err != nil {
			return github_com_mwitkow_go_proto_validators.FieldError("FonContract", err)
		}
	}
	return nil
}
func (this *TransferResponse) Validate() error {
	return nil
}
func (this *ECertRequest) Validate() error {
	return nil
}
func (this *ECertResponse) Validate() error {
	return nil
}
