// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v6.30.0--rc2
// source: api/series/series.proto

package backendSeries

import (
	_ "github.com/mwitkow/go-proto-validators"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type CirculationDetailReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Uuid string `protobuf:"bytes,1,opt,name=uuid,proto3" json:"uuid,omitempty"`
}

func (x *CirculationDetailReq) Reset() {
	*x = CirculationDetailReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_series_series_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CirculationDetailReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CirculationDetailReq) ProtoMessage() {}

func (x *CirculationDetailReq) ProtoReflect() protoreflect.Message {
	mi := &file_api_series_series_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CirculationDetailReq.ProtoReflect.Descriptor instead.
func (*CirculationDetailReq) Descriptor() ([]byte, []int) {
	return file_api_series_series_proto_rawDescGZIP(), []int{0}
}

func (x *CirculationDetailReq) GetUuid() string {
	if x != nil {
		return x.Uuid
	}
	return ""
}

type UpdateCirculationHashReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Hash string `protobuf:"bytes,1,opt,name=hash,proto3" json:"hash,omitempty"`
	Msg  string `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	Uuid string `protobuf:"bytes,3,opt,name=uuid,proto3" json:"uuid,omitempty"`
}

func (x *UpdateCirculationHashReq) Reset() {
	*x = UpdateCirculationHashReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_series_series_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateCirculationHashReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateCirculationHashReq) ProtoMessage() {}

func (x *UpdateCirculationHashReq) ProtoReflect() protoreflect.Message {
	mi := &file_api_series_series_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateCirculationHashReq.ProtoReflect.Descriptor instead.
func (*UpdateCirculationHashReq) Descriptor() ([]byte, []int) {
	return file_api_series_series_proto_rawDescGZIP(), []int{1}
}

func (x *UpdateCirculationHashReq) GetHash() string {
	if x != nil {
		return x.Hash
	}
	return ""
}

func (x *UpdateCirculationHashReq) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *UpdateCirculationHashReq) GetUuid() string {
	if x != nil {
		return x.Uuid
	}
	return ""
}

type GiveCultureArtworkOrderReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	OrderUuid string `protobuf:"bytes,1,opt,name=orderUuid,proto3" json:"orderUuid,omitempty"`
	From      uint32 `protobuf:"varint,2,opt,name=from,proto3" json:"from,omitempty"`
	To        uint32 `protobuf:"varint,3,opt,name=to,proto3" json:"to,omitempty"`
	FromName  string `protobuf:"bytes,4,opt,name=fromName,proto3" json:"fromName,omitempty"`
	ToName    string `protobuf:"bytes,5,opt,name=toName,proto3" json:"toName,omitempty"`
}

func (x *GiveCultureArtworkOrderReq) Reset() {
	*x = GiveCultureArtworkOrderReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_series_series_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GiveCultureArtworkOrderReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GiveCultureArtworkOrderReq) ProtoMessage() {}

func (x *GiveCultureArtworkOrderReq) ProtoReflect() protoreflect.Message {
	mi := &file_api_series_series_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GiveCultureArtworkOrderReq.ProtoReflect.Descriptor instead.
func (*GiveCultureArtworkOrderReq) Descriptor() ([]byte, []int) {
	return file_api_series_series_proto_rawDescGZIP(), []int{2}
}

func (x *GiveCultureArtworkOrderReq) GetOrderUuid() string {
	if x != nil {
		return x.OrderUuid
	}
	return ""
}

func (x *GiveCultureArtworkOrderReq) GetFrom() uint32 {
	if x != nil {
		return x.From
	}
	return 0
}

func (x *GiveCultureArtworkOrderReq) GetTo() uint32 {
	if x != nil {
		return x.To
	}
	return 0
}

func (x *GiveCultureArtworkOrderReq) GetFromName() string {
	if x != nil {
		return x.FromName
	}
	return ""
}

func (x *GiveCultureArtworkOrderReq) GetToName() string {
	if x != nil {
		return x.ToName
	}
	return ""
}

type UpdateCastCultureArtworkOrderReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	OrderUuid       string `protobuf:"bytes,1,opt,name=orderUuid,proto3" json:"orderUuid,omitempty"`
	TransactionHash string `protobuf:"bytes,2,opt,name=transactionHash,proto3" json:"transactionHash,omitempty"`
	CastMsg         string `protobuf:"bytes,3,opt,name=castMsg,proto3" json:"castMsg,omitempty"`
}

func (x *UpdateCastCultureArtworkOrderReq) Reset() {
	*x = UpdateCastCultureArtworkOrderReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_series_series_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateCastCultureArtworkOrderReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateCastCultureArtworkOrderReq) ProtoMessage() {}

func (x *UpdateCastCultureArtworkOrderReq) ProtoReflect() protoreflect.Message {
	mi := &file_api_series_series_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateCastCultureArtworkOrderReq.ProtoReflect.Descriptor instead.
func (*UpdateCastCultureArtworkOrderReq) Descriptor() ([]byte, []int) {
	return file_api_series_series_proto_rawDescGZIP(), []int{3}
}

func (x *UpdateCastCultureArtworkOrderReq) GetOrderUuid() string {
	if x != nil {
		return x.OrderUuid
	}
	return ""
}

func (x *UpdateCastCultureArtworkOrderReq) GetTransactionHash() string {
	if x != nil {
		return x.TransactionHash
	}
	return ""
}

func (x *UpdateCastCultureArtworkOrderReq) GetCastMsg() string {
	if x != nil {
		return x.CastMsg
	}
	return ""
}

type ReceiveCultureArtworkOrderReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	OrderUuid string `protobuf:"bytes,1,opt,name=orderUuid,proto3" json:"orderUuid,omitempty"`
	IsAgree   bool   `protobuf:"varint,2,opt,name=isAgree,proto3" json:"isAgree,omitempty"`
	To        uint32 `protobuf:"varint,3,opt,name=to,proto3" json:"to,omitempty"`
	ToName    string `protobuf:"bytes,4,opt,name=toName,proto3" json:"toName,omitempty"`
}

func (x *ReceiveCultureArtworkOrderReq) Reset() {
	*x = ReceiveCultureArtworkOrderReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_series_series_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReceiveCultureArtworkOrderReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReceiveCultureArtworkOrderReq) ProtoMessage() {}

func (x *ReceiveCultureArtworkOrderReq) ProtoReflect() protoreflect.Message {
	mi := &file_api_series_series_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReceiveCultureArtworkOrderReq.ProtoReflect.Descriptor instead.
func (*ReceiveCultureArtworkOrderReq) Descriptor() ([]byte, []int) {
	return file_api_series_series_proto_rawDescGZIP(), []int{4}
}

func (x *ReceiveCultureArtworkOrderReq) GetOrderUuid() string {
	if x != nil {
		return x.OrderUuid
	}
	return ""
}

func (x *ReceiveCultureArtworkOrderReq) GetIsAgree() bool {
	if x != nil {
		return x.IsAgree
	}
	return false
}

func (x *ReceiveCultureArtworkOrderReq) GetTo() uint32 {
	if x != nil {
		return x.To
	}
	return 0
}

func (x *ReceiveCultureArtworkOrderReq) GetToName() string {
	if x != nil {
		return x.ToName
	}
	return ""
}

type DrawCultureArtworkReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CultureArtworkUuid string `protobuf:"bytes,1,opt,name=cultureArtworkUuid,proto3" json:"cultureArtworkUuid,omitempty"`
	IndexNum           uint32 `protobuf:"varint,2,opt,name=indexNum,proto3" json:"indexNum,omitempty"`
	UserId             uint32 `protobuf:"varint,3,opt,name=userId,proto3" json:"userId,omitempty"`
	UserTelNum         string `protobuf:"bytes,4,opt,name=userTelNum,proto3" json:"userTelNum,omitempty"`
	UserName           string `protobuf:"bytes,5,opt,name=userName,proto3" json:"userName,omitempty"`
	PayType            uint32 `protobuf:"varint,6,opt,name=payType,proto3" json:"payType,omitempty"`
}

func (x *DrawCultureArtworkReq) Reset() {
	*x = DrawCultureArtworkReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_series_series_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DrawCultureArtworkReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DrawCultureArtworkReq) ProtoMessage() {}

func (x *DrawCultureArtworkReq) ProtoReflect() protoreflect.Message {
	mi := &file_api_series_series_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DrawCultureArtworkReq.ProtoReflect.Descriptor instead.
func (*DrawCultureArtworkReq) Descriptor() ([]byte, []int) {
	return file_api_series_series_proto_rawDescGZIP(), []int{5}
}

func (x *DrawCultureArtworkReq) GetCultureArtworkUuid() string {
	if x != nil {
		return x.CultureArtworkUuid
	}
	return ""
}

func (x *DrawCultureArtworkReq) GetIndexNum() uint32 {
	if x != nil {
		return x.IndexNum
	}
	return 0
}

func (x *DrawCultureArtworkReq) GetUserId() uint32 {
	if x != nil {
		return x.UserId
	}
	return 0
}

func (x *DrawCultureArtworkReq) GetUserTelNum() string {
	if x != nil {
		return x.UserTelNum
	}
	return ""
}

func (x *DrawCultureArtworkReq) GetUserName() string {
	if x != nil {
		return x.UserName
	}
	return ""
}

func (x *DrawCultureArtworkReq) GetPayType() uint32 {
	if x != nil {
		return x.PayType
	}
	return 0
}

type CultureItem struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Url        string  `protobuf:"bytes,1,opt,name=url,proto3" json:"url,omitempty"`
	StockNum   int32   `protobuf:"varint,2,opt,name=stockNum,proto3" json:"stockNum,omitempty"`
	SystockNum int32   `protobuf:"varint,3,opt,name=systockNum,proto3" json:"systockNum,omitempty"`
	CultureNum []int32 `protobuf:"varint,4,rep,packed,name=cultureNum,proto3" json:"cultureNum,omitempty"`
	ModelType  string  `protobuf:"bytes,5,opt,name=modelType,proto3" json:"modelType,omitempty"`
}

func (x *CultureItem) Reset() {
	*x = CultureItem{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_series_series_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CultureItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CultureItem) ProtoMessage() {}

func (x *CultureItem) ProtoReflect() protoreflect.Message {
	mi := &file_api_series_series_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CultureItem.ProtoReflect.Descriptor instead.
func (*CultureItem) Descriptor() ([]byte, []int) {
	return file_api_series_series_proto_rawDescGZIP(), []int{6}
}

func (x *CultureItem) GetUrl() string {
	if x != nil {
		return x.Url
	}
	return ""
}

func (x *CultureItem) GetStockNum() int32 {
	if x != nil {
		return x.StockNum
	}
	return 0
}

func (x *CultureItem) GetSystockNum() int32 {
	if x != nil {
		return x.SystockNum
	}
	return 0
}

func (x *CultureItem) GetCultureNum() []int32 {
	if x != nil {
		return x.CultureNum
	}
	return nil
}

func (x *CultureItem) GetModelType() string {
	if x != nil {
		return x.ModelType
	}
	return ""
}

type CultureArtworkDetailReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Uuid string `protobuf:"bytes,1,opt,name=uuid,proto3" json:"uuid,omitempty"`
	Lang string `protobuf:"bytes,2,opt,name=lang,proto3" json:"lang,omitempty"` //暂时无用，仅仅准备
	Num  uint32 `protobuf:"varint,3,opt,name=num,proto3" json:"num,omitempty"`  //序列，看看有没有被买过
}

func (x *CultureArtworkDetailReq) Reset() {
	*x = CultureArtworkDetailReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_series_series_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CultureArtworkDetailReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CultureArtworkDetailReq) ProtoMessage() {}

func (x *CultureArtworkDetailReq) ProtoReflect() protoreflect.Message {
	mi := &file_api_series_series_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CultureArtworkDetailReq.ProtoReflect.Descriptor instead.
func (*CultureArtworkDetailReq) Descriptor() ([]byte, []int) {
	return file_api_series_series_proto_rawDescGZIP(), []int{7}
}

func (x *CultureArtworkDetailReq) GetUuid() string {
	if x != nil {
		return x.Uuid
	}
	return ""
}

func (x *CultureArtworkDetailReq) GetLang() string {
	if x != nil {
		return x.Lang
	}
	return ""
}

func (x *CultureArtworkDetailReq) GetNum() uint32 {
	if x != nil {
		return x.Num
	}
	return 0
}

type ScanListReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	SeriesUuid                string `protobuf:"bytes,1,opt,name=seriesUuid,proto3" json:"seriesUuid,omitempty"`
	SeriesCulturalArtworkName string `protobuf:"bytes,2,opt,name=seriesCulturalArtworkName,proto3" json:"seriesCulturalArtworkName,omitempty"`
	Hash                      string `protobuf:"bytes,3,opt,name=Hash,proto3" json:"Hash,omitempty"`
	FirstUserTel              string `protobuf:"bytes,4,opt,name=firstUserTel,proto3" json:"firstUserTel,omitempty"`
	StartCreatedTime          string `protobuf:"bytes,5,opt,name=startCreatedTime,proto3" json:"startCreatedTime,omitempty"`
	EndCreatedTime            string `protobuf:"bytes,6,opt,name=endCreatedTime,proto3" json:"endCreatedTime,omitempty"`
	Page                      uint64 `protobuf:"varint,7,opt,name=page,proto3" json:"page,omitempty"`
	PageSize                  uint64 `protobuf:"varint,8,opt,name=pageSize,proto3" json:"pageSize,omitempty"`
}

func (x *ScanListReq) Reset() {
	*x = ScanListReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_series_series_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ScanListReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ScanListReq) ProtoMessage() {}

func (x *ScanListReq) ProtoReflect() protoreflect.Message {
	mi := &file_api_series_series_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ScanListReq.ProtoReflect.Descriptor instead.
func (*ScanListReq) Descriptor() ([]byte, []int) {
	return file_api_series_series_proto_rawDescGZIP(), []int{8}
}

func (x *ScanListReq) GetSeriesUuid() string {
	if x != nil {
		return x.SeriesUuid
	}
	return ""
}

func (x *ScanListReq) GetSeriesCulturalArtworkName() string {
	if x != nil {
		return x.SeriesCulturalArtworkName
	}
	return ""
}

func (x *ScanListReq) GetHash() string {
	if x != nil {
		return x.Hash
	}
	return ""
}

func (x *ScanListReq) GetFirstUserTel() string {
	if x != nil {
		return x.FirstUserTel
	}
	return ""
}

func (x *ScanListReq) GetStartCreatedTime() string {
	if x != nil {
		return x.StartCreatedTime
	}
	return ""
}

func (x *ScanListReq) GetEndCreatedTime() string {
	if x != nil {
		return x.EndCreatedTime
	}
	return ""
}

func (x *ScanListReq) GetPage() uint64 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *ScanListReq) GetPageSize() uint64 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

type CultureArtworkListReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	SeriesUuid string `protobuf:"bytes,1,opt,name=seriesUuid,proto3" json:"seriesUuid,omitempty"`
	Page       uint64 `protobuf:"varint,7,opt,name=page,proto3" json:"page,omitempty"`
	PageSize   uint64 `protobuf:"varint,8,opt,name=pageSize,proto3" json:"pageSize,omitempty"`
}

func (x *CultureArtworkListReq) Reset() {
	*x = CultureArtworkListReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_series_series_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CultureArtworkListReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CultureArtworkListReq) ProtoMessage() {}

func (x *CultureArtworkListReq) ProtoReflect() protoreflect.Message {
	mi := &file_api_series_series_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CultureArtworkListReq.ProtoReflect.Descriptor instead.
func (*CultureArtworkListReq) Descriptor() ([]byte, []int) {
	return file_api_series_series_proto_rawDescGZIP(), []int{9}
}

func (x *CultureArtworkListReq) GetSeriesUuid() string {
	if x != nil {
		return x.SeriesUuid
	}
	return ""
}

func (x *CultureArtworkListReq) GetPage() uint64 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *CultureArtworkListReq) GetPageSize() uint64 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

type ScanListResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	List  []*ScanInfo `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`
	Count int64       `protobuf:"varint,2,opt,name=count,proto3" json:"count,omitempty"`
}

func (x *ScanListResp) Reset() {
	*x = ScanListResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_series_series_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ScanListResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ScanListResp) ProtoMessage() {}

func (x *ScanListResp) ProtoReflect() protoreflect.Message {
	mi := &file_api_series_series_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ScanListResp.ProtoReflect.Descriptor instead.
func (*ScanListResp) Descriptor() ([]byte, []int) {
	return file_api_series_series_proto_rawDescGZIP(), []int{10}
}

func (x *ScanListResp) GetList() []*ScanInfo {
	if x != nil {
		return x.List
	}
	return nil
}

func (x *ScanListResp) GetCount() int64 {
	if x != nil {
		return x.Count
	}
	return 0
}

type ScanInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Uuid                      string `protobuf:"bytes,1,opt,name=uuid,proto3" json:"uuid,omitempty"`
	SeriesUuid                string `protobuf:"bytes,2,opt,name=seriesUuid,proto3" json:"seriesUuid,omitempty"`
	SeriesCulturalArtworkName string `protobuf:"bytes,3,opt,name=seriesCulturalArtworkName,proto3" json:"seriesCulturalArtworkName,omitempty"`
	Hash                      string `protobuf:"bytes,4,opt,name=hash,proto3" json:"hash,omitempty"`
	FirstUserTel              string `protobuf:"bytes,5,opt,name=firstUserTel,proto3" json:"firstUserTel,omitempty"`
	CreatedAt                 string `protobuf:"bytes,6,opt,name=createdAt,proto3" json:"createdAt,omitempty"` //扫码时间
}

func (x *ScanInfo) Reset() {
	*x = ScanInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_series_series_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ScanInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ScanInfo) ProtoMessage() {}

func (x *ScanInfo) ProtoReflect() protoreflect.Message {
	mi := &file_api_series_series_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ScanInfo.ProtoReflect.Descriptor instead.
func (*ScanInfo) Descriptor() ([]byte, []int) {
	return file_api_series_series_proto_rawDescGZIP(), []int{11}
}

func (x *ScanInfo) GetUuid() string {
	if x != nil {
		return x.Uuid
	}
	return ""
}

func (x *ScanInfo) GetSeriesUuid() string {
	if x != nil {
		return x.SeriesUuid
	}
	return ""
}

func (x *ScanInfo) GetSeriesCulturalArtworkName() string {
	if x != nil {
		return x.SeriesCulturalArtworkName
	}
	return ""
}

func (x *ScanInfo) GetHash() string {
	if x != nil {
		return x.Hash
	}
	return ""
}

func (x *ScanInfo) GetFirstUserTel() string {
	if x != nil {
		return x.FirstUserTel
	}
	return ""
}

func (x *ScanInfo) GetCreatedAt() string {
	if x != nil {
		return x.CreatedAt
	}
	return ""
}

type CultureArtworkOrderListReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UserId      uint32 `protobuf:"varint,1,opt,name=userId,proto3" json:"userId,omitempty"`
	Page        uint64 `protobuf:"varint,2,opt,name=page,proto3" json:"page,omitempty"`
	PageSize    uint64 `protobuf:"varint,3,opt,name=pageSize,proto3" json:"pageSize,omitempty"`
	IsEmptyHash bool   `protobuf:"varint,4,opt,name=isEmptyHash,proto3" json:"isEmptyHash,omitempty"` //order 那边的hash是空的
	Status      uint32 `protobuf:"varint,5,opt,name=status,proto3" json:"status,omitempty"`           //order 那边的hash是空的
}

func (x *CultureArtworkOrderListReq) Reset() {
	*x = CultureArtworkOrderListReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_series_series_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CultureArtworkOrderListReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CultureArtworkOrderListReq) ProtoMessage() {}

func (x *CultureArtworkOrderListReq) ProtoReflect() protoreflect.Message {
	mi := &file_api_series_series_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CultureArtworkOrderListReq.ProtoReflect.Descriptor instead.
func (*CultureArtworkOrderListReq) Descriptor() ([]byte, []int) {
	return file_api_series_series_proto_rawDescGZIP(), []int{12}
}

func (x *CultureArtworkOrderListReq) GetUserId() uint32 {
	if x != nil {
		return x.UserId
	}
	return 0
}

func (x *CultureArtworkOrderListReq) GetPage() uint64 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *CultureArtworkOrderListReq) GetPageSize() uint64 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *CultureArtworkOrderListReq) GetIsEmptyHash() bool {
	if x != nil {
		return x.IsEmptyHash
	}
	return false
}

func (x *CultureArtworkOrderListReq) GetStatus() uint32 {
	if x != nil {
		return x.Status
	}
	return 0
}

type CirculationListReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	OrderUuid string `protobuf:"bytes,1,opt,name=orderUuid,proto3" json:"orderUuid,omitempty"`
	Page      uint64 `protobuf:"varint,2,opt,name=page,proto3" json:"page,omitempty"`
	PageSize  uint64 `protobuf:"varint,3,opt,name=pageSize,proto3" json:"pageSize,omitempty"`
	UserId    uint32 `protobuf:"varint,4,opt,name=userId,proto3" json:"userId,omitempty"`
}

func (x *CirculationListReq) Reset() {
	*x = CirculationListReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_series_series_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CirculationListReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CirculationListReq) ProtoMessage() {}

func (x *CirculationListReq) ProtoReflect() protoreflect.Message {
	mi := &file_api_series_series_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CirculationListReq.ProtoReflect.Descriptor instead.
func (*CirculationListReq) Descriptor() ([]byte, []int) {
	return file_api_series_series_proto_rawDescGZIP(), []int{13}
}

func (x *CirculationListReq) GetOrderUuid() string {
	if x != nil {
		return x.OrderUuid
	}
	return ""
}

func (x *CirculationListReq) GetPage() uint64 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *CirculationListReq) GetPageSize() uint64 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *CirculationListReq) GetUserId() uint32 {
	if x != nil {
		return x.UserId
	}
	return 0
}

type CultureArtworkOrderDetailReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	OrderUuid string `protobuf:"bytes,1,opt,name=orderUuid,proto3" json:"orderUuid,omitempty"`
	UserId    uint32 `protobuf:"varint,2,opt,name=userId,proto3" json:"userId,omitempty"`
}

func (x *CultureArtworkOrderDetailReq) Reset() {
	*x = CultureArtworkOrderDetailReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_series_series_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CultureArtworkOrderDetailReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CultureArtworkOrderDetailReq) ProtoMessage() {}

func (x *CultureArtworkOrderDetailReq) ProtoReflect() protoreflect.Message {
	mi := &file_api_series_series_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CultureArtworkOrderDetailReq.ProtoReflect.Descriptor instead.
func (*CultureArtworkOrderDetailReq) Descriptor() ([]byte, []int) {
	return file_api_series_series_proto_rawDescGZIP(), []int{14}
}

func (x *CultureArtworkOrderDetailReq) GetOrderUuid() string {
	if x != nil {
		return x.OrderUuid
	}
	return ""
}

func (x *CultureArtworkOrderDetailReq) GetUserId() uint32 {
	if x != nil {
		return x.UserId
	}
	return 0
}

type CirculationListResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	List  []*Circulation `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`
	Count int64          `protobuf:"varint,2,opt,name=count,proto3" json:"count,omitempty"`
}

func (x *CirculationListResp) Reset() {
	*x = CirculationListResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_series_series_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CirculationListResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CirculationListResp) ProtoMessage() {}

func (x *CirculationListResp) ProtoReflect() protoreflect.Message {
	mi := &file_api_series_series_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CirculationListResp.ProtoReflect.Descriptor instead.
func (*CirculationListResp) Descriptor() ([]byte, []int) {
	return file_api_series_series_proto_rawDescGZIP(), []int{15}
}

func (x *CirculationListResp) GetList() []*Circulation {
	if x != nil {
		return x.List
	}
	return nil
}

func (x *CirculationListResp) GetCount() int64 {
	if x != nil {
		return x.Count
	}
	return 0
}

type Circulation struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ActionType   string `protobuf:"bytes,1,opt,name=actionType,proto3" json:"actionType,omitempty"` //状态
	TradeTime    string `protobuf:"bytes,2,opt,name=tradeTime,proto3" json:"tradeTime,omitempty"`
	ReceiverName string `protobuf:"bytes,3,opt,name=receiverName,proto3" json:"receiverName,omitempty"` //接收的的人
	Uuid         string `protobuf:"bytes,4,opt,name=uuid,proto3" json:"uuid,omitempty"`
	Hash         string `protobuf:"bytes,5,opt,name=hash,proto3" json:"hash,omitempty"`                 //当前流转hash
	GiverName    string `protobuf:"bytes,6,opt,name=giverName,proto3" json:"giverName,omitempty"`       //赠送的人
	ReceiveTime  string `protobuf:"bytes,7,opt,name=receiveTime,proto3" json:"receiveTime,omitempty"`   //赠送的人
	TransferTime string `protobuf:"bytes,8,opt,name=transferTime,proto3" json:"transferTime,omitempty"` //
	Name         string `protobuf:"bytes,9,opt,name=name,proto3" json:"name,omitempty"`                 //
	BriefImg     string `protobuf:"bytes,10,opt,name=briefImg,proto3" json:"briefImg,omitempty"`        //
	SenderId     uint32 `protobuf:"varint,11,opt,name=senderId,proto3" json:"senderId,omitempty"`
	ReceiverId   uint32 `protobuf:"varint,12,opt,name=receiverId,proto3" json:"receiverId,omitempty"`
}

func (x *Circulation) Reset() {
	*x = Circulation{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_series_series_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Circulation) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Circulation) ProtoMessage() {}

func (x *Circulation) ProtoReflect() protoreflect.Message {
	mi := &file_api_series_series_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Circulation.ProtoReflect.Descriptor instead.
func (*Circulation) Descriptor() ([]byte, []int) {
	return file_api_series_series_proto_rawDescGZIP(), []int{16}
}

func (x *Circulation) GetActionType() string {
	if x != nil {
		return x.ActionType
	}
	return ""
}

func (x *Circulation) GetTradeTime() string {
	if x != nil {
		return x.TradeTime
	}
	return ""
}

func (x *Circulation) GetReceiverName() string {
	if x != nil {
		return x.ReceiverName
	}
	return ""
}

func (x *Circulation) GetUuid() string {
	if x != nil {
		return x.Uuid
	}
	return ""
}

func (x *Circulation) GetHash() string {
	if x != nil {
		return x.Hash
	}
	return ""
}

func (x *Circulation) GetGiverName() string {
	if x != nil {
		return x.GiverName
	}
	return ""
}

func (x *Circulation) GetReceiveTime() string {
	if x != nil {
		return x.ReceiveTime
	}
	return ""
}

func (x *Circulation) GetTransferTime() string {
	if x != nil {
		return x.TransferTime
	}
	return ""
}

func (x *Circulation) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *Circulation) GetBriefImg() string {
	if x != nil {
		return x.BriefImg
	}
	return ""
}

func (x *Circulation) GetSenderId() uint32 {
	if x != nil {
		return x.SenderId
	}
	return 0
}

func (x *Circulation) GetReceiverId() uint32 {
	if x != nil {
		return x.ReceiverId
	}
	return 0
}

type LanguageInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Lang   string `protobuf:"bytes,1,opt,name=lang,proto3" json:"lang,omitempty"`
	Status int32  `protobuf:"varint,3,opt,name=status,proto3" json:"status,omitempty"`
}

func (x *LanguageInfo) Reset() {
	*x = LanguageInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_series_series_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LanguageInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LanguageInfo) ProtoMessage() {}

func (x *LanguageInfo) ProtoReflect() protoreflect.Message {
	mi := &file_api_series_series_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LanguageInfo.ProtoReflect.Descriptor instead.
func (*LanguageInfo) Descriptor() ([]byte, []int) {
	return file_api_series_series_proto_rawDescGZIP(), []int{17}
}

func (x *LanguageInfo) GetLang() string {
	if x != nil {
		return x.Lang
	}
	return ""
}

func (x *LanguageInfo) GetStatus() int32 {
	if x != nil {
		return x.Status
	}
	return 0
}

// SaveSeries
type SaveSeriesReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	BrandId               string                  `protobuf:"bytes,1,opt,name=brandId,proto3" json:"brandId,omitempty"`
	BrandName             string                  `protobuf:"bytes,2,opt,name=brandName,proto3" json:"brandName,omitempty"`
	CoverImg              string                  `protobuf:"bytes,5,opt,name=coverImg,proto3" json:"coverImg,omitempty"`
	SeriesName            string                  `protobuf:"bytes,6,opt,name=seriesName,proto3" json:"seriesName,omitempty"`
	Desc                  string                  `protobuf:"bytes,8,opt,name=desc,proto3" json:"desc,omitempty"`
	Lang                  string                  `protobuf:"bytes,9,opt,name=lang,proto3" json:"lang,omitempty"`
	SeriesUuid            string                  `protobuf:"bytes,10,opt,name=seriesUuid,proto3" json:"seriesUuid,omitempty"`
	ActionCode            string                  `protobuf:"bytes,12,opt,name=actionCode,proto3" json:"actionCode,omitempty"`
	Artworks              []*SeriesArtworkData    `protobuf:"bytes,13,rep,name=artworks,proto3" json:"artworks,omitempty"`
	Auction               *AuctionRequest         `protobuf:"bytes,14,opt,name=auction,proto3" json:"auction,omitempty"`
	AuctionUuid           string                  `protobuf:"bytes,15,opt,name=auctionUuid,proto3" json:"auctionUuid,omitempty"`
	Link                  string                  `protobuf:"bytes,22,opt,name=link,proto3" json:"link,omitempty"`
	SeriesCultureArtworks []*SeriesCultureArtwork `protobuf:"bytes,23,rep,name=seriesCultureArtworks,proto3" json:"seriesCultureArtworks,omitempty"`
	Currency              string                  `protobuf:"bytes,16,opt,name=currency,proto3" json:"currency,omitempty"`
}

func (x *SaveSeriesReq) Reset() {
	*x = SaveSeriesReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_series_series_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SaveSeriesReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SaveSeriesReq) ProtoMessage() {}

func (x *SaveSeriesReq) ProtoReflect() protoreflect.Message {
	mi := &file_api_series_series_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SaveSeriesReq.ProtoReflect.Descriptor instead.
func (*SaveSeriesReq) Descriptor() ([]byte, []int) {
	return file_api_series_series_proto_rawDescGZIP(), []int{18}
}

func (x *SaveSeriesReq) GetBrandId() string {
	if x != nil {
		return x.BrandId
	}
	return ""
}

func (x *SaveSeriesReq) GetBrandName() string {
	if x != nil {
		return x.BrandName
	}
	return ""
}

func (x *SaveSeriesReq) GetCoverImg() string {
	if x != nil {
		return x.CoverImg
	}
	return ""
}

func (x *SaveSeriesReq) GetSeriesName() string {
	if x != nil {
		return x.SeriesName
	}
	return ""
}

func (x *SaveSeriesReq) GetDesc() string {
	if x != nil {
		return x.Desc
	}
	return ""
}

func (x *SaveSeriesReq) GetLang() string {
	if x != nil {
		return x.Lang
	}
	return ""
}

func (x *SaveSeriesReq) GetSeriesUuid() string {
	if x != nil {
		return x.SeriesUuid
	}
	return ""
}

func (x *SaveSeriesReq) GetActionCode() string {
	if x != nil {
		return x.ActionCode
	}
	return ""
}

func (x *SaveSeriesReq) GetArtworks() []*SeriesArtworkData {
	if x != nil {
		return x.Artworks
	}
	return nil
}

func (x *SaveSeriesReq) GetAuction() *AuctionRequest {
	if x != nil {
		return x.Auction
	}
	return nil
}

func (x *SaveSeriesReq) GetAuctionUuid() string {
	if x != nil {
		return x.AuctionUuid
	}
	return ""
}

func (x *SaveSeriesReq) GetLink() string {
	if x != nil {
		return x.Link
	}
	return ""
}

func (x *SaveSeriesReq) GetSeriesCultureArtworks() []*SeriesCultureArtwork {
	if x != nil {
		return x.SeriesCultureArtworks
	}
	return nil
}

func (x *SaveSeriesReq) GetCurrency() string {
	if x != nil {
		return x.Currency
	}
	return ""
}

type SeriesCultureArtwork struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Uuid         string         `protobuf:"bytes,1,opt,name=uuid,proto3" json:"uuid,omitempty"`
	SeriesUuid   string         `protobuf:"bytes,2,opt,name=seriesUuid,proto3" json:"seriesUuid,omitempty"`
	Name         string         `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`
	CultureNum   string         `protobuf:"bytes,4,opt,name=cultureNum,proto3" json:"cultureNum,omitempty"`
	BriefImage   string         `protobuf:"bytes,5,opt,name=briefImage,proto3" json:"briefImage,omitempty"`
	Size         string         `protobuf:"bytes,6,opt,name=size,proto3" json:"size,omitempty"`
	Detail       string         `protobuf:"bytes,7,opt,name=detail,proto3" json:"detail,omitempty"`
	MainType     string         `protobuf:"bytes,8,opt,name=mainType,proto3" json:"mainType,omitempty"`
	MainImgInfo  []*CultureItem `protobuf:"bytes,9,rep,name=mainImgInfo,proto3" json:"mainImgInfo,omitempty"`
	Id           int32          `protobuf:"varint,10,opt,name=id,proto3" json:"id,omitempty"`
	StockNum     uint32         `protobuf:"varint,11,opt,name=stockNum,proto3" json:"stockNum,omitempty"`
	Hash         string         `protobuf:"bytes,12,opt,name=hash,proto3" json:"hash,omitempty"`
	Lang         string         `protobuf:"bytes,13,opt,name=lang,proto3" json:"lang,omitempty"`
	Price        string         `protobuf:"bytes,14,opt,name=price,proto3" json:"price,omitempty"`
	LeftNum      uint32         `protobuf:"varint,15,opt,name=leftNum,proto3" json:"leftNum,omitempty"`
	SoldNum      uint32         `protobuf:"varint,16,opt,name=soldNum,proto3" json:"soldNum,omitempty"`
	Currency     string         `protobuf:"bytes,17,opt,name=currency,proto3" json:"currency,omitempty"`
	IsReceived   uint32         `protobuf:"varint,18,opt,name=isReceived,proto3" json:"isReceived,omitempty"` //当num请求不为空的时候生效，是否被领取 0-num没传递， 1-被领取 2-没领取
	CultureItem  *CultureItem   `protobuf:"bytes,19,opt,name=cultureItem,proto3" json:"cultureItem,omitempty"`
	BrandAddress string         `protobuf:"bytes,20,opt,name=brandAddress,proto3" json:"brandAddress,omitempty"`
	ModelType    string         `protobuf:"bytes,21,opt,name=modelType,proto3" json:"modelType,omitempty"`
}

func (x *SeriesCultureArtwork) Reset() {
	*x = SeriesCultureArtwork{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_series_series_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SeriesCultureArtwork) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SeriesCultureArtwork) ProtoMessage() {}

func (x *SeriesCultureArtwork) ProtoReflect() protoreflect.Message {
	mi := &file_api_series_series_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SeriesCultureArtwork.ProtoReflect.Descriptor instead.
func (*SeriesCultureArtwork) Descriptor() ([]byte, []int) {
	return file_api_series_series_proto_rawDescGZIP(), []int{19}
}

func (x *SeriesCultureArtwork) GetUuid() string {
	if x != nil {
		return x.Uuid
	}
	return ""
}

func (x *SeriesCultureArtwork) GetSeriesUuid() string {
	if x != nil {
		return x.SeriesUuid
	}
	return ""
}

func (x *SeriesCultureArtwork) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *SeriesCultureArtwork) GetCultureNum() string {
	if x != nil {
		return x.CultureNum
	}
	return ""
}

func (x *SeriesCultureArtwork) GetBriefImage() string {
	if x != nil {
		return x.BriefImage
	}
	return ""
}

func (x *SeriesCultureArtwork) GetSize() string {
	if x != nil {
		return x.Size
	}
	return ""
}

func (x *SeriesCultureArtwork) GetDetail() string {
	if x != nil {
		return x.Detail
	}
	return ""
}

func (x *SeriesCultureArtwork) GetMainType() string {
	if x != nil {
		return x.MainType
	}
	return ""
}

func (x *SeriesCultureArtwork) GetMainImgInfo() []*CultureItem {
	if x != nil {
		return x.MainImgInfo
	}
	return nil
}

func (x *SeriesCultureArtwork) GetId() int32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *SeriesCultureArtwork) GetStockNum() uint32 {
	if x != nil {
		return x.StockNum
	}
	return 0
}

func (x *SeriesCultureArtwork) GetHash() string {
	if x != nil {
		return x.Hash
	}
	return ""
}

func (x *SeriesCultureArtwork) GetLang() string {
	if x != nil {
		return x.Lang
	}
	return ""
}

func (x *SeriesCultureArtwork) GetPrice() string {
	if x != nil {
		return x.Price
	}
	return ""
}

func (x *SeriesCultureArtwork) GetLeftNum() uint32 {
	if x != nil {
		return x.LeftNum
	}
	return 0
}

func (x *SeriesCultureArtwork) GetSoldNum() uint32 {
	if x != nil {
		return x.SoldNum
	}
	return 0
}

func (x *SeriesCultureArtwork) GetCurrency() string {
	if x != nil {
		return x.Currency
	}
	return ""
}

func (x *SeriesCultureArtwork) GetIsReceived() uint32 {
	if x != nil {
		return x.IsReceived
	}
	return 0
}

func (x *SeriesCultureArtwork) GetCultureItem() *CultureItem {
	if x != nil {
		return x.CultureItem
	}
	return nil
}

func (x *SeriesCultureArtwork) GetBrandAddress() string {
	if x != nil {
		return x.BrandAddress
	}
	return ""
}

func (x *SeriesCultureArtwork) GetModelType() string {
	if x != nil {
		return x.ModelType
	}
	return ""
}

type AuctionRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Address              string `protobuf:"bytes,1,opt,name=address,proto3" json:"address,omitempty"`
	Image                string `protobuf:"bytes,5,opt,name=image,proto3" json:"image,omitempty"`
	Info                 string `protobuf:"bytes,6,opt,name=info,proto3" json:"info,omitempty"`
	StartDate            string `protobuf:"bytes,7,opt,name=startDate,proto3" json:"startDate,omitempty"`
	Title                string `protobuf:"bytes,8,opt,name=title,proto3" json:"title,omitempty"`
	StartTitle           string `protobuf:"bytes,16,opt,name=startTitle,proto3" json:"startTitle,omitempty"`
	Lang                 string `protobuf:"bytes,18,opt,name=lang,proto3" json:"lang,omitempty"` //上边是多语言
	CreatedAt            string `protobuf:"bytes,2,opt,name=createdAt,proto3" json:"createdAt,omitempty"`
	DeletedAt            uint32 `protobuf:"varint,3,opt,name=deletedAt,proto3" json:"deletedAt,omitempty"`
	ID                   uint32 `protobuf:"varint,4,opt,name=ID,proto3" json:"ID,omitempty"`
	UpdatedAt            string `protobuf:"bytes,9,opt,name=updatedAt,proto3" json:"updatedAt,omitempty"`
	Uuid                 string `protobuf:"bytes,10,opt,name=uuid,proto3" json:"uuid,omitempty"`
	TotalNum             uint32 `protobuf:"varint,11,opt,name=totalNum,proto3" json:"totalNum,omitempty"`
	IsLiving             uint32 `protobuf:"varint,13,opt,name=isLiving,proto3" json:"isLiving,omitempty"`
	IsExistLivingArtwork bool   `protobuf:"varint,15,opt,name=isExistLivingArtwork,proto3" json:"isExistLivingArtwork,omitempty"` //是否存在正在拍卖的作品
	OperatorName         string `protobuf:"bytes,17,opt,name=operatorName,proto3" json:"operatorName,omitempty"`
}

func (x *AuctionRequest) Reset() {
	*x = AuctionRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_series_series_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AuctionRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AuctionRequest) ProtoMessage() {}

func (x *AuctionRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_series_series_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AuctionRequest.ProtoReflect.Descriptor instead.
func (*AuctionRequest) Descriptor() ([]byte, []int) {
	return file_api_series_series_proto_rawDescGZIP(), []int{20}
}

func (x *AuctionRequest) GetAddress() string {
	if x != nil {
		return x.Address
	}
	return ""
}

func (x *AuctionRequest) GetImage() string {
	if x != nil {
		return x.Image
	}
	return ""
}

func (x *AuctionRequest) GetInfo() string {
	if x != nil {
		return x.Info
	}
	return ""
}

func (x *AuctionRequest) GetStartDate() string {
	if x != nil {
		return x.StartDate
	}
	return ""
}

func (x *AuctionRequest) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

func (x *AuctionRequest) GetStartTitle() string {
	if x != nil {
		return x.StartTitle
	}
	return ""
}

func (x *AuctionRequest) GetLang() string {
	if x != nil {
		return x.Lang
	}
	return ""
}

func (x *AuctionRequest) GetCreatedAt() string {
	if x != nil {
		return x.CreatedAt
	}
	return ""
}

func (x *AuctionRequest) GetDeletedAt() uint32 {
	if x != nil {
		return x.DeletedAt
	}
	return 0
}

func (x *AuctionRequest) GetID() uint32 {
	if x != nil {
		return x.ID
	}
	return 0
}

func (x *AuctionRequest) GetUpdatedAt() string {
	if x != nil {
		return x.UpdatedAt
	}
	return ""
}

func (x *AuctionRequest) GetUuid() string {
	if x != nil {
		return x.Uuid
	}
	return ""
}

func (x *AuctionRequest) GetTotalNum() uint32 {
	if x != nil {
		return x.TotalNum
	}
	return 0
}

func (x *AuctionRequest) GetIsLiving() uint32 {
	if x != nil {
		return x.IsLiving
	}
	return 0
}

func (x *AuctionRequest) GetIsExistLivingArtwork() bool {
	if x != nil {
		return x.IsExistLivingArtwork
	}
	return false
}

func (x *AuctionRequest) GetOperatorName() string {
	if x != nil {
		return x.OperatorName
	}
	return ""
}

type SaveSeriesResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Msg  string `protobuf:"bytes,1,opt,name=msg,proto3" json:"msg,omitempty"`
	Uuid string `protobuf:"bytes,2,opt,name=uuid,proto3" json:"uuid,omitempty"`
}

func (x *SaveSeriesResp) Reset() {
	*x = SaveSeriesResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_series_series_proto_msgTypes[21]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SaveSeriesResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SaveSeriesResp) ProtoMessage() {}

func (x *SaveSeriesResp) ProtoReflect() protoreflect.Message {
	mi := &file_api_series_series_proto_msgTypes[21]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SaveSeriesResp.ProtoReflect.Descriptor instead.
func (*SaveSeriesResp) Descriptor() ([]byte, []int) {
	return file_api_series_series_proto_rawDescGZIP(), []int{21}
}

func (x *SaveSeriesResp) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *SaveSeriesResp) GetUuid() string {
	if x != nil {
		return x.Uuid
	}
	return ""
}

// SeriesDetail
type SeriesDetailReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	SeriesUuid string   `protobuf:"bytes,1,opt,name=seriesUuid,proto3" json:"seriesUuid,omitempty"`
	Num        int32    `protobuf:"varint,2,opt,name=num,proto3" json:"num,omitempty"`
	Lang       string   `protobuf:"bytes,4,opt,name=lang,proto3" json:"lang,omitempty"`
	Langs      []string `protobuf:"bytes,5,rep,name=langs,proto3" json:"langs,omitempty"`
	CompanyId  uint32   `protobuf:"varint,6,opt,name=companyId,proto3" json:"companyId,omitempty"`
}

func (x *SeriesDetailReq) Reset() {
	*x = SeriesDetailReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_series_series_proto_msgTypes[22]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SeriesDetailReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SeriesDetailReq) ProtoMessage() {}

func (x *SeriesDetailReq) ProtoReflect() protoreflect.Message {
	mi := &file_api_series_series_proto_msgTypes[22]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SeriesDetailReq.ProtoReflect.Descriptor instead.
func (*SeriesDetailReq) Descriptor() ([]byte, []int) {
	return file_api_series_series_proto_rawDescGZIP(), []int{22}
}

func (x *SeriesDetailReq) GetSeriesUuid() string {
	if x != nil {
		return x.SeriesUuid
	}
	return ""
}

func (x *SeriesDetailReq) GetNum() int32 {
	if x != nil {
		return x.Num
	}
	return 0
}

func (x *SeriesDetailReq) GetLang() string {
	if x != nil {
		return x.Lang
	}
	return ""
}

func (x *SeriesDetailReq) GetLangs() []string {
	if x != nil {
		return x.Langs
	}
	return nil
}

func (x *SeriesDetailReq) GetCompanyId() uint32 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

type CultureArtworkListResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	List  []*SeriesCultureArtwork `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`
	Count int64                   `protobuf:"varint,2,opt,name=count,proto3" json:"count,omitempty"`
}

func (x *CultureArtworkListResp) Reset() {
	*x = CultureArtworkListResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_series_series_proto_msgTypes[23]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CultureArtworkListResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CultureArtworkListResp) ProtoMessage() {}

func (x *CultureArtworkListResp) ProtoReflect() protoreflect.Message {
	mi := &file_api_series_series_proto_msgTypes[23]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CultureArtworkListResp.ProtoReflect.Descriptor instead.
func (*CultureArtworkListResp) Descriptor() ([]byte, []int) {
	return file_api_series_series_proto_rawDescGZIP(), []int{23}
}

func (x *CultureArtworkListResp) GetList() []*SeriesCultureArtwork {
	if x != nil {
		return x.List
	}
	return nil
}

func (x *CultureArtworkListResp) GetCount() int64 {
	if x != nil {
		return x.Count
	}
	return 0
}

type CultureArtworkOrderListRes struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	List  []*CultureArtworkOrder `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`
	Count int64                  `protobuf:"varint,2,opt,name=count,proto3" json:"count,omitempty"`
}

func (x *CultureArtworkOrderListRes) Reset() {
	*x = CultureArtworkOrderListRes{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_series_series_proto_msgTypes[24]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CultureArtworkOrderListRes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CultureArtworkOrderListRes) ProtoMessage() {}

func (x *CultureArtworkOrderListRes) ProtoReflect() protoreflect.Message {
	mi := &file_api_series_series_proto_msgTypes[24]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CultureArtworkOrderListRes.ProtoReflect.Descriptor instead.
func (*CultureArtworkOrderListRes) Descriptor() ([]byte, []int) {
	return file_api_series_series_proto_rawDescGZIP(), []int{24}
}

func (x *CultureArtworkOrderListRes) GetList() []*CultureArtworkOrder {
	if x != nil {
		return x.List
	}
	return nil
}

func (x *CultureArtworkOrderListRes) GetCount() int64 {
	if x != nil {
		return x.Count
	}
	return 0
}

type CultureArtworkOrder struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Uuid                      string       `protobuf:"bytes,1,opt,name=uuid,proto3" json:"uuid,omitempty"`
	SeriesUuid                string       `protobuf:"bytes,2,opt,name=seriesUuid,proto3" json:"seriesUuid,omitempty"`
	Name                      string       `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`
	Hash                      string       `protobuf:"bytes,4,opt,name=hash,proto3" json:"hash,omitempty"`
	TelNum                    string       `protobuf:"bytes,5,opt,name=telNum,proto3" json:"telNum,omitempty"`
	CreatedAt                 string       `protobuf:"bytes,6,opt,name=createdAt,proto3" json:"createdAt,omitempty"`
	BriefImg                  string       `protobuf:"bytes,7,opt,name=briefImg,proto3" json:"briefImg,omitempty"`
	Status                    uint32       `protobuf:"varint,8,opt,name=status,proto3" json:"status,omitempty"`
	BaseStatus                uint32       `protobuf:"varint,9,opt,name=baseStatus,proto3" json:"baseStatus,omitempty"` //1-无标志 2-转赠中 3-接受中
	NowUserId                 uint32       `protobuf:"varint,18,opt,name=nowUserId,proto3" json:"nowUserId,omitempty"`
	NowUserName               string       `protobuf:"bytes,19,opt,name=nowUserName,proto3" json:"nowUserName,omitempty"`
	TransferTime              string       `protobuf:"bytes,10,opt,name=transferTime,proto3" json:"transferTime,omitempty"`
	TradeTime                 string       `protobuf:"bytes,11,opt,name=tradeTime,proto3" json:"tradeTime,omitempty"`
	ReceiveTime               string       `protobuf:"bytes,12,opt,name=receiveTime,proto3" json:"receiveTime,omitempty"`
	BrandName                 string       `protobuf:"bytes,13,opt,name=brandName,proto3" json:"brandName,omitempty"`
	SeriesCulturalArtworkUuid string       `protobuf:"bytes,14,opt,name=seriesCulturalArtworkUuid,proto3" json:"seriesCulturalArtworkUuid,omitempty"`
	IndexNum                  uint32       `protobuf:"varint,15,opt,name=indexNum,proto3" json:"indexNum,omitempty"`
	BrandAddress              string       `protobuf:"bytes,16,opt,name=brandAddress,proto3" json:"brandAddress,omitempty"`
	CultureItem               *CultureItem `protobuf:"bytes,17,opt,name=cultureItem,proto3" json:"cultureItem,omitempty"`
	ModelType                 string       `protobuf:"bytes,21,opt,name=modelType,proto3" json:"modelType,omitempty"`
	PayStatus                 uint32       `protobuf:"varint,22,opt,name=payStatus,proto3" json:"payStatus,omitempty"`
	PayTime                   string       `protobuf:"bytes,23,opt,name=payTime,proto3" json:"payTime,omitempty"`
	PayPprofList              []string     `protobuf:"bytes,24,rep,name=payPprofList,proto3" json:"payPprofList,omitempty"`
	Currency                  string       `protobuf:"bytes,25,opt,name=currency,proto3" json:"currency,omitempty"`
	PayPrice                  string       `protobuf:"bytes,26,opt,name=payPrice,proto3" json:"payPrice,omitempty"`
	OperatorId                uint32       `protobuf:"varint,27,opt,name=operatorId,proto3" json:"operatorId,omitempty"`
	OperatorTelNum            string       `protobuf:"bytes,28,opt,name=operatorTelNum,proto3" json:"operatorTelNum,omitempty"`
	SessionId                 string       `protobuf:"bytes,29,opt,name=sessionId,proto3" json:"sessionId,omitempty"`
	Remark                    string       `protobuf:"bytes,30,opt,name=remark,proto3" json:"remark,omitempty"`
	PayType                   uint32       `protobuf:"varint,31,opt,name=payType,proto3" json:"payType,omitempty"`
}

func (x *CultureArtworkOrder) Reset() {
	*x = CultureArtworkOrder{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_series_series_proto_msgTypes[25]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CultureArtworkOrder) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CultureArtworkOrder) ProtoMessage() {}

func (x *CultureArtworkOrder) ProtoReflect() protoreflect.Message {
	mi := &file_api_series_series_proto_msgTypes[25]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CultureArtworkOrder.ProtoReflect.Descriptor instead.
func (*CultureArtworkOrder) Descriptor() ([]byte, []int) {
	return file_api_series_series_proto_rawDescGZIP(), []int{25}
}

func (x *CultureArtworkOrder) GetUuid() string {
	if x != nil {
		return x.Uuid
	}
	return ""
}

func (x *CultureArtworkOrder) GetSeriesUuid() string {
	if x != nil {
		return x.SeriesUuid
	}
	return ""
}

func (x *CultureArtworkOrder) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *CultureArtworkOrder) GetHash() string {
	if x != nil {
		return x.Hash
	}
	return ""
}

func (x *CultureArtworkOrder) GetTelNum() string {
	if x != nil {
		return x.TelNum
	}
	return ""
}

func (x *CultureArtworkOrder) GetCreatedAt() string {
	if x != nil {
		return x.CreatedAt
	}
	return ""
}

func (x *CultureArtworkOrder) GetBriefImg() string {
	if x != nil {
		return x.BriefImg
	}
	return ""
}

func (x *CultureArtworkOrder) GetStatus() uint32 {
	if x != nil {
		return x.Status
	}
	return 0
}

func (x *CultureArtworkOrder) GetBaseStatus() uint32 {
	if x != nil {
		return x.BaseStatus
	}
	return 0
}

func (x *CultureArtworkOrder) GetNowUserId() uint32 {
	if x != nil {
		return x.NowUserId
	}
	return 0
}

func (x *CultureArtworkOrder) GetNowUserName() string {
	if x != nil {
		return x.NowUserName
	}
	return ""
}

func (x *CultureArtworkOrder) GetTransferTime() string {
	if x != nil {
		return x.TransferTime
	}
	return ""
}

func (x *CultureArtworkOrder) GetTradeTime() string {
	if x != nil {
		return x.TradeTime
	}
	return ""
}

func (x *CultureArtworkOrder) GetReceiveTime() string {
	if x != nil {
		return x.ReceiveTime
	}
	return ""
}

func (x *CultureArtworkOrder) GetBrandName() string {
	if x != nil {
		return x.BrandName
	}
	return ""
}

func (x *CultureArtworkOrder) GetSeriesCulturalArtworkUuid() string {
	if x != nil {
		return x.SeriesCulturalArtworkUuid
	}
	return ""
}

func (x *CultureArtworkOrder) GetIndexNum() uint32 {
	if x != nil {
		return x.IndexNum
	}
	return 0
}

func (x *CultureArtworkOrder) GetBrandAddress() string {
	if x != nil {
		return x.BrandAddress
	}
	return ""
}

func (x *CultureArtworkOrder) GetCultureItem() *CultureItem {
	if x != nil {
		return x.CultureItem
	}
	return nil
}

func (x *CultureArtworkOrder) GetModelType() string {
	if x != nil {
		return x.ModelType
	}
	return ""
}

func (x *CultureArtworkOrder) GetPayStatus() uint32 {
	if x != nil {
		return x.PayStatus
	}
	return 0
}

func (x *CultureArtworkOrder) GetPayTime() string {
	if x != nil {
		return x.PayTime
	}
	return ""
}

func (x *CultureArtworkOrder) GetPayPprofList() []string {
	if x != nil {
		return x.PayPprofList
	}
	return nil
}

func (x *CultureArtworkOrder) GetCurrency() string {
	if x != nil {
		return x.Currency
	}
	return ""
}

func (x *CultureArtworkOrder) GetPayPrice() string {
	if x != nil {
		return x.PayPrice
	}
	return ""
}

func (x *CultureArtworkOrder) GetOperatorId() uint32 {
	if x != nil {
		return x.OperatorId
	}
	return 0
}

func (x *CultureArtworkOrder) GetOperatorTelNum() string {
	if x != nil {
		return x.OperatorTelNum
	}
	return ""
}

func (x *CultureArtworkOrder) GetSessionId() string {
	if x != nil {
		return x.SessionId
	}
	return ""
}

func (x *CultureArtworkOrder) GetRemark() string {
	if x != nil {
		return x.Remark
	}
	return ""
}

func (x *CultureArtworkOrder) GetPayType() uint32 {
	if x != nil {
		return x.PayType
	}
	return 0
}

type SeriesDetailResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	BrandId               string                             `protobuf:"bytes,1,opt,name=brandId,proto3" json:"brandId,omitempty"`
	BrandName             string                             `protobuf:"bytes,2,opt,name=brandName,proto3" json:"brandName,omitempty"`
	LaunchStartTime       string                             `protobuf:"bytes,3,opt,name=launchStartTime,proto3" json:"launchStartTime,omitempty"`
	LaunchEndTime         string                             `protobuf:"bytes,4,opt,name=launchEndTime,proto3" json:"launchEndTime,omitempty"`
	ShelfState            int32                              `protobuf:"varint,5,opt,name=shelfState,proto3" json:"shelfState,omitempty"` //1=上架 2=下架
	ActionTime            string                             `protobuf:"bytes,6,opt,name=actionTime,proto3" json:"actionTime,omitempty"`
	CoverImg              string                             `protobuf:"bytes,7,opt,name=coverImg,proto3" json:"coverImg,omitempty"`
	CreateTime            string                             `protobuf:"bytes,8,opt,name=createTime,proto3" json:"createTime,omitempty"`
	SeriesUuid            string                             `protobuf:"bytes,9,opt,name=seriesUuid,proto3" json:"seriesUuid,omitempty"`
	SeriesLang            []*SeriesDetailResp_SeriesLangInfo `protobuf:"bytes,10,rep,name=seriesLang,proto3" json:"seriesLang,omitempty"`
	Msg                   string                             `protobuf:"bytes,11,opt,name=msg,proto3" json:"msg,omitempty"`
	ActionCode            string                             `protobuf:"bytes,12,opt,name=actionCode,proto3" json:"actionCode,omitempty"`
	Status                int32                              `protobuf:"varint,13,opt,name=status,proto3" json:"status,omitempty"`
	SeriesName            string                             `protobuf:"bytes,14,opt,name=seriesName,proto3" json:"seriesName,omitempty"`
	Desc                  string                             `protobuf:"bytes,15,opt,name=desc,proto3" json:"desc,omitempty"`
	Lang                  string                             `protobuf:"bytes,16,opt,name=lang,proto3" json:"lang,omitempty"`
	OnShelfTime           string                             `protobuf:"bytes,18,opt,name=onShelfTime,proto3" json:"onShelfTime,omitempty"`
	AutoShelf             int32                              `protobuf:"varint,19,opt,name=autoShelf,proto3" json:"autoShelf,omitempty"` //自动上下架 1=禁用 2=启用
	Artworks              []*SeriesArtworkData               `protobuf:"bytes,20,rep,name=artworks,proto3" json:"artworks,omitempty"`
	AuctionUuid           string                             `protobuf:"bytes,21,opt,name=auctionUuid,proto3" json:"auctionUuid,omitempty"`
	Link                  string                             `protobuf:"bytes,22,opt,name=link,proto3" json:"link,omitempty"`
	Auction               *AuctionRequest                    `protobuf:"bytes,23,opt,name=auction,proto3" json:"auction,omitempty"`
	SeriesCultureArtworks []*SeriesCultureArtwork            `protobuf:"bytes,24,rep,name=seriesCultureArtworks,proto3" json:"seriesCultureArtworks,omitempty"`
	Currency              string                             `protobuf:"bytes,25,opt,name=currency,proto3" json:"currency,omitempty"`
}

func (x *SeriesDetailResp) Reset() {
	*x = SeriesDetailResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_series_series_proto_msgTypes[26]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SeriesDetailResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SeriesDetailResp) ProtoMessage() {}

func (x *SeriesDetailResp) ProtoReflect() protoreflect.Message {
	mi := &file_api_series_series_proto_msgTypes[26]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SeriesDetailResp.ProtoReflect.Descriptor instead.
func (*SeriesDetailResp) Descriptor() ([]byte, []int) {
	return file_api_series_series_proto_rawDescGZIP(), []int{26}
}

func (x *SeriesDetailResp) GetBrandId() string {
	if x != nil {
		return x.BrandId
	}
	return ""
}

func (x *SeriesDetailResp) GetBrandName() string {
	if x != nil {
		return x.BrandName
	}
	return ""
}

func (x *SeriesDetailResp) GetLaunchStartTime() string {
	if x != nil {
		return x.LaunchStartTime
	}
	return ""
}

func (x *SeriesDetailResp) GetLaunchEndTime() string {
	if x != nil {
		return x.LaunchEndTime
	}
	return ""
}

func (x *SeriesDetailResp) GetShelfState() int32 {
	if x != nil {
		return x.ShelfState
	}
	return 0
}

func (x *SeriesDetailResp) GetActionTime() string {
	if x != nil {
		return x.ActionTime
	}
	return ""
}

func (x *SeriesDetailResp) GetCoverImg() string {
	if x != nil {
		return x.CoverImg
	}
	return ""
}

func (x *SeriesDetailResp) GetCreateTime() string {
	if x != nil {
		return x.CreateTime
	}
	return ""
}

func (x *SeriesDetailResp) GetSeriesUuid() string {
	if x != nil {
		return x.SeriesUuid
	}
	return ""
}

func (x *SeriesDetailResp) GetSeriesLang() []*SeriesDetailResp_SeriesLangInfo {
	if x != nil {
		return x.SeriesLang
	}
	return nil
}

func (x *SeriesDetailResp) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *SeriesDetailResp) GetActionCode() string {
	if x != nil {
		return x.ActionCode
	}
	return ""
}

func (x *SeriesDetailResp) GetStatus() int32 {
	if x != nil {
		return x.Status
	}
	return 0
}

func (x *SeriesDetailResp) GetSeriesName() string {
	if x != nil {
		return x.SeriesName
	}
	return ""
}

func (x *SeriesDetailResp) GetDesc() string {
	if x != nil {
		return x.Desc
	}
	return ""
}

func (x *SeriesDetailResp) GetLang() string {
	if x != nil {
		return x.Lang
	}
	return ""
}

func (x *SeriesDetailResp) GetOnShelfTime() string {
	if x != nil {
		return x.OnShelfTime
	}
	return ""
}

func (x *SeriesDetailResp) GetAutoShelf() int32 {
	if x != nil {
		return x.AutoShelf
	}
	return 0
}

func (x *SeriesDetailResp) GetArtworks() []*SeriesArtworkData {
	if x != nil {
		return x.Artworks
	}
	return nil
}

func (x *SeriesDetailResp) GetAuctionUuid() string {
	if x != nil {
		return x.AuctionUuid
	}
	return ""
}

func (x *SeriesDetailResp) GetLink() string {
	if x != nil {
		return x.Link
	}
	return ""
}

func (x *SeriesDetailResp) GetAuction() *AuctionRequest {
	if x != nil {
		return x.Auction
	}
	return nil
}

func (x *SeriesDetailResp) GetSeriesCultureArtworks() []*SeriesCultureArtwork {
	if x != nil {
		return x.SeriesCultureArtworks
	}
	return nil
}

func (x *SeriesDetailResp) GetCurrency() string {
	if x != nil {
		return x.Currency
	}
	return ""
}

// UpdateSeriesLang
type UpdateSeriesLangReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	SeriesUuid string `protobuf:"bytes,1,opt,name=seriesUuid,proto3" json:"seriesUuid,omitempty"`
	SeriesName string `protobuf:"bytes,3,opt,name=seriesName,proto3" json:"seriesName,omitempty"`
	Desc       string `protobuf:"bytes,4,opt,name=desc,proto3" json:"desc,omitempty"`
	Lang       string `protobuf:"bytes,5,opt,name=lang,proto3" json:"lang,omitempty"`
	LangId     int32  `protobuf:"varint,6,opt,name=langId,proto3" json:"langId,omitempty"`
}

func (x *UpdateSeriesLangReq) Reset() {
	*x = UpdateSeriesLangReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_series_series_proto_msgTypes[27]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateSeriesLangReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateSeriesLangReq) ProtoMessage() {}

func (x *UpdateSeriesLangReq) ProtoReflect() protoreflect.Message {
	mi := &file_api_series_series_proto_msgTypes[27]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateSeriesLangReq.ProtoReflect.Descriptor instead.
func (*UpdateSeriesLangReq) Descriptor() ([]byte, []int) {
	return file_api_series_series_proto_rawDescGZIP(), []int{27}
}

func (x *UpdateSeriesLangReq) GetSeriesUuid() string {
	if x != nil {
		return x.SeriesUuid
	}
	return ""
}

func (x *UpdateSeriesLangReq) GetSeriesName() string {
	if x != nil {
		return x.SeriesName
	}
	return ""
}

func (x *UpdateSeriesLangReq) GetDesc() string {
	if x != nil {
		return x.Desc
	}
	return ""
}

func (x *UpdateSeriesLangReq) GetLang() string {
	if x != nil {
		return x.Lang
	}
	return ""
}

func (x *UpdateSeriesLangReq) GetLangId() int32 {
	if x != nil {
		return x.LangId
	}
	return 0
}

type UpdateSeriesLangResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Msg string `protobuf:"bytes,1,opt,name=msg,proto3" json:"msg,omitempty"`
}

func (x *UpdateSeriesLangResp) Reset() {
	*x = UpdateSeriesLangResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_series_series_proto_msgTypes[28]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateSeriesLangResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateSeriesLangResp) ProtoMessage() {}

func (x *UpdateSeriesLangResp) ProtoReflect() protoreflect.Message {
	mi := &file_api_series_series_proto_msgTypes[28]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateSeriesLangResp.ProtoReflect.Descriptor instead.
func (*UpdateSeriesLangResp) Descriptor() ([]byte, []int) {
	return file_api_series_series_proto_rawDescGZIP(), []int{28}
}

func (x *UpdateSeriesLangResp) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

// AutoShelfReq
type AutoShelfReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	SeriesUuid      string `protobuf:"bytes,1,opt,name=seriesUuid,proto3" json:"seriesUuid,omitempty"`
	LaunchStartTime string `protobuf:"bytes,3,opt,name=launchStartTime,proto3" json:"launchStartTime,omitempty"`
	LaunchEndTime   string `protobuf:"bytes,4,opt,name=launchEndTime,proto3" json:"launchEndTime,omitempty"`
}

func (x *AutoShelfReq) Reset() {
	*x = AutoShelfReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_series_series_proto_msgTypes[29]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AutoShelfReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AutoShelfReq) ProtoMessage() {}

func (x *AutoShelfReq) ProtoReflect() protoreflect.Message {
	mi := &file_api_series_series_proto_msgTypes[29]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AutoShelfReq.ProtoReflect.Descriptor instead.
func (*AutoShelfReq) Descriptor() ([]byte, []int) {
	return file_api_series_series_proto_rawDescGZIP(), []int{29}
}

func (x *AutoShelfReq) GetSeriesUuid() string {
	if x != nil {
		return x.SeriesUuid
	}
	return ""
}

func (x *AutoShelfReq) GetLaunchStartTime() string {
	if x != nil {
		return x.LaunchStartTime
	}
	return ""
}

func (x *AutoShelfReq) GetLaunchEndTime() string {
	if x != nil {
		return x.LaunchEndTime
	}
	return ""
}

type AutoShelfResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Msg string `protobuf:"bytes,1,opt,name=msg,proto3" json:"msg,omitempty"`
}

func (x *AutoShelfResp) Reset() {
	*x = AutoShelfResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_series_series_proto_msgTypes[30]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AutoShelfResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AutoShelfResp) ProtoMessage() {}

func (x *AutoShelfResp) ProtoReflect() protoreflect.Message {
	mi := &file_api_series_series_proto_msgTypes[30]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AutoShelfResp.ProtoReflect.Descriptor instead.
func (*AutoShelfResp) Descriptor() ([]byte, []int) {
	return file_api_series_series_proto_rawDescGZIP(), []int{30}
}

func (x *AutoShelfResp) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

type HandShelfReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	SeriesUuid string `protobuf:"bytes,1,opt,name=seriesUuid,proto3" json:"seriesUuid,omitempty"`
	ShelfState int32  `protobuf:"varint,2,opt,name=shelfState,proto3" json:"shelfState,omitempty"` //1=上架 2=下架
}

func (x *HandShelfReq) Reset() {
	*x = HandShelfReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_series_series_proto_msgTypes[31]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *HandShelfReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HandShelfReq) ProtoMessage() {}

func (x *HandShelfReq) ProtoReflect() protoreflect.Message {
	mi := &file_api_series_series_proto_msgTypes[31]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HandShelfReq.ProtoReflect.Descriptor instead.
func (*HandShelfReq) Descriptor() ([]byte, []int) {
	return file_api_series_series_proto_rawDescGZIP(), []int{31}
}

func (x *HandShelfReq) GetSeriesUuid() string {
	if x != nil {
		return x.SeriesUuid
	}
	return ""
}

func (x *HandShelfReq) GetShelfState() int32 {
	if x != nil {
		return x.ShelfState
	}
	return 0
}

type HandShelfResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	SeriesUuid string `protobuf:"bytes,1,opt,name=seriesUuid,proto3" json:"seriesUuid,omitempty"`
	BrandUuid  string `protobuf:"bytes,2,opt,name=brandUuid,proto3" json:"brandUuid,omitempty"`
	Msg        string `protobuf:"bytes,3,opt,name=msg,proto3" json:"msg,omitempty"`
}

func (x *HandShelfResp) Reset() {
	*x = HandShelfResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_series_series_proto_msgTypes[32]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *HandShelfResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HandShelfResp) ProtoMessage() {}

func (x *HandShelfResp) ProtoReflect() protoreflect.Message {
	mi := &file_api_series_series_proto_msgTypes[32]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HandShelfResp.ProtoReflect.Descriptor instead.
func (*HandShelfResp) Descriptor() ([]byte, []int) {
	return file_api_series_series_proto_rawDescGZIP(), []int{32}
}

func (x *HandShelfResp) GetSeriesUuid() string {
	if x != nil {
		return x.SeriesUuid
	}
	return ""
}

func (x *HandShelfResp) GetBrandUuid() string {
	if x != nil {
		return x.BrandUuid
	}
	return ""
}

func (x *HandShelfResp) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

// SeriesList
type SeriesListReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Keyword  string   `protobuf:"bytes,1,opt,name=keyword,proto3" json:"keyword,omitempty"`
	Page     int32    `protobuf:"varint,2,opt,name=page,proto3" json:"page,omitempty"`
	PageSize int32    `protobuf:"varint,3,opt,name=pageSize,proto3" json:"pageSize,omitempty"`
	Lang     string   `protobuf:"bytes,4,opt,name=lang,proto3" json:"lang,omitempty"`
	Langs    []string `protobuf:"bytes,5,rep,name=langs,proto3" json:"langs,omitempty"`
}

func (x *SeriesListReq) Reset() {
	*x = SeriesListReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_series_series_proto_msgTypes[33]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SeriesListReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SeriesListReq) ProtoMessage() {}

func (x *SeriesListReq) ProtoReflect() protoreflect.Message {
	mi := &file_api_series_series_proto_msgTypes[33]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SeriesListReq.ProtoReflect.Descriptor instead.
func (*SeriesListReq) Descriptor() ([]byte, []int) {
	return file_api_series_series_proto_rawDescGZIP(), []int{33}
}

func (x *SeriesListReq) GetKeyword() string {
	if x != nil {
		return x.Keyword
	}
	return ""
}

func (x *SeriesListReq) GetPage() int32 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *SeriesListReq) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *SeriesListReq) GetLang() string {
	if x != nil {
		return x.Lang
	}
	return ""
}

func (x *SeriesListReq) GetLangs() []string {
	if x != nil {
		return x.Langs
	}
	return nil
}

type SeriesListResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Data       []*SeriesListResp_Info `protobuf:"bytes,1,rep,name=data,proto3" json:"data,omitempty"`
	Count      int32                  `protobuf:"varint,2,opt,name=count,proto3" json:"count,omitempty"`
	SystemTime int32                  `protobuf:"varint,3,opt,name=systemTime,proto3" json:"systemTime,omitempty"`
	Msg        string                 `protobuf:"bytes,4,opt,name=msg,proto3" json:"msg,omitempty"`
}

func (x *SeriesListResp) Reset() {
	*x = SeriesListResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_series_series_proto_msgTypes[34]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SeriesListResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SeriesListResp) ProtoMessage() {}

func (x *SeriesListResp) ProtoReflect() protoreflect.Message {
	mi := &file_api_series_series_proto_msgTypes[34]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SeriesListResp.ProtoReflect.Descriptor instead.
func (*SeriesListResp) Descriptor() ([]byte, []int) {
	return file_api_series_series_proto_rawDescGZIP(), []int{34}
}

func (x *SeriesListResp) GetData() []*SeriesListResp_Info {
	if x != nil {
		return x.Data
	}
	return nil
}

func (x *SeriesListResp) GetCount() int32 {
	if x != nil {
		return x.Count
	}
	return 0
}

func (x *SeriesListResp) GetSystemTime() int32 {
	if x != nil {
		return x.SystemTime
	}
	return 0
}

func (x *SeriesListResp) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

// SeriesDelReq
type SeriesDelReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	SeriesUuid string `protobuf:"bytes,1,opt,name=seriesUuid,proto3" json:"seriesUuid,omitempty"`
}

func (x *SeriesDelReq) Reset() {
	*x = SeriesDelReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_series_series_proto_msgTypes[35]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SeriesDelReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SeriesDelReq) ProtoMessage() {}

func (x *SeriesDelReq) ProtoReflect() protoreflect.Message {
	mi := &file_api_series_series_proto_msgTypes[35]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SeriesDelReq.ProtoReflect.Descriptor instead.
func (*SeriesDelReq) Descriptor() ([]byte, []int) {
	return file_api_series_series_proto_rawDescGZIP(), []int{35}
}

func (x *SeriesDelReq) GetSeriesUuid() string {
	if x != nil {
		return x.SeriesUuid
	}
	return ""
}

type SeriesDelResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Msg string `protobuf:"bytes,1,opt,name=msg,proto3" json:"msg,omitempty"`
}

func (x *SeriesDelResp) Reset() {
	*x = SeriesDelResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_series_series_proto_msgTypes[36]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SeriesDelResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SeriesDelResp) ProtoMessage() {}

func (x *SeriesDelResp) ProtoReflect() protoreflect.Message {
	mi := &file_api_series_series_proto_msgTypes[36]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SeriesDelResp.ProtoReflect.Descriptor instead.
func (*SeriesDelResp) Descriptor() ([]byte, []int) {
	return file_api_series_series_proto_rawDescGZIP(), []int{36}
}

func (x *SeriesDelResp) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

type CheckSeriesReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *CheckSeriesReq) Reset() {
	*x = CheckSeriesReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_series_series_proto_msgTypes[37]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CheckSeriesReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CheckSeriesReq) ProtoMessage() {}

func (x *CheckSeriesReq) ProtoReflect() protoreflect.Message {
	mi := &file_api_series_series_proto_msgTypes[37]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CheckSeriesReq.ProtoReflect.Descriptor instead.
func (*CheckSeriesReq) Descriptor() ([]byte, []int) {
	return file_api_series_series_proto_rawDescGZIP(), []int{37}
}

type CheckSeriesResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Data []*CheckSeriesResp_Info `protobuf:"bytes,1,rep,name=data,proto3" json:"data,omitempty"`
}

func (x *CheckSeriesResp) Reset() {
	*x = CheckSeriesResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_series_series_proto_msgTypes[38]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CheckSeriesResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CheckSeriesResp) ProtoMessage() {}

func (x *CheckSeriesResp) ProtoReflect() protoreflect.Message {
	mi := &file_api_series_series_proto_msgTypes[38]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CheckSeriesResp.ProtoReflect.Descriptor instead.
func (*CheckSeriesResp) Descriptor() ([]byte, []int) {
	return file_api_series_series_proto_rawDescGZIP(), []int{38}
}

func (x *CheckSeriesResp) GetData() []*CheckSeriesResp_Info {
	if x != nil {
		return x.Data
	}
	return nil
}

// UpdateSeriesChain
type UpdateSeriesChainReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	SeriesUuid  string `protobuf:"bytes,1,opt,name=seriesUuid,proto3" json:"seriesUuid,omitempty"`
	ChainStatus uint32 `protobuf:"varint,2,opt,name=chainStatus,proto3" json:"chainStatus,omitempty"`
}

func (x *UpdateSeriesChainReq) Reset() {
	*x = UpdateSeriesChainReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_series_series_proto_msgTypes[39]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateSeriesChainReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateSeriesChainReq) ProtoMessage() {}

func (x *UpdateSeriesChainReq) ProtoReflect() protoreflect.Message {
	mi := &file_api_series_series_proto_msgTypes[39]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateSeriesChainReq.ProtoReflect.Descriptor instead.
func (*UpdateSeriesChainReq) Descriptor() ([]byte, []int) {
	return file_api_series_series_proto_rawDescGZIP(), []int{39}
}

func (x *UpdateSeriesChainReq) GetSeriesUuid() string {
	if x != nil {
		return x.SeriesUuid
	}
	return ""
}

func (x *UpdateSeriesChainReq) GetChainStatus() uint32 {
	if x != nil {
		return x.ChainStatus
	}
	return 0
}

type UpdateSeriesChainResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Msg string `protobuf:"bytes,1,opt,name=msg,proto3" json:"msg,omitempty"`
}

func (x *UpdateSeriesChainResp) Reset() {
	*x = UpdateSeriesChainResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_series_series_proto_msgTypes[40]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateSeriesChainResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateSeriesChainResp) ProtoMessage() {}

func (x *UpdateSeriesChainResp) ProtoReflect() protoreflect.Message {
	mi := &file_api_series_series_proto_msgTypes[40]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateSeriesChainResp.ProtoReflect.Descriptor instead.
func (*UpdateSeriesChainResp) Descriptor() ([]byte, []int) {
	return file_api_series_series_proto_rawDescGZIP(), []int{40}
}

func (x *UpdateSeriesChainResp) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

type GetSeriesLanguageInfoReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	SeriesUuid   string          `protobuf:"bytes,1,opt,name=SeriesUuid,proto3" json:"SeriesUuid,omitempty"`
	LanguageInfo []*LanguageInfo `protobuf:"bytes,2,rep,name=languageInfo,proto3" json:"languageInfo,omitempty"`
}

func (x *GetSeriesLanguageInfoReq) Reset() {
	*x = GetSeriesLanguageInfoReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_series_series_proto_msgTypes[41]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetSeriesLanguageInfoReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetSeriesLanguageInfoReq) ProtoMessage() {}

func (x *GetSeriesLanguageInfoReq) ProtoReflect() protoreflect.Message {
	mi := &file_api_series_series_proto_msgTypes[41]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetSeriesLanguageInfoReq.ProtoReflect.Descriptor instead.
func (*GetSeriesLanguageInfoReq) Descriptor() ([]byte, []int) {
	return file_api_series_series_proto_rawDescGZIP(), []int{41}
}

func (x *GetSeriesLanguageInfoReq) GetSeriesUuid() string {
	if x != nil {
		return x.SeriesUuid
	}
	return ""
}

func (x *GetSeriesLanguageInfoReq) GetLanguageInfo() []*LanguageInfo {
	if x != nil {
		return x.LanguageInfo
	}
	return nil
}

type GetSeriesLanguageInfoRep struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	LanguageInfo []*LanguageInfo `protobuf:"bytes,1,rep,name=languageInfo,proto3" json:"languageInfo,omitempty"`
	Msg          string          `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
}

func (x *GetSeriesLanguageInfoRep) Reset() {
	*x = GetSeriesLanguageInfoRep{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_series_series_proto_msgTypes[42]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetSeriesLanguageInfoRep) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetSeriesLanguageInfoRep) ProtoMessage() {}

func (x *GetSeriesLanguageInfoRep) ProtoReflect() protoreflect.Message {
	mi := &file_api_series_series_proto_msgTypes[42]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetSeriesLanguageInfoRep.ProtoReflect.Descriptor instead.
func (*GetSeriesLanguageInfoRep) Descriptor() ([]byte, []int) {
	return file_api_series_series_proto_rawDescGZIP(), []int{42}
}

func (x *GetSeriesLanguageInfoRep) GetLanguageInfo() []*LanguageInfo {
	if x != nil {
		return x.LanguageInfo
	}
	return nil
}

func (x *GetSeriesLanguageInfoRep) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

type DrawGiftReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	SeriesUuid string `protobuf:"bytes,1,opt,name=seriesUuid,proto3" json:"seriesUuid,omitempty"`
}

func (x *DrawGiftReq) Reset() {
	*x = DrawGiftReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_series_series_proto_msgTypes[43]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DrawGiftReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DrawGiftReq) ProtoMessage() {}

func (x *DrawGiftReq) ProtoReflect() protoreflect.Message {
	mi := &file_api_series_series_proto_msgTypes[43]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DrawGiftReq.ProtoReflect.Descriptor instead.
func (*DrawGiftReq) Descriptor() ([]byte, []int) {
	return file_api_series_series_proto_rawDescGZIP(), []int{43}
}

func (x *DrawGiftReq) GetSeriesUuid() string {
	if x != nil {
		return x.SeriesUuid
	}
	return ""
}

type UpdateMemReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	SeriesUuid       string            `protobuf:"bytes,1,opt,name=seriesUuid,proto3" json:"seriesUuid,omitempty"`
	SeriesAddress    string            `protobuf:"bytes,2,opt,name=seriesAddress,proto3" json:"seriesAddress,omitempty"`
	SeriesMem        string            `protobuf:"bytes,3,opt,name=seriesMem,proto3" json:"seriesMem,omitempty"`
	SeriesPwd        string            `protobuf:"bytes,4,opt,name=seriesPwd,proto3" json:"seriesPwd,omitempty"`
	SeriesIsGiftDraw uint32            `protobuf:"varint,5,opt,name=seriesIsGiftDraw,proto3" json:"seriesIsGiftDraw,omitempty"` // 1-领取 2-未领取
	SeriesPublicKey  string            `protobuf:"bytes,6,opt,name=seriesPublicKey,proto3" json:"seriesPublicKey,omitempty"`
	CollectionMap    map[string]string `protobuf:"bytes,7,rep,name=collectionMap,proto3" json:"collectionMap,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

func (x *UpdateMemReq) Reset() {
	*x = UpdateMemReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_series_series_proto_msgTypes[44]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateMemReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateMemReq) ProtoMessage() {}

func (x *UpdateMemReq) ProtoReflect() protoreflect.Message {
	mi := &file_api_series_series_proto_msgTypes[44]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateMemReq.ProtoReflect.Descriptor instead.
func (*UpdateMemReq) Descriptor() ([]byte, []int) {
	return file_api_series_series_proto_rawDescGZIP(), []int{44}
}

func (x *UpdateMemReq) GetSeriesUuid() string {
	if x != nil {
		return x.SeriesUuid
	}
	return ""
}

func (x *UpdateMemReq) GetSeriesAddress() string {
	if x != nil {
		return x.SeriesAddress
	}
	return ""
}

func (x *UpdateMemReq) GetSeriesMem() string {
	if x != nil {
		return x.SeriesMem
	}
	return ""
}

func (x *UpdateMemReq) GetSeriesPwd() string {
	if x != nil {
		return x.SeriesPwd
	}
	return ""
}

func (x *UpdateMemReq) GetSeriesIsGiftDraw() uint32 {
	if x != nil {
		return x.SeriesIsGiftDraw
	}
	return 0
}

func (x *UpdateMemReq) GetSeriesPublicKey() string {
	if x != nil {
		return x.SeriesPublicKey
	}
	return ""
}

func (x *UpdateMemReq) GetCollectionMap() map[string]string {
	if x != nil {
		return x.CollectionMap
	}
	return nil
}

type CollectionM struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	SeriesUuid       string `protobuf:"bytes,1,opt,name=seriesUuid,proto3" json:"seriesUuid,omitempty"`
	SeriesAddress    string `protobuf:"bytes,2,opt,name=seriesAddress,proto3" json:"seriesAddress,omitempty"`
	SeriesMem        string `protobuf:"bytes,3,opt,name=seriesMem,proto3" json:"seriesMem,omitempty"`
	SeriesPwd        string `protobuf:"bytes,4,opt,name=seriesPwd,proto3" json:"seriesPwd,omitempty"`
	SeriesIsGiftDraw uint32 `protobuf:"varint,5,opt,name=seriesIsGiftDraw,proto3" json:"seriesIsGiftDraw,omitempty"` // 1-领取 2-未领取
	SeriesPublicKey  string `protobuf:"bytes,6,opt,name=seriesPublicKey,proto3" json:"seriesPublicKey,omitempty"`
}

func (x *CollectionM) Reset() {
	*x = CollectionM{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_series_series_proto_msgTypes[45]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CollectionM) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CollectionM) ProtoMessage() {}

func (x *CollectionM) ProtoReflect() protoreflect.Message {
	mi := &file_api_series_series_proto_msgTypes[45]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CollectionM.ProtoReflect.Descriptor instead.
func (*CollectionM) Descriptor() ([]byte, []int) {
	return file_api_series_series_proto_rawDescGZIP(), []int{45}
}

func (x *CollectionM) GetSeriesUuid() string {
	if x != nil {
		return x.SeriesUuid
	}
	return ""
}

func (x *CollectionM) GetSeriesAddress() string {
	if x != nil {
		return x.SeriesAddress
	}
	return ""
}

func (x *CollectionM) GetSeriesMem() string {
	if x != nil {
		return x.SeriesMem
	}
	return ""
}

func (x *CollectionM) GetSeriesPwd() string {
	if x != nil {
		return x.SeriesPwd
	}
	return ""
}

func (x *CollectionM) GetSeriesIsGiftDraw() uint32 {
	if x != nil {
		return x.SeriesIsGiftDraw
	}
	return 0
}

func (x *CollectionM) GetSeriesPublicKey() string {
	if x != nil {
		return x.SeriesPublicKey
	}
	return ""
}

type CommonRes struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Uuid string `protobuf:"bytes,1,opt,name=uuid,proto3" json:"uuid,omitempty"`
}

func (x *CommonRes) Reset() {
	*x = CommonRes{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_series_series_proto_msgTypes[46]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CommonRes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CommonRes) ProtoMessage() {}

func (x *CommonRes) ProtoReflect() protoreflect.Message {
	mi := &file_api_series_series_proto_msgTypes[46]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CommonRes.ProtoReflect.Descriptor instead.
func (*CommonRes) Descriptor() ([]byte, []int) {
	return file_api_series_series_proto_rawDescGZIP(), []int{46}
}

func (x *CommonRes) GetUuid() string {
	if x != nil {
		return x.Uuid
	}
	return ""
}

// SeriesList
type FSeriesListReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Page     int32    `protobuf:"varint,1,opt,name=Page,json=page,proto3" json:"Page,omitempty"`
	PageSize int32    `protobuf:"varint,2,opt,name=PageSize,json=page_size,proto3" json:"PageSize,omitempty"`
	Lang     string   `protobuf:"bytes,5,opt,name=lang,proto3" json:"lang,omitempty"`
	Langs    []string `protobuf:"bytes,6,rep,name=langs,proto3" json:"langs,omitempty"`
}

func (x *FSeriesListReq) Reset() {
	*x = FSeriesListReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_series_series_proto_msgTypes[47]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FSeriesListReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FSeriesListReq) ProtoMessage() {}

func (x *FSeriesListReq) ProtoReflect() protoreflect.Message {
	mi := &file_api_series_series_proto_msgTypes[47]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FSeriesListReq.ProtoReflect.Descriptor instead.
func (*FSeriesListReq) Descriptor() ([]byte, []int) {
	return file_api_series_series_proto_rawDescGZIP(), []int{47}
}

func (x *FSeriesListReq) GetPage() int32 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *FSeriesListReq) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *FSeriesListReq) GetLang() string {
	if x != nil {
		return x.Lang
	}
	return ""
}

func (x *FSeriesListReq) GetLangs() []string {
	if x != nil {
		return x.Langs
	}
	return nil
}

type FSeriesListResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Data  []*FSeriesListResp_Info `protobuf:"bytes,1,rep,name=data,proto3" json:"data,omitempty"`
	Count int32                   `protobuf:"varint,2,opt,name=count,proto3" json:"count,omitempty"`
	Msg   string                  `protobuf:"bytes,3,opt,name=msg,proto3" json:"msg,omitempty"`
}

func (x *FSeriesListResp) Reset() {
	*x = FSeriesListResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_series_series_proto_msgTypes[48]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FSeriesListResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FSeriesListResp) ProtoMessage() {}

func (x *FSeriesListResp) ProtoReflect() protoreflect.Message {
	mi := &file_api_series_series_proto_msgTypes[48]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FSeriesListResp.ProtoReflect.Descriptor instead.
func (*FSeriesListResp) Descriptor() ([]byte, []int) {
	return file_api_series_series_proto_rawDescGZIP(), []int{48}
}

func (x *FSeriesListResp) GetData() []*FSeriesListResp_Info {
	if x != nil {
		return x.Data
	}
	return nil
}

func (x *FSeriesListResp) GetCount() int32 {
	if x != nil {
		return x.Count
	}
	return 0
}

func (x *FSeriesListResp) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

type SeriesDetailByMemReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Mem  string `protobuf:"bytes,1,opt,name=mem,proto3" json:"mem,omitempty"`
	Lang string `protobuf:"bytes,2,opt,name=lang,proto3" json:"lang,omitempty"`
}

func (x *SeriesDetailByMemReq) Reset() {
	*x = SeriesDetailByMemReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_series_series_proto_msgTypes[49]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SeriesDetailByMemReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SeriesDetailByMemReq) ProtoMessage() {}

func (x *SeriesDetailByMemReq) ProtoReflect() protoreflect.Message {
	mi := &file_api_series_series_proto_msgTypes[49]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SeriesDetailByMemReq.ProtoReflect.Descriptor instead.
func (*SeriesDetailByMemReq) Descriptor() ([]byte, []int) {
	return file_api_series_series_proto_rawDescGZIP(), []int{49}
}

func (x *SeriesDetailByMemReq) GetMem() string {
	if x != nil {
		return x.Mem
	}
	return ""
}

func (x *SeriesDetailByMemReq) GetLang() string {
	if x != nil {
		return x.Lang
	}
	return ""
}

type SeriesArtworkData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ID          uint32                   `protobuf:"varint,1,opt,name=ID,proto3" json:"ID,omitempty"`
	CreatedAt   string                   `protobuf:"bytes,2,opt,name=createdAt,proto3" json:"createdAt,omitempty"`
	UpdatedAt   string                   `protobuf:"bytes,3,opt,name=updatedAt,proto3" json:"updatedAt,omitempty"`
	DeletedAt   int64                    `protobuf:"varint,4,opt,name=deletedAt,proto3" json:"deletedAt,omitempty"`
	ArtworkId   int64                    `protobuf:"varint,5,opt,name=artworkId,proto3" json:"artworkId,omitempty"`
	ArtworkUid  string                   `protobuf:"bytes,6,opt,name=artworkUid,proto3" json:"artworkUid,omitempty"`
	Tfnum       string                   `protobuf:"bytes,7,opt,name=tfnum,proto3" json:"tfnum,omitempty"`
	ArtworkName string                   `protobuf:"bytes,8,opt,name=artworkName,proto3" json:"artworkName,omitempty"`
	HdPic       string                   `protobuf:"bytes,9,opt,name=hdPic,proto3" json:"hdPic,omitempty"`
	Ruler       int32                    `protobuf:"varint,10,opt,name=ruler,proto3" json:"ruler,omitempty"`
	Length      int32                    `protobuf:"varint,11,opt,name=length,proto3" json:"length,omitempty"`
	Width       int32                    `protobuf:"varint,12,opt,name=width,proto3" json:"width,omitempty"`
	ArtistName  string                   `protobuf:"bytes,13,opt,name=artistName,proto3" json:"artistName,omitempty"`
	ArtistUid   string                   `protobuf:"bytes,14,opt,name=artistUid,proto3" json:"artistUid,omitempty"`
	Abstract    string                   `protobuf:"bytes,15,opt,name=abstract,proto3" json:"abstract,omitempty"`
	Tnum        string                   `protobuf:"bytes,16,opt,name=tnum,proto3" json:"tnum,omitempty"`
	Lang        []*SeriesArtworkLangData `protobuf:"bytes,17,rep,name=lang,proto3" json:"lang,omitempty"`
	Price       string                   `protobuf:"bytes,18,opt,name=price,proto3" json:"price,omitempty"`
	Currency    string                   `protobuf:"bytes,19,opt,name=currency,proto3" json:"currency,omitempty"`
	CompanyId   uint32                   `protobuf:"varint,20,opt,name=companyId,proto3" json:"companyId,omitempty"`
	CompanyName string                   `protobuf:"bytes,21,opt,name=companyName,proto3" json:"companyName,omitempty"`
}

func (x *SeriesArtworkData) Reset() {
	*x = SeriesArtworkData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_series_series_proto_msgTypes[50]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SeriesArtworkData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SeriesArtworkData) ProtoMessage() {}

func (x *SeriesArtworkData) ProtoReflect() protoreflect.Message {
	mi := &file_api_series_series_proto_msgTypes[50]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SeriesArtworkData.ProtoReflect.Descriptor instead.
func (*SeriesArtworkData) Descriptor() ([]byte, []int) {
	return file_api_series_series_proto_rawDescGZIP(), []int{50}
}

func (x *SeriesArtworkData) GetID() uint32 {
	if x != nil {
		return x.ID
	}
	return 0
}

func (x *SeriesArtworkData) GetCreatedAt() string {
	if x != nil {
		return x.CreatedAt
	}
	return ""
}

func (x *SeriesArtworkData) GetUpdatedAt() string {
	if x != nil {
		return x.UpdatedAt
	}
	return ""
}

func (x *SeriesArtworkData) GetDeletedAt() int64 {
	if x != nil {
		return x.DeletedAt
	}
	return 0
}

func (x *SeriesArtworkData) GetArtworkId() int64 {
	if x != nil {
		return x.ArtworkId
	}
	return 0
}

func (x *SeriesArtworkData) GetArtworkUid() string {
	if x != nil {
		return x.ArtworkUid
	}
	return ""
}

func (x *SeriesArtworkData) GetTfnum() string {
	if x != nil {
		return x.Tfnum
	}
	return ""
}

func (x *SeriesArtworkData) GetArtworkName() string {
	if x != nil {
		return x.ArtworkName
	}
	return ""
}

func (x *SeriesArtworkData) GetHdPic() string {
	if x != nil {
		return x.HdPic
	}
	return ""
}

func (x *SeriesArtworkData) GetRuler() int32 {
	if x != nil {
		return x.Ruler
	}
	return 0
}

func (x *SeriesArtworkData) GetLength() int32 {
	if x != nil {
		return x.Length
	}
	return 0
}

func (x *SeriesArtworkData) GetWidth() int32 {
	if x != nil {
		return x.Width
	}
	return 0
}

func (x *SeriesArtworkData) GetArtistName() string {
	if x != nil {
		return x.ArtistName
	}
	return ""
}

func (x *SeriesArtworkData) GetArtistUid() string {
	if x != nil {
		return x.ArtistUid
	}
	return ""
}

func (x *SeriesArtworkData) GetAbstract() string {
	if x != nil {
		return x.Abstract
	}
	return ""
}

func (x *SeriesArtworkData) GetTnum() string {
	if x != nil {
		return x.Tnum
	}
	return ""
}

func (x *SeriesArtworkData) GetLang() []*SeriesArtworkLangData {
	if x != nil {
		return x.Lang
	}
	return nil
}

func (x *SeriesArtworkData) GetPrice() string {
	if x != nil {
		return x.Price
	}
	return ""
}

func (x *SeriesArtworkData) GetCurrency() string {
	if x != nil {
		return x.Currency
	}
	return ""
}

func (x *SeriesArtworkData) GetCompanyId() uint32 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

func (x *SeriesArtworkData) GetCompanyName() string {
	if x != nil {
		return x.CompanyName
	}
	return ""
}

type SeriesArtworkLangData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ID              uint32 `protobuf:"varint,1,opt,name=ID,proto3" json:"ID,omitempty"`
	CreatedAt       string `protobuf:"bytes,2,opt,name=createdAt,proto3" json:"createdAt,omitempty"`
	UpdatedAt       string `protobuf:"bytes,3,opt,name=updatedAt,proto3" json:"updatedAt,omitempty"`
	DeletedAt       int64  `protobuf:"varint,4,opt,name=deletedAt,proto3" json:"deletedAt,omitempty"`
	Lang            string `protobuf:"bytes,5,opt,name=lang,proto3" json:"lang,omitempty"`
	SeriesArtworkId int64  `protobuf:"varint,6,opt,name=seriesArtworkId,proto3" json:"seriesArtworkId,omitempty"`
	ArtworkUid      string `protobuf:"bytes,7,opt,name=artworkUid,proto3" json:"artworkUid,omitempty"`
	ArtworkName     string `protobuf:"bytes,8,opt,name=artworkName,proto3" json:"artworkName,omitempty"`
	ArtistName      string `protobuf:"bytes,9,opt,name=artistName,proto3" json:"artistName,omitempty"`
	Abstract        string `protobuf:"bytes,10,opt,name=abstract,proto3" json:"abstract,omitempty"`
	HdPic           string `protobuf:"bytes,11,opt,name=hdPic,proto3" json:"hdPic,omitempty"`
	Ruler           int32  `protobuf:"varint,12,opt,name=ruler,proto3" json:"ruler,omitempty"`
	Length          int32  `protobuf:"varint,13,opt,name=length,proto3" json:"length,omitempty"`
	Width           int32  `protobuf:"varint,14,opt,name=width,proto3" json:"width,omitempty"`
}

func (x *SeriesArtworkLangData) Reset() {
	*x = SeriesArtworkLangData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_series_series_proto_msgTypes[51]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SeriesArtworkLangData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SeriesArtworkLangData) ProtoMessage() {}

func (x *SeriesArtworkLangData) ProtoReflect() protoreflect.Message {
	mi := &file_api_series_series_proto_msgTypes[51]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SeriesArtworkLangData.ProtoReflect.Descriptor instead.
func (*SeriesArtworkLangData) Descriptor() ([]byte, []int) {
	return file_api_series_series_proto_rawDescGZIP(), []int{51}
}

func (x *SeriesArtworkLangData) GetID() uint32 {
	if x != nil {
		return x.ID
	}
	return 0
}

func (x *SeriesArtworkLangData) GetCreatedAt() string {
	if x != nil {
		return x.CreatedAt
	}
	return ""
}

func (x *SeriesArtworkLangData) GetUpdatedAt() string {
	if x != nil {
		return x.UpdatedAt
	}
	return ""
}

func (x *SeriesArtworkLangData) GetDeletedAt() int64 {
	if x != nil {
		return x.DeletedAt
	}
	return 0
}

func (x *SeriesArtworkLangData) GetLang() string {
	if x != nil {
		return x.Lang
	}
	return ""
}

func (x *SeriesArtworkLangData) GetSeriesArtworkId() int64 {
	if x != nil {
		return x.SeriesArtworkId
	}
	return 0
}

func (x *SeriesArtworkLangData) GetArtworkUid() string {
	if x != nil {
		return x.ArtworkUid
	}
	return ""
}

func (x *SeriesArtworkLangData) GetArtworkName() string {
	if x != nil {
		return x.ArtworkName
	}
	return ""
}

func (x *SeriesArtworkLangData) GetArtistName() string {
	if x != nil {
		return x.ArtistName
	}
	return ""
}

func (x *SeriesArtworkLangData) GetAbstract() string {
	if x != nil {
		return x.Abstract
	}
	return ""
}

func (x *SeriesArtworkLangData) GetHdPic() string {
	if x != nil {
		return x.HdPic
	}
	return ""
}

func (x *SeriesArtworkLangData) GetRuler() int32 {
	if x != nil {
		return x.Ruler
	}
	return 0
}

func (x *SeriesArtworkLangData) GetLength() int32 {
	if x != nil {
		return x.Length
	}
	return 0
}

func (x *SeriesArtworkLangData) GetWidth() int32 {
	if x != nil {
		return x.Width
	}
	return 0
}

type ListSalesArtworkReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	SeriesUid string `protobuf:"bytes,1,opt,name=seriesUid,proto3" json:"seriesUid,omitempty"`
	Page      int64  `protobuf:"varint,2,opt,name=page,proto3" json:"page,omitempty"`
	PageSize  int64  `protobuf:"varint,3,opt,name=pageSize,proto3" json:"pageSize,omitempty"`
}

func (x *ListSalesArtworkReq) Reset() {
	*x = ListSalesArtworkReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_series_series_proto_msgTypes[52]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListSalesArtworkReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListSalesArtworkReq) ProtoMessage() {}

func (x *ListSalesArtworkReq) ProtoReflect() protoreflect.Message {
	mi := &file_api_series_series_proto_msgTypes[52]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListSalesArtworkReq.ProtoReflect.Descriptor instead.
func (*ListSalesArtworkReq) Descriptor() ([]byte, []int) {
	return file_api_series_series_proto_rawDescGZIP(), []int{52}
}

func (x *ListSalesArtworkReq) GetSeriesUid() string {
	if x != nil {
		return x.SeriesUid
	}
	return ""
}

func (x *ListSalesArtworkReq) GetPage() int64 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *ListSalesArtworkReq) GetPageSize() int64 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

type SalesArtwork struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	LotNo              string `protobuf:"bytes,1,opt,name=lotNo,proto3" json:"lotNo,omitempty"`
	ArtworkUid         string `protobuf:"bytes,2,opt,name=artworkUid,proto3" json:"artworkUid,omitempty"`
	ArtworkName        string `protobuf:"bytes,3,opt,name=artworkName,proto3" json:"artworkName,omitempty"`
	HdPic              string `protobuf:"bytes,4,opt,name=hdPic,proto3" json:"hdPic,omitempty"`
	Price              string `protobuf:"bytes,5,opt,name=price,proto3" json:"price,omitempty"`
	Currency           string `protobuf:"bytes,6,opt,name=currency,proto3" json:"currency,omitempty"`
	PayStatus          string `protobuf:"bytes,7,opt,name=payStatus,proto3" json:"payStatus,omitempty"`
	PayTime            string `protobuf:"bytes,8,opt,name=payTime,proto3" json:"payTime,omitempty"`
	UserId             uint32 `protobuf:"varint,9,opt,name=userId,proto3" json:"userId,omitempty"`
	UserName           string `protobuf:"bytes,10,opt,name=userName,proto3" json:"userName,omitempty"`
	TelNum             string `protobuf:"bytes,11,opt,name=telNum,proto3" json:"telNum,omitempty"` //这个通过客户端从账号服务获取
	AuctionBuyUuid     string `protobuf:"bytes,12,opt,name=AuctionBuyUuid,proto3" json:"AuctionBuyUuid,omitempty"`
	AuctionArtworkUuid string `protobuf:"bytes,13,opt,name=AuctionArtworkUuid,proto3" json:"AuctionArtworkUuid,omitempty"`
	PayUid             string `protobuf:"bytes,14,opt,name=payUid,proto3" json:"payUid,omitempty"`
}

func (x *SalesArtwork) Reset() {
	*x = SalesArtwork{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_series_series_proto_msgTypes[53]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SalesArtwork) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SalesArtwork) ProtoMessage() {}

func (x *SalesArtwork) ProtoReflect() protoreflect.Message {
	mi := &file_api_series_series_proto_msgTypes[53]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SalesArtwork.ProtoReflect.Descriptor instead.
func (*SalesArtwork) Descriptor() ([]byte, []int) {
	return file_api_series_series_proto_rawDescGZIP(), []int{53}
}

func (x *SalesArtwork) GetLotNo() string {
	if x != nil {
		return x.LotNo
	}
	return ""
}

func (x *SalesArtwork) GetArtworkUid() string {
	if x != nil {
		return x.ArtworkUid
	}
	return ""
}

func (x *SalesArtwork) GetArtworkName() string {
	if x != nil {
		return x.ArtworkName
	}
	return ""
}

func (x *SalesArtwork) GetHdPic() string {
	if x != nil {
		return x.HdPic
	}
	return ""
}

func (x *SalesArtwork) GetPrice() string {
	if x != nil {
		return x.Price
	}
	return ""
}

func (x *SalesArtwork) GetCurrency() string {
	if x != nil {
		return x.Currency
	}
	return ""
}

func (x *SalesArtwork) GetPayStatus() string {
	if x != nil {
		return x.PayStatus
	}
	return ""
}

func (x *SalesArtwork) GetPayTime() string {
	if x != nil {
		return x.PayTime
	}
	return ""
}

func (x *SalesArtwork) GetUserId() uint32 {
	if x != nil {
		return x.UserId
	}
	return 0
}

func (x *SalesArtwork) GetUserName() string {
	if x != nil {
		return x.UserName
	}
	return ""
}

func (x *SalesArtwork) GetTelNum() string {
	if x != nil {
		return x.TelNum
	}
	return ""
}

func (x *SalesArtwork) GetAuctionBuyUuid() string {
	if x != nil {
		return x.AuctionBuyUuid
	}
	return ""
}

func (x *SalesArtwork) GetAuctionArtworkUuid() string {
	if x != nil {
		return x.AuctionArtworkUuid
	}
	return ""
}

func (x *SalesArtwork) GetPayUid() string {
	if x != nil {
		return x.PayUid
	}
	return ""
}

type ListSalesArtworkResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Data     []*SalesArtwork `protobuf:"bytes,1,rep,name=data,proto3" json:"data,omitempty"`
	Page     int64           `protobuf:"varint,2,opt,name=page,proto3" json:"page,omitempty"`
	PageSize int64           `protobuf:"varint,3,opt,name=pageSize,proto3" json:"pageSize,omitempty"`
	Total    int64           `protobuf:"varint,4,opt,name=Total,proto3" json:"Total,omitempty"`
}

func (x *ListSalesArtworkResp) Reset() {
	*x = ListSalesArtworkResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_series_series_proto_msgTypes[54]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListSalesArtworkResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListSalesArtworkResp) ProtoMessage() {}

func (x *ListSalesArtworkResp) ProtoReflect() protoreflect.Message {
	mi := &file_api_series_series_proto_msgTypes[54]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListSalesArtworkResp.ProtoReflect.Descriptor instead.
func (*ListSalesArtworkResp) Descriptor() ([]byte, []int) {
	return file_api_series_series_proto_rawDescGZIP(), []int{54}
}

func (x *ListSalesArtworkResp) GetData() []*SalesArtwork {
	if x != nil {
		return x.Data
	}
	return nil
}

func (x *ListSalesArtworkResp) GetPage() int64 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *ListSalesArtworkResp) GetPageSize() int64 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *ListSalesArtworkResp) GetTotal() int64 {
	if x != nil {
		return x.Total
	}
	return 0
}

type UpdateOrderStatusReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	OrderUuid      string   `protobuf:"bytes,1,opt,name=orderUuid,proto3" json:"orderUuid,omitempty"`
	PayStatus      int32    `protobuf:"varint,2,opt,name=payStatus,proto3" json:"payStatus,omitempty"`
	Remark         string   `protobuf:"bytes,3,opt,name=remark,proto3" json:"remark,omitempty"`
	PayPprofList   []string `protobuf:"bytes,4,rep,name=payPprofList,proto3" json:"payPprofList,omitempty"`
	PayTime        string   `protobuf:"bytes,5,opt,name=payTime,proto3" json:"payTime,omitempty"`
	OperatorId     uint32   `protobuf:"varint,6,opt,name=operatorId,proto3" json:"operatorId,omitempty"`
	OperatorTelNum string   `protobuf:"bytes,7,opt,name=operatorTelNum,proto3" json:"operatorTelNum,omitempty"`
	SessionId      string   `protobuf:"bytes,8,opt,name=sessionId,proto3" json:"sessionId,omitempty"`
	PayType        uint32   `protobuf:"varint,9,opt,name=payType,proto3" json:"payType,omitempty"`
}

func (x *UpdateOrderStatusReq) Reset() {
	*x = UpdateOrderStatusReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_series_series_proto_msgTypes[55]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateOrderStatusReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateOrderStatusReq) ProtoMessage() {}

func (x *UpdateOrderStatusReq) ProtoReflect() protoreflect.Message {
	mi := &file_api_series_series_proto_msgTypes[55]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateOrderStatusReq.ProtoReflect.Descriptor instead.
func (*UpdateOrderStatusReq) Descriptor() ([]byte, []int) {
	return file_api_series_series_proto_rawDescGZIP(), []int{55}
}

func (x *UpdateOrderStatusReq) GetOrderUuid() string {
	if x != nil {
		return x.OrderUuid
	}
	return ""
}

func (x *UpdateOrderStatusReq) GetPayStatus() int32 {
	if x != nil {
		return x.PayStatus
	}
	return 0
}

func (x *UpdateOrderStatusReq) GetRemark() string {
	if x != nil {
		return x.Remark
	}
	return ""
}

func (x *UpdateOrderStatusReq) GetPayPprofList() []string {
	if x != nil {
		return x.PayPprofList
	}
	return nil
}

func (x *UpdateOrderStatusReq) GetPayTime() string {
	if x != nil {
		return x.PayTime
	}
	return ""
}

func (x *UpdateOrderStatusReq) GetOperatorId() uint32 {
	if x != nil {
		return x.OperatorId
	}
	return 0
}

func (x *UpdateOrderStatusReq) GetOperatorTelNum() string {
	if x != nil {
		return x.OperatorTelNum
	}
	return ""
}

func (x *UpdateOrderStatusReq) GetSessionId() string {
	if x != nil {
		return x.SessionId
	}
	return ""
}

func (x *UpdateOrderStatusReq) GetPayType() uint32 {
	if x != nil {
		return x.PayType
	}
	return 0
}

type SeriesDetailResp_SeriesLangInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	SeriesUuid string `protobuf:"bytes,1,opt,name=seriesUuid,proto3" json:"seriesUuid,omitempty"`
	SeriesName string `protobuf:"bytes,3,opt,name=seriesName,proto3" json:"seriesName,omitempty"`
	Desc       string `protobuf:"bytes,4,opt,name=desc,proto3" json:"desc,omitempty"`
	Id         int32  `protobuf:"varint,6,opt,name=id,proto3" json:"id,omitempty"`
	Lang       int32  `protobuf:"varint,7,opt,name=lang,proto3" json:"lang,omitempty"`
	CoverImg   string `protobuf:"bytes,8,opt,name=coverImg,proto3" json:"coverImg,omitempty"`
}

func (x *SeriesDetailResp_SeriesLangInfo) Reset() {
	*x = SeriesDetailResp_SeriesLangInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_series_series_proto_msgTypes[56]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SeriesDetailResp_SeriesLangInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SeriesDetailResp_SeriesLangInfo) ProtoMessage() {}

func (x *SeriesDetailResp_SeriesLangInfo) ProtoReflect() protoreflect.Message {
	mi := &file_api_series_series_proto_msgTypes[56]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SeriesDetailResp_SeriesLangInfo.ProtoReflect.Descriptor instead.
func (*SeriesDetailResp_SeriesLangInfo) Descriptor() ([]byte, []int) {
	return file_api_series_series_proto_rawDescGZIP(), []int{26, 0}
}

func (x *SeriesDetailResp_SeriesLangInfo) GetSeriesUuid() string {
	if x != nil {
		return x.SeriesUuid
	}
	return ""
}

func (x *SeriesDetailResp_SeriesLangInfo) GetSeriesName() string {
	if x != nil {
		return x.SeriesName
	}
	return ""
}

func (x *SeriesDetailResp_SeriesLangInfo) GetDesc() string {
	if x != nil {
		return x.Desc
	}
	return ""
}

func (x *SeriesDetailResp_SeriesLangInfo) GetId() int32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *SeriesDetailResp_SeriesLangInfo) GetLang() int32 {
	if x != nil {
		return x.Lang
	}
	return 0
}

func (x *SeriesDetailResp_SeriesLangInfo) GetCoverImg() string {
	if x != nil {
		return x.CoverImg
	}
	return ""
}

type SeriesListResp_Info struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	SeriesUuid      string `protobuf:"bytes,1,opt,name=seriesUuid,proto3" json:"seriesUuid,omitempty"`
	SeriesName      string `protobuf:"bytes,2,opt,name=seriesName,proto3" json:"seriesName,omitempty"`
	CoverImg        string `protobuf:"bytes,3,opt,name=coverImg,proto3" json:"coverImg,omitempty"`
	BrandName       string `protobuf:"bytes,4,opt,name=brandName,proto3" json:"brandName,omitempty"`
	CreateTime      string `protobuf:"bytes,5,opt,name=createTime,proto3" json:"createTime,omitempty"`
	ShelfState      int32  `protobuf:"varint,6,opt,name=shelfState,proto3" json:"shelfState,omitempty"` //1=上架 2=下架
	LaunchStartTime string `protobuf:"bytes,7,opt,name=launchStartTime,proto3" json:"launchStartTime,omitempty"`
	LaunchEndTime   string `protobuf:"bytes,8,opt,name=launchEndTime,proto3" json:"launchEndTime,omitempty"`
	ActionTime      string `protobuf:"bytes,9,opt,name=actionTime,proto3" json:"actionTime,omitempty"`
	OnShelfTime     string `protobuf:"bytes,10,opt,name=onShelfTime,proto3" json:"onShelfTime,omitempty"`
	ActionCode      string `protobuf:"bytes,12,opt,name=actionCode,proto3" json:"actionCode,omitempty"`
	Status          int32  `protobuf:"varint,13,opt,name=status,proto3" json:"status,omitempty"`
	AutoShelf       int32  `protobuf:"varint,14,opt,name=autoShelf,proto3" json:"autoShelf,omitempty"` //自动上下架 1=禁用 2=启用
}

func (x *SeriesListResp_Info) Reset() {
	*x = SeriesListResp_Info{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_series_series_proto_msgTypes[57]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SeriesListResp_Info) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SeriesListResp_Info) ProtoMessage() {}

func (x *SeriesListResp_Info) ProtoReflect() protoreflect.Message {
	mi := &file_api_series_series_proto_msgTypes[57]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SeriesListResp_Info.ProtoReflect.Descriptor instead.
func (*SeriesListResp_Info) Descriptor() ([]byte, []int) {
	return file_api_series_series_proto_rawDescGZIP(), []int{34, 0}
}

func (x *SeriesListResp_Info) GetSeriesUuid() string {
	if x != nil {
		return x.SeriesUuid
	}
	return ""
}

func (x *SeriesListResp_Info) GetSeriesName() string {
	if x != nil {
		return x.SeriesName
	}
	return ""
}

func (x *SeriesListResp_Info) GetCoverImg() string {
	if x != nil {
		return x.CoverImg
	}
	return ""
}

func (x *SeriesListResp_Info) GetBrandName() string {
	if x != nil {
		return x.BrandName
	}
	return ""
}

func (x *SeriesListResp_Info) GetCreateTime() string {
	if x != nil {
		return x.CreateTime
	}
	return ""
}

func (x *SeriesListResp_Info) GetShelfState() int32 {
	if x != nil {
		return x.ShelfState
	}
	return 0
}

func (x *SeriesListResp_Info) GetLaunchStartTime() string {
	if x != nil {
		return x.LaunchStartTime
	}
	return ""
}

func (x *SeriesListResp_Info) GetLaunchEndTime() string {
	if x != nil {
		return x.LaunchEndTime
	}
	return ""
}

func (x *SeriesListResp_Info) GetActionTime() string {
	if x != nil {
		return x.ActionTime
	}
	return ""
}

func (x *SeriesListResp_Info) GetOnShelfTime() string {
	if x != nil {
		return x.OnShelfTime
	}
	return ""
}

func (x *SeriesListResp_Info) GetActionCode() string {
	if x != nil {
		return x.ActionCode
	}
	return ""
}

func (x *SeriesListResp_Info) GetStatus() int32 {
	if x != nil {
		return x.Status
	}
	return 0
}

func (x *SeriesListResp_Info) GetAutoShelf() int32 {
	if x != nil {
		return x.AutoShelf
	}
	return 0
}

type CheckSeriesResp_Info struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	SeriesUuid string `protobuf:"bytes,1,opt,name=seriesUuid,proto3" json:"seriesUuid,omitempty"`
	BrandId    string `protobuf:"bytes,2,opt,name=brandId,proto3" json:"brandId,omitempty"`
}

func (x *CheckSeriesResp_Info) Reset() {
	*x = CheckSeriesResp_Info{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_series_series_proto_msgTypes[58]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CheckSeriesResp_Info) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CheckSeriesResp_Info) ProtoMessage() {}

func (x *CheckSeriesResp_Info) ProtoReflect() protoreflect.Message {
	mi := &file_api_series_series_proto_msgTypes[58]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CheckSeriesResp_Info.ProtoReflect.Descriptor instead.
func (*CheckSeriesResp_Info) Descriptor() ([]byte, []int) {
	return file_api_series_series_proto_rawDescGZIP(), []int{38, 0}
}

func (x *CheckSeriesResp_Info) GetSeriesUuid() string {
	if x != nil {
		return x.SeriesUuid
	}
	return ""
}

func (x *CheckSeriesResp_Info) GetBrandId() string {
	if x != nil {
		return x.BrandId
	}
	return ""
}

type FSeriesListResp_Info struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	SeriesUuid string `protobuf:"bytes,1,opt,name=seriesUuid,proto3" json:"seriesUuid,omitempty"`
	SeriesName string `protobuf:"bytes,2,opt,name=seriesName,proto3" json:"seriesName,omitempty"`
	CoverImg   string `protobuf:"bytes,3,opt,name=coverImg,proto3" json:"coverImg,omitempty"`
	Desc       string `protobuf:"bytes,4,opt,name=desc,proto3" json:"desc,omitempty"`
	StartTime  string `protobuf:"bytes,5,opt,name=startTime,proto3" json:"startTime,omitempty"`
	EndTime    string `protobuf:"bytes,6,opt,name=endTime,proto3" json:"endTime,omitempty"`
	BrandId    string `protobuf:"bytes,7,opt,name=brandId,proto3" json:"brandId,omitempty"`
	BrandName  string `protobuf:"bytes,8,opt,name=brandName,proto3" json:"brandName,omitempty"`
	BrandImg   string `protobuf:"bytes,9,opt,name=brandImg,proto3" json:"brandImg,omitempty"`
	ActionCode string `protobuf:"bytes,10,opt,name=actionCode,proto3" json:"actionCode,omitempty"`
}

func (x *FSeriesListResp_Info) Reset() {
	*x = FSeriesListResp_Info{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_series_series_proto_msgTypes[60]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FSeriesListResp_Info) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FSeriesListResp_Info) ProtoMessage() {}

func (x *FSeriesListResp_Info) ProtoReflect() protoreflect.Message {
	mi := &file_api_series_series_proto_msgTypes[60]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FSeriesListResp_Info.ProtoReflect.Descriptor instead.
func (*FSeriesListResp_Info) Descriptor() ([]byte, []int) {
	return file_api_series_series_proto_rawDescGZIP(), []int{48, 0}
}

func (x *FSeriesListResp_Info) GetSeriesUuid() string {
	if x != nil {
		return x.SeriesUuid
	}
	return ""
}

func (x *FSeriesListResp_Info) GetSeriesName() string {
	if x != nil {
		return x.SeriesName
	}
	return ""
}

func (x *FSeriesListResp_Info) GetCoverImg() string {
	if x != nil {
		return x.CoverImg
	}
	return ""
}

func (x *FSeriesListResp_Info) GetDesc() string {
	if x != nil {
		return x.Desc
	}
	return ""
}

func (x *FSeriesListResp_Info) GetStartTime() string {
	if x != nil {
		return x.StartTime
	}
	return ""
}

func (x *FSeriesListResp_Info) GetEndTime() string {
	if x != nil {
		return x.EndTime
	}
	return ""
}

func (x *FSeriesListResp_Info) GetBrandId() string {
	if x != nil {
		return x.BrandId
	}
	return ""
}

func (x *FSeriesListResp_Info) GetBrandName() string {
	if x != nil {
		return x.BrandName
	}
	return ""
}

func (x *FSeriesListResp_Info) GetBrandImg() string {
	if x != nil {
		return x.BrandImg
	}
	return ""
}

func (x *FSeriesListResp_Info) GetActionCode() string {
	if x != nil {
		return x.ActionCode
	}
	return ""
}

var File_api_series_series_proto protoreflect.FileDescriptor

var file_api_series_series_proto_rawDesc = []byte{
	0x0a, 0x17, 0x61, 0x70, 0x69, 0x2f, 0x73, 0x65, 0x72, 0x69, 0x65, 0x73, 0x2f, 0x73, 0x65, 0x72,
	0x69, 0x65, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0d, 0x62, 0x61, 0x63, 0x6b, 0x65,
	0x6e, 0x64, 0x53, 0x65, 0x72, 0x69, 0x65, 0x73, 0x1a, 0x3d, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62,
	0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x6d, 0x77, 0x69, 0x74, 0x6b, 0x6f, 0x77, 0x2f, 0x67, 0x6f, 0x2d,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2d, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x6f, 0x72, 0x73,
	0x40, 0x76, 0x30, 0x2e, 0x33, 0x2e, 0x32, 0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x6f,
	0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x2a, 0x0a, 0x14, 0x43, 0x69, 0x72, 0x63, 0x75,
	0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x65, 0x71, 0x12,
	0x12, 0x0a, 0x04, 0x75, 0x75, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x75,
	0x75, 0x69, 0x64, 0x22, 0x54, 0x0a, 0x18, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x43, 0x69, 0x72,
	0x63, 0x75, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x48, 0x61, 0x73, 0x68, 0x52, 0x65, 0x71, 0x12,
	0x12, 0x0a, 0x04, 0x68, 0x61, 0x73, 0x68, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x68,
	0x61, 0x73, 0x68, 0x12, 0x10, 0x0a, 0x03, 0x6d, 0x73, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x03, 0x6d, 0x73, 0x67, 0x12, 0x12, 0x0a, 0x04, 0x75, 0x75, 0x69, 0x64, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x04, 0x75, 0x75, 0x69, 0x64, 0x22, 0x92, 0x01, 0x0a, 0x1a, 0x47, 0x69,
	0x76, 0x65, 0x43, 0x75, 0x6c, 0x74, 0x75, 0x72, 0x65, 0x41, 0x72, 0x74, 0x77, 0x6f, 0x72, 0x6b,
	0x4f, 0x72, 0x64, 0x65, 0x72, 0x52, 0x65, 0x71, 0x12, 0x1c, 0x0a, 0x09, 0x6f, 0x72, 0x64, 0x65,
	0x72, 0x55, 0x75, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x6f, 0x72, 0x64,
	0x65, 0x72, 0x55, 0x75, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x66, 0x72, 0x6f, 0x6d, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x0d, 0x52, 0x04, 0x66, 0x72, 0x6f, 0x6d, 0x12, 0x0e, 0x0a, 0x02, 0x74, 0x6f,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x02, 0x74, 0x6f, 0x12, 0x1a, 0x0a, 0x08, 0x66, 0x72,
	0x6f, 0x6d, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x66, 0x72,
	0x6f, 0x6d, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x74, 0x6f, 0x4e, 0x61, 0x6d, 0x65,
	0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x74, 0x6f, 0x4e, 0x61, 0x6d, 0x65, 0x22, 0x84,
	0x01, 0x0a, 0x20, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x43, 0x61, 0x73, 0x74, 0x43, 0x75, 0x6c,
	0x74, 0x75, 0x72, 0x65, 0x41, 0x72, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x4f, 0x72, 0x64, 0x65, 0x72,
	0x52, 0x65, 0x71, 0x12, 0x1c, 0x0a, 0x09, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x55, 0x75, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x55, 0x75, 0x69,
	0x64, 0x12, 0x28, 0x0a, 0x0f, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e,
	0x48, 0x61, 0x73, 0x68, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x74, 0x72, 0x61, 0x6e,
	0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x48, 0x61, 0x73, 0x68, 0x12, 0x18, 0x0a, 0x07, 0x63,
	0x61, 0x73, 0x74, 0x4d, 0x73, 0x67, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x63, 0x61,
	0x73, 0x74, 0x4d, 0x73, 0x67, 0x22, 0x7f, 0x0a, 0x1d, 0x52, 0x65, 0x63, 0x65, 0x69, 0x76, 0x65,
	0x43, 0x75, 0x6c, 0x74, 0x75, 0x72, 0x65, 0x41, 0x72, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x4f, 0x72,
	0x64, 0x65, 0x72, 0x52, 0x65, 0x71, 0x12, 0x1c, 0x0a, 0x09, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x55,
	0x75, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x6f, 0x72, 0x64, 0x65, 0x72,
	0x55, 0x75, 0x69, 0x64, 0x12, 0x18, 0x0a, 0x07, 0x69, 0x73, 0x41, 0x67, 0x72, 0x65, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x69, 0x73, 0x41, 0x67, 0x72, 0x65, 0x65, 0x12, 0x0e,
	0x0a, 0x02, 0x74, 0x6f, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x02, 0x74, 0x6f, 0x12, 0x16,
	0x0a, 0x06, 0x74, 0x6f, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06,
	0x74, 0x6f, 0x4e, 0x61, 0x6d, 0x65, 0x22, 0xd1, 0x01, 0x0a, 0x15, 0x44, 0x72, 0x61, 0x77, 0x43,
	0x75, 0x6c, 0x74, 0x75, 0x72, 0x65, 0x41, 0x72, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x52, 0x65, 0x71,
	0x12, 0x2e, 0x0a, 0x12, 0x63, 0x75, 0x6c, 0x74, 0x75, 0x72, 0x65, 0x41, 0x72, 0x74, 0x77, 0x6f,
	0x72, 0x6b, 0x55, 0x75, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x12, 0x63, 0x75,
	0x6c, 0x74, 0x75, 0x72, 0x65, 0x41, 0x72, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x55, 0x75, 0x69, 0x64,
	0x12, 0x1a, 0x0a, 0x08, 0x69, 0x6e, 0x64, 0x65, 0x78, 0x4e, 0x75, 0x6d, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x0d, 0x52, 0x08, 0x69, 0x6e, 0x64, 0x65, 0x78, 0x4e, 0x75, 0x6d, 0x12, 0x16, 0x0a, 0x06,
	0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x06, 0x75, 0x73,
	0x65, 0x72, 0x49, 0x64, 0x12, 0x1e, 0x0a, 0x0a, 0x75, 0x73, 0x65, 0x72, 0x54, 0x65, 0x6c, 0x4e,
	0x75, 0x6d, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x75, 0x73, 0x65, 0x72, 0x54, 0x65,
	0x6c, 0x4e, 0x75, 0x6d, 0x12, 0x1a, 0x0a, 0x08, 0x75, 0x73, 0x65, 0x72, 0x4e, 0x61, 0x6d, 0x65,
	0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x75, 0x73, 0x65, 0x72, 0x4e, 0x61, 0x6d, 0x65,
	0x12, 0x18, 0x0a, 0x07, 0x70, 0x61, 0x79, 0x54, 0x79, 0x70, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28,
	0x0d, 0x52, 0x07, 0x70, 0x61, 0x79, 0x54, 0x79, 0x70, 0x65, 0x22, 0x99, 0x01, 0x0a, 0x0b, 0x43,
	0x75, 0x6c, 0x74, 0x75, 0x72, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x12, 0x10, 0x0a, 0x03, 0x75, 0x72,
	0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x75, 0x72, 0x6c, 0x12, 0x1a, 0x0a, 0x08,
	0x73, 0x74, 0x6f, 0x63, 0x6b, 0x4e, 0x75, 0x6d, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08,
	0x73, 0x74, 0x6f, 0x63, 0x6b, 0x4e, 0x75, 0x6d, 0x12, 0x1e, 0x0a, 0x0a, 0x73, 0x79, 0x73, 0x74,
	0x6f, 0x63, 0x6b, 0x4e, 0x75, 0x6d, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0a, 0x73, 0x79,
	0x73, 0x74, 0x6f, 0x63, 0x6b, 0x4e, 0x75, 0x6d, 0x12, 0x1e, 0x0a, 0x0a, 0x63, 0x75, 0x6c, 0x74,
	0x75, 0x72, 0x65, 0x4e, 0x75, 0x6d, 0x18, 0x04, 0x20, 0x03, 0x28, 0x05, 0x52, 0x0a, 0x63, 0x75,
	0x6c, 0x74, 0x75, 0x72, 0x65, 0x4e, 0x75, 0x6d, 0x12, 0x1c, 0x0a, 0x09, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x54, 0x79, 0x70, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x22, 0x53, 0x0a, 0x17, 0x43, 0x75, 0x6c, 0x74, 0x75, 0x72,
	0x65, 0x41, 0x72, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x65,
	0x71, 0x12, 0x12, 0x0a, 0x04, 0x75, 0x75, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x04, 0x75, 0x75, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6c, 0x61, 0x6e, 0x67, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x04, 0x6c, 0x61, 0x6e, 0x67, 0x12, 0x10, 0x0a, 0x03, 0x6e, 0x75, 0x6d,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x03, 0x6e, 0x75, 0x6d, 0x22, 0xa7, 0x02, 0x0a, 0x0b,
	0x53, 0x63, 0x61, 0x6e, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x12, 0x1e, 0x0a, 0x0a, 0x73,
	0x65, 0x72, 0x69, 0x65, 0x73, 0x55, 0x75, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0a, 0x73, 0x65, 0x72, 0x69, 0x65, 0x73, 0x55, 0x75, 0x69, 0x64, 0x12, 0x3c, 0x0a, 0x19, 0x73,
	0x65, 0x72, 0x69, 0x65, 0x73, 0x43, 0x75, 0x6c, 0x74, 0x75, 0x72, 0x61, 0x6c, 0x41, 0x72, 0x74,
	0x77, 0x6f, 0x72, 0x6b, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x19,
	0x73, 0x65, 0x72, 0x69, 0x65, 0x73, 0x43, 0x75, 0x6c, 0x74, 0x75, 0x72, 0x61, 0x6c, 0x41, 0x72,
	0x74, 0x77, 0x6f, 0x72, 0x6b, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x48, 0x61, 0x73,
	0x68, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x48, 0x61, 0x73, 0x68, 0x12, 0x22, 0x0a,
	0x0c, 0x66, 0x69, 0x72, 0x73, 0x74, 0x55, 0x73, 0x65, 0x72, 0x54, 0x65, 0x6c, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0c, 0x66, 0x69, 0x72, 0x73, 0x74, 0x55, 0x73, 0x65, 0x72, 0x54, 0x65,
	0x6c, 0x12, 0x2a, 0x0a, 0x10, 0x73, 0x74, 0x61, 0x72, 0x74, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65,
	0x64, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x73, 0x74, 0x61,
	0x72, 0x74, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x26, 0x0a,
	0x0e, 0x65, 0x6e, 0x64, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x18,
	0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x65, 0x6e, 0x64, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65,
	0x64, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x70, 0x61, 0x67, 0x65, 0x18, 0x07, 0x20,
	0x01, 0x28, 0x04, 0x52, 0x04, 0x70, 0x61, 0x67, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x61, 0x67,
	0x65, 0x53, 0x69, 0x7a, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x04, 0x52, 0x08, 0x70, 0x61, 0x67,
	0x65, 0x53, 0x69, 0x7a, 0x65, 0x22, 0x67, 0x0a, 0x15, 0x43, 0x75, 0x6c, 0x74, 0x75, 0x72, 0x65,
	0x41, 0x72, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x12, 0x1e,
	0x0a, 0x0a, 0x73, 0x65, 0x72, 0x69, 0x65, 0x73, 0x55, 0x75, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0a, 0x73, 0x65, 0x72, 0x69, 0x65, 0x73, 0x55, 0x75, 0x69, 0x64, 0x12, 0x12,
	0x0a, 0x04, 0x70, 0x61, 0x67, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x04, 0x52, 0x04, 0x70, 0x61,
	0x67, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x61, 0x67, 0x65, 0x53, 0x69, 0x7a, 0x65, 0x18, 0x08,
	0x20, 0x01, 0x28, 0x04, 0x52, 0x08, 0x70, 0x61, 0x67, 0x65, 0x53, 0x69, 0x7a, 0x65, 0x22, 0x51,
	0x0a, 0x0c, 0x53, 0x63, 0x61, 0x6e, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x70, 0x12, 0x2b,
	0x0a, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x62,
	0x61, 0x63, 0x6b, 0x65, 0x6e, 0x64, 0x53, 0x65, 0x72, 0x69, 0x65, 0x73, 0x2e, 0x53, 0x63, 0x61,
	0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x12, 0x14, 0x0a, 0x05, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x22, 0xd2, 0x01, 0x0a, 0x08, 0x53, 0x63, 0x61, 0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x12,
	0x0a, 0x04, 0x75, 0x75, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x75, 0x75,
	0x69, 0x64, 0x12, 0x1e, 0x0a, 0x0a, 0x73, 0x65, 0x72, 0x69, 0x65, 0x73, 0x55, 0x75, 0x69, 0x64,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x73, 0x65, 0x72, 0x69, 0x65, 0x73, 0x55, 0x75,
	0x69, 0x64, 0x12, 0x3c, 0x0a, 0x19, 0x73, 0x65, 0x72, 0x69, 0x65, 0x73, 0x43, 0x75, 0x6c, 0x74,
	0x75, 0x72, 0x61, 0x6c, 0x41, 0x72, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x4e, 0x61, 0x6d, 0x65, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x19, 0x73, 0x65, 0x72, 0x69, 0x65, 0x73, 0x43, 0x75, 0x6c,
	0x74, 0x75, 0x72, 0x61, 0x6c, 0x41, 0x72, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x4e, 0x61, 0x6d, 0x65,
	0x12, 0x12, 0x0a, 0x04, 0x68, 0x61, 0x73, 0x68, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04,
	0x68, 0x61, 0x73, 0x68, 0x12, 0x22, 0x0a, 0x0c, 0x66, 0x69, 0x72, 0x73, 0x74, 0x55, 0x73, 0x65,
	0x72, 0x54, 0x65, 0x6c, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x66, 0x69, 0x72, 0x73,
	0x74, 0x55, 0x73, 0x65, 0x72, 0x54, 0x65, 0x6c, 0x12, 0x1c, 0x0a, 0x09, 0x63, 0x72, 0x65, 0x61,
	0x74, 0x65, 0x64, 0x41, 0x74, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x63, 0x72, 0x65,
	0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x22, 0x9e, 0x01, 0x0a, 0x1a, 0x43, 0x75, 0x6c, 0x74, 0x75,
	0x72, 0x65, 0x41, 0x72, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x4c, 0x69,
	0x73, 0x74, 0x52, 0x65, 0x71, 0x12, 0x16, 0x0a, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x12, 0x12, 0x0a,
	0x04, 0x70, 0x61, 0x67, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x04, 0x52, 0x04, 0x70, 0x61, 0x67,
	0x65, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x61, 0x67, 0x65, 0x53, 0x69, 0x7a, 0x65, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x04, 0x52, 0x08, 0x70, 0x61, 0x67, 0x65, 0x53, 0x69, 0x7a, 0x65, 0x12, 0x20, 0x0a,
	0x0b, 0x69, 0x73, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x48, 0x61, 0x73, 0x68, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x08, 0x52, 0x0b, 0x69, 0x73, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x48, 0x61, 0x73, 0x68, 0x12,
	0x16, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0d, 0x52,
	0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x22, 0x7a, 0x0a, 0x12, 0x43, 0x69, 0x72, 0x63, 0x75,
	0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x12, 0x1c, 0x0a,
	0x09, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x55, 0x75, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x09, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x55, 0x75, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x70,
	0x61, 0x67, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x04, 0x52, 0x04, 0x70, 0x61, 0x67, 0x65, 0x12,
	0x1a, 0x0a, 0x08, 0x70, 0x61, 0x67, 0x65, 0x53, 0x69, 0x7a, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x04, 0x52, 0x08, 0x70, 0x61, 0x67, 0x65, 0x53, 0x69, 0x7a, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x75,
	0x73, 0x65, 0x72, 0x49, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x06, 0x75, 0x73, 0x65,
	0x72, 0x49, 0x64, 0x22, 0x54, 0x0a, 0x1c, 0x43, 0x75, 0x6c, 0x74, 0x75, 0x72, 0x65, 0x41, 0x72,
	0x74, 0x77, 0x6f, 0x72, 0x6b, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c,
	0x52, 0x65, 0x71, 0x12, 0x1c, 0x0a, 0x09, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x55, 0x75, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x55, 0x75, 0x69,
	0x64, 0x12, 0x16, 0x0a, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x0d, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x22, 0x5b, 0x0a, 0x13, 0x43, 0x69, 0x72,
	0x63, 0x75, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x70,
	0x12, 0x2e, 0x0a, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1a,
	0x2e, 0x62, 0x61, 0x63, 0x6b, 0x65, 0x6e, 0x64, 0x53, 0x65, 0x72, 0x69, 0x65, 0x73, 0x2e, 0x43,
	0x69, 0x72, 0x63, 0x75, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x04, 0x6c, 0x69, 0x73, 0x74,
	0x12, 0x14, 0x0a, 0x05, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x05, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x22, 0xe7, 0x02, 0x0a, 0x0b, 0x43, 0x69, 0x72, 0x63, 0x75,
	0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x1e, 0x0a, 0x0a, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e,
	0x54, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x61, 0x63, 0x74, 0x69,
	0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1c, 0x0a, 0x09, 0x74, 0x72, 0x61, 0x64, 0x65, 0x54,
	0x69, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x74, 0x72, 0x61, 0x64, 0x65,
	0x54, 0x69, 0x6d, 0x65, 0x12, 0x22, 0x0a, 0x0c, 0x72, 0x65, 0x63, 0x65, 0x69, 0x76, 0x65, 0x72,
	0x4e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x72, 0x65, 0x63, 0x65,
	0x69, 0x76, 0x65, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x75, 0x75, 0x69, 0x64,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x75, 0x75, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04,
	0x68, 0x61, 0x73, 0x68, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x68, 0x61, 0x73, 0x68,
	0x12, 0x1c, 0x0a, 0x09, 0x67, 0x69, 0x76, 0x65, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x06, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x09, 0x67, 0x69, 0x76, 0x65, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x20,
	0x0a, 0x0b, 0x72, 0x65, 0x63, 0x65, 0x69, 0x76, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x07, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0b, 0x72, 0x65, 0x63, 0x65, 0x69, 0x76, 0x65, 0x54, 0x69, 0x6d, 0x65,
	0x12, 0x22, 0x0a, 0x0c, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x66, 0x65, 0x72, 0x54, 0x69, 0x6d, 0x65,
	0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x66, 0x65, 0x72,
	0x54, 0x69, 0x6d, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x09, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x62, 0x72, 0x69, 0x65,
	0x66, 0x49, 0x6d, 0x67, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x62, 0x72, 0x69, 0x65,
	0x66, 0x49, 0x6d, 0x67, 0x12, 0x1a, 0x0a, 0x08, 0x73, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x49, 0x64,
	0x18, 0x0b, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x08, 0x73, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x49, 0x64,
	0x12, 0x1e, 0x0a, 0x0a, 0x72, 0x65, 0x63, 0x65, 0x69, 0x76, 0x65, 0x72, 0x49, 0x64, 0x18, 0x0c,
	0x20, 0x01, 0x28, 0x0d, 0x52, 0x0a, 0x72, 0x65, 0x63, 0x65, 0x69, 0x76, 0x65, 0x72, 0x49, 0x64,
	0x22, 0x3a, 0x0a, 0x0c, 0x4c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65, 0x49, 0x6e, 0x66, 0x6f,
	0x12, 0x12, 0x0a, 0x04, 0x6c, 0x61, 0x6e, 0x67, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04,
	0x6c, 0x61, 0x6e, 0x67, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x22, 0xd0, 0x04, 0x0a,
	0x0d, 0x53, 0x61, 0x76, 0x65, 0x53, 0x65, 0x72, 0x69, 0x65, 0x73, 0x52, 0x65, 0x71, 0x12, 0x18,
	0x0a, 0x07, 0x62, 0x72, 0x61, 0x6e, 0x64, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x07, 0x62, 0x72, 0x61, 0x6e, 0x64, 0x49, 0x64, 0x12, 0x1c, 0x0a, 0x09, 0x62, 0x72, 0x61, 0x6e,
	0x64, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x62, 0x72, 0x61,
	0x6e, 0x64, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x3c, 0x0a, 0x08, 0x63, 0x6f, 0x76, 0x65, 0x72, 0x49,
	0x6d, 0x67, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x42, 0x20, 0xe2, 0xdf, 0x1f, 0x1c, 0x2a, 0x18,
	0xe7, 0xb3, 0xbb, 0xe5, 0x88, 0x97, 0xe5, 0x9b, 0xbe, 0xe7, 0x89, 0x87, 0xe4, 0xb8, 0x8d, 0xe8,
	0x83, 0xbd, 0xe4, 0xb8, 0xba, 0xe7, 0xa9, 0xba, 0x58, 0x01, 0x52, 0x08, 0x63, 0x6f, 0x76, 0x65,
	0x72, 0x49, 0x6d, 0x67, 0x12, 0x3d, 0x0a, 0x0a, 0x73, 0x65, 0x72, 0x69, 0x65, 0x73, 0x4e, 0x61,
	0x6d, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x42, 0x1d, 0xe2, 0xdf, 0x1f, 0x19, 0x2a, 0x15,
	0xe7, 0xb3, 0xbb, 0xe5, 0x88, 0x97, 0xe5, 0x90, 0x8d, 0xe4, 0xb8, 0x8d, 0xe8, 0x83, 0xbd, 0xe4,
	0xb8, 0xba, 0xe7, 0xa9, 0xba, 0x58, 0x01, 0x52, 0x0a, 0x73, 0x65, 0x72, 0x69, 0x65, 0x73, 0x4e,
	0x61, 0x6d, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x64, 0x65, 0x73, 0x63, 0x18, 0x08, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x04, 0x64, 0x65, 0x73, 0x63, 0x12, 0x12, 0x0a, 0x04, 0x6c, 0x61, 0x6e, 0x67, 0x18,
	0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6c, 0x61, 0x6e, 0x67, 0x12, 0x1e, 0x0a, 0x0a, 0x73,
	0x65, 0x72, 0x69, 0x65, 0x73, 0x55, 0x75, 0x69, 0x64, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0a, 0x73, 0x65, 0x72, 0x69, 0x65, 0x73, 0x55, 0x75, 0x69, 0x64, 0x12, 0x1e, 0x0a, 0x0a, 0x61,
	0x63, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x6f, 0x64, 0x65, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0a, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x3c, 0x0a, 0x08, 0x61,
	0x72, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x18, 0x0d, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x20, 0x2e,
	0x62, 0x61, 0x63, 0x6b, 0x65, 0x6e, 0x64, 0x53, 0x65, 0x72, 0x69, 0x65, 0x73, 0x2e, 0x53, 0x65,
	0x72, 0x69, 0x65, 0x73, 0x41, 0x72, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x44, 0x61, 0x74, 0x61, 0x52,
	0x08, 0x61, 0x72, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x12, 0x37, 0x0a, 0x07, 0x61, 0x75, 0x63,
	0x74, 0x69, 0x6f, 0x6e, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x62, 0x61, 0x63,
	0x6b, 0x65, 0x6e, 0x64, 0x53, 0x65, 0x72, 0x69, 0x65, 0x73, 0x2e, 0x41, 0x75, 0x63, 0x74, 0x69,
	0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x52, 0x07, 0x61, 0x75, 0x63, 0x74, 0x69,
	0x6f, 0x6e, 0x12, 0x20, 0x0a, 0x0b, 0x61, 0x75, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x55, 0x75, 0x69,
	0x64, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x61, 0x75, 0x63, 0x74, 0x69, 0x6f, 0x6e,
	0x55, 0x75, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6c, 0x69, 0x6e, 0x6b, 0x18, 0x16, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x04, 0x6c, 0x69, 0x6e, 0x6b, 0x12, 0x59, 0x0a, 0x15, 0x73, 0x65, 0x72, 0x69,
	0x65, 0x73, 0x43, 0x75, 0x6c, 0x74, 0x75, 0x72, 0x65, 0x41, 0x72, 0x74, 0x77, 0x6f, 0x72, 0x6b,
	0x73, 0x18, 0x17, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x23, 0x2e, 0x62, 0x61, 0x63, 0x6b, 0x65, 0x6e,
	0x64, 0x53, 0x65, 0x72, 0x69, 0x65, 0x73, 0x2e, 0x53, 0x65, 0x72, 0x69, 0x65, 0x73, 0x43, 0x75,
	0x6c, 0x74, 0x75, 0x72, 0x65, 0x41, 0x72, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x52, 0x15, 0x73, 0x65,
	0x72, 0x69, 0x65, 0x73, 0x43, 0x75, 0x6c, 0x74, 0x75, 0x72, 0x65, 0x41, 0x72, 0x74, 0x77, 0x6f,
	0x72, 0x6b, 0x73, 0x12, 0x1a, 0x0a, 0x08, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x63, 0x79, 0x18,
	0x10, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x63, 0x79, 0x22,
	0xfe, 0x04, 0x0a, 0x14, 0x53, 0x65, 0x72, 0x69, 0x65, 0x73, 0x43, 0x75, 0x6c, 0x74, 0x75, 0x72,
	0x65, 0x41, 0x72, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x12, 0x12, 0x0a, 0x04, 0x75, 0x75, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x75, 0x75, 0x69, 0x64, 0x12, 0x1e, 0x0a, 0x0a,
	0x73, 0x65, 0x72, 0x69, 0x65, 0x73, 0x55, 0x75, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0a, 0x73, 0x65, 0x72, 0x69, 0x65, 0x73, 0x55, 0x75, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04,
	0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65,
	0x12, 0x1e, 0x0a, 0x0a, 0x63, 0x75, 0x6c, 0x74, 0x75, 0x72, 0x65, 0x4e, 0x75, 0x6d, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x63, 0x75, 0x6c, 0x74, 0x75, 0x72, 0x65, 0x4e, 0x75, 0x6d,
	0x12, 0x1e, 0x0a, 0x0a, 0x62, 0x72, 0x69, 0x65, 0x66, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x62, 0x72, 0x69, 0x65, 0x66, 0x49, 0x6d, 0x61, 0x67, 0x65,
	0x12, 0x12, 0x0a, 0x04, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04,
	0x73, 0x69, 0x7a, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x18, 0x07,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x12, 0x1a, 0x0a, 0x08,
	0x6d, 0x61, 0x69, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08,
	0x6d, 0x61, 0x69, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x12, 0x3c, 0x0a, 0x0b, 0x6d, 0x61, 0x69, 0x6e,
	0x49, 0x6d, 0x67, 0x49, 0x6e, 0x66, 0x6f, 0x18, 0x09, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1a, 0x2e,
	0x62, 0x61, 0x63, 0x6b, 0x65, 0x6e, 0x64, 0x53, 0x65, 0x72, 0x69, 0x65, 0x73, 0x2e, 0x43, 0x75,
	0x6c, 0x74, 0x75, 0x72, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x52, 0x0b, 0x6d, 0x61, 0x69, 0x6e, 0x49,
	0x6d, 0x67, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x0a, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x02, 0x69, 0x64, 0x12, 0x1a, 0x0a, 0x08, 0x73, 0x74, 0x6f, 0x63, 0x6b, 0x4e,
	0x75, 0x6d, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x08, 0x73, 0x74, 0x6f, 0x63, 0x6b, 0x4e,
	0x75, 0x6d, 0x12, 0x12, 0x0a, 0x04, 0x68, 0x61, 0x73, 0x68, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x04, 0x68, 0x61, 0x73, 0x68, 0x12, 0x12, 0x0a, 0x04, 0x6c, 0x61, 0x6e, 0x67, 0x18, 0x0d,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6c, 0x61, 0x6e, 0x67, 0x12, 0x14, 0x0a, 0x05, 0x70, 0x72,
	0x69, 0x63, 0x65, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x70, 0x72, 0x69, 0x63, 0x65,
	0x12, 0x18, 0x0a, 0x07, 0x6c, 0x65, 0x66, 0x74, 0x4e, 0x75, 0x6d, 0x18, 0x0f, 0x20, 0x01, 0x28,
	0x0d, 0x52, 0x07, 0x6c, 0x65, 0x66, 0x74, 0x4e, 0x75, 0x6d, 0x12, 0x18, 0x0a, 0x07, 0x73, 0x6f,
	0x6c, 0x64, 0x4e, 0x75, 0x6d, 0x18, 0x10, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x07, 0x73, 0x6f, 0x6c,
	0x64, 0x4e, 0x75, 0x6d, 0x12, 0x1a, 0x0a, 0x08, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x63, 0x79,
	0x18, 0x11, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x63, 0x79,
	0x12, 0x1e, 0x0a, 0x0a, 0x69, 0x73, 0x52, 0x65, 0x63, 0x65, 0x69, 0x76, 0x65, 0x64, 0x18, 0x12,
	0x20, 0x01, 0x28, 0x0d, 0x52, 0x0a, 0x69, 0x73, 0x52, 0x65, 0x63, 0x65, 0x69, 0x76, 0x65, 0x64,
	0x12, 0x3c, 0x0a, 0x0b, 0x63, 0x75, 0x6c, 0x74, 0x75, 0x72, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x18,
	0x13, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x62, 0x61, 0x63, 0x6b, 0x65, 0x6e, 0x64, 0x53,
	0x65, 0x72, 0x69, 0x65, 0x73, 0x2e, 0x43, 0x75, 0x6c, 0x74, 0x75, 0x72, 0x65, 0x49, 0x74, 0x65,
	0x6d, 0x52, 0x0b, 0x63, 0x75, 0x6c, 0x74, 0x75, 0x72, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x12, 0x22,
	0x0a, 0x0c, 0x62, 0x72, 0x61, 0x6e, 0x64, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x18, 0x14,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x62, 0x72, 0x61, 0x6e, 0x64, 0x41, 0x64, 0x64, 0x72, 0x65,
	0x73, 0x73, 0x12, 0x1c, 0x0a, 0x09, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x18,
	0x15, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x54, 0x79, 0x70, 0x65,
	0x22, 0xca, 0x03, 0x0a, 0x0e, 0x41, 0x75, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x12, 0x18, 0x0a, 0x07, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x12, 0x14, 0x0a,
	0x05, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x69, 0x6d,
	0x61, 0x67, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x06, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x04, 0x69, 0x6e, 0x66, 0x6f, 0x12, 0x1c, 0x0a, 0x09, 0x73, 0x74, 0x61, 0x72, 0x74,
	0x44, 0x61, 0x74, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x73, 0x74, 0x61, 0x72,
	0x74, 0x44, 0x61, 0x74, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x18, 0x08,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x12, 0x1e, 0x0a, 0x0a, 0x73,
	0x74, 0x61, 0x72, 0x74, 0x54, 0x69, 0x74, 0x6c, 0x65, 0x18, 0x10, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0a, 0x73, 0x74, 0x61, 0x72, 0x74, 0x54, 0x69, 0x74, 0x6c, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x6c,
	0x61, 0x6e, 0x67, 0x18, 0x12, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6c, 0x61, 0x6e, 0x67, 0x12,
	0x1c, 0x0a, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x1c, 0x0a,
	0x09, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x64, 0x41, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0d,
	0x52, 0x09, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x0e, 0x0a, 0x02, 0x49,
	0x44, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x02, 0x49, 0x44, 0x12, 0x1c, 0x0a, 0x09, 0x75,
	0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09,
	0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x12, 0x0a, 0x04, 0x75, 0x75, 0x69,
	0x64, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x75, 0x75, 0x69, 0x64, 0x12, 0x1a, 0x0a,
	0x08, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x4e, 0x75, 0x6d, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0d, 0x52,
	0x08, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x4e, 0x75, 0x6d, 0x12, 0x1a, 0x0a, 0x08, 0x69, 0x73, 0x4c,
	0x69, 0x76, 0x69, 0x6e, 0x67, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x08, 0x69, 0x73, 0x4c,
	0x69, 0x76, 0x69, 0x6e, 0x67, 0x12, 0x32, 0x0a, 0x14, 0x69, 0x73, 0x45, 0x78, 0x69, 0x73, 0x74,
	0x4c, 0x69, 0x76, 0x69, 0x6e, 0x67, 0x41, 0x72, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x18, 0x0f, 0x20,
	0x01, 0x28, 0x08, 0x52, 0x14, 0x69, 0x73, 0x45, 0x78, 0x69, 0x73, 0x74, 0x4c, 0x69, 0x76, 0x69,
	0x6e, 0x67, 0x41, 0x72, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x12, 0x22, 0x0a, 0x0c, 0x6f, 0x70, 0x65,
	0x72, 0x61, 0x74, 0x6f, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x11, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0c, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x22, 0x36, 0x0a,
	0x0e, 0x53, 0x61, 0x76, 0x65, 0x53, 0x65, 0x72, 0x69, 0x65, 0x73, 0x52, 0x65, 0x73, 0x70, 0x12,
	0x10, 0x0a, 0x03, 0x6d, 0x73, 0x67, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6d, 0x73,
	0x67, 0x12, 0x12, 0x0a, 0x04, 0x75, 0x75, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x04, 0x75, 0x75, 0x69, 0x64, 0x22, 0xa9, 0x01, 0x0a, 0x0f, 0x53, 0x65, 0x72, 0x69, 0x65, 0x73,
	0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x65, 0x71, 0x12, 0x3c, 0x0a, 0x0a, 0x73, 0x65, 0x72,
	0x69, 0x65, 0x73, 0x55, 0x75, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x1c, 0xe2,
	0xdf, 0x1f, 0x18, 0x2a, 0x14, 0xe7, 0xb3, 0xbb, 0xe5, 0x88, 0x97, 0x49, 0x44, 0xe4, 0xb8, 0x8d,
	0xe8, 0x83, 0xbd, 0xe4, 0xb8, 0xba, 0xe7, 0xa9, 0xba, 0x58, 0x01, 0x52, 0x0a, 0x73, 0x65, 0x72,
	0x69, 0x65, 0x73, 0x55, 0x75, 0x69, 0x64, 0x12, 0x10, 0x0a, 0x03, 0x6e, 0x75, 0x6d, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x03, 0x6e, 0x75, 0x6d, 0x12, 0x12, 0x0a, 0x04, 0x6c, 0x61, 0x6e,
	0x67, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6c, 0x61, 0x6e, 0x67, 0x12, 0x14, 0x0a,
	0x05, 0x6c, 0x61, 0x6e, 0x67, 0x73, 0x18, 0x05, 0x20, 0x03, 0x28, 0x09, 0x52, 0x05, 0x6c, 0x61,
	0x6e, 0x67, 0x73, 0x12, 0x1c, 0x0a, 0x09, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x49, 0x64,
	0x18, 0x06, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x09, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x49,
	0x64, 0x22, 0x67, 0x0a, 0x16, 0x43, 0x75, 0x6c, 0x74, 0x75, 0x72, 0x65, 0x41, 0x72, 0x74, 0x77,
	0x6f, 0x72, 0x6b, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x70, 0x12, 0x37, 0x0a, 0x04, 0x6c,
	0x69, 0x73, 0x74, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x23, 0x2e, 0x62, 0x61, 0x63, 0x6b,
	0x65, 0x6e, 0x64, 0x53, 0x65, 0x72, 0x69, 0x65, 0x73, 0x2e, 0x53, 0x65, 0x72, 0x69, 0x65, 0x73,
	0x43, 0x75, 0x6c, 0x74, 0x75, 0x72, 0x65, 0x41, 0x72, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x52, 0x04,
	0x6c, 0x69, 0x73, 0x74, 0x12, 0x14, 0x0a, 0x05, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x05, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x22, 0x6a, 0x0a, 0x1a, 0x43, 0x75,
	0x6c, 0x74, 0x75, 0x72, 0x65, 0x41, 0x72, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x4f, 0x72, 0x64, 0x65,
	0x72, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x12, 0x36, 0x0a, 0x04, 0x6c, 0x69, 0x73, 0x74,
	0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x62, 0x61, 0x63, 0x6b, 0x65, 0x6e, 0x64,
	0x53, 0x65, 0x72, 0x69, 0x65, 0x73, 0x2e, 0x43, 0x75, 0x6c, 0x74, 0x75, 0x72, 0x65, 0x41, 0x72,
	0x74, 0x77, 0x6f, 0x72, 0x6b, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x52, 0x04, 0x6c, 0x69, 0x73, 0x74,
	0x12, 0x14, 0x0a, 0x05, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x05, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x22, 0xc3, 0x07, 0x0a, 0x13, 0x43, 0x75, 0x6c, 0x74, 0x75,
	0x72, 0x65, 0x41, 0x72, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x12, 0x12,
	0x0a, 0x04, 0x75, 0x75, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x75, 0x75,
	0x69, 0x64, 0x12, 0x1e, 0x0a, 0x0a, 0x73, 0x65, 0x72, 0x69, 0x65, 0x73, 0x55, 0x75, 0x69, 0x64,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x73, 0x65, 0x72, 0x69, 0x65, 0x73, 0x55, 0x75,
	0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x68, 0x61, 0x73, 0x68, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x68, 0x61, 0x73, 0x68, 0x12, 0x16, 0x0a, 0x06, 0x74, 0x65,
	0x6c, 0x4e, 0x75, 0x6d, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x74, 0x65, 0x6c, 0x4e,
	0x75, 0x6d, 0x12, 0x1c, 0x0a, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x18,
	0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74,
	0x12, 0x1a, 0x0a, 0x08, 0x62, 0x72, 0x69, 0x65, 0x66, 0x49, 0x6d, 0x67, 0x18, 0x07, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x08, 0x62, 0x72, 0x69, 0x65, 0x66, 0x49, 0x6d, 0x67, 0x12, 0x16, 0x0a, 0x06,
	0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x06, 0x73, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x12, 0x1e, 0x0a, 0x0a, 0x62, 0x61, 0x73, 0x65, 0x53, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0a, 0x62, 0x61, 0x73, 0x65, 0x53, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x12, 0x1c, 0x0a, 0x09, 0x6e, 0x6f, 0x77, 0x55, 0x73, 0x65, 0x72, 0x49,
	0x64, 0x18, 0x12, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x09, 0x6e, 0x6f, 0x77, 0x55, 0x73, 0x65, 0x72,
	0x49, 0x64, 0x12, 0x20, 0x0a, 0x0b, 0x6e, 0x6f, 0x77, 0x55, 0x73, 0x65, 0x72, 0x4e, 0x61, 0x6d,
	0x65, 0x18, 0x13, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x6e, 0x6f, 0x77, 0x55, 0x73, 0x65, 0x72,
	0x4e, 0x61, 0x6d, 0x65, 0x12, 0x22, 0x0a, 0x0c, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x66, 0x65, 0x72,
	0x54, 0x69, 0x6d, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x74, 0x72, 0x61, 0x6e,
	0x73, 0x66, 0x65, 0x72, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x1c, 0x0a, 0x09, 0x74, 0x72, 0x61, 0x64,
	0x65, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x74, 0x72, 0x61,
	0x64, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x72, 0x65, 0x63, 0x65, 0x69, 0x76,
	0x65, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x72, 0x65, 0x63,
	0x65, 0x69, 0x76, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x1c, 0x0a, 0x09, 0x62, 0x72, 0x61, 0x6e,
	0x64, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x62, 0x72, 0x61,
	0x6e, 0x64, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x3c, 0x0a, 0x19, 0x73, 0x65, 0x72, 0x69, 0x65, 0x73,
	0x43, 0x75, 0x6c, 0x74, 0x75, 0x72, 0x61, 0x6c, 0x41, 0x72, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x55,
	0x75, 0x69, 0x64, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x09, 0x52, 0x19, 0x73, 0x65, 0x72, 0x69, 0x65,
	0x73, 0x43, 0x75, 0x6c, 0x74, 0x75, 0x72, 0x61, 0x6c, 0x41, 0x72, 0x74, 0x77, 0x6f, 0x72, 0x6b,
	0x55, 0x75, 0x69, 0x64, 0x12, 0x1a, 0x0a, 0x08, 0x69, 0x6e, 0x64, 0x65, 0x78, 0x4e, 0x75, 0x6d,
	0x18, 0x0f, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x08, 0x69, 0x6e, 0x64, 0x65, 0x78, 0x4e, 0x75, 0x6d,
	0x12, 0x22, 0x0a, 0x0c, 0x62, 0x72, 0x61, 0x6e, 0x64, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73,
	0x18, 0x10, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x62, 0x72, 0x61, 0x6e, 0x64, 0x41, 0x64, 0x64,
	0x72, 0x65, 0x73, 0x73, 0x12, 0x3c, 0x0a, 0x0b, 0x63, 0x75, 0x6c, 0x74, 0x75, 0x72, 0x65, 0x49,
	0x74, 0x65, 0x6d, 0x18, 0x11, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x62, 0x61, 0x63, 0x6b,
	0x65, 0x6e, 0x64, 0x53, 0x65, 0x72, 0x69, 0x65, 0x73, 0x2e, 0x43, 0x75, 0x6c, 0x74, 0x75, 0x72,
	0x65, 0x49, 0x74, 0x65, 0x6d, 0x52, 0x0b, 0x63, 0x75, 0x6c, 0x74, 0x75, 0x72, 0x65, 0x49, 0x74,
	0x65, 0x6d, 0x12, 0x1c, 0x0a, 0x09, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x18,
	0x15, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x54, 0x79, 0x70, 0x65,
	0x12, 0x1c, 0x0a, 0x09, 0x70, 0x61, 0x79, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x16, 0x20,
	0x01, 0x28, 0x0d, 0x52, 0x09, 0x70, 0x61, 0x79, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x18,
	0x0a, 0x07, 0x70, 0x61, 0x79, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x17, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x07, 0x70, 0x61, 0x79, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x22, 0x0a, 0x0c, 0x70, 0x61, 0x79, 0x50,
	0x70, 0x72, 0x6f, 0x66, 0x4c, 0x69, 0x73, 0x74, 0x18, 0x18, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0c,
	0x70, 0x61, 0x79, 0x50, 0x70, 0x72, 0x6f, 0x66, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x1a, 0x0a, 0x08,
	0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x63, 0x79, 0x18, 0x19, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08,
	0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x63, 0x79, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x61, 0x79, 0x50,
	0x72, 0x69, 0x63, 0x65, 0x18, 0x1a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x70, 0x61, 0x79, 0x50,
	0x72, 0x69, 0x63, 0x65, 0x12, 0x1e, 0x0a, 0x0a, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72,
	0x49, 0x64, 0x18, 0x1b, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0a, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74,
	0x6f, 0x72, 0x49, 0x64, 0x12, 0x26, 0x0a, 0x0e, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72,
	0x54, 0x65, 0x6c, 0x4e, 0x75, 0x6d, 0x18, 0x1c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x6f, 0x70,
	0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x54, 0x65, 0x6c, 0x4e, 0x75, 0x6d, 0x12, 0x1c, 0x0a, 0x09,
	0x73, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x18, 0x1d, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x09, 0x73, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x72, 0x65,
	0x6d, 0x61, 0x72, 0x6b, 0x18, 0x1e, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x72, 0x65, 0x6d, 0x61,
	0x72, 0x6b, 0x12, 0x18, 0x0a, 0x07, 0x70, 0x61, 0x79, 0x54, 0x79, 0x70, 0x65, 0x18, 0x1f, 0x20,
	0x01, 0x28, 0x0d, 0x52, 0x07, 0x70, 0x61, 0x79, 0x54, 0x79, 0x70, 0x65, 0x22, 0xa3, 0x08, 0x0a,
	0x10, 0x53, 0x65, 0x72, 0x69, 0x65, 0x73, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x65, 0x73,
	0x70, 0x12, 0x18, 0x0a, 0x07, 0x62, 0x72, 0x61, 0x6e, 0x64, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x07, 0x62, 0x72, 0x61, 0x6e, 0x64, 0x49, 0x64, 0x12, 0x1c, 0x0a, 0x09, 0x62,
	0x72, 0x61, 0x6e, 0x64, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09,
	0x62, 0x72, 0x61, 0x6e, 0x64, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x28, 0x0a, 0x0f, 0x6c, 0x61, 0x75,
	0x6e, 0x63, 0x68, 0x53, 0x74, 0x61, 0x72, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0f, 0x6c, 0x61, 0x75, 0x6e, 0x63, 0x68, 0x53, 0x74, 0x61, 0x72, 0x74, 0x54,
	0x69, 0x6d, 0x65, 0x12, 0x24, 0x0a, 0x0d, 0x6c, 0x61, 0x75, 0x6e, 0x63, 0x68, 0x45, 0x6e, 0x64,
	0x54, 0x69, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x6c, 0x61, 0x75, 0x6e,
	0x63, 0x68, 0x45, 0x6e, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x1e, 0x0a, 0x0a, 0x73, 0x68, 0x65,
	0x6c, 0x66, 0x53, 0x74, 0x61, 0x74, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0a, 0x73,
	0x68, 0x65, 0x6c, 0x66, 0x53, 0x74, 0x61, 0x74, 0x65, 0x12, 0x1e, 0x0a, 0x0a, 0x61, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x61,
	0x63, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x63, 0x6f, 0x76,
	0x65, 0x72, 0x49, 0x6d, 0x67, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x63, 0x6f, 0x76,
	0x65, 0x72, 0x49, 0x6d, 0x67, 0x12, 0x1e, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54,
	0x69, 0x6d, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74,
	0x65, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x1e, 0x0a, 0x0a, 0x73, 0x65, 0x72, 0x69, 0x65, 0x73, 0x55,
	0x75, 0x69, 0x64, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x73, 0x65, 0x72, 0x69, 0x65,
	0x73, 0x55, 0x75, 0x69, 0x64, 0x12, 0x4e, 0x0a, 0x0a, 0x73, 0x65, 0x72, 0x69, 0x65, 0x73, 0x4c,
	0x61, 0x6e, 0x67, 0x18, 0x0a, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2e, 0x2e, 0x62, 0x61, 0x63, 0x6b,
	0x65, 0x6e, 0x64, 0x53, 0x65, 0x72, 0x69, 0x65, 0x73, 0x2e, 0x53, 0x65, 0x72, 0x69, 0x65, 0x73,
	0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x65, 0x73, 0x70, 0x2e, 0x53, 0x65, 0x72, 0x69, 0x65,
	0x73, 0x4c, 0x61, 0x6e, 0x67, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x0a, 0x73, 0x65, 0x72, 0x69, 0x65,
	0x73, 0x4c, 0x61, 0x6e, 0x67, 0x12, 0x10, 0x0a, 0x03, 0x6d, 0x73, 0x67, 0x18, 0x0b, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x03, 0x6d, 0x73, 0x67, 0x12, 0x1e, 0x0a, 0x0a, 0x61, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x43, 0x6f, 0x64, 0x65, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x61, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12,
	0x1e, 0x0a, 0x0a, 0x73, 0x65, 0x72, 0x69, 0x65, 0x73, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x0e, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0a, 0x73, 0x65, 0x72, 0x69, 0x65, 0x73, 0x4e, 0x61, 0x6d, 0x65, 0x12,
	0x12, 0x0a, 0x04, 0x64, 0x65, 0x73, 0x63, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x64,
	0x65, 0x73, 0x63, 0x12, 0x12, 0x0a, 0x04, 0x6c, 0x61, 0x6e, 0x67, 0x18, 0x10, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x04, 0x6c, 0x61, 0x6e, 0x67, 0x12, 0x20, 0x0a, 0x0b, 0x6f, 0x6e, 0x53, 0x68, 0x65,
	0x6c, 0x66, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x12, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x6f, 0x6e,
	0x53, 0x68, 0x65, 0x6c, 0x66, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x1c, 0x0a, 0x09, 0x61, 0x75, 0x74,
	0x6f, 0x53, 0x68, 0x65, 0x6c, 0x66, 0x18, 0x13, 0x20, 0x01, 0x28, 0x05, 0x52, 0x09, 0x61, 0x75,
	0x74, 0x6f, 0x53, 0x68, 0x65, 0x6c, 0x66, 0x12, 0x3c, 0x0a, 0x08, 0x61, 0x72, 0x74, 0x77, 0x6f,
	0x72, 0x6b, 0x73, 0x18, 0x14, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x20, 0x2e, 0x62, 0x61, 0x63, 0x6b,
	0x65, 0x6e, 0x64, 0x53, 0x65, 0x72, 0x69, 0x65, 0x73, 0x2e, 0x53, 0x65, 0x72, 0x69, 0x65, 0x73,
	0x41, 0x72, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x44, 0x61, 0x74, 0x61, 0x52, 0x08, 0x61, 0x72, 0x74,
	0x77, 0x6f, 0x72, 0x6b, 0x73, 0x12, 0x20, 0x0a, 0x0b, 0x61, 0x75, 0x63, 0x74, 0x69, 0x6f, 0x6e,
	0x55, 0x75, 0x69, 0x64, 0x18, 0x15, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x61, 0x75, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x55, 0x75, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6c, 0x69, 0x6e, 0x6b, 0x18,
	0x16, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6c, 0x69, 0x6e, 0x6b, 0x12, 0x37, 0x0a, 0x07, 0x61,
	0x75, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x17, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x62,
	0x61, 0x63, 0x6b, 0x65, 0x6e, 0x64, 0x53, 0x65, 0x72, 0x69, 0x65, 0x73, 0x2e, 0x41, 0x75, 0x63,
	0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x52, 0x07, 0x61, 0x75, 0x63,
	0x74, 0x69, 0x6f, 0x6e, 0x12, 0x59, 0x0a, 0x15, 0x73, 0x65, 0x72, 0x69, 0x65, 0x73, 0x43, 0x75,
	0x6c, 0x74, 0x75, 0x72, 0x65, 0x41, 0x72, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x18, 0x18, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x23, 0x2e, 0x62, 0x61, 0x63, 0x6b, 0x65, 0x6e, 0x64, 0x53, 0x65, 0x72,
	0x69, 0x65, 0x73, 0x2e, 0x53, 0x65, 0x72, 0x69, 0x65, 0x73, 0x43, 0x75, 0x6c, 0x74, 0x75, 0x72,
	0x65, 0x41, 0x72, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x52, 0x15, 0x73, 0x65, 0x72, 0x69, 0x65, 0x73,
	0x43, 0x75, 0x6c, 0x74, 0x75, 0x72, 0x65, 0x41, 0x72, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x12,
	0x1a, 0x0a, 0x08, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x63, 0x79, 0x18, 0x19, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x08, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x63, 0x79, 0x1a, 0xa4, 0x01, 0x0a, 0x0e,
	0x53, 0x65, 0x72, 0x69, 0x65, 0x73, 0x4c, 0x61, 0x6e, 0x67, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x1e,
	0x0a, 0x0a, 0x73, 0x65, 0x72, 0x69, 0x65, 0x73, 0x55, 0x75, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0a, 0x73, 0x65, 0x72, 0x69, 0x65, 0x73, 0x55, 0x75, 0x69, 0x64, 0x12, 0x1e,
	0x0a, 0x0a, 0x73, 0x65, 0x72, 0x69, 0x65, 0x73, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0a, 0x73, 0x65, 0x72, 0x69, 0x65, 0x73, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x12,
	0x0a, 0x04, 0x64, 0x65, 0x73, 0x63, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x64, 0x65,
	0x73, 0x63, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x05, 0x52, 0x02,
	0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6c, 0x61, 0x6e, 0x67, 0x18, 0x07, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x04, 0x6c, 0x61, 0x6e, 0x67, 0x12, 0x1a, 0x0a, 0x08, 0x63, 0x6f, 0x76, 0x65, 0x72, 0x49,
	0x6d, 0x67, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x63, 0x6f, 0x76, 0x65, 0x72, 0x49,
	0x6d, 0x67, 0x22, 0xb3, 0x01, 0x0a, 0x13, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x53, 0x65, 0x72,
	0x69, 0x65, 0x73, 0x4c, 0x61, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x12, 0x3c, 0x0a, 0x0a, 0x73, 0x65,
	0x72, 0x69, 0x65, 0x73, 0x55, 0x75, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x1c,
	0xe2, 0xdf, 0x1f, 0x18, 0x2a, 0x14, 0xe7, 0xb3, 0xbb, 0xe5, 0x88, 0x97, 0x49, 0x44, 0xe4, 0xb8,
	0x8d, 0xe8, 0x83, 0xbd, 0xe4, 0xb8, 0xba, 0xe7, 0xa9, 0xba, 0x58, 0x01, 0x52, 0x0a, 0x73, 0x65,
	0x72, 0x69, 0x65, 0x73, 0x55, 0x75, 0x69, 0x64, 0x12, 0x1e, 0x0a, 0x0a, 0x73, 0x65, 0x72, 0x69,
	0x65, 0x73, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x73, 0x65,
	0x72, 0x69, 0x65, 0x73, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x64, 0x65, 0x73, 0x63,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x64, 0x65, 0x73, 0x63, 0x12, 0x12, 0x0a, 0x04,
	0x6c, 0x61, 0x6e, 0x67, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6c, 0x61, 0x6e, 0x67,
	0x12, 0x16, 0x0a, 0x06, 0x6c, 0x61, 0x6e, 0x67, 0x49, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x06, 0x6c, 0x61, 0x6e, 0x67, 0x49, 0x64, 0x22, 0x28, 0x0a, 0x14, 0x55, 0x70, 0x64, 0x61,
	0x74, 0x65, 0x53, 0x65, 0x72, 0x69, 0x65, 0x73, 0x4c, 0x61, 0x6e, 0x67, 0x52, 0x65, 0x73, 0x70,
	0x12, 0x10, 0x0a, 0x03, 0x6d, 0x73, 0x67, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6d,
	0x73, 0x67, 0x22, 0x9c, 0x01, 0x0a, 0x0c, 0x41, 0x75, 0x74, 0x6f, 0x53, 0x68, 0x65, 0x6c, 0x66,
	0x52, 0x65, 0x71, 0x12, 0x3c, 0x0a, 0x0a, 0x73, 0x65, 0x72, 0x69, 0x65, 0x73, 0x55, 0x75, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x1c, 0xe2, 0xdf, 0x1f, 0x18, 0x2a, 0x14, 0xe7,
	0xb3, 0xbb, 0xe5, 0x88, 0x97, 0x49, 0x44, 0xe4, 0xb8, 0x8d, 0xe8, 0x83, 0xbd, 0xe4, 0xb8, 0xba,
	0xe7, 0xa9, 0xba, 0x58, 0x01, 0x52, 0x0a, 0x73, 0x65, 0x72, 0x69, 0x65, 0x73, 0x55, 0x75, 0x69,
	0x64, 0x12, 0x28, 0x0a, 0x0f, 0x6c, 0x61, 0x75, 0x6e, 0x63, 0x68, 0x53, 0x74, 0x61, 0x72, 0x74,
	0x54, 0x69, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x6c, 0x61, 0x75, 0x6e,
	0x63, 0x68, 0x53, 0x74, 0x61, 0x72, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x24, 0x0a, 0x0d, 0x6c,
	0x61, 0x75, 0x6e, 0x63, 0x68, 0x45, 0x6e, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0d, 0x6c, 0x61, 0x75, 0x6e, 0x63, 0x68, 0x45, 0x6e, 0x64, 0x54, 0x69, 0x6d,
	0x65, 0x22, 0x21, 0x0a, 0x0d, 0x41, 0x75, 0x74, 0x6f, 0x53, 0x68, 0x65, 0x6c, 0x66, 0x52, 0x65,
	0x73, 0x70, 0x12, 0x10, 0x0a, 0x03, 0x6d, 0x73, 0x67, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x03, 0x6d, 0x73, 0x67, 0x22, 0x6c, 0x0a, 0x0c, 0x48, 0x61, 0x6e, 0x64, 0x53, 0x68, 0x65, 0x6c,
	0x66, 0x52, 0x65, 0x71, 0x12, 0x3c, 0x0a, 0x0a, 0x73, 0x65, 0x72, 0x69, 0x65, 0x73, 0x55, 0x75,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x1c, 0xe2, 0xdf, 0x1f, 0x18, 0x2a, 0x14,
	0xe7, 0xb3, 0xbb, 0xe5, 0x88, 0x97, 0x49, 0x44, 0xe4, 0xb8, 0x8d, 0xe8, 0x83, 0xbd, 0xe4, 0xb8,
	0xba, 0xe7, 0xa9, 0xba, 0x58, 0x01, 0x52, 0x0a, 0x73, 0x65, 0x72, 0x69, 0x65, 0x73, 0x55, 0x75,
	0x69, 0x64, 0x12, 0x1e, 0x0a, 0x0a, 0x73, 0x68, 0x65, 0x6c, 0x66, 0x53, 0x74, 0x61, 0x74, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0a, 0x73, 0x68, 0x65, 0x6c, 0x66, 0x53, 0x74, 0x61,
	0x74, 0x65, 0x22, 0x5f, 0x0a, 0x0d, 0x48, 0x61, 0x6e, 0x64, 0x53, 0x68, 0x65, 0x6c, 0x66, 0x52,
	0x65, 0x73, 0x70, 0x12, 0x1e, 0x0a, 0x0a, 0x73, 0x65, 0x72, 0x69, 0x65, 0x73, 0x55, 0x75, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x73, 0x65, 0x72, 0x69, 0x65, 0x73, 0x55,
	0x75, 0x69, 0x64, 0x12, 0x1c, 0x0a, 0x09, 0x62, 0x72, 0x61, 0x6e, 0x64, 0x55, 0x75, 0x69, 0x64,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x62, 0x72, 0x61, 0x6e, 0x64, 0x55, 0x75, 0x69,
	0x64, 0x12, 0x10, 0x0a, 0x03, 0x6d, 0x73, 0x67, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03,
	0x6d, 0x73, 0x67, 0x22, 0x83, 0x01, 0x0a, 0x0d, 0x53, 0x65, 0x72, 0x69, 0x65, 0x73, 0x4c, 0x69,
	0x73, 0x74, 0x52, 0x65, 0x71, 0x12, 0x18, 0x0a, 0x07, 0x6b, 0x65, 0x79, 0x77, 0x6f, 0x72, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6b, 0x65, 0x79, 0x77, 0x6f, 0x72, 0x64, 0x12,
	0x12, 0x0a, 0x04, 0x70, 0x61, 0x67, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x70,
	0x61, 0x67, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x61, 0x67, 0x65, 0x53, 0x69, 0x7a, 0x65, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x70, 0x61, 0x67, 0x65, 0x53, 0x69, 0x7a, 0x65, 0x12,
	0x12, 0x0a, 0x04, 0x6c, 0x61, 0x6e, 0x67, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6c,
	0x61, 0x6e, 0x67, 0x12, 0x14, 0x0a, 0x05, 0x6c, 0x61, 0x6e, 0x67, 0x73, 0x18, 0x05, 0x20, 0x03,
	0x28, 0x09, 0x52, 0x05, 0x6c, 0x61, 0x6e, 0x67, 0x73, 0x22, 0xbb, 0x04, 0x0a, 0x0e, 0x53, 0x65,
	0x72, 0x69, 0x65, 0x73, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x70, 0x12, 0x36, 0x0a, 0x04,
	0x64, 0x61, 0x74, 0x61, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x62, 0x61, 0x63,
	0x6b, 0x65, 0x6e, 0x64, 0x53, 0x65, 0x72, 0x69, 0x65, 0x73, 0x2e, 0x53, 0x65, 0x72, 0x69, 0x65,
	0x73, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x70, 0x2e, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x04,
	0x64, 0x61, 0x74, 0x61, 0x12, 0x14, 0x0a, 0x05, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x05, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x1e, 0x0a, 0x0a, 0x73, 0x79,
	0x73, 0x74, 0x65, 0x6d, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0a,
	0x73, 0x79, 0x73, 0x74, 0x65, 0x6d, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x6d, 0x73,
	0x67, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6d, 0x73, 0x67, 0x1a, 0xa8, 0x03, 0x0a,
	0x04, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x1e, 0x0a, 0x0a, 0x73, 0x65, 0x72, 0x69, 0x65, 0x73, 0x55,
	0x75, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x73, 0x65, 0x72, 0x69, 0x65,
	0x73, 0x55, 0x75, 0x69, 0x64, 0x12, 0x1e, 0x0a, 0x0a, 0x73, 0x65, 0x72, 0x69, 0x65, 0x73, 0x4e,
	0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x73, 0x65, 0x72, 0x69, 0x65,
	0x73, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x63, 0x6f, 0x76, 0x65, 0x72, 0x49, 0x6d,
	0x67, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x63, 0x6f, 0x76, 0x65, 0x72, 0x49, 0x6d,
	0x67, 0x12, 0x1c, 0x0a, 0x09, 0x62, 0x72, 0x61, 0x6e, 0x64, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x62, 0x72, 0x61, 0x6e, 0x64, 0x4e, 0x61, 0x6d, 0x65, 0x12,
	0x1e, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x12,
	0x1e, 0x0a, 0x0a, 0x73, 0x68, 0x65, 0x6c, 0x66, 0x53, 0x74, 0x61, 0x74, 0x65, 0x18, 0x06, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x0a, 0x73, 0x68, 0x65, 0x6c, 0x66, 0x53, 0x74, 0x61, 0x74, 0x65, 0x12,
	0x28, 0x0a, 0x0f, 0x6c, 0x61, 0x75, 0x6e, 0x63, 0x68, 0x53, 0x74, 0x61, 0x72, 0x74, 0x54, 0x69,
	0x6d, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x6c, 0x61, 0x75, 0x6e, 0x63, 0x68,
	0x53, 0x74, 0x61, 0x72, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x24, 0x0a, 0x0d, 0x6c, 0x61, 0x75,
	0x6e, 0x63, 0x68, 0x45, 0x6e, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0d, 0x6c, 0x61, 0x75, 0x6e, 0x63, 0x68, 0x45, 0x6e, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x12,
	0x1e, 0x0a, 0x0a, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x09, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0a, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x69, 0x6d, 0x65, 0x12,
	0x20, 0x0a, 0x0b, 0x6f, 0x6e, 0x53, 0x68, 0x65, 0x6c, 0x66, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x0a,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x6f, 0x6e, 0x53, 0x68, 0x65, 0x6c, 0x66, 0x54, 0x69, 0x6d,
	0x65, 0x12, 0x1e, 0x0a, 0x0a, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x6f, 0x64, 0x65, 0x18,
	0x0c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x6f, 0x64,
	0x65, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x0d, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x1c, 0x0a, 0x09, 0x61, 0x75, 0x74,
	0x6f, 0x53, 0x68, 0x65, 0x6c, 0x66, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x05, 0x52, 0x09, 0x61, 0x75,
	0x74, 0x6f, 0x53, 0x68, 0x65, 0x6c, 0x66, 0x22, 0x4c, 0x0a, 0x0c, 0x53, 0x65, 0x72, 0x69, 0x65,
	0x73, 0x44, 0x65, 0x6c, 0x52, 0x65, 0x71, 0x12, 0x3c, 0x0a, 0x0a, 0x73, 0x65, 0x72, 0x69, 0x65,
	0x73, 0x55, 0x75, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x1c, 0xe2, 0xdf, 0x1f,
	0x18, 0x2a, 0x14, 0xe7, 0xb3, 0xbb, 0xe5, 0x88, 0x97, 0x49, 0x44, 0xe4, 0xb8, 0x8d, 0xe8, 0x83,
	0xbd, 0xe4, 0xb8, 0xba, 0xe7, 0xa9, 0xba, 0x58, 0x01, 0x52, 0x0a, 0x73, 0x65, 0x72, 0x69, 0x65,
	0x73, 0x55, 0x75, 0x69, 0x64, 0x22, 0x21, 0x0a, 0x0d, 0x53, 0x65, 0x72, 0x69, 0x65, 0x73, 0x44,
	0x65, 0x6c, 0x52, 0x65, 0x73, 0x70, 0x12, 0x10, 0x0a, 0x03, 0x6d, 0x73, 0x67, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x03, 0x6d, 0x73, 0x67, 0x22, 0x10, 0x0a, 0x0e, 0x43, 0x68, 0x65, 0x63,
	0x6b, 0x53, 0x65, 0x72, 0x69, 0x65, 0x73, 0x52, 0x65, 0x71, 0x22, 0x8c, 0x01, 0x0a, 0x0f, 0x43,
	0x68, 0x65, 0x63, 0x6b, 0x53, 0x65, 0x72, 0x69, 0x65, 0x73, 0x52, 0x65, 0x73, 0x70, 0x12, 0x37,
	0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x23, 0x2e, 0x62,
	0x61, 0x63, 0x6b, 0x65, 0x6e, 0x64, 0x53, 0x65, 0x72, 0x69, 0x65, 0x73, 0x2e, 0x43, 0x68, 0x65,
	0x63, 0x6b, 0x53, 0x65, 0x72, 0x69, 0x65, 0x73, 0x52, 0x65, 0x73, 0x70, 0x2e, 0x49, 0x6e, 0x66,
	0x6f, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x1a, 0x40, 0x0a, 0x04, 0x49, 0x6e, 0x66, 0x6f, 0x12,
	0x1e, 0x0a, 0x0a, 0x73, 0x65, 0x72, 0x69, 0x65, 0x73, 0x55, 0x75, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0a, 0x73, 0x65, 0x72, 0x69, 0x65, 0x73, 0x55, 0x75, 0x69, 0x64, 0x12,
	0x18, 0x0a, 0x07, 0x62, 0x72, 0x61, 0x6e, 0x64, 0x49, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x07, 0x62, 0x72, 0x61, 0x6e, 0x64, 0x49, 0x64, 0x22, 0x76, 0x0a, 0x14, 0x55, 0x70, 0x64,
	0x61, 0x74, 0x65, 0x53, 0x65, 0x72, 0x69, 0x65, 0x73, 0x43, 0x68, 0x61, 0x69, 0x6e, 0x52, 0x65,
	0x71, 0x12, 0x3c, 0x0a, 0x0a, 0x73, 0x65, 0x72, 0x69, 0x65, 0x73, 0x55, 0x75, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x1c, 0xe2, 0xdf, 0x1f, 0x18, 0x2a, 0x14, 0xe7, 0xb3, 0xbb,
	0xe5, 0x88, 0x97, 0x49, 0x44, 0xe4, 0xb8, 0x8d, 0xe8, 0x83, 0xbd, 0xe4, 0xb8, 0xba, 0xe7, 0xa9,
	0xba, 0x58, 0x01, 0x52, 0x0a, 0x73, 0x65, 0x72, 0x69, 0x65, 0x73, 0x55, 0x75, 0x69, 0x64, 0x12,
	0x20, 0x0a, 0x0b, 0x63, 0x68, 0x61, 0x69, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x0d, 0x52, 0x0b, 0x63, 0x68, 0x61, 0x69, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x22, 0x29, 0x0a, 0x15, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x53, 0x65, 0x72, 0x69, 0x65,
	0x73, 0x43, 0x68, 0x61, 0x69, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x12, 0x10, 0x0a, 0x03, 0x6d, 0x73,
	0x67, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6d, 0x73, 0x67, 0x22, 0x7b, 0x0a, 0x18,
	0x47, 0x65, 0x74, 0x53, 0x65, 0x72, 0x69, 0x65, 0x73, 0x4c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67,
	0x65, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x71, 0x12, 0x1e, 0x0a, 0x0a, 0x53, 0x65, 0x72, 0x69,
	0x65, 0x73, 0x55, 0x75, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x53, 0x65,
	0x72, 0x69, 0x65, 0x73, 0x55, 0x75, 0x69, 0x64, 0x12, 0x3f, 0x0a, 0x0c, 0x6c, 0x61, 0x6e, 0x67,
	0x75, 0x61, 0x67, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1b,
	0x2e, 0x62, 0x61, 0x63, 0x6b, 0x65, 0x6e, 0x64, 0x53, 0x65, 0x72, 0x69, 0x65, 0x73, 0x2e, 0x4c,
	0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x0c, 0x6c, 0x61, 0x6e,
	0x67, 0x75, 0x61, 0x67, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x22, 0x6d, 0x0a, 0x18, 0x47, 0x65, 0x74,
	0x53, 0x65, 0x72, 0x69, 0x65, 0x73, 0x4c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65, 0x49, 0x6e,
	0x66, 0x6f, 0x52, 0x65, 0x70, 0x12, 0x3f, 0x0a, 0x0c, 0x6c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67,
	0x65, 0x49, 0x6e, 0x66, 0x6f, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x62, 0x61,
	0x63, 0x6b, 0x65, 0x6e, 0x64, 0x53, 0x65, 0x72, 0x69, 0x65, 0x73, 0x2e, 0x4c, 0x61, 0x6e, 0x67,
	0x75, 0x61, 0x67, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x0c, 0x6c, 0x61, 0x6e, 0x67, 0x75, 0x61,
	0x67, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x10, 0x0a, 0x03, 0x6d, 0x73, 0x67, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x03, 0x6d, 0x73, 0x67, 0x22, 0x2d, 0x0a, 0x0b, 0x44, 0x72, 0x61, 0x77,
	0x47, 0x69, 0x66, 0x74, 0x52, 0x65, 0x71, 0x12, 0x1e, 0x0a, 0x0a, 0x73, 0x65, 0x72, 0x69, 0x65,
	0x73, 0x55, 0x75, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x73, 0x65, 0x72,
	0x69, 0x65, 0x73, 0x55, 0x75, 0x69, 0x64, 0x22, 0xfe, 0x02, 0x0a, 0x0c, 0x55, 0x70, 0x64, 0x61,
	0x74, 0x65, 0x4d, 0x65, 0x6d, 0x52, 0x65, 0x71, 0x12, 0x1e, 0x0a, 0x0a, 0x73, 0x65, 0x72, 0x69,
	0x65, 0x73, 0x55, 0x75, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x73, 0x65,
	0x72, 0x69, 0x65, 0x73, 0x55, 0x75, 0x69, 0x64, 0x12, 0x24, 0x0a, 0x0d, 0x73, 0x65, 0x72, 0x69,
	0x65, 0x73, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0d, 0x73, 0x65, 0x72, 0x69, 0x65, 0x73, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x12, 0x1c,
	0x0a, 0x09, 0x73, 0x65, 0x72, 0x69, 0x65, 0x73, 0x4d, 0x65, 0x6d, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x09, 0x73, 0x65, 0x72, 0x69, 0x65, 0x73, 0x4d, 0x65, 0x6d, 0x12, 0x1c, 0x0a, 0x09,
	0x73, 0x65, 0x72, 0x69, 0x65, 0x73, 0x50, 0x77, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x09, 0x73, 0x65, 0x72, 0x69, 0x65, 0x73, 0x50, 0x77, 0x64, 0x12, 0x2a, 0x0a, 0x10, 0x73, 0x65,
	0x72, 0x69, 0x65, 0x73, 0x49, 0x73, 0x47, 0x69, 0x66, 0x74, 0x44, 0x72, 0x61, 0x77, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x0d, 0x52, 0x10, 0x73, 0x65, 0x72, 0x69, 0x65, 0x73, 0x49, 0x73, 0x47, 0x69,
	0x66, 0x74, 0x44, 0x72, 0x61, 0x77, 0x12, 0x28, 0x0a, 0x0f, 0x73, 0x65, 0x72, 0x69, 0x65, 0x73,
	0x50, 0x75, 0x62, 0x6c, 0x69, 0x63, 0x4b, 0x65, 0x79, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0f, 0x73, 0x65, 0x72, 0x69, 0x65, 0x73, 0x50, 0x75, 0x62, 0x6c, 0x69, 0x63, 0x4b, 0x65, 0x79,
	0x12, 0x54, 0x0a, 0x0d, 0x63, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x4d, 0x61,
	0x70, 0x18, 0x07, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2e, 0x2e, 0x62, 0x61, 0x63, 0x6b, 0x65, 0x6e,
	0x64, 0x53, 0x65, 0x72, 0x69, 0x65, 0x73, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x4d, 0x65,
	0x6d, 0x52, 0x65, 0x71, 0x2e, 0x43, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x4d,
	0x61, 0x70, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x0d, 0x63, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x4d, 0x61, 0x70, 0x1a, 0x40, 0x0a, 0x12, 0x43, 0x6f, 0x6c, 0x6c, 0x65, 0x63,
	0x74, 0x69, 0x6f, 0x6e, 0x4d, 0x61, 0x70, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03,
	0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14,
	0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76,
	0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0xe5, 0x01, 0x0a, 0x0b, 0x43, 0x6f, 0x6c,
	0x6c, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x4d, 0x12, 0x1e, 0x0a, 0x0a, 0x73, 0x65, 0x72, 0x69,
	0x65, 0x73, 0x55, 0x75, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x73, 0x65,
	0x72, 0x69, 0x65, 0x73, 0x55, 0x75, 0x69, 0x64, 0x12, 0x24, 0x0a, 0x0d, 0x73, 0x65, 0x72, 0x69,
	0x65, 0x73, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0d, 0x73, 0x65, 0x72, 0x69, 0x65, 0x73, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x12, 0x1c,
	0x0a, 0x09, 0x73, 0x65, 0x72, 0x69, 0x65, 0x73, 0x4d, 0x65, 0x6d, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x09, 0x73, 0x65, 0x72, 0x69, 0x65, 0x73, 0x4d, 0x65, 0x6d, 0x12, 0x1c, 0x0a, 0x09,
	0x73, 0x65, 0x72, 0x69, 0x65, 0x73, 0x50, 0x77, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x09, 0x73, 0x65, 0x72, 0x69, 0x65, 0x73, 0x50, 0x77, 0x64, 0x12, 0x2a, 0x0a, 0x10, 0x73, 0x65,
	0x72, 0x69, 0x65, 0x73, 0x49, 0x73, 0x47, 0x69, 0x66, 0x74, 0x44, 0x72, 0x61, 0x77, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x0d, 0x52, 0x10, 0x73, 0x65, 0x72, 0x69, 0x65, 0x73, 0x49, 0x73, 0x47, 0x69,
	0x66, 0x74, 0x44, 0x72, 0x61, 0x77, 0x12, 0x28, 0x0a, 0x0f, 0x73, 0x65, 0x72, 0x69, 0x65, 0x73,
	0x50, 0x75, 0x62, 0x6c, 0x69, 0x63, 0x4b, 0x65, 0x79, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0f, 0x73, 0x65, 0x72, 0x69, 0x65, 0x73, 0x50, 0x75, 0x62, 0x6c, 0x69, 0x63, 0x4b, 0x65, 0x79,
	0x22, 0x1f, 0x0a, 0x09, 0x43, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x12, 0x12, 0x0a,
	0x04, 0x75, 0x75, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x75, 0x75, 0x69,
	0x64, 0x22, 0x6b, 0x0a, 0x0e, 0x46, 0x53, 0x65, 0x72, 0x69, 0x65, 0x73, 0x4c, 0x69, 0x73, 0x74,
	0x52, 0x65, 0x71, 0x12, 0x12, 0x0a, 0x04, 0x50, 0x61, 0x67, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x04, 0x70, 0x61, 0x67, 0x65, 0x12, 0x1b, 0x0a, 0x08, 0x50, 0x61, 0x67, 0x65, 0x53,
	0x69, 0x7a, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x09, 0x70, 0x61, 0x67, 0x65, 0x5f,
	0x73, 0x69, 0x7a, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x6c, 0x61, 0x6e, 0x67, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x04, 0x6c, 0x61, 0x6e, 0x67, 0x12, 0x14, 0x0a, 0x05, 0x6c, 0x61, 0x6e, 0x67,
	0x73, 0x18, 0x06, 0x20, 0x03, 0x28, 0x09, 0x52, 0x05, 0x6c, 0x61, 0x6e, 0x67, 0x73, 0x22, 0x97,
	0x03, 0x0a, 0x0f, 0x46, 0x53, 0x65, 0x72, 0x69, 0x65, 0x73, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65,
	0x73, 0x70, 0x12, 0x37, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x23, 0x2e, 0x62, 0x61, 0x63, 0x6b, 0x65, 0x6e, 0x64, 0x53, 0x65, 0x72, 0x69, 0x65, 0x73,
	0x2e, 0x46, 0x53, 0x65, 0x72, 0x69, 0x65, 0x73, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x70,
	0x2e, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x12, 0x14, 0x0a, 0x05, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x12, 0x10, 0x0a, 0x03, 0x6d, 0x73, 0x67, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03,
	0x6d, 0x73, 0x67, 0x1a, 0xa2, 0x02, 0x0a, 0x04, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x1e, 0x0a, 0x0a,
	0x73, 0x65, 0x72, 0x69, 0x65, 0x73, 0x55, 0x75, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0a, 0x73, 0x65, 0x72, 0x69, 0x65, 0x73, 0x55, 0x75, 0x69, 0x64, 0x12, 0x1e, 0x0a, 0x0a,
	0x73, 0x65, 0x72, 0x69, 0x65, 0x73, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0a, 0x73, 0x65, 0x72, 0x69, 0x65, 0x73, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1a, 0x0a, 0x08,
	0x63, 0x6f, 0x76, 0x65, 0x72, 0x49, 0x6d, 0x67, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08,
	0x63, 0x6f, 0x76, 0x65, 0x72, 0x49, 0x6d, 0x67, 0x12, 0x12, 0x0a, 0x04, 0x64, 0x65, 0x73, 0x63,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x64, 0x65, 0x73, 0x63, 0x12, 0x1c, 0x0a, 0x09,
	0x73, 0x74, 0x61, 0x72, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x09, 0x73, 0x74, 0x61, 0x72, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x65, 0x6e,
	0x64, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x65, 0x6e, 0x64,
	0x54, 0x69, 0x6d, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x62, 0x72, 0x61, 0x6e, 0x64, 0x49, 0x64, 0x18,
	0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x62, 0x72, 0x61, 0x6e, 0x64, 0x49, 0x64, 0x12, 0x1c,
	0x0a, 0x09, 0x62, 0x72, 0x61, 0x6e, 0x64, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x09, 0x62, 0x72, 0x61, 0x6e, 0x64, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1a, 0x0a, 0x08,
	0x62, 0x72, 0x61, 0x6e, 0x64, 0x49, 0x6d, 0x67, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08,
	0x62, 0x72, 0x61, 0x6e, 0x64, 0x49, 0x6d, 0x67, 0x12, 0x1e, 0x0a, 0x0a, 0x61, 0x63, 0x74, 0x69,
	0x6f, 0x6e, 0x43, 0x6f, 0x64, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x61, 0x63,
	0x74, 0x69, 0x6f, 0x6e, 0x43, 0x6f, 0x64, 0x65, 0x22, 0x3c, 0x0a, 0x14, 0x53, 0x65, 0x72, 0x69,
	0x65, 0x73, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x42, 0x79, 0x4d, 0x65, 0x6d, 0x52, 0x65, 0x71,
	0x12, 0x10, 0x0a, 0x03, 0x6d, 0x65, 0x6d, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6d,
	0x65, 0x6d, 0x12, 0x12, 0x0a, 0x04, 0x6c, 0x61, 0x6e, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x04, 0x6c, 0x61, 0x6e, 0x67, 0x22, 0xe7, 0x04, 0x0a, 0x11, 0x53, 0x65, 0x72, 0x69, 0x65,
	0x73, 0x41, 0x72, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x44, 0x61, 0x74, 0x61, 0x12, 0x0e, 0x0a, 0x02,
	0x49, 0x44, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x02, 0x49, 0x44, 0x12, 0x1c, 0x0a, 0x09,
	0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x1c, 0x0a, 0x09, 0x75, 0x70,
	0x64, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x75,
	0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x1c, 0x0a, 0x09, 0x64, 0x65, 0x6c, 0x65,
	0x74, 0x65, 0x64, 0x41, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x64, 0x65, 0x6c,
	0x65, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x1c, 0x0a, 0x09, 0x61, 0x72, 0x74, 0x77, 0x6f, 0x72,
	0x6b, 0x49, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x61, 0x72, 0x74, 0x77, 0x6f,
	0x72, 0x6b, 0x49, 0x64, 0x12, 0x1e, 0x0a, 0x0a, 0x61, 0x72, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x55,
	0x69, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x61, 0x72, 0x74, 0x77, 0x6f, 0x72,
	0x6b, 0x55, 0x69, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x66, 0x6e, 0x75, 0x6d, 0x18, 0x07, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x05, 0x74, 0x66, 0x6e, 0x75, 0x6d, 0x12, 0x20, 0x0a, 0x0b, 0x61, 0x72,
	0x74, 0x77, 0x6f, 0x72, 0x6b, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0b, 0x61, 0x72, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x14, 0x0a, 0x05,
	0x68, 0x64, 0x50, 0x69, 0x63, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x68, 0x64, 0x50,
	0x69, 0x63, 0x12, 0x14, 0x0a, 0x05, 0x72, 0x75, 0x6c, 0x65, 0x72, 0x18, 0x0a, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x05, 0x72, 0x75, 0x6c, 0x65, 0x72, 0x12, 0x16, 0x0a, 0x06, 0x6c, 0x65, 0x6e, 0x67,
	0x74, 0x68, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x6c, 0x65, 0x6e, 0x67, 0x74, 0x68,
	0x12, 0x14, 0x0a, 0x05, 0x77, 0x69, 0x64, 0x74, 0x68, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x05, 0x77, 0x69, 0x64, 0x74, 0x68, 0x12, 0x1e, 0x0a, 0x0a, 0x61, 0x72, 0x74, 0x69, 0x73, 0x74,
	0x4e, 0x61, 0x6d, 0x65, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x61, 0x72, 0x74, 0x69,
	0x73, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1c, 0x0a, 0x09, 0x61, 0x72, 0x74, 0x69, 0x73, 0x74,
	0x55, 0x69, 0x64, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x61, 0x72, 0x74, 0x69, 0x73,
	0x74, 0x55, 0x69, 0x64, 0x12, 0x1a, 0x0a, 0x08, 0x61, 0x62, 0x73, 0x74, 0x72, 0x61, 0x63, 0x74,
	0x18, 0x0f, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x61, 0x62, 0x73, 0x74, 0x72, 0x61, 0x63, 0x74,
	0x12, 0x12, 0x0a, 0x04, 0x74, 0x6e, 0x75, 0x6d, 0x18, 0x10, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04,
	0x74, 0x6e, 0x75, 0x6d, 0x12, 0x38, 0x0a, 0x04, 0x6c, 0x61, 0x6e, 0x67, 0x18, 0x11, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x24, 0x2e, 0x62, 0x61, 0x63, 0x6b, 0x65, 0x6e, 0x64, 0x53, 0x65, 0x72, 0x69,
	0x65, 0x73, 0x2e, 0x53, 0x65, 0x72, 0x69, 0x65, 0x73, 0x41, 0x72, 0x74, 0x77, 0x6f, 0x72, 0x6b,
	0x4c, 0x61, 0x6e, 0x67, 0x44, 0x61, 0x74, 0x61, 0x52, 0x04, 0x6c, 0x61, 0x6e, 0x67, 0x12, 0x14,
	0x0a, 0x05, 0x70, 0x72, 0x69, 0x63, 0x65, 0x18, 0x12, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x70,
	0x72, 0x69, 0x63, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x63, 0x79,
	0x18, 0x13, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x63, 0x79,
	0x12, 0x1c, 0x0a, 0x09, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x49, 0x64, 0x18, 0x14, 0x20,
	0x01, 0x28, 0x0d, 0x52, 0x09, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x49, 0x64, 0x12, 0x20,
	0x0a, 0x0b, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x15, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0b, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x4e, 0x61, 0x6d, 0x65,
	0x22, 0x97, 0x03, 0x0a, 0x15, 0x53, 0x65, 0x72, 0x69, 0x65, 0x73, 0x41, 0x72, 0x74, 0x77, 0x6f,
	0x72, 0x6b, 0x4c, 0x61, 0x6e, 0x67, 0x44, 0x61, 0x74, 0x61, 0x12, 0x0e, 0x0a, 0x02, 0x49, 0x44,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x02, 0x49, 0x44, 0x12, 0x1c, 0x0a, 0x09, 0x63, 0x72,
	0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x63,
	0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x1c, 0x0a, 0x09, 0x75, 0x70, 0x64, 0x61,
	0x74, 0x65, 0x64, 0x41, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x75, 0x70, 0x64,
	0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x1c, 0x0a, 0x09, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x65,
	0x64, 0x41, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x64, 0x65, 0x6c, 0x65, 0x74,
	0x65, 0x64, 0x41, 0x74, 0x12, 0x12, 0x0a, 0x04, 0x6c, 0x61, 0x6e, 0x67, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x04, 0x6c, 0x61, 0x6e, 0x67, 0x12, 0x28, 0x0a, 0x0f, 0x73, 0x65, 0x72, 0x69,
	0x65, 0x73, 0x41, 0x72, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x49, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x0f, 0x73, 0x65, 0x72, 0x69, 0x65, 0x73, 0x41, 0x72, 0x74, 0x77, 0x6f, 0x72, 0x6b,
	0x49, 0x64, 0x12, 0x1e, 0x0a, 0x0a, 0x61, 0x72, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x55, 0x69, 0x64,
	0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x61, 0x72, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x55,
	0x69, 0x64, 0x12, 0x20, 0x0a, 0x0b, 0x61, 0x72, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x4e, 0x61, 0x6d,
	0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x61, 0x72, 0x74, 0x77, 0x6f, 0x72, 0x6b,
	0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1e, 0x0a, 0x0a, 0x61, 0x72, 0x74, 0x69, 0x73, 0x74, 0x4e, 0x61,
	0x6d, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x61, 0x72, 0x74, 0x69, 0x73, 0x74,
	0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x61, 0x62, 0x73, 0x74, 0x72, 0x61, 0x63, 0x74,
	0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x61, 0x62, 0x73, 0x74, 0x72, 0x61, 0x63, 0x74,
	0x12, 0x14, 0x0a, 0x05, 0x68, 0x64, 0x50, 0x69, 0x63, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x05, 0x68, 0x64, 0x50, 0x69, 0x63, 0x12, 0x14, 0x0a, 0x05, 0x72, 0x75, 0x6c, 0x65, 0x72, 0x18,
	0x0c, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x72, 0x75, 0x6c, 0x65, 0x72, 0x12, 0x16, 0x0a, 0x06,
	0x6c, 0x65, 0x6e, 0x67, 0x74, 0x68, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x6c, 0x65,
	0x6e, 0x67, 0x74, 0x68, 0x12, 0x14, 0x0a, 0x05, 0x77, 0x69, 0x64, 0x74, 0x68, 0x18, 0x0e, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x05, 0x77, 0x69, 0x64, 0x74, 0x68, 0x22, 0x63, 0x0a, 0x13, 0x4c, 0x69,
	0x73, 0x74, 0x53, 0x61, 0x6c, 0x65, 0x73, 0x41, 0x72, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x52, 0x65,
	0x71, 0x12, 0x1c, 0x0a, 0x09, 0x73, 0x65, 0x72, 0x69, 0x65, 0x73, 0x55, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x73, 0x65, 0x72, 0x69, 0x65, 0x73, 0x55, 0x69, 0x64, 0x12,
	0x12, 0x0a, 0x04, 0x70, 0x61, 0x67, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x04, 0x70,
	0x61, 0x67, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x61, 0x67, 0x65, 0x53, 0x69, 0x7a, 0x65, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x70, 0x61, 0x67, 0x65, 0x53, 0x69, 0x7a, 0x65, 0x22,
	0xa2, 0x03, 0x0a, 0x0c, 0x53, 0x61, 0x6c, 0x65, 0x73, 0x41, 0x72, 0x74, 0x77, 0x6f, 0x72, 0x6b,
	0x12, 0x14, 0x0a, 0x05, 0x6c, 0x6f, 0x74, 0x4e, 0x6f, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x05, 0x6c, 0x6f, 0x74, 0x4e, 0x6f, 0x12, 0x1e, 0x0a, 0x0a, 0x61, 0x72, 0x74, 0x77, 0x6f, 0x72,
	0x6b, 0x55, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x61, 0x72, 0x74, 0x77,
	0x6f, 0x72, 0x6b, 0x55, 0x69, 0x64, 0x12, 0x20, 0x0a, 0x0b, 0x61, 0x72, 0x74, 0x77, 0x6f, 0x72,
	0x6b, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x61, 0x72, 0x74,
	0x77, 0x6f, 0x72, 0x6b, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x68, 0x64, 0x50, 0x69,
	0x63, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x68, 0x64, 0x50, 0x69, 0x63, 0x12, 0x14,
	0x0a, 0x05, 0x70, 0x72, 0x69, 0x63, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x70,
	0x72, 0x69, 0x63, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x63, 0x79,
	0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x63, 0x79,
	0x12, 0x1c, 0x0a, 0x09, 0x70, 0x61, 0x79, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x07, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x09, 0x70, 0x61, 0x79, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x18,
	0x0a, 0x07, 0x70, 0x61, 0x79, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x07, 0x70, 0x61, 0x79, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x75, 0x73, 0x65, 0x72,
	0x49, 0x64, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64,
	0x12, 0x1a, 0x0a, 0x08, 0x75, 0x73, 0x65, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x0a, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x08, 0x75, 0x73, 0x65, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x16, 0x0a, 0x06,
	0x74, 0x65, 0x6c, 0x4e, 0x75, 0x6d, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x74, 0x65,
	0x6c, 0x4e, 0x75, 0x6d, 0x12, 0x26, 0x0a, 0x0e, 0x41, 0x75, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x42,
	0x75, 0x79, 0x55, 0x75, 0x69, 0x64, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x41, 0x75,
	0x63, 0x74, 0x69, 0x6f, 0x6e, 0x42, 0x75, 0x79, 0x55, 0x75, 0x69, 0x64, 0x12, 0x2e, 0x0a, 0x12,
	0x41, 0x75, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x41, 0x72, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x55, 0x75,
	0x69, 0x64, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x09, 0x52, 0x12, 0x41, 0x75, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x41, 0x72, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x55, 0x75, 0x69, 0x64, 0x12, 0x16, 0x0a, 0x06,
	0x70, 0x61, 0x79, 0x55, 0x69, 0x64, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x70, 0x61,
	0x79, 0x55, 0x69, 0x64, 0x22, 0x8d, 0x01, 0x0a, 0x14, 0x4c, 0x69, 0x73, 0x74, 0x53, 0x61, 0x6c,
	0x65, 0x73, 0x41, 0x72, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x52, 0x65, 0x73, 0x70, 0x12, 0x2f, 0x0a,
	0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x62, 0x61,
	0x63, 0x6b, 0x65, 0x6e, 0x64, 0x53, 0x65, 0x72, 0x69, 0x65, 0x73, 0x2e, 0x53, 0x61, 0x6c, 0x65,
	0x73, 0x41, 0x72, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x12, 0x12,
	0x0a, 0x04, 0x70, 0x61, 0x67, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x04, 0x70, 0x61,
	0x67, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x61, 0x67, 0x65, 0x53, 0x69, 0x7a, 0x65, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x70, 0x61, 0x67, 0x65, 0x53, 0x69, 0x7a, 0x65, 0x12, 0x14,
	0x0a, 0x05, 0x54, 0x6f, 0x74, 0x61, 0x6c, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x54,
	0x6f, 0x74, 0x61, 0x6c, 0x22, 0xa8, 0x02, 0x0a, 0x14, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x4f,
	0x72, 0x64, 0x65, 0x72, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x71, 0x12, 0x1c, 0x0a,
	0x09, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x55, 0x75, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x09, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x55, 0x75, 0x69, 0x64, 0x12, 0x1c, 0x0a, 0x09, 0x70,
	0x61, 0x79, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x09,
	0x70, 0x61, 0x79, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x16, 0x0a, 0x06, 0x72, 0x65, 0x6d,
	0x61, 0x72, 0x6b, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x72, 0x65, 0x6d, 0x61, 0x72,
	0x6b, 0x12, 0x22, 0x0a, 0x0c, 0x70, 0x61, 0x79, 0x50, 0x70, 0x72, 0x6f, 0x66, 0x4c, 0x69, 0x73,
	0x74, 0x18, 0x04, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0c, 0x70, 0x61, 0x79, 0x50, 0x70, 0x72, 0x6f,
	0x66, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x18, 0x0a, 0x07, 0x70, 0x61, 0x79, 0x54, 0x69, 0x6d, 0x65,
	0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x70, 0x61, 0x79, 0x54, 0x69, 0x6d, 0x65, 0x12,
	0x1e, 0x0a, 0x0a, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x18, 0x06, 0x20,
	0x01, 0x28, 0x0d, 0x52, 0x0a, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x12,
	0x26, 0x0a, 0x0e, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x54, 0x65, 0x6c, 0x4e, 0x75,
	0x6d, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f,
	0x72, 0x54, 0x65, 0x6c, 0x4e, 0x75, 0x6d, 0x12, 0x1c, 0x0a, 0x09, 0x73, 0x65, 0x73, 0x73, 0x69,
	0x6f, 0x6e, 0x49, 0x64, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x73, 0x65, 0x73, 0x73,
	0x69, 0x6f, 0x6e, 0x49, 0x64, 0x12, 0x18, 0x0a, 0x07, 0x70, 0x61, 0x79, 0x54, 0x79, 0x70, 0x65,
	0x18, 0x09, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x07, 0x70, 0x61, 0x79, 0x54, 0x79, 0x70, 0x65, 0x32,
	0xe3, 0x16, 0x0a, 0x06, 0x53, 0x65, 0x72, 0x69, 0x65, 0x73, 0x12, 0x4b, 0x0a, 0x0a, 0x53, 0x61,
	0x76, 0x65, 0x53, 0x65, 0x72, 0x69, 0x65, 0x73, 0x12, 0x1c, 0x2e, 0x62, 0x61, 0x63, 0x6b, 0x65,
	0x6e, 0x64, 0x53, 0x65, 0x72, 0x69, 0x65, 0x73, 0x2e, 0x53, 0x61, 0x76, 0x65, 0x53, 0x65, 0x72,
	0x69, 0x65, 0x73, 0x52, 0x65, 0x71, 0x1a, 0x1d, 0x2e, 0x62, 0x61, 0x63, 0x6b, 0x65, 0x6e, 0x64,
	0x53, 0x65, 0x72, 0x69, 0x65, 0x73, 0x2e, 0x53, 0x61, 0x76, 0x65, 0x53, 0x65, 0x72, 0x69, 0x65,
	0x73, 0x52, 0x65, 0x73, 0x70, 0x22, 0x00, 0x12, 0x61, 0x0a, 0x13, 0x53, 0x61, 0x76, 0x65, 0x43,
	0x75, 0x6c, 0x74, 0x75, 0x72, 0x61, 0x6c, 0x41, 0x72, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x12, 0x23,
	0x2e, 0x62, 0x61, 0x63, 0x6b, 0x65, 0x6e, 0x64, 0x53, 0x65, 0x72, 0x69, 0x65, 0x73, 0x2e, 0x53,
	0x65, 0x72, 0x69, 0x65, 0x73, 0x43, 0x75, 0x6c, 0x74, 0x75, 0x72, 0x65, 0x41, 0x72, 0x74, 0x77,
	0x6f, 0x72, 0x6b, 0x1a, 0x23, 0x2e, 0x62, 0x61, 0x63, 0x6b, 0x65, 0x6e, 0x64, 0x53, 0x65, 0x72,
	0x69, 0x65, 0x73, 0x2e, 0x53, 0x65, 0x72, 0x69, 0x65, 0x73, 0x43, 0x75, 0x6c, 0x74, 0x75, 0x72,
	0x65, 0x41, 0x72, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x22, 0x00, 0x12, 0x51, 0x0a, 0x0c, 0x53, 0x65,
	0x72, 0x69, 0x65, 0x73, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x12, 0x1e, 0x2e, 0x62, 0x61, 0x63,
	0x6b, 0x65, 0x6e, 0x64, 0x53, 0x65, 0x72, 0x69, 0x65, 0x73, 0x2e, 0x53, 0x65, 0x72, 0x69, 0x65,
	0x73, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x65, 0x71, 0x1a, 0x1f, 0x2e, 0x62, 0x61, 0x63,
	0x6b, 0x65, 0x6e, 0x64, 0x53, 0x65, 0x72, 0x69, 0x65, 0x73, 0x2e, 0x53, 0x65, 0x72, 0x69, 0x65,
	0x73, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x65, 0x73, 0x70, 0x22, 0x00, 0x12, 0x5d, 0x0a,
	0x10, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x53, 0x65, 0x72, 0x69, 0x65, 0x73, 0x4c, 0x61, 0x6e,
	0x67, 0x12, 0x22, 0x2e, 0x62, 0x61, 0x63, 0x6b, 0x65, 0x6e, 0x64, 0x53, 0x65, 0x72, 0x69, 0x65,
	0x73, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x53, 0x65, 0x72, 0x69, 0x65, 0x73, 0x4c, 0x61,
	0x6e, 0x67, 0x52, 0x65, 0x71, 0x1a, 0x23, 0x2e, 0x62, 0x61, 0x63, 0x6b, 0x65, 0x6e, 0x64, 0x53,
	0x65, 0x72, 0x69, 0x65, 0x73, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x53, 0x65, 0x72, 0x69,
	0x65, 0x73, 0x4c, 0x61, 0x6e, 0x67, 0x52, 0x65, 0x73, 0x70, 0x22, 0x00, 0x12, 0x48, 0x0a, 0x09,
	0x41, 0x75, 0x74, 0x6f, 0x53, 0x68, 0x65, 0x6c, 0x66, 0x12, 0x1b, 0x2e, 0x62, 0x61, 0x63, 0x6b,
	0x65, 0x6e, 0x64, 0x53, 0x65, 0x72, 0x69, 0x65, 0x73, 0x2e, 0x41, 0x75, 0x74, 0x6f, 0x53, 0x68,
	0x65, 0x6c, 0x66, 0x52, 0x65, 0x71, 0x1a, 0x1c, 0x2e, 0x62, 0x61, 0x63, 0x6b, 0x65, 0x6e, 0x64,
	0x53, 0x65, 0x72, 0x69, 0x65, 0x73, 0x2e, 0x41, 0x75, 0x74, 0x6f, 0x53, 0x68, 0x65, 0x6c, 0x66,
	0x52, 0x65, 0x73, 0x70, 0x22, 0x00, 0x12, 0x48, 0x0a, 0x09, 0x48, 0x61, 0x6e, 0x64, 0x53, 0x68,
	0x65, 0x6c, 0x66, 0x12, 0x1b, 0x2e, 0x62, 0x61, 0x63, 0x6b, 0x65, 0x6e, 0x64, 0x53, 0x65, 0x72,
	0x69, 0x65, 0x73, 0x2e, 0x48, 0x61, 0x6e, 0x64, 0x53, 0x68, 0x65, 0x6c, 0x66, 0x52, 0x65, 0x71,
	0x1a, 0x1c, 0x2e, 0x62, 0x61, 0x63, 0x6b, 0x65, 0x6e, 0x64, 0x53, 0x65, 0x72, 0x69, 0x65, 0x73,
	0x2e, 0x48, 0x61, 0x6e, 0x64, 0x53, 0x68, 0x65, 0x6c, 0x66, 0x52, 0x65, 0x73, 0x70, 0x22, 0x00,
	0x12, 0x4b, 0x0a, 0x0a, 0x53, 0x65, 0x72, 0x69, 0x65, 0x73, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x1c,
	0x2e, 0x62, 0x61, 0x63, 0x6b, 0x65, 0x6e, 0x64, 0x53, 0x65, 0x72, 0x69, 0x65, 0x73, 0x2e, 0x53,
	0x65, 0x72, 0x69, 0x65, 0x73, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x1a, 0x1d, 0x2e, 0x62,
	0x61, 0x63, 0x6b, 0x65, 0x6e, 0x64, 0x53, 0x65, 0x72, 0x69, 0x65, 0x73, 0x2e, 0x53, 0x65, 0x72,
	0x69, 0x65, 0x73, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x70, 0x22, 0x00, 0x12, 0x48, 0x0a,
	0x09, 0x53, 0x65, 0x72, 0x69, 0x65, 0x73, 0x44, 0x65, 0x6c, 0x12, 0x1b, 0x2e, 0x62, 0x61, 0x63,
	0x6b, 0x65, 0x6e, 0x64, 0x53, 0x65, 0x72, 0x69, 0x65, 0x73, 0x2e, 0x53, 0x65, 0x72, 0x69, 0x65,
	0x73, 0x44, 0x65, 0x6c, 0x52, 0x65, 0x71, 0x1a, 0x1c, 0x2e, 0x62, 0x61, 0x63, 0x6b, 0x65, 0x6e,
	0x64, 0x53, 0x65, 0x72, 0x69, 0x65, 0x73, 0x2e, 0x53, 0x65, 0x72, 0x69, 0x65, 0x73, 0x44, 0x65,
	0x6c, 0x52, 0x65, 0x73, 0x70, 0x22, 0x00, 0x12, 0x4e, 0x0a, 0x0b, 0x43, 0x68, 0x65, 0x63, 0x6b,
	0x53, 0x65, 0x72, 0x69, 0x65, 0x73, 0x12, 0x1d, 0x2e, 0x62, 0x61, 0x63, 0x6b, 0x65, 0x6e, 0x64,
	0x53, 0x65, 0x72, 0x69, 0x65, 0x73, 0x2e, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x53, 0x65, 0x72, 0x69,
	0x65, 0x73, 0x52, 0x65, 0x71, 0x1a, 0x1e, 0x2e, 0x62, 0x61, 0x63, 0x6b, 0x65, 0x6e, 0x64, 0x53,
	0x65, 0x72, 0x69, 0x65, 0x73, 0x2e, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x53, 0x65, 0x72, 0x69, 0x65,
	0x73, 0x52, 0x65, 0x73, 0x70, 0x22, 0x00, 0x12, 0x60, 0x0a, 0x11, 0x55, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x53, 0x65, 0x72, 0x69, 0x65, 0x73, 0x43, 0x68, 0x61, 0x69, 0x6e, 0x12, 0x23, 0x2e, 0x62,
	0x61, 0x63, 0x6b, 0x65, 0x6e, 0x64, 0x53, 0x65, 0x72, 0x69, 0x65, 0x73, 0x2e, 0x55, 0x70, 0x64,
	0x61, 0x74, 0x65, 0x53, 0x65, 0x72, 0x69, 0x65, 0x73, 0x43, 0x68, 0x61, 0x69, 0x6e, 0x52, 0x65,
	0x71, 0x1a, 0x24, 0x2e, 0x62, 0x61, 0x63, 0x6b, 0x65, 0x6e, 0x64, 0x53, 0x65, 0x72, 0x69, 0x65,
	0x73, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x53, 0x65, 0x72, 0x69, 0x65, 0x73, 0x43, 0x68,
	0x61, 0x69, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x22, 0x00, 0x12, 0x6b, 0x0a, 0x15, 0x47, 0x65, 0x74,
	0x53, 0x65, 0x72, 0x69, 0x65, 0x73, 0x4c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65, 0x49, 0x6e,
	0x66, 0x6f, 0x12, 0x27, 0x2e, 0x62, 0x61, 0x63, 0x6b, 0x65, 0x6e, 0x64, 0x53, 0x65, 0x72, 0x69,
	0x65, 0x73, 0x2e, 0x47, 0x65, 0x74, 0x53, 0x65, 0x72, 0x69, 0x65, 0x73, 0x4c, 0x61, 0x6e, 0x67,
	0x75, 0x61, 0x67, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x71, 0x1a, 0x27, 0x2e, 0x62, 0x61,
	0x63, 0x6b, 0x65, 0x6e, 0x64, 0x53, 0x65, 0x72, 0x69, 0x65, 0x73, 0x2e, 0x47, 0x65, 0x74, 0x53,
	0x65, 0x72, 0x69, 0x65, 0x73, 0x4c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65, 0x49, 0x6e, 0x66,
	0x6f, 0x52, 0x65, 0x70, 0x22, 0x00, 0x12, 0x4e, 0x0a, 0x0b, 0x46, 0x53, 0x65, 0x72, 0x69, 0x65,
	0x73, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x1d, 0x2e, 0x62, 0x61, 0x63, 0x6b, 0x65, 0x6e, 0x64, 0x53,
	0x65, 0x72, 0x69, 0x65, 0x73, 0x2e, 0x46, 0x53, 0x65, 0x72, 0x69, 0x65, 0x73, 0x4c, 0x69, 0x73,
	0x74, 0x52, 0x65, 0x71, 0x1a, 0x1e, 0x2e, 0x62, 0x61, 0x63, 0x6b, 0x65, 0x6e, 0x64, 0x53, 0x65,
	0x72, 0x69, 0x65, 0x73, 0x2e, 0x46, 0x53, 0x65, 0x72, 0x69, 0x65, 0x73, 0x4c, 0x69, 0x73, 0x74,
	0x52, 0x65, 0x73, 0x70, 0x22, 0x00, 0x12, 0x5b, 0x0a, 0x11, 0x53, 0x65, 0x72, 0x69, 0x65, 0x73,
	0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x42, 0x79, 0x4d, 0x65, 0x6d, 0x12, 0x23, 0x2e, 0x62, 0x61,
	0x63, 0x6b, 0x65, 0x6e, 0x64, 0x53, 0x65, 0x72, 0x69, 0x65, 0x73, 0x2e, 0x53, 0x65, 0x72, 0x69,
	0x65, 0x73, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x42, 0x79, 0x4d, 0x65, 0x6d, 0x52, 0x65, 0x71,
	0x1a, 0x1f, 0x2e, 0x62, 0x61, 0x63, 0x6b, 0x65, 0x6e, 0x64, 0x53, 0x65, 0x72, 0x69, 0x65, 0x73,
	0x2e, 0x53, 0x65, 0x72, 0x69, 0x65, 0x73, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x65, 0x73,
	0x70, 0x22, 0x00, 0x12, 0x44, 0x0a, 0x09, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x4d, 0x65, 0x6d,
	0x12, 0x1b, 0x2e, 0x62, 0x61, 0x63, 0x6b, 0x65, 0x6e, 0x64, 0x53, 0x65, 0x72, 0x69, 0x65, 0x73,
	0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x4d, 0x65, 0x6d, 0x52, 0x65, 0x71, 0x1a, 0x18, 0x2e,
	0x62, 0x61, 0x63, 0x6b, 0x65, 0x6e, 0x64, 0x53, 0x65, 0x72, 0x69, 0x65, 0x73, 0x2e, 0x43, 0x6f,
	0x6d, 0x6d, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x22, 0x00, 0x12, 0x42, 0x0a, 0x08, 0x44, 0x72, 0x61,
	0x77, 0x47, 0x69, 0x66, 0x74, 0x12, 0x1a, 0x2e, 0x62, 0x61, 0x63, 0x6b, 0x65, 0x6e, 0x64, 0x53,
	0x65, 0x72, 0x69, 0x65, 0x73, 0x2e, 0x44, 0x72, 0x61, 0x77, 0x47, 0x69, 0x66, 0x74, 0x52, 0x65,
	0x71, 0x1a, 0x18, 0x2e, 0x62, 0x61, 0x63, 0x6b, 0x65, 0x6e, 0x64, 0x53, 0x65, 0x72, 0x69, 0x65,
	0x73, 0x2e, 0x43, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x22, 0x00, 0x12, 0x5d, 0x0a,
	0x10, 0x4c, 0x69, 0x73, 0x74, 0x53, 0x61, 0x6c, 0x65, 0x73, 0x41, 0x72, 0x74, 0x77, 0x6f, 0x72,
	0x6b, 0x12, 0x22, 0x2e, 0x62, 0x61, 0x63, 0x6b, 0x65, 0x6e, 0x64, 0x53, 0x65, 0x72, 0x69, 0x65,
	0x73, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x53, 0x61, 0x6c, 0x65, 0x73, 0x41, 0x72, 0x74, 0x77, 0x6f,
	0x72, 0x6b, 0x52, 0x65, 0x71, 0x1a, 0x23, 0x2e, 0x62, 0x61, 0x63, 0x6b, 0x65, 0x6e, 0x64, 0x53,
	0x65, 0x72, 0x69, 0x65, 0x73, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x53, 0x61, 0x6c, 0x65, 0x73, 0x41,
	0x72, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x52, 0x65, 0x73, 0x70, 0x22, 0x00, 0x12, 0x63, 0x0a, 0x12,
	0x43, 0x75, 0x6c, 0x74, 0x75, 0x72, 0x65, 0x41, 0x72, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x4c, 0x69,
	0x73, 0x74, 0x12, 0x24, 0x2e, 0x62, 0x61, 0x63, 0x6b, 0x65, 0x6e, 0x64, 0x53, 0x65, 0x72, 0x69,
	0x65, 0x73, 0x2e, 0x43, 0x75, 0x6c, 0x74, 0x75, 0x72, 0x65, 0x41, 0x72, 0x74, 0x77, 0x6f, 0x72,
	0x6b, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x1a, 0x25, 0x2e, 0x62, 0x61, 0x63, 0x6b, 0x65,
	0x6e, 0x64, 0x53, 0x65, 0x72, 0x69, 0x65, 0x73, 0x2e, 0x43, 0x75, 0x6c, 0x74, 0x75, 0x72, 0x65,
	0x41, 0x72, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x70, 0x22,
	0x00, 0x12, 0x45, 0x0a, 0x08, 0x53, 0x63, 0x61, 0x6e, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x1a, 0x2e,
	0x62, 0x61, 0x63, 0x6b, 0x65, 0x6e, 0x64, 0x53, 0x65, 0x72, 0x69, 0x65, 0x73, 0x2e, 0x53, 0x63,
	0x61, 0x6e, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x1a, 0x1b, 0x2e, 0x62, 0x61, 0x63, 0x6b,
	0x65, 0x6e, 0x64, 0x53, 0x65, 0x72, 0x69, 0x65, 0x73, 0x2e, 0x53, 0x63, 0x61, 0x6e, 0x4c, 0x69,
	0x73, 0x74, 0x52, 0x65, 0x73, 0x70, 0x22, 0x00, 0x12, 0x5a, 0x0a, 0x0f, 0x43, 0x69, 0x72, 0x63,
	0x75, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x21, 0x2e, 0x62, 0x61,
	0x63, 0x6b, 0x65, 0x6e, 0x64, 0x53, 0x65, 0x72, 0x69, 0x65, 0x73, 0x2e, 0x43, 0x69, 0x72, 0x63,
	0x75, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x1a, 0x22,
	0x2e, 0x62, 0x61, 0x63, 0x6b, 0x65, 0x6e, 0x64, 0x53, 0x65, 0x72, 0x69, 0x65, 0x73, 0x2e, 0x43,
	0x69, 0x72, 0x63, 0x75, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65,
	0x73, 0x70, 0x22, 0x00, 0x12, 0x56, 0x0a, 0x11, 0x43, 0x69, 0x72, 0x63, 0x75, 0x6c, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x12, 0x23, 0x2e, 0x62, 0x61, 0x63, 0x6b,
	0x65, 0x6e, 0x64, 0x53, 0x65, 0x72, 0x69, 0x65, 0x73, 0x2e, 0x43, 0x69, 0x72, 0x63, 0x75, 0x6c,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x65, 0x71, 0x1a, 0x1a,
	0x2e, 0x62, 0x61, 0x63, 0x6b, 0x65, 0x6e, 0x64, 0x53, 0x65, 0x72, 0x69, 0x65, 0x73, 0x2e, 0x43,
	0x69, 0x72, 0x63, 0x75, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0x00, 0x12, 0x5e, 0x0a, 0x13,
	0x46, 0x61, 0x6e, 0x73, 0x43, 0x69, 0x72, 0x63, 0x75, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4c,
	0x69, 0x73, 0x74, 0x12, 0x21, 0x2e, 0x62, 0x61, 0x63, 0x6b, 0x65, 0x6e, 0x64, 0x53, 0x65, 0x72,
	0x69, 0x65, 0x73, 0x2e, 0x43, 0x69, 0x72, 0x63, 0x75, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4c,
	0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x1a, 0x22, 0x2e, 0x62, 0x61, 0x63, 0x6b, 0x65, 0x6e, 0x64,
	0x53, 0x65, 0x72, 0x69, 0x65, 0x73, 0x2e, 0x43, 0x69, 0x72, 0x63, 0x75, 0x6c, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x70, 0x22, 0x00, 0x12, 0x56, 0x0a, 0x12,
	0x44, 0x72, 0x61, 0x77, 0x43, 0x75, 0x6c, 0x74, 0x75, 0x72, 0x65, 0x41, 0x72, 0x74, 0x77, 0x6f,
	0x72, 0x6b, 0x12, 0x24, 0x2e, 0x62, 0x61, 0x63, 0x6b, 0x65, 0x6e, 0x64, 0x53, 0x65, 0x72, 0x69,
	0x65, 0x73, 0x2e, 0x44, 0x72, 0x61, 0x77, 0x43, 0x75, 0x6c, 0x74, 0x75, 0x72, 0x65, 0x41, 0x72,
	0x74, 0x77, 0x6f, 0x72, 0x6b, 0x52, 0x65, 0x71, 0x1a, 0x18, 0x2e, 0x62, 0x61, 0x63, 0x6b, 0x65,
	0x6e, 0x64, 0x53, 0x65, 0x72, 0x69, 0x65, 0x73, 0x2e, 0x43, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x52,
	0x65, 0x73, 0x22, 0x00, 0x12, 0x65, 0x0a, 0x14, 0x43, 0x75, 0x6c, 0x74, 0x75, 0x72, 0x65, 0x41,
	0x72, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x12, 0x26, 0x2e, 0x62,
	0x61, 0x63, 0x6b, 0x65, 0x6e, 0x64, 0x53, 0x65, 0x72, 0x69, 0x65, 0x73, 0x2e, 0x43, 0x75, 0x6c,
	0x74, 0x75, 0x72, 0x65, 0x41, 0x72, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x44, 0x65, 0x74, 0x61, 0x69,
	0x6c, 0x52, 0x65, 0x71, 0x1a, 0x23, 0x2e, 0x62, 0x61, 0x63, 0x6b, 0x65, 0x6e, 0x64, 0x53, 0x65,
	0x72, 0x69, 0x65, 0x73, 0x2e, 0x53, 0x65, 0x72, 0x69, 0x65, 0x73, 0x43, 0x75, 0x6c, 0x74, 0x75,
	0x72, 0x65, 0x41, 0x72, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x22, 0x00, 0x12, 0x71, 0x0a, 0x17, 0x43,
	0x75, 0x6c, 0x74, 0x75, 0x72, 0x65, 0x41, 0x72, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x4f, 0x72, 0x64,
	0x65, 0x72, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x29, 0x2e, 0x62, 0x61, 0x63, 0x6b, 0x65, 0x6e, 0x64,
	0x53, 0x65, 0x72, 0x69, 0x65, 0x73, 0x2e, 0x43, 0x75, 0x6c, 0x74, 0x75, 0x72, 0x65, 0x41, 0x72,
	0x74, 0x77, 0x6f, 0x72, 0x6b, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65,
	0x71, 0x1a, 0x29, 0x2e, 0x62, 0x61, 0x63, 0x6b, 0x65, 0x6e, 0x64, 0x53, 0x65, 0x72, 0x69, 0x65,
	0x73, 0x2e, 0x43, 0x75, 0x6c, 0x74, 0x75, 0x72, 0x65, 0x41, 0x72, 0x74, 0x77, 0x6f, 0x72, 0x6b,
	0x4f, 0x72, 0x64, 0x65, 0x72, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x22, 0x00, 0x12, 0x6e,
	0x0a, 0x19, 0x43, 0x75, 0x6c, 0x74, 0x75, 0x72, 0x65, 0x41, 0x72, 0x74, 0x77, 0x6f, 0x72, 0x6b,
	0x4f, 0x72, 0x64, 0x65, 0x72, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x12, 0x2b, 0x2e, 0x62, 0x61,
	0x63, 0x6b, 0x65, 0x6e, 0x64, 0x53, 0x65, 0x72, 0x69, 0x65, 0x73, 0x2e, 0x43, 0x75, 0x6c, 0x74,
	0x75, 0x72, 0x65, 0x41, 0x72, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x44,
	0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x65, 0x71, 0x1a, 0x22, 0x2e, 0x62, 0x61, 0x63, 0x6b, 0x65,
	0x6e, 0x64, 0x53, 0x65, 0x72, 0x69, 0x65, 0x73, 0x2e, 0x43, 0x75, 0x6c, 0x74, 0x75, 0x72, 0x65,
	0x41, 0x72, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x22, 0x00, 0x12, 0x60,
	0x0a, 0x17, 0x47, 0x69, 0x76, 0x65, 0x43, 0x75, 0x6c, 0x74, 0x75, 0x72, 0x65, 0x41, 0x72, 0x74,
	0x77, 0x6f, 0x72, 0x6b, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x12, 0x29, 0x2e, 0x62, 0x61, 0x63, 0x6b,
	0x65, 0x6e, 0x64, 0x53, 0x65, 0x72, 0x69, 0x65, 0x73, 0x2e, 0x47, 0x69, 0x76, 0x65, 0x43, 0x75,
	0x6c, 0x74, 0x75, 0x72, 0x65, 0x41, 0x72, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x4f, 0x72, 0x64, 0x65,
	0x72, 0x52, 0x65, 0x71, 0x1a, 0x18, 0x2e, 0x62, 0x61, 0x63, 0x6b, 0x65, 0x6e, 0x64, 0x53, 0x65,
	0x72, 0x69, 0x65, 0x73, 0x2e, 0x43, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x22, 0x00,
	0x12, 0x66, 0x0a, 0x1a, 0x52, 0x65, 0x63, 0x65, 0x69, 0x76, 0x65, 0x43, 0x75, 0x6c, 0x74, 0x75,
	0x72, 0x65, 0x41, 0x72, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x12, 0x2c,
	0x2e, 0x62, 0x61, 0x63, 0x6b, 0x65, 0x6e, 0x64, 0x53, 0x65, 0x72, 0x69, 0x65, 0x73, 0x2e, 0x52,
	0x65, 0x63, 0x65, 0x69, 0x76, 0x65, 0x43, 0x75, 0x6c, 0x74, 0x75, 0x72, 0x65, 0x41, 0x72, 0x74,
	0x77, 0x6f, 0x72, 0x6b, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x52, 0x65, 0x71, 0x1a, 0x18, 0x2e, 0x62,
	0x61, 0x63, 0x6b, 0x65, 0x6e, 0x64, 0x53, 0x65, 0x72, 0x69, 0x65, 0x73, 0x2e, 0x43, 0x6f, 0x6d,
	0x6d, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x22, 0x00, 0x12, 0x65, 0x0a, 0x19, 0x52, 0x65, 0x66, 0x75,
	0x73, 0x65, 0x43, 0x75, 0x6c, 0x74, 0x75, 0x72, 0x65, 0x41, 0x72, 0x74, 0x77, 0x6f, 0x72, 0x6b,
	0x4f, 0x72, 0x64, 0x65, 0x72, 0x12, 0x2c, 0x2e, 0x62, 0x61, 0x63, 0x6b, 0x65, 0x6e, 0x64, 0x53,
	0x65, 0x72, 0x69, 0x65, 0x73, 0x2e, 0x52, 0x65, 0x63, 0x65, 0x69, 0x76, 0x65, 0x43, 0x75, 0x6c,
	0x74, 0x75, 0x72, 0x65, 0x41, 0x72, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x4f, 0x72, 0x64, 0x65, 0x72,
	0x52, 0x65, 0x71, 0x1a, 0x18, 0x2e, 0x62, 0x61, 0x63, 0x6b, 0x65, 0x6e, 0x64, 0x53, 0x65, 0x72,
	0x69, 0x65, 0x73, 0x2e, 0x43, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x22, 0x00, 0x12,
	0x62, 0x0a, 0x17, 0x4f, 0x76, 0x65, 0x72, 0x54, 0x69, 0x6d, 0x65, 0x43, 0x69, 0x72, 0x63, 0x75,
	0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x21, 0x2e, 0x62, 0x61, 0x63,
	0x6b, 0x65, 0x6e, 0x64, 0x53, 0x65, 0x72, 0x69, 0x65, 0x73, 0x2e, 0x43, 0x69, 0x72, 0x63, 0x75,
	0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x1a, 0x22, 0x2e,
	0x62, 0x61, 0x63, 0x6b, 0x65, 0x6e, 0x64, 0x53, 0x65, 0x72, 0x69, 0x65, 0x73, 0x2e, 0x43, 0x69,
	0x72, 0x63, 0x75, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73,
	0x70, 0x22, 0x00, 0x12, 0x6c, 0x0a, 0x1d, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x43, 0x61, 0x73,
	0x74, 0x43, 0x75, 0x6c, 0x74, 0x75, 0x72, 0x65, 0x41, 0x72, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x4f,
	0x72, 0x64, 0x65, 0x72, 0x12, 0x2f, 0x2e, 0x62, 0x61, 0x63, 0x6b, 0x65, 0x6e, 0x64, 0x53, 0x65,
	0x72, 0x69, 0x65, 0x73, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x43, 0x61, 0x73, 0x74, 0x43,
	0x75, 0x6c, 0x74, 0x75, 0x72, 0x65, 0x41, 0x72, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x4f, 0x72, 0x64,
	0x65, 0x72, 0x52, 0x65, 0x71, 0x1a, 0x18, 0x2e, 0x62, 0x61, 0x63, 0x6b, 0x65, 0x6e, 0x64, 0x53,
	0x65, 0x72, 0x69, 0x65, 0x73, 0x2e, 0x43, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x22,
	0x00, 0x12, 0x5c, 0x0a, 0x15, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x43, 0x69, 0x72, 0x63, 0x75,
	0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x48, 0x61, 0x73, 0x68, 0x12, 0x27, 0x2e, 0x62, 0x61, 0x63,
	0x6b, 0x65, 0x6e, 0x64, 0x53, 0x65, 0x72, 0x69, 0x65, 0x73, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x43, 0x69, 0x72, 0x63, 0x75, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x48, 0x61, 0x73, 0x68,
	0x52, 0x65, 0x71, 0x1a, 0x18, 0x2e, 0x62, 0x61, 0x63, 0x6b, 0x65, 0x6e, 0x64, 0x53, 0x65, 0x72,
	0x69, 0x65, 0x73, 0x2e, 0x43, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x22, 0x00, 0x12,
	0x54, 0x0a, 0x11, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x53, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x12, 0x23, 0x2e, 0x62, 0x61, 0x63, 0x6b, 0x65, 0x6e, 0x64, 0x53, 0x65,
	0x72, 0x69, 0x65, 0x73, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x4f, 0x72, 0x64, 0x65, 0x72,
	0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x71, 0x1a, 0x18, 0x2e, 0x62, 0x61, 0x63, 0x6b,
	0x65, 0x6e, 0x64, 0x53, 0x65, 0x72, 0x69, 0x65, 0x73, 0x2e, 0x43, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e,
	0x52, 0x65, 0x73, 0x22, 0x00, 0x42, 0x12, 0x5a, 0x10, 0x2e, 0x2f, 0x3b, 0x62, 0x61, 0x63, 0x6b,
	0x65, 0x6e, 0x64, 0x53, 0x65, 0x72, 0x69, 0x65, 0x73, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x33,
}

var (
	file_api_series_series_proto_rawDescOnce sync.Once
	file_api_series_series_proto_rawDescData = file_api_series_series_proto_rawDesc
)

func file_api_series_series_proto_rawDescGZIP() []byte {
	file_api_series_series_proto_rawDescOnce.Do(func() {
		file_api_series_series_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_series_series_proto_rawDescData)
	})
	return file_api_series_series_proto_rawDescData
}

var file_api_series_series_proto_msgTypes = make([]protoimpl.MessageInfo, 61)
var file_api_series_series_proto_goTypes = []interface{}{
	(*CirculationDetailReq)(nil),             // 0: backendSeries.CirculationDetailReq
	(*UpdateCirculationHashReq)(nil),         // 1: backendSeries.UpdateCirculationHashReq
	(*GiveCultureArtworkOrderReq)(nil),       // 2: backendSeries.GiveCultureArtworkOrderReq
	(*UpdateCastCultureArtworkOrderReq)(nil), // 3: backendSeries.UpdateCastCultureArtworkOrderReq
	(*ReceiveCultureArtworkOrderReq)(nil),    // 4: backendSeries.ReceiveCultureArtworkOrderReq
	(*DrawCultureArtworkReq)(nil),            // 5: backendSeries.DrawCultureArtworkReq
	(*CultureItem)(nil),                      // 6: backendSeries.CultureItem
	(*CultureArtworkDetailReq)(nil),          // 7: backendSeries.CultureArtworkDetailReq
	(*ScanListReq)(nil),                      // 8: backendSeries.ScanListReq
	(*CultureArtworkListReq)(nil),            // 9: backendSeries.CultureArtworkListReq
	(*ScanListResp)(nil),                     // 10: backendSeries.ScanListResp
	(*ScanInfo)(nil),                         // 11: backendSeries.ScanInfo
	(*CultureArtworkOrderListReq)(nil),       // 12: backendSeries.CultureArtworkOrderListReq
	(*CirculationListReq)(nil),               // 13: backendSeries.CirculationListReq
	(*CultureArtworkOrderDetailReq)(nil),     // 14: backendSeries.CultureArtworkOrderDetailReq
	(*CirculationListResp)(nil),              // 15: backendSeries.CirculationListResp
	(*Circulation)(nil),                      // 16: backendSeries.Circulation
	(*LanguageInfo)(nil),                     // 17: backendSeries.LanguageInfo
	(*SaveSeriesReq)(nil),                    // 18: backendSeries.SaveSeriesReq
	(*SeriesCultureArtwork)(nil),             // 19: backendSeries.SeriesCultureArtwork
	(*AuctionRequest)(nil),                   // 20: backendSeries.AuctionRequest
	(*SaveSeriesResp)(nil),                   // 21: backendSeries.SaveSeriesResp
	(*SeriesDetailReq)(nil),                  // 22: backendSeries.SeriesDetailReq
	(*CultureArtworkListResp)(nil),           // 23: backendSeries.CultureArtworkListResp
	(*CultureArtworkOrderListRes)(nil),       // 24: backendSeries.CultureArtworkOrderListRes
	(*CultureArtworkOrder)(nil),              // 25: backendSeries.CultureArtworkOrder
	(*SeriesDetailResp)(nil),                 // 26: backendSeries.SeriesDetailResp
	(*UpdateSeriesLangReq)(nil),              // 27: backendSeries.UpdateSeriesLangReq
	(*UpdateSeriesLangResp)(nil),             // 28: backendSeries.UpdateSeriesLangResp
	(*AutoShelfReq)(nil),                     // 29: backendSeries.AutoShelfReq
	(*AutoShelfResp)(nil),                    // 30: backendSeries.AutoShelfResp
	(*HandShelfReq)(nil),                     // 31: backendSeries.HandShelfReq
	(*HandShelfResp)(nil),                    // 32: backendSeries.HandShelfResp
	(*SeriesListReq)(nil),                    // 33: backendSeries.SeriesListReq
	(*SeriesListResp)(nil),                   // 34: backendSeries.SeriesListResp
	(*SeriesDelReq)(nil),                     // 35: backendSeries.SeriesDelReq
	(*SeriesDelResp)(nil),                    // 36: backendSeries.SeriesDelResp
	(*CheckSeriesReq)(nil),                   // 37: backendSeries.CheckSeriesReq
	(*CheckSeriesResp)(nil),                  // 38: backendSeries.CheckSeriesResp
	(*UpdateSeriesChainReq)(nil),             // 39: backendSeries.UpdateSeriesChainReq
	(*UpdateSeriesChainResp)(nil),            // 40: backendSeries.UpdateSeriesChainResp
	(*GetSeriesLanguageInfoReq)(nil),         // 41: backendSeries.GetSeriesLanguageInfoReq
	(*GetSeriesLanguageInfoRep)(nil),         // 42: backendSeries.GetSeriesLanguageInfoRep
	(*DrawGiftReq)(nil),                      // 43: backendSeries.DrawGiftReq
	(*UpdateMemReq)(nil),                     // 44: backendSeries.UpdateMemReq
	(*CollectionM)(nil),                      // 45: backendSeries.CollectionM
	(*CommonRes)(nil),                        // 46: backendSeries.CommonRes
	(*FSeriesListReq)(nil),                   // 47: backendSeries.FSeriesListReq
	(*FSeriesListResp)(nil),                  // 48: backendSeries.FSeriesListResp
	(*SeriesDetailByMemReq)(nil),             // 49: backendSeries.SeriesDetailByMemReq
	(*SeriesArtworkData)(nil),                // 50: backendSeries.SeriesArtworkData
	(*SeriesArtworkLangData)(nil),            // 51: backendSeries.SeriesArtworkLangData
	(*ListSalesArtworkReq)(nil),              // 52: backendSeries.ListSalesArtworkReq
	(*SalesArtwork)(nil),                     // 53: backendSeries.SalesArtwork
	(*ListSalesArtworkResp)(nil),             // 54: backendSeries.ListSalesArtworkResp
	(*UpdateOrderStatusReq)(nil),             // 55: backendSeries.UpdateOrderStatusReq
	(*SeriesDetailResp_SeriesLangInfo)(nil),  // 56: backendSeries.SeriesDetailResp.SeriesLangInfo
	(*SeriesListResp_Info)(nil),              // 57: backendSeries.SeriesListResp.Info
	(*CheckSeriesResp_Info)(nil),             // 58: backendSeries.CheckSeriesResp.Info
	nil,                                      // 59: backendSeries.UpdateMemReq.CollectionMapEntry
	(*FSeriesListResp_Info)(nil),             // 60: backendSeries.FSeriesListResp.Info
}
var file_api_series_series_proto_depIdxs = []int32{
	11, // 0: backendSeries.ScanListResp.list:type_name -> backendSeries.ScanInfo
	16, // 1: backendSeries.CirculationListResp.list:type_name -> backendSeries.Circulation
	50, // 2: backendSeries.SaveSeriesReq.artworks:type_name -> backendSeries.SeriesArtworkData
	20, // 3: backendSeries.SaveSeriesReq.auction:type_name -> backendSeries.AuctionRequest
	19, // 4: backendSeries.SaveSeriesReq.seriesCultureArtworks:type_name -> backendSeries.SeriesCultureArtwork
	6,  // 5: backendSeries.SeriesCultureArtwork.mainImgInfo:type_name -> backendSeries.CultureItem
	6,  // 6: backendSeries.SeriesCultureArtwork.cultureItem:type_name -> backendSeries.CultureItem
	19, // 7: backendSeries.CultureArtworkListResp.list:type_name -> backendSeries.SeriesCultureArtwork
	25, // 8: backendSeries.CultureArtworkOrderListRes.list:type_name -> backendSeries.CultureArtworkOrder
	6,  // 9: backendSeries.CultureArtworkOrder.cultureItem:type_name -> backendSeries.CultureItem
	56, // 10: backendSeries.SeriesDetailResp.seriesLang:type_name -> backendSeries.SeriesDetailResp.SeriesLangInfo
	50, // 11: backendSeries.SeriesDetailResp.artworks:type_name -> backendSeries.SeriesArtworkData
	20, // 12: backendSeries.SeriesDetailResp.auction:type_name -> backendSeries.AuctionRequest
	19, // 13: backendSeries.SeriesDetailResp.seriesCultureArtworks:type_name -> backendSeries.SeriesCultureArtwork
	57, // 14: backendSeries.SeriesListResp.data:type_name -> backendSeries.SeriesListResp.Info
	58, // 15: backendSeries.CheckSeriesResp.data:type_name -> backendSeries.CheckSeriesResp.Info
	17, // 16: backendSeries.GetSeriesLanguageInfoReq.languageInfo:type_name -> backendSeries.LanguageInfo
	17, // 17: backendSeries.GetSeriesLanguageInfoRep.languageInfo:type_name -> backendSeries.LanguageInfo
	59, // 18: backendSeries.UpdateMemReq.collectionMap:type_name -> backendSeries.UpdateMemReq.CollectionMapEntry
	60, // 19: backendSeries.FSeriesListResp.data:type_name -> backendSeries.FSeriesListResp.Info
	51, // 20: backendSeries.SeriesArtworkData.lang:type_name -> backendSeries.SeriesArtworkLangData
	53, // 21: backendSeries.ListSalesArtworkResp.data:type_name -> backendSeries.SalesArtwork
	18, // 22: backendSeries.Series.SaveSeries:input_type -> backendSeries.SaveSeriesReq
	19, // 23: backendSeries.Series.SaveCulturalArtwork:input_type -> backendSeries.SeriesCultureArtwork
	22, // 24: backendSeries.Series.SeriesDetail:input_type -> backendSeries.SeriesDetailReq
	27, // 25: backendSeries.Series.UpdateSeriesLang:input_type -> backendSeries.UpdateSeriesLangReq
	29, // 26: backendSeries.Series.AutoShelf:input_type -> backendSeries.AutoShelfReq
	31, // 27: backendSeries.Series.HandShelf:input_type -> backendSeries.HandShelfReq
	33, // 28: backendSeries.Series.SeriesList:input_type -> backendSeries.SeriesListReq
	35, // 29: backendSeries.Series.SeriesDel:input_type -> backendSeries.SeriesDelReq
	37, // 30: backendSeries.Series.CheckSeries:input_type -> backendSeries.CheckSeriesReq
	39, // 31: backendSeries.Series.UpdateSeriesChain:input_type -> backendSeries.UpdateSeriesChainReq
	41, // 32: backendSeries.Series.GetSeriesLanguageInfo:input_type -> backendSeries.GetSeriesLanguageInfoReq
	47, // 33: backendSeries.Series.FSeriesList:input_type -> backendSeries.FSeriesListReq
	49, // 34: backendSeries.Series.SeriesDetailByMem:input_type -> backendSeries.SeriesDetailByMemReq
	44, // 35: backendSeries.Series.UpdateMem:input_type -> backendSeries.UpdateMemReq
	43, // 36: backendSeries.Series.DrawGift:input_type -> backendSeries.DrawGiftReq
	52, // 37: backendSeries.Series.ListSalesArtwork:input_type -> backendSeries.ListSalesArtworkReq
	9,  // 38: backendSeries.Series.CultureArtworkList:input_type -> backendSeries.CultureArtworkListReq
	8,  // 39: backendSeries.Series.ScanList:input_type -> backendSeries.ScanListReq
	13, // 40: backendSeries.Series.CirculationList:input_type -> backendSeries.CirculationListReq
	0,  // 41: backendSeries.Series.CirculationDetail:input_type -> backendSeries.CirculationDetailReq
	13, // 42: backendSeries.Series.FansCirculationList:input_type -> backendSeries.CirculationListReq
	5,  // 43: backendSeries.Series.DrawCultureArtwork:input_type -> backendSeries.DrawCultureArtworkReq
	7,  // 44: backendSeries.Series.CultureArtworkDetail:input_type -> backendSeries.CultureArtworkDetailReq
	12, // 45: backendSeries.Series.CultureArtworkOrderList:input_type -> backendSeries.CultureArtworkOrderListReq
	14, // 46: backendSeries.Series.CultureArtworkOrderDetail:input_type -> backendSeries.CultureArtworkOrderDetailReq
	2,  // 47: backendSeries.Series.GiveCultureArtworkOrder:input_type -> backendSeries.GiveCultureArtworkOrderReq
	4,  // 48: backendSeries.Series.ReceiveCultureArtworkOrder:input_type -> backendSeries.ReceiveCultureArtworkOrderReq
	4,  // 49: backendSeries.Series.RefuseCultureArtworkOrder:input_type -> backendSeries.ReceiveCultureArtworkOrderReq
	13, // 50: backendSeries.Series.OverTimeCirculationList:input_type -> backendSeries.CirculationListReq
	3,  // 51: backendSeries.Series.UpdateCastCultureArtworkOrder:input_type -> backendSeries.UpdateCastCultureArtworkOrderReq
	1,  // 52: backendSeries.Series.UpdateCirculationHash:input_type -> backendSeries.UpdateCirculationHashReq
	55, // 53: backendSeries.Series.UpdateOrderStatus:input_type -> backendSeries.UpdateOrderStatusReq
	21, // 54: backendSeries.Series.SaveSeries:output_type -> backendSeries.SaveSeriesResp
	19, // 55: backendSeries.Series.SaveCulturalArtwork:output_type -> backendSeries.SeriesCultureArtwork
	26, // 56: backendSeries.Series.SeriesDetail:output_type -> backendSeries.SeriesDetailResp
	28, // 57: backendSeries.Series.UpdateSeriesLang:output_type -> backendSeries.UpdateSeriesLangResp
	30, // 58: backendSeries.Series.AutoShelf:output_type -> backendSeries.AutoShelfResp
	32, // 59: backendSeries.Series.HandShelf:output_type -> backendSeries.HandShelfResp
	34, // 60: backendSeries.Series.SeriesList:output_type -> backendSeries.SeriesListResp
	36, // 61: backendSeries.Series.SeriesDel:output_type -> backendSeries.SeriesDelResp
	38, // 62: backendSeries.Series.CheckSeries:output_type -> backendSeries.CheckSeriesResp
	40, // 63: backendSeries.Series.UpdateSeriesChain:output_type -> backendSeries.UpdateSeriesChainResp
	42, // 64: backendSeries.Series.GetSeriesLanguageInfo:output_type -> backendSeries.GetSeriesLanguageInfoRep
	48, // 65: backendSeries.Series.FSeriesList:output_type -> backendSeries.FSeriesListResp
	26, // 66: backendSeries.Series.SeriesDetailByMem:output_type -> backendSeries.SeriesDetailResp
	46, // 67: backendSeries.Series.UpdateMem:output_type -> backendSeries.CommonRes
	46, // 68: backendSeries.Series.DrawGift:output_type -> backendSeries.CommonRes
	54, // 69: backendSeries.Series.ListSalesArtwork:output_type -> backendSeries.ListSalesArtworkResp
	23, // 70: backendSeries.Series.CultureArtworkList:output_type -> backendSeries.CultureArtworkListResp
	10, // 71: backendSeries.Series.ScanList:output_type -> backendSeries.ScanListResp
	15, // 72: backendSeries.Series.CirculationList:output_type -> backendSeries.CirculationListResp
	16, // 73: backendSeries.Series.CirculationDetail:output_type -> backendSeries.Circulation
	15, // 74: backendSeries.Series.FansCirculationList:output_type -> backendSeries.CirculationListResp
	46, // 75: backendSeries.Series.DrawCultureArtwork:output_type -> backendSeries.CommonRes
	19, // 76: backendSeries.Series.CultureArtworkDetail:output_type -> backendSeries.SeriesCultureArtwork
	24, // 77: backendSeries.Series.CultureArtworkOrderList:output_type -> backendSeries.CultureArtworkOrderListRes
	25, // 78: backendSeries.Series.CultureArtworkOrderDetail:output_type -> backendSeries.CultureArtworkOrder
	46, // 79: backendSeries.Series.GiveCultureArtworkOrder:output_type -> backendSeries.CommonRes
	46, // 80: backendSeries.Series.ReceiveCultureArtworkOrder:output_type -> backendSeries.CommonRes
	46, // 81: backendSeries.Series.RefuseCultureArtworkOrder:output_type -> backendSeries.CommonRes
	15, // 82: backendSeries.Series.OverTimeCirculationList:output_type -> backendSeries.CirculationListResp
	46, // 83: backendSeries.Series.UpdateCastCultureArtworkOrder:output_type -> backendSeries.CommonRes
	46, // 84: backendSeries.Series.UpdateCirculationHash:output_type -> backendSeries.CommonRes
	46, // 85: backendSeries.Series.UpdateOrderStatus:output_type -> backendSeries.CommonRes
	54, // [54:86] is the sub-list for method output_type
	22, // [22:54] is the sub-list for method input_type
	22, // [22:22] is the sub-list for extension type_name
	22, // [22:22] is the sub-list for extension extendee
	0,  // [0:22] is the sub-list for field type_name
}

func init() { file_api_series_series_proto_init() }
func file_api_series_series_proto_init() {
	if File_api_series_series_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_api_series_series_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CirculationDetailReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_series_series_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateCirculationHashReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_series_series_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GiveCultureArtworkOrderReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_series_series_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateCastCultureArtworkOrderReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_series_series_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReceiveCultureArtworkOrderReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_series_series_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DrawCultureArtworkReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_series_series_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CultureItem); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_series_series_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CultureArtworkDetailReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_series_series_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ScanListReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_series_series_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CultureArtworkListReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_series_series_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ScanListResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_series_series_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ScanInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_series_series_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CultureArtworkOrderListReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_series_series_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CirculationListReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_series_series_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CultureArtworkOrderDetailReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_series_series_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CirculationListResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_series_series_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Circulation); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_series_series_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LanguageInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_series_series_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SaveSeriesReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_series_series_proto_msgTypes[19].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SeriesCultureArtwork); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_series_series_proto_msgTypes[20].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AuctionRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_series_series_proto_msgTypes[21].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SaveSeriesResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_series_series_proto_msgTypes[22].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SeriesDetailReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_series_series_proto_msgTypes[23].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CultureArtworkListResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_series_series_proto_msgTypes[24].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CultureArtworkOrderListRes); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_series_series_proto_msgTypes[25].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CultureArtworkOrder); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_series_series_proto_msgTypes[26].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SeriesDetailResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_series_series_proto_msgTypes[27].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateSeriesLangReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_series_series_proto_msgTypes[28].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateSeriesLangResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_series_series_proto_msgTypes[29].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AutoShelfReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_series_series_proto_msgTypes[30].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AutoShelfResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_series_series_proto_msgTypes[31].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*HandShelfReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_series_series_proto_msgTypes[32].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*HandShelfResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_series_series_proto_msgTypes[33].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SeriesListReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_series_series_proto_msgTypes[34].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SeriesListResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_series_series_proto_msgTypes[35].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SeriesDelReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_series_series_proto_msgTypes[36].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SeriesDelResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_series_series_proto_msgTypes[37].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CheckSeriesReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_series_series_proto_msgTypes[38].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CheckSeriesResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_series_series_proto_msgTypes[39].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateSeriesChainReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_series_series_proto_msgTypes[40].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateSeriesChainResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_series_series_proto_msgTypes[41].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetSeriesLanguageInfoReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_series_series_proto_msgTypes[42].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetSeriesLanguageInfoRep); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_series_series_proto_msgTypes[43].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DrawGiftReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_series_series_proto_msgTypes[44].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateMemReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_series_series_proto_msgTypes[45].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CollectionM); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_series_series_proto_msgTypes[46].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CommonRes); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_series_series_proto_msgTypes[47].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FSeriesListReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_series_series_proto_msgTypes[48].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FSeriesListResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_series_series_proto_msgTypes[49].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SeriesDetailByMemReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_series_series_proto_msgTypes[50].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SeriesArtworkData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_series_series_proto_msgTypes[51].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SeriesArtworkLangData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_series_series_proto_msgTypes[52].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListSalesArtworkReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_series_series_proto_msgTypes[53].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SalesArtwork); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_series_series_proto_msgTypes[54].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListSalesArtworkResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_series_series_proto_msgTypes[55].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateOrderStatusReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_series_series_proto_msgTypes[56].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SeriesDetailResp_SeriesLangInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_series_series_proto_msgTypes[57].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SeriesListResp_Info); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_series_series_proto_msgTypes[58].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CheckSeriesResp_Info); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_series_series_proto_msgTypes[60].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FSeriesListResp_Info); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_series_series_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   61,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_api_series_series_proto_goTypes,
		DependencyIndexes: file_api_series_series_proto_depIdxs,
		MessageInfos:      file_api_series_series_proto_msgTypes,
	}.Build()
	File_api_series_series_proto = out.File
	file_api_series_series_proto_rawDesc = nil
	file_api_series_series_proto_goTypes = nil
	file_api_series_series_proto_depIdxs = nil
}
