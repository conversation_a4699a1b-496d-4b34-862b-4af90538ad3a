// Code generated by protoc-gen-go-triple. DO NOT EDIT.
// versions:
// - protoc-gen-go-triple v1.0.8
// - protoc             v6.30.0--rc2
// source: api/series/series.proto

package backendSeries

import (
	context "context"
	protocol "dubbo.apache.org/dubbo-go/v3/protocol"
	dubbo3 "dubbo.apache.org/dubbo-go/v3/protocol/dubbo3"
	invocation "dubbo.apache.org/dubbo-go/v3/protocol/invocation"
	grpc_go "github.com/dubbogo/grpc-go"
	codes "github.com/dubbogo/grpc-go/codes"
	metadata "github.com/dubbogo/grpc-go/metadata"
	status "github.com/dubbogo/grpc-go/status"
	common "github.com/dubbogo/triple/pkg/common"
	constant "github.com/dubbogo/triple/pkg/common/constant"
	triple "github.com/dubbogo/triple/pkg/triple"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc_go.SupportPackageIsVersion7

// SeriesClient is the client API for Series service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type SeriesClient interface {
	SaveSeries(ctx context.Context, in *SaveSeriesReq, opts ...grpc_go.CallOption) (*SaveSeriesResp, common.ErrorWithAttachment)
	SaveCulturalArtwork(ctx context.Context, in *SeriesCultureArtwork, opts ...grpc_go.CallOption) (*SeriesCultureArtwork, common.ErrorWithAttachment)
	SeriesDetail(ctx context.Context, in *SeriesDetailReq, opts ...grpc_go.CallOption) (*SeriesDetailResp, common.ErrorWithAttachment)
	UpdateSeriesLang(ctx context.Context, in *UpdateSeriesLangReq, opts ...grpc_go.CallOption) (*UpdateSeriesLangResp, common.ErrorWithAttachment)
	AutoShelf(ctx context.Context, in *AutoShelfReq, opts ...grpc_go.CallOption) (*AutoShelfResp, common.ErrorWithAttachment)
	HandShelf(ctx context.Context, in *HandShelfReq, opts ...grpc_go.CallOption) (*HandShelfResp, common.ErrorWithAttachment)
	SeriesList(ctx context.Context, in *SeriesListReq, opts ...grpc_go.CallOption) (*SeriesListResp, common.ErrorWithAttachment)
	SeriesDel(ctx context.Context, in *SeriesDelReq, opts ...grpc_go.CallOption) (*SeriesDelResp, common.ErrorWithAttachment)
	CheckSeries(ctx context.Context, in *CheckSeriesReq, opts ...grpc_go.CallOption) (*CheckSeriesResp, common.ErrorWithAttachment)
	UpdateSeriesChain(ctx context.Context, in *UpdateSeriesChainReq, opts ...grpc_go.CallOption) (*UpdateSeriesChainResp, common.ErrorWithAttachment)
	GetSeriesLanguageInfo(ctx context.Context, in *GetSeriesLanguageInfoReq, opts ...grpc_go.CallOption) (*GetSeriesLanguageInfoRep, common.ErrorWithAttachment)
	FSeriesList(ctx context.Context, in *FSeriesListReq, opts ...grpc_go.CallOption) (*FSeriesListResp, common.ErrorWithAttachment)
	SeriesDetailByMem(ctx context.Context, in *SeriesDetailByMemReq, opts ...grpc_go.CallOption) (*SeriesDetailResp, common.ErrorWithAttachment)
	UpdateMem(ctx context.Context, in *UpdateMemReq, opts ...grpc_go.CallOption) (*CommonRes, common.ErrorWithAttachment)
	DrawGift(ctx context.Context, in *DrawGiftReq, opts ...grpc_go.CallOption) (*CommonRes, common.ErrorWithAttachment)
	ListSalesArtwork(ctx context.Context, in *ListSalesArtworkReq, opts ...grpc_go.CallOption) (*ListSalesArtworkResp, common.ErrorWithAttachment)
	// 万博相关数据
	CultureArtworkList(ctx context.Context, in *CultureArtworkListReq, opts ...grpc_go.CallOption) (*CultureArtworkListResp, common.ErrorWithAttachment)
	ScanList(ctx context.Context, in *ScanListReq, opts ...grpc_go.CallOption) (*ScanListResp, common.ErrorWithAttachment)
	CirculationList(ctx context.Context, in *CirculationListReq, opts ...grpc_go.CallOption) (*CirculationListResp, common.ErrorWithAttachment)
	CirculationDetail(ctx context.Context, in *CirculationDetailReq, opts ...grpc_go.CallOption) (*Circulation, common.ErrorWithAttachment)
	FansCirculationList(ctx context.Context, in *CirculationListReq, opts ...grpc_go.CallOption) (*CirculationListResp, common.ErrorWithAttachment)
	DrawCultureArtwork(ctx context.Context, in *DrawCultureArtworkReq, opts ...grpc_go.CallOption) (*CommonRes, common.ErrorWithAttachment)
	CultureArtworkDetail(ctx context.Context, in *CultureArtworkDetailReq, opts ...grpc_go.CallOption) (*SeriesCultureArtwork, common.ErrorWithAttachment)
	CultureArtworkOrderList(ctx context.Context, in *CultureArtworkOrderListReq, opts ...grpc_go.CallOption) (*CultureArtworkOrderListRes, common.ErrorWithAttachment)
	CultureArtworkOrderDetail(ctx context.Context, in *CultureArtworkOrderDetailReq, opts ...grpc_go.CallOption) (*CultureArtworkOrder, common.ErrorWithAttachment)
	GiveCultureArtworkOrder(ctx context.Context, in *GiveCultureArtworkOrderReq, opts ...grpc_go.CallOption) (*CommonRes, common.ErrorWithAttachment)
	ReceiveCultureArtworkOrder(ctx context.Context, in *ReceiveCultureArtworkOrderReq, opts ...grpc_go.CallOption) (*CommonRes, common.ErrorWithAttachment)
	RefuseCultureArtworkOrder(ctx context.Context, in *ReceiveCultureArtworkOrderReq, opts ...grpc_go.CallOption) (*CommonRes, common.ErrorWithAttachment)
	OverTimeCirculationList(ctx context.Context, in *CirculationListReq, opts ...grpc_go.CallOption) (*CirculationListResp, common.ErrorWithAttachment)
	UpdateCastCultureArtworkOrder(ctx context.Context, in *UpdateCastCultureArtworkOrderReq, opts ...grpc_go.CallOption) (*CommonRes, common.ErrorWithAttachment)
	UpdateCirculationHash(ctx context.Context, in *UpdateCirculationHashReq, opts ...grpc_go.CallOption) (*CommonRes, common.ErrorWithAttachment)
	// 更新 订单状态
	UpdateOrderStatus(ctx context.Context, in *UpdateOrderStatusReq, opts ...grpc_go.CallOption) (*CommonRes, common.ErrorWithAttachment)
}

type seriesClient struct {
	cc *triple.TripleConn
}

type SeriesClientImpl struct {
	SaveSeries                    func(ctx context.Context, in *SaveSeriesReq) (*SaveSeriesResp, error)
	SaveCulturalArtwork           func(ctx context.Context, in *SeriesCultureArtwork) (*SeriesCultureArtwork, error)
	SeriesDetail                  func(ctx context.Context, in *SeriesDetailReq) (*SeriesDetailResp, error)
	UpdateSeriesLang              func(ctx context.Context, in *UpdateSeriesLangReq) (*UpdateSeriesLangResp, error)
	AutoShelf                     func(ctx context.Context, in *AutoShelfReq) (*AutoShelfResp, error)
	HandShelf                     func(ctx context.Context, in *HandShelfReq) (*HandShelfResp, error)
	SeriesList                    func(ctx context.Context, in *SeriesListReq) (*SeriesListResp, error)
	SeriesDel                     func(ctx context.Context, in *SeriesDelReq) (*SeriesDelResp, error)
	CheckSeries                   func(ctx context.Context, in *CheckSeriesReq) (*CheckSeriesResp, error)
	UpdateSeriesChain             func(ctx context.Context, in *UpdateSeriesChainReq) (*UpdateSeriesChainResp, error)
	GetSeriesLanguageInfo         func(ctx context.Context, in *GetSeriesLanguageInfoReq) (*GetSeriesLanguageInfoRep, error)
	FSeriesList                   func(ctx context.Context, in *FSeriesListReq) (*FSeriesListResp, error)
	SeriesDetailByMem             func(ctx context.Context, in *SeriesDetailByMemReq) (*SeriesDetailResp, error)
	UpdateMem                     func(ctx context.Context, in *UpdateMemReq) (*CommonRes, error)
	DrawGift                      func(ctx context.Context, in *DrawGiftReq) (*CommonRes, error)
	ListSalesArtwork              func(ctx context.Context, in *ListSalesArtworkReq) (*ListSalesArtworkResp, error)
	CultureArtworkList            func(ctx context.Context, in *CultureArtworkListReq) (*CultureArtworkListResp, error)
	ScanList                      func(ctx context.Context, in *ScanListReq) (*ScanListResp, error)
	CirculationList               func(ctx context.Context, in *CirculationListReq) (*CirculationListResp, error)
	CirculationDetail             func(ctx context.Context, in *CirculationDetailReq) (*Circulation, error)
	FansCirculationList           func(ctx context.Context, in *CirculationListReq) (*CirculationListResp, error)
	DrawCultureArtwork            func(ctx context.Context, in *DrawCultureArtworkReq) (*CommonRes, error)
	CultureArtworkDetail          func(ctx context.Context, in *CultureArtworkDetailReq) (*SeriesCultureArtwork, error)
	CultureArtworkOrderList       func(ctx context.Context, in *CultureArtworkOrderListReq) (*CultureArtworkOrderListRes, error)
	CultureArtworkOrderDetail     func(ctx context.Context, in *CultureArtworkOrderDetailReq) (*CultureArtworkOrder, error)
	GiveCultureArtworkOrder       func(ctx context.Context, in *GiveCultureArtworkOrderReq) (*CommonRes, error)
	ReceiveCultureArtworkOrder    func(ctx context.Context, in *ReceiveCultureArtworkOrderReq) (*CommonRes, error)
	RefuseCultureArtworkOrder     func(ctx context.Context, in *ReceiveCultureArtworkOrderReq) (*CommonRes, error)
	OverTimeCirculationList       func(ctx context.Context, in *CirculationListReq) (*CirculationListResp, error)
	UpdateCastCultureArtworkOrder func(ctx context.Context, in *UpdateCastCultureArtworkOrderReq) (*CommonRes, error)
	UpdateCirculationHash         func(ctx context.Context, in *UpdateCirculationHashReq) (*CommonRes, error)
	UpdateOrderStatus             func(ctx context.Context, in *UpdateOrderStatusReq) (*CommonRes, error)
}

func (c *SeriesClientImpl) GetDubboStub(cc *triple.TripleConn) SeriesClient {
	return NewSeriesClient(cc)
}

func (c *SeriesClientImpl) XXX_InterfaceName() string {
	return "backendSeries.Series"
}

func NewSeriesClient(cc *triple.TripleConn) SeriesClient {
	return &seriesClient{cc}
}

func (c *seriesClient) SaveSeries(ctx context.Context, in *SaveSeriesReq, opts ...grpc_go.CallOption) (*SaveSeriesResp, common.ErrorWithAttachment) {
	out := new(SaveSeriesResp)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/SaveSeries", in, out)
}

func (c *seriesClient) SaveCulturalArtwork(ctx context.Context, in *SeriesCultureArtwork, opts ...grpc_go.CallOption) (*SeriesCultureArtwork, common.ErrorWithAttachment) {
	out := new(SeriesCultureArtwork)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/SaveCulturalArtwork", in, out)
}

func (c *seriesClient) SeriesDetail(ctx context.Context, in *SeriesDetailReq, opts ...grpc_go.CallOption) (*SeriesDetailResp, common.ErrorWithAttachment) {
	out := new(SeriesDetailResp)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/SeriesDetail", in, out)
}

func (c *seriesClient) UpdateSeriesLang(ctx context.Context, in *UpdateSeriesLangReq, opts ...grpc_go.CallOption) (*UpdateSeriesLangResp, common.ErrorWithAttachment) {
	out := new(UpdateSeriesLangResp)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/UpdateSeriesLang", in, out)
}

func (c *seriesClient) AutoShelf(ctx context.Context, in *AutoShelfReq, opts ...grpc_go.CallOption) (*AutoShelfResp, common.ErrorWithAttachment) {
	out := new(AutoShelfResp)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/AutoShelf", in, out)
}

func (c *seriesClient) HandShelf(ctx context.Context, in *HandShelfReq, opts ...grpc_go.CallOption) (*HandShelfResp, common.ErrorWithAttachment) {
	out := new(HandShelfResp)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/HandShelf", in, out)
}

func (c *seriesClient) SeriesList(ctx context.Context, in *SeriesListReq, opts ...grpc_go.CallOption) (*SeriesListResp, common.ErrorWithAttachment) {
	out := new(SeriesListResp)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/SeriesList", in, out)
}

func (c *seriesClient) SeriesDel(ctx context.Context, in *SeriesDelReq, opts ...grpc_go.CallOption) (*SeriesDelResp, common.ErrorWithAttachment) {
	out := new(SeriesDelResp)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/SeriesDel", in, out)
}

func (c *seriesClient) CheckSeries(ctx context.Context, in *CheckSeriesReq, opts ...grpc_go.CallOption) (*CheckSeriesResp, common.ErrorWithAttachment) {
	out := new(CheckSeriesResp)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/CheckSeries", in, out)
}

func (c *seriesClient) UpdateSeriesChain(ctx context.Context, in *UpdateSeriesChainReq, opts ...grpc_go.CallOption) (*UpdateSeriesChainResp, common.ErrorWithAttachment) {
	out := new(UpdateSeriesChainResp)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/UpdateSeriesChain", in, out)
}

func (c *seriesClient) GetSeriesLanguageInfo(ctx context.Context, in *GetSeriesLanguageInfoReq, opts ...grpc_go.CallOption) (*GetSeriesLanguageInfoRep, common.ErrorWithAttachment) {
	out := new(GetSeriesLanguageInfoRep)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/GetSeriesLanguageInfo", in, out)
}

func (c *seriesClient) FSeriesList(ctx context.Context, in *FSeriesListReq, opts ...grpc_go.CallOption) (*FSeriesListResp, common.ErrorWithAttachment) {
	out := new(FSeriesListResp)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/FSeriesList", in, out)
}

func (c *seriesClient) SeriesDetailByMem(ctx context.Context, in *SeriesDetailByMemReq, opts ...grpc_go.CallOption) (*SeriesDetailResp, common.ErrorWithAttachment) {
	out := new(SeriesDetailResp)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/SeriesDetailByMem", in, out)
}

func (c *seriesClient) UpdateMem(ctx context.Context, in *UpdateMemReq, opts ...grpc_go.CallOption) (*CommonRes, common.ErrorWithAttachment) {
	out := new(CommonRes)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/UpdateMem", in, out)
}

func (c *seriesClient) DrawGift(ctx context.Context, in *DrawGiftReq, opts ...grpc_go.CallOption) (*CommonRes, common.ErrorWithAttachment) {
	out := new(CommonRes)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/DrawGift", in, out)
}

func (c *seriesClient) ListSalesArtwork(ctx context.Context, in *ListSalesArtworkReq, opts ...grpc_go.CallOption) (*ListSalesArtworkResp, common.ErrorWithAttachment) {
	out := new(ListSalesArtworkResp)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/ListSalesArtwork", in, out)
}

func (c *seriesClient) CultureArtworkList(ctx context.Context, in *CultureArtworkListReq, opts ...grpc_go.CallOption) (*CultureArtworkListResp, common.ErrorWithAttachment) {
	out := new(CultureArtworkListResp)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/CultureArtworkList", in, out)
}

func (c *seriesClient) ScanList(ctx context.Context, in *ScanListReq, opts ...grpc_go.CallOption) (*ScanListResp, common.ErrorWithAttachment) {
	out := new(ScanListResp)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/ScanList", in, out)
}

func (c *seriesClient) CirculationList(ctx context.Context, in *CirculationListReq, opts ...grpc_go.CallOption) (*CirculationListResp, common.ErrorWithAttachment) {
	out := new(CirculationListResp)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/CirculationList", in, out)
}

func (c *seriesClient) CirculationDetail(ctx context.Context, in *CirculationDetailReq, opts ...grpc_go.CallOption) (*Circulation, common.ErrorWithAttachment) {
	out := new(Circulation)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/CirculationDetail", in, out)
}

func (c *seriesClient) FansCirculationList(ctx context.Context, in *CirculationListReq, opts ...grpc_go.CallOption) (*CirculationListResp, common.ErrorWithAttachment) {
	out := new(CirculationListResp)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/FansCirculationList", in, out)
}

func (c *seriesClient) DrawCultureArtwork(ctx context.Context, in *DrawCultureArtworkReq, opts ...grpc_go.CallOption) (*CommonRes, common.ErrorWithAttachment) {
	out := new(CommonRes)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/DrawCultureArtwork", in, out)
}

func (c *seriesClient) CultureArtworkDetail(ctx context.Context, in *CultureArtworkDetailReq, opts ...grpc_go.CallOption) (*SeriesCultureArtwork, common.ErrorWithAttachment) {
	out := new(SeriesCultureArtwork)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/CultureArtworkDetail", in, out)
}

func (c *seriesClient) CultureArtworkOrderList(ctx context.Context, in *CultureArtworkOrderListReq, opts ...grpc_go.CallOption) (*CultureArtworkOrderListRes, common.ErrorWithAttachment) {
	out := new(CultureArtworkOrderListRes)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/CultureArtworkOrderList", in, out)
}

func (c *seriesClient) CultureArtworkOrderDetail(ctx context.Context, in *CultureArtworkOrderDetailReq, opts ...grpc_go.CallOption) (*CultureArtworkOrder, common.ErrorWithAttachment) {
	out := new(CultureArtworkOrder)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/CultureArtworkOrderDetail", in, out)
}

func (c *seriesClient) GiveCultureArtworkOrder(ctx context.Context, in *GiveCultureArtworkOrderReq, opts ...grpc_go.CallOption) (*CommonRes, common.ErrorWithAttachment) {
	out := new(CommonRes)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/GiveCultureArtworkOrder", in, out)
}

func (c *seriesClient) ReceiveCultureArtworkOrder(ctx context.Context, in *ReceiveCultureArtworkOrderReq, opts ...grpc_go.CallOption) (*CommonRes, common.ErrorWithAttachment) {
	out := new(CommonRes)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/ReceiveCultureArtworkOrder", in, out)
}

func (c *seriesClient) RefuseCultureArtworkOrder(ctx context.Context, in *ReceiveCultureArtworkOrderReq, opts ...grpc_go.CallOption) (*CommonRes, common.ErrorWithAttachment) {
	out := new(CommonRes)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/RefuseCultureArtworkOrder", in, out)
}

func (c *seriesClient) OverTimeCirculationList(ctx context.Context, in *CirculationListReq, opts ...grpc_go.CallOption) (*CirculationListResp, common.ErrorWithAttachment) {
	out := new(CirculationListResp)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/OverTimeCirculationList", in, out)
}

func (c *seriesClient) UpdateCastCultureArtworkOrder(ctx context.Context, in *UpdateCastCultureArtworkOrderReq, opts ...grpc_go.CallOption) (*CommonRes, common.ErrorWithAttachment) {
	out := new(CommonRes)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/UpdateCastCultureArtworkOrder", in, out)
}

func (c *seriesClient) UpdateCirculationHash(ctx context.Context, in *UpdateCirculationHashReq, opts ...grpc_go.CallOption) (*CommonRes, common.ErrorWithAttachment) {
	out := new(CommonRes)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/UpdateCirculationHash", in, out)
}

func (c *seriesClient) UpdateOrderStatus(ctx context.Context, in *UpdateOrderStatusReq, opts ...grpc_go.CallOption) (*CommonRes, common.ErrorWithAttachment) {
	out := new(CommonRes)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/UpdateOrderStatus", in, out)
}

// SeriesServer is the server API for Series service.
// All implementations must embed UnimplementedSeriesServer
// for forward compatibility
type SeriesServer interface {
	SaveSeries(context.Context, *SaveSeriesReq) (*SaveSeriesResp, error)
	SaveCulturalArtwork(context.Context, *SeriesCultureArtwork) (*SeriesCultureArtwork, error)
	SeriesDetail(context.Context, *SeriesDetailReq) (*SeriesDetailResp, error)
	UpdateSeriesLang(context.Context, *UpdateSeriesLangReq) (*UpdateSeriesLangResp, error)
	AutoShelf(context.Context, *AutoShelfReq) (*AutoShelfResp, error)
	HandShelf(context.Context, *HandShelfReq) (*HandShelfResp, error)
	SeriesList(context.Context, *SeriesListReq) (*SeriesListResp, error)
	SeriesDel(context.Context, *SeriesDelReq) (*SeriesDelResp, error)
	CheckSeries(context.Context, *CheckSeriesReq) (*CheckSeriesResp, error)
	UpdateSeriesChain(context.Context, *UpdateSeriesChainReq) (*UpdateSeriesChainResp, error)
	GetSeriesLanguageInfo(context.Context, *GetSeriesLanguageInfoReq) (*GetSeriesLanguageInfoRep, error)
	FSeriesList(context.Context, *FSeriesListReq) (*FSeriesListResp, error)
	SeriesDetailByMem(context.Context, *SeriesDetailByMemReq) (*SeriesDetailResp, error)
	UpdateMem(context.Context, *UpdateMemReq) (*CommonRes, error)
	DrawGift(context.Context, *DrawGiftReq) (*CommonRes, error)
	ListSalesArtwork(context.Context, *ListSalesArtworkReq) (*ListSalesArtworkResp, error)
	// 万博相关数据
	CultureArtworkList(context.Context, *CultureArtworkListReq) (*CultureArtworkListResp, error)
	ScanList(context.Context, *ScanListReq) (*ScanListResp, error)
	CirculationList(context.Context, *CirculationListReq) (*CirculationListResp, error)
	CirculationDetail(context.Context, *CirculationDetailReq) (*Circulation, error)
	FansCirculationList(context.Context, *CirculationListReq) (*CirculationListResp, error)
	DrawCultureArtwork(context.Context, *DrawCultureArtworkReq) (*CommonRes, error)
	CultureArtworkDetail(context.Context, *CultureArtworkDetailReq) (*SeriesCultureArtwork, error)
	CultureArtworkOrderList(context.Context, *CultureArtworkOrderListReq) (*CultureArtworkOrderListRes, error)
	CultureArtworkOrderDetail(context.Context, *CultureArtworkOrderDetailReq) (*CultureArtworkOrder, error)
	GiveCultureArtworkOrder(context.Context, *GiveCultureArtworkOrderReq) (*CommonRes, error)
	ReceiveCultureArtworkOrder(context.Context, *ReceiveCultureArtworkOrderReq) (*CommonRes, error)
	RefuseCultureArtworkOrder(context.Context, *ReceiveCultureArtworkOrderReq) (*CommonRes, error)
	OverTimeCirculationList(context.Context, *CirculationListReq) (*CirculationListResp, error)
	UpdateCastCultureArtworkOrder(context.Context, *UpdateCastCultureArtworkOrderReq) (*CommonRes, error)
	UpdateCirculationHash(context.Context, *UpdateCirculationHashReq) (*CommonRes, error)
	// 更新 订单状态
	UpdateOrderStatus(context.Context, *UpdateOrderStatusReq) (*CommonRes, error)
	mustEmbedUnimplementedSeriesServer()
}

// UnimplementedSeriesServer must be embedded to have forward compatible implementations.
type UnimplementedSeriesServer struct {
	proxyImpl protocol.Invoker
}

func (UnimplementedSeriesServer) SaveSeries(context.Context, *SaveSeriesReq) (*SaveSeriesResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SaveSeries not implemented")
}
func (UnimplementedSeriesServer) SaveCulturalArtwork(context.Context, *SeriesCultureArtwork) (*SeriesCultureArtwork, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SaveCulturalArtwork not implemented")
}
func (UnimplementedSeriesServer) SeriesDetail(context.Context, *SeriesDetailReq) (*SeriesDetailResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SeriesDetail not implemented")
}
func (UnimplementedSeriesServer) UpdateSeriesLang(context.Context, *UpdateSeriesLangReq) (*UpdateSeriesLangResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateSeriesLang not implemented")
}
func (UnimplementedSeriesServer) AutoShelf(context.Context, *AutoShelfReq) (*AutoShelfResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AutoShelf not implemented")
}
func (UnimplementedSeriesServer) HandShelf(context.Context, *HandShelfReq) (*HandShelfResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method HandShelf not implemented")
}
func (UnimplementedSeriesServer) SeriesList(context.Context, *SeriesListReq) (*SeriesListResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SeriesList not implemented")
}
func (UnimplementedSeriesServer) SeriesDel(context.Context, *SeriesDelReq) (*SeriesDelResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SeriesDel not implemented")
}
func (UnimplementedSeriesServer) CheckSeries(context.Context, *CheckSeriesReq) (*CheckSeriesResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CheckSeries not implemented")
}
func (UnimplementedSeriesServer) UpdateSeriesChain(context.Context, *UpdateSeriesChainReq) (*UpdateSeriesChainResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateSeriesChain not implemented")
}
func (UnimplementedSeriesServer) GetSeriesLanguageInfo(context.Context, *GetSeriesLanguageInfoReq) (*GetSeriesLanguageInfoRep, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetSeriesLanguageInfo not implemented")
}
func (UnimplementedSeriesServer) FSeriesList(context.Context, *FSeriesListReq) (*FSeriesListResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FSeriesList not implemented")
}
func (UnimplementedSeriesServer) SeriesDetailByMem(context.Context, *SeriesDetailByMemReq) (*SeriesDetailResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SeriesDetailByMem not implemented")
}
func (UnimplementedSeriesServer) UpdateMem(context.Context, *UpdateMemReq) (*CommonRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateMem not implemented")
}
func (UnimplementedSeriesServer) DrawGift(context.Context, *DrawGiftReq) (*CommonRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DrawGift not implemented")
}
func (UnimplementedSeriesServer) ListSalesArtwork(context.Context, *ListSalesArtworkReq) (*ListSalesArtworkResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListSalesArtwork not implemented")
}
func (UnimplementedSeriesServer) CultureArtworkList(context.Context, *CultureArtworkListReq) (*CultureArtworkListResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CultureArtworkList not implemented")
}
func (UnimplementedSeriesServer) ScanList(context.Context, *ScanListReq) (*ScanListResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ScanList not implemented")
}
func (UnimplementedSeriesServer) CirculationList(context.Context, *CirculationListReq) (*CirculationListResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CirculationList not implemented")
}
func (UnimplementedSeriesServer) CirculationDetail(context.Context, *CirculationDetailReq) (*Circulation, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CirculationDetail not implemented")
}
func (UnimplementedSeriesServer) FansCirculationList(context.Context, *CirculationListReq) (*CirculationListResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FansCirculationList not implemented")
}
func (UnimplementedSeriesServer) DrawCultureArtwork(context.Context, *DrawCultureArtworkReq) (*CommonRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DrawCultureArtwork not implemented")
}
func (UnimplementedSeriesServer) CultureArtworkDetail(context.Context, *CultureArtworkDetailReq) (*SeriesCultureArtwork, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CultureArtworkDetail not implemented")
}
func (UnimplementedSeriesServer) CultureArtworkOrderList(context.Context, *CultureArtworkOrderListReq) (*CultureArtworkOrderListRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CultureArtworkOrderList not implemented")
}
func (UnimplementedSeriesServer) CultureArtworkOrderDetail(context.Context, *CultureArtworkOrderDetailReq) (*CultureArtworkOrder, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CultureArtworkOrderDetail not implemented")
}
func (UnimplementedSeriesServer) GiveCultureArtworkOrder(context.Context, *GiveCultureArtworkOrderReq) (*CommonRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GiveCultureArtworkOrder not implemented")
}
func (UnimplementedSeriesServer) ReceiveCultureArtworkOrder(context.Context, *ReceiveCultureArtworkOrderReq) (*CommonRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ReceiveCultureArtworkOrder not implemented")
}
func (UnimplementedSeriesServer) RefuseCultureArtworkOrder(context.Context, *ReceiveCultureArtworkOrderReq) (*CommonRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RefuseCultureArtworkOrder not implemented")
}
func (UnimplementedSeriesServer) OverTimeCirculationList(context.Context, *CirculationListReq) (*CirculationListResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method OverTimeCirculationList not implemented")
}
func (UnimplementedSeriesServer) UpdateCastCultureArtworkOrder(context.Context, *UpdateCastCultureArtworkOrderReq) (*CommonRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateCastCultureArtworkOrder not implemented")
}
func (UnimplementedSeriesServer) UpdateCirculationHash(context.Context, *UpdateCirculationHashReq) (*CommonRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateCirculationHash not implemented")
}
func (UnimplementedSeriesServer) UpdateOrderStatus(context.Context, *UpdateOrderStatusReq) (*CommonRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateOrderStatus not implemented")
}
func (s *UnimplementedSeriesServer) XXX_SetProxyImpl(impl protocol.Invoker) {
	s.proxyImpl = impl
}

func (s *UnimplementedSeriesServer) XXX_GetProxyImpl() protocol.Invoker {
	return s.proxyImpl
}

func (s *UnimplementedSeriesServer) XXX_ServiceDesc() *grpc_go.ServiceDesc {
	return &Series_ServiceDesc
}
func (s *UnimplementedSeriesServer) XXX_InterfaceName() string {
	return "backendSeries.Series"
}

func (UnimplementedSeriesServer) mustEmbedUnimplementedSeriesServer() {}

// UnsafeSeriesServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to SeriesServer will
// result in compilation errors.
type UnsafeSeriesServer interface {
	mustEmbedUnimplementedSeriesServer()
}

func RegisterSeriesServer(s grpc_go.ServiceRegistrar, srv SeriesServer) {
	s.RegisterService(&Series_ServiceDesc, srv)
}

func _Series_SaveSeries_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(SaveSeriesReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("SaveSeries", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Series_SaveCulturalArtwork_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(SeriesCultureArtwork)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("SaveCulturalArtwork", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Series_SeriesDetail_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(SeriesDetailReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("SeriesDetail", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Series_UpdateSeriesLang_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateSeriesLangReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("UpdateSeriesLang", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Series_AutoShelf_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(AutoShelfReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("AutoShelf", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Series_HandShelf_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(HandShelfReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("HandShelf", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Series_SeriesList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(SeriesListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("SeriesList", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Series_SeriesDel_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(SeriesDelReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("SeriesDel", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Series_CheckSeries_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(CheckSeriesReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("CheckSeries", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Series_UpdateSeriesChain_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateSeriesChainReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("UpdateSeriesChain", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Series_GetSeriesLanguageInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetSeriesLanguageInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("GetSeriesLanguageInfo", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Series_FSeriesList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(FSeriesListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("FSeriesList", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Series_SeriesDetailByMem_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(SeriesDetailByMemReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("SeriesDetailByMem", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Series_UpdateMem_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateMemReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("UpdateMem", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Series_DrawGift_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(DrawGiftReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("DrawGift", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Series_ListSalesArtwork_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListSalesArtworkReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("ListSalesArtwork", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Series_CultureArtworkList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(CultureArtworkListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("CultureArtworkList", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Series_ScanList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(ScanListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("ScanList", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Series_CirculationList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(CirculationListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("CirculationList", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Series_CirculationDetail_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(CirculationDetailReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("CirculationDetail", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Series_FansCirculationList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(CirculationListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("FansCirculationList", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Series_DrawCultureArtwork_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(DrawCultureArtworkReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("DrawCultureArtwork", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Series_CultureArtworkDetail_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(CultureArtworkDetailReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("CultureArtworkDetail", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Series_CultureArtworkOrderList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(CultureArtworkOrderListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("CultureArtworkOrderList", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Series_CultureArtworkOrderDetail_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(CultureArtworkOrderDetailReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("CultureArtworkOrderDetail", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Series_GiveCultureArtworkOrder_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(GiveCultureArtworkOrderReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("GiveCultureArtworkOrder", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Series_ReceiveCultureArtworkOrder_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(ReceiveCultureArtworkOrderReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("ReceiveCultureArtworkOrder", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Series_RefuseCultureArtworkOrder_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(ReceiveCultureArtworkOrderReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("RefuseCultureArtworkOrder", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Series_OverTimeCirculationList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(CirculationListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("OverTimeCirculationList", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Series_UpdateCastCultureArtworkOrder_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateCastCultureArtworkOrderReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("UpdateCastCultureArtworkOrder", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Series_UpdateCirculationHash_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateCirculationHashReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("UpdateCirculationHash", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Series_UpdateOrderStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateOrderStatusReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("UpdateOrderStatus", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

// Series_ServiceDesc is the grpc_go.ServiceDesc for Series service.
// It's only intended for direct use with grpc_go.RegisterService,
// and not to be introspected or modified (even as a copy)
var Series_ServiceDesc = grpc_go.ServiceDesc{
	ServiceName: "backendSeries.Series",
	HandlerType: (*SeriesServer)(nil),
	Methods: []grpc_go.MethodDesc{
		{
			MethodName: "SaveSeries",
			Handler:    _Series_SaveSeries_Handler,
		},
		{
			MethodName: "SaveCulturalArtwork",
			Handler:    _Series_SaveCulturalArtwork_Handler,
		},
		{
			MethodName: "SeriesDetail",
			Handler:    _Series_SeriesDetail_Handler,
		},
		{
			MethodName: "UpdateSeriesLang",
			Handler:    _Series_UpdateSeriesLang_Handler,
		},
		{
			MethodName: "AutoShelf",
			Handler:    _Series_AutoShelf_Handler,
		},
		{
			MethodName: "HandShelf",
			Handler:    _Series_HandShelf_Handler,
		},
		{
			MethodName: "SeriesList",
			Handler:    _Series_SeriesList_Handler,
		},
		{
			MethodName: "SeriesDel",
			Handler:    _Series_SeriesDel_Handler,
		},
		{
			MethodName: "CheckSeries",
			Handler:    _Series_CheckSeries_Handler,
		},
		{
			MethodName: "UpdateSeriesChain",
			Handler:    _Series_UpdateSeriesChain_Handler,
		},
		{
			MethodName: "GetSeriesLanguageInfo",
			Handler:    _Series_GetSeriesLanguageInfo_Handler,
		},
		{
			MethodName: "FSeriesList",
			Handler:    _Series_FSeriesList_Handler,
		},
		{
			MethodName: "SeriesDetailByMem",
			Handler:    _Series_SeriesDetailByMem_Handler,
		},
		{
			MethodName: "UpdateMem",
			Handler:    _Series_UpdateMem_Handler,
		},
		{
			MethodName: "DrawGift",
			Handler:    _Series_DrawGift_Handler,
		},
		{
			MethodName: "ListSalesArtwork",
			Handler:    _Series_ListSalesArtwork_Handler,
		},
		{
			MethodName: "CultureArtworkList",
			Handler:    _Series_CultureArtworkList_Handler,
		},
		{
			MethodName: "ScanList",
			Handler:    _Series_ScanList_Handler,
		},
		{
			MethodName: "CirculationList",
			Handler:    _Series_CirculationList_Handler,
		},
		{
			MethodName: "CirculationDetail",
			Handler:    _Series_CirculationDetail_Handler,
		},
		{
			MethodName: "FansCirculationList",
			Handler:    _Series_FansCirculationList_Handler,
		},
		{
			MethodName: "DrawCultureArtwork",
			Handler:    _Series_DrawCultureArtwork_Handler,
		},
		{
			MethodName: "CultureArtworkDetail",
			Handler:    _Series_CultureArtworkDetail_Handler,
		},
		{
			MethodName: "CultureArtworkOrderList",
			Handler:    _Series_CultureArtworkOrderList_Handler,
		},
		{
			MethodName: "CultureArtworkOrderDetail",
			Handler:    _Series_CultureArtworkOrderDetail_Handler,
		},
		{
			MethodName: "GiveCultureArtworkOrder",
			Handler:    _Series_GiveCultureArtworkOrder_Handler,
		},
		{
			MethodName: "ReceiveCultureArtworkOrder",
			Handler:    _Series_ReceiveCultureArtworkOrder_Handler,
		},
		{
			MethodName: "RefuseCultureArtworkOrder",
			Handler:    _Series_RefuseCultureArtworkOrder_Handler,
		},
		{
			MethodName: "OverTimeCirculationList",
			Handler:    _Series_OverTimeCirculationList_Handler,
		},
		{
			MethodName: "UpdateCastCultureArtworkOrder",
			Handler:    _Series_UpdateCastCultureArtworkOrder_Handler,
		},
		{
			MethodName: "UpdateCirculationHash",
			Handler:    _Series_UpdateCirculationHash_Handler,
		},
		{
			MethodName: "UpdateOrderStatus",
			Handler:    _Series_UpdateOrderStatus_Handler,
		},
	},
	Streams:  []grpc_go.StreamDesc{},
	Metadata: "api/series/series.proto",
}
