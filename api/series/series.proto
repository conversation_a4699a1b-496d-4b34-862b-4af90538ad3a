syntax = "proto3";
package backendSeries;
option go_package = "./;backendSeries";

import "github.com/mwitkow/go-proto-validators@v0.3.2/validator.proto";

service Series {
        rpc SaveSeries (SaveSeriesReq) returns (SaveSeriesResp) {} //更新系列
        rpc SaveCulturalArtwork (SeriesCultureArtwork) returns (SeriesCultureArtwork) {} // 创建文创产品
        rpc SeriesDetail (SeriesDetailReq) returns (SeriesDetailResp) {} //系列详情
        rpc UpdateSeriesLang (UpdateSeriesLangReq) returns (UpdateSeriesLangResp) {} //更新语言数据接口
        rpc AutoShelf (AutoShelfReq) returns (AutoShelfResp) {} //自动上下架
        rpc HandShelf (HandShelfReq) returns (HandShelfResp) {} //手动上下架
        rpc SeriesList (SeriesListReq) returns (SeriesListResp) {} //系列列表
        rpc SeriesDel (SeriesDelReq) returns (SeriesDelResp) {} //系列删除
        rpc CheckSeries (CheckSeriesReq) returns (CheckSeriesResp) {} //检测系列开始
        rpc UpdateSeriesChain (UpdateSeriesChainReq) returns (UpdateSeriesChainResp) {} //
        rpc GetSeriesLanguageInfo (GetSeriesLanguageInfoReq) returns (GetSeriesLanguageInfoRep){} // 获取系列的语言填写状态

        rpc FSeriesList (FSeriesListReq) returns (FSeriesListResp) {} //系列列表
        rpc SeriesDetailByMem (SeriesDetailByMemReq) returns (SeriesDetailResp) {} //系列详情
        rpc UpdateMem (UpdateMemReq) returns (CommonRes) {} //更新 区块链地址，助记词，密码等
        rpc DrawGift (DrawGiftReq) returns (CommonRes) {} //领取系列

        rpc ListSalesArtwork(ListSalesArtworkReq)returns(ListSalesArtworkResp){};//获取销售情况列表

        //万博相关数据
        rpc CultureArtworkList (CultureArtworkListReq) returns (CultureArtworkListResp) {} //系列详情
        rpc ScanList(ScanListReq)returns(ScanListResp){};//扫码列表
        rpc CirculationList(CirculationListReq)returns(CirculationListResp){};//转移数据
        rpc CirculationDetail(CirculationDetailReq)returns(Circulation){};
        rpc FansCirculationList(CirculationListReq)returns(CirculationListResp){};//转移数据
        rpc DrawCultureArtwork(DrawCultureArtworkReq)returns(CommonRes){};//领取藏品
        rpc CultureArtworkDetail(CultureArtworkDetailReq)returns(SeriesCultureArtwork){};//获取藏品的数据 通过加密信息或者cultureArtworkUUid TODO
        rpc CultureArtworkOrderList(CultureArtworkOrderListReq)returns(CultureArtworkOrderListRes){};//订单列表 TODO
        rpc CultureArtworkOrderDetail(CultureArtworkOrderDetailReq)returns(CultureArtworkOrder){};//订单详情 TODO
        rpc GiveCultureArtworkOrder(GiveCultureArtworkOrderReq)returns(CommonRes){};//转增 TODO
        rpc ReceiveCultureArtworkOrder(ReceiveCultureArtworkOrderReq)returns(CommonRes){};//领取 TODO
        rpc RefuseCultureArtworkOrder(ReceiveCultureArtworkOrderReq)returns(CommonRes){};//拒绝 TODO

        rpc OverTimeCirculationList(CirculationListReq)returns(CirculationListResp){};//转移数据
        rpc UpdateCastCultureArtworkOrder(UpdateCastCultureArtworkOrderReq)returns(CommonRes){};//更新铸造ntf的信息 hash , err,时间等等
        rpc UpdateCirculationHash(UpdateCirculationHashReq)returns(CommonRes){};//更新ntf转移的hash 仅仅对转移(铸造除外)

        // 更新 订单状态
        rpc UpdateOrderStatus(UpdateOrderStatusReq)returns(CommonRes){};//更新订单状态
}

message CirculationDetailReq {
        string uuid = 1 ;
}

message UpdateCirculationHashReq {
   string hash = 1 ;
   string msg = 2 ;
   string uuid = 3 ;
}

message GiveCultureArtworkOrderReq {
        string   orderUuid = 1 ;
        uint32   from = 2 ;
        uint32   to = 3 ;
        string fromName = 4;
        string toName = 5;
}

message UpdateCastCultureArtworkOrderReq {
        string   orderUuid = 1 ;
        string   transactionHash = 2 ;
        string   castMsg = 3 ;
}

message ReceiveCultureArtworkOrderReq {
        string   orderUuid = 1 ;
        bool   isAgree= 2 ;
        uint32   to= 3 ;
        string   toName= 4 ;
}

message DrawCultureArtworkReq {
        string   cultureArtworkUuid = 1 ;
        uint32   indexNum = 2 ;
        uint32   userId = 3 ;
        string   userTelNum = 4 ;
        string   userName = 5 ;
        uint32   payType = 6 ;
}

message CultureItem {
 string          url            = 1;
 int32           stockNum       = 2;
 int32           systockNum     = 3;
 repeated int32  cultureNum     = 4;
 string          modelType      = 5;
}

message CultureArtworkDetailReq {
        string  uuid = 1 ;
        string  lang = 2 ; //暂时无用，仅仅准备
        uint32  num = 3 ; //序列，看看有没有被买过
}

message ScanListReq {
        string   seriesUuid = 1 ;
        string   seriesCulturalArtworkName = 2 ;
        string   Hash = 3 ;
        string   firstUserTel = 4 ;
        string   startCreatedTime = 5 ;
        string   endCreatedTime = 6 ;
        uint64   page = 7 ;
        uint64   pageSize = 8 ;
}

message CultureArtworkListReq {
        string   seriesUuid = 1 ;
        uint64   page = 7 ;
        uint64   pageSize = 8 ;
}


message ScanListResp {
        repeated ScanInfo list = 1;
        int64 count = 2;
}

message ScanInfo {
        string   uuid = 1 ;
        string   seriesUuid = 2 ;
        string   seriesCulturalArtworkName = 3 ;
        string   hash = 4 ;
        string   firstUserTel = 5 ;
        string   createdAt = 6;//扫码时间
}

message CultureArtworkOrderListReq {
        uint32  userId = 1 ;
        uint64  page = 2 ;
        uint64  pageSize = 3 ;
        bool    isEmptyHash = 4 ;//order 那边的hash是空的
        uint32  status = 5 ;//order 那边的hash是空的
}

message CirculationListReq {
        string  orderUuid = 1 ;
        uint64   page = 2 ;
        uint64   pageSize = 3 ;
        uint32  userId = 4 ;
}

message CultureArtworkOrderDetailReq {
        string  orderUuid = 1 ;
        uint32  userId = 2 ;
}

message CirculationListResp {
        repeated Circulation list = 1;
        int64 count = 2;
}

message Circulation {
        string   actionType = 1 ; //状态
        string   tradeTime = 2 ;
        string   receiverName = 3 ;//接收的的人
        string   uuid = 4 ;
        string   hash = 5 ; //当前流转hash
        string   giverName = 6 ;//赠送的人
        string   receiveTime = 7 ;//赠送的人
        string   transferTime = 8 ;//
        string   name = 9 ;//
        string   briefImg = 10 ;//
        uint32 senderId=11;
        uint32 receiverId=12;
}

message LanguageInfo {
        string   lang = 1 ;
        int32   status = 3 ;
}

// SaveSeries
message SaveSeriesReq{
   string brandId = 1 ;
   string brandName = 2 ;
   string coverImg = 5 [(validator.field) = {string_not_empty: true,human_error:"系列图片不能为空"}];
   string seriesName = 6 [(validator.field) = {string_not_empty: true,human_error:"系列名不能为空"}];
   string desc = 8 ;
   string lang = 9 ;
   string seriesUuid = 10 ;
   string actionCode = 12 ;
   repeated SeriesArtworkData artworks=13;
   AuctionRequest auction = 14 ;
   string auctionUuid = 15 ;
   string link = 22;
   repeated SeriesCultureArtwork seriesCultureArtworks = 23;
   string currency = 16;
}

message SeriesCultureArtwork{
     string   uuid = 1;
     string   seriesUuid =2;
     string   name = 3;
     string   cultureNum = 4;
     string   briefImage = 5;
     string   size = 6;
     string   detail = 7;
     string   mainType = 8;
     repeated CultureItem mainImgInfo = 9;
     int32   id = 10;
     uint32 stockNum=11;
     string hash=12;
     string lang =13;
     string price = 14;
     uint32 leftNum = 15;
     uint32 soldNum = 16;
     string currency = 17;
     uint32 isReceived = 18;//当num请求不为空的时候生效，是否被领取 0-num没传递， 1-被领取 2-没领取
     CultureItem cultureItem = 19;
     string  brandAddress = 20;
     string   modelType = 21;
}

message AuctionRequest {
  string address = 1;
  string image = 5;
  string info = 6;
  string startDate = 7;
  string title = 8;
  string startTitle = 16;
  string lang = 18;
  /*上边是多语言*/

  string createdAt = 2;
  uint32 deletedAt = 3;
  uint32 ID = 4;
  string updatedAt = 9;
  string uuid = 10;
  uint32 totalNum = 11;
  uint32 isLiving = 13;
  bool isExistLivingArtwork = 15; //是否存在正在拍卖的作品
  string operatorName = 17;
}


message SaveSeriesResp{
        string msg = 1 ;
        string uuid = 2 ;
}
// SaveSeries

// SeriesDetail
message SeriesDetailReq{
        string          seriesUuid = 1 [(validator.field) = {string_not_empty: true,human_error:"系列ID不能为空"}];
        int32           num = 2;
        string          lang = 4;
        repeated string          langs = 5;
        uint32           companyId = 6;
}

message CultureArtworkListResp{
        repeated SeriesCultureArtwork list = 1;
        int64 count = 2;
}

message CultureArtworkOrderListRes{
        repeated CultureArtworkOrder list = 1;
        int64 count = 2;
}

message CultureArtworkOrder {
        string   uuid = 1 ;
        string   seriesUuid = 2 ;
        string   name = 3 ;
        string   hash = 4 ;
        string   telNum = 5 ;
        string   createdAt = 6;
        string   briefImg = 7;
        uint32   status = 8;
        uint32   baseStatus = 9; //1-无标志 2-转赠中 3-接受中
        uint32 nowUserId = 18;
        string nowUserName = 19;
        string  transferTime = 10;
        string  tradeTime = 11;
        string  receiveTime = 12;
        string  brandName = 13;
        string  seriesCulturalArtworkUuid = 14;
        uint32  indexNum = 15;
        string  brandAddress = 16;
        CultureItem cultureItem = 17;
        string   modelType = 21;
        uint32 payStatus = 22;
        string payTime = 23;
        repeated string payPprofList = 24;
        string currency = 25;
        string payPrice = 26;
        uint32 operatorId = 27;
        string operatorTelNum = 28;
        string sessionId = 29;
        string remark = 30;
        uint32 payType = 31;
}

message SeriesDetailResp{
        message SeriesLangInfo{
                string seriesUuid = 1;
                string seriesName = 3;
                string desc = 4;
                int32  id = 6;
                int32  lang = 7;
                string coverImg = 8;
        }
        string brandId = 1 ;
        string brandName = 2;
        string launchStartTime = 3;
        string launchEndTime = 4;
        int32  shelfState = 5;//1=上架 2=下架
        string actionTime = 6;
        string coverImg = 7;
        string createTime = 8;
        string seriesUuid = 9;
        repeated SeriesLangInfo seriesLang = 10;
        string msg = 11;
        string actionCode = 12;
        int32  status = 13;
        string seriesName = 14;
        string desc = 15;
        string lang = 16;
        string onShelfTime = 18;
        int32  autoShelf=19; //自动上下架 1=禁用 2=启用
        repeated SeriesArtworkData artworks = 20;
        string auctionUuid = 21;
        string link = 22;
        AuctionRequest auction = 23 ;
        repeated SeriesCultureArtwork seriesCultureArtworks = 24;
        string  currency = 25;
}
// SeriesDetail

// UpdateSeriesLang
message UpdateSeriesLangReq{
        string seriesUuid = 1 [(validator.field) = {string_not_empty: true,human_error:"系列ID不能为空"}];
        string seriesName = 3;
        string desc = 4;
        string lang = 5;
        int32  langId = 6;
}

message UpdateSeriesLangResp{
        string msg = 1 ;
}
// UpdateSeriesLang

//AutoShelfReq
message AutoShelfReq{
        string seriesUuid = 1 [(validator.field) = {string_not_empty: true,human_error:"系列ID不能为空"}];
        string launchStartTime = 3 ;
        string launchEndTime = 4 ;
}

message AutoShelfResp{
        string msg = 1 ;
}

message HandShelfReq{
        string  seriesUuid = 1 [(validator.field) = {string_not_empty: true,human_error:"系列ID不能为空"}];
        int32   shelfState = 2 ;//1=上架 2=下架
}
message HandShelfResp{
        string seriesUuid = 1 ;
        string brandUuid = 2 ;
        string msg = 3 ;
}
//AutoShelfReq

//SeriesList
message SeriesListReq{
        string keyword                = 1 ;
        int32  page                    = 2 ;
        int32  pageSize                = 3 ;
        string lang                = 4 ;
        repeated string langs = 5 ;
}

message SeriesListResp{
        message Info{
                string seriesUuid = 1 ;
                string seriesName = 2 ;
                string coverImg = 3 ;
                string brandName = 4 ;
                string createTime = 5 ;
                int32  shelfState = 6 ;//1=上架 2=下架
                string launchStartTime = 7 ;
                string launchEndTime = 8 ;
                string actionTime = 9 ;
                string onShelfTime = 10 ;
                string actionCode = 12 ;
                int32  status = 13 ;
                int32  autoShelf=14; //自动上下架 1=禁用 2=启用
        }
        repeated Info data  =   1;
        int32  count                   = 2 ;
        int32  systemTime                   = 3 ;
        string msg                = 4 ;
}
//SeriesList

//SeriesDelReq
message SeriesDelReq{
        string seriesUuid = 1 [(validator.field) = {string_not_empty: true,human_error:"系列ID不能为空"}];
}
message SeriesDelResp{
        string msg = 1 ;
}
//SeriesDelResp

message CheckSeriesReq{

}

message CheckSeriesResp{
        message Info{
                string seriesUuid = 1 ;
                string brandId = 2 ;
        }
        repeated Info data = 1 ;
}
// UpdateSeriesChain
message UpdateSeriesChainReq{
        string seriesUuid = 1 [(validator.field) = {string_not_empty: true,human_error:"系列ID不能为空"}];
        uint32 chainStatus= 2 ;
}

message UpdateSeriesChainResp{
        string msg = 1 ;
}

message GetSeriesLanguageInfoReq {
        string          SeriesUuid = 1 ;
        repeated        LanguageInfo languageInfo = 2 ;
}

message GetSeriesLanguageInfoRep {
        repeated LanguageInfo languageInfo = 1;
        string msg = 2 ;
}


message DrawGiftReq{
        string seriesUuid     = 1 ;
}

message UpdateMemReq{
        string seriesUuid     = 1 ;
        string seriesAddress  = 2 ;
        string seriesMem      = 3 ;
        string seriesPwd      = 4 ;
        uint32 seriesIsGiftDraw     = 5 ;// 1-领取 2-未领取
        string seriesPublicKey = 6 ;
        map<string,string>  collectionMap = 7;
}

message CollectionM{
        string seriesUuid     = 1 ;
        string seriesAddress  = 2 ;
        string seriesMem      = 3 ;
        string seriesPwd      = 4 ;
        uint32 seriesIsGiftDraw     = 5 ;// 1-领取 2-未领取
        string seriesPublicKey = 6 ;
}


message CommonRes{
        string uuid = 1 ;
}

//SeriesList
message FSeriesListReq{
        int32 Page                    = 1 [json_name = "page"];
        int32 PageSize                = 2 [json_name = "page_size"];
        string  lang = 5 ;
        repeated string langs = 6 ;
}

message FSeriesListResp{
        message Info{
                string seriesUuid = 1 ;
                string seriesName = 2 ;
                string coverImg = 3 ;
                string desc = 4 ;
                string startTime = 5 ;
                string endTime = 6 ;
                string brandId = 7 ;
                string brandName = 8 ;
                string brandImg = 9 ;
                string actionCode = 10 ;
        }
        repeated Info data  =   1 ;
        int32   count                   = 2 ;
        string  msg                = 3 ;
}

message SeriesDetailByMemReq{
        string mem = 1 ;
        string lang = 2 ;
}

message SeriesArtworkData {
        uint32 ID = 1;
        string createdAt = 2;
        string updatedAt = 3;
        int64 deletedAt = 4;
        int64 artworkId = 5;
        string artworkUid = 6;
        string tfnum = 7;
        string artworkName = 8;
        string hdPic = 9;
        int32 ruler = 10;
        int32 length = 11;
        int32 width = 12;
        string artistName = 13;
        string artistUid = 14;
        string abstract = 15;
        string tnum=16;
        repeated SeriesArtworkLangData lang=17;
        string price = 18;
        string currency=19;
        uint32 companyId=20;
        string companyName =21;
}

message SeriesArtworkLangData {
        uint32 ID = 1;
        string createdAt = 2;
        string updatedAt = 3;
        int64 deletedAt = 4;
        string lang = 5;
        int64 seriesArtworkId = 6;
        string artworkUid = 7;
        string artworkName = 8;
        string artistName = 9;
        string abstract = 10;
        string hdPic=11;
        int32 ruler=12;
        int32 length=13;
        int32 width=14;
}
message ListSalesArtworkReq{
        string seriesUid =1;
        int64 page=2;
        int64 pageSize=3;
}
message SalesArtwork{
        string lotNo=1;
        string artworkUid=2;
        string artworkName=3;
        string hdPic=4;
        string price=5;
        string currency=6;
        string payStatus=7;
        string payTime=8;
        uint32 userId=9;
        string userName=10;
        string telNum=11;//这个通过客户端从账号服务获取
        string AuctionBuyUuid=12;
        string AuctionArtworkUuid=13;
        string payUid=14;
}
message ListSalesArtworkResp{
        repeated SalesArtwork data=1;
        int64 page=2;
        int64 pageSize=3;
        int64 Total=4;
}

message UpdateOrderStatusReq{
        string orderUuid = 1;
        int32  payStatus = 2;
        string remark = 3;
        repeated string payPprofList = 4;
        string payTime = 5;
        uint32 operatorId = 6;
        string operatorTelNum = 7;
        string sessionId = 8;
        uint32 payType = 9;
}