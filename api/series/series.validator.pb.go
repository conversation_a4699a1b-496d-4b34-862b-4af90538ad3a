// Code generated by protoc-gen-gogo. DO NOT EDIT.
// source: api/series/series.proto

package backendSeries

import (
	fmt "fmt"
	math "math"
	proto "github.com/golang/protobuf/proto"
	_ "github.com/mwitkow/go-proto-validators"
	github_com_mwitkow_go_proto_validators "github.com/mwitkow/go-proto-validators"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

func (this *CirculationDetailReq) Validate() error {
	return nil
}
func (this *UpdateCirculationHashReq) Validate() error {
	return nil
}
func (this *GiveCultureArtworkOrderReq) Validate() error {
	return nil
}
func (this *UpdateCastCultureArtworkOrderReq) Validate() error {
	return nil
}
func (this *ReceiveCultureArtworkOrderReq) Validate() error {
	return nil
}
func (this *DrawCultureArtworkReq) Validate() error {
	return nil
}
func (this *CultureItem) Validate() error {
	return nil
}
func (this *CultureArtworkDetailReq) Validate() error {
	return nil
}
func (this *ScanListReq) Validate() error {
	return nil
}
func (this *CultureArtworkListReq) Validate() error {
	return nil
}
func (this *ScanListResp) Validate() error {
	for _, item := range this.List {
		if item != nil {
			if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(item); err != nil {
				return github_com_mwitkow_go_proto_validators.FieldError("List", err)
			}
		}
	}
	return nil
}
func (this *ScanInfo) Validate() error {
	return nil
}
func (this *CultureArtworkOrderListReq) Validate() error {
	return nil
}
func (this *CirculationListReq) Validate() error {
	return nil
}
func (this *CultureArtworkOrderDetailReq) Validate() error {
	return nil
}
func (this *CirculationListResp) Validate() error {
	for _, item := range this.List {
		if item != nil {
			if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(item); err != nil {
				return github_com_mwitkow_go_proto_validators.FieldError("List", err)
			}
		}
	}
	return nil
}
func (this *Circulation) Validate() error {
	return nil
}
func (this *LanguageInfo) Validate() error {
	return nil
}
func (this *SaveSeriesReq) Validate() error {
	if this.CoverImg == "" {
		return github_com_mwitkow_go_proto_validators.FieldError("CoverImg", fmt.Errorf(`系列图片不能为空`))
	}
	if this.SeriesName == "" {
		return github_com_mwitkow_go_proto_validators.FieldError("SeriesName", fmt.Errorf(`系列名不能为空`))
	}
	for _, item := range this.Artworks {
		if item != nil {
			if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(item); err != nil {
				return github_com_mwitkow_go_proto_validators.FieldError("Artworks", err)
			}
		}
	}
	if this.Auction != nil {
		if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(this.Auction); err != nil {
			return github_com_mwitkow_go_proto_validators.FieldError("Auction", err)
		}
	}
	for _, item := range this.SeriesCultureArtworks {
		if item != nil {
			if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(item); err != nil {
				return github_com_mwitkow_go_proto_validators.FieldError("SeriesCultureArtworks", err)
			}
		}
	}
	return nil
}
func (this *SeriesCultureArtwork) Validate() error {
	for _, item := range this.MainImgInfo {
		if item != nil {
			if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(item); err != nil {
				return github_com_mwitkow_go_proto_validators.FieldError("MainImgInfo", err)
			}
		}
	}
	if this.CultureItem != nil {
		if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(this.CultureItem); err != nil {
			return github_com_mwitkow_go_proto_validators.FieldError("CultureItem", err)
		}
	}
	return nil
}
func (this *AuctionRequest) Validate() error {
	return nil
}
func (this *SaveSeriesResp) Validate() error {
	return nil
}
func (this *SeriesDetailReq) Validate() error {
	if this.SeriesUuid == "" {
		return github_com_mwitkow_go_proto_validators.FieldError("SeriesUuid", fmt.Errorf(`系列ID不能为空`))
	}
	return nil
}
func (this *CultureArtworkListResp) Validate() error {
	for _, item := range this.List {
		if item != nil {
			if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(item); err != nil {
				return github_com_mwitkow_go_proto_validators.FieldError("List", err)
			}
		}
	}
	return nil
}
func (this *CultureArtworkOrderListRes) Validate() error {
	for _, item := range this.List {
		if item != nil {
			if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(item); err != nil {
				return github_com_mwitkow_go_proto_validators.FieldError("List", err)
			}
		}
	}
	return nil
}
func (this *CultureArtworkOrder) Validate() error {
	if this.CultureItem != nil {
		if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(this.CultureItem); err != nil {
			return github_com_mwitkow_go_proto_validators.FieldError("CultureItem", err)
		}
	}
	return nil
}
func (this *SeriesDetailResp) Validate() error {
	for _, item := range this.SeriesLang {
		if item != nil {
			if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(item); err != nil {
				return github_com_mwitkow_go_proto_validators.FieldError("SeriesLang", err)
			}
		}
	}
	for _, item := range this.Artworks {
		if item != nil {
			if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(item); err != nil {
				return github_com_mwitkow_go_proto_validators.FieldError("Artworks", err)
			}
		}
	}
	if this.Auction != nil {
		if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(this.Auction); err != nil {
			return github_com_mwitkow_go_proto_validators.FieldError("Auction", err)
		}
	}
	for _, item := range this.SeriesCultureArtworks {
		if item != nil {
			if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(item); err != nil {
				return github_com_mwitkow_go_proto_validators.FieldError("SeriesCultureArtworks", err)
			}
		}
	}
	return nil
}
func (this *SeriesDetailResp_SeriesLangInfo) Validate() error {
	return nil
}
func (this *UpdateSeriesLangReq) Validate() error {
	if this.SeriesUuid == "" {
		return github_com_mwitkow_go_proto_validators.FieldError("SeriesUuid", fmt.Errorf(`系列ID不能为空`))
	}
	return nil
}
func (this *UpdateSeriesLangResp) Validate() error {
	return nil
}
func (this *AutoShelfReq) Validate() error {
	if this.SeriesUuid == "" {
		return github_com_mwitkow_go_proto_validators.FieldError("SeriesUuid", fmt.Errorf(`系列ID不能为空`))
	}
	return nil
}
func (this *AutoShelfResp) Validate() error {
	return nil
}
func (this *HandShelfReq) Validate() error {
	if this.SeriesUuid == "" {
		return github_com_mwitkow_go_proto_validators.FieldError("SeriesUuid", fmt.Errorf(`系列ID不能为空`))
	}
	return nil
}
func (this *HandShelfResp) Validate() error {
	return nil
}
func (this *SeriesListReq) Validate() error {
	return nil
}
func (this *SeriesListResp) Validate() error {
	for _, item := range this.Data {
		if item != nil {
			if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(item); err != nil {
				return github_com_mwitkow_go_proto_validators.FieldError("Data", err)
			}
		}
	}
	return nil
}
func (this *SeriesListResp_Info) Validate() error {
	return nil
}
func (this *SeriesDelReq) Validate() error {
	if this.SeriesUuid == "" {
		return github_com_mwitkow_go_proto_validators.FieldError("SeriesUuid", fmt.Errorf(`系列ID不能为空`))
	}
	return nil
}
func (this *SeriesDelResp) Validate() error {
	return nil
}
func (this *CheckSeriesReq) Validate() error {
	return nil
}
func (this *CheckSeriesResp) Validate() error {
	for _, item := range this.Data {
		if item != nil {
			if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(item); err != nil {
				return github_com_mwitkow_go_proto_validators.FieldError("Data", err)
			}
		}
	}
	return nil
}
func (this *CheckSeriesResp_Info) Validate() error {
	return nil
}
func (this *UpdateSeriesChainReq) Validate() error {
	if this.SeriesUuid == "" {
		return github_com_mwitkow_go_proto_validators.FieldError("SeriesUuid", fmt.Errorf(`系列ID不能为空`))
	}
	return nil
}
func (this *UpdateSeriesChainResp) Validate() error {
	return nil
}
func (this *GetSeriesLanguageInfoReq) Validate() error {
	for _, item := range this.LanguageInfo {
		if item != nil {
			if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(item); err != nil {
				return github_com_mwitkow_go_proto_validators.FieldError("LanguageInfo", err)
			}
		}
	}
	return nil
}
func (this *GetSeriesLanguageInfoRep) Validate() error {
	for _, item := range this.LanguageInfo {
		if item != nil {
			if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(item); err != nil {
				return github_com_mwitkow_go_proto_validators.FieldError("LanguageInfo", err)
			}
		}
	}
	return nil
}
func (this *DrawGiftReq) Validate() error {
	return nil
}
func (this *UpdateMemReq) Validate() error {
	// Validation of proto3 map<> fields is unsupported.
	return nil
}
func (this *CollectionM) Validate() error {
	return nil
}
func (this *CommonRes) Validate() error {
	return nil
}
func (this *FSeriesListReq) Validate() error {
	return nil
}
func (this *FSeriesListResp) Validate() error {
	for _, item := range this.Data {
		if item != nil {
			if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(item); err != nil {
				return github_com_mwitkow_go_proto_validators.FieldError("Data", err)
			}
		}
	}
	return nil
}
func (this *FSeriesListResp_Info) Validate() error {
	return nil
}
func (this *SeriesDetailByMemReq) Validate() error {
	return nil
}
func (this *SeriesArtworkData) Validate() error {
	for _, item := range this.Lang {
		if item != nil {
			if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(item); err != nil {
				return github_com_mwitkow_go_proto_validators.FieldError("Lang", err)
			}
		}
	}
	return nil
}
func (this *SeriesArtworkLangData) Validate() error {
	return nil
}
func (this *ListSalesArtworkReq) Validate() error {
	return nil
}
func (this *SalesArtwork) Validate() error {
	return nil
}
func (this *ListSalesArtworkResp) Validate() error {
	for _, item := range this.Data {
		if item != nil {
			if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(item); err != nil {
				return github_com_mwitkow_go_proto_validators.FieldError("Data", err)
			}
		}
	}
	return nil
}
func (this *UpdateOrderStatusReq) Validate() error {
	return nil
}
