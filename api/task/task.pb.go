// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.24.0--rc1
// source: api/task/task.proto

package task

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type ModelRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ModelName   string `protobuf:"bytes,1,opt,name=modelName,proto3" json:"modelName"`
	SdModelHash string `protobuf:"bytes,2,opt,name=sdModelHash,proto3" json:"sdModelHash"`
	Title       string `protobuf:"bytes,3,opt,name=Title,proto3" json:"Title"`
}

func (x *ModelRequest) Reset() {
	*x = ModelRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_task_task_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ModelRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ModelRequest) ProtoMessage() {}

func (x *ModelRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_task_task_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ModelRequest.ProtoReflect.Descriptor instead.
func (*ModelRequest) Descriptor() ([]byte, []int) {
	return file_api_task_task_proto_rawDescGZIP(), []int{0}
}

func (x *ModelRequest) GetModelName() string {
	if x != nil {
		return x.ModelName
	}
	return ""
}

func (x *ModelRequest) GetSdModelHash() string {
	if x != nil {
		return x.SdModelHash
	}
	return ""
}

func (x *ModelRequest) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

type ModelsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Count uint32   `protobuf:"varint,1,opt,name=count,proto3" json:"count"`
	List  []*Model `protobuf:"bytes,2,rep,name=list,proto3" json:"list"`
}

func (x *ModelsResponse) Reset() {
	*x = ModelsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_task_task_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ModelsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ModelsResponse) ProtoMessage() {}

func (x *ModelsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_task_task_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ModelsResponse.ProtoReflect.Descriptor instead.
func (*ModelsResponse) Descriptor() ([]byte, []int) {
	return file_api_task_task_proto_rawDescGZIP(), []int{1}
}

func (x *ModelsResponse) GetCount() uint32 {
	if x != nil {
		return x.Count
	}
	return 0
}

func (x *ModelsResponse) GetList() []*Model {
	if x != nil {
		return x.List
	}
	return nil
}

type ModelSet struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	FileName string  `protobuf:"bytes,1,opt,name=FileName,proto3" json:"FileName"`
	Name     string  `protobuf:"bytes,2,opt,name=Name,proto3" json:"Name"`
	Url      string  `protobuf:"bytes,3,opt,name=Url,proto3" json:"Url"`
	Image    string  `protobuf:"bytes,4,opt,name=Image,proto3" json:"Image"`
	Head     string  `protobuf:"bytes,11,opt,name=Head,proto3" json:"Head"`
	Tag      string  `protobuf:"bytes,5,opt,name=Tag,proto3" json:"Tag"`
	Author   string  `protobuf:"bytes,6,opt,name=Author,proto3" json:"Author"`
	Cfg      float32 `protobuf:"fixed32,7,opt,name=Cfg,proto3" json:"Cfg"`
	Steps    int32   `protobuf:"varint,8,opt,name=Steps,proto3" json:"Steps"`
	Sampler  string  `protobuf:"bytes,9,opt,name=Sampler,proto3" json:"Sampler"`
	ClipSkip int32   `protobuf:"varint,10,opt,name=ClipSkip,proto3" json:"ClipSkip"`
}

func (x *ModelSet) Reset() {
	*x = ModelSet{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_task_task_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ModelSet) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ModelSet) ProtoMessage() {}

func (x *ModelSet) ProtoReflect() protoreflect.Message {
	mi := &file_api_task_task_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ModelSet.ProtoReflect.Descriptor instead.
func (*ModelSet) Descriptor() ([]byte, []int) {
	return file_api_task_task_proto_rawDescGZIP(), []int{2}
}

func (x *ModelSet) GetFileName() string {
	if x != nil {
		return x.FileName
	}
	return ""
}

func (x *ModelSet) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *ModelSet) GetUrl() string {
	if x != nil {
		return x.Url
	}
	return ""
}

func (x *ModelSet) GetImage() string {
	if x != nil {
		return x.Image
	}
	return ""
}

func (x *ModelSet) GetHead() string {
	if x != nil {
		return x.Head
	}
	return ""
}

func (x *ModelSet) GetTag() string {
	if x != nil {
		return x.Tag
	}
	return ""
}

func (x *ModelSet) GetAuthor() string {
	if x != nil {
		return x.Author
	}
	return ""
}

func (x *ModelSet) GetCfg() float32 {
	if x != nil {
		return x.Cfg
	}
	return 0
}

func (x *ModelSet) GetSteps() int32 {
	if x != nil {
		return x.Steps
	}
	return 0
}

func (x *ModelSet) GetSampler() string {
	if x != nil {
		return x.Sampler
	}
	return ""
}

func (x *ModelSet) GetClipSkip() int32 {
	if x != nil {
		return x.ClipSkip
	}
	return 0
}

type Model struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Title            string    `protobuf:"bytes,2,opt,name=title,proto3" json:"title"`
	SubTitle         string    `protobuf:"bytes,3,opt,name=subTitle,proto3" json:"subTitle"`
	ModelName        string    `protobuf:"bytes,4,opt,name=modelName,proto3" json:"modelName"`
	Hash             string    `protobuf:"bytes,5,opt,name=hash,proto3" json:"hash"`
	Sha256           string    `protobuf:"bytes,6,opt,name=sha256,proto3" json:"sha256"`
	ReplaceAuthor    string    `protobuf:"bytes,7,opt,name=replaceAuthor,proto3" json:"replaceAuthor"`
	ReplaceAuthorImg string    `protobuf:"bytes,8,opt,name=replaceAuthorImg,proto3" json:"replaceAuthorImg"`
	ReplaceImg       string    `protobuf:"bytes,9,opt,name=replaceImg,proto3" json:"replaceImg"`
	ReplaceTag       []string  `protobuf:"bytes,10,rep,name=replaceTag,proto3" json:"replaceTag"`
	ModelSet         *ModelSet `protobuf:"bytes,11,opt,name=ModelSet,proto3" json:"ModelSet"`
	Index            uint32    `protobuf:"varint,12,opt,name=index,proto3" json:"index"`
	SqImage          string    `protobuf:"bytes,13,opt,name=sqImage,proto3" json:"sqImage"`
}

func (x *Model) Reset() {
	*x = Model{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_task_task_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Model) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Model) ProtoMessage() {}

func (x *Model) ProtoReflect() protoreflect.Message {
	mi := &file_api_task_task_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Model.ProtoReflect.Descriptor instead.
func (*Model) Descriptor() ([]byte, []int) {
	return file_api_task_task_proto_rawDescGZIP(), []int{3}
}

func (x *Model) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

func (x *Model) GetSubTitle() string {
	if x != nil {
		return x.SubTitle
	}
	return ""
}

func (x *Model) GetModelName() string {
	if x != nil {
		return x.ModelName
	}
	return ""
}

func (x *Model) GetHash() string {
	if x != nil {
		return x.Hash
	}
	return ""
}

func (x *Model) GetSha256() string {
	if x != nil {
		return x.Sha256
	}
	return ""
}

func (x *Model) GetReplaceAuthor() string {
	if x != nil {
		return x.ReplaceAuthor
	}
	return ""
}

func (x *Model) GetReplaceAuthorImg() string {
	if x != nil {
		return x.ReplaceAuthorImg
	}
	return ""
}

func (x *Model) GetReplaceImg() string {
	if x != nil {
		return x.ReplaceImg
	}
	return ""
}

func (x *Model) GetReplaceTag() []string {
	if x != nil {
		return x.ReplaceTag
	}
	return nil
}

func (x *Model) GetModelSet() *ModelSet {
	if x != nil {
		return x.ModelSet
	}
	return nil
}

func (x *Model) GetIndex() uint32 {
	if x != nil {
		return x.Index
	}
	return 0
}

func (x *Model) GetSqImage() string {
	if x != nil {
		return x.SqImage
	}
	return ""
}

type ModelsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Page     uint32 `protobuf:"varint,1,opt,name=page,proto3" json:"page"`
	PageSize uint32 `protobuf:"varint,2,opt,name=pageSize,proto3" json:"pageSize"`
}

func (x *ModelsRequest) Reset() {
	*x = ModelsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_task_task_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ModelsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ModelsRequest) ProtoMessage() {}

func (x *ModelsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_task_task_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ModelsRequest.ProtoReflect.Descriptor instead.
func (*ModelsRequest) Descriptor() ([]byte, []int) {
	return file_api_task_task_proto_rawDescGZIP(), []int{4}
}

func (x *ModelsRequest) GetPage() uint32 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *ModelsRequest) GetPageSize() uint32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

type EmptyRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *EmptyRequest) Reset() {
	*x = EmptyRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_task_task_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EmptyRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EmptyRequest) ProtoMessage() {}

func (x *EmptyRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_task_task_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EmptyRequest.ProtoReflect.Descriptor instead.
func (*EmptyRequest) Descriptor() ([]byte, []int) {
	return file_api_task_task_proto_rawDescGZIP(), []int{5}
}

type DelRecordImagesRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ids    []uint32 `protobuf:"varint,1,rep,packed,name=ids,proto3" json:"ids"`
	UserId uint32   `protobuf:"varint,2,opt,name=userId,proto3" json:"userId"`
}

func (x *DelRecordImagesRequest) Reset() {
	*x = DelRecordImagesRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_task_task_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DelRecordImagesRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DelRecordImagesRequest) ProtoMessage() {}

func (x *DelRecordImagesRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_task_task_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DelRecordImagesRequest.ProtoReflect.Descriptor instead.
func (*DelRecordImagesRequest) Descriptor() ([]byte, []int) {
	return file_api_task_task_proto_rawDescGZIP(), []int{6}
}

func (x *DelRecordImagesRequest) GetIds() []uint32 {
	if x != nil {
		return x.Ids
	}
	return nil
}

func (x *DelRecordImagesRequest) GetUserId() uint32 {
	if x != nil {
		return x.UserId
	}
	return 0
}

type CommonResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ID       uint32 `protobuf:"varint,1,opt,name=ID,proto3" json:"ID"`
	OrderNum string `protobuf:"bytes,2,opt,name=orderNum,proto3" json:"orderNum"`
}

func (x *CommonResponse) Reset() {
	*x = CommonResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_task_task_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CommonResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CommonResponse) ProtoMessage() {}

func (x *CommonResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_task_task_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CommonResponse.ProtoReflect.Descriptor instead.
func (*CommonResponse) Descriptor() ([]byte, []int) {
	return file_api_task_task_proto_rawDescGZIP(), []int{7}
}

func (x *CommonResponse) GetID() uint32 {
	if x != nil {
		return x.ID
	}
	return 0
}

func (x *CommonResponse) GetOrderNum() string {
	if x != nil {
		return x.OrderNum
	}
	return ""
}

type BatchRecordImgRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UserId             uint32   `protobuf:"varint,1,opt,name=userId,proto3" json:"userId"`
	Images             []string `protobuf:"bytes,2,rep,name=images,proto3" json:"images"`
	ModelName          string   `protobuf:"bytes,3,opt,name=modelName,proto3" json:"modelName"`
	ModelId            string   `protobuf:"bytes,4,opt,name=modelId,proto3" json:"modelId"`
	UserName           string   `protobuf:"bytes,5,opt,name=userName,proto3" json:"userName"`
	UserAvatar         string   `protobuf:"bytes,6,opt,name=userAvatar,proto3" json:"userAvatar"`
	Domain             string   `protobuf:"bytes,7,opt,name=domain,proto3" json:"domain"`
	Prompt             string   `protobuf:"bytes,8,opt,name=prompt,proto3" json:"prompt"`
	NegativePrompt     string   `protobuf:"bytes,9,opt,name=negativePrompt,proto3" json:"negativePrompt"`
	Width              uint32   `protobuf:"varint,10,opt,name=width,proto3" json:"width"`
	Height             uint32   `protobuf:"varint,11,opt,name=height,proto3" json:"height"`
	Type               string   `protobuf:"bytes,12,opt,name=type,proto3" json:"type"`             //"ai"或者plus
	InitImages         []string `protobuf:"bytes,13,rep,name=initImages,proto3" json:"initImages"` //"ai"或者plus
	RealPrompt         string   `protobuf:"bytes,14,opt,name=realPrompt,proto3" json:"realPrompt"`
	RealNegativePrompt string   `protobuf:"bytes,15,opt,name=realNegativePrompt,proto3" json:"realNegativePrompt"`
}

func (x *BatchRecordImgRequest) Reset() {
	*x = BatchRecordImgRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_task_task_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BatchRecordImgRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BatchRecordImgRequest) ProtoMessage() {}

func (x *BatchRecordImgRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_task_task_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BatchRecordImgRequest.ProtoReflect.Descriptor instead.
func (*BatchRecordImgRequest) Descriptor() ([]byte, []int) {
	return file_api_task_task_proto_rawDescGZIP(), []int{8}
}

func (x *BatchRecordImgRequest) GetUserId() uint32 {
	if x != nil {
		return x.UserId
	}
	return 0
}

func (x *BatchRecordImgRequest) GetImages() []string {
	if x != nil {
		return x.Images
	}
	return nil
}

func (x *BatchRecordImgRequest) GetModelName() string {
	if x != nil {
		return x.ModelName
	}
	return ""
}

func (x *BatchRecordImgRequest) GetModelId() string {
	if x != nil {
		return x.ModelId
	}
	return ""
}

func (x *BatchRecordImgRequest) GetUserName() string {
	if x != nil {
		return x.UserName
	}
	return ""
}

func (x *BatchRecordImgRequest) GetUserAvatar() string {
	if x != nil {
		return x.UserAvatar
	}
	return ""
}

func (x *BatchRecordImgRequest) GetDomain() string {
	if x != nil {
		return x.Domain
	}
	return ""
}

func (x *BatchRecordImgRequest) GetPrompt() string {
	if x != nil {
		return x.Prompt
	}
	return ""
}

func (x *BatchRecordImgRequest) GetNegativePrompt() string {
	if x != nil {
		return x.NegativePrompt
	}
	return ""
}

func (x *BatchRecordImgRequest) GetWidth() uint32 {
	if x != nil {
		return x.Width
	}
	return 0
}

func (x *BatchRecordImgRequest) GetHeight() uint32 {
	if x != nil {
		return x.Height
	}
	return 0
}

func (x *BatchRecordImgRequest) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

func (x *BatchRecordImgRequest) GetInitImages() []string {
	if x != nil {
		return x.InitImages
	}
	return nil
}

func (x *BatchRecordImgRequest) GetRealPrompt() string {
	if x != nil {
		return x.RealPrompt
	}
	return ""
}

func (x *BatchRecordImgRequest) GetRealNegativePrompt() string {
	if x != nil {
		return x.RealNegativePrompt
	}
	return ""
}

type RecordImgRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UserId             uint32   `protobuf:"varint,1,opt,name=userId,proto3" json:"userId"`
	Image              string   `protobuf:"bytes,2,opt,name=image,proto3" json:"image"`
	ModelName          string   `protobuf:"bytes,3,opt,name=modelName,proto3" json:"modelName"`
	ModelId            string   `protobuf:"bytes,4,opt,name=modelId,proto3" json:"modelId"`
	UserName           string   `protobuf:"bytes,5,opt,name=userName,proto3" json:"userName"`
	UserAvatar         string   `protobuf:"bytes,6,opt,name=userAvatar,proto3" json:"userAvatar"`
	BatchId            uint32   `protobuf:"varint,7,opt,name=batchId,proto3" json:"batchId"`
	IndexNum           uint32   `protobuf:"varint,8,opt,name=indexNum,proto3" json:"indexNum"`
	CreatedAt          string   `protobuf:"bytes,9,opt,name=createdAt,proto3" json:"createdAt"`
	Prompt             string   `protobuf:"bytes,10,opt,name=prompt,proto3" json:"prompt"`
	NegativePrompt     string   `protobuf:"bytes,11,opt,name=negativePrompt,proto3" json:"negativePrompt"`
	Width              uint32   `protobuf:"varint,12,opt,name=width,proto3" json:"width"`
	Height             uint32   `protobuf:"varint,13,opt,name=height,proto3" json:"height"`
	OrderNum           string   `protobuf:"bytes,14,opt,name=orderNum,proto3" json:"orderNum"`
	Type               string   `protobuf:"bytes,15,opt,name=type,proto3" json:"type"`             //"ai"或者plus
	InitImages         []string `protobuf:"bytes,16,rep,name=initImages,proto3" json:"initImages"` //"ai"或者plus
	ID                 uint32   `protobuf:"varint,17,opt,name=ID,proto3" json:"ID"`                //"ai"或者plus
	RealPrompt         string   `protobuf:"bytes,18,opt,name=realPrompt,proto3" json:"realPrompt"`
	RealNegativePrompt string   `protobuf:"bytes,19,opt,name=realNegativePrompt,proto3" json:"realNegativePrompt"`
}

func (x *RecordImgRequest) Reset() {
	*x = RecordImgRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_task_task_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RecordImgRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RecordImgRequest) ProtoMessage() {}

func (x *RecordImgRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_task_task_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RecordImgRequest.ProtoReflect.Descriptor instead.
func (*RecordImgRequest) Descriptor() ([]byte, []int) {
	return file_api_task_task_proto_rawDescGZIP(), []int{9}
}

func (x *RecordImgRequest) GetUserId() uint32 {
	if x != nil {
		return x.UserId
	}
	return 0
}

func (x *RecordImgRequest) GetImage() string {
	if x != nil {
		return x.Image
	}
	return ""
}

func (x *RecordImgRequest) GetModelName() string {
	if x != nil {
		return x.ModelName
	}
	return ""
}

func (x *RecordImgRequest) GetModelId() string {
	if x != nil {
		return x.ModelId
	}
	return ""
}

func (x *RecordImgRequest) GetUserName() string {
	if x != nil {
		return x.UserName
	}
	return ""
}

func (x *RecordImgRequest) GetUserAvatar() string {
	if x != nil {
		return x.UserAvatar
	}
	return ""
}

func (x *RecordImgRequest) GetBatchId() uint32 {
	if x != nil {
		return x.BatchId
	}
	return 0
}

func (x *RecordImgRequest) GetIndexNum() uint32 {
	if x != nil {
		return x.IndexNum
	}
	return 0
}

func (x *RecordImgRequest) GetCreatedAt() string {
	if x != nil {
		return x.CreatedAt
	}
	return ""
}

func (x *RecordImgRequest) GetPrompt() string {
	if x != nil {
		return x.Prompt
	}
	return ""
}

func (x *RecordImgRequest) GetNegativePrompt() string {
	if x != nil {
		return x.NegativePrompt
	}
	return ""
}

func (x *RecordImgRequest) GetWidth() uint32 {
	if x != nil {
		return x.Width
	}
	return 0
}

func (x *RecordImgRequest) GetHeight() uint32 {
	if x != nil {
		return x.Height
	}
	return 0
}

func (x *RecordImgRequest) GetOrderNum() string {
	if x != nil {
		return x.OrderNum
	}
	return ""
}

func (x *RecordImgRequest) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

func (x *RecordImgRequest) GetInitImages() []string {
	if x != nil {
		return x.InitImages
	}
	return nil
}

func (x *RecordImgRequest) GetID() uint32 {
	if x != nil {
		return x.ID
	}
	return 0
}

func (x *RecordImgRequest) GetRealPrompt() string {
	if x != nil {
		return x.RealPrompt
	}
	return ""
}

func (x *RecordImgRequest) GetRealNegativePrompt() string {
	if x != nil {
		return x.RealNegativePrompt
	}
	return ""
}

type ImageListRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UserId         uint32   `protobuf:"varint,1,opt,name=userId,proto3" json:"userId"`
	Image          string   `protobuf:"bytes,2,opt,name=image,proto3" json:"image"`
	ModelName      string   `protobuf:"bytes,3,opt,name=modelName,proto3" json:"modelName"`
	ModelId        string   `protobuf:"bytes,4,opt,name=modelId,proto3" json:"modelId"`
	Domain         string   `protobuf:"bytes,5,opt,name=domain,proto3" json:"domain"`
	CreatedAtStart string   `protobuf:"bytes,6,opt,name=createdAtStart,proto3" json:"createdAtStart"`
	CreatedAtEnd   string   `protobuf:"bytes,7,opt,name=createdAtEnd,proto3" json:"createdAtEnd"`
	Page           uint32   `protobuf:"varint,8,opt,name=page,proto3" json:"page"`
	PageSize       uint32   `protobuf:"varint,9,opt,name=pageSize,proto3" json:"pageSize"`
	Ids            []uint32 `protobuf:"varint,10,rep,packed,name=ids,proto3" json:"ids"`
	Type           string   `protobuf:"bytes,11,opt,name=type,proto3" json:"type"`
}

func (x *ImageListRequest) Reset() {
	*x = ImageListRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_task_task_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ImageListRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ImageListRequest) ProtoMessage() {}

func (x *ImageListRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_task_task_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ImageListRequest.ProtoReflect.Descriptor instead.
func (*ImageListRequest) Descriptor() ([]byte, []int) {
	return file_api_task_task_proto_rawDescGZIP(), []int{10}
}

func (x *ImageListRequest) GetUserId() uint32 {
	if x != nil {
		return x.UserId
	}
	return 0
}

func (x *ImageListRequest) GetImage() string {
	if x != nil {
		return x.Image
	}
	return ""
}

func (x *ImageListRequest) GetModelName() string {
	if x != nil {
		return x.ModelName
	}
	return ""
}

func (x *ImageListRequest) GetModelId() string {
	if x != nil {
		return x.ModelId
	}
	return ""
}

func (x *ImageListRequest) GetDomain() string {
	if x != nil {
		return x.Domain
	}
	return ""
}

func (x *ImageListRequest) GetCreatedAtStart() string {
	if x != nil {
		return x.CreatedAtStart
	}
	return ""
}

func (x *ImageListRequest) GetCreatedAtEnd() string {
	if x != nil {
		return x.CreatedAtEnd
	}
	return ""
}

func (x *ImageListRequest) GetPage() uint32 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *ImageListRequest) GetPageSize() uint32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *ImageListRequest) GetIds() []uint32 {
	if x != nil {
		return x.Ids
	}
	return nil
}

func (x *ImageListRequest) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

type ImageListResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Count uint32              `protobuf:"varint,1,opt,name=count,proto3" json:"count"`
	List  []*RecordImgRequest `protobuf:"bytes,2,rep,name=list,proto3" json:"list"`
}

func (x *ImageListResponse) Reset() {
	*x = ImageListResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_task_task_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ImageListResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ImageListResponse) ProtoMessage() {}

func (x *ImageListResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_task_task_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ImageListResponse.ProtoReflect.Descriptor instead.
func (*ImageListResponse) Descriptor() ([]byte, []int) {
	return file_api_task_task_proto_rawDescGZIP(), []int{11}
}

func (x *ImageListResponse) GetCount() uint32 {
	if x != nil {
		return x.Count
	}
	return 0
}

func (x *ImageListResponse) GetList() []*RecordImgRequest {
	if x != nil {
		return x.List
	}
	return nil
}

type SetStatusRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ID     uint64 `protobuf:"varint,1,opt,name=ID,proto3" json:"ID"`
	Status uint64 `protobuf:"varint,2,opt,name=Status,json=status,proto3" json:"Status"`
}

func (x *SetStatusRequest) Reset() {
	*x = SetStatusRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_task_task_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SetStatusRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SetStatusRequest) ProtoMessage() {}

func (x *SetStatusRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_task_task_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SetStatusRequest.ProtoReflect.Descriptor instead.
func (*SetStatusRequest) Descriptor() ([]byte, []int) {
	return file_api_task_task_proto_rawDescGZIP(), []int{12}
}

func (x *SetStatusRequest) GetID() uint64 {
	if x != nil {
		return x.ID
	}
	return 0
}

func (x *SetStatusRequest) GetStatus() uint64 {
	if x != nil {
		return x.Status
	}
	return 0
}

type TaskListResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Count uint64         `protobuf:"varint,1,opt,name=Count,json=count,proto3" json:"Count"`
	Data  []*TaskRequest `protobuf:"bytes,2,rep,name=Data,json=data,proto3" json:"Data"`
}

func (x *TaskListResponse) Reset() {
	*x = TaskListResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_task_task_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TaskListResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TaskListResponse) ProtoMessage() {}

func (x *TaskListResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_task_task_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TaskListResponse.ProtoReflect.Descriptor instead.
func (*TaskListResponse) Descriptor() ([]byte, []int) {
	return file_api_task_task_proto_rawDescGZIP(), []int{13}
}

func (x *TaskListResponse) GetCount() uint64 {
	if x != nil {
		return x.Count
	}
	return 0
}

func (x *TaskListResponse) GetData() []*TaskRequest {
	if x != nil {
		return x.Data
	}
	return nil
}

type DataInfoRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	SubmitterID  uint64   `protobuf:"varint,1,opt,name=SubmitterID,json=submitterID,proto3" json:"SubmitterID"`
	SubmitterIDs []uint64 `protobuf:"varint,4,rep,packed,name=SubmitterIDs,json=submitterIDs,proto3" json:"SubmitterIDs"`
	Domain       string   `protobuf:"bytes,2,opt,name=Domain,json=domain,proto3" json:"Domain"`
	Type         string   `protobuf:"bytes,3,opt,name=Type,json=type,proto3" json:"Type"`
}

func (x *DataInfoRequest) Reset() {
	*x = DataInfoRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_task_task_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DataInfoRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DataInfoRequest) ProtoMessage() {}

func (x *DataInfoRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_task_task_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DataInfoRequest.ProtoReflect.Descriptor instead.
func (*DataInfoRequest) Descriptor() ([]byte, []int) {
	return file_api_task_task_proto_rawDescGZIP(), []int{14}
}

func (x *DataInfoRequest) GetSubmitterID() uint64 {
	if x != nil {
		return x.SubmitterID
	}
	return 0
}

func (x *DataInfoRequest) GetSubmitterIDs() []uint64 {
	if x != nil {
		return x.SubmitterIDs
	}
	return nil
}

func (x *DataInfoRequest) GetDomain() string {
	if x != nil {
		return x.Domain
	}
	return ""
}

func (x *DataInfoRequest) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

type DataInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	NearCount     uint64 `protobuf:"varint,1,opt,name=NearCount,json=nearCount,proto3" json:"NearCount"`    //即将开始 明天开始
	DoingCount    uint64 `protobuf:"varint,2,opt,name=DoingCount,json=doingCount,proto3" json:"DoingCount"` //进行中
	DoneCount     uint64 `protobuf:"varint,3,opt,name=DoneCount,json=doneCount,proto3" json:"DoneCount"`    //已经完成
	OverCount     uint64 `protobuf:"varint,4,opt,name=OverCount,json=overCount,proto3" json:"OverCount"`    //超时
	TotalCount    uint64 `protobuf:"varint,5,opt,name=TotalCount,json=totalCount,proto3" json:"TotalCount"` //总
	NotStartCount uint64 `protobuf:"varint,6,opt,name=NotStartCount,json=notStartCount,proto3" json:"NotStartCount"`
	NearEndCount  uint64 `protobuf:"varint,7,opt,name=NearEndCount,json=nearStartCount,proto3" json:"NearEndCount"` //即将超时,当天的
}

func (x *DataInfo) Reset() {
	*x = DataInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_task_task_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DataInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DataInfo) ProtoMessage() {}

func (x *DataInfo) ProtoReflect() protoreflect.Message {
	mi := &file_api_task_task_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DataInfo.ProtoReflect.Descriptor instead.
func (*DataInfo) Descriptor() ([]byte, []int) {
	return file_api_task_task_proto_rawDescGZIP(), []int{15}
}

func (x *DataInfo) GetNearCount() uint64 {
	if x != nil {
		return x.NearCount
	}
	return 0
}

func (x *DataInfo) GetDoingCount() uint64 {
	if x != nil {
		return x.DoingCount
	}
	return 0
}

func (x *DataInfo) GetDoneCount() uint64 {
	if x != nil {
		return x.DoneCount
	}
	return 0
}

func (x *DataInfo) GetOverCount() uint64 {
	if x != nil {
		return x.OverCount
	}
	return 0
}

func (x *DataInfo) GetTotalCount() uint64 {
	if x != nil {
		return x.TotalCount
	}
	return 0
}

func (x *DataInfo) GetNotStartCount() uint64 {
	if x != nil {
		return x.NotStartCount
	}
	return 0
}

func (x *DataInfo) GetNearEndCount() uint64 {
	if x != nil {
		return x.NearEndCount
	}
	return 0
}

type DataInfoResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	MySubmitInfo *DataInfo `protobuf:"bytes,1,opt,name=MySubmitInfo,json=mySubmitInfo,proto3" json:"MySubmitInfo"` // 我提交的;
	DomainInfo   *DataInfo `protobuf:"bytes,2,opt,name=DomainInfo,json=domainInfo,proto3" json:"DomainInfo"`       // 全局的;
}

func (x *DataInfoResponse) Reset() {
	*x = DataInfoResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_task_task_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DataInfoResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DataInfoResponse) ProtoMessage() {}

func (x *DataInfoResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_task_task_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DataInfoResponse.ProtoReflect.Descriptor instead.
func (*DataInfoResponse) Descriptor() ([]byte, []int) {
	return file_api_task_task_proto_rawDescGZIP(), []int{16}
}

func (x *DataInfoResponse) GetMySubmitInfo() *DataInfo {
	if x != nil {
		return x.MySubmitInfo
	}
	return nil
}

func (x *DataInfoResponse) GetDomainInfo() *DataInfo {
	if x != nil {
		return x.DomainInfo
	}
	return nil
}

type PassArtWorkRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ArtworkId uint64 `protobuf:"varint,1,opt,name=ArtworkId,json=artworkId,proto3" json:"ArtworkId"`
}

func (x *PassArtWorkRequest) Reset() {
	*x = PassArtWorkRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_task_task_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PassArtWorkRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PassArtWorkRequest) ProtoMessage() {}

func (x *PassArtWorkRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_task_task_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PassArtWorkRequest.ProtoReflect.Descriptor instead.
func (*PassArtWorkRequest) Descriptor() ([]byte, []int) {
	return file_api_task_task_proto_rawDescGZIP(), []int{17}
}

func (x *PassArtWorkRequest) GetArtworkId() uint64 {
	if x != nil {
		return x.ArtworkId
	}
	return 0
}

type JoinUser struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UserID uint64 `protobuf:"varint,1,opt,name=UserID,json=userID,proto3" json:"UserID"`
	Name   string `protobuf:"bytes,2,opt,name=Name,json=name,proto3" json:"Name"`
	Status uint64 `protobuf:"varint,3,opt,name=Status,json=status,proto3" json:"Status"`
}

func (x *JoinUser) Reset() {
	*x = JoinUser{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_task_task_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *JoinUser) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*JoinUser) ProtoMessage() {}

func (x *JoinUser) ProtoReflect() protoreflect.Message {
	mi := &file_api_task_task_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use JoinUser.ProtoReflect.Descriptor instead.
func (*JoinUser) Descriptor() ([]byte, []int) {
	return file_api_task_task_proto_rawDescGZIP(), []int{18}
}

func (x *JoinUser) GetUserID() uint64 {
	if x != nil {
		return x.UserID
	}
	return 0
}

func (x *JoinUser) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *JoinUser) GetStatus() uint64 {
	if x != nil {
		return x.Status
	}
	return 0
}

type Artwork struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ArtworkID       uint64 `protobuf:"varint,1,opt,name=ArtworkID,json=artworkID,proto3" json:"ArtworkID"`
	Name            string `protobuf:"bytes,2,opt,name=Name,json=name,proto3" json:"Name"`
	Status          uint64 `protobuf:"varint,3,opt,name=Status,json=status,proto3" json:"Status"`
	ArtistName      string `protobuf:"bytes,4,opt,name=ArtistName,proto3" json:"ArtistName"`
	ArtistID        string `protobuf:"bytes,5,opt,name=ArtistID,proto3" json:"ArtistID"`
	ArtistShowCount string `protobuf:"bytes,6,opt,name=artistShowCount,proto3" json:"artistShowCount"`
}

func (x *Artwork) Reset() {
	*x = Artwork{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_task_task_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Artwork) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Artwork) ProtoMessage() {}

func (x *Artwork) ProtoReflect() protoreflect.Message {
	mi := &file_api_task_task_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Artwork.ProtoReflect.Descriptor instead.
func (*Artwork) Descriptor() ([]byte, []int) {
	return file_api_task_task_proto_rawDescGZIP(), []int{19}
}

func (x *Artwork) GetArtworkID() uint64 {
	if x != nil {
		return x.ArtworkID
	}
	return 0
}

func (x *Artwork) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *Artwork) GetStatus() uint64 {
	if x != nil {
		return x.Status
	}
	return 0
}

func (x *Artwork) GetArtistName() string {
	if x != nil {
		return x.ArtistName
	}
	return ""
}

func (x *Artwork) GetArtistID() string {
	if x != nil {
		return x.ArtistID
	}
	return ""
}

func (x *Artwork) GetArtistShowCount() string {
	if x != nil {
		return x.ArtistShowCount
	}
	return ""
}

type TaskRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ID            uint64                 `protobuf:"varint,1,opt,name=ID,proto3" json:"ID"`
	Domain        string                 `protobuf:"bytes,2,opt,name=Domain,json=domain,proto3" json:"Domain"`
	CreatedAt     string                 `protobuf:"bytes,4,opt,name=CreatedAt,json=createdAt,proto3" json:"CreatedAt"`
	UpdatedAt     string                 `protobuf:"bytes,5,opt,name=UpdatedAt,json=updatedAt,proto3" json:"UpdatedAt"`
	SubmitterID   uint64                 `protobuf:"varint,6,opt,name=SubmitterID,json=submitterID,proto3" json:"SubmitterID"`
	SubmitterName string                 `protobuf:"bytes,7,opt,name=SubmitterName,json=submitterName,proto3" json:"SubmitterName"`
	Status        uint64                 `protobuf:"varint,8,opt,name=Status,json=status,proto3" json:"Status"`
	JoinUsers     []*JoinUser            `protobuf:"bytes,9,rep,name=JoinUsers,json=joinUsers,proto3" json:"JoinUsers"`
	Artworks      []*Artwork             `protobuf:"bytes,18,rep,name=Artworks,json=artWorks,proto3" json:"Artworks"`
	StartAt       string                 `protobuf:"bytes,10,opt,name=StartAt,json=startAt,proto3" json:"StartAt"`
	EndAt         string                 `protobuf:"bytes,11,opt,name=EndAt,json=endAt,proto3" json:"EndAt"`
	Type          string                 `protobuf:"bytes,12,opt,name=Type,json=type,proto3" json:"Type"`
	Content       string                 `protobuf:"bytes,13,opt,name=Content,json=content,proto3" json:"Content"`
	TaskWorkflows []*TaskWorkflowRequest `protobuf:"bytes,17,rep,name=TaskWorkflows,json=taskWorkflow,proto3" json:"TaskWorkflows"`
	OperateTime   string                 `protobuf:"bytes,19,opt,name=OperateTime,json=operateTime,proto3" json:"OperateTime"`
}

func (x *TaskRequest) Reset() {
	*x = TaskRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_task_task_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TaskRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TaskRequest) ProtoMessage() {}

func (x *TaskRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_task_task_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TaskRequest.ProtoReflect.Descriptor instead.
func (*TaskRequest) Descriptor() ([]byte, []int) {
	return file_api_task_task_proto_rawDescGZIP(), []int{20}
}

func (x *TaskRequest) GetID() uint64 {
	if x != nil {
		return x.ID
	}
	return 0
}

func (x *TaskRequest) GetDomain() string {
	if x != nil {
		return x.Domain
	}
	return ""
}

func (x *TaskRequest) GetCreatedAt() string {
	if x != nil {
		return x.CreatedAt
	}
	return ""
}

func (x *TaskRequest) GetUpdatedAt() string {
	if x != nil {
		return x.UpdatedAt
	}
	return ""
}

func (x *TaskRequest) GetSubmitterID() uint64 {
	if x != nil {
		return x.SubmitterID
	}
	return 0
}

func (x *TaskRequest) GetSubmitterName() string {
	if x != nil {
		return x.SubmitterName
	}
	return ""
}

func (x *TaskRequest) GetStatus() uint64 {
	if x != nil {
		return x.Status
	}
	return 0
}

func (x *TaskRequest) GetJoinUsers() []*JoinUser {
	if x != nil {
		return x.JoinUsers
	}
	return nil
}

func (x *TaskRequest) GetArtworks() []*Artwork {
	if x != nil {
		return x.Artworks
	}
	return nil
}

func (x *TaskRequest) GetStartAt() string {
	if x != nil {
		return x.StartAt
	}
	return ""
}

func (x *TaskRequest) GetEndAt() string {
	if x != nil {
		return x.EndAt
	}
	return ""
}

func (x *TaskRequest) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

func (x *TaskRequest) GetContent() string {
	if x != nil {
		return x.Content
	}
	return ""
}

func (x *TaskRequest) GetTaskWorkflows() []*TaskWorkflowRequest {
	if x != nil {
		return x.TaskWorkflows
	}
	return nil
}

func (x *TaskRequest) GetOperateTime() string {
	if x != nil {
		return x.OperateTime
	}
	return ""
}

type TaskDetail struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ID     uint64 `protobuf:"varint,1,opt,name=ID,proto3" json:"ID"`
	Domain string `protobuf:"bytes,2,opt,name=Domain,json=domain,proto3" json:"Domain"`
}

func (x *TaskDetail) Reset() {
	*x = TaskDetail{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_task_task_proto_msgTypes[21]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TaskDetail) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TaskDetail) ProtoMessage() {}

func (x *TaskDetail) ProtoReflect() protoreflect.Message {
	mi := &file_api_task_task_proto_msgTypes[21]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TaskDetail.ProtoReflect.Descriptor instead.
func (*TaskDetail) Descriptor() ([]byte, []int) {
	return file_api_task_task_proto_rawDescGZIP(), []int{21}
}

func (x *TaskDetail) GetID() uint64 {
	if x != nil {
		return x.ID
	}
	return 0
}

func (x *TaskDetail) GetDomain() string {
	if x != nil {
		return x.Domain
	}
	return ""
}

type TaskResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ID      uint64 `protobuf:"varint,1,opt,name=ID,proto3" json:"ID"`
	Success bool   `protobuf:"varint,2,opt,name=Success,json=success,proto3" json:"Success"`
}

func (x *TaskResponse) Reset() {
	*x = TaskResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_task_task_proto_msgTypes[22]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TaskResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TaskResponse) ProtoMessage() {}

func (x *TaskResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_task_task_proto_msgTypes[22]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TaskResponse.ProtoReflect.Descriptor instead.
func (*TaskResponse) Descriptor() ([]byte, []int) {
	return file_api_task_task_proto_rawDescGZIP(), []int{22}
}

func (x *TaskResponse) GetID() uint64 {
	if x != nil {
		return x.ID
	}
	return 0
}

func (x *TaskResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

type TaskRemove struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Success bool `protobuf:"varint,1,opt,name=Success,json=success,proto3" json:"Success"`
}

func (x *TaskRemove) Reset() {
	*x = TaskRemove{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_task_task_proto_msgTypes[23]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TaskRemove) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TaskRemove) ProtoMessage() {}

func (x *TaskRemove) ProtoReflect() protoreflect.Message {
	mi := &file_api_task_task_proto_msgTypes[23]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TaskRemove.ProtoReflect.Descriptor instead.
func (*TaskRemove) Descriptor() ([]byte, []int) {
	return file_api_task_task_proto_rawDescGZIP(), []int{23}
}

func (x *TaskRemove) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

type TaskList struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Page         uint64   `protobuf:"varint,1,opt,name=Page,json=page,proto3" json:"Page"`
	PageSize     uint64   `protobuf:"varint,2,opt,name=PageSize,json=pageSize,proto3" json:"PageSize"`
	Domain       string   `protobuf:"bytes,3,opt,name=Domain,json=domain,proto3" json:"Domain"`
	SubmitterID  uint64   `protobuf:"varint,4,opt,name=SubmitterID,json=submitterID,proto3" json:"SubmitterID"`           //
	Status       uint64   `protobuf:"varint,5,opt,name=Status,json=status,proto3" json:"Status"`                          //0-全部 1-未完成(进行中) 2-已经完成 3-超时
	Type         string   `protobuf:"bytes,6,opt,name=Type,json=type,proto3" json:"Type"`                                 //类型
	SubmitterIDs []uint64 `protobuf:"varint,7,rep,packed,name=SubmitterIDs,json=submitterIDs,proto3" json:"SubmitterIDs"` //
	ArtistName   string   `protobuf:"bytes,8,opt,name=ArtistName,json=artistName,proto3" json:"ArtistName"`               //画家名称
	ArtworkName  string   `protobuf:"bytes,9,opt,name=ArtworkName,json=artworkName,proto3" json:"ArtworkName"`            //画作名称
	JoinName     string   `protobuf:"bytes,10,opt,name=JoinName,json=joinName,proto3" json:"JoinName"`                    //参与人员
}

func (x *TaskList) Reset() {
	*x = TaskList{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_task_task_proto_msgTypes[24]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TaskList) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TaskList) ProtoMessage() {}

func (x *TaskList) ProtoReflect() protoreflect.Message {
	mi := &file_api_task_task_proto_msgTypes[24]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TaskList.ProtoReflect.Descriptor instead.
func (*TaskList) Descriptor() ([]byte, []int) {
	return file_api_task_task_proto_rawDescGZIP(), []int{24}
}

func (x *TaskList) GetPage() uint64 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *TaskList) GetPageSize() uint64 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *TaskList) GetDomain() string {
	if x != nil {
		return x.Domain
	}
	return ""
}

func (x *TaskList) GetSubmitterID() uint64 {
	if x != nil {
		return x.SubmitterID
	}
	return 0
}

func (x *TaskList) GetStatus() uint64 {
	if x != nil {
		return x.Status
	}
	return 0
}

func (x *TaskList) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

func (x *TaskList) GetSubmitterIDs() []uint64 {
	if x != nil {
		return x.SubmitterIDs
	}
	return nil
}

func (x *TaskList) GetArtistName() string {
	if x != nil {
		return x.ArtistName
	}
	return ""
}

func (x *TaskList) GetArtworkName() string {
	if x != nil {
		return x.ArtworkName
	}
	return ""
}

func (x *TaskList) GetJoinName() string {
	if x != nil {
		return x.JoinName
	}
	return ""
}

type TaskWorkflowListResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Count uint64                 `protobuf:"varint,1,opt,name=Count,json=count,proto3" json:"Count"`
	Data  []*TaskWorkflowRequest `protobuf:"bytes,2,rep,name=Data,json=data,proto3" json:"Data"`
}

func (x *TaskWorkflowListResponse) Reset() {
	*x = TaskWorkflowListResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_task_task_proto_msgTypes[25]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TaskWorkflowListResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TaskWorkflowListResponse) ProtoMessage() {}

func (x *TaskWorkflowListResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_task_task_proto_msgTypes[25]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TaskWorkflowListResponse.ProtoReflect.Descriptor instead.
func (*TaskWorkflowListResponse) Descriptor() ([]byte, []int) {
	return file_api_task_task_proto_rawDescGZIP(), []int{25}
}

func (x *TaskWorkflowListResponse) GetCount() uint64 {
	if x != nil {
		return x.Count
	}
	return 0
}

func (x *TaskWorkflowListResponse) GetData() []*TaskWorkflowRequest {
	if x != nil {
		return x.Data
	}
	return nil
}

type TaskWorkflowRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ID     uint64 `protobuf:"varint,1,opt,name=ID,proto3" json:"ID"`
	TaskID uint64 `protobuf:"varint,5,opt,name=TaskID,json=taskID,proto3" json:"TaskID"`
	UserID uint64 `protobuf:"varint,6,opt,name=UserID,json=userID,proto3" json:"UserID"`
	Name   string `protobuf:"bytes,7,opt,name=Name,json=name,proto3" json:"Name"`
	Status uint64 `protobuf:"varint,9,opt,name=Status,json=status,proto3" json:"Status"`
}

func (x *TaskWorkflowRequest) Reset() {
	*x = TaskWorkflowRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_task_task_proto_msgTypes[26]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TaskWorkflowRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TaskWorkflowRequest) ProtoMessage() {}

func (x *TaskWorkflowRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_task_task_proto_msgTypes[26]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TaskWorkflowRequest.ProtoReflect.Descriptor instead.
func (*TaskWorkflowRequest) Descriptor() ([]byte, []int) {
	return file_api_task_task_proto_rawDescGZIP(), []int{26}
}

func (x *TaskWorkflowRequest) GetID() uint64 {
	if x != nil {
		return x.ID
	}
	return 0
}

func (x *TaskWorkflowRequest) GetTaskID() uint64 {
	if x != nil {
		return x.TaskID
	}
	return 0
}

func (x *TaskWorkflowRequest) GetUserID() uint64 {
	if x != nil {
		return x.UserID
	}
	return 0
}

func (x *TaskWorkflowRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *TaskWorkflowRequest) GetStatus() uint64 {
	if x != nil {
		return x.Status
	}
	return 0
}

type TaskWorkflowDetail struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ID     uint64 `protobuf:"varint,1,opt,name=ID,proto3" json:"ID"`
	Domain string `protobuf:"bytes,2,opt,name=Domain,json=domain,proto3" json:"Domain"`
}

func (x *TaskWorkflowDetail) Reset() {
	*x = TaskWorkflowDetail{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_task_task_proto_msgTypes[27]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TaskWorkflowDetail) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TaskWorkflowDetail) ProtoMessage() {}

func (x *TaskWorkflowDetail) ProtoReflect() protoreflect.Message {
	mi := &file_api_task_task_proto_msgTypes[27]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TaskWorkflowDetail.ProtoReflect.Descriptor instead.
func (*TaskWorkflowDetail) Descriptor() ([]byte, []int) {
	return file_api_task_task_proto_rawDescGZIP(), []int{27}
}

func (x *TaskWorkflowDetail) GetID() uint64 {
	if x != nil {
		return x.ID
	}
	return 0
}

func (x *TaskWorkflowDetail) GetDomain() string {
	if x != nil {
		return x.Domain
	}
	return ""
}

type TaskWorkflowResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ID      uint64 `protobuf:"varint,1,opt,name=ID,proto3" json:"ID"`
	Success bool   `protobuf:"varint,2,opt,name=Success,json=success,proto3" json:"Success"`
}

func (x *TaskWorkflowResponse) Reset() {
	*x = TaskWorkflowResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_task_task_proto_msgTypes[28]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TaskWorkflowResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TaskWorkflowResponse) ProtoMessage() {}

func (x *TaskWorkflowResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_task_task_proto_msgTypes[28]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TaskWorkflowResponse.ProtoReflect.Descriptor instead.
func (*TaskWorkflowResponse) Descriptor() ([]byte, []int) {
	return file_api_task_task_proto_rawDescGZIP(), []int{28}
}

func (x *TaskWorkflowResponse) GetID() uint64 {
	if x != nil {
		return x.ID
	}
	return 0
}

func (x *TaskWorkflowResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

type TaskWorkflowRemove struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Success bool `protobuf:"varint,1,opt,name=Success,json=success,proto3" json:"Success"`
}

func (x *TaskWorkflowRemove) Reset() {
	*x = TaskWorkflowRemove{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_task_task_proto_msgTypes[29]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TaskWorkflowRemove) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TaskWorkflowRemove) ProtoMessage() {}

func (x *TaskWorkflowRemove) ProtoReflect() protoreflect.Message {
	mi := &file_api_task_task_proto_msgTypes[29]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TaskWorkflowRemove.ProtoReflect.Descriptor instead.
func (*TaskWorkflowRemove) Descriptor() ([]byte, []int) {
	return file_api_task_task_proto_rawDescGZIP(), []int{29}
}

func (x *TaskWorkflowRemove) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

type TaskWorkflowList struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Page     uint64 `protobuf:"varint,1,opt,name=Page,json=page,proto3" json:"Page"`
	PageSize uint64 `protobuf:"varint,2,opt,name=PageSize,json=pageSize,proto3" json:"PageSize"`
}

func (x *TaskWorkflowList) Reset() {
	*x = TaskWorkflowList{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_task_task_proto_msgTypes[30]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TaskWorkflowList) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TaskWorkflowList) ProtoMessage() {}

func (x *TaskWorkflowList) ProtoReflect() protoreflect.Message {
	mi := &file_api_task_task_proto_msgTypes[30]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TaskWorkflowList.ProtoReflect.Descriptor instead.
func (*TaskWorkflowList) Descriptor() ([]byte, []int) {
	return file_api_task_task_proto_rawDescGZIP(), []int{30}
}

func (x *TaskWorkflowList) GetPage() uint64 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *TaskWorkflowList) GetPageSize() uint64 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

var File_api_task_task_proto protoreflect.FileDescriptor

var file_api_task_task_proto_rawDesc = []byte{
	0x0a, 0x13, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x61, 0x73, 0x6b, 0x2f, 0x74, 0x61, 0x73, 0x6b, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x04, 0x74, 0x61, 0x73, 0x6b, 0x22, 0x64, 0x0a, 0x0c, 0x4d,
	0x6f, 0x64, 0x65, 0x6c, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1c, 0x0a, 0x09, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x73, 0x64, 0x4d,
	0x6f, 0x64, 0x65, 0x6c, 0x48, 0x61, 0x73, 0x68, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b,
	0x73, 0x64, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x48, 0x61, 0x73, 0x68, 0x12, 0x14, 0x0a, 0x05, 0x54,
	0x69, 0x74, 0x6c, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x54, 0x69, 0x74, 0x6c,
	0x65, 0x22, 0x47, 0x0a, 0x0e, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0d, 0x52, 0x05, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x1f, 0x0a, 0x04, 0x6c, 0x69, 0x73,
	0x74, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x74, 0x61, 0x73, 0x6b, 0x2e, 0x4d,
	0x6f, 0x64, 0x65, 0x6c, 0x52, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x22, 0xfe, 0x01, 0x0a, 0x08, 0x4d,
	0x6f, 0x64, 0x65, 0x6c, 0x53, 0x65, 0x74, 0x12, 0x1a, 0x0a, 0x08, 0x46, 0x69, 0x6c, 0x65, 0x4e,
	0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x46, 0x69, 0x6c, 0x65, 0x4e,
	0x61, 0x6d, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x04, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x55, 0x72, 0x6c, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x55, 0x72, 0x6c, 0x12, 0x14, 0x0a, 0x05, 0x49, 0x6d, 0x61,
	0x67, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x12,
	0x12, 0x0a, 0x04, 0x48, 0x65, 0x61, 0x64, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x48,
	0x65, 0x61, 0x64, 0x12, 0x10, 0x0a, 0x03, 0x54, 0x61, 0x67, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x03, 0x54, 0x61, 0x67, 0x12, 0x16, 0x0a, 0x06, 0x41, 0x75, 0x74, 0x68, 0x6f, 0x72, 0x18,
	0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x41, 0x75, 0x74, 0x68, 0x6f, 0x72, 0x12, 0x10, 0x0a,
	0x03, 0x43, 0x66, 0x67, 0x18, 0x07, 0x20, 0x01, 0x28, 0x02, 0x52, 0x03, 0x43, 0x66, 0x67, 0x12,
	0x14, 0x0a, 0x05, 0x53, 0x74, 0x65, 0x70, 0x73, 0x18, 0x08, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05,
	0x53, 0x74, 0x65, 0x70, 0x73, 0x12, 0x18, 0x0a, 0x07, 0x53, 0x61, 0x6d, 0x70, 0x6c, 0x65, 0x72,
	0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x53, 0x61, 0x6d, 0x70, 0x6c, 0x65, 0x72, 0x12,
	0x1a, 0x0a, 0x08, 0x43, 0x6c, 0x69, 0x70, 0x53, 0x6b, 0x69, 0x70, 0x18, 0x0a, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x08, 0x43, 0x6c, 0x69, 0x70, 0x53, 0x6b, 0x69, 0x70, 0x22, 0xf1, 0x02, 0x0a, 0x05,
	0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x73,
	0x75, 0x62, 0x54, 0x69, 0x74, 0x6c, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x73,
	0x75, 0x62, 0x54, 0x69, 0x74, 0x6c, 0x65, 0x12, 0x1c, 0x0a, 0x09, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x4e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x68, 0x61, 0x73, 0x68, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x04, 0x68, 0x61, 0x73, 0x68, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x68, 0x61,
	0x32, 0x35, 0x36, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x73, 0x68, 0x61, 0x32, 0x35,
	0x36, 0x12, 0x24, 0x0a, 0x0d, 0x72, 0x65, 0x70, 0x6c, 0x61, 0x63, 0x65, 0x41, 0x75, 0x74, 0x68,
	0x6f, 0x72, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x72, 0x65, 0x70, 0x6c, 0x61, 0x63,
	0x65, 0x41, 0x75, 0x74, 0x68, 0x6f, 0x72, 0x12, 0x2a, 0x0a, 0x10, 0x72, 0x65, 0x70, 0x6c, 0x61,
	0x63, 0x65, 0x41, 0x75, 0x74, 0x68, 0x6f, 0x72, 0x49, 0x6d, 0x67, 0x18, 0x08, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x10, 0x72, 0x65, 0x70, 0x6c, 0x61, 0x63, 0x65, 0x41, 0x75, 0x74, 0x68, 0x6f, 0x72,
	0x49, 0x6d, 0x67, 0x12, 0x1e, 0x0a, 0x0a, 0x72, 0x65, 0x70, 0x6c, 0x61, 0x63, 0x65, 0x49, 0x6d,
	0x67, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x72, 0x65, 0x70, 0x6c, 0x61, 0x63, 0x65,
	0x49, 0x6d, 0x67, 0x12, 0x1e, 0x0a, 0x0a, 0x72, 0x65, 0x70, 0x6c, 0x61, 0x63, 0x65, 0x54, 0x61,
	0x67, 0x18, 0x0a, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0a, 0x72, 0x65, 0x70, 0x6c, 0x61, 0x63, 0x65,
	0x54, 0x61, 0x67, 0x12, 0x2a, 0x0a, 0x08, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x53, 0x65, 0x74, 0x18,
	0x0b, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0e, 0x2e, 0x74, 0x61, 0x73, 0x6b, 0x2e, 0x4d, 0x6f, 0x64,
	0x65, 0x6c, 0x53, 0x65, 0x74, 0x52, 0x08, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x53, 0x65, 0x74, 0x12,
	0x14, 0x0a, 0x05, 0x69, 0x6e, 0x64, 0x65, 0x78, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x05,
	0x69, 0x6e, 0x64, 0x65, 0x78, 0x12, 0x18, 0x0a, 0x07, 0x73, 0x71, 0x49, 0x6d, 0x61, 0x67, 0x65,
	0x18, 0x0d, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x73, 0x71, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x22,
	0x3f, 0x0a, 0x0d, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x12, 0x12, 0x0a, 0x04, 0x70, 0x61, 0x67, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x04,
	0x70, 0x61, 0x67, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x61, 0x67, 0x65, 0x53, 0x69, 0x7a, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x08, 0x70, 0x61, 0x67, 0x65, 0x53, 0x69, 0x7a, 0x65,
	0x22, 0x0e, 0x0a, 0x0c, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x22, 0x42, 0x0a, 0x16, 0x44, 0x65, 0x6c, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x49, 0x6d, 0x61,
	0x67, 0x65, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x10, 0x0a, 0x03, 0x69, 0x64,
	0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0d, 0x52, 0x03, 0x69, 0x64, 0x73, 0x12, 0x16, 0x0a, 0x06,
	0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x06, 0x75, 0x73,
	0x65, 0x72, 0x49, 0x64, 0x22, 0x3c, 0x0a, 0x0e, 0x43, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x0e, 0x0a, 0x02, 0x49, 0x44, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0d, 0x52, 0x02, 0x49, 0x44, 0x12, 0x1a, 0x0a, 0x08, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x4e,
	0x75, 0x6d, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x4e,
	0x75, 0x6d, 0x22, 0xc5, 0x03, 0x0a, 0x15, 0x42, 0x61, 0x74, 0x63, 0x68, 0x52, 0x65, 0x63, 0x6f,
	0x72, 0x64, 0x49, 0x6d, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x16, 0x0a, 0x06,
	0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x06, 0x75, 0x73,
	0x65, 0x72, 0x49, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x73, 0x18, 0x02,
	0x20, 0x03, 0x28, 0x09, 0x52, 0x06, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x73, 0x12, 0x1c, 0x0a, 0x09,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x09, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x49, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x49, 0x64, 0x12, 0x1a, 0x0a, 0x08, 0x75, 0x73, 0x65, 0x72, 0x4e, 0x61, 0x6d, 0x65,
	0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x75, 0x73, 0x65, 0x72, 0x4e, 0x61, 0x6d, 0x65,
	0x12, 0x1e, 0x0a, 0x0a, 0x75, 0x73, 0x65, 0x72, 0x41, 0x76, 0x61, 0x74, 0x61, 0x72, 0x18, 0x06,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x75, 0x73, 0x65, 0x72, 0x41, 0x76, 0x61, 0x74, 0x61, 0x72,
	0x12, 0x16, 0x0a, 0x06, 0x64, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x06, 0x64, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x12, 0x16, 0x0a, 0x06, 0x70, 0x72, 0x6f, 0x6d,
	0x70, 0x74, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x70, 0x72, 0x6f, 0x6d, 0x70, 0x74,
	0x12, 0x26, 0x0a, 0x0e, 0x6e, 0x65, 0x67, 0x61, 0x74, 0x69, 0x76, 0x65, 0x50, 0x72, 0x6f, 0x6d,
	0x70, 0x74, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x6e, 0x65, 0x67, 0x61, 0x74, 0x69,
	0x76, 0x65, 0x50, 0x72, 0x6f, 0x6d, 0x70, 0x74, 0x12, 0x14, 0x0a, 0x05, 0x77, 0x69, 0x64, 0x74,
	0x68, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x05, 0x77, 0x69, 0x64, 0x74, 0x68, 0x12, 0x16,
	0x0a, 0x06, 0x68, 0x65, 0x69, 0x67, 0x68, 0x74, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x06,
	0x68, 0x65, 0x69, 0x67, 0x68, 0x74, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x0c,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x1e, 0x0a, 0x0a, 0x69, 0x6e,
	0x69, 0x74, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x73, 0x18, 0x0d, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0a,
	0x69, 0x6e, 0x69, 0x74, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x73, 0x12, 0x1e, 0x0a, 0x0a, 0x72, 0x65,
	0x61, 0x6c, 0x50, 0x72, 0x6f, 0x6d, 0x70, 0x74, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a,
	0x72, 0x65, 0x61, 0x6c, 0x50, 0x72, 0x6f, 0x6d, 0x70, 0x74, 0x12, 0x2e, 0x0a, 0x12, 0x72, 0x65,
	0x61, 0x6c, 0x4e, 0x65, 0x67, 0x61, 0x74, 0x69, 0x76, 0x65, 0x50, 0x72, 0x6f, 0x6d, 0x70, 0x74,
	0x18, 0x0f, 0x20, 0x01, 0x28, 0x09, 0x52, 0x12, 0x72, 0x65, 0x61, 0x6c, 0x4e, 0x65, 0x67, 0x61,
	0x74, 0x69, 0x76, 0x65, 0x50, 0x72, 0x6f, 0x6d, 0x70, 0x74, 0x22, 0xa6, 0x04, 0x0a, 0x10, 0x52,
	0x65, 0x63, 0x6f, 0x72, 0x64, 0x49, 0x6d, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12,
	0x16, 0x0a, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52,
	0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x69, 0x6d, 0x61, 0x67, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x12, 0x1c, 0x0a,
	0x09, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x09, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x49, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x49, 0x64, 0x12, 0x1a, 0x0a, 0x08, 0x75, 0x73, 0x65, 0x72, 0x4e, 0x61, 0x6d,
	0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x75, 0x73, 0x65, 0x72, 0x4e, 0x61, 0x6d,
	0x65, 0x12, 0x1e, 0x0a, 0x0a, 0x75, 0x73, 0x65, 0x72, 0x41, 0x76, 0x61, 0x74, 0x61, 0x72, 0x18,
	0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x75, 0x73, 0x65, 0x72, 0x41, 0x76, 0x61, 0x74, 0x61,
	0x72, 0x12, 0x18, 0x0a, 0x07, 0x62, 0x61, 0x74, 0x63, 0x68, 0x49, 0x64, 0x18, 0x07, 0x20, 0x01,
	0x28, 0x0d, 0x52, 0x07, 0x62, 0x61, 0x74, 0x63, 0x68, 0x49, 0x64, 0x12, 0x1a, 0x0a, 0x08, 0x69,
	0x6e, 0x64, 0x65, 0x78, 0x4e, 0x75, 0x6d, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x08, 0x69,
	0x6e, 0x64, 0x65, 0x78, 0x4e, 0x75, 0x6d, 0x12, 0x1c, 0x0a, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74,
	0x65, 0x64, 0x41, 0x74, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61,
	0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x16, 0x0a, 0x06, 0x70, 0x72, 0x6f, 0x6d, 0x70, 0x74, 0x18,
	0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x70, 0x72, 0x6f, 0x6d, 0x70, 0x74, 0x12, 0x26, 0x0a,
	0x0e, 0x6e, 0x65, 0x67, 0x61, 0x74, 0x69, 0x76, 0x65, 0x50, 0x72, 0x6f, 0x6d, 0x70, 0x74, 0x18,
	0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x6e, 0x65, 0x67, 0x61, 0x74, 0x69, 0x76, 0x65, 0x50,
	0x72, 0x6f, 0x6d, 0x70, 0x74, 0x12, 0x14, 0x0a, 0x05, 0x77, 0x69, 0x64, 0x74, 0x68, 0x18, 0x0c,
	0x20, 0x01, 0x28, 0x0d, 0x52, 0x05, 0x77, 0x69, 0x64, 0x74, 0x68, 0x12, 0x16, 0x0a, 0x06, 0x68,
	0x65, 0x69, 0x67, 0x68, 0x74, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x06, 0x68, 0x65, 0x69,
	0x67, 0x68, 0x74, 0x12, 0x1a, 0x0a, 0x08, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x4e, 0x75, 0x6d, 0x18,
	0x0e, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x4e, 0x75, 0x6d, 0x12,
	0x12, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x74,
	0x79, 0x70, 0x65, 0x12, 0x1e, 0x0a, 0x0a, 0x69, 0x6e, 0x69, 0x74, 0x49, 0x6d, 0x61, 0x67, 0x65,
	0x73, 0x18, 0x10, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0a, 0x69, 0x6e, 0x69, 0x74, 0x49, 0x6d, 0x61,
	0x67, 0x65, 0x73, 0x12, 0x0e, 0x0a, 0x02, 0x49, 0x44, 0x18, 0x11, 0x20, 0x01, 0x28, 0x0d, 0x52,
	0x02, 0x49, 0x44, 0x12, 0x1e, 0x0a, 0x0a, 0x72, 0x65, 0x61, 0x6c, 0x50, 0x72, 0x6f, 0x6d, 0x70,
	0x74, 0x18, 0x12, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x72, 0x65, 0x61, 0x6c, 0x50, 0x72, 0x6f,
	0x6d, 0x70, 0x74, 0x12, 0x2e, 0x0a, 0x12, 0x72, 0x65, 0x61, 0x6c, 0x4e, 0x65, 0x67, 0x61, 0x74,
	0x69, 0x76, 0x65, 0x50, 0x72, 0x6f, 0x6d, 0x70, 0x74, 0x18, 0x13, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x12, 0x72, 0x65, 0x61, 0x6c, 0x4e, 0x65, 0x67, 0x61, 0x74, 0x69, 0x76, 0x65, 0x50, 0x72, 0x6f,
	0x6d, 0x70, 0x74, 0x22, 0xb2, 0x02, 0x0a, 0x10, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x4c, 0x69, 0x73,
	0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x16, 0x0a, 0x06, 0x75, 0x73, 0x65, 0x72,
	0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64,
	0x12, 0x14, 0x0a, 0x05, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x05, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x12, 0x1c, 0x0a, 0x09, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x4e,
	0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x4e, 0x61, 0x6d, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x49, 0x64, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x49, 0x64, 0x12, 0x16,
	0x0a, 0x06, 0x64, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06,
	0x64, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x12, 0x26, 0x0a, 0x0e, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65,
	0x64, 0x41, 0x74, 0x53, 0x74, 0x61, 0x72, 0x74, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e,
	0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x53, 0x74, 0x61, 0x72, 0x74, 0x12, 0x22,
	0x0a, 0x0c, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x45, 0x6e, 0x64, 0x18, 0x07,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x45,
	0x6e, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x70, 0x61, 0x67, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0d,
	0x52, 0x04, 0x70, 0x61, 0x67, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x61, 0x67, 0x65, 0x53, 0x69,
	0x7a, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x08, 0x70, 0x61, 0x67, 0x65, 0x53, 0x69,
	0x7a, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x69, 0x64, 0x73, 0x18, 0x0a, 0x20, 0x03, 0x28, 0x0d, 0x52,
	0x03, 0x69, 0x64, 0x73, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x0b, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x22, 0x55, 0x0a, 0x11, 0x49, 0x6d, 0x61, 0x67,
	0x65, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x14, 0x0a,
	0x05, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x05, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x12, 0x2a, 0x0a, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x02, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x16, 0x2e, 0x74, 0x61, 0x73, 0x6b, 0x2e, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x49,
	0x6d, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x52, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x22,
	0x3a, 0x0a, 0x10, 0x53, 0x65, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x12, 0x0e, 0x0a, 0x02, 0x49, 0x44, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52,
	0x02, 0x49, 0x44, 0x12, 0x16, 0x0a, 0x06, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x04, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x22, 0x4f, 0x0a, 0x10, 0x54,
	0x61, 0x73, 0x6b, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12,
	0x14, 0x0a, 0x05, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x05,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x25, 0x0a, 0x04, 0x44, 0x61, 0x74, 0x61, 0x18, 0x02, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x74, 0x61, 0x73, 0x6b, 0x2e, 0x54, 0x61, 0x73, 0x6b, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x22, 0x83, 0x01, 0x0a,
	0x0f, 0x44, 0x61, 0x74, 0x61, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x12, 0x20, 0x0a, 0x0b, 0x53, 0x75, 0x62, 0x6d, 0x69, 0x74, 0x74, 0x65, 0x72, 0x49, 0x44, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0b, 0x73, 0x75, 0x62, 0x6d, 0x69, 0x74, 0x74, 0x65, 0x72,
	0x49, 0x44, 0x12, 0x22, 0x0a, 0x0c, 0x53, 0x75, 0x62, 0x6d, 0x69, 0x74, 0x74, 0x65, 0x72, 0x49,
	0x44, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x04, 0x52, 0x0c, 0x73, 0x75, 0x62, 0x6d, 0x69, 0x74,
	0x74, 0x65, 0x72, 0x49, 0x44, 0x73, 0x12, 0x16, 0x0a, 0x06, 0x44, 0x6f, 0x6d, 0x61, 0x69, 0x6e,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x64, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x12, 0x12,
	0x0a, 0x04, 0x54, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x74, 0x79,
	0x70, 0x65, 0x22, 0xf0, 0x01, 0x0a, 0x08, 0x44, 0x61, 0x74, 0x61, 0x49, 0x6e, 0x66, 0x6f, 0x12,
	0x1c, 0x0a, 0x09, 0x4e, 0x65, 0x61, 0x72, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x04, 0x52, 0x09, 0x6e, 0x65, 0x61, 0x72, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x1e, 0x0a,
	0x0a, 0x44, 0x6f, 0x69, 0x6e, 0x67, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x04, 0x52, 0x0a, 0x64, 0x6f, 0x69, 0x6e, 0x67, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x1c, 0x0a,
	0x09, 0x44, 0x6f, 0x6e, 0x65, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x04,
	0x52, 0x09, 0x64, 0x6f, 0x6e, 0x65, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x1c, 0x0a, 0x09, 0x4f,
	0x76, 0x65, 0x72, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x04, 0x52, 0x09,
	0x6f, 0x76, 0x65, 0x72, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x1e, 0x0a, 0x0a, 0x54, 0x6f, 0x74,
	0x61, 0x6c, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0a, 0x74,
	0x6f, 0x74, 0x61, 0x6c, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x24, 0x0a, 0x0d, 0x4e, 0x6f, 0x74,
	0x53, 0x74, 0x61, 0x72, 0x74, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x06, 0x20, 0x01, 0x28, 0x04,
	0x52, 0x0d, 0x6e, 0x6f, 0x74, 0x53, 0x74, 0x61, 0x72, 0x74, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12,
	0x24, 0x0a, 0x0c, 0x4e, 0x65, 0x61, 0x72, 0x45, 0x6e, 0x64, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x18,
	0x07, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0e, 0x6e, 0x65, 0x61, 0x72, 0x53, 0x74, 0x61, 0x72, 0x74,
	0x43, 0x6f, 0x75, 0x6e, 0x74, 0x22, 0x76, 0x0a, 0x10, 0x44, 0x61, 0x74, 0x61, 0x49, 0x6e, 0x66,
	0x6f, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x32, 0x0a, 0x0c, 0x4d, 0x79, 0x53,
	0x75, 0x62, 0x6d, 0x69, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x0e, 0x2e, 0x74, 0x61, 0x73, 0x6b, 0x2e, 0x44, 0x61, 0x74, 0x61, 0x49, 0x6e, 0x66, 0x6f, 0x52,
	0x0c, 0x6d, 0x79, 0x53, 0x75, 0x62, 0x6d, 0x69, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x2e, 0x0a,
	0x0a, 0x44, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x0e, 0x2e, 0x74, 0x61, 0x73, 0x6b, 0x2e, 0x44, 0x61, 0x74, 0x61, 0x49, 0x6e, 0x66,
	0x6f, 0x52, 0x0a, 0x64, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x22, 0x32, 0x0a,
	0x12, 0x50, 0x61, 0x73, 0x73, 0x41, 0x72, 0x74, 0x57, 0x6f, 0x72, 0x6b, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x12, 0x1c, 0x0a, 0x09, 0x41, 0x72, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x49, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x09, 0x61, 0x72, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x49,
	0x64, 0x22, 0x4e, 0x0a, 0x08, 0x4a, 0x6f, 0x69, 0x6e, 0x55, 0x73, 0x65, 0x72, 0x12, 0x16, 0x0a,
	0x06, 0x55, 0x73, 0x65, 0x72, 0x49, 0x44, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x06, 0x75,
	0x73, 0x65, 0x72, 0x49, 0x44, 0x12, 0x12, 0x0a, 0x04, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x04, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x22, 0xb9, 0x01, 0x0a, 0x07, 0x41, 0x72, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x12, 0x1c, 0x0a,
	0x09, 0x41, 0x72, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x49, 0x44, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04,
	0x52, 0x09, 0x61, 0x72, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x49, 0x44, 0x12, 0x12, 0x0a, 0x04, 0x4e,
	0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12,
	0x16, 0x0a, 0x06, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x04, 0x52,
	0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x1e, 0x0a, 0x0a, 0x41, 0x72, 0x74, 0x69, 0x73,
	0x74, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x41, 0x72, 0x74,
	0x69, 0x73, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x41, 0x72, 0x74, 0x69, 0x73,
	0x74, 0x49, 0x44, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x41, 0x72, 0x74, 0x69, 0x73,
	0x74, 0x49, 0x44, 0x12, 0x28, 0x0a, 0x0f, 0x61, 0x72, 0x74, 0x69, 0x73, 0x74, 0x53, 0x68, 0x6f,
	0x77, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x61, 0x72,
	0x74, 0x69, 0x73, 0x74, 0x53, 0x68, 0x6f, 0x77, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x22, 0xea, 0x03,
	0x0a, 0x0b, 0x54, 0x61, 0x73, 0x6b, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x0e, 0x0a,
	0x02, 0x49, 0x44, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x02, 0x49, 0x44, 0x12, 0x16, 0x0a,
	0x06, 0x44, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x64,
	0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x12, 0x1c, 0x0a, 0x09, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64,
	0x41, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65,
	0x64, 0x41, 0x74, 0x12, 0x1c, 0x0a, 0x09, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74,
	0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x41,
	0x74, 0x12, 0x20, 0x0a, 0x0b, 0x53, 0x75, 0x62, 0x6d, 0x69, 0x74, 0x74, 0x65, 0x72, 0x49, 0x44,
	0x18, 0x06, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0b, 0x73, 0x75, 0x62, 0x6d, 0x69, 0x74, 0x74, 0x65,
	0x72, 0x49, 0x44, 0x12, 0x24, 0x0a, 0x0d, 0x53, 0x75, 0x62, 0x6d, 0x69, 0x74, 0x74, 0x65, 0x72,
	0x4e, 0x61, 0x6d, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x73, 0x75, 0x62, 0x6d,
	0x69, 0x74, 0x74, 0x65, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x18, 0x08, 0x20, 0x01, 0x28, 0x04, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x12, 0x2c, 0x0a, 0x09, 0x4a, 0x6f, 0x69, 0x6e, 0x55, 0x73, 0x65, 0x72, 0x73, 0x18, 0x09,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x0e, 0x2e, 0x74, 0x61, 0x73, 0x6b, 0x2e, 0x4a, 0x6f, 0x69, 0x6e,
	0x55, 0x73, 0x65, 0x72, 0x52, 0x09, 0x6a, 0x6f, 0x69, 0x6e, 0x55, 0x73, 0x65, 0x72, 0x73, 0x12,
	0x29, 0x0a, 0x08, 0x41, 0x72, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x18, 0x12, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x0d, 0x2e, 0x74, 0x61, 0x73, 0x6b, 0x2e, 0x41, 0x72, 0x74, 0x77, 0x6f, 0x72, 0x6b,
	0x52, 0x08, 0x61, 0x72, 0x74, 0x57, 0x6f, 0x72, 0x6b, 0x73, 0x12, 0x18, 0x0a, 0x07, 0x53, 0x74,
	0x61, 0x72, 0x74, 0x41, 0x74, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x73, 0x74, 0x61,
	0x72, 0x74, 0x41, 0x74, 0x12, 0x14, 0x0a, 0x05, 0x45, 0x6e, 0x64, 0x41, 0x74, 0x18, 0x0b, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x05, 0x65, 0x6e, 0x64, 0x41, 0x74, 0x12, 0x12, 0x0a, 0x04, 0x54, 0x79,
	0x70, 0x65, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x18,
	0x0a, 0x07, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x07, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x12, 0x3e, 0x0a, 0x0d, 0x54, 0x61, 0x73, 0x6b,
	0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x73, 0x18, 0x11, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x19, 0x2e, 0x74, 0x61, 0x73, 0x6b, 0x2e, 0x54, 0x61, 0x73, 0x6b, 0x57, 0x6f, 0x72, 0x6b, 0x66,
	0x6c, 0x6f, 0x77, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x52, 0x0c, 0x74, 0x61, 0x73, 0x6b,
	0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x12, 0x20, 0x0a, 0x0b, 0x4f, 0x70, 0x65, 0x72,
	0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x13, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x6f,
	0x70, 0x65, 0x72, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x22, 0x34, 0x0a, 0x0a, 0x54, 0x61,
	0x73, 0x6b, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x12, 0x0e, 0x0a, 0x02, 0x49, 0x44, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x04, 0x52, 0x02, 0x49, 0x44, 0x12, 0x16, 0x0a, 0x06, 0x44, 0x6f, 0x6d, 0x61,
	0x69, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x64, 0x6f, 0x6d, 0x61, 0x69, 0x6e,
	0x22, 0x38, 0x0a, 0x0c, 0x54, 0x61, 0x73, 0x6b, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x12, 0x0e, 0x0a, 0x02, 0x49, 0x44, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x02, 0x49, 0x44,
	0x12, 0x18, 0x0a, 0x07, 0x53, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x08, 0x52, 0x07, 0x73, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x22, 0x26, 0x0a, 0x0a, 0x54, 0x61,
	0x73, 0x6b, 0x52, 0x65, 0x6d, 0x6f, 0x76, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x53, 0x75, 0x63, 0x63,
	0x65, 0x73, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x73, 0x75, 0x63, 0x63, 0x65,
	0x73, 0x73, 0x22, 0xa2, 0x02, 0x0a, 0x08, 0x54, 0x61, 0x73, 0x6b, 0x4c, 0x69, 0x73, 0x74, 0x12,
	0x12, 0x0a, 0x04, 0x50, 0x61, 0x67, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x04, 0x70,
	0x61, 0x67, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x50, 0x61, 0x67, 0x65, 0x53, 0x69, 0x7a, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x04, 0x52, 0x08, 0x70, 0x61, 0x67, 0x65, 0x53, 0x69, 0x7a, 0x65, 0x12,
	0x16, 0x0a, 0x06, 0x44, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x06, 0x64, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x12, 0x20, 0x0a, 0x0b, 0x53, 0x75, 0x62, 0x6d, 0x69,
	0x74, 0x74, 0x65, 0x72, 0x49, 0x44, 0x18, 0x04, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0b, 0x73, 0x75,
	0x62, 0x6d, 0x69, 0x74, 0x74, 0x65, 0x72, 0x49, 0x44, 0x12, 0x16, 0x0a, 0x06, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x18, 0x05, 0x20, 0x01, 0x28, 0x04, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x12, 0x12, 0x0a, 0x04, 0x54, 0x79, 0x70, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x22, 0x0a, 0x0c, 0x53, 0x75, 0x62, 0x6d, 0x69, 0x74, 0x74,
	0x65, 0x72, 0x49, 0x44, 0x73, 0x18, 0x07, 0x20, 0x03, 0x28, 0x04, 0x52, 0x0c, 0x73, 0x75, 0x62,
	0x6d, 0x69, 0x74, 0x74, 0x65, 0x72, 0x49, 0x44, 0x73, 0x12, 0x1e, 0x0a, 0x0a, 0x41, 0x72, 0x74,
	0x69, 0x73, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x61,
	0x72, 0x74, 0x69, 0x73, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x41, 0x72, 0x74,
	0x77, 0x6f, 0x72, 0x6b, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b,
	0x61, 0x72, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x4a,
	0x6f, 0x69, 0x6e, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x6a,
	0x6f, 0x69, 0x6e, 0x4e, 0x61, 0x6d, 0x65, 0x22, 0x5f, 0x0a, 0x18, 0x54, 0x61, 0x73, 0x6b, 0x57,
	0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x04, 0x52, 0x05, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x2d, 0x0a, 0x04, 0x44, 0x61, 0x74,
	0x61, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x74, 0x61, 0x73, 0x6b, 0x2e, 0x54,
	0x61, 0x73, 0x6b, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x22, 0x81, 0x01, 0x0a, 0x13, 0x54, 0x61, 0x73,
	0x6b, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x12, 0x0e, 0x0a, 0x02, 0x49, 0x44, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x02, 0x49, 0x44,
	0x12, 0x16, 0x0a, 0x06, 0x54, 0x61, 0x73, 0x6b, 0x49, 0x44, 0x18, 0x05, 0x20, 0x01, 0x28, 0x04,
	0x52, 0x06, 0x74, 0x61, 0x73, 0x6b, 0x49, 0x44, 0x12, 0x16, 0x0a, 0x06, 0x55, 0x73, 0x65, 0x72,
	0x49, 0x44, 0x18, 0x06, 0x20, 0x01, 0x28, 0x04, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x44,
	0x12, 0x12, 0x0a, 0x04, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04,
	0x6e, 0x61, 0x6d, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x09,
	0x20, 0x01, 0x28, 0x04, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x22, 0x3c, 0x0a, 0x12,
	0x54, 0x61, 0x73, 0x6b, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x44, 0x65, 0x74, 0x61,
	0x69, 0x6c, 0x12, 0x0e, 0x0a, 0x02, 0x49, 0x44, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x02,
	0x49, 0x44, 0x12, 0x16, 0x0a, 0x06, 0x44, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x06, 0x64, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x22, 0x40, 0x0a, 0x14, 0x54, 0x61,
	0x73, 0x6b, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x12, 0x0e, 0x0a, 0x02, 0x49, 0x44, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x02,
	0x49, 0x44, 0x12, 0x18, 0x0a, 0x07, 0x53, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x08, 0x52, 0x07, 0x73, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x22, 0x2e, 0x0a, 0x12,
	0x54, 0x61, 0x73, 0x6b, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x52, 0x65, 0x6d, 0x6f,
	0x76, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x53, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x08, 0x52, 0x07, 0x73, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x22, 0x42, 0x0a, 0x10,
	0x54, 0x61, 0x73, 0x6b, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x4c, 0x69, 0x73, 0x74,
	0x12, 0x12, 0x0a, 0x04, 0x50, 0x61, 0x67, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x04,
	0x70, 0x61, 0x67, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x50, 0x61, 0x67, 0x65, 0x53, 0x69, 0x7a, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x04, 0x52, 0x08, 0x70, 0x61, 0x67, 0x65, 0x53, 0x69, 0x7a, 0x65,
	0x32, 0x98, 0x06, 0x0a, 0x04, 0x54, 0x61, 0x73, 0x6b, 0x12, 0x2f, 0x0a, 0x06, 0x44, 0x65, 0x74,
	0x61, 0x69, 0x6c, 0x12, 0x10, 0x2e, 0x74, 0x61, 0x73, 0x6b, 0x2e, 0x54, 0x61, 0x73, 0x6b, 0x44,
	0x65, 0x74, 0x61, 0x69, 0x6c, 0x1a, 0x11, 0x2e, 0x74, 0x61, 0x73, 0x6b, 0x2e, 0x54, 0x61, 0x73,
	0x6b, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x22, 0x00, 0x12, 0x31, 0x0a, 0x06, 0x43, 0x72,
	0x65, 0x61, 0x74, 0x65, 0x12, 0x11, 0x2e, 0x74, 0x61, 0x73, 0x6b, 0x2e, 0x54, 0x61, 0x73, 0x6b,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x12, 0x2e, 0x74, 0x61, 0x73, 0x6b, 0x2e, 0x54,
	0x61, 0x73, 0x6b, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x31, 0x0a,
	0x06, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x12, 0x11, 0x2e, 0x74, 0x61, 0x73, 0x6b, 0x2e, 0x54,
	0x61, 0x73, 0x6b, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x12, 0x2e, 0x74, 0x61, 0x73,
	0x6b, 0x2e, 0x54, 0x61, 0x73, 0x6b, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00,
	0x12, 0x3d, 0x0a, 0x0b, 0x50, 0x61, 0x73, 0x73, 0x41, 0x72, 0x74, 0x57, 0x6f, 0x72, 0x6b, 0x12,
	0x18, 0x2e, 0x74, 0x61, 0x73, 0x6b, 0x2e, 0x50, 0x61, 0x73, 0x73, 0x41, 0x72, 0x74, 0x57, 0x6f,
	0x72, 0x6b, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x12, 0x2e, 0x74, 0x61, 0x73, 0x6b,
	0x2e, 0x54, 0x61, 0x73, 0x6b, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12,
	0x39, 0x0a, 0x09, 0x53, 0x65, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x16, 0x2e, 0x74,
	0x61, 0x73, 0x6b, 0x2e, 0x53, 0x65, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x1a, 0x12, 0x2e, 0x74, 0x61, 0x73, 0x6b, 0x2e, 0x54, 0x61, 0x73, 0x6b,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x2e, 0x0a, 0x06, 0x52, 0x65,
	0x6d, 0x6f, 0x76, 0x65, 0x12, 0x10, 0x2e, 0x74, 0x61, 0x73, 0x6b, 0x2e, 0x54, 0x61, 0x73, 0x6b,
	0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x1a, 0x10, 0x2e, 0x74, 0x61, 0x73, 0x6b, 0x2e, 0x54, 0x61,
	0x73, 0x6b, 0x52, 0x65, 0x6d, 0x6f, 0x76, 0x65, 0x22, 0x00, 0x12, 0x30, 0x0a, 0x04, 0x4c, 0x69,
	0x73, 0x74, 0x12, 0x0e, 0x2e, 0x74, 0x61, 0x73, 0x6b, 0x2e, 0x54, 0x61, 0x73, 0x6b, 0x4c, 0x69,
	0x73, 0x74, 0x1a, 0x16, 0x2e, 0x74, 0x61, 0x73, 0x6b, 0x2e, 0x54, 0x61, 0x73, 0x6b, 0x4c, 0x69,
	0x73, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x2f, 0x0a, 0x07,
	0x54, 0x6f, 0x70, 0x70, 0x69, 0x6e, 0x67, 0x12, 0x10, 0x2e, 0x74, 0x61, 0x73, 0x6b, 0x2e, 0x54,
	0x61, 0x73, 0x6b, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x1a, 0x10, 0x2e, 0x74, 0x61, 0x73, 0x6b,
	0x2e, 0x54, 0x61, 0x73, 0x6b, 0x52, 0x65, 0x6d, 0x6f, 0x76, 0x65, 0x22, 0x00, 0x12, 0x3b, 0x0a,
	0x08, 0x44, 0x61, 0x74, 0x61, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x15, 0x2e, 0x74, 0x61, 0x73, 0x6b,
	0x2e, 0x44, 0x61, 0x74, 0x61, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x1a, 0x16, 0x2e, 0x74, 0x61, 0x73, 0x6b, 0x2e, 0x44, 0x61, 0x74, 0x61, 0x49, 0x6e, 0x66, 0x6f,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x35, 0x0a, 0x06, 0x4d, 0x6f,
	0x64, 0x65, 0x6c, 0x73, 0x12, 0x13, 0x2e, 0x74, 0x61, 0x73, 0x6b, 0x2e, 0x4d, 0x6f, 0x64, 0x65,
	0x6c, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x14, 0x2e, 0x74, 0x61, 0x73, 0x6b,
	0x2e, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22,
	0x00, 0x12, 0x2d, 0x0a, 0x05, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x12, 0x12, 0x2e, 0x74, 0x61, 0x73,
	0x6b, 0x2e, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x0e,
	0x2e, 0x74, 0x61, 0x73, 0x6b, 0x2e, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x53, 0x65, 0x74, 0x22, 0x00,
	0x12, 0x40, 0x0a, 0x09, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x49, 0x6d, 0x67, 0x12, 0x1b, 0x2e,
	0x74, 0x61, 0x73, 0x6b, 0x2e, 0x42, 0x61, 0x74, 0x63, 0x68, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64,
	0x49, 0x6d, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x14, 0x2e, 0x74, 0x61, 0x73,
	0x6b, 0x2e, 0x43, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x22, 0x00, 0x12, 0x3e, 0x0a, 0x09, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x12,
	0x16, 0x2e, 0x74, 0x61, 0x73, 0x6b, 0x2e, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x4c, 0x69, 0x73, 0x74,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x17, 0x2e, 0x74, 0x61, 0x73, 0x6b, 0x2e, 0x49,
	0x6d, 0x61, 0x67, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x22, 0x00, 0x12, 0x47, 0x0a, 0x0f, 0x44, 0x65, 0x6c, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x49,
	0x6d, 0x61, 0x67, 0x65, 0x73, 0x12, 0x1c, 0x2e, 0x74, 0x61, 0x73, 0x6b, 0x2e, 0x44, 0x65, 0x6c,
	0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x73, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x1a, 0x14, 0x2e, 0x74, 0x61, 0x73, 0x6b, 0x2e, 0x43, 0x6f, 0x6d, 0x6d, 0x6f,
	0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x42, 0x09, 0x5a, 0x07, 0x2e,
	0x2f, 0x3b, 0x74, 0x61, 0x73, 0x6b, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_task_task_proto_rawDescOnce sync.Once
	file_api_task_task_proto_rawDescData = file_api_task_task_proto_rawDesc
)

func file_api_task_task_proto_rawDescGZIP() []byte {
	file_api_task_task_proto_rawDescOnce.Do(func() {
		file_api_task_task_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_task_task_proto_rawDescData)
	})
	return file_api_task_task_proto_rawDescData
}

var file_api_task_task_proto_msgTypes = make([]protoimpl.MessageInfo, 31)
var file_api_task_task_proto_goTypes = []interface{}{
	(*ModelRequest)(nil),             // 0: task.ModelRequest
	(*ModelsResponse)(nil),           // 1: task.ModelsResponse
	(*ModelSet)(nil),                 // 2: task.ModelSet
	(*Model)(nil),                    // 3: task.Model
	(*ModelsRequest)(nil),            // 4: task.ModelsRequest
	(*EmptyRequest)(nil),             // 5: task.EmptyRequest
	(*DelRecordImagesRequest)(nil),   // 6: task.DelRecordImagesRequest
	(*CommonResponse)(nil),           // 7: task.CommonResponse
	(*BatchRecordImgRequest)(nil),    // 8: task.BatchRecordImgRequest
	(*RecordImgRequest)(nil),         // 9: task.RecordImgRequest
	(*ImageListRequest)(nil),         // 10: task.ImageListRequest
	(*ImageListResponse)(nil),        // 11: task.ImageListResponse
	(*SetStatusRequest)(nil),         // 12: task.SetStatusRequest
	(*TaskListResponse)(nil),         // 13: task.TaskListResponse
	(*DataInfoRequest)(nil),          // 14: task.DataInfoRequest
	(*DataInfo)(nil),                 // 15: task.DataInfo
	(*DataInfoResponse)(nil),         // 16: task.DataInfoResponse
	(*PassArtWorkRequest)(nil),       // 17: task.PassArtWorkRequest
	(*JoinUser)(nil),                 // 18: task.JoinUser
	(*Artwork)(nil),                  // 19: task.Artwork
	(*TaskRequest)(nil),              // 20: task.TaskRequest
	(*TaskDetail)(nil),               // 21: task.TaskDetail
	(*TaskResponse)(nil),             // 22: task.TaskResponse
	(*TaskRemove)(nil),               // 23: task.TaskRemove
	(*TaskList)(nil),                 // 24: task.TaskList
	(*TaskWorkflowListResponse)(nil), // 25: task.TaskWorkflowListResponse
	(*TaskWorkflowRequest)(nil),      // 26: task.TaskWorkflowRequest
	(*TaskWorkflowDetail)(nil),       // 27: task.TaskWorkflowDetail
	(*TaskWorkflowResponse)(nil),     // 28: task.TaskWorkflowResponse
	(*TaskWorkflowRemove)(nil),       // 29: task.TaskWorkflowRemove
	(*TaskWorkflowList)(nil),         // 30: task.TaskWorkflowList
}
var file_api_task_task_proto_depIdxs = []int32{
	3,  // 0: task.ModelsResponse.list:type_name -> task.Model
	2,  // 1: task.Model.ModelSet:type_name -> task.ModelSet
	9,  // 2: task.ImageListResponse.list:type_name -> task.RecordImgRequest
	20, // 3: task.TaskListResponse.Data:type_name -> task.TaskRequest
	15, // 4: task.DataInfoResponse.MySubmitInfo:type_name -> task.DataInfo
	15, // 5: task.DataInfoResponse.DomainInfo:type_name -> task.DataInfo
	18, // 6: task.TaskRequest.JoinUsers:type_name -> task.JoinUser
	19, // 7: task.TaskRequest.Artworks:type_name -> task.Artwork
	26, // 8: task.TaskRequest.TaskWorkflows:type_name -> task.TaskWorkflowRequest
	26, // 9: task.TaskWorkflowListResponse.Data:type_name -> task.TaskWorkflowRequest
	21, // 10: task.Task.Detail:input_type -> task.TaskDetail
	20, // 11: task.Task.Create:input_type -> task.TaskRequest
	20, // 12: task.Task.Update:input_type -> task.TaskRequest
	17, // 13: task.Task.PassArtWork:input_type -> task.PassArtWorkRequest
	12, // 14: task.Task.SetStatus:input_type -> task.SetStatusRequest
	21, // 15: task.Task.Remove:input_type -> task.TaskDetail
	24, // 16: task.Task.List:input_type -> task.TaskList
	21, // 17: task.Task.Topping:input_type -> task.TaskDetail
	14, // 18: task.Task.DataInfo:input_type -> task.DataInfoRequest
	4,  // 19: task.Task.Models:input_type -> task.ModelsRequest
	0,  // 20: task.Task.Model:input_type -> task.ModelRequest
	8,  // 21: task.Task.RecordImg:input_type -> task.BatchRecordImgRequest
	10, // 22: task.Task.ImageList:input_type -> task.ImageListRequest
	6,  // 23: task.Task.DelRecordImages:input_type -> task.DelRecordImagesRequest
	20, // 24: task.Task.Detail:output_type -> task.TaskRequest
	22, // 25: task.Task.Create:output_type -> task.TaskResponse
	22, // 26: task.Task.Update:output_type -> task.TaskResponse
	22, // 27: task.Task.PassArtWork:output_type -> task.TaskResponse
	22, // 28: task.Task.SetStatus:output_type -> task.TaskResponse
	23, // 29: task.Task.Remove:output_type -> task.TaskRemove
	13, // 30: task.Task.List:output_type -> task.TaskListResponse
	23, // 31: task.Task.Topping:output_type -> task.TaskRemove
	16, // 32: task.Task.DataInfo:output_type -> task.DataInfoResponse
	1,  // 33: task.Task.Models:output_type -> task.ModelsResponse
	2,  // 34: task.Task.Model:output_type -> task.ModelSet
	7,  // 35: task.Task.RecordImg:output_type -> task.CommonResponse
	11, // 36: task.Task.ImageList:output_type -> task.ImageListResponse
	7,  // 37: task.Task.DelRecordImages:output_type -> task.CommonResponse
	24, // [24:38] is the sub-list for method output_type
	10, // [10:24] is the sub-list for method input_type
	10, // [10:10] is the sub-list for extension type_name
	10, // [10:10] is the sub-list for extension extendee
	0,  // [0:10] is the sub-list for field type_name
}

func init() { file_api_task_task_proto_init() }
func file_api_task_task_proto_init() {
	if File_api_task_task_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_api_task_task_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ModelRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_task_task_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ModelsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_task_task_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ModelSet); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_task_task_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Model); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_task_task_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ModelsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_task_task_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*EmptyRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_task_task_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DelRecordImagesRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_task_task_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CommonResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_task_task_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BatchRecordImgRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_task_task_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RecordImgRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_task_task_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ImageListRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_task_task_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ImageListResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_task_task_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SetStatusRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_task_task_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TaskListResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_task_task_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DataInfoRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_task_task_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DataInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_task_task_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DataInfoResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_task_task_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PassArtWorkRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_task_task_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*JoinUser); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_task_task_proto_msgTypes[19].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Artwork); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_task_task_proto_msgTypes[20].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TaskRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_task_task_proto_msgTypes[21].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TaskDetail); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_task_task_proto_msgTypes[22].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TaskResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_task_task_proto_msgTypes[23].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TaskRemove); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_task_task_proto_msgTypes[24].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TaskList); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_task_task_proto_msgTypes[25].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TaskWorkflowListResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_task_task_proto_msgTypes[26].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TaskWorkflowRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_task_task_proto_msgTypes[27].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TaskWorkflowDetail); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_task_task_proto_msgTypes[28].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TaskWorkflowResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_task_task_proto_msgTypes[29].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TaskWorkflowRemove); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_task_task_proto_msgTypes[30].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TaskWorkflowList); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_task_task_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   31,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_api_task_task_proto_goTypes,
		DependencyIndexes: file_api_task_task_proto_depIdxs,
		MessageInfos:      file_api_task_task_proto_msgTypes,
	}.Build()
	File_api_task_task_proto = out.File
	file_api_task_task_proto_rawDesc = nil
	file_api_task_task_proto_goTypes = nil
	file_api_task_task_proto_depIdxs = nil
}
