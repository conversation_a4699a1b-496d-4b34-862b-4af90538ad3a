syntax = "proto3";
package task;

option go_package = "./;task";

// The task service definition.
service Task {
 rpc Detail(TaskDetail) returns (TaskRequest) {};
 rpc Create(TaskRequest) returns (TaskResponse) {};
 rpc Update(TaskRequest) returns (TaskResponse) {};
 rpc PassArtWork(PassArtWorkRequest) returns (TaskResponse) {};
 rpc SetStatus(SetStatusRequest) returns (TaskResponse) {};
 rpc Remove(TaskDetail) returns (TaskRemove) {};
 rpc List(TaskList) returns (TaskListResponse) {};
 rpc Topping(TaskDetail) returns (TaskRemove) {}; //置顶
 rpc DataInfo(DataInfoRequest) returns (DataInfoResponse) {};

 rpc Models(ModelsRequest) returns (ModelsResponse) {};//记录任务图片
 rpc Model(ModelRequest) returns (ModelSet) {};//记录任务图片
 rpc RecordImg(BatchRecordImgRequest) returns (CommonResponse) {};//记录任务图片
 rpc ImageList(ImageListRequest) returns (ImageListResponse) {};//任务列表
 rpc DelRecordImages(DelRecordImagesRequest) returns (CommonResponse) {};//记录任务图片
}

message ModelRequest {
 string modelName = 1;
 string sdModelHash = 2;
 string Title = 3;
}

message ModelsResponse {
 uint32 count = 1;
 repeated Model list = 2;
}

message ModelSet{
 string   FileName =1;
 string   Name  =2;
 string   Url    =3;
 string   Image    =4;
 string   Head     =11;
 string   Tag      =5;
 string   Author   =6;
 float   Cfg      =7;
 int32   Steps    =8;
 string   Sampler  =9;
 int32   ClipSkip =10;

}

message Model{
 string title = 2 ;
 string subTitle = 3 ;
 string modelName = 4 ;
 string hash = 5 ;
 string sha256 = 6 ;
 string replaceAuthor = 7 ;
 string replaceAuthorImg = 8 ;
 string replaceImg= 9;
 repeated string replaceTag= 10 ;
 ModelSet ModelSet = 11;
 uint32 index= 12;
 string sqImage= 13;
}

message ModelsRequest {
 uint32 page = 1 ;
 uint32 pageSize = 2 ;
}

message EmptyRequest {
}

message DelRecordImagesRequest {
 repeated uint32 ids = 1;
 uint32 userId = 2;
}

message CommonResponse {
 uint32 ID = 1;
 string orderNum = 2;
}

message BatchRecordImgRequest {
 uint32 userId = 1 ;
 repeated string images = 2 ;
 string modelName = 3 ;
 string modelId = 4 ;
 string userName = 5 ;
 string userAvatar = 6 ;
 string domain = 7 ;
 string prompt = 8 ;
 string negativePrompt = 9 ;
 uint32 width = 10 ;
 uint32 height = 11 ;
 string type = 12 ;//"ai"或者plus
 repeated string initImages = 13 ;//"ai"或者plus
 string realPrompt = 14 ;
 string realNegativePrompt = 15;
}

message RecordImgRequest {
 uint32 userId = 1 ;
 string image = 2 ;
 string modelName = 3 ;
 string modelId = 4 ;
 string userName = 5 ;
 string userAvatar = 6 ;
 uint32 batchId = 7 ;
 uint32 indexNum = 8 ;
 string createdAt = 9 ;
 string prompt = 10 ;
 string negativePrompt = 11 ;
 uint32 width = 12 ;
 uint32 height = 13 ;
 string orderNum = 14 ;
 string type = 15 ;//"ai"或者plus
 repeated string initImages = 16 ;//"ai"或者plus
 uint32 ID = 17 ;//"ai"或者plus
 string realPrompt = 18 ;
 string realNegativePrompt = 19;
}

message ImageListRequest {
 uint32 userId = 1 ;
 string image = 2 ;
 string modelName = 3 ;
 string modelId = 4 ;
 string domain = 5 ;
 string createdAtStart = 6 ;
 string createdAtEnd = 7 ;
 uint32 page = 8 ;
 uint32 pageSize = 9 ;
 repeated uint32 ids = 10 ;
 string type = 11 ;
}


message ImageListResponse {
 uint32 count =1;
 repeated RecordImgRequest list = 2;
}

message SetStatusRequest {
 uint64 ID = 1 [json_name = "ID"];
 uint64  Status = 2 [json_name = "status"];
}

message TaskListResponse {
 uint64 Count = 1 [json_name = "count"];
 repeated TaskRequest Data = 2 [json_name = "data"];
}

message DataInfoRequest {
 uint64 SubmitterID       =1 [json_name = "submitterID"];
 repeated uint64 SubmitterIDs       =4 [json_name = "submitterIDs"];
 string Domain = 2 [json_name = "domain"];
 string Type = 3 [json_name = "type"];
}

message DataInfo {
 uint64 NearCount      = 1 [json_name = "nearCount"];//即将开始 明天开始
 uint64 DoingCount     = 2 [json_name = "doingCount"];//进行中
 uint64 DoneCount      = 3 [json_name = "doneCount"];//已经完成
 uint64 OverCount      = 4 [json_name = "overCount"];//超时
 uint64 TotalCount     = 5 [json_name = "totalCount"];//总
 uint64 NotStartCount  = 6 [json_name = "notStartCount"];
 uint64 NearEndCount   = 7 [json_name = "nearStartCount"];//即将超时,当天的
}

message DataInfoResponse {
 DataInfo MySubmitInfo    = 1 [json_name = "mySubmitInfo"];   // 我提交的;
 DataInfo DomainInfo    = 2 [json_name = "domainInfo"];   // 全局的;
}

message PassArtWorkRequest {
 uint64 ArtworkId       =1 [json_name = "artworkId"];
}

message JoinUser {
 uint64 UserID       =1 [json_name = "userID"];
 string Name     =2 [json_name = "name"];
 uint64 Status    =3 [json_name = "status"];
}

message Artwork {
 uint64 ArtworkID       =1 [json_name = "artworkID"];
 string Name            =2 [json_name = "name"];
 uint64 Status          =3 [json_name = "status"];
 string ArtistName      =4 [json_name = "ArtistName"];
 string ArtistID       =5 [json_name = "ArtistID"];
 string artistShowCount=6;
}

message TaskRequest {
 uint64 ID = 1 [json_name = "ID"];
 string Domain = 2 [json_name = "domain"];
 string CreatedAt = 4 [json_name = "createdAt"];
 string UpdatedAt = 5 [json_name = "updatedAt"];
 uint64 SubmitterID = 6 [json_name = "submitterID"];
 string SubmitterName = 7 [json_name = "submitterName"];
 uint64 Status = 8 [json_name = "status"];
 repeated JoinUser JoinUsers = 9 [json_name = "joinUsers"];
 repeated Artwork Artworks = 18 [json_name = "artWorks"];
 string StartAt = 10 [json_name = "startAt"];
 string EndAt = 11 [json_name = "endAt"];
 string Type = 12 [json_name = "type"];
 string Content = 13 [json_name = "content"];
 repeated TaskWorkflowRequest TaskWorkflows = 17 [json_name = "taskWorkflow"];
 string OperateTime = 19 [json_name = "operateTime"];
}

message TaskDetail {
 uint64 ID = 1 [json_name = "ID"];
 string Domain = 2 [json_name = "domain"];
}

message TaskResponse {
 uint64 ID = 1 [json_name = "ID"];
 bool Success = 2 [json_name = "success"];
}

message TaskRemove {
 bool Success = 1 [json_name = "success"];

}

message TaskList {
 uint64   Page = 1 [json_name = "page"];
 uint64   PageSize = 2 [json_name = "pageSize"];
 string   Domain = 3 [json_name = "domain"];
 uint64   SubmitterID = 4 [json_name = "submitterID"];//
 uint64   Status = 5 [json_name = "status"];//0-全部 1-未完成(进行中) 2-已经完成 3-超时
 string   Type = 6 [json_name = "type"]; //类型
 repeated uint64 SubmitterIDs = 7 [json_name = "submitterIDs"];//
 string   ArtistName = 8 [json_name = "artistName"]; //画家名称
 string   ArtworkName = 9 [json_name = "artworkName"]; //画作名称
 string   JoinName = 10 [json_name = "joinName"]; //参与人员
}

message TaskWorkflowListResponse {
 uint64 Count = 1 [json_name = "count"];
 repeated TaskWorkflowRequest Data = 2 [json_name = "data"];

}

message TaskWorkflowRequest {
 uint64 ID = 1 [json_name = "ID"];
 uint64 TaskID = 5 [json_name = "taskID"];
 uint64 UserID = 6 [json_name = "userID"];
 string Name = 7 [json_name = "name"];
 uint64 Status = 9 [json_name = "status"];
}

message TaskWorkflowDetail {
 uint64 ID = 1 [json_name = "ID"];
 string Domain = 2 [json_name = "domain"];

}

message TaskWorkflowResponse {
 uint64 ID = 1 [json_name = "ID"];
 bool Success = 2 [json_name = "success"];

}

message TaskWorkflowRemove {
 bool Success = 1 [json_name = "success"];

}

message TaskWorkflowList {
 uint64 Page = 1 [json_name = "page"];
 uint64 PageSize = 2 [json_name = "pageSize"];
}
