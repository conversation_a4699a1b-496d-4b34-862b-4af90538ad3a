// Code generated by protoc-gen-go-triple. DO NOT EDIT.
// versions:
// - protoc-gen-go-triple v1.0.8
// - protoc             v4.24.0--rc1
// source: api/task/task.proto

package task

import (
	context "context"
	protocol "dubbo.apache.org/dubbo-go/v3/protocol"
	dubbo3 "dubbo.apache.org/dubbo-go/v3/protocol/dubbo3"
	invocation "dubbo.apache.org/dubbo-go/v3/protocol/invocation"
	grpc_go "github.com/dubbogo/grpc-go"
	codes "github.com/dubbogo/grpc-go/codes"
	metadata "github.com/dubbogo/grpc-go/metadata"
	status "github.com/dubbogo/grpc-go/status"
	common "github.com/dubbogo/triple/pkg/common"
	constant "github.com/dubbogo/triple/pkg/common/constant"
	triple "github.com/dubbogo/triple/pkg/triple"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc_go.SupportPackageIsVersion7

// TaskClient is the client API for Task service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type TaskClient interface {
	Detail(ctx context.Context, in *TaskDetail, opts ...grpc_go.CallOption) (*TaskRequest, common.ErrorWithAttachment)
	Create(ctx context.Context, in *TaskRequest, opts ...grpc_go.CallOption) (*TaskResponse, common.ErrorWithAttachment)
	Update(ctx context.Context, in *TaskRequest, opts ...grpc_go.CallOption) (*TaskResponse, common.ErrorWithAttachment)
	PassArtWork(ctx context.Context, in *PassArtWorkRequest, opts ...grpc_go.CallOption) (*TaskResponse, common.ErrorWithAttachment)
	SetStatus(ctx context.Context, in *SetStatusRequest, opts ...grpc_go.CallOption) (*TaskResponse, common.ErrorWithAttachment)
	Remove(ctx context.Context, in *TaskDetail, opts ...grpc_go.CallOption) (*TaskRemove, common.ErrorWithAttachment)
	List(ctx context.Context, in *TaskList, opts ...grpc_go.CallOption) (*TaskListResponse, common.ErrorWithAttachment)
	Topping(ctx context.Context, in *TaskDetail, opts ...grpc_go.CallOption) (*TaskRemove, common.ErrorWithAttachment)
	DataInfo(ctx context.Context, in *DataInfoRequest, opts ...grpc_go.CallOption) (*DataInfoResponse, common.ErrorWithAttachment)
	Models(ctx context.Context, in *ModelsRequest, opts ...grpc_go.CallOption) (*ModelsResponse, common.ErrorWithAttachment)
	Model(ctx context.Context, in *ModelRequest, opts ...grpc_go.CallOption) (*ModelSet, common.ErrorWithAttachment)
	RecordImg(ctx context.Context, in *BatchRecordImgRequest, opts ...grpc_go.CallOption) (*CommonResponse, common.ErrorWithAttachment)
	ImageList(ctx context.Context, in *ImageListRequest, opts ...grpc_go.CallOption) (*ImageListResponse, common.ErrorWithAttachment)
	DelRecordImages(ctx context.Context, in *DelRecordImagesRequest, opts ...grpc_go.CallOption) (*CommonResponse, common.ErrorWithAttachment)
}

type taskClient struct {
	cc *triple.TripleConn
}

type TaskClientImpl struct {
	Detail          func(ctx context.Context, in *TaskDetail) (*TaskRequest, error)
	Create          func(ctx context.Context, in *TaskRequest) (*TaskResponse, error)
	Update          func(ctx context.Context, in *TaskRequest) (*TaskResponse, error)
	PassArtWork     func(ctx context.Context, in *PassArtWorkRequest) (*TaskResponse, error)
	SetStatus       func(ctx context.Context, in *SetStatusRequest) (*TaskResponse, error)
	Remove          func(ctx context.Context, in *TaskDetail) (*TaskRemove, error)
	List            func(ctx context.Context, in *TaskList) (*TaskListResponse, error)
	Topping         func(ctx context.Context, in *TaskDetail) (*TaskRemove, error)
	DataInfo        func(ctx context.Context, in *DataInfoRequest) (*DataInfoResponse, error)
	Models          func(ctx context.Context, in *ModelsRequest) (*ModelsResponse, error)
	Model           func(ctx context.Context, in *ModelRequest) (*ModelSet, error)
	RecordImg       func(ctx context.Context, in *BatchRecordImgRequest) (*CommonResponse, error)
	ImageList       func(ctx context.Context, in *ImageListRequest) (*ImageListResponse, error)
	DelRecordImages func(ctx context.Context, in *DelRecordImagesRequest) (*CommonResponse, error)
}

func (c *TaskClientImpl) GetDubboStub(cc *triple.TripleConn) TaskClient {
	return NewTaskClient(cc)
}

func (c *TaskClientImpl) XXX_InterfaceName() string {
	return "task.Task"
}

func NewTaskClient(cc *triple.TripleConn) TaskClient {
	return &taskClient{cc}
}

func (c *taskClient) Detail(ctx context.Context, in *TaskDetail, opts ...grpc_go.CallOption) (*TaskRequest, common.ErrorWithAttachment) {
	out := new(TaskRequest)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/Detail", in, out)
}

func (c *taskClient) Create(ctx context.Context, in *TaskRequest, opts ...grpc_go.CallOption) (*TaskResponse, common.ErrorWithAttachment) {
	out := new(TaskResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/Create", in, out)
}

func (c *taskClient) Update(ctx context.Context, in *TaskRequest, opts ...grpc_go.CallOption) (*TaskResponse, common.ErrorWithAttachment) {
	out := new(TaskResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/Update", in, out)
}

func (c *taskClient) PassArtWork(ctx context.Context, in *PassArtWorkRequest, opts ...grpc_go.CallOption) (*TaskResponse, common.ErrorWithAttachment) {
	out := new(TaskResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/PassArtWork", in, out)
}

func (c *taskClient) SetStatus(ctx context.Context, in *SetStatusRequest, opts ...grpc_go.CallOption) (*TaskResponse, common.ErrorWithAttachment) {
	out := new(TaskResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/SetStatus", in, out)
}

func (c *taskClient) Remove(ctx context.Context, in *TaskDetail, opts ...grpc_go.CallOption) (*TaskRemove, common.ErrorWithAttachment) {
	out := new(TaskRemove)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/Remove", in, out)
}

func (c *taskClient) List(ctx context.Context, in *TaskList, opts ...grpc_go.CallOption) (*TaskListResponse, common.ErrorWithAttachment) {
	out := new(TaskListResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/List", in, out)
}

func (c *taskClient) Topping(ctx context.Context, in *TaskDetail, opts ...grpc_go.CallOption) (*TaskRemove, common.ErrorWithAttachment) {
	out := new(TaskRemove)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/Topping", in, out)
}

func (c *taskClient) DataInfo(ctx context.Context, in *DataInfoRequest, opts ...grpc_go.CallOption) (*DataInfoResponse, common.ErrorWithAttachment) {
	out := new(DataInfoResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/DataInfo", in, out)
}

func (c *taskClient) Models(ctx context.Context, in *ModelsRequest, opts ...grpc_go.CallOption) (*ModelsResponse, common.ErrorWithAttachment) {
	out := new(ModelsResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/Models", in, out)
}

func (c *taskClient) Model(ctx context.Context, in *ModelRequest, opts ...grpc_go.CallOption) (*ModelSet, common.ErrorWithAttachment) {
	out := new(ModelSet)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/Model", in, out)
}

func (c *taskClient) RecordImg(ctx context.Context, in *BatchRecordImgRequest, opts ...grpc_go.CallOption) (*CommonResponse, common.ErrorWithAttachment) {
	out := new(CommonResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/RecordImg", in, out)
}

func (c *taskClient) ImageList(ctx context.Context, in *ImageListRequest, opts ...grpc_go.CallOption) (*ImageListResponse, common.ErrorWithAttachment) {
	out := new(ImageListResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/ImageList", in, out)
}

func (c *taskClient) DelRecordImages(ctx context.Context, in *DelRecordImagesRequest, opts ...grpc_go.CallOption) (*CommonResponse, common.ErrorWithAttachment) {
	out := new(CommonResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/DelRecordImages", in, out)
}

// TaskServer is the server API for Task service.
// All implementations must embed UnimplementedTaskServer
// for forward compatibility
type TaskServer interface {
	Detail(context.Context, *TaskDetail) (*TaskRequest, error)
	Create(context.Context, *TaskRequest) (*TaskResponse, error)
	Update(context.Context, *TaskRequest) (*TaskResponse, error)
	PassArtWork(context.Context, *PassArtWorkRequest) (*TaskResponse, error)
	SetStatus(context.Context, *SetStatusRequest) (*TaskResponse, error)
	Remove(context.Context, *TaskDetail) (*TaskRemove, error)
	List(context.Context, *TaskList) (*TaskListResponse, error)
	Topping(context.Context, *TaskDetail) (*TaskRemove, error)
	DataInfo(context.Context, *DataInfoRequest) (*DataInfoResponse, error)
	Models(context.Context, *ModelsRequest) (*ModelsResponse, error)
	Model(context.Context, *ModelRequest) (*ModelSet, error)
	RecordImg(context.Context, *BatchRecordImgRequest) (*CommonResponse, error)
	ImageList(context.Context, *ImageListRequest) (*ImageListResponse, error)
	DelRecordImages(context.Context, *DelRecordImagesRequest) (*CommonResponse, error)
	mustEmbedUnimplementedTaskServer()
}

// UnimplementedTaskServer must be embedded to have forward compatible implementations.
type UnimplementedTaskServer struct {
	proxyImpl protocol.Invoker
}

func (UnimplementedTaskServer) Detail(context.Context, *TaskDetail) (*TaskRequest, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Detail not implemented")
}
func (UnimplementedTaskServer) Create(context.Context, *TaskRequest) (*TaskResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Create not implemented")
}
func (UnimplementedTaskServer) Update(context.Context, *TaskRequest) (*TaskResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Update not implemented")
}
func (UnimplementedTaskServer) PassArtWork(context.Context, *PassArtWorkRequest) (*TaskResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PassArtWork not implemented")
}
func (UnimplementedTaskServer) SetStatus(context.Context, *SetStatusRequest) (*TaskResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SetStatus not implemented")
}
func (UnimplementedTaskServer) Remove(context.Context, *TaskDetail) (*TaskRemove, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Remove not implemented")
}
func (UnimplementedTaskServer) List(context.Context, *TaskList) (*TaskListResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method List not implemented")
}
func (UnimplementedTaskServer) Topping(context.Context, *TaskDetail) (*TaskRemove, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Topping not implemented")
}
func (UnimplementedTaskServer) DataInfo(context.Context, *DataInfoRequest) (*DataInfoResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DataInfo not implemented")
}
func (UnimplementedTaskServer) Models(context.Context, *ModelsRequest) (*ModelsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Models not implemented")
}
func (UnimplementedTaskServer) Model(context.Context, *ModelRequest) (*ModelSet, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Model not implemented")
}
func (UnimplementedTaskServer) RecordImg(context.Context, *BatchRecordImgRequest) (*CommonResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RecordImg not implemented")
}
func (UnimplementedTaskServer) ImageList(context.Context, *ImageListRequest) (*ImageListResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ImageList not implemented")
}
func (UnimplementedTaskServer) DelRecordImages(context.Context, *DelRecordImagesRequest) (*CommonResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DelRecordImages not implemented")
}
func (s *UnimplementedTaskServer) XXX_SetProxyImpl(impl protocol.Invoker) {
	s.proxyImpl = impl
}

func (s *UnimplementedTaskServer) XXX_GetProxyImpl() protocol.Invoker {
	return s.proxyImpl
}

func (s *UnimplementedTaskServer) XXX_ServiceDesc() *grpc_go.ServiceDesc {
	return &Task_ServiceDesc
}
func (s *UnimplementedTaskServer) XXX_InterfaceName() string {
	return "task.Task"
}

func (UnimplementedTaskServer) mustEmbedUnimplementedTaskServer() {}

// UnsafeTaskServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to TaskServer will
// result in compilation errors.
type UnsafeTaskServer interface {
	mustEmbedUnimplementedTaskServer()
}

func RegisterTaskServer(s grpc_go.ServiceRegistrar, srv TaskServer) {
	s.RegisterService(&Task_ServiceDesc, srv)
}

func _Task_Detail_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(TaskDetail)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("Detail", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Task_Create_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(TaskRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("Create", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Task_Update_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(TaskRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("Update", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Task_PassArtWork_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(PassArtWorkRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("PassArtWork", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Task_SetStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetStatusRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("SetStatus", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Task_Remove_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(TaskDetail)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("Remove", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Task_List_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(TaskList)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("List", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Task_Topping_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(TaskDetail)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("Topping", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Task_DataInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(DataInfoRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("DataInfo", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Task_Models_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(ModelsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("Models", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Task_Model_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(ModelRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("Model", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Task_RecordImg_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchRecordImgRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("RecordImg", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Task_ImageList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(ImageListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("ImageList", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Task_DelRecordImages_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(DelRecordImagesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("DelRecordImages", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

// Task_ServiceDesc is the grpc_go.ServiceDesc for Task service.
// It's only intended for direct use with grpc_go.RegisterService,
// and not to be introspected or modified (even as a copy)
var Task_ServiceDesc = grpc_go.ServiceDesc{
	ServiceName: "task.Task",
	HandlerType: (*TaskServer)(nil),
	Methods: []grpc_go.MethodDesc{
		{
			MethodName: "Detail",
			Handler:    _Task_Detail_Handler,
		},
		{
			MethodName: "Create",
			Handler:    _Task_Create_Handler,
		},
		{
			MethodName: "Update",
			Handler:    _Task_Update_Handler,
		},
		{
			MethodName: "PassArtWork",
			Handler:    _Task_PassArtWork_Handler,
		},
		{
			MethodName: "SetStatus",
			Handler:    _Task_SetStatus_Handler,
		},
		{
			MethodName: "Remove",
			Handler:    _Task_Remove_Handler,
		},
		{
			MethodName: "List",
			Handler:    _Task_List_Handler,
		},
		{
			MethodName: "Topping",
			Handler:    _Task_Topping_Handler,
		},
		{
			MethodName: "DataInfo",
			Handler:    _Task_DataInfo_Handler,
		},
		{
			MethodName: "Models",
			Handler:    _Task_Models_Handler,
		},
		{
			MethodName: "Model",
			Handler:    _Task_Model_Handler,
		},
		{
			MethodName: "RecordImg",
			Handler:    _Task_RecordImg_Handler,
		},
		{
			MethodName: "ImageList",
			Handler:    _Task_ImageList_Handler,
		},
		{
			MethodName: "DelRecordImages",
			Handler:    _Task_DelRecordImages_Handler,
		},
	},
	Streams:  []grpc_go.StreamDesc{},
	Metadata: "api/task/task.proto",
}
