// Code generated by protoc-gen-gogo. DO NOT EDIT.
// source: api/task/task.proto

package task

import (
	fmt "fmt"
	math "math"
	proto "github.com/golang/protobuf/proto"
	github_com_mwitkow_go_proto_validators "github.com/mwitkow/go-proto-validators"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

func (this *ModelRequest) Validate() error {
	return nil
}
func (this *ModelsResponse) Validate() error {
	for _, item := range this.List {
		if item != nil {
			if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(item); err != nil {
				return github_com_mwitkow_go_proto_validators.FieldError("List", err)
			}
		}
	}
	return nil
}
func (this *ModelSet) Validate() error {
	return nil
}
func (this *Model) Validate() error {
	if this.ModelSet != nil {
		if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(this.ModelSet); err != nil {
			return github_com_mwitkow_go_proto_validators.FieldError("ModelSet", err)
		}
	}
	return nil
}
func (this *ModelsRequest) Validate() error {
	return nil
}
func (this *EmptyRequest) Validate() error {
	return nil
}
func (this *DelRecordImagesRequest) Validate() error {
	return nil
}
func (this *CommonResponse) Validate() error {
	return nil
}
func (this *BatchRecordImgRequest) Validate() error {
	return nil
}
func (this *RecordImgRequest) Validate() error {
	return nil
}
func (this *ImageListRequest) Validate() error {
	return nil
}
func (this *ImageListResponse) Validate() error {
	for _, item := range this.List {
		if item != nil {
			if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(item); err != nil {
				return github_com_mwitkow_go_proto_validators.FieldError("List", err)
			}
		}
	}
	return nil
}
func (this *SetStatusRequest) Validate() error {
	return nil
}
func (this *TaskListResponse) Validate() error {
	for _, item := range this.Data {
		if item != nil {
			if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(item); err != nil {
				return github_com_mwitkow_go_proto_validators.FieldError("Data", err)
			}
		}
	}
	return nil
}
func (this *DataInfoRequest) Validate() error {
	return nil
}
func (this *DataInfo) Validate() error {
	return nil
}
func (this *DataInfoResponse) Validate() error {
	if this.MySubmitInfo != nil {
		if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(this.MySubmitInfo); err != nil {
			return github_com_mwitkow_go_proto_validators.FieldError("MySubmitInfo", err)
		}
	}
	if this.DomainInfo != nil {
		if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(this.DomainInfo); err != nil {
			return github_com_mwitkow_go_proto_validators.FieldError("DomainInfo", err)
		}
	}
	return nil
}
func (this *PassArtWorkRequest) Validate() error {
	return nil
}
func (this *JoinUser) Validate() error {
	return nil
}
func (this *Artwork) Validate() error {
	return nil
}
func (this *TaskRequest) Validate() error {
	for _, item := range this.JoinUsers {
		if item != nil {
			if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(item); err != nil {
				return github_com_mwitkow_go_proto_validators.FieldError("JoinUsers", err)
			}
		}
	}
	for _, item := range this.Artworks {
		if item != nil {
			if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(item); err != nil {
				return github_com_mwitkow_go_proto_validators.FieldError("Artworks", err)
			}
		}
	}
	for _, item := range this.TaskWorkflows {
		if item != nil {
			if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(item); err != nil {
				return github_com_mwitkow_go_proto_validators.FieldError("TaskWorkflows", err)
			}
		}
	}
	return nil
}
func (this *TaskDetail) Validate() error {
	return nil
}
func (this *TaskResponse) Validate() error {
	return nil
}
func (this *TaskRemove) Validate() error {
	return nil
}
func (this *TaskList) Validate() error {
	return nil
}
func (this *TaskWorkflowListResponse) Validate() error {
	for _, item := range this.Data {
		if item != nil {
			if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(item); err != nil {
				return github_com_mwitkow_go_proto_validators.FieldError("Data", err)
			}
		}
	}
	return nil
}
func (this *TaskWorkflowRequest) Validate() error {
	return nil
}
func (this *TaskWorkflowDetail) Validate() error {
	return nil
}
func (this *TaskWorkflowResponse) Validate() error {
	return nil
}
func (this *TaskWorkflowRemove) Validate() error {
	return nil
}
func (this *TaskWorkflowList) Validate() error {
	return nil
}
