// Code generated by protoc-gen-gogo. DO NOT EDIT.
// source: api/order/order.proto

package order

import (
	fmt "fmt"
	proto "github.com/golang/protobuf/proto"
	github_com_mwitkow_go_proto_validators "github.com/mwitkow/go-proto-validators"
	math "math"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

func (this *WeeklyCommentResponse) Validate() error {
	for _, item := range this.CommentList {
		if item != nil {
			if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(item); err != nil {
				return github_com_mwitkow_go_proto_validators.FieldError("CommentList", err)
			}
		}
	}
	return nil
}
func (this *GetWeeklyCommentRequest) Validate() error {
	return nil
}
func (this *WeeklyInfoResponse) Validate() error {
	if this.WeeklyInfo != nil {
		if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(this.WeeklyInfo); err != nil {
			return github_com_mwitkow_go_proto_validators.FieldError("WeeklyInfo", err)
		}
	}
	for _, item := range this.CommentList {
		if item != nil {
			if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(item); err != nil {
				return github_com_mwitkow_go_proto_validators.FieldError("CommentList", err)
			}
		}
	}
	return nil
}
func (this *WeeklyCommentRequest) Validate() error {
	return nil
}
func (this *UpdateTransactionDateReq) Validate() error {
	return nil
}
func (this *UpdateTransactionStageReq) Validate() error {
	return nil
}
func (this *GetWipedOutCheckedNumResponse) Validate() error {
	return nil
}
func (this *SetPayCheckRequest) Validate() error {
	return nil
}
func (this *RecordNotifyRequest) Validate() error {
	return nil
}
func (this *WechatPayOkRequest) Validate() error {
	return nil
}
func (this *GetPayByOutTradeNoRequest) Validate() error {
	return nil
}
func (this *GetPayByOutTradeNoResponse) Validate() error {
	return nil
}
func (this *WechatJsApiRefundsResponse) Validate() error {
	return nil
}
func (this *OrderStageSetStatusReq) Validate() error {
	return nil
}
func (this *WechatJsApiPayResponse) Validate() error {
	return nil
}
func (this *WechatAppPayResponse) Validate() error {
	return nil
}
func (this *WechatJsApiQueryByOutTradeNoRequest) Validate() error {
	return nil
}
func (this *WechatJsApiQueryByOutTradeNoResponse) Validate() error {
	return nil
}
func (this *WechatAppQueryByOutTradeNoRequest) Validate() error {
	return nil
}
func (this *WechatAppQueryByOutTradeNoResponse) Validate() error {
	for _, item := range this.PromotionDetail {
		if item != nil {
			if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(item); err != nil {
				return github_com_mwitkow_go_proto_validators.FieldError("PromotionDetail", err)
			}
		}
	}
	if this.Payer != nil {
		if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(this.Payer); err != nil {
			return github_com_mwitkow_go_proto_validators.FieldError("Payer", err)
		}
	}
	if this.Amount != nil {
		if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(this.Amount); err != nil {
			return github_com_mwitkow_go_proto_validators.FieldError("Amount", err)
		}
	}
	return nil
}
func (this *WechatAppQueryByOutTradeNoResponse_Payer) Validate() error {
	return nil
}
func (this *WechatAppQueryByOutTradeNoResponse_Amount) Validate() error {
	return nil
}
func (this *WechatAppQueryByOutTradeNoResponse_PromotionDetail) Validate() error {
	return nil
}
func (this *WechatJsApiPayRequest) Validate() error {
	return nil
}
func (this *AliWapPayRequest) Validate() error {
	return nil
}
func (this *AliWapPayResponse) Validate() error {
	return nil
}
func (this *AliAppPayRequest) Validate() error {
	return nil
}
func (this *AliAppPayResponse) Validate() error {
	return nil
}
func (this *AliNativePayRequest) Validate() error {
	return nil
}
func (this *AliNativePayResponse) Validate() error {
	return nil
}
func (this *AliPcWabPayRequest) Validate() error {
	return nil
}
func (this *AliPcWabPayResponse) Validate() error {
	return nil
}
func (this *AliReFundRequest) Validate() error {
	return nil
}
func (this *AliReFundResponse) Validate() error {
	return nil
}
func (this *AliNotifyRequest) Validate() error {
	return nil
}
func (this *AliNotifyResponse) Validate() error {
	return nil
}
func (this *AliQueryByOutTradeNoRequest) Validate() error {
	return nil
}
func (this *AliQueryByOutTradeNoResponse) Validate() error {
	return nil
}
func (this *AliRefundQueryByOutTradeNoRequest) Validate() error {
	return nil
}
func (this *AliRefundQueryByOutTradeNoResponse) Validate() error {
	return nil
}
func (this *WechatJsApiRefundsResponseOld) Validate() error {
	return nil
}
func (this *WechatNativePayRequest) Validate() error {
	return nil
}
func (this *WechatNativePayResponse) Validate() error {
	return nil
}
func (this *WechatNativeQueryByOutTradeNoRequest) Validate() error {
	return nil
}
func (this *WechatRefundQueryByOutRefundNoRequest) Validate() error {
	return nil
}
func (this *WechatRefundQueryByOutRefundNoResponse) Validate() error {
	return nil
}
func (this *WechatNativeQueryByOutTradeNoResponse) Validate() error {
	return nil
}
func (this *WechatAppPayRequest) Validate() error {
	return nil
}
func (this *WechatJsApiRefundsRequest) Validate() error {
	return nil
}
func (this *WechatH5PayRequest) Validate() error {
	return nil
}
func (this *WechatH5PayResponse) Validate() error {
	return nil
}
func (this *WechatH5QueryByOutTradeNoRequest) Validate() error {
	return nil
}
func (this *WechatH5QueryByOutTradeNoResponse) Validate() error {
	return nil
}
func (this *OrdersByArtworkNumRequest) Validate() error {
	return nil
}
func (this *OrderBaseInfo) Validate() error {
	return nil
}
func (this *OrderBaseList) Validate() error {
	for _, item := range this.Bases {
		if item != nil {
			if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(item); err != nil {
				return github_com_mwitkow_go_proto_validators.FieldError("Bases", err)
			}
		}
	}
	return nil
}
func (this *PayStaticIdResponse) Validate() error {
	return nil
}
func (this *OrderStatisticsInfoRequest) Validate() error {
	return nil
}
func (this *DateInfo) Validate() error {
	return nil
}
func (this *OrderStatisticsInfo) Validate() error {
	for _, item := range this.Infos {
		if item != nil {
			if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(item); err != nil {
				return github_com_mwitkow_go_proto_validators.FieldError("Infos", err)
			}
		}
	}
	return nil
}
func (this *OrderStatisticsInfoResponse) Validate() error {
	for _, item := range this.List {
		if item != nil {
			if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(item); err != nil {
				return github_com_mwitkow_go_proto_validators.FieldError("List", err)
			}
		}
	}
	return nil
}
func (this *PayGiveExistRequest) Validate() error {
	return nil
}
func (this *PaySetCashRequest) Validate() error {
	return nil
}
func (this *PayCreateCacheRequest) Validate() error {
	return nil
}
func (this *PayCronSynFailRequest) Validate() error {
	return nil
}
func (this *PaySeriesDataRequest) Validate() error {
	return nil
}
func (this *PayCollectionDataRequest) Validate() error {
	return nil
}
func (this *PaySynSeriesDataRequest) Validate() error {
	if this.Collection != nil {
		if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(this.Collection); err != nil {
			return github_com_mwitkow_go_proto_validators.FieldError("Collection", err)
		}
	}
	if this.Series != nil {
		if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(this.Series); err != nil {
			return github_com_mwitkow_go_proto_validators.FieldError("Series", err)
		}
	}
	return nil
}
func (this *Collections) Validate() error {
	for _, item := range this.PayInfo {
		if item != nil {
			if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(item); err != nil {
				return github_com_mwitkow_go_proto_validators.FieldError("PayInfo", err)
			}
		}
	}
	return nil
}
func (this *PaySeriesData) Validate() error {
	for _, item := range this.Collections {
		if item != nil {
			if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(item); err != nil {
				return github_com_mwitkow_go_proto_validators.FieldError("Collections", err)
			}
		}
	}
	return nil
}
func (this *PaySeriesDataListResponse) Validate() error {
	for _, item := range this.Data {
		if item != nil {
			if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(item); err != nil {
				return github_com_mwitkow_go_proto_validators.FieldError("Data", err)
			}
		}
	}
	return nil
}
func (this *PaySeriesDataListRequest) Validate() error {
	return nil
}
func (this *PayCollectionsDataListRequest) Validate() error {
	return nil
}
func (this *PayCollectionsDataListResponse) Validate() error {
	if this.SeriesData != nil {
		if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(this.SeriesData); err != nil {
			return github_com_mwitkow_go_proto_validators.FieldError("SeriesData", err)
		}
	}
	for _, item := range this.Data {
		if item != nil {
			if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(item); err != nil {
				return github_com_mwitkow_go_proto_validators.FieldError("Data", err)
			}
		}
	}
	return nil
}
func (this *PaySetExpressRequest) Validate() error {
	if this.Express != nil {
		if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(this.Express); err != nil {
			return github_com_mwitkow_go_proto_validators.FieldError("Express", err)
		}
	}
	return nil
}
func (this *PayDetailRequest) Validate() error {
	return nil
}
func (this *PayListResponse) Validate() error {
	for _, item := range this.Data {
		if item != nil {
			if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(item); err != nil {
				return github_com_mwitkow_go_proto_validators.FieldError("Data", err)
			}
		}
	}
	return nil
}
func (this *PayListRequest) Validate() error {
	return nil
}
func (this *PayFailRequest) Validate() error {
	return nil
}
func (this *PaySellerSureRequest) Validate() error {
	if this.Seller != nil {
		if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(this.Seller); err != nil {
			return github_com_mwitkow_go_proto_validators.FieldError("Seller", err)
		}
	}
	return nil
}
func (this *PayCompleteRequest) Validate() error {
	return nil
}
func (this *PayCreateRequest) Validate() error {
	if this.Express != nil {
		if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(this.Express); err != nil {
			return github_com_mwitkow_go_proto_validators.FieldError("Express", err)
		}
	}
	for _, item := range this.Flows {
		if item != nil {
			if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(item); err != nil {
				return github_com_mwitkow_go_proto_validators.FieldError("Flows", err)
			}
		}
	}
	if this.SellerInfo != nil {
		if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(this.SellerInfo); err != nil {
			return github_com_mwitkow_go_proto_validators.FieldError("SellerInfo", err)
		}
	}
	return nil
}
func (this *Flow) Validate() error {
	return nil
}
func (this *Express) Validate() error {
	return nil
}
func (this *UpDateOrderEntrustsRequest) Validate() error {
	return nil
}
func (this *UpdateEntrustKeysResponse) Validate() error {
	return nil
}
func (this *EntrustList) Validate() error {
	return nil
}
func (this *EntrustListResponse) Validate() error {
	for _, item := range this.Data {
		if item != nil {
			if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(item); err != nil {
				return github_com_mwitkow_go_proto_validators.FieldError("Data", err)
			}
		}
	}
	return nil
}
func (this *EntrustRequest) Validate() error {
	return nil
}
func (this *SellerInfo) Validate() error {
	for _, item := range this.Orders {
		if item != nil {
			if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(item); err != nil {
				return github_com_mwitkow_go_proto_validators.FieldError("Orders", err)
			}
		}
	}
	return nil
}
func (this *SellerResponse) Validate() error {
	for _, item := range this.Data {
		if item != nil {
			if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(item); err != nil {
				return github_com_mwitkow_go_proto_validators.FieldError("Data", err)
			}
		}
	}
	if this.Seller != nil {
		if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(this.Seller); err != nil {
			return github_com_mwitkow_go_proto_validators.FieldError("Seller", err)
		}
	}
	return nil
}
func (this *RandOrderRequest) Validate() error {
	return nil
}
func (this *UpdateSellerId) Validate() error {
	return nil
}
func (this *UpdateSellerIdRequest) Validate() error {
	for _, item := range this.Infos {
		if item != nil {
			if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(item); err != nil {
				return github_com_mwitkow_go_proto_validators.FieldError("Infos", err)
			}
		}
	}
	return nil
}
func (this *BaseInfo) Validate() error {
	return nil
}
func (this *RandOrderResponse) Validate() error {
	for _, item := range this.Orders {
		if item != nil {
			if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(item); err != nil {
				return github_com_mwitkow_go_proto_validators.FieldError("Orders", err)
			}
		}
	}
	return nil
}
func (this *ResultResponse) Validate() error {
	for _, item := range this.Orders {
		if item != nil {
			if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(item); err != nil {
				return github_com_mwitkow_go_proto_validators.FieldError("Orders", err)
			}
		}
	}
	return nil
}
func (this *OrderBase) Validate() error {
	return nil
}
func (this *ResultRequest) Validate() error {
	return nil
}
func (this *ResultsRequest) Validate() error {
	return nil
}
func (this *ResultsResponse) Validate() error {
	for _, item := range this.Data {
		if item != nil {
			if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(item); err != nil {
				return github_com_mwitkow_go_proto_validators.FieldError("Data", err)
			}
		}
	}
	return nil
}
func (this *Staff) Validate() error {
	return nil
}
func (this *ReportCreateRequest) Validate() error {
	for _, item := range this.Staffs {
		if item != nil {
			if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(item); err != nil {
				return github_com_mwitkow_go_proto_validators.FieldError("Staffs", err)
			}
		}
	}
	return nil
}
func (this *BeforeCreateUsersRequest) Validate() error {
	return nil
}
func (this *SetStatusRequest) Validate() error {
	return nil
}
func (this *ReportLogRequest) Validate() error {
	return nil
}
func (this *ReportLogDetail) Validate() error {
	return nil
}
func (this *ReportLogResponse) Validate() error {
	return nil
}
func (this *ReportLogRemove) Validate() error {
	return nil
}
func (this *ReportLogList) Validate() error {
	return nil
}
func (this *ReportLogListResponse) Validate() error {
	for _, item := range this.Data {
		if item != nil {
			if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(item); err != nil {
				return github_com_mwitkow_go_proto_validators.FieldError("Data", err)
			}
		}
	}
	return nil
}
func (this *ReportUserRequest) Validate() error {
	return nil
}
func (this *ReportUserDetail) Validate() error {
	return nil
}
func (this *ReportUserResponse) Validate() error {
	return nil
}
func (this *ReportUserRemove) Validate() error {
	return nil
}
func (this *ReportUserList) Validate() error {
	return nil
}
func (this *ReportUserListResponse) Validate() error {
	for _, item := range this.Data {
		if item != nil {
			if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(item); err != nil {
				return github_com_mwitkow_go_proto_validators.FieldError("Data", err)
			}
		}
	}
	return nil
}
func (this *UpdateReportUserList) Validate() error {
	for _, item := range this.Data {
		if item != nil {
			if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(item); err != nil {
				return github_com_mwitkow_go_proto_validators.FieldError("Data", err)
			}
		}
	}
	return nil
}
func (this *ReportUserLogResponse) Validate() error {
	return nil
}
func (this *ReportUserLogRemove) Validate() error {
	return nil
}
func (this *ReportUserLogList) Validate() error {
	return nil
}
func (this *ReportUserLogListResponse) Validate() error {
	for _, item := range this.Data {
		if item != nil {
			if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(item); err != nil {
				return github_com_mwitkow_go_proto_validators.FieldError("Data", err)
			}
		}
	}
	return nil
}
func (this *ReportUserLogRequest) Validate() error {
	return nil
}
func (this *ReportUserLogDetail) Validate() error {
	return nil
}
func (this *LogRemove) Validate() error {
	return nil
}
func (this *LogList) Validate() error {
	return nil
}
func (this *LogListResponse) Validate() error {
	for _, item := range this.Data {
		if item != nil {
			if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(item); err != nil {
				return github_com_mwitkow_go_proto_validators.FieldError("Data", err)
			}
		}
	}
	return nil
}
func (this *LogRequest) Validate() error {
	return nil
}
func (this *LogDetail) Validate() error {
	return nil
}
func (this *CommonCreateResponse) Validate() error {
	return nil
}
func (this *OrderList) Validate() error {
	return nil
}
func (this *OrderListResponse) Validate() error {
	for _, item := range this.Data {
		if item != nil {
			if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(item); err != nil {
				return github_com_mwitkow_go_proto_validators.FieldError("Data", err)
			}
		}
	}
	return nil
}
func (this *SellerAndOrders) Validate() error {
	for _, item := range this.Data {
		if item != nil {
			if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(item); err != nil {
				return github_com_mwitkow_go_proto_validators.FieldError("Data", err)
			}
		}
	}
	return nil
}
func (this *TransactionsRequest) Validate() error {
	return nil
}
func (this *TransactionsResponse) Validate() error {
	for _, item := range this.Data {
		if item != nil {
			if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(item); err != nil {
				return github_com_mwitkow_go_proto_validators.FieldError("Data", err)
			}
		}
	}
	return nil
}
func (this *StageRequest) Validate() error {
	for _, item := range this.TransactionStages {
		if item != nil {
			if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(item); err != nil {
				return github_com_mwitkow_go_proto_validators.FieldError("TransactionStages", err)
			}
		}
	}
	return nil
}
func (this *Transaction) Validate() error {
	for _, item := range this.Orders {
		if item != nil {
			if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(item); err != nil {
				return github_com_mwitkow_go_proto_validators.FieldError("Orders", err)
			}
		}
	}
	for _, item := range this.TransactionStages {
		if item != nil {
			if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(item); err != nil {
				return github_com_mwitkow_go_proto_validators.FieldError("TransactionStages", err)
			}
		}
	}
	return nil
}
func (this *TransactionStage) Validate() error {
	return nil
}
func (this *OrderRequest) Validate() error {
	if this.Transaction != nil {
		if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(this.Transaction); err != nil {
			return github_com_mwitkow_go_proto_validators.FieldError("Transaction", err)
		}
	}
	for _, item := range this.PayStages {
		if item != nil {
			if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(item); err != nil {
				return github_com_mwitkow_go_proto_validators.FieldError("PayStages", err)
			}
		}
	}
	return nil
}
func (this *PayStage) Validate() error {
	return nil
}
func (this *OrderUpdateRequest) Validate() error {
	return nil
}
func (this *OrderBatchUpdateRequest) Validate() error {
	for _, item := range this.Orders {
		if item != nil {
			if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(item); err != nil {
				return github_com_mwitkow_go_proto_validators.FieldError("Orders", err)
			}
		}
	}
	return nil
}
func (this *CommonRequest) Validate() error {
	return nil
}
func (this *OrderResponse) Validate() error {
	return nil
}
func (this *CommonResponse) Validate() error {
	return nil
}
func (this *OrderRemove) Validate() error {
	return nil
}
func (this *OrderLogRequest) Validate() error {
	return nil
}
func (this *OrderLogDetail) Validate() error {
	return nil
}
func (this *OrderLogResponse) Validate() error {
	return nil
}
func (this *OrderLogRemove) Validate() error {
	return nil
}
func (this *OrderLogList) Validate() error {
	return nil
}
func (this *OrderLogListResponse) Validate() error {
	for _, item := range this.Data {
		if item != nil {
			if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(item); err != nil {
				return github_com_mwitkow_go_proto_validators.FieldError("Data", err)
			}
		}
	}
	return nil
}
func (this *ReportRemove) Validate() error {
	return nil
}
func (this *ReportList) Validate() error {
	return nil
}
func (this *ReportListResponse) Validate() error {
	for _, item := range this.Data {
		if item != nil {
			if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(item); err != nil {
				return github_com_mwitkow_go_proto_validators.FieldError("Data", err)
			}
		}
	}
	return nil
}
func (this *ReportRequest) Validate() error {
	for _, item := range this.ReportUsers {
		if item != nil {
			if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(item); err != nil {
				return github_com_mwitkow_go_proto_validators.FieldError("ReportUsers", err)
			}
		}
	}
	for _, item := range this.ReportReads {
		if item != nil {
			if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(item); err != nil {
				return github_com_mwitkow_go_proto_validators.FieldError("ReportReads", err)
			}
		}
	}
	return nil
}
func (this *ReportReadResponse) Validate() error {
	return nil
}
func (this *ReportReadRequest) Validate() error {
	return nil
}
func (this *ReportDetail) Validate() error {
	return nil
}
func (this *ReportResponse) Validate() error {
	return nil
}
func (this *ReportCheckSales) Validate() error {
	return nil
}
func (this *ResCheckSales) Validate() error {
	return nil
}
func (this *WeeklyRequest) Validate() error {
	return nil
}
func (this *StaffWeeklyReq) Validate() error {
	return nil
}
func (this *WeeklyEveryday) Validate() error {
	return nil
}
func (this *WeeklyResponse) Validate() error {
	if this.Weekly != nil {
		if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(this.Weekly); err != nil {
			return github_com_mwitkow_go_proto_validators.FieldError("Weekly", err)
		}
	}
	return nil
}
func (this *CanWeeklyRequest) Validate() error {
	return nil
}
func (this *WeeklyListRequest) Validate() error {
	return nil
}
func (this *StaffWeeklyListRequest) Validate() error {
	return nil
}
func (this *WeeklyList) Validate() error {
	return nil
}
func (this *WeeklyResponseList) Validate() error {
	for _, item := range this.WeeklyList {
		if item != nil {
			if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(item); err != nil {
				return github_com_mwitkow_go_proto_validators.FieldError("WeeklyList", err)
			}
		}
	}
	return nil
}
func (this *StaffWeeklyResponseList) Validate() error {
	for _, item := range this.WeeklyList {
		if item != nil {
			if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(item); err != nil {
				return github_com_mwitkow_go_proto_validators.FieldError("WeeklyList", err)
			}
		}
	}
	return nil
}
func (this *WeeklyInfoRequest) Validate() error {
	return nil
}
func (this *WeeklyReadRequest) Validate() error {
	return nil
}
func (this *SmsConfigRequest) Validate() error {
	return nil
}
func (this *SmsConfigResponse) Validate() error {
	for _, item := range this.SmsConfig {
		if item != nil {
			if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(item); err != nil {
				return github_com_mwitkow_go_proto_validators.FieldError("SmsConfig", err)
			}
		}
	}
	return nil
}
func (this *SmsConfig) Validate() error {
	return nil
}
func (this *ReportQueryRequest) Validate() error {
	return nil
}
func (this *ReportQueryResponse) Validate() error {
	return nil
}
func (this *BestowRequest) Validate() error {
	return nil
}
func (this *BestowResponse) Validate() error {
	return nil
}
func (this *ReceiveGiftRequest) Validate() error {
	return nil
}
func (this *ReceiveGiftResponse) Validate() error {
	return nil
}
func (this *ApplyBlockchainAddressRequest) Validate() error {
	return nil
}
func (this *ApplyBlockchainAddressResponse) Validate() error {
	return nil
}
func (this *ApplyCertificateRequest) Validate() error {
	return nil
}
func (this *ApplyCertificateResponse) Validate() error {
	return nil
}
func (this *CancelBestowRequest) Validate() error {
	return nil
}
func (this *CancelBestowResponse) Validate() error {
	return nil
}
func (this *GetBestowInfoRequest) Validate() error {
	return nil
}
func (this *GetBestowInfoResponse) Validate() error {
	return nil
}
func (this *InputPersonalBlockchainRequest) Validate() error {
	for _, item := range this.PersonalBlockchainInfo {
		if item != nil {
			if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(item); err != nil {
				return github_com_mwitkow_go_proto_validators.FieldError("PersonalBlockchainInfo", err)
			}
		}
	}
	return nil
}
func (this *PersonalBlockchainInfo) Validate() error {
	return nil
}
func (this *InputPersonalBlockchainResponse) Validate() error {
	return nil
}
func (this *TransactionStageRequest) Validate() error {
	return nil
}
func (this *TransactionStageResponse) Validate() error {
	for _, item := range this.TransactionStage {
		if item != nil {
			if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(item); err != nil {
				return github_com_mwitkow_go_proto_validators.FieldError("TransactionStage", err)
			}
		}
	}
	return nil
}
func (this *BlankOrder) Validate() error {
	return nil
}
func (this *BlankOrders) Validate() error {
	for _, item := range this.Data {
		if item != nil {
			if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(item); err != nil {
				return github_com_mwitkow_go_proto_validators.FieldError("Data", err)
			}
		}
	}
	return nil
}
func (this *ShipAddressCreateRequest) Validate() error {
	return nil
}
func (this *ShipAddressCreateResponse) Validate() error {
	return nil
}
func (this *ShipAddressDeleteRequest) Validate() error {
	return nil
}
func (this *ShipAddressDeleteResponse) Validate() error {
	return nil
}
func (this *ShipAddressListRequest) Validate() error {
	return nil
}
func (this *ShipAddressList) Validate() error {
	return nil
}
func (this *ShipAddressListResponse) Validate() error {
	for _, item := range this.ShipAddressList {
		if item != nil {
			if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(item); err != nil {
				return github_com_mwitkow_go_proto_validators.FieldError("ShipAddressList", err)
			}
		}
	}
	return nil
}
func (this *ShipAddressDefaultRequest) Validate() error {
	return nil
}
func (this *ShipAddressDefaultResponse) Validate() error {
	return nil
}
func (this *GetAddressInfoByIdRequest) Validate() error {
	return nil
}
func (this *GetAddressInfoByIdResponse) Validate() error {
	if this.ShipAddress != nil {
		if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(this.ShipAddress); err != nil {
			return github_com_mwitkow_go_proto_validators.FieldError("ShipAddress", err)
		}
	}
	return nil
}
func (this *LogisticsTrackingRequest) Validate() error {
	return nil
}
func (this *LogisticsTracking) Validate() error {
	return nil
}
func (this *LogisticsTrackingResponse) Validate() error {
	for _, item := range this.LogisticsTracking {
		if item != nil {
			if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(item); err != nil {
				return github_com_mwitkow_go_proto_validators.FieldError("LogisticsTracking", err)
			}
		}
	}
	return nil
}
func (this *AddTrackingNumberRequest) Validate() error {
	return nil
}
func (this *AddTrackingNumberResponse) Validate() error {
	return nil
}
func (this *BlankOrderReq) Validate() error {
	return nil
}
func (this *CreateStripeCheckoutSessionRequest) Validate() error {
	// Validation of proto3 map<> fields is unsupported.
	return nil
}
func (this *CreateStripeCheckoutSessionResponse) Validate() error {
	return nil
}
func (this *CreateStripeRefundRequest) Validate() error {
	return nil
}
func (this *CreateStripeRefundResponse) Validate() error {
	return nil
}
func (this *GetStripePaymentIntentInfoRequest) Validate() error {
	return nil
}
func (this *GetStripePaymentIntentInfoResponse) Validate() error {
	return nil
}
func (this *GetRefundInfoRequest) Validate() error {
	return nil
}
func (this *GetRefundInfoResponse) Validate() error {
	return nil
}
func (this *GetCheckoutWebhookRequest) Validate() error {
	return nil
}
func (this *GetCheckoutWebhookResponse) Validate() error {
	// Validation of proto3 map<> fields is unsupported.
	return nil
}
func (this *QueryStripeInfoRequest) Validate() error {
	return nil
}
func (this *QueryStripeInfoResponse) Validate() error {
	for _, item := range this.StripeInfos {
		if item != nil {
			if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(item); err != nil {
				return github_com_mwitkow_go_proto_validators.FieldError("StripeInfos", err)
			}
		}
	}
	return nil
}
func (this *StripeInfo) Validate() error {
	return nil
}
func (this *CreateStripePaymentIntentRequest) Validate() error {
	// Validation of proto3 map<> fields is unsupported.
	return nil
}
func (this *CreateStripePaymentIntentResponse) Validate() error {
	return nil
}
