// Code generated by protoc-gen-go-triple. DO NOT EDIT.
// versions:
// - protoc-gen-go-triple v1.0.8
// - protoc             v5.26.1
// source: api/order/order.proto

package order

import (
	context "context"
	protocol "dubbo.apache.org/dubbo-go/v3/protocol"
	dubbo3 "dubbo.apache.org/dubbo-go/v3/protocol/dubbo3"
	invocation "dubbo.apache.org/dubbo-go/v3/protocol/invocation"
	grpc_go "github.com/dubbogo/grpc-go"
	codes "github.com/dubbogo/grpc-go/codes"
	metadata "github.com/dubbogo/grpc-go/metadata"
	status "github.com/dubbogo/grpc-go/status"
	common "github.com/dubbogo/triple/pkg/common"
	constant "github.com/dubbogo/triple/pkg/common/constant"
	triple "github.com/dubbogo/triple/pkg/triple"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc_go.SupportPackageIsVersion7

// OrderClient is the client API for Order service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type OrderClient interface {
	// 记录接受数据日志
	CreateLog(ctx context.Context, in *LogRequest, opts ...grpc_go.CallOption) (*CommonCreateResponse, common.ErrorWithAttachment)
	// 订单
	OrderUpdate(ctx context.Context, in *OrderRequest, opts ...grpc_go.CallOption) (*OrderResponse, common.ErrorWithAttachment)
	OrderUpdateByUid(ctx context.Context, in *OrderRequest, opts ...grpc_go.CallOption) (*OrderResponse, common.ErrorWithAttachment)
	OrderUpdateKeys(ctx context.Context, in *OrderRequest, opts ...grpc_go.CallOption) (*OrderResponse, common.ErrorWithAttachment)
	OrderBatchUpdate(ctx context.Context, in *OrderBatchUpdateRequest, opts ...grpc_go.CallOption) (*CommonCreateResponse, common.ErrorWithAttachment)
	OrderExBatchUpdate(ctx context.Context, in *OrderBatchUpdateRequest, opts ...grpc_go.CallOption) (*CommonCreateResponse, common.ErrorWithAttachment)
	OrderDelete(ctx context.Context, in *CommonRequest, opts ...grpc_go.CallOption) (*OrderRemove, common.ErrorWithAttachment)
	GetTransactions(ctx context.Context, in *TransactionsRequest, opts ...grpc_go.CallOption) (*TransactionsResponse, common.ErrorWithAttachment)
	GetTransactionStage(ctx context.Context, in *TransactionStageRequest, opts ...grpc_go.CallOption) (*TransactionStageResponse, common.ErrorWithAttachment)
	GetBlankOrder(ctx context.Context, in *TransactionStageRequest, opts ...grpc_go.CallOption) (*BlankOrders, common.ErrorWithAttachment)
	GetBlankOrderByInfo(ctx context.Context, in *BlankOrderReq, opts ...grpc_go.CallOption) (*BlankOrders, common.ErrorWithAttachment)
	GetSellerTransactions(ctx context.Context, in *TransactionsRequest, opts ...grpc_go.CallOption) (*TransactionsResponse, common.ErrorWithAttachment)
	GetTransaction(ctx context.Context, in *CommonRequest, opts ...grpc_go.CallOption) (*Transaction, common.ErrorWithAttachment)
	SaveTransactionSages(ctx context.Context, in *StageRequest, opts ...grpc_go.CallOption) (*CommonCreateResponse, common.ErrorWithAttachment)
	GetOrders(ctx context.Context, in *OrderList, opts ...grpc_go.CallOption) (*OrderListResponse, common.ErrorWithAttachment)
	GetOrder(ctx context.Context, in *CommonRequest, opts ...grpc_go.CallOption) (*OrderRequest, common.ErrorWithAttachment)
	RandOrders(ctx context.Context, in *RandOrderRequest, opts ...grpc_go.CallOption) (*RandOrderResponse, common.ErrorWithAttachment)
	UpdateOrderSellerId(ctx context.Context, in *UpdateSellerIdRequest, opts ...grpc_go.CallOption) (*CommonCreateResponse, common.ErrorWithAttachment)
	UpDateOrderEntrusts(ctx context.Context, in *RandOrderRequest, opts ...grpc_go.CallOption) (*CommonCreateResponse, common.ErrorWithAttachment)
	OrderCreate(ctx context.Context, in *OrderRequest, opts ...grpc_go.CallOption) (*OrderResponse, common.ErrorWithAttachment)
	SetStatus(ctx context.Context, in *SetStatusRequest, opts ...grpc_go.CallOption) (*OrderResponse, common.ErrorWithAttachment)
	OrderStatisticsInfo(ctx context.Context, in *OrderStatisticsInfoRequest, opts ...grpc_go.CallOption) (*OrderStatisticsInfoResponse, common.ErrorWithAttachment)
	OrdersByArtworkNum(ctx context.Context, in *OrdersByArtworkNumRequest, opts ...grpc_go.CallOption) (*OrderBaseList, common.ErrorWithAttachment)
	OrderStageSetStatus(ctx context.Context, in *OrderStageSetStatusReq, opts ...grpc_go.CallOption) (*CommonResponse, common.ErrorWithAttachment)
	SynTransactionStage(ctx context.Context, in *CommonRequest, opts ...grpc_go.CallOption) (*CommonResponse, common.ErrorWithAttachment)
	UpdateTransactionDate(ctx context.Context, in *UpdateTransactionDateReq, opts ...grpc_go.CallOption) (*CommonResponse, common.ErrorWithAttachment)
	UpdateTransactionStage(ctx context.Context, in *UpdateTransactionStageReq, opts ...grpc_go.CallOption) (*CommonResponse, common.ErrorWithAttachment)
	// 销售的统计数据
	GetReportUsers(ctx context.Context, in *ReportUserList, opts ...grpc_go.CallOption) (*ReportUserListResponse, common.ErrorWithAttachment)
	GetReportUser(ctx context.Context, in *ReportUserDetail, opts ...grpc_go.CallOption) (*ReportUserRequest, common.ErrorWithAttachment)
	// 报表
	GetReport(ctx context.Context, in *ReportDetail, opts ...grpc_go.CallOption) (*ReportRequest, common.ErrorWithAttachment)
	ReportDelete(ctx context.Context, in *ReportDetail, opts ...grpc_go.CallOption) (*CommonCreateResponse, common.ErrorWithAttachment)
	ReportCreate(ctx context.Context, in *ReportCreateRequest, opts ...grpc_go.CallOption) (*ReportResponse, common.ErrorWithAttachment)
	ReportPublic(ctx context.Context, in *ReportDetail, opts ...grpc_go.CallOption) (*ReportLogRemove, common.ErrorWithAttachment)
	GetReports(ctx context.Context, in *ReportList, opts ...grpc_go.CallOption) (*ReportListResponse, common.ErrorWithAttachment)
	BeforeCreateUsers(ctx context.Context, in *ReportCreateRequest, opts ...grpc_go.CallOption) (*ReportUserListResponse, common.ErrorWithAttachment)
	GetCheckSalesAmount(ctx context.Context, in *ReportCheckSales, opts ...grpc_go.CallOption) (*ResCheckSales, common.ErrorWithAttachment)
	SetReportStatus(ctx context.Context, in *SetStatusRequest, opts ...grpc_go.CallOption) (*CommonCreateResponse, common.ErrorWithAttachment)
	UpdateReportUsers(ctx context.Context, in *UpdateReportUserList, opts ...grpc_go.CallOption) (*CommonCreateResponse, common.ErrorWithAttachment)
	GetReportQuery(ctx context.Context, in *ReportQueryRequest, opts ...grpc_go.CallOption) (*ReportQueryResponse, common.ErrorWithAttachment)
	ReportRead(ctx context.Context, in *ReportReadRequest, opts ...grpc_go.CallOption) (*ReportResponse, common.ErrorWithAttachment)
	// 业绩部分
	Result(ctx context.Context, in *ResultRequest, opts ...grpc_go.CallOption) (*SellerResponse, common.ErrorWithAttachment)
	Results(ctx context.Context, in *ResultsRequest, opts ...grpc_go.CallOption) (*ResultsResponse, common.ErrorWithAttachment)
	// 汇报
	CreateWeekly(ctx context.Context, in *WeeklyRequest, opts ...grpc_go.CallOption) (*WeeklyResponse, common.ErrorWithAttachment)
	UpdateWeekly(ctx context.Context, in *WeeklyRequest, opts ...grpc_go.CallOption) (*WeeklyResponse, common.ErrorWithAttachment)
	DetermineCanCreateWeekly(ctx context.Context, in *CanWeeklyRequest, opts ...grpc_go.CallOption) (*WeeklyResponse, common.ErrorWithAttachment)
	WeeklyList(ctx context.Context, in *WeeklyListRequest, opts ...grpc_go.CallOption) (*WeeklyResponseList, common.ErrorWithAttachment)
	WeeklyInfo(ctx context.Context, in *WeeklyInfoRequest, opts ...grpc_go.CallOption) (*WeeklyRequest, common.ErrorWithAttachment)
	WeeklyOaInfo(ctx context.Context, in *WeeklyInfoRequest, opts ...grpc_go.CallOption) (*WeeklyInfoResponse, common.ErrorWithAttachment)
	WeeklyRead(ctx context.Context, in *WeeklyReadRequest, opts ...grpc_go.CallOption) (*WeeklyResponse, common.ErrorWithAttachment)
	WeeklyModifiedNotCommitted(ctx context.Context, in *WeeklyResponse, opts ...grpc_go.CallOption) (*WeeklyResponseList, common.ErrorWithAttachment)
	GetSmsConfig(ctx context.Context, in *SmsConfigRequest, opts ...grpc_go.CallOption) (*SmsConfigResponse, common.ErrorWithAttachment)
	CreateWeeklyComment(ctx context.Context, in *WeeklyCommentRequest, opts ...grpc_go.CallOption) (*ReportResponse, common.ErrorWithAttachment)
	GetWeeklyComment(ctx context.Context, in *GetWeeklyCommentRequest, opts ...grpc_go.CallOption) (*WeeklyCommentResponse, common.ErrorWithAttachment)
	CreateStaffWeekly(ctx context.Context, in *StaffWeeklyReq, opts ...grpc_go.CallOption) (*ReportResponse, common.ErrorWithAttachment)
	UpdateStaffWeekly(ctx context.Context, in *StaffWeeklyReq, opts ...grpc_go.CallOption) (*ReportResponse, common.ErrorWithAttachment)
	DetermineCanStaffWeekly(ctx context.Context, in *CanWeeklyRequest, opts ...grpc_go.CallOption) (*StaffWeeklyReq, common.ErrorWithAttachment)
	StaffWeeklyList(ctx context.Context, in *StaffWeeklyListRequest, opts ...grpc_go.CallOption) (*StaffWeeklyResponseList, common.ErrorWithAttachment)
	StaffWeeklyInfo(ctx context.Context, in *ReportUserDetail, opts ...grpc_go.CallOption) (*StaffWeeklyReq, common.ErrorWithAttachment)
	// 委托业绩
	GetEntrust(ctx context.Context, in *CommonRequest, opts ...grpc_go.CallOption) (*EntrustRequest, common.ErrorWithAttachment)
	EntrustDelete(ctx context.Context, in *CommonRequest, opts ...grpc_go.CallOption) (*CommonCreateResponse, common.ErrorWithAttachment)
	EntrustBatchDelete(ctx context.Context, in *UpDateOrderEntrustsRequest, opts ...grpc_go.CallOption) (*CommonCreateResponse, common.ErrorWithAttachment)
	EntrustCreate(ctx context.Context, in *EntrustRequest, opts ...grpc_go.CallOption) (*CommonCreateResponse, common.ErrorWithAttachment)
	EntrustCreates(ctx context.Context, in *EntrustListResponse, opts ...grpc_go.CallOption) (*UpdateEntrustKeysResponse, common.ErrorWithAttachment)
	GetEntrusts(ctx context.Context, in *EntrustList, opts ...grpc_go.CallOption) (*EntrustListResponse, common.ErrorWithAttachment)
	UpdateEntrustKeys(ctx context.Context, in *EntrustListResponse, opts ...grpc_go.CallOption) (*UpdateEntrustKeysResponse, common.ErrorWithAttachment)
	// 支付订单部分
	PayCreateFirstCache(ctx context.Context, in *PayCreateCacheRequest, opts ...grpc_go.CallOption) (*CommonCreateResponse, common.ErrorWithAttachment)
	PaySetCash(ctx context.Context, in *PaySetCashRequest, opts ...grpc_go.CallOption) (*CommonResponse, common.ErrorWithAttachment)
	PayCreate(ctx context.Context, in *PayCreateRequest, opts ...grpc_go.CallOption) (*CommonCreateResponse, common.ErrorWithAttachment)
	PayComplete(ctx context.Context, in *PayCompleteRequest, opts ...grpc_go.CallOption) (*PayCreateRequest, common.ErrorWithAttachment)
	PaySellerSure(ctx context.Context, in *PaySellerSureRequest, opts ...grpc_go.CallOption) (*PayCreateRequest, common.ErrorWithAttachment)
	PayCancelByOutTradeNo(ctx context.Context, in *PaySellerSureRequest, opts ...grpc_go.CallOption) (*PayCreateRequest, common.ErrorWithAttachment)
	PayInfoByCode(ctx context.Context, in *PaySellerSureRequest, opts ...grpc_go.CallOption) (*PayCreateRequest, common.ErrorWithAttachment)
	PayFail(ctx context.Context, in *PayCompleteRequest, opts ...grpc_go.CallOption) (*CommonCreateResponse, common.ErrorWithAttachment)
	PayDetail(ctx context.Context, in *PayDetailRequest, opts ...grpc_go.CallOption) (*PayCreateRequest, common.ErrorWithAttachment)
	PayDelete(ctx context.Context, in *CommonRequest, opts ...grpc_go.CallOption) (*CommonResponse, common.ErrorWithAttachment)
	PayList(ctx context.Context, in *PayListRequest, opts ...grpc_go.CallOption) (*PayListResponse, common.ErrorWithAttachment)
	PaySetExpress(ctx context.Context, in *PaySetExpressRequest, opts ...grpc_go.CallOption) (*CommonResponse, common.ErrorWithAttachment)
	PaySetExpressRemark(ctx context.Context, in *PaySetExpressRequest, opts ...grpc_go.CallOption) (*CommonResponse, common.ErrorWithAttachment)
	PaySynSeriesData(ctx context.Context, in *PaySynSeriesDataRequest, opts ...grpc_go.CallOption) (*CommonResponse, common.ErrorWithAttachment)
	PaySeriesDataList(ctx context.Context, in *PaySeriesDataListRequest, opts ...grpc_go.CallOption) (*PaySeriesDataListResponse, common.ErrorWithAttachment)
	PayCollectionsDataList(ctx context.Context, in *PayCollectionsDataListRequest, opts ...grpc_go.CallOption) (*PayCollectionsDataListResponse, common.ErrorWithAttachment)
	PayCronSynFail(ctx context.Context, in *PayCronSynFailRequest, opts ...grpc_go.CallOption) (*PayListResponse, common.ErrorWithAttachment)
	PayCronSynSuccess(ctx context.Context, in *PayCronSynFailRequest, opts ...grpc_go.CallOption) (*PayListResponse, common.ErrorWithAttachment)
	PayRefund(ctx context.Context, in *PayDetailRequest, opts ...grpc_go.CallOption) (*PayCreateRequest, common.ErrorWithAttachment)
	PayGiveExist(ctx context.Context, in *PayGiveExistRequest, opts ...grpc_go.CallOption) (*CommonResponse, common.ErrorWithAttachment)
	PayStaticIds(ctx context.Context, in *CommonRequest, opts ...grpc_go.CallOption) (*PayStaticIdResponse, common.ErrorWithAttachment)
	SetPayCheck(ctx context.Context, in *SetPayCheckRequest, opts ...grpc_go.CallOption) (*CommonResponse, common.ErrorWithAttachment)
	GetWipedOutCheckedNum(ctx context.Context, in *CommonRequest, opts ...grpc_go.CallOption) (*GetWipedOutCheckedNumResponse, common.ErrorWithAttachment)
	// 以下是微信jsapi和App和Native支付与退款
	WechatJsApiPay(ctx context.Context, in *WechatJsApiPayRequest, opts ...grpc_go.CallOption) (*WechatJsApiPayResponse, common.ErrorWithAttachment)
	WechatJsApiQueryByOutTradeNo(ctx context.Context, in *WechatJsApiQueryByOutTradeNoRequest, opts ...grpc_go.CallOption) (*WechatJsApiQueryByOutTradeNoResponse, common.ErrorWithAttachment)
	GetPayByOutTradeNo(ctx context.Context, in *GetPayByOutTradeNoRequest, opts ...grpc_go.CallOption) (*GetPayByOutTradeNoResponse, common.ErrorWithAttachment)
	WechatJsApiRefunds(ctx context.Context, in *WechatJsApiRefundsRequest, opts ...grpc_go.CallOption) (*WechatJsApiRefundsResponse, common.ErrorWithAttachment)
	SetPayOk(ctx context.Context, in *WechatPayOkRequest, opts ...grpc_go.CallOption) (*CommonResponse, common.ErrorWithAttachment)
	WechatAppPay(ctx context.Context, in *WechatAppPayRequest, opts ...grpc_go.CallOption) (*WechatAppPayResponse, common.ErrorWithAttachment)
	WechatAppQueryByOutTradeNo(ctx context.Context, in *WechatAppQueryByOutTradeNoRequest, opts ...grpc_go.CallOption) (*WechatAppQueryByOutTradeNoResponse, common.ErrorWithAttachment)
	WechatNativePay(ctx context.Context, in *WechatNativePayRequest, opts ...grpc_go.CallOption) (*WechatNativePayResponse, common.ErrorWithAttachment)
	WechatNativeQueryByOutTradeNo(ctx context.Context, in *WechatNativeQueryByOutTradeNoRequest, opts ...grpc_go.CallOption) (*WechatNativeQueryByOutTradeNoResponse, common.ErrorWithAttachment)
	WechatRefundQueryByOutRefundNo(ctx context.Context, in *WechatRefundQueryByOutRefundNoRequest, opts ...grpc_go.CallOption) (*WechatRefundQueryByOutRefundNoResponse, common.ErrorWithAttachment)
	WechatH5Pay(ctx context.Context, in *WechatH5PayRequest, opts ...grpc_go.CallOption) (*WechatH5PayResponse, common.ErrorWithAttachment)
	WechatH5QueryByOutTradeNo(ctx context.Context, in *WechatH5QueryByOutTradeNoRequest, opts ...grpc_go.CallOption) (*WechatH5QueryByOutTradeNoResponse, common.ErrorWithAttachment)
	// 以下是支付宝网页和App支付与退款
	AliWapPay(ctx context.Context, in *AliWapPayRequest, opts ...grpc_go.CallOption) (*AliWapPayResponse, common.ErrorWithAttachment)
	AliAppPay(ctx context.Context, in *AliAppPayRequest, opts ...grpc_go.CallOption) (*AliAppPayResponse, common.ErrorWithAttachment)
	AliNativePay(ctx context.Context, in *AliNativePayRequest, opts ...grpc_go.CallOption) (*AliNativePayResponse, common.ErrorWithAttachment)
	AliPcWabPay(ctx context.Context, in *AliPcWabPayRequest, opts ...grpc_go.CallOption) (*AliPcWabPayResponse, common.ErrorWithAttachment)
	AliReFund(ctx context.Context, in *AliReFundRequest, opts ...grpc_go.CallOption) (*AliReFundResponse, common.ErrorWithAttachment)
	AliNotify(ctx context.Context, in *AliNotifyRequest, opts ...grpc_go.CallOption) (*AliNotifyResponse, common.ErrorWithAttachment)
	AliQueryByOutTradeNo(ctx context.Context, in *AliQueryByOutTradeNoRequest, opts ...grpc_go.CallOption) (*AliQueryByOutTradeNoResponse, common.ErrorWithAttachment)
	AliRefundQueryByOutTradeNo(ctx context.Context, in *AliRefundQueryByOutTradeNoRequest, opts ...grpc_go.CallOption) (*AliRefundQueryByOutTradeNoResponse, common.ErrorWithAttachment)
	// 以下是商城1.2版本的接口
	Bestow(ctx context.Context, in *BestowRequest, opts ...grpc_go.CallOption) (*BestowResponse, common.ErrorWithAttachment)
	ReceiveGift(ctx context.Context, in *ReceiveGiftRequest, opts ...grpc_go.CallOption) (*ReceiveGiftResponse, common.ErrorWithAttachment)
	ApplyBlockchainAddress(ctx context.Context, in *ApplyBlockchainAddressRequest, opts ...grpc_go.CallOption) (*ApplyBlockchainAddressResponse, common.ErrorWithAttachment)
	ApplyCertificate(ctx context.Context, in *ApplyCertificateRequest, opts ...grpc_go.CallOption) (*ApplyCertificateResponse, common.ErrorWithAttachment)
	CancelBestow(ctx context.Context, in *CancelBestowRequest, opts ...grpc_go.CallOption) (*CancelBestowResponse, common.ErrorWithAttachment)
	GetBestowInfo(ctx context.Context, in *GetBestowInfoRequest, opts ...grpc_go.CallOption) (*GetBestowInfoResponse, common.ErrorWithAttachment)
	InputPersonalBlockchain(ctx context.Context, in *InputPersonalBlockchainRequest, opts ...grpc_go.CallOption) (*InputPersonalBlockchainResponse, common.ErrorWithAttachment)
	ShipAddressCreate(ctx context.Context, in *ShipAddressCreateRequest, opts ...grpc_go.CallOption) (*ShipAddressCreateResponse, common.ErrorWithAttachment)
	ShipAddressDelete(ctx context.Context, in *ShipAddressDeleteRequest, opts ...grpc_go.CallOption) (*ShipAddressDeleteResponse, common.ErrorWithAttachment)
	ShipAddressList(ctx context.Context, in *ShipAddressListRequest, opts ...grpc_go.CallOption) (*ShipAddressListResponse, common.ErrorWithAttachment)
	ShipAddressDefault(ctx context.Context, in *ShipAddressDefaultRequest, opts ...grpc_go.CallOption) (*ShipAddressDefaultResponse, common.ErrorWithAttachment)
	GetAddressInfoById(ctx context.Context, in *GetAddressInfoByIdRequest, opts ...grpc_go.CallOption) (*GetAddressInfoByIdResponse, common.ErrorWithAttachment)
	LogisticsTracking(ctx context.Context, in *LogisticsTrackingRequest, opts ...grpc_go.CallOption) (*LogisticsTrackingResponse, common.ErrorWithAttachment)
	AddTrackingNumber(ctx context.Context, in *AddTrackingNumberRequest, opts ...grpc_go.CallOption) (*AddTrackingNumberResponse, common.ErrorWithAttachment)
	RecordNotify(ctx context.Context, in *RecordNotifyRequest, opts ...grpc_go.CallOption) (*CommonResponse, common.ErrorWithAttachment)
	// 境外支付 stripe
	CreateStripeCheckoutSession(ctx context.Context, in *CreateStripeCheckoutSessionRequest, opts ...grpc_go.CallOption) (*CreateStripeCheckoutSessionResponse, common.ErrorWithAttachment)
	GetStripePaymentIntentInfo(ctx context.Context, in *GetStripePaymentIntentInfoRequest, opts ...grpc_go.CallOption) (*GetStripePaymentIntentInfoResponse, common.ErrorWithAttachment)
	CreateStripeRefund(ctx context.Context, in *CreateStripeRefundRequest, opts ...grpc_go.CallOption) (*CreateStripeRefundResponse, common.ErrorWithAttachment)
	GetRefundInfo(ctx context.Context, in *GetRefundInfoRequest, opts ...grpc_go.CallOption) (*GetRefundInfoResponse, common.ErrorWithAttachment)
	GetCheckoutWebhook(ctx context.Context, in *GetCheckoutWebhookRequest, opts ...grpc_go.CallOption) (*GetCheckoutWebhookResponse, common.ErrorWithAttachment)
	CommonCheckoutWebhook(ctx context.Context, in *GetCheckoutWebhookRequest, opts ...grpc_go.CallOption) (*GetCheckoutWebhookResponse, common.ErrorWithAttachment)
	QueryStripeInfoByCheckSessionIds(ctx context.Context, in *QueryStripeInfoRequest, opts ...grpc_go.CallOption) (*QueryStripeInfoResponse, common.ErrorWithAttachment)
	// stripe改版，用内嵌窗口，自定义前端，不用checkoutsessionid
	CreateStripePaymentIntent(ctx context.Context, in *CreateStripePaymentIntentRequest, opts ...grpc_go.CallOption) (*CreateStripePaymentIntentResponse, common.ErrorWithAttachment)
	CommonStripePayemntIntentWebhook(ctx context.Context, in *GetCheckoutWebhookRequest, opts ...grpc_go.CallOption) (*GetCheckoutWebhookResponse, common.ErrorWithAttachment)
}

type orderClient struct {
	cc *triple.TripleConn
}

type OrderClientImpl struct {
	CreateLog                        func(ctx context.Context, in *LogRequest) (*CommonCreateResponse, error)
	OrderUpdate                      func(ctx context.Context, in *OrderRequest) (*OrderResponse, error)
	OrderUpdateByUid                 func(ctx context.Context, in *OrderRequest) (*OrderResponse, error)
	OrderUpdateKeys                  func(ctx context.Context, in *OrderRequest) (*OrderResponse, error)
	OrderBatchUpdate                 func(ctx context.Context, in *OrderBatchUpdateRequest) (*CommonCreateResponse, error)
	OrderExBatchUpdate               func(ctx context.Context, in *OrderBatchUpdateRequest) (*CommonCreateResponse, error)
	OrderDelete                      func(ctx context.Context, in *CommonRequest) (*OrderRemove, error)
	GetTransactions                  func(ctx context.Context, in *TransactionsRequest) (*TransactionsResponse, error)
	GetTransactionStage              func(ctx context.Context, in *TransactionStageRequest) (*TransactionStageResponse, error)
	GetBlankOrder                    func(ctx context.Context, in *TransactionStageRequest) (*BlankOrders, error)
	GetBlankOrderByInfo              func(ctx context.Context, in *BlankOrderReq) (*BlankOrders, error)
	GetSellerTransactions            func(ctx context.Context, in *TransactionsRequest) (*TransactionsResponse, error)
	GetTransaction                   func(ctx context.Context, in *CommonRequest) (*Transaction, error)
	SaveTransactionSages             func(ctx context.Context, in *StageRequest) (*CommonCreateResponse, error)
	GetOrders                        func(ctx context.Context, in *OrderList) (*OrderListResponse, error)
	GetOrder                         func(ctx context.Context, in *CommonRequest) (*OrderRequest, error)
	RandOrders                       func(ctx context.Context, in *RandOrderRequest) (*RandOrderResponse, error)
	UpdateOrderSellerId              func(ctx context.Context, in *UpdateSellerIdRequest) (*CommonCreateResponse, error)
	UpDateOrderEntrusts              func(ctx context.Context, in *RandOrderRequest) (*CommonCreateResponse, error)
	OrderCreate                      func(ctx context.Context, in *OrderRequest) (*OrderResponse, error)
	SetStatus                        func(ctx context.Context, in *SetStatusRequest) (*OrderResponse, error)
	OrderStatisticsInfo              func(ctx context.Context, in *OrderStatisticsInfoRequest) (*OrderStatisticsInfoResponse, error)
	OrdersByArtworkNum               func(ctx context.Context, in *OrdersByArtworkNumRequest) (*OrderBaseList, error)
	OrderStageSetStatus              func(ctx context.Context, in *OrderStageSetStatusReq) (*CommonResponse, error)
	SynTransactionStage              func(ctx context.Context, in *CommonRequest) (*CommonResponse, error)
	UpdateTransactionDate            func(ctx context.Context, in *UpdateTransactionDateReq) (*CommonResponse, error)
	UpdateTransactionStage           func(ctx context.Context, in *UpdateTransactionStageReq) (*CommonResponse, error)
	GetReportUsers                   func(ctx context.Context, in *ReportUserList) (*ReportUserListResponse, error)
	GetReportUser                    func(ctx context.Context, in *ReportUserDetail) (*ReportUserRequest, error)
	GetReport                        func(ctx context.Context, in *ReportDetail) (*ReportRequest, error)
	ReportDelete                     func(ctx context.Context, in *ReportDetail) (*CommonCreateResponse, error)
	ReportCreate                     func(ctx context.Context, in *ReportCreateRequest) (*ReportResponse, error)
	ReportPublic                     func(ctx context.Context, in *ReportDetail) (*ReportLogRemove, error)
	GetReports                       func(ctx context.Context, in *ReportList) (*ReportListResponse, error)
	BeforeCreateUsers                func(ctx context.Context, in *ReportCreateRequest) (*ReportUserListResponse, error)
	GetCheckSalesAmount              func(ctx context.Context, in *ReportCheckSales) (*ResCheckSales, error)
	SetReportStatus                  func(ctx context.Context, in *SetStatusRequest) (*CommonCreateResponse, error)
	UpdateReportUsers                func(ctx context.Context, in *UpdateReportUserList) (*CommonCreateResponse, error)
	GetReportQuery                   func(ctx context.Context, in *ReportQueryRequest) (*ReportQueryResponse, error)
	ReportRead                       func(ctx context.Context, in *ReportReadRequest) (*ReportResponse, error)
	Result                           func(ctx context.Context, in *ResultRequest) (*SellerResponse, error)
	Results                          func(ctx context.Context, in *ResultsRequest) (*ResultsResponse, error)
	CreateWeekly                     func(ctx context.Context, in *WeeklyRequest) (*WeeklyResponse, error)
	UpdateWeekly                     func(ctx context.Context, in *WeeklyRequest) (*WeeklyResponse, error)
	DetermineCanCreateWeekly         func(ctx context.Context, in *CanWeeklyRequest) (*WeeklyResponse, error)
	WeeklyList                       func(ctx context.Context, in *WeeklyListRequest) (*WeeklyResponseList, error)
	WeeklyInfo                       func(ctx context.Context, in *WeeklyInfoRequest) (*WeeklyRequest, error)
	WeeklyOaInfo                     func(ctx context.Context, in *WeeklyInfoRequest) (*WeeklyInfoResponse, error)
	WeeklyRead                       func(ctx context.Context, in *WeeklyReadRequest) (*WeeklyResponse, error)
	WeeklyModifiedNotCommitted       func(ctx context.Context, in *WeeklyResponse) (*WeeklyResponseList, error)
	GetSmsConfig                     func(ctx context.Context, in *SmsConfigRequest) (*SmsConfigResponse, error)
	CreateWeeklyComment              func(ctx context.Context, in *WeeklyCommentRequest) (*ReportResponse, error)
	GetWeeklyComment                 func(ctx context.Context, in *GetWeeklyCommentRequest) (*WeeklyCommentResponse, error)
	CreateStaffWeekly                func(ctx context.Context, in *StaffWeeklyReq) (*ReportResponse, error)
	UpdateStaffWeekly                func(ctx context.Context, in *StaffWeeklyReq) (*ReportResponse, error)
	DetermineCanStaffWeekly          func(ctx context.Context, in *CanWeeklyRequest) (*StaffWeeklyReq, error)
	StaffWeeklyList                  func(ctx context.Context, in *StaffWeeklyListRequest) (*StaffWeeklyResponseList, error)
	StaffWeeklyInfo                  func(ctx context.Context, in *ReportUserDetail) (*StaffWeeklyReq, error)
	GetEntrust                       func(ctx context.Context, in *CommonRequest) (*EntrustRequest, error)
	EntrustDelete                    func(ctx context.Context, in *CommonRequest) (*CommonCreateResponse, error)
	EntrustBatchDelete               func(ctx context.Context, in *UpDateOrderEntrustsRequest) (*CommonCreateResponse, error)
	EntrustCreate                    func(ctx context.Context, in *EntrustRequest) (*CommonCreateResponse, error)
	EntrustCreates                   func(ctx context.Context, in *EntrustListResponse) (*UpdateEntrustKeysResponse, error)
	GetEntrusts                      func(ctx context.Context, in *EntrustList) (*EntrustListResponse, error)
	UpdateEntrustKeys                func(ctx context.Context, in *EntrustListResponse) (*UpdateEntrustKeysResponse, error)
	PayCreateFirstCache              func(ctx context.Context, in *PayCreateCacheRequest) (*CommonCreateResponse, error)
	PaySetCash                       func(ctx context.Context, in *PaySetCashRequest) (*CommonResponse, error)
	PayCreate                        func(ctx context.Context, in *PayCreateRequest) (*CommonCreateResponse, error)
	PayComplete                      func(ctx context.Context, in *PayCompleteRequest) (*PayCreateRequest, error)
	PaySellerSure                    func(ctx context.Context, in *PaySellerSureRequest) (*PayCreateRequest, error)
	PayCancelByOutTradeNo            func(ctx context.Context, in *PaySellerSureRequest) (*PayCreateRequest, error)
	PayInfoByCode                    func(ctx context.Context, in *PaySellerSureRequest) (*PayCreateRequest, error)
	PayFail                          func(ctx context.Context, in *PayCompleteRequest) (*CommonCreateResponse, error)
	PayDetail                        func(ctx context.Context, in *PayDetailRequest) (*PayCreateRequest, error)
	PayDelete                        func(ctx context.Context, in *CommonRequest) (*CommonResponse, error)
	PayList                          func(ctx context.Context, in *PayListRequest) (*PayListResponse, error)
	PaySetExpress                    func(ctx context.Context, in *PaySetExpressRequest) (*CommonResponse, error)
	PaySetExpressRemark              func(ctx context.Context, in *PaySetExpressRequest) (*CommonResponse, error)
	PaySynSeriesData                 func(ctx context.Context, in *PaySynSeriesDataRequest) (*CommonResponse, error)
	PaySeriesDataList                func(ctx context.Context, in *PaySeriesDataListRequest) (*PaySeriesDataListResponse, error)
	PayCollectionsDataList           func(ctx context.Context, in *PayCollectionsDataListRequest) (*PayCollectionsDataListResponse, error)
	PayCronSynFail                   func(ctx context.Context, in *PayCronSynFailRequest) (*PayListResponse, error)
	PayCronSynSuccess                func(ctx context.Context, in *PayCronSynFailRequest) (*PayListResponse, error)
	PayRefund                        func(ctx context.Context, in *PayDetailRequest) (*PayCreateRequest, error)
	PayGiveExist                     func(ctx context.Context, in *PayGiveExistRequest) (*CommonResponse, error)
	PayStaticIds                     func(ctx context.Context, in *CommonRequest) (*PayStaticIdResponse, error)
	SetPayCheck                      func(ctx context.Context, in *SetPayCheckRequest) (*CommonResponse, error)
	GetWipedOutCheckedNum            func(ctx context.Context, in *CommonRequest) (*GetWipedOutCheckedNumResponse, error)
	WechatJsApiPay                   func(ctx context.Context, in *WechatJsApiPayRequest) (*WechatJsApiPayResponse, error)
	WechatJsApiQueryByOutTradeNo     func(ctx context.Context, in *WechatJsApiQueryByOutTradeNoRequest) (*WechatJsApiQueryByOutTradeNoResponse, error)
	GetPayByOutTradeNo               func(ctx context.Context, in *GetPayByOutTradeNoRequest) (*GetPayByOutTradeNoResponse, error)
	WechatJsApiRefunds               func(ctx context.Context, in *WechatJsApiRefundsRequest) (*WechatJsApiRefundsResponse, error)
	SetPayOk                         func(ctx context.Context, in *WechatPayOkRequest) (*CommonResponse, error)
	WechatAppPay                     func(ctx context.Context, in *WechatAppPayRequest) (*WechatAppPayResponse, error)
	WechatAppQueryByOutTradeNo       func(ctx context.Context, in *WechatAppQueryByOutTradeNoRequest) (*WechatAppQueryByOutTradeNoResponse, error)
	WechatNativePay                  func(ctx context.Context, in *WechatNativePayRequest) (*WechatNativePayResponse, error)
	WechatNativeQueryByOutTradeNo    func(ctx context.Context, in *WechatNativeQueryByOutTradeNoRequest) (*WechatNativeQueryByOutTradeNoResponse, error)
	WechatRefundQueryByOutRefundNo   func(ctx context.Context, in *WechatRefundQueryByOutRefundNoRequest) (*WechatRefundQueryByOutRefundNoResponse, error)
	WechatH5Pay                      func(ctx context.Context, in *WechatH5PayRequest) (*WechatH5PayResponse, error)
	WechatH5QueryByOutTradeNo        func(ctx context.Context, in *WechatH5QueryByOutTradeNoRequest) (*WechatH5QueryByOutTradeNoResponse, error)
	AliWapPay                        func(ctx context.Context, in *AliWapPayRequest) (*AliWapPayResponse, error)
	AliAppPay                        func(ctx context.Context, in *AliAppPayRequest) (*AliAppPayResponse, error)
	AliNativePay                     func(ctx context.Context, in *AliNativePayRequest) (*AliNativePayResponse, error)
	AliPcWabPay                      func(ctx context.Context, in *AliPcWabPayRequest) (*AliPcWabPayResponse, error)
	AliReFund                        func(ctx context.Context, in *AliReFundRequest) (*AliReFundResponse, error)
	AliNotify                        func(ctx context.Context, in *AliNotifyRequest) (*AliNotifyResponse, error)
	AliQueryByOutTradeNo             func(ctx context.Context, in *AliQueryByOutTradeNoRequest) (*AliQueryByOutTradeNoResponse, error)
	AliRefundQueryByOutTradeNo       func(ctx context.Context, in *AliRefundQueryByOutTradeNoRequest) (*AliRefundQueryByOutTradeNoResponse, error)
	Bestow                           func(ctx context.Context, in *BestowRequest) (*BestowResponse, error)
	ReceiveGift                      func(ctx context.Context, in *ReceiveGiftRequest) (*ReceiveGiftResponse, error)
	ApplyBlockchainAddress           func(ctx context.Context, in *ApplyBlockchainAddressRequest) (*ApplyBlockchainAddressResponse, error)
	ApplyCertificate                 func(ctx context.Context, in *ApplyCertificateRequest) (*ApplyCertificateResponse, error)
	CancelBestow                     func(ctx context.Context, in *CancelBestowRequest) (*CancelBestowResponse, error)
	GetBestowInfo                    func(ctx context.Context, in *GetBestowInfoRequest) (*GetBestowInfoResponse, error)
	InputPersonalBlockchain          func(ctx context.Context, in *InputPersonalBlockchainRequest) (*InputPersonalBlockchainResponse, error)
	ShipAddressCreate                func(ctx context.Context, in *ShipAddressCreateRequest) (*ShipAddressCreateResponse, error)
	ShipAddressDelete                func(ctx context.Context, in *ShipAddressDeleteRequest) (*ShipAddressDeleteResponse, error)
	ShipAddressList                  func(ctx context.Context, in *ShipAddressListRequest) (*ShipAddressListResponse, error)
	ShipAddressDefault               func(ctx context.Context, in *ShipAddressDefaultRequest) (*ShipAddressDefaultResponse, error)
	GetAddressInfoById               func(ctx context.Context, in *GetAddressInfoByIdRequest) (*GetAddressInfoByIdResponse, error)
	LogisticsTracking                func(ctx context.Context, in *LogisticsTrackingRequest) (*LogisticsTrackingResponse, error)
	AddTrackingNumber                func(ctx context.Context, in *AddTrackingNumberRequest) (*AddTrackingNumberResponse, error)
	RecordNotify                     func(ctx context.Context, in *RecordNotifyRequest) (*CommonResponse, error)
	CreateStripeCheckoutSession      func(ctx context.Context, in *CreateStripeCheckoutSessionRequest) (*CreateStripeCheckoutSessionResponse, error)
	GetStripePaymentIntentInfo       func(ctx context.Context, in *GetStripePaymentIntentInfoRequest) (*GetStripePaymentIntentInfoResponse, error)
	CreateStripeRefund               func(ctx context.Context, in *CreateStripeRefundRequest) (*CreateStripeRefundResponse, error)
	GetRefundInfo                    func(ctx context.Context, in *GetRefundInfoRequest) (*GetRefundInfoResponse, error)
	GetCheckoutWebhook               func(ctx context.Context, in *GetCheckoutWebhookRequest) (*GetCheckoutWebhookResponse, error)
	CommonCheckoutWebhook            func(ctx context.Context, in *GetCheckoutWebhookRequest) (*GetCheckoutWebhookResponse, error)
	QueryStripeInfoByCheckSessionIds func(ctx context.Context, in *QueryStripeInfoRequest) (*QueryStripeInfoResponse, error)
	CreateStripePaymentIntent        func(ctx context.Context, in *CreateStripePaymentIntentRequest) (*CreateStripePaymentIntentResponse, error)
	CommonStripePayemntIntentWebhook func(ctx context.Context, in *GetCheckoutWebhookRequest) (*GetCheckoutWebhookResponse, error)
}

func (c *OrderClientImpl) GetDubboStub(cc *triple.TripleConn) OrderClient {
	return NewOrderClient(cc)
}

func (c *OrderClientImpl) XXX_InterfaceName() string {
	return "order.Order"
}

func NewOrderClient(cc *triple.TripleConn) OrderClient {
	return &orderClient{cc}
}

func (c *orderClient) CreateLog(ctx context.Context, in *LogRequest, opts ...grpc_go.CallOption) (*CommonCreateResponse, common.ErrorWithAttachment) {
	out := new(CommonCreateResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/CreateLog", in, out)
}

func (c *orderClient) OrderUpdate(ctx context.Context, in *OrderRequest, opts ...grpc_go.CallOption) (*OrderResponse, common.ErrorWithAttachment) {
	out := new(OrderResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/OrderUpdate", in, out)
}

func (c *orderClient) OrderUpdateByUid(ctx context.Context, in *OrderRequest, opts ...grpc_go.CallOption) (*OrderResponse, common.ErrorWithAttachment) {
	out := new(OrderResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/OrderUpdateByUid", in, out)
}

func (c *orderClient) OrderUpdateKeys(ctx context.Context, in *OrderRequest, opts ...grpc_go.CallOption) (*OrderResponse, common.ErrorWithAttachment) {
	out := new(OrderResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/OrderUpdateKeys", in, out)
}

func (c *orderClient) OrderBatchUpdate(ctx context.Context, in *OrderBatchUpdateRequest, opts ...grpc_go.CallOption) (*CommonCreateResponse, common.ErrorWithAttachment) {
	out := new(CommonCreateResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/OrderBatchUpdate", in, out)
}

func (c *orderClient) OrderExBatchUpdate(ctx context.Context, in *OrderBatchUpdateRequest, opts ...grpc_go.CallOption) (*CommonCreateResponse, common.ErrorWithAttachment) {
	out := new(CommonCreateResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/OrderExBatchUpdate", in, out)
}

func (c *orderClient) OrderDelete(ctx context.Context, in *CommonRequest, opts ...grpc_go.CallOption) (*OrderRemove, common.ErrorWithAttachment) {
	out := new(OrderRemove)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/OrderDelete", in, out)
}

func (c *orderClient) GetTransactions(ctx context.Context, in *TransactionsRequest, opts ...grpc_go.CallOption) (*TransactionsResponse, common.ErrorWithAttachment) {
	out := new(TransactionsResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/GetTransactions", in, out)
}

func (c *orderClient) GetTransactionStage(ctx context.Context, in *TransactionStageRequest, opts ...grpc_go.CallOption) (*TransactionStageResponse, common.ErrorWithAttachment) {
	out := new(TransactionStageResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/GetTransactionStage", in, out)
}

func (c *orderClient) GetBlankOrder(ctx context.Context, in *TransactionStageRequest, opts ...grpc_go.CallOption) (*BlankOrders, common.ErrorWithAttachment) {
	out := new(BlankOrders)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/GetBlankOrder", in, out)
}

func (c *orderClient) GetBlankOrderByInfo(ctx context.Context, in *BlankOrderReq, opts ...grpc_go.CallOption) (*BlankOrders, common.ErrorWithAttachment) {
	out := new(BlankOrders)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/GetBlankOrderByInfo", in, out)
}

func (c *orderClient) GetSellerTransactions(ctx context.Context, in *TransactionsRequest, opts ...grpc_go.CallOption) (*TransactionsResponse, common.ErrorWithAttachment) {
	out := new(TransactionsResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/GetSellerTransactions", in, out)
}

func (c *orderClient) GetTransaction(ctx context.Context, in *CommonRequest, opts ...grpc_go.CallOption) (*Transaction, common.ErrorWithAttachment) {
	out := new(Transaction)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/GetTransaction", in, out)
}

func (c *orderClient) SaveTransactionSages(ctx context.Context, in *StageRequest, opts ...grpc_go.CallOption) (*CommonCreateResponse, common.ErrorWithAttachment) {
	out := new(CommonCreateResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/SaveTransactionSages", in, out)
}

func (c *orderClient) GetOrders(ctx context.Context, in *OrderList, opts ...grpc_go.CallOption) (*OrderListResponse, common.ErrorWithAttachment) {
	out := new(OrderListResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/GetOrders", in, out)
}

func (c *orderClient) GetOrder(ctx context.Context, in *CommonRequest, opts ...grpc_go.CallOption) (*OrderRequest, common.ErrorWithAttachment) {
	out := new(OrderRequest)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/GetOrder", in, out)
}

func (c *orderClient) RandOrders(ctx context.Context, in *RandOrderRequest, opts ...grpc_go.CallOption) (*RandOrderResponse, common.ErrorWithAttachment) {
	out := new(RandOrderResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/RandOrders", in, out)
}

func (c *orderClient) UpdateOrderSellerId(ctx context.Context, in *UpdateSellerIdRequest, opts ...grpc_go.CallOption) (*CommonCreateResponse, common.ErrorWithAttachment) {
	out := new(CommonCreateResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/UpdateOrderSellerId", in, out)
}

func (c *orderClient) UpDateOrderEntrusts(ctx context.Context, in *RandOrderRequest, opts ...grpc_go.CallOption) (*CommonCreateResponse, common.ErrorWithAttachment) {
	out := new(CommonCreateResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/UpDateOrderEntrusts", in, out)
}

func (c *orderClient) OrderCreate(ctx context.Context, in *OrderRequest, opts ...grpc_go.CallOption) (*OrderResponse, common.ErrorWithAttachment) {
	out := new(OrderResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/OrderCreate", in, out)
}

func (c *orderClient) SetStatus(ctx context.Context, in *SetStatusRequest, opts ...grpc_go.CallOption) (*OrderResponse, common.ErrorWithAttachment) {
	out := new(OrderResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/SetStatus", in, out)
}

func (c *orderClient) OrderStatisticsInfo(ctx context.Context, in *OrderStatisticsInfoRequest, opts ...grpc_go.CallOption) (*OrderStatisticsInfoResponse, common.ErrorWithAttachment) {
	out := new(OrderStatisticsInfoResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/OrderStatisticsInfo", in, out)
}

func (c *orderClient) OrdersByArtworkNum(ctx context.Context, in *OrdersByArtworkNumRequest, opts ...grpc_go.CallOption) (*OrderBaseList, common.ErrorWithAttachment) {
	out := new(OrderBaseList)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/OrdersByArtworkNum", in, out)
}

func (c *orderClient) OrderStageSetStatus(ctx context.Context, in *OrderStageSetStatusReq, opts ...grpc_go.CallOption) (*CommonResponse, common.ErrorWithAttachment) {
	out := new(CommonResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/OrderStageSetStatus", in, out)
}

func (c *orderClient) SynTransactionStage(ctx context.Context, in *CommonRequest, opts ...grpc_go.CallOption) (*CommonResponse, common.ErrorWithAttachment) {
	out := new(CommonResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/SynTransactionStage", in, out)
}

func (c *orderClient) UpdateTransactionDate(ctx context.Context, in *UpdateTransactionDateReq, opts ...grpc_go.CallOption) (*CommonResponse, common.ErrorWithAttachment) {
	out := new(CommonResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/UpdateTransactionDate", in, out)
}

func (c *orderClient) UpdateTransactionStage(ctx context.Context, in *UpdateTransactionStageReq, opts ...grpc_go.CallOption) (*CommonResponse, common.ErrorWithAttachment) {
	out := new(CommonResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/UpdateTransactionStage", in, out)
}

func (c *orderClient) GetReportUsers(ctx context.Context, in *ReportUserList, opts ...grpc_go.CallOption) (*ReportUserListResponse, common.ErrorWithAttachment) {
	out := new(ReportUserListResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/GetReportUsers", in, out)
}

func (c *orderClient) GetReportUser(ctx context.Context, in *ReportUserDetail, opts ...grpc_go.CallOption) (*ReportUserRequest, common.ErrorWithAttachment) {
	out := new(ReportUserRequest)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/GetReportUser", in, out)
}

func (c *orderClient) GetReport(ctx context.Context, in *ReportDetail, opts ...grpc_go.CallOption) (*ReportRequest, common.ErrorWithAttachment) {
	out := new(ReportRequest)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/GetReport", in, out)
}

func (c *orderClient) ReportDelete(ctx context.Context, in *ReportDetail, opts ...grpc_go.CallOption) (*CommonCreateResponse, common.ErrorWithAttachment) {
	out := new(CommonCreateResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/ReportDelete", in, out)
}

func (c *orderClient) ReportCreate(ctx context.Context, in *ReportCreateRequest, opts ...grpc_go.CallOption) (*ReportResponse, common.ErrorWithAttachment) {
	out := new(ReportResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/ReportCreate", in, out)
}

func (c *orderClient) ReportPublic(ctx context.Context, in *ReportDetail, opts ...grpc_go.CallOption) (*ReportLogRemove, common.ErrorWithAttachment) {
	out := new(ReportLogRemove)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/ReportPublic", in, out)
}

func (c *orderClient) GetReports(ctx context.Context, in *ReportList, opts ...grpc_go.CallOption) (*ReportListResponse, common.ErrorWithAttachment) {
	out := new(ReportListResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/GetReports", in, out)
}

func (c *orderClient) BeforeCreateUsers(ctx context.Context, in *ReportCreateRequest, opts ...grpc_go.CallOption) (*ReportUserListResponse, common.ErrorWithAttachment) {
	out := new(ReportUserListResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/BeforeCreateUsers", in, out)
}

func (c *orderClient) GetCheckSalesAmount(ctx context.Context, in *ReportCheckSales, opts ...grpc_go.CallOption) (*ResCheckSales, common.ErrorWithAttachment) {
	out := new(ResCheckSales)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/GetCheckSalesAmount", in, out)
}

func (c *orderClient) SetReportStatus(ctx context.Context, in *SetStatusRequest, opts ...grpc_go.CallOption) (*CommonCreateResponse, common.ErrorWithAttachment) {
	out := new(CommonCreateResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/SetReportStatus", in, out)
}

func (c *orderClient) UpdateReportUsers(ctx context.Context, in *UpdateReportUserList, opts ...grpc_go.CallOption) (*CommonCreateResponse, common.ErrorWithAttachment) {
	out := new(CommonCreateResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/UpdateReportUsers", in, out)
}

func (c *orderClient) GetReportQuery(ctx context.Context, in *ReportQueryRequest, opts ...grpc_go.CallOption) (*ReportQueryResponse, common.ErrorWithAttachment) {
	out := new(ReportQueryResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/GetReportQuery", in, out)
}

func (c *orderClient) ReportRead(ctx context.Context, in *ReportReadRequest, opts ...grpc_go.CallOption) (*ReportResponse, common.ErrorWithAttachment) {
	out := new(ReportResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/ReportRead", in, out)
}

func (c *orderClient) Result(ctx context.Context, in *ResultRequest, opts ...grpc_go.CallOption) (*SellerResponse, common.ErrorWithAttachment) {
	out := new(SellerResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/Result", in, out)
}

func (c *orderClient) Results(ctx context.Context, in *ResultsRequest, opts ...grpc_go.CallOption) (*ResultsResponse, common.ErrorWithAttachment) {
	out := new(ResultsResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/Results", in, out)
}

func (c *orderClient) CreateWeekly(ctx context.Context, in *WeeklyRequest, opts ...grpc_go.CallOption) (*WeeklyResponse, common.ErrorWithAttachment) {
	out := new(WeeklyResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/CreateWeekly", in, out)
}

func (c *orderClient) UpdateWeekly(ctx context.Context, in *WeeklyRequest, opts ...grpc_go.CallOption) (*WeeklyResponse, common.ErrorWithAttachment) {
	out := new(WeeklyResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/UpdateWeekly", in, out)
}

func (c *orderClient) DetermineCanCreateWeekly(ctx context.Context, in *CanWeeklyRequest, opts ...grpc_go.CallOption) (*WeeklyResponse, common.ErrorWithAttachment) {
	out := new(WeeklyResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/DetermineCanCreateWeekly", in, out)
}

func (c *orderClient) WeeklyList(ctx context.Context, in *WeeklyListRequest, opts ...grpc_go.CallOption) (*WeeklyResponseList, common.ErrorWithAttachment) {
	out := new(WeeklyResponseList)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/WeeklyList", in, out)
}

func (c *orderClient) WeeklyInfo(ctx context.Context, in *WeeklyInfoRequest, opts ...grpc_go.CallOption) (*WeeklyRequest, common.ErrorWithAttachment) {
	out := new(WeeklyRequest)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/WeeklyInfo", in, out)
}

func (c *orderClient) WeeklyOaInfo(ctx context.Context, in *WeeklyInfoRequest, opts ...grpc_go.CallOption) (*WeeklyInfoResponse, common.ErrorWithAttachment) {
	out := new(WeeklyInfoResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/WeeklyOaInfo", in, out)
}

func (c *orderClient) WeeklyRead(ctx context.Context, in *WeeklyReadRequest, opts ...grpc_go.CallOption) (*WeeklyResponse, common.ErrorWithAttachment) {
	out := new(WeeklyResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/WeeklyRead", in, out)
}

func (c *orderClient) WeeklyModifiedNotCommitted(ctx context.Context, in *WeeklyResponse, opts ...grpc_go.CallOption) (*WeeklyResponseList, common.ErrorWithAttachment) {
	out := new(WeeklyResponseList)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/WeeklyModifiedNotCommitted", in, out)
}

func (c *orderClient) GetSmsConfig(ctx context.Context, in *SmsConfigRequest, opts ...grpc_go.CallOption) (*SmsConfigResponse, common.ErrorWithAttachment) {
	out := new(SmsConfigResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/GetSmsConfig", in, out)
}

func (c *orderClient) CreateWeeklyComment(ctx context.Context, in *WeeklyCommentRequest, opts ...grpc_go.CallOption) (*ReportResponse, common.ErrorWithAttachment) {
	out := new(ReportResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/CreateWeeklyComment", in, out)
}

func (c *orderClient) GetWeeklyComment(ctx context.Context, in *GetWeeklyCommentRequest, opts ...grpc_go.CallOption) (*WeeklyCommentResponse, common.ErrorWithAttachment) {
	out := new(WeeklyCommentResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/GetWeeklyComment", in, out)
}

func (c *orderClient) CreateStaffWeekly(ctx context.Context, in *StaffWeeklyReq, opts ...grpc_go.CallOption) (*ReportResponse, common.ErrorWithAttachment) {
	out := new(ReportResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/CreateStaffWeekly", in, out)
}

func (c *orderClient) UpdateStaffWeekly(ctx context.Context, in *StaffWeeklyReq, opts ...grpc_go.CallOption) (*ReportResponse, common.ErrorWithAttachment) {
	out := new(ReportResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/UpdateStaffWeekly", in, out)
}

func (c *orderClient) DetermineCanStaffWeekly(ctx context.Context, in *CanWeeklyRequest, opts ...grpc_go.CallOption) (*StaffWeeklyReq, common.ErrorWithAttachment) {
	out := new(StaffWeeklyReq)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/DetermineCanStaffWeekly", in, out)
}

func (c *orderClient) StaffWeeklyList(ctx context.Context, in *StaffWeeklyListRequest, opts ...grpc_go.CallOption) (*StaffWeeklyResponseList, common.ErrorWithAttachment) {
	out := new(StaffWeeklyResponseList)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/StaffWeeklyList", in, out)
}

func (c *orderClient) StaffWeeklyInfo(ctx context.Context, in *ReportUserDetail, opts ...grpc_go.CallOption) (*StaffWeeklyReq, common.ErrorWithAttachment) {
	out := new(StaffWeeklyReq)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/StaffWeeklyInfo", in, out)
}

func (c *orderClient) GetEntrust(ctx context.Context, in *CommonRequest, opts ...grpc_go.CallOption) (*EntrustRequest, common.ErrorWithAttachment) {
	out := new(EntrustRequest)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/GetEntrust", in, out)
}

func (c *orderClient) EntrustDelete(ctx context.Context, in *CommonRequest, opts ...grpc_go.CallOption) (*CommonCreateResponse, common.ErrorWithAttachment) {
	out := new(CommonCreateResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/EntrustDelete", in, out)
}

func (c *orderClient) EntrustBatchDelete(ctx context.Context, in *UpDateOrderEntrustsRequest, opts ...grpc_go.CallOption) (*CommonCreateResponse, common.ErrorWithAttachment) {
	out := new(CommonCreateResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/EntrustBatchDelete", in, out)
}

func (c *orderClient) EntrustCreate(ctx context.Context, in *EntrustRequest, opts ...grpc_go.CallOption) (*CommonCreateResponse, common.ErrorWithAttachment) {
	out := new(CommonCreateResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/EntrustCreate", in, out)
}

func (c *orderClient) EntrustCreates(ctx context.Context, in *EntrustListResponse, opts ...grpc_go.CallOption) (*UpdateEntrustKeysResponse, common.ErrorWithAttachment) {
	out := new(UpdateEntrustKeysResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/EntrustCreates", in, out)
}

func (c *orderClient) GetEntrusts(ctx context.Context, in *EntrustList, opts ...grpc_go.CallOption) (*EntrustListResponse, common.ErrorWithAttachment) {
	out := new(EntrustListResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/GetEntrusts", in, out)
}

func (c *orderClient) UpdateEntrustKeys(ctx context.Context, in *EntrustListResponse, opts ...grpc_go.CallOption) (*UpdateEntrustKeysResponse, common.ErrorWithAttachment) {
	out := new(UpdateEntrustKeysResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/UpdateEntrustKeys", in, out)
}

func (c *orderClient) PayCreateFirstCache(ctx context.Context, in *PayCreateCacheRequest, opts ...grpc_go.CallOption) (*CommonCreateResponse, common.ErrorWithAttachment) {
	out := new(CommonCreateResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/PayCreateFirstCache", in, out)
}

func (c *orderClient) PaySetCash(ctx context.Context, in *PaySetCashRequest, opts ...grpc_go.CallOption) (*CommonResponse, common.ErrorWithAttachment) {
	out := new(CommonResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/PaySetCash", in, out)
}

func (c *orderClient) PayCreate(ctx context.Context, in *PayCreateRequest, opts ...grpc_go.CallOption) (*CommonCreateResponse, common.ErrorWithAttachment) {
	out := new(CommonCreateResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/PayCreate", in, out)
}

func (c *orderClient) PayComplete(ctx context.Context, in *PayCompleteRequest, opts ...grpc_go.CallOption) (*PayCreateRequest, common.ErrorWithAttachment) {
	out := new(PayCreateRequest)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/PayComplete", in, out)
}

func (c *orderClient) PaySellerSure(ctx context.Context, in *PaySellerSureRequest, opts ...grpc_go.CallOption) (*PayCreateRequest, common.ErrorWithAttachment) {
	out := new(PayCreateRequest)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/PaySellerSure", in, out)
}

func (c *orderClient) PayCancelByOutTradeNo(ctx context.Context, in *PaySellerSureRequest, opts ...grpc_go.CallOption) (*PayCreateRequest, common.ErrorWithAttachment) {
	out := new(PayCreateRequest)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/PayCancelByOutTradeNo", in, out)
}

func (c *orderClient) PayInfoByCode(ctx context.Context, in *PaySellerSureRequest, opts ...grpc_go.CallOption) (*PayCreateRequest, common.ErrorWithAttachment) {
	out := new(PayCreateRequest)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/PayInfoByCode", in, out)
}

func (c *orderClient) PayFail(ctx context.Context, in *PayCompleteRequest, opts ...grpc_go.CallOption) (*CommonCreateResponse, common.ErrorWithAttachment) {
	out := new(CommonCreateResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/PayFail", in, out)
}

func (c *orderClient) PayDetail(ctx context.Context, in *PayDetailRequest, opts ...grpc_go.CallOption) (*PayCreateRequest, common.ErrorWithAttachment) {
	out := new(PayCreateRequest)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/PayDetail", in, out)
}

func (c *orderClient) PayDelete(ctx context.Context, in *CommonRequest, opts ...grpc_go.CallOption) (*CommonResponse, common.ErrorWithAttachment) {
	out := new(CommonResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/PayDelete", in, out)
}

func (c *orderClient) PayList(ctx context.Context, in *PayListRequest, opts ...grpc_go.CallOption) (*PayListResponse, common.ErrorWithAttachment) {
	out := new(PayListResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/PayList", in, out)
}

func (c *orderClient) PaySetExpress(ctx context.Context, in *PaySetExpressRequest, opts ...grpc_go.CallOption) (*CommonResponse, common.ErrorWithAttachment) {
	out := new(CommonResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/PaySetExpress", in, out)
}

func (c *orderClient) PaySetExpressRemark(ctx context.Context, in *PaySetExpressRequest, opts ...grpc_go.CallOption) (*CommonResponse, common.ErrorWithAttachment) {
	out := new(CommonResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/PaySetExpressRemark", in, out)
}

func (c *orderClient) PaySynSeriesData(ctx context.Context, in *PaySynSeriesDataRequest, opts ...grpc_go.CallOption) (*CommonResponse, common.ErrorWithAttachment) {
	out := new(CommonResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/PaySynSeriesData", in, out)
}

func (c *orderClient) PaySeriesDataList(ctx context.Context, in *PaySeriesDataListRequest, opts ...grpc_go.CallOption) (*PaySeriesDataListResponse, common.ErrorWithAttachment) {
	out := new(PaySeriesDataListResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/PaySeriesDataList", in, out)
}

func (c *orderClient) PayCollectionsDataList(ctx context.Context, in *PayCollectionsDataListRequest, opts ...grpc_go.CallOption) (*PayCollectionsDataListResponse, common.ErrorWithAttachment) {
	out := new(PayCollectionsDataListResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/PayCollectionsDataList", in, out)
}

func (c *orderClient) PayCronSynFail(ctx context.Context, in *PayCronSynFailRequest, opts ...grpc_go.CallOption) (*PayListResponse, common.ErrorWithAttachment) {
	out := new(PayListResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/PayCronSynFail", in, out)
}

func (c *orderClient) PayCronSynSuccess(ctx context.Context, in *PayCronSynFailRequest, opts ...grpc_go.CallOption) (*PayListResponse, common.ErrorWithAttachment) {
	out := new(PayListResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/PayCronSynSuccess", in, out)
}

func (c *orderClient) PayRefund(ctx context.Context, in *PayDetailRequest, opts ...grpc_go.CallOption) (*PayCreateRequest, common.ErrorWithAttachment) {
	out := new(PayCreateRequest)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/PayRefund", in, out)
}

func (c *orderClient) PayGiveExist(ctx context.Context, in *PayGiveExistRequest, opts ...grpc_go.CallOption) (*CommonResponse, common.ErrorWithAttachment) {
	out := new(CommonResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/PayGiveExist", in, out)
}

func (c *orderClient) PayStaticIds(ctx context.Context, in *CommonRequest, opts ...grpc_go.CallOption) (*PayStaticIdResponse, common.ErrorWithAttachment) {
	out := new(PayStaticIdResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/PayStaticIds", in, out)
}

func (c *orderClient) SetPayCheck(ctx context.Context, in *SetPayCheckRequest, opts ...grpc_go.CallOption) (*CommonResponse, common.ErrorWithAttachment) {
	out := new(CommonResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/SetPayCheck", in, out)
}

func (c *orderClient) GetWipedOutCheckedNum(ctx context.Context, in *CommonRequest, opts ...grpc_go.CallOption) (*GetWipedOutCheckedNumResponse, common.ErrorWithAttachment) {
	out := new(GetWipedOutCheckedNumResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/GetWipedOutCheckedNum", in, out)
}

func (c *orderClient) WechatJsApiPay(ctx context.Context, in *WechatJsApiPayRequest, opts ...grpc_go.CallOption) (*WechatJsApiPayResponse, common.ErrorWithAttachment) {
	out := new(WechatJsApiPayResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/WechatJsApiPay", in, out)
}

func (c *orderClient) WechatJsApiQueryByOutTradeNo(ctx context.Context, in *WechatJsApiQueryByOutTradeNoRequest, opts ...grpc_go.CallOption) (*WechatJsApiQueryByOutTradeNoResponse, common.ErrorWithAttachment) {
	out := new(WechatJsApiQueryByOutTradeNoResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/WechatJsApiQueryByOutTradeNo", in, out)
}

func (c *orderClient) GetPayByOutTradeNo(ctx context.Context, in *GetPayByOutTradeNoRequest, opts ...grpc_go.CallOption) (*GetPayByOutTradeNoResponse, common.ErrorWithAttachment) {
	out := new(GetPayByOutTradeNoResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/GetPayByOutTradeNo", in, out)
}

func (c *orderClient) WechatJsApiRefunds(ctx context.Context, in *WechatJsApiRefundsRequest, opts ...grpc_go.CallOption) (*WechatJsApiRefundsResponse, common.ErrorWithAttachment) {
	out := new(WechatJsApiRefundsResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/WechatJsApiRefunds", in, out)
}

func (c *orderClient) SetPayOk(ctx context.Context, in *WechatPayOkRequest, opts ...grpc_go.CallOption) (*CommonResponse, common.ErrorWithAttachment) {
	out := new(CommonResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/SetPayOk", in, out)
}

func (c *orderClient) WechatAppPay(ctx context.Context, in *WechatAppPayRequest, opts ...grpc_go.CallOption) (*WechatAppPayResponse, common.ErrorWithAttachment) {
	out := new(WechatAppPayResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/WechatAppPay", in, out)
}

func (c *orderClient) WechatAppQueryByOutTradeNo(ctx context.Context, in *WechatAppQueryByOutTradeNoRequest, opts ...grpc_go.CallOption) (*WechatAppQueryByOutTradeNoResponse, common.ErrorWithAttachment) {
	out := new(WechatAppQueryByOutTradeNoResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/WechatAppQueryByOutTradeNo", in, out)
}

func (c *orderClient) WechatNativePay(ctx context.Context, in *WechatNativePayRequest, opts ...grpc_go.CallOption) (*WechatNativePayResponse, common.ErrorWithAttachment) {
	out := new(WechatNativePayResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/WechatNativePay", in, out)
}

func (c *orderClient) WechatNativeQueryByOutTradeNo(ctx context.Context, in *WechatNativeQueryByOutTradeNoRequest, opts ...grpc_go.CallOption) (*WechatNativeQueryByOutTradeNoResponse, common.ErrorWithAttachment) {
	out := new(WechatNativeQueryByOutTradeNoResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/WechatNativeQueryByOutTradeNo", in, out)
}

func (c *orderClient) WechatRefundQueryByOutRefundNo(ctx context.Context, in *WechatRefundQueryByOutRefundNoRequest, opts ...grpc_go.CallOption) (*WechatRefundQueryByOutRefundNoResponse, common.ErrorWithAttachment) {
	out := new(WechatRefundQueryByOutRefundNoResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/WechatRefundQueryByOutRefundNo", in, out)
}

func (c *orderClient) WechatH5Pay(ctx context.Context, in *WechatH5PayRequest, opts ...grpc_go.CallOption) (*WechatH5PayResponse, common.ErrorWithAttachment) {
	out := new(WechatH5PayResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/WechatH5Pay", in, out)
}

func (c *orderClient) WechatH5QueryByOutTradeNo(ctx context.Context, in *WechatH5QueryByOutTradeNoRequest, opts ...grpc_go.CallOption) (*WechatH5QueryByOutTradeNoResponse, common.ErrorWithAttachment) {
	out := new(WechatH5QueryByOutTradeNoResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/WechatH5QueryByOutTradeNo", in, out)
}

func (c *orderClient) AliWapPay(ctx context.Context, in *AliWapPayRequest, opts ...grpc_go.CallOption) (*AliWapPayResponse, common.ErrorWithAttachment) {
	out := new(AliWapPayResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/AliWapPay", in, out)
}

func (c *orderClient) AliAppPay(ctx context.Context, in *AliAppPayRequest, opts ...grpc_go.CallOption) (*AliAppPayResponse, common.ErrorWithAttachment) {
	out := new(AliAppPayResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/AliAppPay", in, out)
}

func (c *orderClient) AliNativePay(ctx context.Context, in *AliNativePayRequest, opts ...grpc_go.CallOption) (*AliNativePayResponse, common.ErrorWithAttachment) {
	out := new(AliNativePayResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/AliNativePay", in, out)
}

func (c *orderClient) AliPcWabPay(ctx context.Context, in *AliPcWabPayRequest, opts ...grpc_go.CallOption) (*AliPcWabPayResponse, common.ErrorWithAttachment) {
	out := new(AliPcWabPayResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/AliPcWabPay", in, out)
}

func (c *orderClient) AliReFund(ctx context.Context, in *AliReFundRequest, opts ...grpc_go.CallOption) (*AliReFundResponse, common.ErrorWithAttachment) {
	out := new(AliReFundResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/AliReFund", in, out)
}

func (c *orderClient) AliNotify(ctx context.Context, in *AliNotifyRequest, opts ...grpc_go.CallOption) (*AliNotifyResponse, common.ErrorWithAttachment) {
	out := new(AliNotifyResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/AliNotify", in, out)
}

func (c *orderClient) AliQueryByOutTradeNo(ctx context.Context, in *AliQueryByOutTradeNoRequest, opts ...grpc_go.CallOption) (*AliQueryByOutTradeNoResponse, common.ErrorWithAttachment) {
	out := new(AliQueryByOutTradeNoResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/AliQueryByOutTradeNo", in, out)
}

func (c *orderClient) AliRefundQueryByOutTradeNo(ctx context.Context, in *AliRefundQueryByOutTradeNoRequest, opts ...grpc_go.CallOption) (*AliRefundQueryByOutTradeNoResponse, common.ErrorWithAttachment) {
	out := new(AliRefundQueryByOutTradeNoResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/AliRefundQueryByOutTradeNo", in, out)
}

func (c *orderClient) Bestow(ctx context.Context, in *BestowRequest, opts ...grpc_go.CallOption) (*BestowResponse, common.ErrorWithAttachment) {
	out := new(BestowResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/Bestow", in, out)
}

func (c *orderClient) ReceiveGift(ctx context.Context, in *ReceiveGiftRequest, opts ...grpc_go.CallOption) (*ReceiveGiftResponse, common.ErrorWithAttachment) {
	out := new(ReceiveGiftResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/ReceiveGift", in, out)
}

func (c *orderClient) ApplyBlockchainAddress(ctx context.Context, in *ApplyBlockchainAddressRequest, opts ...grpc_go.CallOption) (*ApplyBlockchainAddressResponse, common.ErrorWithAttachment) {
	out := new(ApplyBlockchainAddressResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/ApplyBlockchainAddress", in, out)
}

func (c *orderClient) ApplyCertificate(ctx context.Context, in *ApplyCertificateRequest, opts ...grpc_go.CallOption) (*ApplyCertificateResponse, common.ErrorWithAttachment) {
	out := new(ApplyCertificateResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/ApplyCertificate", in, out)
}

func (c *orderClient) CancelBestow(ctx context.Context, in *CancelBestowRequest, opts ...grpc_go.CallOption) (*CancelBestowResponse, common.ErrorWithAttachment) {
	out := new(CancelBestowResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/CancelBestow", in, out)
}

func (c *orderClient) GetBestowInfo(ctx context.Context, in *GetBestowInfoRequest, opts ...grpc_go.CallOption) (*GetBestowInfoResponse, common.ErrorWithAttachment) {
	out := new(GetBestowInfoResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/GetBestowInfo", in, out)
}

func (c *orderClient) InputPersonalBlockchain(ctx context.Context, in *InputPersonalBlockchainRequest, opts ...grpc_go.CallOption) (*InputPersonalBlockchainResponse, common.ErrorWithAttachment) {
	out := new(InputPersonalBlockchainResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/InputPersonalBlockchain", in, out)
}

func (c *orderClient) ShipAddressCreate(ctx context.Context, in *ShipAddressCreateRequest, opts ...grpc_go.CallOption) (*ShipAddressCreateResponse, common.ErrorWithAttachment) {
	out := new(ShipAddressCreateResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/ShipAddressCreate", in, out)
}

func (c *orderClient) ShipAddressDelete(ctx context.Context, in *ShipAddressDeleteRequest, opts ...grpc_go.CallOption) (*ShipAddressDeleteResponse, common.ErrorWithAttachment) {
	out := new(ShipAddressDeleteResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/ShipAddressDelete", in, out)
}

func (c *orderClient) ShipAddressList(ctx context.Context, in *ShipAddressListRequest, opts ...grpc_go.CallOption) (*ShipAddressListResponse, common.ErrorWithAttachment) {
	out := new(ShipAddressListResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/ShipAddressList", in, out)
}

func (c *orderClient) ShipAddressDefault(ctx context.Context, in *ShipAddressDefaultRequest, opts ...grpc_go.CallOption) (*ShipAddressDefaultResponse, common.ErrorWithAttachment) {
	out := new(ShipAddressDefaultResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/ShipAddressDefault", in, out)
}

func (c *orderClient) GetAddressInfoById(ctx context.Context, in *GetAddressInfoByIdRequest, opts ...grpc_go.CallOption) (*GetAddressInfoByIdResponse, common.ErrorWithAttachment) {
	out := new(GetAddressInfoByIdResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/GetAddressInfoById", in, out)
}

func (c *orderClient) LogisticsTracking(ctx context.Context, in *LogisticsTrackingRequest, opts ...grpc_go.CallOption) (*LogisticsTrackingResponse, common.ErrorWithAttachment) {
	out := new(LogisticsTrackingResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/LogisticsTracking", in, out)
}

func (c *orderClient) AddTrackingNumber(ctx context.Context, in *AddTrackingNumberRequest, opts ...grpc_go.CallOption) (*AddTrackingNumberResponse, common.ErrorWithAttachment) {
	out := new(AddTrackingNumberResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/AddTrackingNumber", in, out)
}

func (c *orderClient) RecordNotify(ctx context.Context, in *RecordNotifyRequest, opts ...grpc_go.CallOption) (*CommonResponse, common.ErrorWithAttachment) {
	out := new(CommonResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/RecordNotify", in, out)
}

func (c *orderClient) CreateStripeCheckoutSession(ctx context.Context, in *CreateStripeCheckoutSessionRequest, opts ...grpc_go.CallOption) (*CreateStripeCheckoutSessionResponse, common.ErrorWithAttachment) {
	out := new(CreateStripeCheckoutSessionResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/CreateStripeCheckoutSession", in, out)
}

func (c *orderClient) GetStripePaymentIntentInfo(ctx context.Context, in *GetStripePaymentIntentInfoRequest, opts ...grpc_go.CallOption) (*GetStripePaymentIntentInfoResponse, common.ErrorWithAttachment) {
	out := new(GetStripePaymentIntentInfoResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/GetStripePaymentIntentInfo", in, out)
}

func (c *orderClient) CreateStripeRefund(ctx context.Context, in *CreateStripeRefundRequest, opts ...grpc_go.CallOption) (*CreateStripeRefundResponse, common.ErrorWithAttachment) {
	out := new(CreateStripeRefundResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/CreateStripeRefund", in, out)
}

func (c *orderClient) GetRefundInfo(ctx context.Context, in *GetRefundInfoRequest, opts ...grpc_go.CallOption) (*GetRefundInfoResponse, common.ErrorWithAttachment) {
	out := new(GetRefundInfoResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/GetRefundInfo", in, out)
}

func (c *orderClient) GetCheckoutWebhook(ctx context.Context, in *GetCheckoutWebhookRequest, opts ...grpc_go.CallOption) (*GetCheckoutWebhookResponse, common.ErrorWithAttachment) {
	out := new(GetCheckoutWebhookResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/GetCheckoutWebhook", in, out)
}

func (c *orderClient) CommonCheckoutWebhook(ctx context.Context, in *GetCheckoutWebhookRequest, opts ...grpc_go.CallOption) (*GetCheckoutWebhookResponse, common.ErrorWithAttachment) {
	out := new(GetCheckoutWebhookResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/CommonCheckoutWebhook", in, out)
}

func (c *orderClient) QueryStripeInfoByCheckSessionIds(ctx context.Context, in *QueryStripeInfoRequest, opts ...grpc_go.CallOption) (*QueryStripeInfoResponse, common.ErrorWithAttachment) {
	out := new(QueryStripeInfoResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/QueryStripeInfoByCheckSessionIds", in, out)
}

func (c *orderClient) CreateStripePaymentIntent(ctx context.Context, in *CreateStripePaymentIntentRequest, opts ...grpc_go.CallOption) (*CreateStripePaymentIntentResponse, common.ErrorWithAttachment) {
	out := new(CreateStripePaymentIntentResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/CreateStripePaymentIntent", in, out)
}

func (c *orderClient) CommonStripePayemntIntentWebhook(ctx context.Context, in *GetCheckoutWebhookRequest, opts ...grpc_go.CallOption) (*GetCheckoutWebhookResponse, common.ErrorWithAttachment) {
	out := new(GetCheckoutWebhookResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/CommonStripePayemntIntentWebhook", in, out)
}

// OrderServer is the server API for Order service.
// All implementations must embed UnimplementedOrderServer
// for forward compatibility
type OrderServer interface {
	// 记录接受数据日志
	CreateLog(context.Context, *LogRequest) (*CommonCreateResponse, error)
	// 订单
	OrderUpdate(context.Context, *OrderRequest) (*OrderResponse, error)
	OrderUpdateByUid(context.Context, *OrderRequest) (*OrderResponse, error)
	OrderUpdateKeys(context.Context, *OrderRequest) (*OrderResponse, error)
	OrderBatchUpdate(context.Context, *OrderBatchUpdateRequest) (*CommonCreateResponse, error)
	OrderExBatchUpdate(context.Context, *OrderBatchUpdateRequest) (*CommonCreateResponse, error)
	OrderDelete(context.Context, *CommonRequest) (*OrderRemove, error)
	GetTransactions(context.Context, *TransactionsRequest) (*TransactionsResponse, error)
	GetTransactionStage(context.Context, *TransactionStageRequest) (*TransactionStageResponse, error)
	GetBlankOrder(context.Context, *TransactionStageRequest) (*BlankOrders, error)
	GetBlankOrderByInfo(context.Context, *BlankOrderReq) (*BlankOrders, error)
	GetSellerTransactions(context.Context, *TransactionsRequest) (*TransactionsResponse, error)
	GetTransaction(context.Context, *CommonRequest) (*Transaction, error)
	SaveTransactionSages(context.Context, *StageRequest) (*CommonCreateResponse, error)
	GetOrders(context.Context, *OrderList) (*OrderListResponse, error)
	GetOrder(context.Context, *CommonRequest) (*OrderRequest, error)
	RandOrders(context.Context, *RandOrderRequest) (*RandOrderResponse, error)
	UpdateOrderSellerId(context.Context, *UpdateSellerIdRequest) (*CommonCreateResponse, error)
	UpDateOrderEntrusts(context.Context, *RandOrderRequest) (*CommonCreateResponse, error)
	OrderCreate(context.Context, *OrderRequest) (*OrderResponse, error)
	SetStatus(context.Context, *SetStatusRequest) (*OrderResponse, error)
	OrderStatisticsInfo(context.Context, *OrderStatisticsInfoRequest) (*OrderStatisticsInfoResponse, error)
	OrdersByArtworkNum(context.Context, *OrdersByArtworkNumRequest) (*OrderBaseList, error)
	OrderStageSetStatus(context.Context, *OrderStageSetStatusReq) (*CommonResponse, error)
	SynTransactionStage(context.Context, *CommonRequest) (*CommonResponse, error)
	UpdateTransactionDate(context.Context, *UpdateTransactionDateReq) (*CommonResponse, error)
	UpdateTransactionStage(context.Context, *UpdateTransactionStageReq) (*CommonResponse, error)
	// 销售的统计数据
	GetReportUsers(context.Context, *ReportUserList) (*ReportUserListResponse, error)
	GetReportUser(context.Context, *ReportUserDetail) (*ReportUserRequest, error)
	// 报表
	GetReport(context.Context, *ReportDetail) (*ReportRequest, error)
	ReportDelete(context.Context, *ReportDetail) (*CommonCreateResponse, error)
	ReportCreate(context.Context, *ReportCreateRequest) (*ReportResponse, error)
	ReportPublic(context.Context, *ReportDetail) (*ReportLogRemove, error)
	GetReports(context.Context, *ReportList) (*ReportListResponse, error)
	BeforeCreateUsers(context.Context, *ReportCreateRequest) (*ReportUserListResponse, error)
	GetCheckSalesAmount(context.Context, *ReportCheckSales) (*ResCheckSales, error)
	SetReportStatus(context.Context, *SetStatusRequest) (*CommonCreateResponse, error)
	UpdateReportUsers(context.Context, *UpdateReportUserList) (*CommonCreateResponse, error)
	GetReportQuery(context.Context, *ReportQueryRequest) (*ReportQueryResponse, error)
	ReportRead(context.Context, *ReportReadRequest) (*ReportResponse, error)
	// 业绩部分
	Result(context.Context, *ResultRequest) (*SellerResponse, error)
	Results(context.Context, *ResultsRequest) (*ResultsResponse, error)
	// 汇报
	CreateWeekly(context.Context, *WeeklyRequest) (*WeeklyResponse, error)
	UpdateWeekly(context.Context, *WeeklyRequest) (*WeeklyResponse, error)
	DetermineCanCreateWeekly(context.Context, *CanWeeklyRequest) (*WeeklyResponse, error)
	WeeklyList(context.Context, *WeeklyListRequest) (*WeeklyResponseList, error)
	WeeklyInfo(context.Context, *WeeklyInfoRequest) (*WeeklyRequest, error)
	WeeklyOaInfo(context.Context, *WeeklyInfoRequest) (*WeeklyInfoResponse, error)
	WeeklyRead(context.Context, *WeeklyReadRequest) (*WeeklyResponse, error)
	WeeklyModifiedNotCommitted(context.Context, *WeeklyResponse) (*WeeklyResponseList, error)
	GetSmsConfig(context.Context, *SmsConfigRequest) (*SmsConfigResponse, error)
	CreateWeeklyComment(context.Context, *WeeklyCommentRequest) (*ReportResponse, error)
	GetWeeklyComment(context.Context, *GetWeeklyCommentRequest) (*WeeklyCommentResponse, error)
	CreateStaffWeekly(context.Context, *StaffWeeklyReq) (*ReportResponse, error)
	UpdateStaffWeekly(context.Context, *StaffWeeklyReq) (*ReportResponse, error)
	DetermineCanStaffWeekly(context.Context, *CanWeeklyRequest) (*StaffWeeklyReq, error)
	StaffWeeklyList(context.Context, *StaffWeeklyListRequest) (*StaffWeeklyResponseList, error)
	StaffWeeklyInfo(context.Context, *ReportUserDetail) (*StaffWeeklyReq, error)
	// 委托业绩
	GetEntrust(context.Context, *CommonRequest) (*EntrustRequest, error)
	EntrustDelete(context.Context, *CommonRequest) (*CommonCreateResponse, error)
	EntrustBatchDelete(context.Context, *UpDateOrderEntrustsRequest) (*CommonCreateResponse, error)
	EntrustCreate(context.Context, *EntrustRequest) (*CommonCreateResponse, error)
	EntrustCreates(context.Context, *EntrustListResponse) (*UpdateEntrustKeysResponse, error)
	GetEntrusts(context.Context, *EntrustList) (*EntrustListResponse, error)
	UpdateEntrustKeys(context.Context, *EntrustListResponse) (*UpdateEntrustKeysResponse, error)
	// 支付订单部分
	PayCreateFirstCache(context.Context, *PayCreateCacheRequest) (*CommonCreateResponse, error)
	PaySetCash(context.Context, *PaySetCashRequest) (*CommonResponse, error)
	PayCreate(context.Context, *PayCreateRequest) (*CommonCreateResponse, error)
	PayComplete(context.Context, *PayCompleteRequest) (*PayCreateRequest, error)
	PaySellerSure(context.Context, *PaySellerSureRequest) (*PayCreateRequest, error)
	PayCancelByOutTradeNo(context.Context, *PaySellerSureRequest) (*PayCreateRequest, error)
	PayInfoByCode(context.Context, *PaySellerSureRequest) (*PayCreateRequest, error)
	PayFail(context.Context, *PayCompleteRequest) (*CommonCreateResponse, error)
	PayDetail(context.Context, *PayDetailRequest) (*PayCreateRequest, error)
	PayDelete(context.Context, *CommonRequest) (*CommonResponse, error)
	PayList(context.Context, *PayListRequest) (*PayListResponse, error)
	PaySetExpress(context.Context, *PaySetExpressRequest) (*CommonResponse, error)
	PaySetExpressRemark(context.Context, *PaySetExpressRequest) (*CommonResponse, error)
	PaySynSeriesData(context.Context, *PaySynSeriesDataRequest) (*CommonResponse, error)
	PaySeriesDataList(context.Context, *PaySeriesDataListRequest) (*PaySeriesDataListResponse, error)
	PayCollectionsDataList(context.Context, *PayCollectionsDataListRequest) (*PayCollectionsDataListResponse, error)
	PayCronSynFail(context.Context, *PayCronSynFailRequest) (*PayListResponse, error)
	PayCronSynSuccess(context.Context, *PayCronSynFailRequest) (*PayListResponse, error)
	PayRefund(context.Context, *PayDetailRequest) (*PayCreateRequest, error)
	PayGiveExist(context.Context, *PayGiveExistRequest) (*CommonResponse, error)
	PayStaticIds(context.Context, *CommonRequest) (*PayStaticIdResponse, error)
	SetPayCheck(context.Context, *SetPayCheckRequest) (*CommonResponse, error)
	GetWipedOutCheckedNum(context.Context, *CommonRequest) (*GetWipedOutCheckedNumResponse, error)
	// 以下是微信jsapi和App和Native支付与退款
	WechatJsApiPay(context.Context, *WechatJsApiPayRequest) (*WechatJsApiPayResponse, error)
	WechatJsApiQueryByOutTradeNo(context.Context, *WechatJsApiQueryByOutTradeNoRequest) (*WechatJsApiQueryByOutTradeNoResponse, error)
	GetPayByOutTradeNo(context.Context, *GetPayByOutTradeNoRequest) (*GetPayByOutTradeNoResponse, error)
	WechatJsApiRefunds(context.Context, *WechatJsApiRefundsRequest) (*WechatJsApiRefundsResponse, error)
	SetPayOk(context.Context, *WechatPayOkRequest) (*CommonResponse, error)
	WechatAppPay(context.Context, *WechatAppPayRequest) (*WechatAppPayResponse, error)
	WechatAppQueryByOutTradeNo(context.Context, *WechatAppQueryByOutTradeNoRequest) (*WechatAppQueryByOutTradeNoResponse, error)
	WechatNativePay(context.Context, *WechatNativePayRequest) (*WechatNativePayResponse, error)
	WechatNativeQueryByOutTradeNo(context.Context, *WechatNativeQueryByOutTradeNoRequest) (*WechatNativeQueryByOutTradeNoResponse, error)
	WechatRefundQueryByOutRefundNo(context.Context, *WechatRefundQueryByOutRefundNoRequest) (*WechatRefundQueryByOutRefundNoResponse, error)
	WechatH5Pay(context.Context, *WechatH5PayRequest) (*WechatH5PayResponse, error)
	WechatH5QueryByOutTradeNo(context.Context, *WechatH5QueryByOutTradeNoRequest) (*WechatH5QueryByOutTradeNoResponse, error)
	// 以下是支付宝网页和App支付与退款
	AliWapPay(context.Context, *AliWapPayRequest) (*AliWapPayResponse, error)
	AliAppPay(context.Context, *AliAppPayRequest) (*AliAppPayResponse, error)
	AliNativePay(context.Context, *AliNativePayRequest) (*AliNativePayResponse, error)
	AliPcWabPay(context.Context, *AliPcWabPayRequest) (*AliPcWabPayResponse, error)
	AliReFund(context.Context, *AliReFundRequest) (*AliReFundResponse, error)
	AliNotify(context.Context, *AliNotifyRequest) (*AliNotifyResponse, error)
	AliQueryByOutTradeNo(context.Context, *AliQueryByOutTradeNoRequest) (*AliQueryByOutTradeNoResponse, error)
	AliRefundQueryByOutTradeNo(context.Context, *AliRefundQueryByOutTradeNoRequest) (*AliRefundQueryByOutTradeNoResponse, error)
	// 以下是商城1.2版本的接口
	Bestow(context.Context, *BestowRequest) (*BestowResponse, error)
	ReceiveGift(context.Context, *ReceiveGiftRequest) (*ReceiveGiftResponse, error)
	ApplyBlockchainAddress(context.Context, *ApplyBlockchainAddressRequest) (*ApplyBlockchainAddressResponse, error)
	ApplyCertificate(context.Context, *ApplyCertificateRequest) (*ApplyCertificateResponse, error)
	CancelBestow(context.Context, *CancelBestowRequest) (*CancelBestowResponse, error)
	GetBestowInfo(context.Context, *GetBestowInfoRequest) (*GetBestowInfoResponse, error)
	InputPersonalBlockchain(context.Context, *InputPersonalBlockchainRequest) (*InputPersonalBlockchainResponse, error)
	ShipAddressCreate(context.Context, *ShipAddressCreateRequest) (*ShipAddressCreateResponse, error)
	ShipAddressDelete(context.Context, *ShipAddressDeleteRequest) (*ShipAddressDeleteResponse, error)
	ShipAddressList(context.Context, *ShipAddressListRequest) (*ShipAddressListResponse, error)
	ShipAddressDefault(context.Context, *ShipAddressDefaultRequest) (*ShipAddressDefaultResponse, error)
	GetAddressInfoById(context.Context, *GetAddressInfoByIdRequest) (*GetAddressInfoByIdResponse, error)
	LogisticsTracking(context.Context, *LogisticsTrackingRequest) (*LogisticsTrackingResponse, error)
	AddTrackingNumber(context.Context, *AddTrackingNumberRequest) (*AddTrackingNumberResponse, error)
	RecordNotify(context.Context, *RecordNotifyRequest) (*CommonResponse, error)
	// 境外支付 stripe
	CreateStripeCheckoutSession(context.Context, *CreateStripeCheckoutSessionRequest) (*CreateStripeCheckoutSessionResponse, error)
	GetStripePaymentIntentInfo(context.Context, *GetStripePaymentIntentInfoRequest) (*GetStripePaymentIntentInfoResponse, error)
	CreateStripeRefund(context.Context, *CreateStripeRefundRequest) (*CreateStripeRefundResponse, error)
	GetRefundInfo(context.Context, *GetRefundInfoRequest) (*GetRefundInfoResponse, error)
	GetCheckoutWebhook(context.Context, *GetCheckoutWebhookRequest) (*GetCheckoutWebhookResponse, error)
	CommonCheckoutWebhook(context.Context, *GetCheckoutWebhookRequest) (*GetCheckoutWebhookResponse, error)
	QueryStripeInfoByCheckSessionIds(context.Context, *QueryStripeInfoRequest) (*QueryStripeInfoResponse, error)
	// stripe改版，用内嵌窗口，自定义前端，不用checkoutsessionid
	CreateStripePaymentIntent(context.Context, *CreateStripePaymentIntentRequest) (*CreateStripePaymentIntentResponse, error)
	CommonStripePayemntIntentWebhook(context.Context, *GetCheckoutWebhookRequest) (*GetCheckoutWebhookResponse, error)
	mustEmbedUnimplementedOrderServer()
}

// UnimplementedOrderServer must be embedded to have forward compatible implementations.
type UnimplementedOrderServer struct {
	proxyImpl protocol.Invoker
}

func (UnimplementedOrderServer) CreateLog(context.Context, *LogRequest) (*CommonCreateResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateLog not implemented")
}
func (UnimplementedOrderServer) OrderUpdate(context.Context, *OrderRequest) (*OrderResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method OrderUpdate not implemented")
}
func (UnimplementedOrderServer) OrderUpdateByUid(context.Context, *OrderRequest) (*OrderResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method OrderUpdateByUid not implemented")
}
func (UnimplementedOrderServer) OrderUpdateKeys(context.Context, *OrderRequest) (*OrderResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method OrderUpdateKeys not implemented")
}
func (UnimplementedOrderServer) OrderBatchUpdate(context.Context, *OrderBatchUpdateRequest) (*CommonCreateResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method OrderBatchUpdate not implemented")
}
func (UnimplementedOrderServer) OrderExBatchUpdate(context.Context, *OrderBatchUpdateRequest) (*CommonCreateResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method OrderExBatchUpdate not implemented")
}
func (UnimplementedOrderServer) OrderDelete(context.Context, *CommonRequest) (*OrderRemove, error) {
	return nil, status.Errorf(codes.Unimplemented, "method OrderDelete not implemented")
}
func (UnimplementedOrderServer) GetTransactions(context.Context, *TransactionsRequest) (*TransactionsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetTransactions not implemented")
}
func (UnimplementedOrderServer) GetTransactionStage(context.Context, *TransactionStageRequest) (*TransactionStageResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetTransactionStage not implemented")
}
func (UnimplementedOrderServer) GetBlankOrder(context.Context, *TransactionStageRequest) (*BlankOrders, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetBlankOrder not implemented")
}
func (UnimplementedOrderServer) GetBlankOrderByInfo(context.Context, *BlankOrderReq) (*BlankOrders, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetBlankOrderByInfo not implemented")
}
func (UnimplementedOrderServer) GetSellerTransactions(context.Context, *TransactionsRequest) (*TransactionsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetSellerTransactions not implemented")
}
func (UnimplementedOrderServer) GetTransaction(context.Context, *CommonRequest) (*Transaction, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetTransaction not implemented")
}
func (UnimplementedOrderServer) SaveTransactionSages(context.Context, *StageRequest) (*CommonCreateResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SaveTransactionSages not implemented")
}
func (UnimplementedOrderServer) GetOrders(context.Context, *OrderList) (*OrderListResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetOrders not implemented")
}
func (UnimplementedOrderServer) GetOrder(context.Context, *CommonRequest) (*OrderRequest, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetOrder not implemented")
}
func (UnimplementedOrderServer) RandOrders(context.Context, *RandOrderRequest) (*RandOrderResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RandOrders not implemented")
}
func (UnimplementedOrderServer) UpdateOrderSellerId(context.Context, *UpdateSellerIdRequest) (*CommonCreateResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateOrderSellerId not implemented")
}
func (UnimplementedOrderServer) UpDateOrderEntrusts(context.Context, *RandOrderRequest) (*CommonCreateResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpDateOrderEntrusts not implemented")
}
func (UnimplementedOrderServer) OrderCreate(context.Context, *OrderRequest) (*OrderResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method OrderCreate not implemented")
}
func (UnimplementedOrderServer) SetStatus(context.Context, *SetStatusRequest) (*OrderResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SetStatus not implemented")
}
func (UnimplementedOrderServer) OrderStatisticsInfo(context.Context, *OrderStatisticsInfoRequest) (*OrderStatisticsInfoResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method OrderStatisticsInfo not implemented")
}
func (UnimplementedOrderServer) OrdersByArtworkNum(context.Context, *OrdersByArtworkNumRequest) (*OrderBaseList, error) {
	return nil, status.Errorf(codes.Unimplemented, "method OrdersByArtworkNum not implemented")
}
func (UnimplementedOrderServer) OrderStageSetStatus(context.Context, *OrderStageSetStatusReq) (*CommonResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method OrderStageSetStatus not implemented")
}
func (UnimplementedOrderServer) SynTransactionStage(context.Context, *CommonRequest) (*CommonResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SynTransactionStage not implemented")
}
func (UnimplementedOrderServer) UpdateTransactionDate(context.Context, *UpdateTransactionDateReq) (*CommonResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateTransactionDate not implemented")
}
func (UnimplementedOrderServer) UpdateTransactionStage(context.Context, *UpdateTransactionStageReq) (*CommonResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateTransactionStage not implemented")
}
func (UnimplementedOrderServer) GetReportUsers(context.Context, *ReportUserList) (*ReportUserListResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetReportUsers not implemented")
}
func (UnimplementedOrderServer) GetReportUser(context.Context, *ReportUserDetail) (*ReportUserRequest, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetReportUser not implemented")
}
func (UnimplementedOrderServer) GetReport(context.Context, *ReportDetail) (*ReportRequest, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetReport not implemented")
}
func (UnimplementedOrderServer) ReportDelete(context.Context, *ReportDetail) (*CommonCreateResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ReportDelete not implemented")
}
func (UnimplementedOrderServer) ReportCreate(context.Context, *ReportCreateRequest) (*ReportResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ReportCreate not implemented")
}
func (UnimplementedOrderServer) ReportPublic(context.Context, *ReportDetail) (*ReportLogRemove, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ReportPublic not implemented")
}
func (UnimplementedOrderServer) GetReports(context.Context, *ReportList) (*ReportListResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetReports not implemented")
}
func (UnimplementedOrderServer) BeforeCreateUsers(context.Context, *ReportCreateRequest) (*ReportUserListResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method BeforeCreateUsers not implemented")
}
func (UnimplementedOrderServer) GetCheckSalesAmount(context.Context, *ReportCheckSales) (*ResCheckSales, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetCheckSalesAmount not implemented")
}
func (UnimplementedOrderServer) SetReportStatus(context.Context, *SetStatusRequest) (*CommonCreateResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SetReportStatus not implemented")
}
func (UnimplementedOrderServer) UpdateReportUsers(context.Context, *UpdateReportUserList) (*CommonCreateResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateReportUsers not implemented")
}
func (UnimplementedOrderServer) GetReportQuery(context.Context, *ReportQueryRequest) (*ReportQueryResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetReportQuery not implemented")
}
func (UnimplementedOrderServer) ReportRead(context.Context, *ReportReadRequest) (*ReportResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ReportRead not implemented")
}
func (UnimplementedOrderServer) Result(context.Context, *ResultRequest) (*SellerResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Result not implemented")
}
func (UnimplementedOrderServer) Results(context.Context, *ResultsRequest) (*ResultsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Results not implemented")
}
func (UnimplementedOrderServer) CreateWeekly(context.Context, *WeeklyRequest) (*WeeklyResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateWeekly not implemented")
}
func (UnimplementedOrderServer) UpdateWeekly(context.Context, *WeeklyRequest) (*WeeklyResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateWeekly not implemented")
}
func (UnimplementedOrderServer) DetermineCanCreateWeekly(context.Context, *CanWeeklyRequest) (*WeeklyResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DetermineCanCreateWeekly not implemented")
}
func (UnimplementedOrderServer) WeeklyList(context.Context, *WeeklyListRequest) (*WeeklyResponseList, error) {
	return nil, status.Errorf(codes.Unimplemented, "method WeeklyList not implemented")
}
func (UnimplementedOrderServer) WeeklyInfo(context.Context, *WeeklyInfoRequest) (*WeeklyRequest, error) {
	return nil, status.Errorf(codes.Unimplemented, "method WeeklyInfo not implemented")
}
func (UnimplementedOrderServer) WeeklyOaInfo(context.Context, *WeeklyInfoRequest) (*WeeklyInfoResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method WeeklyOaInfo not implemented")
}
func (UnimplementedOrderServer) WeeklyRead(context.Context, *WeeklyReadRequest) (*WeeklyResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method WeeklyRead not implemented")
}
func (UnimplementedOrderServer) WeeklyModifiedNotCommitted(context.Context, *WeeklyResponse) (*WeeklyResponseList, error) {
	return nil, status.Errorf(codes.Unimplemented, "method WeeklyModifiedNotCommitted not implemented")
}
func (UnimplementedOrderServer) GetSmsConfig(context.Context, *SmsConfigRequest) (*SmsConfigResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetSmsConfig not implemented")
}
func (UnimplementedOrderServer) CreateWeeklyComment(context.Context, *WeeklyCommentRequest) (*ReportResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateWeeklyComment not implemented")
}
func (UnimplementedOrderServer) GetWeeklyComment(context.Context, *GetWeeklyCommentRequest) (*WeeklyCommentResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetWeeklyComment not implemented")
}
func (UnimplementedOrderServer) CreateStaffWeekly(context.Context, *StaffWeeklyReq) (*ReportResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateStaffWeekly not implemented")
}
func (UnimplementedOrderServer) UpdateStaffWeekly(context.Context, *StaffWeeklyReq) (*ReportResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateStaffWeekly not implemented")
}
func (UnimplementedOrderServer) DetermineCanStaffWeekly(context.Context, *CanWeeklyRequest) (*StaffWeeklyReq, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DetermineCanStaffWeekly not implemented")
}
func (UnimplementedOrderServer) StaffWeeklyList(context.Context, *StaffWeeklyListRequest) (*StaffWeeklyResponseList, error) {
	return nil, status.Errorf(codes.Unimplemented, "method StaffWeeklyList not implemented")
}
func (UnimplementedOrderServer) StaffWeeklyInfo(context.Context, *ReportUserDetail) (*StaffWeeklyReq, error) {
	return nil, status.Errorf(codes.Unimplemented, "method StaffWeeklyInfo not implemented")
}
func (UnimplementedOrderServer) GetEntrust(context.Context, *CommonRequest) (*EntrustRequest, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetEntrust not implemented")
}
func (UnimplementedOrderServer) EntrustDelete(context.Context, *CommonRequest) (*CommonCreateResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method EntrustDelete not implemented")
}
func (UnimplementedOrderServer) EntrustBatchDelete(context.Context, *UpDateOrderEntrustsRequest) (*CommonCreateResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method EntrustBatchDelete not implemented")
}
func (UnimplementedOrderServer) EntrustCreate(context.Context, *EntrustRequest) (*CommonCreateResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method EntrustCreate not implemented")
}
func (UnimplementedOrderServer) EntrustCreates(context.Context, *EntrustListResponse) (*UpdateEntrustKeysResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method EntrustCreates not implemented")
}
func (UnimplementedOrderServer) GetEntrusts(context.Context, *EntrustList) (*EntrustListResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetEntrusts not implemented")
}
func (UnimplementedOrderServer) UpdateEntrustKeys(context.Context, *EntrustListResponse) (*UpdateEntrustKeysResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateEntrustKeys not implemented")
}
func (UnimplementedOrderServer) PayCreateFirstCache(context.Context, *PayCreateCacheRequest) (*CommonCreateResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PayCreateFirstCache not implemented")
}
func (UnimplementedOrderServer) PaySetCash(context.Context, *PaySetCashRequest) (*CommonResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PaySetCash not implemented")
}
func (UnimplementedOrderServer) PayCreate(context.Context, *PayCreateRequest) (*CommonCreateResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PayCreate not implemented")
}
func (UnimplementedOrderServer) PayComplete(context.Context, *PayCompleteRequest) (*PayCreateRequest, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PayComplete not implemented")
}
func (UnimplementedOrderServer) PaySellerSure(context.Context, *PaySellerSureRequest) (*PayCreateRequest, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PaySellerSure not implemented")
}
func (UnimplementedOrderServer) PayCancelByOutTradeNo(context.Context, *PaySellerSureRequest) (*PayCreateRequest, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PayCancelByOutTradeNo not implemented")
}
func (UnimplementedOrderServer) PayInfoByCode(context.Context, *PaySellerSureRequest) (*PayCreateRequest, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PayInfoByCode not implemented")
}
func (UnimplementedOrderServer) PayFail(context.Context, *PayCompleteRequest) (*CommonCreateResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PayFail not implemented")
}
func (UnimplementedOrderServer) PayDetail(context.Context, *PayDetailRequest) (*PayCreateRequest, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PayDetail not implemented")
}
func (UnimplementedOrderServer) PayDelete(context.Context, *CommonRequest) (*CommonResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PayDelete not implemented")
}
func (UnimplementedOrderServer) PayList(context.Context, *PayListRequest) (*PayListResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PayList not implemented")
}
func (UnimplementedOrderServer) PaySetExpress(context.Context, *PaySetExpressRequest) (*CommonResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PaySetExpress not implemented")
}
func (UnimplementedOrderServer) PaySetExpressRemark(context.Context, *PaySetExpressRequest) (*CommonResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PaySetExpressRemark not implemented")
}
func (UnimplementedOrderServer) PaySynSeriesData(context.Context, *PaySynSeriesDataRequest) (*CommonResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PaySynSeriesData not implemented")
}
func (UnimplementedOrderServer) PaySeriesDataList(context.Context, *PaySeriesDataListRequest) (*PaySeriesDataListResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PaySeriesDataList not implemented")
}
func (UnimplementedOrderServer) PayCollectionsDataList(context.Context, *PayCollectionsDataListRequest) (*PayCollectionsDataListResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PayCollectionsDataList not implemented")
}
func (UnimplementedOrderServer) PayCronSynFail(context.Context, *PayCronSynFailRequest) (*PayListResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PayCronSynFail not implemented")
}
func (UnimplementedOrderServer) PayCronSynSuccess(context.Context, *PayCronSynFailRequest) (*PayListResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PayCronSynSuccess not implemented")
}
func (UnimplementedOrderServer) PayRefund(context.Context, *PayDetailRequest) (*PayCreateRequest, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PayRefund not implemented")
}
func (UnimplementedOrderServer) PayGiveExist(context.Context, *PayGiveExistRequest) (*CommonResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PayGiveExist not implemented")
}
func (UnimplementedOrderServer) PayStaticIds(context.Context, *CommonRequest) (*PayStaticIdResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PayStaticIds not implemented")
}
func (UnimplementedOrderServer) SetPayCheck(context.Context, *SetPayCheckRequest) (*CommonResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SetPayCheck not implemented")
}
func (UnimplementedOrderServer) GetWipedOutCheckedNum(context.Context, *CommonRequest) (*GetWipedOutCheckedNumResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetWipedOutCheckedNum not implemented")
}
func (UnimplementedOrderServer) WechatJsApiPay(context.Context, *WechatJsApiPayRequest) (*WechatJsApiPayResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method WechatJsApiPay not implemented")
}
func (UnimplementedOrderServer) WechatJsApiQueryByOutTradeNo(context.Context, *WechatJsApiQueryByOutTradeNoRequest) (*WechatJsApiQueryByOutTradeNoResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method WechatJsApiQueryByOutTradeNo not implemented")
}
func (UnimplementedOrderServer) GetPayByOutTradeNo(context.Context, *GetPayByOutTradeNoRequest) (*GetPayByOutTradeNoResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetPayByOutTradeNo not implemented")
}
func (UnimplementedOrderServer) WechatJsApiRefunds(context.Context, *WechatJsApiRefundsRequest) (*WechatJsApiRefundsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method WechatJsApiRefunds not implemented")
}
func (UnimplementedOrderServer) SetPayOk(context.Context, *WechatPayOkRequest) (*CommonResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SetPayOk not implemented")
}
func (UnimplementedOrderServer) WechatAppPay(context.Context, *WechatAppPayRequest) (*WechatAppPayResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method WechatAppPay not implemented")
}
func (UnimplementedOrderServer) WechatAppQueryByOutTradeNo(context.Context, *WechatAppQueryByOutTradeNoRequest) (*WechatAppQueryByOutTradeNoResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method WechatAppQueryByOutTradeNo not implemented")
}
func (UnimplementedOrderServer) WechatNativePay(context.Context, *WechatNativePayRequest) (*WechatNativePayResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method WechatNativePay not implemented")
}
func (UnimplementedOrderServer) WechatNativeQueryByOutTradeNo(context.Context, *WechatNativeQueryByOutTradeNoRequest) (*WechatNativeQueryByOutTradeNoResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method WechatNativeQueryByOutTradeNo not implemented")
}
func (UnimplementedOrderServer) WechatRefundQueryByOutRefundNo(context.Context, *WechatRefundQueryByOutRefundNoRequest) (*WechatRefundQueryByOutRefundNoResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method WechatRefundQueryByOutRefundNo not implemented")
}
func (UnimplementedOrderServer) WechatH5Pay(context.Context, *WechatH5PayRequest) (*WechatH5PayResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method WechatH5Pay not implemented")
}
func (UnimplementedOrderServer) WechatH5QueryByOutTradeNo(context.Context, *WechatH5QueryByOutTradeNoRequest) (*WechatH5QueryByOutTradeNoResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method WechatH5QueryByOutTradeNo not implemented")
}
func (UnimplementedOrderServer) AliWapPay(context.Context, *AliWapPayRequest) (*AliWapPayResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AliWapPay not implemented")
}
func (UnimplementedOrderServer) AliAppPay(context.Context, *AliAppPayRequest) (*AliAppPayResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AliAppPay not implemented")
}
func (UnimplementedOrderServer) AliNativePay(context.Context, *AliNativePayRequest) (*AliNativePayResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AliNativePay not implemented")
}
func (UnimplementedOrderServer) AliPcWabPay(context.Context, *AliPcWabPayRequest) (*AliPcWabPayResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AliPcWabPay not implemented")
}
func (UnimplementedOrderServer) AliReFund(context.Context, *AliReFundRequest) (*AliReFundResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AliReFund not implemented")
}
func (UnimplementedOrderServer) AliNotify(context.Context, *AliNotifyRequest) (*AliNotifyResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AliNotify not implemented")
}
func (UnimplementedOrderServer) AliQueryByOutTradeNo(context.Context, *AliQueryByOutTradeNoRequest) (*AliQueryByOutTradeNoResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AliQueryByOutTradeNo not implemented")
}
func (UnimplementedOrderServer) AliRefundQueryByOutTradeNo(context.Context, *AliRefundQueryByOutTradeNoRequest) (*AliRefundQueryByOutTradeNoResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AliRefundQueryByOutTradeNo not implemented")
}
func (UnimplementedOrderServer) Bestow(context.Context, *BestowRequest) (*BestowResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Bestow not implemented")
}
func (UnimplementedOrderServer) ReceiveGift(context.Context, *ReceiveGiftRequest) (*ReceiveGiftResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ReceiveGift not implemented")
}
func (UnimplementedOrderServer) ApplyBlockchainAddress(context.Context, *ApplyBlockchainAddressRequest) (*ApplyBlockchainAddressResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ApplyBlockchainAddress not implemented")
}
func (UnimplementedOrderServer) ApplyCertificate(context.Context, *ApplyCertificateRequest) (*ApplyCertificateResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ApplyCertificate not implemented")
}
func (UnimplementedOrderServer) CancelBestow(context.Context, *CancelBestowRequest) (*CancelBestowResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CancelBestow not implemented")
}
func (UnimplementedOrderServer) GetBestowInfo(context.Context, *GetBestowInfoRequest) (*GetBestowInfoResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetBestowInfo not implemented")
}
func (UnimplementedOrderServer) InputPersonalBlockchain(context.Context, *InputPersonalBlockchainRequest) (*InputPersonalBlockchainResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method InputPersonalBlockchain not implemented")
}
func (UnimplementedOrderServer) ShipAddressCreate(context.Context, *ShipAddressCreateRequest) (*ShipAddressCreateResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ShipAddressCreate not implemented")
}
func (UnimplementedOrderServer) ShipAddressDelete(context.Context, *ShipAddressDeleteRequest) (*ShipAddressDeleteResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ShipAddressDelete not implemented")
}
func (UnimplementedOrderServer) ShipAddressList(context.Context, *ShipAddressListRequest) (*ShipAddressListResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ShipAddressList not implemented")
}
func (UnimplementedOrderServer) ShipAddressDefault(context.Context, *ShipAddressDefaultRequest) (*ShipAddressDefaultResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ShipAddressDefault not implemented")
}
func (UnimplementedOrderServer) GetAddressInfoById(context.Context, *GetAddressInfoByIdRequest) (*GetAddressInfoByIdResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetAddressInfoById not implemented")
}
func (UnimplementedOrderServer) LogisticsTracking(context.Context, *LogisticsTrackingRequest) (*LogisticsTrackingResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method LogisticsTracking not implemented")
}
func (UnimplementedOrderServer) AddTrackingNumber(context.Context, *AddTrackingNumberRequest) (*AddTrackingNumberResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AddTrackingNumber not implemented")
}
func (UnimplementedOrderServer) RecordNotify(context.Context, *RecordNotifyRequest) (*CommonResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RecordNotify not implemented")
}
func (UnimplementedOrderServer) CreateStripeCheckoutSession(context.Context, *CreateStripeCheckoutSessionRequest) (*CreateStripeCheckoutSessionResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateStripeCheckoutSession not implemented")
}
func (UnimplementedOrderServer) GetStripePaymentIntentInfo(context.Context, *GetStripePaymentIntentInfoRequest) (*GetStripePaymentIntentInfoResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetStripePaymentIntentInfo not implemented")
}
func (UnimplementedOrderServer) CreateStripeRefund(context.Context, *CreateStripeRefundRequest) (*CreateStripeRefundResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateStripeRefund not implemented")
}
func (UnimplementedOrderServer) GetRefundInfo(context.Context, *GetRefundInfoRequest) (*GetRefundInfoResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetRefundInfo not implemented")
}
func (UnimplementedOrderServer) GetCheckoutWebhook(context.Context, *GetCheckoutWebhookRequest) (*GetCheckoutWebhookResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetCheckoutWebhook not implemented")
}
func (UnimplementedOrderServer) CommonCheckoutWebhook(context.Context, *GetCheckoutWebhookRequest) (*GetCheckoutWebhookResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CommonCheckoutWebhook not implemented")
}
func (UnimplementedOrderServer) QueryStripeInfoByCheckSessionIds(context.Context, *QueryStripeInfoRequest) (*QueryStripeInfoResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method QueryStripeInfoByCheckSessionIds not implemented")
}
func (UnimplementedOrderServer) CreateStripePaymentIntent(context.Context, *CreateStripePaymentIntentRequest) (*CreateStripePaymentIntentResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateStripePaymentIntent not implemented")
}
func (UnimplementedOrderServer) CommonStripePayemntIntentWebhook(context.Context, *GetCheckoutWebhookRequest) (*GetCheckoutWebhookResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CommonStripePayemntIntentWebhook not implemented")
}
func (s *UnimplementedOrderServer) XXX_SetProxyImpl(impl protocol.Invoker) {
	s.proxyImpl = impl
}

func (s *UnimplementedOrderServer) XXX_GetProxyImpl() protocol.Invoker {
	return s.proxyImpl
}

func (s *UnimplementedOrderServer) XXX_ServiceDesc() *grpc_go.ServiceDesc {
	return &Order_ServiceDesc
}
func (s *UnimplementedOrderServer) XXX_InterfaceName() string {
	return "order.Order"
}

func (UnimplementedOrderServer) mustEmbedUnimplementedOrderServer() {}

// UnsafeOrderServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to OrderServer will
// result in compilation errors.
type UnsafeOrderServer interface {
	mustEmbedUnimplementedOrderServer()
}

func RegisterOrderServer(s grpc_go.ServiceRegistrar, srv OrderServer) {
	s.RegisterService(&Order_ServiceDesc, srv)
}

func _Order_CreateLog_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(LogRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("CreateLog", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Order_OrderUpdate_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(OrderRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("OrderUpdate", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Order_OrderUpdateByUid_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(OrderRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("OrderUpdateByUid", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Order_OrderUpdateKeys_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(OrderRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("OrderUpdateKeys", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Order_OrderBatchUpdate_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(OrderBatchUpdateRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("OrderBatchUpdate", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Order_OrderExBatchUpdate_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(OrderBatchUpdateRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("OrderExBatchUpdate", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Order_OrderDelete_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(CommonRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("OrderDelete", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Order_GetTransactions_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(TransactionsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("GetTransactions", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Order_GetTransactionStage_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(TransactionStageRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("GetTransactionStage", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Order_GetBlankOrder_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(TransactionStageRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("GetBlankOrder", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Order_GetBlankOrderByInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(BlankOrderReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("GetBlankOrderByInfo", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Order_GetSellerTransactions_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(TransactionsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("GetSellerTransactions", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Order_GetTransaction_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(CommonRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("GetTransaction", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Order_SaveTransactionSages_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(StageRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("SaveTransactionSages", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Order_GetOrders_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(OrderList)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("GetOrders", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Order_GetOrder_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(CommonRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("GetOrder", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Order_RandOrders_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(RandOrderRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("RandOrders", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Order_UpdateOrderSellerId_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateSellerIdRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("UpdateOrderSellerId", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Order_UpDateOrderEntrusts_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(RandOrderRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("UpDateOrderEntrusts", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Order_OrderCreate_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(OrderRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("OrderCreate", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Order_SetStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetStatusRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("SetStatus", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Order_OrderStatisticsInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(OrderStatisticsInfoRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("OrderStatisticsInfo", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Order_OrdersByArtworkNum_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(OrdersByArtworkNumRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("OrdersByArtworkNum", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Order_OrderStageSetStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(OrderStageSetStatusReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("OrderStageSetStatus", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Order_SynTransactionStage_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(CommonRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("SynTransactionStage", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Order_UpdateTransactionDate_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateTransactionDateReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("UpdateTransactionDate", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Order_UpdateTransactionStage_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateTransactionStageReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("UpdateTransactionStage", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Order_GetReportUsers_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(ReportUserList)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("GetReportUsers", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Order_GetReportUser_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(ReportUserDetail)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("GetReportUser", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Order_GetReport_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(ReportDetail)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("GetReport", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Order_ReportDelete_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(ReportDetail)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("ReportDelete", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Order_ReportCreate_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(ReportCreateRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("ReportCreate", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Order_ReportPublic_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(ReportDetail)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("ReportPublic", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Order_GetReports_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(ReportList)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("GetReports", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Order_BeforeCreateUsers_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(ReportCreateRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("BeforeCreateUsers", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Order_GetCheckSalesAmount_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(ReportCheckSales)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("GetCheckSalesAmount", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Order_SetReportStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetStatusRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("SetReportStatus", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Order_UpdateReportUsers_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateReportUserList)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("UpdateReportUsers", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Order_GetReportQuery_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(ReportQueryRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("GetReportQuery", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Order_ReportRead_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(ReportReadRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("ReportRead", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Order_Result_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(ResultRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("Result", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Order_Results_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(ResultsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("Results", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Order_CreateWeekly_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(WeeklyRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("CreateWeekly", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Order_UpdateWeekly_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(WeeklyRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("UpdateWeekly", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Order_DetermineCanCreateWeekly_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(CanWeeklyRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("DetermineCanCreateWeekly", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Order_WeeklyList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(WeeklyListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("WeeklyList", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Order_WeeklyInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(WeeklyInfoRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("WeeklyInfo", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Order_WeeklyOaInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(WeeklyInfoRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("WeeklyOaInfo", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Order_WeeklyRead_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(WeeklyReadRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("WeeklyRead", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Order_WeeklyModifiedNotCommitted_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(WeeklyResponse)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("WeeklyModifiedNotCommitted", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Order_GetSmsConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(SmsConfigRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("GetSmsConfig", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Order_CreateWeeklyComment_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(WeeklyCommentRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("CreateWeeklyComment", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Order_GetWeeklyComment_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetWeeklyCommentRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("GetWeeklyComment", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Order_CreateStaffWeekly_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(StaffWeeklyReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("CreateStaffWeekly", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Order_UpdateStaffWeekly_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(StaffWeeklyReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("UpdateStaffWeekly", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Order_DetermineCanStaffWeekly_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(CanWeeklyRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("DetermineCanStaffWeekly", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Order_StaffWeeklyList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(StaffWeeklyListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("StaffWeeklyList", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Order_StaffWeeklyInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(ReportUserDetail)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("StaffWeeklyInfo", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Order_GetEntrust_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(CommonRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("GetEntrust", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Order_EntrustDelete_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(CommonRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("EntrustDelete", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Order_EntrustBatchDelete_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpDateOrderEntrustsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("EntrustBatchDelete", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Order_EntrustCreate_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(EntrustRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("EntrustCreate", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Order_EntrustCreates_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(EntrustListResponse)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("EntrustCreates", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Order_GetEntrusts_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(EntrustList)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("GetEntrusts", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Order_UpdateEntrustKeys_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(EntrustListResponse)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("UpdateEntrustKeys", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Order_PayCreateFirstCache_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(PayCreateCacheRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("PayCreateFirstCache", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Order_PaySetCash_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(PaySetCashRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("PaySetCash", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Order_PayCreate_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(PayCreateRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("PayCreate", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Order_PayComplete_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(PayCompleteRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("PayComplete", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Order_PaySellerSure_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(PaySellerSureRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("PaySellerSure", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Order_PayCancelByOutTradeNo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(PaySellerSureRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("PayCancelByOutTradeNo", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Order_PayInfoByCode_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(PaySellerSureRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("PayInfoByCode", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Order_PayFail_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(PayCompleteRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("PayFail", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Order_PayDetail_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(PayDetailRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("PayDetail", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Order_PayDelete_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(CommonRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("PayDelete", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Order_PayList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(PayListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("PayList", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Order_PaySetExpress_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(PaySetExpressRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("PaySetExpress", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Order_PaySetExpressRemark_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(PaySetExpressRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("PaySetExpressRemark", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Order_PaySynSeriesData_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(PaySynSeriesDataRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("PaySynSeriesData", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Order_PaySeriesDataList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(PaySeriesDataListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("PaySeriesDataList", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Order_PayCollectionsDataList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(PayCollectionsDataListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("PayCollectionsDataList", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Order_PayCronSynFail_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(PayCronSynFailRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("PayCronSynFail", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Order_PayCronSynSuccess_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(PayCronSynFailRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("PayCronSynSuccess", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Order_PayRefund_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(PayDetailRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("PayRefund", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Order_PayGiveExist_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(PayGiveExistRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("PayGiveExist", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Order_PayStaticIds_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(CommonRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("PayStaticIds", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Order_SetPayCheck_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetPayCheckRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("SetPayCheck", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Order_GetWipedOutCheckedNum_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(CommonRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("GetWipedOutCheckedNum", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Order_WechatJsApiPay_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(WechatJsApiPayRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("WechatJsApiPay", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Order_WechatJsApiQueryByOutTradeNo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(WechatJsApiQueryByOutTradeNoRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("WechatJsApiQueryByOutTradeNo", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Order_GetPayByOutTradeNo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetPayByOutTradeNoRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("GetPayByOutTradeNo", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Order_WechatJsApiRefunds_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(WechatJsApiRefundsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("WechatJsApiRefunds", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Order_SetPayOk_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(WechatPayOkRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("SetPayOk", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Order_WechatAppPay_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(WechatAppPayRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("WechatAppPay", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Order_WechatAppQueryByOutTradeNo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(WechatAppQueryByOutTradeNoRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("WechatAppQueryByOutTradeNo", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Order_WechatNativePay_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(WechatNativePayRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("WechatNativePay", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Order_WechatNativeQueryByOutTradeNo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(WechatNativeQueryByOutTradeNoRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("WechatNativeQueryByOutTradeNo", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Order_WechatRefundQueryByOutRefundNo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(WechatRefundQueryByOutRefundNoRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("WechatRefundQueryByOutRefundNo", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Order_WechatH5Pay_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(WechatH5PayRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("WechatH5Pay", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Order_WechatH5QueryByOutTradeNo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(WechatH5QueryByOutTradeNoRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("WechatH5QueryByOutTradeNo", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Order_AliWapPay_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(AliWapPayRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("AliWapPay", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Order_AliAppPay_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(AliAppPayRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("AliAppPay", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Order_AliNativePay_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(AliNativePayRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("AliNativePay", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Order_AliPcWabPay_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(AliPcWabPayRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("AliPcWabPay", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Order_AliReFund_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(AliReFundRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("AliReFund", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Order_AliNotify_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(AliNotifyRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("AliNotify", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Order_AliQueryByOutTradeNo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(AliQueryByOutTradeNoRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("AliQueryByOutTradeNo", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Order_AliRefundQueryByOutTradeNo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(AliRefundQueryByOutTradeNoRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("AliRefundQueryByOutTradeNo", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Order_Bestow_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(BestowRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("Bestow", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Order_ReceiveGift_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(ReceiveGiftRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("ReceiveGift", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Order_ApplyBlockchainAddress_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(ApplyBlockchainAddressRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("ApplyBlockchainAddress", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Order_ApplyCertificate_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(ApplyCertificateRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("ApplyCertificate", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Order_CancelBestow_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(CancelBestowRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("CancelBestow", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Order_GetBestowInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetBestowInfoRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("GetBestowInfo", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Order_InputPersonalBlockchain_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(InputPersonalBlockchainRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("InputPersonalBlockchain", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Order_ShipAddressCreate_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(ShipAddressCreateRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("ShipAddressCreate", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Order_ShipAddressDelete_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(ShipAddressDeleteRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("ShipAddressDelete", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Order_ShipAddressList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(ShipAddressListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("ShipAddressList", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Order_ShipAddressDefault_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(ShipAddressDefaultRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("ShipAddressDefault", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Order_GetAddressInfoById_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAddressInfoByIdRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("GetAddressInfoById", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Order_LogisticsTracking_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(LogisticsTrackingRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("LogisticsTracking", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Order_AddTrackingNumber_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddTrackingNumberRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("AddTrackingNumber", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Order_RecordNotify_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(RecordNotifyRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("RecordNotify", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Order_CreateStripeCheckoutSession_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateStripeCheckoutSessionRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("CreateStripeCheckoutSession", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Order_GetStripePaymentIntentInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetStripePaymentIntentInfoRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("GetStripePaymentIntentInfo", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Order_CreateStripeRefund_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateStripeRefundRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("CreateStripeRefund", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Order_GetRefundInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetRefundInfoRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("GetRefundInfo", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Order_GetCheckoutWebhook_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetCheckoutWebhookRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("GetCheckoutWebhook", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Order_CommonCheckoutWebhook_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetCheckoutWebhookRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("CommonCheckoutWebhook", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Order_QueryStripeInfoByCheckSessionIds_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(QueryStripeInfoRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("QueryStripeInfoByCheckSessionIds", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Order_CreateStripePaymentIntent_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateStripePaymentIntentRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("CreateStripePaymentIntent", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Order_CommonStripePayemntIntentWebhook_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetCheckoutWebhookRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("CommonStripePayemntIntentWebhook", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

// Order_ServiceDesc is the grpc_go.ServiceDesc for Order service.
// It's only intended for direct use with grpc_go.RegisterService,
// and not to be introspected or modified (even as a copy)
var Order_ServiceDesc = grpc_go.ServiceDesc{
	ServiceName: "order.Order",
	HandlerType: (*OrderServer)(nil),
	Methods: []grpc_go.MethodDesc{
		{
			MethodName: "CreateLog",
			Handler:    _Order_CreateLog_Handler,
		},
		{
			MethodName: "OrderUpdate",
			Handler:    _Order_OrderUpdate_Handler,
		},
		{
			MethodName: "OrderUpdateByUid",
			Handler:    _Order_OrderUpdateByUid_Handler,
		},
		{
			MethodName: "OrderUpdateKeys",
			Handler:    _Order_OrderUpdateKeys_Handler,
		},
		{
			MethodName: "OrderBatchUpdate",
			Handler:    _Order_OrderBatchUpdate_Handler,
		},
		{
			MethodName: "OrderExBatchUpdate",
			Handler:    _Order_OrderExBatchUpdate_Handler,
		},
		{
			MethodName: "OrderDelete",
			Handler:    _Order_OrderDelete_Handler,
		},
		{
			MethodName: "GetTransactions",
			Handler:    _Order_GetTransactions_Handler,
		},
		{
			MethodName: "GetTransactionStage",
			Handler:    _Order_GetTransactionStage_Handler,
		},
		{
			MethodName: "GetBlankOrder",
			Handler:    _Order_GetBlankOrder_Handler,
		},
		{
			MethodName: "GetBlankOrderByInfo",
			Handler:    _Order_GetBlankOrderByInfo_Handler,
		},
		{
			MethodName: "GetSellerTransactions",
			Handler:    _Order_GetSellerTransactions_Handler,
		},
		{
			MethodName: "GetTransaction",
			Handler:    _Order_GetTransaction_Handler,
		},
		{
			MethodName: "SaveTransactionSages",
			Handler:    _Order_SaveTransactionSages_Handler,
		},
		{
			MethodName: "GetOrders",
			Handler:    _Order_GetOrders_Handler,
		},
		{
			MethodName: "GetOrder",
			Handler:    _Order_GetOrder_Handler,
		},
		{
			MethodName: "RandOrders",
			Handler:    _Order_RandOrders_Handler,
		},
		{
			MethodName: "UpdateOrderSellerId",
			Handler:    _Order_UpdateOrderSellerId_Handler,
		},
		{
			MethodName: "UpDateOrderEntrusts",
			Handler:    _Order_UpDateOrderEntrusts_Handler,
		},
		{
			MethodName: "OrderCreate",
			Handler:    _Order_OrderCreate_Handler,
		},
		{
			MethodName: "SetStatus",
			Handler:    _Order_SetStatus_Handler,
		},
		{
			MethodName: "OrderStatisticsInfo",
			Handler:    _Order_OrderStatisticsInfo_Handler,
		},
		{
			MethodName: "OrdersByArtworkNum",
			Handler:    _Order_OrdersByArtworkNum_Handler,
		},
		{
			MethodName: "OrderStageSetStatus",
			Handler:    _Order_OrderStageSetStatus_Handler,
		},
		{
			MethodName: "SynTransactionStage",
			Handler:    _Order_SynTransactionStage_Handler,
		},
		{
			MethodName: "UpdateTransactionDate",
			Handler:    _Order_UpdateTransactionDate_Handler,
		},
		{
			MethodName: "UpdateTransactionStage",
			Handler:    _Order_UpdateTransactionStage_Handler,
		},
		{
			MethodName: "GetReportUsers",
			Handler:    _Order_GetReportUsers_Handler,
		},
		{
			MethodName: "GetReportUser",
			Handler:    _Order_GetReportUser_Handler,
		},
		{
			MethodName: "GetReport",
			Handler:    _Order_GetReport_Handler,
		},
		{
			MethodName: "ReportDelete",
			Handler:    _Order_ReportDelete_Handler,
		},
		{
			MethodName: "ReportCreate",
			Handler:    _Order_ReportCreate_Handler,
		},
		{
			MethodName: "ReportPublic",
			Handler:    _Order_ReportPublic_Handler,
		},
		{
			MethodName: "GetReports",
			Handler:    _Order_GetReports_Handler,
		},
		{
			MethodName: "BeforeCreateUsers",
			Handler:    _Order_BeforeCreateUsers_Handler,
		},
		{
			MethodName: "GetCheckSalesAmount",
			Handler:    _Order_GetCheckSalesAmount_Handler,
		},
		{
			MethodName: "SetReportStatus",
			Handler:    _Order_SetReportStatus_Handler,
		},
		{
			MethodName: "UpdateReportUsers",
			Handler:    _Order_UpdateReportUsers_Handler,
		},
		{
			MethodName: "GetReportQuery",
			Handler:    _Order_GetReportQuery_Handler,
		},
		{
			MethodName: "ReportRead",
			Handler:    _Order_ReportRead_Handler,
		},
		{
			MethodName: "Result",
			Handler:    _Order_Result_Handler,
		},
		{
			MethodName: "Results",
			Handler:    _Order_Results_Handler,
		},
		{
			MethodName: "CreateWeekly",
			Handler:    _Order_CreateWeekly_Handler,
		},
		{
			MethodName: "UpdateWeekly",
			Handler:    _Order_UpdateWeekly_Handler,
		},
		{
			MethodName: "DetermineCanCreateWeekly",
			Handler:    _Order_DetermineCanCreateWeekly_Handler,
		},
		{
			MethodName: "WeeklyList",
			Handler:    _Order_WeeklyList_Handler,
		},
		{
			MethodName: "WeeklyInfo",
			Handler:    _Order_WeeklyInfo_Handler,
		},
		{
			MethodName: "WeeklyOaInfo",
			Handler:    _Order_WeeklyOaInfo_Handler,
		},
		{
			MethodName: "WeeklyRead",
			Handler:    _Order_WeeklyRead_Handler,
		},
		{
			MethodName: "WeeklyModifiedNotCommitted",
			Handler:    _Order_WeeklyModifiedNotCommitted_Handler,
		},
		{
			MethodName: "GetSmsConfig",
			Handler:    _Order_GetSmsConfig_Handler,
		},
		{
			MethodName: "CreateWeeklyComment",
			Handler:    _Order_CreateWeeklyComment_Handler,
		},
		{
			MethodName: "GetWeeklyComment",
			Handler:    _Order_GetWeeklyComment_Handler,
		},
		{
			MethodName: "CreateStaffWeekly",
			Handler:    _Order_CreateStaffWeekly_Handler,
		},
		{
			MethodName: "UpdateStaffWeekly",
			Handler:    _Order_UpdateStaffWeekly_Handler,
		},
		{
			MethodName: "DetermineCanStaffWeekly",
			Handler:    _Order_DetermineCanStaffWeekly_Handler,
		},
		{
			MethodName: "StaffWeeklyList",
			Handler:    _Order_StaffWeeklyList_Handler,
		},
		{
			MethodName: "StaffWeeklyInfo",
			Handler:    _Order_StaffWeeklyInfo_Handler,
		},
		{
			MethodName: "GetEntrust",
			Handler:    _Order_GetEntrust_Handler,
		},
		{
			MethodName: "EntrustDelete",
			Handler:    _Order_EntrustDelete_Handler,
		},
		{
			MethodName: "EntrustBatchDelete",
			Handler:    _Order_EntrustBatchDelete_Handler,
		},
		{
			MethodName: "EntrustCreate",
			Handler:    _Order_EntrustCreate_Handler,
		},
		{
			MethodName: "EntrustCreates",
			Handler:    _Order_EntrustCreates_Handler,
		},
		{
			MethodName: "GetEntrusts",
			Handler:    _Order_GetEntrusts_Handler,
		},
		{
			MethodName: "UpdateEntrustKeys",
			Handler:    _Order_UpdateEntrustKeys_Handler,
		},
		{
			MethodName: "PayCreateFirstCache",
			Handler:    _Order_PayCreateFirstCache_Handler,
		},
		{
			MethodName: "PaySetCash",
			Handler:    _Order_PaySetCash_Handler,
		},
		{
			MethodName: "PayCreate",
			Handler:    _Order_PayCreate_Handler,
		},
		{
			MethodName: "PayComplete",
			Handler:    _Order_PayComplete_Handler,
		},
		{
			MethodName: "PaySellerSure",
			Handler:    _Order_PaySellerSure_Handler,
		},
		{
			MethodName: "PayCancelByOutTradeNo",
			Handler:    _Order_PayCancelByOutTradeNo_Handler,
		},
		{
			MethodName: "PayInfoByCode",
			Handler:    _Order_PayInfoByCode_Handler,
		},
		{
			MethodName: "PayFail",
			Handler:    _Order_PayFail_Handler,
		},
		{
			MethodName: "PayDetail",
			Handler:    _Order_PayDetail_Handler,
		},
		{
			MethodName: "PayDelete",
			Handler:    _Order_PayDelete_Handler,
		},
		{
			MethodName: "PayList",
			Handler:    _Order_PayList_Handler,
		},
		{
			MethodName: "PaySetExpress",
			Handler:    _Order_PaySetExpress_Handler,
		},
		{
			MethodName: "PaySetExpressRemark",
			Handler:    _Order_PaySetExpressRemark_Handler,
		},
		{
			MethodName: "PaySynSeriesData",
			Handler:    _Order_PaySynSeriesData_Handler,
		},
		{
			MethodName: "PaySeriesDataList",
			Handler:    _Order_PaySeriesDataList_Handler,
		},
		{
			MethodName: "PayCollectionsDataList",
			Handler:    _Order_PayCollectionsDataList_Handler,
		},
		{
			MethodName: "PayCronSynFail",
			Handler:    _Order_PayCronSynFail_Handler,
		},
		{
			MethodName: "PayCronSynSuccess",
			Handler:    _Order_PayCronSynSuccess_Handler,
		},
		{
			MethodName: "PayRefund",
			Handler:    _Order_PayRefund_Handler,
		},
		{
			MethodName: "PayGiveExist",
			Handler:    _Order_PayGiveExist_Handler,
		},
		{
			MethodName: "PayStaticIds",
			Handler:    _Order_PayStaticIds_Handler,
		},
		{
			MethodName: "SetPayCheck",
			Handler:    _Order_SetPayCheck_Handler,
		},
		{
			MethodName: "GetWipedOutCheckedNum",
			Handler:    _Order_GetWipedOutCheckedNum_Handler,
		},
		{
			MethodName: "WechatJsApiPay",
			Handler:    _Order_WechatJsApiPay_Handler,
		},
		{
			MethodName: "WechatJsApiQueryByOutTradeNo",
			Handler:    _Order_WechatJsApiQueryByOutTradeNo_Handler,
		},
		{
			MethodName: "GetPayByOutTradeNo",
			Handler:    _Order_GetPayByOutTradeNo_Handler,
		},
		{
			MethodName: "WechatJsApiRefunds",
			Handler:    _Order_WechatJsApiRefunds_Handler,
		},
		{
			MethodName: "SetPayOk",
			Handler:    _Order_SetPayOk_Handler,
		},
		{
			MethodName: "WechatAppPay",
			Handler:    _Order_WechatAppPay_Handler,
		},
		{
			MethodName: "WechatAppQueryByOutTradeNo",
			Handler:    _Order_WechatAppQueryByOutTradeNo_Handler,
		},
		{
			MethodName: "WechatNativePay",
			Handler:    _Order_WechatNativePay_Handler,
		},
		{
			MethodName: "WechatNativeQueryByOutTradeNo",
			Handler:    _Order_WechatNativeQueryByOutTradeNo_Handler,
		},
		{
			MethodName: "WechatRefundQueryByOutRefundNo",
			Handler:    _Order_WechatRefundQueryByOutRefundNo_Handler,
		},
		{
			MethodName: "WechatH5Pay",
			Handler:    _Order_WechatH5Pay_Handler,
		},
		{
			MethodName: "WechatH5QueryByOutTradeNo",
			Handler:    _Order_WechatH5QueryByOutTradeNo_Handler,
		},
		{
			MethodName: "AliWapPay",
			Handler:    _Order_AliWapPay_Handler,
		},
		{
			MethodName: "AliAppPay",
			Handler:    _Order_AliAppPay_Handler,
		},
		{
			MethodName: "AliNativePay",
			Handler:    _Order_AliNativePay_Handler,
		},
		{
			MethodName: "AliPcWabPay",
			Handler:    _Order_AliPcWabPay_Handler,
		},
		{
			MethodName: "AliReFund",
			Handler:    _Order_AliReFund_Handler,
		},
		{
			MethodName: "AliNotify",
			Handler:    _Order_AliNotify_Handler,
		},
		{
			MethodName: "AliQueryByOutTradeNo",
			Handler:    _Order_AliQueryByOutTradeNo_Handler,
		},
		{
			MethodName: "AliRefundQueryByOutTradeNo",
			Handler:    _Order_AliRefundQueryByOutTradeNo_Handler,
		},
		{
			MethodName: "Bestow",
			Handler:    _Order_Bestow_Handler,
		},
		{
			MethodName: "ReceiveGift",
			Handler:    _Order_ReceiveGift_Handler,
		},
		{
			MethodName: "ApplyBlockchainAddress",
			Handler:    _Order_ApplyBlockchainAddress_Handler,
		},
		{
			MethodName: "ApplyCertificate",
			Handler:    _Order_ApplyCertificate_Handler,
		},
		{
			MethodName: "CancelBestow",
			Handler:    _Order_CancelBestow_Handler,
		},
		{
			MethodName: "GetBestowInfo",
			Handler:    _Order_GetBestowInfo_Handler,
		},
		{
			MethodName: "InputPersonalBlockchain",
			Handler:    _Order_InputPersonalBlockchain_Handler,
		},
		{
			MethodName: "ShipAddressCreate",
			Handler:    _Order_ShipAddressCreate_Handler,
		},
		{
			MethodName: "ShipAddressDelete",
			Handler:    _Order_ShipAddressDelete_Handler,
		},
		{
			MethodName: "ShipAddressList",
			Handler:    _Order_ShipAddressList_Handler,
		},
		{
			MethodName: "ShipAddressDefault",
			Handler:    _Order_ShipAddressDefault_Handler,
		},
		{
			MethodName: "GetAddressInfoById",
			Handler:    _Order_GetAddressInfoById_Handler,
		},
		{
			MethodName: "LogisticsTracking",
			Handler:    _Order_LogisticsTracking_Handler,
		},
		{
			MethodName: "AddTrackingNumber",
			Handler:    _Order_AddTrackingNumber_Handler,
		},
		{
			MethodName: "RecordNotify",
			Handler:    _Order_RecordNotify_Handler,
		},
		{
			MethodName: "CreateStripeCheckoutSession",
			Handler:    _Order_CreateStripeCheckoutSession_Handler,
		},
		{
			MethodName: "GetStripePaymentIntentInfo",
			Handler:    _Order_GetStripePaymentIntentInfo_Handler,
		},
		{
			MethodName: "CreateStripeRefund",
			Handler:    _Order_CreateStripeRefund_Handler,
		},
		{
			MethodName: "GetRefundInfo",
			Handler:    _Order_GetRefundInfo_Handler,
		},
		{
			MethodName: "GetCheckoutWebhook",
			Handler:    _Order_GetCheckoutWebhook_Handler,
		},
		{
			MethodName: "CommonCheckoutWebhook",
			Handler:    _Order_CommonCheckoutWebhook_Handler,
		},
		{
			MethodName: "QueryStripeInfoByCheckSessionIds",
			Handler:    _Order_QueryStripeInfoByCheckSessionIds_Handler,
		},
		{
			MethodName: "CreateStripePaymentIntent",
			Handler:    _Order_CreateStripePaymentIntent_Handler,
		},
		{
			MethodName: "CommonStripePayemntIntentWebhook",
			Handler:    _Order_CommonStripePayemntIntentWebhook_Handler,
		},
	},
	Streams:  []grpc_go.StreamDesc{},
	Metadata: "api/order/order.proto",
}
