//
// Licensed to the Apache Software Foundation (ASF) under one or more
// contributor license agreements.  See the NOTICE file distributed with
// this work for additional information regarding copyright ownership.
// The ASF licenses this file to You under the Apache License, Version 2.0
// (the "License"); you may not use this file except in compliance with
// the License.  You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.29.1
// 	protoc        v3.21.12
// source: api/rule/rule.proto

package rule

import (
	_ "github.com/mwitkow/go-proto-validators"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type UserInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UserId   uint64 `protobuf:"varint,1,opt,name=UserId,json=userId,proto3" json:"UserId,omitempty"`
	UserName string `protobuf:"bytes,2,opt,name=UserName,json=userName,proto3" json:"UserName,omitempty"`
}

func (x *UserInfo) Reset() {
	*x = UserInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_rule_rule_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UserInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserInfo) ProtoMessage() {}

func (x *UserInfo) ProtoReflect() protoreflect.Message {
	mi := &file_api_rule_rule_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserInfo.ProtoReflect.Descriptor instead.
func (*UserInfo) Descriptor() ([]byte, []int) {
	return file_api_rule_rule_proto_rawDescGZIP(), []int{0}
}

func (x *UserInfo) GetUserId() uint64 {
	if x != nil {
		return x.UserId
	}
	return 0
}

func (x *UserInfo) GetUserName() string {
	if x != nil {
		return x.UserName
	}
	return ""
}

type UserInfos struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	List []*UserInfo `protobuf:"bytes,1,rep,name=List,json=list,proto3" json:"List,omitempty"`
}

func (x *UserInfos) Reset() {
	*x = UserInfos{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_rule_rule_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UserInfos) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserInfos) ProtoMessage() {}

func (x *UserInfos) ProtoReflect() protoreflect.Message {
	mi := &file_api_rule_rule_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserInfos.ProtoReflect.Descriptor instead.
func (*UserInfos) Descriptor() ([]byte, []int) {
	return file_api_rule_rule_proto_rawDescGZIP(), []int{1}
}

func (x *UserInfos) GetList() []*UserInfo {
	if x != nil {
		return x.List
	}
	return nil
}

type PositionUserListResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Count uint64          `protobuf:"varint,1,opt,name=Count,json=count,proto3" json:"Count,omitempty"`
	Data  []*PositionUser `protobuf:"bytes,2,rep,name=Data,json=data,proto3" json:"Data,omitempty"`
}

func (x *PositionUserListResponse) Reset() {
	*x = PositionUserListResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_rule_rule_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PositionUserListResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PositionUserListResponse) ProtoMessage() {}

func (x *PositionUserListResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_rule_rule_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PositionUserListResponse.ProtoReflect.Descriptor instead.
func (*PositionUserListResponse) Descriptor() ([]byte, []int) {
	return file_api_rule_rule_proto_rawDescGZIP(), []int{2}
}

func (x *PositionUserListResponse) GetCount() uint64 {
	if x != nil {
		return x.Count
	}
	return 0
}

func (x *PositionUserListResponse) GetData() []*PositionUser {
	if x != nil {
		return x.Data
	}
	return nil
}

type PositionUserListRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Domain       string   `protobuf:"bytes,1,opt,name=Domain,json=domain,proto3" json:"Domain,omitempty"`
	DepartmentId uint64   `protobuf:"varint,2,opt,name=DepartmentId,json=departmentId,proto3" json:"DepartmentId,omitempty"`
	PositionId   uint64   `protobuf:"varint,3,opt,name=PositionId,json=positionId,proto3" json:"PositionId,omitempty"`
	SiteUidS     []string `protobuf:"bytes,4,rep,name=siteUidS,proto3" json:"siteUidS,omitempty"`
}

func (x *PositionUserListRequest) Reset() {
	*x = PositionUserListRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_rule_rule_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PositionUserListRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PositionUserListRequest) ProtoMessage() {}

func (x *PositionUserListRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_rule_rule_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PositionUserListRequest.ProtoReflect.Descriptor instead.
func (*PositionUserListRequest) Descriptor() ([]byte, []int) {
	return file_api_rule_rule_proto_rawDescGZIP(), []int{3}
}

func (x *PositionUserListRequest) GetDomain() string {
	if x != nil {
		return x.Domain
	}
	return ""
}

func (x *PositionUserListRequest) GetDepartmentId() uint64 {
	if x != nil {
		return x.DepartmentId
	}
	return 0
}

func (x *PositionUserListRequest) GetPositionId() uint64 {
	if x != nil {
		return x.PositionId
	}
	return 0
}

func (x *PositionUserListRequest) GetSiteUidS() []string {
	if x != nil {
		return x.SiteUidS
	}
	return nil
}

//数据权限
type RuleData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	DataField []string `protobuf:"bytes,1,rep,name=DataField,json=dataField,proto3" json:"DataField,omitempty"`
	Name      string   `protobuf:"bytes,2,opt,name=Name,json=name,proto3" json:"Name,omitempty"`
	ID        uint64   `protobuf:"varint,3,opt,name=ID,proto3" json:"ID,omitempty"`
}

func (x *RuleData) Reset() {
	*x = RuleData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_rule_rule_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RuleData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RuleData) ProtoMessage() {}

func (x *RuleData) ProtoReflect() protoreflect.Message {
	mi := &file_api_rule_rule_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RuleData.ProtoReflect.Descriptor instead.
func (*RuleData) Descriptor() ([]byte, []int) {
	return file_api_rule_rule_proto_rawDescGZIP(), []int{4}
}

func (x *RuleData) GetDataField() []string {
	if x != nil {
		return x.DataField
	}
	return nil
}

func (x *RuleData) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *RuleData) GetID() uint64 {
	if x != nil {
		return x.ID
	}
	return 0
}

//数据权限
type RuleField struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ID          uint64 `protobuf:"varint,1,opt,name=ID,proto3" json:"ID,omitempty"`
	FieldCnName string `protobuf:"bytes,2,opt,name=fieldCnName,proto3" json:"fieldCnName,omitempty"`
	FieldKey    string `protobuf:"bytes,3,opt,name=fieldKey,proto3" json:"fieldKey,omitempty"`
}

func (x *RuleField) Reset() {
	*x = RuleField{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_rule_rule_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RuleField) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RuleField) ProtoMessage() {}

func (x *RuleField) ProtoReflect() protoreflect.Message {
	mi := &file_api_rule_rule_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RuleField.ProtoReflect.Descriptor instead.
func (*RuleField) Descriptor() ([]byte, []int) {
	return file_api_rule_rule_proto_rawDescGZIP(), []int{5}
}

func (x *RuleField) GetID() uint64 {
	if x != nil {
		return x.ID
	}
	return 0
}

func (x *RuleField) GetFieldCnName() string {
	if x != nil {
		return x.FieldCnName
	}
	return ""
}

func (x *RuleField) GetFieldKey() string {
	if x != nil {
		return x.FieldKey
	}
	return ""
}

type CreateRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ID         uint64       `protobuf:"varint,1,opt,name=ID,proto3" json:"ID,omitempty"`
	Type       string       `protobuf:"bytes,2,opt,name=Type,json=type,proto3" json:"Type,omitempty"`
	Pid        uint64       `protobuf:"varint,3,opt,name=Pid,json=pid,proto3" json:"Pid,omitempty"`
	Title      string       `protobuf:"bytes,4,opt,name=Title,json=title,proto3" json:"Title,omitempty"`
	Icon       string       `protobuf:"bytes,5,opt,name=Icon,json=icon,proto3" json:"Icon,omitempty"`
	Url        string       `protobuf:"bytes,6,opt,name=Url,json=url,proto3" json:"Url,omitempty"`
	Method     string       `protobuf:"bytes,7,opt,name=Method,json=method,proto3" json:"Method,omitempty"`
	Weigh      uint64       `protobuf:"varint,8,opt,name=Weigh,json=weigh,proto3" json:"Weigh,omitempty"`
	Status     string       `protobuf:"bytes,9,opt,name=Status,json=status,proto3" json:"Status,omitempty"`
	Remark     string       `protobuf:"bytes,10,opt,name=Remark,json=remark,proto3" json:"Remark,omitempty"`
	Extend     string       `protobuf:"bytes,11,opt,name=Extend,json=extend,proto3" json:"Extend,omitempty"`
	Domain     string       `protobuf:"bytes,13,opt,name=Domain,json=domain,proto3" json:"Domain,omitempty"`
	DataOpen   bool         `protobuf:"varint,14,opt,name=DataOpen,json=dataOpen,proto3" json:"DataOpen,omitempty"`
	RuleData   []*RuleData  `protobuf:"bytes,12,rep,name=RuleData,json=ruleData,proto3" json:"RuleData,omitempty"`
	CanJump    uint32       `protobuf:"varint,15,opt,name=CanJump,json=canJump,proto3" json:"CanJump,omitempty"`
	MenuType   string       `protobuf:"bytes,16,opt,name=MenuType,json=menuType,proto3" json:"MenuType,omitempty"`
	RuleFields []*RuleField `protobuf:"bytes,17,rep,name=ruleFields,proto3" json:"ruleFields,omitempty"`
	GrayIcon   string       `protobuf:"bytes,18,opt,name=grayIcon,proto3" json:"grayIcon,omitempty"`
}

func (x *CreateRequest) Reset() {
	*x = CreateRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_rule_rule_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateRequest) ProtoMessage() {}

func (x *CreateRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_rule_rule_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateRequest.ProtoReflect.Descriptor instead.
func (*CreateRequest) Descriptor() ([]byte, []int) {
	return file_api_rule_rule_proto_rawDescGZIP(), []int{6}
}

func (x *CreateRequest) GetID() uint64 {
	if x != nil {
		return x.ID
	}
	return 0
}

func (x *CreateRequest) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

func (x *CreateRequest) GetPid() uint64 {
	if x != nil {
		return x.Pid
	}
	return 0
}

func (x *CreateRequest) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

func (x *CreateRequest) GetIcon() string {
	if x != nil {
		return x.Icon
	}
	return ""
}

func (x *CreateRequest) GetUrl() string {
	if x != nil {
		return x.Url
	}
	return ""
}

func (x *CreateRequest) GetMethod() string {
	if x != nil {
		return x.Method
	}
	return ""
}

func (x *CreateRequest) GetWeigh() uint64 {
	if x != nil {
		return x.Weigh
	}
	return 0
}

func (x *CreateRequest) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

func (x *CreateRequest) GetRemark() string {
	if x != nil {
		return x.Remark
	}
	return ""
}

func (x *CreateRequest) GetExtend() string {
	if x != nil {
		return x.Extend
	}
	return ""
}

func (x *CreateRequest) GetDomain() string {
	if x != nil {
		return x.Domain
	}
	return ""
}

func (x *CreateRequest) GetDataOpen() bool {
	if x != nil {
		return x.DataOpen
	}
	return false
}

func (x *CreateRequest) GetRuleData() []*RuleData {
	if x != nil {
		return x.RuleData
	}
	return nil
}

func (x *CreateRequest) GetCanJump() uint32 {
	if x != nil {
		return x.CanJump
	}
	return 0
}

func (x *CreateRequest) GetMenuType() string {
	if x != nil {
		return x.MenuType
	}
	return ""
}

func (x *CreateRequest) GetRuleFields() []*RuleField {
	if x != nil {
		return x.RuleFields
	}
	return nil
}

func (x *CreateRequest) GetGrayIcon() string {
	if x != nil {
		return x.GrayIcon
	}
	return ""
}

type FindByUrlRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Url           string   `protobuf:"bytes,1,opt,name=Url,proto3" json:"Url,omitempty"`
	Domain        string   `protobuf:"bytes,2,opt,name=Domain,json=domain,proto3" json:"Domain,omitempty"`
	Method        string   `protobuf:"bytes,3,opt,name=Method,proto3" json:"Method,omitempty"`
	DepartmentIds []uint64 `protobuf:"varint,4,rep,packed,name=DepartmentIds,proto3" json:"DepartmentIds,omitempty"`
}

func (x *FindByUrlRequest) Reset() {
	*x = FindByUrlRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_rule_rule_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FindByUrlRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FindByUrlRequest) ProtoMessage() {}

func (x *FindByUrlRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_rule_rule_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FindByUrlRequest.ProtoReflect.Descriptor instead.
func (*FindByUrlRequest) Descriptor() ([]byte, []int) {
	return file_api_rule_rule_proto_rawDescGZIP(), []int{7}
}

func (x *FindByUrlRequest) GetUrl() string {
	if x != nil {
		return x.Url
	}
	return ""
}

func (x *FindByUrlRequest) GetDomain() string {
	if x != nil {
		return x.Domain
	}
	return ""
}

func (x *FindByUrlRequest) GetMethod() string {
	if x != nil {
		return x.Method
	}
	return ""
}

func (x *FindByUrlRequest) GetDepartmentIds() []uint64 {
	if x != nil {
		return x.DepartmentIds
	}
	return nil
}

type FindRuleByUrlRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AccountID uint64 `protobuf:"varint,1,opt,name=AccountID,proto3" json:"AccountID,omitempty"`
	Domain    string `protobuf:"bytes,2,opt,name=Domain,json=domain,proto3" json:"Domain,omitempty"`
	Method    string `protobuf:"bytes,3,opt,name=Method,proto3" json:"Method,omitempty"`
	Url       string `protobuf:"bytes,4,opt,name=Url,proto3" json:"Url,omitempty"`
	Type      string `protobuf:"bytes,5,opt,name=Type,json=type,proto3" json:"Type,omitempty"`
}

func (x *FindRuleByUrlRequest) Reset() {
	*x = FindRuleByUrlRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_rule_rule_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FindRuleByUrlRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FindRuleByUrlRequest) ProtoMessage() {}

func (x *FindRuleByUrlRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_rule_rule_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FindRuleByUrlRequest.ProtoReflect.Descriptor instead.
func (*FindRuleByUrlRequest) Descriptor() ([]byte, []int) {
	return file_api_rule_rule_proto_rawDescGZIP(), []int{8}
}

func (x *FindRuleByUrlRequest) GetAccountID() uint64 {
	if x != nil {
		return x.AccountID
	}
	return 0
}

func (x *FindRuleByUrlRequest) GetDomain() string {
	if x != nil {
		return x.Domain
	}
	return ""
}

func (x *FindRuleByUrlRequest) GetMethod() string {
	if x != nil {
		return x.Method
	}
	return ""
}

func (x *FindRuleByUrlRequest) GetUrl() string {
	if x != nil {
		return x.Url
	}
	return ""
}

func (x *FindRuleByUrlRequest) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

type RuleByUrlRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AccountID uint64 `protobuf:"varint,1,opt,name=AccountID,proto3" json:"AccountID,omitempty"`
	Domain    string `protobuf:"bytes,2,opt,name=Domain,json=domain,proto3" json:"Domain,omitempty"`
	Method    string `protobuf:"bytes,3,opt,name=Method,proto3" json:"Method,omitempty"`
	Url       string `protobuf:"bytes,4,opt,name=Url,proto3" json:"Url,omitempty"`
}

func (x *RuleByUrlRequest) Reset() {
	*x = RuleByUrlRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_rule_rule_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RuleByUrlRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RuleByUrlRequest) ProtoMessage() {}

func (x *RuleByUrlRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_rule_rule_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RuleByUrlRequest.ProtoReflect.Descriptor instead.
func (*RuleByUrlRequest) Descriptor() ([]byte, []int) {
	return file_api_rule_rule_proto_rawDescGZIP(), []int{9}
}

func (x *RuleByUrlRequest) GetAccountID() uint64 {
	if x != nil {
		return x.AccountID
	}
	return 0
}

func (x *RuleByUrlRequest) GetDomain() string {
	if x != nil {
		return x.Domain
	}
	return ""
}

func (x *RuleByUrlRequest) GetMethod() string {
	if x != nil {
		return x.Method
	}
	return ""
}

func (x *RuleByUrlRequest) GetUrl() string {
	if x != nil {
		return x.Url
	}
	return ""
}

type RuleByUrlResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	IsPass    bool     `protobuf:"varint,1,opt,name=IsPass,json=isPass,proto3" json:"IsPass,omitempty"`
	IsAdmin   bool     `protobuf:"varint,2,opt,name=IsAdmin,json=isAdmin,proto3" json:"IsAdmin,omitempty"`
	ID        uint64   `protobuf:"varint,3,opt,name=ID,proto3" json:"ID,omitempty"`
	DataOpen  bool     `protobuf:"varint,4,opt,name=DataOpen,json=dataOpen,proto3" json:"DataOpen,omitempty"`
	DataField []string `protobuf:"bytes,5,rep,name=DataField,json=dataField,proto3" json:"DataField,omitempty"`
	Extend    string   `protobuf:"bytes,6,opt,name=Extend,json=extend,proto3" json:"Extend,omitempty"`
	Exist     bool     `protobuf:"varint,7,opt,name=Exist,json=exist,proto3" json:"Exist,omitempty"`
}

func (x *RuleByUrlResponse) Reset() {
	*x = RuleByUrlResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_rule_rule_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RuleByUrlResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RuleByUrlResponse) ProtoMessage() {}

func (x *RuleByUrlResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_rule_rule_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RuleByUrlResponse.ProtoReflect.Descriptor instead.
func (*RuleByUrlResponse) Descriptor() ([]byte, []int) {
	return file_api_rule_rule_proto_rawDescGZIP(), []int{10}
}

func (x *RuleByUrlResponse) GetIsPass() bool {
	if x != nil {
		return x.IsPass
	}
	return false
}

func (x *RuleByUrlResponse) GetIsAdmin() bool {
	if x != nil {
		return x.IsAdmin
	}
	return false
}

func (x *RuleByUrlResponse) GetID() uint64 {
	if x != nil {
		return x.ID
	}
	return 0
}

func (x *RuleByUrlResponse) GetDataOpen() bool {
	if x != nil {
		return x.DataOpen
	}
	return false
}

func (x *RuleByUrlResponse) GetDataField() []string {
	if x != nil {
		return x.DataField
	}
	return nil
}

func (x *RuleByUrlResponse) GetExtend() string {
	if x != nil {
		return x.Extend
	}
	return ""
}

func (x *RuleByUrlResponse) GetExist() bool {
	if x != nil {
		return x.Exist
	}
	return false
}

type CreateResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ID uint64 `protobuf:"varint,1,opt,name=ID,proto3" json:"ID,omitempty"`
}

func (x *CreateResponse) Reset() {
	*x = CreateResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_rule_rule_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateResponse) ProtoMessage() {}

func (x *CreateResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_rule_rule_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateResponse.ProtoReflect.Descriptor instead.
func (*CreateResponse) Descriptor() ([]byte, []int) {
	return file_api_rule_rule_proto_rawDescGZIP(), []int{11}
}

func (x *CreateResponse) GetID() uint64 {
	if x != nil {
		return x.ID
	}
	return 0
}

type RemoveRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ID     uint64 `protobuf:"varint,1,opt,name=ID,proto3" json:"ID,omitempty"`
	Domain string `protobuf:"bytes,2,opt,name=Domain,json=domain,proto3" json:"Domain,omitempty"`
}

func (x *RemoveRequest) Reset() {
	*x = RemoveRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_rule_rule_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RemoveRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RemoveRequest) ProtoMessage() {}

func (x *RemoveRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_rule_rule_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RemoveRequest.ProtoReflect.Descriptor instead.
func (*RemoveRequest) Descriptor() ([]byte, []int) {
	return file_api_rule_rule_proto_rawDescGZIP(), []int{12}
}

func (x *RemoveRequest) GetID() uint64 {
	if x != nil {
		return x.ID
	}
	return 0
}

func (x *RemoveRequest) GetDomain() string {
	if x != nil {
		return x.Domain
	}
	return ""
}

type RemoveResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *RemoveResponse) Reset() {
	*x = RemoveResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_rule_rule_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RemoveResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RemoveResponse) ProtoMessage() {}

func (x *RemoveResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_rule_rule_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RemoveResponse.ProtoReflect.Descriptor instead.
func (*RemoveResponse) Descriptor() ([]byte, []int) {
	return file_api_rule_rule_proto_rawDescGZIP(), []int{13}
}

type DetailRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ID     int64  `protobuf:"varint,1,opt,name=ID,proto3" json:"ID,omitempty"`
	Domain string `protobuf:"bytes,2,opt,name=Domain,json=domain,proto3" json:"Domain,omitempty"`
}

func (x *DetailRequest) Reset() {
	*x = DetailRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_rule_rule_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DetailRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DetailRequest) ProtoMessage() {}

func (x *DetailRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_rule_rule_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DetailRequest.ProtoReflect.Descriptor instead.
func (*DetailRequest) Descriptor() ([]byte, []int) {
	return file_api_rule_rule_proto_rawDescGZIP(), []int{14}
}

func (x *DetailRequest) GetID() int64 {
	if x != nil {
		return x.ID
	}
	return 0
}

func (x *DetailRequest) GetDomain() string {
	if x != nil {
		return x.Domain
	}
	return ""
}

type DetailResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ID                     uint64            `protobuf:"varint,1,opt,name=ID,proto3" json:"ID,omitempty"`
	Type                   string            `protobuf:"bytes,2,opt,name=Type,json=type,proto3" json:"Type,omitempty"`
	Pid                    uint64            `protobuf:"varint,3,opt,name=Pid,json=pid,proto3" json:"Pid,omitempty"`
	Title                  string            `protobuf:"bytes,4,opt,name=Title,json=title,proto3" json:"Title,omitempty"`
	Icon                   string            `protobuf:"bytes,5,opt,name=Icon,json=icon,proto3" json:"Icon,omitempty"`
	Url                    string            `protobuf:"bytes,6,opt,name=Url,json=url,proto3" json:"Url,omitempty"`
	Method                 string            `protobuf:"bytes,7,opt,name=Method,json=method,proto3" json:"Method,omitempty"`
	Weigh                  uint64            `protobuf:"varint,8,opt,name=Weigh,json=weigh,proto3" json:"Weigh,omitempty"`
	Status                 string            `protobuf:"bytes,9,opt,name=Status,json=status,proto3" json:"Status,omitempty"`
	Remark                 string            `protobuf:"bytes,10,opt,name=Remark,json=remark,proto3" json:"Remark,omitempty"`
	Extend                 string            `protobuf:"bytes,11,opt,name=Extend,json=extend,proto3" json:"Extend,omitempty"`
	RuleData               []*RuleData       `protobuf:"bytes,12,rep,name=RuleData,json=ruleData,proto3" json:"RuleData,omitempty"`
	Son                    []*DetailResponse `protobuf:"bytes,13,rep,name=Son,json=rules,proto3" json:"Son,omitempty"`
	CanJump                uint32            `protobuf:"varint,15,opt,name=CanJump,json=canJump,proto3" json:"CanJump,omitempty"`
	MenuType               string            `protobuf:"bytes,16,opt,name=MenuType,json=menuType,proto3" json:"MenuType,omitempty"`
	NowInterfaceDataFields []string          `protobuf:"bytes,17,rep,name=NowInterfaceDataFields,json=nowInterfaceDataFields,proto3" json:"NowInterfaceDataFields,omitempty"` //当前接口的字段权限
	RuleFields             []*RuleField      `protobuf:"bytes,18,rep,name=RuleFields,proto3" json:"RuleFields,omitempty"`
	GrayIcon               string            `protobuf:"bytes,19,opt,name=grayIcon,proto3" json:"grayIcon,omitempty"`
}

func (x *DetailResponse) Reset() {
	*x = DetailResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_rule_rule_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DetailResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DetailResponse) ProtoMessage() {}

func (x *DetailResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_rule_rule_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DetailResponse.ProtoReflect.Descriptor instead.
func (*DetailResponse) Descriptor() ([]byte, []int) {
	return file_api_rule_rule_proto_rawDescGZIP(), []int{15}
}

func (x *DetailResponse) GetID() uint64 {
	if x != nil {
		return x.ID
	}
	return 0
}

func (x *DetailResponse) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

func (x *DetailResponse) GetPid() uint64 {
	if x != nil {
		return x.Pid
	}
	return 0
}

func (x *DetailResponse) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

func (x *DetailResponse) GetIcon() string {
	if x != nil {
		return x.Icon
	}
	return ""
}

func (x *DetailResponse) GetUrl() string {
	if x != nil {
		return x.Url
	}
	return ""
}

func (x *DetailResponse) GetMethod() string {
	if x != nil {
		return x.Method
	}
	return ""
}

func (x *DetailResponse) GetWeigh() uint64 {
	if x != nil {
		return x.Weigh
	}
	return 0
}

func (x *DetailResponse) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

func (x *DetailResponse) GetRemark() string {
	if x != nil {
		return x.Remark
	}
	return ""
}

func (x *DetailResponse) GetExtend() string {
	if x != nil {
		return x.Extend
	}
	return ""
}

func (x *DetailResponse) GetRuleData() []*RuleData {
	if x != nil {
		return x.RuleData
	}
	return nil
}

func (x *DetailResponse) GetSon() []*DetailResponse {
	if x != nil {
		return x.Son
	}
	return nil
}

func (x *DetailResponse) GetCanJump() uint32 {
	if x != nil {
		return x.CanJump
	}
	return 0
}

func (x *DetailResponse) GetMenuType() string {
	if x != nil {
		return x.MenuType
	}
	return ""
}

func (x *DetailResponse) GetNowInterfaceDataFields() []string {
	if x != nil {
		return x.NowInterfaceDataFields
	}
	return nil
}

func (x *DetailResponse) GetRuleFields() []*RuleField {
	if x != nil {
		return x.RuleFields
	}
	return nil
}

func (x *DetailResponse) GetGrayIcon() string {
	if x != nil {
		return x.GrayIcon
	}
	return ""
}

//权限信息
type RuleInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ID                 uint64               `protobuf:"varint,1,opt,name=ID,proto3" json:"ID,omitempty"`
	Name               string               `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	Url                string               `protobuf:"bytes,3,opt,name=url,proto3" json:"url,omitempty"`
	Method             string               `protobuf:"bytes,4,opt,name=method,proto3" json:"method,omitempty"`
	RuleDataID         uint64               `protobuf:"varint,5,opt,name=ruleDataID,proto3" json:"ruleDataID,omitempty"`
	RuleDataName       string               `protobuf:"bytes,6,opt,name=ruleDataName,proto3" json:"ruleDataName,omitempty"`
	RuleDataField      []string             `protobuf:"bytes,7,rep,name=ruleDataField,proto3" json:"ruleDataField,omitempty"`
	Son                []*RuleInfo          `protobuf:"bytes,8,rep,name=son,proto3" json:"son,omitempty"`
	Pid                uint64               `protobuf:"varint,9,opt,name=pid,proto3" json:"pid,omitempty"`
	RuleData           []*RuleData          `protobuf:"bytes,10,rep,name=ruleData,proto3" json:"ruleData,omitempty"`
	MenuType           string               `protobuf:"bytes,11,opt,name=menuType,proto3" json:"menuType,omitempty"`
	Type               string               `protobuf:"bytes,12,opt,name=type,proto3" json:"type,omitempty"`
	PositionRuleFields []*PositionRuleField `protobuf:"bytes,13,rep,name=positionRuleFields,proto3" json:"positionRuleFields,omitempty"`
	DataRange          uint32               `protobuf:"varint,14,opt,name=dataRange,proto3" json:"dataRange,omitempty"` //数据范围 0-默认所有 1-仅自己 2- 自己以及下属 3-本部门
	InterfaceList      []*RuleInfo          `protobuf:"bytes,15,rep,name=interfaceList,proto3" json:"interfaceList,omitempty"`
	ButtonList         []*RuleInfo          `protobuf:"bytes,16,rep,name=buttonList,proto3" json:"buttonList,omitempty"`
	Weigh              uint32               `protobuf:"varint,17,opt,name=weigh,proto3" json:"weigh,omitempty"`
	DataDepartmentIds  []uint32             `protobuf:"varint,18,rep,packed,name=dataDepartmentIds,proto3" json:"dataDepartmentIds,omitempty"`
}

func (x *RuleInfo) Reset() {
	*x = RuleInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_rule_rule_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RuleInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RuleInfo) ProtoMessage() {}

func (x *RuleInfo) ProtoReflect() protoreflect.Message {
	mi := &file_api_rule_rule_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RuleInfo.ProtoReflect.Descriptor instead.
func (*RuleInfo) Descriptor() ([]byte, []int) {
	return file_api_rule_rule_proto_rawDescGZIP(), []int{16}
}

func (x *RuleInfo) GetID() uint64 {
	if x != nil {
		return x.ID
	}
	return 0
}

func (x *RuleInfo) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *RuleInfo) GetUrl() string {
	if x != nil {
		return x.Url
	}
	return ""
}

func (x *RuleInfo) GetMethod() string {
	if x != nil {
		return x.Method
	}
	return ""
}

func (x *RuleInfo) GetRuleDataID() uint64 {
	if x != nil {
		return x.RuleDataID
	}
	return 0
}

func (x *RuleInfo) GetRuleDataName() string {
	if x != nil {
		return x.RuleDataName
	}
	return ""
}

func (x *RuleInfo) GetRuleDataField() []string {
	if x != nil {
		return x.RuleDataField
	}
	return nil
}

func (x *RuleInfo) GetSon() []*RuleInfo {
	if x != nil {
		return x.Son
	}
	return nil
}

func (x *RuleInfo) GetPid() uint64 {
	if x != nil {
		return x.Pid
	}
	return 0
}

func (x *RuleInfo) GetRuleData() []*RuleData {
	if x != nil {
		return x.RuleData
	}
	return nil
}

func (x *RuleInfo) GetMenuType() string {
	if x != nil {
		return x.MenuType
	}
	return ""
}

func (x *RuleInfo) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

func (x *RuleInfo) GetPositionRuleFields() []*PositionRuleField {
	if x != nil {
		return x.PositionRuleFields
	}
	return nil
}

func (x *RuleInfo) GetDataRange() uint32 {
	if x != nil {
		return x.DataRange
	}
	return 0
}

func (x *RuleInfo) GetInterfaceList() []*RuleInfo {
	if x != nil {
		return x.InterfaceList
	}
	return nil
}

func (x *RuleInfo) GetButtonList() []*RuleInfo {
	if x != nil {
		return x.ButtonList
	}
	return nil
}

func (x *RuleInfo) GetWeigh() uint32 {
	if x != nil {
		return x.Weigh
	}
	return 0
}

func (x *RuleInfo) GetDataDepartmentIds() []uint32 {
	if x != nil {
		return x.DataDepartmentIds
	}
	return nil
}

//数据权限
type PositionRuleField struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ID          uint64 `protobuf:"varint,1,opt,name=ID,proto3" json:"ID,omitempty"`
	RuleFieldId uint64 `protobuf:"varint,2,opt,name=ruleFieldId,proto3" json:"ruleFieldId,omitempty"`
	RuleId      uint64 `protobuf:"varint,3,opt,name=ruleId,proto3" json:"ruleId,omitempty"`
	FieldCnName string `protobuf:"bytes,4,opt,name=fieldCnName,proto3" json:"fieldCnName,omitempty"`
	FieldKey    string `protobuf:"bytes,5,opt,name=fieldKey,proto3" json:"fieldKey,omitempty"`
}

func (x *PositionRuleField) Reset() {
	*x = PositionRuleField{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_rule_rule_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PositionRuleField) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PositionRuleField) ProtoMessage() {}

func (x *PositionRuleField) ProtoReflect() protoreflect.Message {
	mi := &file_api_rule_rule_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PositionRuleField.ProtoReflect.Descriptor instead.
func (*PositionRuleField) Descriptor() ([]byte, []int) {
	return file_api_rule_rule_proto_rawDescGZIP(), []int{17}
}

func (x *PositionRuleField) GetID() uint64 {
	if x != nil {
		return x.ID
	}
	return 0
}

func (x *PositionRuleField) GetRuleFieldId() uint64 {
	if x != nil {
		return x.RuleFieldId
	}
	return 0
}

func (x *PositionRuleField) GetRuleId() uint64 {
	if x != nil {
		return x.RuleId
	}
	return 0
}

func (x *PositionRuleField) GetFieldCnName() string {
	if x != nil {
		return x.FieldCnName
	}
	return ""
}

func (x *PositionRuleField) GetFieldKey() string {
	if x != nil {
		return x.FieldKey
	}
	return ""
}

type Leader struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ID   int64  `protobuf:"varint,1,opt,name=ID,json=id,proto3" json:"ID,omitempty"`
	Name string `protobuf:"bytes,2,opt,name=Name,json=name,proto3" json:"Name,omitempty"`
}

func (x *Leader) Reset() {
	*x = Leader{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_rule_rule_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Leader) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Leader) ProtoMessage() {}

func (x *Leader) ProtoReflect() protoreflect.Message {
	mi := &file_api_rule_rule_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Leader.ProtoReflect.Descriptor instead.
func (*Leader) Descriptor() ([]byte, []int) {
	return file_api_rule_rule_proto_rawDescGZIP(), []int{18}
}

func (x *Leader) GetID() int64 {
	if x != nil {
		return x.ID
	}
	return 0
}

func (x *Leader) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

//更新
type UpdateRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ID         uint64       `protobuf:"varint,1,opt,name=ID,json=id,proto3" json:"ID,omitempty"`
	Type       string       `protobuf:"bytes,2,opt,name=Type,json=type,proto3" json:"Type,omitempty"`
	Pid        uint64       `protobuf:"varint,3,opt,name=Pid,json=pid,proto3" json:"Pid,omitempty"`
	Title      string       `protobuf:"bytes,4,opt,name=Title,json=title,proto3" json:"Title,omitempty"`
	Icon       string       `protobuf:"bytes,5,opt,name=Icon,json=icon,proto3" json:"Icon,omitempty"`
	Url        string       `protobuf:"bytes,6,opt,name=Url,json=url,proto3" json:"Url,omitempty"`
	Method     string       `protobuf:"bytes,7,opt,name=Method,json=method,proto3" json:"Method,omitempty"`
	Weigh      uint64       `protobuf:"varint,8,opt,name=Weigh,json=weigh,proto3" json:"Weigh,omitempty"`
	Status     string       `protobuf:"bytes,9,opt,name=Status,json=status,proto3" json:"Status,omitempty"`
	Remark     string       `protobuf:"bytes,10,opt,name=Remark,json=remark,proto3" json:"Remark,omitempty"`
	Extend     string       `protobuf:"bytes,11,opt,name=Extend,json=extend,proto3" json:"Extend,omitempty"`
	RuleData   []*RuleData  `protobuf:"bytes,12,rep,name=RuleData,json=ruleData,proto3" json:"RuleData,omitempty"`
	Domain     string       `protobuf:"bytes,13,opt,name=Domain,json=domain,proto3" json:"Domain,omitempty"`
	CanJump    uint32       `protobuf:"varint,15,opt,name=CanJump,json=canJump,proto3" json:"CanJump,omitempty"`
	MenuType   string       `protobuf:"bytes,16,opt,name=MenuType,json=menuType,proto3" json:"MenuType,omitempty"`
	RuleFields []*RuleField `protobuf:"bytes,17,rep,name=RuleFields,proto3" json:"RuleFields,omitempty"`
	GrayIcon   string       `protobuf:"bytes,18,opt,name=grayIcon,proto3" json:"grayIcon,omitempty"`
}

func (x *UpdateRequest) Reset() {
	*x = UpdateRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_rule_rule_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateRequest) ProtoMessage() {}

func (x *UpdateRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_rule_rule_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateRequest.ProtoReflect.Descriptor instead.
func (*UpdateRequest) Descriptor() ([]byte, []int) {
	return file_api_rule_rule_proto_rawDescGZIP(), []int{19}
}

func (x *UpdateRequest) GetID() uint64 {
	if x != nil {
		return x.ID
	}
	return 0
}

func (x *UpdateRequest) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

func (x *UpdateRequest) GetPid() uint64 {
	if x != nil {
		return x.Pid
	}
	return 0
}

func (x *UpdateRequest) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

func (x *UpdateRequest) GetIcon() string {
	if x != nil {
		return x.Icon
	}
	return ""
}

func (x *UpdateRequest) GetUrl() string {
	if x != nil {
		return x.Url
	}
	return ""
}

func (x *UpdateRequest) GetMethod() string {
	if x != nil {
		return x.Method
	}
	return ""
}

func (x *UpdateRequest) GetWeigh() uint64 {
	if x != nil {
		return x.Weigh
	}
	return 0
}

func (x *UpdateRequest) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

func (x *UpdateRequest) GetRemark() string {
	if x != nil {
		return x.Remark
	}
	return ""
}

func (x *UpdateRequest) GetExtend() string {
	if x != nil {
		return x.Extend
	}
	return ""
}

func (x *UpdateRequest) GetRuleData() []*RuleData {
	if x != nil {
		return x.RuleData
	}
	return nil
}

func (x *UpdateRequest) GetDomain() string {
	if x != nil {
		return x.Domain
	}
	return ""
}

func (x *UpdateRequest) GetCanJump() uint32 {
	if x != nil {
		return x.CanJump
	}
	return 0
}

func (x *UpdateRequest) GetMenuType() string {
	if x != nil {
		return x.MenuType
	}
	return ""
}

func (x *UpdateRequest) GetRuleFields() []*RuleField {
	if x != nil {
		return x.RuleFields
	}
	return nil
}

func (x *UpdateRequest) GetGrayIcon() string {
	if x != nil {
		return x.GrayIcon
	}
	return ""
}

type UpdateResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *UpdateResponse) Reset() {
	*x = UpdateResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_rule_rule_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateResponse) ProtoMessage() {}

func (x *UpdateResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_rule_rule_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateResponse.ProtoReflect.Descriptor instead.
func (*UpdateResponse) Descriptor() ([]byte, []int) {
	return file_api_rule_rule_proto_rawDescGZIP(), []int{20}
}

type ListRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Domain       string   `protobuf:"bytes,1,opt,name=Domain,json=domain,proto3" json:"Domain,omitempty"`
	PageSize     uint64   `protobuf:"varint,2,opt,name=PageSize,json=pageSize,proto3" json:"PageSize,omitempty"`
	Page         uint64   `protobuf:"varint,3,opt,name=Page,json=page,proto3" json:"Page,omitempty"`
	Pid          uint64   `protobuf:"varint,4,opt,name=Pid,json=pid,proto3" json:"Pid,omitempty"`
	Type         string   `protobuf:"bytes,5,opt,name=Type,json=type,proto3" json:"Type,omitempty"`
	Title        string   `protobuf:"bytes,6,opt,name=Title,json=title,proto3" json:"Title,omitempty"`
	IsNotJumpTop bool     `protobuf:"varint,8,opt,name=IsNotJumpTop,json=isNotJumpTop,proto3" json:"IsNotJumpTop,omitempty"`
	Urls         []string `protobuf:"bytes,9,rep,name=Urls,json=urls,proto3" json:"Urls,omitempty"`
}

func (x *ListRequest) Reset() {
	*x = ListRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_rule_rule_proto_msgTypes[21]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListRequest) ProtoMessage() {}

func (x *ListRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_rule_rule_proto_msgTypes[21]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListRequest.ProtoReflect.Descriptor instead.
func (*ListRequest) Descriptor() ([]byte, []int) {
	return file_api_rule_rule_proto_rawDescGZIP(), []int{21}
}

func (x *ListRequest) GetDomain() string {
	if x != nil {
		return x.Domain
	}
	return ""
}

func (x *ListRequest) GetPageSize() uint64 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *ListRequest) GetPage() uint64 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *ListRequest) GetPid() uint64 {
	if x != nil {
		return x.Pid
	}
	return 0
}

func (x *ListRequest) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

func (x *ListRequest) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

func (x *ListRequest) GetIsNotJumpTop() bool {
	if x != nil {
		return x.IsNotJumpTop
	}
	return false
}

func (x *ListRequest) GetUrls() []string {
	if x != nil {
		return x.Urls
	}
	return nil
}

type ListResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Count uint64            `protobuf:"varint,1,opt,name=Count,proto3" json:"Count,omitempty"`
	Data  []*DetailResponse `protobuf:"bytes,3,rep,name=data,proto3" json:"data,omitempty"`
}

func (x *ListResponse) Reset() {
	*x = ListResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_rule_rule_proto_msgTypes[22]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListResponse) ProtoMessage() {}

func (x *ListResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_rule_rule_proto_msgTypes[22]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListResponse.ProtoReflect.Descriptor instead.
func (*ListResponse) Descriptor() ([]byte, []int) {
	return file_api_rule_rule_proto_rawDescGZIP(), []int{22}
}

func (x *ListResponse) GetCount() uint64 {
	if x != nil {
		return x.Count
	}
	return 0
}

func (x *ListResponse) GetData() []*DetailResponse {
	if x != nil {
		return x.Data
	}
	return nil
}

type MenuListResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Count uint64      `protobuf:"varint,1,opt,name=count,proto3" json:"count,omitempty"`
	Data  []*RuleInfo `protobuf:"bytes,3,rep,name=data,proto3" json:"data,omitempty"`
}

func (x *MenuListResponse) Reset() {
	*x = MenuListResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_rule_rule_proto_msgTypes[23]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MenuListResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MenuListResponse) ProtoMessage() {}

func (x *MenuListResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_rule_rule_proto_msgTypes[23]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MenuListResponse.ProtoReflect.Descriptor instead.
func (*MenuListResponse) Descriptor() ([]byte, []int) {
	return file_api_rule_rule_proto_rawDescGZIP(), []int{23}
}

func (x *MenuListResponse) GetCount() uint64 {
	if x != nil {
		return x.Count
	}
	return 0
}

func (x *MenuListResponse) GetData() []*RuleInfo {
	if x != nil {
		return x.Data
	}
	return nil
}

type RulesRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Domain    string `protobuf:"bytes,1,opt,name=Domain,proto3" json:"Domain,omitempty"`
	AccountID uint64 `protobuf:"varint,2,opt,name=AccountID,proto3" json:"AccountID,omitempty"`
}

func (x *RulesRequest) Reset() {
	*x = RulesRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_rule_rule_proto_msgTypes[24]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RulesRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RulesRequest) ProtoMessage() {}

func (x *RulesRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_rule_rule_proto_msgTypes[24]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RulesRequest.ProtoReflect.Descriptor instead.
func (*RulesRequest) Descriptor() ([]byte, []int) {
	return file_api_rule_rule_proto_rawDescGZIP(), []int{24}
}

func (x *RulesRequest) GetDomain() string {
	if x != nil {
		return x.Domain
	}
	return ""
}

func (x *RulesRequest) GetAccountID() uint64 {
	if x != nil {
		return x.AccountID
	}
	return 0
}

type RuleResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ID        uint64    `protobuf:"varint,1,opt,name=ID,proto3" json:"ID,omitempty"`
	Type      string    `protobuf:"bytes,2,opt,name=Type,json=type,proto3" json:"Type,omitempty"`
	Pid       uint64    `protobuf:"varint,3,opt,name=Pid,json=pid,proto3" json:"Pid,omitempty"`
	Title     string    `protobuf:"bytes,4,opt,name=Title,json=title,proto3" json:"Title,omitempty"`
	Icon      string    `protobuf:"bytes,5,opt,name=Icon,json=icon,proto3" json:"Icon,omitempty"`
	Url       string    `protobuf:"bytes,6,opt,name=Url,json=url,proto3" json:"Url,omitempty"`
	Method    string    `protobuf:"bytes,7,opt,name=Method,json=method,proto3" json:"Method,omitempty"`
	Weigh     uint64    `protobuf:"varint,8,opt,name=Weigh,json=weigh,proto3" json:"Weigh,omitempty"`
	Status    string    `protobuf:"bytes,9,opt,name=Status,json=status,proto3" json:"Status,omitempty"`
	Remark    string    `protobuf:"bytes,10,opt,name=Remark,json=remark,proto3" json:"Remark,omitempty"`
	Extend    string    `protobuf:"bytes,11,opt,name=Extend,json=extend,proto3" json:"Extend,omitempty"`
	RuleDatum *RuleData `protobuf:"bytes,12,opt,name=RuleDatum,json=ruleDatum,proto3" json:"RuleDatum,omitempty"`
}

func (x *RuleResponse) Reset() {
	*x = RuleResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_rule_rule_proto_msgTypes[25]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RuleResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RuleResponse) ProtoMessage() {}

func (x *RuleResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_rule_rule_proto_msgTypes[25]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RuleResponse.ProtoReflect.Descriptor instead.
func (*RuleResponse) Descriptor() ([]byte, []int) {
	return file_api_rule_rule_proto_rawDescGZIP(), []int{25}
}

func (x *RuleResponse) GetID() uint64 {
	if x != nil {
		return x.ID
	}
	return 0
}

func (x *RuleResponse) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

func (x *RuleResponse) GetPid() uint64 {
	if x != nil {
		return x.Pid
	}
	return 0
}

func (x *RuleResponse) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

func (x *RuleResponse) GetIcon() string {
	if x != nil {
		return x.Icon
	}
	return ""
}

func (x *RuleResponse) GetUrl() string {
	if x != nil {
		return x.Url
	}
	return ""
}

func (x *RuleResponse) GetMethod() string {
	if x != nil {
		return x.Method
	}
	return ""
}

func (x *RuleResponse) GetWeigh() uint64 {
	if x != nil {
		return x.Weigh
	}
	return 0
}

func (x *RuleResponse) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

func (x *RuleResponse) GetRemark() string {
	if x != nil {
		return x.Remark
	}
	return ""
}

func (x *RuleResponse) GetExtend() string {
	if x != nil {
		return x.Extend
	}
	return ""
}

func (x *RuleResponse) GetRuleDatum() *RuleData {
	if x != nil {
		return x.RuleDatum
	}
	return nil
}

type PositionUser struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PositionID     uint64 `protobuf:"varint,1,opt,name=PositionID,json=positionID,proto3" json:"PositionID,omitempty"`
	PositionName   string `protobuf:"bytes,2,opt,name=PositionName,json=positionName,proto3" json:"PositionName,omitempty"`
	DepartmentId   uint64 `protobuf:"varint,3,opt,name=DepartmentId,json=departmentId,proto3" json:"DepartmentId,omitempty"`
	DepartmentCode string `protobuf:"bytes,4,opt,name=DepartmentCode,json=departmentCode,proto3" json:"DepartmentCode,omitempty"`
	DepartmentName string `protobuf:"bytes,5,opt,name=DepartmentName,json=departmentName,proto3" json:"DepartmentName,omitempty"`
	UserId         uint64 `protobuf:"varint,6,opt,name=UserId,json=userId,proto3" json:"UserId,omitempty"`
	UserName       string `protobuf:"bytes,7,opt,name=UserName,json=userName,proto3" json:"UserName,omitempty"`
	SyncId         string `protobuf:"bytes,8,opt,name=syncId,proto3" json:"syncId,omitempty"`
}

func (x *PositionUser) Reset() {
	*x = PositionUser{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_rule_rule_proto_msgTypes[26]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PositionUser) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PositionUser) ProtoMessage() {}

func (x *PositionUser) ProtoReflect() protoreflect.Message {
	mi := &file_api_rule_rule_proto_msgTypes[26]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PositionUser.ProtoReflect.Descriptor instead.
func (*PositionUser) Descriptor() ([]byte, []int) {
	return file_api_rule_rule_proto_rawDescGZIP(), []int{26}
}

func (x *PositionUser) GetPositionID() uint64 {
	if x != nil {
		return x.PositionID
	}
	return 0
}

func (x *PositionUser) GetPositionName() string {
	if x != nil {
		return x.PositionName
	}
	return ""
}

func (x *PositionUser) GetDepartmentId() uint64 {
	if x != nil {
		return x.DepartmentId
	}
	return 0
}

func (x *PositionUser) GetDepartmentCode() string {
	if x != nil {
		return x.DepartmentCode
	}
	return ""
}

func (x *PositionUser) GetDepartmentName() string {
	if x != nil {
		return x.DepartmentName
	}
	return ""
}

func (x *PositionUser) GetUserId() uint64 {
	if x != nil {
		return x.UserId
	}
	return 0
}

func (x *PositionUser) GetUserName() string {
	if x != nil {
		return x.UserName
	}
	return ""
}

func (x *PositionUser) GetSyncId() string {
	if x != nil {
		return x.SyncId
	}
	return ""
}

type DepartmentLeader struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	DepartmentId   uint64 `protobuf:"varint,1,opt,name=DepartmentId,json=departmentId,proto3" json:"DepartmentId,omitempty"`
	DepartmentCode string `protobuf:"bytes,2,opt,name=DepartmentCode,json=departmentCode,proto3" json:"DepartmentCode,omitempty"`
	DepartmentName string `protobuf:"bytes,3,opt,name=DepartmentName,json=departmentName,proto3" json:"DepartmentName,omitempty"`
}

func (x *DepartmentLeader) Reset() {
	*x = DepartmentLeader{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_rule_rule_proto_msgTypes[27]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DepartmentLeader) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DepartmentLeader) ProtoMessage() {}

func (x *DepartmentLeader) ProtoReflect() protoreflect.Message {
	mi := &file_api_rule_rule_proto_msgTypes[27]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DepartmentLeader.ProtoReflect.Descriptor instead.
func (*DepartmentLeader) Descriptor() ([]byte, []int) {
	return file_api_rule_rule_proto_rawDescGZIP(), []int{27}
}

func (x *DepartmentLeader) GetDepartmentId() uint64 {
	if x != nil {
		return x.DepartmentId
	}
	return 0
}

func (x *DepartmentLeader) GetDepartmentCode() string {
	if x != nil {
		return x.DepartmentCode
	}
	return ""
}

func (x *DepartmentLeader) GetDepartmentName() string {
	if x != nil {
		return x.DepartmentName
	}
	return ""
}

type RulesResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AllMenus          []*DetailResponse   `protobuf:"bytes,1,rep,name=AllMenus,proto3" json:"AllMenus,omitempty"`
	AllRules          []*DetailResponse   `protobuf:"bytes,10,rep,name=AllRules,proto3" json:"AllRules,omitempty"`
	AllInterfaceRules []*DetailResponse   `protobuf:"bytes,11,rep,name=AllInterfaceRules,proto3" json:"AllInterfaceRules,omitempty"`
	MyMenuAuths       []*DetailResponse   `protobuf:"bytes,2,rep,name=MyMenuAuths,proto3" json:"MyMenuAuths,omitempty"`
	MyRules           []*DetailResponse   `protobuf:"bytes,3,rep,name=MyRules,proto3" json:"MyRules,omitempty"`
	MyButtonAuths     []*DetailResponse   `protobuf:"bytes,4,rep,name=MyButtonAuths,proto3" json:"MyButtonAuths,omitempty"`
	MyInterFaceAuths  []*DetailResponse   `protobuf:"bytes,5,rep,name=MyInterFaceAuths,proto3" json:"MyInterFaceAuths,omitempty"`
	PositionUsers     []*PositionUser     `protobuf:"bytes,6,rep,name=PositionUsers,proto3" json:"PositionUsers,omitempty"`
	DepartmentLeaders []*DepartmentLeader `protobuf:"bytes,7,rep,name=DepartmentLeaders,proto3" json:"DepartmentLeaders,omitempty"`
	AllTreeRule       []*DetailResponse   `protobuf:"bytes,8,rep,name=AllTreeRule,proto3" json:"AllTreeRule,omitempty"`
	MyTreeRule        []*DetailResponse   `protobuf:"bytes,9,rep,name=MyTreeRule,proto3" json:"MyTreeRule,omitempty"`
	MySecondRule      []*DetailResponse   `protobuf:"bytes,12,rep,name=MySecondRule,proto3" json:"MySecondRule,omitempty"`
}

func (x *RulesResponse) Reset() {
	*x = RulesResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_rule_rule_proto_msgTypes[28]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RulesResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RulesResponse) ProtoMessage() {}

func (x *RulesResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_rule_rule_proto_msgTypes[28]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RulesResponse.ProtoReflect.Descriptor instead.
func (*RulesResponse) Descriptor() ([]byte, []int) {
	return file_api_rule_rule_proto_rawDescGZIP(), []int{28}
}

func (x *RulesResponse) GetAllMenus() []*DetailResponse {
	if x != nil {
		return x.AllMenus
	}
	return nil
}

func (x *RulesResponse) GetAllRules() []*DetailResponse {
	if x != nil {
		return x.AllRules
	}
	return nil
}

func (x *RulesResponse) GetAllInterfaceRules() []*DetailResponse {
	if x != nil {
		return x.AllInterfaceRules
	}
	return nil
}

func (x *RulesResponse) GetMyMenuAuths() []*DetailResponse {
	if x != nil {
		return x.MyMenuAuths
	}
	return nil
}

func (x *RulesResponse) GetMyRules() []*DetailResponse {
	if x != nil {
		return x.MyRules
	}
	return nil
}

func (x *RulesResponse) GetMyButtonAuths() []*DetailResponse {
	if x != nil {
		return x.MyButtonAuths
	}
	return nil
}

func (x *RulesResponse) GetMyInterFaceAuths() []*DetailResponse {
	if x != nil {
		return x.MyInterFaceAuths
	}
	return nil
}

func (x *RulesResponse) GetPositionUsers() []*PositionUser {
	if x != nil {
		return x.PositionUsers
	}
	return nil
}

func (x *RulesResponse) GetDepartmentLeaders() []*DepartmentLeader {
	if x != nil {
		return x.DepartmentLeaders
	}
	return nil
}

func (x *RulesResponse) GetAllTreeRule() []*DetailResponse {
	if x != nil {
		return x.AllTreeRule
	}
	return nil
}

func (x *RulesResponse) GetMyTreeRule() []*DetailResponse {
	if x != nil {
		return x.MyTreeRule
	}
	return nil
}

func (x *RulesResponse) GetMySecondRule() []*DetailResponse {
	if x != nil {
		return x.MySecondRule
	}
	return nil
}

type UserInfoResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PositionUsers     []*PositionUser     `protobuf:"bytes,1,rep,name=PositionUsers,proto3" json:"PositionUsers,omitempty"`
	DepartmentLeaders []*DepartmentLeader `protobuf:"bytes,2,rep,name=DepartmentLeaders,proto3" json:"DepartmentLeaders,omitempty"`
	IsAdmin           bool                `protobuf:"varint,3,opt,name=IsAdmin,proto3" json:"IsAdmin,omitempty"`
}

func (x *UserInfoResponse) Reset() {
	*x = UserInfoResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_rule_rule_proto_msgTypes[29]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UserInfoResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserInfoResponse) ProtoMessage() {}

func (x *UserInfoResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_rule_rule_proto_msgTypes[29]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserInfoResponse.ProtoReflect.Descriptor instead.
func (*UserInfoResponse) Descriptor() ([]byte, []int) {
	return file_api_rule_rule_proto_rawDescGZIP(), []int{29}
}

func (x *UserInfoResponse) GetPositionUsers() []*PositionUser {
	if x != nil {
		return x.PositionUsers
	}
	return nil
}

func (x *UserInfoResponse) GetDepartmentLeaders() []*DepartmentLeader {
	if x != nil {
		return x.DepartmentLeaders
	}
	return nil
}

func (x *UserInfoResponse) GetIsAdmin() bool {
	if x != nil {
		return x.IsAdmin
	}
	return false
}

var File_api_rule_rule_proto protoreflect.FileDescriptor

var file_api_rule_rule_proto_rawDesc = []byte{
	0x0a, 0x13, 0x61, 0x70, 0x69, 0x2f, 0x72, 0x75, 0x6c, 0x65, 0x2f, 0x72, 0x75, 0x6c, 0x65, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x04, 0x72, 0x75, 0x6c, 0x65, 0x1a, 0x3d, 0x67, 0x69, 0x74,
	0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x6d, 0x77, 0x69, 0x74, 0x6b, 0x6f, 0x77, 0x2f,
	0x67, 0x6f, 0x2d, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2d, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74,
	0x6f, 0x72, 0x73, 0x40, 0x76, 0x30, 0x2e, 0x33, 0x2e, 0x32, 0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64,
	0x61, 0x74, 0x6f, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x3e, 0x0a, 0x08, 0x55, 0x73,
	0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x16, 0x0a, 0x06, 0x55, 0x73, 0x65, 0x72, 0x49, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x12, 0x1a,
	0x0a, 0x08, 0x55, 0x73, 0x65, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x08, 0x75, 0x73, 0x65, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x22, 0x2f, 0x0a, 0x09, 0x55, 0x73,
	0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x73, 0x12, 0x22, 0x0a, 0x04, 0x4c, 0x69, 0x73, 0x74, 0x18,
	0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x0e, 0x2e, 0x72, 0x75, 0x6c, 0x65, 0x2e, 0x55, 0x73, 0x65,
	0x72, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x22, 0x58, 0x0a, 0x18, 0x50,
	0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x55, 0x73, 0x65, 0x72, 0x4c, 0x69, 0x73, 0x74, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x43, 0x6f, 0x75, 0x6e, 0x74,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x05, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x26, 0x0a,
	0x04, 0x44, 0x61, 0x74, 0x61, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x72, 0x75,
	0x6c, 0x65, 0x2e, 0x50, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x55, 0x73, 0x65, 0x72, 0x52,
	0x04, 0x64, 0x61, 0x74, 0x61, 0x22, 0x91, 0x01, 0x0a, 0x17, 0x50, 0x6f, 0x73, 0x69, 0x74, 0x69,
	0x6f, 0x6e, 0x55, 0x73, 0x65, 0x72, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x12, 0x16, 0x0a, 0x06, 0x44, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x06, 0x64, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x12, 0x22, 0x0a, 0x0c, 0x44, 0x65, 0x70,
	0x61, 0x72, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x04, 0x52,
	0x0c, 0x64, 0x65, 0x70, 0x61, 0x72, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x1e, 0x0a,
	0x0a, 0x50, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x04, 0x52, 0x0a, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x12, 0x1a, 0x0a,
	0x08, 0x73, 0x69, 0x74, 0x65, 0x55, 0x69, 0x64, 0x53, 0x18, 0x04, 0x20, 0x03, 0x28, 0x09, 0x52,
	0x08, 0x73, 0x69, 0x74, 0x65, 0x55, 0x69, 0x64, 0x53, 0x22, 0x4c, 0x0a, 0x08, 0x52, 0x75, 0x6c,
	0x65, 0x44, 0x61, 0x74, 0x61, 0x12, 0x1c, 0x0a, 0x09, 0x44, 0x61, 0x74, 0x61, 0x46, 0x69, 0x65,
	0x6c, 0x64, 0x18, 0x01, 0x20, 0x03, 0x28, 0x09, 0x52, 0x09, 0x64, 0x61, 0x74, 0x61, 0x46, 0x69,
	0x65, 0x6c, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x0e, 0x0a, 0x02, 0x49, 0x44, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x04, 0x52, 0x02, 0x49, 0x44, 0x22, 0x59, 0x0a, 0x09, 0x52, 0x75, 0x6c, 0x65, 0x46,
	0x69, 0x65, 0x6c, 0x64, 0x12, 0x0e, 0x0a, 0x02, 0x49, 0x44, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04,
	0x52, 0x02, 0x49, 0x44, 0x12, 0x20, 0x0a, 0x0b, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x43, 0x6e, 0x4e,
	0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x66, 0x69, 0x65, 0x6c, 0x64,
	0x43, 0x6e, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x4b,
	0x65, 0x79, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x4b,
	0x65, 0x79, 0x22, 0x87, 0x04, 0x0a, 0x0d, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x12, 0x0e, 0x0a, 0x02, 0x49, 0x44, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04,
	0x52, 0x02, 0x49, 0x44, 0x12, 0x12, 0x0a, 0x04, 0x54, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x50, 0x69, 0x64, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x04, 0x52, 0x03, 0x70, 0x69, 0x64, 0x12, 0x23, 0x0a, 0x05, 0x54, 0x69,
	0x74, 0x6c, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0d, 0xe2, 0xdf, 0x1f, 0x09, 0x2a,
	0x05, 0x37, 0x30, 0x30, 0x31, 0x33, 0x58, 0x01, 0x52, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x12,
	0x12, 0x0a, 0x04, 0x49, 0x63, 0x6f, 0x6e, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x69,
	0x63, 0x6f, 0x6e, 0x12, 0x10, 0x0a, 0x03, 0x55, 0x72, 0x6c, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x03, 0x75, 0x72, 0x6c, 0x12, 0x25, 0x0a, 0x06, 0x4d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x18,
	0x07, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0d, 0xe2, 0xdf, 0x1f, 0x09, 0x2a, 0x05, 0x37, 0x30, 0x30,
	0x31, 0x35, 0x58, 0x01, 0x52, 0x06, 0x6d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x12, 0x14, 0x0a, 0x05,
	0x57, 0x65, 0x69, 0x67, 0x68, 0x18, 0x08, 0x20, 0x01, 0x28, 0x04, 0x52, 0x05, 0x77, 0x65, 0x69,
	0x67, 0x68, 0x12, 0x16, 0x0a, 0x06, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x09, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x16, 0x0a, 0x06, 0x52, 0x65,
	0x6d, 0x61, 0x72, 0x6b, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x72, 0x65, 0x6d, 0x61,
	0x72, 0x6b, 0x12, 0x16, 0x0a, 0x06, 0x45, 0x78, 0x74, 0x65, 0x6e, 0x64, 0x18, 0x0b, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x06, 0x65, 0x78, 0x74, 0x65, 0x6e, 0x64, 0x12, 0x25, 0x0a, 0x06, 0x44, 0x6f,
	0x6d, 0x61, 0x69, 0x6e, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0d, 0xe2, 0xdf, 0x1f, 0x09,
	0x2a, 0x05, 0x37, 0x30, 0x30, 0x30, 0x31, 0x58, 0x01, 0x52, 0x06, 0x64, 0x6f, 0x6d, 0x61, 0x69,
	0x6e, 0x12, 0x1a, 0x0a, 0x08, 0x44, 0x61, 0x74, 0x61, 0x4f, 0x70, 0x65, 0x6e, 0x18, 0x0e, 0x20,
	0x01, 0x28, 0x08, 0x52, 0x08, 0x64, 0x61, 0x74, 0x61, 0x4f, 0x70, 0x65, 0x6e, 0x12, 0x2a, 0x0a,
	0x08, 0x52, 0x75, 0x6c, 0x65, 0x44, 0x61, 0x74, 0x61, 0x18, 0x0c, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x0e, 0x2e, 0x72, 0x75, 0x6c, 0x65, 0x2e, 0x52, 0x75, 0x6c, 0x65, 0x44, 0x61, 0x74, 0x61, 0x52,
	0x08, 0x72, 0x75, 0x6c, 0x65, 0x44, 0x61, 0x74, 0x61, 0x12, 0x18, 0x0a, 0x07, 0x43, 0x61, 0x6e,
	0x4a, 0x75, 0x6d, 0x70, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x07, 0x63, 0x61, 0x6e, 0x4a,
	0x75, 0x6d, 0x70, 0x12, 0x1a, 0x0a, 0x08, 0x4d, 0x65, 0x6e, 0x75, 0x54, 0x79, 0x70, 0x65, 0x18,
	0x10, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x6d, 0x65, 0x6e, 0x75, 0x54, 0x79, 0x70, 0x65, 0x12,
	0x2f, 0x0a, 0x0a, 0x72, 0x75, 0x6c, 0x65, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x73, 0x18, 0x11, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x0f, 0x2e, 0x72, 0x75, 0x6c, 0x65, 0x2e, 0x52, 0x75, 0x6c, 0x65, 0x46,
	0x69, 0x65, 0x6c, 0x64, 0x52, 0x0a, 0x72, 0x75, 0x6c, 0x65, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x73,
	0x12, 0x1a, 0x0a, 0x08, 0x67, 0x72, 0x61, 0x79, 0x49, 0x63, 0x6f, 0x6e, 0x18, 0x12, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x08, 0x67, 0x72, 0x61, 0x79, 0x49, 0x63, 0x6f, 0x6e, 0x22, 0x7a, 0x0a, 0x10,
	0x46, 0x69, 0x6e, 0x64, 0x42, 0x79, 0x55, 0x72, 0x6c, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x12, 0x10, 0x0a, 0x03, 0x55, 0x72, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x55,
	0x72, 0x6c, 0x12, 0x16, 0x0a, 0x06, 0x44, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x06, 0x64, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x12, 0x16, 0x0a, 0x06, 0x4d, 0x65,
	0x74, 0x68, 0x6f, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x4d, 0x65, 0x74, 0x68,
	0x6f, 0x64, 0x12, 0x24, 0x0a, 0x0d, 0x44, 0x65, 0x70, 0x61, 0x72, 0x74, 0x6d, 0x65, 0x6e, 0x74,
	0x49, 0x64, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x04, 0x52, 0x0d, 0x44, 0x65, 0x70, 0x61, 0x72,
	0x74, 0x6d, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x73, 0x22, 0x8a, 0x01, 0x0a, 0x14, 0x46, 0x69, 0x6e,
	0x64, 0x52, 0x75, 0x6c, 0x65, 0x42, 0x79, 0x55, 0x72, 0x6c, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x12, 0x1c, 0x0a, 0x09, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x49, 0x44, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x04, 0x52, 0x09, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x49, 0x44, 0x12,
	0x16, 0x0a, 0x06, 0x44, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x06, 0x64, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x12, 0x16, 0x0a, 0x06, 0x4d, 0x65, 0x74, 0x68, 0x6f,
	0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x4d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x12,
	0x10, 0x0a, 0x03, 0x55, 0x72, 0x6c, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x55, 0x72,
	0x6c, 0x12, 0x12, 0x0a, 0x04, 0x54, 0x79, 0x70, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x04, 0x74, 0x79, 0x70, 0x65, 0x22, 0x72, 0x0a, 0x10, 0x52, 0x75, 0x6c, 0x65, 0x42, 0x79, 0x55,
	0x72, 0x6c, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1c, 0x0a, 0x09, 0x41, 0x63, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x49, 0x44, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x09, 0x41, 0x63,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x49, 0x44, 0x12, 0x16, 0x0a, 0x06, 0x44, 0x6f, 0x6d, 0x61, 0x69,
	0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x64, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x12,
	0x16, 0x0a, 0x06, 0x4d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x06, 0x4d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x12, 0x10, 0x0a, 0x03, 0x55, 0x72, 0x6c, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x55, 0x72, 0x6c, 0x22, 0xbd, 0x01, 0x0a, 0x11, 0x52, 0x75,
	0x6c, 0x65, 0x42, 0x79, 0x55, 0x72, 0x6c, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12,
	0x16, 0x0a, 0x06, 0x49, 0x73, 0x50, 0x61, 0x73, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52,
	0x06, 0x69, 0x73, 0x50, 0x61, 0x73, 0x73, 0x12, 0x18, 0x0a, 0x07, 0x49, 0x73, 0x41, 0x64, 0x6d,
	0x69, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x69, 0x73, 0x41, 0x64, 0x6d, 0x69,
	0x6e, 0x12, 0x0e, 0x0a, 0x02, 0x49, 0x44, 0x18, 0x03, 0x20, 0x01, 0x28, 0x04, 0x52, 0x02, 0x49,
	0x44, 0x12, 0x1a, 0x0a, 0x08, 0x44, 0x61, 0x74, 0x61, 0x4f, 0x70, 0x65, 0x6e, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x08, 0x52, 0x08, 0x64, 0x61, 0x74, 0x61, 0x4f, 0x70, 0x65, 0x6e, 0x12, 0x1c, 0x0a,
	0x09, 0x44, 0x61, 0x74, 0x61, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x18, 0x05, 0x20, 0x03, 0x28, 0x09,
	0x52, 0x09, 0x64, 0x61, 0x74, 0x61, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x45,
	0x78, 0x74, 0x65, 0x6e, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x65, 0x78, 0x74,
	0x65, 0x6e, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x45, 0x78, 0x69, 0x73, 0x74, 0x18, 0x07, 0x20, 0x01,
	0x28, 0x08, 0x52, 0x05, 0x65, 0x78, 0x69, 0x73, 0x74, 0x22, 0x20, 0x0a, 0x0e, 0x43, 0x72, 0x65,
	0x61, 0x74, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x0e, 0x0a, 0x02, 0x49,
	0x44, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x02, 0x49, 0x44, 0x22, 0x37, 0x0a, 0x0d, 0x52,
	0x65, 0x6d, 0x6f, 0x76, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x0e, 0x0a, 0x02,
	0x49, 0x44, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x02, 0x49, 0x44, 0x12, 0x16, 0x0a, 0x06,
	0x44, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x64, 0x6f,
	0x6d, 0x61, 0x69, 0x6e, 0x22, 0x10, 0x0a, 0x0e, 0x52, 0x65, 0x6d, 0x6f, 0x76, 0x65, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x37, 0x0a, 0x0d, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x0e, 0x0a, 0x02, 0x49, 0x44, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x02, 0x49, 0x44, 0x12, 0x16, 0x0a, 0x06, 0x44, 0x6f, 0x6d, 0x61, 0x69,
	0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x64, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x22,
	0x89, 0x04, 0x0a, 0x0e, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x12, 0x0e, 0x0a, 0x02, 0x49, 0x44, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x02,
	0x49, 0x44, 0x12, 0x12, 0x0a, 0x04, 0x54, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x50, 0x69, 0x64, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x04, 0x52, 0x03, 0x70, 0x69, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x54, 0x69, 0x74, 0x6c,
	0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x12, 0x12,
	0x0a, 0x04, 0x49, 0x63, 0x6f, 0x6e, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x69, 0x63,
	0x6f, 0x6e, 0x12, 0x10, 0x0a, 0x03, 0x55, 0x72, 0x6c, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x03, 0x75, 0x72, 0x6c, 0x12, 0x16, 0x0a, 0x06, 0x4d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x18, 0x07,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x6d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x12, 0x14, 0x0a, 0x05,
	0x57, 0x65, 0x69, 0x67, 0x68, 0x18, 0x08, 0x20, 0x01, 0x28, 0x04, 0x52, 0x05, 0x77, 0x65, 0x69,
	0x67, 0x68, 0x12, 0x16, 0x0a, 0x06, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x09, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x16, 0x0a, 0x06, 0x52, 0x65,
	0x6d, 0x61, 0x72, 0x6b, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x72, 0x65, 0x6d, 0x61,
	0x72, 0x6b, 0x12, 0x16, 0x0a, 0x06, 0x45, 0x78, 0x74, 0x65, 0x6e, 0x64, 0x18, 0x0b, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x06, 0x65, 0x78, 0x74, 0x65, 0x6e, 0x64, 0x12, 0x2a, 0x0a, 0x08, 0x52, 0x75,
	0x6c, 0x65, 0x44, 0x61, 0x74, 0x61, 0x18, 0x0c, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x0e, 0x2e, 0x72,
	0x75, 0x6c, 0x65, 0x2e, 0x52, 0x75, 0x6c, 0x65, 0x44, 0x61, 0x74, 0x61, 0x52, 0x08, 0x72, 0x75,
	0x6c, 0x65, 0x44, 0x61, 0x74, 0x61, 0x12, 0x28, 0x0a, 0x03, 0x53, 0x6f, 0x6e, 0x18, 0x0d, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x72, 0x75, 0x6c, 0x65, 0x2e, 0x44, 0x65, 0x74, 0x61, 0x69,
	0x6c, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x52, 0x05, 0x72, 0x75, 0x6c, 0x65, 0x73,
	0x12, 0x18, 0x0a, 0x07, 0x43, 0x61, 0x6e, 0x4a, 0x75, 0x6d, 0x70, 0x18, 0x0f, 0x20, 0x01, 0x28,
	0x0d, 0x52, 0x07, 0x63, 0x61, 0x6e, 0x4a, 0x75, 0x6d, 0x70, 0x12, 0x1a, 0x0a, 0x08, 0x4d, 0x65,
	0x6e, 0x75, 0x54, 0x79, 0x70, 0x65, 0x18, 0x10, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x6d, 0x65,
	0x6e, 0x75, 0x54, 0x79, 0x70, 0x65, 0x12, 0x36, 0x0a, 0x16, 0x4e, 0x6f, 0x77, 0x49, 0x6e, 0x74,
	0x65, 0x72, 0x66, 0x61, 0x63, 0x65, 0x44, 0x61, 0x74, 0x61, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x73,
	0x18, 0x11, 0x20, 0x03, 0x28, 0x09, 0x52, 0x16, 0x6e, 0x6f, 0x77, 0x49, 0x6e, 0x74, 0x65, 0x72,
	0x66, 0x61, 0x63, 0x65, 0x44, 0x61, 0x74, 0x61, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x73, 0x12, 0x2f,
	0x0a, 0x0a, 0x52, 0x75, 0x6c, 0x65, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x73, 0x18, 0x12, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x0f, 0x2e, 0x72, 0x75, 0x6c, 0x65, 0x2e, 0x52, 0x75, 0x6c, 0x65, 0x46, 0x69,
	0x65, 0x6c, 0x64, 0x52, 0x0a, 0x52, 0x75, 0x6c, 0x65, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x73, 0x12,
	0x1a, 0x0a, 0x08, 0x67, 0x72, 0x61, 0x79, 0x49, 0x63, 0x6f, 0x6e, 0x18, 0x13, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x08, 0x67, 0x72, 0x61, 0x79, 0x49, 0x63, 0x6f, 0x6e, 0x22, 0xe3, 0x04, 0x0a, 0x08,
	0x52, 0x75, 0x6c, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x0e, 0x0a, 0x02, 0x49, 0x44, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x04, 0x52, 0x02, 0x49, 0x44, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x10, 0x0a, 0x03,
	0x75, 0x72, 0x6c, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x75, 0x72, 0x6c, 0x12, 0x16,
	0x0a, 0x06, 0x6d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06,
	0x6d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x12, 0x1e, 0x0a, 0x0a, 0x72, 0x75, 0x6c, 0x65, 0x44, 0x61,
	0x74, 0x61, 0x49, 0x44, 0x18, 0x05, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0a, 0x72, 0x75, 0x6c, 0x65,
	0x44, 0x61, 0x74, 0x61, 0x49, 0x44, 0x12, 0x22, 0x0a, 0x0c, 0x72, 0x75, 0x6c, 0x65, 0x44, 0x61,
	0x74, 0x61, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x72, 0x75,
	0x6c, 0x65, 0x44, 0x61, 0x74, 0x61, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x24, 0x0a, 0x0d, 0x72, 0x75,
	0x6c, 0x65, 0x44, 0x61, 0x74, 0x61, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x18, 0x07, 0x20, 0x03, 0x28,
	0x09, 0x52, 0x0d, 0x72, 0x75, 0x6c, 0x65, 0x44, 0x61, 0x74, 0x61, 0x46, 0x69, 0x65, 0x6c, 0x64,
	0x12, 0x20, 0x0a, 0x03, 0x73, 0x6f, 0x6e, 0x18, 0x08, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x0e, 0x2e,
	0x72, 0x75, 0x6c, 0x65, 0x2e, 0x52, 0x75, 0x6c, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x03, 0x73,
	0x6f, 0x6e, 0x12, 0x10, 0x0a, 0x03, 0x70, 0x69, 0x64, 0x18, 0x09, 0x20, 0x01, 0x28, 0x04, 0x52,
	0x03, 0x70, 0x69, 0x64, 0x12, 0x2a, 0x0a, 0x08, 0x72, 0x75, 0x6c, 0x65, 0x44, 0x61, 0x74, 0x61,
	0x18, 0x0a, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x0e, 0x2e, 0x72, 0x75, 0x6c, 0x65, 0x2e, 0x52, 0x75,
	0x6c, 0x65, 0x44, 0x61, 0x74, 0x61, 0x52, 0x08, 0x72, 0x75, 0x6c, 0x65, 0x44, 0x61, 0x74, 0x61,
	0x12, 0x1a, 0x0a, 0x08, 0x6d, 0x65, 0x6e, 0x75, 0x54, 0x79, 0x70, 0x65, 0x18, 0x0b, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x08, 0x6d, 0x65, 0x6e, 0x75, 0x54, 0x79, 0x70, 0x65, 0x12, 0x12, 0x0a, 0x04,
	0x74, 0x79, 0x70, 0x65, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65,
	0x12, 0x47, 0x0a, 0x12, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x75, 0x6c, 0x65,
	0x46, 0x69, 0x65, 0x6c, 0x64, 0x73, 0x18, 0x0d, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x72,
	0x75, 0x6c, 0x65, 0x2e, 0x50, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x75, 0x6c, 0x65,
	0x46, 0x69, 0x65, 0x6c, 0x64, 0x52, 0x12, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x52,
	0x75, 0x6c, 0x65, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x73, 0x12, 0x1c, 0x0a, 0x09, 0x64, 0x61, 0x74,
	0x61, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x09, 0x64, 0x61,
	0x74, 0x61, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x12, 0x34, 0x0a, 0x0d, 0x69, 0x6e, 0x74, 0x65, 0x72,
	0x66, 0x61, 0x63, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x18, 0x0f, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x0e,
	0x2e, 0x72, 0x75, 0x6c, 0x65, 0x2e, 0x52, 0x75, 0x6c, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x0d,
	0x69, 0x6e, 0x74, 0x65, 0x72, 0x66, 0x61, 0x63, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x2e, 0x0a,
	0x0a, 0x62, 0x75, 0x74, 0x74, 0x6f, 0x6e, 0x4c, 0x69, 0x73, 0x74, 0x18, 0x10, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x0e, 0x2e, 0x72, 0x75, 0x6c, 0x65, 0x2e, 0x52, 0x75, 0x6c, 0x65, 0x49, 0x6e, 0x66,
	0x6f, 0x52, 0x0a, 0x62, 0x75, 0x74, 0x74, 0x6f, 0x6e, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x14, 0x0a,
	0x05, 0x77, 0x65, 0x69, 0x67, 0x68, 0x18, 0x11, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x05, 0x77, 0x65,
	0x69, 0x67, 0x68, 0x12, 0x2c, 0x0a, 0x11, 0x64, 0x61, 0x74, 0x61, 0x44, 0x65, 0x70, 0x61, 0x72,
	0x74, 0x6d, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x73, 0x18, 0x12, 0x20, 0x03, 0x28, 0x0d, 0x52, 0x11,
	0x64, 0x61, 0x74, 0x61, 0x44, 0x65, 0x70, 0x61, 0x72, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x49, 0x64,
	0x73, 0x22, 0x9b, 0x01, 0x0a, 0x11, 0x50, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x75,
	0x6c, 0x65, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x12, 0x0e, 0x0a, 0x02, 0x49, 0x44, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x04, 0x52, 0x02, 0x49, 0x44, 0x12, 0x20, 0x0a, 0x0b, 0x72, 0x75, 0x6c, 0x65, 0x46,
	0x69, 0x65, 0x6c, 0x64, 0x49, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0b, 0x72, 0x75,
	0x6c, 0x65, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x49, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x72, 0x75, 0x6c,
	0x65, 0x49, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x04, 0x52, 0x06, 0x72, 0x75, 0x6c, 0x65, 0x49,
	0x64, 0x12, 0x20, 0x0a, 0x0b, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x43, 0x6e, 0x4e, 0x61, 0x6d, 0x65,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x43, 0x6e, 0x4e,
	0x61, 0x6d, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x4b, 0x65, 0x79, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x4b, 0x65, 0x79, 0x22,
	0x2c, 0x0a, 0x06, 0x4c, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x0e, 0x0a, 0x02, 0x49, 0x44, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x4e, 0x61, 0x6d,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x22, 0xcd, 0x03,
	0x0a, 0x0d, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12,
	0x0e, 0x0a, 0x02, 0x49, 0x44, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x02, 0x69, 0x64, 0x12,
	0x12, 0x0a, 0x04, 0x54, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x74,
	0x79, 0x70, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x50, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x04,
	0x52, 0x03, 0x70, 0x69, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x54, 0x69, 0x74, 0x6c, 0x65, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x49,
	0x63, 0x6f, 0x6e, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x69, 0x63, 0x6f, 0x6e, 0x12,
	0x10, 0x0a, 0x03, 0x55, 0x72, 0x6c, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x75, 0x72,
	0x6c, 0x12, 0x16, 0x0a, 0x06, 0x4d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x18, 0x07, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x06, 0x6d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x57, 0x65, 0x69,
	0x67, 0x68, 0x18, 0x08, 0x20, 0x01, 0x28, 0x04, 0x52, 0x05, 0x77, 0x65, 0x69, 0x67, 0x68, 0x12,
	0x16, 0x0a, 0x06, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x16, 0x0a, 0x06, 0x52, 0x65, 0x6d, 0x61, 0x72,
	0x6b, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x72, 0x65, 0x6d, 0x61, 0x72, 0x6b, 0x12,
	0x16, 0x0a, 0x06, 0x45, 0x78, 0x74, 0x65, 0x6e, 0x64, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x06, 0x65, 0x78, 0x74, 0x65, 0x6e, 0x64, 0x12, 0x2a, 0x0a, 0x08, 0x52, 0x75, 0x6c, 0x65, 0x44,
	0x61, 0x74, 0x61, 0x18, 0x0c, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x0e, 0x2e, 0x72, 0x75, 0x6c, 0x65,
	0x2e, 0x52, 0x75, 0x6c, 0x65, 0x44, 0x61, 0x74, 0x61, 0x52, 0x08, 0x72, 0x75, 0x6c, 0x65, 0x44,
	0x61, 0x74, 0x61, 0x12, 0x25, 0x0a, 0x06, 0x44, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x18, 0x0d, 0x20,
	0x01, 0x28, 0x09, 0x42, 0x0d, 0xe2, 0xdf, 0x1f, 0x09, 0x2a, 0x05, 0x37, 0x30, 0x30, 0x30, 0x31,
	0x58, 0x01, 0x52, 0x06, 0x64, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x12, 0x18, 0x0a, 0x07, 0x43, 0x61,
	0x6e, 0x4a, 0x75, 0x6d, 0x70, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x07, 0x63, 0x61, 0x6e,
	0x4a, 0x75, 0x6d, 0x70, 0x12, 0x1a, 0x0a, 0x08, 0x4d, 0x65, 0x6e, 0x75, 0x54, 0x79, 0x70, 0x65,
	0x18, 0x10, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x6d, 0x65, 0x6e, 0x75, 0x54, 0x79, 0x70, 0x65,
	0x12, 0x2f, 0x0a, 0x0a, 0x52, 0x75, 0x6c, 0x65, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x73, 0x18, 0x11,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x0f, 0x2e, 0x72, 0x75, 0x6c, 0x65, 0x2e, 0x52, 0x75, 0x6c, 0x65,
	0x46, 0x69, 0x65, 0x6c, 0x64, 0x52, 0x0a, 0x52, 0x75, 0x6c, 0x65, 0x46, 0x69, 0x65, 0x6c, 0x64,
	0x73, 0x12, 0x1a, 0x0a, 0x08, 0x67, 0x72, 0x61, 0x79, 0x49, 0x63, 0x6f, 0x6e, 0x18, 0x12, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x08, 0x67, 0x72, 0x61, 0x79, 0x49, 0x63, 0x6f, 0x6e, 0x22, 0x10, 0x0a,
	0x0e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22,
	0xc9, 0x01, 0x0a, 0x0b, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12,
	0x16, 0x0a, 0x06, 0x44, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x06, 0x64, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x12, 0x1a, 0x0a, 0x08, 0x50, 0x61, 0x67, 0x65, 0x53,
	0x69, 0x7a, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x04, 0x52, 0x08, 0x70, 0x61, 0x67, 0x65, 0x53,
	0x69, 0x7a, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x50, 0x61, 0x67, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x04, 0x52, 0x04, 0x70, 0x61, 0x67, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x50, 0x69, 0x64, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x04, 0x52, 0x03, 0x70, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x54, 0x79, 0x70,
	0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x14, 0x0a,
	0x05, 0x54, 0x69, 0x74, 0x6c, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x74, 0x69,
	0x74, 0x6c, 0x65, 0x12, 0x22, 0x0a, 0x0c, 0x49, 0x73, 0x4e, 0x6f, 0x74, 0x4a, 0x75, 0x6d, 0x70,
	0x54, 0x6f, 0x70, 0x18, 0x08, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0c, 0x69, 0x73, 0x4e, 0x6f, 0x74,
	0x4a, 0x75, 0x6d, 0x70, 0x54, 0x6f, 0x70, 0x12, 0x12, 0x0a, 0x04, 0x55, 0x72, 0x6c, 0x73, 0x18,
	0x09, 0x20, 0x03, 0x28, 0x09, 0x52, 0x04, 0x75, 0x72, 0x6c, 0x73, 0x22, 0x4e, 0x0a, 0x0c, 0x4c,
	0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x43,
	0x6f, 0x75, 0x6e, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x05, 0x43, 0x6f, 0x75, 0x6e,
	0x74, 0x12, 0x28, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x14, 0x2e, 0x72, 0x75, 0x6c, 0x65, 0x2e, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x22, 0x4c, 0x0a, 0x10, 0x4d,
	0x65, 0x6e, 0x75, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12,
	0x14, 0x0a, 0x05, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x05,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x22, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x03, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x0e, 0x2e, 0x72, 0x75, 0x6c, 0x65, 0x2e, 0x52, 0x75, 0x6c, 0x65, 0x49,
	0x6e, 0x66, 0x6f, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x22, 0x44, 0x0a, 0x0c, 0x52, 0x75, 0x6c,
	0x65, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x16, 0x0a, 0x06, 0x44, 0x6f, 0x6d,
	0x61, 0x69, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x44, 0x6f, 0x6d, 0x61, 0x69,
	0x6e, 0x12, 0x1c, 0x0a, 0x09, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x49, 0x44, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x04, 0x52, 0x09, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x49, 0x44, 0x22,
	0xa4, 0x02, 0x0a, 0x0c, 0x52, 0x75, 0x6c, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x12, 0x0e, 0x0a, 0x02, 0x49, 0x44, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x02, 0x49, 0x44,
	0x12, 0x12, 0x0a, 0x04, 0x54, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04,
	0x74, 0x79, 0x70, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x50, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x04, 0x52, 0x03, 0x70, 0x69, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x54, 0x69, 0x74, 0x6c, 0x65, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x12, 0x12, 0x0a, 0x04,
	0x49, 0x63, 0x6f, 0x6e, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x69, 0x63, 0x6f, 0x6e,
	0x12, 0x10, 0x0a, 0x03, 0x55, 0x72, 0x6c, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x75,
	0x72, 0x6c, 0x12, 0x16, 0x0a, 0x06, 0x4d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x18, 0x07, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x06, 0x6d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x57, 0x65,
	0x69, 0x67, 0x68, 0x18, 0x08, 0x20, 0x01, 0x28, 0x04, 0x52, 0x05, 0x77, 0x65, 0x69, 0x67, 0x68,
	0x12, 0x16, 0x0a, 0x06, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x16, 0x0a, 0x06, 0x52, 0x65, 0x6d, 0x61,
	0x72, 0x6b, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x72, 0x65, 0x6d, 0x61, 0x72, 0x6b,
	0x12, 0x16, 0x0a, 0x06, 0x45, 0x78, 0x74, 0x65, 0x6e, 0x64, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x06, 0x65, 0x78, 0x74, 0x65, 0x6e, 0x64, 0x12, 0x2c, 0x0a, 0x09, 0x52, 0x75, 0x6c, 0x65,
	0x44, 0x61, 0x74, 0x75, 0x6d, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0e, 0x2e, 0x72, 0x75,
	0x6c, 0x65, 0x2e, 0x52, 0x75, 0x6c, 0x65, 0x44, 0x61, 0x74, 0x61, 0x52, 0x09, 0x72, 0x75, 0x6c,
	0x65, 0x44, 0x61, 0x74, 0x75, 0x6d, 0x22, 0x92, 0x02, 0x0a, 0x0c, 0x50, 0x6f, 0x73, 0x69, 0x74,
	0x69, 0x6f, 0x6e, 0x55, 0x73, 0x65, 0x72, 0x12, 0x1e, 0x0a, 0x0a, 0x50, 0x6f, 0x73, 0x69, 0x74,
	0x69, 0x6f, 0x6e, 0x49, 0x44, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0a, 0x70, 0x6f, 0x73,
	0x69, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x44, 0x12, 0x22, 0x0a, 0x0c, 0x50, 0x6f, 0x73, 0x69, 0x74,
	0x69, 0x6f, 0x6e, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x70,
	0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x22, 0x0a, 0x0c, 0x44,
	0x65, 0x70, 0x61, 0x72, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x04, 0x52, 0x0c, 0x64, 0x65, 0x70, 0x61, 0x72, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x12,
	0x26, 0x0a, 0x0e, 0x44, 0x65, 0x70, 0x61, 0x72, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x43, 0x6f, 0x64,
	0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x64, 0x65, 0x70, 0x61, 0x72, 0x74, 0x6d,
	0x65, 0x6e, 0x74, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x26, 0x0a, 0x0e, 0x44, 0x65, 0x70, 0x61, 0x72,
	0x74, 0x6d, 0x65, 0x6e, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0e, 0x64, 0x65, 0x70, 0x61, 0x72, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x12,
	0x16, 0x0a, 0x06, 0x55, 0x73, 0x65, 0x72, 0x49, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x04, 0x52,
	0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x12, 0x1a, 0x0a, 0x08, 0x55, 0x73, 0x65, 0x72, 0x4e,
	0x61, 0x6d, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x75, 0x73, 0x65, 0x72, 0x4e,
	0x61, 0x6d, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x79, 0x6e, 0x63, 0x49, 0x64, 0x18, 0x08, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x06, 0x73, 0x79, 0x6e, 0x63, 0x49, 0x64, 0x22, 0x86, 0x01, 0x0a, 0x10,
	0x44, 0x65, 0x70, 0x61, 0x72, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x4c, 0x65, 0x61, 0x64, 0x65, 0x72,
	0x12, 0x22, 0x0a, 0x0c, 0x44, 0x65, 0x70, 0x61, 0x72, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x49, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0c, 0x64, 0x65, 0x70, 0x61, 0x72, 0x74, 0x6d, 0x65,
	0x6e, 0x74, 0x49, 0x64, 0x12, 0x26, 0x0a, 0x0e, 0x44, 0x65, 0x70, 0x61, 0x72, 0x74, 0x6d, 0x65,
	0x6e, 0x74, 0x43, 0x6f, 0x64, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x64, 0x65,
	0x70, 0x61, 0x72, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x26, 0x0a, 0x0e,
	0x44, 0x65, 0x70, 0x61, 0x72, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x64, 0x65, 0x70, 0x61, 0x72, 0x74, 0x6d, 0x65, 0x6e, 0x74,
	0x4e, 0x61, 0x6d, 0x65, 0x22, 0xc5, 0x05, 0x0a, 0x0d, 0x52, 0x75, 0x6c, 0x65, 0x73, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x30, 0x0a, 0x08, 0x41, 0x6c, 0x6c, 0x4d, 0x65, 0x6e,
	0x75, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x72, 0x75, 0x6c, 0x65, 0x2e,
	0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x52, 0x08,
	0x41, 0x6c, 0x6c, 0x4d, 0x65, 0x6e, 0x75, 0x73, 0x12, 0x30, 0x0a, 0x08, 0x41, 0x6c, 0x6c, 0x52,
	0x75, 0x6c, 0x65, 0x73, 0x18, 0x0a, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x72, 0x75, 0x6c,
	0x65, 0x2e, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x52, 0x08, 0x41, 0x6c, 0x6c, 0x52, 0x75, 0x6c, 0x65, 0x73, 0x12, 0x42, 0x0a, 0x11, 0x41, 0x6c,
	0x6c, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x66, 0x61, 0x63, 0x65, 0x52, 0x75, 0x6c, 0x65, 0x73, 0x18,
	0x0b, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x72, 0x75, 0x6c, 0x65, 0x2e, 0x44, 0x65, 0x74,
	0x61, 0x69, 0x6c, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x52, 0x11, 0x41, 0x6c, 0x6c,
	0x49, 0x6e, 0x74, 0x65, 0x72, 0x66, 0x61, 0x63, 0x65, 0x52, 0x75, 0x6c, 0x65, 0x73, 0x12, 0x36,
	0x0a, 0x0b, 0x4d, 0x79, 0x4d, 0x65, 0x6e, 0x75, 0x41, 0x75, 0x74, 0x68, 0x73, 0x18, 0x02, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x72, 0x75, 0x6c, 0x65, 0x2e, 0x44, 0x65, 0x74, 0x61, 0x69,
	0x6c, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x52, 0x0b, 0x4d, 0x79, 0x4d, 0x65, 0x6e,
	0x75, 0x41, 0x75, 0x74, 0x68, 0x73, 0x12, 0x2e, 0x0a, 0x07, 0x4d, 0x79, 0x52, 0x75, 0x6c, 0x65,
	0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x72, 0x75, 0x6c, 0x65, 0x2e, 0x44,
	0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x52, 0x07, 0x4d,
	0x79, 0x52, 0x75, 0x6c, 0x65, 0x73, 0x12, 0x3a, 0x0a, 0x0d, 0x4d, 0x79, 0x42, 0x75, 0x74, 0x74,
	0x6f, 0x6e, 0x41, 0x75, 0x74, 0x68, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x14, 0x2e,
	0x72, 0x75, 0x6c, 0x65, 0x2e, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x52, 0x0d, 0x4d, 0x79, 0x42, 0x75, 0x74, 0x74, 0x6f, 0x6e, 0x41, 0x75, 0x74,
	0x68, 0x73, 0x12, 0x40, 0x0a, 0x10, 0x4d, 0x79, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x46, 0x61, 0x63,
	0x65, 0x41, 0x75, 0x74, 0x68, 0x73, 0x18, 0x05, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x72,
	0x75, 0x6c, 0x65, 0x2e, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x52, 0x10, 0x4d, 0x79, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x46, 0x61, 0x63, 0x65, 0x41,
	0x75, 0x74, 0x68, 0x73, 0x12, 0x38, 0x0a, 0x0d, 0x50, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e,
	0x55, 0x73, 0x65, 0x72, 0x73, 0x18, 0x06, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x72, 0x75,
	0x6c, 0x65, 0x2e, 0x50, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x55, 0x73, 0x65, 0x72, 0x52,
	0x0d, 0x50, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x55, 0x73, 0x65, 0x72, 0x73, 0x12, 0x44,
	0x0a, 0x11, 0x44, 0x65, 0x70, 0x61, 0x72, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x4c, 0x65, 0x61, 0x64,
	0x65, 0x72, 0x73, 0x18, 0x07, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x72, 0x75, 0x6c, 0x65,
	0x2e, 0x44, 0x65, 0x70, 0x61, 0x72, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x4c, 0x65, 0x61, 0x64, 0x65,
	0x72, 0x52, 0x11, 0x44, 0x65, 0x70, 0x61, 0x72, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x4c, 0x65, 0x61,
	0x64, 0x65, 0x72, 0x73, 0x12, 0x36, 0x0a, 0x0b, 0x41, 0x6c, 0x6c, 0x54, 0x72, 0x65, 0x65, 0x52,
	0x75, 0x6c, 0x65, 0x18, 0x08, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x72, 0x75, 0x6c, 0x65,
	0x2e, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x52,
	0x0b, 0x41, 0x6c, 0x6c, 0x54, 0x72, 0x65, 0x65, 0x52, 0x75, 0x6c, 0x65, 0x12, 0x34, 0x0a, 0x0a,
	0x4d, 0x79, 0x54, 0x72, 0x65, 0x65, 0x52, 0x75, 0x6c, 0x65, 0x18, 0x09, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x14, 0x2e, 0x72, 0x75, 0x6c, 0x65, 0x2e, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x52, 0x0a, 0x4d, 0x79, 0x54, 0x72, 0x65, 0x65, 0x52, 0x75,
	0x6c, 0x65, 0x12, 0x38, 0x0a, 0x0c, 0x4d, 0x79, 0x53, 0x65, 0x63, 0x6f, 0x6e, 0x64, 0x52, 0x75,
	0x6c, 0x65, 0x18, 0x0c, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x72, 0x75, 0x6c, 0x65, 0x2e,
	0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x52, 0x0c,
	0x4d, 0x79, 0x53, 0x65, 0x63, 0x6f, 0x6e, 0x64, 0x52, 0x75, 0x6c, 0x65, 0x22, 0xac, 0x01, 0x0a,
	0x10, 0x55, 0x73, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x12, 0x38, 0x0a, 0x0d, 0x50, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x55, 0x73, 0x65,
	0x72, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x72, 0x75, 0x6c, 0x65, 0x2e,
	0x50, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x55, 0x73, 0x65, 0x72, 0x52, 0x0d, 0x50, 0x6f,
	0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x55, 0x73, 0x65, 0x72, 0x73, 0x12, 0x44, 0x0a, 0x11, 0x44,
	0x65, 0x70, 0x61, 0x72, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x4c, 0x65, 0x61, 0x64, 0x65, 0x72, 0x73,
	0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x72, 0x75, 0x6c, 0x65, 0x2e, 0x44, 0x65,
	0x70, 0x61, 0x72, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x4c, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x11,
	0x44, 0x65, 0x70, 0x61, 0x72, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x4c, 0x65, 0x61, 0x64, 0x65, 0x72,
	0x73, 0x12, 0x18, 0x0a, 0x07, 0x49, 0x73, 0x41, 0x64, 0x6d, 0x69, 0x6e, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x08, 0x52, 0x07, 0x49, 0x73, 0x41, 0x64, 0x6d, 0x69, 0x6e, 0x32, 0xf6, 0x05, 0x0a, 0x04,
	0x52, 0x75, 0x6c, 0x65, 0x12, 0x33, 0x0a, 0x06, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x12, 0x13,
	0x2e, 0x72, 0x75, 0x6c, 0x65, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x1a, 0x14, 0x2e, 0x72, 0x75, 0x6c, 0x65, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74,
	0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x33, 0x0a, 0x06, 0x52, 0x65, 0x6d,
	0x6f, 0x76, 0x65, 0x12, 0x13, 0x2e, 0x72, 0x75, 0x6c, 0x65, 0x2e, 0x52, 0x65, 0x6d, 0x6f, 0x76,
	0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x14, 0x2e, 0x72, 0x75, 0x6c, 0x65, 0x2e,
	0x52, 0x65, 0x6d, 0x6f, 0x76, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x33,
	0x0a, 0x06, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x12, 0x13, 0x2e, 0x72, 0x75, 0x6c, 0x65, 0x2e,
	0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x14, 0x2e,
	0x72, 0x75, 0x6c, 0x65, 0x2e, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x12, 0x33, 0x0a, 0x06, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x12, 0x13, 0x2e,
	0x72, 0x75, 0x6c, 0x65, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x1a, 0x14, 0x2e, 0x72, 0x75, 0x6c, 0x65, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x2d, 0x0a, 0x04, 0x4c, 0x69, 0x73, 0x74,
	0x12, 0x11, 0x2e, 0x72, 0x75, 0x6c, 0x65, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x1a, 0x12, 0x2e, 0x72, 0x75, 0x6c, 0x65, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x35, 0x0a, 0x08, 0x4d, 0x65, 0x6e, 0x75, 0x4c,
	0x69, 0x73, 0x74, 0x12, 0x11, 0x2e, 0x72, 0x75, 0x6c, 0x65, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x16, 0x2e, 0x72, 0x75, 0x6c, 0x65, 0x2e, 0x4d, 0x65,
	0x6e, 0x75, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x30,
	0x0a, 0x05, 0x52, 0x75, 0x6c, 0x65, 0x73, 0x12, 0x12, 0x2e, 0x72, 0x75, 0x6c, 0x65, 0x2e, 0x52,
	0x75, 0x6c, 0x65, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x13, 0x2e, 0x72, 0x75,
	0x6c, 0x65, 0x2e, 0x52, 0x75, 0x6c, 0x65, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x12, 0x36, 0x0a, 0x08, 0x55, 0x73, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x12, 0x2e, 0x72,
	0x75, 0x6c, 0x65, 0x2e, 0x52, 0x75, 0x6c, 0x65, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x1a, 0x16, 0x2e, 0x72, 0x75, 0x6c, 0x65, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x3c, 0x0a, 0x09, 0x52, 0x75, 0x6c, 0x65,
	0x42, 0x79, 0x55, 0x72, 0x6c, 0x12, 0x16, 0x2e, 0x72, 0x75, 0x6c, 0x65, 0x2e, 0x52, 0x75, 0x6c,
	0x65, 0x42, 0x79, 0x55, 0x72, 0x6c, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x17, 0x2e,
	0x72, 0x75, 0x6c, 0x65, 0x2e, 0x52, 0x75, 0x6c, 0x65, 0x42, 0x79, 0x55, 0x72, 0x6c, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x43, 0x0a, 0x0c, 0x46, 0x69, 0x6e, 0x64, 0x55, 0x73,
	0x65, 0x72, 0x52, 0x75, 0x6c, 0x65, 0x12, 0x1a, 0x2e, 0x72, 0x75, 0x6c, 0x65, 0x2e, 0x46, 0x69,
	0x6e, 0x64, 0x52, 0x75, 0x6c, 0x65, 0x42, 0x79, 0x55, 0x72, 0x6c, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x1a, 0x17, 0x2e, 0x72, 0x75, 0x6c, 0x65, 0x2e, 0x52, 0x75, 0x6c, 0x65, 0x42, 0x79,
	0x55, 0x72, 0x6c, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x39, 0x0a, 0x09, 0x46,
	0x69, 0x6e, 0x64, 0x42, 0x79, 0x55, 0x72, 0x6c, 0x12, 0x16, 0x2e, 0x72, 0x75, 0x6c, 0x65, 0x2e,
	0x46, 0x69, 0x6e, 0x64, 0x42, 0x79, 0x55, 0x72, 0x6c, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x1a, 0x14, 0x2e, 0x72, 0x75, 0x6c, 0x65, 0x2e, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x39, 0x0a, 0x0e, 0x46, 0x69, 0x6e, 0x64, 0x55, 0x73,
	0x65, 0x72, 0x73, 0x42, 0x79, 0x55, 0x72, 0x6c, 0x12, 0x16, 0x2e, 0x72, 0x75, 0x6c, 0x65, 0x2e,
	0x46, 0x69, 0x6e, 0x64, 0x42, 0x79, 0x55, 0x72, 0x6c, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x1a, 0x0f, 0x2e, 0x72, 0x75, 0x6c, 0x65, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f,
	0x73, 0x12, 0x51, 0x0a, 0x10, 0x50, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x55, 0x73, 0x65,
	0x72, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x1d, 0x2e, 0x72, 0x75, 0x6c, 0x65, 0x2e, 0x50, 0x6f, 0x73,
	0x69, 0x74, 0x69, 0x6f, 0x6e, 0x55, 0x73, 0x65, 0x72, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x1a, 0x1e, 0x2e, 0x72, 0x75, 0x6c, 0x65, 0x2e, 0x50, 0x6f, 0x73, 0x69,
	0x74, 0x69, 0x6f, 0x6e, 0x55, 0x73, 0x65, 0x72, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x42, 0x09, 0x5a, 0x07, 0x2e, 0x2f, 0x3b, 0x72, 0x75, 0x6c, 0x65, 0x62,
	0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_rule_rule_proto_rawDescOnce sync.Once
	file_api_rule_rule_proto_rawDescData = file_api_rule_rule_proto_rawDesc
)

func file_api_rule_rule_proto_rawDescGZIP() []byte {
	file_api_rule_rule_proto_rawDescOnce.Do(func() {
		file_api_rule_rule_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_rule_rule_proto_rawDescData)
	})
	return file_api_rule_rule_proto_rawDescData
}

var file_api_rule_rule_proto_msgTypes = make([]protoimpl.MessageInfo, 30)
var file_api_rule_rule_proto_goTypes = []interface{}{
	(*UserInfo)(nil),                 // 0: rule.UserInfo
	(*UserInfos)(nil),                // 1: rule.UserInfos
	(*PositionUserListResponse)(nil), // 2: rule.PositionUserListResponse
	(*PositionUserListRequest)(nil),  // 3: rule.PositionUserListRequest
	(*RuleData)(nil),                 // 4: rule.RuleData
	(*RuleField)(nil),                // 5: rule.RuleField
	(*CreateRequest)(nil),            // 6: rule.CreateRequest
	(*FindByUrlRequest)(nil),         // 7: rule.FindByUrlRequest
	(*FindRuleByUrlRequest)(nil),     // 8: rule.FindRuleByUrlRequest
	(*RuleByUrlRequest)(nil),         // 9: rule.RuleByUrlRequest
	(*RuleByUrlResponse)(nil),        // 10: rule.RuleByUrlResponse
	(*CreateResponse)(nil),           // 11: rule.CreateResponse
	(*RemoveRequest)(nil),            // 12: rule.RemoveRequest
	(*RemoveResponse)(nil),           // 13: rule.RemoveResponse
	(*DetailRequest)(nil),            // 14: rule.DetailRequest
	(*DetailResponse)(nil),           // 15: rule.DetailResponse
	(*RuleInfo)(nil),                 // 16: rule.RuleInfo
	(*PositionRuleField)(nil),        // 17: rule.PositionRuleField
	(*Leader)(nil),                   // 18: rule.Leader
	(*UpdateRequest)(nil),            // 19: rule.UpdateRequest
	(*UpdateResponse)(nil),           // 20: rule.UpdateResponse
	(*ListRequest)(nil),              // 21: rule.ListRequest
	(*ListResponse)(nil),             // 22: rule.ListResponse
	(*MenuListResponse)(nil),         // 23: rule.MenuListResponse
	(*RulesRequest)(nil),             // 24: rule.RulesRequest
	(*RuleResponse)(nil),             // 25: rule.RuleResponse
	(*PositionUser)(nil),             // 26: rule.PositionUser
	(*DepartmentLeader)(nil),         // 27: rule.DepartmentLeader
	(*RulesResponse)(nil),            // 28: rule.RulesResponse
	(*UserInfoResponse)(nil),         // 29: rule.UserInfoResponse
}
var file_api_rule_rule_proto_depIdxs = []int32{
	0,  // 0: rule.UserInfos.List:type_name -> rule.UserInfo
	26, // 1: rule.PositionUserListResponse.Data:type_name -> rule.PositionUser
	4,  // 2: rule.CreateRequest.RuleData:type_name -> rule.RuleData
	5,  // 3: rule.CreateRequest.ruleFields:type_name -> rule.RuleField
	4,  // 4: rule.DetailResponse.RuleData:type_name -> rule.RuleData
	15, // 5: rule.DetailResponse.Son:type_name -> rule.DetailResponse
	5,  // 6: rule.DetailResponse.RuleFields:type_name -> rule.RuleField
	16, // 7: rule.RuleInfo.son:type_name -> rule.RuleInfo
	4,  // 8: rule.RuleInfo.ruleData:type_name -> rule.RuleData
	17, // 9: rule.RuleInfo.positionRuleFields:type_name -> rule.PositionRuleField
	16, // 10: rule.RuleInfo.interfaceList:type_name -> rule.RuleInfo
	16, // 11: rule.RuleInfo.buttonList:type_name -> rule.RuleInfo
	4,  // 12: rule.UpdateRequest.RuleData:type_name -> rule.RuleData
	5,  // 13: rule.UpdateRequest.RuleFields:type_name -> rule.RuleField
	15, // 14: rule.ListResponse.data:type_name -> rule.DetailResponse
	16, // 15: rule.MenuListResponse.data:type_name -> rule.RuleInfo
	4,  // 16: rule.RuleResponse.RuleDatum:type_name -> rule.RuleData
	15, // 17: rule.RulesResponse.AllMenus:type_name -> rule.DetailResponse
	15, // 18: rule.RulesResponse.AllRules:type_name -> rule.DetailResponse
	15, // 19: rule.RulesResponse.AllInterfaceRules:type_name -> rule.DetailResponse
	15, // 20: rule.RulesResponse.MyMenuAuths:type_name -> rule.DetailResponse
	15, // 21: rule.RulesResponse.MyRules:type_name -> rule.DetailResponse
	15, // 22: rule.RulesResponse.MyButtonAuths:type_name -> rule.DetailResponse
	15, // 23: rule.RulesResponse.MyInterFaceAuths:type_name -> rule.DetailResponse
	26, // 24: rule.RulesResponse.PositionUsers:type_name -> rule.PositionUser
	27, // 25: rule.RulesResponse.DepartmentLeaders:type_name -> rule.DepartmentLeader
	15, // 26: rule.RulesResponse.AllTreeRule:type_name -> rule.DetailResponse
	15, // 27: rule.RulesResponse.MyTreeRule:type_name -> rule.DetailResponse
	15, // 28: rule.RulesResponse.MySecondRule:type_name -> rule.DetailResponse
	26, // 29: rule.UserInfoResponse.PositionUsers:type_name -> rule.PositionUser
	27, // 30: rule.UserInfoResponse.DepartmentLeaders:type_name -> rule.DepartmentLeader
	6,  // 31: rule.Rule.Create:input_type -> rule.CreateRequest
	12, // 32: rule.Rule.Remove:input_type -> rule.RemoveRequest
	14, // 33: rule.Rule.Detail:input_type -> rule.DetailRequest
	19, // 34: rule.Rule.Update:input_type -> rule.UpdateRequest
	21, // 35: rule.Rule.List:input_type -> rule.ListRequest
	21, // 36: rule.Rule.MenuList:input_type -> rule.ListRequest
	24, // 37: rule.Rule.Rules:input_type -> rule.RulesRequest
	24, // 38: rule.Rule.UserInfo:input_type -> rule.RulesRequest
	9,  // 39: rule.Rule.RuleByUrl:input_type -> rule.RuleByUrlRequest
	8,  // 40: rule.Rule.FindUserRule:input_type -> rule.FindRuleByUrlRequest
	7,  // 41: rule.Rule.FindByUrl:input_type -> rule.FindByUrlRequest
	7,  // 42: rule.Rule.FindUsersByUrl:input_type -> rule.FindByUrlRequest
	3,  // 43: rule.Rule.PositionUserList:input_type -> rule.PositionUserListRequest
	11, // 44: rule.Rule.Create:output_type -> rule.CreateResponse
	13, // 45: rule.Rule.Remove:output_type -> rule.RemoveResponse
	15, // 46: rule.Rule.Detail:output_type -> rule.DetailResponse
	20, // 47: rule.Rule.Update:output_type -> rule.UpdateResponse
	22, // 48: rule.Rule.List:output_type -> rule.ListResponse
	23, // 49: rule.Rule.MenuList:output_type -> rule.MenuListResponse
	28, // 50: rule.Rule.Rules:output_type -> rule.RulesResponse
	29, // 51: rule.Rule.UserInfo:output_type -> rule.UserInfoResponse
	10, // 52: rule.Rule.RuleByUrl:output_type -> rule.RuleByUrlResponse
	10, // 53: rule.Rule.FindUserRule:output_type -> rule.RuleByUrlResponse
	15, // 54: rule.Rule.FindByUrl:output_type -> rule.DetailResponse
	1,  // 55: rule.Rule.FindUsersByUrl:output_type -> rule.UserInfos
	2,  // 56: rule.Rule.PositionUserList:output_type -> rule.PositionUserListResponse
	44, // [44:57] is the sub-list for method output_type
	31, // [31:44] is the sub-list for method input_type
	31, // [31:31] is the sub-list for extension type_name
	31, // [31:31] is the sub-list for extension extendee
	0,  // [0:31] is the sub-list for field type_name
}

func init() { file_api_rule_rule_proto_init() }
func file_api_rule_rule_proto_init() {
	if File_api_rule_rule_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_api_rule_rule_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UserInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_rule_rule_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UserInfos); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_rule_rule_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PositionUserListResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_rule_rule_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PositionUserListRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_rule_rule_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RuleData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_rule_rule_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RuleField); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_rule_rule_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_rule_rule_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FindByUrlRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_rule_rule_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FindRuleByUrlRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_rule_rule_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RuleByUrlRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_rule_rule_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RuleByUrlResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_rule_rule_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_rule_rule_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RemoveRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_rule_rule_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RemoveResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_rule_rule_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DetailRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_rule_rule_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DetailResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_rule_rule_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RuleInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_rule_rule_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PositionRuleField); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_rule_rule_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Leader); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_rule_rule_proto_msgTypes[19].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_rule_rule_proto_msgTypes[20].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_rule_rule_proto_msgTypes[21].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_rule_rule_proto_msgTypes[22].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_rule_rule_proto_msgTypes[23].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MenuListResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_rule_rule_proto_msgTypes[24].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RulesRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_rule_rule_proto_msgTypes[25].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RuleResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_rule_rule_proto_msgTypes[26].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PositionUser); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_rule_rule_proto_msgTypes[27].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DepartmentLeader); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_rule_rule_proto_msgTypes[28].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RulesResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_rule_rule_proto_msgTypes[29].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UserInfoResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_rule_rule_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   30,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_api_rule_rule_proto_goTypes,
		DependencyIndexes: file_api_rule_rule_proto_depIdxs,
		MessageInfos:      file_api_rule_rule_proto_msgTypes,
	}.Build()
	File_api_rule_rule_proto = out.File
	file_api_rule_rule_proto_rawDesc = nil
	file_api_rule_rule_proto_goTypes = nil
	file_api_rule_rule_proto_depIdxs = nil
}
