// Code generated by protoc-gen-go-triple. DO NOT EDIT.
// versions:
// - protoc-gen-go-triple v1.0.8
// - protoc             v3.21.12
// source: api/rule/rule.proto

package rule

import (
	context "context"
	protocol "dubbo.apache.org/dubbo-go/v3/protocol"
	dubbo3 "dubbo.apache.org/dubbo-go/v3/protocol/dubbo3"
	invocation "dubbo.apache.org/dubbo-go/v3/protocol/invocation"
	grpc_go "github.com/dubbogo/grpc-go"
	codes "github.com/dubbogo/grpc-go/codes"
	metadata "github.com/dubbogo/grpc-go/metadata"
	status "github.com/dubbogo/grpc-go/status"
	common "github.com/dubbogo/triple/pkg/common"
	constant "github.com/dubbogo/triple/pkg/common/constant"
	triple "github.com/dubbogo/triple/pkg/triple"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc_go.SupportPackageIsVersion7

// RuleClient is the client API for Rule service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type RuleClient interface {
	Create(ctx context.Context, in *CreateRequest, opts ...grpc_go.CallOption) (*CreateResponse, common.ErrorWithAttachment)
	Remove(ctx context.Context, in *RemoveRequest, opts ...grpc_go.CallOption) (*RemoveResponse, common.ErrorWithAttachment)
	Detail(ctx context.Context, in *DetailRequest, opts ...grpc_go.CallOption) (*DetailResponse, common.ErrorWithAttachment)
	Update(ctx context.Context, in *UpdateRequest, opts ...grpc_go.CallOption) (*UpdateResponse, common.ErrorWithAttachment)
	List(ctx context.Context, in *ListRequest, opts ...grpc_go.CallOption) (*ListResponse, common.ErrorWithAttachment)
	MenuList(ctx context.Context, in *ListRequest, opts ...grpc_go.CallOption) (*MenuListResponse, common.ErrorWithAttachment)
	Rules(ctx context.Context, in *RulesRequest, opts ...grpc_go.CallOption) (*RulesResponse, common.ErrorWithAttachment)
	UserInfo(ctx context.Context, in *RulesRequest, opts ...grpc_go.CallOption) (*UserInfoResponse, common.ErrorWithAttachment)
	RuleByUrl(ctx context.Context, in *RuleByUrlRequest, opts ...grpc_go.CallOption) (*RuleByUrlResponse, common.ErrorWithAttachment)
	FindUserRule(ctx context.Context, in *FindRuleByUrlRequest, opts ...grpc_go.CallOption) (*RuleByUrlResponse, common.ErrorWithAttachment)
	FindByUrl(ctx context.Context, in *FindByUrlRequest, opts ...grpc_go.CallOption) (*DetailResponse, common.ErrorWithAttachment)
	FindUsersByUrl(ctx context.Context, in *FindByUrlRequest, opts ...grpc_go.CallOption) (*UserInfos, common.ErrorWithAttachment)
	PositionUserList(ctx context.Context, in *PositionUserListRequest, opts ...grpc_go.CallOption) (*PositionUserListResponse, common.ErrorWithAttachment)
}

type ruleClient struct {
	cc *triple.TripleConn
}

type RuleClientImpl struct {
	Create           func(ctx context.Context, in *CreateRequest) (*CreateResponse, error)
	Remove           func(ctx context.Context, in *RemoveRequest) (*RemoveResponse, error)
	Detail           func(ctx context.Context, in *DetailRequest) (*DetailResponse, error)
	Update           func(ctx context.Context, in *UpdateRequest) (*UpdateResponse, error)
	List             func(ctx context.Context, in *ListRequest) (*ListResponse, error)
	MenuList         func(ctx context.Context, in *ListRequest) (*MenuListResponse, error)
	Rules            func(ctx context.Context, in *RulesRequest) (*RulesResponse, error)
	UserInfo         func(ctx context.Context, in *RulesRequest) (*UserInfoResponse, error)
	RuleByUrl        func(ctx context.Context, in *RuleByUrlRequest) (*RuleByUrlResponse, error)
	FindUserRule     func(ctx context.Context, in *FindRuleByUrlRequest) (*RuleByUrlResponse, error)
	FindByUrl        func(ctx context.Context, in *FindByUrlRequest) (*DetailResponse, error)
	FindUsersByUrl   func(ctx context.Context, in *FindByUrlRequest) (*UserInfos, error)
	PositionUserList func(ctx context.Context, in *PositionUserListRequest) (*PositionUserListResponse, error)
}

func (c *RuleClientImpl) GetDubboStub(cc *triple.TripleConn) RuleClient {
	return NewRuleClient(cc)
}

func (c *RuleClientImpl) XXX_InterfaceName() string {
	return "rule.Rule"
}

func NewRuleClient(cc *triple.TripleConn) RuleClient {
	return &ruleClient{cc}
}

func (c *ruleClient) Create(ctx context.Context, in *CreateRequest, opts ...grpc_go.CallOption) (*CreateResponse, common.ErrorWithAttachment) {
	out := new(CreateResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/Create", in, out)
}

func (c *ruleClient) Remove(ctx context.Context, in *RemoveRequest, opts ...grpc_go.CallOption) (*RemoveResponse, common.ErrorWithAttachment) {
	out := new(RemoveResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/Remove", in, out)
}

func (c *ruleClient) Detail(ctx context.Context, in *DetailRequest, opts ...grpc_go.CallOption) (*DetailResponse, common.ErrorWithAttachment) {
	out := new(DetailResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/Detail", in, out)
}

func (c *ruleClient) Update(ctx context.Context, in *UpdateRequest, opts ...grpc_go.CallOption) (*UpdateResponse, common.ErrorWithAttachment) {
	out := new(UpdateResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/Update", in, out)
}

func (c *ruleClient) List(ctx context.Context, in *ListRequest, opts ...grpc_go.CallOption) (*ListResponse, common.ErrorWithAttachment) {
	out := new(ListResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/List", in, out)
}

func (c *ruleClient) MenuList(ctx context.Context, in *ListRequest, opts ...grpc_go.CallOption) (*MenuListResponse, common.ErrorWithAttachment) {
	out := new(MenuListResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/MenuList", in, out)
}

func (c *ruleClient) Rules(ctx context.Context, in *RulesRequest, opts ...grpc_go.CallOption) (*RulesResponse, common.ErrorWithAttachment) {
	out := new(RulesResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/Rules", in, out)
}

func (c *ruleClient) UserInfo(ctx context.Context, in *RulesRequest, opts ...grpc_go.CallOption) (*UserInfoResponse, common.ErrorWithAttachment) {
	out := new(UserInfoResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/UserInfo", in, out)
}

func (c *ruleClient) RuleByUrl(ctx context.Context, in *RuleByUrlRequest, opts ...grpc_go.CallOption) (*RuleByUrlResponse, common.ErrorWithAttachment) {
	out := new(RuleByUrlResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/RuleByUrl", in, out)
}

func (c *ruleClient) FindUserRule(ctx context.Context, in *FindRuleByUrlRequest, opts ...grpc_go.CallOption) (*RuleByUrlResponse, common.ErrorWithAttachment) {
	out := new(RuleByUrlResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/FindUserRule", in, out)
}

func (c *ruleClient) FindByUrl(ctx context.Context, in *FindByUrlRequest, opts ...grpc_go.CallOption) (*DetailResponse, common.ErrorWithAttachment) {
	out := new(DetailResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/FindByUrl", in, out)
}

func (c *ruleClient) FindUsersByUrl(ctx context.Context, in *FindByUrlRequest, opts ...grpc_go.CallOption) (*UserInfos, common.ErrorWithAttachment) {
	out := new(UserInfos)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/FindUsersByUrl", in, out)
}

func (c *ruleClient) PositionUserList(ctx context.Context, in *PositionUserListRequest, opts ...grpc_go.CallOption) (*PositionUserListResponse, common.ErrorWithAttachment) {
	out := new(PositionUserListResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/PositionUserList", in, out)
}

// RuleServer is the server API for Rule service.
// All implementations must embed UnimplementedRuleServer
// for forward compatibility
type RuleServer interface {
	Create(context.Context, *CreateRequest) (*CreateResponse, error)
	Remove(context.Context, *RemoveRequest) (*RemoveResponse, error)
	Detail(context.Context, *DetailRequest) (*DetailResponse, error)
	Update(context.Context, *UpdateRequest) (*UpdateResponse, error)
	List(context.Context, *ListRequest) (*ListResponse, error)
	MenuList(context.Context, *ListRequest) (*MenuListResponse, error)
	Rules(context.Context, *RulesRequest) (*RulesResponse, error)
	UserInfo(context.Context, *RulesRequest) (*UserInfoResponse, error)
	RuleByUrl(context.Context, *RuleByUrlRequest) (*RuleByUrlResponse, error)
	FindUserRule(context.Context, *FindRuleByUrlRequest) (*RuleByUrlResponse, error)
	FindByUrl(context.Context, *FindByUrlRequest) (*DetailResponse, error)
	FindUsersByUrl(context.Context, *FindByUrlRequest) (*UserInfos, error)
	PositionUserList(context.Context, *PositionUserListRequest) (*PositionUserListResponse, error)
	mustEmbedUnimplementedRuleServer()
}

// UnimplementedRuleServer must be embedded to have forward compatible implementations.
type UnimplementedRuleServer struct {
	proxyImpl protocol.Invoker
}

func (UnimplementedRuleServer) Create(context.Context, *CreateRequest) (*CreateResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Create not implemented")
}
func (UnimplementedRuleServer) Remove(context.Context, *RemoveRequest) (*RemoveResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Remove not implemented")
}
func (UnimplementedRuleServer) Detail(context.Context, *DetailRequest) (*DetailResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Detail not implemented")
}
func (UnimplementedRuleServer) Update(context.Context, *UpdateRequest) (*UpdateResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Update not implemented")
}
func (UnimplementedRuleServer) List(context.Context, *ListRequest) (*ListResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method List not implemented")
}
func (UnimplementedRuleServer) MenuList(context.Context, *ListRequest) (*MenuListResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method MenuList not implemented")
}
func (UnimplementedRuleServer) Rules(context.Context, *RulesRequest) (*RulesResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Rules not implemented")
}
func (UnimplementedRuleServer) UserInfo(context.Context, *RulesRequest) (*UserInfoResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UserInfo not implemented")
}
func (UnimplementedRuleServer) RuleByUrl(context.Context, *RuleByUrlRequest) (*RuleByUrlResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RuleByUrl not implemented")
}
func (UnimplementedRuleServer) FindUserRule(context.Context, *FindRuleByUrlRequest) (*RuleByUrlResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FindUserRule not implemented")
}
func (UnimplementedRuleServer) FindByUrl(context.Context, *FindByUrlRequest) (*DetailResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FindByUrl not implemented")
}
func (UnimplementedRuleServer) FindUsersByUrl(context.Context, *FindByUrlRequest) (*UserInfos, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FindUsersByUrl not implemented")
}
func (UnimplementedRuleServer) PositionUserList(context.Context, *PositionUserListRequest) (*PositionUserListResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PositionUserList not implemented")
}
func (s *UnimplementedRuleServer) XXX_SetProxyImpl(impl protocol.Invoker) {
	s.proxyImpl = impl
}

func (s *UnimplementedRuleServer) XXX_GetProxyImpl() protocol.Invoker {
	return s.proxyImpl
}

func (s *UnimplementedRuleServer) XXX_ServiceDesc() *grpc_go.ServiceDesc {
	return &Rule_ServiceDesc
}
func (s *UnimplementedRuleServer) XXX_InterfaceName() string {
	return "rule.Rule"
}

func (UnimplementedRuleServer) mustEmbedUnimplementedRuleServer() {}

// UnsafeRuleServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to RuleServer will
// result in compilation errors.
type UnsafeRuleServer interface {
	mustEmbedUnimplementedRuleServer()
}

func RegisterRuleServer(s grpc_go.ServiceRegistrar, srv RuleServer) {
	s.RegisterService(&Rule_ServiceDesc, srv)
}

func _Rule_Create_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("Create", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Rule_Remove_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(RemoveRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("Remove", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Rule_Detail_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(DetailRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("Detail", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Rule_Update_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("Update", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Rule_List_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("List", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Rule_MenuList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("MenuList", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Rule_Rules_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(RulesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("Rules", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Rule_UserInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(RulesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("UserInfo", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Rule_RuleByUrl_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(RuleByUrlRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("RuleByUrl", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Rule_FindUserRule_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(FindRuleByUrlRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("FindUserRule", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Rule_FindByUrl_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(FindByUrlRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("FindByUrl", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Rule_FindUsersByUrl_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(FindByUrlRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("FindUsersByUrl", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Rule_PositionUserList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(PositionUserListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("PositionUserList", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

// Rule_ServiceDesc is the grpc_go.ServiceDesc for Rule service.
// It's only intended for direct use with grpc_go.RegisterService,
// and not to be introspected or modified (even as a copy)
var Rule_ServiceDesc = grpc_go.ServiceDesc{
	ServiceName: "rule.Rule",
	HandlerType: (*RuleServer)(nil),
	Methods: []grpc_go.MethodDesc{
		{
			MethodName: "Create",
			Handler:    _Rule_Create_Handler,
		},
		{
			MethodName: "Remove",
			Handler:    _Rule_Remove_Handler,
		},
		{
			MethodName: "Detail",
			Handler:    _Rule_Detail_Handler,
		},
		{
			MethodName: "Update",
			Handler:    _Rule_Update_Handler,
		},
		{
			MethodName: "List",
			Handler:    _Rule_List_Handler,
		},
		{
			MethodName: "MenuList",
			Handler:    _Rule_MenuList_Handler,
		},
		{
			MethodName: "Rules",
			Handler:    _Rule_Rules_Handler,
		},
		{
			MethodName: "UserInfo",
			Handler:    _Rule_UserInfo_Handler,
		},
		{
			MethodName: "RuleByUrl",
			Handler:    _Rule_RuleByUrl_Handler,
		},
		{
			MethodName: "FindUserRule",
			Handler:    _Rule_FindUserRule_Handler,
		},
		{
			MethodName: "FindByUrl",
			Handler:    _Rule_FindByUrl_Handler,
		},
		{
			MethodName: "FindUsersByUrl",
			Handler:    _Rule_FindUsersByUrl_Handler,
		},
		{
			MethodName: "PositionUserList",
			Handler:    _Rule_PositionUserList_Handler,
		},
	},
	Streams:  []grpc_go.StreamDesc{},
	Metadata: "api/rule/rule.proto",
}
