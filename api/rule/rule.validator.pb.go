// Code generated by protoc-gen-gogo. DO NOT EDIT.
// source: api/rule/rule.proto

package rule

import (
	fmt "fmt"
	math "math"
	proto "github.com/golang/protobuf/proto"
	_ "github.com/mwitkow/go-proto-validators"
	github_com_mwitkow_go_proto_validators "github.com/mwitkow/go-proto-validators"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

func (this *UserInfo) Validate() error {
	return nil
}
func (this *UserInfos) Validate() error {
	for _, item := range this.List {
		if item != nil {
			if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(item); err != nil {
				return github_com_mwitkow_go_proto_validators.FieldError("List", err)
			}
		}
	}
	return nil
}
func (this *PositionUserListResponse) Validate() error {
	for _, item := range this.Data {
		if item != nil {
			if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(item); err != nil {
				return github_com_mwitkow_go_proto_validators.FieldError("Data", err)
			}
		}
	}
	return nil
}
func (this *PositionUserListRequest) Validate() error {
	return nil
}
func (this *RuleData) Validate() error {
	return nil
}
func (this *RuleField) Validate() error {
	return nil
}
func (this *CreateRequest) Validate() error {
	if this.Title == "" {
		return github_com_mwitkow_go_proto_validators.FieldError("Title", fmt.Errorf(`70013`))
	}
	if this.Method == "" {
		return github_com_mwitkow_go_proto_validators.FieldError("Method", fmt.Errorf(`70015`))
	}
	if this.Domain == "" {
		return github_com_mwitkow_go_proto_validators.FieldError("Domain", fmt.Errorf(`70001`))
	}
	for _, item := range this.RuleData {
		if item != nil {
			if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(item); err != nil {
				return github_com_mwitkow_go_proto_validators.FieldError("RuleData", err)
			}
		}
	}
	for _, item := range this.RuleFields {
		if item != nil {
			if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(item); err != nil {
				return github_com_mwitkow_go_proto_validators.FieldError("RuleFields", err)
			}
		}
	}
	return nil
}
func (this *FindByUrlRequest) Validate() error {
	return nil
}
func (this *FindRuleByUrlRequest) Validate() error {
	return nil
}
func (this *RuleByUrlRequest) Validate() error {
	return nil
}
func (this *RuleByUrlResponse) Validate() error {
	return nil
}
func (this *CreateResponse) Validate() error {
	return nil
}
func (this *RemoveRequest) Validate() error {
	return nil
}
func (this *RemoveResponse) Validate() error {
	return nil
}
func (this *DetailRequest) Validate() error {
	return nil
}
func (this *DetailResponse) Validate() error {
	for _, item := range this.RuleData {
		if item != nil {
			if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(item); err != nil {
				return github_com_mwitkow_go_proto_validators.FieldError("RuleData", err)
			}
		}
	}
	for _, item := range this.Son {
		if item != nil {
			if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(item); err != nil {
				return github_com_mwitkow_go_proto_validators.FieldError("Son", err)
			}
		}
	}
	for _, item := range this.RuleFields {
		if item != nil {
			if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(item); err != nil {
				return github_com_mwitkow_go_proto_validators.FieldError("RuleFields", err)
			}
		}
	}
	return nil
}
func (this *RuleInfo) Validate() error {
	for _, item := range this.Son {
		if item != nil {
			if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(item); err != nil {
				return github_com_mwitkow_go_proto_validators.FieldError("Son", err)
			}
		}
	}
	for _, item := range this.RuleData {
		if item != nil {
			if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(item); err != nil {
				return github_com_mwitkow_go_proto_validators.FieldError("RuleData", err)
			}
		}
	}
	for _, item := range this.PositionRuleFields {
		if item != nil {
			if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(item); err != nil {
				return github_com_mwitkow_go_proto_validators.FieldError("PositionRuleFields", err)
			}
		}
	}
	for _, item := range this.InterfaceList {
		if item != nil {
			if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(item); err != nil {
				return github_com_mwitkow_go_proto_validators.FieldError("InterfaceList", err)
			}
		}
	}
	for _, item := range this.ButtonList {
		if item != nil {
			if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(item); err != nil {
				return github_com_mwitkow_go_proto_validators.FieldError("ButtonList", err)
			}
		}
	}
	return nil
}
func (this *PositionRuleField) Validate() error {
	return nil
}
func (this *Leader) Validate() error {
	return nil
}
func (this *UpdateRequest) Validate() error {
	for _, item := range this.RuleData {
		if item != nil {
			if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(item); err != nil {
				return github_com_mwitkow_go_proto_validators.FieldError("RuleData", err)
			}
		}
	}
	if this.Domain == "" {
		return github_com_mwitkow_go_proto_validators.FieldError("Domain", fmt.Errorf(`70001`))
	}
	for _, item := range this.RuleFields {
		if item != nil {
			if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(item); err != nil {
				return github_com_mwitkow_go_proto_validators.FieldError("RuleFields", err)
			}
		}
	}
	return nil
}
func (this *UpdateResponse) Validate() error {
	return nil
}
func (this *ListRequest) Validate() error {
	return nil
}
func (this *ListResponse) Validate() error {
	for _, item := range this.Data {
		if item != nil {
			if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(item); err != nil {
				return github_com_mwitkow_go_proto_validators.FieldError("Data", err)
			}
		}
	}
	return nil
}
func (this *MenuListResponse) Validate() error {
	for _, item := range this.Data {
		if item != nil {
			if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(item); err != nil {
				return github_com_mwitkow_go_proto_validators.FieldError("Data", err)
			}
		}
	}
	return nil
}
func (this *RulesRequest) Validate() error {
	return nil
}
func (this *RuleResponse) Validate() error {
	if this.RuleDatum != nil {
		if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(this.RuleDatum); err != nil {
			return github_com_mwitkow_go_proto_validators.FieldError("RuleDatum", err)
		}
	}
	return nil
}
func (this *PositionUser) Validate() error {
	return nil
}
func (this *DepartmentLeader) Validate() error {
	return nil
}
func (this *RulesResponse) Validate() error {
	for _, item := range this.AllMenus {
		if item != nil {
			if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(item); err != nil {
				return github_com_mwitkow_go_proto_validators.FieldError("AllMenus", err)
			}
		}
	}
	for _, item := range this.AllRules {
		if item != nil {
			if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(item); err != nil {
				return github_com_mwitkow_go_proto_validators.FieldError("AllRules", err)
			}
		}
	}
	for _, item := range this.AllInterfaceRules {
		if item != nil {
			if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(item); err != nil {
				return github_com_mwitkow_go_proto_validators.FieldError("AllInterfaceRules", err)
			}
		}
	}
	for _, item := range this.MyMenuAuths {
		if item != nil {
			if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(item); err != nil {
				return github_com_mwitkow_go_proto_validators.FieldError("MyMenuAuths", err)
			}
		}
	}
	for _, item := range this.MyRules {
		if item != nil {
			if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(item); err != nil {
				return github_com_mwitkow_go_proto_validators.FieldError("MyRules", err)
			}
		}
	}
	for _, item := range this.MyButtonAuths {
		if item != nil {
			if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(item); err != nil {
				return github_com_mwitkow_go_proto_validators.FieldError("MyButtonAuths", err)
			}
		}
	}
	for _, item := range this.MyInterFaceAuths {
		if item != nil {
			if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(item); err != nil {
				return github_com_mwitkow_go_proto_validators.FieldError("MyInterFaceAuths", err)
			}
		}
	}
	for _, item := range this.PositionUsers {
		if item != nil {
			if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(item); err != nil {
				return github_com_mwitkow_go_proto_validators.FieldError("PositionUsers", err)
			}
		}
	}
	for _, item := range this.DepartmentLeaders {
		if item != nil {
			if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(item); err != nil {
				return github_com_mwitkow_go_proto_validators.FieldError("DepartmentLeaders", err)
			}
		}
	}
	for _, item := range this.AllTreeRule {
		if item != nil {
			if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(item); err != nil {
				return github_com_mwitkow_go_proto_validators.FieldError("AllTreeRule", err)
			}
		}
	}
	for _, item := range this.MyTreeRule {
		if item != nil {
			if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(item); err != nil {
				return github_com_mwitkow_go_proto_validators.FieldError("MyTreeRule", err)
			}
		}
	}
	for _, item := range this.MySecondRule {
		if item != nil {
			if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(item); err != nil {
				return github_com_mwitkow_go_proto_validators.FieldError("MySecondRule", err)
			}
		}
	}
	return nil
}
func (this *UserInfoResponse) Validate() error {
	for _, item := range this.PositionUsers {
		if item != nil {
			if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(item); err != nil {
				return github_com_mwitkow_go_proto_validators.FieldError("PositionUsers", err)
			}
		}
	}
	for _, item := range this.DepartmentLeaders {
		if item != nil {
			if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(item); err != nil {
				return github_com_mwitkow_go_proto_validators.FieldError("DepartmentLeaders", err)
			}
		}
	}
	return nil
}
