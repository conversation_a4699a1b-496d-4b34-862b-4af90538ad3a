// Code generated by protoc-gen-gogo. DO NOT EDIT.
// source: api/shopbrand/shopbrand.proto

//默认的包名

package shopbrand

import (
	fmt "fmt"
	math "math"
	proto "github.com/golang/protobuf/proto"
	github_com_mwitkow_go_proto_validators "github.com/mwitkow/go-proto-validators"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

func (this *BrandInfo) Validate() error {
	return nil
}
func (this *CreateBrandreq) Validate() error {
	return nil
}
func (this *CreateBrandres) Validate() error {
	return nil
}
func (this *BrandListreq) Validate() error {
	return nil
}
func (this *BrandListres) Validate() error {
	for _, item := range this.Data {
		if item != nil {
			if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(item); err != nil {
				return github_com_mwitkow_go_proto_validators.FieldError("Data", err)
			}
		}
	}
	return nil
}
func (this *BrandListres_Info) Validate() error {
	return nil
}
func (this *BrandInforeq) Validate() error {
	return nil
}
func (this *BrandInfores) Validate() error {
	return nil
}
func (this *DeleteBrandreq) Validate() error {
	return nil
}
func (this *DeleteBrandres) Validate() error {
	return nil
}
func (this *UpdateBrandreq) Validate() error {
	return nil
}
func (this *UpdateBrandres) Validate() error {
	return nil
}
func (this *AllBrandreq) Validate() error {
	return nil
}
func (this *AllBrandres) Validate() error {
	for _, item := range this.Data {
		if item != nil {
			if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(item); err != nil {
				return github_com_mwitkow_go_proto_validators.FieldError("Data", err)
			}
		}
	}
	return nil
}
func (this *AllBrandres_Info) Validate() error {
	return nil
}
func (this *GetAllLanguageInfoReq) Validate() error {
	return nil
}
func (this *LanguageInfo) Validate() error {
	return nil
}
func (this *GetAllLanguageInfoRep) Validate() error {
	for _, item := range this.LanguageInfo {
		if item != nil {
			if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(item); err != nil {
				return github_com_mwitkow_go_proto_validators.FieldError("LanguageInfo", err)
			}
		}
	}
	return nil
}
func (this *UpdateLanguageInfoReq) Validate() error {
	for _, item := range this.LanguageInfo {
		if item != nil {
			if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(item); err != nil {
				return github_com_mwitkow_go_proto_validators.FieldError("LanguageInfo", err)
			}
		}
	}
	return nil
}
func (this *UpdateLanguageInfoRep) Validate() error {
	return nil
}
func (this *GetLanguageInfoByNumReq) Validate() error {
	return nil
}
func (this *GetLanguageInfoByNumRep) Validate() error {
	return nil
}
func (this *GetLanguageInfoByLanguageReq) Validate() error {
	return nil
}
func (this *GetLanguageInfoByLanguageRep) Validate() error {
	return nil
}
func (this *CodeCommit) Validate() error {
	return nil
}
func (this *CodeCommitReq) Validate() error {
	for _, item := range this.CodeCommit {
		if item != nil {
			if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(item); err != nil {
				return github_com_mwitkow_go_proto_validators.FieldError("CodeCommit", err)
			}
		}
	}
	return nil
}
func (this *CodeCommitResp) Validate() error {
	return nil
}
