//proto版本
syntax = "proto3";

//导入包暂时没有

//默认的包名
package shopbrand;

//在golang中的包名
option go_package = "./;shopbrand";
// import "pb/validator.proto";
//定义shopbrand服务方法，把brand保存到数据库中
service Brand{
    rpc CreateBrand (CreateBrandreq) returns (CreateBrandres){} //创建品牌方
    rpc BrandList (BrandListreq) returns (BrandListres){} //品牌方列表
    rpc BrandInfo (BrandInforeq) returns (BrandInfores){} //品牌方详细信息
    rpc DeleteBrand (DeleteBrandreq) returns(DeleteBrandres){} //删除品牌方
    rpc UpdateBrand (UpdateBrandreq) returns(UpdateBrandres){} //更新品牌方数据
    rpc AllBrand (AllBrandreq) returns (AllBrandres){}
    rpc GetAllLanguageInfo (GetAllLanguageInfoReq) returns (GetAllLanguageInfoRep){} // 获取所有的语言
    rpc UpdateLanguageInfo (UpdateLanguageInfoReq) returns (UpdateLanguageInfoRep){} // 更新语言信息
    rpc GetLanguageInfoByNum (GetLanguageInfoByNumReq) returns (GetLanguageInfoByNumRep){} // 根据语言编号获取语言信息
    rpc GetLanguageInfoByLanguage (GetLanguageInfoByLanguageReq) returns (GetLanguageInfoByLanguageRep){} // 根据语言缩写获取语言编号
    rpc CodeCommit(CodeCommitReq) returns (CodeCommitResp) {} // gitea webhook 接收数据
    }

message BrandInfo {
    int32  num = 1 ; //语言编号
    string brandName = 2 ;//公司名
    string logo = 3 ;//公司logo
}

//新建品牌方的结构体
message CreateBrandreq {
    string linkman = 1 ; //品牌方联系人姓名
    string linkmanContractway = 2 ;//品牌方联系人联系方式
    string brandDirector = 3 ;//品牌方公司董事
    string legalMan = 4 ;//品牌方公司法人
    string brandNumber = 5 ;//品牌方电话
    string typeOfService = 6 ;//品牌方公司业务类型
    string introduction = 7 ; //品牌方公司简介
    repeated string proof = 8;//资格凭证
    string date = 9 ; //品牌方入驻时间
    string address = 10 ; //区块链hash
    string mnemonic = 11 ; //助记词
    string brandName = 13 ;//公司名
    string logo = 14 ;//公司logo
    string brandUid = 15 ;//品牌方表uid
    string lang = 16 ;//语言
}

message CreateBrandres {
    string msg = 1 ;
    string brandUid = 2 ; //品牌方唯一标识
}

//品牌方列表结构体
message BrandListreq {
    string keyword                = 1 ;
    int32  page                    = 2 ;
    int32  pageSize                = 3 ;
    repeated string brandUid = 5 ; // 品牌uid列表
    int32 status = 6 ;// 1:正常查询 2：获取包括软删除的数据
    string lang = 4 ;// 1:正常查询 2：获取包括软删除的数据
}

message BrandListres {
    message Info {
        string brandUid = 1 ;
        string brandName = 2 ;
        string logo = 3 ;
        string address = 4 ;
        string mnemonic = 5 ;
        int32  status = 6 ;
    }
    repeated Info data = 1 ;
    int32  count = 2 ;
    int32  page = 3 ;
    int32  pageSize = 4 ;
    string msg = 5 ;
}

//品牌方详情结构体
message BrandInforeq {
    string brandUid = 1;//品牌方的唯一标识
    string  lang = 2 ; //语言编号

}

message BrandInfores {
    string linkman = 1 ; //品牌方联系人姓名
    string linkmanContractway = 2 ;//品牌方联系人联系方式
    string brandDirector = 3 ;//品牌方公司董事
    string legalMan = 4 ;//品牌方公司法人
    string brandNumber = 5 ;//品牌方电话
    string typeOfService = 6 ;//品牌方公司业务类型
    string introduction = 7 ; //品牌方公司简介
    string logo = 8 ;//公司logo
    string brandName = 9 ;//公司名
    repeated string proof = 10;//资格凭证
    string lang = 11 ; //语言
    string date = 12 ; //品牌方入驻时间
    string brandUid = 13 ; //品牌方入驻时间
    string msg = 14 ;
    string address = 15 ;
    string mnemonic = 16 ;
    int32  status = 17 ;
}

//删除品牌方结构体
message DeleteBrandreq {
    string brandUid = 1 ;//品牌方的唯一标识
}

message DeleteBrandres {
    string msg = 1 ;
}

//更新品牌方数据结构体
message UpdateBrandreq {
    string brandUid = 1 ;
    string lang = 2 ;
    string linkman = 3 ; //品牌方联系人姓名
    string linkmanContractway = 4 ;//品牌方联系人联系方式
    string brandDirector = 5 ;//品牌方公司董事
    string legalMan = 6 ;//品牌方公司法人
    string brandNumber = 7 ;//品牌方电话
    string typeOfService = 8 ;//品牌方公司业务类型
    string introduction = 9 ; //品牌方公司简介
    string logo = 10 ;//公司logo
    string brandName = 11 ;//公司名
    repeated string proof = 12;//资格凭证
    string date = 13 ; //品牌方入驻时间
}

message UpdateBrandres {
    string msg = 1 ;
}

message AllBrandreq {
    int32 page = 1 ; //页数
    int32 pageSize = 2 ;
}

message AllBrandres {
    message Info{
        string brandUid = 1 ;
        string brandName = 2 ;//公司名
    }
    repeated Info data = 1 ;
    string msg = 5 ;
}

message GetAllLanguageInfoReq {
    string brandUid = 1 ;
}

message LanguageInfo {
    string lang = 2 ;
    int32  status = 3 ;
    string languageTranslate = 4 ;
}

message GetAllLanguageInfoRep {
    repeated LanguageInfo languageInfo = 1 ;
    string msg = 2 ;
 }

 message UpdateLanguageInfoReq {
     repeated LanguageInfo languageInfo = 1 ;
 }

 message UpdateLanguageInfoRep {
     string msg = 1 ;
 }

message GetLanguageInfoByNumReq {
    string lang = 2 ;
}

message GetLanguageInfoByNumRep {
    string lang = 1;
    string msg = 3 ;
}

message GetLanguageInfoByLanguageReq {
    string lang = 1 ;
}

message GetLanguageInfoByLanguageRep {
    string lang = 2 ;
    string msg = 3 ;
}

message CodeCommit {
    int32 commitID = 1  ; // 提交记录id
    string committedAt = 2 ; // 提交时间（YYYY-MM-DD HH:MM:SS）
    string committerName = 3 ; // 提交人姓名
    string committerEmail = 4 ; // 提交人邮箱
    string commitMessage = 5 ; // 提交信息
    string ownerName = 6 ; // 仓库所属用户或者组织名称
    string repositoryName = 7 ; // 代码库名称
    string branchName = 8 ; // 分支名称
    string commitSha = 9 ; // 提交标识
    string commitUrl = 10 ; // 提交页面
    int32 totalChangeCount = 11 ; // 代码总变动数
    int32 addCount = 12 ; // 新增数
    int32 deleteCount = 13 ; // 删除数
}

message CodeCommitReq {
    repeated CodeCommit codeCommit = 1;
}

message CodeCommitResp {
    string msg = 1;
}