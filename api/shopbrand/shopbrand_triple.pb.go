// Code generated by protoc-gen-go-triple. DO NOT EDIT.
// versions:
// - protoc-gen-go-triple v1.0.8
// - protoc             v3.21.12
// source: api/shopbrand/shopbrand.proto

package shopbrand

import (
	context "context"
	protocol "dubbo.apache.org/dubbo-go/v3/protocol"
	dubbo3 "dubbo.apache.org/dubbo-go/v3/protocol/dubbo3"
	invocation "dubbo.apache.org/dubbo-go/v3/protocol/invocation"
	grpc_go "github.com/dubbogo/grpc-go"
	codes "github.com/dubbogo/grpc-go/codes"
	metadata "github.com/dubbogo/grpc-go/metadata"
	status "github.com/dubbogo/grpc-go/status"
	common "github.com/dubbogo/triple/pkg/common"
	constant "github.com/dubbogo/triple/pkg/common/constant"
	triple "github.com/dubbogo/triple/pkg/triple"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc_go.SupportPackageIsVersion7

// BrandClient is the client API for Brand service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type BrandClient interface {
	CreateBrand(ctx context.Context, in *CreateBrandreq, opts ...grpc_go.CallOption) (*CreateBrandres, common.ErrorWithAttachment)
	BrandList(ctx context.Context, in *BrandListreq, opts ...grpc_go.CallOption) (*BrandListres, common.ErrorWithAttachment)
	BrandInfo(ctx context.Context, in *BrandInforeq, opts ...grpc_go.CallOption) (*BrandInfores, common.ErrorWithAttachment)
	DeleteBrand(ctx context.Context, in *DeleteBrandreq, opts ...grpc_go.CallOption) (*DeleteBrandres, common.ErrorWithAttachment)
	UpdateBrand(ctx context.Context, in *UpdateBrandreq, opts ...grpc_go.CallOption) (*UpdateBrandres, common.ErrorWithAttachment)
	AllBrand(ctx context.Context, in *AllBrandreq, opts ...grpc_go.CallOption) (*AllBrandres, common.ErrorWithAttachment)
	GetAllLanguageInfo(ctx context.Context, in *GetAllLanguageInfoReq, opts ...grpc_go.CallOption) (*GetAllLanguageInfoRep, common.ErrorWithAttachment)
	UpdateLanguageInfo(ctx context.Context, in *UpdateLanguageInfoReq, opts ...grpc_go.CallOption) (*UpdateLanguageInfoRep, common.ErrorWithAttachment)
	GetLanguageInfoByNum(ctx context.Context, in *GetLanguageInfoByNumReq, opts ...grpc_go.CallOption) (*GetLanguageInfoByNumRep, common.ErrorWithAttachment)
	GetLanguageInfoByLanguage(ctx context.Context, in *GetLanguageInfoByLanguageReq, opts ...grpc_go.CallOption) (*GetLanguageInfoByLanguageRep, common.ErrorWithAttachment)
	CodeCommit(ctx context.Context, in *CodeCommitReq, opts ...grpc_go.CallOption) (*CodeCommitResp, common.ErrorWithAttachment)
}

type brandClient struct {
	cc *triple.TripleConn
}

type BrandClientImpl struct {
	CreateBrand               func(ctx context.Context, in *CreateBrandreq) (*CreateBrandres, error)
	BrandList                 func(ctx context.Context, in *BrandListreq) (*BrandListres, error)
	BrandInfo                 func(ctx context.Context, in *BrandInforeq) (*BrandInfores, error)
	DeleteBrand               func(ctx context.Context, in *DeleteBrandreq) (*DeleteBrandres, error)
	UpdateBrand               func(ctx context.Context, in *UpdateBrandreq) (*UpdateBrandres, error)
	AllBrand                  func(ctx context.Context, in *AllBrandreq) (*AllBrandres, error)
	GetAllLanguageInfo        func(ctx context.Context, in *GetAllLanguageInfoReq) (*GetAllLanguageInfoRep, error)
	UpdateLanguageInfo        func(ctx context.Context, in *UpdateLanguageInfoReq) (*UpdateLanguageInfoRep, error)
	GetLanguageInfoByNum      func(ctx context.Context, in *GetLanguageInfoByNumReq) (*GetLanguageInfoByNumRep, error)
	GetLanguageInfoByLanguage func(ctx context.Context, in *GetLanguageInfoByLanguageReq) (*GetLanguageInfoByLanguageRep, error)
	CodeCommit                func(ctx context.Context, in *CodeCommitReq) (*CodeCommitResp, error)
}

func (c *BrandClientImpl) GetDubboStub(cc *triple.TripleConn) BrandClient {
	return NewBrandClient(cc)
}

func (c *BrandClientImpl) XXX_InterfaceName() string {
	return "shopbrand.Brand"
}

func NewBrandClient(cc *triple.TripleConn) BrandClient {
	return &brandClient{cc}
}

func (c *brandClient) CreateBrand(ctx context.Context, in *CreateBrandreq, opts ...grpc_go.CallOption) (*CreateBrandres, common.ErrorWithAttachment) {
	out := new(CreateBrandres)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/CreateBrand", in, out)
}

func (c *brandClient) BrandList(ctx context.Context, in *BrandListreq, opts ...grpc_go.CallOption) (*BrandListres, common.ErrorWithAttachment) {
	out := new(BrandListres)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/BrandList", in, out)
}

func (c *brandClient) BrandInfo(ctx context.Context, in *BrandInforeq, opts ...grpc_go.CallOption) (*BrandInfores, common.ErrorWithAttachment) {
	out := new(BrandInfores)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/BrandInfo", in, out)
}

func (c *brandClient) DeleteBrand(ctx context.Context, in *DeleteBrandreq, opts ...grpc_go.CallOption) (*DeleteBrandres, common.ErrorWithAttachment) {
	out := new(DeleteBrandres)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/DeleteBrand", in, out)
}

func (c *brandClient) UpdateBrand(ctx context.Context, in *UpdateBrandreq, opts ...grpc_go.CallOption) (*UpdateBrandres, common.ErrorWithAttachment) {
	out := new(UpdateBrandres)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/UpdateBrand", in, out)
}

func (c *brandClient) AllBrand(ctx context.Context, in *AllBrandreq, opts ...grpc_go.CallOption) (*AllBrandres, common.ErrorWithAttachment) {
	out := new(AllBrandres)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/AllBrand", in, out)
}

func (c *brandClient) GetAllLanguageInfo(ctx context.Context, in *GetAllLanguageInfoReq, opts ...grpc_go.CallOption) (*GetAllLanguageInfoRep, common.ErrorWithAttachment) {
	out := new(GetAllLanguageInfoRep)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/GetAllLanguageInfo", in, out)
}

func (c *brandClient) UpdateLanguageInfo(ctx context.Context, in *UpdateLanguageInfoReq, opts ...grpc_go.CallOption) (*UpdateLanguageInfoRep, common.ErrorWithAttachment) {
	out := new(UpdateLanguageInfoRep)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/UpdateLanguageInfo", in, out)
}

func (c *brandClient) GetLanguageInfoByNum(ctx context.Context, in *GetLanguageInfoByNumReq, opts ...grpc_go.CallOption) (*GetLanguageInfoByNumRep, common.ErrorWithAttachment) {
	out := new(GetLanguageInfoByNumRep)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/GetLanguageInfoByNum", in, out)
}

func (c *brandClient) GetLanguageInfoByLanguage(ctx context.Context, in *GetLanguageInfoByLanguageReq, opts ...grpc_go.CallOption) (*GetLanguageInfoByLanguageRep, common.ErrorWithAttachment) {
	out := new(GetLanguageInfoByLanguageRep)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/GetLanguageInfoByLanguage", in, out)
}

func (c *brandClient) CodeCommit(ctx context.Context, in *CodeCommitReq, opts ...grpc_go.CallOption) (*CodeCommitResp, common.ErrorWithAttachment) {
	out := new(CodeCommitResp)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/CodeCommit", in, out)
}

// BrandServer is the server API for Brand service.
// All implementations must embed UnimplementedBrandServer
// for forward compatibility
type BrandServer interface {
	CreateBrand(context.Context, *CreateBrandreq) (*CreateBrandres, error)
	BrandList(context.Context, *BrandListreq) (*BrandListres, error)
	BrandInfo(context.Context, *BrandInforeq) (*BrandInfores, error)
	DeleteBrand(context.Context, *DeleteBrandreq) (*DeleteBrandres, error)
	UpdateBrand(context.Context, *UpdateBrandreq) (*UpdateBrandres, error)
	AllBrand(context.Context, *AllBrandreq) (*AllBrandres, error)
	GetAllLanguageInfo(context.Context, *GetAllLanguageInfoReq) (*GetAllLanguageInfoRep, error)
	UpdateLanguageInfo(context.Context, *UpdateLanguageInfoReq) (*UpdateLanguageInfoRep, error)
	GetLanguageInfoByNum(context.Context, *GetLanguageInfoByNumReq) (*GetLanguageInfoByNumRep, error)
	GetLanguageInfoByLanguage(context.Context, *GetLanguageInfoByLanguageReq) (*GetLanguageInfoByLanguageRep, error)
	CodeCommit(context.Context, *CodeCommitReq) (*CodeCommitResp, error)
	mustEmbedUnimplementedBrandServer()
}

// UnimplementedBrandServer must be embedded to have forward compatible implementations.
type UnimplementedBrandServer struct {
	proxyImpl protocol.Invoker
}

func (UnimplementedBrandServer) CreateBrand(context.Context, *CreateBrandreq) (*CreateBrandres, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateBrand not implemented")
}
func (UnimplementedBrandServer) BrandList(context.Context, *BrandListreq) (*BrandListres, error) {
	return nil, status.Errorf(codes.Unimplemented, "method BrandList not implemented")
}
func (UnimplementedBrandServer) BrandInfo(context.Context, *BrandInforeq) (*BrandInfores, error) {
	return nil, status.Errorf(codes.Unimplemented, "method BrandInfo not implemented")
}
func (UnimplementedBrandServer) DeleteBrand(context.Context, *DeleteBrandreq) (*DeleteBrandres, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteBrand not implemented")
}
func (UnimplementedBrandServer) UpdateBrand(context.Context, *UpdateBrandreq) (*UpdateBrandres, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateBrand not implemented")
}
func (UnimplementedBrandServer) AllBrand(context.Context, *AllBrandreq) (*AllBrandres, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AllBrand not implemented")
}
func (UnimplementedBrandServer) GetAllLanguageInfo(context.Context, *GetAllLanguageInfoReq) (*GetAllLanguageInfoRep, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetAllLanguageInfo not implemented")
}
func (UnimplementedBrandServer) UpdateLanguageInfo(context.Context, *UpdateLanguageInfoReq) (*UpdateLanguageInfoRep, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateLanguageInfo not implemented")
}
func (UnimplementedBrandServer) GetLanguageInfoByNum(context.Context, *GetLanguageInfoByNumReq) (*GetLanguageInfoByNumRep, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetLanguageInfoByNum not implemented")
}
func (UnimplementedBrandServer) GetLanguageInfoByLanguage(context.Context, *GetLanguageInfoByLanguageReq) (*GetLanguageInfoByLanguageRep, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetLanguageInfoByLanguage not implemented")
}
func (UnimplementedBrandServer) CodeCommit(context.Context, *CodeCommitReq) (*CodeCommitResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CodeCommit not implemented")
}
func (s *UnimplementedBrandServer) XXX_SetProxyImpl(impl protocol.Invoker) {
	s.proxyImpl = impl
}

func (s *UnimplementedBrandServer) XXX_GetProxyImpl() protocol.Invoker {
	return s.proxyImpl
}

func (s *UnimplementedBrandServer) XXX_ServiceDesc() *grpc_go.ServiceDesc {
	return &Brand_ServiceDesc
}
func (s *UnimplementedBrandServer) XXX_InterfaceName() string {
	return "shopbrand.Brand"
}

func (UnimplementedBrandServer) mustEmbedUnimplementedBrandServer() {}

// UnsafeBrandServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to BrandServer will
// result in compilation errors.
type UnsafeBrandServer interface {
	mustEmbedUnimplementedBrandServer()
}

func RegisterBrandServer(s grpc_go.ServiceRegistrar, srv BrandServer) {
	s.RegisterService(&Brand_ServiceDesc, srv)
}

func _Brand_CreateBrand_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateBrandreq)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("CreateBrand", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Brand_BrandList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(BrandListreq)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("BrandList", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Brand_BrandInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(BrandInforeq)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("BrandInfo", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Brand_DeleteBrand_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteBrandreq)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("DeleteBrand", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Brand_UpdateBrand_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateBrandreq)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("UpdateBrand", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Brand_AllBrand_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(AllBrandreq)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("AllBrand", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Brand_GetAllLanguageInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAllLanguageInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("GetAllLanguageInfo", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Brand_UpdateLanguageInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateLanguageInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("UpdateLanguageInfo", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Brand_GetLanguageInfoByNum_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetLanguageInfoByNumReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("GetLanguageInfoByNum", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Brand_GetLanguageInfoByLanguage_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetLanguageInfoByLanguageReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("GetLanguageInfoByLanguage", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Brand_CodeCommit_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(CodeCommitReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("CodeCommit", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

// Brand_ServiceDesc is the grpc_go.ServiceDesc for Brand service.
// It's only intended for direct use with grpc_go.RegisterService,
// and not to be introspected or modified (even as a copy)
var Brand_ServiceDesc = grpc_go.ServiceDesc{
	ServiceName: "shopbrand.Brand",
	HandlerType: (*BrandServer)(nil),
	Methods: []grpc_go.MethodDesc{
		{
			MethodName: "CreateBrand",
			Handler:    _Brand_CreateBrand_Handler,
		},
		{
			MethodName: "BrandList",
			Handler:    _Brand_BrandList_Handler,
		},
		{
			MethodName: "BrandInfo",
			Handler:    _Brand_BrandInfo_Handler,
		},
		{
			MethodName: "DeleteBrand",
			Handler:    _Brand_DeleteBrand_Handler,
		},
		{
			MethodName: "UpdateBrand",
			Handler:    _Brand_UpdateBrand_Handler,
		},
		{
			MethodName: "AllBrand",
			Handler:    _Brand_AllBrand_Handler,
		},
		{
			MethodName: "GetAllLanguageInfo",
			Handler:    _Brand_GetAllLanguageInfo_Handler,
		},
		{
			MethodName: "UpdateLanguageInfo",
			Handler:    _Brand_UpdateLanguageInfo_Handler,
		},
		{
			MethodName: "GetLanguageInfoByNum",
			Handler:    _Brand_GetLanguageInfoByNum_Handler,
		},
		{
			MethodName: "GetLanguageInfoByLanguage",
			Handler:    _Brand_GetLanguageInfoByLanguage_Handler,
		},
		{
			MethodName: "CodeCommit",
			Handler:    _Brand_CodeCommit_Handler,
		},
	},
	Streams:  []grpc_go.StreamDesc{},
	Metadata: "api/shopbrand/shopbrand.proto",
}
