//proto版本

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.29.1
// 	protoc        v3.21.12
// source: api/shopbrand/shopbrand.proto

//导入包暂时没有

//默认的包名

package shopbrand

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type BrandInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Num       int32  `protobuf:"varint,1,opt,name=num,proto3" json:"num,omitempty"`            //语言编号
	BrandName string `protobuf:"bytes,2,opt,name=brandName,proto3" json:"brandName,omitempty"` //公司名
	Logo      string `protobuf:"bytes,3,opt,name=logo,proto3" json:"logo,omitempty"`           //公司logo
}

func (x *BrandInfo) Reset() {
	*x = BrandInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_shopbrand_shopbrand_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BrandInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BrandInfo) ProtoMessage() {}

func (x *BrandInfo) ProtoReflect() protoreflect.Message {
	mi := &file_api_shopbrand_shopbrand_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BrandInfo.ProtoReflect.Descriptor instead.
func (*BrandInfo) Descriptor() ([]byte, []int) {
	return file_api_shopbrand_shopbrand_proto_rawDescGZIP(), []int{0}
}

func (x *BrandInfo) GetNum() int32 {
	if x != nil {
		return x.Num
	}
	return 0
}

func (x *BrandInfo) GetBrandName() string {
	if x != nil {
		return x.BrandName
	}
	return ""
}

func (x *BrandInfo) GetLogo() string {
	if x != nil {
		return x.Logo
	}
	return ""
}

//新建品牌方的结构体
type CreateBrandreq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Linkman            string   `protobuf:"bytes,1,opt,name=linkman,proto3" json:"linkman,omitempty"`                       //品牌方联系人姓名
	LinkmanContractway string   `protobuf:"bytes,2,opt,name=linkmanContractway,proto3" json:"linkmanContractway,omitempty"` //品牌方联系人联系方式
	BrandDirector      string   `protobuf:"bytes,3,opt,name=brandDirector,proto3" json:"brandDirector,omitempty"`           //品牌方公司董事
	LegalMan           string   `protobuf:"bytes,4,opt,name=legalMan,proto3" json:"legalMan,omitempty"`                     //品牌方公司法人
	BrandNumber        string   `protobuf:"bytes,5,opt,name=brandNumber,proto3" json:"brandNumber,omitempty"`               //品牌方电话
	TypeOfService      string   `protobuf:"bytes,6,opt,name=typeOfService,proto3" json:"typeOfService,omitempty"`           //品牌方公司业务类型
	Introduction       string   `protobuf:"bytes,7,opt,name=introduction,proto3" json:"introduction,omitempty"`             //品牌方公司简介
	Proof              []string `protobuf:"bytes,8,rep,name=proof,proto3" json:"proof,omitempty"`                           //资格凭证
	Date               string   `protobuf:"bytes,9,opt,name=date,proto3" json:"date,omitempty"`                             //品牌方入驻时间
	Address            string   `protobuf:"bytes,10,opt,name=address,proto3" json:"address,omitempty"`                      //区块链hash
	Mnemonic           string   `protobuf:"bytes,11,opt,name=mnemonic,proto3" json:"mnemonic,omitempty"`                    //助记词
	BrandName          string   `protobuf:"bytes,13,opt,name=brandName,proto3" json:"brandName,omitempty"`                  //公司名
	Logo               string   `protobuf:"bytes,14,opt,name=logo,proto3" json:"logo,omitempty"`                            //公司logo
	BrandUid           string   `protobuf:"bytes,15,opt,name=brandUid,proto3" json:"brandUid,omitempty"`                    //品牌方表uid
	Lang               string   `protobuf:"bytes,16,opt,name=lang,proto3" json:"lang,omitempty"`                            //语言
}

func (x *CreateBrandreq) Reset() {
	*x = CreateBrandreq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_shopbrand_shopbrand_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateBrandreq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateBrandreq) ProtoMessage() {}

func (x *CreateBrandreq) ProtoReflect() protoreflect.Message {
	mi := &file_api_shopbrand_shopbrand_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateBrandreq.ProtoReflect.Descriptor instead.
func (*CreateBrandreq) Descriptor() ([]byte, []int) {
	return file_api_shopbrand_shopbrand_proto_rawDescGZIP(), []int{1}
}

func (x *CreateBrandreq) GetLinkman() string {
	if x != nil {
		return x.Linkman
	}
	return ""
}

func (x *CreateBrandreq) GetLinkmanContractway() string {
	if x != nil {
		return x.LinkmanContractway
	}
	return ""
}

func (x *CreateBrandreq) GetBrandDirector() string {
	if x != nil {
		return x.BrandDirector
	}
	return ""
}

func (x *CreateBrandreq) GetLegalMan() string {
	if x != nil {
		return x.LegalMan
	}
	return ""
}

func (x *CreateBrandreq) GetBrandNumber() string {
	if x != nil {
		return x.BrandNumber
	}
	return ""
}

func (x *CreateBrandreq) GetTypeOfService() string {
	if x != nil {
		return x.TypeOfService
	}
	return ""
}

func (x *CreateBrandreq) GetIntroduction() string {
	if x != nil {
		return x.Introduction
	}
	return ""
}

func (x *CreateBrandreq) GetProof() []string {
	if x != nil {
		return x.Proof
	}
	return nil
}

func (x *CreateBrandreq) GetDate() string {
	if x != nil {
		return x.Date
	}
	return ""
}

func (x *CreateBrandreq) GetAddress() string {
	if x != nil {
		return x.Address
	}
	return ""
}

func (x *CreateBrandreq) GetMnemonic() string {
	if x != nil {
		return x.Mnemonic
	}
	return ""
}

func (x *CreateBrandreq) GetBrandName() string {
	if x != nil {
		return x.BrandName
	}
	return ""
}

func (x *CreateBrandreq) GetLogo() string {
	if x != nil {
		return x.Logo
	}
	return ""
}

func (x *CreateBrandreq) GetBrandUid() string {
	if x != nil {
		return x.BrandUid
	}
	return ""
}

func (x *CreateBrandreq) GetLang() string {
	if x != nil {
		return x.Lang
	}
	return ""
}

type CreateBrandres struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Msg      string `protobuf:"bytes,1,opt,name=msg,proto3" json:"msg,omitempty"`
	BrandUid string `protobuf:"bytes,2,opt,name=brandUid,proto3" json:"brandUid,omitempty"` //品牌方唯一标识
}

func (x *CreateBrandres) Reset() {
	*x = CreateBrandres{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_shopbrand_shopbrand_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateBrandres) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateBrandres) ProtoMessage() {}

func (x *CreateBrandres) ProtoReflect() protoreflect.Message {
	mi := &file_api_shopbrand_shopbrand_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateBrandres.ProtoReflect.Descriptor instead.
func (*CreateBrandres) Descriptor() ([]byte, []int) {
	return file_api_shopbrand_shopbrand_proto_rawDescGZIP(), []int{2}
}

func (x *CreateBrandres) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *CreateBrandres) GetBrandUid() string {
	if x != nil {
		return x.BrandUid
	}
	return ""
}

//品牌方列表结构体
type BrandListreq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Keyword  string   `protobuf:"bytes,1,opt,name=keyword,proto3" json:"keyword,omitempty"`
	Page     int32    `protobuf:"varint,2,opt,name=page,proto3" json:"page,omitempty"`
	PageSize int32    `protobuf:"varint,3,opt,name=pageSize,proto3" json:"pageSize,omitempty"`
	BrandUid []string `protobuf:"bytes,5,rep,name=brandUid,proto3" json:"brandUid,omitempty"` // 品牌uid列表
	Status   int32    `protobuf:"varint,6,opt,name=status,proto3" json:"status,omitempty"`    // 1:正常查询 2：获取包括软删除的数据
	Lang     string   `protobuf:"bytes,4,opt,name=lang,proto3" json:"lang,omitempty"`         // 1:正常查询 2：获取包括软删除的数据
}

func (x *BrandListreq) Reset() {
	*x = BrandListreq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_shopbrand_shopbrand_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BrandListreq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BrandListreq) ProtoMessage() {}

func (x *BrandListreq) ProtoReflect() protoreflect.Message {
	mi := &file_api_shopbrand_shopbrand_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BrandListreq.ProtoReflect.Descriptor instead.
func (*BrandListreq) Descriptor() ([]byte, []int) {
	return file_api_shopbrand_shopbrand_proto_rawDescGZIP(), []int{3}
}

func (x *BrandListreq) GetKeyword() string {
	if x != nil {
		return x.Keyword
	}
	return ""
}

func (x *BrandListreq) GetPage() int32 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *BrandListreq) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *BrandListreq) GetBrandUid() []string {
	if x != nil {
		return x.BrandUid
	}
	return nil
}

func (x *BrandListreq) GetStatus() int32 {
	if x != nil {
		return x.Status
	}
	return 0
}

func (x *BrandListreq) GetLang() string {
	if x != nil {
		return x.Lang
	}
	return ""
}

type BrandListres struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Data     []*BrandListres_Info `protobuf:"bytes,1,rep,name=data,proto3" json:"data,omitempty"`
	Count    int32                `protobuf:"varint,2,opt,name=count,proto3" json:"count,omitempty"`
	Page     int32                `protobuf:"varint,3,opt,name=page,proto3" json:"page,omitempty"`
	PageSize int32                `protobuf:"varint,4,opt,name=pageSize,proto3" json:"pageSize,omitempty"`
	Msg      string               `protobuf:"bytes,5,opt,name=msg,proto3" json:"msg,omitempty"`
}

func (x *BrandListres) Reset() {
	*x = BrandListres{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_shopbrand_shopbrand_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BrandListres) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BrandListres) ProtoMessage() {}

func (x *BrandListres) ProtoReflect() protoreflect.Message {
	mi := &file_api_shopbrand_shopbrand_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BrandListres.ProtoReflect.Descriptor instead.
func (*BrandListres) Descriptor() ([]byte, []int) {
	return file_api_shopbrand_shopbrand_proto_rawDescGZIP(), []int{4}
}

func (x *BrandListres) GetData() []*BrandListres_Info {
	if x != nil {
		return x.Data
	}
	return nil
}

func (x *BrandListres) GetCount() int32 {
	if x != nil {
		return x.Count
	}
	return 0
}

func (x *BrandListres) GetPage() int32 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *BrandListres) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *BrandListres) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

//品牌方详情结构体
type BrandInforeq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	BrandUid string `protobuf:"bytes,1,opt,name=brandUid,proto3" json:"brandUid,omitempty"` //品牌方的唯一标识
	Lang     string `protobuf:"bytes,2,opt,name=lang,proto3" json:"lang,omitempty"`         //语言编号
}

func (x *BrandInforeq) Reset() {
	*x = BrandInforeq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_shopbrand_shopbrand_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BrandInforeq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BrandInforeq) ProtoMessage() {}

func (x *BrandInforeq) ProtoReflect() protoreflect.Message {
	mi := &file_api_shopbrand_shopbrand_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BrandInforeq.ProtoReflect.Descriptor instead.
func (*BrandInforeq) Descriptor() ([]byte, []int) {
	return file_api_shopbrand_shopbrand_proto_rawDescGZIP(), []int{5}
}

func (x *BrandInforeq) GetBrandUid() string {
	if x != nil {
		return x.BrandUid
	}
	return ""
}

func (x *BrandInforeq) GetLang() string {
	if x != nil {
		return x.Lang
	}
	return ""
}

type BrandInfores struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Linkman            string   `protobuf:"bytes,1,opt,name=linkman,proto3" json:"linkman,omitempty"`                       //品牌方联系人姓名
	LinkmanContractway string   `protobuf:"bytes,2,opt,name=linkmanContractway,proto3" json:"linkmanContractway,omitempty"` //品牌方联系人联系方式
	BrandDirector      string   `protobuf:"bytes,3,opt,name=brandDirector,proto3" json:"brandDirector,omitempty"`           //品牌方公司董事
	LegalMan           string   `protobuf:"bytes,4,opt,name=legalMan,proto3" json:"legalMan,omitempty"`                     //品牌方公司法人
	BrandNumber        string   `protobuf:"bytes,5,opt,name=brandNumber,proto3" json:"brandNumber,omitempty"`               //品牌方电话
	TypeOfService      string   `protobuf:"bytes,6,opt,name=typeOfService,proto3" json:"typeOfService,omitempty"`           //品牌方公司业务类型
	Introduction       string   `protobuf:"bytes,7,opt,name=introduction,proto3" json:"introduction,omitempty"`             //品牌方公司简介
	Logo               string   `protobuf:"bytes,8,opt,name=logo,proto3" json:"logo,omitempty"`                             //公司logo
	BrandName          string   `protobuf:"bytes,9,opt,name=brandName,proto3" json:"brandName,omitempty"`                   //公司名
	Proof              []string `protobuf:"bytes,10,rep,name=proof,proto3" json:"proof,omitempty"`                          //资格凭证
	Lang               string   `protobuf:"bytes,11,opt,name=lang,proto3" json:"lang,omitempty"`                            //语言
	Date               string   `protobuf:"bytes,12,opt,name=date,proto3" json:"date,omitempty"`                            //品牌方入驻时间
	BrandUid           string   `protobuf:"bytes,13,opt,name=brandUid,proto3" json:"brandUid,omitempty"`                    //品牌方入驻时间
	Msg                string   `protobuf:"bytes,14,opt,name=msg,proto3" json:"msg,omitempty"`
	Address            string   `protobuf:"bytes,15,opt,name=address,proto3" json:"address,omitempty"`
	Mnemonic           string   `protobuf:"bytes,16,opt,name=mnemonic,proto3" json:"mnemonic,omitempty"`
	Status             int32    `protobuf:"varint,17,opt,name=status,proto3" json:"status,omitempty"`
}

func (x *BrandInfores) Reset() {
	*x = BrandInfores{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_shopbrand_shopbrand_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BrandInfores) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BrandInfores) ProtoMessage() {}

func (x *BrandInfores) ProtoReflect() protoreflect.Message {
	mi := &file_api_shopbrand_shopbrand_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BrandInfores.ProtoReflect.Descriptor instead.
func (*BrandInfores) Descriptor() ([]byte, []int) {
	return file_api_shopbrand_shopbrand_proto_rawDescGZIP(), []int{6}
}

func (x *BrandInfores) GetLinkman() string {
	if x != nil {
		return x.Linkman
	}
	return ""
}

func (x *BrandInfores) GetLinkmanContractway() string {
	if x != nil {
		return x.LinkmanContractway
	}
	return ""
}

func (x *BrandInfores) GetBrandDirector() string {
	if x != nil {
		return x.BrandDirector
	}
	return ""
}

func (x *BrandInfores) GetLegalMan() string {
	if x != nil {
		return x.LegalMan
	}
	return ""
}

func (x *BrandInfores) GetBrandNumber() string {
	if x != nil {
		return x.BrandNumber
	}
	return ""
}

func (x *BrandInfores) GetTypeOfService() string {
	if x != nil {
		return x.TypeOfService
	}
	return ""
}

func (x *BrandInfores) GetIntroduction() string {
	if x != nil {
		return x.Introduction
	}
	return ""
}

func (x *BrandInfores) GetLogo() string {
	if x != nil {
		return x.Logo
	}
	return ""
}

func (x *BrandInfores) GetBrandName() string {
	if x != nil {
		return x.BrandName
	}
	return ""
}

func (x *BrandInfores) GetProof() []string {
	if x != nil {
		return x.Proof
	}
	return nil
}

func (x *BrandInfores) GetLang() string {
	if x != nil {
		return x.Lang
	}
	return ""
}

func (x *BrandInfores) GetDate() string {
	if x != nil {
		return x.Date
	}
	return ""
}

func (x *BrandInfores) GetBrandUid() string {
	if x != nil {
		return x.BrandUid
	}
	return ""
}

func (x *BrandInfores) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *BrandInfores) GetAddress() string {
	if x != nil {
		return x.Address
	}
	return ""
}

func (x *BrandInfores) GetMnemonic() string {
	if x != nil {
		return x.Mnemonic
	}
	return ""
}

func (x *BrandInfores) GetStatus() int32 {
	if x != nil {
		return x.Status
	}
	return 0
}

//删除品牌方结构体
type DeleteBrandreq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	BrandUid string `protobuf:"bytes,1,opt,name=brandUid,proto3" json:"brandUid,omitempty"` //品牌方的唯一标识
}

func (x *DeleteBrandreq) Reset() {
	*x = DeleteBrandreq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_shopbrand_shopbrand_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteBrandreq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteBrandreq) ProtoMessage() {}

func (x *DeleteBrandreq) ProtoReflect() protoreflect.Message {
	mi := &file_api_shopbrand_shopbrand_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteBrandreq.ProtoReflect.Descriptor instead.
func (*DeleteBrandreq) Descriptor() ([]byte, []int) {
	return file_api_shopbrand_shopbrand_proto_rawDescGZIP(), []int{7}
}

func (x *DeleteBrandreq) GetBrandUid() string {
	if x != nil {
		return x.BrandUid
	}
	return ""
}

type DeleteBrandres struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Msg string `protobuf:"bytes,1,opt,name=msg,proto3" json:"msg,omitempty"`
}

func (x *DeleteBrandres) Reset() {
	*x = DeleteBrandres{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_shopbrand_shopbrand_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteBrandres) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteBrandres) ProtoMessage() {}

func (x *DeleteBrandres) ProtoReflect() protoreflect.Message {
	mi := &file_api_shopbrand_shopbrand_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteBrandres.ProtoReflect.Descriptor instead.
func (*DeleteBrandres) Descriptor() ([]byte, []int) {
	return file_api_shopbrand_shopbrand_proto_rawDescGZIP(), []int{8}
}

func (x *DeleteBrandres) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

//更新品牌方数据结构体
type UpdateBrandreq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	BrandUid           string   `protobuf:"bytes,1,opt,name=brandUid,proto3" json:"brandUid,omitempty"`
	Lang               string   `protobuf:"bytes,2,opt,name=lang,proto3" json:"lang,omitempty"`
	Linkman            string   `protobuf:"bytes,3,opt,name=linkman,proto3" json:"linkman,omitempty"`                       //品牌方联系人姓名
	LinkmanContractway string   `protobuf:"bytes,4,opt,name=linkmanContractway,proto3" json:"linkmanContractway,omitempty"` //品牌方联系人联系方式
	BrandDirector      string   `protobuf:"bytes,5,opt,name=brandDirector,proto3" json:"brandDirector,omitempty"`           //品牌方公司董事
	LegalMan           string   `protobuf:"bytes,6,opt,name=legalMan,proto3" json:"legalMan,omitempty"`                     //品牌方公司法人
	BrandNumber        string   `protobuf:"bytes,7,opt,name=brandNumber,proto3" json:"brandNumber,omitempty"`               //品牌方电话
	TypeOfService      string   `protobuf:"bytes,8,opt,name=typeOfService,proto3" json:"typeOfService,omitempty"`           //品牌方公司业务类型
	Introduction       string   `protobuf:"bytes,9,opt,name=introduction,proto3" json:"introduction,omitempty"`             //品牌方公司简介
	Logo               string   `protobuf:"bytes,10,opt,name=logo,proto3" json:"logo,omitempty"`                            //公司logo
	BrandName          string   `protobuf:"bytes,11,opt,name=brandName,proto3" json:"brandName,omitempty"`                  //公司名
	Proof              []string `protobuf:"bytes,12,rep,name=proof,proto3" json:"proof,omitempty"`                          //资格凭证
	Date               string   `protobuf:"bytes,13,opt,name=date,proto3" json:"date,omitempty"`                            //品牌方入驻时间
}

func (x *UpdateBrandreq) Reset() {
	*x = UpdateBrandreq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_shopbrand_shopbrand_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateBrandreq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateBrandreq) ProtoMessage() {}

func (x *UpdateBrandreq) ProtoReflect() protoreflect.Message {
	mi := &file_api_shopbrand_shopbrand_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateBrandreq.ProtoReflect.Descriptor instead.
func (*UpdateBrandreq) Descriptor() ([]byte, []int) {
	return file_api_shopbrand_shopbrand_proto_rawDescGZIP(), []int{9}
}

func (x *UpdateBrandreq) GetBrandUid() string {
	if x != nil {
		return x.BrandUid
	}
	return ""
}

func (x *UpdateBrandreq) GetLang() string {
	if x != nil {
		return x.Lang
	}
	return ""
}

func (x *UpdateBrandreq) GetLinkman() string {
	if x != nil {
		return x.Linkman
	}
	return ""
}

func (x *UpdateBrandreq) GetLinkmanContractway() string {
	if x != nil {
		return x.LinkmanContractway
	}
	return ""
}

func (x *UpdateBrandreq) GetBrandDirector() string {
	if x != nil {
		return x.BrandDirector
	}
	return ""
}

func (x *UpdateBrandreq) GetLegalMan() string {
	if x != nil {
		return x.LegalMan
	}
	return ""
}

func (x *UpdateBrandreq) GetBrandNumber() string {
	if x != nil {
		return x.BrandNumber
	}
	return ""
}

func (x *UpdateBrandreq) GetTypeOfService() string {
	if x != nil {
		return x.TypeOfService
	}
	return ""
}

func (x *UpdateBrandreq) GetIntroduction() string {
	if x != nil {
		return x.Introduction
	}
	return ""
}

func (x *UpdateBrandreq) GetLogo() string {
	if x != nil {
		return x.Logo
	}
	return ""
}

func (x *UpdateBrandreq) GetBrandName() string {
	if x != nil {
		return x.BrandName
	}
	return ""
}

func (x *UpdateBrandreq) GetProof() []string {
	if x != nil {
		return x.Proof
	}
	return nil
}

func (x *UpdateBrandreq) GetDate() string {
	if x != nil {
		return x.Date
	}
	return ""
}

type UpdateBrandres struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Msg string `protobuf:"bytes,1,opt,name=msg,proto3" json:"msg,omitempty"`
}

func (x *UpdateBrandres) Reset() {
	*x = UpdateBrandres{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_shopbrand_shopbrand_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateBrandres) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateBrandres) ProtoMessage() {}

func (x *UpdateBrandres) ProtoReflect() protoreflect.Message {
	mi := &file_api_shopbrand_shopbrand_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateBrandres.ProtoReflect.Descriptor instead.
func (*UpdateBrandres) Descriptor() ([]byte, []int) {
	return file_api_shopbrand_shopbrand_proto_rawDescGZIP(), []int{10}
}

func (x *UpdateBrandres) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

type AllBrandreq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Page     int32 `protobuf:"varint,1,opt,name=page,proto3" json:"page,omitempty"` //页数
	PageSize int32 `protobuf:"varint,2,opt,name=pageSize,proto3" json:"pageSize,omitempty"`
}

func (x *AllBrandreq) Reset() {
	*x = AllBrandreq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_shopbrand_shopbrand_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AllBrandreq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AllBrandreq) ProtoMessage() {}

func (x *AllBrandreq) ProtoReflect() protoreflect.Message {
	mi := &file_api_shopbrand_shopbrand_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AllBrandreq.ProtoReflect.Descriptor instead.
func (*AllBrandreq) Descriptor() ([]byte, []int) {
	return file_api_shopbrand_shopbrand_proto_rawDescGZIP(), []int{11}
}

func (x *AllBrandreq) GetPage() int32 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *AllBrandreq) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

type AllBrandres struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Data []*AllBrandres_Info `protobuf:"bytes,1,rep,name=data,proto3" json:"data,omitempty"`
	Msg  string              `protobuf:"bytes,5,opt,name=msg,proto3" json:"msg,omitempty"`
}

func (x *AllBrandres) Reset() {
	*x = AllBrandres{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_shopbrand_shopbrand_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AllBrandres) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AllBrandres) ProtoMessage() {}

func (x *AllBrandres) ProtoReflect() protoreflect.Message {
	mi := &file_api_shopbrand_shopbrand_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AllBrandres.ProtoReflect.Descriptor instead.
func (*AllBrandres) Descriptor() ([]byte, []int) {
	return file_api_shopbrand_shopbrand_proto_rawDescGZIP(), []int{12}
}

func (x *AllBrandres) GetData() []*AllBrandres_Info {
	if x != nil {
		return x.Data
	}
	return nil
}

func (x *AllBrandres) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

type GetAllLanguageInfoReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	BrandUid string `protobuf:"bytes,1,opt,name=brandUid,proto3" json:"brandUid,omitempty"`
}

func (x *GetAllLanguageInfoReq) Reset() {
	*x = GetAllLanguageInfoReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_shopbrand_shopbrand_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetAllLanguageInfoReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAllLanguageInfoReq) ProtoMessage() {}

func (x *GetAllLanguageInfoReq) ProtoReflect() protoreflect.Message {
	mi := &file_api_shopbrand_shopbrand_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAllLanguageInfoReq.ProtoReflect.Descriptor instead.
func (*GetAllLanguageInfoReq) Descriptor() ([]byte, []int) {
	return file_api_shopbrand_shopbrand_proto_rawDescGZIP(), []int{13}
}

func (x *GetAllLanguageInfoReq) GetBrandUid() string {
	if x != nil {
		return x.BrandUid
	}
	return ""
}

type LanguageInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Lang              string `protobuf:"bytes,2,opt,name=lang,proto3" json:"lang,omitempty"`
	Status            int32  `protobuf:"varint,3,opt,name=status,proto3" json:"status,omitempty"`
	LanguageTranslate string `protobuf:"bytes,4,opt,name=languageTranslate,proto3" json:"languageTranslate,omitempty"`
}

func (x *LanguageInfo) Reset() {
	*x = LanguageInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_shopbrand_shopbrand_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LanguageInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LanguageInfo) ProtoMessage() {}

func (x *LanguageInfo) ProtoReflect() protoreflect.Message {
	mi := &file_api_shopbrand_shopbrand_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LanguageInfo.ProtoReflect.Descriptor instead.
func (*LanguageInfo) Descriptor() ([]byte, []int) {
	return file_api_shopbrand_shopbrand_proto_rawDescGZIP(), []int{14}
}

func (x *LanguageInfo) GetLang() string {
	if x != nil {
		return x.Lang
	}
	return ""
}

func (x *LanguageInfo) GetStatus() int32 {
	if x != nil {
		return x.Status
	}
	return 0
}

func (x *LanguageInfo) GetLanguageTranslate() string {
	if x != nil {
		return x.LanguageTranslate
	}
	return ""
}

type GetAllLanguageInfoRep struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	LanguageInfo []*LanguageInfo `protobuf:"bytes,1,rep,name=languageInfo,proto3" json:"languageInfo,omitempty"`
	Msg          string          `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
}

func (x *GetAllLanguageInfoRep) Reset() {
	*x = GetAllLanguageInfoRep{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_shopbrand_shopbrand_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetAllLanguageInfoRep) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAllLanguageInfoRep) ProtoMessage() {}

func (x *GetAllLanguageInfoRep) ProtoReflect() protoreflect.Message {
	mi := &file_api_shopbrand_shopbrand_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAllLanguageInfoRep.ProtoReflect.Descriptor instead.
func (*GetAllLanguageInfoRep) Descriptor() ([]byte, []int) {
	return file_api_shopbrand_shopbrand_proto_rawDescGZIP(), []int{15}
}

func (x *GetAllLanguageInfoRep) GetLanguageInfo() []*LanguageInfo {
	if x != nil {
		return x.LanguageInfo
	}
	return nil
}

func (x *GetAllLanguageInfoRep) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

type UpdateLanguageInfoReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	LanguageInfo []*LanguageInfo `protobuf:"bytes,1,rep,name=languageInfo,proto3" json:"languageInfo,omitempty"`
}

func (x *UpdateLanguageInfoReq) Reset() {
	*x = UpdateLanguageInfoReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_shopbrand_shopbrand_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateLanguageInfoReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateLanguageInfoReq) ProtoMessage() {}

func (x *UpdateLanguageInfoReq) ProtoReflect() protoreflect.Message {
	mi := &file_api_shopbrand_shopbrand_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateLanguageInfoReq.ProtoReflect.Descriptor instead.
func (*UpdateLanguageInfoReq) Descriptor() ([]byte, []int) {
	return file_api_shopbrand_shopbrand_proto_rawDescGZIP(), []int{16}
}

func (x *UpdateLanguageInfoReq) GetLanguageInfo() []*LanguageInfo {
	if x != nil {
		return x.LanguageInfo
	}
	return nil
}

type UpdateLanguageInfoRep struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Msg string `protobuf:"bytes,1,opt,name=msg,proto3" json:"msg,omitempty"`
}

func (x *UpdateLanguageInfoRep) Reset() {
	*x = UpdateLanguageInfoRep{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_shopbrand_shopbrand_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateLanguageInfoRep) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateLanguageInfoRep) ProtoMessage() {}

func (x *UpdateLanguageInfoRep) ProtoReflect() protoreflect.Message {
	mi := &file_api_shopbrand_shopbrand_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateLanguageInfoRep.ProtoReflect.Descriptor instead.
func (*UpdateLanguageInfoRep) Descriptor() ([]byte, []int) {
	return file_api_shopbrand_shopbrand_proto_rawDescGZIP(), []int{17}
}

func (x *UpdateLanguageInfoRep) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

type GetLanguageInfoByNumReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Lang string `protobuf:"bytes,2,opt,name=lang,proto3" json:"lang,omitempty"`
}

func (x *GetLanguageInfoByNumReq) Reset() {
	*x = GetLanguageInfoByNumReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_shopbrand_shopbrand_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetLanguageInfoByNumReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetLanguageInfoByNumReq) ProtoMessage() {}

func (x *GetLanguageInfoByNumReq) ProtoReflect() protoreflect.Message {
	mi := &file_api_shopbrand_shopbrand_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetLanguageInfoByNumReq.ProtoReflect.Descriptor instead.
func (*GetLanguageInfoByNumReq) Descriptor() ([]byte, []int) {
	return file_api_shopbrand_shopbrand_proto_rawDescGZIP(), []int{18}
}

func (x *GetLanguageInfoByNumReq) GetLang() string {
	if x != nil {
		return x.Lang
	}
	return ""
}

type GetLanguageInfoByNumRep struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Lang string `protobuf:"bytes,1,opt,name=lang,proto3" json:"lang,omitempty"`
	Msg  string `protobuf:"bytes,3,opt,name=msg,proto3" json:"msg,omitempty"`
}

func (x *GetLanguageInfoByNumRep) Reset() {
	*x = GetLanguageInfoByNumRep{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_shopbrand_shopbrand_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetLanguageInfoByNumRep) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetLanguageInfoByNumRep) ProtoMessage() {}

func (x *GetLanguageInfoByNumRep) ProtoReflect() protoreflect.Message {
	mi := &file_api_shopbrand_shopbrand_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetLanguageInfoByNumRep.ProtoReflect.Descriptor instead.
func (*GetLanguageInfoByNumRep) Descriptor() ([]byte, []int) {
	return file_api_shopbrand_shopbrand_proto_rawDescGZIP(), []int{19}
}

func (x *GetLanguageInfoByNumRep) GetLang() string {
	if x != nil {
		return x.Lang
	}
	return ""
}

func (x *GetLanguageInfoByNumRep) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

type GetLanguageInfoByLanguageReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Lang string `protobuf:"bytes,1,opt,name=lang,proto3" json:"lang,omitempty"`
}

func (x *GetLanguageInfoByLanguageReq) Reset() {
	*x = GetLanguageInfoByLanguageReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_shopbrand_shopbrand_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetLanguageInfoByLanguageReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetLanguageInfoByLanguageReq) ProtoMessage() {}

func (x *GetLanguageInfoByLanguageReq) ProtoReflect() protoreflect.Message {
	mi := &file_api_shopbrand_shopbrand_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetLanguageInfoByLanguageReq.ProtoReflect.Descriptor instead.
func (*GetLanguageInfoByLanguageReq) Descriptor() ([]byte, []int) {
	return file_api_shopbrand_shopbrand_proto_rawDescGZIP(), []int{20}
}

func (x *GetLanguageInfoByLanguageReq) GetLang() string {
	if x != nil {
		return x.Lang
	}
	return ""
}

type GetLanguageInfoByLanguageRep struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Lang string `protobuf:"bytes,2,opt,name=lang,proto3" json:"lang,omitempty"`
	Msg  string `protobuf:"bytes,3,opt,name=msg,proto3" json:"msg,omitempty"`
}

func (x *GetLanguageInfoByLanguageRep) Reset() {
	*x = GetLanguageInfoByLanguageRep{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_shopbrand_shopbrand_proto_msgTypes[21]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetLanguageInfoByLanguageRep) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetLanguageInfoByLanguageRep) ProtoMessage() {}

func (x *GetLanguageInfoByLanguageRep) ProtoReflect() protoreflect.Message {
	mi := &file_api_shopbrand_shopbrand_proto_msgTypes[21]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetLanguageInfoByLanguageRep.ProtoReflect.Descriptor instead.
func (*GetLanguageInfoByLanguageRep) Descriptor() ([]byte, []int) {
	return file_api_shopbrand_shopbrand_proto_rawDescGZIP(), []int{21}
}

func (x *GetLanguageInfoByLanguageRep) GetLang() string {
	if x != nil {
		return x.Lang
	}
	return ""
}

func (x *GetLanguageInfoByLanguageRep) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

type CodeCommit struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CommitID         int32  `protobuf:"varint,1,opt,name=commitID,proto3" json:"commitID,omitempty"`                  // 提交记录id
	CommittedAt      string `protobuf:"bytes,2,opt,name=committedAt,proto3" json:"committedAt,omitempty"`             // 提交时间（YYYY-MM-DD HH:MM:SS）
	CommitterName    string `protobuf:"bytes,3,opt,name=committerName,proto3" json:"committerName,omitempty"`         // 提交人姓名
	CommitterEmail   string `protobuf:"bytes,4,opt,name=committerEmail,proto3" json:"committerEmail,omitempty"`       // 提交人邮箱
	CommitMessage    string `protobuf:"bytes,5,opt,name=commitMessage,proto3" json:"commitMessage,omitempty"`         // 提交信息
	OwnerName        string `protobuf:"bytes,6,opt,name=ownerName,proto3" json:"ownerName,omitempty"`                 // 仓库所属用户或者组织名称
	RepositoryName   string `protobuf:"bytes,7,opt,name=repositoryName,proto3" json:"repositoryName,omitempty"`       // 代码库名称
	BranchName       string `protobuf:"bytes,8,opt,name=branchName,proto3" json:"branchName,omitempty"`               // 分支名称
	CommitSha        string `protobuf:"bytes,9,opt,name=commitSha,proto3" json:"commitSha,omitempty"`                 // 提交标识
	CommitUrl        string `protobuf:"bytes,10,opt,name=commitUrl,proto3" json:"commitUrl,omitempty"`                // 提交页面
	TotalChangeCount int32  `protobuf:"varint,11,opt,name=totalChangeCount,proto3" json:"totalChangeCount,omitempty"` // 代码总变动数
	AddCount         int32  `protobuf:"varint,12,opt,name=addCount,proto3" json:"addCount,omitempty"`                 // 新增数
	DeleteCount      int32  `protobuf:"varint,13,opt,name=deleteCount,proto3" json:"deleteCount,omitempty"`           // 删除数
}

func (x *CodeCommit) Reset() {
	*x = CodeCommit{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_shopbrand_shopbrand_proto_msgTypes[22]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CodeCommit) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CodeCommit) ProtoMessage() {}

func (x *CodeCommit) ProtoReflect() protoreflect.Message {
	mi := &file_api_shopbrand_shopbrand_proto_msgTypes[22]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CodeCommit.ProtoReflect.Descriptor instead.
func (*CodeCommit) Descriptor() ([]byte, []int) {
	return file_api_shopbrand_shopbrand_proto_rawDescGZIP(), []int{22}
}

func (x *CodeCommit) GetCommitID() int32 {
	if x != nil {
		return x.CommitID
	}
	return 0
}

func (x *CodeCommit) GetCommittedAt() string {
	if x != nil {
		return x.CommittedAt
	}
	return ""
}

func (x *CodeCommit) GetCommitterName() string {
	if x != nil {
		return x.CommitterName
	}
	return ""
}

func (x *CodeCommit) GetCommitterEmail() string {
	if x != nil {
		return x.CommitterEmail
	}
	return ""
}

func (x *CodeCommit) GetCommitMessage() string {
	if x != nil {
		return x.CommitMessage
	}
	return ""
}

func (x *CodeCommit) GetOwnerName() string {
	if x != nil {
		return x.OwnerName
	}
	return ""
}

func (x *CodeCommit) GetRepositoryName() string {
	if x != nil {
		return x.RepositoryName
	}
	return ""
}

func (x *CodeCommit) GetBranchName() string {
	if x != nil {
		return x.BranchName
	}
	return ""
}

func (x *CodeCommit) GetCommitSha() string {
	if x != nil {
		return x.CommitSha
	}
	return ""
}

func (x *CodeCommit) GetCommitUrl() string {
	if x != nil {
		return x.CommitUrl
	}
	return ""
}

func (x *CodeCommit) GetTotalChangeCount() int32 {
	if x != nil {
		return x.TotalChangeCount
	}
	return 0
}

func (x *CodeCommit) GetAddCount() int32 {
	if x != nil {
		return x.AddCount
	}
	return 0
}

func (x *CodeCommit) GetDeleteCount() int32 {
	if x != nil {
		return x.DeleteCount
	}
	return 0
}

type CodeCommitReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CodeCommit []*CodeCommit `protobuf:"bytes,1,rep,name=codeCommit,proto3" json:"codeCommit,omitempty"`
}

func (x *CodeCommitReq) Reset() {
	*x = CodeCommitReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_shopbrand_shopbrand_proto_msgTypes[23]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CodeCommitReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CodeCommitReq) ProtoMessage() {}

func (x *CodeCommitReq) ProtoReflect() protoreflect.Message {
	mi := &file_api_shopbrand_shopbrand_proto_msgTypes[23]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CodeCommitReq.ProtoReflect.Descriptor instead.
func (*CodeCommitReq) Descriptor() ([]byte, []int) {
	return file_api_shopbrand_shopbrand_proto_rawDescGZIP(), []int{23}
}

func (x *CodeCommitReq) GetCodeCommit() []*CodeCommit {
	if x != nil {
		return x.CodeCommit
	}
	return nil
}

type CodeCommitResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Msg string `protobuf:"bytes,1,opt,name=msg,proto3" json:"msg,omitempty"`
}

func (x *CodeCommitResp) Reset() {
	*x = CodeCommitResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_shopbrand_shopbrand_proto_msgTypes[24]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CodeCommitResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CodeCommitResp) ProtoMessage() {}

func (x *CodeCommitResp) ProtoReflect() protoreflect.Message {
	mi := &file_api_shopbrand_shopbrand_proto_msgTypes[24]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CodeCommitResp.ProtoReflect.Descriptor instead.
func (*CodeCommitResp) Descriptor() ([]byte, []int) {
	return file_api_shopbrand_shopbrand_proto_rawDescGZIP(), []int{24}
}

func (x *CodeCommitResp) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

type BrandListres_Info struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	BrandUid  string `protobuf:"bytes,1,opt,name=brandUid,proto3" json:"brandUid,omitempty"`
	BrandName string `protobuf:"bytes,2,opt,name=brandName,proto3" json:"brandName,omitempty"`
	Logo      string `protobuf:"bytes,3,opt,name=logo,proto3" json:"logo,omitempty"`
	Address   string `protobuf:"bytes,4,opt,name=address,proto3" json:"address,omitempty"`
	Mnemonic  string `protobuf:"bytes,5,opt,name=mnemonic,proto3" json:"mnemonic,omitempty"`
	Status    int32  `protobuf:"varint,6,opt,name=status,proto3" json:"status,omitempty"`
}

func (x *BrandListres_Info) Reset() {
	*x = BrandListres_Info{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_shopbrand_shopbrand_proto_msgTypes[25]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BrandListres_Info) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BrandListres_Info) ProtoMessage() {}

func (x *BrandListres_Info) ProtoReflect() protoreflect.Message {
	mi := &file_api_shopbrand_shopbrand_proto_msgTypes[25]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BrandListres_Info.ProtoReflect.Descriptor instead.
func (*BrandListres_Info) Descriptor() ([]byte, []int) {
	return file_api_shopbrand_shopbrand_proto_rawDescGZIP(), []int{4, 0}
}

func (x *BrandListres_Info) GetBrandUid() string {
	if x != nil {
		return x.BrandUid
	}
	return ""
}

func (x *BrandListres_Info) GetBrandName() string {
	if x != nil {
		return x.BrandName
	}
	return ""
}

func (x *BrandListres_Info) GetLogo() string {
	if x != nil {
		return x.Logo
	}
	return ""
}

func (x *BrandListres_Info) GetAddress() string {
	if x != nil {
		return x.Address
	}
	return ""
}

func (x *BrandListres_Info) GetMnemonic() string {
	if x != nil {
		return x.Mnemonic
	}
	return ""
}

func (x *BrandListres_Info) GetStatus() int32 {
	if x != nil {
		return x.Status
	}
	return 0
}

type AllBrandres_Info struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	BrandUid  string `protobuf:"bytes,1,opt,name=brandUid,proto3" json:"brandUid,omitempty"`
	BrandName string `protobuf:"bytes,2,opt,name=brandName,proto3" json:"brandName,omitempty"` //公司名
}

func (x *AllBrandres_Info) Reset() {
	*x = AllBrandres_Info{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_shopbrand_shopbrand_proto_msgTypes[26]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AllBrandres_Info) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AllBrandres_Info) ProtoMessage() {}

func (x *AllBrandres_Info) ProtoReflect() protoreflect.Message {
	mi := &file_api_shopbrand_shopbrand_proto_msgTypes[26]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AllBrandres_Info.ProtoReflect.Descriptor instead.
func (*AllBrandres_Info) Descriptor() ([]byte, []int) {
	return file_api_shopbrand_shopbrand_proto_rawDescGZIP(), []int{12, 0}
}

func (x *AllBrandres_Info) GetBrandUid() string {
	if x != nil {
		return x.BrandUid
	}
	return ""
}

func (x *AllBrandres_Info) GetBrandName() string {
	if x != nil {
		return x.BrandName
	}
	return ""
}

var File_api_shopbrand_shopbrand_proto protoreflect.FileDescriptor

var file_api_shopbrand_shopbrand_proto_rawDesc = []byte{
	0x0a, 0x1d, 0x61, 0x70, 0x69, 0x2f, 0x73, 0x68, 0x6f, 0x70, 0x62, 0x72, 0x61, 0x6e, 0x64, 0x2f,
	0x73, 0x68, 0x6f, 0x70, 0x62, 0x72, 0x61, 0x6e, 0x64, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12,
	0x09, 0x73, 0x68, 0x6f, 0x70, 0x62, 0x72, 0x61, 0x6e, 0x64, 0x22, 0x4f, 0x0a, 0x09, 0x42, 0x72,
	0x61, 0x6e, 0x64, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x10, 0x0a, 0x03, 0x6e, 0x75, 0x6d, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x03, 0x6e, 0x75, 0x6d, 0x12, 0x1c, 0x0a, 0x09, 0x62, 0x72, 0x61,
	0x6e, 0x64, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x62, 0x72,
	0x61, 0x6e, 0x64, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x6c, 0x6f, 0x67, 0x6f, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6c, 0x6f, 0x67, 0x6f, 0x22, 0xca, 0x03, 0x0a, 0x0e,
	0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x42, 0x72, 0x61, 0x6e, 0x64, 0x72, 0x65, 0x71, 0x12, 0x18,
	0x0a, 0x07, 0x6c, 0x69, 0x6e, 0x6b, 0x6d, 0x61, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x07, 0x6c, 0x69, 0x6e, 0x6b, 0x6d, 0x61, 0x6e, 0x12, 0x2e, 0x0a, 0x12, 0x6c, 0x69, 0x6e, 0x6b,
	0x6d, 0x61, 0x6e, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x61, 0x63, 0x74, 0x77, 0x61, 0x79, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x12, 0x6c, 0x69, 0x6e, 0x6b, 0x6d, 0x61, 0x6e, 0x43, 0x6f, 0x6e,
	0x74, 0x72, 0x61, 0x63, 0x74, 0x77, 0x61, 0x79, 0x12, 0x24, 0x0a, 0x0d, 0x62, 0x72, 0x61, 0x6e,
	0x64, 0x44, 0x69, 0x72, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0d, 0x62, 0x72, 0x61, 0x6e, 0x64, 0x44, 0x69, 0x72, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x12, 0x1a,
	0x0a, 0x08, 0x6c, 0x65, 0x67, 0x61, 0x6c, 0x4d, 0x61, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x08, 0x6c, 0x65, 0x67, 0x61, 0x6c, 0x4d, 0x61, 0x6e, 0x12, 0x20, 0x0a, 0x0b, 0x62, 0x72,
	0x61, 0x6e, 0x64, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0b, 0x62, 0x72, 0x61, 0x6e, 0x64, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x24, 0x0a, 0x0d,
	0x74, 0x79, 0x70, 0x65, 0x4f, 0x66, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x18, 0x06, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0d, 0x74, 0x79, 0x70, 0x65, 0x4f, 0x66, 0x53, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x12, 0x22, 0x0a, 0x0c, 0x69, 0x6e, 0x74, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x69,
	0x6f, 0x6e, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x69, 0x6e, 0x74, 0x72, 0x6f, 0x64,
	0x75, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x14, 0x0a, 0x05, 0x70, 0x72, 0x6f, 0x6f, 0x66, 0x18,
	0x08, 0x20, 0x03, 0x28, 0x09, 0x52, 0x05, 0x70, 0x72, 0x6f, 0x6f, 0x66, 0x12, 0x12, 0x0a, 0x04,
	0x64, 0x61, 0x74, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x64, 0x61, 0x74, 0x65,
	0x12, 0x18, 0x0a, 0x07, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x18, 0x0a, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x07, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x12, 0x1a, 0x0a, 0x08, 0x6d, 0x6e,
	0x65, 0x6d, 0x6f, 0x6e, 0x69, 0x63, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x6d, 0x6e,
	0x65, 0x6d, 0x6f, 0x6e, 0x69, 0x63, 0x12, 0x1c, 0x0a, 0x09, 0x62, 0x72, 0x61, 0x6e, 0x64, 0x4e,
	0x61, 0x6d, 0x65, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x62, 0x72, 0x61, 0x6e, 0x64,
	0x4e, 0x61, 0x6d, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x6c, 0x6f, 0x67, 0x6f, 0x18, 0x0e, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x04, 0x6c, 0x6f, 0x67, 0x6f, 0x12, 0x1a, 0x0a, 0x08, 0x62, 0x72, 0x61, 0x6e,
	0x64, 0x55, 0x69, 0x64, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x62, 0x72, 0x61, 0x6e,
	0x64, 0x55, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6c, 0x61, 0x6e, 0x67, 0x18, 0x10, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x04, 0x6c, 0x61, 0x6e, 0x67, 0x22, 0x3e, 0x0a, 0x0e, 0x43, 0x72, 0x65, 0x61,
	0x74, 0x65, 0x42, 0x72, 0x61, 0x6e, 0x64, 0x72, 0x65, 0x73, 0x12, 0x10, 0x0a, 0x03, 0x6d, 0x73,
	0x67, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6d, 0x73, 0x67, 0x12, 0x1a, 0x0a, 0x08,
	0x62, 0x72, 0x61, 0x6e, 0x64, 0x55, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08,
	0x62, 0x72, 0x61, 0x6e, 0x64, 0x55, 0x69, 0x64, 0x22, 0xa0, 0x01, 0x0a, 0x0c, 0x42, 0x72, 0x61,
	0x6e, 0x64, 0x4c, 0x69, 0x73, 0x74, 0x72, 0x65, 0x71, 0x12, 0x18, 0x0a, 0x07, 0x6b, 0x65, 0x79,
	0x77, 0x6f, 0x72, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6b, 0x65, 0x79, 0x77,
	0x6f, 0x72, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x70, 0x61, 0x67, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x04, 0x70, 0x61, 0x67, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x61, 0x67, 0x65, 0x53,
	0x69, 0x7a, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x70, 0x61, 0x67, 0x65, 0x53,
	0x69, 0x7a, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x62, 0x72, 0x61, 0x6e, 0x64, 0x55, 0x69, 0x64, 0x18,
	0x05, 0x20, 0x03, 0x28, 0x09, 0x52, 0x08, 0x62, 0x72, 0x61, 0x6e, 0x64, 0x55, 0x69, 0x64, 0x12,
	0x16, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x06, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x12, 0x0a, 0x04, 0x6c, 0x61, 0x6e, 0x67, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6c, 0x61, 0x6e, 0x67, 0x22, 0xbd, 0x02, 0x0a, 0x0c,
	0x42, 0x72, 0x61, 0x6e, 0x64, 0x4c, 0x69, 0x73, 0x74, 0x72, 0x65, 0x73, 0x12, 0x30, 0x0a, 0x04,
	0x64, 0x61, 0x74, 0x61, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x73, 0x68, 0x6f,
	0x70, 0x62, 0x72, 0x61, 0x6e, 0x64, 0x2e, 0x42, 0x72, 0x61, 0x6e, 0x64, 0x4c, 0x69, 0x73, 0x74,
	0x72, 0x65, 0x73, 0x2e, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x12, 0x14,
	0x0a, 0x05, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x12, 0x12, 0x0a, 0x04, 0x70, 0x61, 0x67, 0x65, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x04, 0x70, 0x61, 0x67, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x61, 0x67, 0x65,
	0x53, 0x69, 0x7a, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x70, 0x61, 0x67, 0x65,
	0x53, 0x69, 0x7a, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x6d, 0x73, 0x67, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x03, 0x6d, 0x73, 0x67, 0x1a, 0xa2, 0x01, 0x0a, 0x04, 0x49, 0x6e, 0x66, 0x6f, 0x12,
	0x1a, 0x0a, 0x08, 0x62, 0x72, 0x61, 0x6e, 0x64, 0x55, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x08, 0x62, 0x72, 0x61, 0x6e, 0x64, 0x55, 0x69, 0x64, 0x12, 0x1c, 0x0a, 0x09, 0x62,
	0x72, 0x61, 0x6e, 0x64, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09,
	0x62, 0x72, 0x61, 0x6e, 0x64, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x6c, 0x6f, 0x67,
	0x6f, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6c, 0x6f, 0x67, 0x6f, 0x12, 0x18, 0x0a,
	0x07, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07,
	0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x12, 0x1a, 0x0a, 0x08, 0x6d, 0x6e, 0x65, 0x6d, 0x6f,
	0x6e, 0x69, 0x63, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x6d, 0x6e, 0x65, 0x6d, 0x6f,
	0x6e, 0x69, 0x63, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x06, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x22, 0x3e, 0x0a, 0x0c, 0x42,
	0x72, 0x61, 0x6e, 0x64, 0x49, 0x6e, 0x66, 0x6f, 0x72, 0x65, 0x71, 0x12, 0x1a, 0x0a, 0x08, 0x62,
	0x72, 0x61, 0x6e, 0x64, 0x55, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x62,
	0x72, 0x61, 0x6e, 0x64, 0x55, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6c, 0x61, 0x6e, 0x67, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6c, 0x61, 0x6e, 0x67, 0x22, 0xf2, 0x03, 0x0a, 0x0c,
	0x42, 0x72, 0x61, 0x6e, 0x64, 0x49, 0x6e, 0x66, 0x6f, 0x72, 0x65, 0x73, 0x12, 0x18, 0x0a, 0x07,
	0x6c, 0x69, 0x6e, 0x6b, 0x6d, 0x61, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6c,
	0x69, 0x6e, 0x6b, 0x6d, 0x61, 0x6e, 0x12, 0x2e, 0x0a, 0x12, 0x6c, 0x69, 0x6e, 0x6b, 0x6d, 0x61,
	0x6e, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x61, 0x63, 0x74, 0x77, 0x61, 0x79, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x12, 0x6c, 0x69, 0x6e, 0x6b, 0x6d, 0x61, 0x6e, 0x43, 0x6f, 0x6e, 0x74, 0x72,
	0x61, 0x63, 0x74, 0x77, 0x61, 0x79, 0x12, 0x24, 0x0a, 0x0d, 0x62, 0x72, 0x61, 0x6e, 0x64, 0x44,
	0x69, 0x72, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x62,
	0x72, 0x61, 0x6e, 0x64, 0x44, 0x69, 0x72, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x12, 0x1a, 0x0a, 0x08,
	0x6c, 0x65, 0x67, 0x61, 0x6c, 0x4d, 0x61, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08,
	0x6c, 0x65, 0x67, 0x61, 0x6c, 0x4d, 0x61, 0x6e, 0x12, 0x20, 0x0a, 0x0b, 0x62, 0x72, 0x61, 0x6e,
	0x64, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x62,
	0x72, 0x61, 0x6e, 0x64, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x24, 0x0a, 0x0d, 0x74, 0x79,
	0x70, 0x65, 0x4f, 0x66, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0d, 0x74, 0x79, 0x70, 0x65, 0x4f, 0x66, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x12, 0x22, 0x0a, 0x0c, 0x69, 0x6e, 0x74, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x69, 0x6f, 0x6e,
	0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x69, 0x6e, 0x74, 0x72, 0x6f, 0x64, 0x75, 0x63,
	0x74, 0x69, 0x6f, 0x6e, 0x12, 0x12, 0x0a, 0x04, 0x6c, 0x6f, 0x67, 0x6f, 0x18, 0x08, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x04, 0x6c, 0x6f, 0x67, 0x6f, 0x12, 0x1c, 0x0a, 0x09, 0x62, 0x72, 0x61, 0x6e,
	0x64, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x62, 0x72, 0x61,
	0x6e, 0x64, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x70, 0x72, 0x6f, 0x6f, 0x66, 0x18,
	0x0a, 0x20, 0x03, 0x28, 0x09, 0x52, 0x05, 0x70, 0x72, 0x6f, 0x6f, 0x66, 0x12, 0x12, 0x0a, 0x04,
	0x6c, 0x61, 0x6e, 0x67, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6c, 0x61, 0x6e, 0x67,
	0x12, 0x12, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x65, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04,
	0x64, 0x61, 0x74, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x62, 0x72, 0x61, 0x6e, 0x64, 0x55, 0x69, 0x64,
	0x18, 0x0d, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x62, 0x72, 0x61, 0x6e, 0x64, 0x55, 0x69, 0x64,
	0x12, 0x10, 0x0a, 0x03, 0x6d, 0x73, 0x67, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6d,
	0x73, 0x67, 0x12, 0x18, 0x0a, 0x07, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x18, 0x0f, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x12, 0x1a, 0x0a, 0x08,
	0x6d, 0x6e, 0x65, 0x6d, 0x6f, 0x6e, 0x69, 0x63, 0x18, 0x10, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08,
	0x6d, 0x6e, 0x65, 0x6d, 0x6f, 0x6e, 0x69, 0x63, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x18, 0x11, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x22, 0x2c, 0x0a, 0x0e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x42, 0x72, 0x61, 0x6e, 0x64, 0x72,
	0x65, 0x71, 0x12, 0x1a, 0x0a, 0x08, 0x62, 0x72, 0x61, 0x6e, 0x64, 0x55, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x62, 0x72, 0x61, 0x6e, 0x64, 0x55, 0x69, 0x64, 0x22, 0x22,
	0x0a, 0x0e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x42, 0x72, 0x61, 0x6e, 0x64, 0x72, 0x65, 0x73,
	0x12, 0x10, 0x0a, 0x03, 0x6d, 0x73, 0x67, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6d,
	0x73, 0x67, 0x22, 0x94, 0x03, 0x0a, 0x0e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x42, 0x72, 0x61,
	0x6e, 0x64, 0x72, 0x65, 0x71, 0x12, 0x1a, 0x0a, 0x08, 0x62, 0x72, 0x61, 0x6e, 0x64, 0x55, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x62, 0x72, 0x61, 0x6e, 0x64, 0x55, 0x69,
	0x64, 0x12, 0x12, 0x0a, 0x04, 0x6c, 0x61, 0x6e, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x04, 0x6c, 0x61, 0x6e, 0x67, 0x12, 0x18, 0x0a, 0x07, 0x6c, 0x69, 0x6e, 0x6b, 0x6d, 0x61, 0x6e,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6c, 0x69, 0x6e, 0x6b, 0x6d, 0x61, 0x6e, 0x12,
	0x2e, 0x0a, 0x12, 0x6c, 0x69, 0x6e, 0x6b, 0x6d, 0x61, 0x6e, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x61,
	0x63, 0x74, 0x77, 0x61, 0x79, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x12, 0x6c, 0x69, 0x6e,
	0x6b, 0x6d, 0x61, 0x6e, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x61, 0x63, 0x74, 0x77, 0x61, 0x79, 0x12,
	0x24, 0x0a, 0x0d, 0x62, 0x72, 0x61, 0x6e, 0x64, 0x44, 0x69, 0x72, 0x65, 0x63, 0x74, 0x6f, 0x72,
	0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x62, 0x72, 0x61, 0x6e, 0x64, 0x44, 0x69, 0x72,
	0x65, 0x63, 0x74, 0x6f, 0x72, 0x12, 0x1a, 0x0a, 0x08, 0x6c, 0x65, 0x67, 0x61, 0x6c, 0x4d, 0x61,
	0x6e, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x6c, 0x65, 0x67, 0x61, 0x6c, 0x4d, 0x61,
	0x6e, 0x12, 0x20, 0x0a, 0x0b, 0x62, 0x72, 0x61, 0x6e, 0x64, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72,
	0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x62, 0x72, 0x61, 0x6e, 0x64, 0x4e, 0x75, 0x6d,
	0x62, 0x65, 0x72, 0x12, 0x24, 0x0a, 0x0d, 0x74, 0x79, 0x70, 0x65, 0x4f, 0x66, 0x53, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x74, 0x79, 0x70, 0x65,
	0x4f, 0x66, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x22, 0x0a, 0x0c, 0x69, 0x6e, 0x74,
	0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0c, 0x69, 0x6e, 0x74, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x12, 0x0a,
	0x04, 0x6c, 0x6f, 0x67, 0x6f, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6c, 0x6f, 0x67,
	0x6f, 0x12, 0x1c, 0x0a, 0x09, 0x62, 0x72, 0x61, 0x6e, 0x64, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x0b,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x62, 0x72, 0x61, 0x6e, 0x64, 0x4e, 0x61, 0x6d, 0x65, 0x12,
	0x14, 0x0a, 0x05, 0x70, 0x72, 0x6f, 0x6f, 0x66, 0x18, 0x0c, 0x20, 0x03, 0x28, 0x09, 0x52, 0x05,
	0x70, 0x72, 0x6f, 0x6f, 0x66, 0x12, 0x12, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x65, 0x18, 0x0d, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x04, 0x64, 0x61, 0x74, 0x65, 0x22, 0x22, 0x0a, 0x0e, 0x55, 0x70, 0x64,
	0x61, 0x74, 0x65, 0x42, 0x72, 0x61, 0x6e, 0x64, 0x72, 0x65, 0x73, 0x12, 0x10, 0x0a, 0x03, 0x6d,
	0x73, 0x67, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6d, 0x73, 0x67, 0x22, 0x3d, 0x0a,
	0x0b, 0x41, 0x6c, 0x6c, 0x42, 0x72, 0x61, 0x6e, 0x64, 0x72, 0x65, 0x71, 0x12, 0x12, 0x0a, 0x04,
	0x70, 0x61, 0x67, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x70, 0x61, 0x67, 0x65,
	0x12, 0x1a, 0x0a, 0x08, 0x70, 0x61, 0x67, 0x65, 0x53, 0x69, 0x7a, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x08, 0x70, 0x61, 0x67, 0x65, 0x53, 0x69, 0x7a, 0x65, 0x22, 0x92, 0x01, 0x0a,
	0x0b, 0x41, 0x6c, 0x6c, 0x42, 0x72, 0x61, 0x6e, 0x64, 0x72, 0x65, 0x73, 0x12, 0x2f, 0x0a, 0x04,
	0x64, 0x61, 0x74, 0x61, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x73, 0x68, 0x6f,
	0x70, 0x62, 0x72, 0x61, 0x6e, 0x64, 0x2e, 0x41, 0x6c, 0x6c, 0x42, 0x72, 0x61, 0x6e, 0x64, 0x72,
	0x65, 0x73, 0x2e, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x12, 0x10, 0x0a,
	0x03, 0x6d, 0x73, 0x67, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6d, 0x73, 0x67, 0x1a,
	0x40, 0x0a, 0x04, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x1a, 0x0a, 0x08, 0x62, 0x72, 0x61, 0x6e, 0x64,
	0x55, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x62, 0x72, 0x61, 0x6e, 0x64,
	0x55, 0x69, 0x64, 0x12, 0x1c, 0x0a, 0x09, 0x62, 0x72, 0x61, 0x6e, 0x64, 0x4e, 0x61, 0x6d, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x62, 0x72, 0x61, 0x6e, 0x64, 0x4e, 0x61, 0x6d,
	0x65, 0x22, 0x33, 0x0a, 0x15, 0x47, 0x65, 0x74, 0x41, 0x6c, 0x6c, 0x4c, 0x61, 0x6e, 0x67, 0x75,
	0x61, 0x67, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x71, 0x12, 0x1a, 0x0a, 0x08, 0x62, 0x72,
	0x61, 0x6e, 0x64, 0x55, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x62, 0x72,
	0x61, 0x6e, 0x64, 0x55, 0x69, 0x64, 0x22, 0x68, 0x0a, 0x0c, 0x4c, 0x61, 0x6e, 0x67, 0x75, 0x61,
	0x67, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x12, 0x0a, 0x04, 0x6c, 0x61, 0x6e, 0x67, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6c, 0x61, 0x6e, 0x67, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x12, 0x2c, 0x0a, 0x11, 0x6c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65, 0x54, 0x72,
	0x61, 0x6e, 0x73, 0x6c, 0x61, 0x74, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x11, 0x6c,
	0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x6c, 0x61, 0x74, 0x65,
	0x22, 0x66, 0x0a, 0x15, 0x47, 0x65, 0x74, 0x41, 0x6c, 0x6c, 0x4c, 0x61, 0x6e, 0x67, 0x75, 0x61,
	0x67, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x70, 0x12, 0x3b, 0x0a, 0x0c, 0x6c, 0x61, 0x6e,
	0x67, 0x75, 0x61, 0x67, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x17, 0x2e, 0x73, 0x68, 0x6f, 0x70, 0x62, 0x72, 0x61, 0x6e, 0x64, 0x2e, 0x4c, 0x61, 0x6e, 0x67,
	0x75, 0x61, 0x67, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x0c, 0x6c, 0x61, 0x6e, 0x67, 0x75, 0x61,
	0x67, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x10, 0x0a, 0x03, 0x6d, 0x73, 0x67, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x03, 0x6d, 0x73, 0x67, 0x22, 0x54, 0x0a, 0x15, 0x55, 0x70, 0x64, 0x61,
	0x74, 0x65, 0x4c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65,
	0x71, 0x12, 0x3b, 0x0a, 0x0c, 0x6c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65, 0x49, 0x6e, 0x66,
	0x6f, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x73, 0x68, 0x6f, 0x70, 0x62, 0x72,
	0x61, 0x6e, 0x64, 0x2e, 0x4c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65, 0x49, 0x6e, 0x66, 0x6f,
	0x52, 0x0c, 0x6c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x22, 0x29,
	0x0a, 0x15, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x4c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65,
	0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x70, 0x12, 0x10, 0x0a, 0x03, 0x6d, 0x73, 0x67, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6d, 0x73, 0x67, 0x22, 0x2d, 0x0a, 0x17, 0x47, 0x65, 0x74,
	0x4c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x42, 0x79, 0x4e, 0x75,
	0x6d, 0x52, 0x65, 0x71, 0x12, 0x12, 0x0a, 0x04, 0x6c, 0x61, 0x6e, 0x67, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x04, 0x6c, 0x61, 0x6e, 0x67, 0x22, 0x3f, 0x0a, 0x17, 0x47, 0x65, 0x74, 0x4c,
	0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x42, 0x79, 0x4e, 0x75, 0x6d,
	0x52, 0x65, 0x70, 0x12, 0x12, 0x0a, 0x04, 0x6c, 0x61, 0x6e, 0x67, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x04, 0x6c, 0x61, 0x6e, 0x67, 0x12, 0x10, 0x0a, 0x03, 0x6d, 0x73, 0x67, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6d, 0x73, 0x67, 0x22, 0x32, 0x0a, 0x1c, 0x47, 0x65, 0x74,
	0x4c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x42, 0x79, 0x4c, 0x61,
	0x6e, 0x67, 0x75, 0x61, 0x67, 0x65, 0x52, 0x65, 0x71, 0x12, 0x12, 0x0a, 0x04, 0x6c, 0x61, 0x6e,
	0x67, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6c, 0x61, 0x6e, 0x67, 0x22, 0x44, 0x0a,
	0x1c, 0x47, 0x65, 0x74, 0x4c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65, 0x49, 0x6e, 0x66, 0x6f,
	0x42, 0x79, 0x4c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65, 0x52, 0x65, 0x70, 0x12, 0x12, 0x0a,
	0x04, 0x6c, 0x61, 0x6e, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6c, 0x61, 0x6e,
	0x67, 0x12, 0x10, 0x0a, 0x03, 0x6d, 0x73, 0x67, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03,
	0x6d, 0x73, 0x67, 0x22, 0xca, 0x03, 0x0a, 0x0a, 0x43, 0x6f, 0x64, 0x65, 0x43, 0x6f, 0x6d, 0x6d,
	0x69, 0x74, 0x12, 0x1a, 0x0a, 0x08, 0x63, 0x6f, 0x6d, 0x6d, 0x69, 0x74, 0x49, 0x44, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x63, 0x6f, 0x6d, 0x6d, 0x69, 0x74, 0x49, 0x44, 0x12, 0x20,
	0x0a, 0x0b, 0x63, 0x6f, 0x6d, 0x6d, 0x69, 0x74, 0x74, 0x65, 0x64, 0x41, 0x74, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0b, 0x63, 0x6f, 0x6d, 0x6d, 0x69, 0x74, 0x74, 0x65, 0x64, 0x41, 0x74,
	0x12, 0x24, 0x0a, 0x0d, 0x63, 0x6f, 0x6d, 0x6d, 0x69, 0x74, 0x74, 0x65, 0x72, 0x4e, 0x61, 0x6d,
	0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x63, 0x6f, 0x6d, 0x6d, 0x69, 0x74, 0x74,
	0x65, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x26, 0x0a, 0x0e, 0x63, 0x6f, 0x6d, 0x6d, 0x69, 0x74,
	0x74, 0x65, 0x72, 0x45, 0x6d, 0x61, 0x69, 0x6c, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e,
	0x63, 0x6f, 0x6d, 0x6d, 0x69, 0x74, 0x74, 0x65, 0x72, 0x45, 0x6d, 0x61, 0x69, 0x6c, 0x12, 0x24,
	0x0a, 0x0d, 0x63, 0x6f, 0x6d, 0x6d, 0x69, 0x74, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x63, 0x6f, 0x6d, 0x6d, 0x69, 0x74, 0x4d, 0x65, 0x73,
	0x73, 0x61, 0x67, 0x65, 0x12, 0x1c, 0x0a, 0x09, 0x6f, 0x77, 0x6e, 0x65, 0x72, 0x4e, 0x61, 0x6d,
	0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x6f, 0x77, 0x6e, 0x65, 0x72, 0x4e, 0x61,
	0x6d, 0x65, 0x12, 0x26, 0x0a, 0x0e, 0x72, 0x65, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x6f, 0x72, 0x79,
	0x4e, 0x61, 0x6d, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x72, 0x65, 0x70, 0x6f,
	0x73, 0x69, 0x74, 0x6f, 0x72, 0x79, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1e, 0x0a, 0x0a, 0x62, 0x72,
	0x61, 0x6e, 0x63, 0x68, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a,
	0x62, 0x72, 0x61, 0x6e, 0x63, 0x68, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1c, 0x0a, 0x09, 0x63, 0x6f,
	0x6d, 0x6d, 0x69, 0x74, 0x53, 0x68, 0x61, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x63,
	0x6f, 0x6d, 0x6d, 0x69, 0x74, 0x53, 0x68, 0x61, 0x12, 0x1c, 0x0a, 0x09, 0x63, 0x6f, 0x6d, 0x6d,
	0x69, 0x74, 0x55, 0x72, 0x6c, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x63, 0x6f, 0x6d,
	0x6d, 0x69, 0x74, 0x55, 0x72, 0x6c, 0x12, 0x2a, 0x0a, 0x10, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x43,
	0x68, 0x61, 0x6e, 0x67, 0x65, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x10, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x43, 0x6f, 0x75,
	0x6e, 0x74, 0x12, 0x1a, 0x0a, 0x08, 0x61, 0x64, 0x64, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x0c,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x61, 0x64, 0x64, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x20,
	0x0a, 0x0b, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x0d, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x0b, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x43, 0x6f, 0x75, 0x6e, 0x74,
	0x22, 0x46, 0x0a, 0x0d, 0x43, 0x6f, 0x64, 0x65, 0x43, 0x6f, 0x6d, 0x6d, 0x69, 0x74, 0x52, 0x65,
	0x71, 0x12, 0x35, 0x0a, 0x0a, 0x63, 0x6f, 0x64, 0x65, 0x43, 0x6f, 0x6d, 0x6d, 0x69, 0x74, 0x18,
	0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x73, 0x68, 0x6f, 0x70, 0x62, 0x72, 0x61, 0x6e,
	0x64, 0x2e, 0x43, 0x6f, 0x64, 0x65, 0x43, 0x6f, 0x6d, 0x6d, 0x69, 0x74, 0x52, 0x0a, 0x63, 0x6f,
	0x64, 0x65, 0x43, 0x6f, 0x6d, 0x6d, 0x69, 0x74, 0x22, 0x22, 0x0a, 0x0e, 0x43, 0x6f, 0x64, 0x65,
	0x43, 0x6f, 0x6d, 0x6d, 0x69, 0x74, 0x52, 0x65, 0x73, 0x70, 0x12, 0x10, 0x0a, 0x03, 0x6d, 0x73,
	0x67, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6d, 0x73, 0x67, 0x32, 0xec, 0x06, 0x0a,
	0x05, 0x42, 0x72, 0x61, 0x6e, 0x64, 0x12, 0x45, 0x0a, 0x0b, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65,
	0x42, 0x72, 0x61, 0x6e, 0x64, 0x12, 0x19, 0x2e, 0x73, 0x68, 0x6f, 0x70, 0x62, 0x72, 0x61, 0x6e,
	0x64, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x42, 0x72, 0x61, 0x6e, 0x64, 0x72, 0x65, 0x71,
	0x1a, 0x19, 0x2e, 0x73, 0x68, 0x6f, 0x70, 0x62, 0x72, 0x61, 0x6e, 0x64, 0x2e, 0x43, 0x72, 0x65,
	0x61, 0x74, 0x65, 0x42, 0x72, 0x61, 0x6e, 0x64, 0x72, 0x65, 0x73, 0x22, 0x00, 0x12, 0x3f, 0x0a,
	0x09, 0x42, 0x72, 0x61, 0x6e, 0x64, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x17, 0x2e, 0x73, 0x68, 0x6f,
	0x70, 0x62, 0x72, 0x61, 0x6e, 0x64, 0x2e, 0x42, 0x72, 0x61, 0x6e, 0x64, 0x4c, 0x69, 0x73, 0x74,
	0x72, 0x65, 0x71, 0x1a, 0x17, 0x2e, 0x73, 0x68, 0x6f, 0x70, 0x62, 0x72, 0x61, 0x6e, 0x64, 0x2e,
	0x42, 0x72, 0x61, 0x6e, 0x64, 0x4c, 0x69, 0x73, 0x74, 0x72, 0x65, 0x73, 0x22, 0x00, 0x12, 0x3f,
	0x0a, 0x09, 0x42, 0x72, 0x61, 0x6e, 0x64, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x17, 0x2e, 0x73, 0x68,
	0x6f, 0x70, 0x62, 0x72, 0x61, 0x6e, 0x64, 0x2e, 0x42, 0x72, 0x61, 0x6e, 0x64, 0x49, 0x6e, 0x66,
	0x6f, 0x72, 0x65, 0x71, 0x1a, 0x17, 0x2e, 0x73, 0x68, 0x6f, 0x70, 0x62, 0x72, 0x61, 0x6e, 0x64,
	0x2e, 0x42, 0x72, 0x61, 0x6e, 0x64, 0x49, 0x6e, 0x66, 0x6f, 0x72, 0x65, 0x73, 0x22, 0x00, 0x12,
	0x45, 0x0a, 0x0b, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x42, 0x72, 0x61, 0x6e, 0x64, 0x12, 0x19,
	0x2e, 0x73, 0x68, 0x6f, 0x70, 0x62, 0x72, 0x61, 0x6e, 0x64, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74,
	0x65, 0x42, 0x72, 0x61, 0x6e, 0x64, 0x72, 0x65, 0x71, 0x1a, 0x19, 0x2e, 0x73, 0x68, 0x6f, 0x70,
	0x62, 0x72, 0x61, 0x6e, 0x64, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x42, 0x72, 0x61, 0x6e,
	0x64, 0x72, 0x65, 0x73, 0x22, 0x00, 0x12, 0x45, 0x0a, 0x0b, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x42, 0x72, 0x61, 0x6e, 0x64, 0x12, 0x19, 0x2e, 0x73, 0x68, 0x6f, 0x70, 0x62, 0x72, 0x61, 0x6e,
	0x64, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x42, 0x72, 0x61, 0x6e, 0x64, 0x72, 0x65, 0x71,
	0x1a, 0x19, 0x2e, 0x73, 0x68, 0x6f, 0x70, 0x62, 0x72, 0x61, 0x6e, 0x64, 0x2e, 0x55, 0x70, 0x64,
	0x61, 0x74, 0x65, 0x42, 0x72, 0x61, 0x6e, 0x64, 0x72, 0x65, 0x73, 0x22, 0x00, 0x12, 0x3c, 0x0a,
	0x08, 0x41, 0x6c, 0x6c, 0x42, 0x72, 0x61, 0x6e, 0x64, 0x12, 0x16, 0x2e, 0x73, 0x68, 0x6f, 0x70,
	0x62, 0x72, 0x61, 0x6e, 0x64, 0x2e, 0x41, 0x6c, 0x6c, 0x42, 0x72, 0x61, 0x6e, 0x64, 0x72, 0x65,
	0x71, 0x1a, 0x16, 0x2e, 0x73, 0x68, 0x6f, 0x70, 0x62, 0x72, 0x61, 0x6e, 0x64, 0x2e, 0x41, 0x6c,
	0x6c, 0x42, 0x72, 0x61, 0x6e, 0x64, 0x72, 0x65, 0x73, 0x22, 0x00, 0x12, 0x5a, 0x0a, 0x12, 0x47,
	0x65, 0x74, 0x41, 0x6c, 0x6c, 0x4c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65, 0x49, 0x6e, 0x66,
	0x6f, 0x12, 0x20, 0x2e, 0x73, 0x68, 0x6f, 0x70, 0x62, 0x72, 0x61, 0x6e, 0x64, 0x2e, 0x47, 0x65,
	0x74, 0x41, 0x6c, 0x6c, 0x4c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65, 0x49, 0x6e, 0x66, 0x6f,
	0x52, 0x65, 0x71, 0x1a, 0x20, 0x2e, 0x73, 0x68, 0x6f, 0x70, 0x62, 0x72, 0x61, 0x6e, 0x64, 0x2e,
	0x47, 0x65, 0x74, 0x41, 0x6c, 0x6c, 0x4c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65, 0x49, 0x6e,
	0x66, 0x6f, 0x52, 0x65, 0x70, 0x22, 0x00, 0x12, 0x5a, 0x0a, 0x12, 0x55, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x4c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x20, 0x2e,
	0x73, 0x68, 0x6f, 0x70, 0x62, 0x72, 0x61, 0x6e, 0x64, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x4c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x71, 0x1a,
	0x20, 0x2e, 0x73, 0x68, 0x6f, 0x70, 0x62, 0x72, 0x61, 0x6e, 0x64, 0x2e, 0x55, 0x70, 0x64, 0x61,
	0x74, 0x65, 0x4c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65,
	0x70, 0x22, 0x00, 0x12, 0x60, 0x0a, 0x14, 0x47, 0x65, 0x74, 0x4c, 0x61, 0x6e, 0x67, 0x75, 0x61,
	0x67, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x42, 0x79, 0x4e, 0x75, 0x6d, 0x12, 0x22, 0x2e, 0x73, 0x68,
	0x6f, 0x70, 0x62, 0x72, 0x61, 0x6e, 0x64, 0x2e, 0x47, 0x65, 0x74, 0x4c, 0x61, 0x6e, 0x67, 0x75,
	0x61, 0x67, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x42, 0x79, 0x4e, 0x75, 0x6d, 0x52, 0x65, 0x71, 0x1a,
	0x22, 0x2e, 0x73, 0x68, 0x6f, 0x70, 0x62, 0x72, 0x61, 0x6e, 0x64, 0x2e, 0x47, 0x65, 0x74, 0x4c,
	0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x42, 0x79, 0x4e, 0x75, 0x6d,
	0x52, 0x65, 0x70, 0x22, 0x00, 0x12, 0x6f, 0x0a, 0x19, 0x47, 0x65, 0x74, 0x4c, 0x61, 0x6e, 0x67,
	0x75, 0x61, 0x67, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x42, 0x79, 0x4c, 0x61, 0x6e, 0x67, 0x75, 0x61,
	0x67, 0x65, 0x12, 0x27, 0x2e, 0x73, 0x68, 0x6f, 0x70, 0x62, 0x72, 0x61, 0x6e, 0x64, 0x2e, 0x47,
	0x65, 0x74, 0x4c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x42, 0x79,
	0x4c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65, 0x52, 0x65, 0x71, 0x1a, 0x27, 0x2e, 0x73, 0x68,
	0x6f, 0x70, 0x62, 0x72, 0x61, 0x6e, 0x64, 0x2e, 0x47, 0x65, 0x74, 0x4c, 0x61, 0x6e, 0x67, 0x75,
	0x61, 0x67, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x42, 0x79, 0x4c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67,
	0x65, 0x52, 0x65, 0x70, 0x22, 0x00, 0x12, 0x43, 0x0a, 0x0a, 0x43, 0x6f, 0x64, 0x65, 0x43, 0x6f,
	0x6d, 0x6d, 0x69, 0x74, 0x12, 0x18, 0x2e, 0x73, 0x68, 0x6f, 0x70, 0x62, 0x72, 0x61, 0x6e, 0x64,
	0x2e, 0x43, 0x6f, 0x64, 0x65, 0x43, 0x6f, 0x6d, 0x6d, 0x69, 0x74, 0x52, 0x65, 0x71, 0x1a, 0x19,
	0x2e, 0x73, 0x68, 0x6f, 0x70, 0x62, 0x72, 0x61, 0x6e, 0x64, 0x2e, 0x43, 0x6f, 0x64, 0x65, 0x43,
	0x6f, 0x6d, 0x6d, 0x69, 0x74, 0x52, 0x65, 0x73, 0x70, 0x22, 0x00, 0x42, 0x0e, 0x5a, 0x0c, 0x2e,
	0x2f, 0x3b, 0x73, 0x68, 0x6f, 0x70, 0x62, 0x72, 0x61, 0x6e, 0x64, 0x62, 0x06, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x33,
}

var (
	file_api_shopbrand_shopbrand_proto_rawDescOnce sync.Once
	file_api_shopbrand_shopbrand_proto_rawDescData = file_api_shopbrand_shopbrand_proto_rawDesc
)

func file_api_shopbrand_shopbrand_proto_rawDescGZIP() []byte {
	file_api_shopbrand_shopbrand_proto_rawDescOnce.Do(func() {
		file_api_shopbrand_shopbrand_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_shopbrand_shopbrand_proto_rawDescData)
	})
	return file_api_shopbrand_shopbrand_proto_rawDescData
}

var file_api_shopbrand_shopbrand_proto_msgTypes = make([]protoimpl.MessageInfo, 27)
var file_api_shopbrand_shopbrand_proto_goTypes = []interface{}{
	(*BrandInfo)(nil),                    // 0: shopbrand.BrandInfo
	(*CreateBrandreq)(nil),               // 1: shopbrand.CreateBrandreq
	(*CreateBrandres)(nil),               // 2: shopbrand.CreateBrandres
	(*BrandListreq)(nil),                 // 3: shopbrand.BrandListreq
	(*BrandListres)(nil),                 // 4: shopbrand.BrandListres
	(*BrandInforeq)(nil),                 // 5: shopbrand.BrandInforeq
	(*BrandInfores)(nil),                 // 6: shopbrand.BrandInfores
	(*DeleteBrandreq)(nil),               // 7: shopbrand.DeleteBrandreq
	(*DeleteBrandres)(nil),               // 8: shopbrand.DeleteBrandres
	(*UpdateBrandreq)(nil),               // 9: shopbrand.UpdateBrandreq
	(*UpdateBrandres)(nil),               // 10: shopbrand.UpdateBrandres
	(*AllBrandreq)(nil),                  // 11: shopbrand.AllBrandreq
	(*AllBrandres)(nil),                  // 12: shopbrand.AllBrandres
	(*GetAllLanguageInfoReq)(nil),        // 13: shopbrand.GetAllLanguageInfoReq
	(*LanguageInfo)(nil),                 // 14: shopbrand.LanguageInfo
	(*GetAllLanguageInfoRep)(nil),        // 15: shopbrand.GetAllLanguageInfoRep
	(*UpdateLanguageInfoReq)(nil),        // 16: shopbrand.UpdateLanguageInfoReq
	(*UpdateLanguageInfoRep)(nil),        // 17: shopbrand.UpdateLanguageInfoRep
	(*GetLanguageInfoByNumReq)(nil),      // 18: shopbrand.GetLanguageInfoByNumReq
	(*GetLanguageInfoByNumRep)(nil),      // 19: shopbrand.GetLanguageInfoByNumRep
	(*GetLanguageInfoByLanguageReq)(nil), // 20: shopbrand.GetLanguageInfoByLanguageReq
	(*GetLanguageInfoByLanguageRep)(nil), // 21: shopbrand.GetLanguageInfoByLanguageRep
	(*CodeCommit)(nil),                   // 22: shopbrand.CodeCommit
	(*CodeCommitReq)(nil),                // 23: shopbrand.CodeCommitReq
	(*CodeCommitResp)(nil),               // 24: shopbrand.CodeCommitResp
	(*BrandListres_Info)(nil),            // 25: shopbrand.BrandListres.Info
	(*AllBrandres_Info)(nil),             // 26: shopbrand.AllBrandres.Info
}
var file_api_shopbrand_shopbrand_proto_depIdxs = []int32{
	25, // 0: shopbrand.BrandListres.data:type_name -> shopbrand.BrandListres.Info
	26, // 1: shopbrand.AllBrandres.data:type_name -> shopbrand.AllBrandres.Info
	14, // 2: shopbrand.GetAllLanguageInfoRep.languageInfo:type_name -> shopbrand.LanguageInfo
	14, // 3: shopbrand.UpdateLanguageInfoReq.languageInfo:type_name -> shopbrand.LanguageInfo
	22, // 4: shopbrand.CodeCommitReq.codeCommit:type_name -> shopbrand.CodeCommit
	1,  // 5: shopbrand.Brand.CreateBrand:input_type -> shopbrand.CreateBrandreq
	3,  // 6: shopbrand.Brand.BrandList:input_type -> shopbrand.BrandListreq
	5,  // 7: shopbrand.Brand.BrandInfo:input_type -> shopbrand.BrandInforeq
	7,  // 8: shopbrand.Brand.DeleteBrand:input_type -> shopbrand.DeleteBrandreq
	9,  // 9: shopbrand.Brand.UpdateBrand:input_type -> shopbrand.UpdateBrandreq
	11, // 10: shopbrand.Brand.AllBrand:input_type -> shopbrand.AllBrandreq
	13, // 11: shopbrand.Brand.GetAllLanguageInfo:input_type -> shopbrand.GetAllLanguageInfoReq
	16, // 12: shopbrand.Brand.UpdateLanguageInfo:input_type -> shopbrand.UpdateLanguageInfoReq
	18, // 13: shopbrand.Brand.GetLanguageInfoByNum:input_type -> shopbrand.GetLanguageInfoByNumReq
	20, // 14: shopbrand.Brand.GetLanguageInfoByLanguage:input_type -> shopbrand.GetLanguageInfoByLanguageReq
	23, // 15: shopbrand.Brand.CodeCommit:input_type -> shopbrand.CodeCommitReq
	2,  // 16: shopbrand.Brand.CreateBrand:output_type -> shopbrand.CreateBrandres
	4,  // 17: shopbrand.Brand.BrandList:output_type -> shopbrand.BrandListres
	6,  // 18: shopbrand.Brand.BrandInfo:output_type -> shopbrand.BrandInfores
	8,  // 19: shopbrand.Brand.DeleteBrand:output_type -> shopbrand.DeleteBrandres
	10, // 20: shopbrand.Brand.UpdateBrand:output_type -> shopbrand.UpdateBrandres
	12, // 21: shopbrand.Brand.AllBrand:output_type -> shopbrand.AllBrandres
	15, // 22: shopbrand.Brand.GetAllLanguageInfo:output_type -> shopbrand.GetAllLanguageInfoRep
	17, // 23: shopbrand.Brand.UpdateLanguageInfo:output_type -> shopbrand.UpdateLanguageInfoRep
	19, // 24: shopbrand.Brand.GetLanguageInfoByNum:output_type -> shopbrand.GetLanguageInfoByNumRep
	21, // 25: shopbrand.Brand.GetLanguageInfoByLanguage:output_type -> shopbrand.GetLanguageInfoByLanguageRep
	24, // 26: shopbrand.Brand.CodeCommit:output_type -> shopbrand.CodeCommitResp
	16, // [16:27] is the sub-list for method output_type
	5,  // [5:16] is the sub-list for method input_type
	5,  // [5:5] is the sub-list for extension type_name
	5,  // [5:5] is the sub-list for extension extendee
	0,  // [0:5] is the sub-list for field type_name
}

func init() { file_api_shopbrand_shopbrand_proto_init() }
func file_api_shopbrand_shopbrand_proto_init() {
	if File_api_shopbrand_shopbrand_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_api_shopbrand_shopbrand_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BrandInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_shopbrand_shopbrand_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateBrandreq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_shopbrand_shopbrand_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateBrandres); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_shopbrand_shopbrand_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BrandListreq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_shopbrand_shopbrand_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BrandListres); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_shopbrand_shopbrand_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BrandInforeq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_shopbrand_shopbrand_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BrandInfores); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_shopbrand_shopbrand_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeleteBrandreq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_shopbrand_shopbrand_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeleteBrandres); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_shopbrand_shopbrand_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateBrandreq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_shopbrand_shopbrand_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateBrandres); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_shopbrand_shopbrand_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AllBrandreq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_shopbrand_shopbrand_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AllBrandres); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_shopbrand_shopbrand_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetAllLanguageInfoReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_shopbrand_shopbrand_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LanguageInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_shopbrand_shopbrand_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetAllLanguageInfoRep); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_shopbrand_shopbrand_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateLanguageInfoReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_shopbrand_shopbrand_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateLanguageInfoRep); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_shopbrand_shopbrand_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetLanguageInfoByNumReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_shopbrand_shopbrand_proto_msgTypes[19].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetLanguageInfoByNumRep); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_shopbrand_shopbrand_proto_msgTypes[20].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetLanguageInfoByLanguageReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_shopbrand_shopbrand_proto_msgTypes[21].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetLanguageInfoByLanguageRep); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_shopbrand_shopbrand_proto_msgTypes[22].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CodeCommit); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_shopbrand_shopbrand_proto_msgTypes[23].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CodeCommitReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_shopbrand_shopbrand_proto_msgTypes[24].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CodeCommitResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_shopbrand_shopbrand_proto_msgTypes[25].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BrandListres_Info); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_shopbrand_shopbrand_proto_msgTypes[26].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AllBrandres_Info); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_shopbrand_shopbrand_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   27,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_api_shopbrand_shopbrand_proto_goTypes,
		DependencyIndexes: file_api_shopbrand_shopbrand_proto_depIdxs,
		MessageInfos:      file_api_shopbrand_shopbrand_proto_msgTypes,
	}.Build()
	File_api_shopbrand_shopbrand_proto = out.File
	file_api_shopbrand_shopbrand_proto_rawDesc = nil
	file_api_shopbrand_shopbrand_proto_goTypes = nil
	file_api_shopbrand_shopbrand_proto_depIdxs = nil
}
