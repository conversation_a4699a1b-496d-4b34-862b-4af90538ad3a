// Code generated by protoc-gen-gogo. DO NOT EDIT.
// source: api/account/account.proto

package account

import (
	fmt "fmt"
	math "math"
	proto "github.com/golang/protobuf/proto"
	_ "github.com/mwitkow/go-proto-validators"
	github_com_mwitkow_go_proto_validators "github.com/mwitkow/go-proto-validators"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

func (this *SmsLogReq) Validate() error {
	return nil
}
func (this *SmsLogRes) Validate() error {
	for _, item := range this.List {
		if item != nil {
			if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(item); err != nil {
				return github_com_mwitkow_go_proto_validators.FieldError("List", err)
			}
		}
	}
	return nil
}
func (this *SmsLogInfo) Validate() error {
	return nil
}
func (this *BindStaffRequest) Validate() error {
	return nil
}
func (this *ReviewRealNameListRequest) Validate() error {
	return nil
}
func (this *ReviewRealNameListResponse) Validate() error {
	for _, item := range this.Data {
		if item != nil {
			if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(item); err != nil {
				return github_com_mwitkow_go_proto_validators.FieldError("Data", err)
			}
		}
	}
	return nil
}
func (this *ReviewRealNameInfo) Validate() error {
	return nil
}
func (this *ReviewRealNameRequest) Validate() error {
	return nil
}
func (this *SubmitInfoRequest) Validate() error {
	return nil
}
func (this *CheckBeforeRegisterRequest) Validate() error {
	return nil
}
func (this *SampleAccountRequest) Validate() error {
	return nil
}
func (this *SampleAccountResponse) Validate() error {
	return nil
}
func (this *LoginAndSqueezeOtherResponse) Validate() error {
	return nil
}
func (this *IsSamePersonResponse) Validate() error {
	return nil
}
func (this *IsSamePersonRequest) Validate() error {
	return nil
}
func (this *UpdatePassportStatusRequest) Validate() error {
	return nil
}
func (this *CreateChainAccountResponse) Validate() error {
	return nil
}
func (this *UsersByJobNumRequest) Validate() error {
	return nil
}
func (this *QueryPersonnelWithTheSameNameRequest) Validate() error {
	return nil
}
func (this *QueryPersonnelWithTheSameNameResponse) Validate() error {
	return nil
}
func (this *ListV2Request) Validate() error {
	if this.Domain == "" {
		return github_com_mwitkow_go_proto_validators.FieldError("Domain", fmt.Errorf(`70001`))
	}
	return nil
}
func (this *SendClockInWechatRequest) Validate() error {
	return nil
}
func (this *MailAccountByNickNameRequest) Validate() error {
	if this.Domain == "" {
		return github_com_mwitkow_go_proto_validators.FieldError("Domain", fmt.Errorf(`70001`))
	}
	if this.NickName == "" {
		return github_com_mwitkow_go_proto_validators.FieldError("NickName", fmt.Errorf(`70005`))
	}
	if !(len(this.NickName) < 20) {
		return github_com_mwitkow_go_proto_validators.FieldError("NickName", fmt.Errorf(`70005`))
	}
	return nil
}
func (this *CreateMaiAccountRequest) Validate() error {
	return nil
}
func (this *MaiAccountResponse) Validate() error {
	return nil
}
func (this *FddRemoveUserRequest) Validate() error {
	return nil
}
func (this *FddCreateUserRequest) Validate() error {
	return nil
}
func (this *WxBoxUserInfoRequest) Validate() error {
	return nil
}
func (this *WxGetOpenIdByCodeRequest) Validate() error {
	return nil
}
func (this *WxGetOpenIdByCodeResponse) Validate() error {
	return nil
}
func (this *WxBoxTelNumByCodeResponse) Validate() error {
	return nil
}
func (this *WxBoxUserInfo) Validate() error {
	if this.User != nil {
		if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(this.User); err != nil {
			return github_com_mwitkow_go_proto_validators.FieldError("User", err)
		}
	}
	if this.Fdd != nil {
		if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(this.Fdd); err != nil {
			return github_com_mwitkow_go_proto_validators.FieldError("Fdd", err)
		}
	}
	return nil
}
func (this *FddInfo) Validate() error {
	return nil
}
func (this *UserInfo) Validate() error {
	if this.Passport != nil {
		if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(this.Passport); err != nil {
			return github_com_mwitkow_go_proto_validators.FieldError("Passport", err)
		}
	}
	return nil
}
func (this *CommonRequest) Validate() error {
	return nil
}
func (this *WxAppRequest) Validate() error {
	return nil
}
func (this *WxAppResponse) Validate() error {
	return nil
}
func (this *WxUserUpdateRequest) Validate() error {
	return nil
}
func (this *WxUserOrCreateRequest) Validate() error {
	if this.OpenID == "" {
		return github_com_mwitkow_go_proto_validators.FieldError("OpenID", fmt.Errorf(`缺少openid`))
	}
	if this.GhID == "" {
		return github_com_mwitkow_go_proto_validators.FieldError("GhID", fmt.Errorf(`缺少参数ghid`))
	}
	return nil
}
func (this *WxUserResponse) Validate() error {
	return nil
}
func (this *LoginLogsResponse) Validate() error {
	for _, item := range this.Data {
		if item != nil {
			if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(item); err != nil {
				return github_com_mwitkow_go_proto_validators.FieldError("Data", err)
			}
		}
	}
	return nil
}
func (this *LoginLog) Validate() error {
	return nil
}
func (this *OnlineLogByIdRequest) Validate() error {
	return nil
}
func (this *LoginInfosByUserIdRequest) Validate() error {
	return nil
}
func (this *SendNewTelNumMsgRequest) Validate() error {
	if this.Domain == "" {
		return github_com_mwitkow_go_proto_validators.FieldError("Domain", fmt.Errorf(`70001`))
	}
	return nil
}
func (this *UserByTelRequest) Validate() error {
	if this.Domain == "" {
		return github_com_mwitkow_go_proto_validators.FieldError("Domain", fmt.Errorf(`70001`))
	}
	return nil
}
func (this *CommonResponse) Validate() error {
	return nil
}
func (this *UsersByTelRequest) Validate() error {
	if this.Domain == "" {
		return github_com_mwitkow_go_proto_validators.FieldError("Domain", fmt.Errorf(`70001`))
	}
	return nil
}
func (this *ListByIDsRequest) Validate() error {
	if this.Domain == "" {
		return github_com_mwitkow_go_proto_validators.FieldError("Domain", fmt.Errorf(`70001`))
	}
	return nil
}
func (this *SendMsgRequest) Validate() error {
	if this.Domain == "" {
		return github_com_mwitkow_go_proto_validators.FieldError("Domain", fmt.Errorf(`70001`))
	}
	return nil
}
func (this *SendCustomMsgRequest) Validate() error {
	if this.Domain == "" {
		return github_com_mwitkow_go_proto_validators.FieldError("Domain", fmt.Errorf(`70001`))
	}
	return nil
}
func (this *CheckMsgRequest) Validate() error {
	if this.Domain == "" {
		return github_com_mwitkow_go_proto_validators.FieldError("Domain", fmt.Errorf(`70001`))
	}
	if this.Code == "" {
		return github_com_mwitkow_go_proto_validators.FieldError("Code", fmt.Errorf(`70003`))
	}
	return nil
}
func (this *SendMsgStatusResponse) Validate() error {
	return nil
}
func (this *RemoveRequest) Validate() error {
	if this.Domain == "" {
		return github_com_mwitkow_go_proto_validators.FieldError("Domain", fmt.Errorf(`70001`))
	}
	if !(this.ID > 0) {
		return github_com_mwitkow_go_proto_validators.FieldError("ID", fmt.Errorf(`70004`))
	}
	return nil
}
func (this *WriteOffRequest) Validate() error {
	return nil
}
func (this *WriteOffListRequest) Validate() error {
	return nil
}
func (this *WriteOffApproveRequest) Validate() error {
	return nil
}
func (this *WriteOffListResponse) Validate() error {
	for _, item := range this.WriteOffList {
		if item != nil {
			if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(item); err != nil {
				return github_com_mwitkow_go_proto_validators.FieldError("WriteOffList", err)
			}
		}
	}
	return nil
}
func (this *RemoveResponse) Validate() error {
	return nil
}
func (this *UpdateRequest) Validate() error {
	if this.Domain == "" {
		return github_com_mwitkow_go_proto_validators.FieldError("Domain", fmt.Errorf(`70001`))
	}
	if this.Extend != nil {
		if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(this.Extend); err != nil {
			return github_com_mwitkow_go_proto_validators.FieldError("Extend", err)
		}
	}
	for _, item := range this.TrainVideos {
		if item != nil {
			if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(item); err != nil {
				return github_com_mwitkow_go_proto_validators.FieldError("TrainVideos", err)
			}
		}
	}
	if this.Operator != nil {
		if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(this.Operator); err != nil {
			return github_com_mwitkow_go_proto_validators.FieldError("Operator", err)
		}
	}
	if this.UserExtend != nil {
		if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(this.UserExtend); err != nil {
			return github_com_mwitkow_go_proto_validators.FieldError("UserExtend", err)
		}
	}
	return nil
}
func (this *Operator) Validate() error {
	return nil
}
func (this *TrainVideo) Validate() error {
	return nil
}
func (this *UpdateResponse) Validate() error {
	return nil
}
func (this *PrivacyInfoRequest) Validate() error {
	if this.Domain == "" {
		return github_com_mwitkow_go_proto_validators.FieldError("Domain", fmt.Errorf(`70001`))
	}
	return nil
}
func (this *ListRequest) Validate() error {
	if this.Domain == "" {
		return github_com_mwitkow_go_proto_validators.FieldError("Domain", fmt.Errorf(`70001`))
	}
	return nil
}
func (this *ListResponse) Validate() error {
	for _, item := range this.Data {
		if item != nil {
			if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(item); err != nil {
				return github_com_mwitkow_go_proto_validators.FieldError("Data", err)
			}
		}
	}
	return nil
}
func (this *InfoRequest) Validate() error {
	if this.Domain == "" {
		return github_com_mwitkow_go_proto_validators.FieldError("Domain", fmt.Errorf(`70001`))
	}
	return nil
}
func (this *InfoResponse) Validate() error {
	if this.Info != nil {
		if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(this.Info); err != nil {
			return github_com_mwitkow_go_proto_validators.FieldError("Info", err)
		}
	}
	return nil
}
func (this *DecryptJwtResponse) Validate() error {
	return nil
}
func (this *DecryptJwtRequest) Validate() error {
	return nil
}
func (this *CheckPwdRequest) Validate() error {
	return nil
}
func (this *AuthenticationRequest) Validate() error {
	if !(len(this.IDNum) == 18) {
		return github_com_mwitkow_go_proto_validators.FieldError("IDNum", fmt.Errorf(`70006`))
	}
	return nil
}
func (this *RequestStatus) Validate() error {
	return nil
}
func (this *RegistRequest) Validate() error {
	if this.Domain == "" {
		return github_com_mwitkow_go_proto_validators.FieldError("Domain", fmt.Errorf(`70001`))
	}
	if this.NickName == "" {
		return github_com_mwitkow_go_proto_validators.FieldError("NickName", fmt.Errorf(`70005`))
	}
	if !(len(this.NickName) < 20) {
		return github_com_mwitkow_go_proto_validators.FieldError("NickName", fmt.Errorf(`70005`))
	}
	if !(len(this.Password) > 5) {
		return github_com_mwitkow_go_proto_validators.FieldError("Password", fmt.Errorf(`70007`))
	}
	if this.Extend != nil {
		if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(this.Extend); err != nil {
			return github_com_mwitkow_go_proto_validators.FieldError("Extend", err)
		}
	}
	if this.Operator != nil {
		if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(this.Operator); err != nil {
			return github_com_mwitkow_go_proto_validators.FieldError("Operator", err)
		}
	}
	if this.Passport != nil {
		if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(this.Passport); err != nil {
			return github_com_mwitkow_go_proto_validators.FieldError("Passport", err)
		}
	}
	if this.UserExtend != nil {
		if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(this.UserExtend); err != nil {
			return github_com_mwitkow_go_proto_validators.FieldError("UserExtend", err)
		}
	}
	return nil
}
func (this *UserExtend) Validate() error {
	return nil
}
func (this *Passport) Validate() error {
	return nil
}
func (this *LoginRequest) Validate() error {
	if this.Domain == "" {
		return github_com_mwitkow_go_proto_validators.FieldError("Domain", fmt.Errorf(`70001`))
	}
	return nil
}
func (this *TokenInfo) Validate() error {
	if this.AccountInfo != nil {
		if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(this.AccountInfo); err != nil {
			return github_com_mwitkow_go_proto_validators.FieldError("AccountInfo", err)
		}
	}
	return nil
}
func (this *Extend) Validate() error {
	return nil
}
func (this *Department) Validate() error {
	return nil
}
func (this *AccountInfo) Validate() error {
	if this.Extend != nil {
		if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(this.Extend); err != nil {
			return github_com_mwitkow_go_proto_validators.FieldError("Extend", err)
		}
	}
	for _, item := range this.Departments {
		if item != nil {
			if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(item); err != nil {
				return github_com_mwitkow_go_proto_validators.FieldError("Departments", err)
			}
		}
	}
	for _, item := range this.Positions {
		if item != nil {
			if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(item); err != nil {
				return github_com_mwitkow_go_proto_validators.FieldError("Positions", err)
			}
		}
	}
	for _, item := range this.Clocks {
		if item != nil {
			if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(item); err != nil {
				return github_com_mwitkow_go_proto_validators.FieldError("Clocks", err)
			}
		}
	}
	for _, item := range this.TrainVideos {
		if item != nil {
			if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(item); err != nil {
				return github_com_mwitkow_go_proto_validators.FieldError("TrainVideos", err)
			}
		}
	}
	if this.Operator != nil {
		if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(this.Operator); err != nil {
			return github_com_mwitkow_go_proto_validators.FieldError("Operator", err)
		}
	}
	if this.UserExtend != nil {
		if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(this.UserExtend); err != nil {
			return github_com_mwitkow_go_proto_validators.FieldError("UserExtend", err)
		}
	}
	return nil
}
func (this *UserInfoV2) Validate() error {
	if this.Extend != nil {
		if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(this.Extend); err != nil {
			return github_com_mwitkow_go_proto_validators.FieldError("Extend", err)
		}
	}
	if this.Operator != nil {
		if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(this.Operator); err != nil {
			return github_com_mwitkow_go_proto_validators.FieldError("Operator", err)
		}
	}
	return nil
}
func (this *RefreshTokenRequest) Validate() error {
	return nil
}
func (this *PositionUser) Validate() error {
	return nil
}
func (this *JobNumGetInfoRequest) Validate() error {
	return nil
}
func (this *CreateClockDeviceRequest) Validate() error {
	return nil
}
func (this *UpdateClockDeviceRequest) Validate() error {
	return nil
}
func (this *ClockDeviceResponse) Validate() error {
	return nil
}
func (this *RemoveClockDeviceRequest) Validate() error {
	return nil
}
func (this *ClockDeviceListRequest) Validate() error {
	return nil
}
func (this *ClockDeviceListResponse) Validate() error {
	for _, item := range this.Data {
		if item != nil {
			if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(item); err != nil {
				return github_com_mwitkow_go_proto_validators.FieldError("Data", err)
			}
		}
	}
	return nil
}
func (this *ClockUser) Validate() error {
	if this.Device != nil {
		if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(this.Device); err != nil {
			return github_com_mwitkow_go_proto_validators.FieldError("Device", err)
		}
	}
	return nil
}
func (this *ClockDeviceInfo) Validate() error {
	for _, item := range this.Data {
		if item != nil {
			if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(item); err != nil {
				return github_com_mwitkow_go_proto_validators.FieldError("Data", err)
			}
		}
	}
	return nil
}
func (this *ClockDeviceInfoResponse) Validate() error {
	for _, item := range this.Data {
		if item != nil {
			if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(item); err != nil {
				return github_com_mwitkow_go_proto_validators.FieldError("Data", err)
			}
		}
	}
	return nil
}
func (this *ClockUserRel) Validate() error {
	return nil
}
func (this *ClockDeviceInfoRequest) Validate() error {
	return nil
}
func (this *ClockBatchBindRequest) Validate() error {
	return nil
}
func (this *ClockBatchListResponse) Validate() error {
	for _, item := range this.Data {
		if item != nil {
			if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(item); err != nil {
				return github_com_mwitkow_go_proto_validators.FieldError("Data", err)
			}
		}
	}
	return nil
}
func (this *ClockUserDeviceBatch) Validate() error {
	return nil
}
func (this *ClockLogInfo) Validate() error {
	return nil
}
func (this *ClockLogReq) Validate() error {
	return nil
}
func (this *ClockLogListResponse) Validate() error {
	for _, item := range this.Data {
		if item != nil {
			if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(item); err != nil {
				return github_com_mwitkow_go_proto_validators.FieldError("Data", err)
			}
		}
	}
	return nil
}
func (this *SendNationMsgRequest) Validate() error {
	if this.Domain == "" {
		return github_com_mwitkow_go_proto_validators.FieldError("Domain", fmt.Errorf(`70001`))
	}
	if this.TelNum == "" {
		return github_com_mwitkow_go_proto_validators.FieldError("TelNum", fmt.Errorf(`70001`))
	}
	return nil
}
func (this *UpdateLanguageRequest) Validate() error {
	if this.Domain == "" {
		return github_com_mwitkow_go_proto_validators.FieldError("Domain", fmt.Errorf(`70001`))
	}
	return nil
}
func (this *UpdateLanguageResponse) Validate() error {
	return nil
}
func (this *GenerateSliderCaptchaRequest) Validate() error {
	return nil
}
func (this *GenerateSliderCaptchaResponse) Validate() error {
	return nil
}
func (this *VerifySliderCaptchaRequest) Validate() error {
	return nil
}
func (this *VerifySliderCaptchaResponse) Validate() error {
	return nil
}
func (this *VerifySliderStatusRequest) Validate() error {
	return nil
}
func (this *VerifySliderStatusResponse) Validate() error {
	return nil
}
func (this *ValidateCodeReq) Validate() error {
	return nil
}
func (this *ValidateCodeResp) Validate() error {
	return nil
}
func (this *SellerCustomerRelation) Validate() error {
	return nil
}
func (this *SellerCustomerRelationsRequest) Validate() error {
	return nil
}
func (this *SellerCustomerRelationsResponse) Validate() error {
	return nil
}
func (this *SellerCustomerRelationsListRequest) Validate() error {
	return nil
}
func (this *SellerCustomerRelationsListResponse) Validate() error {
	for _, item := range this.Data {
		if item != nil {
			if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(item); err != nil {
				return github_com_mwitkow_go_proto_validators.FieldError("Data", err)
			}
		}
	}
	return nil
}
func (this *SellerCustomerCountRequest) Validate() error {
	return nil
}
func (this *SellerCustomerCountResponse) Validate() error {
	for _, item := range this.Customers {
		if item != nil {
			if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(item); err != nil {
				return github_com_mwitkow_go_proto_validators.FieldError("Customers", err)
			}
		}
	}
	return nil
}
func (this *SellerCustomerCountResponse_CustomerCount) Validate() error {
	return nil
}
