// Code generated by protoc-gen-go-triple. DO NOT EDIT.
// versions:
// - protoc-gen-go-triple v1.0.8
// - protoc             v3.21.12
// source: api/account/account.proto

package account

import (
	context "context"
	protocol "dubbo.apache.org/dubbo-go/v3/protocol"
	dubbo3 "dubbo.apache.org/dubbo-go/v3/protocol/dubbo3"
	invocation "dubbo.apache.org/dubbo-go/v3/protocol/invocation"
	grpc_go "github.com/dubbogo/grpc-go"
	codes "github.com/dubbogo/grpc-go/codes"
	metadata "github.com/dubbogo/grpc-go/metadata"
	status "github.com/dubbogo/grpc-go/status"
	common "github.com/dubbogo/triple/pkg/common"
	constant "github.com/dubbogo/triple/pkg/common/constant"
	triple "github.com/dubbogo/triple/pkg/triple"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc_go.SupportPackageIsVersion7

// AccountClient is the client API for Account service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type AccountClient interface {
	Login(ctx context.Context, in *LoginRequest, opts ...grpc_go.CallOption) (*TokenInfo, common.ErrorWithAttachment)
	RefreshToken(ctx context.Context, in *RefreshTokenRequest, opts ...grpc_go.CallOption) (*TokenInfo, common.ErrorWithAttachment)
	Logout(ctx context.Context, in *DecryptJwtRequest, opts ...grpc_go.CallOption) (*CommonResponse, common.ErrorWithAttachment)
	WxApp(ctx context.Context, in *WxAppRequest, opts ...grpc_go.CallOption) (*WxAppResponse, common.ErrorWithAttachment)
	WxUserInfo(ctx context.Context, in *WxUserOrCreateRequest, opts ...grpc_go.CallOption) (*WxUserResponse, common.ErrorWithAttachment)
	WxUserCreate(ctx context.Context, in *WxUserOrCreateRequest, opts ...grpc_go.CallOption) (*WxUserResponse, common.ErrorWithAttachment)
	WxUserUpdate(ctx context.Context, in *WxUserUpdateRequest, opts ...grpc_go.CallOption) (*WxUserResponse, common.ErrorWithAttachment)
	WxGetOpenIdByCode(ctx context.Context, in *WxGetOpenIdByCodeRequest, opts ...grpc_go.CallOption) (*WxGetOpenIdByCodeResponse, common.ErrorWithAttachment)
	WxBoxLogin(ctx context.Context, in *WxGetOpenIdByCodeRequest, opts ...grpc_go.CallOption) (*WxBoxUserInfo, common.ErrorWithAttachment)
	WxBoxUserInfoByOpenId(ctx context.Context, in *WxBoxUserInfoRequest, opts ...grpc_go.CallOption) (*WxBoxUserInfo, common.ErrorWithAttachment)
	WxBoxTelNumByCode(ctx context.Context, in *WxGetOpenIdByCodeRequest, opts ...grpc_go.CallOption) (*WxBoxTelNumByCodeResponse, common.ErrorWithAttachment)
	WxBoxUpdateUser(ctx context.Context, in *WxBoxUserInfo, opts ...grpc_go.CallOption) (*CommonResponse, common.ErrorWithAttachment)
	WxBoxCreateUser(ctx context.Context, in *WxBoxUserInfo, opts ...grpc_go.CallOption) (*CommonResponse, common.ErrorWithAttachment)
	FddCreateUser(ctx context.Context, in *FddCreateUserRequest, opts ...grpc_go.CallOption) (*CommonResponse, common.ErrorWithAttachment)
	FddUpdateUser(ctx context.Context, in *FddCreateUserRequest, opts ...grpc_go.CallOption) (*CommonResponse, common.ErrorWithAttachment)
	FddRemoveUser(ctx context.Context, in *FddRemoveUserRequest, opts ...grpc_go.CallOption) (*CommonResponse, common.ErrorWithAttachment)
	OffLine(ctx context.Context, in *CommonRequest, opts ...grpc_go.CallOption) (*CommonResponse, common.ErrorWithAttachment)
	OnlineLog(ctx context.Context, in *LoginInfosByUserIdRequest, opts ...grpc_go.CallOption) (*LoginLogsResponse, common.ErrorWithAttachment)
	OnlineLogById(ctx context.Context, in *OnlineLogByIdRequest, opts ...grpc_go.CallOption) (*LoginLog, common.ErrorWithAttachment)
	CheckPwd(ctx context.Context, in *CheckPwdRequest, opts ...grpc_go.CallOption) (*UpdateResponse, common.ErrorWithAttachment)
	Register(ctx context.Context, in *RegistRequest, opts ...grpc_go.CallOption) (*RequestStatus, common.ErrorWithAttachment)
	RegisterOrExist(ctx context.Context, in *RegistRequest, opts ...grpc_go.CallOption) (*RequestStatus, common.ErrorWithAttachment)
	SendMsg(ctx context.Context, in *SendMsgRequest, opts ...grpc_go.CallOption) (*SendMsgStatusResponse, common.ErrorWithAttachment)
	SendCustomMsg(ctx context.Context, in *SendCustomMsgRequest, opts ...grpc_go.CallOption) (*SendMsgStatusResponse, common.ErrorWithAttachment)
	SendExCustomMsg(ctx context.Context, in *SendCustomMsgRequest, opts ...grpc_go.CallOption) (*SendMsgStatusResponse, common.ErrorWithAttachment)
	SendMsgRegister(ctx context.Context, in *SendMsgRequest, opts ...grpc_go.CallOption) (*SendMsgStatusResponse, common.ErrorWithAttachment)
	CheckMsg(ctx context.Context, in *CheckMsgRequest, opts ...grpc_go.CallOption) (*SendMsgStatusResponse, common.ErrorWithAttachment)
	SendNewTelNumMsg(ctx context.Context, in *SendNewTelNumMsgRequest, opts ...grpc_go.CallOption) (*SendMsgStatusResponse, common.ErrorWithAttachment)
	UpdateTelNum(ctx context.Context, in *SendNewTelNumMsgRequest, opts ...grpc_go.CallOption) (*SendMsgStatusResponse, common.ErrorWithAttachment)
	Authentication(ctx context.Context, in *AuthenticationRequest, opts ...grpc_go.CallOption) (*RequestStatus, common.ErrorWithAttachment)
	DecryptJwt(ctx context.Context, in *DecryptJwtRequest, opts ...grpc_go.CallOption) (*DecryptJwtResponse, common.ErrorWithAttachment)
	Info(ctx context.Context, in *InfoRequest, opts ...grpc_go.CallOption) (*InfoResponse, common.ErrorWithAttachment)
	JobNumGetInfo(ctx context.Context, in *JobNumGetInfoRequest, opts ...grpc_go.CallOption) (*InfoResponse, common.ErrorWithAttachment)
	List(ctx context.Context, in *ListRequest, opts ...grpc_go.CallOption) (*ListResponse, common.ErrorWithAttachment)
	RandList(ctx context.Context, in *ListRequest, opts ...grpc_go.CallOption) (*ListResponse, common.ErrorWithAttachment)
	ListByIDs(ctx context.Context, in *ListByIDsRequest, opts ...grpc_go.CallOption) (*ListResponse, common.ErrorWithAttachment)
	Remove(ctx context.Context, in *RemoveRequest, opts ...grpc_go.CallOption) (*RemoveResponse, common.ErrorWithAttachment)
	WriteOff(ctx context.Context, in *WriteOffRequest, opts ...grpc_go.CallOption) (*RemoveResponse, common.ErrorWithAttachment)
	WriteOffApp(ctx context.Context, in *RemoveRequest, opts ...grpc_go.CallOption) (*RemoveResponse, common.ErrorWithAttachment)
	WriteOffInfo(ctx context.Context, in *WriteOffApproveRequest, opts ...grpc_go.CallOption) (*WriteOffRequest, common.ErrorWithAttachment)
	FindWriteOffList(ctx context.Context, in *WriteOffListRequest, opts ...grpc_go.CallOption) (*WriteOffListResponse, common.ErrorWithAttachment)
	WriteOffUpdate(ctx context.Context, in *WriteOffApproveRequest, opts ...grpc_go.CallOption) (*RemoveResponse, common.ErrorWithAttachment)
	Update(ctx context.Context, in *UpdateRequest, opts ...grpc_go.CallOption) (*UpdateResponse, common.ErrorWithAttachment)
	PrivacyInfo(ctx context.Context, in *PrivacyInfoRequest, opts ...grpc_go.CallOption) (*AccountInfo, common.ErrorWithAttachment)
	UsersByTel(ctx context.Context, in *UsersByTelRequest, opts ...grpc_go.CallOption) (*ListResponse, common.ErrorWithAttachment)
	UserByTel(ctx context.Context, in *UserByTelRequest, opts ...grpc_go.CallOption) (*InfoResponse, common.ErrorWithAttachment)
	CheckBeforeRegister(ctx context.Context, in *CheckBeforeRegisterRequest, opts ...grpc_go.CallOption) (*CommonResponse, common.ErrorWithAttachment)
	OnlySendMsg(ctx context.Context, in *SendMsgRequest, opts ...grpc_go.CallOption) (*SendMsgStatusResponse, common.ErrorWithAttachment)
	OnlyCheckMsg(ctx context.Context, in *CheckMsgRequest, opts ...grpc_go.CallOption) (*SendMsgStatusResponse, common.ErrorWithAttachment)
	CreateClockDevice(ctx context.Context, in *CreateClockDeviceRequest, opts ...grpc_go.CallOption) (*ClockDeviceResponse, common.ErrorWithAttachment)
	UpdateClockDevice(ctx context.Context, in *UpdateClockDeviceRequest, opts ...grpc_go.CallOption) (*ClockDeviceResponse, common.ErrorWithAttachment)
	RemoveClockDevice(ctx context.Context, in *RemoveClockDeviceRequest, opts ...grpc_go.CallOption) (*ClockDeviceResponse, common.ErrorWithAttachment)
	ClockDeviceList(ctx context.Context, in *ClockDeviceListRequest, opts ...grpc_go.CallOption) (*ClockDeviceListResponse, common.ErrorWithAttachment)
	ClockDeviceInfo(ctx context.Context, in *ClockDeviceInfoRequest, opts ...grpc_go.CallOption) (*ClockDeviceInfoResponse, common.ErrorWithAttachment)
	ClockDeviceSingleUntie(ctx context.Context, in *RemoveClockDeviceRequest, opts ...grpc_go.CallOption) (*ClockDeviceResponse, common.ErrorWithAttachment)
	ClockDeviceBatchBind(ctx context.Context, in *ClockBatchListResponse, opts ...grpc_go.CallOption) (*ClockDeviceInfoResponse, common.ErrorWithAttachment)
	ClockDeviceBatchUntie(ctx context.Context, in *ClockBatchBindRequest, opts ...grpc_go.CallOption) (*ClockDeviceInfoResponse, common.ErrorWithAttachment)
	ClockDeviceBatchList(ctx context.Context, in *ClockBatchBindRequest, opts ...grpc_go.CallOption) (*ClockBatchListResponse, common.ErrorWithAttachment)
	UpdateDeviceRelevance(ctx context.Context, in *ClockUserDeviceBatch, opts ...grpc_go.CallOption) (*ClockDeviceResponse, common.ErrorWithAttachment)
	MailAccountByNickName(ctx context.Context, in *MailAccountByNickNameRequest, opts ...grpc_go.CallOption) (*MaiAccountResponse, common.ErrorWithAttachment)
	CreateMaiAccount(ctx context.Context, in *CreateMaiAccountRequest, opts ...grpc_go.CallOption) (*CommonResponse, common.ErrorWithAttachment)
	CreateClockLog(ctx context.Context, in *ClockLogInfo, opts ...grpc_go.CallOption) (*ClockDeviceResponse, common.ErrorWithAttachment)
	SendClockInWechat(ctx context.Context, in *SendClockInWechatRequest, opts ...grpc_go.CallOption) (*CommonResponse, common.ErrorWithAttachment)
	FindClockLogList(ctx context.Context, in *ClockLogReq, opts ...grpc_go.CallOption) (*ClockLogListResponse, common.ErrorWithAttachment)
	SendStrangerClockInWechat(ctx context.Context, in *SendClockInWechatRequest, opts ...grpc_go.CallOption) (*CommonResponse, common.ErrorWithAttachment)
	ListV2(ctx context.Context, in *ListV2Request, opts ...grpc_go.CallOption) (*ListResponse, common.ErrorWithAttachment)
	CreateChainAccount(ctx context.Context, in *CommonRequest, opts ...grpc_go.CallOption) (*CreateChainAccountResponse, common.ErrorWithAttachment)
	SendNationMsg(ctx context.Context, in *SendNationMsgRequest, opts ...grpc_go.CallOption) (*SendMsgStatusResponse, common.ErrorWithAttachment)
	UpdateLanguage(ctx context.Context, in *UpdateLanguageRequest, opts ...grpc_go.CallOption) (*UpdateLanguageResponse, common.ErrorWithAttachment)
	GenerateSliderCaptcha(ctx context.Context, in *GenerateSliderCaptchaRequest, opts ...grpc_go.CallOption) (*GenerateSliderCaptchaResponse, common.ErrorWithAttachment)
	VerifySliderCaptcha(ctx context.Context, in *VerifySliderCaptchaRequest, opts ...grpc_go.CallOption) (*VerifySliderCaptchaResponse, common.ErrorWithAttachment)
	VerifySliderStatus(ctx context.Context, in *VerifySliderStatusRequest, opts ...grpc_go.CallOption) (*VerifySliderStatusResponse, common.ErrorWithAttachment)
	SampleAccount(ctx context.Context, in *SampleAccountRequest, opts ...grpc_go.CallOption) (*SampleAccountResponse, common.ErrorWithAttachment)
	LoginAndSqueezeOther(ctx context.Context, in *LoginRequest, opts ...grpc_go.CallOption) (*TokenInfo, common.ErrorWithAttachment)
	QueryPersonnelWithTheSameName(ctx context.Context, in *QueryPersonnelWithTheSameNameRequest, opts ...grpc_go.CallOption) (*QueryPersonnelWithTheSameNameResponse, common.ErrorWithAttachment)
	UsersByJobNum(ctx context.Context, in *UsersByJobNumRequest, opts ...grpc_go.CallOption) (*ListResponse, common.ErrorWithAttachment)
	//人脸检测
	IsSamePerson(ctx context.Context, in *IsSamePersonRequest, opts ...grpc_go.CallOption) (*IsSamePersonResponse, common.ErrorWithAttachment)
	CreateRealNameOrPassPort(ctx context.Context, in *UserInfo, opts ...grpc_go.CallOption) (*CommonResponse, common.ErrorWithAttachment)
	FddCreateUserV2(ctx context.Context, in *FddCreateUserRequest, opts ...grpc_go.CallOption) (*CommonResponse, common.ErrorWithAttachment)
	FddUserFindByUserId(ctx context.Context, in *UserInfo, opts ...grpc_go.CallOption) (*FddInfo, common.ErrorWithAttachment)
	UserInfoById(ctx context.Context, in *InfoRequest, opts ...grpc_go.CallOption) (*UserInfo, common.ErrorWithAttachment)
	//校验短信二维码
	ValidateCode(ctx context.Context, in *ValidateCodeReq, opts ...grpc_go.CallOption) (*ValidateCodeResp, common.ErrorWithAttachment)
	SaveSubmitInfo(ctx context.Context, in *SubmitInfoRequest, opts ...grpc_go.CallOption) (*CommonResponse, common.ErrorWithAttachment)
	SmsLog(ctx context.Context, in *SmsLogReq, opts ...grpc_go.CallOption) (*SmsLogRes, common.ErrorWithAttachment)
	//认证审核
	ReviewRealName(ctx context.Context, in *ReviewRealNameRequest, opts ...grpc_go.CallOption) (*CommonResponse, common.ErrorWithAttachment)
	ReviewRealNameList(ctx context.Context, in *ReviewRealNameListRequest, opts ...grpc_go.CallOption) (*ReviewRealNameListResponse, common.ErrorWithAttachment)
	//风和商城以及销售宝部分
	BindStaff(ctx context.Context, in *BindStaffRequest, opts ...grpc_go.CallOption) (*CommonResponse, common.ErrorWithAttachment)
	// seller 销售宝 客户和销售关联
	SellerCustomerRelations(ctx context.Context, in *SellerCustomerRelationsRequest, opts ...grpc_go.CallOption) (*SellerCustomerRelationsResponse, common.ErrorWithAttachment)
	SellerCustomerRelationsList(ctx context.Context, in *SellerCustomerRelationsListRequest, opts ...grpc_go.CallOption) (*SellerCustomerRelationsListResponse, common.ErrorWithAttachment)
	SellerCustomerCount(ctx context.Context, in *SellerCustomerCountRequest, opts ...grpc_go.CallOption) (*SellerCustomerCountResponse, common.ErrorWithAttachment)
}

type accountClient struct {
	cc *triple.TripleConn
}

type AccountClientImpl struct {
	Login                         func(ctx context.Context, in *LoginRequest) (*TokenInfo, error)
	RefreshToken                  func(ctx context.Context, in *RefreshTokenRequest) (*TokenInfo, error)
	Logout                        func(ctx context.Context, in *DecryptJwtRequest) (*CommonResponse, error)
	WxApp                         func(ctx context.Context, in *WxAppRequest) (*WxAppResponse, error)
	WxUserInfo                    func(ctx context.Context, in *WxUserOrCreateRequest) (*WxUserResponse, error)
	WxUserCreate                  func(ctx context.Context, in *WxUserOrCreateRequest) (*WxUserResponse, error)
	WxUserUpdate                  func(ctx context.Context, in *WxUserUpdateRequest) (*WxUserResponse, error)
	WxGetOpenIdByCode             func(ctx context.Context, in *WxGetOpenIdByCodeRequest) (*WxGetOpenIdByCodeResponse, error)
	WxBoxLogin                    func(ctx context.Context, in *WxGetOpenIdByCodeRequest) (*WxBoxUserInfo, error)
	WxBoxUserInfoByOpenId         func(ctx context.Context, in *WxBoxUserInfoRequest) (*WxBoxUserInfo, error)
	WxBoxTelNumByCode             func(ctx context.Context, in *WxGetOpenIdByCodeRequest) (*WxBoxTelNumByCodeResponse, error)
	WxBoxUpdateUser               func(ctx context.Context, in *WxBoxUserInfo) (*CommonResponse, error)
	WxBoxCreateUser               func(ctx context.Context, in *WxBoxUserInfo) (*CommonResponse, error)
	FddCreateUser                 func(ctx context.Context, in *FddCreateUserRequest) (*CommonResponse, error)
	FddUpdateUser                 func(ctx context.Context, in *FddCreateUserRequest) (*CommonResponse, error)
	FddRemoveUser                 func(ctx context.Context, in *FddRemoveUserRequest) (*CommonResponse, error)
	OffLine                       func(ctx context.Context, in *CommonRequest) (*CommonResponse, error)
	OnlineLog                     func(ctx context.Context, in *LoginInfosByUserIdRequest) (*LoginLogsResponse, error)
	OnlineLogById                 func(ctx context.Context, in *OnlineLogByIdRequest) (*LoginLog, error)
	CheckPwd                      func(ctx context.Context, in *CheckPwdRequest) (*UpdateResponse, error)
	Register                      func(ctx context.Context, in *RegistRequest) (*RequestStatus, error)
	RegisterOrExist               func(ctx context.Context, in *RegistRequest) (*RequestStatus, error)
	SendMsg                       func(ctx context.Context, in *SendMsgRequest) (*SendMsgStatusResponse, error)
	SendCustomMsg                 func(ctx context.Context, in *SendCustomMsgRequest) (*SendMsgStatusResponse, error)
	SendExCustomMsg               func(ctx context.Context, in *SendCustomMsgRequest) (*SendMsgStatusResponse, error)
	SendMsgRegister               func(ctx context.Context, in *SendMsgRequest) (*SendMsgStatusResponse, error)
	CheckMsg                      func(ctx context.Context, in *CheckMsgRequest) (*SendMsgStatusResponse, error)
	SendNewTelNumMsg              func(ctx context.Context, in *SendNewTelNumMsgRequest) (*SendMsgStatusResponse, error)
	UpdateTelNum                  func(ctx context.Context, in *SendNewTelNumMsgRequest) (*SendMsgStatusResponse, error)
	Authentication                func(ctx context.Context, in *AuthenticationRequest) (*RequestStatus, error)
	DecryptJwt                    func(ctx context.Context, in *DecryptJwtRequest) (*DecryptJwtResponse, error)
	Info                          func(ctx context.Context, in *InfoRequest) (*InfoResponse, error)
	JobNumGetInfo                 func(ctx context.Context, in *JobNumGetInfoRequest) (*InfoResponse, error)
	List                          func(ctx context.Context, in *ListRequest) (*ListResponse, error)
	RandList                      func(ctx context.Context, in *ListRequest) (*ListResponse, error)
	ListByIDs                     func(ctx context.Context, in *ListByIDsRequest) (*ListResponse, error)
	Remove                        func(ctx context.Context, in *RemoveRequest) (*RemoveResponse, error)
	WriteOff                      func(ctx context.Context, in *WriteOffRequest) (*RemoveResponse, error)
	WriteOffApp                   func(ctx context.Context, in *RemoveRequest) (*RemoveResponse, error)
	WriteOffInfo                  func(ctx context.Context, in *WriteOffApproveRequest) (*WriteOffRequest, error)
	FindWriteOffList              func(ctx context.Context, in *WriteOffListRequest) (*WriteOffListResponse, error)
	WriteOffUpdate                func(ctx context.Context, in *WriteOffApproveRequest) (*RemoveResponse, error)
	Update                        func(ctx context.Context, in *UpdateRequest) (*UpdateResponse, error)
	PrivacyInfo                   func(ctx context.Context, in *PrivacyInfoRequest) (*AccountInfo, error)
	UsersByTel                    func(ctx context.Context, in *UsersByTelRequest) (*ListResponse, error)
	UserByTel                     func(ctx context.Context, in *UserByTelRequest) (*InfoResponse, error)
	CheckBeforeRegister           func(ctx context.Context, in *CheckBeforeRegisterRequest) (*CommonResponse, error)
	OnlySendMsg                   func(ctx context.Context, in *SendMsgRequest) (*SendMsgStatusResponse, error)
	OnlyCheckMsg                  func(ctx context.Context, in *CheckMsgRequest) (*SendMsgStatusResponse, error)
	CreateClockDevice             func(ctx context.Context, in *CreateClockDeviceRequest) (*ClockDeviceResponse, error)
	UpdateClockDevice             func(ctx context.Context, in *UpdateClockDeviceRequest) (*ClockDeviceResponse, error)
	RemoveClockDevice             func(ctx context.Context, in *RemoveClockDeviceRequest) (*ClockDeviceResponse, error)
	ClockDeviceList               func(ctx context.Context, in *ClockDeviceListRequest) (*ClockDeviceListResponse, error)
	ClockDeviceInfo               func(ctx context.Context, in *ClockDeviceInfoRequest) (*ClockDeviceInfoResponse, error)
	ClockDeviceSingleUntie        func(ctx context.Context, in *RemoveClockDeviceRequest) (*ClockDeviceResponse, error)
	ClockDeviceBatchBind          func(ctx context.Context, in *ClockBatchListResponse) (*ClockDeviceInfoResponse, error)
	ClockDeviceBatchUntie         func(ctx context.Context, in *ClockBatchBindRequest) (*ClockDeviceInfoResponse, error)
	ClockDeviceBatchList          func(ctx context.Context, in *ClockBatchBindRequest) (*ClockBatchListResponse, error)
	UpdateDeviceRelevance         func(ctx context.Context, in *ClockUserDeviceBatch) (*ClockDeviceResponse, error)
	MailAccountByNickName         func(ctx context.Context, in *MailAccountByNickNameRequest) (*MaiAccountResponse, error)
	CreateMaiAccount              func(ctx context.Context, in *CreateMaiAccountRequest) (*CommonResponse, error)
	CreateClockLog                func(ctx context.Context, in *ClockLogInfo) (*ClockDeviceResponse, error)
	SendClockInWechat             func(ctx context.Context, in *SendClockInWechatRequest) (*CommonResponse, error)
	FindClockLogList              func(ctx context.Context, in *ClockLogReq) (*ClockLogListResponse, error)
	SendStrangerClockInWechat     func(ctx context.Context, in *SendClockInWechatRequest) (*CommonResponse, error)
	ListV2                        func(ctx context.Context, in *ListV2Request) (*ListResponse, error)
	CreateChainAccount            func(ctx context.Context, in *CommonRequest) (*CreateChainAccountResponse, error)
	SendNationMsg                 func(ctx context.Context, in *SendNationMsgRequest) (*SendMsgStatusResponse, error)
	UpdateLanguage                func(ctx context.Context, in *UpdateLanguageRequest) (*UpdateLanguageResponse, error)
	GenerateSliderCaptcha         func(ctx context.Context, in *GenerateSliderCaptchaRequest) (*GenerateSliderCaptchaResponse, error)
	VerifySliderCaptcha           func(ctx context.Context, in *VerifySliderCaptchaRequest) (*VerifySliderCaptchaResponse, error)
	VerifySliderStatus            func(ctx context.Context, in *VerifySliderStatusRequest) (*VerifySliderStatusResponse, error)
	SampleAccount                 func(ctx context.Context, in *SampleAccountRequest) (*SampleAccountResponse, error)
	LoginAndSqueezeOther          func(ctx context.Context, in *LoginRequest) (*TokenInfo, error)
	QueryPersonnelWithTheSameName func(ctx context.Context, in *QueryPersonnelWithTheSameNameRequest) (*QueryPersonnelWithTheSameNameResponse, error)
	UsersByJobNum                 func(ctx context.Context, in *UsersByJobNumRequest) (*ListResponse, error)
	IsSamePerson                  func(ctx context.Context, in *IsSamePersonRequest) (*IsSamePersonResponse, error)
	CreateRealNameOrPassPort      func(ctx context.Context, in *UserInfo) (*CommonResponse, error)
	FddCreateUserV2               func(ctx context.Context, in *FddCreateUserRequest) (*CommonResponse, error)
	FddUserFindByUserId           func(ctx context.Context, in *UserInfo) (*FddInfo, error)
	UserInfoById                  func(ctx context.Context, in *InfoRequest) (*UserInfo, error)
	ValidateCode                  func(ctx context.Context, in *ValidateCodeReq) (*ValidateCodeResp, error)
	SaveSubmitInfo                func(ctx context.Context, in *SubmitInfoRequest) (*CommonResponse, error)
	SmsLog                        func(ctx context.Context, in *SmsLogReq) (*SmsLogRes, error)
	ReviewRealName                func(ctx context.Context, in *ReviewRealNameRequest) (*CommonResponse, error)
	ReviewRealNameList            func(ctx context.Context, in *ReviewRealNameListRequest) (*ReviewRealNameListResponse, error)
	BindStaff                     func(ctx context.Context, in *BindStaffRequest) (*CommonResponse, error)
	SellerCustomerRelations       func(ctx context.Context, in *SellerCustomerRelationsRequest) (*SellerCustomerRelationsResponse, error)
	SellerCustomerRelationsList   func(ctx context.Context, in *SellerCustomerRelationsListRequest) (*SellerCustomerRelationsListResponse, error)
	SellerCustomerCount           func(ctx context.Context, in *SellerCustomerCountRequest) (*SellerCustomerCountResponse, error)
}

func (c *AccountClientImpl) GetDubboStub(cc *triple.TripleConn) AccountClient {
	return NewAccountClient(cc)
}

func (c *AccountClientImpl) XXX_InterfaceName() string {
	return "account.Account"
}

func NewAccountClient(cc *triple.TripleConn) AccountClient {
	return &accountClient{cc}
}

func (c *accountClient) Login(ctx context.Context, in *LoginRequest, opts ...grpc_go.CallOption) (*TokenInfo, common.ErrorWithAttachment) {
	out := new(TokenInfo)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/Login", in, out)
}

func (c *accountClient) RefreshToken(ctx context.Context, in *RefreshTokenRequest, opts ...grpc_go.CallOption) (*TokenInfo, common.ErrorWithAttachment) {
	out := new(TokenInfo)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/RefreshToken", in, out)
}

func (c *accountClient) Logout(ctx context.Context, in *DecryptJwtRequest, opts ...grpc_go.CallOption) (*CommonResponse, common.ErrorWithAttachment) {
	out := new(CommonResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/Logout", in, out)
}

func (c *accountClient) WxApp(ctx context.Context, in *WxAppRequest, opts ...grpc_go.CallOption) (*WxAppResponse, common.ErrorWithAttachment) {
	out := new(WxAppResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/WxApp", in, out)
}

func (c *accountClient) WxUserInfo(ctx context.Context, in *WxUserOrCreateRequest, opts ...grpc_go.CallOption) (*WxUserResponse, common.ErrorWithAttachment) {
	out := new(WxUserResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/WxUserInfo", in, out)
}

func (c *accountClient) WxUserCreate(ctx context.Context, in *WxUserOrCreateRequest, opts ...grpc_go.CallOption) (*WxUserResponse, common.ErrorWithAttachment) {
	out := new(WxUserResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/WxUserCreate", in, out)
}

func (c *accountClient) WxUserUpdate(ctx context.Context, in *WxUserUpdateRequest, opts ...grpc_go.CallOption) (*WxUserResponse, common.ErrorWithAttachment) {
	out := new(WxUserResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/WxUserUpdate", in, out)
}

func (c *accountClient) WxGetOpenIdByCode(ctx context.Context, in *WxGetOpenIdByCodeRequest, opts ...grpc_go.CallOption) (*WxGetOpenIdByCodeResponse, common.ErrorWithAttachment) {
	out := new(WxGetOpenIdByCodeResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/WxGetOpenIdByCode", in, out)
}

func (c *accountClient) WxBoxLogin(ctx context.Context, in *WxGetOpenIdByCodeRequest, opts ...grpc_go.CallOption) (*WxBoxUserInfo, common.ErrorWithAttachment) {
	out := new(WxBoxUserInfo)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/WxBoxLogin", in, out)
}

func (c *accountClient) WxBoxUserInfoByOpenId(ctx context.Context, in *WxBoxUserInfoRequest, opts ...grpc_go.CallOption) (*WxBoxUserInfo, common.ErrorWithAttachment) {
	out := new(WxBoxUserInfo)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/WxBoxUserInfoByOpenId", in, out)
}

func (c *accountClient) WxBoxTelNumByCode(ctx context.Context, in *WxGetOpenIdByCodeRequest, opts ...grpc_go.CallOption) (*WxBoxTelNumByCodeResponse, common.ErrorWithAttachment) {
	out := new(WxBoxTelNumByCodeResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/WxBoxTelNumByCode", in, out)
}

func (c *accountClient) WxBoxUpdateUser(ctx context.Context, in *WxBoxUserInfo, opts ...grpc_go.CallOption) (*CommonResponse, common.ErrorWithAttachment) {
	out := new(CommonResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/WxBoxUpdateUser", in, out)
}

func (c *accountClient) WxBoxCreateUser(ctx context.Context, in *WxBoxUserInfo, opts ...grpc_go.CallOption) (*CommonResponse, common.ErrorWithAttachment) {
	out := new(CommonResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/WxBoxCreateUser", in, out)
}

func (c *accountClient) FddCreateUser(ctx context.Context, in *FddCreateUserRequest, opts ...grpc_go.CallOption) (*CommonResponse, common.ErrorWithAttachment) {
	out := new(CommonResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/FddCreateUser", in, out)
}

func (c *accountClient) FddUpdateUser(ctx context.Context, in *FddCreateUserRequest, opts ...grpc_go.CallOption) (*CommonResponse, common.ErrorWithAttachment) {
	out := new(CommonResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/FddUpdateUser", in, out)
}

func (c *accountClient) FddRemoveUser(ctx context.Context, in *FddRemoveUserRequest, opts ...grpc_go.CallOption) (*CommonResponse, common.ErrorWithAttachment) {
	out := new(CommonResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/FddRemoveUser", in, out)
}

func (c *accountClient) OffLine(ctx context.Context, in *CommonRequest, opts ...grpc_go.CallOption) (*CommonResponse, common.ErrorWithAttachment) {
	out := new(CommonResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/OffLine", in, out)
}

func (c *accountClient) OnlineLog(ctx context.Context, in *LoginInfosByUserIdRequest, opts ...grpc_go.CallOption) (*LoginLogsResponse, common.ErrorWithAttachment) {
	out := new(LoginLogsResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/OnlineLog", in, out)
}

func (c *accountClient) OnlineLogById(ctx context.Context, in *OnlineLogByIdRequest, opts ...grpc_go.CallOption) (*LoginLog, common.ErrorWithAttachment) {
	out := new(LoginLog)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/OnlineLogById", in, out)
}

func (c *accountClient) CheckPwd(ctx context.Context, in *CheckPwdRequest, opts ...grpc_go.CallOption) (*UpdateResponse, common.ErrorWithAttachment) {
	out := new(UpdateResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/CheckPwd", in, out)
}

func (c *accountClient) Register(ctx context.Context, in *RegistRequest, opts ...grpc_go.CallOption) (*RequestStatus, common.ErrorWithAttachment) {
	out := new(RequestStatus)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/Register", in, out)
}

func (c *accountClient) RegisterOrExist(ctx context.Context, in *RegistRequest, opts ...grpc_go.CallOption) (*RequestStatus, common.ErrorWithAttachment) {
	out := new(RequestStatus)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/RegisterOrExist", in, out)
}

func (c *accountClient) SendMsg(ctx context.Context, in *SendMsgRequest, opts ...grpc_go.CallOption) (*SendMsgStatusResponse, common.ErrorWithAttachment) {
	out := new(SendMsgStatusResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/SendMsg", in, out)
}

func (c *accountClient) SendCustomMsg(ctx context.Context, in *SendCustomMsgRequest, opts ...grpc_go.CallOption) (*SendMsgStatusResponse, common.ErrorWithAttachment) {
	out := new(SendMsgStatusResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/SendCustomMsg", in, out)
}

func (c *accountClient) SendExCustomMsg(ctx context.Context, in *SendCustomMsgRequest, opts ...grpc_go.CallOption) (*SendMsgStatusResponse, common.ErrorWithAttachment) {
	out := new(SendMsgStatusResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/SendExCustomMsg", in, out)
}

func (c *accountClient) SendMsgRegister(ctx context.Context, in *SendMsgRequest, opts ...grpc_go.CallOption) (*SendMsgStatusResponse, common.ErrorWithAttachment) {
	out := new(SendMsgStatusResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/SendMsgRegister", in, out)
}

func (c *accountClient) CheckMsg(ctx context.Context, in *CheckMsgRequest, opts ...grpc_go.CallOption) (*SendMsgStatusResponse, common.ErrorWithAttachment) {
	out := new(SendMsgStatusResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/CheckMsg", in, out)
}

func (c *accountClient) SendNewTelNumMsg(ctx context.Context, in *SendNewTelNumMsgRequest, opts ...grpc_go.CallOption) (*SendMsgStatusResponse, common.ErrorWithAttachment) {
	out := new(SendMsgStatusResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/SendNewTelNumMsg", in, out)
}

func (c *accountClient) UpdateTelNum(ctx context.Context, in *SendNewTelNumMsgRequest, opts ...grpc_go.CallOption) (*SendMsgStatusResponse, common.ErrorWithAttachment) {
	out := new(SendMsgStatusResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/UpdateTelNum", in, out)
}

func (c *accountClient) Authentication(ctx context.Context, in *AuthenticationRequest, opts ...grpc_go.CallOption) (*RequestStatus, common.ErrorWithAttachment) {
	out := new(RequestStatus)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/Authentication", in, out)
}

func (c *accountClient) DecryptJwt(ctx context.Context, in *DecryptJwtRequest, opts ...grpc_go.CallOption) (*DecryptJwtResponse, common.ErrorWithAttachment) {
	out := new(DecryptJwtResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/DecryptJwt", in, out)
}

func (c *accountClient) Info(ctx context.Context, in *InfoRequest, opts ...grpc_go.CallOption) (*InfoResponse, common.ErrorWithAttachment) {
	out := new(InfoResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/Info", in, out)
}

func (c *accountClient) JobNumGetInfo(ctx context.Context, in *JobNumGetInfoRequest, opts ...grpc_go.CallOption) (*InfoResponse, common.ErrorWithAttachment) {
	out := new(InfoResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/JobNumGetInfo", in, out)
}

func (c *accountClient) List(ctx context.Context, in *ListRequest, opts ...grpc_go.CallOption) (*ListResponse, common.ErrorWithAttachment) {
	out := new(ListResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/List", in, out)
}

func (c *accountClient) RandList(ctx context.Context, in *ListRequest, opts ...grpc_go.CallOption) (*ListResponse, common.ErrorWithAttachment) {
	out := new(ListResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/RandList", in, out)
}

func (c *accountClient) ListByIDs(ctx context.Context, in *ListByIDsRequest, opts ...grpc_go.CallOption) (*ListResponse, common.ErrorWithAttachment) {
	out := new(ListResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/ListByIDs", in, out)
}

func (c *accountClient) Remove(ctx context.Context, in *RemoveRequest, opts ...grpc_go.CallOption) (*RemoveResponse, common.ErrorWithAttachment) {
	out := new(RemoveResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/Remove", in, out)
}

func (c *accountClient) WriteOff(ctx context.Context, in *WriteOffRequest, opts ...grpc_go.CallOption) (*RemoveResponse, common.ErrorWithAttachment) {
	out := new(RemoveResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/WriteOff", in, out)
}

func (c *accountClient) WriteOffApp(ctx context.Context, in *RemoveRequest, opts ...grpc_go.CallOption) (*RemoveResponse, common.ErrorWithAttachment) {
	out := new(RemoveResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/WriteOffApp", in, out)
}

func (c *accountClient) WriteOffInfo(ctx context.Context, in *WriteOffApproveRequest, opts ...grpc_go.CallOption) (*WriteOffRequest, common.ErrorWithAttachment) {
	out := new(WriteOffRequest)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/WriteOffInfo", in, out)
}

func (c *accountClient) FindWriteOffList(ctx context.Context, in *WriteOffListRequest, opts ...grpc_go.CallOption) (*WriteOffListResponse, common.ErrorWithAttachment) {
	out := new(WriteOffListResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/FindWriteOffList", in, out)
}

func (c *accountClient) WriteOffUpdate(ctx context.Context, in *WriteOffApproveRequest, opts ...grpc_go.CallOption) (*RemoveResponse, common.ErrorWithAttachment) {
	out := new(RemoveResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/WriteOffUpdate", in, out)
}

func (c *accountClient) Update(ctx context.Context, in *UpdateRequest, opts ...grpc_go.CallOption) (*UpdateResponse, common.ErrorWithAttachment) {
	out := new(UpdateResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/Update", in, out)
}

func (c *accountClient) PrivacyInfo(ctx context.Context, in *PrivacyInfoRequest, opts ...grpc_go.CallOption) (*AccountInfo, common.ErrorWithAttachment) {
	out := new(AccountInfo)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/PrivacyInfo", in, out)
}

func (c *accountClient) UsersByTel(ctx context.Context, in *UsersByTelRequest, opts ...grpc_go.CallOption) (*ListResponse, common.ErrorWithAttachment) {
	out := new(ListResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/UsersByTel", in, out)
}

func (c *accountClient) UserByTel(ctx context.Context, in *UserByTelRequest, opts ...grpc_go.CallOption) (*InfoResponse, common.ErrorWithAttachment) {
	out := new(InfoResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/UserByTel", in, out)
}

func (c *accountClient) CheckBeforeRegister(ctx context.Context, in *CheckBeforeRegisterRequest, opts ...grpc_go.CallOption) (*CommonResponse, common.ErrorWithAttachment) {
	out := new(CommonResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/CheckBeforeRegister", in, out)
}

func (c *accountClient) OnlySendMsg(ctx context.Context, in *SendMsgRequest, opts ...grpc_go.CallOption) (*SendMsgStatusResponse, common.ErrorWithAttachment) {
	out := new(SendMsgStatusResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/OnlySendMsg", in, out)
}

func (c *accountClient) OnlyCheckMsg(ctx context.Context, in *CheckMsgRequest, opts ...grpc_go.CallOption) (*SendMsgStatusResponse, common.ErrorWithAttachment) {
	out := new(SendMsgStatusResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/OnlyCheckMsg", in, out)
}

func (c *accountClient) CreateClockDevice(ctx context.Context, in *CreateClockDeviceRequest, opts ...grpc_go.CallOption) (*ClockDeviceResponse, common.ErrorWithAttachment) {
	out := new(ClockDeviceResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/CreateClockDevice", in, out)
}

func (c *accountClient) UpdateClockDevice(ctx context.Context, in *UpdateClockDeviceRequest, opts ...grpc_go.CallOption) (*ClockDeviceResponse, common.ErrorWithAttachment) {
	out := new(ClockDeviceResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/UpdateClockDevice", in, out)
}

func (c *accountClient) RemoveClockDevice(ctx context.Context, in *RemoveClockDeviceRequest, opts ...grpc_go.CallOption) (*ClockDeviceResponse, common.ErrorWithAttachment) {
	out := new(ClockDeviceResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/RemoveClockDevice", in, out)
}

func (c *accountClient) ClockDeviceList(ctx context.Context, in *ClockDeviceListRequest, opts ...grpc_go.CallOption) (*ClockDeviceListResponse, common.ErrorWithAttachment) {
	out := new(ClockDeviceListResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/ClockDeviceList", in, out)
}

func (c *accountClient) ClockDeviceInfo(ctx context.Context, in *ClockDeviceInfoRequest, opts ...grpc_go.CallOption) (*ClockDeviceInfoResponse, common.ErrorWithAttachment) {
	out := new(ClockDeviceInfoResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/ClockDeviceInfo", in, out)
}

func (c *accountClient) ClockDeviceSingleUntie(ctx context.Context, in *RemoveClockDeviceRequest, opts ...grpc_go.CallOption) (*ClockDeviceResponse, common.ErrorWithAttachment) {
	out := new(ClockDeviceResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/ClockDeviceSingleUntie", in, out)
}

func (c *accountClient) ClockDeviceBatchBind(ctx context.Context, in *ClockBatchListResponse, opts ...grpc_go.CallOption) (*ClockDeviceInfoResponse, common.ErrorWithAttachment) {
	out := new(ClockDeviceInfoResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/ClockDeviceBatchBind", in, out)
}

func (c *accountClient) ClockDeviceBatchUntie(ctx context.Context, in *ClockBatchBindRequest, opts ...grpc_go.CallOption) (*ClockDeviceInfoResponse, common.ErrorWithAttachment) {
	out := new(ClockDeviceInfoResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/ClockDeviceBatchUntie", in, out)
}

func (c *accountClient) ClockDeviceBatchList(ctx context.Context, in *ClockBatchBindRequest, opts ...grpc_go.CallOption) (*ClockBatchListResponse, common.ErrorWithAttachment) {
	out := new(ClockBatchListResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/ClockDeviceBatchList", in, out)
}

func (c *accountClient) UpdateDeviceRelevance(ctx context.Context, in *ClockUserDeviceBatch, opts ...grpc_go.CallOption) (*ClockDeviceResponse, common.ErrorWithAttachment) {
	out := new(ClockDeviceResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/UpdateDeviceRelevance", in, out)
}

func (c *accountClient) MailAccountByNickName(ctx context.Context, in *MailAccountByNickNameRequest, opts ...grpc_go.CallOption) (*MaiAccountResponse, common.ErrorWithAttachment) {
	out := new(MaiAccountResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/MailAccountByNickName", in, out)
}

func (c *accountClient) CreateMaiAccount(ctx context.Context, in *CreateMaiAccountRequest, opts ...grpc_go.CallOption) (*CommonResponse, common.ErrorWithAttachment) {
	out := new(CommonResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/CreateMaiAccount", in, out)
}

func (c *accountClient) CreateClockLog(ctx context.Context, in *ClockLogInfo, opts ...grpc_go.CallOption) (*ClockDeviceResponse, common.ErrorWithAttachment) {
	out := new(ClockDeviceResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/CreateClockLog", in, out)
}

func (c *accountClient) SendClockInWechat(ctx context.Context, in *SendClockInWechatRequest, opts ...grpc_go.CallOption) (*CommonResponse, common.ErrorWithAttachment) {
	out := new(CommonResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/SendClockInWechat", in, out)
}

func (c *accountClient) FindClockLogList(ctx context.Context, in *ClockLogReq, opts ...grpc_go.CallOption) (*ClockLogListResponse, common.ErrorWithAttachment) {
	out := new(ClockLogListResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/FindClockLogList", in, out)
}

func (c *accountClient) SendStrangerClockInWechat(ctx context.Context, in *SendClockInWechatRequest, opts ...grpc_go.CallOption) (*CommonResponse, common.ErrorWithAttachment) {
	out := new(CommonResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/SendStrangerClockInWechat", in, out)
}

func (c *accountClient) ListV2(ctx context.Context, in *ListV2Request, opts ...grpc_go.CallOption) (*ListResponse, common.ErrorWithAttachment) {
	out := new(ListResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/ListV2", in, out)
}

func (c *accountClient) CreateChainAccount(ctx context.Context, in *CommonRequest, opts ...grpc_go.CallOption) (*CreateChainAccountResponse, common.ErrorWithAttachment) {
	out := new(CreateChainAccountResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/CreateChainAccount", in, out)
}

func (c *accountClient) SendNationMsg(ctx context.Context, in *SendNationMsgRequest, opts ...grpc_go.CallOption) (*SendMsgStatusResponse, common.ErrorWithAttachment) {
	out := new(SendMsgStatusResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/SendNationMsg", in, out)
}

func (c *accountClient) UpdateLanguage(ctx context.Context, in *UpdateLanguageRequest, opts ...grpc_go.CallOption) (*UpdateLanguageResponse, common.ErrorWithAttachment) {
	out := new(UpdateLanguageResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/UpdateLanguage", in, out)
}

func (c *accountClient) GenerateSliderCaptcha(ctx context.Context, in *GenerateSliderCaptchaRequest, opts ...grpc_go.CallOption) (*GenerateSliderCaptchaResponse, common.ErrorWithAttachment) {
	out := new(GenerateSliderCaptchaResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/GenerateSliderCaptcha", in, out)
}

func (c *accountClient) VerifySliderCaptcha(ctx context.Context, in *VerifySliderCaptchaRequest, opts ...grpc_go.CallOption) (*VerifySliderCaptchaResponse, common.ErrorWithAttachment) {
	out := new(VerifySliderCaptchaResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/VerifySliderCaptcha", in, out)
}

func (c *accountClient) VerifySliderStatus(ctx context.Context, in *VerifySliderStatusRequest, opts ...grpc_go.CallOption) (*VerifySliderStatusResponse, common.ErrorWithAttachment) {
	out := new(VerifySliderStatusResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/VerifySliderStatus", in, out)
}

func (c *accountClient) SampleAccount(ctx context.Context, in *SampleAccountRequest, opts ...grpc_go.CallOption) (*SampleAccountResponse, common.ErrorWithAttachment) {
	out := new(SampleAccountResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/SampleAccount", in, out)
}

func (c *accountClient) LoginAndSqueezeOther(ctx context.Context, in *LoginRequest, opts ...grpc_go.CallOption) (*TokenInfo, common.ErrorWithAttachment) {
	out := new(TokenInfo)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/LoginAndSqueezeOther", in, out)
}

func (c *accountClient) QueryPersonnelWithTheSameName(ctx context.Context, in *QueryPersonnelWithTheSameNameRequest, opts ...grpc_go.CallOption) (*QueryPersonnelWithTheSameNameResponse, common.ErrorWithAttachment) {
	out := new(QueryPersonnelWithTheSameNameResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/QueryPersonnelWithTheSameName", in, out)
}

func (c *accountClient) UsersByJobNum(ctx context.Context, in *UsersByJobNumRequest, opts ...grpc_go.CallOption) (*ListResponse, common.ErrorWithAttachment) {
	out := new(ListResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/UsersByJobNum", in, out)
}

func (c *accountClient) IsSamePerson(ctx context.Context, in *IsSamePersonRequest, opts ...grpc_go.CallOption) (*IsSamePersonResponse, common.ErrorWithAttachment) {
	out := new(IsSamePersonResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/IsSamePerson", in, out)
}

func (c *accountClient) CreateRealNameOrPassPort(ctx context.Context, in *UserInfo, opts ...grpc_go.CallOption) (*CommonResponse, common.ErrorWithAttachment) {
	out := new(CommonResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/CreateRealNameOrPassPort", in, out)
}

func (c *accountClient) FddCreateUserV2(ctx context.Context, in *FddCreateUserRequest, opts ...grpc_go.CallOption) (*CommonResponse, common.ErrorWithAttachment) {
	out := new(CommonResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/FddCreateUserV2", in, out)
}

func (c *accountClient) FddUserFindByUserId(ctx context.Context, in *UserInfo, opts ...grpc_go.CallOption) (*FddInfo, common.ErrorWithAttachment) {
	out := new(FddInfo)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/FddUserFindByUserId", in, out)
}

func (c *accountClient) UserInfoById(ctx context.Context, in *InfoRequest, opts ...grpc_go.CallOption) (*UserInfo, common.ErrorWithAttachment) {
	out := new(UserInfo)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/UserInfoById", in, out)
}

func (c *accountClient) ValidateCode(ctx context.Context, in *ValidateCodeReq, opts ...grpc_go.CallOption) (*ValidateCodeResp, common.ErrorWithAttachment) {
	out := new(ValidateCodeResp)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/ValidateCode", in, out)
}

func (c *accountClient) SaveSubmitInfo(ctx context.Context, in *SubmitInfoRequest, opts ...grpc_go.CallOption) (*CommonResponse, common.ErrorWithAttachment) {
	out := new(CommonResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/SaveSubmitInfo", in, out)
}

func (c *accountClient) SmsLog(ctx context.Context, in *SmsLogReq, opts ...grpc_go.CallOption) (*SmsLogRes, common.ErrorWithAttachment) {
	out := new(SmsLogRes)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/SmsLog", in, out)
}

func (c *accountClient) ReviewRealName(ctx context.Context, in *ReviewRealNameRequest, opts ...grpc_go.CallOption) (*CommonResponse, common.ErrorWithAttachment) {
	out := new(CommonResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/ReviewRealName", in, out)
}

func (c *accountClient) ReviewRealNameList(ctx context.Context, in *ReviewRealNameListRequest, opts ...grpc_go.CallOption) (*ReviewRealNameListResponse, common.ErrorWithAttachment) {
	out := new(ReviewRealNameListResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/ReviewRealNameList", in, out)
}

func (c *accountClient) BindStaff(ctx context.Context, in *BindStaffRequest, opts ...grpc_go.CallOption) (*CommonResponse, common.ErrorWithAttachment) {
	out := new(CommonResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/BindStaff", in, out)
}

func (c *accountClient) SellerCustomerRelations(ctx context.Context, in *SellerCustomerRelationsRequest, opts ...grpc_go.CallOption) (*SellerCustomerRelationsResponse, common.ErrorWithAttachment) {
	out := new(SellerCustomerRelationsResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/SellerCustomerRelations", in, out)
}

func (c *accountClient) SellerCustomerRelationsList(ctx context.Context, in *SellerCustomerRelationsListRequest, opts ...grpc_go.CallOption) (*SellerCustomerRelationsListResponse, common.ErrorWithAttachment) {
	out := new(SellerCustomerRelationsListResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/SellerCustomerRelationsList", in, out)
}

func (c *accountClient) SellerCustomerCount(ctx context.Context, in *SellerCustomerCountRequest, opts ...grpc_go.CallOption) (*SellerCustomerCountResponse, common.ErrorWithAttachment) {
	out := new(SellerCustomerCountResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/SellerCustomerCount", in, out)
}

// AccountServer is the server API for Account service.
// All implementations must embed UnimplementedAccountServer
// for forward compatibility
type AccountServer interface {
	Login(context.Context, *LoginRequest) (*TokenInfo, error)
	RefreshToken(context.Context, *RefreshTokenRequest) (*TokenInfo, error)
	Logout(context.Context, *DecryptJwtRequest) (*CommonResponse, error)
	WxApp(context.Context, *WxAppRequest) (*WxAppResponse, error)
	WxUserInfo(context.Context, *WxUserOrCreateRequest) (*WxUserResponse, error)
	WxUserCreate(context.Context, *WxUserOrCreateRequest) (*WxUserResponse, error)
	WxUserUpdate(context.Context, *WxUserUpdateRequest) (*WxUserResponse, error)
	WxGetOpenIdByCode(context.Context, *WxGetOpenIdByCodeRequest) (*WxGetOpenIdByCodeResponse, error)
	WxBoxLogin(context.Context, *WxGetOpenIdByCodeRequest) (*WxBoxUserInfo, error)
	WxBoxUserInfoByOpenId(context.Context, *WxBoxUserInfoRequest) (*WxBoxUserInfo, error)
	WxBoxTelNumByCode(context.Context, *WxGetOpenIdByCodeRequest) (*WxBoxTelNumByCodeResponse, error)
	WxBoxUpdateUser(context.Context, *WxBoxUserInfo) (*CommonResponse, error)
	WxBoxCreateUser(context.Context, *WxBoxUserInfo) (*CommonResponse, error)
	FddCreateUser(context.Context, *FddCreateUserRequest) (*CommonResponse, error)
	FddUpdateUser(context.Context, *FddCreateUserRequest) (*CommonResponse, error)
	FddRemoveUser(context.Context, *FddRemoveUserRequest) (*CommonResponse, error)
	OffLine(context.Context, *CommonRequest) (*CommonResponse, error)
	OnlineLog(context.Context, *LoginInfosByUserIdRequest) (*LoginLogsResponse, error)
	OnlineLogById(context.Context, *OnlineLogByIdRequest) (*LoginLog, error)
	CheckPwd(context.Context, *CheckPwdRequest) (*UpdateResponse, error)
	Register(context.Context, *RegistRequest) (*RequestStatus, error)
	RegisterOrExist(context.Context, *RegistRequest) (*RequestStatus, error)
	SendMsg(context.Context, *SendMsgRequest) (*SendMsgStatusResponse, error)
	SendCustomMsg(context.Context, *SendCustomMsgRequest) (*SendMsgStatusResponse, error)
	SendExCustomMsg(context.Context, *SendCustomMsgRequest) (*SendMsgStatusResponse, error)
	SendMsgRegister(context.Context, *SendMsgRequest) (*SendMsgStatusResponse, error)
	CheckMsg(context.Context, *CheckMsgRequest) (*SendMsgStatusResponse, error)
	SendNewTelNumMsg(context.Context, *SendNewTelNumMsgRequest) (*SendMsgStatusResponse, error)
	UpdateTelNum(context.Context, *SendNewTelNumMsgRequest) (*SendMsgStatusResponse, error)
	Authentication(context.Context, *AuthenticationRequest) (*RequestStatus, error)
	DecryptJwt(context.Context, *DecryptJwtRequest) (*DecryptJwtResponse, error)
	Info(context.Context, *InfoRequest) (*InfoResponse, error)
	JobNumGetInfo(context.Context, *JobNumGetInfoRequest) (*InfoResponse, error)
	List(context.Context, *ListRequest) (*ListResponse, error)
	RandList(context.Context, *ListRequest) (*ListResponse, error)
	ListByIDs(context.Context, *ListByIDsRequest) (*ListResponse, error)
	Remove(context.Context, *RemoveRequest) (*RemoveResponse, error)
	WriteOff(context.Context, *WriteOffRequest) (*RemoveResponse, error)
	WriteOffApp(context.Context, *RemoveRequest) (*RemoveResponse, error)
	WriteOffInfo(context.Context, *WriteOffApproveRequest) (*WriteOffRequest, error)
	FindWriteOffList(context.Context, *WriteOffListRequest) (*WriteOffListResponse, error)
	WriteOffUpdate(context.Context, *WriteOffApproveRequest) (*RemoveResponse, error)
	Update(context.Context, *UpdateRequest) (*UpdateResponse, error)
	PrivacyInfo(context.Context, *PrivacyInfoRequest) (*AccountInfo, error)
	UsersByTel(context.Context, *UsersByTelRequest) (*ListResponse, error)
	UserByTel(context.Context, *UserByTelRequest) (*InfoResponse, error)
	CheckBeforeRegister(context.Context, *CheckBeforeRegisterRequest) (*CommonResponse, error)
	OnlySendMsg(context.Context, *SendMsgRequest) (*SendMsgStatusResponse, error)
	OnlyCheckMsg(context.Context, *CheckMsgRequest) (*SendMsgStatusResponse, error)
	CreateClockDevice(context.Context, *CreateClockDeviceRequest) (*ClockDeviceResponse, error)
	UpdateClockDevice(context.Context, *UpdateClockDeviceRequest) (*ClockDeviceResponse, error)
	RemoveClockDevice(context.Context, *RemoveClockDeviceRequest) (*ClockDeviceResponse, error)
	ClockDeviceList(context.Context, *ClockDeviceListRequest) (*ClockDeviceListResponse, error)
	ClockDeviceInfo(context.Context, *ClockDeviceInfoRequest) (*ClockDeviceInfoResponse, error)
	ClockDeviceSingleUntie(context.Context, *RemoveClockDeviceRequest) (*ClockDeviceResponse, error)
	ClockDeviceBatchBind(context.Context, *ClockBatchListResponse) (*ClockDeviceInfoResponse, error)
	ClockDeviceBatchUntie(context.Context, *ClockBatchBindRequest) (*ClockDeviceInfoResponse, error)
	ClockDeviceBatchList(context.Context, *ClockBatchBindRequest) (*ClockBatchListResponse, error)
	UpdateDeviceRelevance(context.Context, *ClockUserDeviceBatch) (*ClockDeviceResponse, error)
	MailAccountByNickName(context.Context, *MailAccountByNickNameRequest) (*MaiAccountResponse, error)
	CreateMaiAccount(context.Context, *CreateMaiAccountRequest) (*CommonResponse, error)
	CreateClockLog(context.Context, *ClockLogInfo) (*ClockDeviceResponse, error)
	SendClockInWechat(context.Context, *SendClockInWechatRequest) (*CommonResponse, error)
	FindClockLogList(context.Context, *ClockLogReq) (*ClockLogListResponse, error)
	SendStrangerClockInWechat(context.Context, *SendClockInWechatRequest) (*CommonResponse, error)
	ListV2(context.Context, *ListV2Request) (*ListResponse, error)
	CreateChainAccount(context.Context, *CommonRequest) (*CreateChainAccountResponse, error)
	SendNationMsg(context.Context, *SendNationMsgRequest) (*SendMsgStatusResponse, error)
	UpdateLanguage(context.Context, *UpdateLanguageRequest) (*UpdateLanguageResponse, error)
	GenerateSliderCaptcha(context.Context, *GenerateSliderCaptchaRequest) (*GenerateSliderCaptchaResponse, error)
	VerifySliderCaptcha(context.Context, *VerifySliderCaptchaRequest) (*VerifySliderCaptchaResponse, error)
	VerifySliderStatus(context.Context, *VerifySliderStatusRequest) (*VerifySliderStatusResponse, error)
	SampleAccount(context.Context, *SampleAccountRequest) (*SampleAccountResponse, error)
	LoginAndSqueezeOther(context.Context, *LoginRequest) (*TokenInfo, error)
	QueryPersonnelWithTheSameName(context.Context, *QueryPersonnelWithTheSameNameRequest) (*QueryPersonnelWithTheSameNameResponse, error)
	UsersByJobNum(context.Context, *UsersByJobNumRequest) (*ListResponse, error)
	//人脸检测
	IsSamePerson(context.Context, *IsSamePersonRequest) (*IsSamePersonResponse, error)
	CreateRealNameOrPassPort(context.Context, *UserInfo) (*CommonResponse, error)
	FddCreateUserV2(context.Context, *FddCreateUserRequest) (*CommonResponse, error)
	FddUserFindByUserId(context.Context, *UserInfo) (*FddInfo, error)
	UserInfoById(context.Context, *InfoRequest) (*UserInfo, error)
	//校验短信二维码
	ValidateCode(context.Context, *ValidateCodeReq) (*ValidateCodeResp, error)
	SaveSubmitInfo(context.Context, *SubmitInfoRequest) (*CommonResponse, error)
	SmsLog(context.Context, *SmsLogReq) (*SmsLogRes, error)
	//认证审核
	ReviewRealName(context.Context, *ReviewRealNameRequest) (*CommonResponse, error)
	ReviewRealNameList(context.Context, *ReviewRealNameListRequest) (*ReviewRealNameListResponse, error)
	//风和商城以及销售宝部分
	BindStaff(context.Context, *BindStaffRequest) (*CommonResponse, error)
	// seller 销售宝 客户和销售关联
	SellerCustomerRelations(context.Context, *SellerCustomerRelationsRequest) (*SellerCustomerRelationsResponse, error)
	SellerCustomerRelationsList(context.Context, *SellerCustomerRelationsListRequest) (*SellerCustomerRelationsListResponse, error)
	SellerCustomerCount(context.Context, *SellerCustomerCountRequest) (*SellerCustomerCountResponse, error)
	mustEmbedUnimplementedAccountServer()
}

// UnimplementedAccountServer must be embedded to have forward compatible implementations.
type UnimplementedAccountServer struct {
	proxyImpl protocol.Invoker
}

func (UnimplementedAccountServer) Login(context.Context, *LoginRequest) (*TokenInfo, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Login not implemented")
}
func (UnimplementedAccountServer) RefreshToken(context.Context, *RefreshTokenRequest) (*TokenInfo, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RefreshToken not implemented")
}
func (UnimplementedAccountServer) Logout(context.Context, *DecryptJwtRequest) (*CommonResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Logout not implemented")
}
func (UnimplementedAccountServer) WxApp(context.Context, *WxAppRequest) (*WxAppResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method WxApp not implemented")
}
func (UnimplementedAccountServer) WxUserInfo(context.Context, *WxUserOrCreateRequest) (*WxUserResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method WxUserInfo not implemented")
}
func (UnimplementedAccountServer) WxUserCreate(context.Context, *WxUserOrCreateRequest) (*WxUserResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method WxUserCreate not implemented")
}
func (UnimplementedAccountServer) WxUserUpdate(context.Context, *WxUserUpdateRequest) (*WxUserResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method WxUserUpdate not implemented")
}
func (UnimplementedAccountServer) WxGetOpenIdByCode(context.Context, *WxGetOpenIdByCodeRequest) (*WxGetOpenIdByCodeResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method WxGetOpenIdByCode not implemented")
}
func (UnimplementedAccountServer) WxBoxLogin(context.Context, *WxGetOpenIdByCodeRequest) (*WxBoxUserInfo, error) {
	return nil, status.Errorf(codes.Unimplemented, "method WxBoxLogin not implemented")
}
func (UnimplementedAccountServer) WxBoxUserInfoByOpenId(context.Context, *WxBoxUserInfoRequest) (*WxBoxUserInfo, error) {
	return nil, status.Errorf(codes.Unimplemented, "method WxBoxUserInfoByOpenId not implemented")
}
func (UnimplementedAccountServer) WxBoxTelNumByCode(context.Context, *WxGetOpenIdByCodeRequest) (*WxBoxTelNumByCodeResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method WxBoxTelNumByCode not implemented")
}
func (UnimplementedAccountServer) WxBoxUpdateUser(context.Context, *WxBoxUserInfo) (*CommonResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method WxBoxUpdateUser not implemented")
}
func (UnimplementedAccountServer) WxBoxCreateUser(context.Context, *WxBoxUserInfo) (*CommonResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method WxBoxCreateUser not implemented")
}
func (UnimplementedAccountServer) FddCreateUser(context.Context, *FddCreateUserRequest) (*CommonResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FddCreateUser not implemented")
}
func (UnimplementedAccountServer) FddUpdateUser(context.Context, *FddCreateUserRequest) (*CommonResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FddUpdateUser not implemented")
}
func (UnimplementedAccountServer) FddRemoveUser(context.Context, *FddRemoveUserRequest) (*CommonResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FddRemoveUser not implemented")
}
func (UnimplementedAccountServer) OffLine(context.Context, *CommonRequest) (*CommonResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method OffLine not implemented")
}
func (UnimplementedAccountServer) OnlineLog(context.Context, *LoginInfosByUserIdRequest) (*LoginLogsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method OnlineLog not implemented")
}
func (UnimplementedAccountServer) OnlineLogById(context.Context, *OnlineLogByIdRequest) (*LoginLog, error) {
	return nil, status.Errorf(codes.Unimplemented, "method OnlineLogById not implemented")
}
func (UnimplementedAccountServer) CheckPwd(context.Context, *CheckPwdRequest) (*UpdateResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CheckPwd not implemented")
}
func (UnimplementedAccountServer) Register(context.Context, *RegistRequest) (*RequestStatus, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Register not implemented")
}
func (UnimplementedAccountServer) RegisterOrExist(context.Context, *RegistRequest) (*RequestStatus, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RegisterOrExist not implemented")
}
func (UnimplementedAccountServer) SendMsg(context.Context, *SendMsgRequest) (*SendMsgStatusResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SendMsg not implemented")
}
func (UnimplementedAccountServer) SendCustomMsg(context.Context, *SendCustomMsgRequest) (*SendMsgStatusResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SendCustomMsg not implemented")
}
func (UnimplementedAccountServer) SendExCustomMsg(context.Context, *SendCustomMsgRequest) (*SendMsgStatusResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SendExCustomMsg not implemented")
}
func (UnimplementedAccountServer) SendMsgRegister(context.Context, *SendMsgRequest) (*SendMsgStatusResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SendMsgRegister not implemented")
}
func (UnimplementedAccountServer) CheckMsg(context.Context, *CheckMsgRequest) (*SendMsgStatusResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CheckMsg not implemented")
}
func (UnimplementedAccountServer) SendNewTelNumMsg(context.Context, *SendNewTelNumMsgRequest) (*SendMsgStatusResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SendNewTelNumMsg not implemented")
}
func (UnimplementedAccountServer) UpdateTelNum(context.Context, *SendNewTelNumMsgRequest) (*SendMsgStatusResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateTelNum not implemented")
}
func (UnimplementedAccountServer) Authentication(context.Context, *AuthenticationRequest) (*RequestStatus, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Authentication not implemented")
}
func (UnimplementedAccountServer) DecryptJwt(context.Context, *DecryptJwtRequest) (*DecryptJwtResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DecryptJwt not implemented")
}
func (UnimplementedAccountServer) Info(context.Context, *InfoRequest) (*InfoResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Info not implemented")
}
func (UnimplementedAccountServer) JobNumGetInfo(context.Context, *JobNumGetInfoRequest) (*InfoResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method JobNumGetInfo not implemented")
}
func (UnimplementedAccountServer) List(context.Context, *ListRequest) (*ListResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method List not implemented")
}
func (UnimplementedAccountServer) RandList(context.Context, *ListRequest) (*ListResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RandList not implemented")
}
func (UnimplementedAccountServer) ListByIDs(context.Context, *ListByIDsRequest) (*ListResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListByIDs not implemented")
}
func (UnimplementedAccountServer) Remove(context.Context, *RemoveRequest) (*RemoveResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Remove not implemented")
}
func (UnimplementedAccountServer) WriteOff(context.Context, *WriteOffRequest) (*RemoveResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method WriteOff not implemented")
}
func (UnimplementedAccountServer) WriteOffApp(context.Context, *RemoveRequest) (*RemoveResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method WriteOffApp not implemented")
}
func (UnimplementedAccountServer) WriteOffInfo(context.Context, *WriteOffApproveRequest) (*WriteOffRequest, error) {
	return nil, status.Errorf(codes.Unimplemented, "method WriteOffInfo not implemented")
}
func (UnimplementedAccountServer) FindWriteOffList(context.Context, *WriteOffListRequest) (*WriteOffListResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FindWriteOffList not implemented")
}
func (UnimplementedAccountServer) WriteOffUpdate(context.Context, *WriteOffApproveRequest) (*RemoveResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method WriteOffUpdate not implemented")
}
func (UnimplementedAccountServer) Update(context.Context, *UpdateRequest) (*UpdateResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Update not implemented")
}
func (UnimplementedAccountServer) PrivacyInfo(context.Context, *PrivacyInfoRequest) (*AccountInfo, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PrivacyInfo not implemented")
}
func (UnimplementedAccountServer) UsersByTel(context.Context, *UsersByTelRequest) (*ListResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UsersByTel not implemented")
}
func (UnimplementedAccountServer) UserByTel(context.Context, *UserByTelRequest) (*InfoResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UserByTel not implemented")
}
func (UnimplementedAccountServer) CheckBeforeRegister(context.Context, *CheckBeforeRegisterRequest) (*CommonResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CheckBeforeRegister not implemented")
}
func (UnimplementedAccountServer) OnlySendMsg(context.Context, *SendMsgRequest) (*SendMsgStatusResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method OnlySendMsg not implemented")
}
func (UnimplementedAccountServer) OnlyCheckMsg(context.Context, *CheckMsgRequest) (*SendMsgStatusResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method OnlyCheckMsg not implemented")
}
func (UnimplementedAccountServer) CreateClockDevice(context.Context, *CreateClockDeviceRequest) (*ClockDeviceResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateClockDevice not implemented")
}
func (UnimplementedAccountServer) UpdateClockDevice(context.Context, *UpdateClockDeviceRequest) (*ClockDeviceResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateClockDevice not implemented")
}
func (UnimplementedAccountServer) RemoveClockDevice(context.Context, *RemoveClockDeviceRequest) (*ClockDeviceResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RemoveClockDevice not implemented")
}
func (UnimplementedAccountServer) ClockDeviceList(context.Context, *ClockDeviceListRequest) (*ClockDeviceListResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ClockDeviceList not implemented")
}
func (UnimplementedAccountServer) ClockDeviceInfo(context.Context, *ClockDeviceInfoRequest) (*ClockDeviceInfoResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ClockDeviceInfo not implemented")
}
func (UnimplementedAccountServer) ClockDeviceSingleUntie(context.Context, *RemoveClockDeviceRequest) (*ClockDeviceResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ClockDeviceSingleUntie not implemented")
}
func (UnimplementedAccountServer) ClockDeviceBatchBind(context.Context, *ClockBatchListResponse) (*ClockDeviceInfoResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ClockDeviceBatchBind not implemented")
}
func (UnimplementedAccountServer) ClockDeviceBatchUntie(context.Context, *ClockBatchBindRequest) (*ClockDeviceInfoResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ClockDeviceBatchUntie not implemented")
}
func (UnimplementedAccountServer) ClockDeviceBatchList(context.Context, *ClockBatchBindRequest) (*ClockBatchListResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ClockDeviceBatchList not implemented")
}
func (UnimplementedAccountServer) UpdateDeviceRelevance(context.Context, *ClockUserDeviceBatch) (*ClockDeviceResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateDeviceRelevance not implemented")
}
func (UnimplementedAccountServer) MailAccountByNickName(context.Context, *MailAccountByNickNameRequest) (*MaiAccountResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method MailAccountByNickName not implemented")
}
func (UnimplementedAccountServer) CreateMaiAccount(context.Context, *CreateMaiAccountRequest) (*CommonResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateMaiAccount not implemented")
}
func (UnimplementedAccountServer) CreateClockLog(context.Context, *ClockLogInfo) (*ClockDeviceResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateClockLog not implemented")
}
func (UnimplementedAccountServer) SendClockInWechat(context.Context, *SendClockInWechatRequest) (*CommonResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SendClockInWechat not implemented")
}
func (UnimplementedAccountServer) FindClockLogList(context.Context, *ClockLogReq) (*ClockLogListResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FindClockLogList not implemented")
}
func (UnimplementedAccountServer) SendStrangerClockInWechat(context.Context, *SendClockInWechatRequest) (*CommonResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SendStrangerClockInWechat not implemented")
}
func (UnimplementedAccountServer) ListV2(context.Context, *ListV2Request) (*ListResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListV2 not implemented")
}
func (UnimplementedAccountServer) CreateChainAccount(context.Context, *CommonRequest) (*CreateChainAccountResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateChainAccount not implemented")
}
func (UnimplementedAccountServer) SendNationMsg(context.Context, *SendNationMsgRequest) (*SendMsgStatusResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SendNationMsg not implemented")
}
func (UnimplementedAccountServer) UpdateLanguage(context.Context, *UpdateLanguageRequest) (*UpdateLanguageResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateLanguage not implemented")
}
func (UnimplementedAccountServer) GenerateSliderCaptcha(context.Context, *GenerateSliderCaptchaRequest) (*GenerateSliderCaptchaResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GenerateSliderCaptcha not implemented")
}
func (UnimplementedAccountServer) VerifySliderCaptcha(context.Context, *VerifySliderCaptchaRequest) (*VerifySliderCaptchaResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method VerifySliderCaptcha not implemented")
}
func (UnimplementedAccountServer) VerifySliderStatus(context.Context, *VerifySliderStatusRequest) (*VerifySliderStatusResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method VerifySliderStatus not implemented")
}
func (UnimplementedAccountServer) SampleAccount(context.Context, *SampleAccountRequest) (*SampleAccountResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SampleAccount not implemented")
}
func (UnimplementedAccountServer) LoginAndSqueezeOther(context.Context, *LoginRequest) (*TokenInfo, error) {
	return nil, status.Errorf(codes.Unimplemented, "method LoginAndSqueezeOther not implemented")
}
func (UnimplementedAccountServer) QueryPersonnelWithTheSameName(context.Context, *QueryPersonnelWithTheSameNameRequest) (*QueryPersonnelWithTheSameNameResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method QueryPersonnelWithTheSameName not implemented")
}
func (UnimplementedAccountServer) UsersByJobNum(context.Context, *UsersByJobNumRequest) (*ListResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UsersByJobNum not implemented")
}
func (UnimplementedAccountServer) IsSamePerson(context.Context, *IsSamePersonRequest) (*IsSamePersonResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method IsSamePerson not implemented")
}
func (UnimplementedAccountServer) CreateRealNameOrPassPort(context.Context, *UserInfo) (*CommonResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateRealNameOrPassPort not implemented")
}
func (UnimplementedAccountServer) FddCreateUserV2(context.Context, *FddCreateUserRequest) (*CommonResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FddCreateUserV2 not implemented")
}
func (UnimplementedAccountServer) FddUserFindByUserId(context.Context, *UserInfo) (*FddInfo, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FddUserFindByUserId not implemented")
}
func (UnimplementedAccountServer) UserInfoById(context.Context, *InfoRequest) (*UserInfo, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UserInfoById not implemented")
}
func (UnimplementedAccountServer) ValidateCode(context.Context, *ValidateCodeReq) (*ValidateCodeResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ValidateCode not implemented")
}
func (UnimplementedAccountServer) SaveSubmitInfo(context.Context, *SubmitInfoRequest) (*CommonResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SaveSubmitInfo not implemented")
}
func (UnimplementedAccountServer) SmsLog(context.Context, *SmsLogReq) (*SmsLogRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SmsLog not implemented")
}
func (UnimplementedAccountServer) ReviewRealName(context.Context, *ReviewRealNameRequest) (*CommonResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ReviewRealName not implemented")
}
func (UnimplementedAccountServer) ReviewRealNameList(context.Context, *ReviewRealNameListRequest) (*ReviewRealNameListResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ReviewRealNameList not implemented")
}
func (UnimplementedAccountServer) BindStaff(context.Context, *BindStaffRequest) (*CommonResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method BindStaff not implemented")
}
func (UnimplementedAccountServer) SellerCustomerRelations(context.Context, *SellerCustomerRelationsRequest) (*SellerCustomerRelationsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SellerCustomerRelations not implemented")
}
func (UnimplementedAccountServer) SellerCustomerRelationsList(context.Context, *SellerCustomerRelationsListRequest) (*SellerCustomerRelationsListResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SellerCustomerRelationsList not implemented")
}
func (UnimplementedAccountServer) SellerCustomerCount(context.Context, *SellerCustomerCountRequest) (*SellerCustomerCountResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SellerCustomerCount not implemented")
}
func (s *UnimplementedAccountServer) XXX_SetProxyImpl(impl protocol.Invoker) {
	s.proxyImpl = impl
}

func (s *UnimplementedAccountServer) XXX_GetProxyImpl() protocol.Invoker {
	return s.proxyImpl
}

func (s *UnimplementedAccountServer) XXX_ServiceDesc() *grpc_go.ServiceDesc {
	return &Account_ServiceDesc
}
func (s *UnimplementedAccountServer) XXX_InterfaceName() string {
	return "account.Account"
}

func (UnimplementedAccountServer) mustEmbedUnimplementedAccountServer() {}

// UnsafeAccountServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to AccountServer will
// result in compilation errors.
type UnsafeAccountServer interface {
	mustEmbedUnimplementedAccountServer()
}

func RegisterAccountServer(s grpc_go.ServiceRegistrar, srv AccountServer) {
	s.RegisterService(&Account_ServiceDesc, srv)
}

func _Account_Login_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(LoginRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("Login", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Account_RefreshToken_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(RefreshTokenRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("RefreshToken", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Account_Logout_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(DecryptJwtRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("Logout", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Account_WxApp_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(WxAppRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("WxApp", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Account_WxUserInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(WxUserOrCreateRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("WxUserInfo", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Account_WxUserCreate_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(WxUserOrCreateRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("WxUserCreate", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Account_WxUserUpdate_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(WxUserUpdateRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("WxUserUpdate", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Account_WxGetOpenIdByCode_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(WxGetOpenIdByCodeRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("WxGetOpenIdByCode", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Account_WxBoxLogin_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(WxGetOpenIdByCodeRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("WxBoxLogin", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Account_WxBoxUserInfoByOpenId_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(WxBoxUserInfoRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("WxBoxUserInfoByOpenId", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Account_WxBoxTelNumByCode_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(WxGetOpenIdByCodeRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("WxBoxTelNumByCode", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Account_WxBoxUpdateUser_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(WxBoxUserInfo)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("WxBoxUpdateUser", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Account_WxBoxCreateUser_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(WxBoxUserInfo)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("WxBoxCreateUser", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Account_FddCreateUser_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(FddCreateUserRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("FddCreateUser", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Account_FddUpdateUser_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(FddCreateUserRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("FddUpdateUser", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Account_FddRemoveUser_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(FddRemoveUserRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("FddRemoveUser", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Account_OffLine_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(CommonRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("OffLine", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Account_OnlineLog_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(LoginInfosByUserIdRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("OnlineLog", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Account_OnlineLogById_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(OnlineLogByIdRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("OnlineLogById", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Account_CheckPwd_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(CheckPwdRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("CheckPwd", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Account_Register_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(RegistRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("Register", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Account_RegisterOrExist_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(RegistRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("RegisterOrExist", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Account_SendMsg_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(SendMsgRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("SendMsg", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Account_SendCustomMsg_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(SendCustomMsgRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("SendCustomMsg", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Account_SendExCustomMsg_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(SendCustomMsgRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("SendExCustomMsg", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Account_SendMsgRegister_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(SendMsgRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("SendMsgRegister", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Account_CheckMsg_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(CheckMsgRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("CheckMsg", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Account_SendNewTelNumMsg_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(SendNewTelNumMsgRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("SendNewTelNumMsg", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Account_UpdateTelNum_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(SendNewTelNumMsgRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("UpdateTelNum", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Account_Authentication_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(AuthenticationRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("Authentication", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Account_DecryptJwt_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(DecryptJwtRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("DecryptJwt", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Account_Info_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(InfoRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("Info", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Account_JobNumGetInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(JobNumGetInfoRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("JobNumGetInfo", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Account_List_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("List", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Account_RandList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("RandList", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Account_ListByIDs_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListByIDsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("ListByIDs", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Account_Remove_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(RemoveRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("Remove", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Account_WriteOff_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(WriteOffRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("WriteOff", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Account_WriteOffApp_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(RemoveRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("WriteOffApp", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Account_WriteOffInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(WriteOffApproveRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("WriteOffInfo", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Account_FindWriteOffList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(WriteOffListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("FindWriteOffList", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Account_WriteOffUpdate_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(WriteOffApproveRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("WriteOffUpdate", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Account_Update_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("Update", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Account_PrivacyInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(PrivacyInfoRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("PrivacyInfo", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Account_UsersByTel_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(UsersByTelRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("UsersByTel", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Account_UserByTel_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(UserByTelRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("UserByTel", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Account_CheckBeforeRegister_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(CheckBeforeRegisterRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("CheckBeforeRegister", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Account_OnlySendMsg_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(SendMsgRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("OnlySendMsg", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Account_OnlyCheckMsg_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(CheckMsgRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("OnlyCheckMsg", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Account_CreateClockDevice_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateClockDeviceRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("CreateClockDevice", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Account_UpdateClockDevice_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateClockDeviceRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("UpdateClockDevice", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Account_RemoveClockDevice_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(RemoveClockDeviceRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("RemoveClockDevice", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Account_ClockDeviceList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(ClockDeviceListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("ClockDeviceList", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Account_ClockDeviceInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(ClockDeviceInfoRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("ClockDeviceInfo", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Account_ClockDeviceSingleUntie_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(RemoveClockDeviceRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("ClockDeviceSingleUntie", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Account_ClockDeviceBatchBind_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(ClockBatchListResponse)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("ClockDeviceBatchBind", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Account_ClockDeviceBatchUntie_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(ClockBatchBindRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("ClockDeviceBatchUntie", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Account_ClockDeviceBatchList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(ClockBatchBindRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("ClockDeviceBatchList", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Account_UpdateDeviceRelevance_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(ClockUserDeviceBatch)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("UpdateDeviceRelevance", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Account_MailAccountByNickName_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(MailAccountByNickNameRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("MailAccountByNickName", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Account_CreateMaiAccount_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateMaiAccountRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("CreateMaiAccount", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Account_CreateClockLog_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(ClockLogInfo)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("CreateClockLog", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Account_SendClockInWechat_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(SendClockInWechatRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("SendClockInWechat", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Account_FindClockLogList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(ClockLogReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("FindClockLogList", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Account_SendStrangerClockInWechat_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(SendClockInWechatRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("SendStrangerClockInWechat", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Account_ListV2_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListV2Request)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("ListV2", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Account_CreateChainAccount_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(CommonRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("CreateChainAccount", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Account_SendNationMsg_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(SendNationMsgRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("SendNationMsg", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Account_UpdateLanguage_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateLanguageRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("UpdateLanguage", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Account_GenerateSliderCaptcha_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(GenerateSliderCaptchaRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("GenerateSliderCaptcha", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Account_VerifySliderCaptcha_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(VerifySliderCaptchaRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("VerifySliderCaptcha", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Account_VerifySliderStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(VerifySliderStatusRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("VerifySliderStatus", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Account_SampleAccount_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(SampleAccountRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("SampleAccount", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Account_LoginAndSqueezeOther_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(LoginRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("LoginAndSqueezeOther", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Account_QueryPersonnelWithTheSameName_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(QueryPersonnelWithTheSameNameRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("QueryPersonnelWithTheSameName", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Account_UsersByJobNum_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(UsersByJobNumRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("UsersByJobNum", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Account_IsSamePerson_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(IsSamePersonRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("IsSamePerson", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Account_CreateRealNameOrPassPort_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(UserInfo)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("CreateRealNameOrPassPort", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Account_FddCreateUserV2_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(FddCreateUserRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("FddCreateUserV2", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Account_FddUserFindByUserId_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(UserInfo)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("FddUserFindByUserId", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Account_UserInfoById_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(InfoRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("UserInfoById", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Account_ValidateCode_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(ValidateCodeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("ValidateCode", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Account_SaveSubmitInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(SubmitInfoRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("SaveSubmitInfo", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Account_SmsLog_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(SmsLogReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("SmsLog", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Account_ReviewRealName_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(ReviewRealNameRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("ReviewRealName", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Account_ReviewRealNameList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(ReviewRealNameListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("ReviewRealNameList", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Account_BindStaff_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(BindStaffRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("BindStaff", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Account_SellerCustomerRelations_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(SellerCustomerRelationsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("SellerCustomerRelations", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Account_SellerCustomerRelationsList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(SellerCustomerRelationsListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("SellerCustomerRelationsList", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Account_SellerCustomerCount_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(SellerCustomerCountRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("SellerCustomerCount", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

// Account_ServiceDesc is the grpc_go.ServiceDesc for Account service.
// It's only intended for direct use with grpc_go.RegisterService,
// and not to be introspected or modified (even as a copy)
var Account_ServiceDesc = grpc_go.ServiceDesc{
	ServiceName: "account.Account",
	HandlerType: (*AccountServer)(nil),
	Methods: []grpc_go.MethodDesc{
		{
			MethodName: "Login",
			Handler:    _Account_Login_Handler,
		},
		{
			MethodName: "RefreshToken",
			Handler:    _Account_RefreshToken_Handler,
		},
		{
			MethodName: "Logout",
			Handler:    _Account_Logout_Handler,
		},
		{
			MethodName: "WxApp",
			Handler:    _Account_WxApp_Handler,
		},
		{
			MethodName: "WxUserInfo",
			Handler:    _Account_WxUserInfo_Handler,
		},
		{
			MethodName: "WxUserCreate",
			Handler:    _Account_WxUserCreate_Handler,
		},
		{
			MethodName: "WxUserUpdate",
			Handler:    _Account_WxUserUpdate_Handler,
		},
		{
			MethodName: "WxGetOpenIdByCode",
			Handler:    _Account_WxGetOpenIdByCode_Handler,
		},
		{
			MethodName: "WxBoxLogin",
			Handler:    _Account_WxBoxLogin_Handler,
		},
		{
			MethodName: "WxBoxUserInfoByOpenId",
			Handler:    _Account_WxBoxUserInfoByOpenId_Handler,
		},
		{
			MethodName: "WxBoxTelNumByCode",
			Handler:    _Account_WxBoxTelNumByCode_Handler,
		},
		{
			MethodName: "WxBoxUpdateUser",
			Handler:    _Account_WxBoxUpdateUser_Handler,
		},
		{
			MethodName: "WxBoxCreateUser",
			Handler:    _Account_WxBoxCreateUser_Handler,
		},
		{
			MethodName: "FddCreateUser",
			Handler:    _Account_FddCreateUser_Handler,
		},
		{
			MethodName: "FddUpdateUser",
			Handler:    _Account_FddUpdateUser_Handler,
		},
		{
			MethodName: "FddRemoveUser",
			Handler:    _Account_FddRemoveUser_Handler,
		},
		{
			MethodName: "OffLine",
			Handler:    _Account_OffLine_Handler,
		},
		{
			MethodName: "OnlineLog",
			Handler:    _Account_OnlineLog_Handler,
		},
		{
			MethodName: "OnlineLogById",
			Handler:    _Account_OnlineLogById_Handler,
		},
		{
			MethodName: "CheckPwd",
			Handler:    _Account_CheckPwd_Handler,
		},
		{
			MethodName: "Register",
			Handler:    _Account_Register_Handler,
		},
		{
			MethodName: "RegisterOrExist",
			Handler:    _Account_RegisterOrExist_Handler,
		},
		{
			MethodName: "SendMsg",
			Handler:    _Account_SendMsg_Handler,
		},
		{
			MethodName: "SendCustomMsg",
			Handler:    _Account_SendCustomMsg_Handler,
		},
		{
			MethodName: "SendExCustomMsg",
			Handler:    _Account_SendExCustomMsg_Handler,
		},
		{
			MethodName: "SendMsgRegister",
			Handler:    _Account_SendMsgRegister_Handler,
		},
		{
			MethodName: "CheckMsg",
			Handler:    _Account_CheckMsg_Handler,
		},
		{
			MethodName: "SendNewTelNumMsg",
			Handler:    _Account_SendNewTelNumMsg_Handler,
		},
		{
			MethodName: "UpdateTelNum",
			Handler:    _Account_UpdateTelNum_Handler,
		},
		{
			MethodName: "Authentication",
			Handler:    _Account_Authentication_Handler,
		},
		{
			MethodName: "DecryptJwt",
			Handler:    _Account_DecryptJwt_Handler,
		},
		{
			MethodName: "Info",
			Handler:    _Account_Info_Handler,
		},
		{
			MethodName: "JobNumGetInfo",
			Handler:    _Account_JobNumGetInfo_Handler,
		},
		{
			MethodName: "List",
			Handler:    _Account_List_Handler,
		},
		{
			MethodName: "RandList",
			Handler:    _Account_RandList_Handler,
		},
		{
			MethodName: "ListByIDs",
			Handler:    _Account_ListByIDs_Handler,
		},
		{
			MethodName: "Remove",
			Handler:    _Account_Remove_Handler,
		},
		{
			MethodName: "WriteOff",
			Handler:    _Account_WriteOff_Handler,
		},
		{
			MethodName: "WriteOffApp",
			Handler:    _Account_WriteOffApp_Handler,
		},
		{
			MethodName: "WriteOffInfo",
			Handler:    _Account_WriteOffInfo_Handler,
		},
		{
			MethodName: "FindWriteOffList",
			Handler:    _Account_FindWriteOffList_Handler,
		},
		{
			MethodName: "WriteOffUpdate",
			Handler:    _Account_WriteOffUpdate_Handler,
		},
		{
			MethodName: "Update",
			Handler:    _Account_Update_Handler,
		},
		{
			MethodName: "PrivacyInfo",
			Handler:    _Account_PrivacyInfo_Handler,
		},
		{
			MethodName: "UsersByTel",
			Handler:    _Account_UsersByTel_Handler,
		},
		{
			MethodName: "UserByTel",
			Handler:    _Account_UserByTel_Handler,
		},
		{
			MethodName: "CheckBeforeRegister",
			Handler:    _Account_CheckBeforeRegister_Handler,
		},
		{
			MethodName: "OnlySendMsg",
			Handler:    _Account_OnlySendMsg_Handler,
		},
		{
			MethodName: "OnlyCheckMsg",
			Handler:    _Account_OnlyCheckMsg_Handler,
		},
		{
			MethodName: "CreateClockDevice",
			Handler:    _Account_CreateClockDevice_Handler,
		},
		{
			MethodName: "UpdateClockDevice",
			Handler:    _Account_UpdateClockDevice_Handler,
		},
		{
			MethodName: "RemoveClockDevice",
			Handler:    _Account_RemoveClockDevice_Handler,
		},
		{
			MethodName: "ClockDeviceList",
			Handler:    _Account_ClockDeviceList_Handler,
		},
		{
			MethodName: "ClockDeviceInfo",
			Handler:    _Account_ClockDeviceInfo_Handler,
		},
		{
			MethodName: "ClockDeviceSingleUntie",
			Handler:    _Account_ClockDeviceSingleUntie_Handler,
		},
		{
			MethodName: "ClockDeviceBatchBind",
			Handler:    _Account_ClockDeviceBatchBind_Handler,
		},
		{
			MethodName: "ClockDeviceBatchUntie",
			Handler:    _Account_ClockDeviceBatchUntie_Handler,
		},
		{
			MethodName: "ClockDeviceBatchList",
			Handler:    _Account_ClockDeviceBatchList_Handler,
		},
		{
			MethodName: "UpdateDeviceRelevance",
			Handler:    _Account_UpdateDeviceRelevance_Handler,
		},
		{
			MethodName: "MailAccountByNickName",
			Handler:    _Account_MailAccountByNickName_Handler,
		},
		{
			MethodName: "CreateMaiAccount",
			Handler:    _Account_CreateMaiAccount_Handler,
		},
		{
			MethodName: "CreateClockLog",
			Handler:    _Account_CreateClockLog_Handler,
		},
		{
			MethodName: "SendClockInWechat",
			Handler:    _Account_SendClockInWechat_Handler,
		},
		{
			MethodName: "FindClockLogList",
			Handler:    _Account_FindClockLogList_Handler,
		},
		{
			MethodName: "SendStrangerClockInWechat",
			Handler:    _Account_SendStrangerClockInWechat_Handler,
		},
		{
			MethodName: "ListV2",
			Handler:    _Account_ListV2_Handler,
		},
		{
			MethodName: "CreateChainAccount",
			Handler:    _Account_CreateChainAccount_Handler,
		},
		{
			MethodName: "SendNationMsg",
			Handler:    _Account_SendNationMsg_Handler,
		},
		{
			MethodName: "UpdateLanguage",
			Handler:    _Account_UpdateLanguage_Handler,
		},
		{
			MethodName: "GenerateSliderCaptcha",
			Handler:    _Account_GenerateSliderCaptcha_Handler,
		},
		{
			MethodName: "VerifySliderCaptcha",
			Handler:    _Account_VerifySliderCaptcha_Handler,
		},
		{
			MethodName: "VerifySliderStatus",
			Handler:    _Account_VerifySliderStatus_Handler,
		},
		{
			MethodName: "SampleAccount",
			Handler:    _Account_SampleAccount_Handler,
		},
		{
			MethodName: "LoginAndSqueezeOther",
			Handler:    _Account_LoginAndSqueezeOther_Handler,
		},
		{
			MethodName: "QueryPersonnelWithTheSameName",
			Handler:    _Account_QueryPersonnelWithTheSameName_Handler,
		},
		{
			MethodName: "UsersByJobNum",
			Handler:    _Account_UsersByJobNum_Handler,
		},
		{
			MethodName: "IsSamePerson",
			Handler:    _Account_IsSamePerson_Handler,
		},
		{
			MethodName: "CreateRealNameOrPassPort",
			Handler:    _Account_CreateRealNameOrPassPort_Handler,
		},
		{
			MethodName: "FddCreateUserV2",
			Handler:    _Account_FddCreateUserV2_Handler,
		},
		{
			MethodName: "FddUserFindByUserId",
			Handler:    _Account_FddUserFindByUserId_Handler,
		},
		{
			MethodName: "UserInfoById",
			Handler:    _Account_UserInfoById_Handler,
		},
		{
			MethodName: "ValidateCode",
			Handler:    _Account_ValidateCode_Handler,
		},
		{
			MethodName: "SaveSubmitInfo",
			Handler:    _Account_SaveSubmitInfo_Handler,
		},
		{
			MethodName: "SmsLog",
			Handler:    _Account_SmsLog_Handler,
		},
		{
			MethodName: "ReviewRealName",
			Handler:    _Account_ReviewRealName_Handler,
		},
		{
			MethodName: "ReviewRealNameList",
			Handler:    _Account_ReviewRealNameList_Handler,
		},
		{
			MethodName: "BindStaff",
			Handler:    _Account_BindStaff_Handler,
		},
		{
			MethodName: "SellerCustomerRelations",
			Handler:    _Account_SellerCustomerRelations_Handler,
		},
		{
			MethodName: "SellerCustomerRelationsList",
			Handler:    _Account_SellerCustomerRelationsList_Handler,
		},
		{
			MethodName: "SellerCustomerCount",
			Handler:    _Account_SellerCustomerCount_Handler,
		},
	},
	Streams:  []grpc_go.StreamDesc{},
	Metadata: "api/account/account.proto",
}
