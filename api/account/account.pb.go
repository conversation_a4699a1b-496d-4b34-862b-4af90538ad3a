//
// Licensed to the Apache Software Foundation (ASF) under one or more
// contributor license agreements.  See the NOTICE file distributed with
// this work for additional information regarding copyright ownership.
// The ASF licenses this file to You under the Apache License, Version 2.0
// (the "License"); you may not use this file except in compliance with
// the License.  You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.29.1
// 	protoc        v3.21.12
// source: api/account/account.proto

package account

import (
	_ "github.com/mwitkow/go-proto-validators"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type SmsLogReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	MsgNo            string `protobuf:"bytes,1,opt,name=msgNo,proto3" json:"msgNo,omitempty"`                       //用户的uuid
	Mobile           string `protobuf:"bytes,2,opt,name=mobile,proto3" json:"mobile,omitempty"`                     //用户的uuid
	AllContent       string `protobuf:"bytes,3,opt,name=allContent,proto3" json:"allContent,omitempty"`             //用户的uuid
	BaseStatus       uint32 `protobuf:"varint,4,opt,name=baseStatus,proto3" json:"baseStatus,omitempty"`            //用户的uuid
	StartSendTime    string `protobuf:"bytes,5,opt,name=startSendTime,proto3" json:"startSendTime,omitempty"`       //用户的uuid
	EndSendTime      string `protobuf:"bytes,6,opt,name=endSendTime,proto3" json:"endSendTime,omitempty"`           //用户的uuid
	StartReceiveTime string `protobuf:"bytes,7,opt,name=startReceiveTime,proto3" json:"startReceiveTime,omitempty"` //用户的uuid
	EndReceiveTime   string `protobuf:"bytes,8,opt,name=endReceiveTime,proto3" json:"endReceiveTime,omitempty"`     //用户的uuid
	PageSize         uint64 `protobuf:"varint,9,opt,name=pageSize,proto3" json:"pageSize,omitempty"`
	Page             uint64 `protobuf:"varint,10,opt,name=page,proto3" json:"page,omitempty"`
}

func (x *SmsLogReq) Reset() {
	*x = SmsLogReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_account_account_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SmsLogReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SmsLogReq) ProtoMessage() {}

func (x *SmsLogReq) ProtoReflect() protoreflect.Message {
	mi := &file_api_account_account_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SmsLogReq.ProtoReflect.Descriptor instead.
func (*SmsLogReq) Descriptor() ([]byte, []int) {
	return file_api_account_account_proto_rawDescGZIP(), []int{0}
}

func (x *SmsLogReq) GetMsgNo() string {
	if x != nil {
		return x.MsgNo
	}
	return ""
}

func (x *SmsLogReq) GetMobile() string {
	if x != nil {
		return x.Mobile
	}
	return ""
}

func (x *SmsLogReq) GetAllContent() string {
	if x != nil {
		return x.AllContent
	}
	return ""
}

func (x *SmsLogReq) GetBaseStatus() uint32 {
	if x != nil {
		return x.BaseStatus
	}
	return 0
}

func (x *SmsLogReq) GetStartSendTime() string {
	if x != nil {
		return x.StartSendTime
	}
	return ""
}

func (x *SmsLogReq) GetEndSendTime() string {
	if x != nil {
		return x.EndSendTime
	}
	return ""
}

func (x *SmsLogReq) GetStartReceiveTime() string {
	if x != nil {
		return x.StartReceiveTime
	}
	return ""
}

func (x *SmsLogReq) GetEndReceiveTime() string {
	if x != nil {
		return x.EndReceiveTime
	}
	return ""
}

func (x *SmsLogReq) GetPageSize() uint64 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *SmsLogReq) GetPage() uint64 {
	if x != nil {
		return x.Page
	}
	return 0
}

type SmsLogRes struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	List  []*SmsLogInfo `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`
	Count uint32        `protobuf:"varint,2,opt,name=count,proto3" json:"count,omitempty"` //用户的uuid
}

func (x *SmsLogRes) Reset() {
	*x = SmsLogRes{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_account_account_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SmsLogRes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SmsLogRes) ProtoMessage() {}

func (x *SmsLogRes) ProtoReflect() protoreflect.Message {
	mi := &file_api_account_account_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SmsLogRes.ProtoReflect.Descriptor instead.
func (*SmsLogRes) Descriptor() ([]byte, []int) {
	return file_api_account_account_proto_rawDescGZIP(), []int{1}
}

func (x *SmsLogRes) GetList() []*SmsLogInfo {
	if x != nil {
		return x.List
	}
	return nil
}

func (x *SmsLogRes) GetCount() uint32 {
	if x != nil {
		return x.Count
	}
	return 0
}

type SmsLogInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	MsgNo       string `protobuf:"bytes,1,opt,name=msgNo,proto3" json:"msgNo,omitempty"`             //用户的uuid
	Mobile      string `protobuf:"bytes,2,opt,name=mobile,proto3" json:"mobile,omitempty"`           //用户的uuid
	AllContent  string `protobuf:"bytes,3,opt,name=allContent,proto3" json:"allContent,omitempty"`   //用户的uuid
	BaseStatus  uint32 `protobuf:"varint,4,opt,name=baseStatus,proto3" json:"baseStatus,omitempty"`  //用户的uuid
	SendTime    string `protobuf:"bytes,5,opt,name=sendTime,proto3" json:"sendTime,omitempty"`       //用户的uuid
	ReceiveTime string `protobuf:"bytes,7,opt,name=receiveTime,proto3" json:"receiveTime,omitempty"` //用户的uuid
	StatusDesc  string `protobuf:"bytes,8,opt,name=statusDesc,proto3" json:"statusDesc,omitempty"`   //用户的uuid
	Status      string `protobuf:"bytes,6,opt,name=status,proto3" json:"status,omitempty"`           //用户的uuid
	Id          uint32 `protobuf:"varint,9,opt,name=id,proto3" json:"id,omitempty"`                  //用户的uuid
	Plat        string `protobuf:"bytes,10,opt,name=plat,proto3" json:"plat,omitempty"`              //短信平台 feige-飞鸽国内 feige-intel -飞鸽国际
}

func (x *SmsLogInfo) Reset() {
	*x = SmsLogInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_account_account_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SmsLogInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SmsLogInfo) ProtoMessage() {}

func (x *SmsLogInfo) ProtoReflect() protoreflect.Message {
	mi := &file_api_account_account_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SmsLogInfo.ProtoReflect.Descriptor instead.
func (*SmsLogInfo) Descriptor() ([]byte, []int) {
	return file_api_account_account_proto_rawDescGZIP(), []int{2}
}

func (x *SmsLogInfo) GetMsgNo() string {
	if x != nil {
		return x.MsgNo
	}
	return ""
}

func (x *SmsLogInfo) GetMobile() string {
	if x != nil {
		return x.Mobile
	}
	return ""
}

func (x *SmsLogInfo) GetAllContent() string {
	if x != nil {
		return x.AllContent
	}
	return ""
}

func (x *SmsLogInfo) GetBaseStatus() uint32 {
	if x != nil {
		return x.BaseStatus
	}
	return 0
}

func (x *SmsLogInfo) GetSendTime() string {
	if x != nil {
		return x.SendTime
	}
	return ""
}

func (x *SmsLogInfo) GetReceiveTime() string {
	if x != nil {
		return x.ReceiveTime
	}
	return ""
}

func (x *SmsLogInfo) GetStatusDesc() string {
	if x != nil {
		return x.StatusDesc
	}
	return ""
}

func (x *SmsLogInfo) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

func (x *SmsLogInfo) GetId() uint32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *SmsLogInfo) GetPlat() string {
	if x != nil {
		return x.Plat
	}
	return ""
}

type BindStaffRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UserId  uint32 `protobuf:"varint,1,opt,name=userId,proto3" json:"userId,omitempty"`   //用户的uuid
	StaffId uint32 `protobuf:"varint,2,opt,name=staffId,proto3" json:"staffId,omitempty"` //销售的uuid
}

func (x *BindStaffRequest) Reset() {
	*x = BindStaffRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_account_account_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BindStaffRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BindStaffRequest) ProtoMessage() {}

func (x *BindStaffRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_account_account_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BindStaffRequest.ProtoReflect.Descriptor instead.
func (*BindStaffRequest) Descriptor() ([]byte, []int) {
	return file_api_account_account_proto_rawDescGZIP(), []int{3}
}

func (x *BindStaffRequest) GetUserId() uint32 {
	if x != nil {
		return x.UserId
	}
	return 0
}

func (x *BindStaffRequest) GetStaffId() uint32 {
	if x != nil {
		return x.StaffId
	}
	return 0
}

type ReviewRealNameListRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UserId   uint32 `protobuf:"varint,1,opt,name=userId,proto3" json:"userId,omitempty"`
	Page     uint32 `protobuf:"varint,2,opt,name=page,proto3" json:"page,omitempty"`
	PageSize uint32 `protobuf:"varint,3,opt,name=pageSize,proto3" json:"pageSize,omitempty"`
}

func (x *ReviewRealNameListRequest) Reset() {
	*x = ReviewRealNameListRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_account_account_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReviewRealNameListRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReviewRealNameListRequest) ProtoMessage() {}

func (x *ReviewRealNameListRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_account_account_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReviewRealNameListRequest.ProtoReflect.Descriptor instead.
func (*ReviewRealNameListRequest) Descriptor() ([]byte, []int) {
	return file_api_account_account_proto_rawDescGZIP(), []int{4}
}

func (x *ReviewRealNameListRequest) GetUserId() uint32 {
	if x != nil {
		return x.UserId
	}
	return 0
}

func (x *ReviewRealNameListRequest) GetPage() uint32 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *ReviewRealNameListRequest) GetPageSize() uint32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

type ReviewRealNameListResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Data  []*ReviewRealNameRequest `protobuf:"bytes,1,rep,name=data,proto3" json:"data,omitempty"`
	Count int64                    `protobuf:"varint,2,opt,name=count,proto3" json:"count,omitempty"`
}

func (x *ReviewRealNameListResponse) Reset() {
	*x = ReviewRealNameListResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_account_account_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReviewRealNameListResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReviewRealNameListResponse) ProtoMessage() {}

func (x *ReviewRealNameListResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_account_account_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReviewRealNameListResponse.ProtoReflect.Descriptor instead.
func (*ReviewRealNameListResponse) Descriptor() ([]byte, []int) {
	return file_api_account_account_proto_rawDescGZIP(), []int{5}
}

func (x *ReviewRealNameListResponse) GetData() []*ReviewRealNameRequest {
	if x != nil {
		return x.Data
	}
	return nil
}

func (x *ReviewRealNameListResponse) GetCount() int64 {
	if x != nil {
		return x.Count
	}
	return 0
}

type ReviewRealNameInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id         uint64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	CreateAt   string `protobuf:"bytes,2,opt,name=createAt,proto3" json:"createAt,omitempty"`
	UpdateAt   string `protobuf:"bytes,3,opt,name=updateAt,proto3" json:"updateAt,omitempty"`
	Message    string `protobuf:"bytes,4,opt,name=message,proto3" json:"message,omitempty"`
	PassStatus uint32 `protobuf:"varint,5,opt,name=passStatus,proto3" json:"passStatus,omitempty"`
	UserId     uint32 `protobuf:"varint,6,opt,name=userId,proto3" json:"userId,omitempty"`
	UserName   string `protobuf:"bytes,7,opt,name=userName,proto3" json:"userName,omitempty"`
}

func (x *ReviewRealNameInfo) Reset() {
	*x = ReviewRealNameInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_account_account_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReviewRealNameInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReviewRealNameInfo) ProtoMessage() {}

func (x *ReviewRealNameInfo) ProtoReflect() protoreflect.Message {
	mi := &file_api_account_account_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReviewRealNameInfo.ProtoReflect.Descriptor instead.
func (*ReviewRealNameInfo) Descriptor() ([]byte, []int) {
	return file_api_account_account_proto_rawDescGZIP(), []int{6}
}

func (x *ReviewRealNameInfo) GetId() uint64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *ReviewRealNameInfo) GetCreateAt() string {
	if x != nil {
		return x.CreateAt
	}
	return ""
}

func (x *ReviewRealNameInfo) GetUpdateAt() string {
	if x != nil {
		return x.UpdateAt
	}
	return ""
}

func (x *ReviewRealNameInfo) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *ReviewRealNameInfo) GetPassStatus() uint32 {
	if x != nil {
		return x.PassStatus
	}
	return 0
}

func (x *ReviewRealNameInfo) GetUserId() uint32 {
	if x != nil {
		return x.UserId
	}
	return 0
}

func (x *ReviewRealNameInfo) GetUserName() string {
	if x != nil {
		return x.UserName
	}
	return ""
}

type ReviewRealNameRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ids        []uint32 `protobuf:"varint,1,rep,packed,name=ids,proto3" json:"ids,omitempty"`
	Message    string   `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	IsPass     bool     `protobuf:"varint,3,opt,name=isPass,proto3" json:"isPass,omitempty"`
	UserId     uint32   `protobuf:"varint,4,opt,name=userId,proto3" json:"userId,omitempty"`
	UserName   string   `protobuf:"bytes,5,opt,name=userName,proto3" json:"userName,omitempty"`
	CreatedAt  string   `protobuf:"bytes,6,opt,name=createdAt,proto3" json:"createdAt,omitempty"`
	PassStatus uint32   `protobuf:"varint,7,opt,name=passStatus,proto3" json:"passStatus,omitempty"`
}

func (x *ReviewRealNameRequest) Reset() {
	*x = ReviewRealNameRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_account_account_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReviewRealNameRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReviewRealNameRequest) ProtoMessage() {}

func (x *ReviewRealNameRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_account_account_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReviewRealNameRequest.ProtoReflect.Descriptor instead.
func (*ReviewRealNameRequest) Descriptor() ([]byte, []int) {
	return file_api_account_account_proto_rawDescGZIP(), []int{7}
}

func (x *ReviewRealNameRequest) GetIds() []uint32 {
	if x != nil {
		return x.Ids
	}
	return nil
}

func (x *ReviewRealNameRequest) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *ReviewRealNameRequest) GetIsPass() bool {
	if x != nil {
		return x.IsPass
	}
	return false
}

func (x *ReviewRealNameRequest) GetUserId() uint32 {
	if x != nil {
		return x.UserId
	}
	return 0
}

func (x *ReviewRealNameRequest) GetUserName() string {
	if x != nil {
		return x.UserName
	}
	return ""
}

func (x *ReviewRealNameRequest) GetCreatedAt() string {
	if x != nil {
		return x.CreatedAt
	}
	return ""
}

func (x *ReviewRealNameRequest) GetPassStatus() uint32 {
	if x != nil {
		return x.PassStatus
	}
	return 0
}

type SubmitInfoRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	FirstName string `protobuf:"bytes,1,opt,name=firstName,proto3" json:"firstName,omitempty"`
	LastName  string `protobuf:"bytes,2,opt,name=lastName,proto3" json:"lastName,omitempty"`
	Email     string `protobuf:"bytes,3,opt,name=email,proto3" json:"email,omitempty"`
	Company   string `protobuf:"bytes,4,opt,name=company,proto3" json:"company,omitempty"`
	Phone     string `protobuf:"bytes,5,opt,name=phone,proto3" json:"phone,omitempty"`
}

func (x *SubmitInfoRequest) Reset() {
	*x = SubmitInfoRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_account_account_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SubmitInfoRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SubmitInfoRequest) ProtoMessage() {}

func (x *SubmitInfoRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_account_account_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SubmitInfoRequest.ProtoReflect.Descriptor instead.
func (*SubmitInfoRequest) Descriptor() ([]byte, []int) {
	return file_api_account_account_proto_rawDescGZIP(), []int{8}
}

func (x *SubmitInfoRequest) GetFirstName() string {
	if x != nil {
		return x.FirstName
	}
	return ""
}

func (x *SubmitInfoRequest) GetLastName() string {
	if x != nil {
		return x.LastName
	}
	return ""
}

func (x *SubmitInfoRequest) GetEmail() string {
	if x != nil {
		return x.Email
	}
	return ""
}

func (x *SubmitInfoRequest) GetCompany() string {
	if x != nil {
		return x.Company
	}
	return ""
}

func (x *SubmitInfoRequest) GetPhone() string {
	if x != nil {
		return x.Phone
	}
	return ""
}

type CheckBeforeRegisterRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Domain string `protobuf:"bytes,1,opt,name=domain,proto3" json:"domain,omitempty"`
	JobNum string `protobuf:"bytes,2,opt,name=jobNum,proto3" json:"jobNum,omitempty"`
	TelNum string `protobuf:"bytes,3,opt,name=telNum,proto3" json:"telNum,omitempty"`
}

func (x *CheckBeforeRegisterRequest) Reset() {
	*x = CheckBeforeRegisterRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_account_account_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CheckBeforeRegisterRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CheckBeforeRegisterRequest) ProtoMessage() {}

func (x *CheckBeforeRegisterRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_account_account_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CheckBeforeRegisterRequest.ProtoReflect.Descriptor instead.
func (*CheckBeforeRegisterRequest) Descriptor() ([]byte, []int) {
	return file_api_account_account_proto_rawDescGZIP(), []int{9}
}

func (x *CheckBeforeRegisterRequest) GetDomain() string {
	if x != nil {
		return x.Domain
	}
	return ""
}

func (x *CheckBeforeRegisterRequest) GetJobNum() string {
	if x != nil {
		return x.JobNum
	}
	return ""
}

func (x *CheckBeforeRegisterRequest) GetTelNum() string {
	if x != nil {
		return x.TelNum
	}
	return ""
}

type SampleAccountRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Domain   string `protobuf:"bytes,1,opt,name=domain,proto3" json:"domain,omitempty"`
	Password string `protobuf:"bytes,2,opt,name=password,proto3" json:"password,omitempty"`
	Code     string `protobuf:"bytes,3,opt,name=code,proto3" json:"code,omitempty"`
	From     string `protobuf:"bytes,4,opt,name=from,proto3" json:"from,omitempty"`
	TelNum   string `protobuf:"bytes,5,opt,name=telNum,proto3" json:"telNum,omitempty"`
	Zone     string `protobuf:"bytes,6,opt,name=zone,proto3" json:"zone,omitempty"`
}

func (x *SampleAccountRequest) Reset() {
	*x = SampleAccountRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_account_account_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SampleAccountRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SampleAccountRequest) ProtoMessage() {}

func (x *SampleAccountRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_account_account_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SampleAccountRequest.ProtoReflect.Descriptor instead.
func (*SampleAccountRequest) Descriptor() ([]byte, []int) {
	return file_api_account_account_proto_rawDescGZIP(), []int{10}
}

func (x *SampleAccountRequest) GetDomain() string {
	if x != nil {
		return x.Domain
	}
	return ""
}

func (x *SampleAccountRequest) GetPassword() string {
	if x != nil {
		return x.Password
	}
	return ""
}

func (x *SampleAccountRequest) GetCode() string {
	if x != nil {
		return x.Code
	}
	return ""
}

func (x *SampleAccountRequest) GetFrom() string {
	if x != nil {
		return x.From
	}
	return ""
}

func (x *SampleAccountRequest) GetTelNum() string {
	if x != nil {
		return x.TelNum
	}
	return ""
}

func (x *SampleAccountRequest) GetZone() string {
	if x != nil {
		return x.Zone
	}
	return ""
}

type SampleAccountResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	IsNowAlreadyLogin bool   `protobuf:"varint,1,opt,name=isNowAlreadyLogin,proto3" json:"isNowAlreadyLogin,omitempty"`
	Num               uint32 `protobuf:"varint,2,opt,name=num,proto3" json:"num,omitempty"` // 同时在线的数量
}

func (x *SampleAccountResponse) Reset() {
	*x = SampleAccountResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_account_account_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SampleAccountResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SampleAccountResponse) ProtoMessage() {}

func (x *SampleAccountResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_account_account_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SampleAccountResponse.ProtoReflect.Descriptor instead.
func (*SampleAccountResponse) Descriptor() ([]byte, []int) {
	return file_api_account_account_proto_rawDescGZIP(), []int{11}
}

func (x *SampleAccountResponse) GetIsNowAlreadyLogin() bool {
	if x != nil {
		return x.IsNowAlreadyLogin
	}
	return false
}

func (x *SampleAccountResponse) GetNum() uint32 {
	if x != nil {
		return x.Num
	}
	return 0
}

type LoginAndSqueezeOtherResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	NeedDetectImageUrl string `protobuf:"bytes,1,opt,name=needDetectImageUrl,proto3" json:"needDetectImageUrl,omitempty"`
	RecentImageUrl     string `protobuf:"bytes,2,opt,name=recentImageUrl,proto3" json:"recentImageUrl,omitempty"`
}

func (x *LoginAndSqueezeOtherResponse) Reset() {
	*x = LoginAndSqueezeOtherResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_account_account_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LoginAndSqueezeOtherResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LoginAndSqueezeOtherResponse) ProtoMessage() {}

func (x *LoginAndSqueezeOtherResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_account_account_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LoginAndSqueezeOtherResponse.ProtoReflect.Descriptor instead.
func (*LoginAndSqueezeOtherResponse) Descriptor() ([]byte, []int) {
	return file_api_account_account_proto_rawDescGZIP(), []int{12}
}

func (x *LoginAndSqueezeOtherResponse) GetNeedDetectImageUrl() string {
	if x != nil {
		return x.NeedDetectImageUrl
	}
	return ""
}

func (x *LoginAndSqueezeOtherResponse) GetRecentImageUrl() string {
	if x != nil {
		return x.RecentImageUrl
	}
	return ""
}

type IsSamePersonResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	IsPass  bool   `protobuf:"varint,1,opt,name=isPass,proto3" json:"isPass,omitempty"`
	Rate    uint32 `protobuf:"varint,2,opt,name=rate,proto3" json:"rate,omitempty"`      //相似度
	Message string `protobuf:"bytes,3,opt,name=message,proto3" json:"message,omitempty"` //相似度
}

func (x *IsSamePersonResponse) Reset() {
	*x = IsSamePersonResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_account_account_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *IsSamePersonResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*IsSamePersonResponse) ProtoMessage() {}

func (x *IsSamePersonResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_account_account_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use IsSamePersonResponse.ProtoReflect.Descriptor instead.
func (*IsSamePersonResponse) Descriptor() ([]byte, []int) {
	return file_api_account_account_proto_rawDescGZIP(), []int{13}
}

func (x *IsSamePersonResponse) GetIsPass() bool {
	if x != nil {
		return x.IsPass
	}
	return false
}

func (x *IsSamePersonResponse) GetRate() uint32 {
	if x != nil {
		return x.Rate
	}
	return 0
}

func (x *IsSamePersonResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

type IsSamePersonRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	NeedDetectImageUrl string `protobuf:"bytes,1,opt,name=needDetectImageUrl,proto3" json:"needDetectImageUrl,omitempty"`
	RecentImageUrl     string `protobuf:"bytes,2,opt,name=recentImageUrl,proto3" json:"recentImageUrl,omitempty"`
}

func (x *IsSamePersonRequest) Reset() {
	*x = IsSamePersonRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_account_account_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *IsSamePersonRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*IsSamePersonRequest) ProtoMessage() {}

func (x *IsSamePersonRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_account_account_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use IsSamePersonRequest.ProtoReflect.Descriptor instead.
func (*IsSamePersonRequest) Descriptor() ([]byte, []int) {
	return file_api_account_account_proto_rawDescGZIP(), []int{14}
}

func (x *IsSamePersonRequest) GetNeedDetectImageUrl() string {
	if x != nil {
		return x.NeedDetectImageUrl
	}
	return ""
}

func (x *IsSamePersonRequest) GetRecentImageUrl() string {
	if x != nil {
		return x.RecentImageUrl
	}
	return ""
}

type UpdatePassportStatusRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ID     uint32 `protobuf:"varint,1,opt,name=ID,proto3" json:"ID,omitempty"`
	Status uint32 `protobuf:"varint,2,opt,name=status,proto3" json:"status,omitempty"`
}

func (x *UpdatePassportStatusRequest) Reset() {
	*x = UpdatePassportStatusRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_account_account_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdatePassportStatusRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdatePassportStatusRequest) ProtoMessage() {}

func (x *UpdatePassportStatusRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_account_account_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdatePassportStatusRequest.ProtoReflect.Descriptor instead.
func (*UpdatePassportStatusRequest) Descriptor() ([]byte, []int) {
	return file_api_account_account_proto_rawDescGZIP(), []int{15}
}

func (x *UpdatePassportStatusRequest) GetID() uint32 {
	if x != nil {
		return x.ID
	}
	return 0
}

func (x *UpdatePassportStatusRequest) GetStatus() uint32 {
	if x != nil {
		return x.Status
	}
	return 0
}

type CreateChainAccountResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Account   string `protobuf:"bytes,1,opt,name=account,proto3" json:"account,omitempty"`
	Men       string `protobuf:"bytes,2,opt,name=men,proto3" json:"men,omitempty"`
	PublicKey string `protobuf:"bytes,3,opt,name=publicKey,proto3" json:"publicKey,omitempty"`
}

func (x *CreateChainAccountResponse) Reset() {
	*x = CreateChainAccountResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_account_account_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateChainAccountResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateChainAccountResponse) ProtoMessage() {}

func (x *CreateChainAccountResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_account_account_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateChainAccountResponse.ProtoReflect.Descriptor instead.
func (*CreateChainAccountResponse) Descriptor() ([]byte, []int) {
	return file_api_account_account_proto_rawDescGZIP(), []int{16}
}

func (x *CreateChainAccountResponse) GetAccount() string {
	if x != nil {
		return x.Account
	}
	return ""
}

func (x *CreateChainAccountResponse) GetMen() string {
	if x != nil {
		return x.Men
	}
	return ""
}

func (x *CreateChainAccountResponse) GetPublicKey() string {
	if x != nil {
		return x.PublicKey
	}
	return ""
}

type UsersByJobNumRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Domain string   `protobuf:"bytes,1,opt,name=domain,proto3" json:"domain,omitempty"`
	JobNum []string `protobuf:"bytes,2,rep,name=jobNum,proto3" json:"jobNum,omitempty"`
}

func (x *UsersByJobNumRequest) Reset() {
	*x = UsersByJobNumRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_account_account_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UsersByJobNumRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UsersByJobNumRequest) ProtoMessage() {}

func (x *UsersByJobNumRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_account_account_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UsersByJobNumRequest.ProtoReflect.Descriptor instead.
func (*UsersByJobNumRequest) Descriptor() ([]byte, []int) {
	return file_api_account_account_proto_rawDescGZIP(), []int{17}
}

func (x *UsersByJobNumRequest) GetDomain() string {
	if x != nil {
		return x.Domain
	}
	return ""
}

func (x *UsersByJobNumRequest) GetJobNum() []string {
	if x != nil {
		return x.JobNum
	}
	return nil
}

type QueryPersonnelWithTheSameNameRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Names  []string `protobuf:"bytes,1,rep,name=names,proto3" json:"names,omitempty"`
	Domain string   `protobuf:"bytes,2,opt,name=domain,proto3" json:"domain,omitempty"`
	Status string   `protobuf:"bytes,3,opt,name=status,proto3" json:"status,omitempty"`
}

func (x *QueryPersonnelWithTheSameNameRequest) Reset() {
	*x = QueryPersonnelWithTheSameNameRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_account_account_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *QueryPersonnelWithTheSameNameRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QueryPersonnelWithTheSameNameRequest) ProtoMessage() {}

func (x *QueryPersonnelWithTheSameNameRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_account_account_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QueryPersonnelWithTheSameNameRequest.ProtoReflect.Descriptor instead.
func (*QueryPersonnelWithTheSameNameRequest) Descriptor() ([]byte, []int) {
	return file_api_account_account_proto_rawDescGZIP(), []int{18}
}

func (x *QueryPersonnelWithTheSameNameRequest) GetNames() []string {
	if x != nil {
		return x.Names
	}
	return nil
}

func (x *QueryPersonnelWithTheSameNameRequest) GetDomain() string {
	if x != nil {
		return x.Domain
	}
	return ""
}

func (x *QueryPersonnelWithTheSameNameRequest) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

type QueryPersonnelWithTheSameNameResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Names []string `protobuf:"bytes,1,rep,name=names,proto3" json:"names,omitempty"`
	Count uint64   `protobuf:"varint,2,opt,name=count,proto3" json:"count,omitempty"`
}

func (x *QueryPersonnelWithTheSameNameResponse) Reset() {
	*x = QueryPersonnelWithTheSameNameResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_account_account_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *QueryPersonnelWithTheSameNameResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QueryPersonnelWithTheSameNameResponse) ProtoMessage() {}

func (x *QueryPersonnelWithTheSameNameResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_account_account_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QueryPersonnelWithTheSameNameResponse.ProtoReflect.Descriptor instead.
func (*QueryPersonnelWithTheSameNameResponse) Descriptor() ([]byte, []int) {
	return file_api_account_account_proto_rawDescGZIP(), []int{19}
}

func (x *QueryPersonnelWithTheSameNameResponse) GetNames() []string {
	if x != nil {
		return x.Names
	}
	return nil
}

func (x *QueryPersonnelWithTheSameNameResponse) GetCount() uint64 {
	if x != nil {
		return x.Count
	}
	return 0
}

type ListV2Request struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Domain             string   `protobuf:"bytes,1,opt,name=domain,proto3" json:"domain,omitempty"`
	PageSize           uint64   `protobuf:"varint,2,opt,name=pageSize,proto3" json:"pageSize,omitempty"`
	Page               uint64   `protobuf:"varint,3,opt,name=page,proto3" json:"page,omitempty"`
	Key                string   `protobuf:"bytes,4,opt,name=key,proto3" json:"key,omitempty"`
	NickName           string   `protobuf:"bytes,5,opt,name=nickName,proto3" json:"nickName,omitempty"`
	TelNum             string   `protobuf:"bytes,6,opt,name=telNum,proto3" json:"telNum,omitempty"`
	Status             string   `protobuf:"bytes,7,opt,name=status,proto3" json:"status,omitempty"`
	PositionName       string   `protobuf:"bytes,8,opt,name=positionName,proto3" json:"positionName,omitempty"`
	JobNum             string   `protobuf:"bytes,9,opt,name=jobNum,proto3" json:"jobNum,omitempty"`
	MailAccount        string   `protobuf:"bytes,10,opt,name=mailAccount,proto3" json:"mailAccount,omitempty"`
	StartEnterDate     string   `protobuf:"bytes,11,opt,name=startEnterDate,proto3" json:"startEnterDate,omitempty"`
	EndEnterDate       string   `protobuf:"bytes,12,opt,name=endEnterDate,proto3" json:"endEnterDate,omitempty"`
	PositionId         uint32   `protobuf:"varint,13,opt,name=positionId,proto3" json:"positionId,omitempty"`
	DepartmentId       uint32   `protobuf:"varint,14,opt,name=departmentId,proto3" json:"departmentId,omitempty"`
	DepartmentName     string   `protobuf:"bytes,15,opt,name=departmentName,proto3" json:"departmentName,omitempty"`
	DepartmentNames    []string `protobuf:"bytes,16,rep,name=departmentNames,proto3" json:"departmentNames,omitempty"`
	PositionIds        []uint32 `protobuf:"varint,17,rep,packed,name=positionIds,proto3" json:"positionIds,omitempty"`
	DepartmentIds      []uint32 `protobuf:"varint,18,rep,packed,name=departmentIds,proto3" json:"departmentIds,omitempty"`
	FatherDepartmentId uint32   `protobuf:"varint,19,opt,name=fatherDepartmentId,proto3" json:"fatherDepartmentId,omitempty"`
	BlurTelName        string   `protobuf:"bytes,20,opt,name=blurTelName,proto3" json:"blurTelName,omitempty"`
}

func (x *ListV2Request) Reset() {
	*x = ListV2Request{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_account_account_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListV2Request) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListV2Request) ProtoMessage() {}

func (x *ListV2Request) ProtoReflect() protoreflect.Message {
	mi := &file_api_account_account_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListV2Request.ProtoReflect.Descriptor instead.
func (*ListV2Request) Descriptor() ([]byte, []int) {
	return file_api_account_account_proto_rawDescGZIP(), []int{20}
}

func (x *ListV2Request) GetDomain() string {
	if x != nil {
		return x.Domain
	}
	return ""
}

func (x *ListV2Request) GetPageSize() uint64 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *ListV2Request) GetPage() uint64 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *ListV2Request) GetKey() string {
	if x != nil {
		return x.Key
	}
	return ""
}

func (x *ListV2Request) GetNickName() string {
	if x != nil {
		return x.NickName
	}
	return ""
}

func (x *ListV2Request) GetTelNum() string {
	if x != nil {
		return x.TelNum
	}
	return ""
}

func (x *ListV2Request) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

func (x *ListV2Request) GetPositionName() string {
	if x != nil {
		return x.PositionName
	}
	return ""
}

func (x *ListV2Request) GetJobNum() string {
	if x != nil {
		return x.JobNum
	}
	return ""
}

func (x *ListV2Request) GetMailAccount() string {
	if x != nil {
		return x.MailAccount
	}
	return ""
}

func (x *ListV2Request) GetStartEnterDate() string {
	if x != nil {
		return x.StartEnterDate
	}
	return ""
}

func (x *ListV2Request) GetEndEnterDate() string {
	if x != nil {
		return x.EndEnterDate
	}
	return ""
}

func (x *ListV2Request) GetPositionId() uint32 {
	if x != nil {
		return x.PositionId
	}
	return 0
}

func (x *ListV2Request) GetDepartmentId() uint32 {
	if x != nil {
		return x.DepartmentId
	}
	return 0
}

func (x *ListV2Request) GetDepartmentName() string {
	if x != nil {
		return x.DepartmentName
	}
	return ""
}

func (x *ListV2Request) GetDepartmentNames() []string {
	if x != nil {
		return x.DepartmentNames
	}
	return nil
}

func (x *ListV2Request) GetPositionIds() []uint32 {
	if x != nil {
		return x.PositionIds
	}
	return nil
}

func (x *ListV2Request) GetDepartmentIds() []uint32 {
	if x != nil {
		return x.DepartmentIds
	}
	return nil
}

func (x *ListV2Request) GetFatherDepartmentId() uint32 {
	if x != nil {
		return x.FatherDepartmentId
	}
	return 0
}

func (x *ListV2Request) GetBlurTelName() string {
	if x != nil {
		return x.BlurTelName
	}
	return ""
}

type SendClockInWechatRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Domain     string `protobuf:"bytes,1,opt,name=domain,proto3" json:"domain,omitempty"`
	TelNum     string `protobuf:"bytes,2,opt,name=telNum,proto3" json:"telNum,omitempty"`
	OperatedAt string `protobuf:"bytes,3,opt,name=operatedAt,proto3" json:"operatedAt,omitempty"`
	ClockType  string `protobuf:"bytes,4,opt,name=clockType,proto3" json:"clockType,omitempty"`
	UserId     uint32 `protobuf:"varint,5,opt,name=userId,proto3" json:"userId,omitempty"`
	GhId       string `protobuf:"bytes,6,opt,name=ghId,proto3" json:"ghId,omitempty"`
	Address    string `protobuf:"bytes,7,opt,name=address,proto3" json:"address,omitempty"`
	LogId      uint64 `protobuf:"varint,8,opt,name=logId,proto3" json:"logId,omitempty"`
}

func (x *SendClockInWechatRequest) Reset() {
	*x = SendClockInWechatRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_account_account_proto_msgTypes[21]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SendClockInWechatRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SendClockInWechatRequest) ProtoMessage() {}

func (x *SendClockInWechatRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_account_account_proto_msgTypes[21]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SendClockInWechatRequest.ProtoReflect.Descriptor instead.
func (*SendClockInWechatRequest) Descriptor() ([]byte, []int) {
	return file_api_account_account_proto_rawDescGZIP(), []int{21}
}

func (x *SendClockInWechatRequest) GetDomain() string {
	if x != nil {
		return x.Domain
	}
	return ""
}

func (x *SendClockInWechatRequest) GetTelNum() string {
	if x != nil {
		return x.TelNum
	}
	return ""
}

func (x *SendClockInWechatRequest) GetOperatedAt() string {
	if x != nil {
		return x.OperatedAt
	}
	return ""
}

func (x *SendClockInWechatRequest) GetClockType() string {
	if x != nil {
		return x.ClockType
	}
	return ""
}

func (x *SendClockInWechatRequest) GetUserId() uint32 {
	if x != nil {
		return x.UserId
	}
	return 0
}

func (x *SendClockInWechatRequest) GetGhId() string {
	if x != nil {
		return x.GhId
	}
	return ""
}

func (x *SendClockInWechatRequest) GetAddress() string {
	if x != nil {
		return x.Address
	}
	return ""
}

func (x *SendClockInWechatRequest) GetLogId() uint64 {
	if x != nil {
		return x.LogId
	}
	return 0
}

type MailAccountByNickNameRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Domain   string `protobuf:"bytes,1,opt,name=Domain,json=domain,proto3" json:"Domain,omitempty"`
	NickName string `protobuf:"bytes,2,opt,name=NickName,json=nickName,proto3" json:"NickName,omitempty"`
	ID       uint32 `protobuf:"varint,3,opt,name=ID,proto3" json:"ID,omitempty"`
}

func (x *MailAccountByNickNameRequest) Reset() {
	*x = MailAccountByNickNameRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_account_account_proto_msgTypes[22]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MailAccountByNickNameRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MailAccountByNickNameRequest) ProtoMessage() {}

func (x *MailAccountByNickNameRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_account_account_proto_msgTypes[22]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MailAccountByNickNameRequest.ProtoReflect.Descriptor instead.
func (*MailAccountByNickNameRequest) Descriptor() ([]byte, []int) {
	return file_api_account_account_proto_rawDescGZIP(), []int{22}
}

func (x *MailAccountByNickNameRequest) GetDomain() string {
	if x != nil {
		return x.Domain
	}
	return ""
}

func (x *MailAccountByNickNameRequest) GetNickName() string {
	if x != nil {
		return x.NickName
	}
	return ""
}

func (x *MailAccountByNickNameRequest) GetID() uint32 {
	if x != nil {
		return x.ID
	}
	return 0
}

type CreateMaiAccountRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ID       uint32 `protobuf:"varint,1,opt,name=ID,proto3" json:"ID,omitempty"`
	NickName string `protobuf:"bytes,2,opt,name=nickName,proto3" json:"nickName,omitempty"`
	Domain   string `protobuf:"bytes,3,opt,name=domain,proto3" json:"domain,omitempty"`
}

func (x *CreateMaiAccountRequest) Reset() {
	*x = CreateMaiAccountRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_account_account_proto_msgTypes[23]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateMaiAccountRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateMaiAccountRequest) ProtoMessage() {}

func (x *CreateMaiAccountRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_account_account_proto_msgTypes[23]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateMaiAccountRequest.ProtoReflect.Descriptor instead.
func (*CreateMaiAccountRequest) Descriptor() ([]byte, []int) {
	return file_api_account_account_proto_rawDescGZIP(), []int{23}
}

func (x *CreateMaiAccountRequest) GetID() uint32 {
	if x != nil {
		return x.ID
	}
	return 0
}

func (x *CreateMaiAccountRequest) GetNickName() string {
	if x != nil {
		return x.NickName
	}
	return ""
}

func (x *CreateMaiAccountRequest) GetDomain() string {
	if x != nil {
		return x.Domain
	}
	return ""
}

type MaiAccountResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	EnglishName string `protobuf:"bytes,1,opt,name=englishName,proto3" json:"englishName,omitempty"`
	MailAccount string `protobuf:"bytes,2,opt,name=mailAccount,proto3" json:"mailAccount,omitempty"`
}

func (x *MaiAccountResponse) Reset() {
	*x = MaiAccountResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_account_account_proto_msgTypes[24]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MaiAccountResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MaiAccountResponse) ProtoMessage() {}

func (x *MaiAccountResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_account_account_proto_msgTypes[24]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MaiAccountResponse.ProtoReflect.Descriptor instead.
func (*MaiAccountResponse) Descriptor() ([]byte, []int) {
	return file_api_account_account_proto_rawDescGZIP(), []int{24}
}

func (x *MaiAccountResponse) GetEnglishName() string {
	if x != nil {
		return x.EnglishName
	}
	return ""
}

func (x *MaiAccountResponse) GetMailAccount() string {
	if x != nil {
		return x.MailAccount
	}
	return ""
}

type FddRemoveUserRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	WxUserId uint32 `protobuf:"varint,2,opt,name=wxUserId,proto3" json:"wxUserId,omitempty"`
}

func (x *FddRemoveUserRequest) Reset() {
	*x = FddRemoveUserRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_account_account_proto_msgTypes[25]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FddRemoveUserRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FddRemoveUserRequest) ProtoMessage() {}

func (x *FddRemoveUserRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_account_account_proto_msgTypes[25]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FddRemoveUserRequest.ProtoReflect.Descriptor instead.
func (*FddRemoveUserRequest) Descriptor() ([]byte, []int) {
	return file_api_account_account_proto_rawDescGZIP(), []int{25}
}

func (x *FddRemoveUserRequest) GetWxUserId() uint32 {
	if x != nil {
		return x.WxUserId
	}
	return 0
}

type FddCreateUserRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Openid        string `protobuf:"bytes,1,opt,name=openid,proto3" json:"openid,omitempty"`
	WxUserId      uint32 `protobuf:"varint,2,opt,name=wxUserId,proto3" json:"wxUserId,omitempty"`
	UserId        uint64 `protobuf:"varint,3,opt,name=UserId,proto3" json:"UserId,omitempty"`
	CustomerId    string `protobuf:"bytes,4,opt,name=customerId,proto3" json:"customerId,omitempty"`
	IsVerify      bool   `protobuf:"varint,5,opt,name=isVerify,proto3" json:"isVerify,omitempty"`
	TransactionNo string `protobuf:"bytes,6,opt,name=transactionNo,proto3" json:"transactionNo,omitempty"`
	Status        uint32 `protobuf:"varint,7,opt,name=status,proto3" json:"status,omitempty"`
	IdType        string `protobuf:"bytes,8,opt,name=idType,proto3" json:"idType,omitempty"`
}

func (x *FddCreateUserRequest) Reset() {
	*x = FddCreateUserRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_account_account_proto_msgTypes[26]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FddCreateUserRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FddCreateUserRequest) ProtoMessage() {}

func (x *FddCreateUserRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_account_account_proto_msgTypes[26]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FddCreateUserRequest.ProtoReflect.Descriptor instead.
func (*FddCreateUserRequest) Descriptor() ([]byte, []int) {
	return file_api_account_account_proto_rawDescGZIP(), []int{26}
}

func (x *FddCreateUserRequest) GetOpenid() string {
	if x != nil {
		return x.Openid
	}
	return ""
}

func (x *FddCreateUserRequest) GetWxUserId() uint32 {
	if x != nil {
		return x.WxUserId
	}
	return 0
}

func (x *FddCreateUserRequest) GetUserId() uint64 {
	if x != nil {
		return x.UserId
	}
	return 0
}

func (x *FddCreateUserRequest) GetCustomerId() string {
	if x != nil {
		return x.CustomerId
	}
	return ""
}

func (x *FddCreateUserRequest) GetIsVerify() bool {
	if x != nil {
		return x.IsVerify
	}
	return false
}

func (x *FddCreateUserRequest) GetTransactionNo() string {
	if x != nil {
		return x.TransactionNo
	}
	return ""
}

func (x *FddCreateUserRequest) GetStatus() uint32 {
	if x != nil {
		return x.Status
	}
	return 0
}

func (x *FddCreateUserRequest) GetIdType() string {
	if x != nil {
		return x.IdType
	}
	return ""
}

type WxBoxUserInfoRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Openid string `protobuf:"bytes,2,opt,name=openid,proto3" json:"openid,omitempty"`
	GhId   string `protobuf:"bytes,3,opt,name=ghId,proto3" json:"ghId,omitempty"`
}

func (x *WxBoxUserInfoRequest) Reset() {
	*x = WxBoxUserInfoRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_account_account_proto_msgTypes[27]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *WxBoxUserInfoRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WxBoxUserInfoRequest) ProtoMessage() {}

func (x *WxBoxUserInfoRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_account_account_proto_msgTypes[27]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WxBoxUserInfoRequest.ProtoReflect.Descriptor instead.
func (*WxBoxUserInfoRequest) Descriptor() ([]byte, []int) {
	return file_api_account_account_proto_rawDescGZIP(), []int{27}
}

func (x *WxBoxUserInfoRequest) GetOpenid() string {
	if x != nil {
		return x.Openid
	}
	return ""
}

func (x *WxBoxUserInfoRequest) GetGhId() string {
	if x != nil {
		return x.GhId
	}
	return ""
}

type WxGetOpenIdByCodeRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code  string `protobuf:"bytes,1,opt,name=code,proto3" json:"code,omitempty"`
	State string `protobuf:"bytes,2,opt,name=state,proto3" json:"state,omitempty"`
	GhId  string `protobuf:"bytes,3,opt,name=ghId,proto3" json:"ghId,omitempty"`
}

func (x *WxGetOpenIdByCodeRequest) Reset() {
	*x = WxGetOpenIdByCodeRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_account_account_proto_msgTypes[28]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *WxGetOpenIdByCodeRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WxGetOpenIdByCodeRequest) ProtoMessage() {}

func (x *WxGetOpenIdByCodeRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_account_account_proto_msgTypes[28]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WxGetOpenIdByCodeRequest.ProtoReflect.Descriptor instead.
func (*WxGetOpenIdByCodeRequest) Descriptor() ([]byte, []int) {
	return file_api_account_account_proto_rawDescGZIP(), []int{28}
}

func (x *WxGetOpenIdByCodeRequest) GetCode() string {
	if x != nil {
		return x.Code
	}
	return ""
}

func (x *WxGetOpenIdByCodeRequest) GetState() string {
	if x != nil {
		return x.State
	}
	return ""
}

func (x *WxGetOpenIdByCodeRequest) GetGhId() string {
	if x != nil {
		return x.GhId
	}
	return ""
}

type WxGetOpenIdByCodeResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	OpenId string `protobuf:"bytes,1,opt,name=openId,proto3" json:"openId,omitempty"`
}

func (x *WxGetOpenIdByCodeResponse) Reset() {
	*x = WxGetOpenIdByCodeResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_account_account_proto_msgTypes[29]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *WxGetOpenIdByCodeResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WxGetOpenIdByCodeResponse) ProtoMessage() {}

func (x *WxGetOpenIdByCodeResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_account_account_proto_msgTypes[29]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WxGetOpenIdByCodeResponse.ProtoReflect.Descriptor instead.
func (*WxGetOpenIdByCodeResponse) Descriptor() ([]byte, []int) {
	return file_api_account_account_proto_rawDescGZIP(), []int{29}
}

func (x *WxGetOpenIdByCodeResponse) GetOpenId() string {
	if x != nil {
		return x.OpenId
	}
	return ""
}

type WxBoxTelNumByCodeResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TelNum string `protobuf:"bytes,1,opt,name=telNum,proto3" json:"telNum,omitempty"`
}

func (x *WxBoxTelNumByCodeResponse) Reset() {
	*x = WxBoxTelNumByCodeResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_account_account_proto_msgTypes[30]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *WxBoxTelNumByCodeResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WxBoxTelNumByCodeResponse) ProtoMessage() {}

func (x *WxBoxTelNumByCodeResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_account_account_proto_msgTypes[30]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WxBoxTelNumByCodeResponse.ProtoReflect.Descriptor instead.
func (*WxBoxTelNumByCodeResponse) Descriptor() ([]byte, []int) {
	return file_api_account_account_proto_rawDescGZIP(), []int{30}
}

func (x *WxBoxTelNumByCodeResponse) GetTelNum() string {
	if x != nil {
		return x.TelNum
	}
	return ""
}

type WxBoxUserInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	OpenId   string    `protobuf:"bytes,1,opt,name=openId,proto3" json:"openId,omitempty"`
	GhId     string    `protobuf:"bytes,2,opt,name=ghId,proto3" json:"ghId,omitempty"`
	IsNew    bool      `protobuf:"varint,3,opt,name=isNew,proto3" json:"isNew,omitempty"`
	User     *UserInfo `protobuf:"bytes,4,opt,name=user,proto3" json:"user,omitempty"`
	WxUserId uint32    `protobuf:"varint,5,opt,name=wxUserId,proto3" json:"wxUserId,omitempty"`
	Fdd      *FddInfo  `protobuf:"bytes,6,opt,name=fdd,proto3" json:"fdd,omitempty"`
}

func (x *WxBoxUserInfo) Reset() {
	*x = WxBoxUserInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_account_account_proto_msgTypes[31]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *WxBoxUserInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WxBoxUserInfo) ProtoMessage() {}

func (x *WxBoxUserInfo) ProtoReflect() protoreflect.Message {
	mi := &file_api_account_account_proto_msgTypes[31]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WxBoxUserInfo.ProtoReflect.Descriptor instead.
func (*WxBoxUserInfo) Descriptor() ([]byte, []int) {
	return file_api_account_account_proto_rawDescGZIP(), []int{31}
}

func (x *WxBoxUserInfo) GetOpenId() string {
	if x != nil {
		return x.OpenId
	}
	return ""
}

func (x *WxBoxUserInfo) GetGhId() string {
	if x != nil {
		return x.GhId
	}
	return ""
}

func (x *WxBoxUserInfo) GetIsNew() bool {
	if x != nil {
		return x.IsNew
	}
	return false
}

func (x *WxBoxUserInfo) GetUser() *UserInfo {
	if x != nil {
		return x.User
	}
	return nil
}

func (x *WxBoxUserInfo) GetWxUserId() uint32 {
	if x != nil {
		return x.WxUserId
	}
	return 0
}

func (x *WxBoxUserInfo) GetFdd() *FddInfo {
	if x != nil {
		return x.Fdd
	}
	return nil
}

type FddInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ID            uint64 `protobuf:"varint,1,opt,name=ID,proto3" json:"ID,omitempty"`
	CustomerId    string `protobuf:"bytes,2,opt,name=customerId,proto3" json:"customerId,omitempty"`
	IsVerify      bool   `protobuf:"varint,3,opt,name=isVerify,proto3" json:"isVerify,omitempty"`
	TransactionNo string `protobuf:"bytes,4,opt,name=transactionNo,proto3" json:"transactionNo,omitempty"`
	Status        uint32 `protobuf:"varint,5,opt,name=status,proto3" json:"status,omitempty"`
	IdType        string `protobuf:"bytes,6,opt,name=idType,proto3" json:"idType,omitempty"`
}

func (x *FddInfo) Reset() {
	*x = FddInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_account_account_proto_msgTypes[32]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FddInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FddInfo) ProtoMessage() {}

func (x *FddInfo) ProtoReflect() protoreflect.Message {
	mi := &file_api_account_account_proto_msgTypes[32]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FddInfo.ProtoReflect.Descriptor instead.
func (*FddInfo) Descriptor() ([]byte, []int) {
	return file_api_account_account_proto_rawDescGZIP(), []int{32}
}

func (x *FddInfo) GetID() uint64 {
	if x != nil {
		return x.ID
	}
	return 0
}

func (x *FddInfo) GetCustomerId() string {
	if x != nil {
		return x.CustomerId
	}
	return ""
}

func (x *FddInfo) GetIsVerify() bool {
	if x != nil {
		return x.IsVerify
	}
	return false
}

func (x *FddInfo) GetTransactionNo() string {
	if x != nil {
		return x.TransactionNo
	}
	return ""
}

func (x *FddInfo) GetStatus() uint32 {
	if x != nil {
		return x.Status
	}
	return 0
}

func (x *FddInfo) GetIdType() string {
	if x != nil {
		return x.IdType
	}
	return ""
}

type UserInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ID             uint64    `protobuf:"varint,1,opt,name=ID,proto3" json:"ID,omitempty"`
	NickName       string    `protobuf:"bytes,3,opt,name=nickName,proto3" json:"nickName,omitempty"`
	TelNum         string    `protobuf:"bytes,5,opt,name=telNum,proto3" json:"telNum,omitempty"`
	Avatar         string    `protobuf:"bytes,7,opt,name=avatar,proto3" json:"avatar,omitempty"`
	CreateAt       string    `protobuf:"bytes,8,opt,name=createAt,proto3" json:"createAt,omitempty"`
	RealNameID     uint64    `protobuf:"varint,9,opt,name=realNameID,proto3" json:"realNameID,omitempty"`
	RealName       string    `protobuf:"bytes,10,opt,name=realName,proto3" json:"realName,omitempty"`
	IDNum          string    `protobuf:"bytes,11,opt,name=iDNum,proto3" json:"iDNum,omitempty"`
	Domain         string    `protobuf:"bytes,12,opt,name=domain,proto3" json:"domain,omitempty"`
	RealIDImgA     string    `protobuf:"bytes,17,opt,name=realIDImgA,proto3" json:"realIDImgA,omitempty"`
	RealIDImgB     string    `protobuf:"bytes,18,opt,name=realIDImgB,proto3" json:"realIDImgB,omitempty"`
	RealNameIDName string    `protobuf:"bytes,19,opt,name=realNameIDName,proto3" json:"realNameIDName,omitempty"`
	Video          string    `protobuf:"bytes,20,opt,name=video,proto3" json:"video,omitempty"`
	IdType         string    `protobuf:"bytes,21,opt,name=idType,proto3" json:"idType,omitempty"`     //0 护照 港澳台 护照 通信证等
	Passport       *Passport `protobuf:"bytes,22,opt,name=passport,proto3" json:"passport,omitempty"` //护照 港澳台 护照 通信证等
}

func (x *UserInfo) Reset() {
	*x = UserInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_account_account_proto_msgTypes[33]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UserInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserInfo) ProtoMessage() {}

func (x *UserInfo) ProtoReflect() protoreflect.Message {
	mi := &file_api_account_account_proto_msgTypes[33]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserInfo.ProtoReflect.Descriptor instead.
func (*UserInfo) Descriptor() ([]byte, []int) {
	return file_api_account_account_proto_rawDescGZIP(), []int{33}
}

func (x *UserInfo) GetID() uint64 {
	if x != nil {
		return x.ID
	}
	return 0
}

func (x *UserInfo) GetNickName() string {
	if x != nil {
		return x.NickName
	}
	return ""
}

func (x *UserInfo) GetTelNum() string {
	if x != nil {
		return x.TelNum
	}
	return ""
}

func (x *UserInfo) GetAvatar() string {
	if x != nil {
		return x.Avatar
	}
	return ""
}

func (x *UserInfo) GetCreateAt() string {
	if x != nil {
		return x.CreateAt
	}
	return ""
}

func (x *UserInfo) GetRealNameID() uint64 {
	if x != nil {
		return x.RealNameID
	}
	return 0
}

func (x *UserInfo) GetRealName() string {
	if x != nil {
		return x.RealName
	}
	return ""
}

func (x *UserInfo) GetIDNum() string {
	if x != nil {
		return x.IDNum
	}
	return ""
}

func (x *UserInfo) GetDomain() string {
	if x != nil {
		return x.Domain
	}
	return ""
}

func (x *UserInfo) GetRealIDImgA() string {
	if x != nil {
		return x.RealIDImgA
	}
	return ""
}

func (x *UserInfo) GetRealIDImgB() string {
	if x != nil {
		return x.RealIDImgB
	}
	return ""
}

func (x *UserInfo) GetRealNameIDName() string {
	if x != nil {
		return x.RealNameIDName
	}
	return ""
}

func (x *UserInfo) GetVideo() string {
	if x != nil {
		return x.Video
	}
	return ""
}

func (x *UserInfo) GetIdType() string {
	if x != nil {
		return x.IdType
	}
	return ""
}

func (x *UserInfo) GetPassport() *Passport {
	if x != nil {
		return x.Passport
	}
	return nil
}

type CommonRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ID   uint64 `protobuf:"varint,1,opt,name=ID,proto3" json:"ID,omitempty"`
	From string `protobuf:"bytes,2,opt,name=from,proto3" json:"from,omitempty"`
}

func (x *CommonRequest) Reset() {
	*x = CommonRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_account_account_proto_msgTypes[34]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CommonRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CommonRequest) ProtoMessage() {}

func (x *CommonRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_account_account_proto_msgTypes[34]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CommonRequest.ProtoReflect.Descriptor instead.
func (*CommonRequest) Descriptor() ([]byte, []int) {
	return file_api_account_account_proto_rawDescGZIP(), []int{34}
}

func (x *CommonRequest) GetID() uint64 {
	if x != nil {
		return x.ID
	}
	return 0
}

func (x *CommonRequest) GetFrom() string {
	if x != nil {
		return x.From
	}
	return ""
}

type WxAppRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	GhId string `protobuf:"bytes,1,opt,name=GhId,json=ID,proto3" json:"GhId,omitempty"`
}

func (x *WxAppRequest) Reset() {
	*x = WxAppRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_account_account_proto_msgTypes[35]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *WxAppRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WxAppRequest) ProtoMessage() {}

func (x *WxAppRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_account_account_proto_msgTypes[35]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WxAppRequest.ProtoReflect.Descriptor instead.
func (*WxAppRequest) Descriptor() ([]byte, []int) {
	return file_api_account_account_proto_rawDescGZIP(), []int{35}
}

func (x *WxAppRequest) GetGhId() string {
	if x != nil {
		return x.GhId
	}
	return ""
}

type WxAppResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AppID     string `protobuf:"bytes,1,opt,name=AppID,json=appID,proto3" json:"AppID,omitempty"`
	AppSecret string `protobuf:"bytes,2,opt,name=AppSecret,json=appSecret,proto3" json:"AppSecret,omitempty"`
}

func (x *WxAppResponse) Reset() {
	*x = WxAppResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_account_account_proto_msgTypes[36]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *WxAppResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WxAppResponse) ProtoMessage() {}

func (x *WxAppResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_account_account_proto_msgTypes[36]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WxAppResponse.ProtoReflect.Descriptor instead.
func (*WxAppResponse) Descriptor() ([]byte, []int) {
	return file_api_account_account_proto_rawDescGZIP(), []int{36}
}

func (x *WxAppResponse) GetAppID() string {
	if x != nil {
		return x.AppID
	}
	return ""
}

func (x *WxAppResponse) GetAppSecret() string {
	if x != nil {
		return x.AppSecret
	}
	return ""
}

type WxUserUpdateRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	WxID   uint32 `protobuf:"varint,1,opt,name=wxID,json=wxId,proto3" json:"wxID,omitempty"`
	UserID uint32 `protobuf:"varint,2,opt,name=userID,proto3" json:"userID,omitempty"`
}

func (x *WxUserUpdateRequest) Reset() {
	*x = WxUserUpdateRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_account_account_proto_msgTypes[37]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *WxUserUpdateRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WxUserUpdateRequest) ProtoMessage() {}

func (x *WxUserUpdateRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_account_account_proto_msgTypes[37]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WxUserUpdateRequest.ProtoReflect.Descriptor instead.
func (*WxUserUpdateRequest) Descriptor() ([]byte, []int) {
	return file_api_account_account_proto_rawDescGZIP(), []int{37}
}

func (x *WxUserUpdateRequest) GetWxID() uint32 {
	if x != nil {
		return x.WxID
	}
	return 0
}

func (x *WxUserUpdateRequest) GetUserID() uint32 {
	if x != nil {
		return x.UserID
	}
	return 0
}

type WxUserOrCreateRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	OpenID string `protobuf:"bytes,1,opt,name=OpenID,json=openID,proto3" json:"OpenID,omitempty"`
	GhID   string `protobuf:"bytes,2,opt,name=GhID,json=ghID,proto3" json:"GhID,omitempty"`
}

func (x *WxUserOrCreateRequest) Reset() {
	*x = WxUserOrCreateRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_account_account_proto_msgTypes[38]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *WxUserOrCreateRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WxUserOrCreateRequest) ProtoMessage() {}

func (x *WxUserOrCreateRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_account_account_proto_msgTypes[38]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WxUserOrCreateRequest.ProtoReflect.Descriptor instead.
func (*WxUserOrCreateRequest) Descriptor() ([]byte, []int) {
	return file_api_account_account_proto_rawDescGZIP(), []int{38}
}

func (x *WxUserOrCreateRequest) GetOpenID() string {
	if x != nil {
		return x.OpenID
	}
	return ""
}

func (x *WxUserOrCreateRequest) GetGhID() string {
	if x != nil {
		return x.GhID
	}
	return ""
}

type WxUserResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	OpenID   string `protobuf:"bytes,1,opt,name=OpenID,json=openID,proto3" json:"OpenID,omitempty"`
	UserID   uint32 `protobuf:"varint,2,opt,name=UserID,json=userID,proto3" json:"UserID,omitempty"`
	GhID     string `protobuf:"bytes,3,opt,name=GhID,json=ghID,proto3" json:"GhID,omitempty"`
	RoleAuth string `protobuf:"bytes,4,opt,name=RoleAuth,json=roleAuth,proto3" json:"RoleAuth,omitempty"`
	ID       uint32 `protobuf:"varint,5,opt,name=ID,proto3" json:"ID,omitempty"`
}

func (x *WxUserResponse) Reset() {
	*x = WxUserResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_account_account_proto_msgTypes[39]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *WxUserResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WxUserResponse) ProtoMessage() {}

func (x *WxUserResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_account_account_proto_msgTypes[39]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WxUserResponse.ProtoReflect.Descriptor instead.
func (*WxUserResponse) Descriptor() ([]byte, []int) {
	return file_api_account_account_proto_rawDescGZIP(), []int{39}
}

func (x *WxUserResponse) GetOpenID() string {
	if x != nil {
		return x.OpenID
	}
	return ""
}

func (x *WxUserResponse) GetUserID() uint32 {
	if x != nil {
		return x.UserID
	}
	return 0
}

func (x *WxUserResponse) GetGhID() string {
	if x != nil {
		return x.GhID
	}
	return ""
}

func (x *WxUserResponse) GetRoleAuth() string {
	if x != nil {
		return x.RoleAuth
	}
	return ""
}

func (x *WxUserResponse) GetID() uint32 {
	if x != nil {
		return x.ID
	}
	return 0
}

type LoginLogsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Data []*LoginLog `protobuf:"bytes,1,rep,name=Data,json=data,proto3" json:"Data,omitempty"`
}

func (x *LoginLogsResponse) Reset() {
	*x = LoginLogsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_account_account_proto_msgTypes[40]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LoginLogsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LoginLogsResponse) ProtoMessage() {}

func (x *LoginLogsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_account_account_proto_msgTypes[40]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LoginLogsResponse.ProtoReflect.Descriptor instead.
func (*LoginLogsResponse) Descriptor() ([]byte, []int) {
	return file_api_account_account_proto_rawDescGZIP(), []int{40}
}

func (x *LoginLogsResponse) GetData() []*LoginLog {
	if x != nil {
		return x.Data
	}
	return nil
}

type LoginLog struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Domain     string `protobuf:"bytes,1,opt,name=Domain,json=domain,proto3" json:"Domain,omitempty"`
	ID         uint64 `protobuf:"varint,2,opt,name=ID,proto3" json:"ID,omitempty"`
	UserId     uint64 `protobuf:"varint,3,opt,name=UserId,json=userId,proto3" json:"UserId,omitempty"`
	Ip         string `protobuf:"bytes,4,opt,name=Ip,json=ip,proto3" json:"Ip,omitempty"`
	Token      string `protobuf:"bytes,5,opt,name=Token,json=token,proto3" json:"Token,omitempty"`
	Status     uint64 `protobuf:"varint,6,opt,name=Status,json=status,proto3" json:"Status,omitempty"`
	ExpireDate string `protobuf:"bytes,7,opt,name=ExpireDate,json=expireDate,proto3" json:"ExpireDate,omitempty"`
	LastDate   string `protobuf:"bytes,8,opt,name=LastDate,json=lastDate,proto3" json:"LastDate,omitempty"`
	LogoutDate string `protobuf:"bytes,9,opt,name=LogoutDate,json=logoutDate,proto3" json:"LogoutDate,omitempty"`
	CreatedAt  string `protobuf:"bytes,10,opt,name=CreatedAt,json=createdAt,proto3" json:"CreatedAt,omitempty"`
	Address    string `protobuf:"bytes,11,opt,name=Address,json=address,proto3" json:"Address,omitempty"`
}

func (x *LoginLog) Reset() {
	*x = LoginLog{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_account_account_proto_msgTypes[41]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LoginLog) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LoginLog) ProtoMessage() {}

func (x *LoginLog) ProtoReflect() protoreflect.Message {
	mi := &file_api_account_account_proto_msgTypes[41]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LoginLog.ProtoReflect.Descriptor instead.
func (*LoginLog) Descriptor() ([]byte, []int) {
	return file_api_account_account_proto_rawDescGZIP(), []int{41}
}

func (x *LoginLog) GetDomain() string {
	if x != nil {
		return x.Domain
	}
	return ""
}

func (x *LoginLog) GetID() uint64 {
	if x != nil {
		return x.ID
	}
	return 0
}

func (x *LoginLog) GetUserId() uint64 {
	if x != nil {
		return x.UserId
	}
	return 0
}

func (x *LoginLog) GetIp() string {
	if x != nil {
		return x.Ip
	}
	return ""
}

func (x *LoginLog) GetToken() string {
	if x != nil {
		return x.Token
	}
	return ""
}

func (x *LoginLog) GetStatus() uint64 {
	if x != nil {
		return x.Status
	}
	return 0
}

func (x *LoginLog) GetExpireDate() string {
	if x != nil {
		return x.ExpireDate
	}
	return ""
}

func (x *LoginLog) GetLastDate() string {
	if x != nil {
		return x.LastDate
	}
	return ""
}

func (x *LoginLog) GetLogoutDate() string {
	if x != nil {
		return x.LogoutDate
	}
	return ""
}

func (x *LoginLog) GetCreatedAt() string {
	if x != nil {
		return x.CreatedAt
	}
	return ""
}

func (x *LoginLog) GetAddress() string {
	if x != nil {
		return x.Address
	}
	return ""
}

type OnlineLogByIdRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Domain string `protobuf:"bytes,1,opt,name=Domain,json=domain,proto3" json:"Domain,omitempty"`
	ID     uint64 `protobuf:"varint,2,opt,name=ID,proto3" json:"ID,omitempty"`
}

func (x *OnlineLogByIdRequest) Reset() {
	*x = OnlineLogByIdRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_account_account_proto_msgTypes[42]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *OnlineLogByIdRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OnlineLogByIdRequest) ProtoMessage() {}

func (x *OnlineLogByIdRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_account_account_proto_msgTypes[42]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OnlineLogByIdRequest.ProtoReflect.Descriptor instead.
func (*OnlineLogByIdRequest) Descriptor() ([]byte, []int) {
	return file_api_account_account_proto_rawDescGZIP(), []int{42}
}

func (x *OnlineLogByIdRequest) GetDomain() string {
	if x != nil {
		return x.Domain
	}
	return ""
}

func (x *OnlineLogByIdRequest) GetID() uint64 {
	if x != nil {
		return x.ID
	}
	return 0
}

type LoginInfosByUserIdRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Domain string `protobuf:"bytes,1,opt,name=Domain,json=domain,proto3" json:"Domain,omitempty"`
	UserId uint64 `protobuf:"varint,2,opt,name=UserId,json=userId,proto3" json:"UserId,omitempty"`
}

func (x *LoginInfosByUserIdRequest) Reset() {
	*x = LoginInfosByUserIdRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_account_account_proto_msgTypes[43]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LoginInfosByUserIdRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LoginInfosByUserIdRequest) ProtoMessage() {}

func (x *LoginInfosByUserIdRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_account_account_proto_msgTypes[43]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LoginInfosByUserIdRequest.ProtoReflect.Descriptor instead.
func (*LoginInfosByUserIdRequest) Descriptor() ([]byte, []int) {
	return file_api_account_account_proto_rawDescGZIP(), []int{43}
}

func (x *LoginInfosByUserIdRequest) GetDomain() string {
	if x != nil {
		return x.Domain
	}
	return ""
}

func (x *LoginInfosByUserIdRequest) GetUserId() uint64 {
	if x != nil {
		return x.UserId
	}
	return 0
}

type SendNewTelNumMsgRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Domain    string `protobuf:"bytes,1,opt,name=Domain,json=domain,proto3" json:"Domain,omitempty"`
	ID        uint64 `protobuf:"varint,2,opt,name=ID,proto3" json:"ID,omitempty"`
	NewTelNum string `protobuf:"bytes,3,opt,name=NewTelNum,json=newTelNum,proto3" json:"NewTelNum,omitempty"`
	Code      string `protobuf:"bytes,4,opt,name=Code,json=code,proto3" json:"Code,omitempty"`
	Project   string `protobuf:"bytes,5,opt,name=Project,json=project,proto3" json:"Project,omitempty"`
	SignNo    uint32 `protobuf:"varint,6,opt,name=signNo,proto3" json:"signNo,omitempty"`
}

func (x *SendNewTelNumMsgRequest) Reset() {
	*x = SendNewTelNumMsgRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_account_account_proto_msgTypes[44]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SendNewTelNumMsgRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SendNewTelNumMsgRequest) ProtoMessage() {}

func (x *SendNewTelNumMsgRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_account_account_proto_msgTypes[44]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SendNewTelNumMsgRequest.ProtoReflect.Descriptor instead.
func (*SendNewTelNumMsgRequest) Descriptor() ([]byte, []int) {
	return file_api_account_account_proto_rawDescGZIP(), []int{44}
}

func (x *SendNewTelNumMsgRequest) GetDomain() string {
	if x != nil {
		return x.Domain
	}
	return ""
}

func (x *SendNewTelNumMsgRequest) GetID() uint64 {
	if x != nil {
		return x.ID
	}
	return 0
}

func (x *SendNewTelNumMsgRequest) GetNewTelNum() string {
	if x != nil {
		return x.NewTelNum
	}
	return ""
}

func (x *SendNewTelNumMsgRequest) GetCode() string {
	if x != nil {
		return x.Code
	}
	return ""
}

func (x *SendNewTelNumMsgRequest) GetProject() string {
	if x != nil {
		return x.Project
	}
	return ""
}

func (x *SendNewTelNumMsgRequest) GetSignNo() uint32 {
	if x != nil {
		return x.SignNo
	}
	return 0
}

type UserByTelRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Domain string `protobuf:"bytes,1,opt,name=Domain,json=domain,proto3" json:"Domain,omitempty"`
	Tel    string `protobuf:"bytes,2,opt,name=Tel,json=tel,proto3" json:"Tel,omitempty"`
}

func (x *UserByTelRequest) Reset() {
	*x = UserByTelRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_account_account_proto_msgTypes[45]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UserByTelRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserByTelRequest) ProtoMessage() {}

func (x *UserByTelRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_account_account_proto_msgTypes[45]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserByTelRequest.ProtoReflect.Descriptor instead.
func (*UserByTelRequest) Descriptor() ([]byte, []int) {
	return file_api_account_account_proto_rawDescGZIP(), []int{45}
}

func (x *UserByTelRequest) GetDomain() string {
	if x != nil {
		return x.Domain
	}
	return ""
}

func (x *UserByTelRequest) GetTel() string {
	if x != nil {
		return x.Tel
	}
	return ""
}

type CommonResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *CommonResponse) Reset() {
	*x = CommonResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_account_account_proto_msgTypes[46]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CommonResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CommonResponse) ProtoMessage() {}

func (x *CommonResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_account_account_proto_msgTypes[46]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CommonResponse.ProtoReflect.Descriptor instead.
func (*CommonResponse) Descriptor() ([]byte, []int) {
	return file_api_account_account_proto_rawDescGZIP(), []int{46}
}

type UsersByTelRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Domain string   `protobuf:"bytes,1,opt,name=Domain,json=domain,proto3" json:"Domain,omitempty"`
	Tels   []string `protobuf:"bytes,2,rep,name=Tels,json=tels,proto3" json:"Tels,omitempty"`
}

func (x *UsersByTelRequest) Reset() {
	*x = UsersByTelRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_account_account_proto_msgTypes[47]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UsersByTelRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UsersByTelRequest) ProtoMessage() {}

func (x *UsersByTelRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_account_account_proto_msgTypes[47]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UsersByTelRequest.ProtoReflect.Descriptor instead.
func (*UsersByTelRequest) Descriptor() ([]byte, []int) {
	return file_api_account_account_proto_rawDescGZIP(), []int{47}
}

func (x *UsersByTelRequest) GetDomain() string {
	if x != nil {
		return x.Domain
	}
	return ""
}

func (x *UsersByTelRequest) GetTels() []string {
	if x != nil {
		return x.Tels
	}
	return nil
}

type ListByIDsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Domain         string   `protobuf:"bytes,1,opt,name=Domain,json=domain,proto3" json:"Domain,omitempty"`
	IDs            []uint64 `protobuf:"varint,2,rep,packed,name=IDs,proto3" json:"IDs,omitempty"`
	OrderType      uint64   `protobuf:"varint,3,opt,name=OrderType,proto3" json:"OrderType,omitempty"`
	Page           uint64   `protobuf:"varint,4,opt,name=Page,json=page,proto3" json:"Page,omitempty"`
	PageSize       uint64   `protobuf:"varint,5,opt,name=PageSize,json=pageSize,proto3" json:"PageSize,omitempty"`
	NickName       string   `protobuf:"bytes,6,opt,name=NickName,json=nickName,proto3" json:"NickName,omitempty"`
	InvitationCode []string `protobuf:"bytes,7,rep,name=InvitationCode,json=invitationCode,proto3" json:"InvitationCode,omitempty"`
	Status         string   `protobuf:"bytes,8,opt,name=Status,json=status,proto3" json:"Status,omitempty"`
}

func (x *ListByIDsRequest) Reset() {
	*x = ListByIDsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_account_account_proto_msgTypes[48]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListByIDsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListByIDsRequest) ProtoMessage() {}

func (x *ListByIDsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_account_account_proto_msgTypes[48]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListByIDsRequest.ProtoReflect.Descriptor instead.
func (*ListByIDsRequest) Descriptor() ([]byte, []int) {
	return file_api_account_account_proto_rawDescGZIP(), []int{48}
}

func (x *ListByIDsRequest) GetDomain() string {
	if x != nil {
		return x.Domain
	}
	return ""
}

func (x *ListByIDsRequest) GetIDs() []uint64 {
	if x != nil {
		return x.IDs
	}
	return nil
}

func (x *ListByIDsRequest) GetOrderType() uint64 {
	if x != nil {
		return x.OrderType
	}
	return 0
}

func (x *ListByIDsRequest) GetPage() uint64 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *ListByIDsRequest) GetPageSize() uint64 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *ListByIDsRequest) GetNickName() string {
	if x != nil {
		return x.NickName
	}
	return ""
}

func (x *ListByIDsRequest) GetInvitationCode() []string {
	if x != nil {
		return x.InvitationCode
	}
	return nil
}

func (x *ListByIDsRequest) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

type SendMsgRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Domain  string `protobuf:"bytes,1,opt,name=Domain,json=domain,proto3" json:"Domain,omitempty"`
	TelNum  string `protobuf:"bytes,2,opt,name=TelNum,json=telNum,proto3" json:"TelNum,omitempty"`
	Project string `protobuf:"bytes,3,opt,name=Project,json=project,proto3" json:"Project,omitempty"`
	SignNo  uint32 `protobuf:"varint,4,opt,name=signNo,proto3" json:"signNo,omitempty"`
	MId     uint32 `protobuf:"varint,5,opt,name=mId,proto3" json:"mId,omitempty"`
	Scope   string `protobuf:"bytes,6,opt,name=scope,proto3" json:"scope,omitempty"` //标记模块
	Zone    string `protobuf:"bytes,7,opt,name=zone,proto3" json:"zone,omitempty"`   //地区 不同地区切换不同发送帐号
}

func (x *SendMsgRequest) Reset() {
	*x = SendMsgRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_account_account_proto_msgTypes[49]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SendMsgRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SendMsgRequest) ProtoMessage() {}

func (x *SendMsgRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_account_account_proto_msgTypes[49]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SendMsgRequest.ProtoReflect.Descriptor instead.
func (*SendMsgRequest) Descriptor() ([]byte, []int) {
	return file_api_account_account_proto_rawDescGZIP(), []int{49}
}

func (x *SendMsgRequest) GetDomain() string {
	if x != nil {
		return x.Domain
	}
	return ""
}

func (x *SendMsgRequest) GetTelNum() string {
	if x != nil {
		return x.TelNum
	}
	return ""
}

func (x *SendMsgRequest) GetProject() string {
	if x != nil {
		return x.Project
	}
	return ""
}

func (x *SendMsgRequest) GetSignNo() uint32 {
	if x != nil {
		return x.SignNo
	}
	return 0
}

func (x *SendMsgRequest) GetMId() uint32 {
	if x != nil {
		return x.MId
	}
	return 0
}

func (x *SendMsgRequest) GetScope() string {
	if x != nil {
		return x.Scope
	}
	return ""
}

func (x *SendMsgRequest) GetZone() string {
	if x != nil {
		return x.Zone
	}
	return ""
}

type SendCustomMsgRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Domain   string `protobuf:"bytes,1,opt,name=Domain,json=domain,proto3" json:"Domain,omitempty"`
	TelNum   string `protobuf:"bytes,2,opt,name=TelNum,json=telNum,proto3" json:"TelNum,omitempty"`
	Project  string `protobuf:"bytes,3,opt,name=Project,json=project,proto3" json:"Project,omitempty"`
	Url      string `protobuf:"bytes,4,opt,name=Url,proto3" json:"Url,omitempty"`
	ID       uint64 `protobuf:"varint,5,opt,name=ID,proto3" json:"ID,omitempty"`
	MId      uint64 `protobuf:"varint,6,opt,name=MId,json=mId,proto3" json:"MId,omitempty"`
	Location uint64 `protobuf:"varint,7,opt,name=Location,json=location,proto3" json:"Location,omitempty"`
	SigNo    uint32 `protobuf:"varint,8,opt,name=SigNo,json=sigNo,proto3" json:"SigNo,omitempty"`
	Zone     string `protobuf:"bytes,9,opt,name=zone,proto3" json:"zone,omitempty"` //地区 不同地区切换不同发送帐号
}

func (x *SendCustomMsgRequest) Reset() {
	*x = SendCustomMsgRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_account_account_proto_msgTypes[50]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SendCustomMsgRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SendCustomMsgRequest) ProtoMessage() {}

func (x *SendCustomMsgRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_account_account_proto_msgTypes[50]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SendCustomMsgRequest.ProtoReflect.Descriptor instead.
func (*SendCustomMsgRequest) Descriptor() ([]byte, []int) {
	return file_api_account_account_proto_rawDescGZIP(), []int{50}
}

func (x *SendCustomMsgRequest) GetDomain() string {
	if x != nil {
		return x.Domain
	}
	return ""
}

func (x *SendCustomMsgRequest) GetTelNum() string {
	if x != nil {
		return x.TelNum
	}
	return ""
}

func (x *SendCustomMsgRequest) GetProject() string {
	if x != nil {
		return x.Project
	}
	return ""
}

func (x *SendCustomMsgRequest) GetUrl() string {
	if x != nil {
		return x.Url
	}
	return ""
}

func (x *SendCustomMsgRequest) GetID() uint64 {
	if x != nil {
		return x.ID
	}
	return 0
}

func (x *SendCustomMsgRequest) GetMId() uint64 {
	if x != nil {
		return x.MId
	}
	return 0
}

func (x *SendCustomMsgRequest) GetLocation() uint64 {
	if x != nil {
		return x.Location
	}
	return 0
}

func (x *SendCustomMsgRequest) GetSigNo() uint32 {
	if x != nil {
		return x.SigNo
	}
	return 0
}

func (x *SendCustomMsgRequest) GetZone() string {
	if x != nil {
		return x.Zone
	}
	return ""
}

type CheckMsgRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Domain string `protobuf:"bytes,1,opt,name=Domain,json=domain,proto3" json:"Domain,omitempty"`
	TelNum string `protobuf:"bytes,2,opt,name=TelNum,json=telNum,proto3" json:"TelNum,omitempty"`
	Code   string `protobuf:"bytes,3,opt,name=Code,json=code,proto3" json:"Code,omitempty"`
	Scope  string `protobuf:"bytes,4,opt,name=scope,proto3" json:"scope,omitempty"` //标记模块
}

func (x *CheckMsgRequest) Reset() {
	*x = CheckMsgRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_account_account_proto_msgTypes[51]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CheckMsgRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CheckMsgRequest) ProtoMessage() {}

func (x *CheckMsgRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_account_account_proto_msgTypes[51]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CheckMsgRequest.ProtoReflect.Descriptor instead.
func (*CheckMsgRequest) Descriptor() ([]byte, []int) {
	return file_api_account_account_proto_rawDescGZIP(), []int{51}
}

func (x *CheckMsgRequest) GetDomain() string {
	if x != nil {
		return x.Domain
	}
	return ""
}

func (x *CheckMsgRequest) GetTelNum() string {
	if x != nil {
		return x.TelNum
	}
	return ""
}

func (x *CheckMsgRequest) GetCode() string {
	if x != nil {
		return x.Code
	}
	return ""
}

func (x *CheckMsgRequest) GetScope() string {
	if x != nil {
		return x.Scope
	}
	return ""
}

type SendMsgStatusResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *SendMsgStatusResponse) Reset() {
	*x = SendMsgStatusResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_account_account_proto_msgTypes[52]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SendMsgStatusResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SendMsgStatusResponse) ProtoMessage() {}

func (x *SendMsgStatusResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_account_account_proto_msgTypes[52]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SendMsgStatusResponse.ProtoReflect.Descriptor instead.
func (*SendMsgStatusResponse) Descriptor() ([]byte, []int) {
	return file_api_account_account_proto_rawDescGZIP(), []int{52}
}

type RemoveRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Domain string `protobuf:"bytes,1,opt,name=Domain,json=domain,proto3" json:"Domain,omitempty"`
	ID     uint64 `protobuf:"varint,2,opt,name=ID,proto3" json:"ID,omitempty"`
	Code   string `protobuf:"bytes,3,opt,name=code,proto3" json:"code,omitempty"`
}

func (x *RemoveRequest) Reset() {
	*x = RemoveRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_account_account_proto_msgTypes[53]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RemoveRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RemoveRequest) ProtoMessage() {}

func (x *RemoveRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_account_account_proto_msgTypes[53]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RemoveRequest.ProtoReflect.Descriptor instead.
func (*RemoveRequest) Descriptor() ([]byte, []int) {
	return file_api_account_account_proto_rawDescGZIP(), []int{53}
}

func (x *RemoveRequest) GetDomain() string {
	if x != nil {
		return x.Domain
	}
	return ""
}

func (x *RemoveRequest) GetID() uint64 {
	if x != nil {
		return x.ID
	}
	return 0
}

func (x *RemoveRequest) GetCode() string {
	if x != nil {
		return x.Code
	}
	return ""
}

type WriteOffRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id           uint64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Domain       string `protobuf:"bytes,2,opt,name=domain,proto3" json:"domain,omitempty"`
	UserId       uint64 `protobuf:"varint,3,opt,name=userId,proto3" json:"userId,omitempty"`
	Tel          string `protobuf:"bytes,4,opt,name=tel,proto3" json:"tel,omitempty"`
	JonNum       string `protobuf:"bytes,5,opt,name=jonNum,proto3" json:"jonNum,omitempty"`
	UserName     string `protobuf:"bytes,6,opt,name=userName,proto3" json:"userName,omitempty"`
	EnterDate    string `protobuf:"bytes,7,opt,name=enterDate,proto3" json:"enterDate,omitempty"`
	PositionName string `protobuf:"bytes,8,opt,name=positionName,proto3" json:"positionName,omitempty"`
	PositionId   uint64 `protobuf:"varint,9,opt,name=positionId,proto3" json:"positionId,omitempty"`
	SiteName     string `protobuf:"bytes,10,opt,name=siteName,proto3" json:"siteName,omitempty"`
	SiteId       uint64 `protobuf:"varint,11,opt,name=siteId,proto3" json:"siteId,omitempty"`
	AuthUrl      string `protobuf:"bytes,12,opt,name=authUrl,proto3" json:"authUrl,omitempty"`
	Type         string `protobuf:"bytes,13,opt,name=type,proto3" json:"type,omitempty"`
	Status       uint32 `protobuf:"varint,14,opt,name=status,proto3" json:"status,omitempty"`
	SubmitDate   string `protobuf:"bytes,15,opt,name=submitDate,proto3" json:"submitDate,omitempty"`
}

func (x *WriteOffRequest) Reset() {
	*x = WriteOffRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_account_account_proto_msgTypes[54]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *WriteOffRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WriteOffRequest) ProtoMessage() {}

func (x *WriteOffRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_account_account_proto_msgTypes[54]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WriteOffRequest.ProtoReflect.Descriptor instead.
func (*WriteOffRequest) Descriptor() ([]byte, []int) {
	return file_api_account_account_proto_rawDescGZIP(), []int{54}
}

func (x *WriteOffRequest) GetId() uint64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *WriteOffRequest) GetDomain() string {
	if x != nil {
		return x.Domain
	}
	return ""
}

func (x *WriteOffRequest) GetUserId() uint64 {
	if x != nil {
		return x.UserId
	}
	return 0
}

func (x *WriteOffRequest) GetTel() string {
	if x != nil {
		return x.Tel
	}
	return ""
}

func (x *WriteOffRequest) GetJonNum() string {
	if x != nil {
		return x.JonNum
	}
	return ""
}

func (x *WriteOffRequest) GetUserName() string {
	if x != nil {
		return x.UserName
	}
	return ""
}

func (x *WriteOffRequest) GetEnterDate() string {
	if x != nil {
		return x.EnterDate
	}
	return ""
}

func (x *WriteOffRequest) GetPositionName() string {
	if x != nil {
		return x.PositionName
	}
	return ""
}

func (x *WriteOffRequest) GetPositionId() uint64 {
	if x != nil {
		return x.PositionId
	}
	return 0
}

func (x *WriteOffRequest) GetSiteName() string {
	if x != nil {
		return x.SiteName
	}
	return ""
}

func (x *WriteOffRequest) GetSiteId() uint64 {
	if x != nil {
		return x.SiteId
	}
	return 0
}

func (x *WriteOffRequest) GetAuthUrl() string {
	if x != nil {
		return x.AuthUrl
	}
	return ""
}

func (x *WriteOffRequest) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

func (x *WriteOffRequest) GetStatus() uint32 {
	if x != nil {
		return x.Status
	}
	return 0
}

func (x *WriteOffRequest) GetSubmitDate() string {
	if x != nil {
		return x.SubmitDate
	}
	return ""
}

type WriteOffListRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Page     uint64 `protobuf:"varint,1,opt,name=page,proto3" json:"page,omitempty"`
	PageSize uint64 `protobuf:"varint,2,opt,name=pageSize,proto3" json:"pageSize,omitempty"`
	Domain   string `protobuf:"bytes,3,opt,name=domain,proto3" json:"domain,omitempty"`
	UserName string `protobuf:"bytes,4,opt,name=userName,proto3" json:"userName,omitempty"`
	UserId   uint64 `protobuf:"varint,5,opt,name=userId,proto3" json:"userId,omitempty"`
	SiteId   uint64 `protobuf:"varint,6,opt,name=siteId,proto3" json:"siteId,omitempty"`
	Type     string `protobuf:"bytes,7,opt,name=type,proto3" json:"type,omitempty"`
	Status   uint32 `protobuf:"varint,8,opt,name=status,proto3" json:"status,omitempty"`
}

func (x *WriteOffListRequest) Reset() {
	*x = WriteOffListRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_account_account_proto_msgTypes[55]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *WriteOffListRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WriteOffListRequest) ProtoMessage() {}

func (x *WriteOffListRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_account_account_proto_msgTypes[55]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WriteOffListRequest.ProtoReflect.Descriptor instead.
func (*WriteOffListRequest) Descriptor() ([]byte, []int) {
	return file_api_account_account_proto_rawDescGZIP(), []int{55}
}

func (x *WriteOffListRequest) GetPage() uint64 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *WriteOffListRequest) GetPageSize() uint64 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *WriteOffListRequest) GetDomain() string {
	if x != nil {
		return x.Domain
	}
	return ""
}

func (x *WriteOffListRequest) GetUserName() string {
	if x != nil {
		return x.UserName
	}
	return ""
}

func (x *WriteOffListRequest) GetUserId() uint64 {
	if x != nil {
		return x.UserId
	}
	return 0
}

func (x *WriteOffListRequest) GetSiteId() uint64 {
	if x != nil {
		return x.SiteId
	}
	return 0
}

func (x *WriteOffListRequest) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

func (x *WriteOffListRequest) GetStatus() uint32 {
	if x != nil {
		return x.Status
	}
	return 0
}

type WriteOffApproveRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id     uint64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Status uint32 `protobuf:"varint,2,opt,name=status,proto3" json:"status,omitempty"`
}

func (x *WriteOffApproveRequest) Reset() {
	*x = WriteOffApproveRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_account_account_proto_msgTypes[56]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *WriteOffApproveRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WriteOffApproveRequest) ProtoMessage() {}

func (x *WriteOffApproveRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_account_account_proto_msgTypes[56]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WriteOffApproveRequest.ProtoReflect.Descriptor instead.
func (*WriteOffApproveRequest) Descriptor() ([]byte, []int) {
	return file_api_account_account_proto_rawDescGZIP(), []int{56}
}

func (x *WriteOffApproveRequest) GetId() uint64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *WriteOffApproveRequest) GetStatus() uint32 {
	if x != nil {
		return x.Status
	}
	return 0
}

type WriteOffListResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Total        int64              `protobuf:"varint,1,opt,name=total,proto3" json:"total,omitempty"`
	WriteOffList []*WriteOffRequest `protobuf:"bytes,2,rep,name=writeOffList,proto3" json:"writeOffList,omitempty"`
}

func (x *WriteOffListResponse) Reset() {
	*x = WriteOffListResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_account_account_proto_msgTypes[57]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *WriteOffListResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WriteOffListResponse) ProtoMessage() {}

func (x *WriteOffListResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_account_account_proto_msgTypes[57]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WriteOffListResponse.ProtoReflect.Descriptor instead.
func (*WriteOffListResponse) Descriptor() ([]byte, []int) {
	return file_api_account_account_proto_rawDescGZIP(), []int{57}
}

func (x *WriteOffListResponse) GetTotal() int64 {
	if x != nil {
		return x.Total
	}
	return 0
}

func (x *WriteOffListResponse) GetWriteOffList() []*WriteOffRequest {
	if x != nil {
		return x.WriteOffList
	}
	return nil
}

type RemoveResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *RemoveResponse) Reset() {
	*x = RemoveResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_account_account_proto_msgTypes[58]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RemoveResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RemoveResponse) ProtoMessage() {}

func (x *RemoveResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_account_account_proto_msgTypes[58]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RemoveResponse.ProtoReflect.Descriptor instead.
func (*RemoveResponse) Descriptor() ([]byte, []int) {
	return file_api_account_account_proto_rawDescGZIP(), []int{58}
}

type UpdateRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ID             uint64        `protobuf:"varint,1,opt,name=ID,proto3" json:"ID,omitempty"` //ID
	Domain         string        `protobuf:"bytes,2,opt,name=Domain,json=domain,proto3" json:"Domain,omitempty"`
	NickName       string        `protobuf:"bytes,3,opt,name=NickName,json=nickName,proto3" json:"NickName,omitempty"`
	Password       string        `protobuf:"bytes,4,opt,name=Password,json=password,proto3" json:"Password,omitempty"` //密码
	Avatar         string        `protobuf:"bytes,5,opt,name=Avatar,json=avatar,proto3" json:"Avatar,omitempty"`       //头像
	Status         string        `protobuf:"bytes,7,opt,name=Status,json=status,proto3" json:"Status,omitempty"`
	TelNum         string        `protobuf:"bytes,8,opt,name=TelNum,json=telNum,proto3" json:"TelNum,omitempty"`
	EnterDate      string        `protobuf:"bytes,14,opt,name=EnterDate,json=enterDate,proto3" json:"EnterDate,omitempty"`
	Extend         *Extend       `protobuf:"bytes,17,opt,name=Extend,json=extend,proto3" json:"Extend,omitempty"`
	Title          string        `protobuf:"bytes,18,opt,name=Title,json=title,proto3" json:"Title,omitempty"`
	JobNum         string        `protobuf:"bytes,19,opt,name=JobNum,json=jobNum,proto3" json:"JobNum,omitempty"`
	BirthDate      string        `protobuf:"bytes,20,opt,name=BirthDate,json=birthDate,proto3" json:"BirthDate,omitempty"`
	Sex            uint64        `protobuf:"varint,21,opt,name=Sex,json=sex,proto3" json:"Sex,omitempty"`
	IdNum          string        `protobuf:"bytes,22,opt,name=IdNum,json=idNum,proto3" json:"IdNum,omitempty"`
	RealName       string        `protobuf:"bytes,23,opt,name=RealName,json=realName,proto3" json:"RealName,omitempty"`
	InvitationCode string        `protobuf:"bytes,24,opt,name=InvitationCode,json=invitationCode,proto3" json:"InvitationCode,omitempty"`
	LeftDate       string        `protobuf:"bytes,25,opt,name=LeftDate,json=leftDate,proto3" json:"LeftDate,omitempty"`
	Remark         string        `protobuf:"bytes,26,opt,name=Remark,json=remark,proto3" json:"Remark,omitempty"`
	RecentImg      string        `protobuf:"bytes,27,opt,name=RecentImg,json=recentImg,proto3" json:"RecentImg,omitempty"`
	ICNum          string        `protobuf:"bytes,28,opt,name=ICNum,json=icNum,proto3" json:"ICNum,omitempty"`
	Train          string        `protobuf:"bytes,29,opt,name=Train,json=train,proto3" json:"Train,omitempty"`
	Certificate    string        `protobuf:"bytes,30,opt,name=Certificate,json=certificate,proto3" json:"Certificate,omitempty"`
	TrainVideos    []*TrainVideo `protobuf:"bytes,31,rep,name=TrainVideos,json=trainVideos,proto3" json:"TrainVideos,omitempty"`
	Operator       *Operator     `protobuf:"bytes,32,opt,name=operator,proto3" json:"operator,omitempty"`
	SecurityCode   string        `protobuf:"bytes,33,opt,name=SecurityCode,json=securityCode,proto3" json:"SecurityCode,omitempty"`
	UserExtend     *UserExtend   `protobuf:"bytes,34,opt,name=userExtend,proto3" json:"userExtend,omitempty"`
}

func (x *UpdateRequest) Reset() {
	*x = UpdateRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_account_account_proto_msgTypes[59]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateRequest) ProtoMessage() {}

func (x *UpdateRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_account_account_proto_msgTypes[59]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateRequest.ProtoReflect.Descriptor instead.
func (*UpdateRequest) Descriptor() ([]byte, []int) {
	return file_api_account_account_proto_rawDescGZIP(), []int{59}
}

func (x *UpdateRequest) GetID() uint64 {
	if x != nil {
		return x.ID
	}
	return 0
}

func (x *UpdateRequest) GetDomain() string {
	if x != nil {
		return x.Domain
	}
	return ""
}

func (x *UpdateRequest) GetNickName() string {
	if x != nil {
		return x.NickName
	}
	return ""
}

func (x *UpdateRequest) GetPassword() string {
	if x != nil {
		return x.Password
	}
	return ""
}

func (x *UpdateRequest) GetAvatar() string {
	if x != nil {
		return x.Avatar
	}
	return ""
}

func (x *UpdateRequest) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

func (x *UpdateRequest) GetTelNum() string {
	if x != nil {
		return x.TelNum
	}
	return ""
}

func (x *UpdateRequest) GetEnterDate() string {
	if x != nil {
		return x.EnterDate
	}
	return ""
}

func (x *UpdateRequest) GetExtend() *Extend {
	if x != nil {
		return x.Extend
	}
	return nil
}

func (x *UpdateRequest) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

func (x *UpdateRequest) GetJobNum() string {
	if x != nil {
		return x.JobNum
	}
	return ""
}

func (x *UpdateRequest) GetBirthDate() string {
	if x != nil {
		return x.BirthDate
	}
	return ""
}

func (x *UpdateRequest) GetSex() uint64 {
	if x != nil {
		return x.Sex
	}
	return 0
}

func (x *UpdateRequest) GetIdNum() string {
	if x != nil {
		return x.IdNum
	}
	return ""
}

func (x *UpdateRequest) GetRealName() string {
	if x != nil {
		return x.RealName
	}
	return ""
}

func (x *UpdateRequest) GetInvitationCode() string {
	if x != nil {
		return x.InvitationCode
	}
	return ""
}

func (x *UpdateRequest) GetLeftDate() string {
	if x != nil {
		return x.LeftDate
	}
	return ""
}

func (x *UpdateRequest) GetRemark() string {
	if x != nil {
		return x.Remark
	}
	return ""
}

func (x *UpdateRequest) GetRecentImg() string {
	if x != nil {
		return x.RecentImg
	}
	return ""
}

func (x *UpdateRequest) GetICNum() string {
	if x != nil {
		return x.ICNum
	}
	return ""
}

func (x *UpdateRequest) GetTrain() string {
	if x != nil {
		return x.Train
	}
	return ""
}

func (x *UpdateRequest) GetCertificate() string {
	if x != nil {
		return x.Certificate
	}
	return ""
}

func (x *UpdateRequest) GetTrainVideos() []*TrainVideo {
	if x != nil {
		return x.TrainVideos
	}
	return nil
}

func (x *UpdateRequest) GetOperator() *Operator {
	if x != nil {
		return x.Operator
	}
	return nil
}

func (x *UpdateRequest) GetSecurityCode() string {
	if x != nil {
		return x.SecurityCode
	}
	return ""
}

func (x *UpdateRequest) GetUserExtend() *UserExtend {
	if x != nil {
		return x.UserExtend
	}
	return nil
}

type Operator struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ID   uint32 `protobuf:"varint,1,opt,name=ID,proto3" json:"ID,omitempty"`
	Name string `protobuf:"bytes,2,opt,name=Name,proto3" json:"Name,omitempty"`
}

func (x *Operator) Reset() {
	*x = Operator{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_account_account_proto_msgTypes[60]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Operator) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Operator) ProtoMessage() {}

func (x *Operator) ProtoReflect() protoreflect.Message {
	mi := &file_api_account_account_proto_msgTypes[60]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Operator.ProtoReflect.Descriptor instead.
func (*Operator) Descriptor() ([]byte, []int) {
	return file_api_account_account_proto_rawDescGZIP(), []int{60}
}

func (x *Operator) GetID() uint32 {
	if x != nil {
		return x.ID
	}
	return 0
}

func (x *Operator) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

type TrainVideo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TrainUUID    string `protobuf:"bytes,1,opt,name=trainUUID,proto3" json:"trainUUID,omitempty"`
	TrainDesc    string `protobuf:"bytes,2,opt,name=trainDesc,proto3" json:"trainDesc,omitempty"`
	Video        string `protobuf:"bytes,3,opt,name=video,proto3" json:"video,omitempty"`
	SecurityCode string `protobuf:"bytes,31,opt,name=SecurityCode,json=securityCode,proto3" json:"SecurityCode,omitempty"`
}

func (x *TrainVideo) Reset() {
	*x = TrainVideo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_account_account_proto_msgTypes[61]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TrainVideo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TrainVideo) ProtoMessage() {}

func (x *TrainVideo) ProtoReflect() protoreflect.Message {
	mi := &file_api_account_account_proto_msgTypes[61]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TrainVideo.ProtoReflect.Descriptor instead.
func (*TrainVideo) Descriptor() ([]byte, []int) {
	return file_api_account_account_proto_rawDescGZIP(), []int{61}
}

func (x *TrainVideo) GetTrainUUID() string {
	if x != nil {
		return x.TrainUUID
	}
	return ""
}

func (x *TrainVideo) GetTrainDesc() string {
	if x != nil {
		return x.TrainDesc
	}
	return ""
}

func (x *TrainVideo) GetVideo() string {
	if x != nil {
		return x.Video
	}
	return ""
}

func (x *TrainVideo) GetSecurityCode() string {
	if x != nil {
		return x.SecurityCode
	}
	return ""
}

type UpdateResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *UpdateResponse) Reset() {
	*x = UpdateResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_account_account_proto_msgTypes[62]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateResponse) ProtoMessage() {}

func (x *UpdateResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_account_account_proto_msgTypes[62]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateResponse.ProtoReflect.Descriptor instead.
func (*UpdateResponse) Descriptor() ([]byte, []int) {
	return file_api_account_account_proto_rawDescGZIP(), []int{62}
}

type PrivacyInfoRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ID           uint64 `protobuf:"varint,1,opt,name=ID,proto3" json:"ID,omitempty"` //ID
	Domain       string `protobuf:"bytes,2,opt,name=Domain,json=domain,proto3" json:"Domain,omitempty"`
	SecurityCode string `protobuf:"bytes,3,opt,name=SecurityCode,json=securityCode,proto3" json:"SecurityCode,omitempty"`
}

func (x *PrivacyInfoRequest) Reset() {
	*x = PrivacyInfoRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_account_account_proto_msgTypes[63]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PrivacyInfoRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PrivacyInfoRequest) ProtoMessage() {}

func (x *PrivacyInfoRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_account_account_proto_msgTypes[63]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PrivacyInfoRequest.ProtoReflect.Descriptor instead.
func (*PrivacyInfoRequest) Descriptor() ([]byte, []int) {
	return file_api_account_account_proto_rawDescGZIP(), []int{63}
}

func (x *PrivacyInfoRequest) GetID() uint64 {
	if x != nil {
		return x.ID
	}
	return 0
}

func (x *PrivacyInfoRequest) GetDomain() string {
	if x != nil {
		return x.Domain
	}
	return ""
}

func (x *PrivacyInfoRequest) GetSecurityCode() string {
	if x != nil {
		return x.SecurityCode
	}
	return ""
}

type ListRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Domain         string `protobuf:"bytes,1,opt,name=Domain,json=domain,proto3" json:"Domain,omitempty"`
	PageSize       uint64 `protobuf:"varint,2,opt,name=PageSize,json=pageSize,proto3" json:"PageSize,omitempty"`
	Page           uint64 `protobuf:"varint,3,opt,name=Page,json=page,proto3" json:"Page,omitempty"`
	Key            string `protobuf:"bytes,4,opt,name=key,proto3" json:"key,omitempty"`
	Status         string `protobuf:"bytes,5,opt,name=status,proto3" json:"status,omitempty"`
	TelNum         string `protobuf:"bytes,6,opt,name=telNum,proto3" json:"telNum,omitempty"`
	StartEnterDate string `protobuf:"bytes,7,opt,name=startEnterDate,proto3" json:"startEnterDate,omitempty"`
	EndEnterDate   string `protobuf:"bytes,8,opt,name=endEnterDate,proto3" json:"endEnterDate,omitempty"`
	NickName       string `protobuf:"bytes,9,opt,name=nickName,proto3" json:"nickName,omitempty"`
	IsReal         uint32 `protobuf:"varint,10,opt,name=isReal,proto3" json:"isReal,omitempty"`
	IsMainLand     uint32 `protobuf:"varint,11,opt,name=isMainLand,proto3" json:"isMainLand,omitempty"`
	FromCode       string `protobuf:"bytes,12,opt,name=fromCode,proto3" json:"fromCode,omitempty"`
	BankName       string `protobuf:"bytes,13,opt,name=bankName,proto3" json:"bankName,omitempty"`
	BankNo         string `protobuf:"bytes,14,opt,name=bankNo,proto3" json:"bankNo,omitempty"`
	Sex            uint32 `protobuf:"varint,15,opt,name=sex,proto3" json:"sex,omitempty"`
	StartRealTime  string `protobuf:"bytes,16,opt,name=startRealTime,proto3" json:"startRealTime,omitempty"`
	EndRealTime    string `protobuf:"bytes,17,opt,name=endRealTime,proto3" json:"endRealTime,omitempty"`
	EndCreatedAt   string `protobuf:"bytes,18,opt,name=endCreatedAt,proto3" json:"endCreatedAt,omitempty"`
	StartCreatedAt string `protobuf:"bytes,19,opt,name=startCreatedAt,proto3" json:"startCreatedAt,omitempty"`
	RealName       string `protobuf:"bytes,20,opt,name=realName,proto3" json:"realName,omitempty"`
	IDNum          string `protobuf:"bytes,21,opt,name=iDNum,proto3" json:"iDNum,omitempty"`
	PassStatus     uint32 `protobuf:"varint,22,opt,name=passStatus,proto3" json:"passStatus,omitempty"`
}

func (x *ListRequest) Reset() {
	*x = ListRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_account_account_proto_msgTypes[64]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListRequest) ProtoMessage() {}

func (x *ListRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_account_account_proto_msgTypes[64]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListRequest.ProtoReflect.Descriptor instead.
func (*ListRequest) Descriptor() ([]byte, []int) {
	return file_api_account_account_proto_rawDescGZIP(), []int{64}
}

func (x *ListRequest) GetDomain() string {
	if x != nil {
		return x.Domain
	}
	return ""
}

func (x *ListRequest) GetPageSize() uint64 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *ListRequest) GetPage() uint64 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *ListRequest) GetKey() string {
	if x != nil {
		return x.Key
	}
	return ""
}

func (x *ListRequest) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

func (x *ListRequest) GetTelNum() string {
	if x != nil {
		return x.TelNum
	}
	return ""
}

func (x *ListRequest) GetStartEnterDate() string {
	if x != nil {
		return x.StartEnterDate
	}
	return ""
}

func (x *ListRequest) GetEndEnterDate() string {
	if x != nil {
		return x.EndEnterDate
	}
	return ""
}

func (x *ListRequest) GetNickName() string {
	if x != nil {
		return x.NickName
	}
	return ""
}

func (x *ListRequest) GetIsReal() uint32 {
	if x != nil {
		return x.IsReal
	}
	return 0
}

func (x *ListRequest) GetIsMainLand() uint32 {
	if x != nil {
		return x.IsMainLand
	}
	return 0
}

func (x *ListRequest) GetFromCode() string {
	if x != nil {
		return x.FromCode
	}
	return ""
}

func (x *ListRequest) GetBankName() string {
	if x != nil {
		return x.BankName
	}
	return ""
}

func (x *ListRequest) GetBankNo() string {
	if x != nil {
		return x.BankNo
	}
	return ""
}

func (x *ListRequest) GetSex() uint32 {
	if x != nil {
		return x.Sex
	}
	return 0
}

func (x *ListRequest) GetStartRealTime() string {
	if x != nil {
		return x.StartRealTime
	}
	return ""
}

func (x *ListRequest) GetEndRealTime() string {
	if x != nil {
		return x.EndRealTime
	}
	return ""
}

func (x *ListRequest) GetEndCreatedAt() string {
	if x != nil {
		return x.EndCreatedAt
	}
	return ""
}

func (x *ListRequest) GetStartCreatedAt() string {
	if x != nil {
		return x.StartCreatedAt
	}
	return ""
}

func (x *ListRequest) GetRealName() string {
	if x != nil {
		return x.RealName
	}
	return ""
}

func (x *ListRequest) GetIDNum() string {
	if x != nil {
		return x.IDNum
	}
	return ""
}

func (x *ListRequest) GetPassStatus() uint32 {
	if x != nil {
		return x.PassStatus
	}
	return 0
}

type ListResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status   string         `protobuf:"bytes,1,opt,name=Status,json=status,proto3" json:"Status,omitempty"`
	Count    uint64         `protobuf:"varint,2,opt,name=Count,json=count,proto3" json:"Count,omitempty"`
	Data     []*AccountInfo `protobuf:"bytes,3,rep,name=Data,json=data,proto3" json:"Data,omitempty"`
	AllCount uint64         `protobuf:"varint,4,opt,name=AllCount,json=allCount,proto3" json:"AllCount,omitempty"`
}

func (x *ListResponse) Reset() {
	*x = ListResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_account_account_proto_msgTypes[65]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListResponse) ProtoMessage() {}

func (x *ListResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_account_account_proto_msgTypes[65]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListResponse.ProtoReflect.Descriptor instead.
func (*ListResponse) Descriptor() ([]byte, []int) {
	return file_api_account_account_proto_rawDescGZIP(), []int{65}
}

func (x *ListResponse) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

func (x *ListResponse) GetCount() uint64 {
	if x != nil {
		return x.Count
	}
	return 0
}

func (x *ListResponse) GetData() []*AccountInfo {
	if x != nil {
		return x.Data
	}
	return nil
}

func (x *ListResponse) GetAllCount() uint64 {
	if x != nil {
		return x.AllCount
	}
	return 0
}

type InfoRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Domain  string   `protobuf:"bytes,1,opt,name=Domain,json=domain,proto3" json:"Domain,omitempty"`
	ID      uint64   `protobuf:"varint,2,opt,name=ID,json=id,proto3" json:"ID,omitempty"`
	Scene   string   `protobuf:"bytes,3,opt,name=scene,proto3" json:"scene,omitempty"` //场景值 base-默认仅仅user数据
	Uuid    string   `protobuf:"bytes,4,opt,name=uuid,proto3" json:"uuid,omitempty"`
	Extends []string `protobuf:"bytes,5,rep,name=extends,proto3" json:"extends,omitempty"` //加载的扩展数据
}

func (x *InfoRequest) Reset() {
	*x = InfoRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_account_account_proto_msgTypes[66]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *InfoRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InfoRequest) ProtoMessage() {}

func (x *InfoRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_account_account_proto_msgTypes[66]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InfoRequest.ProtoReflect.Descriptor instead.
func (*InfoRequest) Descriptor() ([]byte, []int) {
	return file_api_account_account_proto_rawDescGZIP(), []int{66}
}

func (x *InfoRequest) GetDomain() string {
	if x != nil {
		return x.Domain
	}
	return ""
}

func (x *InfoRequest) GetID() uint64 {
	if x != nil {
		return x.ID
	}
	return 0
}

func (x *InfoRequest) GetScene() string {
	if x != nil {
		return x.Scene
	}
	return ""
}

func (x *InfoRequest) GetUuid() string {
	if x != nil {
		return x.Uuid
	}
	return ""
}

func (x *InfoRequest) GetExtends() []string {
	if x != nil {
		return x.Extends
	}
	return nil
}

type InfoResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status  string       `protobuf:"bytes,1,opt,name=Status,json=status,proto3" json:"Status,omitempty"`
	Info    *AccountInfo `protobuf:"bytes,2,opt,name=Info,json=accountInfo,proto3" json:"Info,omitempty"`
	IsExist bool         `protobuf:"varint,3,opt,name=IsExist,json=isExist,proto3" json:"IsExist,omitempty"`
}

func (x *InfoResponse) Reset() {
	*x = InfoResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_account_account_proto_msgTypes[67]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *InfoResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InfoResponse) ProtoMessage() {}

func (x *InfoResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_account_account_proto_msgTypes[67]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InfoResponse.ProtoReflect.Descriptor instead.
func (*InfoResponse) Descriptor() ([]byte, []int) {
	return file_api_account_account_proto_rawDescGZIP(), []int{67}
}

func (x *InfoResponse) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

func (x *InfoResponse) GetInfo() *AccountInfo {
	if x != nil {
		return x.Info
	}
	return nil
}

func (x *InfoResponse) GetIsExist() bool {
	if x != nil {
		return x.IsExist
	}
	return false
}

type DecryptJwtResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Domain      string `protobuf:"bytes,1,opt,name=Domain,json=status,proto3" json:"Domain,omitempty"`
	ID          uint64 `protobuf:"varint,2,opt,name=ID,json=id,proto3" json:"ID,omitempty"`
	Account     string `protobuf:"bytes,3,opt,name=Account,json=account,proto3" json:"Account,omitempty"`
	NickName    string `protobuf:"bytes,4,opt,name=NickName,json=nickName,proto3" json:"NickName,omitempty"`
	IsOffline   bool   `protobuf:"varint,5,opt,name=IsOffline,json=isOffline,proto3" json:"IsOffline,omitempty"`
	OfflineCode string `protobuf:"bytes,6,opt,name=offlineCode,proto3" json:"offlineCode,omitempty"` //下线的原因
}

func (x *DecryptJwtResponse) Reset() {
	*x = DecryptJwtResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_account_account_proto_msgTypes[68]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DecryptJwtResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DecryptJwtResponse) ProtoMessage() {}

func (x *DecryptJwtResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_account_account_proto_msgTypes[68]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DecryptJwtResponse.ProtoReflect.Descriptor instead.
func (*DecryptJwtResponse) Descriptor() ([]byte, []int) {
	return file_api_account_account_proto_rawDescGZIP(), []int{68}
}

func (x *DecryptJwtResponse) GetDomain() string {
	if x != nil {
		return x.Domain
	}
	return ""
}

func (x *DecryptJwtResponse) GetID() uint64 {
	if x != nil {
		return x.ID
	}
	return 0
}

func (x *DecryptJwtResponse) GetAccount() string {
	if x != nil {
		return x.Account
	}
	return ""
}

func (x *DecryptJwtResponse) GetNickName() string {
	if x != nil {
		return x.NickName
	}
	return ""
}

func (x *DecryptJwtResponse) GetIsOffline() bool {
	if x != nil {
		return x.IsOffline
	}
	return false
}

func (x *DecryptJwtResponse) GetOfflineCode() string {
	if x != nil {
		return x.OfflineCode
	}
	return ""
}

type DecryptJwtRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Token  string `protobuf:"bytes,1,opt,name=token,proto3" json:"token,omitempty"`
	Domain string `protobuf:"bytes,2,opt,name=Domain,proto3" json:"Domain,omitempty"`
}

func (x *DecryptJwtRequest) Reset() {
	*x = DecryptJwtRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_account_account_proto_msgTypes[69]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DecryptJwtRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DecryptJwtRequest) ProtoMessage() {}

func (x *DecryptJwtRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_account_account_proto_msgTypes[69]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DecryptJwtRequest.ProtoReflect.Descriptor instead.
func (*DecryptJwtRequest) Descriptor() ([]byte, []int) {
	return file_api_account_account_proto_rawDescGZIP(), []int{69}
}

func (x *DecryptJwtRequest) GetToken() string {
	if x != nil {
		return x.Token
	}
	return ""
}

func (x *DecryptJwtRequest) GetDomain() string {
	if x != nil {
		return x.Domain
	}
	return ""
}

type CheckPwdRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Token    string `protobuf:"bytes,1,opt,name=Token,json=token,proto3" json:"Token,omitempty"`
	Password string `protobuf:"bytes,2,opt,name=Password,json=password,proto3" json:"Password,omitempty"`
}

func (x *CheckPwdRequest) Reset() {
	*x = CheckPwdRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_account_account_proto_msgTypes[70]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CheckPwdRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CheckPwdRequest) ProtoMessage() {}

func (x *CheckPwdRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_account_account_proto_msgTypes[70]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CheckPwdRequest.ProtoReflect.Descriptor instead.
func (*CheckPwdRequest) Descriptor() ([]byte, []int) {
	return file_api_account_account_proto_rawDescGZIP(), []int{70}
}

func (x *CheckPwdRequest) GetToken() string {
	if x != nil {
		return x.Token
	}
	return ""
}

func (x *CheckPwdRequest) GetPassword() string {
	if x != nil {
		return x.Password
	}
	return ""
}

type AuthenticationRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name  string `protobuf:"bytes,1,opt,name=Name,json=name,proto3" json:"Name,omitempty"`
	IDNum string `protobuf:"bytes,2,opt,name=IDNum,json=idNum,proto3" json:"IDNum,omitempty"`
	Token string `protobuf:"bytes,3,opt,name=Token,json=token,proto3" json:"Token,omitempty"`
}

func (x *AuthenticationRequest) Reset() {
	*x = AuthenticationRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_account_account_proto_msgTypes[71]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AuthenticationRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AuthenticationRequest) ProtoMessage() {}

func (x *AuthenticationRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_account_account_proto_msgTypes[71]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AuthenticationRequest.ProtoReflect.Descriptor instead.
func (*AuthenticationRequest) Descriptor() ([]byte, []int) {
	return file_api_account_account_proto_rawDescGZIP(), []int{71}
}

func (x *AuthenticationRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *AuthenticationRequest) GetIDNum() string {
	if x != nil {
		return x.IDNum
	}
	return ""
}

func (x *AuthenticationRequest) GetToken() string {
	if x != nil {
		return x.Token
	}
	return ""
}

type RequestStatus struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status  string `protobuf:"bytes,1,opt,name=Status,json=status,proto3" json:"Status,omitempty"`
	ID      uint64 `protobuf:"varint,2,opt,name=ID,proto3" json:"ID,omitempty"`
	IsExist bool   `protobuf:"varint,3,opt,name=isExist,proto3" json:"isExist,omitempty"`
}

func (x *RequestStatus) Reset() {
	*x = RequestStatus{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_account_account_proto_msgTypes[72]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RequestStatus) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RequestStatus) ProtoMessage() {}

func (x *RequestStatus) ProtoReflect() protoreflect.Message {
	mi := &file_api_account_account_proto_msgTypes[72]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RequestStatus.ProtoReflect.Descriptor instead.
func (*RequestStatus) Descriptor() ([]byte, []int) {
	return file_api_account_account_proto_rawDescGZIP(), []int{72}
}

func (x *RequestStatus) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

func (x *RequestStatus) GetID() uint64 {
	if x != nil {
		return x.ID
	}
	return 0
}

func (x *RequestStatus) GetIsExist() bool {
	if x != nil {
		return x.IsExist
	}
	return false
}

type RegistRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Domain         string      `protobuf:"bytes,1,opt,name=Domain,json=domain,proto3" json:"Domain,omitempty"`
	NickName       string      `protobuf:"bytes,2,opt,name=NickName,json=nickName,proto3" json:"NickName,omitempty"`
	TelNum         string      `protobuf:"bytes,3,opt,name=TelNum,json=telNum,proto3" json:"TelNum,omitempty"`
	Password       string      `protobuf:"bytes,4,opt,name=Password,json=password,proto3" json:"Password,omitempty"` //密码
	Avatar         string      `protobuf:"bytes,5,opt,name=Avatar,json=avatar,proto3" json:"Avatar,omitempty"`       //头像
	EnterDate      string      `protobuf:"bytes,14,opt,name=EnterDate,json=enterDate,proto3" json:"EnterDate,omitempty"`
	Extend         *Extend     `protobuf:"bytes,15,opt,name=Extend,json=extend,proto3" json:"Extend,omitempty"`
	JobNum         string      `protobuf:"bytes,16,opt,name=JobNum,proto3" json:"JobNum,omitempty"`                   //工号
	Code           string      `protobuf:"bytes,17,opt,name=Code,json=code,proto3" json:"Code,omitempty"`             //工号
	IdNum          string      `protobuf:"bytes,18,opt,name=IdNum,json=idNum,proto3" json:"IdNum,omitempty"`          //年龄
	RealName       string      `protobuf:"bytes,19,opt,name=RealName,json=realName,proto3" json:"RealName,omitempty"` //
	RecentImg      string      `protobuf:"bytes,20,opt,name=RecentImg,proto3" json:"RecentImg,omitempty"`
	RealIDImgA     string      `protobuf:"bytes,21,opt,name=RealIDImgA,proto3" json:"RealIDImgA,omitempty"`
	RealIDImgB     string      `protobuf:"bytes,22,opt,name=RealIDImgB,proto3" json:"RealIDImgB,omitempty"`
	Video          string      `protobuf:"bytes,23,opt,name=Video,proto3" json:"Video,omitempty"`
	ICNum          string      `protobuf:"bytes,24,opt,name=ICNum,proto3" json:"ICNum,omitempty"`
	Train          string      `protobuf:"bytes,25,opt,name=Train,proto3" json:"Train,omitempty"`
	Certificate    string      `protobuf:"bytes,26,opt,name=Certificate,proto3" json:"Certificate,omitempty"`
	Source         string      `protobuf:"bytes,27,opt,name=Source,proto3" json:"Source,omitempty"`
	Operator       *Operator   `protobuf:"bytes,28,opt,name=operator,proto3" json:"operator,omitempty"`
	Status         string      `protobuf:"bytes,29,opt,name=Status,proto3" json:"Status,omitempty"`
	BlockAddr      string      `protobuf:"bytes,30,opt,name=BlockAddr,proto3" json:"BlockAddr,omitempty"`
	Passport       *Passport   `protobuf:"bytes,31,opt,name=passport,proto3" json:"passport,omitempty"`     //护照 港澳台 护照 通信证等
	LeftDate       string      `protobuf:"bytes,32,opt,name=leftDate,proto3" json:"leftDate,omitempty"`     //离职时间
	UserExtend     *UserExtend `protobuf:"bytes,33,opt,name=UserExtend,proto3" json:"UserExtend,omitempty"` //离职时间
	NotCheckTelNum bool        `protobuf:"varint,34,opt,name=notCheckTelNum,proto3" json:"notCheckTelNum,omitempty"`
	Zone           string      `protobuf:"bytes,35,opt,name=zone,proto3" json:"zone,omitempty"` //手机号区号的信息
}

func (x *RegistRequest) Reset() {
	*x = RegistRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_account_account_proto_msgTypes[73]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RegistRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RegistRequest) ProtoMessage() {}

func (x *RegistRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_account_account_proto_msgTypes[73]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RegistRequest.ProtoReflect.Descriptor instead.
func (*RegistRequest) Descriptor() ([]byte, []int) {
	return file_api_account_account_proto_rawDescGZIP(), []int{73}
}

func (x *RegistRequest) GetDomain() string {
	if x != nil {
		return x.Domain
	}
	return ""
}

func (x *RegistRequest) GetNickName() string {
	if x != nil {
		return x.NickName
	}
	return ""
}

func (x *RegistRequest) GetTelNum() string {
	if x != nil {
		return x.TelNum
	}
	return ""
}

func (x *RegistRequest) GetPassword() string {
	if x != nil {
		return x.Password
	}
	return ""
}

func (x *RegistRequest) GetAvatar() string {
	if x != nil {
		return x.Avatar
	}
	return ""
}

func (x *RegistRequest) GetEnterDate() string {
	if x != nil {
		return x.EnterDate
	}
	return ""
}

func (x *RegistRequest) GetExtend() *Extend {
	if x != nil {
		return x.Extend
	}
	return nil
}

func (x *RegistRequest) GetJobNum() string {
	if x != nil {
		return x.JobNum
	}
	return ""
}

func (x *RegistRequest) GetCode() string {
	if x != nil {
		return x.Code
	}
	return ""
}

func (x *RegistRequest) GetIdNum() string {
	if x != nil {
		return x.IdNum
	}
	return ""
}

func (x *RegistRequest) GetRealName() string {
	if x != nil {
		return x.RealName
	}
	return ""
}

func (x *RegistRequest) GetRecentImg() string {
	if x != nil {
		return x.RecentImg
	}
	return ""
}

func (x *RegistRequest) GetRealIDImgA() string {
	if x != nil {
		return x.RealIDImgA
	}
	return ""
}

func (x *RegistRequest) GetRealIDImgB() string {
	if x != nil {
		return x.RealIDImgB
	}
	return ""
}

func (x *RegistRequest) GetVideo() string {
	if x != nil {
		return x.Video
	}
	return ""
}

func (x *RegistRequest) GetICNum() string {
	if x != nil {
		return x.ICNum
	}
	return ""
}

func (x *RegistRequest) GetTrain() string {
	if x != nil {
		return x.Train
	}
	return ""
}

func (x *RegistRequest) GetCertificate() string {
	if x != nil {
		return x.Certificate
	}
	return ""
}

func (x *RegistRequest) GetSource() string {
	if x != nil {
		return x.Source
	}
	return ""
}

func (x *RegistRequest) GetOperator() *Operator {
	if x != nil {
		return x.Operator
	}
	return nil
}

func (x *RegistRequest) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

func (x *RegistRequest) GetBlockAddr() string {
	if x != nil {
		return x.BlockAddr
	}
	return ""
}

func (x *RegistRequest) GetPassport() *Passport {
	if x != nil {
		return x.Passport
	}
	return nil
}

func (x *RegistRequest) GetLeftDate() string {
	if x != nil {
		return x.LeftDate
	}
	return ""
}

func (x *RegistRequest) GetUserExtend() *UserExtend {
	if x != nil {
		return x.UserExtend
	}
	return nil
}

func (x *RegistRequest) GetNotCheckTelNum() bool {
	if x != nil {
		return x.NotCheckTelNum
	}
	return false
}

func (x *RegistRequest) GetZone() string {
	if x != nil {
		return x.Zone
	}
	return ""
}

type UserExtend struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id               string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	Uuid             string `protobuf:"bytes,2,opt,name=uuid,proto3" json:"uuid,omitempty"`
	UserID           string `protobuf:"bytes,3,opt,name=userID,proto3" json:"userID,omitempty"`
	Address          string `protobuf:"bytes,4,opt,name=address,proto3" json:"address,omitempty"` //家庭住址
	BankName         string `protobuf:"bytes,5,opt,name=bankName,proto3" json:"bankName,omitempty"`
	BankNo           string `protobuf:"bytes,6,opt,name=bankNo,proto3" json:"bankNo,omitempty"`
	Zone             string `protobuf:"bytes,7,opt,name=zone,proto3" json:"zone,omitempty"`
	IsMainland       uint32 `protobuf:"varint,8,opt,name=isMainland,proto3" json:"isMainland,omitempty"`
	IsReal           uint32 `protobuf:"varint,9,opt,name=isReal,proto3" json:"isReal,omitempty"`                      //是否实名
	FromCode         string `protobuf:"bytes,10,opt,name=fromCode,proto3" json:"fromCode,omitempty"`                  // 99999
	RealTime         string `protobuf:"bytes,11,opt,name=realTime,proto3" json:"realTime,omitempty"`                  // 实名信息时间
	RealName         string `protobuf:"bytes,12,opt,name=realName,proto3" json:"realName,omitempty"`                  // 姓名
	IdType           string `protobuf:"bytes,13,opt,name=idType,proto3" json:"idType,omitempty"`                      //证件类型
	IdNo             string `protobuf:"bytes,14,opt,name=idNo,proto3" json:"idNo,omitempty"`                          //证件号码
	CardA            string `protobuf:"bytes,15,opt,name=cardA,proto3" json:"cardA,omitempty"`                        //证件照片A
	CardB            string `protobuf:"bytes,16,opt,name=cardB,proto3" json:"cardB,omitempty"`                        //证件照片B
	PassStatus       uint32 `protobuf:"varint,17,opt,name=passStatus,proto3" json:"passStatus,omitempty"`             //审核状态
	RecommendStaffId uint32 `protobuf:"varint,18,opt,name=recommendStaffId,proto3" json:"recommendStaffId,omitempty"` //我的推荐人ID
	IsRecommended    bool   `protobuf:"varint,19,opt,name=isRecommended,proto3" json:"isRecommended,omitempty"`       //我的推荐人ID
	PassStatusMsg    string `protobuf:"bytes,20,opt,name=passStatusMsg,proto3" json:"passStatusMsg,omitempty"`        //拒绝的原因
}

func (x *UserExtend) Reset() {
	*x = UserExtend{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_account_account_proto_msgTypes[74]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UserExtend) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserExtend) ProtoMessage() {}

func (x *UserExtend) ProtoReflect() protoreflect.Message {
	mi := &file_api_account_account_proto_msgTypes[74]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserExtend.ProtoReflect.Descriptor instead.
func (*UserExtend) Descriptor() ([]byte, []int) {
	return file_api_account_account_proto_rawDescGZIP(), []int{74}
}

func (x *UserExtend) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *UserExtend) GetUuid() string {
	if x != nil {
		return x.Uuid
	}
	return ""
}

func (x *UserExtend) GetUserID() string {
	if x != nil {
		return x.UserID
	}
	return ""
}

func (x *UserExtend) GetAddress() string {
	if x != nil {
		return x.Address
	}
	return ""
}

func (x *UserExtend) GetBankName() string {
	if x != nil {
		return x.BankName
	}
	return ""
}

func (x *UserExtend) GetBankNo() string {
	if x != nil {
		return x.BankNo
	}
	return ""
}

func (x *UserExtend) GetZone() string {
	if x != nil {
		return x.Zone
	}
	return ""
}

func (x *UserExtend) GetIsMainland() uint32 {
	if x != nil {
		return x.IsMainland
	}
	return 0
}

func (x *UserExtend) GetIsReal() uint32 {
	if x != nil {
		return x.IsReal
	}
	return 0
}

func (x *UserExtend) GetFromCode() string {
	if x != nil {
		return x.FromCode
	}
	return ""
}

func (x *UserExtend) GetRealTime() string {
	if x != nil {
		return x.RealTime
	}
	return ""
}

func (x *UserExtend) GetRealName() string {
	if x != nil {
		return x.RealName
	}
	return ""
}

func (x *UserExtend) GetIdType() string {
	if x != nil {
		return x.IdType
	}
	return ""
}

func (x *UserExtend) GetIdNo() string {
	if x != nil {
		return x.IdNo
	}
	return ""
}

func (x *UserExtend) GetCardA() string {
	if x != nil {
		return x.CardA
	}
	return ""
}

func (x *UserExtend) GetCardB() string {
	if x != nil {
		return x.CardB
	}
	return ""
}

func (x *UserExtend) GetPassStatus() uint32 {
	if x != nil {
		return x.PassStatus
	}
	return 0
}

func (x *UserExtend) GetRecommendStaffId() uint32 {
	if x != nil {
		return x.RecommendStaffId
	}
	return 0
}

func (x *UserExtend) GetIsRecommended() bool {
	if x != nil {
		return x.IsRecommended
	}
	return false
}

func (x *UserExtend) GetPassStatusMsg() string {
	if x != nil {
		return x.PassStatusMsg
	}
	return ""
}

type Passport struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	IdNum      string `protobuf:"bytes,1,opt,name=idNum,proto3" json:"idNum,omitempty"`           //编号
	RealIDImgA string `protobuf:"bytes,2,opt,name=realIDImgA,proto3" json:"realIDImgA,omitempty"` //正反面
	RealIDImgB string `protobuf:"bytes,3,opt,name=realIDImgB,proto3" json:"realIDImgB,omitempty"` //正反面
	Name       string `protobuf:"bytes,4,opt,name=name,proto3" json:"name,omitempty"`             //名字
	IdType     string `protobuf:"bytes,5,opt,name=idType,proto3" json:"idType,omitempty"`         //法大大 保持一致 "0"身份证号 "1" 护照号 "B" 港澳居民来往内地通行证号 "C" 台湾居民来往大陆通行证号
}

func (x *Passport) Reset() {
	*x = Passport{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_account_account_proto_msgTypes[75]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Passport) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Passport) ProtoMessage() {}

func (x *Passport) ProtoReflect() protoreflect.Message {
	mi := &file_api_account_account_proto_msgTypes[75]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Passport.ProtoReflect.Descriptor instead.
func (*Passport) Descriptor() ([]byte, []int) {
	return file_api_account_account_proto_rawDescGZIP(), []int{75}
}

func (x *Passport) GetIdNum() string {
	if x != nil {
		return x.IdNum
	}
	return ""
}

func (x *Passport) GetRealIDImgA() string {
	if x != nil {
		return x.RealIDImgA
	}
	return ""
}

func (x *Passport) GetRealIDImgB() string {
	if x != nil {
		return x.RealIDImgB
	}
	return ""
}

func (x *Passport) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *Passport) GetIdType() string {
	if x != nil {
		return x.IdType
	}
	return ""
}

type LoginRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Domain string `protobuf:"bytes,1,opt,name=Domain,json=domain,proto3" json:"Domain,omitempty"`
	//string TelNum     = 2 [json_name = "telNum",(validator.field) = {regex: "^1\\d{10}$",human_error: "70002"}];
	TelNum      string `protobuf:"bytes,2,opt,name=TelNum,json=telNum,proto3" json:"TelNum,omitempty"`
	Code        string `protobuf:"bytes,3,opt,name=Code,json=code,proto3" json:"Code,omitempty"`
	Password    string `protobuf:"bytes,4,opt,name=Password,json=password,proto3" json:"Password,omitempty"`
	Ip          string `protobuf:"bytes,5,opt,name=Ip,json=ip,proto3" json:"Ip,omitempty"`
	PassCheckIp bool   `protobuf:"varint,6,opt,name=passCheckIp,proto3" json:"passCheckIp,omitempty"`
	From        string `protobuf:"bytes,7,opt,name=from,proto3" json:"from,omitempty"` //登陆来源 PC 还是 h5 之类的
	Zone        string `protobuf:"bytes,8,opt,name=zone,proto3" json:"zone,omitempty"`
}

func (x *LoginRequest) Reset() {
	*x = LoginRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_account_account_proto_msgTypes[76]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LoginRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LoginRequest) ProtoMessage() {}

func (x *LoginRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_account_account_proto_msgTypes[76]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LoginRequest.ProtoReflect.Descriptor instead.
func (*LoginRequest) Descriptor() ([]byte, []int) {
	return file_api_account_account_proto_rawDescGZIP(), []int{76}
}

func (x *LoginRequest) GetDomain() string {
	if x != nil {
		return x.Domain
	}
	return ""
}

func (x *LoginRequest) GetTelNum() string {
	if x != nil {
		return x.TelNum
	}
	return ""
}

func (x *LoginRequest) GetCode() string {
	if x != nil {
		return x.Code
	}
	return ""
}

func (x *LoginRequest) GetPassword() string {
	if x != nil {
		return x.Password
	}
	return ""
}

func (x *LoginRequest) GetIp() string {
	if x != nil {
		return x.Ip
	}
	return ""
}

func (x *LoginRequest) GetPassCheckIp() bool {
	if x != nil {
		return x.PassCheckIp
	}
	return false
}

func (x *LoginRequest) GetFrom() string {
	if x != nil {
		return x.From
	}
	return ""
}

func (x *LoginRequest) GetZone() string {
	if x != nil {
		return x.Zone
	}
	return ""
}

type TokenInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AccountInfo     *AccountInfo `protobuf:"bytes,1,opt,name=AccountInfo,json=accountInfo,proto3" json:"AccountInfo,omitempty"`
	Token           string       `protobuf:"bytes,2,opt,name=Token,json=token,proto3" json:"Token,omitempty"`
	RefreshToken    string       `protobuf:"bytes,4,opt,name=RefreshToken,json=refresh,proto3" json:"RefreshToken,omitempty"`
	IsSampleAddress bool         `protobuf:"varint,3,opt,name=IsSampleAddress,json=isSampleAddress,proto3" json:"IsSampleAddress,omitempty"`
	NowAddress      string       `protobuf:"bytes,5,opt,name=nowAddress,proto3" json:"nowAddress,omitempty"`
}

func (x *TokenInfo) Reset() {
	*x = TokenInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_account_account_proto_msgTypes[77]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TokenInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TokenInfo) ProtoMessage() {}

func (x *TokenInfo) ProtoReflect() protoreflect.Message {
	mi := &file_api_account_account_proto_msgTypes[77]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TokenInfo.ProtoReflect.Descriptor instead.
func (*TokenInfo) Descriptor() ([]byte, []int) {
	return file_api_account_account_proto_rawDescGZIP(), []int{77}
}

func (x *TokenInfo) GetAccountInfo() *AccountInfo {
	if x != nil {
		return x.AccountInfo
	}
	return nil
}

func (x *TokenInfo) GetToken() string {
	if x != nil {
		return x.Token
	}
	return ""
}

func (x *TokenInfo) GetRefreshToken() string {
	if x != nil {
		return x.RefreshToken
	}
	return ""
}

func (x *TokenInfo) GetIsSampleAddress() bool {
	if x != nil {
		return x.IsSampleAddress
	}
	return false
}

func (x *TokenInfo) GetNowAddress() string {
	if x != nil {
		return x.NowAddress
	}
	return ""
}

type Extend struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	JumpTo          string `protobuf:"bytes,1,opt,name=JumpTo,json=jumpTo,proto3" json:"JumpTo,omitempty"`
	Lang            string `protobuf:"bytes,2,opt,name=Lang,json=lang,proto3" json:"Lang,omitempty"`
	CanScan         bool   `protobuf:"varint,3,opt,name=CanScan,json=canScan,proto3" json:"CanScan,omitempty"`
	ResolutionRatio bool   `protobuf:"varint,4,opt,name=ResolutionRatio,json=resolutionRatio,proto3" json:"ResolutionRatio,omitempty"`
}

func (x *Extend) Reset() {
	*x = Extend{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_account_account_proto_msgTypes[78]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Extend) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Extend) ProtoMessage() {}

func (x *Extend) ProtoReflect() protoreflect.Message {
	mi := &file_api_account_account_proto_msgTypes[78]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Extend.ProtoReflect.Descriptor instead.
func (*Extend) Descriptor() ([]byte, []int) {
	return file_api_account_account_proto_rawDescGZIP(), []int{78}
}

func (x *Extend) GetJumpTo() string {
	if x != nil {
		return x.JumpTo
	}
	return ""
}

func (x *Extend) GetLang() string {
	if x != nil {
		return x.Lang
	}
	return ""
}

func (x *Extend) GetCanScan() bool {
	if x != nil {
		return x.CanScan
	}
	return false
}

func (x *Extend) GetResolutionRatio() bool {
	if x != nil {
		return x.ResolutionRatio
	}
	return false
}

type Department struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ID   uint64 `protobuf:"varint,1,opt,name=ID,proto3" json:"ID,omitempty"`
	Name string `protobuf:"bytes,2,opt,name=Name,json=name,proto3" json:"Name,omitempty"`
}

func (x *Department) Reset() {
	*x = Department{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_account_account_proto_msgTypes[79]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Department) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Department) ProtoMessage() {}

func (x *Department) ProtoReflect() protoreflect.Message {
	mi := &file_api_account_account_proto_msgTypes[79]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Department.ProtoReflect.Descriptor instead.
func (*Department) Descriptor() ([]byte, []int) {
	return file_api_account_account_proto_rawDescGZIP(), []int{79}
}

func (x *Department) GetID() uint64 {
	if x != nil {
		return x.ID
	}
	return 0
}

func (x *Department) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

// The response message containing the greetings
type AccountInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ID             uint64          `protobuf:"varint,1,opt,name=ID,json=id,proto3" json:"ID,omitempty"`
	Account        string          `protobuf:"bytes,2,opt,name=Account,json=account,proto3" json:"Account,omitempty"`
	NickName       string          `protobuf:"bytes,3,opt,name=NickName,json=nickName,proto3" json:"NickName,omitempty"`
	Type           int64           `protobuf:"varint,4,opt,name=Type,json=type,proto3" json:"Type,omitempty"`
	TelNum         string          `protobuf:"bytes,5,opt,name=TelNum,json=telNum,proto3" json:"TelNum,omitempty"`
	Status         string          `protobuf:"bytes,6,opt,name=Status,json=status,proto3" json:"Status,omitempty"`
	Avatar         string          `protobuf:"bytes,7,opt,name=Avatar,json=avatar,proto3" json:"Avatar,omitempty"`
	CreateAt       string          `protobuf:"bytes,8,opt,name=CreateAt,json=createAt,proto3" json:"CreateAt,omitempty"`
	RealNameID     uint64          `protobuf:"varint,9,opt,name=RealNameID,json=realNameID,proto3" json:"RealNameID,omitempty"`
	RealName       string          `protobuf:"bytes,10,opt,name=RealName,json=realName,proto3" json:"RealName,omitempty"`
	IDNum          string          `protobuf:"bytes,11,opt,name=IDNum,json=iDNum,proto3" json:"IDNum,omitempty"`
	MnemonicWords  string          `protobuf:"bytes,12,opt,name=MnemonicWords,json=mnemonicWords,proto3" json:"MnemonicWords,omitempty"`
	IsNeedChange   uint64          `protobuf:"varint,13,opt,name=IsNeedChange,json=isNeedChange,proto3" json:"IsNeedChange,omitempty"`
	EnterDate      string          `protobuf:"bytes,14,opt,name=EnterDate,json=enterDate,proto3" json:"EnterDate,omitempty"`
	WorkYear       float32         `protobuf:"fixed32,15,opt,name=WorkYear,json=workYear,proto3" json:"WorkYear,omitempty"`
	Domain         string          `protobuf:"bytes,16,opt,name=Domain,json=domain,proto3" json:"Domain,omitempty"`
	Extend         *Extend         `protobuf:"bytes,17,opt,name=Extend,json=extend,proto3" json:"Extend,omitempty"`
	JobNum         string          `protobuf:"bytes,18,opt,name=JobNum,json=jobNum,proto3" json:"JobNum,omitempty"`
	BirthDate      string          `protobuf:"bytes,19,opt,name=BirthDate,json=birth_date,proto3" json:"BirthDate,omitempty"`
	Age            uint64          `protobuf:"varint,20,opt,name=Age,json=age,proto3" json:"Age,omitempty"`
	Sex            uint64          `protobuf:"varint,21,opt,name=Sex,json=sex,proto3" json:"Sex,omitempty"`
	Title          string          `protobuf:"bytes,22,opt,name=Title,json=title,proto3" json:"Title,omitempty"`
	Departments    []*Department   `protobuf:"bytes,23,rep,name=Departments,json=departments,proto3" json:"Departments,omitempty"`
	Ip             string          `protobuf:"bytes,24,opt,name=Ip,json=ip,proto3" json:"Ip,omitempty"`
	LoginDate      string          `protobuf:"bytes,25,opt,name=LoginDate,json=loginDate,proto3" json:"LoginDate,omitempty"`
	InvitationCode string          `protobuf:"bytes,26,opt,name=InvitationCode,json=invitationCode,proto3" json:"InvitationCode,omitempty"`
	NowLogId       uint64          `protobuf:"varint,27,opt,name=NowLogId,json=nowLogId,proto3" json:"NowLogId,omitempty"`
	CanScan        bool            `protobuf:"varint,28,opt,name=CanScan,json=canScan,proto3" json:"CanScan,omitempty"`
	LeftDate       string          `protobuf:"bytes,29,opt,name=LeftDate,json=leftDate,proto3" json:"LeftDate,omitempty"`
	Positions      []*PositionUser `protobuf:"bytes,30,rep,name=Positions,json=positions,proto3" json:"Positions,omitempty"`
	Remark         string          `protobuf:"bytes,31,opt,name=Remark,json=remark,proto3" json:"Remark,omitempty"`
	RecentImg      string          `protobuf:"bytes,32,opt,name=recentImg,proto3" json:"recentImg,omitempty"`
	Clocks         []*ClockUser    `protobuf:"bytes,33,rep,name=clocks,proto3" json:"clocks,omitempty"`
	MailAccount    string          `protobuf:"bytes,34,opt,name=mailAccount,proto3" json:"mailAccount,omitempty"`
	ICNum          string          `protobuf:"bytes,35,opt,name=ICNum,json=icNum,proto3" json:"ICNum,omitempty"`
	EnglishName    string          `protobuf:"bytes,36,opt,name=englishName,proto3" json:"englishName,omitempty"`
	Train          string          `protobuf:"bytes,37,opt,name=Train,json=train,proto3" json:"Train,omitempty"`
	Certificate    string          `protobuf:"bytes,38,opt,name=Certificate,json=certificate,proto3" json:"Certificate,omitempty"`
	TrainVideos    []*TrainVideo   `protobuf:"bytes,39,rep,name=TrainVideos,json=trainVideos,proto3" json:"TrainVideos,omitempty"`
	Operator       *Operator       `protobuf:"bytes,40,opt,name=operator,proto3" json:"operator,omitempty"`
	UpdatedAt      string          `protobuf:"bytes,41,opt,name=updatedAt,proto3" json:"updatedAt,omitempty"`
	SecurityCode   string          `protobuf:"bytes,42,opt,name=SecurityCode,json=securityCode,proto3" json:"SecurityCode,omitempty"`
	BlockAddr      string          `protobuf:"bytes,43,opt,name=BlockAddr,json=blockAddr,proto3" json:"BlockAddr,omitempty"`
	Language       string          `protobuf:"bytes,44,opt,name=Language,json=language,proto3" json:"Language,omitempty"`
	UserExtend     *UserExtend     `protobuf:"bytes,45,opt,name=userExtend,proto3" json:"userExtend,omitempty"`
}

func (x *AccountInfo) Reset() {
	*x = AccountInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_account_account_proto_msgTypes[80]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AccountInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AccountInfo) ProtoMessage() {}

func (x *AccountInfo) ProtoReflect() protoreflect.Message {
	mi := &file_api_account_account_proto_msgTypes[80]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AccountInfo.ProtoReflect.Descriptor instead.
func (*AccountInfo) Descriptor() ([]byte, []int) {
	return file_api_account_account_proto_rawDescGZIP(), []int{80}
}

func (x *AccountInfo) GetID() uint64 {
	if x != nil {
		return x.ID
	}
	return 0
}

func (x *AccountInfo) GetAccount() string {
	if x != nil {
		return x.Account
	}
	return ""
}

func (x *AccountInfo) GetNickName() string {
	if x != nil {
		return x.NickName
	}
	return ""
}

func (x *AccountInfo) GetType() int64 {
	if x != nil {
		return x.Type
	}
	return 0
}

func (x *AccountInfo) GetTelNum() string {
	if x != nil {
		return x.TelNum
	}
	return ""
}

func (x *AccountInfo) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

func (x *AccountInfo) GetAvatar() string {
	if x != nil {
		return x.Avatar
	}
	return ""
}

func (x *AccountInfo) GetCreateAt() string {
	if x != nil {
		return x.CreateAt
	}
	return ""
}

func (x *AccountInfo) GetRealNameID() uint64 {
	if x != nil {
		return x.RealNameID
	}
	return 0
}

func (x *AccountInfo) GetRealName() string {
	if x != nil {
		return x.RealName
	}
	return ""
}

func (x *AccountInfo) GetIDNum() string {
	if x != nil {
		return x.IDNum
	}
	return ""
}

func (x *AccountInfo) GetMnemonicWords() string {
	if x != nil {
		return x.MnemonicWords
	}
	return ""
}

func (x *AccountInfo) GetIsNeedChange() uint64 {
	if x != nil {
		return x.IsNeedChange
	}
	return 0
}

func (x *AccountInfo) GetEnterDate() string {
	if x != nil {
		return x.EnterDate
	}
	return ""
}

func (x *AccountInfo) GetWorkYear() float32 {
	if x != nil {
		return x.WorkYear
	}
	return 0
}

func (x *AccountInfo) GetDomain() string {
	if x != nil {
		return x.Domain
	}
	return ""
}

func (x *AccountInfo) GetExtend() *Extend {
	if x != nil {
		return x.Extend
	}
	return nil
}

func (x *AccountInfo) GetJobNum() string {
	if x != nil {
		return x.JobNum
	}
	return ""
}

func (x *AccountInfo) GetBirthDate() string {
	if x != nil {
		return x.BirthDate
	}
	return ""
}

func (x *AccountInfo) GetAge() uint64 {
	if x != nil {
		return x.Age
	}
	return 0
}

func (x *AccountInfo) GetSex() uint64 {
	if x != nil {
		return x.Sex
	}
	return 0
}

func (x *AccountInfo) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

func (x *AccountInfo) GetDepartments() []*Department {
	if x != nil {
		return x.Departments
	}
	return nil
}

func (x *AccountInfo) GetIp() string {
	if x != nil {
		return x.Ip
	}
	return ""
}

func (x *AccountInfo) GetLoginDate() string {
	if x != nil {
		return x.LoginDate
	}
	return ""
}

func (x *AccountInfo) GetInvitationCode() string {
	if x != nil {
		return x.InvitationCode
	}
	return ""
}

func (x *AccountInfo) GetNowLogId() uint64 {
	if x != nil {
		return x.NowLogId
	}
	return 0
}

func (x *AccountInfo) GetCanScan() bool {
	if x != nil {
		return x.CanScan
	}
	return false
}

func (x *AccountInfo) GetLeftDate() string {
	if x != nil {
		return x.LeftDate
	}
	return ""
}

func (x *AccountInfo) GetPositions() []*PositionUser {
	if x != nil {
		return x.Positions
	}
	return nil
}

func (x *AccountInfo) GetRemark() string {
	if x != nil {
		return x.Remark
	}
	return ""
}

func (x *AccountInfo) GetRecentImg() string {
	if x != nil {
		return x.RecentImg
	}
	return ""
}

func (x *AccountInfo) GetClocks() []*ClockUser {
	if x != nil {
		return x.Clocks
	}
	return nil
}

func (x *AccountInfo) GetMailAccount() string {
	if x != nil {
		return x.MailAccount
	}
	return ""
}

func (x *AccountInfo) GetICNum() string {
	if x != nil {
		return x.ICNum
	}
	return ""
}

func (x *AccountInfo) GetEnglishName() string {
	if x != nil {
		return x.EnglishName
	}
	return ""
}

func (x *AccountInfo) GetTrain() string {
	if x != nil {
		return x.Train
	}
	return ""
}

func (x *AccountInfo) GetCertificate() string {
	if x != nil {
		return x.Certificate
	}
	return ""
}

func (x *AccountInfo) GetTrainVideos() []*TrainVideo {
	if x != nil {
		return x.TrainVideos
	}
	return nil
}

func (x *AccountInfo) GetOperator() *Operator {
	if x != nil {
		return x.Operator
	}
	return nil
}

func (x *AccountInfo) GetUpdatedAt() string {
	if x != nil {
		return x.UpdatedAt
	}
	return ""
}

func (x *AccountInfo) GetSecurityCode() string {
	if x != nil {
		return x.SecurityCode
	}
	return ""
}

func (x *AccountInfo) GetBlockAddr() string {
	if x != nil {
		return x.BlockAddr
	}
	return ""
}

func (x *AccountInfo) GetLanguage() string {
	if x != nil {
		return x.Language
	}
	return ""
}

func (x *AccountInfo) GetUserExtend() *UserExtend {
	if x != nil {
		return x.UserExtend
	}
	return nil
}

type UserInfoV2 struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ID          uint64    `protobuf:"varint,1,opt,name=ID,proto3" json:"ID,omitempty"`
	Account     string    `protobuf:"bytes,2,opt,name=Account,proto3" json:"Account,omitempty"`
	NickName    string    `protobuf:"bytes,3,opt,name=NickName,proto3" json:"NickName,omitempty"`
	TelNum      string    `protobuf:"bytes,4,opt,name=TelNum,proto3" json:"TelNum,omitempty"`
	Status      string    `protobuf:"bytes,5,opt,name=Status,proto3" json:"Status,omitempty"`
	Avatar      string    `protobuf:"bytes,6,opt,name=Avatar,proto3" json:"Avatar,omitempty"`
	CreateAt    string    `protobuf:"bytes,7,opt,name=CreateAt,proto3" json:"CreateAt,omitempty"`
	RealName    string    `protobuf:"bytes,8,opt,name=RealName,proto3" json:"RealName,omitempty"`
	IDNum       string    `protobuf:"bytes,9,opt,name=IDNum,proto3" json:"IDNum,omitempty"`
	EnterDate   string    `protobuf:"bytes,10,opt,name=EnterDate,proto3" json:"EnterDate,omitempty"`
	Extend      *Extend   `protobuf:"bytes,11,opt,name=Extend,proto3" json:"Extend,omitempty"`
	JobNum      string    `protobuf:"bytes,12,opt,name=JobNum,proto3" json:"JobNum,omitempty"`
	RecentImg   string    `protobuf:"bytes,13,opt,name=recentImg,proto3" json:"recentImg,omitempty"`
	MailAccount string    `protobuf:"bytes,14,opt,name=mailAccount,proto3" json:"mailAccount,omitempty"`
	Operator    *Operator `protobuf:"bytes,15,opt,name=operator,proto3" json:"operator,omitempty"`
	UpdatedAt   string    `protobuf:"bytes,16,opt,name=updatedAt,proto3" json:"updatedAt,omitempty"`
}

func (x *UserInfoV2) Reset() {
	*x = UserInfoV2{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_account_account_proto_msgTypes[81]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UserInfoV2) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserInfoV2) ProtoMessage() {}

func (x *UserInfoV2) ProtoReflect() protoreflect.Message {
	mi := &file_api_account_account_proto_msgTypes[81]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserInfoV2.ProtoReflect.Descriptor instead.
func (*UserInfoV2) Descriptor() ([]byte, []int) {
	return file_api_account_account_proto_rawDescGZIP(), []int{81}
}

func (x *UserInfoV2) GetID() uint64 {
	if x != nil {
		return x.ID
	}
	return 0
}

func (x *UserInfoV2) GetAccount() string {
	if x != nil {
		return x.Account
	}
	return ""
}

func (x *UserInfoV2) GetNickName() string {
	if x != nil {
		return x.NickName
	}
	return ""
}

func (x *UserInfoV2) GetTelNum() string {
	if x != nil {
		return x.TelNum
	}
	return ""
}

func (x *UserInfoV2) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

func (x *UserInfoV2) GetAvatar() string {
	if x != nil {
		return x.Avatar
	}
	return ""
}

func (x *UserInfoV2) GetCreateAt() string {
	if x != nil {
		return x.CreateAt
	}
	return ""
}

func (x *UserInfoV2) GetRealName() string {
	if x != nil {
		return x.RealName
	}
	return ""
}

func (x *UserInfoV2) GetIDNum() string {
	if x != nil {
		return x.IDNum
	}
	return ""
}

func (x *UserInfoV2) GetEnterDate() string {
	if x != nil {
		return x.EnterDate
	}
	return ""
}

func (x *UserInfoV2) GetExtend() *Extend {
	if x != nil {
		return x.Extend
	}
	return nil
}

func (x *UserInfoV2) GetJobNum() string {
	if x != nil {
		return x.JobNum
	}
	return ""
}

func (x *UserInfoV2) GetRecentImg() string {
	if x != nil {
		return x.RecentImg
	}
	return ""
}

func (x *UserInfoV2) GetMailAccount() string {
	if x != nil {
		return x.MailAccount
	}
	return ""
}

func (x *UserInfoV2) GetOperator() *Operator {
	if x != nil {
		return x.Operator
	}
	return nil
}

func (x *UserInfoV2) GetUpdatedAt() string {
	if x != nil {
		return x.UpdatedAt
	}
	return ""
}

type RefreshTokenRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RefreshToken string `protobuf:"bytes,1,opt,name=refreshToken,proto3" json:"refreshToken,omitempty"`
	Domain       string `protobuf:"bytes,2,opt,name=domain,proto3" json:"domain,omitempty"`
	Ip           string `protobuf:"bytes,3,opt,name=ip,proto3" json:"ip,omitempty"`
}

func (x *RefreshTokenRequest) Reset() {
	*x = RefreshTokenRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_account_account_proto_msgTypes[82]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RefreshTokenRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RefreshTokenRequest) ProtoMessage() {}

func (x *RefreshTokenRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_account_account_proto_msgTypes[82]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RefreshTokenRequest.ProtoReflect.Descriptor instead.
func (*RefreshTokenRequest) Descriptor() ([]byte, []int) {
	return file_api_account_account_proto_rawDescGZIP(), []int{82}
}

func (x *RefreshTokenRequest) GetRefreshToken() string {
	if x != nil {
		return x.RefreshToken
	}
	return ""
}

func (x *RefreshTokenRequest) GetDomain() string {
	if x != nil {
		return x.Domain
	}
	return ""
}

func (x *RefreshTokenRequest) GetIp() string {
	if x != nil {
		return x.Ip
	}
	return ""
}

type PositionUser struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PositionID     uint64 `protobuf:"varint,1,opt,name=PositionID,json=positionID,proto3" json:"PositionID,omitempty"`
	PositionName   string `protobuf:"bytes,2,opt,name=PositionName,json=positionName,proto3" json:"PositionName,omitempty"`
	DepartmentId   uint64 `protobuf:"varint,3,opt,name=DepartmentId,json=departmentId,proto3" json:"DepartmentId,omitempty"`
	DepartmentCode string `protobuf:"bytes,4,opt,name=DepartmentCode,json=departmentCode,proto3" json:"DepartmentCode,omitempty"`
	DepartmentName string `protobuf:"bytes,5,opt,name=DepartmentName,json=departmentName,proto3" json:"DepartmentName,omitempty"`
	UserId         uint64 `protobuf:"varint,6,opt,name=UserId,json=userId,proto3" json:"UserId,omitempty"`
	UserName       string `protobuf:"bytes,7,opt,name=UserName,json=userName,proto3" json:"UserName,omitempty"`
}

func (x *PositionUser) Reset() {
	*x = PositionUser{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_account_account_proto_msgTypes[83]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PositionUser) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PositionUser) ProtoMessage() {}

func (x *PositionUser) ProtoReflect() protoreflect.Message {
	mi := &file_api_account_account_proto_msgTypes[83]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PositionUser.ProtoReflect.Descriptor instead.
func (*PositionUser) Descriptor() ([]byte, []int) {
	return file_api_account_account_proto_rawDescGZIP(), []int{83}
}

func (x *PositionUser) GetPositionID() uint64 {
	if x != nil {
		return x.PositionID
	}
	return 0
}

func (x *PositionUser) GetPositionName() string {
	if x != nil {
		return x.PositionName
	}
	return ""
}

func (x *PositionUser) GetDepartmentId() uint64 {
	if x != nil {
		return x.DepartmentId
	}
	return 0
}

func (x *PositionUser) GetDepartmentCode() string {
	if x != nil {
		return x.DepartmentCode
	}
	return ""
}

func (x *PositionUser) GetDepartmentName() string {
	if x != nil {
		return x.DepartmentName
	}
	return ""
}

func (x *PositionUser) GetUserId() uint64 {
	if x != nil {
		return x.UserId
	}
	return 0
}

func (x *PositionUser) GetUserName() string {
	if x != nil {
		return x.UserName
	}
	return ""
}

type JobNumGetInfoRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	JobNum string `protobuf:"bytes,1,opt,name=jobNum,proto3" json:"jobNum,omitempty"`
	Domain string `protobuf:"bytes,2,opt,name=domain,proto3" json:"domain,omitempty"`
}

func (x *JobNumGetInfoRequest) Reset() {
	*x = JobNumGetInfoRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_account_account_proto_msgTypes[84]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *JobNumGetInfoRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*JobNumGetInfoRequest) ProtoMessage() {}

func (x *JobNumGetInfoRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_account_account_proto_msgTypes[84]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use JobNumGetInfoRequest.ProtoReflect.Descriptor instead.
func (*JobNumGetInfoRequest) Descriptor() ([]byte, []int) {
	return file_api_account_account_proto_rawDescGZIP(), []int{84}
}

func (x *JobNumGetInfoRequest) GetJobNum() string {
	if x != nil {
		return x.JobNum
	}
	return ""
}

func (x *JobNumGetInfoRequest) GetDomain() string {
	if x != nil {
		return x.Domain
	}
	return ""
}

type CreateClockDeviceRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	DeviceNum  string `protobuf:"bytes,1,opt,name=deviceNum,proto3" json:"deviceNum,omitempty"`
	DeviceName string `protobuf:"bytes,2,opt,name=deviceName,proto3" json:"deviceName,omitempty"`
	DeviceSite string `protobuf:"bytes,3,opt,name=deviceSite,proto3" json:"deviceSite,omitempty"`
}

func (x *CreateClockDeviceRequest) Reset() {
	*x = CreateClockDeviceRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_account_account_proto_msgTypes[85]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateClockDeviceRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateClockDeviceRequest) ProtoMessage() {}

func (x *CreateClockDeviceRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_account_account_proto_msgTypes[85]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateClockDeviceRequest.ProtoReflect.Descriptor instead.
func (*CreateClockDeviceRequest) Descriptor() ([]byte, []int) {
	return file_api_account_account_proto_rawDescGZIP(), []int{85}
}

func (x *CreateClockDeviceRequest) GetDeviceNum() string {
	if x != nil {
		return x.DeviceNum
	}
	return ""
}

func (x *CreateClockDeviceRequest) GetDeviceName() string {
	if x != nil {
		return x.DeviceName
	}
	return ""
}

func (x *CreateClockDeviceRequest) GetDeviceSite() string {
	if x != nil {
		return x.DeviceSite
	}
	return ""
}

type UpdateClockDeviceRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id         uint64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	DeviceNum  string `protobuf:"bytes,2,opt,name=deviceNum,proto3" json:"deviceNum,omitempty"`
	DeviceName string `protobuf:"bytes,3,opt,name=deviceName,proto3" json:"deviceName,omitempty"`
	DeviceSite string `protobuf:"bytes,4,opt,name=deviceSite,proto3" json:"deviceSite,omitempty"`
}

func (x *UpdateClockDeviceRequest) Reset() {
	*x = UpdateClockDeviceRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_account_account_proto_msgTypes[86]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateClockDeviceRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateClockDeviceRequest) ProtoMessage() {}

func (x *UpdateClockDeviceRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_account_account_proto_msgTypes[86]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateClockDeviceRequest.ProtoReflect.Descriptor instead.
func (*UpdateClockDeviceRequest) Descriptor() ([]byte, []int) {
	return file_api_account_account_proto_rawDescGZIP(), []int{86}
}

func (x *UpdateClockDeviceRequest) GetId() uint64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *UpdateClockDeviceRequest) GetDeviceNum() string {
	if x != nil {
		return x.DeviceNum
	}
	return ""
}

func (x *UpdateClockDeviceRequest) GetDeviceName() string {
	if x != nil {
		return x.DeviceName
	}
	return ""
}

func (x *UpdateClockDeviceRequest) GetDeviceSite() string {
	if x != nil {
		return x.DeviceSite
	}
	return ""
}

type ClockDeviceResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id uint64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
}

func (x *ClockDeviceResponse) Reset() {
	*x = ClockDeviceResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_account_account_proto_msgTypes[87]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ClockDeviceResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ClockDeviceResponse) ProtoMessage() {}

func (x *ClockDeviceResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_account_account_proto_msgTypes[87]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ClockDeviceResponse.ProtoReflect.Descriptor instead.
func (*ClockDeviceResponse) Descriptor() ([]byte, []int) {
	return file_api_account_account_proto_rawDescGZIP(), []int{87}
}

func (x *ClockDeviceResponse) GetId() uint64 {
	if x != nil {
		return x.Id
	}
	return 0
}

type RemoveClockDeviceRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id uint64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
}

func (x *RemoveClockDeviceRequest) Reset() {
	*x = RemoveClockDeviceRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_account_account_proto_msgTypes[88]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RemoveClockDeviceRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RemoveClockDeviceRequest) ProtoMessage() {}

func (x *RemoveClockDeviceRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_account_account_proto_msgTypes[88]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RemoveClockDeviceRequest.ProtoReflect.Descriptor instead.
func (*RemoveClockDeviceRequest) Descriptor() ([]byte, []int) {
	return file_api_account_account_proto_rawDescGZIP(), []int{88}
}

func (x *RemoveClockDeviceRequest) GetId() uint64 {
	if x != nil {
		return x.Id
	}
	return 0
}

type ClockDeviceListRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id         uint64   `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	DeviceSite string   `protobuf:"bytes,2,opt,name=deviceSite,proto3" json:"deviceSite,omitempty"`
	DeviceNum  string   `protobuf:"bytes,3,opt,name=deviceNum,proto3" json:"deviceNum,omitempty"`
	DeviceName string   `protobuf:"bytes,4,opt,name=deviceName,proto3" json:"deviceName,omitempty"`
	Page       uint64   `protobuf:"varint,5,opt,name=page,proto3" json:"page,omitempty"`
	PageSize   uint64   `protobuf:"varint,6,opt,name=pageSize,proto3" json:"pageSize,omitempty"`
	Ids        []uint64 `protobuf:"varint,7,rep,packed,name=ids,proto3" json:"ids,omitempty"`
}

func (x *ClockDeviceListRequest) Reset() {
	*x = ClockDeviceListRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_account_account_proto_msgTypes[89]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ClockDeviceListRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ClockDeviceListRequest) ProtoMessage() {}

func (x *ClockDeviceListRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_account_account_proto_msgTypes[89]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ClockDeviceListRequest.ProtoReflect.Descriptor instead.
func (*ClockDeviceListRequest) Descriptor() ([]byte, []int) {
	return file_api_account_account_proto_rawDescGZIP(), []int{89}
}

func (x *ClockDeviceListRequest) GetId() uint64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *ClockDeviceListRequest) GetDeviceSite() string {
	if x != nil {
		return x.DeviceSite
	}
	return ""
}

func (x *ClockDeviceListRequest) GetDeviceNum() string {
	if x != nil {
		return x.DeviceNum
	}
	return ""
}

func (x *ClockDeviceListRequest) GetDeviceName() string {
	if x != nil {
		return x.DeviceName
	}
	return ""
}

func (x *ClockDeviceListRequest) GetPage() uint64 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *ClockDeviceListRequest) GetPageSize() uint64 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *ClockDeviceListRequest) GetIds() []uint64 {
	if x != nil {
		return x.Ids
	}
	return nil
}

type ClockDeviceListResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Count uint64             `protobuf:"varint,1,opt,name=count,proto3" json:"count,omitempty"`
	Data  []*ClockDeviceInfo `protobuf:"bytes,2,rep,name=data,proto3" json:"data,omitempty"`
}

func (x *ClockDeviceListResponse) Reset() {
	*x = ClockDeviceListResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_account_account_proto_msgTypes[90]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ClockDeviceListResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ClockDeviceListResponse) ProtoMessage() {}

func (x *ClockDeviceListResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_account_account_proto_msgTypes[90]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ClockDeviceListResponse.ProtoReflect.Descriptor instead.
func (*ClockDeviceListResponse) Descriptor() ([]byte, []int) {
	return file_api_account_account_proto_rawDescGZIP(), []int{90}
}

func (x *ClockDeviceListResponse) GetCount() uint64 {
	if x != nil {
		return x.Count
	}
	return 0
}

func (x *ClockDeviceListResponse) GetData() []*ClockDeviceInfo {
	if x != nil {
		return x.Data
	}
	return nil
}

type ClockUser struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id        uint64           `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	CreatedAt string           `protobuf:"bytes,2,opt,name=createdAt,proto3" json:"createdAt,omitempty"`
	UpdatedAt string           `protobuf:"bytes,3,opt,name=updatedAt,proto3" json:"updatedAt,omitempty"`
	DeletedAt string           `protobuf:"bytes,4,opt,name=deletedAt,proto3" json:"deletedAt,omitempty"`
	DeviceID  uint64           `protobuf:"varint,5,opt,name=deviceID,proto3" json:"deviceID,omitempty"`
	UserId    uint64           `protobuf:"varint,6,opt,name=userId,proto3" json:"userId,omitempty"`
	Status    uint64           `protobuf:"varint,7,opt,name=status,proto3" json:"status,omitempty"`
	Device    *ClockDeviceInfo `protobuf:"bytes,8,opt,name=device,proto3" json:"device,omitempty"`
}

func (x *ClockUser) Reset() {
	*x = ClockUser{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_account_account_proto_msgTypes[91]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ClockUser) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ClockUser) ProtoMessage() {}

func (x *ClockUser) ProtoReflect() protoreflect.Message {
	mi := &file_api_account_account_proto_msgTypes[91]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ClockUser.ProtoReflect.Descriptor instead.
func (*ClockUser) Descriptor() ([]byte, []int) {
	return file_api_account_account_proto_rawDescGZIP(), []int{91}
}

func (x *ClockUser) GetId() uint64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *ClockUser) GetCreatedAt() string {
	if x != nil {
		return x.CreatedAt
	}
	return ""
}

func (x *ClockUser) GetUpdatedAt() string {
	if x != nil {
		return x.UpdatedAt
	}
	return ""
}

func (x *ClockUser) GetDeletedAt() string {
	if x != nil {
		return x.DeletedAt
	}
	return ""
}

func (x *ClockUser) GetDeviceID() uint64 {
	if x != nil {
		return x.DeviceID
	}
	return 0
}

func (x *ClockUser) GetUserId() uint64 {
	if x != nil {
		return x.UserId
	}
	return 0
}

func (x *ClockUser) GetStatus() uint64 {
	if x != nil {
		return x.Status
	}
	return 0
}

func (x *ClockUser) GetDevice() *ClockDeviceInfo {
	if x != nil {
		return x.Device
	}
	return nil
}

type ClockDeviceInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id         uint64          `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	CreateAt   string          `protobuf:"bytes,2,opt,name=createAt,proto3" json:"createAt,omitempty"`
	UpdateAt   string          `protobuf:"bytes,3,opt,name=updateAt,proto3" json:"updateAt,omitempty"`
	DeviceSite string          `protobuf:"bytes,4,opt,name=deviceSite,proto3" json:"deviceSite,omitempty"`
	DeviceNum  string          `protobuf:"bytes,5,opt,name=deviceNum,proto3" json:"deviceNum,omitempty"`
	DeviceName string          `protobuf:"bytes,6,opt,name=deviceName,proto3" json:"deviceName,omitempty"`
	UserNum    uint64          `protobuf:"varint,7,opt,name=userNum,proto3" json:"userNum,omitempty"`
	Data       []*ClockUserRel `protobuf:"bytes,8,rep,name=data,proto3" json:"data,omitempty"`
}

func (x *ClockDeviceInfo) Reset() {
	*x = ClockDeviceInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_account_account_proto_msgTypes[92]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ClockDeviceInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ClockDeviceInfo) ProtoMessage() {}

func (x *ClockDeviceInfo) ProtoReflect() protoreflect.Message {
	mi := &file_api_account_account_proto_msgTypes[92]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ClockDeviceInfo.ProtoReflect.Descriptor instead.
func (*ClockDeviceInfo) Descriptor() ([]byte, []int) {
	return file_api_account_account_proto_rawDescGZIP(), []int{92}
}

func (x *ClockDeviceInfo) GetId() uint64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *ClockDeviceInfo) GetCreateAt() string {
	if x != nil {
		return x.CreateAt
	}
	return ""
}

func (x *ClockDeviceInfo) GetUpdateAt() string {
	if x != nil {
		return x.UpdateAt
	}
	return ""
}

func (x *ClockDeviceInfo) GetDeviceSite() string {
	if x != nil {
		return x.DeviceSite
	}
	return ""
}

func (x *ClockDeviceInfo) GetDeviceNum() string {
	if x != nil {
		return x.DeviceNum
	}
	return ""
}

func (x *ClockDeviceInfo) GetDeviceName() string {
	if x != nil {
		return x.DeviceName
	}
	return ""
}

func (x *ClockDeviceInfo) GetUserNum() uint64 {
	if x != nil {
		return x.UserNum
	}
	return 0
}

func (x *ClockDeviceInfo) GetData() []*ClockUserRel {
	if x != nil {
		return x.Data
	}
	return nil
}

type ClockDeviceInfoResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Count uint64          `protobuf:"varint,1,opt,name=count,proto3" json:"count,omitempty"`
	Data  []*ClockUserRel `protobuf:"bytes,2,rep,name=data,proto3" json:"data,omitempty"`
}

func (x *ClockDeviceInfoResponse) Reset() {
	*x = ClockDeviceInfoResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_account_account_proto_msgTypes[93]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ClockDeviceInfoResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ClockDeviceInfoResponse) ProtoMessage() {}

func (x *ClockDeviceInfoResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_account_account_proto_msgTypes[93]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ClockDeviceInfoResponse.ProtoReflect.Descriptor instead.
func (*ClockDeviceInfoResponse) Descriptor() ([]byte, []int) {
	return file_api_account_account_proto_rawDescGZIP(), []int{93}
}

func (x *ClockDeviceInfoResponse) GetCount() uint64 {
	if x != nil {
		return x.Count
	}
	return 0
}

func (x *ClockDeviceInfoResponse) GetData() []*ClockUserRel {
	if x != nil {
		return x.Data
	}
	return nil
}

type ClockUserRel struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id       uint64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	CreateAt string `protobuf:"bytes,2,opt,name=createAt,proto3" json:"createAt,omitempty"`
	UpdateAt string `protobuf:"bytes,3,opt,name=updateAt,proto3" json:"updateAt,omitempty"`
	NickName string `protobuf:"bytes,4,opt,name=nickName,proto3" json:"nickName,omitempty"`
	JobNum   string `protobuf:"bytes,5,opt,name=jobNum,proto3" json:"jobNum,omitempty"`
	IcNum    string `protobuf:"bytes,6,opt,name=icNum,proto3" json:"icNum,omitempty"`
}

func (x *ClockUserRel) Reset() {
	*x = ClockUserRel{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_account_account_proto_msgTypes[94]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ClockUserRel) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ClockUserRel) ProtoMessage() {}

func (x *ClockUserRel) ProtoReflect() protoreflect.Message {
	mi := &file_api_account_account_proto_msgTypes[94]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ClockUserRel.ProtoReflect.Descriptor instead.
func (*ClockUserRel) Descriptor() ([]byte, []int) {
	return file_api_account_account_proto_rawDescGZIP(), []int{94}
}

func (x *ClockUserRel) GetId() uint64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *ClockUserRel) GetCreateAt() string {
	if x != nil {
		return x.CreateAt
	}
	return ""
}

func (x *ClockUserRel) GetUpdateAt() string {
	if x != nil {
		return x.UpdateAt
	}
	return ""
}

func (x *ClockUserRel) GetNickName() string {
	if x != nil {
		return x.NickName
	}
	return ""
}

func (x *ClockUserRel) GetJobNum() string {
	if x != nil {
		return x.JobNum
	}
	return ""
}

func (x *ClockUserRel) GetIcNum() string {
	if x != nil {
		return x.IcNum
	}
	return ""
}

type ClockDeviceInfoRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id       uint64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Page     uint64 `protobuf:"varint,2,opt,name=page,proto3" json:"page,omitempty"`
	PageSize uint64 `protobuf:"varint,3,opt,name=pageSize,proto3" json:"pageSize,omitempty"`
}

func (x *ClockDeviceInfoRequest) Reset() {
	*x = ClockDeviceInfoRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_account_account_proto_msgTypes[95]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ClockDeviceInfoRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ClockDeviceInfoRequest) ProtoMessage() {}

func (x *ClockDeviceInfoRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_account_account_proto_msgTypes[95]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ClockDeviceInfoRequest.ProtoReflect.Descriptor instead.
func (*ClockDeviceInfoRequest) Descriptor() ([]byte, []int) {
	return file_api_account_account_proto_rawDescGZIP(), []int{95}
}

func (x *ClockDeviceInfoRequest) GetId() uint64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *ClockDeviceInfoRequest) GetPage() uint64 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *ClockDeviceInfoRequest) GetPageSize() uint64 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

type ClockBatchBindRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id       uint64   `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	UserId   []uint64 `protobuf:"varint,2,rep,packed,name=userId,proto3" json:"userId,omitempty"`
	DeviceId []uint64 `protobuf:"varint,3,rep,packed,name=deviceId,proto3" json:"deviceId,omitempty"`
}

func (x *ClockBatchBindRequest) Reset() {
	*x = ClockBatchBindRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_account_account_proto_msgTypes[96]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ClockBatchBindRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ClockBatchBindRequest) ProtoMessage() {}

func (x *ClockBatchBindRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_account_account_proto_msgTypes[96]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ClockBatchBindRequest.ProtoReflect.Descriptor instead.
func (*ClockBatchBindRequest) Descriptor() ([]byte, []int) {
	return file_api_account_account_proto_rawDescGZIP(), []int{96}
}

func (x *ClockBatchBindRequest) GetId() uint64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *ClockBatchBindRequest) GetUserId() []uint64 {
	if x != nil {
		return x.UserId
	}
	return nil
}

func (x *ClockBatchBindRequest) GetDeviceId() []uint64 {
	if x != nil {
		return x.DeviceId
	}
	return nil
}

type ClockBatchListResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Data []*ClockUserDeviceBatch `protobuf:"bytes,1,rep,name=data,proto3" json:"data,omitempty"`
}

func (x *ClockBatchListResponse) Reset() {
	*x = ClockBatchListResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_account_account_proto_msgTypes[97]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ClockBatchListResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ClockBatchListResponse) ProtoMessage() {}

func (x *ClockBatchListResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_account_account_proto_msgTypes[97]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ClockBatchListResponse.ProtoReflect.Descriptor instead.
func (*ClockBatchListResponse) Descriptor() ([]byte, []int) {
	return file_api_account_account_proto_rawDescGZIP(), []int{97}
}

func (x *ClockBatchListResponse) GetData() []*ClockUserDeviceBatch {
	if x != nil {
		return x.Data
	}
	return nil
}

type ClockUserDeviceBatch struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UserId     uint64 `protobuf:"varint,1,opt,name=userId,proto3" json:"userId,omitempty"`
	DeviceId   uint64 `protobuf:"varint,2,opt,name=deviceId,proto3" json:"deviceId,omitempty"`
	JobNum     string `protobuf:"bytes,3,opt,name=JobNum,proto3" json:"JobNum,omitempty"`
	DeviceNum  string `protobuf:"bytes,4,opt,name=deviceNum,proto3" json:"deviceNum,omitempty"`
	DeviceName string `protobuf:"bytes,5,opt,name=deviceName,proto3" json:"deviceName,omitempty"`
	WorkStatus string `protobuf:"bytes,6,opt,name=workStatus,proto3" json:"workStatus,omitempty"`
}

func (x *ClockUserDeviceBatch) Reset() {
	*x = ClockUserDeviceBatch{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_account_account_proto_msgTypes[98]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ClockUserDeviceBatch) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ClockUserDeviceBatch) ProtoMessage() {}

func (x *ClockUserDeviceBatch) ProtoReflect() protoreflect.Message {
	mi := &file_api_account_account_proto_msgTypes[98]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ClockUserDeviceBatch.ProtoReflect.Descriptor instead.
func (*ClockUserDeviceBatch) Descriptor() ([]byte, []int) {
	return file_api_account_account_proto_rawDescGZIP(), []int{98}
}

func (x *ClockUserDeviceBatch) GetUserId() uint64 {
	if x != nil {
		return x.UserId
	}
	return 0
}

func (x *ClockUserDeviceBatch) GetDeviceId() uint64 {
	if x != nil {
		return x.DeviceId
	}
	return 0
}

func (x *ClockUserDeviceBatch) GetJobNum() string {
	if x != nil {
		return x.JobNum
	}
	return ""
}

func (x *ClockUserDeviceBatch) GetDeviceNum() string {
	if x != nil {
		return x.DeviceNum
	}
	return ""
}

func (x *ClockUserDeviceBatch) GetDeviceName() string {
	if x != nil {
		return x.DeviceName
	}
	return ""
}

func (x *ClockUserDeviceBatch) GetWorkStatus() string {
	if x != nil {
		return x.WorkStatus
	}
	return ""
}

type ClockLogInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id           uint64  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Sn           string  `protobuf:"bytes,2,opt,name=sn,proto3" json:"sn,omitempty"`
	UserId       string  `protobuf:"bytes,3,opt,name=userId,proto3" json:"userId,omitempty"`
	RecogType    string  `protobuf:"bytes,4,opt,name=recogType,proto3" json:"recogType,omitempty"`
	RecogTime    string  `protobuf:"bytes,5,opt,name=recogTime,proto3" json:"recogTime,omitempty"`
	Gender       float32 `protobuf:"fixed32,6,opt,name=gender,proto3" json:"gender,omitempty"`
	Photo        string  `protobuf:"bytes,7,opt,name=photo,proto3" json:"photo,omitempty"`
	PassStatus   float32 `protobuf:"fixed32,8,opt,name=passStatus,proto3" json:"passStatus,omitempty"`
	UserName     string  `protobuf:"bytes,9,opt,name=userName,proto3" json:"userName,omitempty"`
	UserType     float32 `protobuf:"fixed32,10,opt,name=userType,proto3" json:"userType,omitempty"`
	Confidence   string  `protobuf:"bytes,11,opt,name=confidence,proto3" json:"confidence,omitempty"`
	Reflectivity float32 `protobuf:"fixed32,12,opt,name=reflectivity,proto3" json:"reflectivity,omitempty"`
	CardNumber   string  `protobuf:"bytes,13,opt,name=cardNumber,proto3" json:"cardNumber,omitempty"`
	PassWord     string  `protobuf:"bytes,14,opt,name=passWord,proto3" json:"passWord,omitempty"`
	QrCode       string  `protobuf:"bytes,15,opt,name=qrCode,proto3" json:"qrCode,omitempty"`
	Tel          string  `protobuf:"bytes,16,opt,name=tel,proto3" json:"tel,omitempty"`
	ReasonVisit  string  `protobuf:"bytes,17,opt,name=reasonVisit,proto3" json:"reasonVisit,omitempty"`
	ReceiverTel  string  `protobuf:"bytes,18,opt,name=receiverTel,proto3" json:"receiverTel,omitempty"`
	NumOfPeople  uint64  `protobuf:"varint,19,opt,name=numOfPeople,proto3" json:"numOfPeople,omitempty"`
}

func (x *ClockLogInfo) Reset() {
	*x = ClockLogInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_account_account_proto_msgTypes[99]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ClockLogInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ClockLogInfo) ProtoMessage() {}

func (x *ClockLogInfo) ProtoReflect() protoreflect.Message {
	mi := &file_api_account_account_proto_msgTypes[99]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ClockLogInfo.ProtoReflect.Descriptor instead.
func (*ClockLogInfo) Descriptor() ([]byte, []int) {
	return file_api_account_account_proto_rawDescGZIP(), []int{99}
}

func (x *ClockLogInfo) GetId() uint64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *ClockLogInfo) GetSn() string {
	if x != nil {
		return x.Sn
	}
	return ""
}

func (x *ClockLogInfo) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *ClockLogInfo) GetRecogType() string {
	if x != nil {
		return x.RecogType
	}
	return ""
}

func (x *ClockLogInfo) GetRecogTime() string {
	if x != nil {
		return x.RecogTime
	}
	return ""
}

func (x *ClockLogInfo) GetGender() float32 {
	if x != nil {
		return x.Gender
	}
	return 0
}

func (x *ClockLogInfo) GetPhoto() string {
	if x != nil {
		return x.Photo
	}
	return ""
}

func (x *ClockLogInfo) GetPassStatus() float32 {
	if x != nil {
		return x.PassStatus
	}
	return 0
}

func (x *ClockLogInfo) GetUserName() string {
	if x != nil {
		return x.UserName
	}
	return ""
}

func (x *ClockLogInfo) GetUserType() float32 {
	if x != nil {
		return x.UserType
	}
	return 0
}

func (x *ClockLogInfo) GetConfidence() string {
	if x != nil {
		return x.Confidence
	}
	return ""
}

func (x *ClockLogInfo) GetReflectivity() float32 {
	if x != nil {
		return x.Reflectivity
	}
	return 0
}

func (x *ClockLogInfo) GetCardNumber() string {
	if x != nil {
		return x.CardNumber
	}
	return ""
}

func (x *ClockLogInfo) GetPassWord() string {
	if x != nil {
		return x.PassWord
	}
	return ""
}

func (x *ClockLogInfo) GetQrCode() string {
	if x != nil {
		return x.QrCode
	}
	return ""
}

func (x *ClockLogInfo) GetTel() string {
	if x != nil {
		return x.Tel
	}
	return ""
}

func (x *ClockLogInfo) GetReasonVisit() string {
	if x != nil {
		return x.ReasonVisit
	}
	return ""
}

func (x *ClockLogInfo) GetReceiverTel() string {
	if x != nil {
		return x.ReceiverTel
	}
	return ""
}

func (x *ClockLogInfo) GetNumOfPeople() uint64 {
	if x != nil {
		return x.NumOfPeople
	}
	return 0
}

type ClockLogReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id        uint64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Page      uint64 `protobuf:"varint,2,opt,name=page,proto3" json:"page,omitempty"`
	PageSize  uint64 `protobuf:"varint,3,opt,name=pageSize,proto3" json:"pageSize,omitempty"`
	UserName  string `protobuf:"bytes,4,opt,name=userName,proto3" json:"userName,omitempty"`
	RecogType string `protobuf:"bytes,5,opt,name=recogType,proto3" json:"recogType,omitempty"`
	DeviceNum string `protobuf:"bytes,6,opt,name=deviceNum,proto3" json:"deviceNum,omitempty"`
	RecogDate string `protobuf:"bytes,7,opt,name=recogDate,proto3" json:"recogDate,omitempty"`
	UserId    uint64 `protobuf:"varint,8,opt,name=userId,proto3" json:"userId,omitempty"`
}

func (x *ClockLogReq) Reset() {
	*x = ClockLogReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_account_account_proto_msgTypes[100]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ClockLogReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ClockLogReq) ProtoMessage() {}

func (x *ClockLogReq) ProtoReflect() protoreflect.Message {
	mi := &file_api_account_account_proto_msgTypes[100]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ClockLogReq.ProtoReflect.Descriptor instead.
func (*ClockLogReq) Descriptor() ([]byte, []int) {
	return file_api_account_account_proto_rawDescGZIP(), []int{100}
}

func (x *ClockLogReq) GetId() uint64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *ClockLogReq) GetPage() uint64 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *ClockLogReq) GetPageSize() uint64 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *ClockLogReq) GetUserName() string {
	if x != nil {
		return x.UserName
	}
	return ""
}

func (x *ClockLogReq) GetRecogType() string {
	if x != nil {
		return x.RecogType
	}
	return ""
}

func (x *ClockLogReq) GetDeviceNum() string {
	if x != nil {
		return x.DeviceNum
	}
	return ""
}

func (x *ClockLogReq) GetRecogDate() string {
	if x != nil {
		return x.RecogDate
	}
	return ""
}

func (x *ClockLogReq) GetUserId() uint64 {
	if x != nil {
		return x.UserId
	}
	return 0
}

type ClockLogListResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Data  []*ClockLogInfo `protobuf:"bytes,1,rep,name=data,proto3" json:"data,omitempty"`
	Count uint64          `protobuf:"varint,2,opt,name=count,proto3" json:"count,omitempty"`
}

func (x *ClockLogListResponse) Reset() {
	*x = ClockLogListResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_account_account_proto_msgTypes[101]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ClockLogListResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ClockLogListResponse) ProtoMessage() {}

func (x *ClockLogListResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_account_account_proto_msgTypes[101]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ClockLogListResponse.ProtoReflect.Descriptor instead.
func (*ClockLogListResponse) Descriptor() ([]byte, []int) {
	return file_api_account_account_proto_rawDescGZIP(), []int{101}
}

func (x *ClockLogListResponse) GetData() []*ClockLogInfo {
	if x != nil {
		return x.Data
	}
	return nil
}

func (x *ClockLogListResponse) GetCount() uint64 {
	if x != nil {
		return x.Count
	}
	return 0
}

type SendNationMsgRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Domain  string `protobuf:"bytes,1,opt,name=Domain,json=domain,proto3" json:"Domain,omitempty"`
	TelNum  string `protobuf:"bytes,2,opt,name=TelNum,json=telNum,proto3" json:"TelNum,omitempty"`
	Project string `protobuf:"bytes,3,opt,name=Project,json=project,proto3" json:"Project,omitempty"`
	SignNo  uint32 `protobuf:"varint,4,opt,name=signNo,proto3" json:"signNo,omitempty"`
	MId     uint32 `protobuf:"varint,5,opt,name=mId,proto3" json:"mId,omitempty"`
	Scope   string `protobuf:"bytes,6,opt,name=scope,proto3" json:"scope,omitempty"` //标记模块
}

func (x *SendNationMsgRequest) Reset() {
	*x = SendNationMsgRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_account_account_proto_msgTypes[102]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SendNationMsgRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SendNationMsgRequest) ProtoMessage() {}

func (x *SendNationMsgRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_account_account_proto_msgTypes[102]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SendNationMsgRequest.ProtoReflect.Descriptor instead.
func (*SendNationMsgRequest) Descriptor() ([]byte, []int) {
	return file_api_account_account_proto_rawDescGZIP(), []int{102}
}

func (x *SendNationMsgRequest) GetDomain() string {
	if x != nil {
		return x.Domain
	}
	return ""
}

func (x *SendNationMsgRequest) GetTelNum() string {
	if x != nil {
		return x.TelNum
	}
	return ""
}

func (x *SendNationMsgRequest) GetProject() string {
	if x != nil {
		return x.Project
	}
	return ""
}

func (x *SendNationMsgRequest) GetSignNo() uint32 {
	if x != nil {
		return x.SignNo
	}
	return 0
}

func (x *SendNationMsgRequest) GetMId() uint32 {
	if x != nil {
		return x.MId
	}
	return 0
}

func (x *SendNationMsgRequest) GetScope() string {
	if x != nil {
		return x.Scope
	}
	return ""
}

type UpdateLanguageRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Domain   string `protobuf:"bytes,1,opt,name=domain,proto3" json:"domain,omitempty"`
	ID       uint64 `protobuf:"varint,2,opt,name=ID,proto3" json:"ID,omitempty"`
	Language string `protobuf:"bytes,3,opt,name=language,proto3" json:"language,omitempty"`
}

func (x *UpdateLanguageRequest) Reset() {
	*x = UpdateLanguageRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_account_account_proto_msgTypes[103]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateLanguageRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateLanguageRequest) ProtoMessage() {}

func (x *UpdateLanguageRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_account_account_proto_msgTypes[103]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateLanguageRequest.ProtoReflect.Descriptor instead.
func (*UpdateLanguageRequest) Descriptor() ([]byte, []int) {
	return file_api_account_account_proto_rawDescGZIP(), []int{103}
}

func (x *UpdateLanguageRequest) GetDomain() string {
	if x != nil {
		return x.Domain
	}
	return ""
}

func (x *UpdateLanguageRequest) GetID() uint64 {
	if x != nil {
		return x.ID
	}
	return 0
}

func (x *UpdateLanguageRequest) GetLanguage() string {
	if x != nil {
		return x.Language
	}
	return ""
}

type UpdateLanguageResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ID       uint64 `protobuf:"varint,1,opt,name=ID,proto3" json:"ID,omitempty"`
	TelNum   string `protobuf:"bytes,2,opt,name=telNum,proto3" json:"telNum,omitempty"`
	Language string `protobuf:"bytes,3,opt,name=language,proto3" json:"language,omitempty"`
}

func (x *UpdateLanguageResponse) Reset() {
	*x = UpdateLanguageResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_account_account_proto_msgTypes[104]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateLanguageResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateLanguageResponse) ProtoMessage() {}

func (x *UpdateLanguageResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_account_account_proto_msgTypes[104]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateLanguageResponse.ProtoReflect.Descriptor instead.
func (*UpdateLanguageResponse) Descriptor() ([]byte, []int) {
	return file_api_account_account_proto_rawDescGZIP(), []int{104}
}

func (x *UpdateLanguageResponse) GetID() uint64 {
	if x != nil {
		return x.ID
	}
	return 0
}

func (x *UpdateLanguageResponse) GetTelNum() string {
	if x != nil {
		return x.TelNum
	}
	return ""
}

func (x *UpdateLanguageResponse) GetLanguage() string {
	if x != nil {
		return x.Language
	}
	return ""
}

type GenerateSliderCaptchaRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CanvasWidth  uint64 `protobuf:"varint,1,opt,name=canvasWidth,proto3" json:"canvasWidth,omitempty"`
	CanvasHeight uint64 `protobuf:"varint,2,opt,name=canvasHeight,proto3" json:"canvasHeight,omitempty"`
	BlockWidth   uint64 `protobuf:"varint,3,opt,name=blockWidth,proto3" json:"blockWidth,omitempty"`
	BlockHeight  uint64 `protobuf:"varint,4,opt,name=blockHeight,proto3" json:"blockHeight,omitempty"`
	BlockRadius  uint64 `protobuf:"varint,5,opt,name=blockRadius,proto3" json:"blockRadius,omitempty"`
	Place        uint64 `protobuf:"varint,6,opt,name=place,proto3" json:"place,omitempty"`
}

func (x *GenerateSliderCaptchaRequest) Reset() {
	*x = GenerateSliderCaptchaRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_account_account_proto_msgTypes[105]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GenerateSliderCaptchaRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GenerateSliderCaptchaRequest) ProtoMessage() {}

func (x *GenerateSliderCaptchaRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_account_account_proto_msgTypes[105]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GenerateSliderCaptchaRequest.ProtoReflect.Descriptor instead.
func (*GenerateSliderCaptchaRequest) Descriptor() ([]byte, []int) {
	return file_api_account_account_proto_rawDescGZIP(), []int{105}
}

func (x *GenerateSliderCaptchaRequest) GetCanvasWidth() uint64 {
	if x != nil {
		return x.CanvasWidth
	}
	return 0
}

func (x *GenerateSliderCaptchaRequest) GetCanvasHeight() uint64 {
	if x != nil {
		return x.CanvasHeight
	}
	return 0
}

func (x *GenerateSliderCaptchaRequest) GetBlockWidth() uint64 {
	if x != nil {
		return x.BlockWidth
	}
	return 0
}

func (x *GenerateSliderCaptchaRequest) GetBlockHeight() uint64 {
	if x != nil {
		return x.BlockHeight
	}
	return 0
}

func (x *GenerateSliderCaptchaRequest) GetBlockRadius() uint64 {
	if x != nil {
		return x.BlockRadius
	}
	return 0
}

func (x *GenerateSliderCaptchaRequest) GetPlace() uint64 {
	if x != nil {
		return x.Place
	}
	return 0
}

type GenerateSliderCaptchaResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	NonceStr  string `protobuf:"bytes,1,opt,name=nonceStr,proto3" json:"nonceStr,omitempty"`
	CanvasSrc string `protobuf:"bytes,2,opt,name=canvasSrc,proto3" json:"canvasSrc,omitempty"`
	BlockSrc  string `protobuf:"bytes,3,opt,name=blockSrc,proto3" json:"blockSrc,omitempty"`
	BlockY    uint64 `protobuf:"varint,4,opt,name=blockY,proto3" json:"blockY,omitempty"`
	FaceY     uint64 `protobuf:"varint,5,opt,name=faceY,proto3" json:"faceY,omitempty"`
	BlockX    uint64 `protobuf:"varint,6,opt,name=blockX,proto3" json:"blockX,omitempty"`
}

func (x *GenerateSliderCaptchaResponse) Reset() {
	*x = GenerateSliderCaptchaResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_account_account_proto_msgTypes[106]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GenerateSliderCaptchaResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GenerateSliderCaptchaResponse) ProtoMessage() {}

func (x *GenerateSliderCaptchaResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_account_account_proto_msgTypes[106]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GenerateSliderCaptchaResponse.ProtoReflect.Descriptor instead.
func (*GenerateSliderCaptchaResponse) Descriptor() ([]byte, []int) {
	return file_api_account_account_proto_rawDescGZIP(), []int{106}
}

func (x *GenerateSliderCaptchaResponse) GetNonceStr() string {
	if x != nil {
		return x.NonceStr
	}
	return ""
}

func (x *GenerateSliderCaptchaResponse) GetCanvasSrc() string {
	if x != nil {
		return x.CanvasSrc
	}
	return ""
}

func (x *GenerateSliderCaptchaResponse) GetBlockSrc() string {
	if x != nil {
		return x.BlockSrc
	}
	return ""
}

func (x *GenerateSliderCaptchaResponse) GetBlockY() uint64 {
	if x != nil {
		return x.BlockY
	}
	return 0
}

func (x *GenerateSliderCaptchaResponse) GetFaceY() uint64 {
	if x != nil {
		return x.FaceY
	}
	return 0
}

func (x *GenerateSliderCaptchaResponse) GetBlockX() uint64 {
	if x != nil {
		return x.BlockX
	}
	return 0
}

type VerifySliderCaptchaRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	NonceStr string  `protobuf:"bytes,1,opt,name=nonceStr,proto3" json:"nonceStr,omitempty"`
	BlockX   float32 `protobuf:"fixed32,2,opt,name=blockX,proto3" json:"blockX,omitempty"`
}

func (x *VerifySliderCaptchaRequest) Reset() {
	*x = VerifySliderCaptchaRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_account_account_proto_msgTypes[107]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *VerifySliderCaptchaRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VerifySliderCaptchaRequest) ProtoMessage() {}

func (x *VerifySliderCaptchaRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_account_account_proto_msgTypes[107]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VerifySliderCaptchaRequest.ProtoReflect.Descriptor instead.
func (*VerifySliderCaptchaRequest) Descriptor() ([]byte, []int) {
	return file_api_account_account_proto_rawDescGZIP(), []int{107}
}

func (x *VerifySliderCaptchaRequest) GetNonceStr() string {
	if x != nil {
		return x.NonceStr
	}
	return ""
}

func (x *VerifySliderCaptchaRequest) GetBlockX() float32 {
	if x != nil {
		return x.BlockX
	}
	return 0
}

type VerifySliderCaptchaResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	NonceStr string `protobuf:"bytes,1,opt,name=nonceStr,proto3" json:"nonceStr,omitempty"`
}

func (x *VerifySliderCaptchaResponse) Reset() {
	*x = VerifySliderCaptchaResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_account_account_proto_msgTypes[108]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *VerifySliderCaptchaResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VerifySliderCaptchaResponse) ProtoMessage() {}

func (x *VerifySliderCaptchaResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_account_account_proto_msgTypes[108]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VerifySliderCaptchaResponse.ProtoReflect.Descriptor instead.
func (*VerifySliderCaptchaResponse) Descriptor() ([]byte, []int) {
	return file_api_account_account_proto_rawDescGZIP(), []int{108}
}

func (x *VerifySliderCaptchaResponse) GetNonceStr() string {
	if x != nil {
		return x.NonceStr
	}
	return ""
}

type VerifySliderStatusRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	NonceStr string `protobuf:"bytes,1,opt,name=nonceStr,proto3" json:"nonceStr,omitempty"`
}

func (x *VerifySliderStatusRequest) Reset() {
	*x = VerifySliderStatusRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_account_account_proto_msgTypes[109]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *VerifySliderStatusRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VerifySliderStatusRequest) ProtoMessage() {}

func (x *VerifySliderStatusRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_account_account_proto_msgTypes[109]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VerifySliderStatusRequest.ProtoReflect.Descriptor instead.
func (*VerifySliderStatusRequest) Descriptor() ([]byte, []int) {
	return file_api_account_account_proto_rawDescGZIP(), []int{109}
}

func (x *VerifySliderStatusRequest) GetNonceStr() string {
	if x != nil {
		return x.NonceStr
	}
	return ""
}

type VerifySliderStatusResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	NonceStr string `protobuf:"bytes,1,opt,name=nonceStr,proto3" json:"nonceStr,omitempty"`
	Status   int32  `protobuf:"varint,2,opt,name=status,proto3" json:"status,omitempty"`
}

func (x *VerifySliderStatusResponse) Reset() {
	*x = VerifySliderStatusResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_account_account_proto_msgTypes[110]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *VerifySliderStatusResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VerifySliderStatusResponse) ProtoMessage() {}

func (x *VerifySliderStatusResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_account_account_proto_msgTypes[110]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VerifySliderStatusResponse.ProtoReflect.Descriptor instead.
func (*VerifySliderStatusResponse) Descriptor() ([]byte, []int) {
	return file_api_account_account_proto_rawDescGZIP(), []int{110}
}

func (x *VerifySliderStatusResponse) GetNonceStr() string {
	if x != nil {
		return x.NonceStr
	}
	return ""
}

func (x *VerifySliderStatusResponse) GetStatus() int32 {
	if x != nil {
		return x.Status
	}
	return 0
}

type ValidateCodeReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code   string `protobuf:"bytes,1,opt,name=code,proto3" json:"code,omitempty"`
	Domain string `protobuf:"bytes,2,opt,name=domain,proto3" json:"domain,omitempty"`
	TelNum string `protobuf:"bytes,3,opt,name=telNum,proto3" json:"telNum,omitempty"`
}

func (x *ValidateCodeReq) Reset() {
	*x = ValidateCodeReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_account_account_proto_msgTypes[111]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ValidateCodeReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ValidateCodeReq) ProtoMessage() {}

func (x *ValidateCodeReq) ProtoReflect() protoreflect.Message {
	mi := &file_api_account_account_proto_msgTypes[111]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ValidateCodeReq.ProtoReflect.Descriptor instead.
func (*ValidateCodeReq) Descriptor() ([]byte, []int) {
	return file_api_account_account_proto_rawDescGZIP(), []int{111}
}

func (x *ValidateCodeReq) GetCode() string {
	if x != nil {
		return x.Code
	}
	return ""
}

func (x *ValidateCodeReq) GetDomain() string {
	if x != nil {
		return x.Domain
	}
	return ""
}

func (x *ValidateCodeReq) GetTelNum() string {
	if x != nil {
		return x.TelNum
	}
	return ""
}

type ValidateCodeResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Msg string `protobuf:"bytes,1,opt,name=msg,proto3" json:"msg,omitempty"`
}

func (x *ValidateCodeResp) Reset() {
	*x = ValidateCodeResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_account_account_proto_msgTypes[112]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ValidateCodeResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ValidateCodeResp) ProtoMessage() {}

func (x *ValidateCodeResp) ProtoReflect() protoreflect.Message {
	mi := &file_api_account_account_proto_msgTypes[112]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ValidateCodeResp.ProtoReflect.Descriptor instead.
func (*ValidateCodeResp) Descriptor() ([]byte, []int) {
	return file_api_account_account_proto_rawDescGZIP(), []int{112}
}

func (x *ValidateCodeResp) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

//  seller
type SellerCustomerRelation struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id                uint32 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	CustomerId        uint64 `protobuf:"varint,2,opt,name=customerId,proto3" json:"customerId,omitempty"`
	CustomerName      string `protobuf:"bytes,3,opt,name=customerName,proto3" json:"customerName,omitempty"`
	CustomerTel       string `protobuf:"bytes,12,opt,name=customerTel,proto3" json:"customerTel,omitempty"`
	CustomerSex       uint64 `protobuf:"varint,13,opt,name=customerSex,proto3" json:"customerSex,omitempty"`
	CustomerBirthDate string `protobuf:"bytes,14,opt,name=customerBirthDate,proto3" json:"customerBirthDate,omitempty"`
	SellerId          uint64 `protobuf:"varint,4,opt,name=sellerId,proto3" json:"sellerId,omitempty"`
	SellerName        string `protobuf:"bytes,5,opt,name=sellerName,proto3" json:"sellerName,omitempty"`
	SellerTel         string `protobuf:"bytes,15,opt,name=sellerTel,proto3" json:"sellerTel,omitempty"`
	SellerPosition    string `protobuf:"bytes,16,opt,name=sellerPosition,proto3" json:"sellerPosition,omitempty"`
	SellerSite        string `protobuf:"bytes,17,opt,name=sellerSite,proto3" json:"sellerSite,omitempty"`
	OperatorId        uint64 `protobuf:"varint,6,opt,name=operatorId,proto3" json:"operatorId,omitempty"`
	OperatorName      string `protobuf:"bytes,7,opt,name=operatorName,proto3" json:"operatorName,omitempty"`
	Remark            string `protobuf:"bytes,8,opt,name=remark,proto3" json:"remark,omitempty"`
	Status            uint32 `protobuf:"varint,9,opt,name=status,proto3" json:"status,omitempty"`
	BindTime          string `protobuf:"bytes,10,opt,name=bindTime,proto3" json:"bindTime,omitempty"`
	UnbindTime        string `protobuf:"bytes,11,opt,name=unbindTime,proto3" json:"unbindTime,omitempty"`
	UpdatedAt         string `protobuf:"bytes,18,opt,name=updatedAt,proto3" json:"updatedAt,omitempty"`
	LeftStatus        string `protobuf:"bytes,19,opt,name=leftStatus,proto3" json:"leftStatus,omitempty"`
}

func (x *SellerCustomerRelation) Reset() {
	*x = SellerCustomerRelation{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_account_account_proto_msgTypes[113]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SellerCustomerRelation) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SellerCustomerRelation) ProtoMessage() {}

func (x *SellerCustomerRelation) ProtoReflect() protoreflect.Message {
	mi := &file_api_account_account_proto_msgTypes[113]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SellerCustomerRelation.ProtoReflect.Descriptor instead.
func (*SellerCustomerRelation) Descriptor() ([]byte, []int) {
	return file_api_account_account_proto_rawDescGZIP(), []int{113}
}

func (x *SellerCustomerRelation) GetId() uint32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *SellerCustomerRelation) GetCustomerId() uint64 {
	if x != nil {
		return x.CustomerId
	}
	return 0
}

func (x *SellerCustomerRelation) GetCustomerName() string {
	if x != nil {
		return x.CustomerName
	}
	return ""
}

func (x *SellerCustomerRelation) GetCustomerTel() string {
	if x != nil {
		return x.CustomerTel
	}
	return ""
}

func (x *SellerCustomerRelation) GetCustomerSex() uint64 {
	if x != nil {
		return x.CustomerSex
	}
	return 0
}

func (x *SellerCustomerRelation) GetCustomerBirthDate() string {
	if x != nil {
		return x.CustomerBirthDate
	}
	return ""
}

func (x *SellerCustomerRelation) GetSellerId() uint64 {
	if x != nil {
		return x.SellerId
	}
	return 0
}

func (x *SellerCustomerRelation) GetSellerName() string {
	if x != nil {
		return x.SellerName
	}
	return ""
}

func (x *SellerCustomerRelation) GetSellerTel() string {
	if x != nil {
		return x.SellerTel
	}
	return ""
}

func (x *SellerCustomerRelation) GetSellerPosition() string {
	if x != nil {
		return x.SellerPosition
	}
	return ""
}

func (x *SellerCustomerRelation) GetSellerSite() string {
	if x != nil {
		return x.SellerSite
	}
	return ""
}

func (x *SellerCustomerRelation) GetOperatorId() uint64 {
	if x != nil {
		return x.OperatorId
	}
	return 0
}

func (x *SellerCustomerRelation) GetOperatorName() string {
	if x != nil {
		return x.OperatorName
	}
	return ""
}

func (x *SellerCustomerRelation) GetRemark() string {
	if x != nil {
		return x.Remark
	}
	return ""
}

func (x *SellerCustomerRelation) GetStatus() uint32 {
	if x != nil {
		return x.Status
	}
	return 0
}

func (x *SellerCustomerRelation) GetBindTime() string {
	if x != nil {
		return x.BindTime
	}
	return ""
}

func (x *SellerCustomerRelation) GetUnbindTime() string {
	if x != nil {
		return x.UnbindTime
	}
	return ""
}

func (x *SellerCustomerRelation) GetUpdatedAt() string {
	if x != nil {
		return x.UpdatedAt
	}
	return ""
}

func (x *SellerCustomerRelation) GetLeftStatus() string {
	if x != nil {
		return x.LeftStatus
	}
	return ""
}

type SellerCustomerRelationsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CustomerId   uint64 `protobuf:"varint,1,opt,name=customerId,proto3" json:"customerId,omitempty"`
	SellerId     uint64 `protobuf:"varint,2,opt,name=sellerId,proto3" json:"sellerId,omitempty"`
	OperatorId   uint64 `protobuf:"varint,3,opt,name=operatorId,proto3" json:"operatorId,omitempty"`
	OperatorName string `protobuf:"bytes,4,opt,name=operatorName,proto3" json:"operatorName,omitempty"`
	Remark       string `protobuf:"bytes,5,opt,name=remark,proto3" json:"remark,omitempty"`
}

func (x *SellerCustomerRelationsRequest) Reset() {
	*x = SellerCustomerRelationsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_account_account_proto_msgTypes[114]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SellerCustomerRelationsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SellerCustomerRelationsRequest) ProtoMessage() {}

func (x *SellerCustomerRelationsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_account_account_proto_msgTypes[114]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SellerCustomerRelationsRequest.ProtoReflect.Descriptor instead.
func (*SellerCustomerRelationsRequest) Descriptor() ([]byte, []int) {
	return file_api_account_account_proto_rawDescGZIP(), []int{114}
}

func (x *SellerCustomerRelationsRequest) GetCustomerId() uint64 {
	if x != nil {
		return x.CustomerId
	}
	return 0
}

func (x *SellerCustomerRelationsRequest) GetSellerId() uint64 {
	if x != nil {
		return x.SellerId
	}
	return 0
}

func (x *SellerCustomerRelationsRequest) GetOperatorId() uint64 {
	if x != nil {
		return x.OperatorId
	}
	return 0
}

func (x *SellerCustomerRelationsRequest) GetOperatorName() string {
	if x != nil {
		return x.OperatorName
	}
	return ""
}

func (x *SellerCustomerRelationsRequest) GetRemark() string {
	if x != nil {
		return x.Remark
	}
	return ""
}

type SellerCustomerRelationsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Msg string `protobuf:"bytes,1,opt,name=msg,proto3" json:"msg,omitempty"`
}

func (x *SellerCustomerRelationsResponse) Reset() {
	*x = SellerCustomerRelationsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_account_account_proto_msgTypes[115]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SellerCustomerRelationsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SellerCustomerRelationsResponse) ProtoMessage() {}

func (x *SellerCustomerRelationsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_account_account_proto_msgTypes[115]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SellerCustomerRelationsResponse.ProtoReflect.Descriptor instead.
func (*SellerCustomerRelationsResponse) Descriptor() ([]byte, []int) {
	return file_api_account_account_proto_rawDescGZIP(), []int{115}
}

func (x *SellerCustomerRelationsResponse) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

type SellerCustomerRelationsListRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CustomerId   uint64   `protobuf:"varint,1,opt,name=customerId,proto3" json:"customerId,omitempty"`
	CustomerName string   `protobuf:"bytes,6,opt,name=customerName,proto3" json:"customerName,omitempty"`
	SellerId     uint64   `protobuf:"varint,2,opt,name=sellerId,proto3" json:"sellerId,omitempty"`
	SellerName   string   `protobuf:"bytes,7,opt,name=sellerName,proto3" json:"sellerName,omitempty"`
	Status       uint32   `protobuf:"varint,3,opt,name=status,proto3" json:"status,omitempty"`
	Page         uint32   `protobuf:"varint,4,opt,name=page,proto3" json:"page,omitempty"`
	PageSize     uint32   `protobuf:"varint,5,opt,name=pageSize,proto3" json:"pageSize,omitempty"`
	LeftStatus   string   `protobuf:"bytes,8,opt,name=leftStatus,proto3" json:"leftStatus,omitempty"`
	SellerIds    []uint64 `protobuf:"varint,9,rep,packed,name=sellerIds,proto3" json:"sellerIds,omitempty"`
}

func (x *SellerCustomerRelationsListRequest) Reset() {
	*x = SellerCustomerRelationsListRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_account_account_proto_msgTypes[116]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SellerCustomerRelationsListRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SellerCustomerRelationsListRequest) ProtoMessage() {}

func (x *SellerCustomerRelationsListRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_account_account_proto_msgTypes[116]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SellerCustomerRelationsListRequest.ProtoReflect.Descriptor instead.
func (*SellerCustomerRelationsListRequest) Descriptor() ([]byte, []int) {
	return file_api_account_account_proto_rawDescGZIP(), []int{116}
}

func (x *SellerCustomerRelationsListRequest) GetCustomerId() uint64 {
	if x != nil {
		return x.CustomerId
	}
	return 0
}

func (x *SellerCustomerRelationsListRequest) GetCustomerName() string {
	if x != nil {
		return x.CustomerName
	}
	return ""
}

func (x *SellerCustomerRelationsListRequest) GetSellerId() uint64 {
	if x != nil {
		return x.SellerId
	}
	return 0
}

func (x *SellerCustomerRelationsListRequest) GetSellerName() string {
	if x != nil {
		return x.SellerName
	}
	return ""
}

func (x *SellerCustomerRelationsListRequest) GetStatus() uint32 {
	if x != nil {
		return x.Status
	}
	return 0
}

func (x *SellerCustomerRelationsListRequest) GetPage() uint32 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *SellerCustomerRelationsListRequest) GetPageSize() uint32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *SellerCustomerRelationsListRequest) GetLeftStatus() string {
	if x != nil {
		return x.LeftStatus
	}
	return ""
}

func (x *SellerCustomerRelationsListRequest) GetSellerIds() []uint64 {
	if x != nil {
		return x.SellerIds
	}
	return nil
}

type SellerCustomerRelationsListResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Data  []*SellerCustomerRelation `protobuf:"bytes,1,rep,name=data,proto3" json:"data,omitempty"`
	Count uint32                    `protobuf:"varint,2,opt,name=count,proto3" json:"count,omitempty"`
}

func (x *SellerCustomerRelationsListResponse) Reset() {
	*x = SellerCustomerRelationsListResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_account_account_proto_msgTypes[117]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SellerCustomerRelationsListResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SellerCustomerRelationsListResponse) ProtoMessage() {}

func (x *SellerCustomerRelationsListResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_account_account_proto_msgTypes[117]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SellerCustomerRelationsListResponse.ProtoReflect.Descriptor instead.
func (*SellerCustomerRelationsListResponse) Descriptor() ([]byte, []int) {
	return file_api_account_account_proto_rawDescGZIP(), []int{117}
}

func (x *SellerCustomerRelationsListResponse) GetData() []*SellerCustomerRelation {
	if x != nil {
		return x.Data
	}
	return nil
}

func (x *SellerCustomerRelationsListResponse) GetCount() uint32 {
	if x != nil {
		return x.Count
	}
	return 0
}

type SellerCustomerCountRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	SellerId []uint64 `protobuf:"varint,1,rep,packed,name=sellerId,proto3" json:"sellerId,omitempty"`
}

func (x *SellerCustomerCountRequest) Reset() {
	*x = SellerCustomerCountRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_account_account_proto_msgTypes[118]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SellerCustomerCountRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SellerCustomerCountRequest) ProtoMessage() {}

func (x *SellerCustomerCountRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_account_account_proto_msgTypes[118]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SellerCustomerCountRequest.ProtoReflect.Descriptor instead.
func (*SellerCustomerCountRequest) Descriptor() ([]byte, []int) {
	return file_api_account_account_proto_rawDescGZIP(), []int{118}
}

func (x *SellerCustomerCountRequest) GetSellerId() []uint64 {
	if x != nil {
		return x.SellerId
	}
	return nil
}

type SellerCustomerCountResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Customers []*SellerCustomerCountResponse_CustomerCount `protobuf:"bytes,1,rep,name=customers,proto3" json:"customers,omitempty"`
}

func (x *SellerCustomerCountResponse) Reset() {
	*x = SellerCustomerCountResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_account_account_proto_msgTypes[119]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SellerCustomerCountResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SellerCustomerCountResponse) ProtoMessage() {}

func (x *SellerCustomerCountResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_account_account_proto_msgTypes[119]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SellerCustomerCountResponse.ProtoReflect.Descriptor instead.
func (*SellerCustomerCountResponse) Descriptor() ([]byte, []int) {
	return file_api_account_account_proto_rawDescGZIP(), []int{119}
}

func (x *SellerCustomerCountResponse) GetCustomers() []*SellerCustomerCountResponse_CustomerCount {
	if x != nil {
		return x.Customers
	}
	return nil
}

type SellerCustomerCountResponse_CustomerCount struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	SellerId uint64 `protobuf:"varint,1,opt,name=SellerId,proto3" json:"SellerId,omitempty"`
	Count    uint64 `protobuf:"varint,2,opt,name=Count,proto3" json:"Count,omitempty"`
}

func (x *SellerCustomerCountResponse_CustomerCount) Reset() {
	*x = SellerCustomerCountResponse_CustomerCount{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_account_account_proto_msgTypes[120]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SellerCustomerCountResponse_CustomerCount) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SellerCustomerCountResponse_CustomerCount) ProtoMessage() {}

func (x *SellerCustomerCountResponse_CustomerCount) ProtoReflect() protoreflect.Message {
	mi := &file_api_account_account_proto_msgTypes[120]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SellerCustomerCountResponse_CustomerCount.ProtoReflect.Descriptor instead.
func (*SellerCustomerCountResponse_CustomerCount) Descriptor() ([]byte, []int) {
	return file_api_account_account_proto_rawDescGZIP(), []int{119, 0}
}

func (x *SellerCustomerCountResponse_CustomerCount) GetSellerId() uint64 {
	if x != nil {
		return x.SellerId
	}
	return 0
}

func (x *SellerCustomerCountResponse_CustomerCount) GetCount() uint64 {
	if x != nil {
		return x.Count
	}
	return 0
}

var File_api_account_account_proto protoreflect.FileDescriptor

var file_api_account_account_proto_rawDesc = []byte{
	0x0a, 0x19, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2f, 0x61, 0x63,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x07, 0x61, 0x63, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x1a, 0x3d, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d,
	0x2f, 0x6d, 0x77, 0x69, 0x74, 0x6b, 0x6f, 0x77, 0x2f, 0x67, 0x6f, 0x2d, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x2d, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x6f, 0x72, 0x73, 0x40, 0x76, 0x30, 0x2e,
	0x33, 0x2e, 0x32, 0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x6f, 0x72, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x22, 0xc5, 0x02, 0x0a, 0x09, 0x53, 0x6d, 0x73, 0x4c, 0x6f, 0x67, 0x52, 0x65,
	0x71, 0x12, 0x14, 0x0a, 0x05, 0x6d, 0x73, 0x67, 0x4e, 0x6f, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x05, 0x6d, 0x73, 0x67, 0x4e, 0x6f, 0x12, 0x16, 0x0a, 0x06, 0x6d, 0x6f, 0x62, 0x69, 0x6c,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x6d, 0x6f, 0x62, 0x69, 0x6c, 0x65, 0x12,
	0x1e, 0x0a, 0x0a, 0x61, 0x6c, 0x6c, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0a, 0x61, 0x6c, 0x6c, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x12,
	0x1e, 0x0a, 0x0a, 0x62, 0x61, 0x73, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x0d, 0x52, 0x0a, 0x62, 0x61, 0x73, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12,
	0x24, 0x0a, 0x0d, 0x73, 0x74, 0x61, 0x72, 0x74, 0x53, 0x65, 0x6e, 0x64, 0x54, 0x69, 0x6d, 0x65,
	0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x73, 0x74, 0x61, 0x72, 0x74, 0x53, 0x65, 0x6e,
	0x64, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x65, 0x6e, 0x64, 0x53, 0x65, 0x6e, 0x64,
	0x54, 0x69, 0x6d, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x65, 0x6e, 0x64, 0x53,
	0x65, 0x6e, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x2a, 0x0a, 0x10, 0x73, 0x74, 0x61, 0x72, 0x74,
	0x52, 0x65, 0x63, 0x65, 0x69, 0x76, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x10, 0x73, 0x74, 0x61, 0x72, 0x74, 0x52, 0x65, 0x63, 0x65, 0x69, 0x76, 0x65, 0x54,
	0x69, 0x6d, 0x65, 0x12, 0x26, 0x0a, 0x0e, 0x65, 0x6e, 0x64, 0x52, 0x65, 0x63, 0x65, 0x69, 0x76,
	0x65, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x65, 0x6e, 0x64,
	0x52, 0x65, 0x63, 0x65, 0x69, 0x76, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x70,
	0x61, 0x67, 0x65, 0x53, 0x69, 0x7a, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x04, 0x52, 0x08, 0x70,
	0x61, 0x67, 0x65, 0x53, 0x69, 0x7a, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x70, 0x61, 0x67, 0x65, 0x18,
	0x0a, 0x20, 0x01, 0x28, 0x04, 0x52, 0x04, 0x70, 0x61, 0x67, 0x65, 0x22, 0x4a, 0x0a, 0x09, 0x53,
	0x6d, 0x73, 0x4c, 0x6f, 0x67, 0x52, 0x65, 0x73, 0x12, 0x27, 0x0a, 0x04, 0x6c, 0x69, 0x73, 0x74,
	0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x2e, 0x53, 0x6d, 0x73, 0x4c, 0x6f, 0x67, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x04, 0x6c, 0x69, 0x73,
	0x74, 0x12, 0x14, 0x0a, 0x05, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d,
	0x52, 0x05, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x22, 0x94, 0x02, 0x0a, 0x0a, 0x53, 0x6d, 0x73, 0x4c,
	0x6f, 0x67, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x14, 0x0a, 0x05, 0x6d, 0x73, 0x67, 0x4e, 0x6f, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x6d, 0x73, 0x67, 0x4e, 0x6f, 0x12, 0x16, 0x0a, 0x06,
	0x6d, 0x6f, 0x62, 0x69, 0x6c, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x6d, 0x6f,
	0x62, 0x69, 0x6c, 0x65, 0x12, 0x1e, 0x0a, 0x0a, 0x61, 0x6c, 0x6c, 0x43, 0x6f, 0x6e, 0x74, 0x65,
	0x6e, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x61, 0x6c, 0x6c, 0x43, 0x6f, 0x6e,
	0x74, 0x65, 0x6e, 0x74, 0x12, 0x1e, 0x0a, 0x0a, 0x62, 0x61, 0x73, 0x65, 0x53, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0a, 0x62, 0x61, 0x73, 0x65, 0x53, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x12, 0x1a, 0x0a, 0x08, 0x73, 0x65, 0x6e, 0x64, 0x54, 0x69, 0x6d, 0x65,
	0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x73, 0x65, 0x6e, 0x64, 0x54, 0x69, 0x6d, 0x65,
	0x12, 0x20, 0x0a, 0x0b, 0x72, 0x65, 0x63, 0x65, 0x69, 0x76, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x18,
	0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x72, 0x65, 0x63, 0x65, 0x69, 0x76, 0x65, 0x54, 0x69,
	0x6d, 0x65, 0x12, 0x1e, 0x0a, 0x0a, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x44, 0x65, 0x73, 0x63,
	0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x44, 0x65,
	0x73, 0x63, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x06, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64,
	0x18, 0x09, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x02, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x70, 0x6c,
	0x61, 0x74, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x70, 0x6c, 0x61, 0x74, 0x22, 0x44,
	0x0a, 0x10, 0x42, 0x69, 0x6e, 0x64, 0x53, 0x74, 0x61, 0x66, 0x66, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x12, 0x16, 0x0a, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0d, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x12, 0x18, 0x0a, 0x07, 0x73, 0x74,
	0x61, 0x66, 0x66, 0x49, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x07, 0x73, 0x74, 0x61,
	0x66, 0x66, 0x49, 0x64, 0x22, 0x63, 0x0a, 0x19, 0x52, 0x65, 0x76, 0x69, 0x65, 0x77, 0x52, 0x65,
	0x61, 0x6c, 0x4e, 0x61, 0x6d, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x12, 0x16, 0x0a, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0d, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x70, 0x61, 0x67,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x04, 0x70, 0x61, 0x67, 0x65, 0x12, 0x1a, 0x0a,
	0x08, 0x70, 0x61, 0x67, 0x65, 0x53, 0x69, 0x7a, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0d, 0x52,
	0x08, 0x70, 0x61, 0x67, 0x65, 0x53, 0x69, 0x7a, 0x65, 0x22, 0x66, 0x0a, 0x1a, 0x52, 0x65, 0x76,
	0x69, 0x65, 0x77, 0x52, 0x65, 0x61, 0x6c, 0x4e, 0x61, 0x6d, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x32, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18,
	0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e,
	0x52, 0x65, 0x76, 0x69, 0x65, 0x77, 0x52, 0x65, 0x61, 0x6c, 0x4e, 0x61, 0x6d, 0x65, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x12, 0x14, 0x0a, 0x05, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x22, 0xca, 0x01, 0x0a, 0x12, 0x52, 0x65, 0x76, 0x69, 0x65, 0x77, 0x52, 0x65, 0x61, 0x6c,
	0x4e, 0x61, 0x6d, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x04, 0x52, 0x02, 0x69, 0x64, 0x12, 0x1a, 0x0a, 0x08, 0x63, 0x72, 0x65, 0x61,
	0x74, 0x65, 0x41, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x63, 0x72, 0x65, 0x61,
	0x74, 0x65, 0x41, 0x74, 0x12, 0x1a, 0x0a, 0x08, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x41, 0x74,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x41, 0x74,
	0x12, 0x18, 0x0a, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x1e, 0x0a, 0x0a, 0x70, 0x61,
	0x73, 0x73, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0a,
	0x70, 0x61, 0x73, 0x73, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x16, 0x0a, 0x06, 0x75, 0x73,
	0x65, 0x72, 0x49, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72,
	0x49, 0x64, 0x12, 0x1a, 0x0a, 0x08, 0x75, 0x73, 0x65, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x07,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x75, 0x73, 0x65, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x22, 0xcd,
	0x01, 0x0a, 0x15, 0x52, 0x65, 0x76, 0x69, 0x65, 0x77, 0x52, 0x65, 0x61, 0x6c, 0x4e, 0x61, 0x6d,
	0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x10, 0x0a, 0x03, 0x69, 0x64, 0x73, 0x18,
	0x01, 0x20, 0x03, 0x28, 0x0d, 0x52, 0x03, 0x69, 0x64, 0x73, 0x12, 0x18, 0x0a, 0x07, 0x6d, 0x65,
	0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6d, 0x65, 0x73,
	0x73, 0x61, 0x67, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x69, 0x73, 0x50, 0x61, 0x73, 0x73, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x08, 0x52, 0x06, 0x69, 0x73, 0x50, 0x61, 0x73, 0x73, 0x12, 0x16, 0x0a, 0x06,
	0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x06, 0x75, 0x73,
	0x65, 0x72, 0x49, 0x64, 0x12, 0x1a, 0x0a, 0x08, 0x75, 0x73, 0x65, 0x72, 0x4e, 0x61, 0x6d, 0x65,
	0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x75, 0x73, 0x65, 0x72, 0x4e, 0x61, 0x6d, 0x65,
	0x12, 0x1c, 0x0a, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x18, 0x06, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x1e,
	0x0a, 0x0a, 0x70, 0x61, 0x73, 0x73, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x07, 0x20, 0x01,
	0x28, 0x0d, 0x52, 0x0a, 0x70, 0x61, 0x73, 0x73, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x22, 0x93,
	0x01, 0x0a, 0x11, 0x53, 0x75, 0x62, 0x6d, 0x69, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x12, 0x1c, 0x0a, 0x09, 0x66, 0x69, 0x72, 0x73, 0x74, 0x4e, 0x61, 0x6d,
	0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x66, 0x69, 0x72, 0x73, 0x74, 0x4e, 0x61,
	0x6d, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x6c, 0x61, 0x73, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x6c, 0x61, 0x73, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x14,
	0x0a, 0x05, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x65,
	0x6d, 0x61, 0x69, 0x6c, 0x12, 0x18, 0x0a, 0x07, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x12, 0x14,
	0x0a, 0x05, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x70,
	0x68, 0x6f, 0x6e, 0x65, 0x22, 0x64, 0x0a, 0x1a, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x42, 0x65, 0x66,
	0x6f, 0x72, 0x65, 0x52, 0x65, 0x67, 0x69, 0x73, 0x74, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x12, 0x16, 0x0a, 0x06, 0x64, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x06, 0x64, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x12, 0x16, 0x0a, 0x06, 0x6a, 0x6f,
	0x62, 0x4e, 0x75, 0x6d, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x6a, 0x6f, 0x62, 0x4e,
	0x75, 0x6d, 0x12, 0x16, 0x0a, 0x06, 0x74, 0x65, 0x6c, 0x4e, 0x75, 0x6d, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x06, 0x74, 0x65, 0x6c, 0x4e, 0x75, 0x6d, 0x22, 0x9e, 0x01, 0x0a, 0x14, 0x53,
	0x61, 0x6d, 0x70, 0x6c, 0x65, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x12, 0x16, 0x0a, 0x06, 0x64, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x06, 0x64, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x12, 0x1a, 0x0a, 0x08, 0x70,
	0x61, 0x73, 0x73, 0x77, 0x6f, 0x72, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x70,
	0x61, 0x73, 0x73, 0x77, 0x6f, 0x72, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x66,
	0x72, 0x6f, 0x6d, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x66, 0x72, 0x6f, 0x6d, 0x12,
	0x16, 0x0a, 0x06, 0x74, 0x65, 0x6c, 0x4e, 0x75, 0x6d, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x06, 0x74, 0x65, 0x6c, 0x4e, 0x75, 0x6d, 0x12, 0x12, 0x0a, 0x04, 0x7a, 0x6f, 0x6e, 0x65, 0x18,
	0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x7a, 0x6f, 0x6e, 0x65, 0x22, 0x57, 0x0a, 0x15, 0x53,
	0x61, 0x6d, 0x70, 0x6c, 0x65, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x12, 0x2c, 0x0a, 0x11, 0x69, 0x73, 0x4e, 0x6f, 0x77, 0x41, 0x6c, 0x72,
	0x65, 0x61, 0x64, 0x79, 0x4c, 0x6f, 0x67, 0x69, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52,
	0x11, 0x69, 0x73, 0x4e, 0x6f, 0x77, 0x41, 0x6c, 0x72, 0x65, 0x61, 0x64, 0x79, 0x4c, 0x6f, 0x67,
	0x69, 0x6e, 0x12, 0x10, 0x0a, 0x03, 0x6e, 0x75, 0x6d, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52,
	0x03, 0x6e, 0x75, 0x6d, 0x22, 0x76, 0x0a, 0x1c, 0x4c, 0x6f, 0x67, 0x69, 0x6e, 0x41, 0x6e, 0x64,
	0x53, 0x71, 0x75, 0x65, 0x65, 0x7a, 0x65, 0x4f, 0x74, 0x68, 0x65, 0x72, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x12, 0x2e, 0x0a, 0x12, 0x6e, 0x65, 0x65, 0x64, 0x44, 0x65, 0x74, 0x65,
	0x63, 0x74, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x55, 0x72, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x12, 0x6e, 0x65, 0x65, 0x64, 0x44, 0x65, 0x74, 0x65, 0x63, 0x74, 0x49, 0x6d, 0x61, 0x67,
	0x65, 0x55, 0x72, 0x6c, 0x12, 0x26, 0x0a, 0x0e, 0x72, 0x65, 0x63, 0x65, 0x6e, 0x74, 0x49, 0x6d,
	0x61, 0x67, 0x65, 0x55, 0x72, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x72, 0x65,
	0x63, 0x65, 0x6e, 0x74, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x55, 0x72, 0x6c, 0x22, 0x5c, 0x0a, 0x14,
	0x49, 0x73, 0x53, 0x61, 0x6d, 0x65, 0x50, 0x65, 0x72, 0x73, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x69, 0x73, 0x50, 0x61, 0x73, 0x73, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x08, 0x52, 0x06, 0x69, 0x73, 0x50, 0x61, 0x73, 0x73, 0x12, 0x12, 0x0a, 0x04,
	0x72, 0x61, 0x74, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x04, 0x72, 0x61, 0x74, 0x65,
	0x12, 0x18, 0x0a, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x22, 0x6d, 0x0a, 0x13, 0x49, 0x73,
	0x53, 0x61, 0x6d, 0x65, 0x50, 0x65, 0x72, 0x73, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x12, 0x2e, 0x0a, 0x12, 0x6e, 0x65, 0x65, 0x64, 0x44, 0x65, 0x74, 0x65, 0x63, 0x74, 0x49,
	0x6d, 0x61, 0x67, 0x65, 0x55, 0x72, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x12, 0x6e,
	0x65, 0x65, 0x64, 0x44, 0x65, 0x74, 0x65, 0x63, 0x74, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x55, 0x72,
	0x6c, 0x12, 0x26, 0x0a, 0x0e, 0x72, 0x65, 0x63, 0x65, 0x6e, 0x74, 0x49, 0x6d, 0x61, 0x67, 0x65,
	0x55, 0x72, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x72, 0x65, 0x63, 0x65, 0x6e,
	0x74, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x55, 0x72, 0x6c, 0x22, 0x45, 0x0a, 0x1b, 0x55, 0x70, 0x64,
	0x61, 0x74, 0x65, 0x50, 0x61, 0x73, 0x73, 0x70, 0x6f, 0x72, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x0e, 0x0a, 0x02, 0x49, 0x44, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0d, 0x52, 0x02, 0x49, 0x44, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x22, 0x66, 0x0a, 0x1a, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x43, 0x68, 0x61, 0x69, 0x6e, 0x41,
	0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x18,
	0x0a, 0x07, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x07, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x10, 0x0a, 0x03, 0x6d, 0x65, 0x6e, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6d, 0x65, 0x6e, 0x12, 0x1c, 0x0a, 0x09, 0x70, 0x75,
	0x62, 0x6c, 0x69, 0x63, 0x4b, 0x65, 0x79, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x70,
	0x75, 0x62, 0x6c, 0x69, 0x63, 0x4b, 0x65, 0x79, 0x22, 0x46, 0x0a, 0x14, 0x55, 0x73, 0x65, 0x72,
	0x73, 0x42, 0x79, 0x4a, 0x6f, 0x62, 0x4e, 0x75, 0x6d, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x12, 0x16, 0x0a, 0x06, 0x64, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x06, 0x64, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x12, 0x16, 0x0a, 0x06, 0x6a, 0x6f, 0x62, 0x4e,
	0x75, 0x6d, 0x18, 0x02, 0x20, 0x03, 0x28, 0x09, 0x52, 0x06, 0x6a, 0x6f, 0x62, 0x4e, 0x75, 0x6d,
	0x22, 0x6c, 0x0a, 0x24, 0x51, 0x75, 0x65, 0x72, 0x79, 0x50, 0x65, 0x72, 0x73, 0x6f, 0x6e, 0x6e,
	0x65, 0x6c, 0x57, 0x69, 0x74, 0x68, 0x54, 0x68, 0x65, 0x53, 0x61, 0x6d, 0x65, 0x4e, 0x61, 0x6d,
	0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x14, 0x0a, 0x05, 0x6e, 0x61, 0x6d, 0x65,
	0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x09, 0x52, 0x05, 0x6e, 0x61, 0x6d, 0x65, 0x73, 0x12, 0x16,
	0x0a, 0x06, 0x64, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06,
	0x64, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x22, 0x53,
	0x0a, 0x25, 0x51, 0x75, 0x65, 0x72, 0x79, 0x50, 0x65, 0x72, 0x73, 0x6f, 0x6e, 0x6e, 0x65, 0x6c,
	0x57, 0x69, 0x74, 0x68, 0x54, 0x68, 0x65, 0x53, 0x61, 0x6d, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x6e, 0x61, 0x6d, 0x65, 0x73,
	0x18, 0x01, 0x20, 0x03, 0x28, 0x09, 0x52, 0x05, 0x6e, 0x61, 0x6d, 0x65, 0x73, 0x12, 0x14, 0x0a,
	0x05, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x04, 0x52, 0x05, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x22, 0x9e, 0x05, 0x0a, 0x0d, 0x4c, 0x69, 0x73, 0x74, 0x56, 0x32, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x25, 0x0a, 0x06, 0x64, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0d, 0xe2, 0xdf, 0x1f, 0x09, 0x2a, 0x05, 0x37, 0x30, 0x30,
	0x30, 0x31, 0x58, 0x01, 0x52, 0x06, 0x64, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x12, 0x1a, 0x0a, 0x08,
	0x70, 0x61, 0x67, 0x65, 0x53, 0x69, 0x7a, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x04, 0x52, 0x08,
	0x70, 0x61, 0x67, 0x65, 0x53, 0x69, 0x7a, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x70, 0x61, 0x67, 0x65,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x04, 0x52, 0x04, 0x70, 0x61, 0x67, 0x65, 0x12, 0x10, 0x0a, 0x03,
	0x6b, 0x65, 0x79, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x1a,
	0x0a, 0x08, 0x6e, 0x69, 0x63, 0x6b, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x08, 0x6e, 0x69, 0x63, 0x6b, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x74, 0x65,
	0x6c, 0x4e, 0x75, 0x6d, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x74, 0x65, 0x6c, 0x4e,
	0x75, 0x6d, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x07, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x22, 0x0a, 0x0c, 0x70, 0x6f,
	0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0c, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x16,
	0x0a, 0x06, 0x6a, 0x6f, 0x62, 0x4e, 0x75, 0x6d, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06,
	0x6a, 0x6f, 0x62, 0x4e, 0x75, 0x6d, 0x12, 0x20, 0x0a, 0x0b, 0x6d, 0x61, 0x69, 0x6c, 0x41, 0x63,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x6d, 0x61, 0x69,
	0x6c, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x26, 0x0a, 0x0e, 0x73, 0x74, 0x61, 0x72,
	0x74, 0x45, 0x6e, 0x74, 0x65, 0x72, 0x44, 0x61, 0x74, 0x65, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0e, 0x73, 0x74, 0x61, 0x72, 0x74, 0x45, 0x6e, 0x74, 0x65, 0x72, 0x44, 0x61, 0x74, 0x65,
	0x12, 0x22, 0x0a, 0x0c, 0x65, 0x6e, 0x64, 0x45, 0x6e, 0x74, 0x65, 0x72, 0x44, 0x61, 0x74, 0x65,
	0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x65, 0x6e, 0x64, 0x45, 0x6e, 0x74, 0x65, 0x72,
	0x44, 0x61, 0x74, 0x65, 0x12, 0x1e, 0x0a, 0x0a, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e,
	0x49, 0x64, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0a, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x69,
	0x6f, 0x6e, 0x49, 0x64, 0x12, 0x22, 0x0a, 0x0c, 0x64, 0x65, 0x70, 0x61, 0x72, 0x74, 0x6d, 0x65,
	0x6e, 0x74, 0x49, 0x64, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0c, 0x64, 0x65, 0x70, 0x61,
	0x72, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x26, 0x0a, 0x0e, 0x64, 0x65, 0x70, 0x61,
	0x72, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0e, 0x64, 0x65, 0x70, 0x61, 0x72, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x4e, 0x61, 0x6d, 0x65,
	0x12, 0x28, 0x0a, 0x0f, 0x64, 0x65, 0x70, 0x61, 0x72, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x4e, 0x61,
	0x6d, 0x65, 0x73, 0x18, 0x10, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0f, 0x64, 0x65, 0x70, 0x61, 0x72,
	0x74, 0x6d, 0x65, 0x6e, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x73, 0x12, 0x20, 0x0a, 0x0b, 0x70, 0x6f,
	0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x73, 0x18, 0x11, 0x20, 0x03, 0x28, 0x0d, 0x52,
	0x0b, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x73, 0x12, 0x24, 0x0a, 0x0d,
	0x64, 0x65, 0x70, 0x61, 0x72, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x73, 0x18, 0x12, 0x20,
	0x03, 0x28, 0x0d, 0x52, 0x0d, 0x64, 0x65, 0x70, 0x61, 0x72, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x49,
	0x64, 0x73, 0x12, 0x2e, 0x0a, 0x12, 0x66, 0x61, 0x74, 0x68, 0x65, 0x72, 0x44, 0x65, 0x70, 0x61,
	0x72, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x18, 0x13, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x12,
	0x66, 0x61, 0x74, 0x68, 0x65, 0x72, 0x44, 0x65, 0x70, 0x61, 0x72, 0x74, 0x6d, 0x65, 0x6e, 0x74,
	0x49, 0x64, 0x12, 0x20, 0x0a, 0x0b, 0x62, 0x6c, 0x75, 0x72, 0x54, 0x65, 0x6c, 0x4e, 0x61, 0x6d,
	0x65, 0x18, 0x14, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x62, 0x6c, 0x75, 0x72, 0x54, 0x65, 0x6c,
	0x4e, 0x61, 0x6d, 0x65, 0x22, 0xe4, 0x01, 0x0a, 0x18, 0x53, 0x65, 0x6e, 0x64, 0x43, 0x6c, 0x6f,
	0x63, 0x6b, 0x49, 0x6e, 0x57, 0x65, 0x63, 0x68, 0x61, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x12, 0x16, 0x0a, 0x06, 0x64, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x06, 0x64, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x12, 0x16, 0x0a, 0x06, 0x74, 0x65, 0x6c,
	0x4e, 0x75, 0x6d, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x74, 0x65, 0x6c, 0x4e, 0x75,
	0x6d, 0x12, 0x1e, 0x0a, 0x0a, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x65, 0x64, 0x41,
	0x74, 0x12, 0x1c, 0x0a, 0x09, 0x63, 0x6c, 0x6f, 0x63, 0x6b, 0x54, 0x79, 0x70, 0x65, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x63, 0x6c, 0x6f, 0x63, 0x6b, 0x54, 0x79, 0x70, 0x65, 0x12,
	0x16, 0x0a, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0d, 0x52,
	0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x67, 0x68, 0x49, 0x64, 0x18,
	0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x67, 0x68, 0x49, 0x64, 0x12, 0x18, 0x0a, 0x07, 0x61,
	0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x64,
	0x64, 0x72, 0x65, 0x73, 0x73, 0x12, 0x14, 0x0a, 0x05, 0x6c, 0x6f, 0x67, 0x49, 0x64, 0x18, 0x08,
	0x20, 0x01, 0x28, 0x04, 0x52, 0x05, 0x6c, 0x6f, 0x67, 0x49, 0x64, 0x22, 0x82, 0x01, 0x0a, 0x1c,
	0x4d, 0x61, 0x69, 0x6c, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x42, 0x79, 0x4e, 0x69, 0x63,
	0x6b, 0x4e, 0x61, 0x6d, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x25, 0x0a, 0x06,
	0x44, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0d, 0xe2, 0xdf,
	0x1f, 0x09, 0x2a, 0x05, 0x37, 0x30, 0x30, 0x30, 0x31, 0x58, 0x01, 0x52, 0x06, 0x64, 0x6f, 0x6d,
	0x61, 0x69, 0x6e, 0x12, 0x2b, 0x0a, 0x08, 0x4e, 0x69, 0x63, 0x6b, 0x4e, 0x61, 0x6d, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0f, 0xe2, 0xdf, 0x1f, 0x0b, 0x2a, 0x05, 0x37, 0x30, 0x30,
	0x30, 0x35, 0x58, 0x01, 0x78, 0x14, 0x52, 0x08, 0x6e, 0x69, 0x63, 0x6b, 0x4e, 0x61, 0x6d, 0x65,
	0x12, 0x0e, 0x0a, 0x02, 0x49, 0x44, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x02, 0x49, 0x44,
	0x22, 0x5d, 0x0a, 0x17, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x4d, 0x61, 0x69, 0x41, 0x63, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x0e, 0x0a, 0x02, 0x49,
	0x44, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x02, 0x49, 0x44, 0x12, 0x1a, 0x0a, 0x08, 0x6e,
	0x69, 0x63, 0x6b, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x6e,
	0x69, 0x63, 0x6b, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x64, 0x6f, 0x6d, 0x61, 0x69,
	0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x64, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x22,
	0x58, 0x0a, 0x12, 0x4d, 0x61, 0x69, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x65, 0x6e, 0x67, 0x6c, 0x69, 0x73, 0x68,
	0x4e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x65, 0x6e, 0x67, 0x6c,
	0x69, 0x73, 0x68, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x6d, 0x61, 0x69, 0x6c, 0x41,
	0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x6d, 0x61,
	0x69, 0x6c, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x22, 0x32, 0x0a, 0x14, 0x46, 0x64, 0x64,
	0x52, 0x65, 0x6d, 0x6f, 0x76, 0x65, 0x55, 0x73, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x12, 0x1a, 0x0a, 0x08, 0x77, 0x78, 0x55, 0x73, 0x65, 0x72, 0x49, 0x64, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x0d, 0x52, 0x08, 0x77, 0x78, 0x55, 0x73, 0x65, 0x72, 0x49, 0x64, 0x22, 0xf4, 0x01,
	0x0a, 0x14, 0x46, 0x64, 0x64, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x55, 0x73, 0x65, 0x72, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x16, 0x0a, 0x06, 0x6f, 0x70, 0x65, 0x6e, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x6f, 0x70, 0x65, 0x6e, 0x69, 0x64, 0x12, 0x1a,
	0x0a, 0x08, 0x77, 0x78, 0x55, 0x73, 0x65, 0x72, 0x49, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d,
	0x52, 0x08, 0x77, 0x78, 0x55, 0x73, 0x65, 0x72, 0x49, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x55, 0x73,
	0x65, 0x72, 0x49, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x04, 0x52, 0x06, 0x55, 0x73, 0x65, 0x72,
	0x49, 0x64, 0x12, 0x1e, 0x0a, 0x0a, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x49, 0x64,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72,
	0x49, 0x64, 0x12, 0x1a, 0x0a, 0x08, 0x69, 0x73, 0x56, 0x65, 0x72, 0x69, 0x66, 0x79, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x08, 0x52, 0x08, 0x69, 0x73, 0x56, 0x65, 0x72, 0x69, 0x66, 0x79, 0x12, 0x24,
	0x0a, 0x0d, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x4e, 0x6f, 0x18,
	0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69,
	0x6f, 0x6e, 0x4e, 0x6f, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x07,
	0x20, 0x01, 0x28, 0x0d, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x16, 0x0a, 0x06,
	0x69, 0x64, 0x54, 0x79, 0x70, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x69, 0x64,
	0x54, 0x79, 0x70, 0x65, 0x22, 0x42, 0x0a, 0x14, 0x57, 0x78, 0x42, 0x6f, 0x78, 0x55, 0x73, 0x65,
	0x72, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x16, 0x0a, 0x06,
	0x6f, 0x70, 0x65, 0x6e, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x6f, 0x70,
	0x65, 0x6e, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x67, 0x68, 0x49, 0x64, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x04, 0x67, 0x68, 0x49, 0x64, 0x22, 0x58, 0x0a, 0x18, 0x57, 0x78, 0x47, 0x65,
	0x74, 0x4f, 0x70, 0x65, 0x6e, 0x49, 0x64, 0x42, 0x79, 0x43, 0x6f, 0x64, 0x65, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x73, 0x74, 0x61, 0x74,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x12, 0x12,
	0x0a, 0x04, 0x67, 0x68, 0x49, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x67, 0x68,
	0x49, 0x64, 0x22, 0x33, 0x0a, 0x19, 0x57, 0x78, 0x47, 0x65, 0x74, 0x4f, 0x70, 0x65, 0x6e, 0x49,
	0x64, 0x42, 0x79, 0x43, 0x6f, 0x64, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12,
	0x16, 0x0a, 0x06, 0x6f, 0x70, 0x65, 0x6e, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x06, 0x6f, 0x70, 0x65, 0x6e, 0x49, 0x64, 0x22, 0x33, 0x0a, 0x19, 0x57, 0x78, 0x42, 0x6f, 0x78,
	0x54, 0x65, 0x6c, 0x4e, 0x75, 0x6d, 0x42, 0x79, 0x43, 0x6f, 0x64, 0x65, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x74, 0x65, 0x6c, 0x4e, 0x75, 0x6d, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x74, 0x65, 0x6c, 0x4e, 0x75, 0x6d, 0x22, 0xb8, 0x01, 0x0a,
	0x0d, 0x57, 0x78, 0x42, 0x6f, 0x78, 0x55, 0x73, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x16,
	0x0a, 0x06, 0x6f, 0x70, 0x65, 0x6e, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06,
	0x6f, 0x70, 0x65, 0x6e, 0x49, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x67, 0x68, 0x49, 0x64, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x67, 0x68, 0x49, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x69, 0x73,
	0x4e, 0x65, 0x77, 0x18, 0x03, 0x20, 0x01, 0x28, 0x08, 0x52, 0x05, 0x69, 0x73, 0x4e, 0x65, 0x77,
	0x12, 0x25, 0x0a, 0x04, 0x75, 0x73, 0x65, 0x72, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11,
	0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x49, 0x6e, 0x66,
	0x6f, 0x52, 0x04, 0x75, 0x73, 0x65, 0x72, 0x12, 0x1a, 0x0a, 0x08, 0x77, 0x78, 0x55, 0x73, 0x65,
	0x72, 0x49, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x08, 0x77, 0x78, 0x55, 0x73, 0x65,
	0x72, 0x49, 0x64, 0x12, 0x22, 0x0a, 0x03, 0x66, 0x64, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x10, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x46, 0x64, 0x64, 0x49, 0x6e,
	0x66, 0x6f, 0x52, 0x03, 0x66, 0x64, 0x64, 0x22, 0xab, 0x01, 0x0a, 0x07, 0x46, 0x64, 0x64, 0x49,
	0x6e, 0x66, 0x6f, 0x12, 0x0e, 0x0a, 0x02, 0x49, 0x44, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52,
	0x02, 0x49, 0x44, 0x12, 0x1e, 0x0a, 0x0a, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x49,
	0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65,
	0x72, 0x49, 0x64, 0x12, 0x1a, 0x0a, 0x08, 0x69, 0x73, 0x56, 0x65, 0x72, 0x69, 0x66, 0x79, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x08, 0x52, 0x08, 0x69, 0x73, 0x56, 0x65, 0x72, 0x69, 0x66, 0x79, 0x12,
	0x24, 0x0a, 0x0d, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x4e, 0x6f,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x4e, 0x6f, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x16, 0x0a,
	0x06, 0x69, 0x64, 0x54, 0x79, 0x70, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x69,
	0x64, 0x54, 0x79, 0x70, 0x65, 0x22, 0xb1, 0x03, 0x0a, 0x08, 0x55, 0x73, 0x65, 0x72, 0x49, 0x6e,
	0x66, 0x6f, 0x12, 0x0e, 0x0a, 0x02, 0x49, 0x44, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x02,
	0x49, 0x44, 0x12, 0x1a, 0x0a, 0x08, 0x6e, 0x69, 0x63, 0x6b, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x6e, 0x69, 0x63, 0x6b, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x16,
	0x0a, 0x06, 0x74, 0x65, 0x6c, 0x4e, 0x75, 0x6d, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06,
	0x74, 0x65, 0x6c, 0x4e, 0x75, 0x6d, 0x12, 0x16, 0x0a, 0x06, 0x61, 0x76, 0x61, 0x74, 0x61, 0x72,
	0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x61, 0x76, 0x61, 0x74, 0x61, 0x72, 0x12, 0x1a,
	0x0a, 0x08, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x41, 0x74, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x08, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x41, 0x74, 0x12, 0x1e, 0x0a, 0x0a, 0x72, 0x65,
	0x61, 0x6c, 0x4e, 0x61, 0x6d, 0x65, 0x49, 0x44, 0x18, 0x09, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0a,
	0x72, 0x65, 0x61, 0x6c, 0x4e, 0x61, 0x6d, 0x65, 0x49, 0x44, 0x12, 0x1a, 0x0a, 0x08, 0x72, 0x65,
	0x61, 0x6c, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x72, 0x65,
	0x61, 0x6c, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x69, 0x44, 0x4e, 0x75, 0x6d, 0x18,
	0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x69, 0x44, 0x4e, 0x75, 0x6d, 0x12, 0x16, 0x0a, 0x06,
	0x64, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x64, 0x6f,
	0x6d, 0x61, 0x69, 0x6e, 0x12, 0x1e, 0x0a, 0x0a, 0x72, 0x65, 0x61, 0x6c, 0x49, 0x44, 0x49, 0x6d,
	0x67, 0x41, 0x18, 0x11, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x72, 0x65, 0x61, 0x6c, 0x49, 0x44,
	0x49, 0x6d, 0x67, 0x41, 0x12, 0x1e, 0x0a, 0x0a, 0x72, 0x65, 0x61, 0x6c, 0x49, 0x44, 0x49, 0x6d,
	0x67, 0x42, 0x18, 0x12, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x72, 0x65, 0x61, 0x6c, 0x49, 0x44,
	0x49, 0x6d, 0x67, 0x42, 0x12, 0x26, 0x0a, 0x0e, 0x72, 0x65, 0x61, 0x6c, 0x4e, 0x61, 0x6d, 0x65,
	0x49, 0x44, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x13, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x72, 0x65,
	0x61, 0x6c, 0x4e, 0x61, 0x6d, 0x65, 0x49, 0x44, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x14, 0x0a, 0x05,
	0x76, 0x69, 0x64, 0x65, 0x6f, 0x18, 0x14, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x69, 0x64,
	0x65, 0x6f, 0x12, 0x16, 0x0a, 0x06, 0x69, 0x64, 0x54, 0x79, 0x70, 0x65, 0x18, 0x15, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x06, 0x69, 0x64, 0x54, 0x79, 0x70, 0x65, 0x12, 0x2d, 0x0a, 0x08, 0x70, 0x61,
	0x73, 0x73, 0x70, 0x6f, 0x72, 0x74, 0x18, 0x16, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x61,
	0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x50, 0x61, 0x73, 0x73, 0x70, 0x6f, 0x72, 0x74, 0x52,
	0x08, 0x70, 0x61, 0x73, 0x73, 0x70, 0x6f, 0x72, 0x74, 0x22, 0x33, 0x0a, 0x0d, 0x43, 0x6f, 0x6d,
	0x6d, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x0e, 0x0a, 0x02, 0x49, 0x44,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x02, 0x49, 0x44, 0x12, 0x12, 0x0a, 0x04, 0x66, 0x72,
	0x6f, 0x6d, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x66, 0x72, 0x6f, 0x6d, 0x22, 0x20,
	0x0a, 0x0c, 0x57, 0x78, 0x41, 0x70, 0x70, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x10,
	0x0a, 0x04, 0x47, 0x68, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x49, 0x44,
	0x22, 0x43, 0x0a, 0x0d, 0x57, 0x78, 0x41, 0x70, 0x70, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x12, 0x14, 0x0a, 0x05, 0x41, 0x70, 0x70, 0x49, 0x44, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x05, 0x61, 0x70, 0x70, 0x49, 0x44, 0x12, 0x1c, 0x0a, 0x09, 0x41, 0x70, 0x70, 0x53, 0x65,
	0x63, 0x72, 0x65, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x61, 0x70, 0x70, 0x53,
	0x65, 0x63, 0x72, 0x65, 0x74, 0x22, 0x5b, 0x0a, 0x13, 0x57, 0x78, 0x55, 0x73, 0x65, 0x72, 0x55,
	0x70, 0x64, 0x61, 0x74, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x2c, 0x0a, 0x04,
	0x77, 0x78, 0x49, 0x44, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x42, 0x18, 0xe2, 0xdf, 0x1f, 0x14,
	0x2a, 0x10, 0xe7, 0xbc, 0xba, 0xe5, 0xb0, 0x91, 0xe5, 0x8f, 0x82, 0xe6, 0x95, 0xb0, 0x77, 0x78,
	0x49, 0x44, 0x58, 0x01, 0x52, 0x04, 0x77, 0x78, 0x49, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x75, 0x73,
	0x65, 0x72, 0x49, 0x44, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72,
	0x49, 0x44, 0x22, 0x73, 0x0a, 0x15, 0x57, 0x78, 0x55, 0x73, 0x65, 0x72, 0x4f, 0x72, 0x43, 0x72,
	0x65, 0x61, 0x74, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x2c, 0x0a, 0x06, 0x4f,
	0x70, 0x65, 0x6e, 0x49, 0x44, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x14, 0xe2, 0xdf, 0x1f,
	0x10, 0x2a, 0x0c, 0xe7, 0xbc, 0xba, 0xe5, 0xb0, 0x91, 0x6f, 0x70, 0x65, 0x6e, 0x69, 0x64, 0x58,
	0x01, 0x52, 0x06, 0x6f, 0x70, 0x65, 0x6e, 0x49, 0x44, 0x12, 0x2c, 0x0a, 0x04, 0x47, 0x68, 0x49,
	0x44, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x18, 0xe2, 0xdf, 0x1f, 0x14, 0x2a, 0x10, 0xe7,
	0xbc, 0xba, 0xe5, 0xb0, 0x91, 0xe5, 0x8f, 0x82, 0xe6, 0x95, 0xb0, 0x67, 0x68, 0x69, 0x64, 0x58,
	0x01, 0x52, 0x04, 0x67, 0x68, 0x49, 0x44, 0x22, 0x80, 0x01, 0x0a, 0x0e, 0x57, 0x78, 0x55, 0x73,
	0x65, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x4f, 0x70,
	0x65, 0x6e, 0x49, 0x44, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x6f, 0x70, 0x65, 0x6e,
	0x49, 0x44, 0x12, 0x16, 0x0a, 0x06, 0x55, 0x73, 0x65, 0x72, 0x49, 0x44, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x0d, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x44, 0x12, 0x12, 0x0a, 0x04, 0x47, 0x68,
	0x49, 0x44, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x67, 0x68, 0x49, 0x44, 0x12, 0x1a,
	0x0a, 0x08, 0x52, 0x6f, 0x6c, 0x65, 0x41, 0x75, 0x74, 0x68, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x08, 0x72, 0x6f, 0x6c, 0x65, 0x41, 0x75, 0x74, 0x68, 0x12, 0x0e, 0x0a, 0x02, 0x49, 0x44,
	0x18, 0x05, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x02, 0x49, 0x44, 0x22, 0x3a, 0x0a, 0x11, 0x4c, 0x6f,
	0x67, 0x69, 0x6e, 0x4c, 0x6f, 0x67, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12,
	0x25, 0x0a, 0x04, 0x44, 0x61, 0x74, 0x61, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x11, 0x2e,
	0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x4c, 0x6f, 0x67, 0x69, 0x6e, 0x4c, 0x6f, 0x67,
	0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x22, 0x9c, 0x02, 0x0a, 0x08, 0x4c, 0x6f, 0x67, 0x69, 0x6e,
	0x4c, 0x6f, 0x67, 0x12, 0x16, 0x0a, 0x06, 0x44, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x06, 0x64, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x12, 0x0e, 0x0a, 0x02, 0x49,
	0x44, 0x18, 0x02, 0x20, 0x01, 0x28, 0x04, 0x52, 0x02, 0x49, 0x44, 0x12, 0x16, 0x0a, 0x06, 0x55,
	0x73, 0x65, 0x72, 0x49, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x04, 0x52, 0x06, 0x75, 0x73, 0x65,
	0x72, 0x49, 0x64, 0x12, 0x0e, 0x0a, 0x02, 0x49, 0x70, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x02, 0x69, 0x70, 0x12, 0x14, 0x0a, 0x05, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x05, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x12, 0x16, 0x0a, 0x06, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x18, 0x06, 0x20, 0x01, 0x28, 0x04, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x12, 0x1e, 0x0a, 0x0a, 0x45, 0x78, 0x70, 0x69, 0x72, 0x65, 0x44, 0x61, 0x74, 0x65, 0x18,
	0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x65, 0x78, 0x70, 0x69, 0x72, 0x65, 0x44, 0x61, 0x74,
	0x65, 0x12, 0x1a, 0x0a, 0x08, 0x4c, 0x61, 0x73, 0x74, 0x44, 0x61, 0x74, 0x65, 0x18, 0x08, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x08, 0x6c, 0x61, 0x73, 0x74, 0x44, 0x61, 0x74, 0x65, 0x12, 0x1e, 0x0a,
	0x0a, 0x4c, 0x6f, 0x67, 0x6f, 0x75, 0x74, 0x44, 0x61, 0x74, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0a, 0x6c, 0x6f, 0x67, 0x6f, 0x75, 0x74, 0x44, 0x61, 0x74, 0x65, 0x12, 0x1c, 0x0a,
	0x09, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x18, 0x0a, 0x07, 0x41,
	0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x64,
	0x64, 0x72, 0x65, 0x73, 0x73, 0x22, 0x3e, 0x0a, 0x14, 0x4f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x4c,
	0x6f, 0x67, 0x42, 0x79, 0x49, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x16, 0x0a,
	0x06, 0x44, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x64,
	0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x12, 0x0e, 0x0a, 0x02, 0x49, 0x44, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x04, 0x52, 0x02, 0x49, 0x44, 0x22, 0x4b, 0x0a, 0x19, 0x4c, 0x6f, 0x67, 0x69, 0x6e, 0x49, 0x6e,
	0x66, 0x6f, 0x73, 0x42, 0x79, 0x55, 0x73, 0x65, 0x72, 0x49, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x12, 0x16, 0x0a, 0x06, 0x44, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x06, 0x64, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x12, 0x16, 0x0a, 0x06, 0x55, 0x73,
	0x65, 0x72, 0x49, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x04, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72,
	0x49, 0x64, 0x22, 0xca, 0x01, 0x0a, 0x17, 0x53, 0x65, 0x6e, 0x64, 0x4e, 0x65, 0x77, 0x54, 0x65,
	0x6c, 0x4e, 0x75, 0x6d, 0x4d, 0x73, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x25,
	0x0a, 0x06, 0x44, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0d,
	0xe2, 0xdf, 0x1f, 0x09, 0x2a, 0x05, 0x37, 0x30, 0x30, 0x30, 0x31, 0x58, 0x01, 0x52, 0x06, 0x64,
	0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x12, 0x24, 0x0a, 0x02, 0x49, 0x44, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x04, 0x42, 0x14, 0xe2, 0xdf, 0x1f, 0x10, 0x2a, 0x0c, 0xe7, 0xbc, 0xba, 0xe5, 0xb0, 0x91, 0xe5,
	0x8f, 0x82, 0xe6, 0x95, 0xb0, 0x58, 0x01, 0x52, 0x02, 0x49, 0x44, 0x12, 0x1c, 0x0a, 0x09, 0x4e,
	0x65, 0x77, 0x54, 0x65, 0x6c, 0x4e, 0x75, 0x6d, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09,
	0x6e, 0x65, 0x77, 0x54, 0x65, 0x6c, 0x4e, 0x75, 0x6d, 0x12, 0x12, 0x0a, 0x04, 0x43, 0x6f, 0x64,
	0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x18, 0x0a,
	0x07, 0x50, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07,
	0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x69, 0x67, 0x6e, 0x4e,
	0x6f, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x06, 0x73, 0x69, 0x67, 0x6e, 0x4e, 0x6f, 0x22,
	0x4b, 0x0a, 0x10, 0x55, 0x73, 0x65, 0x72, 0x42, 0x79, 0x54, 0x65, 0x6c, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x12, 0x25, 0x0a, 0x06, 0x44, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x42, 0x0d, 0xe2, 0xdf, 0x1f, 0x09, 0x2a, 0x05, 0x37, 0x30, 0x30, 0x30, 0x31,
	0x58, 0x01, 0x52, 0x06, 0x64, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x12, 0x10, 0x0a, 0x03, 0x54, 0x65,
	0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x74, 0x65, 0x6c, 0x22, 0x10, 0x0a, 0x0e,
	0x43, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x4e,
	0x0a, 0x11, 0x55, 0x73, 0x65, 0x72, 0x73, 0x42, 0x79, 0x54, 0x65, 0x6c, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x12, 0x25, 0x0a, 0x06, 0x44, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x42, 0x0d, 0xe2, 0xdf, 0x1f, 0x09, 0x2a, 0x05, 0x37, 0x30, 0x30, 0x30, 0x31,
	0x58, 0x01, 0x52, 0x06, 0x64, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x12, 0x12, 0x0a, 0x04, 0x54, 0x65,
	0x6c, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x09, 0x52, 0x04, 0x74, 0x65, 0x6c, 0x73, 0x22, 0xf5,
	0x01, 0x0a, 0x10, 0x4c, 0x69, 0x73, 0x74, 0x42, 0x79, 0x49, 0x44, 0x73, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x12, 0x25, 0x0a, 0x06, 0x44, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x42, 0x0d, 0xe2, 0xdf, 0x1f, 0x09, 0x2a, 0x05, 0x37, 0x30, 0x30, 0x30, 0x31,
	0x58, 0x01, 0x52, 0x06, 0x64, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x12, 0x10, 0x0a, 0x03, 0x49, 0x44,
	0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x04, 0x52, 0x03, 0x49, 0x44, 0x73, 0x12, 0x1c, 0x0a, 0x09,
	0x4f, 0x72, 0x64, 0x65, 0x72, 0x54, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x04, 0x52,
	0x09, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x54, 0x79, 0x70, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x50, 0x61,
	0x67, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x04, 0x52, 0x04, 0x70, 0x61, 0x67, 0x65, 0x12, 0x1a,
	0x0a, 0x08, 0x50, 0x61, 0x67, 0x65, 0x53, 0x69, 0x7a, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x04,
	0x52, 0x08, 0x70, 0x61, 0x67, 0x65, 0x53, 0x69, 0x7a, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x4e, 0x69,
	0x63, 0x6b, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x6e, 0x69,
	0x63, 0x6b, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x26, 0x0a, 0x0e, 0x49, 0x6e, 0x76, 0x69, 0x74, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x43, 0x6f, 0x64, 0x65, 0x18, 0x07, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0e,
	0x69, 0x6e, 0x76, 0x69, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x16,
	0x0a, 0x06, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06,
	0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x22, 0xbd, 0x01, 0x0a, 0x0e, 0x53, 0x65, 0x6e, 0x64, 0x4d,
	0x73, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x25, 0x0a, 0x06, 0x44, 0x6f, 0x6d,
	0x61, 0x69, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0d, 0xe2, 0xdf, 0x1f, 0x09, 0x2a,
	0x05, 0x37, 0x30, 0x30, 0x30, 0x31, 0x58, 0x01, 0x52, 0x06, 0x64, 0x6f, 0x6d, 0x61, 0x69, 0x6e,
	0x12, 0x16, 0x0a, 0x06, 0x54, 0x65, 0x6c, 0x4e, 0x75, 0x6d, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x06, 0x74, 0x65, 0x6c, 0x4e, 0x75, 0x6d, 0x12, 0x18, 0x0a, 0x07, 0x50, 0x72, 0x6f, 0x6a,
	0x65, 0x63, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x70, 0x72, 0x6f, 0x6a, 0x65,
	0x63, 0x74, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x69, 0x67, 0x6e, 0x4e, 0x6f, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x0d, 0x52, 0x06, 0x73, 0x69, 0x67, 0x6e, 0x4e, 0x6f, 0x12, 0x10, 0x0a, 0x03, 0x6d, 0x49,
	0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x03, 0x6d, 0x49, 0x64, 0x12, 0x14, 0x0a, 0x05,
	0x73, 0x63, 0x6f, 0x70, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x73, 0x63, 0x6f,
	0x70, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x7a, 0x6f, 0x6e, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x04, 0x7a, 0x6f, 0x6e, 0x65, 0x22, 0xe9, 0x01, 0x0a, 0x14, 0x53, 0x65, 0x6e, 0x64, 0x43,
	0x75, 0x73, 0x74, 0x6f, 0x6d, 0x4d, 0x73, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12,
	0x25, 0x0a, 0x06, 0x44, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42,
	0x0d, 0xe2, 0xdf, 0x1f, 0x09, 0x2a, 0x05, 0x37, 0x30, 0x30, 0x30, 0x31, 0x58, 0x01, 0x52, 0x06,
	0x64, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x12, 0x16, 0x0a, 0x06, 0x54, 0x65, 0x6c, 0x4e, 0x75, 0x6d,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x74, 0x65, 0x6c, 0x4e, 0x75, 0x6d, 0x12, 0x18,
	0x0a, 0x07, 0x50, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x07, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x12, 0x10, 0x0a, 0x03, 0x55, 0x72, 0x6c, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x55, 0x72, 0x6c, 0x12, 0x0e, 0x0a, 0x02, 0x49, 0x44,
	0x18, 0x05, 0x20, 0x01, 0x28, 0x04, 0x52, 0x02, 0x49, 0x44, 0x12, 0x10, 0x0a, 0x03, 0x4d, 0x49,
	0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x04, 0x52, 0x03, 0x6d, 0x49, 0x64, 0x12, 0x1a, 0x0a, 0x08,
	0x4c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x07, 0x20, 0x01, 0x28, 0x04, 0x52, 0x08,
	0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x14, 0x0a, 0x05, 0x53, 0x69, 0x67, 0x4e,
	0x6f, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x05, 0x73, 0x69, 0x67, 0x4e, 0x6f, 0x12, 0x12,
	0x0a, 0x04, 0x7a, 0x6f, 0x6e, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x7a, 0x6f,
	0x6e, 0x65, 0x22, 0x89, 0x01, 0x0a, 0x0f, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x4d, 0x73, 0x67, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x25, 0x0a, 0x06, 0x44, 0x6f, 0x6d, 0x61, 0x69, 0x6e,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0d, 0xe2, 0xdf, 0x1f, 0x09, 0x2a, 0x05, 0x37, 0x30,
	0x30, 0x30, 0x31, 0x58, 0x01, 0x52, 0x06, 0x64, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x12, 0x16, 0x0a,
	0x06, 0x54, 0x65, 0x6c, 0x4e, 0x75, 0x6d, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x74,
	0x65, 0x6c, 0x4e, 0x75, 0x6d, 0x12, 0x21, 0x0a, 0x04, 0x43, 0x6f, 0x64, 0x65, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x09, 0x42, 0x0d, 0xe2, 0xdf, 0x1f, 0x09, 0x2a, 0x05, 0x37, 0x30, 0x30, 0x30, 0x33,
	0x58, 0x01, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x73, 0x63, 0x6f, 0x70,
	0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x73, 0x63, 0x6f, 0x70, 0x65, 0x22, 0x17,
	0x0a, 0x15, 0x53, 0x65, 0x6e, 0x64, 0x4d, 0x73, 0x67, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x69, 0x0a, 0x0d, 0x52, 0x65, 0x6d, 0x6f, 0x76,
	0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x25, 0x0a, 0x06, 0x44, 0x6f, 0x6d, 0x61,
	0x69, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0d, 0xe2, 0xdf, 0x1f, 0x09, 0x2a, 0x05,
	0x37, 0x30, 0x30, 0x30, 0x31, 0x58, 0x01, 0x52, 0x06, 0x64, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x12,
	0x1d, 0x0a, 0x02, 0x49, 0x44, 0x18, 0x02, 0x20, 0x01, 0x28, 0x04, 0x42, 0x0d, 0xe2, 0xdf, 0x1f,
	0x09, 0x10, 0x00, 0x2a, 0x05, 0x37, 0x30, 0x30, 0x30, 0x34, 0x52, 0x02, 0x49, 0x44, 0x12, 0x12,
	0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x63, 0x6f,
	0x64, 0x65, 0x22, 0x93, 0x03, 0x0a, 0x0f, 0x57, 0x72, 0x69, 0x74, 0x65, 0x4f, 0x66, 0x66, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x04, 0x52, 0x02, 0x69, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x64, 0x6f, 0x6d, 0x61, 0x69, 0x6e,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x64, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x12, 0x16,
	0x0a, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x04, 0x52, 0x06,
	0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x12, 0x10, 0x0a, 0x03, 0x74, 0x65, 0x6c, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x03, 0x74, 0x65, 0x6c, 0x12, 0x16, 0x0a, 0x06, 0x6a, 0x6f, 0x6e, 0x4e,
	0x75, 0x6d, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x6a, 0x6f, 0x6e, 0x4e, 0x75, 0x6d,
	0x12, 0x1a, 0x0a, 0x08, 0x75, 0x73, 0x65, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x06, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x08, 0x75, 0x73, 0x65, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1c, 0x0a, 0x09,
	0x65, 0x6e, 0x74, 0x65, 0x72, 0x44, 0x61, 0x74, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x09, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x44, 0x61, 0x74, 0x65, 0x12, 0x22, 0x0a, 0x0c, 0x70, 0x6f,
	0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0c, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1e,
	0x0a, 0x0a, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x18, 0x09, 0x20, 0x01,
	0x28, 0x04, 0x52, 0x0a, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x12, 0x1a,
	0x0a, 0x08, 0x73, 0x69, 0x74, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x08, 0x73, 0x69, 0x74, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x69,
	0x74, 0x65, 0x49, 0x64, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x04, 0x52, 0x06, 0x73, 0x69, 0x74, 0x65,
	0x49, 0x64, 0x12, 0x18, 0x0a, 0x07, 0x61, 0x75, 0x74, 0x68, 0x55, 0x72, 0x6c, 0x18, 0x0c, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x75, 0x74, 0x68, 0x55, 0x72, 0x6c, 0x12, 0x12, 0x0a, 0x04,
	0x74, 0x79, 0x70, 0x65, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65,
	0x12, 0x16, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x0d,
	0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x1e, 0x0a, 0x0a, 0x73, 0x75, 0x62, 0x6d,
	0x69, 0x74, 0x44, 0x61, 0x74, 0x65, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x73, 0x75,
	0x62, 0x6d, 0x69, 0x74, 0x44, 0x61, 0x74, 0x65, 0x22, 0xd5, 0x01, 0x0a, 0x13, 0x57, 0x72, 0x69,
	0x74, 0x65, 0x4f, 0x66, 0x66, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x12, 0x12, 0x0a, 0x04, 0x70, 0x61, 0x67, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x04,
	0x70, 0x61, 0x67, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x61, 0x67, 0x65, 0x53, 0x69, 0x7a, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x04, 0x52, 0x08, 0x70, 0x61, 0x67, 0x65, 0x53, 0x69, 0x7a, 0x65,
	0x12, 0x16, 0x0a, 0x06, 0x64, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x06, 0x64, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x12, 0x1a, 0x0a, 0x08, 0x75, 0x73, 0x65, 0x72,
	0x4e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x75, 0x73, 0x65, 0x72,
	0x4e, 0x61, 0x6d, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x04, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x12, 0x16, 0x0a, 0x06,
	0x73, 0x69, 0x74, 0x65, 0x49, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x04, 0x52, 0x06, 0x73, 0x69,
	0x74, 0x65, 0x49, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x07, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x22, 0x40, 0x0a, 0x16, 0x57, 0x72, 0x69, 0x74, 0x65, 0x4f, 0x66, 0x66, 0x41, 0x70, 0x70, 0x72,
	0x6f, 0x76, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x02, 0x69, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x22, 0x6a, 0x0a, 0x14, 0x57, 0x72, 0x69, 0x74, 0x65, 0x4f, 0x66, 0x66, 0x4c, 0x69,
	0x73, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x6f,
	0x74, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c,
	0x12, 0x3c, 0x0a, 0x0c, 0x77, 0x72, 0x69, 0x74, 0x65, 0x4f, 0x66, 0x66, 0x4c, 0x69, 0x73, 0x74,
	0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x2e, 0x57, 0x72, 0x69, 0x74, 0x65, 0x4f, 0x66, 0x66, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x52, 0x0c, 0x77, 0x72, 0x69, 0x74, 0x65, 0x4f, 0x66, 0x66, 0x4c, 0x69, 0x73, 0x74, 0x22, 0x10,
	0x0a, 0x0e, 0x52, 0x65, 0x6d, 0x6f, 0x76, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x22, 0xa4, 0x06, 0x0a, 0x0d, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x12, 0x0e, 0x0a, 0x02, 0x49, 0x44, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x02,
	0x49, 0x44, 0x12, 0x25, 0x0a, 0x06, 0x44, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x42, 0x0d, 0xe2, 0xdf, 0x1f, 0x09, 0x2a, 0x05, 0x37, 0x30, 0x30, 0x30, 0x31, 0x58,
	0x01, 0x52, 0x06, 0x64, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x12, 0x1a, 0x0a, 0x08, 0x4e, 0x69, 0x63,
	0x6b, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x6e, 0x69, 0x63,
	0x6b, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x50, 0x61, 0x73, 0x73, 0x77, 0x6f, 0x72,
	0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x70, 0x61, 0x73, 0x73, 0x77, 0x6f, 0x72,
	0x64, 0x12, 0x16, 0x0a, 0x06, 0x41, 0x76, 0x61, 0x74, 0x61, 0x72, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x06, 0x61, 0x76, 0x61, 0x74, 0x61, 0x72, 0x12, 0x16, 0x0a, 0x06, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x12, 0x16, 0x0a, 0x06, 0x54, 0x65, 0x6c, 0x4e, 0x75, 0x6d, 0x18, 0x08, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x06, 0x74, 0x65, 0x6c, 0x4e, 0x75, 0x6d, 0x12, 0x1c, 0x0a, 0x09, 0x45, 0x6e, 0x74,
	0x65, 0x72, 0x44, 0x61, 0x74, 0x65, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x65, 0x6e,
	0x74, 0x65, 0x72, 0x44, 0x61, 0x74, 0x65, 0x12, 0x27, 0x0a, 0x06, 0x45, 0x78, 0x74, 0x65, 0x6e,
	0x64, 0x18, 0x11, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0f, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x2e, 0x45, 0x78, 0x74, 0x65, 0x6e, 0x64, 0x52, 0x06, 0x65, 0x78, 0x74, 0x65, 0x6e, 0x64,
	0x12, 0x14, 0x0a, 0x05, 0x54, 0x69, 0x74, 0x6c, 0x65, 0x18, 0x12, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x4a, 0x6f, 0x62, 0x4e, 0x75, 0x6d,
	0x18, 0x13, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x6a, 0x6f, 0x62, 0x4e, 0x75, 0x6d, 0x12, 0x1c,
	0x0a, 0x09, 0x42, 0x69, 0x72, 0x74, 0x68, 0x44, 0x61, 0x74, 0x65, 0x18, 0x14, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x09, 0x62, 0x69, 0x72, 0x74, 0x68, 0x44, 0x61, 0x74, 0x65, 0x12, 0x10, 0x0a, 0x03,
	0x53, 0x65, 0x78, 0x18, 0x15, 0x20, 0x01, 0x28, 0x04, 0x52, 0x03, 0x73, 0x65, 0x78, 0x12, 0x14,
	0x0a, 0x05, 0x49, 0x64, 0x4e, 0x75, 0x6d, 0x18, 0x16, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x69,
	0x64, 0x4e, 0x75, 0x6d, 0x12, 0x1a, 0x0a, 0x08, 0x52, 0x65, 0x61, 0x6c, 0x4e, 0x61, 0x6d, 0x65,
	0x18, 0x17, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x72, 0x65, 0x61, 0x6c, 0x4e, 0x61, 0x6d, 0x65,
	0x12, 0x26, 0x0a, 0x0e, 0x49, 0x6e, 0x76, 0x69, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x6f,
	0x64, 0x65, 0x18, 0x18, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x69, 0x6e, 0x76, 0x69, 0x74, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x4c, 0x65, 0x66, 0x74,
	0x44, 0x61, 0x74, 0x65, 0x18, 0x19, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x6c, 0x65, 0x66, 0x74,
	0x44, 0x61, 0x74, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x52, 0x65, 0x6d, 0x61, 0x72, 0x6b, 0x18, 0x1a,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x72, 0x65, 0x6d, 0x61, 0x72, 0x6b, 0x12, 0x1c, 0x0a, 0x09,
	0x52, 0x65, 0x63, 0x65, 0x6e, 0x74, 0x49, 0x6d, 0x67, 0x18, 0x1b, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x09, 0x72, 0x65, 0x63, 0x65, 0x6e, 0x74, 0x49, 0x6d, 0x67, 0x12, 0x14, 0x0a, 0x05, 0x49, 0x43,
	0x4e, 0x75, 0x6d, 0x18, 0x1c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x69, 0x63, 0x4e, 0x75, 0x6d,
	0x12, 0x14, 0x0a, 0x05, 0x54, 0x72, 0x61, 0x69, 0x6e, 0x18, 0x1d, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x05, 0x74, 0x72, 0x61, 0x69, 0x6e, 0x12, 0x20, 0x0a, 0x0b, 0x43, 0x65, 0x72, 0x74, 0x69, 0x66,
	0x69, 0x63, 0x61, 0x74, 0x65, 0x18, 0x1e, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x63, 0x65, 0x72,
	0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x65, 0x12, 0x35, 0x0a, 0x0b, 0x54, 0x72, 0x61, 0x69,
	0x6e, 0x56, 0x69, 0x64, 0x65, 0x6f, 0x73, 0x18, 0x1f, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x13, 0x2e,
	0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x74, 0x72, 0x61, 0x69, 0x6e, 0x56, 0x69, 0x64,
	0x65, 0x6f, 0x52, 0x0b, 0x74, 0x72, 0x61, 0x69, 0x6e, 0x56, 0x69, 0x64, 0x65, 0x6f, 0x73, 0x12,
	0x2d, 0x0a, 0x08, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x18, 0x20, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x11, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x4f, 0x70, 0x65, 0x72,
	0x61, 0x74, 0x6f, 0x72, 0x52, 0x08, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x12, 0x22,
	0x0a, 0x0c, 0x53, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x79, 0x43, 0x6f, 0x64, 0x65, 0x18, 0x21,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x73, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x79, 0x43, 0x6f,
	0x64, 0x65, 0x12, 0x33, 0x0a, 0x0a, 0x75, 0x73, 0x65, 0x72, 0x45, 0x78, 0x74, 0x65, 0x6e, 0x64,
	0x18, 0x22, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x2e, 0x55, 0x73, 0x65, 0x72, 0x45, 0x78, 0x74, 0x65, 0x6e, 0x64, 0x52, 0x0a, 0x75, 0x73, 0x65,
	0x72, 0x45, 0x78, 0x74, 0x65, 0x6e, 0x64, 0x22, 0x2e, 0x0a, 0x08, 0x4f, 0x70, 0x65, 0x72, 0x61,
	0x74, 0x6f, 0x72, 0x12, 0x0e, 0x0a, 0x02, 0x49, 0x44, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52,
	0x02, 0x49, 0x44, 0x12, 0x12, 0x0a, 0x04, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x04, 0x4e, 0x61, 0x6d, 0x65, 0x22, 0x82, 0x01, 0x0a, 0x0a, 0x74, 0x72, 0x61, 0x69,
	0x6e, 0x56, 0x69, 0x64, 0x65, 0x6f, 0x12, 0x1c, 0x0a, 0x09, 0x74, 0x72, 0x61, 0x69, 0x6e, 0x55,
	0x55, 0x49, 0x44, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x74, 0x72, 0x61, 0x69, 0x6e,
	0x55, 0x55, 0x49, 0x44, 0x12, 0x1c, 0x0a, 0x09, 0x74, 0x72, 0x61, 0x69, 0x6e, 0x44, 0x65, 0x73,
	0x63, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x74, 0x72, 0x61, 0x69, 0x6e, 0x44, 0x65,
	0x73, 0x63, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x69, 0x64, 0x65, 0x6f, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x05, 0x76, 0x69, 0x64, 0x65, 0x6f, 0x12, 0x22, 0x0a, 0x0c, 0x53, 0x65, 0x63, 0x75,
	0x72, 0x69, 0x74, 0x79, 0x43, 0x6f, 0x64, 0x65, 0x18, 0x1f, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c,
	0x73, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x79, 0x43, 0x6f, 0x64, 0x65, 0x22, 0x10, 0x0a, 0x0e,
	0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x6f,
	0x0a, 0x12, 0x50, 0x72, 0x69, 0x76, 0x61, 0x63, 0x79, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x12, 0x0e, 0x0a, 0x02, 0x49, 0x44, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04,
	0x52, 0x02, 0x49, 0x44, 0x12, 0x25, 0x0a, 0x06, 0x44, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x42, 0x0d, 0xe2, 0xdf, 0x1f, 0x09, 0x2a, 0x05, 0x37, 0x30, 0x30, 0x30,
	0x31, 0x58, 0x01, 0x52, 0x06, 0x64, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x12, 0x22, 0x0a, 0x0c, 0x53,
	0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x79, 0x43, 0x6f, 0x64, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0c, 0x73, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x79, 0x43, 0x6f, 0x64, 0x65, 0x22,
	0x8e, 0x05, 0x0a, 0x0b, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12,
	0x25, 0x0a, 0x06, 0x44, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42,
	0x0d, 0xe2, 0xdf, 0x1f, 0x09, 0x2a, 0x05, 0x37, 0x30, 0x30, 0x30, 0x31, 0x58, 0x01, 0x52, 0x06,
	0x64, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x12, 0x1a, 0x0a, 0x08, 0x50, 0x61, 0x67, 0x65, 0x53, 0x69,
	0x7a, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x04, 0x52, 0x08, 0x70, 0x61, 0x67, 0x65, 0x53, 0x69,
	0x7a, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x50, 0x61, 0x67, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x04,
	0x52, 0x04, 0x70, 0x61, 0x67, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x12, 0x16, 0x0a, 0x06, 0x74, 0x65, 0x6c, 0x4e, 0x75, 0x6d, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x06, 0x74, 0x65, 0x6c, 0x4e, 0x75, 0x6d, 0x12, 0x26, 0x0a, 0x0e, 0x73, 0x74, 0x61, 0x72,
	0x74, 0x45, 0x6e, 0x74, 0x65, 0x72, 0x44, 0x61, 0x74, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0e, 0x73, 0x74, 0x61, 0x72, 0x74, 0x45, 0x6e, 0x74, 0x65, 0x72, 0x44, 0x61, 0x74, 0x65,
	0x12, 0x22, 0x0a, 0x0c, 0x65, 0x6e, 0x64, 0x45, 0x6e, 0x74, 0x65, 0x72, 0x44, 0x61, 0x74, 0x65,
	0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x65, 0x6e, 0x64, 0x45, 0x6e, 0x74, 0x65, 0x72,
	0x44, 0x61, 0x74, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x6e, 0x69, 0x63, 0x6b, 0x4e, 0x61, 0x6d, 0x65,
	0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x6e, 0x69, 0x63, 0x6b, 0x4e, 0x61, 0x6d, 0x65,
	0x12, 0x16, 0x0a, 0x06, 0x69, 0x73, 0x52, 0x65, 0x61, 0x6c, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0d,
	0x52, 0x06, 0x69, 0x73, 0x52, 0x65, 0x61, 0x6c, 0x12, 0x1e, 0x0a, 0x0a, 0x69, 0x73, 0x4d, 0x61,
	0x69, 0x6e, 0x4c, 0x61, 0x6e, 0x64, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0a, 0x69, 0x73,
	0x4d, 0x61, 0x69, 0x6e, 0x4c, 0x61, 0x6e, 0x64, 0x12, 0x1a, 0x0a, 0x08, 0x66, 0x72, 0x6f, 0x6d,
	0x43, 0x6f, 0x64, 0x65, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x66, 0x72, 0x6f, 0x6d,
	0x43, 0x6f, 0x64, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x62, 0x61, 0x6e, 0x6b, 0x4e, 0x61, 0x6d, 0x65,
	0x18, 0x0d, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x62, 0x61, 0x6e, 0x6b, 0x4e, 0x61, 0x6d, 0x65,
	0x12, 0x16, 0x0a, 0x06, 0x62, 0x61, 0x6e, 0x6b, 0x4e, 0x6f, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x06, 0x62, 0x61, 0x6e, 0x6b, 0x4e, 0x6f, 0x12, 0x10, 0x0a, 0x03, 0x73, 0x65, 0x78, 0x18,
	0x0f, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x03, 0x73, 0x65, 0x78, 0x12, 0x24, 0x0a, 0x0d, 0x73, 0x74,
	0x61, 0x72, 0x74, 0x52, 0x65, 0x61, 0x6c, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x10, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0d, 0x73, 0x74, 0x61, 0x72, 0x74, 0x52, 0x65, 0x61, 0x6c, 0x54, 0x69, 0x6d, 0x65,
	0x12, 0x20, 0x0a, 0x0b, 0x65, 0x6e, 0x64, 0x52, 0x65, 0x61, 0x6c, 0x54, 0x69, 0x6d, 0x65, 0x18,
	0x11, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x65, 0x6e, 0x64, 0x52, 0x65, 0x61, 0x6c, 0x54, 0x69,
	0x6d, 0x65, 0x12, 0x22, 0x0a, 0x0c, 0x65, 0x6e, 0x64, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64,
	0x41, 0x74, 0x18, 0x12, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x65, 0x6e, 0x64, 0x43, 0x72, 0x65,
	0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x26, 0x0a, 0x0e, 0x73, 0x74, 0x61, 0x72, 0x74, 0x43,
	0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x18, 0x13, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e,
	0x73, 0x74, 0x61, 0x72, 0x74, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x1a,
	0x0a, 0x08, 0x72, 0x65, 0x61, 0x6c, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x14, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x08, 0x72, 0x65, 0x61, 0x6c, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x69, 0x44,
	0x4e, 0x75, 0x6d, 0x18, 0x15, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x69, 0x44, 0x4e, 0x75, 0x6d,
	0x12, 0x1e, 0x0a, 0x0a, 0x70, 0x61, 0x73, 0x73, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x16,
	0x20, 0x01, 0x28, 0x0d, 0x52, 0x0a, 0x70, 0x61, 0x73, 0x73, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x22, 0x82, 0x01, 0x0a, 0x0c, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x12, 0x16, 0x0a, 0x06, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x14, 0x0a, 0x05, 0x43, 0x6f, 0x75,
	0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x04, 0x52, 0x05, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x12,
	0x28, 0x0a, 0x04, 0x44, 0x61, 0x74, 0x61, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x14, 0x2e,
	0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x49,
	0x6e, 0x66, 0x6f, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x12, 0x1a, 0x0a, 0x08, 0x41, 0x6c, 0x6c,
	0x43, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x04, 0x52, 0x08, 0x61, 0x6c, 0x6c,
	0x43, 0x6f, 0x75, 0x6e, 0x74, 0x22, 0x88, 0x01, 0x0a, 0x0b, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x25, 0x0a, 0x06, 0x44, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0d, 0xe2, 0xdf, 0x1f, 0x09, 0x2a, 0x05, 0x37, 0x30, 0x30,
	0x30, 0x31, 0x58, 0x01, 0x52, 0x06, 0x64, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x12, 0x0e, 0x0a, 0x02,
	0x49, 0x44, 0x18, 0x02, 0x20, 0x01, 0x28, 0x04, 0x52, 0x02, 0x69, 0x64, 0x12, 0x14, 0x0a, 0x05,
	0x73, 0x63, 0x65, 0x6e, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x73, 0x63, 0x65,
	0x6e, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x75, 0x75, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x04, 0x75, 0x75, 0x69, 0x64, 0x12, 0x18, 0x0a, 0x07, 0x65, 0x78, 0x74, 0x65, 0x6e, 0x64,
	0x73, 0x18, 0x05, 0x20, 0x03, 0x28, 0x09, 0x52, 0x07, 0x65, 0x78, 0x74, 0x65, 0x6e, 0x64, 0x73,
	0x22, 0x71, 0x0a, 0x0c, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x12, 0x16, 0x0a, 0x06, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x2f, 0x0a, 0x04, 0x49, 0x6e, 0x66, 0x6f,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x2e, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x0b, 0x61, 0x63,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x18, 0x0a, 0x07, 0x49, 0x73, 0x45,
	0x78, 0x69, 0x73, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x69, 0x73, 0x45, 0x78,
	0x69, 0x73, 0x74, 0x22, 0xb2, 0x01, 0x0a, 0x12, 0x44, 0x65, 0x63, 0x72, 0x79, 0x70, 0x74, 0x4a,
	0x77, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x44, 0x6f,
	0x6d, 0x61, 0x69, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x12, 0x0e, 0x0a, 0x02, 0x49, 0x44, 0x18, 0x02, 0x20, 0x01, 0x28, 0x04, 0x52, 0x02,
	0x69, 0x64, 0x12, 0x18, 0x0a, 0x07, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x1a, 0x0a, 0x08,
	0x4e, 0x69, 0x63, 0x6b, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08,
	0x6e, 0x69, 0x63, 0x6b, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1c, 0x0a, 0x09, 0x49, 0x73, 0x4f, 0x66,
	0x66, 0x6c, 0x69, 0x6e, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x08, 0x52, 0x09, 0x69, 0x73, 0x4f,
	0x66, 0x66, 0x6c, 0x69, 0x6e, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x6f, 0x66, 0x66, 0x6c, 0x69, 0x6e,
	0x65, 0x43, 0x6f, 0x64, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x6f, 0x66, 0x66,
	0x6c, 0x69, 0x6e, 0x65, 0x43, 0x6f, 0x64, 0x65, 0x22, 0x41, 0x0a, 0x11, 0x44, 0x65, 0x63, 0x72,
	0x79, 0x70, 0x74, 0x4a, 0x77, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x14, 0x0a,
	0x05, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x74, 0x6f,
	0x6b, 0x65, 0x6e, 0x12, 0x16, 0x0a, 0x06, 0x44, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x06, 0x44, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x22, 0x43, 0x0a, 0x0f, 0x43,
	0x68, 0x65, 0x63, 0x6b, 0x50, 0x77, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x14,
	0x0a, 0x05, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x74,
	0x6f, 0x6b, 0x65, 0x6e, 0x12, 0x1a, 0x0a, 0x08, 0x50, 0x61, 0x73, 0x73, 0x77, 0x6f, 0x72, 0x64,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x70, 0x61, 0x73, 0x73, 0x77, 0x6f, 0x72, 0x64,
	0x22, 0x67, 0x0a, 0x15, 0x41, 0x75, 0x74, 0x68, 0x65, 0x6e, 0x74, 0x69, 0x63, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x12, 0x0a, 0x04, 0x4e, 0x61, 0x6d,
	0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x24, 0x0a,
	0x05, 0x49, 0x44, 0x4e, 0x75, 0x6d, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0e, 0xe2, 0xdf,
	0x1f, 0x0a, 0x2a, 0x05, 0x37, 0x30, 0x30, 0x30, 0x36, 0x80, 0x01, 0x12, 0x52, 0x05, 0x69, 0x64,
	0x4e, 0x75, 0x6d, 0x12, 0x14, 0x0a, 0x05, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x05, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x22, 0x51, 0x0a, 0x0d, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x16, 0x0a, 0x06, 0x53, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x12, 0x0e, 0x0a, 0x02, 0x49, 0x44, 0x18, 0x02, 0x20, 0x01, 0x28, 0x04, 0x52, 0x02,
	0x49, 0x44, 0x12, 0x18, 0x0a, 0x07, 0x69, 0x73, 0x45, 0x78, 0x69, 0x73, 0x74, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x08, 0x52, 0x07, 0x69, 0x73, 0x45, 0x78, 0x69, 0x73, 0x74, 0x22, 0xde, 0x06, 0x0a,
	0x0d, 0x52, 0x65, 0x67, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x25,
	0x0a, 0x06, 0x44, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0d,
	0xe2, 0xdf, 0x1f, 0x09, 0x2a, 0x05, 0x37, 0x30, 0x30, 0x30, 0x31, 0x58, 0x01, 0x52, 0x06, 0x64,
	0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x12, 0x2b, 0x0a, 0x08, 0x4e, 0x69, 0x63, 0x6b, 0x4e, 0x61, 0x6d,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0f, 0xe2, 0xdf, 0x1f, 0x0b, 0x2a, 0x05, 0x37,
	0x30, 0x30, 0x30, 0x35, 0x58, 0x01, 0x78, 0x14, 0x52, 0x08, 0x6e, 0x69, 0x63, 0x6b, 0x4e, 0x61,
	0x6d, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x54, 0x65, 0x6c, 0x4e, 0x75, 0x6d, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x06, 0x74, 0x65, 0x6c, 0x4e, 0x75, 0x6d, 0x12, 0x29, 0x0a, 0x08, 0x50, 0x61,
	0x73, 0x73, 0x77, 0x6f, 0x72, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0d, 0xe2, 0xdf,
	0x1f, 0x09, 0x2a, 0x05, 0x37, 0x30, 0x30, 0x30, 0x37, 0x70, 0x05, 0x52, 0x08, 0x70, 0x61, 0x73,
	0x73, 0x77, 0x6f, 0x72, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x41, 0x76, 0x61, 0x74, 0x61, 0x72, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x61, 0x76, 0x61, 0x74, 0x61, 0x72, 0x12, 0x1c, 0x0a,
	0x09, 0x45, 0x6e, 0x74, 0x65, 0x72, 0x44, 0x61, 0x74, 0x65, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x09, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x44, 0x61, 0x74, 0x65, 0x12, 0x27, 0x0a, 0x06, 0x45,
	0x78, 0x74, 0x65, 0x6e, 0x64, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0f, 0x2e, 0x61, 0x63,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x45, 0x78, 0x74, 0x65, 0x6e, 0x64, 0x52, 0x06, 0x65, 0x78,
	0x74, 0x65, 0x6e, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x4a, 0x6f, 0x62, 0x4e, 0x75, 0x6d, 0x18, 0x10,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x4a, 0x6f, 0x62, 0x4e, 0x75, 0x6d, 0x12, 0x12, 0x0a, 0x04,
	0x43, 0x6f, 0x64, 0x65, 0x18, 0x11, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65,
	0x12, 0x14, 0x0a, 0x05, 0x49, 0x64, 0x4e, 0x75, 0x6d, 0x18, 0x12, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x05, 0x69, 0x64, 0x4e, 0x75, 0x6d, 0x12, 0x1a, 0x0a, 0x08, 0x52, 0x65, 0x61, 0x6c, 0x4e, 0x61,
	0x6d, 0x65, 0x18, 0x13, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x72, 0x65, 0x61, 0x6c, 0x4e, 0x61,
	0x6d, 0x65, 0x12, 0x1c, 0x0a, 0x09, 0x52, 0x65, 0x63, 0x65, 0x6e, 0x74, 0x49, 0x6d, 0x67, 0x18,
	0x14, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x52, 0x65, 0x63, 0x65, 0x6e, 0x74, 0x49, 0x6d, 0x67,
	0x12, 0x1e, 0x0a, 0x0a, 0x52, 0x65, 0x61, 0x6c, 0x49, 0x44, 0x49, 0x6d, 0x67, 0x41, 0x18, 0x15,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x52, 0x65, 0x61, 0x6c, 0x49, 0x44, 0x49, 0x6d, 0x67, 0x41,
	0x12, 0x1e, 0x0a, 0x0a, 0x52, 0x65, 0x61, 0x6c, 0x49, 0x44, 0x49, 0x6d, 0x67, 0x42, 0x18, 0x16,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x52, 0x65, 0x61, 0x6c, 0x49, 0x44, 0x49, 0x6d, 0x67, 0x42,
	0x12, 0x14, 0x0a, 0x05, 0x56, 0x69, 0x64, 0x65, 0x6f, 0x18, 0x17, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x05, 0x56, 0x69, 0x64, 0x65, 0x6f, 0x12, 0x14, 0x0a, 0x05, 0x49, 0x43, 0x4e, 0x75, 0x6d, 0x18,
	0x18, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x49, 0x43, 0x4e, 0x75, 0x6d, 0x12, 0x14, 0x0a, 0x05,
	0x54, 0x72, 0x61, 0x69, 0x6e, 0x18, 0x19, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x54, 0x72, 0x61,
	0x69, 0x6e, 0x12, 0x20, 0x0a, 0x0b, 0x43, 0x65, 0x72, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74,
	0x65, 0x18, 0x1a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x43, 0x65, 0x72, 0x74, 0x69, 0x66, 0x69,
	0x63, 0x61, 0x74, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x18, 0x1b,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x12, 0x2d, 0x0a, 0x08,
	0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x18, 0x1c, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11,
	0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f,
	0x72, 0x52, 0x08, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x12, 0x16, 0x0a, 0x06, 0x53,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x1d, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x12, 0x1c, 0x0a, 0x09, 0x42, 0x6c, 0x6f, 0x63, 0x6b, 0x41, 0x64, 0x64, 0x72,
	0x18, 0x1e, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x42, 0x6c, 0x6f, 0x63, 0x6b, 0x41, 0x64, 0x64,
	0x72, 0x12, 0x2d, 0x0a, 0x08, 0x70, 0x61, 0x73, 0x73, 0x70, 0x6f, 0x72, 0x74, 0x18, 0x1f, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x50, 0x61,
	0x73, 0x73, 0x70, 0x6f, 0x72, 0x74, 0x52, 0x08, 0x70, 0x61, 0x73, 0x73, 0x70, 0x6f, 0x72, 0x74,
	0x12, 0x1a, 0x0a, 0x08, 0x6c, 0x65, 0x66, 0x74, 0x44, 0x61, 0x74, 0x65, 0x18, 0x20, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x08, 0x6c, 0x65, 0x66, 0x74, 0x44, 0x61, 0x74, 0x65, 0x12, 0x33, 0x0a, 0x0a,
	0x55, 0x73, 0x65, 0x72, 0x45, 0x78, 0x74, 0x65, 0x6e, 0x64, 0x18, 0x21, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x13, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x45,
	0x78, 0x74, 0x65, 0x6e, 0x64, 0x52, 0x0a, 0x55, 0x73, 0x65, 0x72, 0x45, 0x78, 0x74, 0x65, 0x6e,
	0x64, 0x12, 0x26, 0x0a, 0x0e, 0x6e, 0x6f, 0x74, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x54, 0x65, 0x6c,
	0x4e, 0x75, 0x6d, 0x18, 0x22, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0e, 0x6e, 0x6f, 0x74, 0x43, 0x68,
	0x65, 0x63, 0x6b, 0x54, 0x65, 0x6c, 0x4e, 0x75, 0x6d, 0x12, 0x12, 0x0a, 0x04, 0x7a, 0x6f, 0x6e,
	0x65, 0x18, 0x23, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x7a, 0x6f, 0x6e, 0x65, 0x22, 0xa6, 0x04,
	0x0a, 0x0a, 0x55, 0x73, 0x65, 0x72, 0x45, 0x78, 0x74, 0x65, 0x6e, 0x64, 0x12, 0x0e, 0x0a, 0x02,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04,
	0x75, 0x75, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x75, 0x75, 0x69, 0x64,
	0x12, 0x16, 0x0a, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x44, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x44, 0x12, 0x18, 0x0a, 0x07, 0x61, 0x64, 0x64, 0x72,
	0x65, 0x73, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x64, 0x64, 0x72, 0x65,
	0x73, 0x73, 0x12, 0x1a, 0x0a, 0x08, 0x62, 0x61, 0x6e, 0x6b, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x62, 0x61, 0x6e, 0x6b, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x16,
	0x0a, 0x06, 0x62, 0x61, 0x6e, 0x6b, 0x4e, 0x6f, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06,
	0x62, 0x61, 0x6e, 0x6b, 0x4e, 0x6f, 0x12, 0x12, 0x0a, 0x04, 0x7a, 0x6f, 0x6e, 0x65, 0x18, 0x07,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x7a, 0x6f, 0x6e, 0x65, 0x12, 0x1e, 0x0a, 0x0a, 0x69, 0x73,
	0x4d, 0x61, 0x69, 0x6e, 0x6c, 0x61, 0x6e, 0x64, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0a,
	0x69, 0x73, 0x4d, 0x61, 0x69, 0x6e, 0x6c, 0x61, 0x6e, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x69, 0x73,
	0x52, 0x65, 0x61, 0x6c, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x06, 0x69, 0x73, 0x52, 0x65,
	0x61, 0x6c, 0x12, 0x1a, 0x0a, 0x08, 0x66, 0x72, 0x6f, 0x6d, 0x43, 0x6f, 0x64, 0x65, 0x18, 0x0a,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x66, 0x72, 0x6f, 0x6d, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x1a,
	0x0a, 0x08, 0x72, 0x65, 0x61, 0x6c, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x08, 0x72, 0x65, 0x61, 0x6c, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x72, 0x65,
	0x61, 0x6c, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x72, 0x65,
	0x61, 0x6c, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x69, 0x64, 0x54, 0x79, 0x70, 0x65,
	0x18, 0x0d, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x69, 0x64, 0x54, 0x79, 0x70, 0x65, 0x12, 0x12,
	0x0a, 0x04, 0x69, 0x64, 0x4e, 0x6f, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x69, 0x64,
	0x4e, 0x6f, 0x12, 0x14, 0x0a, 0x05, 0x63, 0x61, 0x72, 0x64, 0x41, 0x18, 0x0f, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x05, 0x63, 0x61, 0x72, 0x64, 0x41, 0x12, 0x14, 0x0a, 0x05, 0x63, 0x61, 0x72, 0x64,
	0x42, 0x18, 0x10, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x63, 0x61, 0x72, 0x64, 0x42, 0x12, 0x1e,
	0x0a, 0x0a, 0x70, 0x61, 0x73, 0x73, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x11, 0x20, 0x01,
	0x28, 0x0d, 0x52, 0x0a, 0x70, 0x61, 0x73, 0x73, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x2a,
	0x0a, 0x10, 0x72, 0x65, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x64, 0x53, 0x74, 0x61, 0x66, 0x66,
	0x49, 0x64, 0x18, 0x12, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x10, 0x72, 0x65, 0x63, 0x6f, 0x6d, 0x6d,
	0x65, 0x6e, 0x64, 0x53, 0x74, 0x61, 0x66, 0x66, 0x49, 0x64, 0x12, 0x24, 0x0a, 0x0d, 0x69, 0x73,
	0x52, 0x65, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x64, 0x65, 0x64, 0x18, 0x13, 0x20, 0x01, 0x28,
	0x08, 0x52, 0x0d, 0x69, 0x73, 0x52, 0x65, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x64, 0x65, 0x64,
	0x12, 0x24, 0x0a, 0x0d, 0x70, 0x61, 0x73, 0x73, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x4d, 0x73,
	0x67, 0x18, 0x14, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x70, 0x61, 0x73, 0x73, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x4d, 0x73, 0x67, 0x22, 0x8c, 0x01, 0x0a, 0x08, 0x50, 0x61, 0x73, 0x73, 0x70,
	0x6f, 0x72, 0x74, 0x12, 0x14, 0x0a, 0x05, 0x69, 0x64, 0x4e, 0x75, 0x6d, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x05, 0x69, 0x64, 0x4e, 0x75, 0x6d, 0x12, 0x1e, 0x0a, 0x0a, 0x72, 0x65, 0x61,
	0x6c, 0x49, 0x44, 0x49, 0x6d, 0x67, 0x41, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x72,
	0x65, 0x61, 0x6c, 0x49, 0x44, 0x49, 0x6d, 0x67, 0x41, 0x12, 0x1e, 0x0a, 0x0a, 0x72, 0x65, 0x61,
	0x6c, 0x49, 0x44, 0x49, 0x6d, 0x67, 0x42, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x72,
	0x65, 0x61, 0x6c, 0x49, 0x44, 0x49, 0x6d, 0x67, 0x42, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d,
	0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x16, 0x0a,
	0x06, 0x69, 0x64, 0x54, 0x79, 0x70, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x69,
	0x64, 0x54, 0x79, 0x70, 0x65, 0x22, 0xd7, 0x01, 0x0a, 0x0c, 0x4c, 0x6f, 0x67, 0x69, 0x6e, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x25, 0x0a, 0x06, 0x44, 0x6f, 0x6d, 0x61, 0x69, 0x6e,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0d, 0xe2, 0xdf, 0x1f, 0x09, 0x2a, 0x05, 0x37, 0x30,
	0x30, 0x30, 0x31, 0x58, 0x01, 0x52, 0x06, 0x64, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x12, 0x16, 0x0a,
	0x06, 0x54, 0x65, 0x6c, 0x4e, 0x75, 0x6d, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x74,
	0x65, 0x6c, 0x4e, 0x75, 0x6d, 0x12, 0x12, 0x0a, 0x04, 0x43, 0x6f, 0x64, 0x65, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x50, 0x61, 0x73,
	0x73, 0x77, 0x6f, 0x72, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x70, 0x61, 0x73,
	0x73, 0x77, 0x6f, 0x72, 0x64, 0x12, 0x0e, 0x0a, 0x02, 0x49, 0x70, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x02, 0x69, 0x70, 0x12, 0x20, 0x0a, 0x0b, 0x70, 0x61, 0x73, 0x73, 0x43, 0x68, 0x65,
	0x63, 0x6b, 0x49, 0x70, 0x18, 0x06, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0b, 0x70, 0x61, 0x73, 0x73,
	0x43, 0x68, 0x65, 0x63, 0x6b, 0x49, 0x70, 0x12, 0x12, 0x0a, 0x04, 0x66, 0x72, 0x6f, 0x6d, 0x18,
	0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x66, 0x72, 0x6f, 0x6d, 0x12, 0x12, 0x0a, 0x04, 0x7a,
	0x6f, 0x6e, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x7a, 0x6f, 0x6e, 0x65, 0x22,
	0xc2, 0x01, 0x0a, 0x09, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x36, 0x0a,
	0x0b, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x14, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x41, 0x63, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x0b, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x14, 0x0a, 0x05, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x12, 0x1d, 0x0a, 0x0c, 0x52,
	0x65, 0x66, 0x72, 0x65, 0x73, 0x68, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x07, 0x72, 0x65, 0x66, 0x72, 0x65, 0x73, 0x68, 0x12, 0x28, 0x0a, 0x0f, 0x49, 0x73,
	0x53, 0x61, 0x6d, 0x70, 0x6c, 0x65, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x08, 0x52, 0x0f, 0x69, 0x73, 0x53, 0x61, 0x6d, 0x70, 0x6c, 0x65, 0x41, 0x64, 0x64,
	0x72, 0x65, 0x73, 0x73, 0x12, 0x1e, 0x0a, 0x0a, 0x6e, 0x6f, 0x77, 0x41, 0x64, 0x64, 0x72, 0x65,
	0x73, 0x73, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x6e, 0x6f, 0x77, 0x41, 0x64, 0x64,
	0x72, 0x65, 0x73, 0x73, 0x22, 0x78, 0x0a, 0x06, 0x45, 0x78, 0x74, 0x65, 0x6e, 0x64, 0x12, 0x16,
	0x0a, 0x06, 0x4a, 0x75, 0x6d, 0x70, 0x54, 0x6f, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06,
	0x6a, 0x75, 0x6d, 0x70, 0x54, 0x6f, 0x12, 0x12, 0x0a, 0x04, 0x4c, 0x61, 0x6e, 0x67, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6c, 0x61, 0x6e, 0x67, 0x12, 0x18, 0x0a, 0x07, 0x43, 0x61,
	0x6e, 0x53, 0x63, 0x61, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x63, 0x61, 0x6e,
	0x53, 0x63, 0x61, 0x6e, 0x12, 0x28, 0x0a, 0x0f, 0x52, 0x65, 0x73, 0x6f, 0x6c, 0x75, 0x74, 0x69,
	0x6f, 0x6e, 0x52, 0x61, 0x74, 0x69, 0x6f, 0x18, 0x04, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0f, 0x72,
	0x65, 0x73, 0x6f, 0x6c, 0x75, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x61, 0x74, 0x69, 0x6f, 0x22, 0x30,
	0x0a, 0x0a, 0x44, 0x65, 0x70, 0x61, 0x72, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x12, 0x0e, 0x0a, 0x02,
	0x49, 0x44, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x02, 0x49, 0x44, 0x12, 0x12, 0x0a, 0x04,
	0x4e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65,
	0x22, 0xf2, 0x0a, 0x0a, 0x0b, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x49, 0x6e, 0x66, 0x6f,
	0x12, 0x0e, 0x0a, 0x02, 0x49, 0x44, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x02, 0x69, 0x64,
	0x12, 0x18, 0x0a, 0x07, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x07, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x1a, 0x0a, 0x08, 0x4e, 0x69,
	0x63, 0x6b, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x6e, 0x69,
	0x63, 0x6b, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x54, 0x79, 0x70, 0x65, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x54, 0x65,
	0x6c, 0x4e, 0x75, 0x6d, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x74, 0x65, 0x6c, 0x4e,
	0x75, 0x6d, 0x12, 0x16, 0x0a, 0x06, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x06, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x16, 0x0a, 0x06, 0x41, 0x76,
	0x61, 0x74, 0x61, 0x72, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x61, 0x76, 0x61, 0x74,
	0x61, 0x72, 0x12, 0x1a, 0x0a, 0x08, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x41, 0x74, 0x18, 0x08,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x41, 0x74, 0x12, 0x1e,
	0x0a, 0x0a, 0x52, 0x65, 0x61, 0x6c, 0x4e, 0x61, 0x6d, 0x65, 0x49, 0x44, 0x18, 0x09, 0x20, 0x01,
	0x28, 0x04, 0x52, 0x0a, 0x72, 0x65, 0x61, 0x6c, 0x4e, 0x61, 0x6d, 0x65, 0x49, 0x44, 0x12, 0x1a,
	0x0a, 0x08, 0x52, 0x65, 0x61, 0x6c, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x08, 0x72, 0x65, 0x61, 0x6c, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x49, 0x44,
	0x4e, 0x75, 0x6d, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x69, 0x44, 0x4e, 0x75, 0x6d,
	0x12, 0x24, 0x0a, 0x0d, 0x4d, 0x6e, 0x65, 0x6d, 0x6f, 0x6e, 0x69, 0x63, 0x57, 0x6f, 0x72, 0x64,
	0x73, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x6d, 0x6e, 0x65, 0x6d, 0x6f, 0x6e, 0x69,
	0x63, 0x57, 0x6f, 0x72, 0x64, 0x73, 0x12, 0x22, 0x0a, 0x0c, 0x49, 0x73, 0x4e, 0x65, 0x65, 0x64,
	0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0c, 0x69, 0x73,
	0x4e, 0x65, 0x65, 0x64, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x12, 0x1c, 0x0a, 0x09, 0x45, 0x6e,
	0x74, 0x65, 0x72, 0x44, 0x61, 0x74, 0x65, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x65,
	0x6e, 0x74, 0x65, 0x72, 0x44, 0x61, 0x74, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x57, 0x6f, 0x72, 0x6b,
	0x59, 0x65, 0x61, 0x72, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x02, 0x52, 0x08, 0x77, 0x6f, 0x72, 0x6b,
	0x59, 0x65, 0x61, 0x72, 0x12, 0x16, 0x0a, 0x06, 0x44, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x18, 0x10,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x64, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x12, 0x27, 0x0a, 0x06,
	0x45, 0x78, 0x74, 0x65, 0x6e, 0x64, 0x18, 0x11, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0f, 0x2e, 0x61,
	0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x45, 0x78, 0x74, 0x65, 0x6e, 0x64, 0x52, 0x06, 0x65,
	0x78, 0x74, 0x65, 0x6e, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x4a, 0x6f, 0x62, 0x4e, 0x75, 0x6d, 0x18,
	0x12, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x6a, 0x6f, 0x62, 0x4e, 0x75, 0x6d, 0x12, 0x1d, 0x0a,
	0x09, 0x42, 0x69, 0x72, 0x74, 0x68, 0x44, 0x61, 0x74, 0x65, 0x18, 0x13, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0a, 0x62, 0x69, 0x72, 0x74, 0x68, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x12, 0x10, 0x0a, 0x03,
	0x41, 0x67, 0x65, 0x18, 0x14, 0x20, 0x01, 0x28, 0x04, 0x52, 0x03, 0x61, 0x67, 0x65, 0x12, 0x10,
	0x0a, 0x03, 0x53, 0x65, 0x78, 0x18, 0x15, 0x20, 0x01, 0x28, 0x04, 0x52, 0x03, 0x73, 0x65, 0x78,
	0x12, 0x14, 0x0a, 0x05, 0x54, 0x69, 0x74, 0x6c, 0x65, 0x18, 0x16, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x12, 0x35, 0x0a, 0x0b, 0x44, 0x65, 0x70, 0x61, 0x72, 0x74,
	0x6d, 0x65, 0x6e, 0x74, 0x73, 0x18, 0x17, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x61, 0x63,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x44, 0x65, 0x70, 0x61, 0x72, 0x74, 0x6d, 0x65, 0x6e, 0x74,
	0x52, 0x0b, 0x64, 0x65, 0x70, 0x61, 0x72, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x12, 0x0e, 0x0a,
	0x02, 0x49, 0x70, 0x18, 0x18, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x70, 0x12, 0x1c, 0x0a,
	0x09, 0x4c, 0x6f, 0x67, 0x69, 0x6e, 0x44, 0x61, 0x74, 0x65, 0x18, 0x19, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x09, 0x6c, 0x6f, 0x67, 0x69, 0x6e, 0x44, 0x61, 0x74, 0x65, 0x12, 0x26, 0x0a, 0x0e, 0x49,
	0x6e, 0x76, 0x69, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x6f, 0x64, 0x65, 0x18, 0x1a, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0e, 0x69, 0x6e, 0x76, 0x69, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x43,
	0x6f, 0x64, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x4e, 0x6f, 0x77, 0x4c, 0x6f, 0x67, 0x49, 0x64, 0x18,
	0x1b, 0x20, 0x01, 0x28, 0x04, 0x52, 0x08, 0x6e, 0x6f, 0x77, 0x4c, 0x6f, 0x67, 0x49, 0x64, 0x12,
	0x18, 0x0a, 0x07, 0x43, 0x61, 0x6e, 0x53, 0x63, 0x61, 0x6e, 0x18, 0x1c, 0x20, 0x01, 0x28, 0x08,
	0x52, 0x07, 0x63, 0x61, 0x6e, 0x53, 0x63, 0x61, 0x6e, 0x12, 0x1a, 0x0a, 0x08, 0x4c, 0x65, 0x66,
	0x74, 0x44, 0x61, 0x74, 0x65, 0x18, 0x1d, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x6c, 0x65, 0x66,
	0x74, 0x44, 0x61, 0x74, 0x65, 0x12, 0x33, 0x0a, 0x09, 0x50, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f,
	0x6e, 0x73, 0x18, 0x1e, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x2e, 0x50, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x55, 0x73, 0x65, 0x72, 0x52,
	0x09, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x16, 0x0a, 0x06, 0x52, 0x65,
	0x6d, 0x61, 0x72, 0x6b, 0x18, 0x1f, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x72, 0x65, 0x6d, 0x61,
	0x72, 0x6b, 0x12, 0x1c, 0x0a, 0x09, 0x72, 0x65, 0x63, 0x65, 0x6e, 0x74, 0x49, 0x6d, 0x67, 0x18,
	0x20, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x72, 0x65, 0x63, 0x65, 0x6e, 0x74, 0x49, 0x6d, 0x67,
	0x12, 0x2a, 0x0a, 0x06, 0x63, 0x6c, 0x6f, 0x63, 0x6b, 0x73, 0x18, 0x21, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x12, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x43, 0x6c, 0x6f, 0x63, 0x6b,
	0x55, 0x73, 0x65, 0x72, 0x52, 0x06, 0x63, 0x6c, 0x6f, 0x63, 0x6b, 0x73, 0x12, 0x20, 0x0a, 0x0b,
	0x6d, 0x61, 0x69, 0x6c, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x22, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0b, 0x6d, 0x61, 0x69, 0x6c, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x14,
	0x0a, 0x05, 0x49, 0x43, 0x4e, 0x75, 0x6d, 0x18, 0x23, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x69,
	0x63, 0x4e, 0x75, 0x6d, 0x12, 0x20, 0x0a, 0x0b, 0x65, 0x6e, 0x67, 0x6c, 0x69, 0x73, 0x68, 0x4e,
	0x61, 0x6d, 0x65, 0x18, 0x24, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x65, 0x6e, 0x67, 0x6c, 0x69,
	0x73, 0x68, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x54, 0x72, 0x61, 0x69, 0x6e, 0x18,
	0x25, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x74, 0x72, 0x61, 0x69, 0x6e, 0x12, 0x20, 0x0a, 0x0b,
	0x43, 0x65, 0x72, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x65, 0x18, 0x26, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0b, 0x63, 0x65, 0x72, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x65, 0x12, 0x35,
	0x0a, 0x0b, 0x54, 0x72, 0x61, 0x69, 0x6e, 0x56, 0x69, 0x64, 0x65, 0x6f, 0x73, 0x18, 0x27, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x74, 0x72,
	0x61, 0x69, 0x6e, 0x56, 0x69, 0x64, 0x65, 0x6f, 0x52, 0x0b, 0x74, 0x72, 0x61, 0x69, 0x6e, 0x56,
	0x69, 0x64, 0x65, 0x6f, 0x73, 0x12, 0x2d, 0x0a, 0x08, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f,
	0x72, 0x18, 0x28, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x2e, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x52, 0x08, 0x6f, 0x70, 0x65, 0x72,
	0x61, 0x74, 0x6f, 0x72, 0x12, 0x1c, 0x0a, 0x09, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x41,
	0x74, 0x18, 0x29, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64,
	0x41, 0x74, 0x12, 0x22, 0x0a, 0x0c, 0x53, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x79, 0x43, 0x6f,
	0x64, 0x65, 0x18, 0x2a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x73, 0x65, 0x63, 0x75, 0x72, 0x69,
	0x74, 0x79, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x1c, 0x0a, 0x09, 0x42, 0x6c, 0x6f, 0x63, 0x6b, 0x41,
	0x64, 0x64, 0x72, 0x18, 0x2b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x62, 0x6c, 0x6f, 0x63, 0x6b,
	0x41, 0x64, 0x64, 0x72, 0x12, 0x1a, 0x0a, 0x08, 0x4c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65,
	0x18, 0x2c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x6c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65,
	0x12, 0x33, 0x0a, 0x0a, 0x75, 0x73, 0x65, 0x72, 0x45, 0x78, 0x74, 0x65, 0x6e, 0x64, 0x18, 0x2d,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x55,
	0x73, 0x65, 0x72, 0x45, 0x78, 0x74, 0x65, 0x6e, 0x64, 0x52, 0x0a, 0x75, 0x73, 0x65, 0x72, 0x45,
	0x78, 0x74, 0x65, 0x6e, 0x64, 0x22, 0xd4, 0x03, 0x0a, 0x0a, 0x55, 0x73, 0x65, 0x72, 0x49, 0x6e,
	0x66, 0x6f, 0x56, 0x32, 0x12, 0x0e, 0x0a, 0x02, 0x49, 0x44, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04,
	0x52, 0x02, 0x49, 0x44, 0x12, 0x18, 0x0a, 0x07, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x1a,
	0x0a, 0x08, 0x4e, 0x69, 0x63, 0x6b, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x08, 0x4e, 0x69, 0x63, 0x6b, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x54, 0x65,
	0x6c, 0x4e, 0x75, 0x6d, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x54, 0x65, 0x6c, 0x4e,
	0x75, 0x6d, 0x12, 0x16, 0x0a, 0x06, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x06, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x16, 0x0a, 0x06, 0x41, 0x76,
	0x61, 0x74, 0x61, 0x72, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x41, 0x76, 0x61, 0x74,
	0x61, 0x72, 0x12, 0x1a, 0x0a, 0x08, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x41, 0x74, 0x18, 0x07,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x41, 0x74, 0x12, 0x1a,
	0x0a, 0x08, 0x52, 0x65, 0x61, 0x6c, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x08, 0x52, 0x65, 0x61, 0x6c, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x49, 0x44,
	0x4e, 0x75, 0x6d, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x49, 0x44, 0x4e, 0x75, 0x6d,
	0x12, 0x1c, 0x0a, 0x09, 0x45, 0x6e, 0x74, 0x65, 0x72, 0x44, 0x61, 0x74, 0x65, 0x18, 0x0a, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x09, 0x45, 0x6e, 0x74, 0x65, 0x72, 0x44, 0x61, 0x74, 0x65, 0x12, 0x27,
	0x0a, 0x06, 0x45, 0x78, 0x74, 0x65, 0x6e, 0x64, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0f,
	0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x45, 0x78, 0x74, 0x65, 0x6e, 0x64, 0x52,
	0x06, 0x45, 0x78, 0x74, 0x65, 0x6e, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x4a, 0x6f, 0x62, 0x4e, 0x75,
	0x6d, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x4a, 0x6f, 0x62, 0x4e, 0x75, 0x6d, 0x12,
	0x1c, 0x0a, 0x09, 0x72, 0x65, 0x63, 0x65, 0x6e, 0x74, 0x49, 0x6d, 0x67, 0x18, 0x0d, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x09, 0x72, 0x65, 0x63, 0x65, 0x6e, 0x74, 0x49, 0x6d, 0x67, 0x12, 0x20, 0x0a,
	0x0b, 0x6d, 0x61, 0x69, 0x6c, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x0e, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0b, 0x6d, 0x61, 0x69, 0x6c, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x12,
	0x2d, 0x0a, 0x08, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x18, 0x0f, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x11, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x4f, 0x70, 0x65, 0x72,
	0x61, 0x74, 0x6f, 0x72, 0x52, 0x08, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x12, 0x1c,
	0x0a, 0x09, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x18, 0x10, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x09, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x22, 0x61, 0x0a, 0x13,
	0x52, 0x65, 0x66, 0x72, 0x65, 0x73, 0x68, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x12, 0x22, 0x0a, 0x0c, 0x72, 0x65, 0x66, 0x72, 0x65, 0x73, 0x68, 0x54, 0x6f,
	0x6b, 0x65, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x72, 0x65, 0x66, 0x72, 0x65,
	0x73, 0x68, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x12, 0x16, 0x0a, 0x06, 0x64, 0x6f, 0x6d, 0x61, 0x69,
	0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x64, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x12,
	0x0e, 0x0a, 0x02, 0x69, 0x70, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x70, 0x22,
	0xfa, 0x01, 0x0a, 0x0c, 0x50, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x55, 0x73, 0x65, 0x72,
	0x12, 0x1e, 0x0a, 0x0a, 0x50, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x44, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x04, 0x52, 0x0a, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x44,
	0x12, 0x22, 0x0a, 0x0c, 0x50, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x4e, 0x61, 0x6d, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e,
	0x4e, 0x61, 0x6d, 0x65, 0x12, 0x22, 0x0a, 0x0c, 0x44, 0x65, 0x70, 0x61, 0x72, 0x74, 0x6d, 0x65,
	0x6e, 0x74, 0x49, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0c, 0x64, 0x65, 0x70, 0x61,
	0x72, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x26, 0x0a, 0x0e, 0x44, 0x65, 0x70, 0x61,
	0x72, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x43, 0x6f, 0x64, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0e, 0x64, 0x65, 0x70, 0x61, 0x72, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x43, 0x6f, 0x64, 0x65,
	0x12, 0x26, 0x0a, 0x0e, 0x44, 0x65, 0x70, 0x61, 0x72, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x4e, 0x61,
	0x6d, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x64, 0x65, 0x70, 0x61, 0x72, 0x74,
	0x6d, 0x65, 0x6e, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x55, 0x73, 0x65, 0x72,
	0x49, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x04, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64,
	0x12, 0x1a, 0x0a, 0x08, 0x55, 0x73, 0x65, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x07, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x08, 0x75, 0x73, 0x65, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x22, 0x46, 0x0a, 0x14,
	0x4a, 0x6f, 0x62, 0x4e, 0x75, 0x6d, 0x47, 0x65, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x12, 0x16, 0x0a, 0x06, 0x6a, 0x6f, 0x62, 0x4e, 0x75, 0x6d, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x6a, 0x6f, 0x62, 0x4e, 0x75, 0x6d, 0x12, 0x16, 0x0a, 0x06,
	0x64, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x64, 0x6f,
	0x6d, 0x61, 0x69, 0x6e, 0x22, 0x78, 0x0a, 0x18, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x43, 0x6c,
	0x6f, 0x63, 0x6b, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x12, 0x1c, 0x0a, 0x09, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x4e, 0x75, 0x6d, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x09, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x4e, 0x75, 0x6d, 0x12, 0x1e,
	0x0a, 0x0a, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0a, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1e,
	0x0a, 0x0a, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x53, 0x69, 0x74, 0x65, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0a, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x53, 0x69, 0x74, 0x65, 0x22, 0x88,
	0x01, 0x0a, 0x18, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x43, 0x6c, 0x6f, 0x63, 0x6b, 0x44, 0x65,
	0x76, 0x69, 0x63, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x0e, 0x0a, 0x02, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x02, 0x69, 0x64, 0x12, 0x1c, 0x0a, 0x09, 0x64,
	0x65, 0x76, 0x69, 0x63, 0x65, 0x4e, 0x75, 0x6d, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09,
	0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x4e, 0x75, 0x6d, 0x12, 0x1e, 0x0a, 0x0a, 0x64, 0x65, 0x76,
	0x69, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x64,
	0x65, 0x76, 0x69, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1e, 0x0a, 0x0a, 0x64, 0x65, 0x76,
	0x69, 0x63, 0x65, 0x53, 0x69, 0x74, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x64,
	0x65, 0x76, 0x69, 0x63, 0x65, 0x53, 0x69, 0x74, 0x65, 0x22, 0x25, 0x0a, 0x13, 0x43, 0x6c, 0x6f,
	0x63, 0x6b, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x02, 0x69, 0x64,
	0x22, 0x2a, 0x0a, 0x18, 0x52, 0x65, 0x6d, 0x6f, 0x76, 0x65, 0x43, 0x6c, 0x6f, 0x63, 0x6b, 0x44,
	0x65, 0x76, 0x69, 0x63, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x0e, 0x0a, 0x02,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x02, 0x69, 0x64, 0x22, 0xc8, 0x01, 0x0a,
	0x16, 0x43, 0x6c, 0x6f, 0x63, 0x6b, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x4c, 0x69, 0x73, 0x74,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x04, 0x52, 0x02, 0x69, 0x64, 0x12, 0x1e, 0x0a, 0x0a, 0x64, 0x65, 0x76, 0x69, 0x63,
	0x65, 0x53, 0x69, 0x74, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x64, 0x65, 0x76,
	0x69, 0x63, 0x65, 0x53, 0x69, 0x74, 0x65, 0x12, 0x1c, 0x0a, 0x09, 0x64, 0x65, 0x76, 0x69, 0x63,
	0x65, 0x4e, 0x75, 0x6d, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x64, 0x65, 0x76, 0x69,
	0x63, 0x65, 0x4e, 0x75, 0x6d, 0x12, 0x1e, 0x0a, 0x0a, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x4e,
	0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x64, 0x65, 0x76, 0x69, 0x63,
	0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x70, 0x61, 0x67, 0x65, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x04, 0x52, 0x04, 0x70, 0x61, 0x67, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x61, 0x67,
	0x65, 0x53, 0x69, 0x7a, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x04, 0x52, 0x08, 0x70, 0x61, 0x67,
	0x65, 0x53, 0x69, 0x7a, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x69, 0x64, 0x73, 0x18, 0x07, 0x20, 0x03,
	0x28, 0x04, 0x52, 0x03, 0x69, 0x64, 0x73, 0x22, 0x5d, 0x0a, 0x17, 0x43, 0x6c, 0x6f, 0x63, 0x6b,
	0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x04, 0x52, 0x05, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x2c, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61,
	0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x2e, 0x43, 0x6c, 0x6f, 0x63, 0x6b, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x49, 0x6e, 0x66, 0x6f,
	0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x22, 0xf3, 0x01, 0x0a, 0x09, 0x43, 0x6c, 0x6f, 0x63, 0x6b,
	0x55, 0x73, 0x65, 0x72, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04,
	0x52, 0x02, 0x69, 0x64, 0x12, 0x1c, 0x0a, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41,
	0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64,
	0x41, 0x74, 0x12, 0x1c, 0x0a, 0x09, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74,
	0x12, 0x1c, 0x0a, 0x09, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x64, 0x41, 0x74, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x09, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x1a,
	0x0a, 0x08, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x49, 0x44, 0x18, 0x05, 0x20, 0x01, 0x28, 0x04,
	0x52, 0x08, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x49, 0x44, 0x12, 0x16, 0x0a, 0x06, 0x75, 0x73,
	0x65, 0x72, 0x49, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x04, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72,
	0x49, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x07, 0x20, 0x01,
	0x28, 0x04, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x30, 0x0a, 0x06, 0x64, 0x65,
	0x76, 0x69, 0x63, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x61, 0x63, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x43, 0x6c, 0x6f, 0x63, 0x6b, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65,
	0x49, 0x6e, 0x66, 0x6f, 0x52, 0x06, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x22, 0xfc, 0x01, 0x0a,
	0x0f, 0x43, 0x6c, 0x6f, 0x63, 0x6b, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x49, 0x6e, 0x66, 0x6f,
	0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x02, 0x69, 0x64,
	0x12, 0x1a, 0x0a, 0x08, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x41, 0x74, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x08, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x41, 0x74, 0x12, 0x1a, 0x0a, 0x08,
	0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x41, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08,
	0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x41, 0x74, 0x12, 0x1e, 0x0a, 0x0a, 0x64, 0x65, 0x76, 0x69,
	0x63, 0x65, 0x53, 0x69, 0x74, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x64, 0x65,
	0x76, 0x69, 0x63, 0x65, 0x53, 0x69, 0x74, 0x65, 0x12, 0x1c, 0x0a, 0x09, 0x64, 0x65, 0x76, 0x69,
	0x63, 0x65, 0x4e, 0x75, 0x6d, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x64, 0x65, 0x76,
	0x69, 0x63, 0x65, 0x4e, 0x75, 0x6d, 0x12, 0x1e, 0x0a, 0x0a, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65,
	0x4e, 0x61, 0x6d, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x64, 0x65, 0x76, 0x69,
	0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x75, 0x73, 0x65, 0x72, 0x4e, 0x75,
	0x6d, 0x18, 0x07, 0x20, 0x01, 0x28, 0x04, 0x52, 0x07, 0x75, 0x73, 0x65, 0x72, 0x4e, 0x75, 0x6d,
	0x12, 0x29, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x08, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x15,
	0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x43, 0x6c, 0x6f, 0x63, 0x6b, 0x55, 0x73,
	0x65, 0x72, 0x52, 0x65, 0x6c, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x22, 0x5a, 0x0a, 0x17, 0x43,
	0x6c, 0x6f, 0x63, 0x6b, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x05, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x29, 0x0a, 0x04,
	0x64, 0x61, 0x74, 0x61, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x61, 0x63, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x43, 0x6c, 0x6f, 0x63, 0x6b, 0x55, 0x73, 0x65, 0x72, 0x52, 0x65,
	0x6c, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x22, 0xa0, 0x01, 0x0a, 0x0c, 0x43, 0x6c, 0x6f, 0x63,
	0x6b, 0x55, 0x73, 0x65, 0x72, 0x52, 0x65, 0x6c, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x04, 0x52, 0x02, 0x69, 0x64, 0x12, 0x1a, 0x0a, 0x08, 0x63, 0x72, 0x65, 0x61,
	0x74, 0x65, 0x41, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x63, 0x72, 0x65, 0x61,
	0x74, 0x65, 0x41, 0x74, 0x12, 0x1a, 0x0a, 0x08, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x41, 0x74,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x41, 0x74,
	0x12, 0x1a, 0x0a, 0x08, 0x6e, 0x69, 0x63, 0x6b, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x08, 0x6e, 0x69, 0x63, 0x6b, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x16, 0x0a, 0x06,
	0x6a, 0x6f, 0x62, 0x4e, 0x75, 0x6d, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x6a, 0x6f,
	0x62, 0x4e, 0x75, 0x6d, 0x12, 0x14, 0x0a, 0x05, 0x69, 0x63, 0x4e, 0x75, 0x6d, 0x18, 0x06, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x05, 0x69, 0x63, 0x4e, 0x75, 0x6d, 0x22, 0x58, 0x0a, 0x16, 0x43, 0x6c,
	0x6f, 0x63, 0x6b, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04,
	0x52, 0x02, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x70, 0x61, 0x67, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x04, 0x52, 0x04, 0x70, 0x61, 0x67, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x61, 0x67, 0x65,
	0x53, 0x69, 0x7a, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x04, 0x52, 0x08, 0x70, 0x61, 0x67, 0x65,
	0x53, 0x69, 0x7a, 0x65, 0x22, 0x5b, 0x0a, 0x15, 0x43, 0x6c, 0x6f, 0x63, 0x6b, 0x42, 0x61, 0x74,
	0x63, 0x68, 0x42, 0x69, 0x6e, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x0e, 0x0a,
	0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x02, 0x69, 0x64, 0x12, 0x16, 0x0a,
	0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x18, 0x02, 0x20, 0x03, 0x28, 0x04, 0x52, 0x06, 0x75,
	0x73, 0x65, 0x72, 0x49, 0x64, 0x12, 0x1a, 0x0a, 0x08, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x49,
	0x64, 0x18, 0x03, 0x20, 0x03, 0x28, 0x04, 0x52, 0x08, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x49,
	0x64, 0x22, 0x4b, 0x0a, 0x16, 0x43, 0x6c, 0x6f, 0x63, 0x6b, 0x42, 0x61, 0x74, 0x63, 0x68, 0x4c,
	0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x31, 0x0a, 0x04, 0x64,
	0x61, 0x74, 0x61, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x61, 0x63, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x2e, 0x43, 0x6c, 0x6f, 0x63, 0x6b, 0x55, 0x73, 0x65, 0x72, 0x44, 0x65, 0x76,
	0x69, 0x63, 0x65, 0x42, 0x61, 0x74, 0x63, 0x68, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x22, 0xc0,
	0x01, 0x0a, 0x14, 0x43, 0x6c, 0x6f, 0x63, 0x6b, 0x55, 0x73, 0x65, 0x72, 0x44, 0x65, 0x76, 0x69,
	0x63, 0x65, 0x42, 0x61, 0x74, 0x63, 0x68, 0x12, 0x16, 0x0a, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x12,
	0x1a, 0x0a, 0x08, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x49, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x04, 0x52, 0x08, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x49, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x4a,
	0x6f, 0x62, 0x4e, 0x75, 0x6d, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x4a, 0x6f, 0x62,
	0x4e, 0x75, 0x6d, 0x12, 0x1c, 0x0a, 0x09, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x4e, 0x75, 0x6d,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x4e, 0x75,
	0x6d, 0x12, 0x1e, 0x0a, 0x0a, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x4e, 0x61, 0x6d,
	0x65, 0x12, 0x1e, 0x0a, 0x0a, 0x77, 0x6f, 0x72, 0x6b, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18,
	0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x77, 0x6f, 0x72, 0x6b, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x22, 0x98, 0x04, 0x0a, 0x0c, 0x43, 0x6c, 0x6f, 0x63, 0x6b, 0x4c, 0x6f, 0x67, 0x49, 0x6e,
	0x66, 0x6f, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x02,
	0x69, 0x64, 0x12, 0x0e, 0x0a, 0x02, 0x73, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02,
	0x73, 0x6e, 0x12, 0x16, 0x0a, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x12, 0x1c, 0x0a, 0x09, 0x72, 0x65,
	0x63, 0x6f, 0x67, 0x54, 0x79, 0x70, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x72,
	0x65, 0x63, 0x6f, 0x67, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1c, 0x0a, 0x09, 0x72, 0x65, 0x63, 0x6f,
	0x67, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x72, 0x65, 0x63,
	0x6f, 0x67, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x67, 0x65, 0x6e, 0x64, 0x65, 0x72,
	0x18, 0x06, 0x20, 0x01, 0x28, 0x02, 0x52, 0x06, 0x67, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x12, 0x14,
	0x0a, 0x05, 0x70, 0x68, 0x6f, 0x74, 0x6f, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x70,
	0x68, 0x6f, 0x74, 0x6f, 0x12, 0x1e, 0x0a, 0x0a, 0x70, 0x61, 0x73, 0x73, 0x53, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x18, 0x08, 0x20, 0x01, 0x28, 0x02, 0x52, 0x0a, 0x70, 0x61, 0x73, 0x73, 0x53, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x12, 0x1a, 0x0a, 0x08, 0x75, 0x73, 0x65, 0x72, 0x4e, 0x61, 0x6d, 0x65,
	0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x75, 0x73, 0x65, 0x72, 0x4e, 0x61, 0x6d, 0x65,
	0x12, 0x1a, 0x0a, 0x08, 0x75, 0x73, 0x65, 0x72, 0x54, 0x79, 0x70, 0x65, 0x18, 0x0a, 0x20, 0x01,
	0x28, 0x02, 0x52, 0x08, 0x75, 0x73, 0x65, 0x72, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1e, 0x0a, 0x0a,
	0x63, 0x6f, 0x6e, 0x66, 0x69, 0x64, 0x65, 0x6e, 0x63, 0x65, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0a, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x64, 0x65, 0x6e, 0x63, 0x65, 0x12, 0x22, 0x0a, 0x0c,
	0x72, 0x65, 0x66, 0x6c, 0x65, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x18, 0x0c, 0x20, 0x01,
	0x28, 0x02, 0x52, 0x0c, 0x72, 0x65, 0x66, 0x6c, 0x65, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79,
	0x12, 0x1e, 0x0a, 0x0a, 0x63, 0x61, 0x72, 0x64, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x0d,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x63, 0x61, 0x72, 0x64, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72,
	0x12, 0x1a, 0x0a, 0x08, 0x70, 0x61, 0x73, 0x73, 0x57, 0x6f, 0x72, 0x64, 0x18, 0x0e, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x08, 0x70, 0x61, 0x73, 0x73, 0x57, 0x6f, 0x72, 0x64, 0x12, 0x16, 0x0a, 0x06,
	0x71, 0x72, 0x43, 0x6f, 0x64, 0x65, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x71, 0x72,
	0x43, 0x6f, 0x64, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x74, 0x65, 0x6c, 0x18, 0x10, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x03, 0x74, 0x65, 0x6c, 0x12, 0x20, 0x0a, 0x0b, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e,
	0x56, 0x69, 0x73, 0x69, 0x74, 0x18, 0x11, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x72, 0x65, 0x61,
	0x73, 0x6f, 0x6e, 0x56, 0x69, 0x73, 0x69, 0x74, 0x12, 0x20, 0x0a, 0x0b, 0x72, 0x65, 0x63, 0x65,
	0x69, 0x76, 0x65, 0x72, 0x54, 0x65, 0x6c, 0x18, 0x12, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x72,
	0x65, 0x63, 0x65, 0x69, 0x76, 0x65, 0x72, 0x54, 0x65, 0x6c, 0x12, 0x20, 0x0a, 0x0b, 0x6e, 0x75,
	0x6d, 0x4f, 0x66, 0x50, 0x65, 0x6f, 0x70, 0x6c, 0x65, 0x18, 0x13, 0x20, 0x01, 0x28, 0x04, 0x52,
	0x0b, 0x6e, 0x75, 0x6d, 0x4f, 0x66, 0x50, 0x65, 0x6f, 0x70, 0x6c, 0x65, 0x22, 0xdb, 0x01, 0x0a,
	0x0b, 0x43, 0x6c, 0x6f, 0x63, 0x6b, 0x4c, 0x6f, 0x67, 0x52, 0x65, 0x71, 0x12, 0x0e, 0x0a, 0x02,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x02, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04,
	0x70, 0x61, 0x67, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x04, 0x52, 0x04, 0x70, 0x61, 0x67, 0x65,
	0x12, 0x1a, 0x0a, 0x08, 0x70, 0x61, 0x67, 0x65, 0x53, 0x69, 0x7a, 0x65, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x04, 0x52, 0x08, 0x70, 0x61, 0x67, 0x65, 0x53, 0x69, 0x7a, 0x65, 0x12, 0x1a, 0x0a, 0x08,
	0x75, 0x73, 0x65, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08,
	0x75, 0x73, 0x65, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1c, 0x0a, 0x09, 0x72, 0x65, 0x63, 0x6f,
	0x67, 0x54, 0x79, 0x70, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x72, 0x65, 0x63,
	0x6f, 0x67, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1c, 0x0a, 0x09, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65,
	0x4e, 0x75, 0x6d, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x64, 0x65, 0x76, 0x69, 0x63,
	0x65, 0x4e, 0x75, 0x6d, 0x12, 0x1c, 0x0a, 0x09, 0x72, 0x65, 0x63, 0x6f, 0x67, 0x44, 0x61, 0x74,
	0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x72, 0x65, 0x63, 0x6f, 0x67, 0x44, 0x61,
	0x74, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x18, 0x08, 0x20, 0x01,
	0x28, 0x04, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x22, 0x57, 0x0a, 0x14, 0x43, 0x6c,
	0x6f, 0x63, 0x6b, 0x4c, 0x6f, 0x67, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x12, 0x29, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x15, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x43, 0x6c, 0x6f, 0x63, 0x6b,
	0x4c, 0x6f, 0x67, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x12, 0x14, 0x0a,
	0x05, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x04, 0x52, 0x05, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x22, 0xbe, 0x01, 0x0a, 0x14, 0x53, 0x65, 0x6e, 0x64, 0x4e, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x4d, 0x73, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x25, 0x0a, 0x06,
	0x44, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0d, 0xe2, 0xdf,
	0x1f, 0x09, 0x2a, 0x05, 0x37, 0x30, 0x30, 0x30, 0x31, 0x58, 0x01, 0x52, 0x06, 0x64, 0x6f, 0x6d,
	0x61, 0x69, 0x6e, 0x12, 0x25, 0x0a, 0x06, 0x54, 0x65, 0x6c, 0x4e, 0x75, 0x6d, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x42, 0x0d, 0xe2, 0xdf, 0x1f, 0x09, 0x2a, 0x05, 0x37, 0x30, 0x30, 0x30, 0x31,
	0x58, 0x01, 0x52, 0x06, 0x74, 0x65, 0x6c, 0x4e, 0x75, 0x6d, 0x12, 0x18, 0x0a, 0x07, 0x50, 0x72,
	0x6f, 0x6a, 0x65, 0x63, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x70, 0x72, 0x6f,
	0x6a, 0x65, 0x63, 0x74, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x69, 0x67, 0x6e, 0x4e, 0x6f, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x0d, 0x52, 0x06, 0x73, 0x69, 0x67, 0x6e, 0x4e, 0x6f, 0x12, 0x10, 0x0a, 0x03,
	0x6d, 0x49, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x03, 0x6d, 0x49, 0x64, 0x12, 0x14,
	0x0a, 0x05, 0x73, 0x63, 0x6f, 0x70, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x73,
	0x63, 0x6f, 0x70, 0x65, 0x22, 0x6a, 0x0a, 0x15, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x4c, 0x61,
	0x6e, 0x67, 0x75, 0x61, 0x67, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x25, 0x0a,
	0x06, 0x64, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0d, 0xe2,
	0xdf, 0x1f, 0x09, 0x2a, 0x05, 0x37, 0x30, 0x30, 0x30, 0x31, 0x58, 0x01, 0x52, 0x06, 0x64, 0x6f,
	0x6d, 0x61, 0x69, 0x6e, 0x12, 0x0e, 0x0a, 0x02, 0x49, 0x44, 0x18, 0x02, 0x20, 0x01, 0x28, 0x04,
	0x52, 0x02, 0x49, 0x44, 0x12, 0x1a, 0x0a, 0x08, 0x6c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x6c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65,
	0x22, 0x5c, 0x0a, 0x16, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x4c, 0x61, 0x6e, 0x67, 0x75, 0x61,
	0x67, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x0e, 0x0a, 0x02, 0x49, 0x44,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x02, 0x49, 0x44, 0x12, 0x16, 0x0a, 0x06, 0x74, 0x65,
	0x6c, 0x4e, 0x75, 0x6d, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x74, 0x65, 0x6c, 0x4e,
	0x75, 0x6d, 0x12, 0x1a, 0x0a, 0x08, 0x6c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x6c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65, 0x22, 0xde,
	0x01, 0x0a, 0x1c, 0x47, 0x65, 0x6e, 0x65, 0x72, 0x61, 0x74, 0x65, 0x53, 0x6c, 0x69, 0x64, 0x65,
	0x72, 0x43, 0x61, 0x70, 0x74, 0x63, 0x68, 0x61, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12,
	0x20, 0x0a, 0x0b, 0x63, 0x61, 0x6e, 0x76, 0x61, 0x73, 0x57, 0x69, 0x64, 0x74, 0x68, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x04, 0x52, 0x0b, 0x63, 0x61, 0x6e, 0x76, 0x61, 0x73, 0x57, 0x69, 0x64, 0x74,
	0x68, 0x12, 0x22, 0x0a, 0x0c, 0x63, 0x61, 0x6e, 0x76, 0x61, 0x73, 0x48, 0x65, 0x69, 0x67, 0x68,
	0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0c, 0x63, 0x61, 0x6e, 0x76, 0x61, 0x73, 0x48,
	0x65, 0x69, 0x67, 0x68, 0x74, 0x12, 0x1e, 0x0a, 0x0a, 0x62, 0x6c, 0x6f, 0x63, 0x6b, 0x57, 0x69,
	0x64, 0x74, 0x68, 0x18, 0x03, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0a, 0x62, 0x6c, 0x6f, 0x63, 0x6b,
	0x57, 0x69, 0x64, 0x74, 0x68, 0x12, 0x20, 0x0a, 0x0b, 0x62, 0x6c, 0x6f, 0x63, 0x6b, 0x48, 0x65,
	0x69, 0x67, 0x68, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0b, 0x62, 0x6c, 0x6f, 0x63,
	0x6b, 0x48, 0x65, 0x69, 0x67, 0x68, 0x74, 0x12, 0x20, 0x0a, 0x0b, 0x62, 0x6c, 0x6f, 0x63, 0x6b,
	0x52, 0x61, 0x64, 0x69, 0x75, 0x73, 0x18, 0x05, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0b, 0x62, 0x6c,
	0x6f, 0x63, 0x6b, 0x52, 0x61, 0x64, 0x69, 0x75, 0x73, 0x12, 0x14, 0x0a, 0x05, 0x70, 0x6c, 0x61,
	0x63, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x04, 0x52, 0x05, 0x70, 0x6c, 0x61, 0x63, 0x65, 0x22,
	0xbb, 0x01, 0x0a, 0x1d, 0x47, 0x65, 0x6e, 0x65, 0x72, 0x61, 0x74, 0x65, 0x53, 0x6c, 0x69, 0x64,
	0x65, 0x72, 0x43, 0x61, 0x70, 0x74, 0x63, 0x68, 0x61, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x12, 0x1a, 0x0a, 0x08, 0x6e, 0x6f, 0x6e, 0x63, 0x65, 0x53, 0x74, 0x72, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x08, 0x6e, 0x6f, 0x6e, 0x63, 0x65, 0x53, 0x74, 0x72, 0x12, 0x1c, 0x0a,
	0x09, 0x63, 0x61, 0x6e, 0x76, 0x61, 0x73, 0x53, 0x72, 0x63, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x09, 0x63, 0x61, 0x6e, 0x76, 0x61, 0x73, 0x53, 0x72, 0x63, 0x12, 0x1a, 0x0a, 0x08, 0x62,
	0x6c, 0x6f, 0x63, 0x6b, 0x53, 0x72, 0x63, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x62,
	0x6c, 0x6f, 0x63, 0x6b, 0x53, 0x72, 0x63, 0x12, 0x16, 0x0a, 0x06, 0x62, 0x6c, 0x6f, 0x63, 0x6b,
	0x59, 0x18, 0x04, 0x20, 0x01, 0x28, 0x04, 0x52, 0x06, 0x62, 0x6c, 0x6f, 0x63, 0x6b, 0x59, 0x12,
	0x14, 0x0a, 0x05, 0x66, 0x61, 0x63, 0x65, 0x59, 0x18, 0x05, 0x20, 0x01, 0x28, 0x04, 0x52, 0x05,
	0x66, 0x61, 0x63, 0x65, 0x59, 0x12, 0x16, 0x0a, 0x06, 0x62, 0x6c, 0x6f, 0x63, 0x6b, 0x58, 0x18,
	0x06, 0x20, 0x01, 0x28, 0x04, 0x52, 0x06, 0x62, 0x6c, 0x6f, 0x63, 0x6b, 0x58, 0x22, 0x50, 0x0a,
	0x1a, 0x56, 0x65, 0x72, 0x69, 0x66, 0x79, 0x53, 0x6c, 0x69, 0x64, 0x65, 0x72, 0x43, 0x61, 0x70,
	0x74, 0x63, 0x68, 0x61, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1a, 0x0a, 0x08, 0x6e,
	0x6f, 0x6e, 0x63, 0x65, 0x53, 0x74, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x6e,
	0x6f, 0x6e, 0x63, 0x65, 0x53, 0x74, 0x72, 0x12, 0x16, 0x0a, 0x06, 0x62, 0x6c, 0x6f, 0x63, 0x6b,
	0x58, 0x18, 0x02, 0x20, 0x01, 0x28, 0x02, 0x52, 0x06, 0x62, 0x6c, 0x6f, 0x63, 0x6b, 0x58, 0x22,
	0x39, 0x0a, 0x1b, 0x56, 0x65, 0x72, 0x69, 0x66, 0x79, 0x53, 0x6c, 0x69, 0x64, 0x65, 0x72, 0x43,
	0x61, 0x70, 0x74, 0x63, 0x68, 0x61, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x1a,
	0x0a, 0x08, 0x6e, 0x6f, 0x6e, 0x63, 0x65, 0x53, 0x74, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x08, 0x6e, 0x6f, 0x6e, 0x63, 0x65, 0x53, 0x74, 0x72, 0x22, 0x37, 0x0a, 0x19, 0x56, 0x65,
	0x72, 0x69, 0x66, 0x79, 0x53, 0x6c, 0x69, 0x64, 0x65, 0x72, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1a, 0x0a, 0x08, 0x6e, 0x6f, 0x6e, 0x63, 0x65,
	0x53, 0x74, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x6e, 0x6f, 0x6e, 0x63, 0x65,
	0x53, 0x74, 0x72, 0x22, 0x50, 0x0a, 0x1a, 0x56, 0x65, 0x72, 0x69, 0x66, 0x79, 0x53, 0x6c, 0x69,
	0x64, 0x65, 0x72, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x12, 0x1a, 0x0a, 0x08, 0x6e, 0x6f, 0x6e, 0x63, 0x65, 0x53, 0x74, 0x72, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x08, 0x6e, 0x6f, 0x6e, 0x63, 0x65, 0x53, 0x74, 0x72, 0x12, 0x16, 0x0a,
	0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x73,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x22, 0x55, 0x0a, 0x0f, 0x56, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74,
	0x65, 0x43, 0x6f, 0x64, 0x65, 0x52, 0x65, 0x71, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x16, 0x0a, 0x06,
	0x64, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x64, 0x6f,
	0x6d, 0x61, 0x69, 0x6e, 0x12, 0x16, 0x0a, 0x06, 0x74, 0x65, 0x6c, 0x4e, 0x75, 0x6d, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x74, 0x65, 0x6c, 0x4e, 0x75, 0x6d, 0x22, 0x24, 0x0a, 0x10,
	0x56, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x43, 0x6f, 0x64, 0x65, 0x52, 0x65, 0x73, 0x70,
	0x12, 0x10, 0x0a, 0x03, 0x6d, 0x73, 0x67, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6d,
	0x73, 0x67, 0x22, 0xee, 0x04, 0x0a, 0x16, 0x53, 0x65, 0x6c, 0x6c, 0x65, 0x72, 0x43, 0x75, 0x73,
	0x74, 0x6f, 0x6d, 0x65, 0x72, 0x52, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x0e, 0x0a,
	0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x02, 0x69, 0x64, 0x12, 0x1e, 0x0a,
	0x0a, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x49, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x04, 0x52, 0x0a, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x49, 0x64, 0x12, 0x22, 0x0a,
	0x0c, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0c, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x4e, 0x61, 0x6d,
	0x65, 0x12, 0x20, 0x0a, 0x0b, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x54, 0x65, 0x6c,
	0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72,
	0x54, 0x65, 0x6c, 0x12, 0x20, 0x0a, 0x0b, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x53,
	0x65, 0x78, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0b, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d,
	0x65, 0x72, 0x53, 0x65, 0x78, 0x12, 0x2c, 0x0a, 0x11, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65,
	0x72, 0x42, 0x69, 0x72, 0x74, 0x68, 0x44, 0x61, 0x74, 0x65, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x11, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x42, 0x69, 0x72, 0x74, 0x68, 0x44,
	0x61, 0x74, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x73, 0x65, 0x6c, 0x6c, 0x65, 0x72, 0x49, 0x64, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x04, 0x52, 0x08, 0x73, 0x65, 0x6c, 0x6c, 0x65, 0x72, 0x49, 0x64, 0x12,
	0x1e, 0x0a, 0x0a, 0x73, 0x65, 0x6c, 0x6c, 0x65, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0a, 0x73, 0x65, 0x6c, 0x6c, 0x65, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x12,
	0x1c, 0x0a, 0x09, 0x73, 0x65, 0x6c, 0x6c, 0x65, 0x72, 0x54, 0x65, 0x6c, 0x18, 0x0f, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x09, 0x73, 0x65, 0x6c, 0x6c, 0x65, 0x72, 0x54, 0x65, 0x6c, 0x12, 0x26, 0x0a,
	0x0e, 0x73, 0x65, 0x6c, 0x6c, 0x65, 0x72, 0x50, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x18,
	0x10, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x73, 0x65, 0x6c, 0x6c, 0x65, 0x72, 0x50, 0x6f, 0x73,
	0x69, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x1e, 0x0a, 0x0a, 0x73, 0x65, 0x6c, 0x6c, 0x65, 0x72, 0x53,
	0x69, 0x74, 0x65, 0x18, 0x11, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x73, 0x65, 0x6c, 0x6c, 0x65,
	0x72, 0x53, 0x69, 0x74, 0x65, 0x12, 0x1e, 0x0a, 0x0a, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f,
	0x72, 0x49, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0a, 0x6f, 0x70, 0x65, 0x72, 0x61,
	0x74, 0x6f, 0x72, 0x49, 0x64, 0x12, 0x22, 0x0a, 0x0c, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f,
	0x72, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x6f, 0x70, 0x65,
	0x72, 0x61, 0x74, 0x6f, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x72, 0x65, 0x6d,
	0x61, 0x72, 0x6b, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x72, 0x65, 0x6d, 0x61, 0x72,
	0x6b, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x09, 0x20, 0x01, 0x28,
	0x0d, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x1a, 0x0a, 0x08, 0x62, 0x69, 0x6e,
	0x64, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x62, 0x69, 0x6e,
	0x64, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x1e, 0x0a, 0x0a, 0x75, 0x6e, 0x62, 0x69, 0x6e, 0x64, 0x54,
	0x69, 0x6d, 0x65, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x75, 0x6e, 0x62, 0x69, 0x6e,
	0x64, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x1c, 0x0a, 0x09, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64,
	0x41, 0x74, 0x18, 0x12, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x64, 0x41, 0x74, 0x12, 0x1e, 0x0a, 0x0a, 0x6c, 0x65, 0x66, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x18, 0x13, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x6c, 0x65, 0x66, 0x74, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x22, 0xb8, 0x01, 0x0a, 0x1e, 0x53, 0x65, 0x6c, 0x6c, 0x65, 0x72, 0x43, 0x75,
	0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x52, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1e, 0x0a, 0x0a, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d,
	0x65, 0x72, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0a, 0x63, 0x75, 0x73, 0x74,
	0x6f, 0x6d, 0x65, 0x72, 0x49, 0x64, 0x12, 0x1a, 0x0a, 0x08, 0x73, 0x65, 0x6c, 0x6c, 0x65, 0x72,
	0x49, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x04, 0x52, 0x08, 0x73, 0x65, 0x6c, 0x6c, 0x65, 0x72,
	0x49, 0x64, 0x12, 0x1e, 0x0a, 0x0a, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x49, 0x64,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0a, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72,
	0x49, 0x64, 0x12, 0x22, 0x0a, 0x0c, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x4e, 0x61,
	0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74,
	0x6f, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x72, 0x65, 0x6d, 0x61, 0x72, 0x6b,
	0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x72, 0x65, 0x6d, 0x61, 0x72, 0x6b, 0x22, 0x33,
	0x0a, 0x1f, 0x53, 0x65, 0x6c, 0x6c, 0x65, 0x72, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72,
	0x52, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x12, 0x10, 0x0a, 0x03, 0x6d, 0x73, 0x67, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03,
	0x6d, 0x73, 0x67, 0x22, 0xaa, 0x02, 0x0a, 0x22, 0x53, 0x65, 0x6c, 0x6c, 0x65, 0x72, 0x43, 0x75,
	0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x52, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x4c,
	0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1e, 0x0a, 0x0a, 0x63, 0x75,
	0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0a,
	0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x49, 0x64, 0x12, 0x22, 0x0a, 0x0c, 0x63, 0x75,
	0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0c, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1a,
	0x0a, 0x08, 0x73, 0x65, 0x6c, 0x6c, 0x65, 0x72, 0x49, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x04,
	0x52, 0x08, 0x73, 0x65, 0x6c, 0x6c, 0x65, 0x72, 0x49, 0x64, 0x12, 0x1e, 0x0a, 0x0a, 0x73, 0x65,
	0x6c, 0x6c, 0x65, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a,
	0x73, 0x65, 0x6c, 0x6c, 0x65, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x12, 0x12, 0x0a, 0x04, 0x70, 0x61, 0x67, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0d,
	0x52, 0x04, 0x70, 0x61, 0x67, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x61, 0x67, 0x65, 0x53, 0x69,
	0x7a, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x08, 0x70, 0x61, 0x67, 0x65, 0x53, 0x69,
	0x7a, 0x65, 0x12, 0x1e, 0x0a, 0x0a, 0x6c, 0x65, 0x66, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x6c, 0x65, 0x66, 0x74, 0x53, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x12, 0x1c, 0x0a, 0x09, 0x73, 0x65, 0x6c, 0x6c, 0x65, 0x72, 0x49, 0x64, 0x73, 0x18,
	0x09, 0x20, 0x03, 0x28, 0x04, 0x52, 0x09, 0x73, 0x65, 0x6c, 0x6c, 0x65, 0x72, 0x49, 0x64, 0x73,
	0x22, 0x70, 0x0a, 0x23, 0x53, 0x65, 0x6c, 0x6c, 0x65, 0x72, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d,
	0x65, 0x72, 0x52, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x4c, 0x69, 0x73, 0x74, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x33, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18,
	0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e,
	0x53, 0x65, 0x6c, 0x6c, 0x65, 0x72, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x52, 0x65,
	0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x12, 0x14, 0x0a, 0x05,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x05, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x22, 0x38, 0x0a, 0x1a, 0x53, 0x65, 0x6c, 0x6c, 0x65, 0x72, 0x43, 0x75, 0x73, 0x74,
	0x6f, 0x6d, 0x65, 0x72, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x12, 0x1a, 0x0a, 0x08, 0x73, 0x65, 0x6c, 0x6c, 0x65, 0x72, 0x49, 0x64, 0x18, 0x01, 0x20, 0x03,
	0x28, 0x04, 0x52, 0x08, 0x73, 0x65, 0x6c, 0x6c, 0x65, 0x72, 0x49, 0x64, 0x22, 0xb2, 0x01, 0x0a,
	0x1b, 0x53, 0x65, 0x6c, 0x6c, 0x65, 0x72, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x43,
	0x6f, 0x75, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x50, 0x0a, 0x09,
	0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x32, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x53, 0x65, 0x6c, 0x6c, 0x65, 0x72,
	0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x43, 0x6f,
	0x75, 0x6e, 0x74, 0x52, 0x09, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x73, 0x1a, 0x41,
	0x0a, 0x0d, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12,
	0x1a, 0x0a, 0x08, 0x53, 0x65, 0x6c, 0x6c, 0x65, 0x72, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x04, 0x52, 0x08, 0x53, 0x65, 0x6c, 0x6c, 0x65, 0x72, 0x49, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x43,
	0x6f, 0x75, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x04, 0x52, 0x05, 0x43, 0x6f, 0x75, 0x6e,
	0x74, 0x32, 0xde, 0x36, 0x0a, 0x07, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x34, 0x0a,
	0x05, 0x4c, 0x6f, 0x67, 0x69, 0x6e, 0x12, 0x15, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x2e, 0x4c, 0x6f, 0x67, 0x69, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x12, 0x2e,
	0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x49, 0x6e, 0x66,
	0x6f, 0x22, 0x00, 0x12, 0x42, 0x0a, 0x0c, 0x52, 0x65, 0x66, 0x72, 0x65, 0x73, 0x68, 0x54, 0x6f,
	0x6b, 0x65, 0x6e, 0x12, 0x1c, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x52, 0x65,
	0x66, 0x72, 0x65, 0x73, 0x68, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x1a, 0x12, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x54, 0x6f, 0x6b, 0x65,
	0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x22, 0x00, 0x12, 0x3f, 0x0a, 0x06, 0x4c, 0x6f, 0x67, 0x6f, 0x75,
	0x74, 0x12, 0x1a, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x44, 0x65, 0x63, 0x72,
	0x79, 0x70, 0x74, 0x4a, 0x77, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x17, 0x2e,
	0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x43, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x38, 0x0a, 0x05, 0x57, 0x78, 0x41, 0x70,
	0x70, 0x12, 0x15, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x57, 0x78, 0x41, 0x70,
	0x70, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x16, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x2e, 0x57, 0x78, 0x41, 0x70, 0x70, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x22, 0x00, 0x12, 0x47, 0x0a, 0x0a, 0x57, 0x78, 0x55, 0x73, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f,
	0x12, 0x1e, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x57, 0x78, 0x55, 0x73, 0x65,
	0x72, 0x4f, 0x72, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x1a, 0x17, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x57, 0x78, 0x55, 0x73, 0x65,
	0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x49, 0x0a, 0x0c, 0x57,
	0x78, 0x55, 0x73, 0x65, 0x72, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x12, 0x1e, 0x2e, 0x61, 0x63,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x57, 0x78, 0x55, 0x73, 0x65, 0x72, 0x4f, 0x72, 0x43, 0x72,
	0x65, 0x61, 0x74, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x17, 0x2e, 0x61, 0x63,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x57, 0x78, 0x55, 0x73, 0x65, 0x72, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x47, 0x0a, 0x0c, 0x57, 0x78, 0x55, 0x73, 0x65, 0x72,
	0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x12, 0x1c, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x2e, 0x57, 0x78, 0x55, 0x73, 0x65, 0x72, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x1a, 0x17, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x57,
	0x78, 0x55, 0x73, 0x65, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12,
	0x5c, 0x0a, 0x11, 0x57, 0x78, 0x47, 0x65, 0x74, 0x4f, 0x70, 0x65, 0x6e, 0x49, 0x64, 0x42, 0x79,
	0x43, 0x6f, 0x64, 0x65, 0x12, 0x21, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x57,
	0x78, 0x47, 0x65, 0x74, 0x4f, 0x70, 0x65, 0x6e, 0x49, 0x64, 0x42, 0x79, 0x43, 0x6f, 0x64, 0x65,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x22, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x2e, 0x57, 0x78, 0x47, 0x65, 0x74, 0x4f, 0x70, 0x65, 0x6e, 0x49, 0x64, 0x42, 0x79, 0x43,
	0x6f, 0x64, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x49, 0x0a,
	0x0a, 0x57, 0x78, 0x42, 0x6f, 0x78, 0x4c, 0x6f, 0x67, 0x69, 0x6e, 0x12, 0x21, 0x2e, 0x61, 0x63,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x57, 0x78, 0x47, 0x65, 0x74, 0x4f, 0x70, 0x65, 0x6e, 0x49,
	0x64, 0x42, 0x79, 0x43, 0x6f, 0x64, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x16,
	0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x57, 0x78, 0x42, 0x6f, 0x78, 0x55, 0x73,
	0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x22, 0x00, 0x12, 0x50, 0x0a, 0x15, 0x57, 0x78, 0x42, 0x6f,
	0x78, 0x55, 0x73, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x42, 0x79, 0x4f, 0x70, 0x65, 0x6e, 0x49,
	0x64, 0x12, 0x1d, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x57, 0x78, 0x42, 0x6f,
	0x78, 0x55, 0x73, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x1a, 0x16, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x57, 0x78, 0x42, 0x6f, 0x78,
	0x55, 0x73, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x22, 0x00, 0x12, 0x5c, 0x0a, 0x11, 0x57, 0x78,
	0x42, 0x6f, 0x78, 0x54, 0x65, 0x6c, 0x4e, 0x75, 0x6d, 0x42, 0x79, 0x43, 0x6f, 0x64, 0x65, 0x12,
	0x21, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x57, 0x78, 0x47, 0x65, 0x74, 0x4f,
	0x70, 0x65, 0x6e, 0x49, 0x64, 0x42, 0x79, 0x43, 0x6f, 0x64, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x1a, 0x22, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x57, 0x78, 0x42,
	0x6f, 0x78, 0x54, 0x65, 0x6c, 0x4e, 0x75, 0x6d, 0x42, 0x79, 0x43, 0x6f, 0x64, 0x65, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x44, 0x0a, 0x0f, 0x57, 0x78, 0x42, 0x6f,
	0x78, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x55, 0x73, 0x65, 0x72, 0x12, 0x16, 0x2e, 0x61, 0x63,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x57, 0x78, 0x42, 0x6f, 0x78, 0x55, 0x73, 0x65, 0x72, 0x49,
	0x6e, 0x66, 0x6f, 0x1a, 0x17, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x43, 0x6f,
	0x6d, 0x6d, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x44,
	0x0a, 0x0f, 0x57, 0x78, 0x42, 0x6f, 0x78, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x55, 0x73, 0x65,
	0x72, 0x12, 0x16, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x57, 0x78, 0x42, 0x6f,
	0x78, 0x55, 0x73, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x1a, 0x17, 0x2e, 0x61, 0x63, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x2e, 0x43, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x22, 0x00, 0x12, 0x49, 0x0a, 0x0d, 0x46, 0x64, 0x64, 0x43, 0x72, 0x65, 0x61, 0x74,
	0x65, 0x55, 0x73, 0x65, 0x72, 0x12, 0x1d, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e,
	0x46, 0x64, 0x64, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x55, 0x73, 0x65, 0x72, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x1a, 0x17, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x43,
	0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12,
	0x49, 0x0a, 0x0d, 0x46, 0x64, 0x64, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x55, 0x73, 0x65, 0x72,
	0x12, 0x1d, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x46, 0x64, 0x64, 0x43, 0x72,
	0x65, 0x61, 0x74, 0x65, 0x55, 0x73, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a,
	0x17, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x43, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x49, 0x0a, 0x0d, 0x46, 0x64,
	0x64, 0x52, 0x65, 0x6d, 0x6f, 0x76, 0x65, 0x55, 0x73, 0x65, 0x72, 0x12, 0x1d, 0x2e, 0x61, 0x63,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x46, 0x64, 0x64, 0x52, 0x65, 0x6d, 0x6f, 0x76, 0x65, 0x55,
	0x73, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x17, 0x2e, 0x61, 0x63, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x43, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x3c, 0x0a, 0x07, 0x4f, 0x66, 0x66, 0x4c, 0x69, 0x6e, 0x65,
	0x12, 0x16, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x43, 0x6f, 0x6d, 0x6d, 0x6f,
	0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x17, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x2e, 0x43, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x22, 0x00, 0x12, 0x4d, 0x0a, 0x09, 0x4f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x4c, 0x6f, 0x67,
	0x12, 0x22, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x4c, 0x6f, 0x67, 0x69, 0x6e,
	0x49, 0x6e, 0x66, 0x6f, 0x73, 0x42, 0x79, 0x55, 0x73, 0x65, 0x72, 0x49, 0x64, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x1a, 0x1a, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x4c,
	0x6f, 0x67, 0x69, 0x6e, 0x4c, 0x6f, 0x67, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x22, 0x00, 0x12, 0x43, 0x0a, 0x0d, 0x4f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x4c, 0x6f, 0x67, 0x42,
	0x79, 0x49, 0x64, 0x12, 0x1d, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x4f, 0x6e,
	0x6c, 0x69, 0x6e, 0x65, 0x4c, 0x6f, 0x67, 0x42, 0x79, 0x49, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x1a, 0x11, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x4c, 0x6f, 0x67,
	0x69, 0x6e, 0x4c, 0x6f, 0x67, 0x22, 0x00, 0x12, 0x3f, 0x0a, 0x08, 0x43, 0x68, 0x65, 0x63, 0x6b,
	0x50, 0x77, 0x64, 0x12, 0x18, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x43, 0x68,
	0x65, 0x63, 0x6b, 0x50, 0x77, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x17, 0x2e,
	0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x3c, 0x0a, 0x08, 0x52, 0x65, 0x67, 0x69,
	0x73, 0x74, 0x65, 0x72, 0x12, 0x16, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x52,
	0x65, 0x67, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x16, 0x2e, 0x61,
	0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x53, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x22, 0x00, 0x12, 0x43, 0x0a, 0x0f, 0x52, 0x65, 0x67, 0x69, 0x73, 0x74,
	0x65, 0x72, 0x4f, 0x72, 0x45, 0x78, 0x69, 0x73, 0x74, 0x12, 0x16, 0x2e, 0x61, 0x63, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x2e, 0x52, 0x65, 0x67, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x1a, 0x16, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x22, 0x00, 0x12, 0x44, 0x0a, 0x07, 0x53,
	0x65, 0x6e, 0x64, 0x4d, 0x73, 0x67, 0x12, 0x17, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x2e, 0x53, 0x65, 0x6e, 0x64, 0x4d, 0x73, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a,
	0x1e, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x53, 0x65, 0x6e, 0x64, 0x4d, 0x73,
	0x67, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22,
	0x00, 0x12, 0x50, 0x0a, 0x0d, 0x53, 0x65, 0x6e, 0x64, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x4d,
	0x73, 0x67, 0x12, 0x1d, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x53, 0x65, 0x6e,
	0x64, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x4d, 0x73, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x1a, 0x1e, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x53, 0x65, 0x6e, 0x64,
	0x4d, 0x73, 0x67, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x22, 0x00, 0x12, 0x52, 0x0a, 0x0f, 0x53, 0x65, 0x6e, 0x64, 0x45, 0x78, 0x43, 0x75, 0x73,
	0x74, 0x6f, 0x6d, 0x4d, 0x73, 0x67, 0x12, 0x1d, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x2e, 0x53, 0x65, 0x6e, 0x64, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x4d, 0x73, 0x67, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1e, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e,
	0x53, 0x65, 0x6e, 0x64, 0x4d, 0x73, 0x67, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x4c, 0x0a, 0x0f, 0x53, 0x65, 0x6e, 0x64, 0x4d,
	0x73, 0x67, 0x52, 0x65, 0x67, 0x69, 0x73, 0x74, 0x65, 0x72, 0x12, 0x17, 0x2e, 0x61, 0x63, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x53, 0x65, 0x6e, 0x64, 0x4d, 0x73, 0x67, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x1a, 0x1e, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x53, 0x65,
	0x6e, 0x64, 0x4d, 0x73, 0x67, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x46, 0x0a, 0x08, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x4d, 0x73,
	0x67, 0x12, 0x18, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x43, 0x68, 0x65, 0x63,
	0x6b, 0x4d, 0x73, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1e, 0x2e, 0x61, 0x63,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x53, 0x65, 0x6e, 0x64, 0x4d, 0x73, 0x67, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x56, 0x0a,
	0x10, 0x53, 0x65, 0x6e, 0x64, 0x4e, 0x65, 0x77, 0x54, 0x65, 0x6c, 0x4e, 0x75, 0x6d, 0x4d, 0x73,
	0x67, 0x12, 0x20, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x53, 0x65, 0x6e, 0x64,
	0x4e, 0x65, 0x77, 0x54, 0x65, 0x6c, 0x4e, 0x75, 0x6d, 0x4d, 0x73, 0x67, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x1a, 0x1e, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x53, 0x65,
	0x6e, 0x64, 0x4d, 0x73, 0x67, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x52, 0x0a, 0x0c, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x54,
	0x65, 0x6c, 0x4e, 0x75, 0x6d, 0x12, 0x20, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e,
	0x53, 0x65, 0x6e, 0x64, 0x4e, 0x65, 0x77, 0x54, 0x65, 0x6c, 0x4e, 0x75, 0x6d, 0x4d, 0x73, 0x67,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1e, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x2e, 0x53, 0x65, 0x6e, 0x64, 0x4d, 0x73, 0x67, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x4a, 0x0a, 0x0e, 0x41, 0x75, 0x74,
	0x68, 0x65, 0x6e, 0x74, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x1e, 0x2e, 0x61, 0x63,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x41, 0x75, 0x74, 0x68, 0x65, 0x6e, 0x74, 0x69, 0x63, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x16, 0x2e, 0x61, 0x63,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x22, 0x00, 0x12, 0x47, 0x0a, 0x0a, 0x44, 0x65, 0x63, 0x72, 0x79, 0x70, 0x74,
	0x4a, 0x77, 0x74, 0x12, 0x1a, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x44, 0x65,
	0x63, 0x72, 0x79, 0x70, 0x74, 0x4a, 0x77, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a,
	0x1b, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x44, 0x65, 0x63, 0x72, 0x79, 0x70,
	0x74, 0x4a, 0x77, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x35,
	0x0a, 0x04, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x14, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x2e, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x15, 0x2e, 0x61,
	0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x47, 0x0a, 0x0d, 0x4a, 0x6f, 0x62, 0x4e, 0x75, 0x6d, 0x47,
	0x65, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x1d, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x2e, 0x4a, 0x6f, 0x62, 0x4e, 0x75, 0x6d, 0x47, 0x65, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x15, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e,
	0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x35,
	0x0a, 0x04, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x14, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x2e, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x15, 0x2e, 0x61,
	0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x39, 0x0a, 0x08, 0x52, 0x61, 0x6e, 0x64, 0x4c, 0x69, 0x73,
	0x74, 0x12, 0x14, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x4c, 0x69, 0x73, 0x74,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x15, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00,
	0x12, 0x3f, 0x0a, 0x09, 0x4c, 0x69, 0x73, 0x74, 0x42, 0x79, 0x49, 0x44, 0x73, 0x12, 0x19, 0x2e,
	0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x42, 0x79, 0x49, 0x44,
	0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x15, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22,
	0x00, 0x12, 0x3b, 0x0a, 0x06, 0x52, 0x65, 0x6d, 0x6f, 0x76, 0x65, 0x12, 0x16, 0x2e, 0x61, 0x63,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x52, 0x65, 0x6d, 0x6f, 0x76, 0x65, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x1a, 0x17, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x52, 0x65,
	0x6d, 0x6f, 0x76, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x3f,
	0x0a, 0x08, 0x57, 0x72, 0x69, 0x74, 0x65, 0x4f, 0x66, 0x66, 0x12, 0x18, 0x2e, 0x61, 0x63, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x57, 0x72, 0x69, 0x74, 0x65, 0x4f, 0x66, 0x66, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x1a, 0x17, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x52,
	0x65, 0x6d, 0x6f, 0x76, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12,
	0x40, 0x0a, 0x0b, 0x57, 0x72, 0x69, 0x74, 0x65, 0x4f, 0x66, 0x66, 0x41, 0x70, 0x70, 0x12, 0x16,
	0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x52, 0x65, 0x6d, 0x6f, 0x76, 0x65, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x17, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x2e, 0x52, 0x65, 0x6d, 0x6f, 0x76, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22,
	0x00, 0x12, 0x4b, 0x0a, 0x0c, 0x57, 0x72, 0x69, 0x74, 0x65, 0x4f, 0x66, 0x66, 0x49, 0x6e, 0x66,
	0x6f, 0x12, 0x1f, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x57, 0x72, 0x69, 0x74,
	0x65, 0x4f, 0x66, 0x66, 0x41, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x1a, 0x18, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x57, 0x72, 0x69,
	0x74, 0x65, 0x4f, 0x66, 0x66, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x22, 0x00, 0x12, 0x51,
	0x0a, 0x10, 0x46, 0x69, 0x6e, 0x64, 0x57, 0x72, 0x69, 0x74, 0x65, 0x4f, 0x66, 0x66, 0x4c, 0x69,
	0x73, 0x74, 0x12, 0x1c, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x57, 0x72, 0x69,
	0x74, 0x65, 0x4f, 0x66, 0x66, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x1a, 0x1d, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x57, 0x72, 0x69, 0x74, 0x65,
	0x4f, 0x66, 0x66, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22,
	0x00, 0x12, 0x4c, 0x0a, 0x0e, 0x57, 0x72, 0x69, 0x74, 0x65, 0x4f, 0x66, 0x66, 0x55, 0x70, 0x64,
	0x61, 0x74, 0x65, 0x12, 0x1f, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x57, 0x72,
	0x69, 0x74, 0x65, 0x4f, 0x66, 0x66, 0x41, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x1a, 0x17, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x52,
	0x65, 0x6d, 0x6f, 0x76, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12,
	0x3b, 0x0a, 0x06, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x12, 0x16, 0x2e, 0x61, 0x63, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x1a, 0x17, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x55, 0x70, 0x64, 0x61,
	0x74, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x42, 0x0a, 0x0b,
	0x50, 0x72, 0x69, 0x76, 0x61, 0x63, 0x79, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x1b, 0x2e, 0x61, 0x63,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x50, 0x72, 0x69, 0x76, 0x61, 0x63, 0x79, 0x49, 0x6e, 0x66,
	0x6f, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x14, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x2e, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x22, 0x00,
	0x12, 0x41, 0x0a, 0x0a, 0x55, 0x73, 0x65, 0x72, 0x73, 0x42, 0x79, 0x54, 0x65, 0x6c, 0x12, 0x1a,
	0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x73, 0x42, 0x79,
	0x54, 0x65, 0x6c, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x15, 0x2e, 0x61, 0x63, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x22, 0x00, 0x12, 0x3f, 0x0a, 0x09, 0x55, 0x73, 0x65, 0x72, 0x42, 0x79, 0x54, 0x65, 0x6c,
	0x12, 0x19, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x42,
	0x79, 0x54, 0x65, 0x6c, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x15, 0x2e, 0x61, 0x63,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x22, 0x00, 0x12, 0x55, 0x0a, 0x13, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x42, 0x65, 0x66,
	0x6f, 0x72, 0x65, 0x52, 0x65, 0x67, 0x69, 0x73, 0x74, 0x65, 0x72, 0x12, 0x23, 0x2e, 0x61, 0x63,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x42, 0x65, 0x66, 0x6f, 0x72,
	0x65, 0x52, 0x65, 0x67, 0x69, 0x73, 0x74, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x1a, 0x17, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x43, 0x6f, 0x6d, 0x6d, 0x6f,
	0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x48, 0x0a, 0x0b, 0x4f,
	0x6e, 0x6c, 0x79, 0x53, 0x65, 0x6e, 0x64, 0x4d, 0x73, 0x67, 0x12, 0x17, 0x2e, 0x61, 0x63, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x53, 0x65, 0x6e, 0x64, 0x4d, 0x73, 0x67, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x1a, 0x1e, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x53, 0x65,
	0x6e, 0x64, 0x4d, 0x73, 0x67, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x4a, 0x0a, 0x0c, 0x4f, 0x6e, 0x6c, 0x79, 0x43, 0x68, 0x65,
	0x63, 0x6b, 0x4d, 0x73, 0x67, 0x12, 0x18, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e,
	0x43, 0x68, 0x65, 0x63, 0x6b, 0x4d, 0x73, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a,
	0x1e, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x53, 0x65, 0x6e, 0x64, 0x4d, 0x73,
	0x67, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22,
	0x00, 0x12, 0x56, 0x0a, 0x11, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x43, 0x6c, 0x6f, 0x63, 0x6b,
	0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x12, 0x21, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x43, 0x6c, 0x6f, 0x63, 0x6b, 0x44, 0x65, 0x76, 0x69,
	0x63, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1c, 0x2e, 0x61, 0x63, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x2e, 0x43, 0x6c, 0x6f, 0x63, 0x6b, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x56, 0x0a, 0x11, 0x55, 0x70, 0x64,
	0x61, 0x74, 0x65, 0x43, 0x6c, 0x6f, 0x63, 0x6b, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x12, 0x21,
	0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x43,
	0x6c, 0x6f, 0x63, 0x6b, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x1a, 0x1c, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x43, 0x6c, 0x6f, 0x63,
	0x6b, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22,
	0x00, 0x12, 0x56, 0x0a, 0x11, 0x52, 0x65, 0x6d, 0x6f, 0x76, 0x65, 0x43, 0x6c, 0x6f, 0x63, 0x6b,
	0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x12, 0x21, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x2e, 0x52, 0x65, 0x6d, 0x6f, 0x76, 0x65, 0x43, 0x6c, 0x6f, 0x63, 0x6b, 0x44, 0x65, 0x76, 0x69,
	0x63, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1c, 0x2e, 0x61, 0x63, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x2e, 0x43, 0x6c, 0x6f, 0x63, 0x6b, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x56, 0x0a, 0x0f, 0x43, 0x6c, 0x6f,
	0x63, 0x6b, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x1f, 0x2e, 0x61,
	0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x43, 0x6c, 0x6f, 0x63, 0x6b, 0x44, 0x65, 0x76, 0x69,
	0x63, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x20, 0x2e,
	0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x43, 0x6c, 0x6f, 0x63, 0x6b, 0x44, 0x65, 0x76,
	0x69, 0x63, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22,
	0x00, 0x12, 0x56, 0x0a, 0x0f, 0x43, 0x6c, 0x6f, 0x63, 0x6b, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65,
	0x49, 0x6e, 0x66, 0x6f, 0x12, 0x1f, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x43,
	0x6c, 0x6f, 0x63, 0x6b, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x20, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e,
	0x43, 0x6c, 0x6f, 0x63, 0x6b, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x5b, 0x0a, 0x16, 0x43, 0x6c, 0x6f,
	0x63, 0x6b, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x53, 0x69, 0x6e, 0x67, 0x6c, 0x65, 0x55, 0x6e,
	0x74, 0x69, 0x65, 0x12, 0x21, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x52, 0x65,
	0x6d, 0x6f, 0x76, 0x65, 0x43, 0x6c, 0x6f, 0x63, 0x6b, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1c, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x2e, 0x43, 0x6c, 0x6f, 0x63, 0x6b, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x5b, 0x0a, 0x14, 0x43, 0x6c, 0x6f, 0x63, 0x6b, 0x44,
	0x65, 0x76, 0x69, 0x63, 0x65, 0x42, 0x61, 0x74, 0x63, 0x68, 0x42, 0x69, 0x6e, 0x64, 0x12, 0x1f,
	0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x43, 0x6c, 0x6f, 0x63, 0x6b, 0x42, 0x61,
	0x74, 0x63, 0x68, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x1a,
	0x20, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x43, 0x6c, 0x6f, 0x63, 0x6b, 0x44,
	0x65, 0x76, 0x69, 0x63, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x22, 0x00, 0x12, 0x5b, 0x0a, 0x15, 0x43, 0x6c, 0x6f, 0x63, 0x6b, 0x44, 0x65, 0x76, 0x69,
	0x63, 0x65, 0x42, 0x61, 0x74, 0x63, 0x68, 0x55, 0x6e, 0x74, 0x69, 0x65, 0x12, 0x1e, 0x2e, 0x61,
	0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x43, 0x6c, 0x6f, 0x63, 0x6b, 0x42, 0x61, 0x74, 0x63,
	0x68, 0x42, 0x69, 0x6e, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x20, 0x2e, 0x61,
	0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x43, 0x6c, 0x6f, 0x63, 0x6b, 0x44, 0x65, 0x76, 0x69,
	0x63, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00,
	0x12, 0x59, 0x0a, 0x14, 0x43, 0x6c, 0x6f, 0x63, 0x6b, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x42,
	0x61, 0x74, 0x63, 0x68, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x1e, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x2e, 0x43, 0x6c, 0x6f, 0x63, 0x6b, 0x42, 0x61, 0x74, 0x63, 0x68, 0x42, 0x69, 0x6e,
	0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1f, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x2e, 0x43, 0x6c, 0x6f, 0x63, 0x6b, 0x42, 0x61, 0x74, 0x63, 0x68, 0x4c, 0x69, 0x73,
	0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x56, 0x0a, 0x15, 0x55,
	0x70, 0x64, 0x61, 0x74, 0x65, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x52, 0x65, 0x6c, 0x65, 0x76,
	0x61, 0x6e, 0x63, 0x65, 0x12, 0x1d, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x43,
	0x6c, 0x6f, 0x63, 0x6b, 0x55, 0x73, 0x65, 0x72, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x42, 0x61,
	0x74, 0x63, 0x68, 0x1a, 0x1c, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x43, 0x6c,
	0x6f, 0x63, 0x6b, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x22, 0x00, 0x12, 0x5d, 0x0a, 0x15, 0x4d, 0x61, 0x69, 0x6c, 0x41, 0x63, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x42, 0x79, 0x4e, 0x69, 0x63, 0x6b, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x25, 0x2e, 0x61,
	0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x4d, 0x61, 0x69, 0x6c, 0x41, 0x63, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x42, 0x79, 0x4e, 0x69, 0x63, 0x6b, 0x4e, 0x61, 0x6d, 0x65, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x1a, 0x1b, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x4d, 0x61,
	0x69, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x22, 0x00, 0x12, 0x4f, 0x0a, 0x10, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x4d, 0x61, 0x69, 0x41,
	0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x20, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x4d, 0x61, 0x69, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x17, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x2e, 0x43, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x22, 0x00, 0x12, 0x47, 0x0a, 0x0e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x43, 0x6c, 0x6f,
	0x63, 0x6b, 0x4c, 0x6f, 0x67, 0x12, 0x15, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e,
	0x43, 0x6c, 0x6f, 0x63, 0x6b, 0x4c, 0x6f, 0x67, 0x49, 0x6e, 0x66, 0x6f, 0x1a, 0x1c, 0x2e, 0x61,
	0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x43, 0x6c, 0x6f, 0x63, 0x6b, 0x44, 0x65, 0x76, 0x69,
	0x63, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x51, 0x0a, 0x11,
	0x53, 0x65, 0x6e, 0x64, 0x43, 0x6c, 0x6f, 0x63, 0x6b, 0x49, 0x6e, 0x57, 0x65, 0x63, 0x68, 0x61,
	0x74, 0x12, 0x21, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x53, 0x65, 0x6e, 0x64,
	0x43, 0x6c, 0x6f, 0x63, 0x6b, 0x49, 0x6e, 0x57, 0x65, 0x63, 0x68, 0x61, 0x74, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x1a, 0x17, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x43,
	0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12,
	0x49, 0x0a, 0x10, 0x46, 0x69, 0x6e, 0x64, 0x43, 0x6c, 0x6f, 0x63, 0x6b, 0x4c, 0x6f, 0x67, 0x4c,
	0x69, 0x73, 0x74, 0x12, 0x14, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x43, 0x6c,
	0x6f, 0x63, 0x6b, 0x4c, 0x6f, 0x67, 0x52, 0x65, 0x71, 0x1a, 0x1d, 0x2e, 0x61, 0x63, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x2e, 0x43, 0x6c, 0x6f, 0x63, 0x6b, 0x4c, 0x6f, 0x67, 0x4c, 0x69, 0x73, 0x74,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x59, 0x0a, 0x19, 0x53, 0x65,
	0x6e, 0x64, 0x53, 0x74, 0x72, 0x61, 0x6e, 0x67, 0x65, 0x72, 0x43, 0x6c, 0x6f, 0x63, 0x6b, 0x49,
	0x6e, 0x57, 0x65, 0x63, 0x68, 0x61, 0x74, 0x12, 0x21, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x2e, 0x53, 0x65, 0x6e, 0x64, 0x43, 0x6c, 0x6f, 0x63, 0x6b, 0x49, 0x6e, 0x57, 0x65, 0x63,
	0x68, 0x61, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x17, 0x2e, 0x61, 0x63, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x43, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x39, 0x0a, 0x06, 0x4c, 0x69, 0x73, 0x74, 0x56, 0x32, 0x12,
	0x16, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x56, 0x32,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x15, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00,
	0x12, 0x53, 0x0a, 0x12, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x43, 0x68, 0x61, 0x69, 0x6e, 0x41,
	0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x16, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x2e, 0x43, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x23,
	0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x43,
	0x68, 0x61, 0x69, 0x6e, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x50, 0x0a, 0x0d, 0x53, 0x65, 0x6e, 0x64, 0x4e, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x4d, 0x73, 0x67, 0x12, 0x1d, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x2e, 0x53, 0x65, 0x6e, 0x64, 0x4e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4d, 0x73, 0x67, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1e, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e,
	0x53, 0x65, 0x6e, 0x64, 0x4d, 0x73, 0x67, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x53, 0x0a, 0x0e, 0x55, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x4c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65, 0x12, 0x1e, 0x2e, 0x61, 0x63, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x4c, 0x61, 0x6e, 0x67, 0x75, 0x61,
	0x67, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1f, 0x2e, 0x61, 0x63, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x4c, 0x61, 0x6e, 0x67, 0x75, 0x61,
	0x67, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x68, 0x0a, 0x15,
	0x47, 0x65, 0x6e, 0x65, 0x72, 0x61, 0x74, 0x65, 0x53, 0x6c, 0x69, 0x64, 0x65, 0x72, 0x43, 0x61,
	0x70, 0x74, 0x63, 0x68, 0x61, 0x12, 0x25, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e,
	0x47, 0x65, 0x6e, 0x65, 0x72, 0x61, 0x74, 0x65, 0x53, 0x6c, 0x69, 0x64, 0x65, 0x72, 0x43, 0x61,
	0x70, 0x74, 0x63, 0x68, 0x61, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x26, 0x2e, 0x61,
	0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x47, 0x65, 0x6e, 0x65, 0x72, 0x61, 0x74, 0x65, 0x53,
	0x6c, 0x69, 0x64, 0x65, 0x72, 0x43, 0x61, 0x70, 0x74, 0x63, 0x68, 0x61, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x62, 0x0a, 0x13, 0x56, 0x65, 0x72, 0x69, 0x66, 0x79,
	0x53, 0x6c, 0x69, 0x64, 0x65, 0x72, 0x43, 0x61, 0x70, 0x74, 0x63, 0x68, 0x61, 0x12, 0x23, 0x2e,
	0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x56, 0x65, 0x72, 0x69, 0x66, 0x79, 0x53, 0x6c,
	0x69, 0x64, 0x65, 0x72, 0x43, 0x61, 0x70, 0x74, 0x63, 0x68, 0x61, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x1a, 0x24, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x56, 0x65, 0x72,
	0x69, 0x66, 0x79, 0x53, 0x6c, 0x69, 0x64, 0x65, 0x72, 0x43, 0x61, 0x70, 0x74, 0x63, 0x68, 0x61,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x5f, 0x0a, 0x12, 0x56, 0x65,
	0x72, 0x69, 0x66, 0x79, 0x53, 0x6c, 0x69, 0x64, 0x65, 0x72, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x12, 0x22, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x56, 0x65, 0x72, 0x69, 0x66,
	0x79, 0x53, 0x6c, 0x69, 0x64, 0x65, 0x72, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x1a, 0x23, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x56,
	0x65, 0x72, 0x69, 0x66, 0x79, 0x53, 0x6c, 0x69, 0x64, 0x65, 0x72, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x50, 0x0a, 0x0d, 0x53,
	0x61, 0x6d, 0x70, 0x6c, 0x65, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x1d, 0x2e, 0x61,
	0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x53, 0x61, 0x6d, 0x70, 0x6c, 0x65, 0x41, 0x63, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1e, 0x2e, 0x61, 0x63,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x53, 0x61, 0x6d, 0x70, 0x6c, 0x65, 0x41, 0x63, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x43, 0x0a,
	0x14, 0x4c, 0x6f, 0x67, 0x69, 0x6e, 0x41, 0x6e, 0x64, 0x53, 0x71, 0x75, 0x65, 0x65, 0x7a, 0x65,
	0x4f, 0x74, 0x68, 0x65, 0x72, 0x12, 0x15, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e,
	0x4c, 0x6f, 0x67, 0x69, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x12, 0x2e, 0x61,
	0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x49, 0x6e, 0x66, 0x6f,
	0x22, 0x00, 0x12, 0x80, 0x01, 0x0a, 0x1d, 0x51, 0x75, 0x65, 0x72, 0x79, 0x50, 0x65, 0x72, 0x73,
	0x6f, 0x6e, 0x6e, 0x65, 0x6c, 0x57, 0x69, 0x74, 0x68, 0x54, 0x68, 0x65, 0x53, 0x61, 0x6d, 0x65,
	0x4e, 0x61, 0x6d, 0x65, 0x12, 0x2d, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x51,
	0x75, 0x65, 0x72, 0x79, 0x50, 0x65, 0x72, 0x73, 0x6f, 0x6e, 0x6e, 0x65, 0x6c, 0x57, 0x69, 0x74,
	0x68, 0x54, 0x68, 0x65, 0x53, 0x61, 0x6d, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x1a, 0x2e, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x51, 0x75,
	0x65, 0x72, 0x79, 0x50, 0x65, 0x72, 0x73, 0x6f, 0x6e, 0x6e, 0x65, 0x6c, 0x57, 0x69, 0x74, 0x68,
	0x54, 0x68, 0x65, 0x53, 0x61, 0x6d, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x47, 0x0a, 0x0d, 0x55, 0x73, 0x65, 0x72, 0x73, 0x42, 0x79,
	0x4a, 0x6f, 0x62, 0x4e, 0x75, 0x6d, 0x12, 0x1d, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x2e, 0x55, 0x73, 0x65, 0x72, 0x73, 0x42, 0x79, 0x4a, 0x6f, 0x62, 0x4e, 0x75, 0x6d, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x15, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e,
	0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x4d,
	0x0a, 0x0c, 0x49, 0x73, 0x53, 0x61, 0x6d, 0x65, 0x50, 0x65, 0x72, 0x73, 0x6f, 0x6e, 0x12, 0x1c,
	0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x49, 0x73, 0x53, 0x61, 0x6d, 0x65, 0x50,
	0x65, 0x72, 0x73, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1d, 0x2e, 0x61,
	0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x49, 0x73, 0x53, 0x61, 0x6d, 0x65, 0x50, 0x65, 0x72,
	0x73, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x48, 0x0a,
	0x18, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x52, 0x65, 0x61, 0x6c, 0x4e, 0x61, 0x6d, 0x65, 0x4f,
	0x72, 0x50, 0x61, 0x73, 0x73, 0x50, 0x6f, 0x72, 0x74, 0x12, 0x11, 0x2e, 0x61, 0x63, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x1a, 0x17, 0x2e, 0x61,
	0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x43, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x4b, 0x0a, 0x0f, 0x46, 0x64, 0x64, 0x43, 0x72,
	0x65, 0x61, 0x74, 0x65, 0x55, 0x73, 0x65, 0x72, 0x56, 0x32, 0x12, 0x1d, 0x2e, 0x61, 0x63, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x46, 0x64, 0x64, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x55, 0x73,
	0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x17, 0x2e, 0x61, 0x63, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x2e, 0x43, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x22, 0x00, 0x12, 0x3c, 0x0a, 0x13, 0x46, 0x64, 0x64, 0x55, 0x73, 0x65, 0x72, 0x46,
	0x69, 0x6e, 0x64, 0x42, 0x79, 0x55, 0x73, 0x65, 0x72, 0x49, 0x64, 0x12, 0x11, 0x2e, 0x61, 0x63,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x1a, 0x10,
	0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x46, 0x64, 0x64, 0x49, 0x6e, 0x66, 0x6f,
	0x22, 0x00, 0x12, 0x39, 0x0a, 0x0c, 0x55, 0x73, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x42, 0x79,
	0x49, 0x64, 0x12, 0x14, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x49, 0x6e, 0x66,
	0x6f, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x11, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x22, 0x00, 0x12, 0x45, 0x0a,
	0x0c, 0x56, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x18, 0x2e,
	0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x56, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65,
	0x43, 0x6f, 0x64, 0x65, 0x52, 0x65, 0x71, 0x1a, 0x19, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x2e, 0x56, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x43, 0x6f, 0x64, 0x65, 0x52, 0x65,
	0x73, 0x70, 0x22, 0x00, 0x12, 0x45, 0x0a, 0x0e, 0x53, 0x61, 0x76, 0x65, 0x53, 0x75, 0x62, 0x6d,
	0x69, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x1a, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x2e, 0x53, 0x75, 0x62, 0x6d, 0x69, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x1a, 0x17, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x43, 0x6f, 0x6d,
	0x6d, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x30, 0x0a, 0x06, 0x53,
	0x6d, 0x73, 0x4c, 0x6f, 0x67, 0x12, 0x12, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e,
	0x53, 0x6d, 0x73, 0x4c, 0x6f, 0x67, 0x52, 0x65, 0x71, 0x1a, 0x12, 0x2e, 0x61, 0x63, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x2e, 0x53, 0x6d, 0x73, 0x4c, 0x6f, 0x67, 0x52, 0x65, 0x73, 0x12, 0x49, 0x0a,
	0x0e, 0x52, 0x65, 0x76, 0x69, 0x65, 0x77, 0x52, 0x65, 0x61, 0x6c, 0x4e, 0x61, 0x6d, 0x65, 0x12,
	0x1e, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x52, 0x65, 0x76, 0x69, 0x65, 0x77,
	0x52, 0x65, 0x61, 0x6c, 0x4e, 0x61, 0x6d, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a,
	0x17, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x43, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x5d, 0x0a, 0x12, 0x52, 0x65, 0x76, 0x69,
	0x65, 0x77, 0x52, 0x65, 0x61, 0x6c, 0x4e, 0x61, 0x6d, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x22,
	0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x52, 0x65, 0x76, 0x69, 0x65, 0x77, 0x52,
	0x65, 0x61, 0x6c, 0x4e, 0x61, 0x6d, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x1a, 0x23, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x52, 0x65, 0x76,
	0x69, 0x65, 0x77, 0x52, 0x65, 0x61, 0x6c, 0x4e, 0x61, 0x6d, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x3f, 0x0a, 0x09, 0x42, 0x69, 0x6e, 0x64, 0x53,
	0x74, 0x61, 0x66, 0x66, 0x12, 0x19, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x42,
	0x69, 0x6e, 0x64, 0x53, 0x74, 0x61, 0x66, 0x66, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a,
	0x17, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x43, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x6c, 0x0a, 0x17, 0x53, 0x65, 0x6c, 0x6c,
	0x65, 0x72, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x52, 0x65, 0x6c, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x73, 0x12, 0x27, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x53, 0x65,
	0x6c, 0x6c, 0x65, 0x72, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x52, 0x65, 0x6c, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x28, 0x2e, 0x61,
	0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x53, 0x65, 0x6c, 0x6c, 0x65, 0x72, 0x43, 0x75, 0x73,
	0x74, 0x6f, 0x6d, 0x65, 0x72, 0x52, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x78, 0x0a, 0x1b, 0x53, 0x65, 0x6c, 0x6c, 0x65, 0x72,
	0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x52, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x73, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x2b, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e,
	0x53, 0x65, 0x6c, 0x6c, 0x65, 0x72, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x52, 0x65,
	0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x1a, 0x2c, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x53, 0x65, 0x6c,
	0x6c, 0x65, 0x72, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x52, 0x65, 0x6c, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x73, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x12, 0x60, 0x0a, 0x13, 0x53, 0x65, 0x6c, 0x6c, 0x65, 0x72, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d,
	0x65, 0x72, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x23, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x2e, 0x53, 0x65, 0x6c, 0x6c, 0x65, 0x72, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72,
	0x43, 0x6f, 0x75, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x24, 0x2e, 0x61,
	0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x53, 0x65, 0x6c, 0x6c, 0x65, 0x72, 0x43, 0x75, 0x73,
	0x74, 0x6f, 0x6d, 0x65, 0x72, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x42, 0x0c, 0x5a, 0x0a, 0x2e, 0x2f, 0x3b, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_account_account_proto_rawDescOnce sync.Once
	file_api_account_account_proto_rawDescData = file_api_account_account_proto_rawDesc
)

func file_api_account_account_proto_rawDescGZIP() []byte {
	file_api_account_account_proto_rawDescOnce.Do(func() {
		file_api_account_account_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_account_account_proto_rawDescData)
	})
	return file_api_account_account_proto_rawDescData
}

var file_api_account_account_proto_msgTypes = make([]protoimpl.MessageInfo, 121)
var file_api_account_account_proto_goTypes = []interface{}{
	(*SmsLogReq)(nil),                                 // 0: account.SmsLogReq
	(*SmsLogRes)(nil),                                 // 1: account.SmsLogRes
	(*SmsLogInfo)(nil),                                // 2: account.SmsLogInfo
	(*BindStaffRequest)(nil),                          // 3: account.BindStaffRequest
	(*ReviewRealNameListRequest)(nil),                 // 4: account.ReviewRealNameListRequest
	(*ReviewRealNameListResponse)(nil),                // 5: account.ReviewRealNameListResponse
	(*ReviewRealNameInfo)(nil),                        // 6: account.ReviewRealNameInfo
	(*ReviewRealNameRequest)(nil),                     // 7: account.ReviewRealNameRequest
	(*SubmitInfoRequest)(nil),                         // 8: account.SubmitInfoRequest
	(*CheckBeforeRegisterRequest)(nil),                // 9: account.CheckBeforeRegisterRequest
	(*SampleAccountRequest)(nil),                      // 10: account.SampleAccountRequest
	(*SampleAccountResponse)(nil),                     // 11: account.SampleAccountResponse
	(*LoginAndSqueezeOtherResponse)(nil),              // 12: account.LoginAndSqueezeOtherResponse
	(*IsSamePersonResponse)(nil),                      // 13: account.IsSamePersonResponse
	(*IsSamePersonRequest)(nil),                       // 14: account.IsSamePersonRequest
	(*UpdatePassportStatusRequest)(nil),               // 15: account.UpdatePassportStatusRequest
	(*CreateChainAccountResponse)(nil),                // 16: account.CreateChainAccountResponse
	(*UsersByJobNumRequest)(nil),                      // 17: account.UsersByJobNumRequest
	(*QueryPersonnelWithTheSameNameRequest)(nil),      // 18: account.QueryPersonnelWithTheSameNameRequest
	(*QueryPersonnelWithTheSameNameResponse)(nil),     // 19: account.QueryPersonnelWithTheSameNameResponse
	(*ListV2Request)(nil),                             // 20: account.ListV2Request
	(*SendClockInWechatRequest)(nil),                  // 21: account.SendClockInWechatRequest
	(*MailAccountByNickNameRequest)(nil),              // 22: account.MailAccountByNickNameRequest
	(*CreateMaiAccountRequest)(nil),                   // 23: account.CreateMaiAccountRequest
	(*MaiAccountResponse)(nil),                        // 24: account.MaiAccountResponse
	(*FddRemoveUserRequest)(nil),                      // 25: account.FddRemoveUserRequest
	(*FddCreateUserRequest)(nil),                      // 26: account.FddCreateUserRequest
	(*WxBoxUserInfoRequest)(nil),                      // 27: account.WxBoxUserInfoRequest
	(*WxGetOpenIdByCodeRequest)(nil),                  // 28: account.WxGetOpenIdByCodeRequest
	(*WxGetOpenIdByCodeResponse)(nil),                 // 29: account.WxGetOpenIdByCodeResponse
	(*WxBoxTelNumByCodeResponse)(nil),                 // 30: account.WxBoxTelNumByCodeResponse
	(*WxBoxUserInfo)(nil),                             // 31: account.WxBoxUserInfo
	(*FddInfo)(nil),                                   // 32: account.FddInfo
	(*UserInfo)(nil),                                  // 33: account.UserInfo
	(*CommonRequest)(nil),                             // 34: account.CommonRequest
	(*WxAppRequest)(nil),                              // 35: account.WxAppRequest
	(*WxAppResponse)(nil),                             // 36: account.WxAppResponse
	(*WxUserUpdateRequest)(nil),                       // 37: account.WxUserUpdateRequest
	(*WxUserOrCreateRequest)(nil),                     // 38: account.WxUserOrCreateRequest
	(*WxUserResponse)(nil),                            // 39: account.WxUserResponse
	(*LoginLogsResponse)(nil),                         // 40: account.LoginLogsResponse
	(*LoginLog)(nil),                                  // 41: account.LoginLog
	(*OnlineLogByIdRequest)(nil),                      // 42: account.OnlineLogByIdRequest
	(*LoginInfosByUserIdRequest)(nil),                 // 43: account.LoginInfosByUserIdRequest
	(*SendNewTelNumMsgRequest)(nil),                   // 44: account.SendNewTelNumMsgRequest
	(*UserByTelRequest)(nil),                          // 45: account.UserByTelRequest
	(*CommonResponse)(nil),                            // 46: account.CommonResponse
	(*UsersByTelRequest)(nil),                         // 47: account.UsersByTelRequest
	(*ListByIDsRequest)(nil),                          // 48: account.ListByIDsRequest
	(*SendMsgRequest)(nil),                            // 49: account.SendMsgRequest
	(*SendCustomMsgRequest)(nil),                      // 50: account.SendCustomMsgRequest
	(*CheckMsgRequest)(nil),                           // 51: account.CheckMsgRequest
	(*SendMsgStatusResponse)(nil),                     // 52: account.SendMsgStatusResponse
	(*RemoveRequest)(nil),                             // 53: account.RemoveRequest
	(*WriteOffRequest)(nil),                           // 54: account.WriteOffRequest
	(*WriteOffListRequest)(nil),                       // 55: account.WriteOffListRequest
	(*WriteOffApproveRequest)(nil),                    // 56: account.WriteOffApproveRequest
	(*WriteOffListResponse)(nil),                      // 57: account.WriteOffListResponse
	(*RemoveResponse)(nil),                            // 58: account.RemoveResponse
	(*UpdateRequest)(nil),                             // 59: account.UpdateRequest
	(*Operator)(nil),                                  // 60: account.Operator
	(*TrainVideo)(nil),                                // 61: account.trainVideo
	(*UpdateResponse)(nil),                            // 62: account.UpdateResponse
	(*PrivacyInfoRequest)(nil),                        // 63: account.PrivacyInfoRequest
	(*ListRequest)(nil),                               // 64: account.ListRequest
	(*ListResponse)(nil),                              // 65: account.ListResponse
	(*InfoRequest)(nil),                               // 66: account.InfoRequest
	(*InfoResponse)(nil),                              // 67: account.InfoResponse
	(*DecryptJwtResponse)(nil),                        // 68: account.DecryptJwtResponse
	(*DecryptJwtRequest)(nil),                         // 69: account.DecryptJwtRequest
	(*CheckPwdRequest)(nil),                           // 70: account.CheckPwdRequest
	(*AuthenticationRequest)(nil),                     // 71: account.AuthenticationRequest
	(*RequestStatus)(nil),                             // 72: account.RequestStatus
	(*RegistRequest)(nil),                             // 73: account.RegistRequest
	(*UserExtend)(nil),                                // 74: account.UserExtend
	(*Passport)(nil),                                  // 75: account.Passport
	(*LoginRequest)(nil),                              // 76: account.LoginRequest
	(*TokenInfo)(nil),                                 // 77: account.TokenInfo
	(*Extend)(nil),                                    // 78: account.Extend
	(*Department)(nil),                                // 79: account.Department
	(*AccountInfo)(nil),                               // 80: account.AccountInfo
	(*UserInfoV2)(nil),                                // 81: account.UserInfoV2
	(*RefreshTokenRequest)(nil),                       // 82: account.RefreshTokenRequest
	(*PositionUser)(nil),                              // 83: account.PositionUser
	(*JobNumGetInfoRequest)(nil),                      // 84: account.JobNumGetInfoRequest
	(*CreateClockDeviceRequest)(nil),                  // 85: account.CreateClockDeviceRequest
	(*UpdateClockDeviceRequest)(nil),                  // 86: account.UpdateClockDeviceRequest
	(*ClockDeviceResponse)(nil),                       // 87: account.ClockDeviceResponse
	(*RemoveClockDeviceRequest)(nil),                  // 88: account.RemoveClockDeviceRequest
	(*ClockDeviceListRequest)(nil),                    // 89: account.ClockDeviceListRequest
	(*ClockDeviceListResponse)(nil),                   // 90: account.ClockDeviceListResponse
	(*ClockUser)(nil),                                 // 91: account.ClockUser
	(*ClockDeviceInfo)(nil),                           // 92: account.ClockDeviceInfo
	(*ClockDeviceInfoResponse)(nil),                   // 93: account.ClockDeviceInfoResponse
	(*ClockUserRel)(nil),                              // 94: account.ClockUserRel
	(*ClockDeviceInfoRequest)(nil),                    // 95: account.ClockDeviceInfoRequest
	(*ClockBatchBindRequest)(nil),                     // 96: account.ClockBatchBindRequest
	(*ClockBatchListResponse)(nil),                    // 97: account.ClockBatchListResponse
	(*ClockUserDeviceBatch)(nil),                      // 98: account.ClockUserDeviceBatch
	(*ClockLogInfo)(nil),                              // 99: account.ClockLogInfo
	(*ClockLogReq)(nil),                               // 100: account.ClockLogReq
	(*ClockLogListResponse)(nil),                      // 101: account.ClockLogListResponse
	(*SendNationMsgRequest)(nil),                      // 102: account.SendNationMsgRequest
	(*UpdateLanguageRequest)(nil),                     // 103: account.UpdateLanguageRequest
	(*UpdateLanguageResponse)(nil),                    // 104: account.UpdateLanguageResponse
	(*GenerateSliderCaptchaRequest)(nil),              // 105: account.GenerateSliderCaptchaRequest
	(*GenerateSliderCaptchaResponse)(nil),             // 106: account.GenerateSliderCaptchaResponse
	(*VerifySliderCaptchaRequest)(nil),                // 107: account.VerifySliderCaptchaRequest
	(*VerifySliderCaptchaResponse)(nil),               // 108: account.VerifySliderCaptchaResponse
	(*VerifySliderStatusRequest)(nil),                 // 109: account.VerifySliderStatusRequest
	(*VerifySliderStatusResponse)(nil),                // 110: account.VerifySliderStatusResponse
	(*ValidateCodeReq)(nil),                           // 111: account.ValidateCodeReq
	(*ValidateCodeResp)(nil),                          // 112: account.ValidateCodeResp
	(*SellerCustomerRelation)(nil),                    // 113: account.SellerCustomerRelation
	(*SellerCustomerRelationsRequest)(nil),            // 114: account.SellerCustomerRelationsRequest
	(*SellerCustomerRelationsResponse)(nil),           // 115: account.SellerCustomerRelationsResponse
	(*SellerCustomerRelationsListRequest)(nil),        // 116: account.SellerCustomerRelationsListRequest
	(*SellerCustomerRelationsListResponse)(nil),       // 117: account.SellerCustomerRelationsListResponse
	(*SellerCustomerCountRequest)(nil),                // 118: account.SellerCustomerCountRequest
	(*SellerCustomerCountResponse)(nil),               // 119: account.SellerCustomerCountResponse
	(*SellerCustomerCountResponse_CustomerCount)(nil), // 120: account.SellerCustomerCountResponse.CustomerCount
}
var file_api_account_account_proto_depIdxs = []int32{
	2,   // 0: account.SmsLogRes.list:type_name -> account.SmsLogInfo
	7,   // 1: account.ReviewRealNameListResponse.data:type_name -> account.ReviewRealNameRequest
	33,  // 2: account.WxBoxUserInfo.user:type_name -> account.UserInfo
	32,  // 3: account.WxBoxUserInfo.fdd:type_name -> account.FddInfo
	75,  // 4: account.UserInfo.passport:type_name -> account.Passport
	41,  // 5: account.LoginLogsResponse.Data:type_name -> account.LoginLog
	54,  // 6: account.WriteOffListResponse.writeOffList:type_name -> account.WriteOffRequest
	78,  // 7: account.UpdateRequest.Extend:type_name -> account.Extend
	61,  // 8: account.UpdateRequest.TrainVideos:type_name -> account.trainVideo
	60,  // 9: account.UpdateRequest.operator:type_name -> account.Operator
	74,  // 10: account.UpdateRequest.userExtend:type_name -> account.UserExtend
	80,  // 11: account.ListResponse.Data:type_name -> account.AccountInfo
	80,  // 12: account.InfoResponse.Info:type_name -> account.AccountInfo
	78,  // 13: account.RegistRequest.Extend:type_name -> account.Extend
	60,  // 14: account.RegistRequest.operator:type_name -> account.Operator
	75,  // 15: account.RegistRequest.passport:type_name -> account.Passport
	74,  // 16: account.RegistRequest.UserExtend:type_name -> account.UserExtend
	80,  // 17: account.TokenInfo.AccountInfo:type_name -> account.AccountInfo
	78,  // 18: account.AccountInfo.Extend:type_name -> account.Extend
	79,  // 19: account.AccountInfo.Departments:type_name -> account.Department
	83,  // 20: account.AccountInfo.Positions:type_name -> account.PositionUser
	91,  // 21: account.AccountInfo.clocks:type_name -> account.ClockUser
	61,  // 22: account.AccountInfo.TrainVideos:type_name -> account.trainVideo
	60,  // 23: account.AccountInfo.operator:type_name -> account.Operator
	74,  // 24: account.AccountInfo.userExtend:type_name -> account.UserExtend
	78,  // 25: account.UserInfoV2.Extend:type_name -> account.Extend
	60,  // 26: account.UserInfoV2.operator:type_name -> account.Operator
	92,  // 27: account.ClockDeviceListResponse.data:type_name -> account.ClockDeviceInfo
	92,  // 28: account.ClockUser.device:type_name -> account.ClockDeviceInfo
	94,  // 29: account.ClockDeviceInfo.data:type_name -> account.ClockUserRel
	94,  // 30: account.ClockDeviceInfoResponse.data:type_name -> account.ClockUserRel
	98,  // 31: account.ClockBatchListResponse.data:type_name -> account.ClockUserDeviceBatch
	99,  // 32: account.ClockLogListResponse.data:type_name -> account.ClockLogInfo
	113, // 33: account.SellerCustomerRelationsListResponse.data:type_name -> account.SellerCustomerRelation
	120, // 34: account.SellerCustomerCountResponse.customers:type_name -> account.SellerCustomerCountResponse.CustomerCount
	76,  // 35: account.Account.Login:input_type -> account.LoginRequest
	82,  // 36: account.Account.RefreshToken:input_type -> account.RefreshTokenRequest
	69,  // 37: account.Account.Logout:input_type -> account.DecryptJwtRequest
	35,  // 38: account.Account.WxApp:input_type -> account.WxAppRequest
	38,  // 39: account.Account.WxUserInfo:input_type -> account.WxUserOrCreateRequest
	38,  // 40: account.Account.WxUserCreate:input_type -> account.WxUserOrCreateRequest
	37,  // 41: account.Account.WxUserUpdate:input_type -> account.WxUserUpdateRequest
	28,  // 42: account.Account.WxGetOpenIdByCode:input_type -> account.WxGetOpenIdByCodeRequest
	28,  // 43: account.Account.WxBoxLogin:input_type -> account.WxGetOpenIdByCodeRequest
	27,  // 44: account.Account.WxBoxUserInfoByOpenId:input_type -> account.WxBoxUserInfoRequest
	28,  // 45: account.Account.WxBoxTelNumByCode:input_type -> account.WxGetOpenIdByCodeRequest
	31,  // 46: account.Account.WxBoxUpdateUser:input_type -> account.WxBoxUserInfo
	31,  // 47: account.Account.WxBoxCreateUser:input_type -> account.WxBoxUserInfo
	26,  // 48: account.Account.FddCreateUser:input_type -> account.FddCreateUserRequest
	26,  // 49: account.Account.FddUpdateUser:input_type -> account.FddCreateUserRequest
	25,  // 50: account.Account.FddRemoveUser:input_type -> account.FddRemoveUserRequest
	34,  // 51: account.Account.OffLine:input_type -> account.CommonRequest
	43,  // 52: account.Account.OnlineLog:input_type -> account.LoginInfosByUserIdRequest
	42,  // 53: account.Account.OnlineLogById:input_type -> account.OnlineLogByIdRequest
	70,  // 54: account.Account.CheckPwd:input_type -> account.CheckPwdRequest
	73,  // 55: account.Account.Register:input_type -> account.RegistRequest
	73,  // 56: account.Account.RegisterOrExist:input_type -> account.RegistRequest
	49,  // 57: account.Account.SendMsg:input_type -> account.SendMsgRequest
	50,  // 58: account.Account.SendCustomMsg:input_type -> account.SendCustomMsgRequest
	50,  // 59: account.Account.SendExCustomMsg:input_type -> account.SendCustomMsgRequest
	49,  // 60: account.Account.SendMsgRegister:input_type -> account.SendMsgRequest
	51,  // 61: account.Account.CheckMsg:input_type -> account.CheckMsgRequest
	44,  // 62: account.Account.SendNewTelNumMsg:input_type -> account.SendNewTelNumMsgRequest
	44,  // 63: account.Account.UpdateTelNum:input_type -> account.SendNewTelNumMsgRequest
	71,  // 64: account.Account.Authentication:input_type -> account.AuthenticationRequest
	69,  // 65: account.Account.DecryptJwt:input_type -> account.DecryptJwtRequest
	66,  // 66: account.Account.Info:input_type -> account.InfoRequest
	84,  // 67: account.Account.JobNumGetInfo:input_type -> account.JobNumGetInfoRequest
	64,  // 68: account.Account.List:input_type -> account.ListRequest
	64,  // 69: account.Account.RandList:input_type -> account.ListRequest
	48,  // 70: account.Account.ListByIDs:input_type -> account.ListByIDsRequest
	53,  // 71: account.Account.Remove:input_type -> account.RemoveRequest
	54,  // 72: account.Account.WriteOff:input_type -> account.WriteOffRequest
	53,  // 73: account.Account.WriteOffApp:input_type -> account.RemoveRequest
	56,  // 74: account.Account.WriteOffInfo:input_type -> account.WriteOffApproveRequest
	55,  // 75: account.Account.FindWriteOffList:input_type -> account.WriteOffListRequest
	56,  // 76: account.Account.WriteOffUpdate:input_type -> account.WriteOffApproveRequest
	59,  // 77: account.Account.Update:input_type -> account.UpdateRequest
	63,  // 78: account.Account.PrivacyInfo:input_type -> account.PrivacyInfoRequest
	47,  // 79: account.Account.UsersByTel:input_type -> account.UsersByTelRequest
	45,  // 80: account.Account.UserByTel:input_type -> account.UserByTelRequest
	9,   // 81: account.Account.CheckBeforeRegister:input_type -> account.CheckBeforeRegisterRequest
	49,  // 82: account.Account.OnlySendMsg:input_type -> account.SendMsgRequest
	51,  // 83: account.Account.OnlyCheckMsg:input_type -> account.CheckMsgRequest
	85,  // 84: account.Account.CreateClockDevice:input_type -> account.CreateClockDeviceRequest
	86,  // 85: account.Account.UpdateClockDevice:input_type -> account.UpdateClockDeviceRequest
	88,  // 86: account.Account.RemoveClockDevice:input_type -> account.RemoveClockDeviceRequest
	89,  // 87: account.Account.ClockDeviceList:input_type -> account.ClockDeviceListRequest
	95,  // 88: account.Account.ClockDeviceInfo:input_type -> account.ClockDeviceInfoRequest
	88,  // 89: account.Account.ClockDeviceSingleUntie:input_type -> account.RemoveClockDeviceRequest
	97,  // 90: account.Account.ClockDeviceBatchBind:input_type -> account.ClockBatchListResponse
	96,  // 91: account.Account.ClockDeviceBatchUntie:input_type -> account.ClockBatchBindRequest
	96,  // 92: account.Account.ClockDeviceBatchList:input_type -> account.ClockBatchBindRequest
	98,  // 93: account.Account.UpdateDeviceRelevance:input_type -> account.ClockUserDeviceBatch
	22,  // 94: account.Account.MailAccountByNickName:input_type -> account.MailAccountByNickNameRequest
	23,  // 95: account.Account.CreateMaiAccount:input_type -> account.CreateMaiAccountRequest
	99,  // 96: account.Account.CreateClockLog:input_type -> account.ClockLogInfo
	21,  // 97: account.Account.SendClockInWechat:input_type -> account.SendClockInWechatRequest
	100, // 98: account.Account.FindClockLogList:input_type -> account.ClockLogReq
	21,  // 99: account.Account.SendStrangerClockInWechat:input_type -> account.SendClockInWechatRequest
	20,  // 100: account.Account.ListV2:input_type -> account.ListV2Request
	34,  // 101: account.Account.CreateChainAccount:input_type -> account.CommonRequest
	102, // 102: account.Account.SendNationMsg:input_type -> account.SendNationMsgRequest
	103, // 103: account.Account.UpdateLanguage:input_type -> account.UpdateLanguageRequest
	105, // 104: account.Account.GenerateSliderCaptcha:input_type -> account.GenerateSliderCaptchaRequest
	107, // 105: account.Account.VerifySliderCaptcha:input_type -> account.VerifySliderCaptchaRequest
	109, // 106: account.Account.VerifySliderStatus:input_type -> account.VerifySliderStatusRequest
	10,  // 107: account.Account.SampleAccount:input_type -> account.SampleAccountRequest
	76,  // 108: account.Account.LoginAndSqueezeOther:input_type -> account.LoginRequest
	18,  // 109: account.Account.QueryPersonnelWithTheSameName:input_type -> account.QueryPersonnelWithTheSameNameRequest
	17,  // 110: account.Account.UsersByJobNum:input_type -> account.UsersByJobNumRequest
	14,  // 111: account.Account.IsSamePerson:input_type -> account.IsSamePersonRequest
	33,  // 112: account.Account.CreateRealNameOrPassPort:input_type -> account.UserInfo
	26,  // 113: account.Account.FddCreateUserV2:input_type -> account.FddCreateUserRequest
	33,  // 114: account.Account.FddUserFindByUserId:input_type -> account.UserInfo
	66,  // 115: account.Account.UserInfoById:input_type -> account.InfoRequest
	111, // 116: account.Account.ValidateCode:input_type -> account.ValidateCodeReq
	8,   // 117: account.Account.SaveSubmitInfo:input_type -> account.SubmitInfoRequest
	0,   // 118: account.Account.SmsLog:input_type -> account.SmsLogReq
	7,   // 119: account.Account.ReviewRealName:input_type -> account.ReviewRealNameRequest
	4,   // 120: account.Account.ReviewRealNameList:input_type -> account.ReviewRealNameListRequest
	3,   // 121: account.Account.BindStaff:input_type -> account.BindStaffRequest
	114, // 122: account.Account.SellerCustomerRelations:input_type -> account.SellerCustomerRelationsRequest
	116, // 123: account.Account.SellerCustomerRelationsList:input_type -> account.SellerCustomerRelationsListRequest
	118, // 124: account.Account.SellerCustomerCount:input_type -> account.SellerCustomerCountRequest
	77,  // 125: account.Account.Login:output_type -> account.TokenInfo
	77,  // 126: account.Account.RefreshToken:output_type -> account.TokenInfo
	46,  // 127: account.Account.Logout:output_type -> account.CommonResponse
	36,  // 128: account.Account.WxApp:output_type -> account.WxAppResponse
	39,  // 129: account.Account.WxUserInfo:output_type -> account.WxUserResponse
	39,  // 130: account.Account.WxUserCreate:output_type -> account.WxUserResponse
	39,  // 131: account.Account.WxUserUpdate:output_type -> account.WxUserResponse
	29,  // 132: account.Account.WxGetOpenIdByCode:output_type -> account.WxGetOpenIdByCodeResponse
	31,  // 133: account.Account.WxBoxLogin:output_type -> account.WxBoxUserInfo
	31,  // 134: account.Account.WxBoxUserInfoByOpenId:output_type -> account.WxBoxUserInfo
	30,  // 135: account.Account.WxBoxTelNumByCode:output_type -> account.WxBoxTelNumByCodeResponse
	46,  // 136: account.Account.WxBoxUpdateUser:output_type -> account.CommonResponse
	46,  // 137: account.Account.WxBoxCreateUser:output_type -> account.CommonResponse
	46,  // 138: account.Account.FddCreateUser:output_type -> account.CommonResponse
	46,  // 139: account.Account.FddUpdateUser:output_type -> account.CommonResponse
	46,  // 140: account.Account.FddRemoveUser:output_type -> account.CommonResponse
	46,  // 141: account.Account.OffLine:output_type -> account.CommonResponse
	40,  // 142: account.Account.OnlineLog:output_type -> account.LoginLogsResponse
	41,  // 143: account.Account.OnlineLogById:output_type -> account.LoginLog
	62,  // 144: account.Account.CheckPwd:output_type -> account.UpdateResponse
	72,  // 145: account.Account.Register:output_type -> account.RequestStatus
	72,  // 146: account.Account.RegisterOrExist:output_type -> account.RequestStatus
	52,  // 147: account.Account.SendMsg:output_type -> account.SendMsgStatusResponse
	52,  // 148: account.Account.SendCustomMsg:output_type -> account.SendMsgStatusResponse
	52,  // 149: account.Account.SendExCustomMsg:output_type -> account.SendMsgStatusResponse
	52,  // 150: account.Account.SendMsgRegister:output_type -> account.SendMsgStatusResponse
	52,  // 151: account.Account.CheckMsg:output_type -> account.SendMsgStatusResponse
	52,  // 152: account.Account.SendNewTelNumMsg:output_type -> account.SendMsgStatusResponse
	52,  // 153: account.Account.UpdateTelNum:output_type -> account.SendMsgStatusResponse
	72,  // 154: account.Account.Authentication:output_type -> account.RequestStatus
	68,  // 155: account.Account.DecryptJwt:output_type -> account.DecryptJwtResponse
	67,  // 156: account.Account.Info:output_type -> account.InfoResponse
	67,  // 157: account.Account.JobNumGetInfo:output_type -> account.InfoResponse
	65,  // 158: account.Account.List:output_type -> account.ListResponse
	65,  // 159: account.Account.RandList:output_type -> account.ListResponse
	65,  // 160: account.Account.ListByIDs:output_type -> account.ListResponse
	58,  // 161: account.Account.Remove:output_type -> account.RemoveResponse
	58,  // 162: account.Account.WriteOff:output_type -> account.RemoveResponse
	58,  // 163: account.Account.WriteOffApp:output_type -> account.RemoveResponse
	54,  // 164: account.Account.WriteOffInfo:output_type -> account.WriteOffRequest
	57,  // 165: account.Account.FindWriteOffList:output_type -> account.WriteOffListResponse
	58,  // 166: account.Account.WriteOffUpdate:output_type -> account.RemoveResponse
	62,  // 167: account.Account.Update:output_type -> account.UpdateResponse
	80,  // 168: account.Account.PrivacyInfo:output_type -> account.AccountInfo
	65,  // 169: account.Account.UsersByTel:output_type -> account.ListResponse
	67,  // 170: account.Account.UserByTel:output_type -> account.InfoResponse
	46,  // 171: account.Account.CheckBeforeRegister:output_type -> account.CommonResponse
	52,  // 172: account.Account.OnlySendMsg:output_type -> account.SendMsgStatusResponse
	52,  // 173: account.Account.OnlyCheckMsg:output_type -> account.SendMsgStatusResponse
	87,  // 174: account.Account.CreateClockDevice:output_type -> account.ClockDeviceResponse
	87,  // 175: account.Account.UpdateClockDevice:output_type -> account.ClockDeviceResponse
	87,  // 176: account.Account.RemoveClockDevice:output_type -> account.ClockDeviceResponse
	90,  // 177: account.Account.ClockDeviceList:output_type -> account.ClockDeviceListResponse
	93,  // 178: account.Account.ClockDeviceInfo:output_type -> account.ClockDeviceInfoResponse
	87,  // 179: account.Account.ClockDeviceSingleUntie:output_type -> account.ClockDeviceResponse
	93,  // 180: account.Account.ClockDeviceBatchBind:output_type -> account.ClockDeviceInfoResponse
	93,  // 181: account.Account.ClockDeviceBatchUntie:output_type -> account.ClockDeviceInfoResponse
	97,  // 182: account.Account.ClockDeviceBatchList:output_type -> account.ClockBatchListResponse
	87,  // 183: account.Account.UpdateDeviceRelevance:output_type -> account.ClockDeviceResponse
	24,  // 184: account.Account.MailAccountByNickName:output_type -> account.MaiAccountResponse
	46,  // 185: account.Account.CreateMaiAccount:output_type -> account.CommonResponse
	87,  // 186: account.Account.CreateClockLog:output_type -> account.ClockDeviceResponse
	46,  // 187: account.Account.SendClockInWechat:output_type -> account.CommonResponse
	101, // 188: account.Account.FindClockLogList:output_type -> account.ClockLogListResponse
	46,  // 189: account.Account.SendStrangerClockInWechat:output_type -> account.CommonResponse
	65,  // 190: account.Account.ListV2:output_type -> account.ListResponse
	16,  // 191: account.Account.CreateChainAccount:output_type -> account.CreateChainAccountResponse
	52,  // 192: account.Account.SendNationMsg:output_type -> account.SendMsgStatusResponse
	104, // 193: account.Account.UpdateLanguage:output_type -> account.UpdateLanguageResponse
	106, // 194: account.Account.GenerateSliderCaptcha:output_type -> account.GenerateSliderCaptchaResponse
	108, // 195: account.Account.VerifySliderCaptcha:output_type -> account.VerifySliderCaptchaResponse
	110, // 196: account.Account.VerifySliderStatus:output_type -> account.VerifySliderStatusResponse
	11,  // 197: account.Account.SampleAccount:output_type -> account.SampleAccountResponse
	77,  // 198: account.Account.LoginAndSqueezeOther:output_type -> account.TokenInfo
	19,  // 199: account.Account.QueryPersonnelWithTheSameName:output_type -> account.QueryPersonnelWithTheSameNameResponse
	65,  // 200: account.Account.UsersByJobNum:output_type -> account.ListResponse
	13,  // 201: account.Account.IsSamePerson:output_type -> account.IsSamePersonResponse
	46,  // 202: account.Account.CreateRealNameOrPassPort:output_type -> account.CommonResponse
	46,  // 203: account.Account.FddCreateUserV2:output_type -> account.CommonResponse
	32,  // 204: account.Account.FddUserFindByUserId:output_type -> account.FddInfo
	33,  // 205: account.Account.UserInfoById:output_type -> account.UserInfo
	112, // 206: account.Account.ValidateCode:output_type -> account.ValidateCodeResp
	46,  // 207: account.Account.SaveSubmitInfo:output_type -> account.CommonResponse
	1,   // 208: account.Account.SmsLog:output_type -> account.SmsLogRes
	46,  // 209: account.Account.ReviewRealName:output_type -> account.CommonResponse
	5,   // 210: account.Account.ReviewRealNameList:output_type -> account.ReviewRealNameListResponse
	46,  // 211: account.Account.BindStaff:output_type -> account.CommonResponse
	115, // 212: account.Account.SellerCustomerRelations:output_type -> account.SellerCustomerRelationsResponse
	117, // 213: account.Account.SellerCustomerRelationsList:output_type -> account.SellerCustomerRelationsListResponse
	119, // 214: account.Account.SellerCustomerCount:output_type -> account.SellerCustomerCountResponse
	125, // [125:215] is the sub-list for method output_type
	35,  // [35:125] is the sub-list for method input_type
	35,  // [35:35] is the sub-list for extension type_name
	35,  // [35:35] is the sub-list for extension extendee
	0,   // [0:35] is the sub-list for field type_name
}

func init() { file_api_account_account_proto_init() }
func file_api_account_account_proto_init() {
	if File_api_account_account_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_api_account_account_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SmsLogReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_account_account_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SmsLogRes); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_account_account_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SmsLogInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_account_account_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BindStaffRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_account_account_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReviewRealNameListRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_account_account_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReviewRealNameListResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_account_account_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReviewRealNameInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_account_account_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReviewRealNameRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_account_account_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SubmitInfoRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_account_account_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CheckBeforeRegisterRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_account_account_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SampleAccountRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_account_account_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SampleAccountResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_account_account_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LoginAndSqueezeOtherResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_account_account_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*IsSamePersonResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_account_account_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*IsSamePersonRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_account_account_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdatePassportStatusRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_account_account_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateChainAccountResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_account_account_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UsersByJobNumRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_account_account_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*QueryPersonnelWithTheSameNameRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_account_account_proto_msgTypes[19].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*QueryPersonnelWithTheSameNameResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_account_account_proto_msgTypes[20].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListV2Request); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_account_account_proto_msgTypes[21].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SendClockInWechatRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_account_account_proto_msgTypes[22].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MailAccountByNickNameRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_account_account_proto_msgTypes[23].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateMaiAccountRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_account_account_proto_msgTypes[24].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MaiAccountResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_account_account_proto_msgTypes[25].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FddRemoveUserRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_account_account_proto_msgTypes[26].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FddCreateUserRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_account_account_proto_msgTypes[27].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*WxBoxUserInfoRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_account_account_proto_msgTypes[28].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*WxGetOpenIdByCodeRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_account_account_proto_msgTypes[29].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*WxGetOpenIdByCodeResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_account_account_proto_msgTypes[30].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*WxBoxTelNumByCodeResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_account_account_proto_msgTypes[31].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*WxBoxUserInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_account_account_proto_msgTypes[32].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FddInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_account_account_proto_msgTypes[33].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UserInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_account_account_proto_msgTypes[34].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CommonRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_account_account_proto_msgTypes[35].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*WxAppRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_account_account_proto_msgTypes[36].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*WxAppResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_account_account_proto_msgTypes[37].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*WxUserUpdateRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_account_account_proto_msgTypes[38].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*WxUserOrCreateRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_account_account_proto_msgTypes[39].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*WxUserResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_account_account_proto_msgTypes[40].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LoginLogsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_account_account_proto_msgTypes[41].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LoginLog); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_account_account_proto_msgTypes[42].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*OnlineLogByIdRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_account_account_proto_msgTypes[43].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LoginInfosByUserIdRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_account_account_proto_msgTypes[44].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SendNewTelNumMsgRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_account_account_proto_msgTypes[45].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UserByTelRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_account_account_proto_msgTypes[46].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CommonResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_account_account_proto_msgTypes[47].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UsersByTelRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_account_account_proto_msgTypes[48].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListByIDsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_account_account_proto_msgTypes[49].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SendMsgRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_account_account_proto_msgTypes[50].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SendCustomMsgRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_account_account_proto_msgTypes[51].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CheckMsgRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_account_account_proto_msgTypes[52].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SendMsgStatusResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_account_account_proto_msgTypes[53].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RemoveRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_account_account_proto_msgTypes[54].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*WriteOffRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_account_account_proto_msgTypes[55].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*WriteOffListRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_account_account_proto_msgTypes[56].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*WriteOffApproveRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_account_account_proto_msgTypes[57].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*WriteOffListResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_account_account_proto_msgTypes[58].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RemoveResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_account_account_proto_msgTypes[59].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_account_account_proto_msgTypes[60].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Operator); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_account_account_proto_msgTypes[61].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TrainVideo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_account_account_proto_msgTypes[62].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_account_account_proto_msgTypes[63].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PrivacyInfoRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_account_account_proto_msgTypes[64].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_account_account_proto_msgTypes[65].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_account_account_proto_msgTypes[66].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*InfoRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_account_account_proto_msgTypes[67].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*InfoResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_account_account_proto_msgTypes[68].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DecryptJwtResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_account_account_proto_msgTypes[69].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DecryptJwtRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_account_account_proto_msgTypes[70].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CheckPwdRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_account_account_proto_msgTypes[71].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AuthenticationRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_account_account_proto_msgTypes[72].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RequestStatus); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_account_account_proto_msgTypes[73].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RegistRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_account_account_proto_msgTypes[74].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UserExtend); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_account_account_proto_msgTypes[75].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Passport); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_account_account_proto_msgTypes[76].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LoginRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_account_account_proto_msgTypes[77].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TokenInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_account_account_proto_msgTypes[78].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Extend); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_account_account_proto_msgTypes[79].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Department); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_account_account_proto_msgTypes[80].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AccountInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_account_account_proto_msgTypes[81].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UserInfoV2); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_account_account_proto_msgTypes[82].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RefreshTokenRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_account_account_proto_msgTypes[83].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PositionUser); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_account_account_proto_msgTypes[84].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*JobNumGetInfoRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_account_account_proto_msgTypes[85].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateClockDeviceRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_account_account_proto_msgTypes[86].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateClockDeviceRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_account_account_proto_msgTypes[87].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ClockDeviceResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_account_account_proto_msgTypes[88].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RemoveClockDeviceRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_account_account_proto_msgTypes[89].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ClockDeviceListRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_account_account_proto_msgTypes[90].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ClockDeviceListResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_account_account_proto_msgTypes[91].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ClockUser); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_account_account_proto_msgTypes[92].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ClockDeviceInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_account_account_proto_msgTypes[93].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ClockDeviceInfoResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_account_account_proto_msgTypes[94].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ClockUserRel); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_account_account_proto_msgTypes[95].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ClockDeviceInfoRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_account_account_proto_msgTypes[96].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ClockBatchBindRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_account_account_proto_msgTypes[97].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ClockBatchListResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_account_account_proto_msgTypes[98].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ClockUserDeviceBatch); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_account_account_proto_msgTypes[99].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ClockLogInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_account_account_proto_msgTypes[100].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ClockLogReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_account_account_proto_msgTypes[101].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ClockLogListResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_account_account_proto_msgTypes[102].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SendNationMsgRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_account_account_proto_msgTypes[103].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateLanguageRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_account_account_proto_msgTypes[104].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateLanguageResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_account_account_proto_msgTypes[105].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GenerateSliderCaptchaRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_account_account_proto_msgTypes[106].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GenerateSliderCaptchaResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_account_account_proto_msgTypes[107].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*VerifySliderCaptchaRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_account_account_proto_msgTypes[108].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*VerifySliderCaptchaResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_account_account_proto_msgTypes[109].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*VerifySliderStatusRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_account_account_proto_msgTypes[110].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*VerifySliderStatusResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_account_account_proto_msgTypes[111].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ValidateCodeReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_account_account_proto_msgTypes[112].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ValidateCodeResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_account_account_proto_msgTypes[113].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SellerCustomerRelation); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_account_account_proto_msgTypes[114].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SellerCustomerRelationsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_account_account_proto_msgTypes[115].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SellerCustomerRelationsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_account_account_proto_msgTypes[116].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SellerCustomerRelationsListRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_account_account_proto_msgTypes[117].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SellerCustomerRelationsListResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_account_account_proto_msgTypes[118].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SellerCustomerCountRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_account_account_proto_msgTypes[119].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SellerCustomerCountResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_account_account_proto_msgTypes[120].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SellerCustomerCountResponse_CustomerCount); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_account_account_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   121,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_api_account_account_proto_goTypes,
		DependencyIndexes: file_api_account_account_proto_depIdxs,
		MessageInfos:      file_api_account_account_proto_msgTypes,
	}.Build()
	File_api_account_account_proto = out.File
	file_api_account_account_proto_rawDesc = nil
	file_api_account_account_proto_goTypes = nil
	file_api_account_account_proto_depIdxs = nil
}
