// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.1
// 	protoc        v5.26.1
// source: pb/payment.proto

package payment

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type AntomPayQueryRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CheckoutSessionIds []string `protobuf:"bytes,1,rep,name=checkoutSessionIds,proto3" json:"checkoutSessionIds"`
}

func (x *AntomPayQueryRequest) Reset() {
	*x = AntomPayQueryRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_payment_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AntomPayQueryRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AntomPayQueryRequest) ProtoMessage() {}

func (x *AntomPayQueryRequest) ProtoReflect() protoreflect.Message {
	mi := &file_pb_payment_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AntomPayQueryRequest.ProtoReflect.Descriptor instead.
func (*AntomPayQueryRequest) Descriptor() ([]byte, []int) {
	return file_pb_payment_proto_rawDescGZIP(), []int{0}
}

func (x *AntomPayQueryRequest) GetCheckoutSessionIds() []string {
	if x != nil {
		return x.CheckoutSessionIds
	}
	return nil
}

type AntomPayQueryResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Infos []*PaymentOrderInfo `protobuf:"bytes,1,rep,name=infos,proto3" json:"infos"`
}

func (x *AntomPayQueryResponse) Reset() {
	*x = AntomPayQueryResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_payment_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AntomPayQueryResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AntomPayQueryResponse) ProtoMessage() {}

func (x *AntomPayQueryResponse) ProtoReflect() protoreflect.Message {
	mi := &file_pb_payment_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AntomPayQueryResponse.ProtoReflect.Descriptor instead.
func (*AntomPayQueryResponse) Descriptor() ([]byte, []int) {
	return file_pb_payment_proto_rawDescGZIP(), []int{1}
}

func (x *AntomPayQueryResponse) GetInfos() []*PaymentOrderInfo {
	if x != nil {
		return x.Infos
	}
	return nil
}

type AntomNotifyPayRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	NotifyType    string `protobuf:"bytes,1,opt,name=notifyType,proto3" json:"notifyType"`
	RequestId     string `protobuf:"bytes,2,opt,name=requestId,proto3" json:"requestId"`
	PaymentId     string `protobuf:"bytes,3,opt,name=paymentId,proto3" json:"paymentId"`
	PaymentTime   string `protobuf:"bytes,4,opt,name=paymentTime,proto3" json:"paymentTime"`
	ResultStatus  string `protobuf:"bytes,5,opt,name=resultStatus,proto3" json:"resultStatus"`
	ResultMessage string `protobuf:"bytes,6,opt,name=resultMessage,proto3" json:"resultMessage"`
	ChannelCode   string `protobuf:"bytes,7,opt,name=channelCode,proto3" json:"channelCode"`
}

func (x *AntomNotifyPayRequest) Reset() {
	*x = AntomNotifyPayRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_payment_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AntomNotifyPayRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AntomNotifyPayRequest) ProtoMessage() {}

func (x *AntomNotifyPayRequest) ProtoReflect() protoreflect.Message {
	mi := &file_pb_payment_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AntomNotifyPayRequest.ProtoReflect.Descriptor instead.
func (*AntomNotifyPayRequest) Descriptor() ([]byte, []int) {
	return file_pb_payment_proto_rawDescGZIP(), []int{2}
}

func (x *AntomNotifyPayRequest) GetNotifyType() string {
	if x != nil {
		return x.NotifyType
	}
	return ""
}

func (x *AntomNotifyPayRequest) GetRequestId() string {
	if x != nil {
		return x.RequestId
	}
	return ""
}

func (x *AntomNotifyPayRequest) GetPaymentId() string {
	if x != nil {
		return x.PaymentId
	}
	return ""
}

func (x *AntomNotifyPayRequest) GetPaymentTime() string {
	if x != nil {
		return x.PaymentTime
	}
	return ""
}

func (x *AntomNotifyPayRequest) GetResultStatus() string {
	if x != nil {
		return x.ResultStatus
	}
	return ""
}

func (x *AntomNotifyPayRequest) GetResultMessage() string {
	if x != nil {
		return x.ResultMessage
	}
	return ""
}

func (x *AntomNotifyPayRequest) GetChannelCode() string {
	if x != nil {
		return x.ChannelCode
	}
	return ""
}

type AntomNotifyPayResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status     string `protobuf:"bytes,1,opt,name=status,proto3" json:"status"`
	OutTradeNo string `protobuf:"bytes,2,opt,name=outTradeNo,proto3" json:"outTradeNo"`
}

func (x *AntomNotifyPayResponse) Reset() {
	*x = AntomNotifyPayResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_payment_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AntomNotifyPayResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AntomNotifyPayResponse) ProtoMessage() {}

func (x *AntomNotifyPayResponse) ProtoReflect() protoreflect.Message {
	mi := &file_pb_payment_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AntomNotifyPayResponse.ProtoReflect.Descriptor instead.
func (*AntomNotifyPayResponse) Descriptor() ([]byte, []int) {
	return file_pb_payment_proto_rawDescGZIP(), []int{3}
}

func (x *AntomNotifyPayResponse) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

func (x *AntomNotifyPayResponse) GetOutTradeNo() string {
	if x != nil {
		return x.OutTradeNo
	}
	return ""
}

type CreatePayRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PostUrl            string `protobuf:"bytes,1,opt,name=postUrl,proto3" json:"postUrl"`                       // 请求接口
	Subject            string `protobuf:"bytes,2,opt,name=subject,proto3" json:"subject"`                       // 订单标题
	ProductUUID        string `protobuf:"bytes,3,opt,name=productUUID,proto3" json:"productUUID"`               // 商品uid
	ProductName        string `protobuf:"bytes,4,opt,name=productName,proto3" json:"productName"`               // 商品名称
	ProductImg         string `protobuf:"bytes,5,opt,name=productImg,proto3" json:"productImg"`                 // 商品图像
	ProductDescription string `protobuf:"bytes,6,opt,name=productDescription,proto3" json:"productDescription"` // 商品描述
	QuitUrl            string `protobuf:"bytes,7,opt,name=quitUrl,proto3" json:"quitUrl"`                       // 退出url
	NotifyUrl          string `protobuf:"bytes,8,opt,name=notifyUrl,proto3" json:"notifyUrl"`                   // 回调url
	ReturnUrl          string `protobuf:"bytes,9,opt,name=returnUrl,proto3" json:"returnUrl"`                   // 返回url
	OutTradeNo         string `protobuf:"bytes,10,opt,name=outTradeNo,proto3" json:"outTradeNo"`                // 外部流水号
	ChannelTradeNo     string `protobuf:"bytes,11,opt,name=channelTradeNo,proto3" json:"channelTradeNo"`        // 渠道流水号
	CheckSessionId     string `protobuf:"bytes,12,opt,name=checkSessionId,proto3" json:"checkSessionId"`        // stripe的支付会话id
	Amount             int64  `protobuf:"varint,13,opt,name=amount,proto3" json:"amount"`                       // 金额
	Currency           string `protobuf:"bytes,14,opt,name=currency,proto3" json:"currency"`                    // 币种
	Payee              string `protobuf:"bytes,15,opt,name=payee,proto3" json:"payee"`                          // 收款方
	ChannelType        string `protobuf:"bytes,16,opt,name=channelType,proto3" json:"channelType"`              // 支付渠道 alipay-支付宝 wxpay-微信 stripe-Stripe支付
	Platform           string `protobuf:"bytes,17,opt,name=platform,proto3" json:"platform"`                    // 支付具体来源，是app还是h5还是jsapi还是wap
	Domain             string `protobuf:"bytes,18,opt,name=domain,proto3" json:"domain"`                        // 使用平台
	BusinessType       string `protobuf:"bytes,19,opt,name=businessType,proto3" json:"businessType"`            // 业务类型，用来确认mq发送
	Language           string `protobuf:"bytes,20,opt,name=language,proto3" json:"language"`                    // 语言，国际化
	ClientIp           string `protobuf:"bytes,21,opt,name=clientIp,proto3" json:"clientIp"`
	OpenID             string `protobuf:"bytes,22,opt,name=openID,proto3" json:"openID"`          // 微信的参数
	TimeExpire         int32  `protobuf:"varint,23,opt,name=timeExpire,proto3" json:"timeExpire"` // 微信的参数
	Locale             string `protobuf:"bytes,24,opt,name=locale,proto3" json:"locale"`          // stripe参数，区域，跟页面国际化有关
}

func (x *CreatePayRequest) Reset() {
	*x = CreatePayRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_payment_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreatePayRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreatePayRequest) ProtoMessage() {}

func (x *CreatePayRequest) ProtoReflect() protoreflect.Message {
	mi := &file_pb_payment_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreatePayRequest.ProtoReflect.Descriptor instead.
func (*CreatePayRequest) Descriptor() ([]byte, []int) {
	return file_pb_payment_proto_rawDescGZIP(), []int{4}
}

func (x *CreatePayRequest) GetPostUrl() string {
	if x != nil {
		return x.PostUrl
	}
	return ""
}

func (x *CreatePayRequest) GetSubject() string {
	if x != nil {
		return x.Subject
	}
	return ""
}

func (x *CreatePayRequest) GetProductUUID() string {
	if x != nil {
		return x.ProductUUID
	}
	return ""
}

func (x *CreatePayRequest) GetProductName() string {
	if x != nil {
		return x.ProductName
	}
	return ""
}

func (x *CreatePayRequest) GetProductImg() string {
	if x != nil {
		return x.ProductImg
	}
	return ""
}

func (x *CreatePayRequest) GetProductDescription() string {
	if x != nil {
		return x.ProductDescription
	}
	return ""
}

func (x *CreatePayRequest) GetQuitUrl() string {
	if x != nil {
		return x.QuitUrl
	}
	return ""
}

func (x *CreatePayRequest) GetNotifyUrl() string {
	if x != nil {
		return x.NotifyUrl
	}
	return ""
}

func (x *CreatePayRequest) GetReturnUrl() string {
	if x != nil {
		return x.ReturnUrl
	}
	return ""
}

func (x *CreatePayRequest) GetOutTradeNo() string {
	if x != nil {
		return x.OutTradeNo
	}
	return ""
}

func (x *CreatePayRequest) GetChannelTradeNo() string {
	if x != nil {
		return x.ChannelTradeNo
	}
	return ""
}

func (x *CreatePayRequest) GetCheckSessionId() string {
	if x != nil {
		return x.CheckSessionId
	}
	return ""
}

func (x *CreatePayRequest) GetAmount() int64 {
	if x != nil {
		return x.Amount
	}
	return 0
}

func (x *CreatePayRequest) GetCurrency() string {
	if x != nil {
		return x.Currency
	}
	return ""
}

func (x *CreatePayRequest) GetPayee() string {
	if x != nil {
		return x.Payee
	}
	return ""
}

func (x *CreatePayRequest) GetChannelType() string {
	if x != nil {
		return x.ChannelType
	}
	return ""
}

func (x *CreatePayRequest) GetPlatform() string {
	if x != nil {
		return x.Platform
	}
	return ""
}

func (x *CreatePayRequest) GetDomain() string {
	if x != nil {
		return x.Domain
	}
	return ""
}

func (x *CreatePayRequest) GetBusinessType() string {
	if x != nil {
		return x.BusinessType
	}
	return ""
}

func (x *CreatePayRequest) GetLanguage() string {
	if x != nil {
		return x.Language
	}
	return ""
}

func (x *CreatePayRequest) GetClientIp() string {
	if x != nil {
		return x.ClientIp
	}
	return ""
}

func (x *CreatePayRequest) GetOpenID() string {
	if x != nil {
		return x.OpenID
	}
	return ""
}

func (x *CreatePayRequest) GetTimeExpire() int32 {
	if x != nil {
		return x.TimeExpire
	}
	return 0
}

func (x *CreatePayRequest) GetLocale() string {
	if x != nil {
		return x.Locale
	}
	return ""
}

type CreatePayResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Url               string `protobuf:"bytes,1,opt,name=url,proto3" json:"url"`
	LogRecordID       string `protobuf:"bytes,2,opt,name=logRecordID,json=log_record_ID,proto3" json:"logRecordID"`
	Msg               string `protobuf:"bytes,3,opt,name=msg,proto3" json:"msg"`
	Appid             string `protobuf:"bytes,4,opt,name=appid,proto3" json:"appid"`
	TimeStamp         string `protobuf:"bytes,5,opt,name=timeStamp,proto3" json:"timeStamp"`
	Package           string `protobuf:"bytes,6,opt,name=package,proto3" json:"package"`
	SignType          string `protobuf:"bytes,7,opt,name=signType,proto3" json:"signType"`
	NonceStr          string `protobuf:"bytes,8,opt,name=nonceStr,proto3" json:"nonceStr"`
	PaySign           string `protobuf:"bytes,9,opt,name=paySign,proto3" json:"paySign"`
	PrepayId          string `protobuf:"bytes,10,opt,name=prepayId,proto3" json:"prepayId"`
	PartnerId         string `protobuf:"bytes,11,opt,name=partnerId,proto3" json:"partnerId"`
	CheckoutSessionId string `protobuf:"bytes,12,opt,name=checkoutSessionId,proto3" json:"checkoutSessionId"`
}

func (x *CreatePayResponse) Reset() {
	*x = CreatePayResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_payment_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreatePayResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreatePayResponse) ProtoMessage() {}

func (x *CreatePayResponse) ProtoReflect() protoreflect.Message {
	mi := &file_pb_payment_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreatePayResponse.ProtoReflect.Descriptor instead.
func (*CreatePayResponse) Descriptor() ([]byte, []int) {
	return file_pb_payment_proto_rawDescGZIP(), []int{5}
}

func (x *CreatePayResponse) GetUrl() string {
	if x != nil {
		return x.Url
	}
	return ""
}

func (x *CreatePayResponse) GetLogRecordID() string {
	if x != nil {
		return x.LogRecordID
	}
	return ""
}

func (x *CreatePayResponse) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *CreatePayResponse) GetAppid() string {
	if x != nil {
		return x.Appid
	}
	return ""
}

func (x *CreatePayResponse) GetTimeStamp() string {
	if x != nil {
		return x.TimeStamp
	}
	return ""
}

func (x *CreatePayResponse) GetPackage() string {
	if x != nil {
		return x.Package
	}
	return ""
}

func (x *CreatePayResponse) GetSignType() string {
	if x != nil {
		return x.SignType
	}
	return ""
}

func (x *CreatePayResponse) GetNonceStr() string {
	if x != nil {
		return x.NonceStr
	}
	return ""
}

func (x *CreatePayResponse) GetPaySign() string {
	if x != nil {
		return x.PaySign
	}
	return ""
}

func (x *CreatePayResponse) GetPrepayId() string {
	if x != nil {
		return x.PrepayId
	}
	return ""
}

func (x *CreatePayResponse) GetPartnerId() string {
	if x != nil {
		return x.PartnerId
	}
	return ""
}

func (x *CreatePayResponse) GetCheckoutSessionId() string {
	if x != nil {
		return x.CheckoutSessionId
	}
	return ""
}

type CreateRefundRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PostUrl        string `protobuf:"bytes,1,opt,name=postUrl,proto3" json:"postUrl"`               // 请求接口
	Subject        string `protobuf:"bytes,2,opt,name=subject,proto3" json:"subject"`               // 订单标题
	ProductUUID    string `protobuf:"bytes,3,opt,name=productUUID,proto3" json:"productUUID"`       // 商品uid
	OutTradeNo     string `protobuf:"bytes,4,opt,name=outTradeNo,proto3" json:"outTradeNo"`         // 外部流水号
	ChannelTradeNo string `protobuf:"bytes,5,opt,name=channelTradeNo,proto3" json:"channelTradeNo"` // 渠道流水号
	CheckSessionId string `protobuf:"bytes,6,opt,name=checkSessionId,proto3" json:"checkSessionId"` // stripe的支付会话id
	RefundAmount   int64  `protobuf:"varint,7,opt,name=refundAmount,proto3" json:"refundAmount"`    // 退款金额
	Currency       string `protobuf:"bytes,8,opt,name=currency,proto3" json:"currency"`             // 币种
	Payee          string `protobuf:"bytes,9,opt,name=payee,proto3" json:"payee"`                   // 收款方
	ChannelType    string `protobuf:"bytes,10,opt,name=channelType,proto3" json:"channelType"`      // 支付渠道 alipay-支付宝 wxpay-微信 stripe-Stripe支付
	Platform       string `protobuf:"bytes,11,opt,name=platform,proto3" json:"platform"`            // 支付具体来源，是app还是h5还是jsapi还是wap
	Domain         string `protobuf:"bytes,12,opt,name=domain,proto3" json:"domain"`                // 使用平台
	BusinessType   string `protobuf:"bytes,13,opt,name=businessType,proto3" json:"businessType"`    // 业务类型，用来确认mq发送
	Language       string `protobuf:"bytes,14,opt,name=language,proto3" json:"language"`            // 语言，国际化
	ClientIp       string `protobuf:"bytes,15,opt,name=clientIp,proto3" json:"clientIp"`
	OpenID         string `protobuf:"bytes,16,opt,name=openID,proto3" json:"openID"`             // 微信的参数
	CreatorId      int64  `protobuf:"varint,17,opt,name=creatorId,proto3" json:"creatorId"`      // 创建人id
	CreatorName    string `protobuf:"bytes,18,opt,name=creatorName,proto3" json:"creatorName"`   // 创建人名称
	RefundReason   string `protobuf:"bytes,19,opt,name=refundReason,proto3" json:"refundReason"` // 退款理由
}

func (x *CreateRefundRequest) Reset() {
	*x = CreateRefundRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_payment_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateRefundRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateRefundRequest) ProtoMessage() {}

func (x *CreateRefundRequest) ProtoReflect() protoreflect.Message {
	mi := &file_pb_payment_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateRefundRequest.ProtoReflect.Descriptor instead.
func (*CreateRefundRequest) Descriptor() ([]byte, []int) {
	return file_pb_payment_proto_rawDescGZIP(), []int{6}
}

func (x *CreateRefundRequest) GetPostUrl() string {
	if x != nil {
		return x.PostUrl
	}
	return ""
}

func (x *CreateRefundRequest) GetSubject() string {
	if x != nil {
		return x.Subject
	}
	return ""
}

func (x *CreateRefundRequest) GetProductUUID() string {
	if x != nil {
		return x.ProductUUID
	}
	return ""
}

func (x *CreateRefundRequest) GetOutTradeNo() string {
	if x != nil {
		return x.OutTradeNo
	}
	return ""
}

func (x *CreateRefundRequest) GetChannelTradeNo() string {
	if x != nil {
		return x.ChannelTradeNo
	}
	return ""
}

func (x *CreateRefundRequest) GetCheckSessionId() string {
	if x != nil {
		return x.CheckSessionId
	}
	return ""
}

func (x *CreateRefundRequest) GetRefundAmount() int64 {
	if x != nil {
		return x.RefundAmount
	}
	return 0
}

func (x *CreateRefundRequest) GetCurrency() string {
	if x != nil {
		return x.Currency
	}
	return ""
}

func (x *CreateRefundRequest) GetPayee() string {
	if x != nil {
		return x.Payee
	}
	return ""
}

func (x *CreateRefundRequest) GetChannelType() string {
	if x != nil {
		return x.ChannelType
	}
	return ""
}

func (x *CreateRefundRequest) GetPlatform() string {
	if x != nil {
		return x.Platform
	}
	return ""
}

func (x *CreateRefundRequest) GetDomain() string {
	if x != nil {
		return x.Domain
	}
	return ""
}

func (x *CreateRefundRequest) GetBusinessType() string {
	if x != nil {
		return x.BusinessType
	}
	return ""
}

func (x *CreateRefundRequest) GetLanguage() string {
	if x != nil {
		return x.Language
	}
	return ""
}

func (x *CreateRefundRequest) GetClientIp() string {
	if x != nil {
		return x.ClientIp
	}
	return ""
}

func (x *CreateRefundRequest) GetOpenID() string {
	if x != nil {
		return x.OpenID
	}
	return ""
}

func (x *CreateRefundRequest) GetCreatorId() int64 {
	if x != nil {
		return x.CreatorId
	}
	return 0
}

func (x *CreateRefundRequest) GetCreatorName() string {
	if x != nil {
		return x.CreatorName
	}
	return ""
}

func (x *CreateRefundRequest) GetRefundReason() string {
	if x != nil {
		return x.RefundReason
	}
	return ""
}

type CreateRefundResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code int64  `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	Msg  string `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg"`
}

func (x *CreateRefundResponse) Reset() {
	*x = CreateRefundResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_payment_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateRefundResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateRefundResponse) ProtoMessage() {}

func (x *CreateRefundResponse) ProtoReflect() protoreflect.Message {
	mi := &file_pb_payment_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateRefundResponse.ProtoReflect.Descriptor instead.
func (*CreateRefundResponse) Descriptor() ([]byte, []int) {
	return file_pb_payment_proto_rawDescGZIP(), []int{7}
}

func (x *CreateRefundResponse) GetCode() int64 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *CreateRefundResponse) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

type NotifyPayRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PostUrl     string       `protobuf:"bytes,1,opt,name=postUrl,proto3" json:"postUrl"` // 请求接口
	HttpRequest *HttpRequest `protobuf:"bytes,2,opt,name=http_request,json=httpRequest,proto3" json:"http_request"`
	RawQuery    string       `protobuf:"bytes,3,opt,name=rawQuery,json=raw_query,proto3" json:"rawQuery"`
}

func (x *NotifyPayRequest) Reset() {
	*x = NotifyPayRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_payment_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *NotifyPayRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NotifyPayRequest) ProtoMessage() {}

func (x *NotifyPayRequest) ProtoReflect() protoreflect.Message {
	mi := &file_pb_payment_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NotifyPayRequest.ProtoReflect.Descriptor instead.
func (*NotifyPayRequest) Descriptor() ([]byte, []int) {
	return file_pb_payment_proto_rawDescGZIP(), []int{8}
}

func (x *NotifyPayRequest) GetPostUrl() string {
	if x != nil {
		return x.PostUrl
	}
	return ""
}

func (x *NotifyPayRequest) GetHttpRequest() *HttpRequest {
	if x != nil {
		return x.HttpRequest
	}
	return nil
}

func (x *NotifyPayRequest) GetRawQuery() string {
	if x != nil {
		return x.RawQuery
	}
	return ""
}

type NotifyPayResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Msg        string `protobuf:"bytes,1,opt,name=msg,proto3" json:"msg"`
	OutTradeNo string `protobuf:"bytes,2,opt,name=outTradeNo,proto3" json:"outTradeNo"`
}

func (x *NotifyPayResponse) Reset() {
	*x = NotifyPayResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_payment_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *NotifyPayResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NotifyPayResponse) ProtoMessage() {}

func (x *NotifyPayResponse) ProtoReflect() protoreflect.Message {
	mi := &file_pb_payment_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NotifyPayResponse.ProtoReflect.Descriptor instead.
func (*NotifyPayResponse) Descriptor() ([]byte, []int) {
	return file_pb_payment_proto_rawDescGZIP(), []int{9}
}

func (x *NotifyPayResponse) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *NotifyPayResponse) GetOutTradeNo() string {
	if x != nil {
		return x.OutTradeNo
	}
	return ""
}

type HttpRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Method  string            `protobuf:"bytes,1,opt,name=method,proto3" json:"method"`
	Url     string            `protobuf:"bytes,2,opt,name=url,proto3" json:"url"`
	Headers map[string]string `protobuf:"bytes,3,rep,name=headers,proto3" json:"headers" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	Body    []byte            `protobuf:"bytes,4,opt,name=body,proto3" json:"body"`
}

func (x *HttpRequest) Reset() {
	*x = HttpRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_payment_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *HttpRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HttpRequest) ProtoMessage() {}

func (x *HttpRequest) ProtoReflect() protoreflect.Message {
	mi := &file_pb_payment_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HttpRequest.ProtoReflect.Descriptor instead.
func (*HttpRequest) Descriptor() ([]byte, []int) {
	return file_pb_payment_proto_rawDescGZIP(), []int{10}
}

func (x *HttpRequest) GetMethod() string {
	if x != nil {
		return x.Method
	}
	return ""
}

func (x *HttpRequest) GetUrl() string {
	if x != nil {
		return x.Url
	}
	return ""
}

func (x *HttpRequest) GetHeaders() map[string]string {
	if x != nil {
		return x.Headers
	}
	return nil
}

func (x *HttpRequest) GetBody() []byte {
	if x != nil {
		return x.Body
	}
	return nil
}

type CommonMsg struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Msg string `protobuf:"bytes,1,opt,name=msg,proto3" json:"msg"`
}

func (x *CommonMsg) Reset() {
	*x = CommonMsg{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_payment_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CommonMsg) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CommonMsg) ProtoMessage() {}

func (x *CommonMsg) ProtoReflect() protoreflect.Message {
	mi := &file_pb_payment_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CommonMsg.ProtoReflect.Descriptor instead.
func (*CommonMsg) Descriptor() ([]byte, []int) {
	return file_pb_payment_proto_rawDescGZIP(), []int{11}
}

func (x *CommonMsg) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

type EmptyRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *EmptyRequest) Reset() {
	*x = EmptyRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_payment_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EmptyRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EmptyRequest) ProtoMessage() {}

func (x *EmptyRequest) ProtoReflect() protoreflect.Message {
	mi := &file_pb_payment_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EmptyRequest.ProtoReflect.Descriptor instead.
func (*EmptyRequest) Descriptor() ([]byte, []int) {
	return file_pb_payment_proto_rawDescGZIP(), []int{12}
}

type CreateStripeCheckoutSessionRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ProductUUID          string `protobuf:"bytes,1,opt,name=productUUID,proto3" json:"productUUID"`
	ProductName          string `protobuf:"bytes,2,opt,name=productName,proto3" json:"productName"`
	ProductQuantity      int64  `protobuf:"varint,3,opt,name=productQuantity,proto3" json:"productQuantity"`
	ProductAllPrice      int64  `protobuf:"varint,4,opt,name=productAllPrice,proto3" json:"productAllPrice"`
	ProductDescription   string `protobuf:"bytes,5,opt,name=productDescription,proto3" json:"productDescription"`
	ProductImageUrl      string `protobuf:"bytes,6,opt,name=productImageUrl,proto3" json:"productImageUrl"`
	ProductPriceCurrency string `protobuf:"bytes,7,opt,name=productPriceCurrency,proto3" json:"productPriceCurrency"`
	CreaterID            string `protobuf:"bytes,8,opt,name=createrID,proto3" json:"createrID"`
	CreaterName          string `protobuf:"bytes,9,opt,name=createrName,proto3" json:"createrName"`
	Domain               string `protobuf:"bytes,10,opt,name=domain,proto3" json:"domain"`
	SuccessUrl           string `protobuf:"bytes,11,opt,name=successUrl,proto3" json:"successUrl"`
	CancelUrl            string `protobuf:"bytes,12,opt,name=cancelUrl,proto3" json:"cancelUrl"`
	OutTradeNo           string `protobuf:"bytes,13,opt,name=outTradeNo,proto3" json:"outTradeNo"`
	Locale               string `protobuf:"bytes,14,opt,name=locale,proto3" json:"locale"`
}

func (x *CreateStripeCheckoutSessionRequest) Reset() {
	*x = CreateStripeCheckoutSessionRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_payment_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateStripeCheckoutSessionRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateStripeCheckoutSessionRequest) ProtoMessage() {}

func (x *CreateStripeCheckoutSessionRequest) ProtoReflect() protoreflect.Message {
	mi := &file_pb_payment_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateStripeCheckoutSessionRequest.ProtoReflect.Descriptor instead.
func (*CreateStripeCheckoutSessionRequest) Descriptor() ([]byte, []int) {
	return file_pb_payment_proto_rawDescGZIP(), []int{13}
}

func (x *CreateStripeCheckoutSessionRequest) GetProductUUID() string {
	if x != nil {
		return x.ProductUUID
	}
	return ""
}

func (x *CreateStripeCheckoutSessionRequest) GetProductName() string {
	if x != nil {
		return x.ProductName
	}
	return ""
}

func (x *CreateStripeCheckoutSessionRequest) GetProductQuantity() int64 {
	if x != nil {
		return x.ProductQuantity
	}
	return 0
}

func (x *CreateStripeCheckoutSessionRequest) GetProductAllPrice() int64 {
	if x != nil {
		return x.ProductAllPrice
	}
	return 0
}

func (x *CreateStripeCheckoutSessionRequest) GetProductDescription() string {
	if x != nil {
		return x.ProductDescription
	}
	return ""
}

func (x *CreateStripeCheckoutSessionRequest) GetProductImageUrl() string {
	if x != nil {
		return x.ProductImageUrl
	}
	return ""
}

func (x *CreateStripeCheckoutSessionRequest) GetProductPriceCurrency() string {
	if x != nil {
		return x.ProductPriceCurrency
	}
	return ""
}

func (x *CreateStripeCheckoutSessionRequest) GetCreaterID() string {
	if x != nil {
		return x.CreaterID
	}
	return ""
}

func (x *CreateStripeCheckoutSessionRequest) GetCreaterName() string {
	if x != nil {
		return x.CreaterName
	}
	return ""
}

func (x *CreateStripeCheckoutSessionRequest) GetDomain() string {
	if x != nil {
		return x.Domain
	}
	return ""
}

func (x *CreateStripeCheckoutSessionRequest) GetSuccessUrl() string {
	if x != nil {
		return x.SuccessUrl
	}
	return ""
}

func (x *CreateStripeCheckoutSessionRequest) GetCancelUrl() string {
	if x != nil {
		return x.CancelUrl
	}
	return ""
}

func (x *CreateStripeCheckoutSessionRequest) GetOutTradeNo() string {
	if x != nil {
		return x.OutTradeNo
	}
	return ""
}

func (x *CreateStripeCheckoutSessionRequest) GetLocale() string {
	if x != nil {
		return x.Locale
	}
	return ""
}

type CreateStripeCheckoutSessionResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CheckoutSessionId  string `protobuf:"bytes,1,opt,name=checkoutSessionId,proto3" json:"checkoutSessionId"`
	CheckoutSessionUrl string `protobuf:"bytes,2,opt,name=checkoutSessionUrl,proto3" json:"checkoutSessionUrl"`
}

func (x *CreateStripeCheckoutSessionResponse) Reset() {
	*x = CreateStripeCheckoutSessionResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_payment_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateStripeCheckoutSessionResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateStripeCheckoutSessionResponse) ProtoMessage() {}

func (x *CreateStripeCheckoutSessionResponse) ProtoReflect() protoreflect.Message {
	mi := &file_pb_payment_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateStripeCheckoutSessionResponse.ProtoReflect.Descriptor instead.
func (*CreateStripeCheckoutSessionResponse) Descriptor() ([]byte, []int) {
	return file_pb_payment_proto_rawDescGZIP(), []int{14}
}

func (x *CreateStripeCheckoutSessionResponse) GetCheckoutSessionId() string {
	if x != nil {
		return x.CheckoutSessionId
	}
	return ""
}

func (x *CreateStripeCheckoutSessionResponse) GetCheckoutSessionUrl() string {
	if x != nil {
		return x.CheckoutSessionUrl
	}
	return ""
}

type GetCheckoutWebhookRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PostUrl    string `protobuf:"bytes,1,opt,name=postUrl,proto3" json:"postUrl"`       // 请求接口
	Type       string `protobuf:"bytes,2,opt,name=type,proto3" json:"type"`             // 事件类型，例如"payment_intent.succeeded"
	Payload    string `protobuf:"bytes,3,opt,name=payload,proto3" json:"payload"`       // 事件的原始JSON payload
	Signature  string `protobuf:"bytes,4,opt,name=signature,proto3" json:"signature"`   // Stripe-Signature头的值，用于验证事件
	WebhookKey string `protobuf:"bytes,5,opt,name=webhookKey,proto3" json:"webhookKey"` // Webhook密钥
}

func (x *GetCheckoutWebhookRequest) Reset() {
	*x = GetCheckoutWebhookRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_payment_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetCheckoutWebhookRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetCheckoutWebhookRequest) ProtoMessage() {}

func (x *GetCheckoutWebhookRequest) ProtoReflect() protoreflect.Message {
	mi := &file_pb_payment_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetCheckoutWebhookRequest.ProtoReflect.Descriptor instead.
func (*GetCheckoutWebhookRequest) Descriptor() ([]byte, []int) {
	return file_pb_payment_proto_rawDescGZIP(), []int{15}
}

func (x *GetCheckoutWebhookRequest) GetPostUrl() string {
	if x != nil {
		return x.PostUrl
	}
	return ""
}

func (x *GetCheckoutWebhookRequest) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

func (x *GetCheckoutWebhookRequest) GetPayload() string {
	if x != nil {
		return x.Payload
	}
	return ""
}

func (x *GetCheckoutWebhookRequest) GetSignature() string {
	if x != nil {
		return x.Signature
	}
	return ""
}

func (x *GetCheckoutWebhookRequest) GetWebhookKey() string {
	if x != nil {
		return x.WebhookKey
	}
	return ""
}

type GetCheckoutWebhookResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Success             bool   `protobuf:"varint,1,opt,name=success,proto3" json:"success"` // 处理是否成功
	Message             string `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`  // 可选的处理消息
	OutTradeNo          string `protobuf:"bytes,3,opt,name=outTradeNo,proto3" json:"outTradeNo"`
	PaymentIntentStatus string `protobuf:"bytes,4,opt,name=paymentIntentStatus,proto3" json:"paymentIntentStatus"`
}

func (x *GetCheckoutWebhookResponse) Reset() {
	*x = GetCheckoutWebhookResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_payment_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetCheckoutWebhookResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetCheckoutWebhookResponse) ProtoMessage() {}

func (x *GetCheckoutWebhookResponse) ProtoReflect() protoreflect.Message {
	mi := &file_pb_payment_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetCheckoutWebhookResponse.ProtoReflect.Descriptor instead.
func (*GetCheckoutWebhookResponse) Descriptor() ([]byte, []int) {
	return file_pb_payment_proto_rawDescGZIP(), []int{16}
}

func (x *GetCheckoutWebhookResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

func (x *GetCheckoutWebhookResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *GetCheckoutWebhookResponse) GetOutTradeNo() string {
	if x != nil {
		return x.OutTradeNo
	}
	return ""
}

func (x *GetCheckoutWebhookResponse) GetPaymentIntentStatus() string {
	if x != nil {
		return x.PaymentIntentStatus
	}
	return ""
}

type GetStripePaymentIntentInfoRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CheckoutSessionId string `protobuf:"bytes,1,opt,name=checkoutSessionId,proto3" json:"checkoutSessionId"`
	CreaterID         string `protobuf:"bytes,2,opt,name=createrID,proto3" json:"createrID"`
}

func (x *GetStripePaymentIntentInfoRequest) Reset() {
	*x = GetStripePaymentIntentInfoRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_payment_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetStripePaymentIntentInfoRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetStripePaymentIntentInfoRequest) ProtoMessage() {}

func (x *GetStripePaymentIntentInfoRequest) ProtoReflect() protoreflect.Message {
	mi := &file_pb_payment_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetStripePaymentIntentInfoRequest.ProtoReflect.Descriptor instead.
func (*GetStripePaymentIntentInfoRequest) Descriptor() ([]byte, []int) {
	return file_pb_payment_proto_rawDescGZIP(), []int{17}
}

func (x *GetStripePaymentIntentInfoRequest) GetCheckoutSessionId() string {
	if x != nil {
		return x.CheckoutSessionId
	}
	return ""
}

func (x *GetStripePaymentIntentInfoRequest) GetCreaterID() string {
	if x != nil {
		return x.CreaterID
	}
	return ""
}

type GetStripePaymentIntentInfoResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CheckoutSessionId     string `protobuf:"bytes,1,opt,name=checkoutSessionId,proto3" json:"checkoutSessionId"`
	CheckoutSessionStatus string `protobuf:"bytes,2,opt,name=checkoutSessionStatus,proto3" json:"checkoutSessionStatus"`
	PaymentIntentId       string `protobuf:"bytes,3,opt,name=paymentIntentId,proto3" json:"paymentIntentId"`
	PaymentIntentCreated  string `protobuf:"bytes,4,opt,name=paymentIntentCreated,proto3" json:"paymentIntentCreated"`
	PaymentIntentStatus   string `protobuf:"bytes,5,opt,name=paymentIntentStatus,proto3" json:"paymentIntentStatus"`
	ChargeId              string `protobuf:"bytes,6,opt,name=chargeId,proto3" json:"chargeId"`
	PayPrice              int64  `protobuf:"varint,7,opt,name=payPrice,proto3" json:"payPrice"`
	PayExchangeRate       string `protobuf:"bytes,8,opt,name=payExchangeRate,proto3" json:"payExchangeRate"`
	PayCurrency           string `protobuf:"bytes,9,opt,name=payCurrency,proto3" json:"payCurrency"`
	AccountCurrency       string `protobuf:"bytes,10,opt,name=accountCurrency,proto3" json:"accountCurrency"`
	AfterRatePayPrice     int64  `protobuf:"varint,11,opt,name=afterRatePayPrice,proto3" json:"afterRatePayPrice"`
	PayHandingFee         int64  `protobuf:"varint,12,opt,name=payHandingFee,proto3" json:"payHandingFee"`
	AfterRatePayNetAmount int64  `protobuf:"varint,13,opt,name=afterRatePayNetAmount,proto3" json:"afterRatePayNetAmount"`
	CustomerId            string `protobuf:"bytes,14,opt,name=customerId,proto3" json:"customerId"`
	RefundPrice           int64  `protobuf:"varint,15,opt,name=refundPrice,proto3" json:"refundPrice"`
	OutTradeNo            string `protobuf:"bytes,16,opt,name=outTradeNo,proto3" json:"outTradeNo"`
}

func (x *GetStripePaymentIntentInfoResponse) Reset() {
	*x = GetStripePaymentIntentInfoResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_payment_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetStripePaymentIntentInfoResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetStripePaymentIntentInfoResponse) ProtoMessage() {}

func (x *GetStripePaymentIntentInfoResponse) ProtoReflect() protoreflect.Message {
	mi := &file_pb_payment_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetStripePaymentIntentInfoResponse.ProtoReflect.Descriptor instead.
func (*GetStripePaymentIntentInfoResponse) Descriptor() ([]byte, []int) {
	return file_pb_payment_proto_rawDescGZIP(), []int{18}
}

func (x *GetStripePaymentIntentInfoResponse) GetCheckoutSessionId() string {
	if x != nil {
		return x.CheckoutSessionId
	}
	return ""
}

func (x *GetStripePaymentIntentInfoResponse) GetCheckoutSessionStatus() string {
	if x != nil {
		return x.CheckoutSessionStatus
	}
	return ""
}

func (x *GetStripePaymentIntentInfoResponse) GetPaymentIntentId() string {
	if x != nil {
		return x.PaymentIntentId
	}
	return ""
}

func (x *GetStripePaymentIntentInfoResponse) GetPaymentIntentCreated() string {
	if x != nil {
		return x.PaymentIntentCreated
	}
	return ""
}

func (x *GetStripePaymentIntentInfoResponse) GetPaymentIntentStatus() string {
	if x != nil {
		return x.PaymentIntentStatus
	}
	return ""
}

func (x *GetStripePaymentIntentInfoResponse) GetChargeId() string {
	if x != nil {
		return x.ChargeId
	}
	return ""
}

func (x *GetStripePaymentIntentInfoResponse) GetPayPrice() int64 {
	if x != nil {
		return x.PayPrice
	}
	return 0
}

func (x *GetStripePaymentIntentInfoResponse) GetPayExchangeRate() string {
	if x != nil {
		return x.PayExchangeRate
	}
	return ""
}

func (x *GetStripePaymentIntentInfoResponse) GetPayCurrency() string {
	if x != nil {
		return x.PayCurrency
	}
	return ""
}

func (x *GetStripePaymentIntentInfoResponse) GetAccountCurrency() string {
	if x != nil {
		return x.AccountCurrency
	}
	return ""
}

func (x *GetStripePaymentIntentInfoResponse) GetAfterRatePayPrice() int64 {
	if x != nil {
		return x.AfterRatePayPrice
	}
	return 0
}

func (x *GetStripePaymentIntentInfoResponse) GetPayHandingFee() int64 {
	if x != nil {
		return x.PayHandingFee
	}
	return 0
}

func (x *GetStripePaymentIntentInfoResponse) GetAfterRatePayNetAmount() int64 {
	if x != nil {
		return x.AfterRatePayNetAmount
	}
	return 0
}

func (x *GetStripePaymentIntentInfoResponse) GetCustomerId() string {
	if x != nil {
		return x.CustomerId
	}
	return ""
}

func (x *GetStripePaymentIntentInfoResponse) GetRefundPrice() int64 {
	if x != nil {
		return x.RefundPrice
	}
	return 0
}

func (x *GetStripePaymentIntentInfoResponse) GetOutTradeNo() string {
	if x != nil {
		return x.OutTradeNo
	}
	return ""
}

type GetRefundInfoRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RefundId  string `protobuf:"bytes,1,opt,name=refundId,proto3" json:"refundId"`
	CreaterID string `protobuf:"bytes,2,opt,name=createrID,proto3" json:"createrID"`
}

func (x *GetRefundInfoRequest) Reset() {
	*x = GetRefundInfoRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_payment_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetRefundInfoRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetRefundInfoRequest) ProtoMessage() {}

func (x *GetRefundInfoRequest) ProtoReflect() protoreflect.Message {
	mi := &file_pb_payment_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetRefundInfoRequest.ProtoReflect.Descriptor instead.
func (*GetRefundInfoRequest) Descriptor() ([]byte, []int) {
	return file_pb_payment_proto_rawDescGZIP(), []int{19}
}

func (x *GetRefundInfoRequest) GetRefundId() string {
	if x != nil {
		return x.RefundId
	}
	return ""
}

func (x *GetRefundInfoRequest) GetCreaterID() string {
	if x != nil {
		return x.CreaterID
	}
	return ""
}

type GetRefundInfoResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RefundStatus string `protobuf:"bytes,1,opt,name=refundStatus,proto3" json:"refundStatus"`
}

func (x *GetRefundInfoResponse) Reset() {
	*x = GetRefundInfoResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_payment_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetRefundInfoResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetRefundInfoResponse) ProtoMessage() {}

func (x *GetRefundInfoResponse) ProtoReflect() protoreflect.Message {
	mi := &file_pb_payment_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetRefundInfoResponse.ProtoReflect.Descriptor instead.
func (*GetRefundInfoResponse) Descriptor() ([]byte, []int) {
	return file_pb_payment_proto_rawDescGZIP(), []int{20}
}

func (x *GetRefundInfoResponse) GetRefundStatus() string {
	if x != nil {
		return x.RefundStatus
	}
	return ""
}

type AliWapPayRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Subject     string `protobuf:"bytes,1,opt,name=subject,proto3" json:"subject"`
	ProductCode string `protobuf:"bytes,2,opt,name=productCode,json=product_code,proto3" json:"productCode"`
	OutTradeNo  string `protobuf:"bytes,3,opt,name=outTradeNo,json=out_trade_no,proto3" json:"outTradeNo"`
	QuitUrl     string `protobuf:"bytes,4,opt,name=quitUrl,json=quit_url,proto3" json:"quitUrl"`
	NotifyUrl   string `protobuf:"bytes,5,opt,name=notifyUrl,json=notify_url,proto3" json:"notifyUrl"`
	ReturnUrl   string `protobuf:"bytes,6,opt,name=returnUrl,json=return_url,proto3" json:"returnUrl"`
	TotalAmount int32  `protobuf:"varint,7,opt,name=totalAmount,json=total_amount,proto3" json:"totalAmount"`
	TimeExpire  int32  `protobuf:"varint,8,opt,name=timeExpire,json=time_expire,proto3" json:"timeExpire"`
	Domain      string `protobuf:"bytes,9,opt,name=domain,proto3" json:"domain"`
	Platform    string `protobuf:"bytes,10,opt,name=platform,proto3" json:"platform"`
}

func (x *AliWapPayRequest) Reset() {
	*x = AliWapPayRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_payment_proto_msgTypes[21]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AliWapPayRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AliWapPayRequest) ProtoMessage() {}

func (x *AliWapPayRequest) ProtoReflect() protoreflect.Message {
	mi := &file_pb_payment_proto_msgTypes[21]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AliWapPayRequest.ProtoReflect.Descriptor instead.
func (*AliWapPayRequest) Descriptor() ([]byte, []int) {
	return file_pb_payment_proto_rawDescGZIP(), []int{21}
}

func (x *AliWapPayRequest) GetSubject() string {
	if x != nil {
		return x.Subject
	}
	return ""
}

func (x *AliWapPayRequest) GetProductCode() string {
	if x != nil {
		return x.ProductCode
	}
	return ""
}

func (x *AliWapPayRequest) GetOutTradeNo() string {
	if x != nil {
		return x.OutTradeNo
	}
	return ""
}

func (x *AliWapPayRequest) GetQuitUrl() string {
	if x != nil {
		return x.QuitUrl
	}
	return ""
}

func (x *AliWapPayRequest) GetNotifyUrl() string {
	if x != nil {
		return x.NotifyUrl
	}
	return ""
}

func (x *AliWapPayRequest) GetReturnUrl() string {
	if x != nil {
		return x.ReturnUrl
	}
	return ""
}

func (x *AliWapPayRequest) GetTotalAmount() int32 {
	if x != nil {
		return x.TotalAmount
	}
	return 0
}

func (x *AliWapPayRequest) GetTimeExpire() int32 {
	if x != nil {
		return x.TimeExpire
	}
	return 0
}

func (x *AliWapPayRequest) GetDomain() string {
	if x != nil {
		return x.Domain
	}
	return ""
}

func (x *AliWapPayRequest) GetPlatform() string {
	if x != nil {
		return x.Platform
	}
	return ""
}

type AliWapPayResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Url         string `protobuf:"bytes,1,opt,name=url,proto3" json:"url"`
	LogRecordID string `protobuf:"bytes,2,opt,name=logRecordID,json=log_record_ID,proto3" json:"logRecordID"`
	Msg         string `protobuf:"bytes,3,opt,name=msg,proto3" json:"msg"`
}

func (x *AliWapPayResponse) Reset() {
	*x = AliWapPayResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_payment_proto_msgTypes[22]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AliWapPayResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AliWapPayResponse) ProtoMessage() {}

func (x *AliWapPayResponse) ProtoReflect() protoreflect.Message {
	mi := &file_pb_payment_proto_msgTypes[22]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AliWapPayResponse.ProtoReflect.Descriptor instead.
func (*AliWapPayResponse) Descriptor() ([]byte, []int) {
	return file_pb_payment_proto_rawDescGZIP(), []int{22}
}

func (x *AliWapPayResponse) GetUrl() string {
	if x != nil {
		return x.Url
	}
	return ""
}

func (x *AliWapPayResponse) GetLogRecordID() string {
	if x != nil {
		return x.LogRecordID
	}
	return ""
}

func (x *AliWapPayResponse) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

type AliAppPayRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Subject     string `protobuf:"bytes,1,opt,name=subject,proto3" json:"subject"`
	ProductCode string `protobuf:"bytes,2,opt,name=productCode,json=product_code,proto3" json:"productCode"`
	OutTradeNo  string `protobuf:"bytes,3,opt,name=outTradeNo,json=out_trade_no,proto3" json:"outTradeNo"`
	QuitUrl     string `protobuf:"bytes,4,opt,name=quitUrl,json=quit_url,proto3" json:"quitUrl"`
	NotifyUrl   string `protobuf:"bytes,5,opt,name=notifyUrl,json=notify_url,proto3" json:"notifyUrl"`
	ReturnUrl   string `protobuf:"bytes,6,opt,name=returnUrl,json=return_url,proto3" json:"returnUrl"`
	TotalAmount string `protobuf:"bytes,7,opt,name=totalAmount,json=total_amount,proto3" json:"totalAmount"`
	TimeExpire  int32  `protobuf:"varint,8,opt,name=timeExpire,json=time_expire,proto3" json:"timeExpire"`
	Domain      string `protobuf:"bytes,9,opt,name=domain,proto3" json:"domain"`
	Platform    string `protobuf:"bytes,10,opt,name=platform,proto3" json:"platform"`
	PayType     int32  `protobuf:"varint,11,opt,name=payType,proto3" json:"payType"`
}

func (x *AliAppPayRequest) Reset() {
	*x = AliAppPayRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_payment_proto_msgTypes[23]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AliAppPayRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AliAppPayRequest) ProtoMessage() {}

func (x *AliAppPayRequest) ProtoReflect() protoreflect.Message {
	mi := &file_pb_payment_proto_msgTypes[23]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AliAppPayRequest.ProtoReflect.Descriptor instead.
func (*AliAppPayRequest) Descriptor() ([]byte, []int) {
	return file_pb_payment_proto_rawDescGZIP(), []int{23}
}

func (x *AliAppPayRequest) GetSubject() string {
	if x != nil {
		return x.Subject
	}
	return ""
}

func (x *AliAppPayRequest) GetProductCode() string {
	if x != nil {
		return x.ProductCode
	}
	return ""
}

func (x *AliAppPayRequest) GetOutTradeNo() string {
	if x != nil {
		return x.OutTradeNo
	}
	return ""
}

func (x *AliAppPayRequest) GetQuitUrl() string {
	if x != nil {
		return x.QuitUrl
	}
	return ""
}

func (x *AliAppPayRequest) GetNotifyUrl() string {
	if x != nil {
		return x.NotifyUrl
	}
	return ""
}

func (x *AliAppPayRequest) GetReturnUrl() string {
	if x != nil {
		return x.ReturnUrl
	}
	return ""
}

func (x *AliAppPayRequest) GetTotalAmount() string {
	if x != nil {
		return x.TotalAmount
	}
	return ""
}

func (x *AliAppPayRequest) GetTimeExpire() int32 {
	if x != nil {
		return x.TimeExpire
	}
	return 0
}

func (x *AliAppPayRequest) GetDomain() string {
	if x != nil {
		return x.Domain
	}
	return ""
}

func (x *AliAppPayRequest) GetPlatform() string {
	if x != nil {
		return x.Platform
	}
	return ""
}

func (x *AliAppPayRequest) GetPayType() int32 {
	if x != nil {
		return x.PayType
	}
	return 0
}

type AliAppPayResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Url         string `protobuf:"bytes,1,opt,name=url,proto3" json:"url"`
	LogRecordID string `protobuf:"bytes,2,opt,name=logRecordID,json=log_record_ID,proto3" json:"logRecordID"`
	Msg         string `protobuf:"bytes,3,opt,name=msg,proto3" json:"msg"`
}

func (x *AliAppPayResponse) Reset() {
	*x = AliAppPayResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_payment_proto_msgTypes[24]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AliAppPayResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AliAppPayResponse) ProtoMessage() {}

func (x *AliAppPayResponse) ProtoReflect() protoreflect.Message {
	mi := &file_pb_payment_proto_msgTypes[24]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AliAppPayResponse.ProtoReflect.Descriptor instead.
func (*AliAppPayResponse) Descriptor() ([]byte, []int) {
	return file_pb_payment_proto_rawDescGZIP(), []int{24}
}

func (x *AliAppPayResponse) GetUrl() string {
	if x != nil {
		return x.Url
	}
	return ""
}

func (x *AliAppPayResponse) GetLogRecordID() string {
	if x != nil {
		return x.LogRecordID
	}
	return ""
}

func (x *AliAppPayResponse) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

type AliNativePayRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Subject     string `protobuf:"bytes,1,opt,name=subject,proto3" json:"subject"`
	OutTradeNo  string `protobuf:"bytes,2,opt,name=outTradeNo,json=out_trade_no,proto3" json:"outTradeNo"`
	NotifyUrl   string `protobuf:"bytes,3,opt,name=notifyUrl,json=notify_url,proto3" json:"notifyUrl"`
	TotalAmount int32  `protobuf:"varint,4,opt,name=totalAmount,json=total_amount,proto3" json:"totalAmount"`
	TimeExpire  int32  `protobuf:"varint,5,opt,name=timeExpire,json=time_expire,proto3" json:"timeExpire"`
	Domain      string `protobuf:"bytes,6,opt,name=domain,proto3" json:"domain"`
	Platform    string `protobuf:"bytes,7,opt,name=platform,proto3" json:"platform"`
}

func (x *AliNativePayRequest) Reset() {
	*x = AliNativePayRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_payment_proto_msgTypes[25]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AliNativePayRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AliNativePayRequest) ProtoMessage() {}

func (x *AliNativePayRequest) ProtoReflect() protoreflect.Message {
	mi := &file_pb_payment_proto_msgTypes[25]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AliNativePayRequest.ProtoReflect.Descriptor instead.
func (*AliNativePayRequest) Descriptor() ([]byte, []int) {
	return file_pb_payment_proto_rawDescGZIP(), []int{25}
}

func (x *AliNativePayRequest) GetSubject() string {
	if x != nil {
		return x.Subject
	}
	return ""
}

func (x *AliNativePayRequest) GetOutTradeNo() string {
	if x != nil {
		return x.OutTradeNo
	}
	return ""
}

func (x *AliNativePayRequest) GetNotifyUrl() string {
	if x != nil {
		return x.NotifyUrl
	}
	return ""
}

func (x *AliNativePayRequest) GetTotalAmount() int32 {
	if x != nil {
		return x.TotalAmount
	}
	return 0
}

func (x *AliNativePayRequest) GetTimeExpire() int32 {
	if x != nil {
		return x.TimeExpire
	}
	return 0
}

func (x *AliNativePayRequest) GetDomain() string {
	if x != nil {
		return x.Domain
	}
	return ""
}

func (x *AliNativePayRequest) GetPlatform() string {
	if x != nil {
		return x.Platform
	}
	return ""
}

type AliNativePayResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Url string `protobuf:"bytes,1,opt,name=url,proto3" json:"url"`
	Msg string `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg"`
}

func (x *AliNativePayResponse) Reset() {
	*x = AliNativePayResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_payment_proto_msgTypes[26]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AliNativePayResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AliNativePayResponse) ProtoMessage() {}

func (x *AliNativePayResponse) ProtoReflect() protoreflect.Message {
	mi := &file_pb_payment_proto_msgTypes[26]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AliNativePayResponse.ProtoReflect.Descriptor instead.
func (*AliNativePayResponse) Descriptor() ([]byte, []int) {
	return file_pb_payment_proto_rawDescGZIP(), []int{26}
}

func (x *AliNativePayResponse) GetUrl() string {
	if x != nil {
		return x.Url
	}
	return ""
}

func (x *AliNativePayResponse) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

type AliPcWabPayRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Subject     string `protobuf:"bytes,1,opt,name=subject,proto3" json:"subject"`
	OutTradeNo  string `protobuf:"bytes,2,opt,name=outTradeNo,json=out_trade_no,proto3" json:"outTradeNo"`
	NotifyUrl   string `protobuf:"bytes,3,opt,name=notifyUrl,json=notify_url,proto3" json:"notifyUrl"`
	TotalAmount int32  `protobuf:"varint,4,opt,name=totalAmount,json=total_amount,proto3" json:"totalAmount"`
	TimeExpire  int32  `protobuf:"varint,5,opt,name=timeExpire,json=time_expire,proto3" json:"timeExpire"`
	Domain      string `protobuf:"bytes,6,opt,name=domain,proto3" json:"domain"`
	Platform    string `protobuf:"bytes,7,opt,name=platform,proto3" json:"platform"`
	QuitUrl     string `protobuf:"bytes,8,opt,name=quitUrl,json=quit_url,proto3" json:"quitUrl"`
	ReturnUrl   string `protobuf:"bytes,9,opt,name=returnUrl,json=return_url,proto3" json:"returnUrl"`
}

func (x *AliPcWabPayRequest) Reset() {
	*x = AliPcWabPayRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_payment_proto_msgTypes[27]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AliPcWabPayRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AliPcWabPayRequest) ProtoMessage() {}

func (x *AliPcWabPayRequest) ProtoReflect() protoreflect.Message {
	mi := &file_pb_payment_proto_msgTypes[27]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AliPcWabPayRequest.ProtoReflect.Descriptor instead.
func (*AliPcWabPayRequest) Descriptor() ([]byte, []int) {
	return file_pb_payment_proto_rawDescGZIP(), []int{27}
}

func (x *AliPcWabPayRequest) GetSubject() string {
	if x != nil {
		return x.Subject
	}
	return ""
}

func (x *AliPcWabPayRequest) GetOutTradeNo() string {
	if x != nil {
		return x.OutTradeNo
	}
	return ""
}

func (x *AliPcWabPayRequest) GetNotifyUrl() string {
	if x != nil {
		return x.NotifyUrl
	}
	return ""
}

func (x *AliPcWabPayRequest) GetTotalAmount() int32 {
	if x != nil {
		return x.TotalAmount
	}
	return 0
}

func (x *AliPcWabPayRequest) GetTimeExpire() int32 {
	if x != nil {
		return x.TimeExpire
	}
	return 0
}

func (x *AliPcWabPayRequest) GetDomain() string {
	if x != nil {
		return x.Domain
	}
	return ""
}

func (x *AliPcWabPayRequest) GetPlatform() string {
	if x != nil {
		return x.Platform
	}
	return ""
}

func (x *AliPcWabPayRequest) GetQuitUrl() string {
	if x != nil {
		return x.QuitUrl
	}
	return ""
}

func (x *AliPcWabPayRequest) GetReturnUrl() string {
	if x != nil {
		return x.ReturnUrl
	}
	return ""
}

type AliPcWabPayResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PageRedirectionData string `protobuf:"bytes,1,opt,name=pageRedirectionData,json=page_redirection_data,proto3" json:"pageRedirectionData"`
	Msg                 string `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg"`
}

func (x *AliPcWabPayResponse) Reset() {
	*x = AliPcWabPayResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_payment_proto_msgTypes[28]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AliPcWabPayResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AliPcWabPayResponse) ProtoMessage() {}

func (x *AliPcWabPayResponse) ProtoReflect() protoreflect.Message {
	mi := &file_pb_payment_proto_msgTypes[28]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AliPcWabPayResponse.ProtoReflect.Descriptor instead.
func (*AliPcWabPayResponse) Descriptor() ([]byte, []int) {
	return file_pb_payment_proto_rawDescGZIP(), []int{28}
}

func (x *AliPcWabPayResponse) GetPageRedirectionData() string {
	if x != nil {
		return x.PageRedirectionData
	}
	return ""
}

func (x *AliPcWabPayResponse) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

type AliReFundRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	OutTradeNo  string `protobuf:"bytes,1,opt,name=outTradeNo,json=out_trade_no,proto3" json:"outTradeNo"`
	TotalAmount int32  `protobuf:"varint,2,opt,name=totalAmount,json=total_amount,proto3" json:"totalAmount"`
}

func (x *AliReFundRequest) Reset() {
	*x = AliReFundRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_payment_proto_msgTypes[29]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AliReFundRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AliReFundRequest) ProtoMessage() {}

func (x *AliReFundRequest) ProtoReflect() protoreflect.Message {
	mi := &file_pb_payment_proto_msgTypes[29]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AliReFundRequest.ProtoReflect.Descriptor instead.
func (*AliReFundRequest) Descriptor() ([]byte, []int) {
	return file_pb_payment_proto_rawDescGZIP(), []int{29}
}

func (x *AliReFundRequest) GetOutTradeNo() string {
	if x != nil {
		return x.OutTradeNo
	}
	return ""
}

func (x *AliReFundRequest) GetTotalAmount() int32 {
	if x != nil {
		return x.TotalAmount
	}
	return 0
}

type AliReFundResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TradeNo      string `protobuf:"bytes,1,opt,name=tradeNo,json=trade_no,proto3" json:"tradeNo"`
	OutTradeNo   string `protobuf:"bytes,2,opt,name=outTradeNo,json=out_trade_no,proto3" json:"outTradeNo"`
	BuyerLogonId string `protobuf:"bytes,3,opt,name=buyer_logon_id,proto3" json:"buyer_logon_id"`
	RefundFee    string `protobuf:"bytes,4,opt,name=refund_fee,proto3" json:"refund_fee"`
	Msg          string `protobuf:"bytes,5,opt,name=msg,proto3" json:"msg"`
}

func (x *AliReFundResponse) Reset() {
	*x = AliReFundResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_payment_proto_msgTypes[30]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AliReFundResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AliReFundResponse) ProtoMessage() {}

func (x *AliReFundResponse) ProtoReflect() protoreflect.Message {
	mi := &file_pb_payment_proto_msgTypes[30]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AliReFundResponse.ProtoReflect.Descriptor instead.
func (*AliReFundResponse) Descriptor() ([]byte, []int) {
	return file_pb_payment_proto_rawDescGZIP(), []int{30}
}

func (x *AliReFundResponse) GetTradeNo() string {
	if x != nil {
		return x.TradeNo
	}
	return ""
}

func (x *AliReFundResponse) GetOutTradeNo() string {
	if x != nil {
		return x.OutTradeNo
	}
	return ""
}

func (x *AliReFundResponse) GetBuyerLogonId() string {
	if x != nil {
		return x.BuyerLogonId
	}
	return ""
}

func (x *AliReFundResponse) GetRefundFee() string {
	if x != nil {
		return x.RefundFee
	}
	return ""
}

func (x *AliReFundResponse) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

type AliNotifyRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RawQuery string `protobuf:"bytes,1,opt,name=rawQuery,json=raw_query,proto3" json:"rawQuery"`
}

func (x *AliNotifyRequest) Reset() {
	*x = AliNotifyRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_payment_proto_msgTypes[31]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AliNotifyRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AliNotifyRequest) ProtoMessage() {}

func (x *AliNotifyRequest) ProtoReflect() protoreflect.Message {
	mi := &file_pb_payment_proto_msgTypes[31]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AliNotifyRequest.ProtoReflect.Descriptor instead.
func (*AliNotifyRequest) Descriptor() ([]byte, []int) {
	return file_pb_payment_proto_rawDescGZIP(), []int{31}
}

func (x *AliNotifyRequest) GetRawQuery() string {
	if x != nil {
		return x.RawQuery
	}
	return ""
}

type AliNotifyResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	NotifyTime   string `protobuf:"bytes,1,opt,name=notifyTime,json=notify_time,proto3" json:"notifyTime"`
	NotifyType   string `protobuf:"bytes,2,opt,name=notifyType,json=notify_type,proto3" json:"notifyType"`
	NotifyId     string `protobuf:"bytes,3,opt,name=notifyId,json=notify_id,proto3" json:"notifyId"`
	AppId        string `protobuf:"bytes,4,opt,name=appId,json=app_id,proto3" json:"appId"`
	Version      string `protobuf:"bytes,5,opt,name=version,proto3" json:"version"`
	SignType     string `protobuf:"bytes,6,opt,name=signType,json=sign_type,proto3" json:"signType"`
	Sign         string `protobuf:"bytes,7,opt,name=sign,proto3" json:"sign"`
	TradeNo      string `protobuf:"bytes,8,opt,name=tradeNo,json=trade_no,proto3" json:"tradeNo"`
	OutTradeNo   string `protobuf:"bytes,9,opt,name=outTradeNo,json=out_trade_no,proto3" json:"outTradeNo"`
	BuyerLogonId string `protobuf:"bytes,10,opt,name=buyerLogonId,json=buyer_logon_id,proto3" json:"buyerLogonId"`
	BuyerId      string `protobuf:"bytes,11,opt,name=buyerId,json=buyer_id,proto3" json:"buyerId"`
	TradeStatus  string `protobuf:"bytes,12,opt,name=tradeStatus,json=trade_status,proto3" json:"tradeStatus"`
}

func (x *AliNotifyResponse) Reset() {
	*x = AliNotifyResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_payment_proto_msgTypes[32]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AliNotifyResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AliNotifyResponse) ProtoMessage() {}

func (x *AliNotifyResponse) ProtoReflect() protoreflect.Message {
	mi := &file_pb_payment_proto_msgTypes[32]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AliNotifyResponse.ProtoReflect.Descriptor instead.
func (*AliNotifyResponse) Descriptor() ([]byte, []int) {
	return file_pb_payment_proto_rawDescGZIP(), []int{32}
}

func (x *AliNotifyResponse) GetNotifyTime() string {
	if x != nil {
		return x.NotifyTime
	}
	return ""
}

func (x *AliNotifyResponse) GetNotifyType() string {
	if x != nil {
		return x.NotifyType
	}
	return ""
}

func (x *AliNotifyResponse) GetNotifyId() string {
	if x != nil {
		return x.NotifyId
	}
	return ""
}

func (x *AliNotifyResponse) GetAppId() string {
	if x != nil {
		return x.AppId
	}
	return ""
}

func (x *AliNotifyResponse) GetVersion() string {
	if x != nil {
		return x.Version
	}
	return ""
}

func (x *AliNotifyResponse) GetSignType() string {
	if x != nil {
		return x.SignType
	}
	return ""
}

func (x *AliNotifyResponse) GetSign() string {
	if x != nil {
		return x.Sign
	}
	return ""
}

func (x *AliNotifyResponse) GetTradeNo() string {
	if x != nil {
		return x.TradeNo
	}
	return ""
}

func (x *AliNotifyResponse) GetOutTradeNo() string {
	if x != nil {
		return x.OutTradeNo
	}
	return ""
}

func (x *AliNotifyResponse) GetBuyerLogonId() string {
	if x != nil {
		return x.BuyerLogonId
	}
	return ""
}

func (x *AliNotifyResponse) GetBuyerId() string {
	if x != nil {
		return x.BuyerId
	}
	return ""
}

func (x *AliNotifyResponse) GetTradeStatus() string {
	if x != nil {
		return x.TradeStatus
	}
	return ""
}

type AliQueryByOutTradeNoRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	OutTradeNo string `protobuf:"bytes,1,opt,name=outTradeNo,proto3" json:"outTradeNo"`
}

func (x *AliQueryByOutTradeNoRequest) Reset() {
	*x = AliQueryByOutTradeNoRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_payment_proto_msgTypes[33]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AliQueryByOutTradeNoRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AliQueryByOutTradeNoRequest) ProtoMessage() {}

func (x *AliQueryByOutTradeNoRequest) ProtoReflect() protoreflect.Message {
	mi := &file_pb_payment_proto_msgTypes[33]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AliQueryByOutTradeNoRequest.ProtoReflect.Descriptor instead.
func (*AliQueryByOutTradeNoRequest) Descriptor() ([]byte, []int) {
	return file_pb_payment_proto_rawDescGZIP(), []int{33}
}

func (x *AliQueryByOutTradeNoRequest) GetOutTradeNo() string {
	if x != nil {
		return x.OutTradeNo
	}
	return ""
}

type AliQueryByOutTradeNoResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TradeNo      string `protobuf:"bytes,1,opt,name=tradeNo,json=trade_no,proto3" json:"tradeNo"`
	OutTradeNo   string `protobuf:"bytes,2,opt,name=outTradeNo,json=out_trade_no,proto3" json:"outTradeNo"`
	BuyerLogonId string `protobuf:"bytes,3,opt,name=buyerLogonId,json=buyer_logon_id,proto3" json:"buyerLogonId"`
	TradeStatus  string `protobuf:"bytes,4,opt,name=tradeStatus,json=trade_status,proto3" json:"tradeStatus"`
	TotalAmount  string `protobuf:"bytes,5,opt,name=totalAmount,json=total_amount,proto3" json:"totalAmount"`
	BuyerUserID  string `protobuf:"bytes,6,opt,name=buyerUserID,json=buyer_user_id,proto3" json:"buyerUserID"`
}

func (x *AliQueryByOutTradeNoResponse) Reset() {
	*x = AliQueryByOutTradeNoResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_payment_proto_msgTypes[34]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AliQueryByOutTradeNoResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AliQueryByOutTradeNoResponse) ProtoMessage() {}

func (x *AliQueryByOutTradeNoResponse) ProtoReflect() protoreflect.Message {
	mi := &file_pb_payment_proto_msgTypes[34]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AliQueryByOutTradeNoResponse.ProtoReflect.Descriptor instead.
func (*AliQueryByOutTradeNoResponse) Descriptor() ([]byte, []int) {
	return file_pb_payment_proto_rawDescGZIP(), []int{34}
}

func (x *AliQueryByOutTradeNoResponse) GetTradeNo() string {
	if x != nil {
		return x.TradeNo
	}
	return ""
}

func (x *AliQueryByOutTradeNoResponse) GetOutTradeNo() string {
	if x != nil {
		return x.OutTradeNo
	}
	return ""
}

func (x *AliQueryByOutTradeNoResponse) GetBuyerLogonId() string {
	if x != nil {
		return x.BuyerLogonId
	}
	return ""
}

func (x *AliQueryByOutTradeNoResponse) GetTradeStatus() string {
	if x != nil {
		return x.TradeStatus
	}
	return ""
}

func (x *AliQueryByOutTradeNoResponse) GetTotalAmount() string {
	if x != nil {
		return x.TotalAmount
	}
	return ""
}

func (x *AliQueryByOutTradeNoResponse) GetBuyerUserID() string {
	if x != nil {
		return x.BuyerUserID
	}
	return ""
}

type AliRefundQueryByOutTradeNoRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	OutTradeNo string `protobuf:"bytes,1,opt,name=outTradeNo,proto3" json:"outTradeNo"`
}

func (x *AliRefundQueryByOutTradeNoRequest) Reset() {
	*x = AliRefundQueryByOutTradeNoRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_payment_proto_msgTypes[35]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AliRefundQueryByOutTradeNoRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AliRefundQueryByOutTradeNoRequest) ProtoMessage() {}

func (x *AliRefundQueryByOutTradeNoRequest) ProtoReflect() protoreflect.Message {
	mi := &file_pb_payment_proto_msgTypes[35]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AliRefundQueryByOutTradeNoRequest.ProtoReflect.Descriptor instead.
func (*AliRefundQueryByOutTradeNoRequest) Descriptor() ([]byte, []int) {
	return file_pb_payment_proto_rawDescGZIP(), []int{35}
}

func (x *AliRefundQueryByOutTradeNoRequest) GetOutTradeNo() string {
	if x != nil {
		return x.OutTradeNo
	}
	return ""
}

type AliRefundQueryByOutTradeNoResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TradeNo      string `protobuf:"bytes,1,opt,name=tradeNo,json=trade_no,proto3" json:"tradeNo"`
	OutTradeNo   string `protobuf:"bytes,2,opt,name=outTradeNo,json=out_trade_no,proto3" json:"outTradeNo"`
	OutRequestNo string `protobuf:"bytes,3,opt,name=out_request_no,proto3" json:"out_request_no"`
	TotalAmount  string `protobuf:"bytes,4,opt,name=total_amount,proto3" json:"total_amount"`
	RefundAmount string `protobuf:"bytes,5,opt,name=refund_amount,proto3" json:"refund_amount"`
	RefundStatus string `protobuf:"bytes,6,opt,name=refund_status,proto3" json:"refund_status"`
}

func (x *AliRefundQueryByOutTradeNoResponse) Reset() {
	*x = AliRefundQueryByOutTradeNoResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_payment_proto_msgTypes[36]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AliRefundQueryByOutTradeNoResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AliRefundQueryByOutTradeNoResponse) ProtoMessage() {}

func (x *AliRefundQueryByOutTradeNoResponse) ProtoReflect() protoreflect.Message {
	mi := &file_pb_payment_proto_msgTypes[36]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AliRefundQueryByOutTradeNoResponse.ProtoReflect.Descriptor instead.
func (*AliRefundQueryByOutTradeNoResponse) Descriptor() ([]byte, []int) {
	return file_pb_payment_proto_rawDescGZIP(), []int{36}
}

func (x *AliRefundQueryByOutTradeNoResponse) GetTradeNo() string {
	if x != nil {
		return x.TradeNo
	}
	return ""
}

func (x *AliRefundQueryByOutTradeNoResponse) GetOutTradeNo() string {
	if x != nil {
		return x.OutTradeNo
	}
	return ""
}

func (x *AliRefundQueryByOutTradeNoResponse) GetOutRequestNo() string {
	if x != nil {
		return x.OutRequestNo
	}
	return ""
}

func (x *AliRefundQueryByOutTradeNoResponse) GetTotalAmount() string {
	if x != nil {
		return x.TotalAmount
	}
	return ""
}

func (x *AliRefundQueryByOutTradeNoResponse) GetRefundAmount() string {
	if x != nil {
		return x.RefundAmount
	}
	return ""
}

func (x *AliRefundQueryByOutTradeNoResponse) GetRefundStatus() string {
	if x != nil {
		return x.RefundStatus
	}
	return ""
}

type WechatJsApiPayRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Description string `protobuf:"bytes,1,opt,name=Description,json=description,proto3" json:"Description"`
	OutTradeNo  string `protobuf:"bytes,2,opt,name=OutTradeNo,json=outTradeNo,proto3" json:"OutTradeNo"`
	Cent        int64  `protobuf:"varint,3,opt,name=Cent,json=cent,proto3" json:"Cent"`
	OpenID      string `protobuf:"bytes,4,opt,name=OpenID,json=openID,proto3" json:"OpenID"`
	NotifyUrl   string `protobuf:"bytes,5,opt,name=NotifyUrl,json=notifyUrl,proto3" json:"NotifyUrl"`
	AppID       string `protobuf:"bytes,6,opt,name=AppID,json=appID,proto3" json:"AppID"`
	ClientIP    string `protobuf:"bytes,7,opt,name=ClientIP,proto3" json:"ClientIP"`
	RecordId    uint32 `protobuf:"varint,9,opt,name=recordId,proto3" json:"recordId"`
	Domain      string `protobuf:"bytes,10,opt,name=domain,proto3" json:"domain"`
	Platform    string `protobuf:"bytes,11,opt,name=platform,proto3" json:"platform"`
	Scene       string `protobuf:"bytes,12,opt,name=scene,proto3" json:"scene"`
	TimeExpire  int32  `protobuf:"varint,13,opt,name=timeExpire,json=time_expire,proto3" json:"timeExpire"`
}

func (x *WechatJsApiPayRequest) Reset() {
	*x = WechatJsApiPayRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_payment_proto_msgTypes[37]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *WechatJsApiPayRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WechatJsApiPayRequest) ProtoMessage() {}

func (x *WechatJsApiPayRequest) ProtoReflect() protoreflect.Message {
	mi := &file_pb_payment_proto_msgTypes[37]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WechatJsApiPayRequest.ProtoReflect.Descriptor instead.
func (*WechatJsApiPayRequest) Descriptor() ([]byte, []int) {
	return file_pb_payment_proto_rawDescGZIP(), []int{37}
}

func (x *WechatJsApiPayRequest) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *WechatJsApiPayRequest) GetOutTradeNo() string {
	if x != nil {
		return x.OutTradeNo
	}
	return ""
}

func (x *WechatJsApiPayRequest) GetCent() int64 {
	if x != nil {
		return x.Cent
	}
	return 0
}

func (x *WechatJsApiPayRequest) GetOpenID() string {
	if x != nil {
		return x.OpenID
	}
	return ""
}

func (x *WechatJsApiPayRequest) GetNotifyUrl() string {
	if x != nil {
		return x.NotifyUrl
	}
	return ""
}

func (x *WechatJsApiPayRequest) GetAppID() string {
	if x != nil {
		return x.AppID
	}
	return ""
}

func (x *WechatJsApiPayRequest) GetClientIP() string {
	if x != nil {
		return x.ClientIP
	}
	return ""
}

func (x *WechatJsApiPayRequest) GetRecordId() uint32 {
	if x != nil {
		return x.RecordId
	}
	return 0
}

func (x *WechatJsApiPayRequest) GetDomain() string {
	if x != nil {
		return x.Domain
	}
	return ""
}

func (x *WechatJsApiPayRequest) GetPlatform() string {
	if x != nil {
		return x.Platform
	}
	return ""
}

func (x *WechatJsApiPayRequest) GetScene() string {
	if x != nil {
		return x.Scene
	}
	return ""
}

func (x *WechatJsApiPayRequest) GetTimeExpire() int32 {
	if x != nil {
		return x.TimeExpire
	}
	return 0
}

type WechatJsApiPayResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Appid     string `protobuf:"bytes,1,opt,name=Appid,json=appid,proto3" json:"Appid"`
	TimeStamp string `protobuf:"bytes,2,opt,name=TimeStamp,json=timeStamp,proto3" json:"TimeStamp"`
	Package   string `protobuf:"bytes,3,opt,name=Package,json=package,proto3" json:"Package"`
	SignType  string `protobuf:"bytes,4,opt,name=SignType,json=signType,proto3" json:"SignType"`
	NonceStr  string `protobuf:"bytes,5,opt,name=NonceStr,json=nonceStr,proto3" json:"NonceStr"`
	PaySign   string `protobuf:"bytes,6,opt,name=PaySign,json=paySign,proto3" json:"PaySign"`
	PrepayId  string `protobuf:"bytes,7,opt,name=PrepayId,json=prepayId,proto3" json:"PrepayId"`
}

func (x *WechatJsApiPayResponse) Reset() {
	*x = WechatJsApiPayResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_payment_proto_msgTypes[38]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *WechatJsApiPayResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WechatJsApiPayResponse) ProtoMessage() {}

func (x *WechatJsApiPayResponse) ProtoReflect() protoreflect.Message {
	mi := &file_pb_payment_proto_msgTypes[38]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WechatJsApiPayResponse.ProtoReflect.Descriptor instead.
func (*WechatJsApiPayResponse) Descriptor() ([]byte, []int) {
	return file_pb_payment_proto_rawDescGZIP(), []int{38}
}

func (x *WechatJsApiPayResponse) GetAppid() string {
	if x != nil {
		return x.Appid
	}
	return ""
}

func (x *WechatJsApiPayResponse) GetTimeStamp() string {
	if x != nil {
		return x.TimeStamp
	}
	return ""
}

func (x *WechatJsApiPayResponse) GetPackage() string {
	if x != nil {
		return x.Package
	}
	return ""
}

func (x *WechatJsApiPayResponse) GetSignType() string {
	if x != nil {
		return x.SignType
	}
	return ""
}

func (x *WechatJsApiPayResponse) GetNonceStr() string {
	if x != nil {
		return x.NonceStr
	}
	return ""
}

func (x *WechatJsApiPayResponse) GetPaySign() string {
	if x != nil {
		return x.PaySign
	}
	return ""
}

func (x *WechatJsApiPayResponse) GetPrepayId() string {
	if x != nil {
		return x.PrepayId
	}
	return ""
}

type WechatAppPayResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Appid     string `protobuf:"bytes,1,opt,name=Appid,json=appid,proto3" json:"Appid"`
	TimeStamp string `protobuf:"bytes,2,opt,name=TimeStamp,json=timeStamp,proto3" json:"TimeStamp"`
	Package   string `protobuf:"bytes,3,opt,name=Package,json=package,proto3" json:"Package"`
	SignType  string `protobuf:"bytes,4,opt,name=SignType,json=signType,proto3" json:"SignType"`
	NonceStr  string `protobuf:"bytes,5,opt,name=NonceStr,json=nonceStr,proto3" json:"NonceStr"`
	PaySign   string `protobuf:"bytes,6,opt,name=PaySign,json=paySign,proto3" json:"PaySign"`
	PrepayId  string `protobuf:"bytes,7,opt,name=PrepayId,json=prepayId,proto3" json:"PrepayId"`
	PartnerId string `protobuf:"bytes,8,opt,name=PartnerId,json=partnerId,proto3" json:"PartnerId"`
	Msg       string `protobuf:"bytes,9,opt,name=msg,proto3" json:"msg"`
}

func (x *WechatAppPayResponse) Reset() {
	*x = WechatAppPayResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_payment_proto_msgTypes[39]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *WechatAppPayResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WechatAppPayResponse) ProtoMessage() {}

func (x *WechatAppPayResponse) ProtoReflect() protoreflect.Message {
	mi := &file_pb_payment_proto_msgTypes[39]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WechatAppPayResponse.ProtoReflect.Descriptor instead.
func (*WechatAppPayResponse) Descriptor() ([]byte, []int) {
	return file_pb_payment_proto_rawDescGZIP(), []int{39}
}

func (x *WechatAppPayResponse) GetAppid() string {
	if x != nil {
		return x.Appid
	}
	return ""
}

func (x *WechatAppPayResponse) GetTimeStamp() string {
	if x != nil {
		return x.TimeStamp
	}
	return ""
}

func (x *WechatAppPayResponse) GetPackage() string {
	if x != nil {
		return x.Package
	}
	return ""
}

func (x *WechatAppPayResponse) GetSignType() string {
	if x != nil {
		return x.SignType
	}
	return ""
}

func (x *WechatAppPayResponse) GetNonceStr() string {
	if x != nil {
		return x.NonceStr
	}
	return ""
}

func (x *WechatAppPayResponse) GetPaySign() string {
	if x != nil {
		return x.PaySign
	}
	return ""
}

func (x *WechatAppPayResponse) GetPrepayId() string {
	if x != nil {
		return x.PrepayId
	}
	return ""
}

func (x *WechatAppPayResponse) GetPartnerId() string {
	if x != nil {
		return x.PartnerId
	}
	return ""
}

func (x *WechatAppPayResponse) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

type WechatJsApiQueryByOutTradeNoRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	OutTradeNo string `protobuf:"bytes,1,opt,name=outTradeNo,proto3" json:"outTradeNo"`
}

func (x *WechatJsApiQueryByOutTradeNoRequest) Reset() {
	*x = WechatJsApiQueryByOutTradeNoRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_payment_proto_msgTypes[40]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *WechatJsApiQueryByOutTradeNoRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WechatJsApiQueryByOutTradeNoRequest) ProtoMessage() {}

func (x *WechatJsApiQueryByOutTradeNoRequest) ProtoReflect() protoreflect.Message {
	mi := &file_pb_payment_proto_msgTypes[40]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WechatJsApiQueryByOutTradeNoRequest.ProtoReflect.Descriptor instead.
func (*WechatJsApiQueryByOutTradeNoRequest) Descriptor() ([]byte, []int) {
	return file_pb_payment_proto_rawDescGZIP(), []int{40}
}

func (x *WechatJsApiQueryByOutTradeNoRequest) GetOutTradeNo() string {
	if x != nil {
		return x.OutTradeNo
	}
	return ""
}

type WechatJsApiQueryByOutTradeNoResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AppID         string `protobuf:"bytes,1,opt,name=appID,proto3" json:"appID"`
	MchID         string `protobuf:"bytes,2,opt,name=mchID,json=mch_id,proto3" json:"mchID"`
	OutTradeNo    string `protobuf:"bytes,3,opt,name=outTradeNo,json=out_trade_no,proto3" json:"outTradeNo"`
	TransactionId string `protobuf:"bytes,4,opt,name=transactionId,json=transaction_id,proto3" json:"transactionId"`
	TradeType     string `protobuf:"bytes,5,opt,name=tradeType,json=trade_type,proto3" json:"tradeType"`
	TradeState    string `protobuf:"bytes,6,opt,name=tradeState,json=trade_state,proto3" json:"tradeState"`
	BankType      string `protobuf:"bytes,7,opt,name=bankType,json=bank_ype,proto3" json:"bankType"`
	SuccessTime   string `protobuf:"bytes,8,opt,name=successTime,json=success_time,proto3" json:"successTime"`
	OpenID        string `protobuf:"bytes,9,opt,name=openID,json=open_id,proto3" json:"openID"`
	Total         int32  `protobuf:"varint,10,opt,name=total,proto3" json:"total"`
}

func (x *WechatJsApiQueryByOutTradeNoResponse) Reset() {
	*x = WechatJsApiQueryByOutTradeNoResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_payment_proto_msgTypes[41]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *WechatJsApiQueryByOutTradeNoResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WechatJsApiQueryByOutTradeNoResponse) ProtoMessage() {}

func (x *WechatJsApiQueryByOutTradeNoResponse) ProtoReflect() protoreflect.Message {
	mi := &file_pb_payment_proto_msgTypes[41]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WechatJsApiQueryByOutTradeNoResponse.ProtoReflect.Descriptor instead.
func (*WechatJsApiQueryByOutTradeNoResponse) Descriptor() ([]byte, []int) {
	return file_pb_payment_proto_rawDescGZIP(), []int{41}
}

func (x *WechatJsApiQueryByOutTradeNoResponse) GetAppID() string {
	if x != nil {
		return x.AppID
	}
	return ""
}

func (x *WechatJsApiQueryByOutTradeNoResponse) GetMchID() string {
	if x != nil {
		return x.MchID
	}
	return ""
}

func (x *WechatJsApiQueryByOutTradeNoResponse) GetOutTradeNo() string {
	if x != nil {
		return x.OutTradeNo
	}
	return ""
}

func (x *WechatJsApiQueryByOutTradeNoResponse) GetTransactionId() string {
	if x != nil {
		return x.TransactionId
	}
	return ""
}

func (x *WechatJsApiQueryByOutTradeNoResponse) GetTradeType() string {
	if x != nil {
		return x.TradeType
	}
	return ""
}

func (x *WechatJsApiQueryByOutTradeNoResponse) GetTradeState() string {
	if x != nil {
		return x.TradeState
	}
	return ""
}

func (x *WechatJsApiQueryByOutTradeNoResponse) GetBankType() string {
	if x != nil {
		return x.BankType
	}
	return ""
}

func (x *WechatJsApiQueryByOutTradeNoResponse) GetSuccessTime() string {
	if x != nil {
		return x.SuccessTime
	}
	return ""
}

func (x *WechatJsApiQueryByOutTradeNoResponse) GetOpenID() string {
	if x != nil {
		return x.OpenID
	}
	return ""
}

func (x *WechatJsApiQueryByOutTradeNoResponse) GetTotal() int32 {
	if x != nil {
		return x.Total
	}
	return 0
}

type WechatAppQueryByOutTradeNoRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	OutTradeNo string `protobuf:"bytes,1,opt,name=outTradeNo,proto3" json:"outTradeNo"`
}

func (x *WechatAppQueryByOutTradeNoRequest) Reset() {
	*x = WechatAppQueryByOutTradeNoRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_payment_proto_msgTypes[42]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *WechatAppQueryByOutTradeNoRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WechatAppQueryByOutTradeNoRequest) ProtoMessage() {}

func (x *WechatAppQueryByOutTradeNoRequest) ProtoReflect() protoreflect.Message {
	mi := &file_pb_payment_proto_msgTypes[42]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WechatAppQueryByOutTradeNoRequest.ProtoReflect.Descriptor instead.
func (*WechatAppQueryByOutTradeNoRequest) Descriptor() ([]byte, []int) {
	return file_pb_payment_proto_rawDescGZIP(), []int{42}
}

func (x *WechatAppQueryByOutTradeNoRequest) GetOutTradeNo() string {
	if x != nil {
		return x.OutTradeNo
	}
	return ""
}

type WechatAppQueryByOutTradeNoResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AppID string `protobuf:"bytes,1,opt,name=appID,proto3" json:"appID"`             // 服务提供商的应用ID
	MchID string `protobuf:"bytes,2,opt,name=mchID,json=mch_id,proto3" json:"mchID"` // 服务商商户ID
	// string subAppId = 3 [json_name = "sub_appid"]; // 子应用ID
	// string subMchId = 4 [json_name = "sub_mchid"];// 子商户ID
	OutTradeNo     string `protobuf:"bytes,5,opt,name=outTradeNo,json=out_trade_no,proto3" json:"outTradeNo"`             // 订单号
	TransactionId  string `protobuf:"bytes,6,opt,name=transactionId,json=transaction_id,proto3" json:"transactionId"`     // 微信交易ID
	TradeType      string `protobuf:"bytes,7,opt,name=tradeType,json=trade_type,proto3" json:"tradeType"`                 // 交易类型
	TradeState     string `protobuf:"bytes,8,opt,name=tradeState,json=trade_state,proto3" json:"tradeState"`              // 交易状态
	TradeStateDesc string `protobuf:"bytes,9,opt,name=tradeStateDesc,json=trade_state_desc,proto3" json:"tradeStateDesc"` // 交易状态描述
	BankType       string `protobuf:"bytes,10,opt,name=bankType,json=bank_type,proto3" json:"bankType"`                   // 银行类型
	Attach         string `protobuf:"bytes,11,opt,name=attach,proto3" json:"attach"`                                      // 附加数据
	SuccessTime    string `protobuf:"bytes,12,opt,name=successTime,json=success_time,proto3" json:"successTime"`          // 支付成功时间
	// 包含促销详情列表
	PromotionDetail []*WechatAppQueryByOutTradeNoResponse_PromotionDetail `protobuf:"bytes,13,rep,name=promotionDetail,json=promotion_detail,proto3" json:"promotionDetail"`
	// 付款人信息
	Payer *WechatAppQueryByOutTradeNoResponse_Payer `protobuf:"bytes,14,opt,name=payer,proto3" json:"payer"`
	// 金额信息
	Amount *WechatAppQueryByOutTradeNoResponse_Amount `protobuf:"bytes,15,opt,name=amount,proto3" json:"amount"`
}

func (x *WechatAppQueryByOutTradeNoResponse) Reset() {
	*x = WechatAppQueryByOutTradeNoResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_payment_proto_msgTypes[43]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *WechatAppQueryByOutTradeNoResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WechatAppQueryByOutTradeNoResponse) ProtoMessage() {}

func (x *WechatAppQueryByOutTradeNoResponse) ProtoReflect() protoreflect.Message {
	mi := &file_pb_payment_proto_msgTypes[43]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WechatAppQueryByOutTradeNoResponse.ProtoReflect.Descriptor instead.
func (*WechatAppQueryByOutTradeNoResponse) Descriptor() ([]byte, []int) {
	return file_pb_payment_proto_rawDescGZIP(), []int{43}
}

func (x *WechatAppQueryByOutTradeNoResponse) GetAppID() string {
	if x != nil {
		return x.AppID
	}
	return ""
}

func (x *WechatAppQueryByOutTradeNoResponse) GetMchID() string {
	if x != nil {
		return x.MchID
	}
	return ""
}

func (x *WechatAppQueryByOutTradeNoResponse) GetOutTradeNo() string {
	if x != nil {
		return x.OutTradeNo
	}
	return ""
}

func (x *WechatAppQueryByOutTradeNoResponse) GetTransactionId() string {
	if x != nil {
		return x.TransactionId
	}
	return ""
}

func (x *WechatAppQueryByOutTradeNoResponse) GetTradeType() string {
	if x != nil {
		return x.TradeType
	}
	return ""
}

func (x *WechatAppQueryByOutTradeNoResponse) GetTradeState() string {
	if x != nil {
		return x.TradeState
	}
	return ""
}

func (x *WechatAppQueryByOutTradeNoResponse) GetTradeStateDesc() string {
	if x != nil {
		return x.TradeStateDesc
	}
	return ""
}

func (x *WechatAppQueryByOutTradeNoResponse) GetBankType() string {
	if x != nil {
		return x.BankType
	}
	return ""
}

func (x *WechatAppQueryByOutTradeNoResponse) GetAttach() string {
	if x != nil {
		return x.Attach
	}
	return ""
}

func (x *WechatAppQueryByOutTradeNoResponse) GetSuccessTime() string {
	if x != nil {
		return x.SuccessTime
	}
	return ""
}

func (x *WechatAppQueryByOutTradeNoResponse) GetPromotionDetail() []*WechatAppQueryByOutTradeNoResponse_PromotionDetail {
	if x != nil {
		return x.PromotionDetail
	}
	return nil
}

func (x *WechatAppQueryByOutTradeNoResponse) GetPayer() *WechatAppQueryByOutTradeNoResponse_Payer {
	if x != nil {
		return x.Payer
	}
	return nil
}

func (x *WechatAppQueryByOutTradeNoResponse) GetAmount() *WechatAppQueryByOutTradeNoResponse_Amount {
	if x != nil {
		return x.Amount
	}
	return nil
}

type GetPayByOutTradeNoRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	OutTradeNo string `protobuf:"bytes,4,opt,name=OutTradeNo,proto3" json:"OutTradeNo"`
}

func (x *GetPayByOutTradeNoRequest) Reset() {
	*x = GetPayByOutTradeNoRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_payment_proto_msgTypes[44]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetPayByOutTradeNoRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetPayByOutTradeNoRequest) ProtoMessage() {}

func (x *GetPayByOutTradeNoRequest) ProtoReflect() protoreflect.Message {
	mi := &file_pb_payment_proto_msgTypes[44]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetPayByOutTradeNoRequest.ProtoReflect.Descriptor instead.
func (*GetPayByOutTradeNoRequest) Descriptor() ([]byte, []int) {
	return file_pb_payment_proto_rawDescGZIP(), []int{44}
}

func (x *GetPayByOutTradeNoRequest) GetOutTradeNo() string {
	if x != nil {
		return x.OutTradeNo
	}
	return ""
}

type GetPayByOutTradeNoResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	OutTradeNo string `protobuf:"bytes,1,opt,name=outTradeNo,proto3" json:"outTradeNo"`
	RecordId   uint32 `protobuf:"varint,2,opt,name=recordId,proto3" json:"recordId"`
	Cent       string `protobuf:"bytes,3,opt,name=cent,proto3" json:"cent"`
	Platform   string `protobuf:"bytes,4,opt,name=platform,proto3" json:"platform"`
	Domain     string `protobuf:"bytes,5,opt,name=domain,proto3" json:"domain"`
	Scene      string `protobuf:"bytes,6,opt,name=scene,proto3" json:"scene"`
}

func (x *GetPayByOutTradeNoResponse) Reset() {
	*x = GetPayByOutTradeNoResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_payment_proto_msgTypes[45]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetPayByOutTradeNoResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetPayByOutTradeNoResponse) ProtoMessage() {}

func (x *GetPayByOutTradeNoResponse) ProtoReflect() protoreflect.Message {
	mi := &file_pb_payment_proto_msgTypes[45]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetPayByOutTradeNoResponse.ProtoReflect.Descriptor instead.
func (*GetPayByOutTradeNoResponse) Descriptor() ([]byte, []int) {
	return file_pb_payment_proto_rawDescGZIP(), []int{45}
}

func (x *GetPayByOutTradeNoResponse) GetOutTradeNo() string {
	if x != nil {
		return x.OutTradeNo
	}
	return ""
}

func (x *GetPayByOutTradeNoResponse) GetRecordId() uint32 {
	if x != nil {
		return x.RecordId
	}
	return 0
}

func (x *GetPayByOutTradeNoResponse) GetCent() string {
	if x != nil {
		return x.Cent
	}
	return ""
}

func (x *GetPayByOutTradeNoResponse) GetPlatform() string {
	if x != nil {
		return x.Platform
	}
	return ""
}

func (x *GetPayByOutTradeNoResponse) GetDomain() string {
	if x != nil {
		return x.Domain
	}
	return ""
}

func (x *GetPayByOutTradeNoResponse) GetScene() string {
	if x != nil {
		return x.Scene
	}
	return ""
}

type WechatJsApiRefundsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RefundId      string `protobuf:"bytes,1,opt,name=RefundId,proto3" json:"RefundId"`
	OutRefundNo   string `protobuf:"bytes,2,opt,name=OutRefundNo,proto3" json:"OutRefundNo"`
	TransactionId string `protobuf:"bytes,3,opt,name=TransactionId,proto3" json:"TransactionId"`
	OutTradeNo    string `protobuf:"bytes,4,opt,name=OutTradeNo,proto3" json:"OutTradeNo"`
	SuccessTime   string `protobuf:"bytes,5,opt,name=SuccessTime,proto3" json:"SuccessTime"`
	CreateTime    string `protobuf:"bytes,6,opt,name=CreateTime,proto3" json:"CreateTime"`
	Status        string `protobuf:"bytes,7,opt,name=Status,proto3" json:"Status"`
	Amount        string `protobuf:"bytes,8,opt,name=Amount,proto3" json:"Amount"`
}

func (x *WechatJsApiRefundsResponse) Reset() {
	*x = WechatJsApiRefundsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_payment_proto_msgTypes[46]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *WechatJsApiRefundsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WechatJsApiRefundsResponse) ProtoMessage() {}

func (x *WechatJsApiRefundsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_pb_payment_proto_msgTypes[46]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WechatJsApiRefundsResponse.ProtoReflect.Descriptor instead.
func (*WechatJsApiRefundsResponse) Descriptor() ([]byte, []int) {
	return file_pb_payment_proto_rawDescGZIP(), []int{46}
}

func (x *WechatJsApiRefundsResponse) GetRefundId() string {
	if x != nil {
		return x.RefundId
	}
	return ""
}

func (x *WechatJsApiRefundsResponse) GetOutRefundNo() string {
	if x != nil {
		return x.OutRefundNo
	}
	return ""
}

func (x *WechatJsApiRefundsResponse) GetTransactionId() string {
	if x != nil {
		return x.TransactionId
	}
	return ""
}

func (x *WechatJsApiRefundsResponse) GetOutTradeNo() string {
	if x != nil {
		return x.OutTradeNo
	}
	return ""
}

func (x *WechatJsApiRefundsResponse) GetSuccessTime() string {
	if x != nil {
		return x.SuccessTime
	}
	return ""
}

func (x *WechatJsApiRefundsResponse) GetCreateTime() string {
	if x != nil {
		return x.CreateTime
	}
	return ""
}

func (x *WechatJsApiRefundsResponse) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

func (x *WechatJsApiRefundsResponse) GetAmount() string {
	if x != nil {
		return x.Amount
	}
	return ""
}

type WechatNativePayRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Description string `protobuf:"bytes,1,opt,name=description,proto3" json:"description"`
	OutTradeNo  string `protobuf:"bytes,2,opt,name=outTradeNo,proto3" json:"outTradeNo"`
	Cent        int32  `protobuf:"varint,3,opt,name=cent,proto3" json:"cent"`
	NotifyUrl   string `protobuf:"bytes,5,opt,name=notifyUrl,proto3" json:"notifyUrl"`
	AppID       string `protobuf:"bytes,6,opt,name=appID,proto3" json:"appID"`
	ClientIP    string `protobuf:"bytes,7,opt,name=clientIP,json=ClientIP,proto3" json:"clientIP"`
	RecordId    uint32 `protobuf:"varint,8,opt,name=recordId,proto3" json:"recordId"`
	Domain      string `protobuf:"bytes,9,opt,name=domain,proto3" json:"domain"`
	Platform    string `protobuf:"bytes,10,opt,name=platform,proto3" json:"platform"`
	Scene       string `protobuf:"bytes,11,opt,name=scene,proto3" json:"scene"`
	TimeExpire  int32  `protobuf:"varint,12,opt,name=timeExpire,json=time_expire,proto3" json:"timeExpire"`
}

func (x *WechatNativePayRequest) Reset() {
	*x = WechatNativePayRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_payment_proto_msgTypes[47]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *WechatNativePayRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WechatNativePayRequest) ProtoMessage() {}

func (x *WechatNativePayRequest) ProtoReflect() protoreflect.Message {
	mi := &file_pb_payment_proto_msgTypes[47]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WechatNativePayRequest.ProtoReflect.Descriptor instead.
func (*WechatNativePayRequest) Descriptor() ([]byte, []int) {
	return file_pb_payment_proto_rawDescGZIP(), []int{47}
}

func (x *WechatNativePayRequest) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *WechatNativePayRequest) GetOutTradeNo() string {
	if x != nil {
		return x.OutTradeNo
	}
	return ""
}

func (x *WechatNativePayRequest) GetCent() int32 {
	if x != nil {
		return x.Cent
	}
	return 0
}

func (x *WechatNativePayRequest) GetNotifyUrl() string {
	if x != nil {
		return x.NotifyUrl
	}
	return ""
}

func (x *WechatNativePayRequest) GetAppID() string {
	if x != nil {
		return x.AppID
	}
	return ""
}

func (x *WechatNativePayRequest) GetClientIP() string {
	if x != nil {
		return x.ClientIP
	}
	return ""
}

func (x *WechatNativePayRequest) GetRecordId() uint32 {
	if x != nil {
		return x.RecordId
	}
	return 0
}

func (x *WechatNativePayRequest) GetDomain() string {
	if x != nil {
		return x.Domain
	}
	return ""
}

func (x *WechatNativePayRequest) GetPlatform() string {
	if x != nil {
		return x.Platform
	}
	return ""
}

func (x *WechatNativePayRequest) GetScene() string {
	if x != nil {
		return x.Scene
	}
	return ""
}

func (x *WechatNativePayRequest) GetTimeExpire() int32 {
	if x != nil {
		return x.TimeExpire
	}
	return 0
}

type WechatNativePayResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CodeUrl string `protobuf:"bytes,1,opt,name=codeUrl,json=code_url,proto3" json:"codeUrl"`
}

func (x *WechatNativePayResponse) Reset() {
	*x = WechatNativePayResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_payment_proto_msgTypes[48]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *WechatNativePayResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WechatNativePayResponse) ProtoMessage() {}

func (x *WechatNativePayResponse) ProtoReflect() protoreflect.Message {
	mi := &file_pb_payment_proto_msgTypes[48]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WechatNativePayResponse.ProtoReflect.Descriptor instead.
func (*WechatNativePayResponse) Descriptor() ([]byte, []int) {
	return file_pb_payment_proto_rawDescGZIP(), []int{48}
}

func (x *WechatNativePayResponse) GetCodeUrl() string {
	if x != nil {
		return x.CodeUrl
	}
	return ""
}

type WechatNativeQueryByOutTradeNoRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	OutTradeNo string `protobuf:"bytes,1,opt,name=outTradeNo,proto3" json:"outTradeNo"`
}

func (x *WechatNativeQueryByOutTradeNoRequest) Reset() {
	*x = WechatNativeQueryByOutTradeNoRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_payment_proto_msgTypes[49]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *WechatNativeQueryByOutTradeNoRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WechatNativeQueryByOutTradeNoRequest) ProtoMessage() {}

func (x *WechatNativeQueryByOutTradeNoRequest) ProtoReflect() protoreflect.Message {
	mi := &file_pb_payment_proto_msgTypes[49]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WechatNativeQueryByOutTradeNoRequest.ProtoReflect.Descriptor instead.
func (*WechatNativeQueryByOutTradeNoRequest) Descriptor() ([]byte, []int) {
	return file_pb_payment_proto_rawDescGZIP(), []int{49}
}

func (x *WechatNativeQueryByOutTradeNoRequest) GetOutTradeNo() string {
	if x != nil {
		return x.OutTradeNo
	}
	return ""
}

type WechatRefundQueryByOutRefundNoRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	OutRefundNo string `protobuf:"bytes,1,opt,name=outRefundNo,json=out_refund_no,proto3" json:"outRefundNo"`
}

func (x *WechatRefundQueryByOutRefundNoRequest) Reset() {
	*x = WechatRefundQueryByOutRefundNoRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_payment_proto_msgTypes[50]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *WechatRefundQueryByOutRefundNoRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WechatRefundQueryByOutRefundNoRequest) ProtoMessage() {}

func (x *WechatRefundQueryByOutRefundNoRequest) ProtoReflect() protoreflect.Message {
	mi := &file_pb_payment_proto_msgTypes[50]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WechatRefundQueryByOutRefundNoRequest.ProtoReflect.Descriptor instead.
func (*WechatRefundQueryByOutRefundNoRequest) Descriptor() ([]byte, []int) {
	return file_pb_payment_proto_rawDescGZIP(), []int{50}
}

func (x *WechatRefundQueryByOutRefundNoRequest) GetOutRefundNo() string {
	if x != nil {
		return x.OutRefundNo
	}
	return ""
}

type WechatRefundQueryByOutRefundNoResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RefundId            string `protobuf:"bytes,1,opt,name=refundId,json=refund_id,proto3" json:"refundId"`
	OutRefundNo         string `protobuf:"bytes,2,opt,name=outRefundNo,json=out_refund_no,proto3" json:"outRefundNo"`
	TransactionId       string `protobuf:"bytes,3,opt,name=transactionId,json=transaction_id,proto3" json:"transactionId"`
	OutTradeNo          string `protobuf:"bytes,4,opt,name=outTradeNo,json=out_trade_no,proto3" json:"outTradeNo"`
	Channel             string `protobuf:"bytes,5,opt,name=channel,proto3" json:"channel"`
	UserReceivedAccount string `protobuf:"bytes,6,opt,name=userReceivedAccount,json=user_received_account,proto3" json:"userReceivedAccount"`
	SuccessTime         string `protobuf:"bytes,7,opt,name=successTime,json=success_time,proto3" json:"successTime"`
	CreateTime          string `protobuf:"bytes,8,opt,name=createTime,json=create_time,proto3" json:"createTime"`
	Status              string `protobuf:"bytes,9,opt,name=status,proto3" json:"status"`
	Total               int32  `protobuf:"varint,10,opt,name=total,proto3" json:"total"`
}

func (x *WechatRefundQueryByOutRefundNoResponse) Reset() {
	*x = WechatRefundQueryByOutRefundNoResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_payment_proto_msgTypes[51]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *WechatRefundQueryByOutRefundNoResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WechatRefundQueryByOutRefundNoResponse) ProtoMessage() {}

func (x *WechatRefundQueryByOutRefundNoResponse) ProtoReflect() protoreflect.Message {
	mi := &file_pb_payment_proto_msgTypes[51]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WechatRefundQueryByOutRefundNoResponse.ProtoReflect.Descriptor instead.
func (*WechatRefundQueryByOutRefundNoResponse) Descriptor() ([]byte, []int) {
	return file_pb_payment_proto_rawDescGZIP(), []int{51}
}

func (x *WechatRefundQueryByOutRefundNoResponse) GetRefundId() string {
	if x != nil {
		return x.RefundId
	}
	return ""
}

func (x *WechatRefundQueryByOutRefundNoResponse) GetOutRefundNo() string {
	if x != nil {
		return x.OutRefundNo
	}
	return ""
}

func (x *WechatRefundQueryByOutRefundNoResponse) GetTransactionId() string {
	if x != nil {
		return x.TransactionId
	}
	return ""
}

func (x *WechatRefundQueryByOutRefundNoResponse) GetOutTradeNo() string {
	if x != nil {
		return x.OutTradeNo
	}
	return ""
}

func (x *WechatRefundQueryByOutRefundNoResponse) GetChannel() string {
	if x != nil {
		return x.Channel
	}
	return ""
}

func (x *WechatRefundQueryByOutRefundNoResponse) GetUserReceivedAccount() string {
	if x != nil {
		return x.UserReceivedAccount
	}
	return ""
}

func (x *WechatRefundQueryByOutRefundNoResponse) GetSuccessTime() string {
	if x != nil {
		return x.SuccessTime
	}
	return ""
}

func (x *WechatRefundQueryByOutRefundNoResponse) GetCreateTime() string {
	if x != nil {
		return x.CreateTime
	}
	return ""
}

func (x *WechatRefundQueryByOutRefundNoResponse) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

func (x *WechatRefundQueryByOutRefundNoResponse) GetTotal() int32 {
	if x != nil {
		return x.Total
	}
	return 0
}

type WechatNativeQueryByOutTradeNoResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AppID         string `protobuf:"bytes,1,opt,name=appID,proto3" json:"appID"`
	MchID         string `protobuf:"bytes,2,opt,name=mchID,json=mch_id,proto3" json:"mchID"`
	OutTradeNo    string `protobuf:"bytes,3,opt,name=outTradeNo,json=out_trade_no,proto3" json:"outTradeNo"`
	TransactionId string `protobuf:"bytes,4,opt,name=transactionId,json=transaction_id,proto3" json:"transactionId"`
	TradeType     string `protobuf:"bytes,5,opt,name=tradeType,json=trade_type,proto3" json:"tradeType"`
	TradeState    string `protobuf:"bytes,6,opt,name=tradeState,json=trade_state,proto3" json:"tradeState"`
	BankType      string `protobuf:"bytes,7,opt,name=bankType,json=bank_ype,proto3" json:"bankType"`
	SuccessTime   string `protobuf:"bytes,8,opt,name=successTime,json=success_time,proto3" json:"successTime"`
	OpenID        string `protobuf:"bytes,9,opt,name=openID,json=open_id,proto3" json:"openID"`
	Total         int32  `protobuf:"varint,10,opt,name=total,proto3" json:"total"`
}

func (x *WechatNativeQueryByOutTradeNoResponse) Reset() {
	*x = WechatNativeQueryByOutTradeNoResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_payment_proto_msgTypes[52]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *WechatNativeQueryByOutTradeNoResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WechatNativeQueryByOutTradeNoResponse) ProtoMessage() {}

func (x *WechatNativeQueryByOutTradeNoResponse) ProtoReflect() protoreflect.Message {
	mi := &file_pb_payment_proto_msgTypes[52]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WechatNativeQueryByOutTradeNoResponse.ProtoReflect.Descriptor instead.
func (*WechatNativeQueryByOutTradeNoResponse) Descriptor() ([]byte, []int) {
	return file_pb_payment_proto_rawDescGZIP(), []int{52}
}

func (x *WechatNativeQueryByOutTradeNoResponse) GetAppID() string {
	if x != nil {
		return x.AppID
	}
	return ""
}

func (x *WechatNativeQueryByOutTradeNoResponse) GetMchID() string {
	if x != nil {
		return x.MchID
	}
	return ""
}

func (x *WechatNativeQueryByOutTradeNoResponse) GetOutTradeNo() string {
	if x != nil {
		return x.OutTradeNo
	}
	return ""
}

func (x *WechatNativeQueryByOutTradeNoResponse) GetTransactionId() string {
	if x != nil {
		return x.TransactionId
	}
	return ""
}

func (x *WechatNativeQueryByOutTradeNoResponse) GetTradeType() string {
	if x != nil {
		return x.TradeType
	}
	return ""
}

func (x *WechatNativeQueryByOutTradeNoResponse) GetTradeState() string {
	if x != nil {
		return x.TradeState
	}
	return ""
}

func (x *WechatNativeQueryByOutTradeNoResponse) GetBankType() string {
	if x != nil {
		return x.BankType
	}
	return ""
}

func (x *WechatNativeQueryByOutTradeNoResponse) GetSuccessTime() string {
	if x != nil {
		return x.SuccessTime
	}
	return ""
}

func (x *WechatNativeQueryByOutTradeNoResponse) GetOpenID() string {
	if x != nil {
		return x.OpenID
	}
	return ""
}

func (x *WechatNativeQueryByOutTradeNoResponse) GetTotal() int32 {
	if x != nil {
		return x.Total
	}
	return 0
}

type WechatAppPayRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Description string `protobuf:"bytes,1,opt,name=Description,json=description,proto3" json:"Description"`
	OutTradeNo  string `protobuf:"bytes,2,opt,name=OutTradeNo,json=outTradeNo,proto3" json:"OutTradeNo"`
	Cent        int64  `protobuf:"varint,3,opt,name=Cent,json=cent,proto3" json:"Cent"`
	OpenID      string `protobuf:"bytes,4,opt,name=OpenID,json=openID,proto3" json:"OpenID"`
	NotifyUrl   string `protobuf:"bytes,5,opt,name=NotifyUrl,json=notifyUrl,proto3" json:"NotifyUrl"`
	AppID       string `protobuf:"bytes,6,opt,name=AppID,json=appID,proto3" json:"AppID"`
	ClientIP    string `protobuf:"bytes,7,opt,name=ClientIP,proto3" json:"ClientIP"`
	RecordId    uint32 `protobuf:"varint,9,opt,name=recordId,proto3" json:"recordId"`
	Domain      string `protobuf:"bytes,10,opt,name=domain,proto3" json:"domain"`
	Platform    string `protobuf:"bytes,11,opt,name=platform,proto3" json:"platform"`
	Scene       string `protobuf:"bytes,12,opt,name=scene,proto3" json:"scene"`
	TimeExpire  int32  `protobuf:"varint,13,opt,name=timeExpire,json=time_expire,proto3" json:"timeExpire"`
}

func (x *WechatAppPayRequest) Reset() {
	*x = WechatAppPayRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_payment_proto_msgTypes[53]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *WechatAppPayRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WechatAppPayRequest) ProtoMessage() {}

func (x *WechatAppPayRequest) ProtoReflect() protoreflect.Message {
	mi := &file_pb_payment_proto_msgTypes[53]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WechatAppPayRequest.ProtoReflect.Descriptor instead.
func (*WechatAppPayRequest) Descriptor() ([]byte, []int) {
	return file_pb_payment_proto_rawDescGZIP(), []int{53}
}

func (x *WechatAppPayRequest) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *WechatAppPayRequest) GetOutTradeNo() string {
	if x != nil {
		return x.OutTradeNo
	}
	return ""
}

func (x *WechatAppPayRequest) GetCent() int64 {
	if x != nil {
		return x.Cent
	}
	return 0
}

func (x *WechatAppPayRequest) GetOpenID() string {
	if x != nil {
		return x.OpenID
	}
	return ""
}

func (x *WechatAppPayRequest) GetNotifyUrl() string {
	if x != nil {
		return x.NotifyUrl
	}
	return ""
}

func (x *WechatAppPayRequest) GetAppID() string {
	if x != nil {
		return x.AppID
	}
	return ""
}

func (x *WechatAppPayRequest) GetClientIP() string {
	if x != nil {
		return x.ClientIP
	}
	return ""
}

func (x *WechatAppPayRequest) GetRecordId() uint32 {
	if x != nil {
		return x.RecordId
	}
	return 0
}

func (x *WechatAppPayRequest) GetDomain() string {
	if x != nil {
		return x.Domain
	}
	return ""
}

func (x *WechatAppPayRequest) GetPlatform() string {
	if x != nil {
		return x.Platform
	}
	return ""
}

func (x *WechatAppPayRequest) GetScene() string {
	if x != nil {
		return x.Scene
	}
	return ""
}

func (x *WechatAppPayRequest) GetTimeExpire() int32 {
	if x != nil {
		return x.TimeExpire
	}
	return 0
}

type WechatJsApiRefundsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Reason      string `protobuf:"bytes,1,opt,name=Reason,proto3" json:"Reason"`
	OutTradeNo  string `protobuf:"bytes,2,opt,name=OutTradeNo,proto3" json:"OutTradeNo"`
	OutRefundNo string `protobuf:"bytes,3,opt,name=OutRefundNo,proto3" json:"OutRefundNo"`
	NotifyUrl   string `protobuf:"bytes,4,opt,name=NotifyUrl,proto3" json:"NotifyUrl"`
	Cent        int64  `protobuf:"varint,5,opt,name=Cent,proto3" json:"Cent"`
	OpenID      string `protobuf:"bytes,6,opt,name=OpenID,proto3" json:"OpenID"`
	Total       int64  `protobuf:"varint,7,opt,name=Total,proto3" json:"Total"`
}

func (x *WechatJsApiRefundsRequest) Reset() {
	*x = WechatJsApiRefundsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_payment_proto_msgTypes[54]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *WechatJsApiRefundsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WechatJsApiRefundsRequest) ProtoMessage() {}

func (x *WechatJsApiRefundsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_pb_payment_proto_msgTypes[54]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WechatJsApiRefundsRequest.ProtoReflect.Descriptor instead.
func (*WechatJsApiRefundsRequest) Descriptor() ([]byte, []int) {
	return file_pb_payment_proto_rawDescGZIP(), []int{54}
}

func (x *WechatJsApiRefundsRequest) GetReason() string {
	if x != nil {
		return x.Reason
	}
	return ""
}

func (x *WechatJsApiRefundsRequest) GetOutTradeNo() string {
	if x != nil {
		return x.OutTradeNo
	}
	return ""
}

func (x *WechatJsApiRefundsRequest) GetOutRefundNo() string {
	if x != nil {
		return x.OutRefundNo
	}
	return ""
}

func (x *WechatJsApiRefundsRequest) GetNotifyUrl() string {
	if x != nil {
		return x.NotifyUrl
	}
	return ""
}

func (x *WechatJsApiRefundsRequest) GetCent() int64 {
	if x != nil {
		return x.Cent
	}
	return 0
}

func (x *WechatJsApiRefundsRequest) GetOpenID() string {
	if x != nil {
		return x.OpenID
	}
	return ""
}

func (x *WechatJsApiRefundsRequest) GetTotal() int64 {
	if x != nil {
		return x.Total
	}
	return 0
}

type WechatH5PayRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Description string `protobuf:"bytes,1,opt,name=Description,json=description,proto3" json:"Description"`
	OutTradeNo  string `protobuf:"bytes,2,opt,name=OutTradeNo,json=outTradeNo,proto3" json:"OutTradeNo"`
	Cent        int64  `protobuf:"varint,3,opt,name=Cent,json=cent,proto3" json:"Cent"`
	OpenID      string `protobuf:"bytes,4,opt,name=OpenID,json=openID,proto3" json:"OpenID"`
	NotifyUrl   string `protobuf:"bytes,5,opt,name=NotifyUrl,json=notifyUrl,proto3" json:"NotifyUrl"`
	AppID       string `protobuf:"bytes,6,opt,name=AppID,json=appID,proto3" json:"AppID"`
	ClientIP    string `protobuf:"bytes,7,opt,name=ClientIP,proto3" json:"ClientIP"`
	RecordId    uint32 `protobuf:"varint,9,opt,name=recordId,proto3" json:"recordId"`
	Domain      string `protobuf:"bytes,10,opt,name=domain,proto3" json:"domain"`
	Platform    string `protobuf:"bytes,11,opt,name=platform,proto3" json:"platform"`
	Scene       string `protobuf:"bytes,12,opt,name=scene,proto3" json:"scene"`
	TimeExpire  int32  `protobuf:"varint,13,opt,name=timeExpire,json=time_expire,proto3" json:"timeExpire"`
}

func (x *WechatH5PayRequest) Reset() {
	*x = WechatH5PayRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_payment_proto_msgTypes[55]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *WechatH5PayRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WechatH5PayRequest) ProtoMessage() {}

func (x *WechatH5PayRequest) ProtoReflect() protoreflect.Message {
	mi := &file_pb_payment_proto_msgTypes[55]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WechatH5PayRequest.ProtoReflect.Descriptor instead.
func (*WechatH5PayRequest) Descriptor() ([]byte, []int) {
	return file_pb_payment_proto_rawDescGZIP(), []int{55}
}

func (x *WechatH5PayRequest) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *WechatH5PayRequest) GetOutTradeNo() string {
	if x != nil {
		return x.OutTradeNo
	}
	return ""
}

func (x *WechatH5PayRequest) GetCent() int64 {
	if x != nil {
		return x.Cent
	}
	return 0
}

func (x *WechatH5PayRequest) GetOpenID() string {
	if x != nil {
		return x.OpenID
	}
	return ""
}

func (x *WechatH5PayRequest) GetNotifyUrl() string {
	if x != nil {
		return x.NotifyUrl
	}
	return ""
}

func (x *WechatH5PayRequest) GetAppID() string {
	if x != nil {
		return x.AppID
	}
	return ""
}

func (x *WechatH5PayRequest) GetClientIP() string {
	if x != nil {
		return x.ClientIP
	}
	return ""
}

func (x *WechatH5PayRequest) GetRecordId() uint32 {
	if x != nil {
		return x.RecordId
	}
	return 0
}

func (x *WechatH5PayRequest) GetDomain() string {
	if x != nil {
		return x.Domain
	}
	return ""
}

func (x *WechatH5PayRequest) GetPlatform() string {
	if x != nil {
		return x.Platform
	}
	return ""
}

func (x *WechatH5PayRequest) GetScene() string {
	if x != nil {
		return x.Scene
	}
	return ""
}

func (x *WechatH5PayRequest) GetTimeExpire() int32 {
	if x != nil {
		return x.TimeExpire
	}
	return 0
}

type WechatH5PayResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	H5Url string `protobuf:"bytes,1,opt,name=h5Url,json=h5_url,proto3" json:"h5Url"`
}

func (x *WechatH5PayResponse) Reset() {
	*x = WechatH5PayResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_payment_proto_msgTypes[56]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *WechatH5PayResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WechatH5PayResponse) ProtoMessage() {}

func (x *WechatH5PayResponse) ProtoReflect() protoreflect.Message {
	mi := &file_pb_payment_proto_msgTypes[56]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WechatH5PayResponse.ProtoReflect.Descriptor instead.
func (*WechatH5PayResponse) Descriptor() ([]byte, []int) {
	return file_pb_payment_proto_rawDescGZIP(), []int{56}
}

func (x *WechatH5PayResponse) GetH5Url() string {
	if x != nil {
		return x.H5Url
	}
	return ""
}

type WechatH5QueryByOutTradeNoRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	OutTradeNo string `protobuf:"bytes,1,opt,name=outTradeNo,proto3" json:"outTradeNo"`
}

func (x *WechatH5QueryByOutTradeNoRequest) Reset() {
	*x = WechatH5QueryByOutTradeNoRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_payment_proto_msgTypes[57]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *WechatH5QueryByOutTradeNoRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WechatH5QueryByOutTradeNoRequest) ProtoMessage() {}

func (x *WechatH5QueryByOutTradeNoRequest) ProtoReflect() protoreflect.Message {
	mi := &file_pb_payment_proto_msgTypes[57]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WechatH5QueryByOutTradeNoRequest.ProtoReflect.Descriptor instead.
func (*WechatH5QueryByOutTradeNoRequest) Descriptor() ([]byte, []int) {
	return file_pb_payment_proto_rawDescGZIP(), []int{57}
}

func (x *WechatH5QueryByOutTradeNoRequest) GetOutTradeNo() string {
	if x != nil {
		return x.OutTradeNo
	}
	return ""
}

type WechatH5QueryByOutTradeNoResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AppID          string `protobuf:"bytes,1,opt,name=appID,proto3" json:"appID"`
	MchID          string `protobuf:"bytes,2,opt,name=mchID,json=mch_id,proto3" json:"mchID"`
	OutTradeNo     string `protobuf:"bytes,3,opt,name=outTradeNo,json=out_trade_no,proto3" json:"outTradeNo"`
	TransactionId  string `protobuf:"bytes,4,opt,name=transactionId,json=transaction_id,proto3" json:"transactionId"`
	TradeType      string `protobuf:"bytes,5,opt,name=tradeType,json=trade_type,proto3" json:"tradeType"`
	TradeState     string `protobuf:"bytes,6,opt,name=tradeState,json=trade_state,proto3" json:"tradeState"`
	TradeStateDesc string `protobuf:"bytes,7,opt,name=tradeStateDesc,json=trade_state_desc,proto3" json:"tradeStateDesc"`
	BankType       string `protobuf:"bytes,8,opt,name=bankType,json=bank_ype,proto3" json:"bankType"`
	SuccessTime    string `protobuf:"bytes,9,opt,name=successTime,json=success_time,proto3" json:"successTime"`
	OpenID         string `protobuf:"bytes,10,opt,name=openID,json=open_id,proto3" json:"openID"`
	Total          int32  `protobuf:"varint,11,opt,name=total,proto3" json:"total"`
}

func (x *WechatH5QueryByOutTradeNoResponse) Reset() {
	*x = WechatH5QueryByOutTradeNoResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_payment_proto_msgTypes[58]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *WechatH5QueryByOutTradeNoResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WechatH5QueryByOutTradeNoResponse) ProtoMessage() {}

func (x *WechatH5QueryByOutTradeNoResponse) ProtoReflect() protoreflect.Message {
	mi := &file_pb_payment_proto_msgTypes[58]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WechatH5QueryByOutTradeNoResponse.ProtoReflect.Descriptor instead.
func (*WechatH5QueryByOutTradeNoResponse) Descriptor() ([]byte, []int) {
	return file_pb_payment_proto_rawDescGZIP(), []int{58}
}

func (x *WechatH5QueryByOutTradeNoResponse) GetAppID() string {
	if x != nil {
		return x.AppID
	}
	return ""
}

func (x *WechatH5QueryByOutTradeNoResponse) GetMchID() string {
	if x != nil {
		return x.MchID
	}
	return ""
}

func (x *WechatH5QueryByOutTradeNoResponse) GetOutTradeNo() string {
	if x != nil {
		return x.OutTradeNo
	}
	return ""
}

func (x *WechatH5QueryByOutTradeNoResponse) GetTransactionId() string {
	if x != nil {
		return x.TransactionId
	}
	return ""
}

func (x *WechatH5QueryByOutTradeNoResponse) GetTradeType() string {
	if x != nil {
		return x.TradeType
	}
	return ""
}

func (x *WechatH5QueryByOutTradeNoResponse) GetTradeState() string {
	if x != nil {
		return x.TradeState
	}
	return ""
}

func (x *WechatH5QueryByOutTradeNoResponse) GetTradeStateDesc() string {
	if x != nil {
		return x.TradeStateDesc
	}
	return ""
}

func (x *WechatH5QueryByOutTradeNoResponse) GetBankType() string {
	if x != nil {
		return x.BankType
	}
	return ""
}

func (x *WechatH5QueryByOutTradeNoResponse) GetSuccessTime() string {
	if x != nil {
		return x.SuccessTime
	}
	return ""
}

func (x *WechatH5QueryByOutTradeNoResponse) GetOpenID() string {
	if x != nil {
		return x.OpenID
	}
	return ""
}

func (x *WechatH5QueryByOutTradeNoResponse) GetTotal() int32 {
	if x != nil {
		return x.Total
	}
	return 0
}

type WechatPayOkRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	OutTradeNo string `protobuf:"bytes,1,opt,name=outTradeNo,proto3" json:"outTradeNo"`
	Body       string `protobuf:"bytes,2,opt,name=body,proto3" json:"body"`
}

func (x *WechatPayOkRequest) Reset() {
	*x = WechatPayOkRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_payment_proto_msgTypes[59]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *WechatPayOkRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WechatPayOkRequest) ProtoMessage() {}

func (x *WechatPayOkRequest) ProtoReflect() protoreflect.Message {
	mi := &file_pb_payment_proto_msgTypes[59]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WechatPayOkRequest.ProtoReflect.Descriptor instead.
func (*WechatPayOkRequest) Descriptor() ([]byte, []int) {
	return file_pb_payment_proto_rawDescGZIP(), []int{59}
}

func (x *WechatPayOkRequest) GetOutTradeNo() string {
	if x != nil {
		return x.OutTradeNo
	}
	return ""
}

func (x *WechatPayOkRequest) GetBody() string {
	if x != nil {
		return x.Body
	}
	return ""
}

type CommonResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Success bool   `protobuf:"varint,1,opt,name=Success,json=success,proto3" json:"Success"`
	ID      uint32 `protobuf:"varint,2,opt,name=ID,proto3" json:"ID"`
}

func (x *CommonResponse) Reset() {
	*x = CommonResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_payment_proto_msgTypes[60]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CommonResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CommonResponse) ProtoMessage() {}

func (x *CommonResponse) ProtoReflect() protoreflect.Message {
	mi := &file_pb_payment_proto_msgTypes[60]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CommonResponse.ProtoReflect.Descriptor instead.
func (*CommonResponse) Descriptor() ([]byte, []int) {
	return file_pb_payment_proto_rawDescGZIP(), []int{60}
}

func (x *CommonResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

func (x *CommonResponse) GetID() uint32 {
	if x != nil {
		return x.ID
	}
	return 0
}

type PayQueryRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PayType    string `protobuf:"bytes,1,opt,name=payType,proto3" json:"payType"`
	OutTradeNo string `protobuf:"bytes,2,opt,name=outTradeNo,proto3" json:"outTradeNo"`
}

func (x *PayQueryRequest) Reset() {
	*x = PayQueryRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_payment_proto_msgTypes[61]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PayQueryRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PayQueryRequest) ProtoMessage() {}

func (x *PayQueryRequest) ProtoReflect() protoreflect.Message {
	mi := &file_pb_payment_proto_msgTypes[61]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PayQueryRequest.ProtoReflect.Descriptor instead.
func (*PayQueryRequest) Descriptor() ([]byte, []int) {
	return file_pb_payment_proto_rawDescGZIP(), []int{61}
}

func (x *PayQueryRequest) GetPayType() string {
	if x != nil {
		return x.PayType
	}
	return ""
}

func (x *PayQueryRequest) GetOutTradeNo() string {
	if x != nil {
		return x.OutTradeNo
	}
	return ""
}

type PayQueryResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Infos []*PaymentOrderInfo `protobuf:"bytes,1,rep,name=infos,proto3" json:"infos"`
}

func (x *PayQueryResponse) Reset() {
	*x = PayQueryResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_payment_proto_msgTypes[62]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PayQueryResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PayQueryResponse) ProtoMessage() {}

func (x *PayQueryResponse) ProtoReflect() protoreflect.Message {
	mi := &file_pb_payment_proto_msgTypes[62]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PayQueryResponse.ProtoReflect.Descriptor instead.
func (*PayQueryResponse) Descriptor() ([]byte, []int) {
	return file_pb_payment_proto_rawDescGZIP(), []int{62}
}

func (x *PayQueryResponse) GetInfos() []*PaymentOrderInfo {
	if x != nil {
		return x.Infos
	}
	return nil
}

type PaymentOrderInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id                 int64  `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
	CreatedAt          string `protobuf:"bytes,2,opt,name=createdAt,proto3" json:"createdAt"`
	UpdatedAt          string `protobuf:"bytes,3,opt,name=updatedAt,proto3" json:"updatedAt"`
	OutTradeNo         string `protobuf:"bytes,4,opt,name=outTradeNo,proto3" json:"outTradeNo"`         // 外部流水号
	ChannelTradeNo     string `protobuf:"bytes,5,opt,name=channelTradeNo,proto3" json:"channelTradeNo"` // 渠道流水号
	Amount             int64  `protobuf:"varint,6,opt,name=amount,proto3" json:"amount"`                // 用户支付总金额
	Currency           string `protobuf:"bytes,7,opt,name=currency,proto3" json:"currency"`             // 币种
	Status             string `protobuf:"bytes,8,opt,name=status,proto3" json:"status"`
	PayTime            string `protobuf:"bytes,9,opt,name=payTime,proto3" json:"payTime"`
	RefundTime         string `protobuf:"bytes,10,opt,name=refundTime,proto3" json:"refundTime"`
	Payee              string `protobuf:"bytes,11,opt,name=payee,proto3" json:"payee"`
	ChannelType        string `protobuf:"bytes,12,opt,name=channelType,proto3" json:"channelType"`   // 支付渠道 alipay-支付宝 wxpay-微信 stripe-Stripe支付
	Platform           string `protobuf:"bytes,13,opt,name=platform,proto3" json:"platform"`         // 支付具体来源，是app还是h5还是jsapi还是wap
	Domain             string `protobuf:"bytes,14,opt,name=domain,proto3" json:"domain"`             // 使用平台
	BusinessType       string `protobuf:"bytes,15,opt,name=businessType,proto3" json:"businessType"` // 业务类型，用来确认mq发送
	LogId              int64  `protobuf:"varint,16,opt,name=logId,proto3" json:"logId"`
	CheckSessionId     string `protobuf:"bytes,17,opt,name=checkSessionId,proto3" json:"checkSessionId"`
	ProductUUID        string `protobuf:"bytes,18,opt,name=productUUID,proto3" json:"productUUID"`
	ProductName        string `protobuf:"bytes,19,opt,name=productName,proto3" json:"productName"`
	ProductImg         string `protobuf:"bytes,20,opt,name=productImg,proto3" json:"productImg"`
	ProductDescription string `protobuf:"bytes,21,opt,name=productDescription,proto3" json:"productDescription"`
	Fee                int64  `protobuf:"varint,22,opt,name=fee,proto3" json:"fee"`             // 手续费
	NetIncome          int64  `protobuf:"varint,23,opt,name=netIncome,proto3" json:"netIncome"` // 净收入【允许退款的最大金额】
}

func (x *PaymentOrderInfo) Reset() {
	*x = PaymentOrderInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_payment_proto_msgTypes[63]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PaymentOrderInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PaymentOrderInfo) ProtoMessage() {}

func (x *PaymentOrderInfo) ProtoReflect() protoreflect.Message {
	mi := &file_pb_payment_proto_msgTypes[63]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PaymentOrderInfo.ProtoReflect.Descriptor instead.
func (*PaymentOrderInfo) Descriptor() ([]byte, []int) {
	return file_pb_payment_proto_rawDescGZIP(), []int{63}
}

func (x *PaymentOrderInfo) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *PaymentOrderInfo) GetCreatedAt() string {
	if x != nil {
		return x.CreatedAt
	}
	return ""
}

func (x *PaymentOrderInfo) GetUpdatedAt() string {
	if x != nil {
		return x.UpdatedAt
	}
	return ""
}

func (x *PaymentOrderInfo) GetOutTradeNo() string {
	if x != nil {
		return x.OutTradeNo
	}
	return ""
}

func (x *PaymentOrderInfo) GetChannelTradeNo() string {
	if x != nil {
		return x.ChannelTradeNo
	}
	return ""
}

func (x *PaymentOrderInfo) GetAmount() int64 {
	if x != nil {
		return x.Amount
	}
	return 0
}

func (x *PaymentOrderInfo) GetCurrency() string {
	if x != nil {
		return x.Currency
	}
	return ""
}

func (x *PaymentOrderInfo) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

func (x *PaymentOrderInfo) GetPayTime() string {
	if x != nil {
		return x.PayTime
	}
	return ""
}

func (x *PaymentOrderInfo) GetRefundTime() string {
	if x != nil {
		return x.RefundTime
	}
	return ""
}

func (x *PaymentOrderInfo) GetPayee() string {
	if x != nil {
		return x.Payee
	}
	return ""
}

func (x *PaymentOrderInfo) GetChannelType() string {
	if x != nil {
		return x.ChannelType
	}
	return ""
}

func (x *PaymentOrderInfo) GetPlatform() string {
	if x != nil {
		return x.Platform
	}
	return ""
}

func (x *PaymentOrderInfo) GetDomain() string {
	if x != nil {
		return x.Domain
	}
	return ""
}

func (x *PaymentOrderInfo) GetBusinessType() string {
	if x != nil {
		return x.BusinessType
	}
	return ""
}

func (x *PaymentOrderInfo) GetLogId() int64 {
	if x != nil {
		return x.LogId
	}
	return 0
}

func (x *PaymentOrderInfo) GetCheckSessionId() string {
	if x != nil {
		return x.CheckSessionId
	}
	return ""
}

func (x *PaymentOrderInfo) GetProductUUID() string {
	if x != nil {
		return x.ProductUUID
	}
	return ""
}

func (x *PaymentOrderInfo) GetProductName() string {
	if x != nil {
		return x.ProductName
	}
	return ""
}

func (x *PaymentOrderInfo) GetProductImg() string {
	if x != nil {
		return x.ProductImg
	}
	return ""
}

func (x *PaymentOrderInfo) GetProductDescription() string {
	if x != nil {
		return x.ProductDescription
	}
	return ""
}

func (x *PaymentOrderInfo) GetFee() int64 {
	if x != nil {
		return x.Fee
	}
	return 0
}

func (x *PaymentOrderInfo) GetNetIncome() int64 {
	if x != nil {
		return x.NetIncome
	}
	return 0
}

type ExportPayRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	StartTime      string `protobuf:"bytes,1,opt,name=startTime,proto3" json:"startTime"` // 开始时间
	EndTime        string `protobuf:"bytes,2,opt,name=endTime,proto3" json:"endTime"`     // 结束时间
	ReportType     string `protobuf:"bytes,3,opt,name=reportType,proto3" json:"reportType"`
	ReportRangeNum string `protobuf:"bytes,4,opt,name=reportRangeNum,proto3" json:"reportRangeNum"` // 年月
	ChannelType    string `protobuf:"bytes,5,opt,name=channelType,proto3" json:"channelType"`       // 交易渠道
	Currency       string `protobuf:"bytes,6,opt,name=currency,proto3" json:"currency"`
	SortBy         string `protobuf:"bytes,7,opt,name=sortBy,proto3" json:"sortBy"` // 时间排序asc,desc
}

func (x *ExportPayRequest) Reset() {
	*x = ExportPayRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_payment_proto_msgTypes[64]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ExportPayRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ExportPayRequest) ProtoMessage() {}

func (x *ExportPayRequest) ProtoReflect() protoreflect.Message {
	mi := &file_pb_payment_proto_msgTypes[64]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ExportPayRequest.ProtoReflect.Descriptor instead.
func (*ExportPayRequest) Descriptor() ([]byte, []int) {
	return file_pb_payment_proto_rawDescGZIP(), []int{64}
}

func (x *ExportPayRequest) GetStartTime() string {
	if x != nil {
		return x.StartTime
	}
	return ""
}

func (x *ExportPayRequest) GetEndTime() string {
	if x != nil {
		return x.EndTime
	}
	return ""
}

func (x *ExportPayRequest) GetReportType() string {
	if x != nil {
		return x.ReportType
	}
	return ""
}

func (x *ExportPayRequest) GetReportRangeNum() string {
	if x != nil {
		return x.ReportRangeNum
	}
	return ""
}

func (x *ExportPayRequest) GetChannelType() string {
	if x != nil {
		return x.ChannelType
	}
	return ""
}

func (x *ExportPayRequest) GetCurrency() string {
	if x != nil {
		return x.Currency
	}
	return ""
}

func (x *ExportPayRequest) GetSortBy() string {
	if x != nil {
		return x.SortBy
	}
	return ""
}

type ExportPayResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ReportType     string           `protobuf:"bytes,1,opt,name=reportType,proto3" json:"reportType"`         // 日报 月报
	OrderNum       int32            `protobuf:"varint,2,opt,name=orderNum,proto3" json:"orderNum"`            // 成功交易的订单数
	Overview       []*Overview      `protobuf:"bytes,3,rep,name=overview,proto3" json:"overview"`             // 总览
	ChannelIncomes []*ChannelIncome `protobuf:"bytes,4,rep,name=channelIncomes,proto3" json:"channelIncomes"` // 渠道交易情况
	BusinessInfos  []*BusinessInfo  `protobuf:"bytes,5,rep,name=businessInfos,proto3" json:"businessInfos"`   // 业务交易情况
	OrderDetails   []*OrderDetail   `protobuf:"bytes,6,rep,name=orderDetails,proto3" json:"orderDetails"`     // sheet2,订单详情
}

func (x *ExportPayResponse) Reset() {
	*x = ExportPayResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_payment_proto_msgTypes[65]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ExportPayResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ExportPayResponse) ProtoMessage() {}

func (x *ExportPayResponse) ProtoReflect() protoreflect.Message {
	mi := &file_pb_payment_proto_msgTypes[65]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ExportPayResponse.ProtoReflect.Descriptor instead.
func (*ExportPayResponse) Descriptor() ([]byte, []int) {
	return file_pb_payment_proto_rawDescGZIP(), []int{65}
}

func (x *ExportPayResponse) GetReportType() string {
	if x != nil {
		return x.ReportType
	}
	return ""
}

func (x *ExportPayResponse) GetOrderNum() int32 {
	if x != nil {
		return x.OrderNum
	}
	return 0
}

func (x *ExportPayResponse) GetOverview() []*Overview {
	if x != nil {
		return x.Overview
	}
	return nil
}

func (x *ExportPayResponse) GetChannelIncomes() []*ChannelIncome {
	if x != nil {
		return x.ChannelIncomes
	}
	return nil
}

func (x *ExportPayResponse) GetBusinessInfos() []*BusinessInfo {
	if x != nil {
		return x.BusinessInfos
	}
	return nil
}

func (x *ExportPayResponse) GetOrderDetails() []*OrderDetail {
	if x != nil {
		return x.OrderDetails
	}
	return nil
}

type OrderDetail struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PaymentOrderId     int64  `protobuf:"varint,1,opt,name=paymentOrderId,proto3" json:"paymentOrderId"`
	CreatedAt          string `protobuf:"bytes,2,opt,name=createdAt,proto3" json:"createdAt"`
	UpdatedAt          string `protobuf:"bytes,3,opt,name=updatedAt,proto3" json:"updatedAt"`
	OutTradeNo         string `protobuf:"bytes,4,opt,name=outTradeNo,proto3" json:"outTradeNo"`         // 外部流水号
	ChannelTradeNo     string `protobuf:"bytes,5,opt,name=channelTradeNo,proto3" json:"channelTradeNo"` // 渠道流水号
	Currency           string `protobuf:"bytes,6,opt,name=currency,proto3" json:"currency"`             // 币种
	PayAmount          int64  `protobuf:"varint,7,opt,name=payAmount,proto3" json:"payAmount"`          // 付款金额
	Fee                int64  `protobuf:"varint,8,opt,name=fee,proto3" json:"fee"`                      // 手续费
	NetIncome          int64  `protobuf:"varint,9,opt,name=netIncome,proto3" json:"netIncome"`          // 净收入
	Status             string `protobuf:"bytes,10,opt,name=status,proto3" json:"status"`
	PayTime            string `protobuf:"bytes,11,opt,name=payTime,proto3" json:"payTime"`
	ChannelType        string `protobuf:"bytes,12,opt,name=channelType,proto3" json:"channelType"`   // 支付渠道 alipay-支付宝 wxpay-微信 stripe-Stripe支付
	Platform           string `protobuf:"bytes,13,opt,name=platform,proto3" json:"platform"`         // 支付具体来源，是app还是h5还是jsapi还是wap
	Domain             string `protobuf:"bytes,14,opt,name=domain,proto3" json:"domain"`             // 使用平台
	BusinessType       string `protobuf:"bytes,15,opt,name=businessType,proto3" json:"businessType"` // 业务类型，用来确认mq发送
	LogId              int64  `protobuf:"varint,16,opt,name=logId,proto3" json:"logId"`
	CheckSessionId     string `protobuf:"bytes,17,opt,name=checkSessionId,proto3" json:"checkSessionId"`
	ProductUUID        string `protobuf:"bytes,18,opt,name=productUUID,proto3" json:"productUUID"`
	ProductName        string `protobuf:"bytes,19,opt,name=productName,proto3" json:"productName"`
	ProductImg         string `protobuf:"bytes,20,opt,name=productImg,proto3" json:"productImg"`
	ProductDescription string `protobuf:"bytes,21,opt,name=productDescription,proto3" json:"productDescription"`
}

func (x *OrderDetail) Reset() {
	*x = OrderDetail{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_payment_proto_msgTypes[66]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *OrderDetail) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OrderDetail) ProtoMessage() {}

func (x *OrderDetail) ProtoReflect() protoreflect.Message {
	mi := &file_pb_payment_proto_msgTypes[66]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OrderDetail.ProtoReflect.Descriptor instead.
func (*OrderDetail) Descriptor() ([]byte, []int) {
	return file_pb_payment_proto_rawDescGZIP(), []int{66}
}

func (x *OrderDetail) GetPaymentOrderId() int64 {
	if x != nil {
		return x.PaymentOrderId
	}
	return 0
}

func (x *OrderDetail) GetCreatedAt() string {
	if x != nil {
		return x.CreatedAt
	}
	return ""
}

func (x *OrderDetail) GetUpdatedAt() string {
	if x != nil {
		return x.UpdatedAt
	}
	return ""
}

func (x *OrderDetail) GetOutTradeNo() string {
	if x != nil {
		return x.OutTradeNo
	}
	return ""
}

func (x *OrderDetail) GetChannelTradeNo() string {
	if x != nil {
		return x.ChannelTradeNo
	}
	return ""
}

func (x *OrderDetail) GetCurrency() string {
	if x != nil {
		return x.Currency
	}
	return ""
}

func (x *OrderDetail) GetPayAmount() int64 {
	if x != nil {
		return x.PayAmount
	}
	return 0
}

func (x *OrderDetail) GetFee() int64 {
	if x != nil {
		return x.Fee
	}
	return 0
}

func (x *OrderDetail) GetNetIncome() int64 {
	if x != nil {
		return x.NetIncome
	}
	return 0
}

func (x *OrderDetail) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

func (x *OrderDetail) GetPayTime() string {
	if x != nil {
		return x.PayTime
	}
	return ""
}

func (x *OrderDetail) GetChannelType() string {
	if x != nil {
		return x.ChannelType
	}
	return ""
}

func (x *OrderDetail) GetPlatform() string {
	if x != nil {
		return x.Platform
	}
	return ""
}

func (x *OrderDetail) GetDomain() string {
	if x != nil {
		return x.Domain
	}
	return ""
}

func (x *OrderDetail) GetBusinessType() string {
	if x != nil {
		return x.BusinessType
	}
	return ""
}

func (x *OrderDetail) GetLogId() int64 {
	if x != nil {
		return x.LogId
	}
	return 0
}

func (x *OrderDetail) GetCheckSessionId() string {
	if x != nil {
		return x.CheckSessionId
	}
	return ""
}

func (x *OrderDetail) GetProductUUID() string {
	if x != nil {
		return x.ProductUUID
	}
	return ""
}

func (x *OrderDetail) GetProductName() string {
	if x != nil {
		return x.ProductName
	}
	return ""
}

func (x *OrderDetail) GetProductImg() string {
	if x != nil {
		return x.ProductImg
	}
	return ""
}

func (x *OrderDetail) GetProductDescription() string {
	if x != nil {
		return x.ProductDescription
	}
	return ""
}

type BusinessInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	BusinessName      string `protobuf:"bytes,1,opt,name=BusinessName,proto3" json:"BusinessName"`            // 业务名称
	Currency          string `protobuf:"bytes,2,opt,name=currency,proto3" json:"currency"`                    // 交易币种
	OrderNum          int32  `protobuf:"varint,3,opt,name=orderNum,proto3" json:"orderNum"`                   // 成功交易的订单数
	TotalIncome       int64  `protobuf:"varint,4,opt,name=totalIncome,proto3" json:"totalIncome"`             // 总收入
	TotalFee          int64  `protobuf:"varint,5,opt,name=totalFee,proto3" json:"totalFee"`                   // 总手续费
	TotalRefundAmount int64  `protobuf:"varint,6,opt,name=totalRefundAmount,proto3" json:"totalRefundAmount"` // 总退款费
	NetIncome         int64  `protobuf:"varint,7,opt,name=netIncome,proto3" json:"netIncome"`                 // 净收入
}

func (x *BusinessInfo) Reset() {
	*x = BusinessInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_payment_proto_msgTypes[67]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BusinessInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BusinessInfo) ProtoMessage() {}

func (x *BusinessInfo) ProtoReflect() protoreflect.Message {
	mi := &file_pb_payment_proto_msgTypes[67]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BusinessInfo.ProtoReflect.Descriptor instead.
func (*BusinessInfo) Descriptor() ([]byte, []int) {
	return file_pb_payment_proto_rawDescGZIP(), []int{67}
}

func (x *BusinessInfo) GetBusinessName() string {
	if x != nil {
		return x.BusinessName
	}
	return ""
}

func (x *BusinessInfo) GetCurrency() string {
	if x != nil {
		return x.Currency
	}
	return ""
}

func (x *BusinessInfo) GetOrderNum() int32 {
	if x != nil {
		return x.OrderNum
	}
	return 0
}

func (x *BusinessInfo) GetTotalIncome() int64 {
	if x != nil {
		return x.TotalIncome
	}
	return 0
}

func (x *BusinessInfo) GetTotalFee() int64 {
	if x != nil {
		return x.TotalFee
	}
	return 0
}

func (x *BusinessInfo) GetTotalRefundAmount() int64 {
	if x != nil {
		return x.TotalRefundAmount
	}
	return 0
}

func (x *BusinessInfo) GetNetIncome() int64 {
	if x != nil {
		return x.NetIncome
	}
	return 0
}

type Overview struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Currency          string `protobuf:"bytes,1,opt,name=currency,proto3" json:"currency"`                    // 交易币种
	OrderNum          int32  `protobuf:"varint,2,opt,name=orderNum,proto3" json:"orderNum"`                   // 成功交易的订单数
	TotalIncome       int64  `protobuf:"varint,3,opt,name=totalIncome,proto3" json:"totalIncome"`             // 总收入
	TotalFee          int64  `protobuf:"varint,4,opt,name=totalFee,proto3" json:"totalFee"`                   // 总手续费
	TotalRefundAmount int64  `protobuf:"varint,5,opt,name=totalRefundAmount,proto3" json:"totalRefundAmount"` // 总退款费
	NetIncome         int64  `protobuf:"varint,6,opt,name=netIncome,proto3" json:"netIncome"`                 // 净收入
}

func (x *Overview) Reset() {
	*x = Overview{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_payment_proto_msgTypes[68]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Overview) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Overview) ProtoMessage() {}

func (x *Overview) ProtoReflect() protoreflect.Message {
	mi := &file_pb_payment_proto_msgTypes[68]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Overview.ProtoReflect.Descriptor instead.
func (*Overview) Descriptor() ([]byte, []int) {
	return file_pb_payment_proto_rawDescGZIP(), []int{68}
}

func (x *Overview) GetCurrency() string {
	if x != nil {
		return x.Currency
	}
	return ""
}

func (x *Overview) GetOrderNum() int32 {
	if x != nil {
		return x.OrderNum
	}
	return 0
}

func (x *Overview) GetTotalIncome() int64 {
	if x != nil {
		return x.TotalIncome
	}
	return 0
}

func (x *Overview) GetTotalFee() int64 {
	if x != nil {
		return x.TotalFee
	}
	return 0
}

func (x *Overview) GetTotalRefundAmount() int64 {
	if x != nil {
		return x.TotalRefundAmount
	}
	return 0
}

func (x *Overview) GetNetIncome() int64 {
	if x != nil {
		return x.NetIncome
	}
	return 0
}

type ChannelIncome struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ChannelType       string `protobuf:"bytes,1,opt,name=channelType,proto3" json:"channelType"`              // 交易渠道
	Currency          string `protobuf:"bytes,2,opt,name=currency,proto3" json:"currency"`                    // 交易币种
	OrderNum          int32  `protobuf:"varint,3,opt,name=orderNum,proto3" json:"orderNum"`                   // 成功交易的订单数
	TotalIncome       int64  `protobuf:"varint,4,opt,name=totalIncome,proto3" json:"totalIncome"`             // 总收入
	TotalFee          int64  `protobuf:"varint,5,opt,name=totalFee,proto3" json:"totalFee"`                   // 总手续费
	TotalRefundAmount int64  `protobuf:"varint,6,opt,name=totalRefundAmount,proto3" json:"totalRefundAmount"` // 总退款费
	NetIncome         int64  `protobuf:"varint,7,opt,name=netIncome,proto3" json:"netIncome"`                 // 净收入
}

func (x *ChannelIncome) Reset() {
	*x = ChannelIncome{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_payment_proto_msgTypes[69]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ChannelIncome) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ChannelIncome) ProtoMessage() {}

func (x *ChannelIncome) ProtoReflect() protoreflect.Message {
	mi := &file_pb_payment_proto_msgTypes[69]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ChannelIncome.ProtoReflect.Descriptor instead.
func (*ChannelIncome) Descriptor() ([]byte, []int) {
	return file_pb_payment_proto_rawDescGZIP(), []int{69}
}

func (x *ChannelIncome) GetChannelType() string {
	if x != nil {
		return x.ChannelType
	}
	return ""
}

func (x *ChannelIncome) GetCurrency() string {
	if x != nil {
		return x.Currency
	}
	return ""
}

func (x *ChannelIncome) GetOrderNum() int32 {
	if x != nil {
		return x.OrderNum
	}
	return 0
}

func (x *ChannelIncome) GetTotalIncome() int64 {
	if x != nil {
		return x.TotalIncome
	}
	return 0
}

func (x *ChannelIncome) GetTotalFee() int64 {
	if x != nil {
		return x.TotalFee
	}
	return 0
}

func (x *ChannelIncome) GetTotalRefundAmount() int64 {
	if x != nil {
		return x.TotalRefundAmount
	}
	return 0
}

func (x *ChannelIncome) GetNetIncome() int64 {
	if x != nil {
		return x.NetIncome
	}
	return 0
}

type WechatAppQueryByOutTradeNoResponse_Payer struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Openid string `protobuf:"bytes,1,opt,name=openid,proto3" json:"openid"`
}

func (x *WechatAppQueryByOutTradeNoResponse_Payer) Reset() {
	*x = WechatAppQueryByOutTradeNoResponse_Payer{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_payment_proto_msgTypes[71]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *WechatAppQueryByOutTradeNoResponse_Payer) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WechatAppQueryByOutTradeNoResponse_Payer) ProtoMessage() {}

func (x *WechatAppQueryByOutTradeNoResponse_Payer) ProtoReflect() protoreflect.Message {
	mi := &file_pb_payment_proto_msgTypes[71]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WechatAppQueryByOutTradeNoResponse_Payer.ProtoReflect.Descriptor instead.
func (*WechatAppQueryByOutTradeNoResponse_Payer) Descriptor() ([]byte, []int) {
	return file_pb_payment_proto_rawDescGZIP(), []int{43, 0}
}

func (x *WechatAppQueryByOutTradeNoResponse_Payer) GetOpenid() string {
	if x != nil {
		return x.Openid
	}
	return ""
}

type WechatAppQueryByOutTradeNoResponse_Amount struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Total         int64  `protobuf:"varint,1,opt,name=total,proto3" json:"total"`                                    // 总金额
	PayerTotal    int64  `protobuf:"varint,2,opt,name=payerTotal,json=payer_total,proto3" json:"payerTotal"`         // 付款人支付的金额
	Currency      string `protobuf:"bytes,3,opt,name=currency,proto3" json:"currency"`                               // 货币类型
	PayerCurrency string `protobuf:"bytes,4,opt,name=payerCurrency,json=payer_currency,proto3" json:"payerCurrency"` // 付款人货币类型
}

func (x *WechatAppQueryByOutTradeNoResponse_Amount) Reset() {
	*x = WechatAppQueryByOutTradeNoResponse_Amount{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_payment_proto_msgTypes[72]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *WechatAppQueryByOutTradeNoResponse_Amount) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WechatAppQueryByOutTradeNoResponse_Amount) ProtoMessage() {}

func (x *WechatAppQueryByOutTradeNoResponse_Amount) ProtoReflect() protoreflect.Message {
	mi := &file_pb_payment_proto_msgTypes[72]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WechatAppQueryByOutTradeNoResponse_Amount.ProtoReflect.Descriptor instead.
func (*WechatAppQueryByOutTradeNoResponse_Amount) Descriptor() ([]byte, []int) {
	return file_pb_payment_proto_rawDescGZIP(), []int{43, 1}
}

func (x *WechatAppQueryByOutTradeNoResponse_Amount) GetTotal() int64 {
	if x != nil {
		return x.Total
	}
	return 0
}

func (x *WechatAppQueryByOutTradeNoResponse_Amount) GetPayerTotal() int64 {
	if x != nil {
		return x.PayerTotal
	}
	return 0
}

func (x *WechatAppQueryByOutTradeNoResponse_Amount) GetCurrency() string {
	if x != nil {
		return x.Currency
	}
	return ""
}

func (x *WechatAppQueryByOutTradeNoResponse_Amount) GetPayerCurrency() string {
	if x != nil {
		return x.PayerCurrency
	}
	return ""
}

type WechatAppQueryByOutTradeNoResponse_PromotionDetail struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CouponId            string `protobuf:"bytes,1,opt,name=couponId,json=coupon_id,proto3" json:"couponId"`                                   // 优惠券ID
	Name                string `protobuf:"bytes,2,opt,name=name,proto3" json:"name"`                                                          // 名称
	Scope               string `protobuf:"bytes,3,opt,name=scope,proto3" json:"scope"`                                                        // 范围
	Type                string `protobuf:"bytes,4,opt,name=type,proto3" json:"type"`                                                          // 类型
	Amount              int64  `protobuf:"varint,5,opt,name=amount,proto3" json:"amount"`                                                     // 金额
	StockId             string `protobuf:"bytes,6,opt,name=stockId,json=stock_id,proto3" json:"stockId"`                                      // 库存ID
	WechatpayContribute int64  `protobuf:"varint,7,opt,name=wechatpayContribute,json=wechatpay_contribute,proto3" json:"wechatpayContribute"` // 微信支付贡献的金额
	MerchantContribute  int64  `protobuf:"varint,8,opt,name=merchantContribute,json=merchant_contribute,proto3" json:"merchantContribute"`    // 商户贡献的金额
	OtherContribute     int64  `protobuf:"varint,9,opt,name=otherContribute,json=other_contribute,proto3" json:"otherContribute"`             // 其他贡献的金额
	Currency            string `protobuf:"bytes,10,opt,name=currency,proto3" json:"currency"`                                                 // 货币类型
}

func (x *WechatAppQueryByOutTradeNoResponse_PromotionDetail) Reset() {
	*x = WechatAppQueryByOutTradeNoResponse_PromotionDetail{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_payment_proto_msgTypes[73]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *WechatAppQueryByOutTradeNoResponse_PromotionDetail) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WechatAppQueryByOutTradeNoResponse_PromotionDetail) ProtoMessage() {}

func (x *WechatAppQueryByOutTradeNoResponse_PromotionDetail) ProtoReflect() protoreflect.Message {
	mi := &file_pb_payment_proto_msgTypes[73]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WechatAppQueryByOutTradeNoResponse_PromotionDetail.ProtoReflect.Descriptor instead.
func (*WechatAppQueryByOutTradeNoResponse_PromotionDetail) Descriptor() ([]byte, []int) {
	return file_pb_payment_proto_rawDescGZIP(), []int{43, 2}
}

func (x *WechatAppQueryByOutTradeNoResponse_PromotionDetail) GetCouponId() string {
	if x != nil {
		return x.CouponId
	}
	return ""
}

func (x *WechatAppQueryByOutTradeNoResponse_PromotionDetail) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *WechatAppQueryByOutTradeNoResponse_PromotionDetail) GetScope() string {
	if x != nil {
		return x.Scope
	}
	return ""
}

func (x *WechatAppQueryByOutTradeNoResponse_PromotionDetail) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

func (x *WechatAppQueryByOutTradeNoResponse_PromotionDetail) GetAmount() int64 {
	if x != nil {
		return x.Amount
	}
	return 0
}

func (x *WechatAppQueryByOutTradeNoResponse_PromotionDetail) GetStockId() string {
	if x != nil {
		return x.StockId
	}
	return ""
}

func (x *WechatAppQueryByOutTradeNoResponse_PromotionDetail) GetWechatpayContribute() int64 {
	if x != nil {
		return x.WechatpayContribute
	}
	return 0
}

func (x *WechatAppQueryByOutTradeNoResponse_PromotionDetail) GetMerchantContribute() int64 {
	if x != nil {
		return x.MerchantContribute
	}
	return 0
}

func (x *WechatAppQueryByOutTradeNoResponse_PromotionDetail) GetOtherContribute() int64 {
	if x != nil {
		return x.OtherContribute
	}
	return 0
}

func (x *WechatAppQueryByOutTradeNoResponse_PromotionDetail) GetCurrency() string {
	if x != nil {
		return x.Currency
	}
	return ""
}

var File_pb_payment_proto protoreflect.FileDescriptor

var file_pb_payment_proto_rawDesc = []byte{
	0x0a, 0x10, 0x70, 0x62, 0x2f, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x12, 0x07, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x22, 0x46, 0x0a, 0x14, 0x41,
	0x6e, 0x74, 0x6f, 0x6d, 0x50, 0x61, 0x79, 0x51, 0x75, 0x65, 0x72, 0x79, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x12, 0x2e, 0x0a, 0x12, 0x63, 0x68, 0x65, 0x63, 0x6b, 0x6f, 0x75, 0x74, 0x53,
	0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x09, 0x52,
	0x12, 0x63, 0x68, 0x65, 0x63, 0x6b, 0x6f, 0x75, 0x74, 0x53, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e,
	0x49, 0x64, 0x73, 0x22, 0x48, 0x0a, 0x15, 0x41, 0x6e, 0x74, 0x6f, 0x6d, 0x50, 0x61, 0x79, 0x51,
	0x75, 0x65, 0x72, 0x79, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x2f, 0x0a, 0x05,
	0x69, 0x6e, 0x66, 0x6f, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x70, 0x61,
	0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x4f, 0x72, 0x64,
	0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x05, 0x69, 0x6e, 0x66, 0x6f, 0x73, 0x22, 0x81, 0x02,
	0x0a, 0x15, 0x41, 0x6e, 0x74, 0x6f, 0x6d, 0x4e, 0x6f, 0x74, 0x69, 0x66, 0x79, 0x50, 0x61, 0x79,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1e, 0x0a, 0x0a, 0x6e, 0x6f, 0x74, 0x69, 0x66,
	0x79, 0x54, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x6e, 0x6f, 0x74,
	0x69, 0x66, 0x79, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1c, 0x0a, 0x09, 0x72, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x49, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x72, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x49, 0x64, 0x12, 0x1c, 0x0a, 0x09, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74,
	0x49, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e,
	0x74, 0x49, 0x64, 0x12, 0x20, 0x0a, 0x0b, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x54, 0x69,
	0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e,
	0x74, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x22, 0x0a, 0x0c, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x53,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x72, 0x65, 0x73,
	0x75, 0x6c, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x24, 0x0a, 0x0d, 0x72, 0x65, 0x73,
	0x75, 0x6c, 0x74, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0d, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12,
	0x20, 0x0a, 0x0b, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x43, 0x6f, 0x64, 0x65, 0x18, 0x07,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x43, 0x6f, 0x64,
	0x65, 0x22, 0x50, 0x0a, 0x16, 0x41, 0x6e, 0x74, 0x6f, 0x6d, 0x4e, 0x6f, 0x74, 0x69, 0x66, 0x79,
	0x50, 0x61, 0x79, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x73,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x73, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x12, 0x1e, 0x0a, 0x0a, 0x6f, 0x75, 0x74, 0x54, 0x72, 0x61, 0x64, 0x65, 0x4e,
	0x6f, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x6f, 0x75, 0x74, 0x54, 0x72, 0x61, 0x64,
	0x65, 0x4e, 0x6f, 0x22, 0xec, 0x05, 0x0a, 0x10, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x50, 0x61,
	0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x18, 0x0a, 0x07, 0x70, 0x6f, 0x73, 0x74,
	0x55, 0x72, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x70, 0x6f, 0x73, 0x74, 0x55,
	0x72, 0x6c, 0x12, 0x18, 0x0a, 0x07, 0x73, 0x75, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x07, 0x73, 0x75, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x12, 0x20, 0x0a, 0x0b,
	0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x55, 0x55, 0x49, 0x44, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0b, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x55, 0x55, 0x49, 0x44, 0x12, 0x20,
	0x0a, 0x0b, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0b, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x4e, 0x61, 0x6d, 0x65,
	0x12, 0x1e, 0x0a, 0x0a, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x49, 0x6d, 0x67, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x49, 0x6d, 0x67,
	0x12, 0x2e, 0x0a, 0x12, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x44, 0x65, 0x73, 0x63, 0x72,
	0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x12, 0x70, 0x72,
	0x6f, 0x64, 0x75, 0x63, 0x74, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e,
	0x12, 0x18, 0x0a, 0x07, 0x71, 0x75, 0x69, 0x74, 0x55, 0x72, 0x6c, 0x18, 0x07, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x07, 0x71, 0x75, 0x69, 0x74, 0x55, 0x72, 0x6c, 0x12, 0x1c, 0x0a, 0x09, 0x6e, 0x6f,
	0x74, 0x69, 0x66, 0x79, 0x55, 0x72, 0x6c, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x6e,
	0x6f, 0x74, 0x69, 0x66, 0x79, 0x55, 0x72, 0x6c, 0x12, 0x1c, 0x0a, 0x09, 0x72, 0x65, 0x74, 0x75,
	0x72, 0x6e, 0x55, 0x72, 0x6c, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x72, 0x65, 0x74,
	0x75, 0x72, 0x6e, 0x55, 0x72, 0x6c, 0x12, 0x1e, 0x0a, 0x0a, 0x6f, 0x75, 0x74, 0x54, 0x72, 0x61,
	0x64, 0x65, 0x4e, 0x6f, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x6f, 0x75, 0x74, 0x54,
	0x72, 0x61, 0x64, 0x65, 0x4e, 0x6f, 0x12, 0x26, 0x0a, 0x0e, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65,
	0x6c, 0x54, 0x72, 0x61, 0x64, 0x65, 0x4e, 0x6f, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e,
	0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x54, 0x72, 0x61, 0x64, 0x65, 0x4e, 0x6f, 0x12, 0x26,
	0x0a, 0x0e, 0x63, 0x68, 0x65, 0x63, 0x6b, 0x53, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x49, 0x64,
	0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x63, 0x68, 0x65, 0x63, 0x6b, 0x53, 0x65, 0x73,
	0x73, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74,
	0x18, 0x0d, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x1a,
	0x0a, 0x08, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x63, 0x79, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x08, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x63, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x70, 0x61,
	0x79, 0x65, 0x65, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x70, 0x61, 0x79, 0x65, 0x65,
	0x12, 0x20, 0x0a, 0x0b, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x18,
	0x10, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x54, 0x79,
	0x70, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x18, 0x11,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x12, 0x16,
	0x0a, 0x06, 0x64, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x18, 0x12, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06,
	0x64, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x12, 0x22, 0x0a, 0x0c, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65,
	0x73, 0x73, 0x54, 0x79, 0x70, 0x65, 0x18, 0x13, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x62, 0x75,
	0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x6c, 0x61,
	0x6e, 0x67, 0x75, 0x61, 0x67, 0x65, 0x18, 0x14, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x6c, 0x61,
	0x6e, 0x67, 0x75, 0x61, 0x67, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74,
	0x49, 0x70, 0x18, 0x15, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74,
	0x49, 0x70, 0x12, 0x16, 0x0a, 0x06, 0x6f, 0x70, 0x65, 0x6e, 0x49, 0x44, 0x18, 0x16, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x06, 0x6f, 0x70, 0x65, 0x6e, 0x49, 0x44, 0x12, 0x1e, 0x0a, 0x0a, 0x74, 0x69,
	0x6d, 0x65, 0x45, 0x78, 0x70, 0x69, 0x72, 0x65, 0x18, 0x17, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0a,
	0x74, 0x69, 0x6d, 0x65, 0x45, 0x78, 0x70, 0x69, 0x72, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x6c, 0x6f,
	0x63, 0x61, 0x6c, 0x65, 0x18, 0x18, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x6c, 0x6f, 0x63, 0x61,
	0x6c, 0x65, 0x22, 0xe3, 0x02, 0x0a, 0x11, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x50, 0x61, 0x79,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x75, 0x72, 0x6c, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x75, 0x72, 0x6c, 0x12, 0x22, 0x0a, 0x0b, 0x6c, 0x6f,
	0x67, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x49, 0x44, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0d, 0x6c, 0x6f, 0x67, 0x5f, 0x72, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x5f, 0x49, 0x44, 0x12, 0x10,
	0x0a, 0x03, 0x6d, 0x73, 0x67, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6d, 0x73, 0x67,
	0x12, 0x14, 0x0a, 0x05, 0x61, 0x70, 0x70, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x05, 0x61, 0x70, 0x70, 0x69, 0x64, 0x12, 0x1c, 0x0a, 0x09, 0x74, 0x69, 0x6d, 0x65, 0x53, 0x74,
	0x61, 0x6d, 0x70, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x74, 0x69, 0x6d, 0x65, 0x53,
	0x74, 0x61, 0x6d, 0x70, 0x12, 0x18, 0x0a, 0x07, 0x70, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x18,
	0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x70, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x12, 0x1a,
	0x0a, 0x08, 0x73, 0x69, 0x67, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x08, 0x73, 0x69, 0x67, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x6e, 0x6f,
	0x6e, 0x63, 0x65, 0x53, 0x74, 0x72, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x6e, 0x6f,
	0x6e, 0x63, 0x65, 0x53, 0x74, 0x72, 0x12, 0x18, 0x0a, 0x07, 0x70, 0x61, 0x79, 0x53, 0x69, 0x67,
	0x6e, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x70, 0x61, 0x79, 0x53, 0x69, 0x67, 0x6e,
	0x12, 0x1a, 0x0a, 0x08, 0x70, 0x72, 0x65, 0x70, 0x61, 0x79, 0x49, 0x64, 0x18, 0x0a, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x08, 0x70, 0x72, 0x65, 0x70, 0x61, 0x79, 0x49, 0x64, 0x12, 0x1c, 0x0a, 0x09,
	0x70, 0x61, 0x72, 0x74, 0x6e, 0x65, 0x72, 0x49, 0x64, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x09, 0x70, 0x61, 0x72, 0x74, 0x6e, 0x65, 0x72, 0x49, 0x64, 0x12, 0x2c, 0x0a, 0x11, 0x63, 0x68,
	0x65, 0x63, 0x6b, 0x6f, 0x75, 0x74, 0x53, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x18,
	0x0c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x11, 0x63, 0x68, 0x65, 0x63, 0x6b, 0x6f, 0x75, 0x74, 0x53,
	0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x22, 0xdf, 0x04, 0x0a, 0x13, 0x43, 0x72, 0x65,
	0x61, 0x74, 0x65, 0x52, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x12, 0x18, 0x0a, 0x07, 0x70, 0x6f, 0x73, 0x74, 0x55, 0x72, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x07, 0x70, 0x6f, 0x73, 0x74, 0x55, 0x72, 0x6c, 0x12, 0x18, 0x0a, 0x07, 0x73, 0x75,
	0x62, 0x6a, 0x65, 0x63, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x73, 0x75, 0x62,
	0x6a, 0x65, 0x63, 0x74, 0x12, 0x20, 0x0a, 0x0b, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x55,
	0x55, 0x49, 0x44, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x70, 0x72, 0x6f, 0x64, 0x75,
	0x63, 0x74, 0x55, 0x55, 0x49, 0x44, 0x12, 0x1e, 0x0a, 0x0a, 0x6f, 0x75, 0x74, 0x54, 0x72, 0x61,
	0x64, 0x65, 0x4e, 0x6f, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x6f, 0x75, 0x74, 0x54,
	0x72, 0x61, 0x64, 0x65, 0x4e, 0x6f, 0x12, 0x26, 0x0a, 0x0e, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65,
	0x6c, 0x54, 0x72, 0x61, 0x64, 0x65, 0x4e, 0x6f, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e,
	0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x54, 0x72, 0x61, 0x64, 0x65, 0x4e, 0x6f, 0x12, 0x26,
	0x0a, 0x0e, 0x63, 0x68, 0x65, 0x63, 0x6b, 0x53, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x49, 0x64,
	0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x63, 0x68, 0x65, 0x63, 0x6b, 0x53, 0x65, 0x73,
	0x73, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x12, 0x22, 0x0a, 0x0c, 0x72, 0x65, 0x66, 0x75, 0x6e, 0x64,
	0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x07, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0c, 0x72, 0x65,
	0x66, 0x75, 0x6e, 0x64, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x1a, 0x0a, 0x08, 0x63, 0x75,
	0x72, 0x72, 0x65, 0x6e, 0x63, 0x79, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x63, 0x75,
	0x72, 0x72, 0x65, 0x6e, 0x63, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x70, 0x61, 0x79, 0x65, 0x65, 0x18,
	0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x70, 0x61, 0x79, 0x65, 0x65, 0x12, 0x20, 0x0a, 0x0b,
	0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0b, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1a,
	0x0a, 0x08, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x08, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x12, 0x16, 0x0a, 0x06, 0x64, 0x6f,
	0x6d, 0x61, 0x69, 0x6e, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x64, 0x6f, 0x6d, 0x61,
	0x69, 0x6e, 0x12, 0x22, 0x0a, 0x0c, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x54, 0x79,
	0x70, 0x65, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65,
	0x73, 0x73, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x6c, 0x61, 0x6e, 0x67, 0x75, 0x61,
	0x67, 0x65, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x6c, 0x61, 0x6e, 0x67, 0x75, 0x61,
	0x67, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x49, 0x70, 0x18, 0x0f,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x49, 0x70, 0x12, 0x16,
	0x0a, 0x06, 0x6f, 0x70, 0x65, 0x6e, 0x49, 0x44, 0x18, 0x10, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06,
	0x6f, 0x70, 0x65, 0x6e, 0x49, 0x44, 0x12, 0x1c, 0x0a, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x6f,
	0x72, 0x49, 0x64, 0x18, 0x11, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74,
	0x6f, 0x72, 0x49, 0x64, 0x12, 0x20, 0x0a, 0x0b, 0x63, 0x72, 0x65, 0x61, 0x74, 0x6f, 0x72, 0x4e,
	0x61, 0x6d, 0x65, 0x18, 0x12, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x63, 0x72, 0x65, 0x61, 0x74,
	0x6f, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x22, 0x0a, 0x0c, 0x72, 0x65, 0x66, 0x75, 0x6e, 0x64,
	0x52, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x18, 0x13, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x72, 0x65,
	0x66, 0x75, 0x6e, 0x64, 0x52, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x22, 0x3c, 0x0a, 0x14, 0x43, 0x72,
	0x65, 0x61, 0x74, 0x65, 0x52, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x6d, 0x73, 0x67, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x03, 0x6d, 0x73, 0x67, 0x22, 0x82, 0x01, 0x0a, 0x10, 0x4e, 0x6f, 0x74,
	0x69, 0x66, 0x79, 0x50, 0x61, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x18, 0x0a,
	0x07, 0x70, 0x6f, 0x73, 0x74, 0x55, 0x72, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07,
	0x70, 0x6f, 0x73, 0x74, 0x55, 0x72, 0x6c, 0x12, 0x37, 0x0a, 0x0c, 0x68, 0x74, 0x74, 0x70, 0x5f,
	0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x14, 0x2e,
	0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x48, 0x74, 0x74, 0x70, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x52, 0x0b, 0x68, 0x74, 0x74, 0x70, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x12, 0x1b, 0x0a, 0x08, 0x72, 0x61, 0x77, 0x51, 0x75, 0x65, 0x72, 0x79, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x09, 0x72, 0x61, 0x77, 0x5f, 0x71, 0x75, 0x65, 0x72, 0x79, 0x22, 0x45, 0x0a,
	0x11, 0x4e, 0x6f, 0x74, 0x69, 0x66, 0x79, 0x50, 0x61, 0x79, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x6d, 0x73, 0x67, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x03, 0x6d, 0x73, 0x67, 0x12, 0x1e, 0x0a, 0x0a, 0x6f, 0x75, 0x74, 0x54, 0x72, 0x61, 0x64, 0x65,
	0x4e, 0x6f, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x6f, 0x75, 0x74, 0x54, 0x72, 0x61,
	0x64, 0x65, 0x4e, 0x6f, 0x22, 0xc4, 0x01, 0x0a, 0x0b, 0x48, 0x74, 0x74, 0x70, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x12, 0x16, 0x0a, 0x06, 0x6d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x6d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x12, 0x10, 0x0a, 0x03,
	0x75, 0x72, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x75, 0x72, 0x6c, 0x12, 0x3b,
	0x0a, 0x07, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x21, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x48, 0x74, 0x74, 0x70, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x73, 0x45, 0x6e, 0x74,
	0x72, 0x79, 0x52, 0x07, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x73, 0x12, 0x12, 0x0a, 0x04, 0x62,
	0x6f, 0x64, 0x79, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x04, 0x62, 0x6f, 0x64, 0x79, 0x1a,
	0x3a, 0x0a, 0x0c, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12,
	0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65,
	0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0x1d, 0x0a, 0x09, 0x43,
	0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x4d, 0x73, 0x67, 0x12, 0x10, 0x0a, 0x03, 0x6d, 0x73, 0x67, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6d, 0x73, 0x67, 0x22, 0x0e, 0x0a, 0x0c, 0x45, 0x6d,
	0x70, 0x74, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x22, 0x98, 0x04, 0x0a, 0x22, 0x43,
	0x72, 0x65, 0x61, 0x74, 0x65, 0x53, 0x74, 0x72, 0x69, 0x70, 0x65, 0x43, 0x68, 0x65, 0x63, 0x6b,
	0x6f, 0x75, 0x74, 0x53, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x12, 0x20, 0x0a, 0x0b, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x55, 0x55, 0x49, 0x44,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x55,
	0x55, 0x49, 0x44, 0x12, 0x20, 0x0a, 0x0b, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x4e, 0x61,
	0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63,
	0x74, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x28, 0x0a, 0x0f, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74,
	0x51, 0x75, 0x61, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0f,
	0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x51, 0x75, 0x61, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x12,
	0x28, 0x0a, 0x0f, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x41, 0x6c, 0x6c, 0x50, 0x72, 0x69,
	0x63, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0f, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63,
	0x74, 0x41, 0x6c, 0x6c, 0x50, 0x72, 0x69, 0x63, 0x65, 0x12, 0x2e, 0x0a, 0x12, 0x70, 0x72, 0x6f,
	0x64, 0x75, 0x63, 0x74, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x12, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x44, 0x65,
	0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x28, 0x0a, 0x0f, 0x70, 0x72, 0x6f,
	0x64, 0x75, 0x63, 0x74, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x55, 0x72, 0x6c, 0x18, 0x06, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0f, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x49, 0x6d, 0x61, 0x67, 0x65,
	0x55, 0x72, 0x6c, 0x12, 0x32, 0x0a, 0x14, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x50, 0x72,
	0x69, 0x63, 0x65, 0x43, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x63, 0x79, 0x18, 0x07, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x14, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x50, 0x72, 0x69, 0x63, 0x65, 0x43,
	0x75, 0x72, 0x72, 0x65, 0x6e, 0x63, 0x79, 0x12, 0x1c, 0x0a, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74,
	0x65, 0x72, 0x49, 0x44, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61,
	0x74, 0x65, 0x72, 0x49, 0x44, 0x12, 0x20, 0x0a, 0x0b, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x72,
	0x4e, 0x61, 0x6d, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x63, 0x72, 0x65, 0x61,
	0x74, 0x65, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x64, 0x6f, 0x6d, 0x61, 0x69,
	0x6e, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x64, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x12,
	0x1e, 0x0a, 0x0a, 0x73, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x55, 0x72, 0x6c, 0x18, 0x0b, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0a, 0x73, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x55, 0x72, 0x6c, 0x12,
	0x1c, 0x0a, 0x09, 0x63, 0x61, 0x6e, 0x63, 0x65, 0x6c, 0x55, 0x72, 0x6c, 0x18, 0x0c, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x09, 0x63, 0x61, 0x6e, 0x63, 0x65, 0x6c, 0x55, 0x72, 0x6c, 0x12, 0x1e, 0x0a,
	0x0a, 0x6f, 0x75, 0x74, 0x54, 0x72, 0x61, 0x64, 0x65, 0x4e, 0x6f, 0x18, 0x0d, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0a, 0x6f, 0x75, 0x74, 0x54, 0x72, 0x61, 0x64, 0x65, 0x4e, 0x6f, 0x12, 0x16, 0x0a,
	0x06, 0x6c, 0x6f, 0x63, 0x61, 0x6c, 0x65, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x6c,
	0x6f, 0x63, 0x61, 0x6c, 0x65, 0x22, 0x83, 0x01, 0x0a, 0x23, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65,
	0x53, 0x74, 0x72, 0x69, 0x70, 0x65, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x6f, 0x75, 0x74, 0x53, 0x65,
	0x73, 0x73, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x2c, 0x0a,
	0x11, 0x63, 0x68, 0x65, 0x63, 0x6b, 0x6f, 0x75, 0x74, 0x53, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e,
	0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x11, 0x63, 0x68, 0x65, 0x63, 0x6b, 0x6f,
	0x75, 0x74, 0x53, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x12, 0x2e, 0x0a, 0x12, 0x63,
	0x68, 0x65, 0x63, 0x6b, 0x6f, 0x75, 0x74, 0x53, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x55, 0x72,
	0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x12, 0x63, 0x68, 0x65, 0x63, 0x6b, 0x6f, 0x75,
	0x74, 0x53, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x55, 0x72, 0x6c, 0x22, 0xa1, 0x01, 0x0a, 0x19,
	0x47, 0x65, 0x74, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x6f, 0x75, 0x74, 0x57, 0x65, 0x62, 0x68, 0x6f,
	0x6f, 0x6b, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x18, 0x0a, 0x07, 0x70, 0x6f, 0x73,
	0x74, 0x55, 0x72, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x70, 0x6f, 0x73, 0x74,
	0x55, 0x72, 0x6c, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x70, 0x61, 0x79, 0x6c, 0x6f,
	0x61, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x70, 0x61, 0x79, 0x6c, 0x6f, 0x61,
	0x64, 0x12, 0x1c, 0x0a, 0x09, 0x73, 0x69, 0x67, 0x6e, 0x61, 0x74, 0x75, 0x72, 0x65, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x73, 0x69, 0x67, 0x6e, 0x61, 0x74, 0x75, 0x72, 0x65, 0x12,
	0x1e, 0x0a, 0x0a, 0x77, 0x65, 0x62, 0x68, 0x6f, 0x6f, 0x6b, 0x4b, 0x65, 0x79, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0a, 0x77, 0x65, 0x62, 0x68, 0x6f, 0x6f, 0x6b, 0x4b, 0x65, 0x79, 0x22,
	0xa2, 0x01, 0x0a, 0x1a, 0x47, 0x65, 0x74, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x6f, 0x75, 0x74, 0x57,
	0x65, 0x62, 0x68, 0x6f, 0x6f, 0x6b, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x18,
	0x0a, 0x07, 0x73, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52,
	0x07, 0x73, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x12, 0x18, 0x0a, 0x07, 0x6d, 0x65, 0x73, 0x73,
	0x61, 0x67, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61,
	0x67, 0x65, 0x12, 0x1e, 0x0a, 0x0a, 0x6f, 0x75, 0x74, 0x54, 0x72, 0x61, 0x64, 0x65, 0x4e, 0x6f,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x6f, 0x75, 0x74, 0x54, 0x72, 0x61, 0x64, 0x65,
	0x4e, 0x6f, 0x12, 0x30, 0x0a, 0x13, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x49, 0x6e, 0x74,
	0x65, 0x6e, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x13, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x49, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x53, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x22, 0x6f, 0x0a, 0x21, 0x47, 0x65, 0x74, 0x53, 0x74, 0x72, 0x69, 0x70,
	0x65, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x49, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x49, 0x6e,
	0x66, 0x6f, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x2c, 0x0a, 0x11, 0x63, 0x68, 0x65,
	0x63, 0x6b, 0x6f, 0x75, 0x74, 0x53, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x11, 0x63, 0x68, 0x65, 0x63, 0x6b, 0x6f, 0x75, 0x74, 0x53, 0x65,
	0x73, 0x73, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x12, 0x1c, 0x0a, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74,
	0x65, 0x72, 0x49, 0x44, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61,
	0x74, 0x65, 0x72, 0x49, 0x44, 0x22, 0xb2, 0x05, 0x0a, 0x22, 0x47, 0x65, 0x74, 0x53, 0x74, 0x72,
	0x69, 0x70, 0x65, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x49, 0x6e, 0x74, 0x65, 0x6e, 0x74,
	0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x2c, 0x0a, 0x11,
	0x63, 0x68, 0x65, 0x63, 0x6b, 0x6f, 0x75, 0x74, 0x53, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x49,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x11, 0x63, 0x68, 0x65, 0x63, 0x6b, 0x6f, 0x75,
	0x74, 0x53, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x12, 0x34, 0x0a, 0x15, 0x63, 0x68,
	0x65, 0x63, 0x6b, 0x6f, 0x75, 0x74, 0x53, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x15, 0x63, 0x68, 0x65, 0x63, 0x6b,
	0x6f, 0x75, 0x74, 0x53, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x12, 0x28, 0x0a, 0x0f, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x49, 0x6e, 0x74, 0x65, 0x6e,
	0x74, 0x49, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x70, 0x61, 0x79, 0x6d, 0x65,
	0x6e, 0x74, 0x49, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x32, 0x0a, 0x14, 0x70, 0x61,
	0x79, 0x6d, 0x65, 0x6e, 0x74, 0x49, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x43, 0x72, 0x65, 0x61, 0x74,
	0x65, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x14, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e,
	0x74, 0x49, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x12, 0x30,
	0x0a, 0x13, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x49, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x53,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x13, 0x70, 0x61, 0x79,
	0x6d, 0x65, 0x6e, 0x74, 0x49, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x12, 0x1a, 0x0a, 0x08, 0x63, 0x68, 0x61, 0x72, 0x67, 0x65, 0x49, 0x64, 0x18, 0x06, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x08, 0x63, 0x68, 0x61, 0x72, 0x67, 0x65, 0x49, 0x64, 0x12, 0x1a, 0x0a, 0x08,
	0x70, 0x61, 0x79, 0x50, 0x72, 0x69, 0x63, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08,
	0x70, 0x61, 0x79, 0x50, 0x72, 0x69, 0x63, 0x65, 0x12, 0x28, 0x0a, 0x0f, 0x70, 0x61, 0x79, 0x45,
	0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x52, 0x61, 0x74, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0f, 0x70, 0x61, 0x79, 0x45, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x52, 0x61,
	0x74, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x70, 0x61, 0x79, 0x43, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x63,
	0x79, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x70, 0x61, 0x79, 0x43, 0x75, 0x72, 0x72,
	0x65, 0x6e, 0x63, 0x79, 0x12, 0x28, 0x0a, 0x0f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x43,
	0x75, 0x72, 0x72, 0x65, 0x6e, 0x63, 0x79, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x61,
	0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x43, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x63, 0x79, 0x12, 0x2c,
	0x0a, 0x11, 0x61, 0x66, 0x74, 0x65, 0x72, 0x52, 0x61, 0x74, 0x65, 0x50, 0x61, 0x79, 0x50, 0x72,
	0x69, 0x63, 0x65, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x03, 0x52, 0x11, 0x61, 0x66, 0x74, 0x65, 0x72,
	0x52, 0x61, 0x74, 0x65, 0x50, 0x61, 0x79, 0x50, 0x72, 0x69, 0x63, 0x65, 0x12, 0x24, 0x0a, 0x0d,
	0x70, 0x61, 0x79, 0x48, 0x61, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x46, 0x65, 0x65, 0x18, 0x0c, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x0d, 0x70, 0x61, 0x79, 0x48, 0x61, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x46,
	0x65, 0x65, 0x12, 0x34, 0x0a, 0x15, 0x61, 0x66, 0x74, 0x65, 0x72, 0x52, 0x61, 0x74, 0x65, 0x50,
	0x61, 0x79, 0x4e, 0x65, 0x74, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x0d, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x15, 0x61, 0x66, 0x74, 0x65, 0x72, 0x52, 0x61, 0x74, 0x65, 0x50, 0x61, 0x79, 0x4e,
	0x65, 0x74, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x1e, 0x0a, 0x0a, 0x63, 0x75, 0x73, 0x74,
	0x6f, 0x6d, 0x65, 0x72, 0x49, 0x64, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x63, 0x75,
	0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x49, 0x64, 0x12, 0x20, 0x0a, 0x0b, 0x72, 0x65, 0x66, 0x75,
	0x6e, 0x64, 0x50, 0x72, 0x69, 0x63, 0x65, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0b, 0x72,
	0x65, 0x66, 0x75, 0x6e, 0x64, 0x50, 0x72, 0x69, 0x63, 0x65, 0x12, 0x1e, 0x0a, 0x0a, 0x6f, 0x75,
	0x74, 0x54, 0x72, 0x61, 0x64, 0x65, 0x4e, 0x6f, 0x18, 0x10, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a,
	0x6f, 0x75, 0x74, 0x54, 0x72, 0x61, 0x64, 0x65, 0x4e, 0x6f, 0x22, 0x50, 0x0a, 0x14, 0x47, 0x65,
	0x74, 0x52, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x12, 0x1a, 0x0a, 0x08, 0x72, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x49, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x72, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x49, 0x64, 0x12, 0x1c,
	0x0a, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x72, 0x49, 0x44, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x72, 0x49, 0x44, 0x22, 0x3b, 0x0a, 0x15,
	0x47, 0x65, 0x74, 0x52, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x22, 0x0a, 0x0c, 0x72, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x53,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x72, 0x65, 0x66,
	0x75, 0x6e, 0x64, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x22, 0xc2, 0x02, 0x0a, 0x10, 0x41, 0x6c,
	0x69, 0x57, 0x61, 0x70, 0x50, 0x61, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x18,
	0x0a, 0x07, 0x73, 0x75, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x07, 0x73, 0x75, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x12, 0x21, 0x0a, 0x0b, 0x70, 0x72, 0x6f, 0x64,
	0x75, 0x63, 0x74, 0x43, 0x6f, 0x64, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x70,
	0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x20, 0x0a, 0x0a, 0x6f,
	0x75, 0x74, 0x54, 0x72, 0x61, 0x64, 0x65, 0x4e, 0x6f, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0c, 0x6f, 0x75, 0x74, 0x5f, 0x74, 0x72, 0x61, 0x64, 0x65, 0x5f, 0x6e, 0x6f, 0x12, 0x19, 0x0a,
	0x07, 0x71, 0x75, 0x69, 0x74, 0x55, 0x72, 0x6c, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08,
	0x71, 0x75, 0x69, 0x74, 0x5f, 0x75, 0x72, 0x6c, 0x12, 0x1d, 0x0a, 0x09, 0x6e, 0x6f, 0x74, 0x69,
	0x66, 0x79, 0x55, 0x72, 0x6c, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x6e, 0x6f, 0x74,
	0x69, 0x66, 0x79, 0x5f, 0x75, 0x72, 0x6c, 0x12, 0x1d, 0x0a, 0x09, 0x72, 0x65, 0x74, 0x75, 0x72,
	0x6e, 0x55, 0x72, 0x6c, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x72, 0x65, 0x74, 0x75,
	0x72, 0x6e, 0x5f, 0x75, 0x72, 0x6c, 0x12, 0x21, 0x0a, 0x0b, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x41,
	0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x07, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0c, 0x74, 0x6f, 0x74,
	0x61, 0x6c, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x1f, 0x0a, 0x0a, 0x74, 0x69, 0x6d,
	0x65, 0x45, 0x78, 0x70, 0x69, 0x72, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0b, 0x74,
	0x69, 0x6d, 0x65, 0x5f, 0x65, 0x78, 0x70, 0x69, 0x72, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x64, 0x6f,
	0x6d, 0x61, 0x69, 0x6e, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x64, 0x6f, 0x6d, 0x61,
	0x69, 0x6e, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x18, 0x0a,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x22, 0x5b,
	0x0a, 0x11, 0x41, 0x6c, 0x69, 0x57, 0x61, 0x70, 0x50, 0x61, 0x79, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x75, 0x72, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x03, 0x75, 0x72, 0x6c, 0x12, 0x22, 0x0a, 0x0b, 0x6c, 0x6f, 0x67, 0x52, 0x65, 0x63, 0x6f,
	0x72, 0x64, 0x49, 0x44, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x6c, 0x6f, 0x67, 0x5f,
	0x72, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x5f, 0x49, 0x44, 0x12, 0x10, 0x0a, 0x03, 0x6d, 0x73, 0x67,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6d, 0x73, 0x67, 0x22, 0xdc, 0x02, 0x0a, 0x10,
	0x41, 0x6c, 0x69, 0x41, 0x70, 0x70, 0x50, 0x61, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x12, 0x18, 0x0a, 0x07, 0x73, 0x75, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x07, 0x73, 0x75, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x12, 0x21, 0x0a, 0x0b, 0x70, 0x72,
	0x6f, 0x64, 0x75, 0x63, 0x74, 0x43, 0x6f, 0x64, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0c, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x20, 0x0a,
	0x0a, 0x6f, 0x75, 0x74, 0x54, 0x72, 0x61, 0x64, 0x65, 0x4e, 0x6f, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0c, 0x6f, 0x75, 0x74, 0x5f, 0x74, 0x72, 0x61, 0x64, 0x65, 0x5f, 0x6e, 0x6f, 0x12,
	0x19, 0x0a, 0x07, 0x71, 0x75, 0x69, 0x74, 0x55, 0x72, 0x6c, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x08, 0x71, 0x75, 0x69, 0x74, 0x5f, 0x75, 0x72, 0x6c, 0x12, 0x1d, 0x0a, 0x09, 0x6e, 0x6f,
	0x74, 0x69, 0x66, 0x79, 0x55, 0x72, 0x6c, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x6e,
	0x6f, 0x74, 0x69, 0x66, 0x79, 0x5f, 0x75, 0x72, 0x6c, 0x12, 0x1d, 0x0a, 0x09, 0x72, 0x65, 0x74,
	0x75, 0x72, 0x6e, 0x55, 0x72, 0x6c, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x72, 0x65,
	0x74, 0x75, 0x72, 0x6e, 0x5f, 0x75, 0x72, 0x6c, 0x12, 0x21, 0x0a, 0x0b, 0x74, 0x6f, 0x74, 0x61,
	0x6c, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x74,
	0x6f, 0x74, 0x61, 0x6c, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x1f, 0x0a, 0x0a, 0x74,
	0x69, 0x6d, 0x65, 0x45, 0x78, 0x70, 0x69, 0x72, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x0b, 0x74, 0x69, 0x6d, 0x65, 0x5f, 0x65, 0x78, 0x70, 0x69, 0x72, 0x65, 0x12, 0x16, 0x0a, 0x06,
	0x64, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x64, 0x6f,
	0x6d, 0x61, 0x69, 0x6e, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d,
	0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d,
	0x12, 0x18, 0x0a, 0x07, 0x70, 0x61, 0x79, 0x54, 0x79, 0x70, 0x65, 0x18, 0x0b, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x07, 0x70, 0x61, 0x79, 0x54, 0x79, 0x70, 0x65, 0x22, 0x5b, 0x0a, 0x11, 0x41, 0x6c,
	0x69, 0x41, 0x70, 0x70, 0x50, 0x61, 0x79, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12,
	0x10, 0x0a, 0x03, 0x75, 0x72, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x75, 0x72,
	0x6c, 0x12, 0x22, 0x0a, 0x0b, 0x6c, 0x6f, 0x67, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x49, 0x44,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x6c, 0x6f, 0x67, 0x5f, 0x72, 0x65, 0x63, 0x6f,
	0x72, 0x64, 0x5f, 0x49, 0x44, 0x12, 0x10, 0x0a, 0x03, 0x6d, 0x73, 0x67, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x03, 0x6d, 0x73, 0x67, 0x22, 0xe8, 0x01, 0x0a, 0x13, 0x41, 0x6c, 0x69, 0x4e,
	0x61, 0x74, 0x69, 0x76, 0x65, 0x50, 0x61, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12,
	0x18, 0x0a, 0x07, 0x73, 0x75, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x07, 0x73, 0x75, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x12, 0x20, 0x0a, 0x0a, 0x6f, 0x75, 0x74,
	0x54, 0x72, 0x61, 0x64, 0x65, 0x4e, 0x6f, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x6f,
	0x75, 0x74, 0x5f, 0x74, 0x72, 0x61, 0x64, 0x65, 0x5f, 0x6e, 0x6f, 0x12, 0x1d, 0x0a, 0x09, 0x6e,
	0x6f, 0x74, 0x69, 0x66, 0x79, 0x55, 0x72, 0x6c, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a,
	0x6e, 0x6f, 0x74, 0x69, 0x66, 0x79, 0x5f, 0x75, 0x72, 0x6c, 0x12, 0x21, 0x0a, 0x0b, 0x74, 0x6f,
	0x74, 0x61, 0x6c, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x0c, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x1f, 0x0a,
	0x0a, 0x74, 0x69, 0x6d, 0x65, 0x45, 0x78, 0x70, 0x69, 0x72, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x0b, 0x74, 0x69, 0x6d, 0x65, 0x5f, 0x65, 0x78, 0x70, 0x69, 0x72, 0x65, 0x12, 0x16,
	0x0a, 0x06, 0x64, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06,
	0x64, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f,
	0x72, 0x6d, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f,
	0x72, 0x6d, 0x22, 0x3a, 0x0a, 0x14, 0x41, 0x6c, 0x69, 0x4e, 0x61, 0x74, 0x69, 0x76, 0x65, 0x50,
	0x61, 0x79, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x75, 0x72,
	0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x75, 0x72, 0x6c, 0x12, 0x10, 0x0a, 0x03,
	0x6d, 0x73, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6d, 0x73, 0x67, 0x22, 0xa1,
	0x02, 0x0a, 0x12, 0x41, 0x6c, 0x69, 0x50, 0x63, 0x57, 0x61, 0x62, 0x50, 0x61, 0x79, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x18, 0x0a, 0x07, 0x73, 0x75, 0x62, 0x6a, 0x65, 0x63, 0x74,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x73, 0x75, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x12,
	0x20, 0x0a, 0x0a, 0x6f, 0x75, 0x74, 0x54, 0x72, 0x61, 0x64, 0x65, 0x4e, 0x6f, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0c, 0x6f, 0x75, 0x74, 0x5f, 0x74, 0x72, 0x61, 0x64, 0x65, 0x5f, 0x6e,
	0x6f, 0x12, 0x1d, 0x0a, 0x09, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x79, 0x55, 0x72, 0x6c, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x79, 0x5f, 0x75, 0x72, 0x6c,
	0x12, 0x21, 0x0a, 0x0b, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0c, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x5f, 0x61, 0x6d, 0x6f,
	0x75, 0x6e, 0x74, 0x12, 0x1f, 0x0a, 0x0a, 0x74, 0x69, 0x6d, 0x65, 0x45, 0x78, 0x70, 0x69, 0x72,
	0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0b, 0x74, 0x69, 0x6d, 0x65, 0x5f, 0x65, 0x78,
	0x70, 0x69, 0x72, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x64, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x18, 0x06,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x64, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x12, 0x1a, 0x0a, 0x08,
	0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08,
	0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x12, 0x19, 0x0a, 0x07, 0x71, 0x75, 0x69, 0x74,
	0x55, 0x72, 0x6c, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x71, 0x75, 0x69, 0x74, 0x5f,
	0x75, 0x72, 0x6c, 0x12, 0x1d, 0x0a, 0x09, 0x72, 0x65, 0x74, 0x75, 0x72, 0x6e, 0x55, 0x72, 0x6c,
	0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x72, 0x65, 0x74, 0x75, 0x72, 0x6e, 0x5f, 0x75,
	0x72, 0x6c, 0x22, 0x5b, 0x0a, 0x13, 0x41, 0x6c, 0x69, 0x50, 0x63, 0x57, 0x61, 0x62, 0x50, 0x61,
	0x79, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x32, 0x0a, 0x13, 0x70, 0x61, 0x67,
	0x65, 0x52, 0x65, 0x64, 0x69, 0x72, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x61, 0x74, 0x61,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x15, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x72, 0x65, 0x64,
	0x69, 0x72, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x12, 0x10, 0x0a,
	0x03, 0x6d, 0x73, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6d, 0x73, 0x67, 0x22,
	0x57, 0x0a, 0x10, 0x41, 0x6c, 0x69, 0x52, 0x65, 0x46, 0x75, 0x6e, 0x64, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x12, 0x20, 0x0a, 0x0a, 0x6f, 0x75, 0x74, 0x54, 0x72, 0x61, 0x64, 0x65, 0x4e,
	0x6f, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x6f, 0x75, 0x74, 0x5f, 0x74, 0x72, 0x61,
	0x64, 0x65, 0x5f, 0x6e, 0x6f, 0x12, 0x21, 0x0a, 0x0b, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x41, 0x6d,
	0x6f, 0x75, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0c, 0x74, 0x6f, 0x74, 0x61,
	0x6c, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x22, 0xaa, 0x01, 0x0a, 0x11, 0x41, 0x6c, 0x69,
	0x52, 0x65, 0x46, 0x75, 0x6e, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x19,
	0x0a, 0x07, 0x74, 0x72, 0x61, 0x64, 0x65, 0x4e, 0x6f, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x08, 0x74, 0x72, 0x61, 0x64, 0x65, 0x5f, 0x6e, 0x6f, 0x12, 0x20, 0x0a, 0x0a, 0x6f, 0x75, 0x74,
	0x54, 0x72, 0x61, 0x64, 0x65, 0x4e, 0x6f, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x6f,
	0x75, 0x74, 0x5f, 0x74, 0x72, 0x61, 0x64, 0x65, 0x5f, 0x6e, 0x6f, 0x12, 0x26, 0x0a, 0x0e, 0x62,
	0x75, 0x79, 0x65, 0x72, 0x5f, 0x6c, 0x6f, 0x67, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0e, 0x62, 0x75, 0x79, 0x65, 0x72, 0x5f, 0x6c, 0x6f, 0x67, 0x6f, 0x6e,
	0x5f, 0x69, 0x64, 0x12, 0x1e, 0x0a, 0x0a, 0x72, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x5f, 0x66, 0x65,
	0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x72, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x5f,
	0x66, 0x65, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x6d, 0x73, 0x67, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x03, 0x6d, 0x73, 0x67, 0x22, 0x2f, 0x0a, 0x10, 0x41, 0x6c, 0x69, 0x4e, 0x6f, 0x74, 0x69,
	0x66, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1b, 0x0a, 0x08, 0x72, 0x61, 0x77,
	0x51, 0x75, 0x65, 0x72, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x72, 0x61, 0x77,
	0x5f, 0x71, 0x75, 0x65, 0x72, 0x79, 0x22, 0xf5, 0x02, 0x0a, 0x11, 0x41, 0x6c, 0x69, 0x4e, 0x6f,
	0x74, 0x69, 0x66, 0x79, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x1f, 0x0a, 0x0a,
	0x6e, 0x6f, 0x74, 0x69, 0x66, 0x79, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0b, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x79, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x12, 0x1f, 0x0a,
	0x0a, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x79, 0x54, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0b, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x79, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x12, 0x1b,
	0x0a, 0x08, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x79, 0x49, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x09, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x79, 0x5f, 0x69, 0x64, 0x12, 0x15, 0x0a, 0x05, 0x61,
	0x70, 0x70, 0x49, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x61, 0x70, 0x70, 0x5f,
	0x69, 0x64, 0x12, 0x18, 0x0a, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x1b, 0x0a, 0x08,
	0x73, 0x69, 0x67, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09,
	0x73, 0x69, 0x67, 0x6e, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x73, 0x69, 0x67,
	0x6e, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x73, 0x69, 0x67, 0x6e, 0x12, 0x19, 0x0a,
	0x07, 0x74, 0x72, 0x61, 0x64, 0x65, 0x4e, 0x6f, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08,
	0x74, 0x72, 0x61, 0x64, 0x65, 0x5f, 0x6e, 0x6f, 0x12, 0x20, 0x0a, 0x0a, 0x6f, 0x75, 0x74, 0x54,
	0x72, 0x61, 0x64, 0x65, 0x4e, 0x6f, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x6f, 0x75,
	0x74, 0x5f, 0x74, 0x72, 0x61, 0x64, 0x65, 0x5f, 0x6e, 0x6f, 0x12, 0x24, 0x0a, 0x0c, 0x62, 0x75,
	0x79, 0x65, 0x72, 0x4c, 0x6f, 0x67, 0x6f, 0x6e, 0x49, 0x64, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0e, 0x62, 0x75, 0x79, 0x65, 0x72, 0x5f, 0x6c, 0x6f, 0x67, 0x6f, 0x6e, 0x5f, 0x69, 0x64,
	0x12, 0x19, 0x0a, 0x07, 0x62, 0x75, 0x79, 0x65, 0x72, 0x49, 0x64, 0x18, 0x0b, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x08, 0x62, 0x75, 0x79, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x12, 0x21, 0x0a, 0x0b, 0x74,
	0x72, 0x61, 0x64, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0c, 0x74, 0x72, 0x61, 0x64, 0x65, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x22, 0x3d,
	0x0a, 0x1b, 0x41, 0x6c, 0x69, 0x51, 0x75, 0x65, 0x72, 0x79, 0x42, 0x79, 0x4f, 0x75, 0x74, 0x54,
	0x72, 0x61, 0x64, 0x65, 0x4e, 0x6f, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1e, 0x0a,
	0x0a, 0x6f, 0x75, 0x74, 0x54, 0x72, 0x61, 0x64, 0x65, 0x4e, 0x6f, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0a, 0x6f, 0x75, 0x74, 0x54, 0x72, 0x61, 0x64, 0x65, 0x4e, 0x6f, 0x22, 0xeb, 0x01,
	0x0a, 0x1c, 0x41, 0x6c, 0x69, 0x51, 0x75, 0x65, 0x72, 0x79, 0x42, 0x79, 0x4f, 0x75, 0x74, 0x54,
	0x72, 0x61, 0x64, 0x65, 0x4e, 0x6f, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x19,
	0x0a, 0x07, 0x74, 0x72, 0x61, 0x64, 0x65, 0x4e, 0x6f, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x08, 0x74, 0x72, 0x61, 0x64, 0x65, 0x5f, 0x6e, 0x6f, 0x12, 0x20, 0x0a, 0x0a, 0x6f, 0x75, 0x74,
	0x54, 0x72, 0x61, 0x64, 0x65, 0x4e, 0x6f, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x6f,
	0x75, 0x74, 0x5f, 0x74, 0x72, 0x61, 0x64, 0x65, 0x5f, 0x6e, 0x6f, 0x12, 0x24, 0x0a, 0x0c, 0x62,
	0x75, 0x79, 0x65, 0x72, 0x4c, 0x6f, 0x67, 0x6f, 0x6e, 0x49, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0e, 0x62, 0x75, 0x79, 0x65, 0x72, 0x5f, 0x6c, 0x6f, 0x67, 0x6f, 0x6e, 0x5f, 0x69,
	0x64, 0x12, 0x21, 0x0a, 0x0b, 0x74, 0x72, 0x61, 0x64, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x74, 0x72, 0x61, 0x64, 0x65, 0x5f, 0x73, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x12, 0x21, 0x0a, 0x0b, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x41, 0x6d, 0x6f,
	0x75, 0x6e, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x74, 0x6f, 0x74, 0x61, 0x6c,
	0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x22, 0x0a, 0x0b, 0x62, 0x75, 0x79, 0x65, 0x72,
	0x55, 0x73, 0x65, 0x72, 0x49, 0x44, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x62, 0x75,
	0x79, 0x65, 0x72, 0x5f, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x22, 0x43, 0x0a, 0x21, 0x41,
	0x6c, 0x69, 0x52, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x51, 0x75, 0x65, 0x72, 0x79, 0x42, 0x79, 0x4f,
	0x75, 0x74, 0x54, 0x72, 0x61, 0x64, 0x65, 0x4e, 0x6f, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x12, 0x1e, 0x0a, 0x0a, 0x6f, 0x75, 0x74, 0x54, 0x72, 0x61, 0x64, 0x65, 0x4e, 0x6f, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x6f, 0x75, 0x74, 0x54, 0x72, 0x61, 0x64, 0x65, 0x4e, 0x6f,
	0x22, 0xf9, 0x01, 0x0a, 0x22, 0x41, 0x6c, 0x69, 0x52, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x51, 0x75,
	0x65, 0x72, 0x79, 0x42, 0x79, 0x4f, 0x75, 0x74, 0x54, 0x72, 0x61, 0x64, 0x65, 0x4e, 0x6f, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x19, 0x0a, 0x07, 0x74, 0x72, 0x61, 0x64, 0x65,
	0x4e, 0x6f, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x74, 0x72, 0x61, 0x64, 0x65, 0x5f,
	0x6e, 0x6f, 0x12, 0x20, 0x0a, 0x0a, 0x6f, 0x75, 0x74, 0x54, 0x72, 0x61, 0x64, 0x65, 0x4e, 0x6f,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x6f, 0x75, 0x74, 0x5f, 0x74, 0x72, 0x61, 0x64,
	0x65, 0x5f, 0x6e, 0x6f, 0x12, 0x26, 0x0a, 0x0e, 0x6f, 0x75, 0x74, 0x5f, 0x72, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x5f, 0x6e, 0x6f, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x6f, 0x75,
	0x74, 0x5f, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x6e, 0x6f, 0x12, 0x22, 0x0a, 0x0c,
	0x74, 0x6f, 0x74, 0x61, 0x6c, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0c, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74,
	0x12, 0x24, 0x0a, 0x0d, 0x72, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e,
	0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x72, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x5f,
	0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x24, 0x0a, 0x0d, 0x72, 0x65, 0x66, 0x75, 0x6e, 0x64,
	0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x72,
	0x65, 0x66, 0x75, 0x6e, 0x64, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x22, 0xdc, 0x02, 0x0a,
	0x15, 0x57, 0x65, 0x63, 0x68, 0x61, 0x74, 0x4a, 0x73, 0x41, 0x70, 0x69, 0x50, 0x61, 0x79, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x20, 0x0a, 0x0b, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69,
	0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x65, 0x73,
	0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x1e, 0x0a, 0x0a, 0x4f, 0x75, 0x74, 0x54,
	0x72, 0x61, 0x64, 0x65, 0x4e, 0x6f, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x6f, 0x75,
	0x74, 0x54, 0x72, 0x61, 0x64, 0x65, 0x4e, 0x6f, 0x12, 0x12, 0x0a, 0x04, 0x43, 0x65, 0x6e, 0x74,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x04, 0x63, 0x65, 0x6e, 0x74, 0x12, 0x16, 0x0a, 0x06,
	0x4f, 0x70, 0x65, 0x6e, 0x49, 0x44, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x6f, 0x70,
	0x65, 0x6e, 0x49, 0x44, 0x12, 0x1c, 0x0a, 0x09, 0x4e, 0x6f, 0x74, 0x69, 0x66, 0x79, 0x55, 0x72,
	0x6c, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x79, 0x55,
	0x72, 0x6c, 0x12, 0x14, 0x0a, 0x05, 0x41, 0x70, 0x70, 0x49, 0x44, 0x18, 0x06, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x05, 0x61, 0x70, 0x70, 0x49, 0x44, 0x12, 0x1a, 0x0a, 0x08, 0x43, 0x6c, 0x69, 0x65,
	0x6e, 0x74, 0x49, 0x50, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x43, 0x6c, 0x69, 0x65,
	0x6e, 0x74, 0x49, 0x50, 0x12, 0x1a, 0x0a, 0x08, 0x72, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x49, 0x64,
	0x18, 0x09, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x08, 0x72, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x49, 0x64,
	0x12, 0x16, 0x0a, 0x06, 0x64, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x06, 0x64, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x6c, 0x61, 0x74,
	0x66, 0x6f, 0x72, 0x6d, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x70, 0x6c, 0x61, 0x74,
	0x66, 0x6f, 0x72, 0x6d, 0x12, 0x14, 0x0a, 0x05, 0x73, 0x63, 0x65, 0x6e, 0x65, 0x18, 0x0c, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x05, 0x73, 0x63, 0x65, 0x6e, 0x65, 0x12, 0x1f, 0x0a, 0x0a, 0x74, 0x69,
	0x6d, 0x65, 0x45, 0x78, 0x70, 0x69, 0x72, 0x65, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0b,
	0x74, 0x69, 0x6d, 0x65, 0x5f, 0x65, 0x78, 0x70, 0x69, 0x72, 0x65, 0x22, 0xd4, 0x01, 0x0a, 0x16,
	0x57, 0x65, 0x63, 0x68, 0x61, 0x74, 0x4a, 0x73, 0x41, 0x70, 0x69, 0x50, 0x61, 0x79, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x41, 0x70, 0x70, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x61, 0x70, 0x70, 0x69, 0x64, 0x12, 0x1c, 0x0a, 0x09,
	0x54, 0x69, 0x6d, 0x65, 0x53, 0x74, 0x61, 0x6d, 0x70, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x09, 0x74, 0x69, 0x6d, 0x65, 0x53, 0x74, 0x61, 0x6d, 0x70, 0x12, 0x18, 0x0a, 0x07, 0x50, 0x61,
	0x63, 0x6b, 0x61, 0x67, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x70, 0x61, 0x63,
	0x6b, 0x61, 0x67, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x53, 0x69, 0x67, 0x6e, 0x54, 0x79, 0x70, 0x65,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x73, 0x69, 0x67, 0x6e, 0x54, 0x79, 0x70, 0x65,
	0x12, 0x1a, 0x0a, 0x08, 0x4e, 0x6f, 0x6e, 0x63, 0x65, 0x53, 0x74, 0x72, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x08, 0x6e, 0x6f, 0x6e, 0x63, 0x65, 0x53, 0x74, 0x72, 0x12, 0x18, 0x0a, 0x07,
	0x50, 0x61, 0x79, 0x53, 0x69, 0x67, 0x6e, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x70,
	0x61, 0x79, 0x53, 0x69, 0x67, 0x6e, 0x12, 0x1a, 0x0a, 0x08, 0x50, 0x72, 0x65, 0x70, 0x61, 0x79,
	0x49, 0x64, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x70, 0x72, 0x65, 0x70, 0x61, 0x79,
	0x49, 0x64, 0x22, 0x82, 0x02, 0x0a, 0x14, 0x57, 0x65, 0x63, 0x68, 0x61, 0x74, 0x41, 0x70, 0x70,
	0x50, 0x61, 0x79, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x41,
	0x70, 0x70, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x61, 0x70, 0x70, 0x69,
	0x64, 0x12, 0x1c, 0x0a, 0x09, 0x54, 0x69, 0x6d, 0x65, 0x53, 0x74, 0x61, 0x6d, 0x70, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x74, 0x69, 0x6d, 0x65, 0x53, 0x74, 0x61, 0x6d, 0x70, 0x12,
	0x18, 0x0a, 0x07, 0x50, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x07, 0x70, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x53, 0x69, 0x67,
	0x6e, 0x54, 0x79, 0x70, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x73, 0x69, 0x67,
	0x6e, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x4e, 0x6f, 0x6e, 0x63, 0x65, 0x53, 0x74,
	0x72, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x6e, 0x6f, 0x6e, 0x63, 0x65, 0x53, 0x74,
	0x72, 0x12, 0x18, 0x0a, 0x07, 0x50, 0x61, 0x79, 0x53, 0x69, 0x67, 0x6e, 0x18, 0x06, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x07, 0x70, 0x61, 0x79, 0x53, 0x69, 0x67, 0x6e, 0x12, 0x1a, 0x0a, 0x08, 0x50,
	0x72, 0x65, 0x70, 0x61, 0x79, 0x49, 0x64, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x70,
	0x72, 0x65, 0x70, 0x61, 0x79, 0x49, 0x64, 0x12, 0x1c, 0x0a, 0x09, 0x50, 0x61, 0x72, 0x74, 0x6e,
	0x65, 0x72, 0x49, 0x64, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x70, 0x61, 0x72, 0x74,
	0x6e, 0x65, 0x72, 0x49, 0x64, 0x12, 0x10, 0x0a, 0x03, 0x6d, 0x73, 0x67, 0x18, 0x09, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x03, 0x6d, 0x73, 0x67, 0x22, 0x45, 0x0a, 0x23, 0x57, 0x65, 0x63, 0x68, 0x61,
	0x74, 0x4a, 0x73, 0x41, 0x70, 0x69, 0x51, 0x75, 0x65, 0x72, 0x79, 0x42, 0x79, 0x4f, 0x75, 0x74,
	0x54, 0x72, 0x61, 0x64, 0x65, 0x4e, 0x6f, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1e,
	0x0a, 0x0a, 0x6f, 0x75, 0x74, 0x54, 0x72, 0x61, 0x64, 0x65, 0x4e, 0x6f, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0a, 0x6f, 0x75, 0x74, 0x54, 0x72, 0x61, 0x64, 0x65, 0x4e, 0x6f, 0x22, 0xca,
	0x02, 0x0a, 0x24, 0x57, 0x65, 0x63, 0x68, 0x61, 0x74, 0x4a, 0x73, 0x41, 0x70, 0x69, 0x51, 0x75,
	0x65, 0x72, 0x79, 0x42, 0x79, 0x4f, 0x75, 0x74, 0x54, 0x72, 0x61, 0x64, 0x65, 0x4e, 0x6f, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x61, 0x70, 0x70, 0x49, 0x44,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x61, 0x70, 0x70, 0x49, 0x44, 0x12, 0x15, 0x0a,
	0x05, 0x6d, 0x63, 0x68, 0x49, 0x44, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x6d, 0x63,
	0x68, 0x5f, 0x69, 0x64, 0x12, 0x20, 0x0a, 0x0a, 0x6f, 0x75, 0x74, 0x54, 0x72, 0x61, 0x64, 0x65,
	0x4e, 0x6f, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x6f, 0x75, 0x74, 0x5f, 0x74, 0x72,
	0x61, 0x64, 0x65, 0x5f, 0x6e, 0x6f, 0x12, 0x25, 0x0a, 0x0d, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61,
	0x63, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x74,
	0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x12, 0x1d, 0x0a,
	0x09, 0x74, 0x72, 0x61, 0x64, 0x65, 0x54, 0x79, 0x70, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0a, 0x74, 0x72, 0x61, 0x64, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x12, 0x1f, 0x0a, 0x0a,
	0x74, 0x72, 0x61, 0x64, 0x65, 0x53, 0x74, 0x61, 0x74, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0b, 0x74, 0x72, 0x61, 0x64, 0x65, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x65, 0x12, 0x1a, 0x0a,
	0x08, 0x62, 0x61, 0x6e, 0x6b, 0x54, 0x79, 0x70, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x08, 0x62, 0x61, 0x6e, 0x6b, 0x5f, 0x79, 0x70, 0x65, 0x12, 0x21, 0x0a, 0x0b, 0x73, 0x75, 0x63,
	0x63, 0x65, 0x73, 0x73, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c,
	0x73, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x12, 0x17, 0x0a, 0x06,
	0x6f, 0x70, 0x65, 0x6e, 0x49, 0x44, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6f, 0x70,
	0x65, 0x6e, 0x5f, 0x69, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x18, 0x0a,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x22, 0x43, 0x0a, 0x21, 0x57,
	0x65, 0x63, 0x68, 0x61, 0x74, 0x41, 0x70, 0x70, 0x51, 0x75, 0x65, 0x72, 0x79, 0x42, 0x79, 0x4f,
	0x75, 0x74, 0x54, 0x72, 0x61, 0x64, 0x65, 0x4e, 0x6f, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x12, 0x1e, 0x0a, 0x0a, 0x6f, 0x75, 0x74, 0x54, 0x72, 0x61, 0x64, 0x65, 0x4e, 0x6f, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x6f, 0x75, 0x74, 0x54, 0x72, 0x61, 0x64, 0x65, 0x4e, 0x6f,
	0x22, 0xcc, 0x08, 0x0a, 0x22, 0x57, 0x65, 0x63, 0x68, 0x61, 0x74, 0x41, 0x70, 0x70, 0x51, 0x75,
	0x65, 0x72, 0x79, 0x42, 0x79, 0x4f, 0x75, 0x74, 0x54, 0x72, 0x61, 0x64, 0x65, 0x4e, 0x6f, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x61, 0x70, 0x70, 0x49, 0x44,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x61, 0x70, 0x70, 0x49, 0x44, 0x12, 0x15, 0x0a,
	0x05, 0x6d, 0x63, 0x68, 0x49, 0x44, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x6d, 0x63,
	0x68, 0x5f, 0x69, 0x64, 0x12, 0x20, 0x0a, 0x0a, 0x6f, 0x75, 0x74, 0x54, 0x72, 0x61, 0x64, 0x65,
	0x4e, 0x6f, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x6f, 0x75, 0x74, 0x5f, 0x74, 0x72,
	0x61, 0x64, 0x65, 0x5f, 0x6e, 0x6f, 0x12, 0x25, 0x0a, 0x0d, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61,
	0x63, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x74,
	0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x12, 0x1d, 0x0a,
	0x09, 0x74, 0x72, 0x61, 0x64, 0x65, 0x54, 0x79, 0x70, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0a, 0x74, 0x72, 0x61, 0x64, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x12, 0x1f, 0x0a, 0x0a,
	0x74, 0x72, 0x61, 0x64, 0x65, 0x53, 0x74, 0x61, 0x74, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0b, 0x74, 0x72, 0x61, 0x64, 0x65, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x65, 0x12, 0x28, 0x0a,
	0x0e, 0x74, 0x72, 0x61, 0x64, 0x65, 0x53, 0x74, 0x61, 0x74, 0x65, 0x44, 0x65, 0x73, 0x63, 0x18,
	0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x74, 0x72, 0x61, 0x64, 0x65, 0x5f, 0x73, 0x74, 0x61,
	0x74, 0x65, 0x5f, 0x64, 0x65, 0x73, 0x63, 0x12, 0x1b, 0x0a, 0x08, 0x62, 0x61, 0x6e, 0x6b, 0x54,
	0x79, 0x70, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x62, 0x61, 0x6e, 0x6b, 0x5f,
	0x74, 0x79, 0x70, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x61, 0x74, 0x74, 0x61, 0x63, 0x68, 0x18, 0x0b,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x61, 0x74, 0x74, 0x61, 0x63, 0x68, 0x12, 0x21, 0x0a, 0x0b,
	0x73, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x0c, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0c, 0x73, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x12,
	0x66, 0x0a, 0x0f, 0x70, 0x72, 0x6f, 0x6d, 0x6f, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x65, 0x74, 0x61,
	0x69, 0x6c, 0x18, 0x0d, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x3b, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65,
	0x6e, 0x74, 0x2e, 0x57, 0x65, 0x63, 0x68, 0x61, 0x74, 0x41, 0x70, 0x70, 0x51, 0x75, 0x65, 0x72,
	0x79, 0x42, 0x79, 0x4f, 0x75, 0x74, 0x54, 0x72, 0x61, 0x64, 0x65, 0x4e, 0x6f, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x50, 0x72, 0x6f, 0x6d, 0x6f, 0x74, 0x69, 0x6f, 0x6e, 0x44,
	0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x10, 0x70, 0x72, 0x6f, 0x6d, 0x6f, 0x74, 0x69, 0x6f, 0x6e,
	0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x12, 0x47, 0x0a, 0x05, 0x70, 0x61, 0x79, 0x65, 0x72,
	0x18, 0x0e, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x31, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74,
	0x2e, 0x57, 0x65, 0x63, 0x68, 0x61, 0x74, 0x41, 0x70, 0x70, 0x51, 0x75, 0x65, 0x72, 0x79, 0x42,
	0x79, 0x4f, 0x75, 0x74, 0x54, 0x72, 0x61, 0x64, 0x65, 0x4e, 0x6f, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x2e, 0x50, 0x61, 0x79, 0x65, 0x72, 0x52, 0x05, 0x70, 0x61, 0x79, 0x65, 0x72,
	0x12, 0x4a, 0x0a, 0x06, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x32, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x57, 0x65, 0x63, 0x68, 0x61,
	0x74, 0x41, 0x70, 0x70, 0x51, 0x75, 0x65, 0x72, 0x79, 0x42, 0x79, 0x4f, 0x75, 0x74, 0x54, 0x72,
	0x61, 0x64, 0x65, 0x4e, 0x6f, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x41, 0x6d,
	0x6f, 0x75, 0x6e, 0x74, 0x52, 0x06, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x1a, 0x1f, 0x0a, 0x05,
	0x50, 0x61, 0x79, 0x65, 0x72, 0x12, 0x16, 0x0a, 0x06, 0x6f, 0x70, 0x65, 0x6e, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x6f, 0x70, 0x65, 0x6e, 0x69, 0x64, 0x1a, 0x82, 0x01,
	0x0a, 0x06, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x6f, 0x74, 0x61,
	0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x12, 0x1f,
	0x0a, 0x0a, 0x70, 0x61, 0x79, 0x65, 0x72, 0x54, 0x6f, 0x74, 0x61, 0x6c, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x0b, 0x70, 0x61, 0x79, 0x65, 0x72, 0x5f, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x12,
	0x1a, 0x0a, 0x08, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x63, 0x79, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x08, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x63, 0x79, 0x12, 0x25, 0x0a, 0x0d, 0x70,
	0x61, 0x79, 0x65, 0x72, 0x43, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x63, 0x79, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0e, 0x70, 0x61, 0x79, 0x65, 0x72, 0x5f, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e,
	0x63, 0x79, 0x1a, 0xca, 0x02, 0x0a, 0x0f, 0x50, 0x72, 0x6f, 0x6d, 0x6f, 0x74, 0x69, 0x6f, 0x6e,
	0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x12, 0x1b, 0x0a, 0x08, 0x63, 0x6f, 0x75, 0x70, 0x6f, 0x6e,
	0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x63, 0x6f, 0x75, 0x70, 0x6f, 0x6e,
	0x5f, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x73, 0x63, 0x6f, 0x70, 0x65,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x73, 0x63, 0x6f, 0x70, 0x65, 0x12, 0x12, 0x0a,
	0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x74, 0x79, 0x70,
	0x65, 0x12, 0x16, 0x0a, 0x06, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x06, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x19, 0x0a, 0x07, 0x73, 0x74, 0x6f,
	0x63, 0x6b, 0x49, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x73, 0x74, 0x6f, 0x63,
	0x6b, 0x5f, 0x69, 0x64, 0x12, 0x31, 0x0a, 0x13, 0x77, 0x65, 0x63, 0x68, 0x61, 0x74, 0x70, 0x61,
	0x79, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x14, 0x77, 0x65, 0x63, 0x68, 0x61, 0x74, 0x70, 0x61, 0x79, 0x5f, 0x63, 0x6f, 0x6e,
	0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x12, 0x2f, 0x0a, 0x12, 0x6d, 0x65, 0x72, 0x63, 0x68,
	0x61, 0x6e, 0x74, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x18, 0x08, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x13, 0x6d, 0x65, 0x72, 0x63, 0x68, 0x61, 0x6e, 0x74, 0x5f, 0x63, 0x6f,
	0x6e, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x12, 0x29, 0x0a, 0x0f, 0x6f, 0x74, 0x68, 0x65,
	0x72, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x10, 0x6f, 0x74, 0x68, 0x65, 0x72, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x72, 0x69, 0x62,
	0x75, 0x74, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x63, 0x79, 0x18,
	0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x63, 0x79, 0x22,
	0x3b, 0x0a, 0x19, 0x47, 0x65, 0x74, 0x50, 0x61, 0x79, 0x42, 0x79, 0x4f, 0x75, 0x74, 0x54, 0x72,
	0x61, 0x64, 0x65, 0x4e, 0x6f, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1e, 0x0a, 0x0a,
	0x4f, 0x75, 0x74, 0x54, 0x72, 0x61, 0x64, 0x65, 0x4e, 0x6f, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0a, 0x4f, 0x75, 0x74, 0x54, 0x72, 0x61, 0x64, 0x65, 0x4e, 0x6f, 0x22, 0xb6, 0x01, 0x0a,
	0x1a, 0x47, 0x65, 0x74, 0x50, 0x61, 0x79, 0x42, 0x79, 0x4f, 0x75, 0x74, 0x54, 0x72, 0x61, 0x64,
	0x65, 0x4e, 0x6f, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x1e, 0x0a, 0x0a, 0x6f,
	0x75, 0x74, 0x54, 0x72, 0x61, 0x64, 0x65, 0x4e, 0x6f, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0a, 0x6f, 0x75, 0x74, 0x54, 0x72, 0x61, 0x64, 0x65, 0x4e, 0x6f, 0x12, 0x1a, 0x0a, 0x08, 0x72,
	0x65, 0x63, 0x6f, 0x72, 0x64, 0x49, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x08, 0x72,
	0x65, 0x63, 0x6f, 0x72, 0x64, 0x49, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x65, 0x6e, 0x74, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x63, 0x65, 0x6e, 0x74, 0x12, 0x1a, 0x0a, 0x08, 0x70,
	0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x70,
	0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x12, 0x16, 0x0a, 0x06, 0x64, 0x6f, 0x6d, 0x61, 0x69,
	0x6e, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x64, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x12,
	0x14, 0x0a, 0x05, 0x73, 0x63, 0x65, 0x6e, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05,
	0x73, 0x63, 0x65, 0x6e, 0x65, 0x22, 0x92, 0x02, 0x0a, 0x1a, 0x57, 0x65, 0x63, 0x68, 0x61, 0x74,
	0x4a, 0x73, 0x41, 0x70, 0x69, 0x52, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x73, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x52, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x49, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x52, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x49, 0x64,
	0x12, 0x20, 0x0a, 0x0b, 0x4f, 0x75, 0x74, 0x52, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x4e, 0x6f, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x4f, 0x75, 0x74, 0x52, 0x65, 0x66, 0x75, 0x6e, 0x64,
	0x4e, 0x6f, 0x12, 0x24, 0x0a, 0x0d, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x49, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x54, 0x72, 0x61, 0x6e, 0x73,
	0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x12, 0x1e, 0x0a, 0x0a, 0x4f, 0x75, 0x74, 0x54,
	0x72, 0x61, 0x64, 0x65, 0x4e, 0x6f, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x4f, 0x75,
	0x74, 0x54, 0x72, 0x61, 0x64, 0x65, 0x4e, 0x6f, 0x12, 0x20, 0x0a, 0x0b, 0x53, 0x75, 0x63, 0x63,
	0x65, 0x73, 0x73, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x53,
	0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x1e, 0x0a, 0x0a, 0x43, 0x72,
	0x65, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a,
	0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x53, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x53, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x12, 0x16, 0x0a, 0x06, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x08, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x06, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x22, 0xc5, 0x02, 0x0a, 0x16, 0x57,
	0x65, 0x63, 0x68, 0x61, 0x74, 0x4e, 0x61, 0x74, 0x69, 0x76, 0x65, 0x50, 0x61, 0x79, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x20, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70,
	0x74, 0x69, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63,
	0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x1e, 0x0a, 0x0a, 0x6f, 0x75, 0x74, 0x54, 0x72,
	0x61, 0x64, 0x65, 0x4e, 0x6f, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x6f, 0x75, 0x74,
	0x54, 0x72, 0x61, 0x64, 0x65, 0x4e, 0x6f, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x65, 0x6e, 0x74, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x63, 0x65, 0x6e, 0x74, 0x12, 0x1c, 0x0a, 0x09, 0x6e,
	0x6f, 0x74, 0x69, 0x66, 0x79, 0x55, 0x72, 0x6c, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09,
	0x6e, 0x6f, 0x74, 0x69, 0x66, 0x79, 0x55, 0x72, 0x6c, 0x12, 0x14, 0x0a, 0x05, 0x61, 0x70, 0x70,
	0x49, 0x44, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x61, 0x70, 0x70, 0x49, 0x44, 0x12,
	0x1a, 0x0a, 0x08, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x49, 0x50, 0x18, 0x07, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x08, 0x43, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x49, 0x50, 0x12, 0x1a, 0x0a, 0x08, 0x72,
	0x65, 0x63, 0x6f, 0x72, 0x64, 0x49, 0x64, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x08, 0x72,
	0x65, 0x63, 0x6f, 0x72, 0x64, 0x49, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x64, 0x6f, 0x6d, 0x61, 0x69,
	0x6e, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x64, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x12,
	0x1a, 0x0a, 0x08, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x18, 0x0a, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x08, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x12, 0x14, 0x0a, 0x05, 0x73,
	0x63, 0x65, 0x6e, 0x65, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x73, 0x63, 0x65, 0x6e,
	0x65, 0x12, 0x1f, 0x0a, 0x0a, 0x74, 0x69, 0x6d, 0x65, 0x45, 0x78, 0x70, 0x69, 0x72, 0x65, 0x18,
	0x0c, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0b, 0x74, 0x69, 0x6d, 0x65, 0x5f, 0x65, 0x78, 0x70, 0x69,
	0x72, 0x65, 0x22, 0x34, 0x0a, 0x17, 0x57, 0x65, 0x63, 0x68, 0x61, 0x74, 0x4e, 0x61, 0x74, 0x69,
	0x76, 0x65, 0x50, 0x61, 0x79, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x19, 0x0a,
	0x07, 0x63, 0x6f, 0x64, 0x65, 0x55, 0x72, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08,
	0x63, 0x6f, 0x64, 0x65, 0x5f, 0x75, 0x72, 0x6c, 0x22, 0x46, 0x0a, 0x24, 0x57, 0x65, 0x63, 0x68,
	0x61, 0x74, 0x4e, 0x61, 0x74, 0x69, 0x76, 0x65, 0x51, 0x75, 0x65, 0x72, 0x79, 0x42, 0x79, 0x4f,
	0x75, 0x74, 0x54, 0x72, 0x61, 0x64, 0x65, 0x4e, 0x6f, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x12, 0x1e, 0x0a, 0x0a, 0x6f, 0x75, 0x74, 0x54, 0x72, 0x61, 0x64, 0x65, 0x4e, 0x6f, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x6f, 0x75, 0x74, 0x54, 0x72, 0x61, 0x64, 0x65, 0x4e, 0x6f,
	0x22, 0x4b, 0x0a, 0x25, 0x57, 0x65, 0x63, 0x68, 0x61, 0x74, 0x52, 0x65, 0x66, 0x75, 0x6e, 0x64,
	0x51, 0x75, 0x65, 0x72, 0x79, 0x42, 0x79, 0x4f, 0x75, 0x74, 0x52, 0x65, 0x66, 0x75, 0x6e, 0x64,
	0x4e, 0x6f, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x22, 0x0a, 0x0b, 0x6f, 0x75, 0x74,
	0x52, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x4e, 0x6f, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d,
	0x6f, 0x75, 0x74, 0x5f, 0x72, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x5f, 0x6e, 0x6f, 0x22, 0xf2, 0x02,
	0x0a, 0x26, 0x57, 0x65, 0x63, 0x68, 0x61, 0x74, 0x52, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x51, 0x75,
	0x65, 0x72, 0x79, 0x42, 0x79, 0x4f, 0x75, 0x74, 0x52, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x4e, 0x6f,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x1b, 0x0a, 0x08, 0x72, 0x65, 0x66, 0x75,
	0x6e, 0x64, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x72, 0x65, 0x66, 0x75,
	0x6e, 0x64, 0x5f, 0x69, 0x64, 0x12, 0x22, 0x0a, 0x0b, 0x6f, 0x75, 0x74, 0x52, 0x65, 0x66, 0x75,
	0x6e, 0x64, 0x4e, 0x6f, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x6f, 0x75, 0x74, 0x5f,
	0x72, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x5f, 0x6e, 0x6f, 0x12, 0x25, 0x0a, 0x0d, 0x74, 0x72, 0x61,
	0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0e, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64,
	0x12, 0x20, 0x0a, 0x0a, 0x6f, 0x75, 0x74, 0x54, 0x72, 0x61, 0x64, 0x65, 0x4e, 0x6f, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x6f, 0x75, 0x74, 0x5f, 0x74, 0x72, 0x61, 0x64, 0x65, 0x5f,
	0x6e, 0x6f, 0x12, 0x18, 0x0a, 0x07, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x07, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x12, 0x32, 0x0a, 0x13,
	0x75, 0x73, 0x65, 0x72, 0x52, 0x65, 0x63, 0x65, 0x69, 0x76, 0x65, 0x64, 0x41, 0x63, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x15, 0x75, 0x73, 0x65, 0x72, 0x5f,
	0x72, 0x65, 0x63, 0x65, 0x69, 0x76, 0x65, 0x64, 0x5f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x12, 0x21, 0x0a, 0x0b, 0x73, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x54, 0x69, 0x6d, 0x65, 0x18,
	0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x73, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x5f, 0x74,
	0x69, 0x6d, 0x65, 0x12, 0x1f, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d,
	0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x5f,
	0x74, 0x69, 0x6d, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x09,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x14, 0x0a, 0x05,
	0x74, 0x6f, 0x74, 0x61, 0x6c, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x74, 0x6f, 0x74,
	0x61, 0x6c, 0x22, 0xcb, 0x02, 0x0a, 0x25, 0x57, 0x65, 0x63, 0x68, 0x61, 0x74, 0x4e, 0x61, 0x74,
	0x69, 0x76, 0x65, 0x51, 0x75, 0x65, 0x72, 0x79, 0x42, 0x79, 0x4f, 0x75, 0x74, 0x54, 0x72, 0x61,
	0x64, 0x65, 0x4e, 0x6f, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x14, 0x0a, 0x05,
	0x61, 0x70, 0x70, 0x49, 0x44, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x61, 0x70, 0x70,
	0x49, 0x44, 0x12, 0x15, 0x0a, 0x05, 0x6d, 0x63, 0x68, 0x49, 0x44, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x06, 0x6d, 0x63, 0x68, 0x5f, 0x69, 0x64, 0x12, 0x20, 0x0a, 0x0a, 0x6f, 0x75, 0x74,
	0x54, 0x72, 0x61, 0x64, 0x65, 0x4e, 0x6f, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x6f,
	0x75, 0x74, 0x5f, 0x74, 0x72, 0x61, 0x64, 0x65, 0x5f, 0x6e, 0x6f, 0x12, 0x25, 0x0a, 0x0d, 0x74,
	0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0e, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f,
	0x69, 0x64, 0x12, 0x1d, 0x0a, 0x09, 0x74, 0x72, 0x61, 0x64, 0x65, 0x54, 0x79, 0x70, 0x65, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x74, 0x72, 0x61, 0x64, 0x65, 0x5f, 0x74, 0x79, 0x70,
	0x65, 0x12, 0x1f, 0x0a, 0x0a, 0x74, 0x72, 0x61, 0x64, 0x65, 0x53, 0x74, 0x61, 0x74, 0x65, 0x18,
	0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x74, 0x72, 0x61, 0x64, 0x65, 0x5f, 0x73, 0x74, 0x61,
	0x74, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x62, 0x61, 0x6e, 0x6b, 0x54, 0x79, 0x70, 0x65, 0x18, 0x07,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x62, 0x61, 0x6e, 0x6b, 0x5f, 0x79, 0x70, 0x65, 0x12, 0x21,
	0x0a, 0x0b, 0x73, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x08, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0c, 0x73, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x5f, 0x74, 0x69, 0x6d,
	0x65, 0x12, 0x17, 0x0a, 0x06, 0x6f, 0x70, 0x65, 0x6e, 0x49, 0x44, 0x18, 0x09, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x07, 0x6f, 0x70, 0x65, 0x6e, 0x5f, 0x69, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x6f,
	0x74, 0x61, 0x6c, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c,
	0x22, 0xda, 0x02, 0x0a, 0x13, 0x57, 0x65, 0x63, 0x68, 0x61, 0x74, 0x41, 0x70, 0x70, 0x50, 0x61,
	0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x20, 0x0a, 0x0b, 0x44, 0x65, 0x73, 0x63,
	0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64,
	0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x1e, 0x0a, 0x0a, 0x4f, 0x75,
	0x74, 0x54, 0x72, 0x61, 0x64, 0x65, 0x4e, 0x6f, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a,
	0x6f, 0x75, 0x74, 0x54, 0x72, 0x61, 0x64, 0x65, 0x4e, 0x6f, 0x12, 0x12, 0x0a, 0x04, 0x43, 0x65,
	0x6e, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x04, 0x63, 0x65, 0x6e, 0x74, 0x12, 0x16,
	0x0a, 0x06, 0x4f, 0x70, 0x65, 0x6e, 0x49, 0x44, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06,
	0x6f, 0x70, 0x65, 0x6e, 0x49, 0x44, 0x12, 0x1c, 0x0a, 0x09, 0x4e, 0x6f, 0x74, 0x69, 0x66, 0x79,
	0x55, 0x72, 0x6c, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x6e, 0x6f, 0x74, 0x69, 0x66,
	0x79, 0x55, 0x72, 0x6c, 0x12, 0x14, 0x0a, 0x05, 0x41, 0x70, 0x70, 0x49, 0x44, 0x18, 0x06, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x05, 0x61, 0x70, 0x70, 0x49, 0x44, 0x12, 0x1a, 0x0a, 0x08, 0x43, 0x6c,
	0x69, 0x65, 0x6e, 0x74, 0x49, 0x50, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x43, 0x6c,
	0x69, 0x65, 0x6e, 0x74, 0x49, 0x50, 0x12, 0x1a, 0x0a, 0x08, 0x72, 0x65, 0x63, 0x6f, 0x72, 0x64,
	0x49, 0x64, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x08, 0x72, 0x65, 0x63, 0x6f, 0x72, 0x64,
	0x49, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x64, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x18, 0x0a, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x06, 0x64, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x6c,
	0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x70, 0x6c,
	0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x12, 0x14, 0x0a, 0x05, 0x73, 0x63, 0x65, 0x6e, 0x65, 0x18,
	0x0c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x73, 0x63, 0x65, 0x6e, 0x65, 0x12, 0x1f, 0x0a, 0x0a,
	0x74, 0x69, 0x6d, 0x65, 0x45, 0x78, 0x70, 0x69, 0x72, 0x65, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x0b, 0x74, 0x69, 0x6d, 0x65, 0x5f, 0x65, 0x78, 0x70, 0x69, 0x72, 0x65, 0x22, 0xd5, 0x01,
	0x0a, 0x19, 0x57, 0x65, 0x63, 0x68, 0x61, 0x74, 0x4a, 0x73, 0x41, 0x70, 0x69, 0x52, 0x65, 0x66,
	0x75, 0x6e, 0x64, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x16, 0x0a, 0x06, 0x52,
	0x65, 0x61, 0x73, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x52, 0x65, 0x61,
	0x73, 0x6f, 0x6e, 0x12, 0x1e, 0x0a, 0x0a, 0x4f, 0x75, 0x74, 0x54, 0x72, 0x61, 0x64, 0x65, 0x4e,
	0x6f, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x4f, 0x75, 0x74, 0x54, 0x72, 0x61, 0x64,
	0x65, 0x4e, 0x6f, 0x12, 0x20, 0x0a, 0x0b, 0x4f, 0x75, 0x74, 0x52, 0x65, 0x66, 0x75, 0x6e, 0x64,
	0x4e, 0x6f, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x4f, 0x75, 0x74, 0x52, 0x65, 0x66,
	0x75, 0x6e, 0x64, 0x4e, 0x6f, 0x12, 0x1c, 0x0a, 0x09, 0x4e, 0x6f, 0x74, 0x69, 0x66, 0x79, 0x55,
	0x72, 0x6c, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x4e, 0x6f, 0x74, 0x69, 0x66, 0x79,
	0x55, 0x72, 0x6c, 0x12, 0x12, 0x0a, 0x04, 0x43, 0x65, 0x6e, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x04, 0x43, 0x65, 0x6e, 0x74, 0x12, 0x16, 0x0a, 0x06, 0x4f, 0x70, 0x65, 0x6e, 0x49,
	0x44, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x4f, 0x70, 0x65, 0x6e, 0x49, 0x44, 0x12,
	0x14, 0x0a, 0x05, 0x54, 0x6f, 0x74, 0x61, 0x6c, 0x18, 0x07, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05,
	0x54, 0x6f, 0x74, 0x61, 0x6c, 0x22, 0xd9, 0x02, 0x0a, 0x12, 0x57, 0x65, 0x63, 0x68, 0x61, 0x74,
	0x48, 0x35, 0x50, 0x61, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x20, 0x0a, 0x0b,
	0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x1e,
	0x0a, 0x0a, 0x4f, 0x75, 0x74, 0x54, 0x72, 0x61, 0x64, 0x65, 0x4e, 0x6f, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0a, 0x6f, 0x75, 0x74, 0x54, 0x72, 0x61, 0x64, 0x65, 0x4e, 0x6f, 0x12, 0x12,
	0x0a, 0x04, 0x43, 0x65, 0x6e, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x04, 0x63, 0x65,
	0x6e, 0x74, 0x12, 0x16, 0x0a, 0x06, 0x4f, 0x70, 0x65, 0x6e, 0x49, 0x44, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x06, 0x6f, 0x70, 0x65, 0x6e, 0x49, 0x44, 0x12, 0x1c, 0x0a, 0x09, 0x4e, 0x6f,
	0x74, 0x69, 0x66, 0x79, 0x55, 0x72, 0x6c, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x6e,
	0x6f, 0x74, 0x69, 0x66, 0x79, 0x55, 0x72, 0x6c, 0x12, 0x14, 0x0a, 0x05, 0x41, 0x70, 0x70, 0x49,
	0x44, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x61, 0x70, 0x70, 0x49, 0x44, 0x12, 0x1a,
	0x0a, 0x08, 0x43, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x49, 0x50, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x08, 0x43, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x49, 0x50, 0x12, 0x1a, 0x0a, 0x08, 0x72, 0x65,
	0x63, 0x6f, 0x72, 0x64, 0x49, 0x64, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x08, 0x72, 0x65,
	0x63, 0x6f, 0x72, 0x64, 0x49, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x64, 0x6f, 0x6d, 0x61, 0x69, 0x6e,
	0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x64, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x12, 0x1a,
	0x0a, 0x08, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x08, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x12, 0x14, 0x0a, 0x05, 0x73, 0x63,
	0x65, 0x6e, 0x65, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x73, 0x63, 0x65, 0x6e, 0x65,
	0x12, 0x1f, 0x0a, 0x0a, 0x74, 0x69, 0x6d, 0x65, 0x45, 0x78, 0x70, 0x69, 0x72, 0x65, 0x18, 0x0d,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x0b, 0x74, 0x69, 0x6d, 0x65, 0x5f, 0x65, 0x78, 0x70, 0x69, 0x72,
	0x65, 0x22, 0x2c, 0x0a, 0x13, 0x57, 0x65, 0x63, 0x68, 0x61, 0x74, 0x48, 0x35, 0x50, 0x61, 0x79,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x15, 0x0a, 0x05, 0x68, 0x35, 0x55, 0x72,
	0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x68, 0x35, 0x5f, 0x75, 0x72, 0x6c, 0x22,
	0x42, 0x0a, 0x20, 0x57, 0x65, 0x63, 0x68, 0x61, 0x74, 0x48, 0x35, 0x51, 0x75, 0x65, 0x72, 0x79,
	0x42, 0x79, 0x4f, 0x75, 0x74, 0x54, 0x72, 0x61, 0x64, 0x65, 0x4e, 0x6f, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x12, 0x1e, 0x0a, 0x0a, 0x6f, 0x75, 0x74, 0x54, 0x72, 0x61, 0x64, 0x65, 0x4e,
	0x6f, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x6f, 0x75, 0x74, 0x54, 0x72, 0x61, 0x64,
	0x65, 0x4e, 0x6f, 0x22, 0xf1, 0x02, 0x0a, 0x21, 0x57, 0x65, 0x63, 0x68, 0x61, 0x74, 0x48, 0x35,
	0x51, 0x75, 0x65, 0x72, 0x79, 0x42, 0x79, 0x4f, 0x75, 0x74, 0x54, 0x72, 0x61, 0x64, 0x65, 0x4e,
	0x6f, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x61, 0x70, 0x70,
	0x49, 0x44, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x61, 0x70, 0x70, 0x49, 0x44, 0x12,
	0x15, 0x0a, 0x05, 0x6d, 0x63, 0x68, 0x49, 0x44, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06,
	0x6d, 0x63, 0x68, 0x5f, 0x69, 0x64, 0x12, 0x20, 0x0a, 0x0a, 0x6f, 0x75, 0x74, 0x54, 0x72, 0x61,
	0x64, 0x65, 0x4e, 0x6f, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x6f, 0x75, 0x74, 0x5f,
	0x74, 0x72, 0x61, 0x64, 0x65, 0x5f, 0x6e, 0x6f, 0x12, 0x25, 0x0a, 0x0d, 0x74, 0x72, 0x61, 0x6e,
	0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0e, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x12,
	0x1d, 0x0a, 0x09, 0x74, 0x72, 0x61, 0x64, 0x65, 0x54, 0x79, 0x70, 0x65, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0a, 0x74, 0x72, 0x61, 0x64, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x12, 0x1f,
	0x0a, 0x0a, 0x74, 0x72, 0x61, 0x64, 0x65, 0x53, 0x74, 0x61, 0x74, 0x65, 0x18, 0x06, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0b, 0x74, 0x72, 0x61, 0x64, 0x65, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x65, 0x12,
	0x28, 0x0a, 0x0e, 0x74, 0x72, 0x61, 0x64, 0x65, 0x53, 0x74, 0x61, 0x74, 0x65, 0x44, 0x65, 0x73,
	0x63, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x74, 0x72, 0x61, 0x64, 0x65, 0x5f, 0x73,
	0x74, 0x61, 0x74, 0x65, 0x5f, 0x64, 0x65, 0x73, 0x63, 0x12, 0x1a, 0x0a, 0x08, 0x62, 0x61, 0x6e,
	0x6b, 0x54, 0x79, 0x70, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x62, 0x61, 0x6e,
	0x6b, 0x5f, 0x79, 0x70, 0x65, 0x12, 0x21, 0x0a, 0x0b, 0x73, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73,
	0x54, 0x69, 0x6d, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x73, 0x75, 0x63, 0x63,
	0x65, 0x73, 0x73, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x12, 0x17, 0x0a, 0x06, 0x6f, 0x70, 0x65, 0x6e,
	0x49, 0x44, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6f, 0x70, 0x65, 0x6e, 0x5f, 0x69,
	0x64, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x22, 0x48, 0x0a, 0x12, 0x57, 0x65, 0x63, 0x68, 0x61,
	0x74, 0x50, 0x61, 0x79, 0x4f, 0x6b, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1e, 0x0a,
	0x0a, 0x6f, 0x75, 0x74, 0x54, 0x72, 0x61, 0x64, 0x65, 0x4e, 0x6f, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0a, 0x6f, 0x75, 0x74, 0x54, 0x72, 0x61, 0x64, 0x65, 0x4e, 0x6f, 0x12, 0x12, 0x0a,
	0x04, 0x62, 0x6f, 0x64, 0x79, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x62, 0x6f, 0x64,
	0x79, 0x22, 0x3a, 0x0a, 0x0e, 0x43, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x53, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x73, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x12, 0x0e, 0x0a,
	0x02, 0x49, 0x44, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x02, 0x49, 0x44, 0x22, 0x4b, 0x0a,
	0x0f, 0x50, 0x61, 0x79, 0x51, 0x75, 0x65, 0x72, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x12, 0x18, 0x0a, 0x07, 0x70, 0x61, 0x79, 0x54, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x07, 0x70, 0x61, 0x79, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1e, 0x0a, 0x0a, 0x6f, 0x75,
	0x74, 0x54, 0x72, 0x61, 0x64, 0x65, 0x4e, 0x6f, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a,
	0x6f, 0x75, 0x74, 0x54, 0x72, 0x61, 0x64, 0x65, 0x4e, 0x6f, 0x22, 0x43, 0x0a, 0x10, 0x50, 0x61,
	0x79, 0x51, 0x75, 0x65, 0x72, 0x79, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x2f,
	0x0a, 0x05, 0x69, 0x6e, 0x66, 0x6f, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x19, 0x2e,
	0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x4f,
	0x72, 0x64, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x05, 0x69, 0x6e, 0x66, 0x6f, 0x73, 0x22,
	0xbe, 0x05, 0x0a, 0x10, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x4f, 0x72, 0x64, 0x65, 0x72,
	0x49, 0x6e, 0x66, 0x6f, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x02, 0x69, 0x64, 0x12, 0x1c, 0x0a, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41,
	0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64,
	0x41, 0x74, 0x12, 0x1c, 0x0a, 0x09, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74,
	0x12, 0x1e, 0x0a, 0x0a, 0x6f, 0x75, 0x74, 0x54, 0x72, 0x61, 0x64, 0x65, 0x4e, 0x6f, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x6f, 0x75, 0x74, 0x54, 0x72, 0x61, 0x64, 0x65, 0x4e, 0x6f,
	0x12, 0x26, 0x0a, 0x0e, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x54, 0x72, 0x61, 0x64, 0x65,
	0x4e, 0x6f, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65,
	0x6c, 0x54, 0x72, 0x61, 0x64, 0x65, 0x4e, 0x6f, 0x12, 0x16, 0x0a, 0x06, 0x61, 0x6d, 0x6f, 0x75,
	0x6e, 0x74, 0x18, 0x06, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74,
	0x12, 0x1a, 0x0a, 0x08, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x63, 0x79, 0x18, 0x07, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x08, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x63, 0x79, 0x12, 0x16, 0x0a, 0x06,
	0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x73, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x12, 0x18, 0x0a, 0x07, 0x70, 0x61, 0x79, 0x54, 0x69, 0x6d, 0x65, 0x18,
	0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x70, 0x61, 0x79, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x1e,
	0x0a, 0x0a, 0x72, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x0a, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0a, 0x72, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x14,
	0x0a, 0x05, 0x70, 0x61, 0x79, 0x65, 0x65, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x70,
	0x61, 0x79, 0x65, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x54,
	0x79, 0x70, 0x65, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x63, 0x68, 0x61, 0x6e, 0x6e,
	0x65, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f,
	0x72, 0x6d, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f,
	0x72, 0x6d, 0x12, 0x16, 0x0a, 0x06, 0x64, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x18, 0x0e, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x06, 0x64, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x12, 0x22, 0x0a, 0x0c, 0x62, 0x75,
	0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x54, 0x79, 0x70, 0x65, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0c, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x54, 0x79, 0x70, 0x65, 0x12, 0x14,
	0x0a, 0x05, 0x6c, 0x6f, 0x67, 0x49, 0x64, 0x18, 0x10, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x6c,
	0x6f, 0x67, 0x49, 0x64, 0x12, 0x26, 0x0a, 0x0e, 0x63, 0x68, 0x65, 0x63, 0x6b, 0x53, 0x65, 0x73,
	0x73, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x18, 0x11, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x63, 0x68,
	0x65, 0x63, 0x6b, 0x53, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x12, 0x20, 0x0a, 0x0b,
	0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x55, 0x55, 0x49, 0x44, 0x18, 0x12, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0b, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x55, 0x55, 0x49, 0x44, 0x12, 0x20,
	0x0a, 0x0b, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x13, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0b, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x4e, 0x61, 0x6d, 0x65,
	0x12, 0x1e, 0x0a, 0x0a, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x49, 0x6d, 0x67, 0x18, 0x14,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x49, 0x6d, 0x67,
	0x12, 0x2e, 0x0a, 0x12, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x44, 0x65, 0x73, 0x63, 0x72,
	0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x15, 0x20, 0x01, 0x28, 0x09, 0x52, 0x12, 0x70, 0x72,
	0x6f, 0x64, 0x75, 0x63, 0x74, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e,
	0x12, 0x10, 0x0a, 0x03, 0x66, 0x65, 0x65, 0x18, 0x16, 0x20, 0x01, 0x28, 0x03, 0x52, 0x03, 0x66,
	0x65, 0x65, 0x12, 0x1c, 0x0a, 0x09, 0x6e, 0x65, 0x74, 0x49, 0x6e, 0x63, 0x6f, 0x6d, 0x65, 0x18,
	0x17, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x6e, 0x65, 0x74, 0x49, 0x6e, 0x63, 0x6f, 0x6d, 0x65,
	0x22, 0xe8, 0x01, 0x0a, 0x10, 0x45, 0x78, 0x70, 0x6f, 0x72, 0x74, 0x50, 0x61, 0x79, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1c, 0x0a, 0x09, 0x73, 0x74, 0x61, 0x72, 0x74, 0x54, 0x69,
	0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x73, 0x74, 0x61, 0x72, 0x74, 0x54,
	0x69, 0x6d, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x65, 0x6e, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x65, 0x6e, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x1e, 0x0a,
	0x0a, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x54, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0a, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x26, 0x0a,
	0x0e, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x4e, 0x75, 0x6d, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x52, 0x61, 0x6e,
	0x67, 0x65, 0x4e, 0x75, 0x6d, 0x12, 0x20, 0x0a, 0x0b, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c,
	0x54, 0x79, 0x70, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x63, 0x68, 0x61, 0x6e,
	0x6e, 0x65, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x63, 0x75, 0x72, 0x72, 0x65,
	0x6e, 0x63, 0x79, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x63, 0x75, 0x72, 0x72, 0x65,
	0x6e, 0x63, 0x79, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x6f, 0x72, 0x74, 0x42, 0x79, 0x18, 0x07, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x06, 0x73, 0x6f, 0x72, 0x74, 0x42, 0x79, 0x22, 0xb5, 0x02, 0x0a, 0x11,
	0x45, 0x78, 0x70, 0x6f, 0x72, 0x74, 0x50, 0x61, 0x79, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x12, 0x1e, 0x0a, 0x0a, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x54, 0x79, 0x70, 0x65, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x54, 0x79, 0x70,
	0x65, 0x12, 0x1a, 0x0a, 0x08, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x4e, 0x75, 0x6d, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x08, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x4e, 0x75, 0x6d, 0x12, 0x2d, 0x0a,
	0x08, 0x6f, 0x76, 0x65, 0x72, 0x76, 0x69, 0x65, 0x77, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x11, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x4f, 0x76, 0x65, 0x72, 0x76, 0x69,
	0x65, 0x77, 0x52, 0x08, 0x6f, 0x76, 0x65, 0x72, 0x76, 0x69, 0x65, 0x77, 0x12, 0x3e, 0x0a, 0x0e,
	0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x49, 0x6e, 0x63, 0x6f, 0x6d, 0x65, 0x73, 0x18, 0x04,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x43,
	0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x49, 0x6e, 0x63, 0x6f, 0x6d, 0x65, 0x52, 0x0e, 0x63, 0x68,
	0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x49, 0x6e, 0x63, 0x6f, 0x6d, 0x65, 0x73, 0x12, 0x3b, 0x0a, 0x0d,
	0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x49, 0x6e, 0x66, 0x6f, 0x73, 0x18, 0x05, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x42, 0x75,
	0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x0d, 0x62, 0x75, 0x73, 0x69,
	0x6e, 0x65, 0x73, 0x73, 0x49, 0x6e, 0x66, 0x6f, 0x73, 0x12, 0x38, 0x0a, 0x0c, 0x6f, 0x72, 0x64,
	0x65, 0x72, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x18, 0x06, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x14, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x44,
	0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x0c, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x44, 0x65, 0x74, 0x61,
	0x69, 0x6c, 0x73, 0x22, 0xa1, 0x05, 0x0a, 0x0b, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x44, 0x65, 0x74,
	0x61, 0x69, 0x6c, 0x12, 0x26, 0x0a, 0x0e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x4f, 0x72,
	0x64, 0x65, 0x72, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0e, 0x70, 0x61, 0x79,
	0x6d, 0x65, 0x6e, 0x74, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x49, 0x64, 0x12, 0x1c, 0x0a, 0x09, 0x63,
	0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09,
	0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x1c, 0x0a, 0x09, 0x75, 0x70, 0x64,
	0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x75, 0x70,
	0x64, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x1e, 0x0a, 0x0a, 0x6f, 0x75, 0x74, 0x54, 0x72,
	0x61, 0x64, 0x65, 0x4e, 0x6f, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x6f, 0x75, 0x74,
	0x54, 0x72, 0x61, 0x64, 0x65, 0x4e, 0x6f, 0x12, 0x26, 0x0a, 0x0e, 0x63, 0x68, 0x61, 0x6e, 0x6e,
	0x65, 0x6c, 0x54, 0x72, 0x61, 0x64, 0x65, 0x4e, 0x6f, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0e, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x54, 0x72, 0x61, 0x64, 0x65, 0x4e, 0x6f, 0x12,
	0x1a, 0x0a, 0x08, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x63, 0x79, 0x18, 0x06, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x08, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x63, 0x79, 0x12, 0x1c, 0x0a, 0x09, 0x70,
	0x61, 0x79, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x07, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09,
	0x70, 0x61, 0x79, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x10, 0x0a, 0x03, 0x66, 0x65, 0x65,
	0x18, 0x08, 0x20, 0x01, 0x28, 0x03, 0x52, 0x03, 0x66, 0x65, 0x65, 0x12, 0x1c, 0x0a, 0x09, 0x6e,
	0x65, 0x74, 0x49, 0x6e, 0x63, 0x6f, 0x6d, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09,
	0x6e, 0x65, 0x74, 0x49, 0x6e, 0x63, 0x6f, 0x6d, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x12, 0x18, 0x0a, 0x07, 0x70, 0x61, 0x79, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x0b, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x07, 0x70, 0x61, 0x79, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x63,
	0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0b, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1a, 0x0a,
	0x08, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x08, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x12, 0x16, 0x0a, 0x06, 0x64, 0x6f, 0x6d,
	0x61, 0x69, 0x6e, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x64, 0x6f, 0x6d, 0x61, 0x69,
	0x6e, 0x12, 0x22, 0x0a, 0x0c, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x54, 0x79, 0x70,
	0x65, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73,
	0x73, 0x54, 0x79, 0x70, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x6c, 0x6f, 0x67, 0x49, 0x64, 0x18, 0x10,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x6c, 0x6f, 0x67, 0x49, 0x64, 0x12, 0x26, 0x0a, 0x0e, 0x63,
	0x68, 0x65, 0x63, 0x6b, 0x53, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x18, 0x11, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0e, 0x63, 0x68, 0x65, 0x63, 0x6b, 0x53, 0x65, 0x73, 0x73, 0x69, 0x6f,
	0x6e, 0x49, 0x64, 0x12, 0x20, 0x0a, 0x0b, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x55, 0x55,
	0x49, 0x44, 0x18, 0x12, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63,
	0x74, 0x55, 0x55, 0x49, 0x44, 0x12, 0x20, 0x0a, 0x0b, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74,
	0x4e, 0x61, 0x6d, 0x65, 0x18, 0x13, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x70, 0x72, 0x6f, 0x64,
	0x75, 0x63, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1e, 0x0a, 0x0a, 0x70, 0x72, 0x6f, 0x64, 0x75,
	0x63, 0x74, 0x49, 0x6d, 0x67, 0x18, 0x14, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x70, 0x72, 0x6f,
	0x64, 0x75, 0x63, 0x74, 0x49, 0x6d, 0x67, 0x12, 0x2e, 0x0a, 0x12, 0x70, 0x72, 0x6f, 0x64, 0x75,
	0x63, 0x74, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x15, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x12, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x44, 0x65, 0x73, 0x63,
	0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0xf4, 0x01, 0x0a, 0x0c, 0x42, 0x75, 0x73, 0x69,
	0x6e, 0x65, 0x73, 0x73, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x22, 0x0a, 0x0c, 0x42, 0x75, 0x73, 0x69,
	0x6e, 0x65, 0x73, 0x73, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c,
	0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1a, 0x0a, 0x08,
	0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x63, 0x79, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08,
	0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x63, 0x79, 0x12, 0x1a, 0x0a, 0x08, 0x6f, 0x72, 0x64, 0x65,
	0x72, 0x4e, 0x75, 0x6d, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x6f, 0x72, 0x64, 0x65,
	0x72, 0x4e, 0x75, 0x6d, 0x12, 0x20, 0x0a, 0x0b, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x49, 0x6e, 0x63,
	0x6f, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0b, 0x74, 0x6f, 0x74, 0x61, 0x6c,
	0x49, 0x6e, 0x63, 0x6f, 0x6d, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x46,
	0x65, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x46,
	0x65, 0x65, 0x12, 0x2c, 0x0a, 0x11, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x52, 0x65, 0x66, 0x75, 0x6e,
	0x64, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x06, 0x20, 0x01, 0x28, 0x03, 0x52, 0x11, 0x74,
	0x6f, 0x74, 0x61, 0x6c, 0x52, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74,
	0x12, 0x1c, 0x0a, 0x09, 0x6e, 0x65, 0x74, 0x49, 0x6e, 0x63, 0x6f, 0x6d, 0x65, 0x18, 0x07, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x09, 0x6e, 0x65, 0x74, 0x49, 0x6e, 0x63, 0x6f, 0x6d, 0x65, 0x22, 0xcc,
	0x01, 0x0a, 0x08, 0x4f, 0x76, 0x65, 0x72, 0x76, 0x69, 0x65, 0x77, 0x12, 0x1a, 0x0a, 0x08, 0x63,
	0x75, 0x72, 0x72, 0x65, 0x6e, 0x63, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x63,
	0x75, 0x72, 0x72, 0x65, 0x6e, 0x63, 0x79, 0x12, 0x1a, 0x0a, 0x08, 0x6f, 0x72, 0x64, 0x65, 0x72,
	0x4e, 0x75, 0x6d, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x6f, 0x72, 0x64, 0x65, 0x72,
	0x4e, 0x75, 0x6d, 0x12, 0x20, 0x0a, 0x0b, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x49, 0x6e, 0x63, 0x6f,
	0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0b, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x49,
	0x6e, 0x63, 0x6f, 0x6d, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x46, 0x65,
	0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x46, 0x65,
	0x65, 0x12, 0x2c, 0x0a, 0x11, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x52, 0x65, 0x66, 0x75, 0x6e, 0x64,
	0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x03, 0x52, 0x11, 0x74, 0x6f,
	0x74, 0x61, 0x6c, 0x52, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12,
	0x1c, 0x0a, 0x09, 0x6e, 0x65, 0x74, 0x49, 0x6e, 0x63, 0x6f, 0x6d, 0x65, 0x18, 0x06, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x09, 0x6e, 0x65, 0x74, 0x49, 0x6e, 0x63, 0x6f, 0x6d, 0x65, 0x22, 0xf3, 0x01,
	0x0a, 0x0d, 0x43, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x49, 0x6e, 0x63, 0x6f, 0x6d, 0x65, 0x12,
	0x20, 0x0a, 0x0b, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x54, 0x79, 0x70,
	0x65, 0x12, 0x1a, 0x0a, 0x08, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x63, 0x79, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x08, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x63, 0x79, 0x12, 0x1a, 0x0a,
	0x08, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x4e, 0x75, 0x6d, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x08, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x4e, 0x75, 0x6d, 0x12, 0x20, 0x0a, 0x0b, 0x74, 0x6f, 0x74,
	0x61, 0x6c, 0x49, 0x6e, 0x63, 0x6f, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0b,
	0x74, 0x6f, 0x74, 0x61, 0x6c, 0x49, 0x6e, 0x63, 0x6f, 0x6d, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x74,
	0x6f, 0x74, 0x61, 0x6c, 0x46, 0x65, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x74,
	0x6f, 0x74, 0x61, 0x6c, 0x46, 0x65, 0x65, 0x12, 0x2c, 0x0a, 0x11, 0x74, 0x6f, 0x74, 0x61, 0x6c,
	0x52, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x06, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x11, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x52, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x41,
	0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x1c, 0x0a, 0x09, 0x6e, 0x65, 0x74, 0x49, 0x6e, 0x63, 0x6f,
	0x6d, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x6e, 0x65, 0x74, 0x49, 0x6e, 0x63,
	0x6f, 0x6d, 0x65, 0x32, 0xe7, 0x16, 0x0a, 0x0b, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x43,
	0x65, 0x6e, 0x74, 0x12, 0x42, 0x0a, 0x09, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x50, 0x61, 0x79,
	0x12, 0x19, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74,
	0x65, 0x50, 0x61, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1a, 0x2e, 0x70, 0x61,
	0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x50, 0x61, 0x79, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x42, 0x0a, 0x09, 0x4e, 0x6f, 0x74, 0x69, 0x66,
	0x79, 0x50, 0x61, 0x79, 0x12, 0x19, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x4e,
	0x6f, 0x74, 0x69, 0x66, 0x79, 0x50, 0x61, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a,
	0x1a, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x4e, 0x6f, 0x74, 0x69, 0x66, 0x79,
	0x50, 0x61, 0x79, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x4b, 0x0a, 0x14, 0x51,
	0x75, 0x65, 0x72, 0x79, 0x50, 0x61, 0x79, 0x42, 0x79, 0x4f, 0x75, 0x74, 0x54, 0x72, 0x61, 0x64,
	0x65, 0x4e, 0x6f, 0x12, 0x18, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x50, 0x61,
	0x79, 0x51, 0x75, 0x65, 0x72, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x19, 0x2e,
	0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x50, 0x61, 0x79, 0x51, 0x75, 0x65, 0x72, 0x79,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x47, 0x0a, 0x0e, 0x51, 0x75, 0x65, 0x72,
	0x79, 0x45, 0x78, 0x70, 0x6f, 0x72, 0x74, 0x50, 0x61, 0x79, 0x12, 0x19, 0x2e, 0x70, 0x61, 0x79,
	0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x45, 0x78, 0x70, 0x6f, 0x72, 0x74, 0x50, 0x61, 0x79, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1a, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e,
	0x45, 0x78, 0x70, 0x6f, 0x72, 0x74, 0x50, 0x61, 0x79, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x12, 0x4b, 0x0a, 0x0c, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x52, 0x65, 0x66, 0x75, 0x6e,
	0x64, 0x12, 0x1c, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x43, 0x72, 0x65, 0x61,
	0x74, 0x65, 0x52, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a,
	0x1d, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65,
	0x52, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x61,
	0x0a, 0x14, 0x53, 0x74, 0x72, 0x69, 0x70, 0x65, 0x47, 0x65, 0x72, 0x6d, 0x61, 0x6e, 0x79, 0x57,
	0x65, 0x62, 0x68, 0x6f, 0x6f, 0x6b, 0x12, 0x22, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74,
	0x2e, 0x47, 0x65, 0x74, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x6f, 0x75, 0x74, 0x57, 0x65, 0x62, 0x68,
	0x6f, 0x6f, 0x6b, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x23, 0x2e, 0x70, 0x61, 0x79,
	0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x47, 0x65, 0x74, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x6f, 0x75, 0x74,
	0x57, 0x65, 0x62, 0x68, 0x6f, 0x6f, 0x6b, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22,
	0x00, 0x12, 0x5f, 0x0a, 0x12, 0x53, 0x74, 0x72, 0x69, 0x70, 0x65, 0x4a, 0x61, 0x70, 0x61, 0x6e,
	0x57, 0x65, 0x62, 0x68, 0x6f, 0x6f, 0x6b, 0x12, 0x22, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e,
	0x74, 0x2e, 0x47, 0x65, 0x74, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x6f, 0x75, 0x74, 0x57, 0x65, 0x62,
	0x68, 0x6f, 0x6f, 0x6b, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x23, 0x2e, 0x70, 0x61,
	0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x47, 0x65, 0x74, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x6f, 0x75,
	0x74, 0x57, 0x65, 0x62, 0x68, 0x6f, 0x6f, 0x6b, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x22, 0x00, 0x12, 0x4b, 0x0a, 0x10, 0x41, 0x6c, 0x69, 0x43, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x57,
	0x65, 0x62, 0x68, 0x6f, 0x6f, 0x6b, 0x12, 0x19, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74,
	0x2e, 0x4e, 0x6f, 0x74, 0x69, 0x66, 0x79, 0x50, 0x61, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x1a, 0x1a, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x4e, 0x6f, 0x74, 0x69,
	0x66, 0x79, 0x50, 0x61, 0x79, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12,
	0x50, 0x0a, 0x15, 0x57, 0x65, 0x63, 0x68, 0x61, 0x74, 0x46, 0x65, 0x6e, 0x67, 0x4c, 0x69, 0x61,
	0x6e, 0x57, 0x65, 0x62, 0x68, 0x6f, 0x6f, 0x6b, 0x12, 0x19, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65,
	0x6e, 0x74, 0x2e, 0x4e, 0x6f, 0x74, 0x69, 0x66, 0x79, 0x50, 0x61, 0x79, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x1a, 0x1a, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x4e, 0x6f,
	0x74, 0x69, 0x66, 0x79, 0x50, 0x61, 0x79, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22,
	0x00, 0x12, 0x51, 0x0a, 0x0c, 0x41, 0x6e, 0x74, 0x6f, 0x6d, 0x57, 0x65, 0x62, 0x68, 0x6f, 0x6f,
	0x6b, 0x12, 0x1e, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x41, 0x6e, 0x74, 0x6f,
	0x6d, 0x4e, 0x6f, 0x74, 0x69, 0x66, 0x79, 0x50, 0x61, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x1a, 0x1f, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x41, 0x6e, 0x74, 0x6f,
	0x6d, 0x4e, 0x6f, 0x74, 0x69, 0x66, 0x79, 0x50, 0x61, 0x79, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x22, 0x00, 0x12, 0x63, 0x0a, 0x20, 0x51, 0x75, 0x65, 0x72, 0x79, 0x41, 0x6e, 0x74,
	0x6f, 0x6d, 0x50, 0x61, 0x79, 0x42, 0x79, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x6f, 0x75, 0x74, 0x53,
	0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x12, 0x1d, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65,
	0x6e, 0x74, 0x2e, 0x41, 0x6e, 0x74, 0x6f, 0x6d, 0x50, 0x61, 0x79, 0x51, 0x75, 0x65, 0x72, 0x79,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1e, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e,
	0x74, 0x2e, 0x41, 0x6e, 0x74, 0x6f, 0x6d, 0x50, 0x61, 0x79, 0x51, 0x75, 0x65, 0x72, 0x79, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x7a, 0x0a, 0x1b, 0x43, 0x72, 0x65,
	0x61, 0x74, 0x65, 0x53, 0x74, 0x72, 0x69, 0x70, 0x65, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x6f, 0x75,
	0x74, 0x53, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x2b, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65,
	0x6e, 0x74, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x53, 0x74, 0x72, 0x69, 0x70, 0x65, 0x43,
	0x68, 0x65, 0x63, 0x6b, 0x6f, 0x75, 0x74, 0x53, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2c, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e,
	0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x53, 0x74, 0x72, 0x69, 0x70, 0x65, 0x43, 0x68, 0x65, 0x63,
	0x6b, 0x6f, 0x75, 0x74, 0x53, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x44, 0x0a, 0x09, 0x41, 0x6c, 0x69, 0x57, 0x61, 0x70, 0x50,
	0x61, 0x79, 0x12, 0x19, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x41, 0x6c, 0x69,
	0x57, 0x61, 0x70, 0x50, 0x61, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1a, 0x2e,
	0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x41, 0x6c, 0x69, 0x57, 0x61, 0x70, 0x50, 0x61,
	0x79, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x44, 0x0a, 0x09, 0x41,
	0x6c, 0x69, 0x41, 0x70, 0x70, 0x50, 0x61, 0x79, 0x12, 0x19, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65,
	0x6e, 0x74, 0x2e, 0x41, 0x6c, 0x69, 0x41, 0x70, 0x70, 0x50, 0x61, 0x79, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x1a, 0x1a, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x41, 0x6c,
	0x69, 0x41, 0x70, 0x70, 0x50, 0x61, 0x79, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22,
	0x00, 0x12, 0x4d, 0x0a, 0x0c, 0x41, 0x6c, 0x69, 0x4e, 0x61, 0x74, 0x69, 0x76, 0x65, 0x50, 0x61,
	0x79, 0x12, 0x1c, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x41, 0x6c, 0x69, 0x4e,
	0x61, 0x74, 0x69, 0x76, 0x65, 0x50, 0x61, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a,
	0x1d, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x41, 0x6c, 0x69, 0x4e, 0x61, 0x74,
	0x69, 0x76, 0x65, 0x50, 0x61, 0x79, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00,
	0x12, 0x4a, 0x0a, 0x0b, 0x41, 0x6c, 0x69, 0x50, 0x63, 0x57, 0x61, 0x62, 0x50, 0x61, 0x79, 0x12,
	0x1b, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x41, 0x6c, 0x69, 0x50, 0x63, 0x57,
	0x61, 0x62, 0x50, 0x61, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1c, 0x2e, 0x70,
	0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x41, 0x6c, 0x69, 0x50, 0x63, 0x57, 0x61, 0x62, 0x50,
	0x61, 0x79, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x44, 0x0a, 0x09,
	0x41, 0x6c, 0x69, 0x52, 0x65, 0x46, 0x75, 0x6e, 0x64, 0x12, 0x19, 0x2e, 0x70, 0x61, 0x79, 0x6d,
	0x65, 0x6e, 0x74, 0x2e, 0x41, 0x6c, 0x69, 0x52, 0x65, 0x46, 0x75, 0x6e, 0x64, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x1a, 0x1a, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x41,
	0x6c, 0x69, 0x52, 0x65, 0x46, 0x75, 0x6e, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x22, 0x00, 0x12, 0x44, 0x0a, 0x09, 0x41, 0x6c, 0x69, 0x4e, 0x6f, 0x74, 0x69, 0x66, 0x79, 0x12,
	0x19, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x41, 0x6c, 0x69, 0x4e, 0x6f, 0x74,
	0x69, 0x66, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1a, 0x2e, 0x70, 0x61, 0x79,
	0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x41, 0x6c, 0x69, 0x4e, 0x6f, 0x74, 0x69, 0x66, 0x79, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x65, 0x0a, 0x14, 0x41, 0x6c, 0x69, 0x51,
	0x75, 0x65, 0x72, 0x79, 0x42, 0x79, 0x4f, 0x75, 0x74, 0x54, 0x72, 0x61, 0x64, 0x65, 0x4e, 0x6f,
	0x12, 0x24, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x41, 0x6c, 0x69, 0x51, 0x75,
	0x65, 0x72, 0x79, 0x42, 0x79, 0x4f, 0x75, 0x74, 0x54, 0x72, 0x61, 0x64, 0x65, 0x4e, 0x6f, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x25, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74,
	0x2e, 0x41, 0x6c, 0x69, 0x51, 0x75, 0x65, 0x72, 0x79, 0x42, 0x79, 0x4f, 0x75, 0x74, 0x54, 0x72,
	0x61, 0x64, 0x65, 0x4e, 0x6f, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12,
	0x77, 0x0a, 0x1a, 0x41, 0x6c, 0x69, 0x52, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x51, 0x75, 0x65, 0x72,
	0x79, 0x42, 0x79, 0x4f, 0x75, 0x74, 0x54, 0x72, 0x61, 0x64, 0x65, 0x4e, 0x6f, 0x12, 0x2a, 0x2e,
	0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x41, 0x6c, 0x69, 0x52, 0x65, 0x66, 0x75, 0x6e,
	0x64, 0x51, 0x75, 0x65, 0x72, 0x79, 0x42, 0x79, 0x4f, 0x75, 0x74, 0x54, 0x72, 0x61, 0x64, 0x65,
	0x4e, 0x6f, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2b, 0x2e, 0x70, 0x61, 0x79, 0x6d,
	0x65, 0x6e, 0x74, 0x2e, 0x41, 0x6c, 0x69, 0x52, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x51, 0x75, 0x65,
	0x72, 0x79, 0x42, 0x79, 0x4f, 0x75, 0x74, 0x54, 0x72, 0x61, 0x64, 0x65, 0x4e, 0x6f, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x53, 0x0a, 0x0e, 0x57, 0x65, 0x63, 0x68,
	0x61, 0x74, 0x4a, 0x73, 0x41, 0x70, 0x69, 0x50, 0x61, 0x79, 0x12, 0x1e, 0x2e, 0x70, 0x61, 0x79,
	0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x57, 0x65, 0x63, 0x68, 0x61, 0x74, 0x4a, 0x73, 0x41, 0x70, 0x69,
	0x50, 0x61, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1f, 0x2e, 0x70, 0x61, 0x79,
	0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x57, 0x65, 0x63, 0x68, 0x61, 0x74, 0x4a, 0x73, 0x41, 0x70, 0x69,
	0x50, 0x61, 0x79, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x7d, 0x0a,
	0x1c, 0x57, 0x65, 0x63, 0x68, 0x61, 0x74, 0x4a, 0x73, 0x41, 0x70, 0x69, 0x51, 0x75, 0x65, 0x72,
	0x79, 0x42, 0x79, 0x4f, 0x75, 0x74, 0x54, 0x72, 0x61, 0x64, 0x65, 0x4e, 0x6f, 0x12, 0x2c, 0x2e,
	0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x57, 0x65, 0x63, 0x68, 0x61, 0x74, 0x4a, 0x73,
	0x41, 0x70, 0x69, 0x51, 0x75, 0x65, 0x72, 0x79, 0x42, 0x79, 0x4f, 0x75, 0x74, 0x54, 0x72, 0x61,
	0x64, 0x65, 0x4e, 0x6f, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2d, 0x2e, 0x70, 0x61,
	0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x57, 0x65, 0x63, 0x68, 0x61, 0x74, 0x4a, 0x73, 0x41, 0x70,
	0x69, 0x51, 0x75, 0x65, 0x72, 0x79, 0x42, 0x79, 0x4f, 0x75, 0x74, 0x54, 0x72, 0x61, 0x64, 0x65,
	0x4e, 0x6f, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x5f, 0x0a, 0x12,
	0x47, 0x65, 0x74, 0x50, 0x61, 0x79, 0x42, 0x79, 0x4f, 0x75, 0x74, 0x54, 0x72, 0x61, 0x64, 0x65,
	0x4e, 0x6f, 0x12, 0x22, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x47, 0x65, 0x74,
	0x50, 0x61, 0x79, 0x42, 0x79, 0x4f, 0x75, 0x74, 0x54, 0x72, 0x61, 0x64, 0x65, 0x4e, 0x6f, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x23, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74,
	0x2e, 0x47, 0x65, 0x74, 0x50, 0x61, 0x79, 0x42, 0x79, 0x4f, 0x75, 0x74, 0x54, 0x72, 0x61, 0x64,
	0x65, 0x4e, 0x6f, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x5f, 0x0a,
	0x12, 0x57, 0x65, 0x63, 0x68, 0x61, 0x74, 0x4a, 0x73, 0x41, 0x70, 0x69, 0x52, 0x65, 0x66, 0x75,
	0x6e, 0x64, 0x73, 0x12, 0x22, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x57, 0x65,
	0x63, 0x68, 0x61, 0x74, 0x4a, 0x73, 0x41, 0x70, 0x69, 0x52, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x73,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x23, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e,
	0x74, 0x2e, 0x57, 0x65, 0x63, 0x68, 0x61, 0x74, 0x4a, 0x73, 0x41, 0x70, 0x69, 0x52, 0x65, 0x66,
	0x75, 0x6e, 0x64, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x42,
	0x0a, 0x08, 0x53, 0x65, 0x74, 0x50, 0x61, 0x79, 0x4f, 0x6b, 0x12, 0x1b, 0x2e, 0x70, 0x61, 0x79,
	0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x57, 0x65, 0x63, 0x68, 0x61, 0x74, 0x50, 0x61, 0x79, 0x4f, 0x6b,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x17, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e,
	0x74, 0x2e, 0x43, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x22, 0x00, 0x12, 0x4d, 0x0a, 0x0c, 0x57, 0x65, 0x63, 0x68, 0x61, 0x74, 0x41, 0x70, 0x70, 0x50,
	0x61, 0x79, 0x12, 0x1c, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x57, 0x65, 0x63,
	0x68, 0x61, 0x74, 0x41, 0x70, 0x70, 0x50, 0x61, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x1a, 0x1d, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x57, 0x65, 0x63, 0x68, 0x61,
	0x74, 0x41, 0x70, 0x70, 0x50, 0x61, 0x79, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22,
	0x00, 0x12, 0x77, 0x0a, 0x1a, 0x57, 0x65, 0x63, 0x68, 0x61, 0x74, 0x41, 0x70, 0x70, 0x51, 0x75,
	0x65, 0x72, 0x79, 0x42, 0x79, 0x4f, 0x75, 0x74, 0x54, 0x72, 0x61, 0x64, 0x65, 0x4e, 0x6f, 0x12,
	0x2a, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x57, 0x65, 0x63, 0x68, 0x61, 0x74,
	0x41, 0x70, 0x70, 0x51, 0x75, 0x65, 0x72, 0x79, 0x42, 0x79, 0x4f, 0x75, 0x74, 0x54, 0x72, 0x61,
	0x64, 0x65, 0x4e, 0x6f, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2b, 0x2e, 0x70, 0x61,
	0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x57, 0x65, 0x63, 0x68, 0x61, 0x74, 0x41, 0x70, 0x70, 0x51,
	0x75, 0x65, 0x72, 0x79, 0x42, 0x79, 0x4f, 0x75, 0x74, 0x54, 0x72, 0x61, 0x64, 0x65, 0x4e, 0x6f,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x56, 0x0a, 0x0f, 0x57, 0x65,
	0x63, 0x68, 0x61, 0x74, 0x4e, 0x61, 0x74, 0x69, 0x76, 0x65, 0x50, 0x61, 0x79, 0x12, 0x1f, 0x2e,
	0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x57, 0x65, 0x63, 0x68, 0x61, 0x74, 0x4e, 0x61,
	0x74, 0x69, 0x76, 0x65, 0x50, 0x61, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x20,
	0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x57, 0x65, 0x63, 0x68, 0x61, 0x74, 0x4e,
	0x61, 0x74, 0x69, 0x76, 0x65, 0x50, 0x61, 0x79, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x22, 0x00, 0x12, 0x80, 0x01, 0x0a, 0x1d, 0x57, 0x65, 0x63, 0x68, 0x61, 0x74, 0x4e, 0x61, 0x74,
	0x69, 0x76, 0x65, 0x51, 0x75, 0x65, 0x72, 0x79, 0x42, 0x79, 0x4f, 0x75, 0x74, 0x54, 0x72, 0x61,
	0x64, 0x65, 0x4e, 0x6f, 0x12, 0x2d, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x57,
	0x65, 0x63, 0x68, 0x61, 0x74, 0x4e, 0x61, 0x74, 0x69, 0x76, 0x65, 0x51, 0x75, 0x65, 0x72, 0x79,
	0x42, 0x79, 0x4f, 0x75, 0x74, 0x54, 0x72, 0x61, 0x64, 0x65, 0x4e, 0x6f, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x1a, 0x2e, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x57, 0x65,
	0x63, 0x68, 0x61, 0x74, 0x4e, 0x61, 0x74, 0x69, 0x76, 0x65, 0x51, 0x75, 0x65, 0x72, 0x79, 0x42,
	0x79, 0x4f, 0x75, 0x74, 0x54, 0x72, 0x61, 0x64, 0x65, 0x4e, 0x6f, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x83, 0x01, 0x0a, 0x1e, 0x57, 0x65, 0x63, 0x68, 0x61, 0x74,
	0x52, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x51, 0x75, 0x65, 0x72, 0x79, 0x42, 0x79, 0x4f, 0x75, 0x74,
	0x52, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x4e, 0x6f, 0x12, 0x2e, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65,
	0x6e, 0x74, 0x2e, 0x57, 0x65, 0x63, 0x68, 0x61, 0x74, 0x52, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x51,
	0x75, 0x65, 0x72, 0x79, 0x42, 0x79, 0x4f, 0x75, 0x74, 0x52, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x4e,
	0x6f, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2f, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65,
	0x6e, 0x74, 0x2e, 0x57, 0x65, 0x63, 0x68, 0x61, 0x74, 0x52, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x51,
	0x75, 0x65, 0x72, 0x79, 0x42, 0x79, 0x4f, 0x75, 0x74, 0x52, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x4e,
	0x6f, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x4a, 0x0a, 0x0b, 0x57,
	0x65, 0x63, 0x68, 0x61, 0x74, 0x48, 0x35, 0x50, 0x61, 0x79, 0x12, 0x1b, 0x2e, 0x70, 0x61, 0x79,
	0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x57, 0x65, 0x63, 0x68, 0x61, 0x74, 0x48, 0x35, 0x50, 0x61, 0x79,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1c, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e,
	0x74, 0x2e, 0x57, 0x65, 0x63, 0x68, 0x61, 0x74, 0x48, 0x35, 0x50, 0x61, 0x79, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x74, 0x0a, 0x19, 0x57, 0x65, 0x63, 0x68, 0x61,
	0x74, 0x48, 0x35, 0x51, 0x75, 0x65, 0x72, 0x79, 0x42, 0x79, 0x4f, 0x75, 0x74, 0x54, 0x72, 0x61,
	0x64, 0x65, 0x4e, 0x6f, 0x12, 0x29, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x57,
	0x65, 0x63, 0x68, 0x61, 0x74, 0x48, 0x35, 0x51, 0x75, 0x65, 0x72, 0x79, 0x42, 0x79, 0x4f, 0x75,
	0x74, 0x54, 0x72, 0x61, 0x64, 0x65, 0x4e, 0x6f, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a,
	0x2a, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x57, 0x65, 0x63, 0x68, 0x61, 0x74,
	0x48, 0x35, 0x51, 0x75, 0x65, 0x72, 0x79, 0x42, 0x79, 0x4f, 0x75, 0x74, 0x54, 0x72, 0x61, 0x64,
	0x65, 0x4e, 0x6f, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x42, 0x0b, 0x5a,
	0x09, 0x2e, 0x2f, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x33,
}

var (
	file_pb_payment_proto_rawDescOnce sync.Once
	file_pb_payment_proto_rawDescData = file_pb_payment_proto_rawDesc
)

func file_pb_payment_proto_rawDescGZIP() []byte {
	file_pb_payment_proto_rawDescOnce.Do(func() {
		file_pb_payment_proto_rawDescData = protoimpl.X.CompressGZIP(file_pb_payment_proto_rawDescData)
	})
	return file_pb_payment_proto_rawDescData
}

var file_pb_payment_proto_msgTypes = make([]protoimpl.MessageInfo, 74)
var file_pb_payment_proto_goTypes = []interface{}{
	(*AntomPayQueryRequest)(nil),                               // 0: payment.AntomPayQueryRequest
	(*AntomPayQueryResponse)(nil),                              // 1: payment.AntomPayQueryResponse
	(*AntomNotifyPayRequest)(nil),                              // 2: payment.AntomNotifyPayRequest
	(*AntomNotifyPayResponse)(nil),                             // 3: payment.AntomNotifyPayResponse
	(*CreatePayRequest)(nil),                                   // 4: payment.CreatePayRequest
	(*CreatePayResponse)(nil),                                  // 5: payment.CreatePayResponse
	(*CreateRefundRequest)(nil),                                // 6: payment.CreateRefundRequest
	(*CreateRefundResponse)(nil),                               // 7: payment.CreateRefundResponse
	(*NotifyPayRequest)(nil),                                   // 8: payment.NotifyPayRequest
	(*NotifyPayResponse)(nil),                                  // 9: payment.NotifyPayResponse
	(*HttpRequest)(nil),                                        // 10: payment.HttpRequest
	(*CommonMsg)(nil),                                          // 11: payment.CommonMsg
	(*EmptyRequest)(nil),                                       // 12: payment.EmptyRequest
	(*CreateStripeCheckoutSessionRequest)(nil),                 // 13: payment.CreateStripeCheckoutSessionRequest
	(*CreateStripeCheckoutSessionResponse)(nil),                // 14: payment.CreateStripeCheckoutSessionResponse
	(*GetCheckoutWebhookRequest)(nil),                          // 15: payment.GetCheckoutWebhookRequest
	(*GetCheckoutWebhookResponse)(nil),                         // 16: payment.GetCheckoutWebhookResponse
	(*GetStripePaymentIntentInfoRequest)(nil),                  // 17: payment.GetStripePaymentIntentInfoRequest
	(*GetStripePaymentIntentInfoResponse)(nil),                 // 18: payment.GetStripePaymentIntentInfoResponse
	(*GetRefundInfoRequest)(nil),                               // 19: payment.GetRefundInfoRequest
	(*GetRefundInfoResponse)(nil),                              // 20: payment.GetRefundInfoResponse
	(*AliWapPayRequest)(nil),                                   // 21: payment.AliWapPayRequest
	(*AliWapPayResponse)(nil),                                  // 22: payment.AliWapPayResponse
	(*AliAppPayRequest)(nil),                                   // 23: payment.AliAppPayRequest
	(*AliAppPayResponse)(nil),                                  // 24: payment.AliAppPayResponse
	(*AliNativePayRequest)(nil),                                // 25: payment.AliNativePayRequest
	(*AliNativePayResponse)(nil),                               // 26: payment.AliNativePayResponse
	(*AliPcWabPayRequest)(nil),                                 // 27: payment.AliPcWabPayRequest
	(*AliPcWabPayResponse)(nil),                                // 28: payment.AliPcWabPayResponse
	(*AliReFundRequest)(nil),                                   // 29: payment.AliReFundRequest
	(*AliReFundResponse)(nil),                                  // 30: payment.AliReFundResponse
	(*AliNotifyRequest)(nil),                                   // 31: payment.AliNotifyRequest
	(*AliNotifyResponse)(nil),                                  // 32: payment.AliNotifyResponse
	(*AliQueryByOutTradeNoRequest)(nil),                        // 33: payment.AliQueryByOutTradeNoRequest
	(*AliQueryByOutTradeNoResponse)(nil),                       // 34: payment.AliQueryByOutTradeNoResponse
	(*AliRefundQueryByOutTradeNoRequest)(nil),                  // 35: payment.AliRefundQueryByOutTradeNoRequest
	(*AliRefundQueryByOutTradeNoResponse)(nil),                 // 36: payment.AliRefundQueryByOutTradeNoResponse
	(*WechatJsApiPayRequest)(nil),                              // 37: payment.WechatJsApiPayRequest
	(*WechatJsApiPayResponse)(nil),                             // 38: payment.WechatJsApiPayResponse
	(*WechatAppPayResponse)(nil),                               // 39: payment.WechatAppPayResponse
	(*WechatJsApiQueryByOutTradeNoRequest)(nil),                // 40: payment.WechatJsApiQueryByOutTradeNoRequest
	(*WechatJsApiQueryByOutTradeNoResponse)(nil),               // 41: payment.WechatJsApiQueryByOutTradeNoResponse
	(*WechatAppQueryByOutTradeNoRequest)(nil),                  // 42: payment.WechatAppQueryByOutTradeNoRequest
	(*WechatAppQueryByOutTradeNoResponse)(nil),                 // 43: payment.WechatAppQueryByOutTradeNoResponse
	(*GetPayByOutTradeNoRequest)(nil),                          // 44: payment.GetPayByOutTradeNoRequest
	(*GetPayByOutTradeNoResponse)(nil),                         // 45: payment.GetPayByOutTradeNoResponse
	(*WechatJsApiRefundsResponse)(nil),                         // 46: payment.WechatJsApiRefundsResponse
	(*WechatNativePayRequest)(nil),                             // 47: payment.WechatNativePayRequest
	(*WechatNativePayResponse)(nil),                            // 48: payment.WechatNativePayResponse
	(*WechatNativeQueryByOutTradeNoRequest)(nil),               // 49: payment.WechatNativeQueryByOutTradeNoRequest
	(*WechatRefundQueryByOutRefundNoRequest)(nil),              // 50: payment.WechatRefundQueryByOutRefundNoRequest
	(*WechatRefundQueryByOutRefundNoResponse)(nil),             // 51: payment.WechatRefundQueryByOutRefundNoResponse
	(*WechatNativeQueryByOutTradeNoResponse)(nil),              // 52: payment.WechatNativeQueryByOutTradeNoResponse
	(*WechatAppPayRequest)(nil),                                // 53: payment.WechatAppPayRequest
	(*WechatJsApiRefundsRequest)(nil),                          // 54: payment.WechatJsApiRefundsRequest
	(*WechatH5PayRequest)(nil),                                 // 55: payment.WechatH5PayRequest
	(*WechatH5PayResponse)(nil),                                // 56: payment.WechatH5PayResponse
	(*WechatH5QueryByOutTradeNoRequest)(nil),                   // 57: payment.WechatH5QueryByOutTradeNoRequest
	(*WechatH5QueryByOutTradeNoResponse)(nil),                  // 58: payment.WechatH5QueryByOutTradeNoResponse
	(*WechatPayOkRequest)(nil),                                 // 59: payment.WechatPayOkRequest
	(*CommonResponse)(nil),                                     // 60: payment.CommonResponse
	(*PayQueryRequest)(nil),                                    // 61: payment.PayQueryRequest
	(*PayQueryResponse)(nil),                                   // 62: payment.PayQueryResponse
	(*PaymentOrderInfo)(nil),                                   // 63: payment.PaymentOrderInfo
	(*ExportPayRequest)(nil),                                   // 64: payment.ExportPayRequest
	(*ExportPayResponse)(nil),                                  // 65: payment.ExportPayResponse
	(*OrderDetail)(nil),                                        // 66: payment.OrderDetail
	(*BusinessInfo)(nil),                                       // 67: payment.BusinessInfo
	(*Overview)(nil),                                           // 68: payment.Overview
	(*ChannelIncome)(nil),                                      // 69: payment.ChannelIncome
	nil,                                                        // 70: payment.HttpRequest.HeadersEntry
	(*WechatAppQueryByOutTradeNoResponse_Payer)(nil),           // 71: payment.WechatAppQueryByOutTradeNoResponse.Payer
	(*WechatAppQueryByOutTradeNoResponse_Amount)(nil),          // 72: payment.WechatAppQueryByOutTradeNoResponse.Amount
	(*WechatAppQueryByOutTradeNoResponse_PromotionDetail)(nil), // 73: payment.WechatAppQueryByOutTradeNoResponse.PromotionDetail
}
var file_pb_payment_proto_depIdxs = []int32{
	63, // 0: payment.AntomPayQueryResponse.infos:type_name -> payment.PaymentOrderInfo
	10, // 1: payment.NotifyPayRequest.http_request:type_name -> payment.HttpRequest
	70, // 2: payment.HttpRequest.headers:type_name -> payment.HttpRequest.HeadersEntry
	73, // 3: payment.WechatAppQueryByOutTradeNoResponse.promotionDetail:type_name -> payment.WechatAppQueryByOutTradeNoResponse.PromotionDetail
	71, // 4: payment.WechatAppQueryByOutTradeNoResponse.payer:type_name -> payment.WechatAppQueryByOutTradeNoResponse.Payer
	72, // 5: payment.WechatAppQueryByOutTradeNoResponse.amount:type_name -> payment.WechatAppQueryByOutTradeNoResponse.Amount
	63, // 6: payment.PayQueryResponse.infos:type_name -> payment.PaymentOrderInfo
	68, // 7: payment.ExportPayResponse.overview:type_name -> payment.Overview
	69, // 8: payment.ExportPayResponse.channelIncomes:type_name -> payment.ChannelIncome
	67, // 9: payment.ExportPayResponse.businessInfos:type_name -> payment.BusinessInfo
	66, // 10: payment.ExportPayResponse.orderDetails:type_name -> payment.OrderDetail
	4,  // 11: payment.PaymentCent.CreatePay:input_type -> payment.CreatePayRequest
	8,  // 12: payment.PaymentCent.NotifyPay:input_type -> payment.NotifyPayRequest
	61, // 13: payment.PaymentCent.QueryPayByOutTradeNo:input_type -> payment.PayQueryRequest
	64, // 14: payment.PaymentCent.QueryExportPay:input_type -> payment.ExportPayRequest
	6,  // 15: payment.PaymentCent.CreateRefund:input_type -> payment.CreateRefundRequest
	15, // 16: payment.PaymentCent.StripeGermanyWebhook:input_type -> payment.GetCheckoutWebhookRequest
	15, // 17: payment.PaymentCent.StripeJapanWebhook:input_type -> payment.GetCheckoutWebhookRequest
	8,  // 18: payment.PaymentCent.AliCommonWebhook:input_type -> payment.NotifyPayRequest
	8,  // 19: payment.PaymentCent.WechatFengLianWebhook:input_type -> payment.NotifyPayRequest
	2,  // 20: payment.PaymentCent.AntomWebhook:input_type -> payment.AntomNotifyPayRequest
	0,  // 21: payment.PaymentCent.QueryAntomPayByCheckoutSessionId:input_type -> payment.AntomPayQueryRequest
	13, // 22: payment.PaymentCent.CreateStripeCheckoutSession:input_type -> payment.CreateStripeCheckoutSessionRequest
	21, // 23: payment.PaymentCent.AliWapPay:input_type -> payment.AliWapPayRequest
	23, // 24: payment.PaymentCent.AliAppPay:input_type -> payment.AliAppPayRequest
	25, // 25: payment.PaymentCent.AliNativePay:input_type -> payment.AliNativePayRequest
	27, // 26: payment.PaymentCent.AliPcWabPay:input_type -> payment.AliPcWabPayRequest
	29, // 27: payment.PaymentCent.AliReFund:input_type -> payment.AliReFundRequest
	31, // 28: payment.PaymentCent.AliNotify:input_type -> payment.AliNotifyRequest
	33, // 29: payment.PaymentCent.AliQueryByOutTradeNo:input_type -> payment.AliQueryByOutTradeNoRequest
	35, // 30: payment.PaymentCent.AliRefundQueryByOutTradeNo:input_type -> payment.AliRefundQueryByOutTradeNoRequest
	37, // 31: payment.PaymentCent.WechatJsApiPay:input_type -> payment.WechatJsApiPayRequest
	40, // 32: payment.PaymentCent.WechatJsApiQueryByOutTradeNo:input_type -> payment.WechatJsApiQueryByOutTradeNoRequest
	44, // 33: payment.PaymentCent.GetPayByOutTradeNo:input_type -> payment.GetPayByOutTradeNoRequest
	54, // 34: payment.PaymentCent.WechatJsApiRefunds:input_type -> payment.WechatJsApiRefundsRequest
	59, // 35: payment.PaymentCent.SetPayOk:input_type -> payment.WechatPayOkRequest
	53, // 36: payment.PaymentCent.WechatAppPay:input_type -> payment.WechatAppPayRequest
	42, // 37: payment.PaymentCent.WechatAppQueryByOutTradeNo:input_type -> payment.WechatAppQueryByOutTradeNoRequest
	47, // 38: payment.PaymentCent.WechatNativePay:input_type -> payment.WechatNativePayRequest
	49, // 39: payment.PaymentCent.WechatNativeQueryByOutTradeNo:input_type -> payment.WechatNativeQueryByOutTradeNoRequest
	50, // 40: payment.PaymentCent.WechatRefundQueryByOutRefundNo:input_type -> payment.WechatRefundQueryByOutRefundNoRequest
	55, // 41: payment.PaymentCent.WechatH5Pay:input_type -> payment.WechatH5PayRequest
	57, // 42: payment.PaymentCent.WechatH5QueryByOutTradeNo:input_type -> payment.WechatH5QueryByOutTradeNoRequest
	5,  // 43: payment.PaymentCent.CreatePay:output_type -> payment.CreatePayResponse
	9,  // 44: payment.PaymentCent.NotifyPay:output_type -> payment.NotifyPayResponse
	62, // 45: payment.PaymentCent.QueryPayByOutTradeNo:output_type -> payment.PayQueryResponse
	65, // 46: payment.PaymentCent.QueryExportPay:output_type -> payment.ExportPayResponse
	7,  // 47: payment.PaymentCent.CreateRefund:output_type -> payment.CreateRefundResponse
	16, // 48: payment.PaymentCent.StripeGermanyWebhook:output_type -> payment.GetCheckoutWebhookResponse
	16, // 49: payment.PaymentCent.StripeJapanWebhook:output_type -> payment.GetCheckoutWebhookResponse
	9,  // 50: payment.PaymentCent.AliCommonWebhook:output_type -> payment.NotifyPayResponse
	9,  // 51: payment.PaymentCent.WechatFengLianWebhook:output_type -> payment.NotifyPayResponse
	3,  // 52: payment.PaymentCent.AntomWebhook:output_type -> payment.AntomNotifyPayResponse
	1,  // 53: payment.PaymentCent.QueryAntomPayByCheckoutSessionId:output_type -> payment.AntomPayQueryResponse
	14, // 54: payment.PaymentCent.CreateStripeCheckoutSession:output_type -> payment.CreateStripeCheckoutSessionResponse
	22, // 55: payment.PaymentCent.AliWapPay:output_type -> payment.AliWapPayResponse
	24, // 56: payment.PaymentCent.AliAppPay:output_type -> payment.AliAppPayResponse
	26, // 57: payment.PaymentCent.AliNativePay:output_type -> payment.AliNativePayResponse
	28, // 58: payment.PaymentCent.AliPcWabPay:output_type -> payment.AliPcWabPayResponse
	30, // 59: payment.PaymentCent.AliReFund:output_type -> payment.AliReFundResponse
	32, // 60: payment.PaymentCent.AliNotify:output_type -> payment.AliNotifyResponse
	34, // 61: payment.PaymentCent.AliQueryByOutTradeNo:output_type -> payment.AliQueryByOutTradeNoResponse
	36, // 62: payment.PaymentCent.AliRefundQueryByOutTradeNo:output_type -> payment.AliRefundQueryByOutTradeNoResponse
	38, // 63: payment.PaymentCent.WechatJsApiPay:output_type -> payment.WechatJsApiPayResponse
	41, // 64: payment.PaymentCent.WechatJsApiQueryByOutTradeNo:output_type -> payment.WechatJsApiQueryByOutTradeNoResponse
	45, // 65: payment.PaymentCent.GetPayByOutTradeNo:output_type -> payment.GetPayByOutTradeNoResponse
	46, // 66: payment.PaymentCent.WechatJsApiRefunds:output_type -> payment.WechatJsApiRefundsResponse
	60, // 67: payment.PaymentCent.SetPayOk:output_type -> payment.CommonResponse
	39, // 68: payment.PaymentCent.WechatAppPay:output_type -> payment.WechatAppPayResponse
	43, // 69: payment.PaymentCent.WechatAppQueryByOutTradeNo:output_type -> payment.WechatAppQueryByOutTradeNoResponse
	48, // 70: payment.PaymentCent.WechatNativePay:output_type -> payment.WechatNativePayResponse
	52, // 71: payment.PaymentCent.WechatNativeQueryByOutTradeNo:output_type -> payment.WechatNativeQueryByOutTradeNoResponse
	51, // 72: payment.PaymentCent.WechatRefundQueryByOutRefundNo:output_type -> payment.WechatRefundQueryByOutRefundNoResponse
	56, // 73: payment.PaymentCent.WechatH5Pay:output_type -> payment.WechatH5PayResponse
	58, // 74: payment.PaymentCent.WechatH5QueryByOutTradeNo:output_type -> payment.WechatH5QueryByOutTradeNoResponse
	43, // [43:75] is the sub-list for method output_type
	11, // [11:43] is the sub-list for method input_type
	11, // [11:11] is the sub-list for extension type_name
	11, // [11:11] is the sub-list for extension extendee
	0,  // [0:11] is the sub-list for field type_name
}

func init() { file_pb_payment_proto_init() }
func file_pb_payment_proto_init() {
	if File_pb_payment_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_pb_payment_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AntomPayQueryRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_payment_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AntomPayQueryResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_payment_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AntomNotifyPayRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_payment_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AntomNotifyPayResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_payment_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreatePayRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_payment_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreatePayResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_payment_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateRefundRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_payment_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateRefundResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_payment_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*NotifyPayRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_payment_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*NotifyPayResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_payment_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*HttpRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_payment_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CommonMsg); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_payment_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*EmptyRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_payment_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateStripeCheckoutSessionRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_payment_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateStripeCheckoutSessionResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_payment_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetCheckoutWebhookRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_payment_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetCheckoutWebhookResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_payment_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetStripePaymentIntentInfoRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_payment_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetStripePaymentIntentInfoResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_payment_proto_msgTypes[19].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetRefundInfoRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_payment_proto_msgTypes[20].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetRefundInfoResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_payment_proto_msgTypes[21].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AliWapPayRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_payment_proto_msgTypes[22].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AliWapPayResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_payment_proto_msgTypes[23].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AliAppPayRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_payment_proto_msgTypes[24].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AliAppPayResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_payment_proto_msgTypes[25].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AliNativePayRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_payment_proto_msgTypes[26].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AliNativePayResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_payment_proto_msgTypes[27].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AliPcWabPayRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_payment_proto_msgTypes[28].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AliPcWabPayResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_payment_proto_msgTypes[29].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AliReFundRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_payment_proto_msgTypes[30].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AliReFundResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_payment_proto_msgTypes[31].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AliNotifyRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_payment_proto_msgTypes[32].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AliNotifyResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_payment_proto_msgTypes[33].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AliQueryByOutTradeNoRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_payment_proto_msgTypes[34].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AliQueryByOutTradeNoResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_payment_proto_msgTypes[35].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AliRefundQueryByOutTradeNoRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_payment_proto_msgTypes[36].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AliRefundQueryByOutTradeNoResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_payment_proto_msgTypes[37].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*WechatJsApiPayRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_payment_proto_msgTypes[38].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*WechatJsApiPayResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_payment_proto_msgTypes[39].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*WechatAppPayResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_payment_proto_msgTypes[40].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*WechatJsApiQueryByOutTradeNoRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_payment_proto_msgTypes[41].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*WechatJsApiQueryByOutTradeNoResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_payment_proto_msgTypes[42].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*WechatAppQueryByOutTradeNoRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_payment_proto_msgTypes[43].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*WechatAppQueryByOutTradeNoResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_payment_proto_msgTypes[44].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetPayByOutTradeNoRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_payment_proto_msgTypes[45].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetPayByOutTradeNoResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_payment_proto_msgTypes[46].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*WechatJsApiRefundsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_payment_proto_msgTypes[47].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*WechatNativePayRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_payment_proto_msgTypes[48].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*WechatNativePayResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_payment_proto_msgTypes[49].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*WechatNativeQueryByOutTradeNoRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_payment_proto_msgTypes[50].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*WechatRefundQueryByOutRefundNoRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_payment_proto_msgTypes[51].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*WechatRefundQueryByOutRefundNoResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_payment_proto_msgTypes[52].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*WechatNativeQueryByOutTradeNoResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_payment_proto_msgTypes[53].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*WechatAppPayRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_payment_proto_msgTypes[54].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*WechatJsApiRefundsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_payment_proto_msgTypes[55].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*WechatH5PayRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_payment_proto_msgTypes[56].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*WechatH5PayResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_payment_proto_msgTypes[57].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*WechatH5QueryByOutTradeNoRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_payment_proto_msgTypes[58].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*WechatH5QueryByOutTradeNoResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_payment_proto_msgTypes[59].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*WechatPayOkRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_payment_proto_msgTypes[60].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CommonResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_payment_proto_msgTypes[61].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PayQueryRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_payment_proto_msgTypes[62].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PayQueryResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_payment_proto_msgTypes[63].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PaymentOrderInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_payment_proto_msgTypes[64].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ExportPayRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_payment_proto_msgTypes[65].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ExportPayResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_payment_proto_msgTypes[66].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*OrderDetail); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_payment_proto_msgTypes[67].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BusinessInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_payment_proto_msgTypes[68].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Overview); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_payment_proto_msgTypes[69].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ChannelIncome); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_payment_proto_msgTypes[71].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*WechatAppQueryByOutTradeNoResponse_Payer); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_payment_proto_msgTypes[72].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*WechatAppQueryByOutTradeNoResponse_Amount); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_payment_proto_msgTypes[73].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*WechatAppQueryByOutTradeNoResponse_PromotionDetail); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_pb_payment_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   74,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_pb_payment_proto_goTypes,
		DependencyIndexes: file_pb_payment_proto_depIdxs,
		MessageInfos:      file_pb_payment_proto_msgTypes,
	}.Build()
	File_pb_payment_proto = out.File
	file_pb_payment_proto_rawDesc = nil
	file_pb_payment_proto_goTypes = nil
	file_pb_payment_proto_depIdxs = nil
}
