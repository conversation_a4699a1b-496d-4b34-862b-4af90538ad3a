syntax = "proto3";
package payment;

option go_package = "./payment";

//import "pb/descriptor.proto";
service PaymentCent{

  // 统一渠道支付
  rpc CreatePay(CreatePayRequest) returns (CreatePayResponse); // 创建支付
  rpc NotifyPay(NotifyPayRequest) returns (NotifyPayResponse); // 支付回调
  rpc QueryPayByOutTradeNo(PayQueryRequest) returns (PayQueryResponse);// 根据外部流水号和来源，查看付款信息
  rpc QueryExportPay(ExportPayRequest) returns (ExportPayResponse); // 导出数据报表
  rpc CreateRefund(CreateRefundRequest) returns (CreateRefundResponse); // 发起退款，暂定公共

  rpc StripeGermanyWebhook(GetCheckoutWebhookRequest) returns(GetCheckoutWebhookResponse){};// stripe支付回调, 德国账号
  rpc StripeJapanWebhook(GetCheckoutWebhookRequest) returns(GetCheckoutWebhookResponse){};// stripe支付回调, 日本账号

  rpc AliCommonWebhook(NotifyPayRequest) returns (NotifyPayResponse){}; // 支付宝支付回调，通用

  rpc WechatFengLianWebhook(NotifyPayRequest) returns (NotifyPayResponse){}; // 微信支付回调，丰链
  rpc AntomWebhook(AntomNotifyPayRequest) returns (AntomNotifyPayResponse){}; // Antom支付回调
  rpc QueryAntomPayByCheckoutSessionId(AntomPayQueryRequest) returns (AntomPayQueryResponse){}; // 根据checkoutSessionIds查询支付情况



  // ========== 下面的暂时没用


  // stripe支付
  rpc CreateStripeCheckoutSession(CreateStripeCheckoutSessionRequest) returns (CreateStripeCheckoutSessionResponse){}; // 创建支付会话链接
  //  rpc CommonCheckoutWebhook(GetCheckoutWebhookRequest) returns(GetCheckoutWebhookResponse){}; // 支付回调,通用

  // 支付宝支付
  rpc AliWapPay(AliWapPayRequest) returns (AliWapPayResponse) {}; // ali网页支付
  rpc AliAppPay(AliAppPayRequest) returns (AliAppPayResponse) {}; // aliApp支付
  rpc AliNativePay(AliNativePayRequest) returns (AliNativePayResponse) {}; // aliApp当面支付
  rpc AliPcWabPay(AliPcWabPayRequest) returns (AliPcWabPayResponse) {}; // ali电脑网页支付
  rpc AliReFund(AliReFundRequest) returns (AliReFundResponse) {}; // ali退款
  rpc AliNotify(AliNotifyRequest) returns (AliNotifyResponse) {}; // ali回调
  rpc AliQueryByOutTradeNo(AliQueryByOutTradeNoRequest) returns (AliQueryByOutTradeNoResponse) {}; // ali查询订单
  rpc AliRefundQueryByOutTradeNo(AliRefundQueryByOutTradeNoRequest) returns (AliRefundQueryByOutTradeNoResponse) {}; // ali查询退款订单

  // 微信支付
  rpc WechatJsApiPay(WechatJsApiPayRequest) returns (WechatJsApiPayResponse) {}; // jsapi 本地支付订单保存+微信支付平台预支付订单生成
  rpc WechatJsApiQueryByOutTradeNo(WechatJsApiQueryByOutTradeNoRequest) returns (WechatJsApiQueryByOutTradeNoResponse) {}; // 通过outTrandeNo 获取 微信 jsapi 支付订单状态
  rpc GetPayByOutTradeNo(GetPayByOutTradeNoRequest) returns (GetPayByOutTradeNoResponse) {}; // 通过outTrandeNo 查询支付的id
  rpc WechatJsApiRefunds(WechatJsApiRefundsRequest) returns (WechatJsApiRefundsResponse) {}; //
  rpc SetPayOk(WechatPayOkRequest) returns (CommonResponse) {}; //
  rpc WechatAppPay(WechatAppPayRequest) returns (WechatAppPayResponse) {}; // app 本地支付订单保存+微信支付平台预支付订单生成
  rpc WechatAppQueryByOutTradeNo(WechatAppQueryByOutTradeNoRequest) returns (WechatAppQueryByOutTradeNoResponse) {}; // 通过outTrandeNo 获取 微信 App 支付订单状态
  rpc WechatNativePay(WechatNativePayRequest) returns (WechatNativePayResponse) {}; // native 本地支付订单保存+微信付款链接生成
  rpc WechatNativeQueryByOutTradeNo(WechatNativeQueryByOutTradeNoRequest) returns (WechatNativeQueryByOutTradeNoResponse) {}; // 通过outTrandeNo 获取 微信 App 支付订单状态
  rpc WechatRefundQueryByOutRefundNo(WechatRefundQueryByOutRefundNoRequest) returns (WechatRefundQueryByOutRefundNoResponse) {}; // wx查询退款订单
  rpc WechatH5Pay(WechatH5PayRequest) returns (WechatH5PayResponse) {}; // H5 本地支付订单保存+微信支付平台预支付订单生成
  rpc WechatH5QueryByOutTradeNo(WechatH5QueryByOutTradeNoRequest) returns (WechatH5QueryByOutTradeNoResponse) {}; // 通过outTrandeNo 获取 微信 H5 支付订单状态

}

message AntomPayQueryRequest {
  repeated string checkoutSessionIds = 1 [json_name = "checkoutSessionIds"];
}

message AntomPayQueryResponse {
  repeated PaymentOrderInfo infos = 1 [json_name = "infos"];
}

message AntomNotifyPayRequest {
  string notifyType = 1 [json_name = "notifyType"];
  string requestId = 2 [json_name = "requestId"];
  string paymentId = 3 [json_name = "paymentId"];
  string paymentTime = 4 [json_name = "paymentTime"];
  string resultStatus = 5 [json_name = "resultStatus"];
  string resultMessage = 6 [json_name = "resultMessage"];
  string channelCode = 7 [json_name = "channelCode"];
}

message AntomNotifyPayResponse {
  string status = 1 [json_name = "status"];
  string outTradeNo = 2 [json_name = "outTradeNo"];
}

message CreatePayRequest {
  string postUrl = 1 [json_name = "postUrl"]; // 请求接口
  string  subject = 2 [json_name = "subject"];  // 订单标题
  string  productUUID = 3 [json_name = "productUUID"];  // 商品uid
  string  productName = 4 [json_name = "productName"];  // 商品名称
  string  productImg = 5 [json_name = "productImg"];  // 商品图像
  string  productDescription = 6 [json_name = "productDescription"];  // 商品描述

  string  quitUrl = 7 [json_name = "quitUrl"];  // 退出url
  string  notifyUrl = 8 [json_name = "notifyUrl"];  // 回调url
  string  returnUrl = 9 [json_name = "returnUrl"];  // 返回url

  string  outTradeNo = 10 [json_name = "outTradeNo"];  // 外部流水号
  string  channelTradeNo = 11 [json_name = "channelTradeNo"];  // 渠道流水号
  string  checkSessionId = 12 [json_name = "checkSessionId"];  // stripe的支付会话id

  int64  amount = 13 [json_name = "amount"];  // 金额
  string  currency = 14 [json_name = "currency"]; // 币种

  string payee = 15 [json_name = "payee"];  // 收款方
  string channelType = 16 [json_name = "channelType"];  // 支付渠道 alipay-支付宝 wxpay-微信 stripe-Stripe支付
  string platform = 17 [json_name = "platform"];  // 支付具体来源，是app还是h5还是jsapi还是wap
  string domain = 18 [json_name = "domain"];  // 使用平台
  string businessType = 19 [json_name = "businessType"];  // 业务类型，用来确认mq发送

  string language = 20 [json_name = "language"]; // 语言，国际化
  string clientIp = 21 [json_name = "clientIp"];

  string openID = 22 [json_name = "openID"];  // 微信的参数
  int32 timeExpire = 23 [json_name = "timeExpire"];  // 微信的参数
  string locale = 24 [json_name = "locale"]; // stripe参数，区域，跟页面国际化有关
}

message CreatePayResponse {
  string url = 1 [json_name = "url"];
  string logRecordID = 2 [json_name = "log_record_ID"];
  string msg = 3 [json_name="msg"];

  string appid = 4 [json_name = "appid"];
  string timeStamp = 5 [json_name = "timeStamp"];
  string  package = 6 [json_name = "package"];
  string signType = 7 [json_name = "signType"];
  string nonceStr = 8 [json_name = "nonceStr"];
  string paySign = 9 [json_name = "paySign"];
  string prepayId = 10 [json_name = "prepayId"];
  string partnerId = 11 [json_name = "partnerId"];

  string checkoutSessionId = 12 [json_name = "checkoutSessionId"];
}

message CreateRefundRequest {
  string postUrl = 1 [json_name = "postUrl"]; // 请求接口
  string  subject = 2 [json_name = "subject"];  // 订单标题
  string  productUUID = 3 [json_name = "productUUID"];  // 商品uid
  string  outTradeNo = 4 [json_name = "outTradeNo"];  // 外部流水号
  string  channelTradeNo = 5 [json_name = "channelTradeNo"];  // 渠道流水号
  string  checkSessionId = 6 [json_name = "checkSessionId"];  // stripe的支付会话id
  int64  refundAmount = 7 [json_name = "refundAmount"];  // 退款金额
  string  currency = 8 [json_name = "currency"]; // 币种
  string payee = 9 [json_name = "payee"];  // 收款方
  string channelType = 10 [json_name = "channelType"];  // 支付渠道 alipay-支付宝 wxpay-微信 stripe-Stripe支付
  string platform = 11 [json_name = "platform"];  // 支付具体来源，是app还是h5还是jsapi还是wap
  string domain = 12 [json_name = "domain"];  // 使用平台
  string businessType = 13 [json_name = "businessType"];  // 业务类型，用来确认mq发送
  string language = 14 [json_name = "language"]; // 语言，国际化
  string clientIp = 15 [json_name = "clientIp"];
  string openID = 16 [json_name = "openID"];  // 微信的参数
  int64 creatorId = 17 [json_name = "creatorId"]; // 创建人id
  string creatorName = 18 [json_name = "creatorName"]; // 创建人名称
  string refundReason = 19 [json_name = "refundReason"]; // 退款理由
}

message CreateRefundResponse {
  int64 code = 1 [json_name = "code"];
  string msg = 2 [json_name="msg"];
}

message NotifyPayRequest {
  string postUrl = 1; // 请求接口
  HttpRequest http_request = 2;
  string  rawQuery = 3 [json_name="raw_query"];
}

message NotifyPayResponse {
  string msg = 1;
  string outTradeNo = 2;
}

message HttpRequest {
  string method = 1;
  string url = 2;
  map<string, string> headers = 3;
  bytes body = 4;
}

message CommonMsg{
  string msg = 1;
}

message EmptyRequest{}

message CreateStripeCheckoutSessionRequest {
  string productUUID = 1 [json_name = "productUUID"];
  string productName = 2 [json_name = "productName"];
  int64 productQuantity = 3 [json_name = "productQuantity"];
  int64 productAllPrice = 4 [json_name = "productAllPrice"];
  string productDescription = 5 [json_name = "productDescription"];
  string productImageUrl = 6 [json_name = "productImageUrl"];
  string productPriceCurrency = 7 [json_name = "productPriceCurrency"];
  string createrID = 8 [json_name = "createrID"];
  string createrName = 9 [json_name = "createrName"];
  string domain = 10 [json_name = "domain"];
  string successUrl = 11 [json_name = "successUrl"];
  string cancelUrl = 12 [json_name = "cancelUrl"];
  string outTradeNo = 13 [json_name = "outTradeNo"];
  string locale = 14 [json_name = "locale"];
}

message CreateStripeCheckoutSessionResponse {
  string checkoutSessionId = 1 [json_name = "checkoutSessionId"];
  string checkoutSessionUrl = 2 [json_name = "checkoutSessionUrl"];
}

message GetCheckoutWebhookRequest {
  string postUrl = 1; // 请求接口
  string type = 2; // 事件类型，例如"payment_intent.succeeded"
  string payload = 3; // 事件的原始JSON payload
  string signature = 4; // Stripe-Signature头的值，用于验证事件
  string webhookKey = 5;  // Webhook密钥
}

message GetCheckoutWebhookResponse {
  bool success = 1; // 处理是否成功
  string message = 2; // 可选的处理消息
  string outTradeNo = 3 [json_name = "outTradeNo"];
  string paymentIntentStatus = 4 [json_name = "paymentIntentStatus"];
}

message GetStripePaymentIntentInfoRequest {
  string checkoutSessionId = 1 [json_name = "checkoutSessionId"];
  string createrID = 2 [json_name = "createrID"];
}

message GetStripePaymentIntentInfoResponse {
  string checkoutSessionId = 1 [json_name = "checkoutSessionId"];
  string checkoutSessionStatus = 2 [json_name = "checkoutSessionStatus"];
  string paymentIntentId = 3 [json_name = "paymentIntentId"];
  string paymentIntentCreated = 4 [json_name = "paymentIntentCreated"];
  string paymentIntentStatus = 5 [json_name = "paymentIntentStatus"];
  string chargeId = 6 [json_name = "chargeId"];
  int64 payPrice = 7 [json_name = "payPrice"];
  string payExchangeRate = 8 [json_name = "payExchangeRate"];
  string payCurrency = 9 [json_name = "payCurrency"];
  string accountCurrency = 10 [json_name = "accountCurrency"];
  int64 afterRatePayPrice = 11 [json_name = "afterRatePayPrice"];
  int64 payHandingFee = 12 [json_name = "payHandingFee"];
  int64 afterRatePayNetAmount = 13 [json_name = "afterRatePayNetAmount"];
  string customerId = 14 [json_name = "customerId"];
  int64 refundPrice = 15 [json_name = "refundPrice"];
  string outTradeNo = 16 [json_name = "outTradeNo"];
}

message GetRefundInfoRequest {
  string refundId = 1 [json_name = "refundId"];
  string createrID = 2 [json_name = "createrID"];
}

message GetRefundInfoResponse {
  string refundStatus = 1 [json_name = "refundStatus"];
}

message AliWapPayRequest {
  string  subject = 1 [json_name = "subject"];
  string  productCode = 2 [json_name = "product_code"];
  string  outTradeNo = 3 [json_name = "out_trade_no"];
  string  quitUrl = 4 [json_name = "quit_url"];
  string  notifyUrl = 5 [json_name = "notify_url"];
  string  returnUrl = 6 [json_name = "return_url"];
  int32  totalAmount = 7 [json_name = "total_amount"];
  int32 timeExpire = 8 [json_name = "time_expire"];
  string domain                  = 9 ;
  string platform                = 10 ;
}

message AliWapPayResponse {
  string url = 1 [json_name = "url"];
  string logRecordID = 2 [json_name = "log_record_ID"];
  string msg = 3 [json_name="msg"];
}

message AliAppPayRequest {
  string  subject = 1 [json_name = "subject"];
  string  productCode = 2 [json_name = "product_code"];
  string  outTradeNo = 3 [json_name = "out_trade_no"];
  string  quitUrl = 4 [json_name = "quit_url"];
  string  notifyUrl = 5 [json_name = "notify_url"];
  string  returnUrl = 6 [json_name = "return_url"];
  string  totalAmount = 7 [json_name = "total_amount"];
  int32  timeExpire = 8 [json_name = "time_expire"];
  string domain                  = 9 ;
  string platform                = 10 ;
  int32 payType                = 11 ;
}

message AliAppPayResponse {
  string url = 1 [json_name = "url"];
  string logRecordID = 2 [json_name = "log_record_ID"];
  string msg = 3 [json_name="msg"];
}

message AliNativePayRequest {
  string  subject = 1 [json_name = "subject"];
  string  outTradeNo = 2 [json_name = "out_trade_no"];
  string  notifyUrl = 3 [json_name = "notify_url"];
  int32  totalAmount = 4 [json_name = "total_amount"];
  int32  timeExpire = 5 [json_name = "time_expire"];
  string domain = 6 [json_name = "domain"];
  string platform = 7 [json_name = "platform"];
}

message AliNativePayResponse {
  string url = 1 [json_name = "url"];
  string msg = 2 [json_name="msg"];
}

message AliPcWabPayRequest {
  string  subject = 1 [json_name = "subject"];
  string  outTradeNo = 2 [json_name = "out_trade_no"];
  string  notifyUrl = 3 [json_name = "notify_url"];
  int32  totalAmount = 4 [json_name = "total_amount"];
  int32  timeExpire = 5 [json_name = "time_expire"];
  string domain = 6 [json_name = "domain"];
  string platform = 7 [json_name = "platform"];
  string  quitUrl = 8 [json_name = "quit_url"];
  string  returnUrl = 9 [json_name = "return_url"];
}

message AliPcWabPayResponse {
  string pageRedirectionData = 1 [json_name = "page_redirection_data"];
  string msg = 2 [json_name="msg"];
}

message AliReFundRequest {
  string outTradeNo = 1 [json_name = "out_trade_no"];
  int32  totalAmount = 2 [json_name = "total_amount"];
}

message AliReFundResponse {
  string tradeNo = 1 [json_name = "trade_no"];
  string outTradeNo = 2 [json_name = "out_trade_no"];
  string buyer_logon_id = 3 [json_name = "buyer_logon_id"];
  string refund_fee = 4 [json_name = "refund_fee"];
  string msg = 5 [json_name="msg"];
}

message AliNotifyRequest {
  string  rawQuery = 1 [json_name="raw_query"];
}

message AliNotifyResponse {
  string  notifyTime = 1 [json_name="notify_time"];
  string  notifyType = 2 [json_name="notify_type"];
  string  notifyId = 3 [json_name="notify_id"];
  string  appId = 4 [json_name="app_id"];
  string  version = 5 [json_name="version"];
  string  signType = 6 [json_name="sign_type"];
  string  sign = 7 [json_name="sign"];
  string  tradeNo = 8 [json_name="trade_no"];
  string  outTradeNo = 9 [json_name="out_trade_no"]    ;
  string  buyerLogonId = 10 [json_name="buyer_logon_id"]  ;
  string  buyerId = 11 [json_name="buyer_id"] ;
  string  tradeStatus = 12 [json_name="trade_status"] ;
}

message AliQueryByOutTradeNoRequest {
  string outTradeNo  = 1 [json_name = "outTradeNo"];
}

message AliQueryByOutTradeNoResponse {
  string tradeNo = 1 [json_name = "trade_no"];
  string outTradeNo = 2 [json_name = "out_trade_no"];
  string buyerLogonId = 3 [json_name = "buyer_logon_id"];
  string tradeStatus = 4 [json_name = "trade_status"];
  string totalAmount = 5 [json_name = "total_amount"];
  string buyerUserID = 6 [json_name = "buyer_user_id"];
}

message AliRefundQueryByOutTradeNoRequest {
  string outTradeNo  = 1 [json_name = "outTradeNo"];
}

message AliRefundQueryByOutTradeNoResponse {
  string tradeNo = 1 [json_name = "trade_no"];
  string outTradeNo = 2 [json_name = "out_trade_no"];
  string out_request_no = 3 [json_name = "out_request_no"];
  string total_amount = 4 [json_name = "total_amount"];
  string refund_amount = 5 [json_name = "refund_amount"];
  string refund_status = 6 [json_name = "refund_status"];
}

message WechatJsApiPayRequest {
  string Description = 1 [json_name = "description"];
  string OutTradeNo  = 2 [json_name = "outTradeNo"];
  int64  Cent        = 3 [json_name = "cent"];
  string OpenID      = 4 [json_name = "openID"];
  string NotifyUrl   = 5 [json_name = "notifyUrl"];
  string AppID       = 6 [json_name = "appID"];
  string ClientIP    = 7 [json_name = "ClientIP"];
  uint32 recordId                = 9 ;
  string domain                  = 10 ;
  string platform                = 11 ;
  string scene             = 12 ;
  int32 timeExpire = 13 [json_name = "time_expire"];
}


message WechatJsApiPayResponse {
  string Appid = 1 [json_name = "appid"];
  string TimeStamp  = 2 [json_name = "timeStamp"];
  string  Package        = 3 [json_name = "package"];
  string SignType      = 4 [json_name = "signType"];
  string NonceStr   = 5 [json_name = "nonceStr"];
  string PaySign       = 6 [json_name = "paySign"];
  string PrepayId       = 7 [json_name = "prepayId"];

}

message WechatAppPayResponse {
  string Appid = 1 [json_name = "appid"];
  string TimeStamp  = 2 [json_name = "timeStamp"];
  string  Package        = 3 [json_name = "package"];
  string SignType      = 4 [json_name = "signType"];
  string NonceStr   = 5 [json_name = "nonceStr"];
  string PaySign       = 6 [json_name = "paySign"];
  string PrepayId       = 7 [json_name = "prepayId"];
  string PartnerId       = 8 [json_name = "partnerId"];
  string msg = 9 [json_name="msg"];
}

message WechatJsApiQueryByOutTradeNoRequest {
  string outTradeNo  = 1 [json_name = "outTradeNo"];
}

message WechatJsApiQueryByOutTradeNoResponse {
  string appID = 1 [json_name = "appID"];
  string mchID = 2 [json_name = "mch_id"];
  string outTradeNo = 3 [json_name = "out_trade_no"];
  string transactionId = 4 [json_name = "transaction_id"];
  string tradeType = 5 [json_name = "trade_type"];
  string tradeState = 6 [json_name = "trade_state"];
  string bankType = 7 [json_name = "bank_ype"];
  string successTime = 8 [json_name = "success_time"];
  string openID = 9 [json_name = "open_id"];
  int32 total = 10 [json_name = "total"];
}

message WechatAppQueryByOutTradeNoRequest {
  string outTradeNo  = 1 [json_name = "outTradeNo"];
}

message WechatAppQueryByOutTradeNoResponse {
  string appID = 1 [json_name = "appID"];// 服务提供商的应用ID
  string mchID = 2 [json_name = "mch_id"];// 服务商商户ID
  //  string subAppId = 3 [json_name = "sub_appid"]; // 子应用ID
  //  string subMchId = 4 [json_name = "sub_mchid"];// 子商户ID
  string outTradeNo = 5 [json_name = "out_trade_no"];// 订单号
  string transactionId = 6 [json_name = "transaction_id"];// 微信交易ID
  string tradeType = 7 [json_name = "trade_type"];// 交易类型
  string tradeState = 8 [json_name = "trade_state"];// 交易状态
  string tradeStateDesc = 9 [json_name = "trade_state_desc"]; // 交易状态描述
  string bankType = 10 [json_name = "bank_type"]; // 银行类型
  string attach = 11 [json_name = "attach"]; // 附加数据
  string successTime = 12 [json_name = "success_time"];// 支付成功时间

  message Payer {// 付款人信息
    string openid = 1 [json_name = "openid"];
  }

  message Amount {// 金额信息
    int64 total = 1 [json_name = "total"];// 总金额
    int64 payerTotal = 2 [json_name = "payer_total"];// 付款人支付的金额
    string currency = 3 [json_name = "currency"];// 货币类型
    string payerCurrency = 4 [json_name = "payer_currency"];// 付款人货币类型
  }

  message PromotionDetail {// 促销详情
    string couponId = 1 [json_name = "coupon_id"];// 优惠券ID
    string name = 2 [json_name = "name"];// 名称
    string scope = 3 [json_name = "scope"];// 范围
    string type = 4 [json_name = "type"];// 类型
    int64 amount = 5 [json_name = "amount"];// 金额
    string stockId = 6 [json_name = "stock_id"];// 库存ID
    int64 wechatpayContribute = 7 [json_name = "wechatpay_contribute"];// 微信支付贡献的金额
    int64 merchantContribute = 8 [json_name = "merchant_contribute"];// 商户贡献的金额
    int64 otherContribute = 9 [json_name = "other_contribute"];// 其他贡献的金额
    string currency = 10 [json_name = "currency"];// 货币类型
  }
  // 包含促销详情列表
  repeated PromotionDetail promotionDetail = 13 [json_name = "promotion_detail"];
  // 付款人信息
  Payer payer = 14;
  // 金额信息
  Amount amount = 15;
}


message GetPayByOutTradeNoRequest {
  string OutTradeNo              = 4 [json_name = "OutTradeNo"];
}

message GetPayByOutTradeNoResponse {
  string outTradeNo        = 1 ;
  uint32 recordId          = 2 ;
  string cent              = 3 ;
  string platform          = 4 ;
  string domain            = 5 ;
  string scene             = 6 ;
}

message WechatJsApiRefundsResponse {
  string RefundId                = 1 [json_name = "RefundId"];
  string OutRefundNo             = 2 [json_name = "OutRefundNo"];
  string TransactionId           = 3 [json_name = "TransactionId"];
  string OutTradeNo              = 4 [json_name = "OutTradeNo"];
  string SuccessTime             = 5 [json_name = "SuccessTime"];
  string CreateTime              = 6 [json_name = "CreateTime"];
  string Status                  = 7 [json_name = "Status"];
  string Amount                  = 8 [json_name = "Amount"];
}


message WechatNativePayRequest {
  string description = 1 [json_name = "description"];
  string outTradeNo  = 2 [json_name = "outTradeNo"];
  int32  cent        = 3 [json_name = "cent"];
  string notifyUrl   = 5 [json_name = "notifyUrl"];
  string appID       = 6 [json_name = "appID"];
  string clientIP    = 7 [json_name = "ClientIP"];
  uint32 recordId                = 8 ;
  string domain                  = 9 ;
  string platform                = 10 ;
  string scene             = 11 ;
  int32 timeExpire = 12 [json_name = "time_expire"];
}

message WechatNativePayResponse {
  string codeUrl = 1 [json_name = "code_url"];
}

message WechatNativeQueryByOutTradeNoRequest {
  string outTradeNo  = 1 [json_name = "outTradeNo"];
}

message WechatRefundQueryByOutRefundNoRequest {
  string outRefundNo = 1 [json_name = "out_refund_no"];
}

message WechatRefundQueryByOutRefundNoResponse {
  string refundId = 1 [json_name = "refund_id"];
  string outRefundNo = 2 [json_name = "out_refund_no"];
  string transactionId = 3 [json_name = "transaction_id"];
  string outTradeNo = 4 [json_name = "out_trade_no"];
  string channel = 5 [json_name = "channel"];
  string userReceivedAccount = 6 [json_name = "user_received_account"];
  string successTime = 7 [json_name = "success_time"];
  string createTime = 8 [json_name = "create_time"];
  string status = 9 [json_name = "status"];
  int32 total = 10 [json_name = "total"];
}

message WechatNativeQueryByOutTradeNoResponse {
  string appID = 1 [json_name = "appID"];
  string mchID = 2 [json_name = "mch_id"];
  string outTradeNo = 3 [json_name = "out_trade_no"];
  string transactionId = 4 [json_name = "transaction_id"];
  string tradeType = 5 [json_name = "trade_type"];
  string tradeState = 6 [json_name = "trade_state"];
  string bankType = 7 [json_name = "bank_ype"];
  string successTime = 8 [json_name = "success_time"];
  string openID = 9 [json_name = "open_id"];
  int32 total = 10 [json_name = "total"];
}

message WechatAppPayRequest {
  string Description = 1 [json_name = "description"];
  string OutTradeNo  = 2 [json_name = "outTradeNo"];
  int64  Cent        = 3 [json_name = "cent"];
  string OpenID      = 4 [json_name = "openID"];
  string NotifyUrl   = 5 [json_name = "notifyUrl"];
  string AppID       = 6 [json_name = "appID"];
  string ClientIP    = 7 [json_name = "ClientIP"];
  uint32 recordId                = 9 ;
  string domain                  = 10 ;
  string platform                = 11 ;
  string scene             = 12 ;
  int32 timeExpire = 13 [json_name = "time_expire"];
}

message WechatJsApiRefundsRequest {
  string Reason = 1 [json_name = "Reason"];
  string OutTradeNo = 2 [json_name = "OutTradeNo"];
  string OutRefundNo = 3 [json_name = "OutRefundNo"];
  string NotifyUrl = 4 [json_name = "NotifyUrl"];
  int64  Cent = 5 [json_name = "Cent"];
  string OpenID = 6 [json_name = "OpenID"];
  int64  Total =7[json_name = "Total"];
}


message WechatH5PayRequest {
  string Description = 1 [json_name = "description"];
  string OutTradeNo  = 2 [json_name = "outTradeNo"];
  int64  Cent        = 3 [json_name = "cent"];
  string OpenID      = 4 [json_name = "openID"];
  string NotifyUrl   = 5 [json_name = "notifyUrl"];
  string AppID       = 6 [json_name = "appID"];
  string ClientIP    = 7 [json_name = "ClientIP"];
  uint32 recordId                = 9 ;
  string domain                  = 10 ;
  string platform                = 11 ;
  string scene             = 12 ;
  int32 timeExpire = 13 [json_name = "time_expire"];
}

message WechatH5PayResponse {
  string h5Url = 1 [json_name = "h5_url"];
}

message WechatH5QueryByOutTradeNoRequest {
  string outTradeNo  = 1 [json_name = "outTradeNo"];
}

message WechatH5QueryByOutTradeNoResponse {
  string appID = 1 [json_name = "appID"];
  string mchID = 2 [json_name = "mch_id"];
  string outTradeNo = 3 [json_name = "out_trade_no"];
  string transactionId = 4 [json_name = "transaction_id"];
  string tradeType = 5 [json_name = "trade_type"];
  string tradeState = 6 [json_name = "trade_state"];
  string tradeStateDesc = 7 [json_name = "trade_state_desc"];
  string bankType = 8 [json_name = "bank_ype"];
  string successTime = 9 [json_name = "success_time"];
  string openID = 10 [json_name = "open_id"];
  int32 total = 11 [json_name = "total"];
}

message WechatPayOkRequest {
  string outTradeNo    = 1 ;
  string body    = 2 ;
}

message CommonResponse {
  bool Success = 1 [json_name = "success"];
  uint32 ID = 2 ;
}

message PayQueryRequest {
  string payType = 1;
  string outTradeNo = 2;
}

message PayQueryResponse {
  repeated PaymentOrderInfo infos = 1;
}

message PaymentOrderInfo {
  int64 id = 1;
  string createdAt = 2;
  string updatedAt = 3;
  string outTradeNo = 4;  // 外部流水号
  string channelTradeNo = 5;  // 渠道流水号
  int64  amount = 6;  // 用户支付总金额
  string  currency = 7; // 币种
  string  status = 8;
  string payTime = 9;
  string refundTime = 10;
  string payee = 11;
  string channelType = 12;  // 支付渠道 alipay-支付宝 wxpay-微信 stripe-Stripe支付
  string platform = 13;  // 支付具体来源，是app还是h5还是jsapi还是wap
  string domain = 14;  // 使用平台
  string businessType = 15;  // 业务类型，用来确认mq发送
  int64  logId = 16;
  string checkSessionId = 17;
  string  productUUID = 18;
  string  productName = 19;
  string  productImg = 20;
  string  productDescription = 21;
  int64 fee = 22; // 手续费
  int64 netIncome = 23; // 净收入【允许退款的最大金额】
}

message ExportPayRequest {
  string startTime = 1; // 开始时间
  string endTime = 2; // 结束时间
  string reportType = 3;
  string reportRangeNum = 4; // 年月
  string channelType = 5; // 交易渠道
  string currency = 6;
  string sortBy = 7; // 时间排序asc,desc
}

message ExportPayResponse {
  string reportType = 1; // 日报 月报
  int32 orderNum = 2; // 成功交易的订单数
  repeated Overview overview = 3; // 总览
  repeated ChannelIncome channelIncomes = 4; // 渠道交易情况
  repeated BusinessInfo businessInfos = 5; // 业务交易情况
  repeated OrderDetail orderDetails = 6; // sheet2,订单详情
}

message OrderDetail {
  int64 paymentOrderId = 1;
  string createdAt = 2;
  string updatedAt = 3;
  string outTradeNo = 4;  // 外部流水号
  string channelTradeNo = 5;  // 渠道流水号
  string currency = 6; // 币种
  int64  payAmount = 7;  // 付款金额
  int64 fee = 8;  // 手续费
  int64 netIncome = 9;  // 净收入
  string status = 10;
  string payTime = 11;
  string channelType = 12;  // 支付渠道 alipay-支付宝 wxpay-微信 stripe-Stripe支付
  string platform = 13;  // 支付具体来源，是app还是h5还是jsapi还是wap
  string domain = 14;  // 使用平台
  string businessType = 15;  // 业务类型，用来确认mq发送
  int64 logId = 16;
  string checkSessionId = 17;
  string  productUUID = 18;
  string  productName = 19;
  string  productImg = 20;
  string  productDescription = 21;
}

message BusinessInfo {
  string BusinessName = 1; // 业务名称
  string currency = 2; // 交易币种
  int32 orderNum = 3; // 成功交易的订单数
  int64 totalIncome = 4; // 总收入
  int64 totalFee = 5; // 总手续费
  int64 totalRefundAmount = 6; // 总退款费
  int64 netIncome = 7; // 净收入
}

message Overview {
  string currency = 1; // 交易币种
  int32 orderNum = 2; // 成功交易的订单数
  int64 totalIncome = 3; // 总收入
  int64 totalFee = 4; // 总手续费
  int64 totalRefundAmount = 5; // 总退款费
  int64 netIncome = 6; // 净收入
}

message ChannelIncome {
  string channelType = 1; // 交易渠道
  string currency = 2; // 交易币种
  int32 orderNum = 3; // 成功交易的订单数
  int64 totalIncome = 4; // 总收入
  int64 totalFee = 5; // 总手续费
  int64 totalRefundAmount = 6; // 总退款费
  int64 netIncome = 7; // 净收入
}
