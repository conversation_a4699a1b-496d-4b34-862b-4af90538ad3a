// Code generated by protoc-gen-gogo. DO NOT EDIT.
// source: pb/payment.proto

package payment

import (
	fmt "fmt"
	proto "github.com/golang/protobuf/proto"
	github_com_mwitkow_go_proto_validators "github.com/mwitkow/go-proto-validators"
	math "math"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

func (this *AntomPayQueryRequest) Validate() error {
	return nil
}
func (this *AntomPayQueryResponse) Validate() error {
	for _, item := range this.Infos {
		if item != nil {
			if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(item); err != nil {
				return github_com_mwitkow_go_proto_validators.FieldError("Infos", err)
			}
		}
	}
	return nil
}
func (this *AntomNotifyPayRequest) Validate() error {
	return nil
}
func (this *AntomNotifyPayResponse) Validate() error {
	return nil
}
func (this *CreatePayRequest) Validate() error {
	return nil
}
func (this *CreatePayResponse) Validate() error {
	return nil
}
func (this *CreateRefundRequest) Validate() error {
	return nil
}
func (this *CreateRefundResponse) Validate() error {
	return nil
}
func (this *NotifyPayRequest) Validate() error {
	if this.HttpRequest != nil {
		if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(this.HttpRequest); err != nil {
			return github_com_mwitkow_go_proto_validators.FieldError("HttpRequest", err)
		}
	}
	return nil
}
func (this *NotifyPayResponse) Validate() error {
	return nil
}
func (this *HttpRequest) Validate() error {
	// Validation of proto3 map<> fields is unsupported.
	return nil
}
func (this *CommonMsg) Validate() error {
	return nil
}
func (this *EmptyRequest) Validate() error {
	return nil
}
func (this *CreateStripeCheckoutSessionRequest) Validate() error {
	return nil
}
func (this *CreateStripeCheckoutSessionResponse) Validate() error {
	return nil
}
func (this *GetCheckoutWebhookRequest) Validate() error {
	return nil
}
func (this *GetCheckoutWebhookResponse) Validate() error {
	return nil
}
func (this *GetStripePaymentIntentInfoRequest) Validate() error {
	return nil
}
func (this *GetStripePaymentIntentInfoResponse) Validate() error {
	return nil
}
func (this *GetRefundInfoRequest) Validate() error {
	return nil
}
func (this *GetRefundInfoResponse) Validate() error {
	return nil
}
func (this *AliWapPayRequest) Validate() error {
	return nil
}
func (this *AliWapPayResponse) Validate() error {
	return nil
}
func (this *AliAppPayRequest) Validate() error {
	return nil
}
func (this *AliAppPayResponse) Validate() error {
	return nil
}
func (this *AliNativePayRequest) Validate() error {
	return nil
}
func (this *AliNativePayResponse) Validate() error {
	return nil
}
func (this *AliPcWabPayRequest) Validate() error {
	return nil
}
func (this *AliPcWabPayResponse) Validate() error {
	return nil
}
func (this *AliReFundRequest) Validate() error {
	return nil
}
func (this *AliReFundResponse) Validate() error {
	return nil
}
func (this *AliNotifyRequest) Validate() error {
	return nil
}
func (this *AliNotifyResponse) Validate() error {
	return nil
}
func (this *AliQueryByOutTradeNoRequest) Validate() error {
	return nil
}
func (this *AliQueryByOutTradeNoResponse) Validate() error {
	return nil
}
func (this *AliRefundQueryByOutTradeNoRequest) Validate() error {
	return nil
}
func (this *AliRefundQueryByOutTradeNoResponse) Validate() error {
	return nil
}
func (this *WechatJsApiPayRequest) Validate() error {
	return nil
}
func (this *WechatJsApiPayResponse) Validate() error {
	return nil
}
func (this *WechatAppPayResponse) Validate() error {
	return nil
}
func (this *WechatJsApiQueryByOutTradeNoRequest) Validate() error {
	return nil
}
func (this *WechatJsApiQueryByOutTradeNoResponse) Validate() error {
	return nil
}
func (this *WechatAppQueryByOutTradeNoRequest) Validate() error {
	return nil
}
func (this *WechatAppQueryByOutTradeNoResponse) Validate() error {
	for _, item := range this.PromotionDetail {
		if item != nil {
			if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(item); err != nil {
				return github_com_mwitkow_go_proto_validators.FieldError("PromotionDetail", err)
			}
		}
	}
	if this.Payer != nil {
		if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(this.Payer); err != nil {
			return github_com_mwitkow_go_proto_validators.FieldError("Payer", err)
		}
	}
	if this.Amount != nil {
		if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(this.Amount); err != nil {
			return github_com_mwitkow_go_proto_validators.FieldError("Amount", err)
		}
	}
	return nil
}
func (this *WechatAppQueryByOutTradeNoResponse_Payer) Validate() error {
	return nil
}
func (this *WechatAppQueryByOutTradeNoResponse_Amount) Validate() error {
	return nil
}
func (this *WechatAppQueryByOutTradeNoResponse_PromotionDetail) Validate() error {
	return nil
}
func (this *GetPayByOutTradeNoRequest) Validate() error {
	return nil
}
func (this *GetPayByOutTradeNoResponse) Validate() error {
	return nil
}
func (this *WechatJsApiRefundsResponse) Validate() error {
	return nil
}
func (this *WechatNativePayRequest) Validate() error {
	return nil
}
func (this *WechatNativePayResponse) Validate() error {
	return nil
}
func (this *WechatNativeQueryByOutTradeNoRequest) Validate() error {
	return nil
}
func (this *WechatRefundQueryByOutRefundNoRequest) Validate() error {
	return nil
}
func (this *WechatRefundQueryByOutRefundNoResponse) Validate() error {
	return nil
}
func (this *WechatNativeQueryByOutTradeNoResponse) Validate() error {
	return nil
}
func (this *WechatAppPayRequest) Validate() error {
	return nil
}
func (this *WechatJsApiRefundsRequest) Validate() error {
	return nil
}
func (this *WechatH5PayRequest) Validate() error {
	return nil
}
func (this *WechatH5PayResponse) Validate() error {
	return nil
}
func (this *WechatH5QueryByOutTradeNoRequest) Validate() error {
	return nil
}
func (this *WechatH5QueryByOutTradeNoResponse) Validate() error {
	return nil
}
func (this *WechatPayOkRequest) Validate() error {
	return nil
}
func (this *CommonResponse) Validate() error {
	return nil
}
func (this *PayQueryRequest) Validate() error {
	return nil
}
func (this *PayQueryResponse) Validate() error {
	for _, item := range this.Infos {
		if item != nil {
			if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(item); err != nil {
				return github_com_mwitkow_go_proto_validators.FieldError("Infos", err)
			}
		}
	}
	return nil
}
func (this *PaymentOrderInfo) Validate() error {
	return nil
}
func (this *ExportPayRequest) Validate() error {
	return nil
}
func (this *ExportPayResponse) Validate() error {
	for _, item := range this.Overview {
		if item != nil {
			if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(item); err != nil {
				return github_com_mwitkow_go_proto_validators.FieldError("Overview", err)
			}
		}
	}
	for _, item := range this.ChannelIncomes {
		if item != nil {
			if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(item); err != nil {
				return github_com_mwitkow_go_proto_validators.FieldError("ChannelIncomes", err)
			}
		}
	}
	for _, item := range this.BusinessInfos {
		if item != nil {
			if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(item); err != nil {
				return github_com_mwitkow_go_proto_validators.FieldError("BusinessInfos", err)
			}
		}
	}
	for _, item := range this.OrderDetails {
		if item != nil {
			if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(item); err != nil {
				return github_com_mwitkow_go_proto_validators.FieldError("OrderDetails", err)
			}
		}
	}
	return nil
}
func (this *OrderDetail) Validate() error {
	return nil
}
func (this *BusinessInfo) Validate() error {
	return nil
}
func (this *Overview) Validate() error {
	return nil
}
func (this *ChannelIncome) Validate() error {
	return nil
}
