// Code generated by protoc-gen-go-triple. DO NOT EDIT.
// versions:
// - protoc-gen-go-triple v1.0.8
// - protoc             v5.26.1
// source: pb/payment.proto

package payment

import (
	context "context"
	protocol "dubbo.apache.org/dubbo-go/v3/protocol"
	dubbo3 "dubbo.apache.org/dubbo-go/v3/protocol/dubbo3"
	invocation "dubbo.apache.org/dubbo-go/v3/protocol/invocation"
	grpc_go "github.com/dubbogo/grpc-go"
	codes "github.com/dubbogo/grpc-go/codes"
	metadata "github.com/dubbogo/grpc-go/metadata"
	status "github.com/dubbogo/grpc-go/status"
	common "github.com/dubbogo/triple/pkg/common"
	constant "github.com/dubbogo/triple/pkg/common/constant"
	triple "github.com/dubbogo/triple/pkg/triple"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc_go.SupportPackageIsVersion7

// PaymentCentClient is the client API for PaymentCent service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type PaymentCentClient interface {
	// 统一渠道支付
	CreatePay(ctx context.Context, in *CreatePayRequest, opts ...grpc_go.CallOption) (*CreatePayResponse, common.ErrorWithAttachment)
	NotifyPay(ctx context.Context, in *NotifyPayRequest, opts ...grpc_go.CallOption) (*NotifyPayResponse, common.ErrorWithAttachment)
	QueryPayByOutTradeNo(ctx context.Context, in *PayQueryRequest, opts ...grpc_go.CallOption) (*PayQueryResponse, common.ErrorWithAttachment)
	QueryExportPay(ctx context.Context, in *ExportPayRequest, opts ...grpc_go.CallOption) (*ExportPayResponse, common.ErrorWithAttachment)
	CreateRefund(ctx context.Context, in *CreateRefundRequest, opts ...grpc_go.CallOption) (*CreateRefundResponse, common.ErrorWithAttachment)
	StripeGermanyWebhook(ctx context.Context, in *GetCheckoutWebhookRequest, opts ...grpc_go.CallOption) (*GetCheckoutWebhookResponse, common.ErrorWithAttachment)
	StripeJapanWebhook(ctx context.Context, in *GetCheckoutWebhookRequest, opts ...grpc_go.CallOption) (*GetCheckoutWebhookResponse, common.ErrorWithAttachment)
	AliCommonWebhook(ctx context.Context, in *NotifyPayRequest, opts ...grpc_go.CallOption) (*NotifyPayResponse, common.ErrorWithAttachment)
	WechatFengLianWebhook(ctx context.Context, in *NotifyPayRequest, opts ...grpc_go.CallOption) (*NotifyPayResponse, common.ErrorWithAttachment)
	AntomWebhook(ctx context.Context, in *AntomNotifyPayRequest, opts ...grpc_go.CallOption) (*AntomNotifyPayResponse, common.ErrorWithAttachment)
	QueryAntomPayByCheckoutSessionId(ctx context.Context, in *AntomPayQueryRequest, opts ...grpc_go.CallOption) (*AntomPayQueryResponse, common.ErrorWithAttachment)
	// stripe支付
	CreateStripeCheckoutSession(ctx context.Context, in *CreateStripeCheckoutSessionRequest, opts ...grpc_go.CallOption) (*CreateStripeCheckoutSessionResponse, common.ErrorWithAttachment)
	// 支付宝支付
	AliWapPay(ctx context.Context, in *AliWapPayRequest, opts ...grpc_go.CallOption) (*AliWapPayResponse, common.ErrorWithAttachment)
	AliAppPay(ctx context.Context, in *AliAppPayRequest, opts ...grpc_go.CallOption) (*AliAppPayResponse, common.ErrorWithAttachment)
	AliNativePay(ctx context.Context, in *AliNativePayRequest, opts ...grpc_go.CallOption) (*AliNativePayResponse, common.ErrorWithAttachment)
	AliPcWabPay(ctx context.Context, in *AliPcWabPayRequest, opts ...grpc_go.CallOption) (*AliPcWabPayResponse, common.ErrorWithAttachment)
	AliReFund(ctx context.Context, in *AliReFundRequest, opts ...grpc_go.CallOption) (*AliReFundResponse, common.ErrorWithAttachment)
	AliNotify(ctx context.Context, in *AliNotifyRequest, opts ...grpc_go.CallOption) (*AliNotifyResponse, common.ErrorWithAttachment)
	AliQueryByOutTradeNo(ctx context.Context, in *AliQueryByOutTradeNoRequest, opts ...grpc_go.CallOption) (*AliQueryByOutTradeNoResponse, common.ErrorWithAttachment)
	AliRefundQueryByOutTradeNo(ctx context.Context, in *AliRefundQueryByOutTradeNoRequest, opts ...grpc_go.CallOption) (*AliRefundQueryByOutTradeNoResponse, common.ErrorWithAttachment)
	// 微信支付
	WechatJsApiPay(ctx context.Context, in *WechatJsApiPayRequest, opts ...grpc_go.CallOption) (*WechatJsApiPayResponse, common.ErrorWithAttachment)
	WechatJsApiQueryByOutTradeNo(ctx context.Context, in *WechatJsApiQueryByOutTradeNoRequest, opts ...grpc_go.CallOption) (*WechatJsApiQueryByOutTradeNoResponse, common.ErrorWithAttachment)
	GetPayByOutTradeNo(ctx context.Context, in *GetPayByOutTradeNoRequest, opts ...grpc_go.CallOption) (*GetPayByOutTradeNoResponse, common.ErrorWithAttachment)
	WechatJsApiRefunds(ctx context.Context, in *WechatJsApiRefundsRequest, opts ...grpc_go.CallOption) (*WechatJsApiRefundsResponse, common.ErrorWithAttachment)
	SetPayOk(ctx context.Context, in *WechatPayOkRequest, opts ...grpc_go.CallOption) (*CommonResponse, common.ErrorWithAttachment)
	WechatAppPay(ctx context.Context, in *WechatAppPayRequest, opts ...grpc_go.CallOption) (*WechatAppPayResponse, common.ErrorWithAttachment)
	WechatAppQueryByOutTradeNo(ctx context.Context, in *WechatAppQueryByOutTradeNoRequest, opts ...grpc_go.CallOption) (*WechatAppQueryByOutTradeNoResponse, common.ErrorWithAttachment)
	WechatNativePay(ctx context.Context, in *WechatNativePayRequest, opts ...grpc_go.CallOption) (*WechatNativePayResponse, common.ErrorWithAttachment)
	WechatNativeQueryByOutTradeNo(ctx context.Context, in *WechatNativeQueryByOutTradeNoRequest, opts ...grpc_go.CallOption) (*WechatNativeQueryByOutTradeNoResponse, common.ErrorWithAttachment)
	WechatRefundQueryByOutRefundNo(ctx context.Context, in *WechatRefundQueryByOutRefundNoRequest, opts ...grpc_go.CallOption) (*WechatRefundQueryByOutRefundNoResponse, common.ErrorWithAttachment)
	WechatH5Pay(ctx context.Context, in *WechatH5PayRequest, opts ...grpc_go.CallOption) (*WechatH5PayResponse, common.ErrorWithAttachment)
	WechatH5QueryByOutTradeNo(ctx context.Context, in *WechatH5QueryByOutTradeNoRequest, opts ...grpc_go.CallOption) (*WechatH5QueryByOutTradeNoResponse, common.ErrorWithAttachment)
}

type paymentCentClient struct {
	cc *triple.TripleConn
}

type PaymentCentClientImpl struct {
	CreatePay                        func(ctx context.Context, in *CreatePayRequest) (*CreatePayResponse, error)
	NotifyPay                        func(ctx context.Context, in *NotifyPayRequest) (*NotifyPayResponse, error)
	QueryPayByOutTradeNo             func(ctx context.Context, in *PayQueryRequest) (*PayQueryResponse, error)
	QueryExportPay                   func(ctx context.Context, in *ExportPayRequest) (*ExportPayResponse, error)
	CreateRefund                     func(ctx context.Context, in *CreateRefundRequest) (*CreateRefundResponse, error)
	StripeGermanyWebhook             func(ctx context.Context, in *GetCheckoutWebhookRequest) (*GetCheckoutWebhookResponse, error)
	StripeJapanWebhook               func(ctx context.Context, in *GetCheckoutWebhookRequest) (*GetCheckoutWebhookResponse, error)
	AliCommonWebhook                 func(ctx context.Context, in *NotifyPayRequest) (*NotifyPayResponse, error)
	WechatFengLianWebhook            func(ctx context.Context, in *NotifyPayRequest) (*NotifyPayResponse, error)
	AntomWebhook                     func(ctx context.Context, in *AntomNotifyPayRequest) (*AntomNotifyPayResponse, error)
	QueryAntomPayByCheckoutSessionId func(ctx context.Context, in *AntomPayQueryRequest) (*AntomPayQueryResponse, error)
	CreateStripeCheckoutSession      func(ctx context.Context, in *CreateStripeCheckoutSessionRequest) (*CreateStripeCheckoutSessionResponse, error)
	AliWapPay                        func(ctx context.Context, in *AliWapPayRequest) (*AliWapPayResponse, error)
	AliAppPay                        func(ctx context.Context, in *AliAppPayRequest) (*AliAppPayResponse, error)
	AliNativePay                     func(ctx context.Context, in *AliNativePayRequest) (*AliNativePayResponse, error)
	AliPcWabPay                      func(ctx context.Context, in *AliPcWabPayRequest) (*AliPcWabPayResponse, error)
	AliReFund                        func(ctx context.Context, in *AliReFundRequest) (*AliReFundResponse, error)
	AliNotify                        func(ctx context.Context, in *AliNotifyRequest) (*AliNotifyResponse, error)
	AliQueryByOutTradeNo             func(ctx context.Context, in *AliQueryByOutTradeNoRequest) (*AliQueryByOutTradeNoResponse, error)
	AliRefundQueryByOutTradeNo       func(ctx context.Context, in *AliRefundQueryByOutTradeNoRequest) (*AliRefundQueryByOutTradeNoResponse, error)
	WechatJsApiPay                   func(ctx context.Context, in *WechatJsApiPayRequest) (*WechatJsApiPayResponse, error)
	WechatJsApiQueryByOutTradeNo     func(ctx context.Context, in *WechatJsApiQueryByOutTradeNoRequest) (*WechatJsApiQueryByOutTradeNoResponse, error)
	GetPayByOutTradeNo               func(ctx context.Context, in *GetPayByOutTradeNoRequest) (*GetPayByOutTradeNoResponse, error)
	WechatJsApiRefunds               func(ctx context.Context, in *WechatJsApiRefundsRequest) (*WechatJsApiRefundsResponse, error)
	SetPayOk                         func(ctx context.Context, in *WechatPayOkRequest) (*CommonResponse, error)
	WechatAppPay                     func(ctx context.Context, in *WechatAppPayRequest) (*WechatAppPayResponse, error)
	WechatAppQueryByOutTradeNo       func(ctx context.Context, in *WechatAppQueryByOutTradeNoRequest) (*WechatAppQueryByOutTradeNoResponse, error)
	WechatNativePay                  func(ctx context.Context, in *WechatNativePayRequest) (*WechatNativePayResponse, error)
	WechatNativeQueryByOutTradeNo    func(ctx context.Context, in *WechatNativeQueryByOutTradeNoRequest) (*WechatNativeQueryByOutTradeNoResponse, error)
	WechatRefundQueryByOutRefundNo   func(ctx context.Context, in *WechatRefundQueryByOutRefundNoRequest) (*WechatRefundQueryByOutRefundNoResponse, error)
	WechatH5Pay                      func(ctx context.Context, in *WechatH5PayRequest) (*WechatH5PayResponse, error)
	WechatH5QueryByOutTradeNo        func(ctx context.Context, in *WechatH5QueryByOutTradeNoRequest) (*WechatH5QueryByOutTradeNoResponse, error)
}

func (c *PaymentCentClientImpl) GetDubboStub(cc *triple.TripleConn) PaymentCentClient {
	return NewPaymentCentClient(cc)
}

func (c *PaymentCentClientImpl) XXX_InterfaceName() string {
	return "payment.PaymentCent"
}

func NewPaymentCentClient(cc *triple.TripleConn) PaymentCentClient {
	return &paymentCentClient{cc}
}

func (c *paymentCentClient) CreatePay(ctx context.Context, in *CreatePayRequest, opts ...grpc_go.CallOption) (*CreatePayResponse, common.ErrorWithAttachment) {
	out := new(CreatePayResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/CreatePay", in, out)
}

func (c *paymentCentClient) NotifyPay(ctx context.Context, in *NotifyPayRequest, opts ...grpc_go.CallOption) (*NotifyPayResponse, common.ErrorWithAttachment) {
	out := new(NotifyPayResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/NotifyPay", in, out)
}

func (c *paymentCentClient) QueryPayByOutTradeNo(ctx context.Context, in *PayQueryRequest, opts ...grpc_go.CallOption) (*PayQueryResponse, common.ErrorWithAttachment) {
	out := new(PayQueryResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/QueryPayByOutTradeNo", in, out)
}

func (c *paymentCentClient) QueryExportPay(ctx context.Context, in *ExportPayRequest, opts ...grpc_go.CallOption) (*ExportPayResponse, common.ErrorWithAttachment) {
	out := new(ExportPayResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/QueryExportPay", in, out)
}

func (c *paymentCentClient) CreateRefund(ctx context.Context, in *CreateRefundRequest, opts ...grpc_go.CallOption) (*CreateRefundResponse, common.ErrorWithAttachment) {
	out := new(CreateRefundResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/CreateRefund", in, out)
}

func (c *paymentCentClient) StripeGermanyWebhook(ctx context.Context, in *GetCheckoutWebhookRequest, opts ...grpc_go.CallOption) (*GetCheckoutWebhookResponse, common.ErrorWithAttachment) {
	out := new(GetCheckoutWebhookResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/StripeGermanyWebhook", in, out)
}

func (c *paymentCentClient) StripeJapanWebhook(ctx context.Context, in *GetCheckoutWebhookRequest, opts ...grpc_go.CallOption) (*GetCheckoutWebhookResponse, common.ErrorWithAttachment) {
	out := new(GetCheckoutWebhookResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/StripeJapanWebhook", in, out)
}

func (c *paymentCentClient) AliCommonWebhook(ctx context.Context, in *NotifyPayRequest, opts ...grpc_go.CallOption) (*NotifyPayResponse, common.ErrorWithAttachment) {
	out := new(NotifyPayResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/AliCommonWebhook", in, out)
}

func (c *paymentCentClient) WechatFengLianWebhook(ctx context.Context, in *NotifyPayRequest, opts ...grpc_go.CallOption) (*NotifyPayResponse, common.ErrorWithAttachment) {
	out := new(NotifyPayResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/WechatFengLianWebhook", in, out)
}

func (c *paymentCentClient) AntomWebhook(ctx context.Context, in *AntomNotifyPayRequest, opts ...grpc_go.CallOption) (*AntomNotifyPayResponse, common.ErrorWithAttachment) {
	out := new(AntomNotifyPayResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/AntomWebhook", in, out)
}

func (c *paymentCentClient) QueryAntomPayByCheckoutSessionId(ctx context.Context, in *AntomPayQueryRequest, opts ...grpc_go.CallOption) (*AntomPayQueryResponse, common.ErrorWithAttachment) {
	out := new(AntomPayQueryResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/QueryAntomPayByCheckoutSessionId", in, out)
}

func (c *paymentCentClient) CreateStripeCheckoutSession(ctx context.Context, in *CreateStripeCheckoutSessionRequest, opts ...grpc_go.CallOption) (*CreateStripeCheckoutSessionResponse, common.ErrorWithAttachment) {
	out := new(CreateStripeCheckoutSessionResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/CreateStripeCheckoutSession", in, out)
}

func (c *paymentCentClient) AliWapPay(ctx context.Context, in *AliWapPayRequest, opts ...grpc_go.CallOption) (*AliWapPayResponse, common.ErrorWithAttachment) {
	out := new(AliWapPayResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/AliWapPay", in, out)
}

func (c *paymentCentClient) AliAppPay(ctx context.Context, in *AliAppPayRequest, opts ...grpc_go.CallOption) (*AliAppPayResponse, common.ErrorWithAttachment) {
	out := new(AliAppPayResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/AliAppPay", in, out)
}

func (c *paymentCentClient) AliNativePay(ctx context.Context, in *AliNativePayRequest, opts ...grpc_go.CallOption) (*AliNativePayResponse, common.ErrorWithAttachment) {
	out := new(AliNativePayResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/AliNativePay", in, out)
}

func (c *paymentCentClient) AliPcWabPay(ctx context.Context, in *AliPcWabPayRequest, opts ...grpc_go.CallOption) (*AliPcWabPayResponse, common.ErrorWithAttachment) {
	out := new(AliPcWabPayResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/AliPcWabPay", in, out)
}

func (c *paymentCentClient) AliReFund(ctx context.Context, in *AliReFundRequest, opts ...grpc_go.CallOption) (*AliReFundResponse, common.ErrorWithAttachment) {
	out := new(AliReFundResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/AliReFund", in, out)
}

func (c *paymentCentClient) AliNotify(ctx context.Context, in *AliNotifyRequest, opts ...grpc_go.CallOption) (*AliNotifyResponse, common.ErrorWithAttachment) {
	out := new(AliNotifyResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/AliNotify", in, out)
}

func (c *paymentCentClient) AliQueryByOutTradeNo(ctx context.Context, in *AliQueryByOutTradeNoRequest, opts ...grpc_go.CallOption) (*AliQueryByOutTradeNoResponse, common.ErrorWithAttachment) {
	out := new(AliQueryByOutTradeNoResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/AliQueryByOutTradeNo", in, out)
}

func (c *paymentCentClient) AliRefundQueryByOutTradeNo(ctx context.Context, in *AliRefundQueryByOutTradeNoRequest, opts ...grpc_go.CallOption) (*AliRefundQueryByOutTradeNoResponse, common.ErrorWithAttachment) {
	out := new(AliRefundQueryByOutTradeNoResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/AliRefundQueryByOutTradeNo", in, out)
}

func (c *paymentCentClient) WechatJsApiPay(ctx context.Context, in *WechatJsApiPayRequest, opts ...grpc_go.CallOption) (*WechatJsApiPayResponse, common.ErrorWithAttachment) {
	out := new(WechatJsApiPayResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/WechatJsApiPay", in, out)
}

func (c *paymentCentClient) WechatJsApiQueryByOutTradeNo(ctx context.Context, in *WechatJsApiQueryByOutTradeNoRequest, opts ...grpc_go.CallOption) (*WechatJsApiQueryByOutTradeNoResponse, common.ErrorWithAttachment) {
	out := new(WechatJsApiQueryByOutTradeNoResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/WechatJsApiQueryByOutTradeNo", in, out)
}

func (c *paymentCentClient) GetPayByOutTradeNo(ctx context.Context, in *GetPayByOutTradeNoRequest, opts ...grpc_go.CallOption) (*GetPayByOutTradeNoResponse, common.ErrorWithAttachment) {
	out := new(GetPayByOutTradeNoResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/GetPayByOutTradeNo", in, out)
}

func (c *paymentCentClient) WechatJsApiRefunds(ctx context.Context, in *WechatJsApiRefundsRequest, opts ...grpc_go.CallOption) (*WechatJsApiRefundsResponse, common.ErrorWithAttachment) {
	out := new(WechatJsApiRefundsResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/WechatJsApiRefunds", in, out)
}

func (c *paymentCentClient) SetPayOk(ctx context.Context, in *WechatPayOkRequest, opts ...grpc_go.CallOption) (*CommonResponse, common.ErrorWithAttachment) {
	out := new(CommonResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/SetPayOk", in, out)
}

func (c *paymentCentClient) WechatAppPay(ctx context.Context, in *WechatAppPayRequest, opts ...grpc_go.CallOption) (*WechatAppPayResponse, common.ErrorWithAttachment) {
	out := new(WechatAppPayResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/WechatAppPay", in, out)
}

func (c *paymentCentClient) WechatAppQueryByOutTradeNo(ctx context.Context, in *WechatAppQueryByOutTradeNoRequest, opts ...grpc_go.CallOption) (*WechatAppQueryByOutTradeNoResponse, common.ErrorWithAttachment) {
	out := new(WechatAppQueryByOutTradeNoResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/WechatAppQueryByOutTradeNo", in, out)
}

func (c *paymentCentClient) WechatNativePay(ctx context.Context, in *WechatNativePayRequest, opts ...grpc_go.CallOption) (*WechatNativePayResponse, common.ErrorWithAttachment) {
	out := new(WechatNativePayResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/WechatNativePay", in, out)
}

func (c *paymentCentClient) WechatNativeQueryByOutTradeNo(ctx context.Context, in *WechatNativeQueryByOutTradeNoRequest, opts ...grpc_go.CallOption) (*WechatNativeQueryByOutTradeNoResponse, common.ErrorWithAttachment) {
	out := new(WechatNativeQueryByOutTradeNoResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/WechatNativeQueryByOutTradeNo", in, out)
}

func (c *paymentCentClient) WechatRefundQueryByOutRefundNo(ctx context.Context, in *WechatRefundQueryByOutRefundNoRequest, opts ...grpc_go.CallOption) (*WechatRefundQueryByOutRefundNoResponse, common.ErrorWithAttachment) {
	out := new(WechatRefundQueryByOutRefundNoResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/WechatRefundQueryByOutRefundNo", in, out)
}

func (c *paymentCentClient) WechatH5Pay(ctx context.Context, in *WechatH5PayRequest, opts ...grpc_go.CallOption) (*WechatH5PayResponse, common.ErrorWithAttachment) {
	out := new(WechatH5PayResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/WechatH5Pay", in, out)
}

func (c *paymentCentClient) WechatH5QueryByOutTradeNo(ctx context.Context, in *WechatH5QueryByOutTradeNoRequest, opts ...grpc_go.CallOption) (*WechatH5QueryByOutTradeNoResponse, common.ErrorWithAttachment) {
	out := new(WechatH5QueryByOutTradeNoResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/WechatH5QueryByOutTradeNo", in, out)
}

// PaymentCentServer is the server API for PaymentCent service.
// All implementations must embed UnimplementedPaymentCentServer
// for forward compatibility
type PaymentCentServer interface {
	// 统一渠道支付
	CreatePay(context.Context, *CreatePayRequest) (*CreatePayResponse, error)
	NotifyPay(context.Context, *NotifyPayRequest) (*NotifyPayResponse, error)
	QueryPayByOutTradeNo(context.Context, *PayQueryRequest) (*PayQueryResponse, error)
	QueryExportPay(context.Context, *ExportPayRequest) (*ExportPayResponse, error)
	CreateRefund(context.Context, *CreateRefundRequest) (*CreateRefundResponse, error)
	StripeGermanyWebhook(context.Context, *GetCheckoutWebhookRequest) (*GetCheckoutWebhookResponse, error)
	StripeJapanWebhook(context.Context, *GetCheckoutWebhookRequest) (*GetCheckoutWebhookResponse, error)
	AliCommonWebhook(context.Context, *NotifyPayRequest) (*NotifyPayResponse, error)
	WechatFengLianWebhook(context.Context, *NotifyPayRequest) (*NotifyPayResponse, error)
	AntomWebhook(context.Context, *AntomNotifyPayRequest) (*AntomNotifyPayResponse, error)
	QueryAntomPayByCheckoutSessionId(context.Context, *AntomPayQueryRequest) (*AntomPayQueryResponse, error)
	// stripe支付
	CreateStripeCheckoutSession(context.Context, *CreateStripeCheckoutSessionRequest) (*CreateStripeCheckoutSessionResponse, error)
	// 支付宝支付
	AliWapPay(context.Context, *AliWapPayRequest) (*AliWapPayResponse, error)
	AliAppPay(context.Context, *AliAppPayRequest) (*AliAppPayResponse, error)
	AliNativePay(context.Context, *AliNativePayRequest) (*AliNativePayResponse, error)
	AliPcWabPay(context.Context, *AliPcWabPayRequest) (*AliPcWabPayResponse, error)
	AliReFund(context.Context, *AliReFundRequest) (*AliReFundResponse, error)
	AliNotify(context.Context, *AliNotifyRequest) (*AliNotifyResponse, error)
	AliQueryByOutTradeNo(context.Context, *AliQueryByOutTradeNoRequest) (*AliQueryByOutTradeNoResponse, error)
	AliRefundQueryByOutTradeNo(context.Context, *AliRefundQueryByOutTradeNoRequest) (*AliRefundQueryByOutTradeNoResponse, error)
	// 微信支付
	WechatJsApiPay(context.Context, *WechatJsApiPayRequest) (*WechatJsApiPayResponse, error)
	WechatJsApiQueryByOutTradeNo(context.Context, *WechatJsApiQueryByOutTradeNoRequest) (*WechatJsApiQueryByOutTradeNoResponse, error)
	GetPayByOutTradeNo(context.Context, *GetPayByOutTradeNoRequest) (*GetPayByOutTradeNoResponse, error)
	WechatJsApiRefunds(context.Context, *WechatJsApiRefundsRequest) (*WechatJsApiRefundsResponse, error)
	SetPayOk(context.Context, *WechatPayOkRequest) (*CommonResponse, error)
	WechatAppPay(context.Context, *WechatAppPayRequest) (*WechatAppPayResponse, error)
	WechatAppQueryByOutTradeNo(context.Context, *WechatAppQueryByOutTradeNoRequest) (*WechatAppQueryByOutTradeNoResponse, error)
	WechatNativePay(context.Context, *WechatNativePayRequest) (*WechatNativePayResponse, error)
	WechatNativeQueryByOutTradeNo(context.Context, *WechatNativeQueryByOutTradeNoRequest) (*WechatNativeQueryByOutTradeNoResponse, error)
	WechatRefundQueryByOutRefundNo(context.Context, *WechatRefundQueryByOutRefundNoRequest) (*WechatRefundQueryByOutRefundNoResponse, error)
	WechatH5Pay(context.Context, *WechatH5PayRequest) (*WechatH5PayResponse, error)
	WechatH5QueryByOutTradeNo(context.Context, *WechatH5QueryByOutTradeNoRequest) (*WechatH5QueryByOutTradeNoResponse, error)
	mustEmbedUnimplementedPaymentCentServer()
}

// UnimplementedPaymentCentServer must be embedded to have forward compatible implementations.
type UnimplementedPaymentCentServer struct {
	proxyImpl protocol.Invoker
}

func (UnimplementedPaymentCentServer) CreatePay(context.Context, *CreatePayRequest) (*CreatePayResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreatePay not implemented")
}
func (UnimplementedPaymentCentServer) NotifyPay(context.Context, *NotifyPayRequest) (*NotifyPayResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method NotifyPay not implemented")
}
func (UnimplementedPaymentCentServer) QueryPayByOutTradeNo(context.Context, *PayQueryRequest) (*PayQueryResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method QueryPayByOutTradeNo not implemented")
}
func (UnimplementedPaymentCentServer) QueryExportPay(context.Context, *ExportPayRequest) (*ExportPayResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method QueryExportPay not implemented")
}
func (UnimplementedPaymentCentServer) CreateRefund(context.Context, *CreateRefundRequest) (*CreateRefundResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateRefund not implemented")
}
func (UnimplementedPaymentCentServer) StripeGermanyWebhook(context.Context, *GetCheckoutWebhookRequest) (*GetCheckoutWebhookResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method StripeGermanyWebhook not implemented")
}
func (UnimplementedPaymentCentServer) StripeJapanWebhook(context.Context, *GetCheckoutWebhookRequest) (*GetCheckoutWebhookResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method StripeJapanWebhook not implemented")
}
func (UnimplementedPaymentCentServer) AliCommonWebhook(context.Context, *NotifyPayRequest) (*NotifyPayResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AliCommonWebhook not implemented")
}
func (UnimplementedPaymentCentServer) WechatFengLianWebhook(context.Context, *NotifyPayRequest) (*NotifyPayResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method WechatFengLianWebhook not implemented")
}
func (UnimplementedPaymentCentServer) AntomWebhook(context.Context, *AntomNotifyPayRequest) (*AntomNotifyPayResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AntomWebhook not implemented")
}
func (UnimplementedPaymentCentServer) QueryAntomPayByCheckoutSessionId(context.Context, *AntomPayQueryRequest) (*AntomPayQueryResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method QueryAntomPayByCheckoutSessionId not implemented")
}
func (UnimplementedPaymentCentServer) CreateStripeCheckoutSession(context.Context, *CreateStripeCheckoutSessionRequest) (*CreateStripeCheckoutSessionResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateStripeCheckoutSession not implemented")
}
func (UnimplementedPaymentCentServer) AliWapPay(context.Context, *AliWapPayRequest) (*AliWapPayResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AliWapPay not implemented")
}
func (UnimplementedPaymentCentServer) AliAppPay(context.Context, *AliAppPayRequest) (*AliAppPayResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AliAppPay not implemented")
}
func (UnimplementedPaymentCentServer) AliNativePay(context.Context, *AliNativePayRequest) (*AliNativePayResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AliNativePay not implemented")
}
func (UnimplementedPaymentCentServer) AliPcWabPay(context.Context, *AliPcWabPayRequest) (*AliPcWabPayResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AliPcWabPay not implemented")
}
func (UnimplementedPaymentCentServer) AliReFund(context.Context, *AliReFundRequest) (*AliReFundResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AliReFund not implemented")
}
func (UnimplementedPaymentCentServer) AliNotify(context.Context, *AliNotifyRequest) (*AliNotifyResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AliNotify not implemented")
}
func (UnimplementedPaymentCentServer) AliQueryByOutTradeNo(context.Context, *AliQueryByOutTradeNoRequest) (*AliQueryByOutTradeNoResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AliQueryByOutTradeNo not implemented")
}
func (UnimplementedPaymentCentServer) AliRefundQueryByOutTradeNo(context.Context, *AliRefundQueryByOutTradeNoRequest) (*AliRefundQueryByOutTradeNoResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AliRefundQueryByOutTradeNo not implemented")
}
func (UnimplementedPaymentCentServer) WechatJsApiPay(context.Context, *WechatJsApiPayRequest) (*WechatJsApiPayResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method WechatJsApiPay not implemented")
}
func (UnimplementedPaymentCentServer) WechatJsApiQueryByOutTradeNo(context.Context, *WechatJsApiQueryByOutTradeNoRequest) (*WechatJsApiQueryByOutTradeNoResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method WechatJsApiQueryByOutTradeNo not implemented")
}
func (UnimplementedPaymentCentServer) GetPayByOutTradeNo(context.Context, *GetPayByOutTradeNoRequest) (*GetPayByOutTradeNoResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetPayByOutTradeNo not implemented")
}
func (UnimplementedPaymentCentServer) WechatJsApiRefunds(context.Context, *WechatJsApiRefundsRequest) (*WechatJsApiRefundsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method WechatJsApiRefunds not implemented")
}
func (UnimplementedPaymentCentServer) SetPayOk(context.Context, *WechatPayOkRequest) (*CommonResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SetPayOk not implemented")
}
func (UnimplementedPaymentCentServer) WechatAppPay(context.Context, *WechatAppPayRequest) (*WechatAppPayResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method WechatAppPay not implemented")
}
func (UnimplementedPaymentCentServer) WechatAppQueryByOutTradeNo(context.Context, *WechatAppQueryByOutTradeNoRequest) (*WechatAppQueryByOutTradeNoResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method WechatAppQueryByOutTradeNo not implemented")
}
func (UnimplementedPaymentCentServer) WechatNativePay(context.Context, *WechatNativePayRequest) (*WechatNativePayResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method WechatNativePay not implemented")
}
func (UnimplementedPaymentCentServer) WechatNativeQueryByOutTradeNo(context.Context, *WechatNativeQueryByOutTradeNoRequest) (*WechatNativeQueryByOutTradeNoResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method WechatNativeQueryByOutTradeNo not implemented")
}
func (UnimplementedPaymentCentServer) WechatRefundQueryByOutRefundNo(context.Context, *WechatRefundQueryByOutRefundNoRequest) (*WechatRefundQueryByOutRefundNoResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method WechatRefundQueryByOutRefundNo not implemented")
}
func (UnimplementedPaymentCentServer) WechatH5Pay(context.Context, *WechatH5PayRequest) (*WechatH5PayResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method WechatH5Pay not implemented")
}
func (UnimplementedPaymentCentServer) WechatH5QueryByOutTradeNo(context.Context, *WechatH5QueryByOutTradeNoRequest) (*WechatH5QueryByOutTradeNoResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method WechatH5QueryByOutTradeNo not implemented")
}
func (s *UnimplementedPaymentCentServer) XXX_SetProxyImpl(impl protocol.Invoker) {
	s.proxyImpl = impl
}

func (s *UnimplementedPaymentCentServer) XXX_GetProxyImpl() protocol.Invoker {
	return s.proxyImpl
}

func (s *UnimplementedPaymentCentServer) XXX_ServiceDesc() *grpc_go.ServiceDesc {
	return &PaymentCent_ServiceDesc
}
func (s *UnimplementedPaymentCentServer) XXX_InterfaceName() string {
	return "payment.PaymentCent"
}

func (UnimplementedPaymentCentServer) mustEmbedUnimplementedPaymentCentServer() {}

// UnsafePaymentCentServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to PaymentCentServer will
// result in compilation errors.
type UnsafePaymentCentServer interface {
	mustEmbedUnimplementedPaymentCentServer()
}

func RegisterPaymentCentServer(s grpc_go.ServiceRegistrar, srv PaymentCentServer) {
	s.RegisterService(&PaymentCent_ServiceDesc, srv)
}

func _PaymentCent_CreatePay_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreatePayRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("CreatePay", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _PaymentCent_NotifyPay_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(NotifyPayRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("NotifyPay", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _PaymentCent_QueryPayByOutTradeNo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(PayQueryRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("QueryPayByOutTradeNo", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _PaymentCent_QueryExportPay_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(ExportPayRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("QueryExportPay", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _PaymentCent_CreateRefund_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateRefundRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("CreateRefund", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _PaymentCent_StripeGermanyWebhook_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetCheckoutWebhookRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("StripeGermanyWebhook", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _PaymentCent_StripeJapanWebhook_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetCheckoutWebhookRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("StripeJapanWebhook", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _PaymentCent_AliCommonWebhook_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(NotifyPayRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("AliCommonWebhook", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _PaymentCent_WechatFengLianWebhook_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(NotifyPayRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("WechatFengLianWebhook", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _PaymentCent_AntomWebhook_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(AntomNotifyPayRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("AntomWebhook", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _PaymentCent_QueryAntomPayByCheckoutSessionId_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(AntomPayQueryRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("QueryAntomPayByCheckoutSessionId", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _PaymentCent_CreateStripeCheckoutSession_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateStripeCheckoutSessionRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("CreateStripeCheckoutSession", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _PaymentCent_AliWapPay_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(AliWapPayRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("AliWapPay", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _PaymentCent_AliAppPay_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(AliAppPayRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("AliAppPay", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _PaymentCent_AliNativePay_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(AliNativePayRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("AliNativePay", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _PaymentCent_AliPcWabPay_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(AliPcWabPayRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("AliPcWabPay", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _PaymentCent_AliReFund_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(AliReFundRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("AliReFund", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _PaymentCent_AliNotify_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(AliNotifyRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("AliNotify", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _PaymentCent_AliQueryByOutTradeNo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(AliQueryByOutTradeNoRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("AliQueryByOutTradeNo", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _PaymentCent_AliRefundQueryByOutTradeNo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(AliRefundQueryByOutTradeNoRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("AliRefundQueryByOutTradeNo", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _PaymentCent_WechatJsApiPay_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(WechatJsApiPayRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("WechatJsApiPay", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _PaymentCent_WechatJsApiQueryByOutTradeNo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(WechatJsApiQueryByOutTradeNoRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("WechatJsApiQueryByOutTradeNo", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _PaymentCent_GetPayByOutTradeNo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetPayByOutTradeNoRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("GetPayByOutTradeNo", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _PaymentCent_WechatJsApiRefunds_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(WechatJsApiRefundsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("WechatJsApiRefunds", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _PaymentCent_SetPayOk_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(WechatPayOkRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("SetPayOk", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _PaymentCent_WechatAppPay_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(WechatAppPayRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("WechatAppPay", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _PaymentCent_WechatAppQueryByOutTradeNo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(WechatAppQueryByOutTradeNoRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("WechatAppQueryByOutTradeNo", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _PaymentCent_WechatNativePay_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(WechatNativePayRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("WechatNativePay", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _PaymentCent_WechatNativeQueryByOutTradeNo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(WechatNativeQueryByOutTradeNoRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("WechatNativeQueryByOutTradeNo", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _PaymentCent_WechatRefundQueryByOutRefundNo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(WechatRefundQueryByOutRefundNoRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("WechatRefundQueryByOutRefundNo", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _PaymentCent_WechatH5Pay_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(WechatH5PayRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("WechatH5Pay", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _PaymentCent_WechatH5QueryByOutTradeNo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(WechatH5QueryByOutTradeNoRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("WechatH5QueryByOutTradeNo", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

// PaymentCent_ServiceDesc is the grpc_go.ServiceDesc for PaymentCent service.
// It's only intended for direct use with grpc_go.RegisterService,
// and not to be introspected or modified (even as a copy)
var PaymentCent_ServiceDesc = grpc_go.ServiceDesc{
	ServiceName: "payment.PaymentCent",
	HandlerType: (*PaymentCentServer)(nil),
	Methods: []grpc_go.MethodDesc{
		{
			MethodName: "CreatePay",
			Handler:    _PaymentCent_CreatePay_Handler,
		},
		{
			MethodName: "NotifyPay",
			Handler:    _PaymentCent_NotifyPay_Handler,
		},
		{
			MethodName: "QueryPayByOutTradeNo",
			Handler:    _PaymentCent_QueryPayByOutTradeNo_Handler,
		},
		{
			MethodName: "QueryExportPay",
			Handler:    _PaymentCent_QueryExportPay_Handler,
		},
		{
			MethodName: "CreateRefund",
			Handler:    _PaymentCent_CreateRefund_Handler,
		},
		{
			MethodName: "StripeGermanyWebhook",
			Handler:    _PaymentCent_StripeGermanyWebhook_Handler,
		},
		{
			MethodName: "StripeJapanWebhook",
			Handler:    _PaymentCent_StripeJapanWebhook_Handler,
		},
		{
			MethodName: "AliCommonWebhook",
			Handler:    _PaymentCent_AliCommonWebhook_Handler,
		},
		{
			MethodName: "WechatFengLianWebhook",
			Handler:    _PaymentCent_WechatFengLianWebhook_Handler,
		},
		{
			MethodName: "AntomWebhook",
			Handler:    _PaymentCent_AntomWebhook_Handler,
		},
		{
			MethodName: "QueryAntomPayByCheckoutSessionId",
			Handler:    _PaymentCent_QueryAntomPayByCheckoutSessionId_Handler,
		},
		{
			MethodName: "CreateStripeCheckoutSession",
			Handler:    _PaymentCent_CreateStripeCheckoutSession_Handler,
		},
		{
			MethodName: "AliWapPay",
			Handler:    _PaymentCent_AliWapPay_Handler,
		},
		{
			MethodName: "AliAppPay",
			Handler:    _PaymentCent_AliAppPay_Handler,
		},
		{
			MethodName: "AliNativePay",
			Handler:    _PaymentCent_AliNativePay_Handler,
		},
		{
			MethodName: "AliPcWabPay",
			Handler:    _PaymentCent_AliPcWabPay_Handler,
		},
		{
			MethodName: "AliReFund",
			Handler:    _PaymentCent_AliReFund_Handler,
		},
		{
			MethodName: "AliNotify",
			Handler:    _PaymentCent_AliNotify_Handler,
		},
		{
			MethodName: "AliQueryByOutTradeNo",
			Handler:    _PaymentCent_AliQueryByOutTradeNo_Handler,
		},
		{
			MethodName: "AliRefundQueryByOutTradeNo",
			Handler:    _PaymentCent_AliRefundQueryByOutTradeNo_Handler,
		},
		{
			MethodName: "WechatJsApiPay",
			Handler:    _PaymentCent_WechatJsApiPay_Handler,
		},
		{
			MethodName: "WechatJsApiQueryByOutTradeNo",
			Handler:    _PaymentCent_WechatJsApiQueryByOutTradeNo_Handler,
		},
		{
			MethodName: "GetPayByOutTradeNo",
			Handler:    _PaymentCent_GetPayByOutTradeNo_Handler,
		},
		{
			MethodName: "WechatJsApiRefunds",
			Handler:    _PaymentCent_WechatJsApiRefunds_Handler,
		},
		{
			MethodName: "SetPayOk",
			Handler:    _PaymentCent_SetPayOk_Handler,
		},
		{
			MethodName: "WechatAppPay",
			Handler:    _PaymentCent_WechatAppPay_Handler,
		},
		{
			MethodName: "WechatAppQueryByOutTradeNo",
			Handler:    _PaymentCent_WechatAppQueryByOutTradeNo_Handler,
		},
		{
			MethodName: "WechatNativePay",
			Handler:    _PaymentCent_WechatNativePay_Handler,
		},
		{
			MethodName: "WechatNativeQueryByOutTradeNo",
			Handler:    _PaymentCent_WechatNativeQueryByOutTradeNo_Handler,
		},
		{
			MethodName: "WechatRefundQueryByOutRefundNo",
			Handler:    _PaymentCent_WechatRefundQueryByOutRefundNo_Handler,
		},
		{
			MethodName: "WechatH5Pay",
			Handler:    _PaymentCent_WechatH5Pay_Handler,
		},
		{
			MethodName: "WechatH5QueryByOutTradeNo",
			Handler:    _PaymentCent_WechatH5QueryByOutTradeNo_Handler,
		},
	},
	Streams:  []grpc_go.StreamDesc{},
	Metadata: "pb/payment.proto",
}
