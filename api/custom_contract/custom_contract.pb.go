// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.35.2
// 	protoc        v3.21.1
// source: pb/custom_contract.proto

package custom_contract

import (
	_ "github.com/mwitkow/go-proto-validators"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	_ "google.golang.org/protobuf/types/descriptorpb"
	_ "google.golang.org/protobuf/types/known/anypb"
	_ "google.golang.org/protobuf/types/known/emptypb"
	_ "google.golang.org/protobuf/types/known/wrapperspb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type SignType int32

const (
	SignType_SignTypeUnknown SignType = 0
	SignType_SignTypeCustom  SignType = 1
	SignType_SignTypeFdd     SignType = 2
)

// Enum value maps for SignType.
var (
	SignType_name = map[int32]string{
		0: "SignTypeUnknown",
		1: "SignTypeCustom",
		2: "SignTypeFdd",
	}
	SignType_value = map[string]int32{
		"SignTypeUnknown": 0,
		"SignTypeCustom":  1,
		"SignTypeFdd":     2,
	}
)

func (x SignType) Enum() *SignType {
	p := new(SignType)
	*p = x
	return p
}

func (x SignType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (SignType) Descriptor() protoreflect.EnumDescriptor {
	return file_pb_custom_contract_proto_enumTypes[0].Descriptor()
}

func (SignType) Type() protoreflect.EnumType {
	return &file_pb_custom_contract_proto_enumTypes[0]
}

func (x SignType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use SignType.Descriptor instead.
func (SignType) EnumDescriptor() ([]byte, []int) {
	return file_pb_custom_contract_proto_rawDescGZIP(), []int{0}
}

type UserInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CountryCode       string `protobuf:"bytes,1,opt,name=countryCode,proto3" json:"countryCode"`
	Phone             string `protobuf:"bytes,2,opt,name=phone,proto3" json:"phone"`
	UserName          string `protobuf:"bytes,3,opt,name=userName,proto3" json:"userName"`
	Gender            int32  `protobuf:"varint,4,opt,name=gender,proto3" json:"gender"`
	Birthday          string `protobuf:"bytes,5,opt,name=birthday,proto3" json:"birthday"`
	Address           string `protobuf:"bytes,6,opt,name=address,proto3" json:"address"`
	BankName          string `protobuf:"bytes,7,opt,name=bankName,proto3" json:"bankName"`
	BankNo            string `protobuf:"bytes,8,opt,name=bankNo,proto3" json:"bankNo"`
	FddCustomerId     string `protobuf:"bytes,9,opt,name=fddCustomerId,proto3" json:"fddCustomerId"`
	CardId            string `protobuf:"bytes,10,opt,name=cardId,proto3" json:"cardId"`
	CardType          int32  `protobuf:"varint,11,opt,name=cardType,proto3" json:"cardType"`
	Sex               string `protobuf:"bytes,12,opt,name=sex,proto3" json:"sex"`
	CardTypeName      string `protobuf:"bytes,13,opt,name=cardTypeName,proto3" json:"cardTypeName"`
	IsMainLand        int32  `protobuf:"varint,14,opt,name=isMainLand,proto3" json:"isMainLand"`
	UserID            string `protobuf:"bytes,15,opt,name=UserID,proto3" json:"UserID"`
	FddRegisterStatus int32  `protobuf:"varint,16,opt,name=fddRegisterStatus,proto3" json:"fddRegisterStatus"`
	RegisterType      int32  `protobuf:"varint,17,opt,name=registerType,proto3" json:"registerType"`
}

func (x *UserInfo) Reset() {
	*x = UserInfo{}
	mi := &file_pb_custom_contract_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UserInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserInfo) ProtoMessage() {}

func (x *UserInfo) ProtoReflect() protoreflect.Message {
	mi := &file_pb_custom_contract_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserInfo.ProtoReflect.Descriptor instead.
func (*UserInfo) Descriptor() ([]byte, []int) {
	return file_pb_custom_contract_proto_rawDescGZIP(), []int{0}
}

func (x *UserInfo) GetCountryCode() string {
	if x != nil {
		return x.CountryCode
	}
	return ""
}

func (x *UserInfo) GetPhone() string {
	if x != nil {
		return x.Phone
	}
	return ""
}

func (x *UserInfo) GetUserName() string {
	if x != nil {
		return x.UserName
	}
	return ""
}

func (x *UserInfo) GetGender() int32 {
	if x != nil {
		return x.Gender
	}
	return 0
}

func (x *UserInfo) GetBirthday() string {
	if x != nil {
		return x.Birthday
	}
	return ""
}

func (x *UserInfo) GetAddress() string {
	if x != nil {
		return x.Address
	}
	return ""
}

func (x *UserInfo) GetBankName() string {
	if x != nil {
		return x.BankName
	}
	return ""
}

func (x *UserInfo) GetBankNo() string {
	if x != nil {
		return x.BankNo
	}
	return ""
}

func (x *UserInfo) GetFddCustomerId() string {
	if x != nil {
		return x.FddCustomerId
	}
	return ""
}

func (x *UserInfo) GetCardId() string {
	if x != nil {
		return x.CardId
	}
	return ""
}

func (x *UserInfo) GetCardType() int32 {
	if x != nil {
		return x.CardType
	}
	return 0
}

func (x *UserInfo) GetSex() string {
	if x != nil {
		return x.Sex
	}
	return ""
}

func (x *UserInfo) GetCardTypeName() string {
	if x != nil {
		return x.CardTypeName
	}
	return ""
}

func (x *UserInfo) GetIsMainLand() int32 {
	if x != nil {
		return x.IsMainLand
	}
	return 0
}

func (x *UserInfo) GetUserID() string {
	if x != nil {
		return x.UserID
	}
	return ""
}

func (x *UserInfo) GetFddRegisterStatus() int32 {
	if x != nil {
		return x.FddRegisterStatus
	}
	return 0
}

func (x *UserInfo) GetRegisterType() int32 {
	if x != nil {
		return x.RegisterType
	}
	return 0
}

// ProtocolSignOffline
type ProtocolSignOfflineReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UserInfo            *UserInfo `protobuf:"bytes,1,opt,name=userInfo,proto3" json:"userInfo"`
	SignImgFileData     string    `protobuf:"bytes,2,opt,name=signImgFileData,proto3" json:"signImgFileData"`
	SignOrder           int32     `protobuf:"varint,3,opt,name=signOrder,proto3" json:"signOrder"`
	BidNum              string    `protobuf:"bytes,4,opt,name=bidNum,proto3" json:"bidNum"`
	BidPrice            string    `protobuf:"bytes,5,opt,name=bidPrice,proto3" json:"bidPrice"`
	IsMainLand          uint32    `protobuf:"varint,6,opt,name=isMainLand,proto3" json:"isMainLand"`
	AuctionArtworkUuid  string    `protobuf:"bytes,7,opt,name=auctionArtworkUuid,proto3" json:"auctionArtworkUuid"`
	TestReturnHost      string    `protobuf:"bytes,8,opt,name=testReturnHost,proto3" json:"testReturnHost"`
	TestReturnEndPoint  string    `protobuf:"bytes,9,opt,name=testReturnEndPoint,proto3" json:"testReturnEndPoint"`
	AuctionUuid         string    `protobuf:"bytes,10,opt,name=auctionUuid,proto3" json:"auctionUuid"`
	AuctionArtworkUuids []string  `protobuf:"bytes,11,rep,name=auctionArtworkUuids,proto3" json:"auctionArtworkUuids"`
	UserID              string    `protobuf:"bytes,12,opt,name=userID,proto3" json:"userID"`
}

func (x *ProtocolSignOfflineReq) Reset() {
	*x = ProtocolSignOfflineReq{}
	mi := &file_pb_custom_contract_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ProtocolSignOfflineReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ProtocolSignOfflineReq) ProtoMessage() {}

func (x *ProtocolSignOfflineReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_custom_contract_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ProtocolSignOfflineReq.ProtoReflect.Descriptor instead.
func (*ProtocolSignOfflineReq) Descriptor() ([]byte, []int) {
	return file_pb_custom_contract_proto_rawDescGZIP(), []int{1}
}

func (x *ProtocolSignOfflineReq) GetUserInfo() *UserInfo {
	if x != nil {
		return x.UserInfo
	}
	return nil
}

func (x *ProtocolSignOfflineReq) GetSignImgFileData() string {
	if x != nil {
		return x.SignImgFileData
	}
	return ""
}

func (x *ProtocolSignOfflineReq) GetSignOrder() int32 {
	if x != nil {
		return x.SignOrder
	}
	return 0
}

func (x *ProtocolSignOfflineReq) GetBidNum() string {
	if x != nil {
		return x.BidNum
	}
	return ""
}

func (x *ProtocolSignOfflineReq) GetBidPrice() string {
	if x != nil {
		return x.BidPrice
	}
	return ""
}

func (x *ProtocolSignOfflineReq) GetIsMainLand() uint32 {
	if x != nil {
		return x.IsMainLand
	}
	return 0
}

func (x *ProtocolSignOfflineReq) GetAuctionArtworkUuid() string {
	if x != nil {
		return x.AuctionArtworkUuid
	}
	return ""
}

func (x *ProtocolSignOfflineReq) GetTestReturnHost() string {
	if x != nil {
		return x.TestReturnHost
	}
	return ""
}

func (x *ProtocolSignOfflineReq) GetTestReturnEndPoint() string {
	if x != nil {
		return x.TestReturnEndPoint
	}
	return ""
}

func (x *ProtocolSignOfflineReq) GetAuctionUuid() string {
	if x != nil {
		return x.AuctionUuid
	}
	return ""
}

func (x *ProtocolSignOfflineReq) GetAuctionArtworkUuids() []string {
	if x != nil {
		return x.AuctionArtworkUuids
	}
	return nil
}

func (x *ProtocolSignOfflineReq) GetUserID() string {
	if x != nil {
		return x.UserID
	}
	return ""
}

type ProtocolSignOfflineResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	SignType     SignType `protobuf:"varint,2,opt,name=signType,proto3,enum=CustomContract.SignType" json:"signType"`
	UserID       string   `protobuf:"bytes,3,opt,name=userID,proto3" json:"userID"`
	FddVerifyUrl string   `protobuf:"bytes,4,opt,name=fddVerifyUrl,proto3" json:"fddVerifyUrl"`
}

func (x *ProtocolSignOfflineResp) Reset() {
	*x = ProtocolSignOfflineResp{}
	mi := &file_pb_custom_contract_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ProtocolSignOfflineResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ProtocolSignOfflineResp) ProtoMessage() {}

func (x *ProtocolSignOfflineResp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_custom_contract_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ProtocolSignOfflineResp.ProtoReflect.Descriptor instead.
func (*ProtocolSignOfflineResp) Descriptor() ([]byte, []int) {
	return file_pb_custom_contract_proto_rawDescGZIP(), []int{2}
}

func (x *ProtocolSignOfflineResp) GetSignType() SignType {
	if x != nil {
		return x.SignType
	}
	return SignType_SignTypeUnknown
}

func (x *ProtocolSignOfflineResp) GetUserID() string {
	if x != nil {
		return x.UserID
	}
	return ""
}

func (x *ProtocolSignOfflineResp) GetFddVerifyUrl() string {
	if x != nil {
		return x.FddVerifyUrl
	}
	return ""
}

// ProtocolSignOnline
type ProtocolSignOnlineReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UserID              string    `protobuf:"bytes,1,opt,name=userID,proto3" json:"userID"`
	SignImgFileData     string    `protobuf:"bytes,2,opt,name=signImgFileData,proto3" json:"signImgFileData"`
	IsMainLand          uint32    `protobuf:"varint,3,opt,name=isMainLand,proto3" json:"isMainLand"`
	Phone               string    `protobuf:"bytes,4,opt,name=phone,proto3" json:"phone"`
	FddCustomerId       string    `protobuf:"bytes,5,opt,name=fddCustomerId,proto3" json:"fddCustomerId"`
	AuctionArtworkUuid  string    `protobuf:"bytes,6,opt,name=auctionArtworkUuid,proto3" json:"auctionArtworkUuid"`
	TestReturnHost      string    `protobuf:"bytes,8,opt,name=testReturnHost,proto3" json:"testReturnHost"`
	TestReturnEndPoint  string    `protobuf:"bytes,9,opt,name=testReturnEndPoint,proto3" json:"testReturnEndPoint"`
	UserInfo            *UserInfo `protobuf:"bytes,10,opt,name=userInfo,proto3" json:"userInfo"`
	AuctionArtworkUuids []string  `protobuf:"bytes,11,rep,name=auctionArtworkUuids,proto3" json:"auctionArtworkUuids"`
}

func (x *ProtocolSignOnlineReq) Reset() {
	*x = ProtocolSignOnlineReq{}
	mi := &file_pb_custom_contract_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ProtocolSignOnlineReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ProtocolSignOnlineReq) ProtoMessage() {}

func (x *ProtocolSignOnlineReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_custom_contract_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ProtocolSignOnlineReq.ProtoReflect.Descriptor instead.
func (*ProtocolSignOnlineReq) Descriptor() ([]byte, []int) {
	return file_pb_custom_contract_proto_rawDescGZIP(), []int{3}
}

func (x *ProtocolSignOnlineReq) GetUserID() string {
	if x != nil {
		return x.UserID
	}
	return ""
}

func (x *ProtocolSignOnlineReq) GetSignImgFileData() string {
	if x != nil {
		return x.SignImgFileData
	}
	return ""
}

func (x *ProtocolSignOnlineReq) GetIsMainLand() uint32 {
	if x != nil {
		return x.IsMainLand
	}
	return 0
}

func (x *ProtocolSignOnlineReq) GetPhone() string {
	if x != nil {
		return x.Phone
	}
	return ""
}

func (x *ProtocolSignOnlineReq) GetFddCustomerId() string {
	if x != nil {
		return x.FddCustomerId
	}
	return ""
}

func (x *ProtocolSignOnlineReq) GetAuctionArtworkUuid() string {
	if x != nil {
		return x.AuctionArtworkUuid
	}
	return ""
}

func (x *ProtocolSignOnlineReq) GetTestReturnHost() string {
	if x != nil {
		return x.TestReturnHost
	}
	return ""
}

func (x *ProtocolSignOnlineReq) GetTestReturnEndPoint() string {
	if x != nil {
		return x.TestReturnEndPoint
	}
	return ""
}

func (x *ProtocolSignOnlineReq) GetUserInfo() *UserInfo {
	if x != nil {
		return x.UserInfo
	}
	return nil
}

func (x *ProtocolSignOnlineReq) GetAuctionArtworkUuids() []string {
	if x != nil {
		return x.AuctionArtworkUuids
	}
	return nil
}

type ProtocolSignOnlineResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Data *ProtocolSignOnlineResp_Info `protobuf:"bytes,1,opt,name=Data,proto3" json:"Data"`
}

func (x *ProtocolSignOnlineResp) Reset() {
	*x = ProtocolSignOnlineResp{}
	mi := &file_pb_custom_contract_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ProtocolSignOnlineResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ProtocolSignOnlineResp) ProtoMessage() {}

func (x *ProtocolSignOnlineResp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_custom_contract_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ProtocolSignOnlineResp.ProtoReflect.Descriptor instead.
func (*ProtocolSignOnlineResp) Descriptor() ([]byte, []int) {
	return file_pb_custom_contract_proto_rawDescGZIP(), []int{4}
}

func (x *ProtocolSignOnlineResp) GetData() *ProtocolSignOnlineResp_Info {
	if x != nil {
		return x.Data
	}
	return nil
}

// TemplateData
type TemplateDataReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TemplateId string `protobuf:"bytes,1,opt,name=templateId,proto3" json:"templateId"`
}

func (x *TemplateDataReq) Reset() {
	*x = TemplateDataReq{}
	mi := &file_pb_custom_contract_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TemplateDataReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TemplateDataReq) ProtoMessage() {}

func (x *TemplateDataReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_custom_contract_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TemplateDataReq.ProtoReflect.Descriptor instead.
func (*TemplateDataReq) Descriptor() ([]byte, []int) {
	return file_pb_custom_contract_proto_rawDescGZIP(), []int{5}
}

func (x *TemplateDataReq) GetTemplateId() string {
	if x != nil {
		return x.TemplateId
	}
	return ""
}

type TemplateDataResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Data []*TemplateDataResp_Info `protobuf:"bytes,1,rep,name=Data,proto3" json:"Data"`
}

func (x *TemplateDataResp) Reset() {
	*x = TemplateDataResp{}
	mi := &file_pb_custom_contract_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TemplateDataResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TemplateDataResp) ProtoMessage() {}

func (x *TemplateDataResp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_custom_contract_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TemplateDataResp.ProtoReflect.Descriptor instead.
func (*TemplateDataResp) Descriptor() ([]byte, []int) {
	return file_pb_custom_contract_proto_rawDescGZIP(), []int{6}
}

func (x *TemplateDataResp) GetData() []*TemplateDataResp_Info {
	if x != nil {
		return x.Data
	}
	return nil
}

// UserInfo
type UserInfoReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Phone        string `protobuf:"bytes,1,opt,name=phone,proto3" json:"phone"`
	NeedRegister bool   `protobuf:"varint,2,opt,name=needRegister,proto3" json:"needRegister"`
	CountryCode  string `protobuf:"bytes,3,opt,name=countryCode,proto3" json:"countryCode"`
	CardId       string `protobuf:"bytes,4,opt,name=cardId,proto3" json:"cardId"`
	UserId       string `protobuf:"bytes,5,opt,name=userId,proto3" json:"userId"`
	RegisterType int32  `protobuf:"varint,6,opt,name=registerType,proto3" json:"registerType"`
	Nickname     string `protobuf:"bytes,7,opt,name=nickname,proto3" json:"nickname"`
}

func (x *UserInfoReq) Reset() {
	*x = UserInfoReq{}
	mi := &file_pb_custom_contract_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UserInfoReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserInfoReq) ProtoMessage() {}

func (x *UserInfoReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_custom_contract_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserInfoReq.ProtoReflect.Descriptor instead.
func (*UserInfoReq) Descriptor() ([]byte, []int) {
	return file_pb_custom_contract_proto_rawDescGZIP(), []int{7}
}

func (x *UserInfoReq) GetPhone() string {
	if x != nil {
		return x.Phone
	}
	return ""
}

func (x *UserInfoReq) GetNeedRegister() bool {
	if x != nil {
		return x.NeedRegister
	}
	return false
}

func (x *UserInfoReq) GetCountryCode() string {
	if x != nil {
		return x.CountryCode
	}
	return ""
}

func (x *UserInfoReq) GetCardId() string {
	if x != nil {
		return x.CardId
	}
	return ""
}

func (x *UserInfoReq) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *UserInfoReq) GetRegisterType() int32 {
	if x != nil {
		return x.RegisterType
	}
	return 0
}

func (x *UserInfoReq) GetNickname() string {
	if x != nil {
		return x.Nickname
	}
	return ""
}

type UserInfoReqResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UserId            string `protobuf:"bytes,1,opt,name=userId,proto3" json:"userId"`
	Phone             string `protobuf:"bytes,2,opt,name=phone,proto3" json:"phone"`
	FddCustomerId     string `protobuf:"bytes,3,opt,name=fddCustomerId,proto3" json:"fddCustomerId"`
	CountryCode       string `protobuf:"bytes,4,opt,name=countryCode,proto3" json:"countryCode"`
	IsMainland        uint32 `protobuf:"varint,5,opt,name=isMainland,proto3" json:"isMainland"`
	FddRegisterStatus int32  `protobuf:"varint,6,opt,name=fddRegisterStatus,proto3" json:"fddRegisterStatus"`
	Status            int32  `protobuf:"varint,7,opt,name=status,proto3" json:"status"`
}

func (x *UserInfoReqResp) Reset() {
	*x = UserInfoReqResp{}
	mi := &file_pb_custom_contract_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UserInfoReqResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserInfoReqResp) ProtoMessage() {}

func (x *UserInfoReqResp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_custom_contract_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserInfoReqResp.ProtoReflect.Descriptor instead.
func (*UserInfoReqResp) Descriptor() ([]byte, []int) {
	return file_pb_custom_contract_proto_rawDescGZIP(), []int{8}
}

func (x *UserInfoReqResp) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *UserInfoReqResp) GetPhone() string {
	if x != nil {
		return x.Phone
	}
	return ""
}

func (x *UserInfoReqResp) GetFddCustomerId() string {
	if x != nil {
		return x.FddCustomerId
	}
	return ""
}

func (x *UserInfoReqResp) GetCountryCode() string {
	if x != nil {
		return x.CountryCode
	}
	return ""
}

func (x *UserInfoReqResp) GetIsMainland() uint32 {
	if x != nil {
		return x.IsMainland
	}
	return 0
}

func (x *UserInfoReqResp) GetFddRegisterStatus() int32 {
	if x != nil {
		return x.FddRegisterStatus
	}
	return 0
}

func (x *UserInfoReqResp) GetStatus() int32 {
	if x != nil {
		return x.Status
	}
	return 0
}

// UpdateUserInfo
type UpdateUserInfoReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UserId        string    `protobuf:"bytes,1,opt,name=userId,proto3" json:"userId"`
	FddCustomerId string    `protobuf:"bytes,2,opt,name=fddCustomerId,proto3" json:"fddCustomerId"`
	IsMainland    uint32    `protobuf:"varint,3,opt,name=isMainland,proto3" json:"isMainland"`
	TransactionNo string    `protobuf:"bytes,4,opt,name=transactionNo,proto3" json:"transactionNo"`
	FddInfo       string    `protobuf:"bytes,5,opt,name=fddInfo,proto3" json:"fddInfo"`
	FddStatus     int32     `protobuf:"varint,6,opt,name=fddStatus,proto3" json:"fddStatus"`
	RegisterType  int32     `protobuf:"varint,7,opt,name=registerType,proto3" json:"registerType"`
	Status        int32     `protobuf:"varint,8,opt,name=status,proto3" json:"status"`
	UserInfo      *UserInfo `protobuf:"bytes,9,opt,name=userInfo,proto3" json:"userInfo"`
}

func (x *UpdateUserInfoReq) Reset() {
	*x = UpdateUserInfoReq{}
	mi := &file_pb_custom_contract_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateUserInfoReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateUserInfoReq) ProtoMessage() {}

func (x *UpdateUserInfoReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_custom_contract_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateUserInfoReq.ProtoReflect.Descriptor instead.
func (*UpdateUserInfoReq) Descriptor() ([]byte, []int) {
	return file_pb_custom_contract_proto_rawDescGZIP(), []int{9}
}

func (x *UpdateUserInfoReq) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *UpdateUserInfoReq) GetFddCustomerId() string {
	if x != nil {
		return x.FddCustomerId
	}
	return ""
}

func (x *UpdateUserInfoReq) GetIsMainland() uint32 {
	if x != nil {
		return x.IsMainland
	}
	return 0
}

func (x *UpdateUserInfoReq) GetTransactionNo() string {
	if x != nil {
		return x.TransactionNo
	}
	return ""
}

func (x *UpdateUserInfoReq) GetFddInfo() string {
	if x != nil {
		return x.FddInfo
	}
	return ""
}

func (x *UpdateUserInfoReq) GetFddStatus() int32 {
	if x != nil {
		return x.FddStatus
	}
	return 0
}

func (x *UpdateUserInfoReq) GetRegisterType() int32 {
	if x != nil {
		return x.RegisterType
	}
	return 0
}

func (x *UpdateUserInfoReq) GetStatus() int32 {
	if x != nil {
		return x.Status
	}
	return 0
}

func (x *UpdateUserInfoReq) GetUserInfo() *UserInfo {
	if x != nil {
		return x.UserInfo
	}
	return nil
}

type UpdateUserInfoResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UserId string `protobuf:"bytes,1,opt,name=userId,proto3" json:"userId"`
}

func (x *UpdateUserInfoResp) Reset() {
	*x = UpdateUserInfoResp{}
	mi := &file_pb_custom_contract_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateUserInfoResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateUserInfoResp) ProtoMessage() {}

func (x *UpdateUserInfoResp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_custom_contract_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateUserInfoResp.ProtoReflect.Descriptor instead.
func (*UpdateUserInfoResp) Descriptor() ([]byte, []int) {
	return file_pb_custom_contract_proto_rawDescGZIP(), []int{10}
}

func (x *UpdateUserInfoResp) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

// UpdateContract
type UpdateContractReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ContractId         string `protobuf:"bytes,1,opt,name=contractId,proto3" json:"contractId"`
	TemplateId         string `protobuf:"bytes,2,opt,name=templateId,proto3" json:"templateId"`
	ContractName       string `protobuf:"bytes,3,opt,name=contractName,proto3" json:"contractName"`
	ShortName          string `protobuf:"bytes,4,opt,name=shortName,proto3" json:"shortName"`
	UserId             string `protobuf:"bytes,5,opt,name=userId,proto3" json:"userId"`
	SignType           int32  `protobuf:"varint,6,opt,name=signType,proto3" json:"signType"`
	BatchNo            string `protobuf:"bytes,7,opt,name=batchNo,proto3" json:"batchNo"`
	ContractUuid       string `protobuf:"bytes,8,opt,name=contractUuid,proto3" json:"contractUuid"`
	Status             int32  `protobuf:"varint,9,opt,name=status,proto3" json:"status"`
	SignOrder          int32  `protobuf:"varint,10,opt,name=signOrder,proto3" json:"signOrder"`
	Phone              string `protobuf:"bytes,11,opt,name=phone,proto3" json:"phone"`
	LineType           int32  `protobuf:"varint,12,opt,name=lineType,proto3" json:"lineType"`
	ViewUrl            string `protobuf:"bytes,13,opt,name=viewUrl,proto3" json:"viewUrl"`
	AuctionUuid        string `protobuf:"bytes,14,opt,name=auctionUuid,proto3" json:"auctionUuid"`
	AuctionArtworkUuid string `protobuf:"bytes,15,opt,name=auctionArtworkUuid,proto3" json:"auctionArtworkUuid"`
	DownloadUrl        string `protobuf:"bytes,16,opt,name=downloadUrl,proto3" json:"downloadUrl"`
	SeriesUuid         string `protobuf:"bytes,17,opt,name=seriesUuid,proto3" json:"seriesUuid"`
	NeedOrderNo        bool   `protobuf:"varint,18,opt,name=needOrderNo,proto3" json:"needOrderNo"`
	SourceType         string `protobuf:"bytes,19,opt,name=sourceType,proto3" json:"sourceType"`
}

func (x *UpdateContractReq) Reset() {
	*x = UpdateContractReq{}
	mi := &file_pb_custom_contract_proto_msgTypes[11]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateContractReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateContractReq) ProtoMessage() {}

func (x *UpdateContractReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_custom_contract_proto_msgTypes[11]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateContractReq.ProtoReflect.Descriptor instead.
func (*UpdateContractReq) Descriptor() ([]byte, []int) {
	return file_pb_custom_contract_proto_rawDescGZIP(), []int{11}
}

func (x *UpdateContractReq) GetContractId() string {
	if x != nil {
		return x.ContractId
	}
	return ""
}

func (x *UpdateContractReq) GetTemplateId() string {
	if x != nil {
		return x.TemplateId
	}
	return ""
}

func (x *UpdateContractReq) GetContractName() string {
	if x != nil {
		return x.ContractName
	}
	return ""
}

func (x *UpdateContractReq) GetShortName() string {
	if x != nil {
		return x.ShortName
	}
	return ""
}

func (x *UpdateContractReq) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *UpdateContractReq) GetSignType() int32 {
	if x != nil {
		return x.SignType
	}
	return 0
}

func (x *UpdateContractReq) GetBatchNo() string {
	if x != nil {
		return x.BatchNo
	}
	return ""
}

func (x *UpdateContractReq) GetContractUuid() string {
	if x != nil {
		return x.ContractUuid
	}
	return ""
}

func (x *UpdateContractReq) GetStatus() int32 {
	if x != nil {
		return x.Status
	}
	return 0
}

func (x *UpdateContractReq) GetSignOrder() int32 {
	if x != nil {
		return x.SignOrder
	}
	return 0
}

func (x *UpdateContractReq) GetPhone() string {
	if x != nil {
		return x.Phone
	}
	return ""
}

func (x *UpdateContractReq) GetLineType() int32 {
	if x != nil {
		return x.LineType
	}
	return 0
}

func (x *UpdateContractReq) GetViewUrl() string {
	if x != nil {
		return x.ViewUrl
	}
	return ""
}

func (x *UpdateContractReq) GetAuctionUuid() string {
	if x != nil {
		return x.AuctionUuid
	}
	return ""
}

func (x *UpdateContractReq) GetAuctionArtworkUuid() string {
	if x != nil {
		return x.AuctionArtworkUuid
	}
	return ""
}

func (x *UpdateContractReq) GetDownloadUrl() string {
	if x != nil {
		return x.DownloadUrl
	}
	return ""
}

func (x *UpdateContractReq) GetSeriesUuid() string {
	if x != nil {
		return x.SeriesUuid
	}
	return ""
}

func (x *UpdateContractReq) GetNeedOrderNo() bool {
	if x != nil {
		return x.NeedOrderNo
	}
	return false
}

func (x *UpdateContractReq) GetSourceType() string {
	if x != nil {
		return x.SourceType
	}
	return ""
}

type UpdateContractResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	OrderNos []string `protobuf:"bytes,1,rep,name=orderNos,proto3" json:"orderNos"`
}

func (x *UpdateContractResp) Reset() {
	*x = UpdateContractResp{}
	mi := &file_pb_custom_contract_proto_msgTypes[12]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateContractResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateContractResp) ProtoMessage() {}

func (x *UpdateContractResp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_custom_contract_proto_msgTypes[12]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateContractResp.ProtoReflect.Descriptor instead.
func (*UpdateContractResp) Descriptor() ([]byte, []int) {
	return file_pb_custom_contract_proto_rawDescGZIP(), []int{12}
}

func (x *UpdateContractResp) GetOrderNos() []string {
	if x != nil {
		return x.OrderNos
	}
	return nil
}

// UpdateContractBatch
type UpdateContractBatchReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	BatchNo string `protobuf:"bytes,1,opt,name=batchNo,proto3" json:"batchNo"`
	Status  int32  `protobuf:"varint,2,opt,name=status,proto3" json:"status"`
	FddInfo string `protobuf:"bytes,3,opt,name=fddInfo,proto3" json:"fddInfo"`
}

func (x *UpdateContractBatchReq) Reset() {
	*x = UpdateContractBatchReq{}
	mi := &file_pb_custom_contract_proto_msgTypes[13]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateContractBatchReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateContractBatchReq) ProtoMessage() {}

func (x *UpdateContractBatchReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_custom_contract_proto_msgTypes[13]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateContractBatchReq.ProtoReflect.Descriptor instead.
func (*UpdateContractBatchReq) Descriptor() ([]byte, []int) {
	return file_pb_custom_contract_proto_rawDescGZIP(), []int{13}
}

func (x *UpdateContractBatchReq) GetBatchNo() string {
	if x != nil {
		return x.BatchNo
	}
	return ""
}

func (x *UpdateContractBatchReq) GetStatus() int32 {
	if x != nil {
		return x.Status
	}
	return 0
}

func (x *UpdateContractBatchReq) GetFddInfo() string {
	if x != nil {
		return x.FddInfo
	}
	return ""
}

type UpdateContractBatchResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *UpdateContractBatchResp) Reset() {
	*x = UpdateContractBatchResp{}
	mi := &file_pb_custom_contract_proto_msgTypes[14]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateContractBatchResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateContractBatchResp) ProtoMessage() {}

func (x *UpdateContractBatchResp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_custom_contract_proto_msgTypes[14]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateContractBatchResp.ProtoReflect.Descriptor instead.
func (*UpdateContractBatchResp) Descriptor() ([]byte, []int) {
	return file_pb_custom_contract_proto_rawDescGZIP(), []int{14}
}

// UserContractData
type UserContractDataReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UserId     string `protobuf:"bytes,1,opt,name=userId,proto3" json:"userId"`
	ContractId string `protobuf:"bytes,2,opt,name=contractId,proto3" json:"contractId"`
}

func (x *UserContractDataReq) Reset() {
	*x = UserContractDataReq{}
	mi := &file_pb_custom_contract_proto_msgTypes[15]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UserContractDataReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserContractDataReq) ProtoMessage() {}

func (x *UserContractDataReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_custom_contract_proto_msgTypes[15]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserContractDataReq.ProtoReflect.Descriptor instead.
func (*UserContractDataReq) Descriptor() ([]byte, []int) {
	return file_pb_custom_contract_proto_rawDescGZIP(), []int{15}
}

func (x *UserContractDataReq) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *UserContractDataReq) GetContractId() string {
	if x != nil {
		return x.ContractId
	}
	return ""
}

type UserContractDataResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *UserContractDataResp) Reset() {
	*x = UserContractDataResp{}
	mi := &file_pb_custom_contract_proto_msgTypes[16]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UserContractDataResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserContractDataResp) ProtoMessage() {}

func (x *UserContractDataResp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_custom_contract_proto_msgTypes[16]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserContractDataResp.ProtoReflect.Descriptor instead.
func (*UserContractDataResp) Descriptor() ([]byte, []int) {
	return file_pb_custom_contract_proto_rawDescGZIP(), []int{16}
}

// FddInfo
type FddInfoReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Phone string `protobuf:"bytes,1,opt,name=phone,proto3" json:"phone"`
}

func (x *FddInfoReq) Reset() {
	*x = FddInfoReq{}
	mi := &file_pb_custom_contract_proto_msgTypes[17]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *FddInfoReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FddInfoReq) ProtoMessage() {}

func (x *FddInfoReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_custom_contract_proto_msgTypes[17]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FddInfoReq.ProtoReflect.Descriptor instead.
func (*FddInfoReq) Descriptor() ([]byte, []int) {
	return file_pb_custom_contract_proto_rawDescGZIP(), []int{17}
}

func (x *FddInfoReq) GetPhone() string {
	if x != nil {
		return x.Phone
	}
	return ""
}

type FddInfoResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UserId     string `protobuf:"bytes,1,opt,name=userId,proto3" json:"userId"`
	Status     int32  `protobuf:"varint,2,opt,name=status,proto3" json:"status"`
	CustomerId string `protobuf:"bytes,3,opt,name=customerId,proto3" json:"customerId"`
	IsMainland uint32 `protobuf:"varint,4,opt,name=isMainland,proto3" json:"isMainland"`
}

func (x *FddInfoResp) Reset() {
	*x = FddInfoResp{}
	mi := &file_pb_custom_contract_proto_msgTypes[18]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *FddInfoResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FddInfoResp) ProtoMessage() {}

func (x *FddInfoResp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_custom_contract_proto_msgTypes[18]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FddInfoResp.ProtoReflect.Descriptor instead.
func (*FddInfoResp) Descriptor() ([]byte, []int) {
	return file_pb_custom_contract_proto_rawDescGZIP(), []int{18}
}

func (x *FddInfoResp) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *FddInfoResp) GetStatus() int32 {
	if x != nil {
		return x.Status
	}
	return 0
}

func (x *FddInfoResp) GetCustomerId() string {
	if x != nil {
		return x.CustomerId
	}
	return ""
}

func (x *FddInfoResp) GetIsMainland() uint32 {
	if x != nil {
		return x.IsMainland
	}
	return 0
}

// OfflineSignOrder
type OfflineSignOrderReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Phone string `protobuf:"bytes,1,opt,name=phone,proto3" json:"phone"`
}

func (x *OfflineSignOrderReq) Reset() {
	*x = OfflineSignOrderReq{}
	mi := &file_pb_custom_contract_proto_msgTypes[19]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *OfflineSignOrderReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OfflineSignOrderReq) ProtoMessage() {}

func (x *OfflineSignOrderReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_custom_contract_proto_msgTypes[19]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OfflineSignOrderReq.ProtoReflect.Descriptor instead.
func (*OfflineSignOrderReq) Descriptor() ([]byte, []int) {
	return file_pb_custom_contract_proto_rawDescGZIP(), []int{19}
}

func (x *OfflineSignOrderReq) GetPhone() string {
	if x != nil {
		return x.Phone
	}
	return ""
}

type OfflineSignOrderResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	SignOrder int32 `protobuf:"varint,1,opt,name=signOrder,proto3" json:"signOrder"`
}

func (x *OfflineSignOrderResp) Reset() {
	*x = OfflineSignOrderResp{}
	mi := &file_pb_custom_contract_proto_msgTypes[20]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *OfflineSignOrderResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OfflineSignOrderResp) ProtoMessage() {}

func (x *OfflineSignOrderResp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_custom_contract_proto_msgTypes[20]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OfflineSignOrderResp.ProtoReflect.Descriptor instead.
func (*OfflineSignOrderResp) Descriptor() ([]byte, []int) {
	return file_pb_custom_contract_proto_rawDescGZIP(), []int{20}
}

func (x *OfflineSignOrderResp) GetSignOrder() int32 {
	if x != nil {
		return x.SignOrder
	}
	return 0
}

// ArtworkBidData
type ArtworkBidDataReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Phone              string `protobuf:"bytes,1,opt,name=phone,proto3" json:"phone"`
	AuctionArtworkUuid string `protobuf:"bytes,2,opt,name=auctionArtworkUuid,proto3" json:"auctionArtworkUuid"`
}

func (x *ArtworkBidDataReq) Reset() {
	*x = ArtworkBidDataReq{}
	mi := &file_pb_custom_contract_proto_msgTypes[21]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ArtworkBidDataReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ArtworkBidDataReq) ProtoMessage() {}

func (x *ArtworkBidDataReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_custom_contract_proto_msgTypes[21]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ArtworkBidDataReq.ProtoReflect.Descriptor instead.
func (*ArtworkBidDataReq) Descriptor() ([]byte, []int) {
	return file_pb_custom_contract_proto_rawDescGZIP(), []int{21}
}

func (x *ArtworkBidDataReq) GetPhone() string {
	if x != nil {
		return x.Phone
	}
	return ""
}

func (x *ArtworkBidDataReq) GetAuctionArtworkUuid() string {
	if x != nil {
		return x.AuctionArtworkUuid
	}
	return ""
}

type ArtworkBidDataResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	BigData          []*ArtworkBidDataResp_BigInfo   `protobuf:"bytes,1,rep,name=bigData,proto3" json:"bigData"`
	ArtworkName      string                          `protobuf:"bytes,2,opt,name=artworkName,proto3" json:"artworkName"`
	SoldPrice        string                          `protobuf:"bytes,3,opt,name=soldPrice,proto3" json:"soldPrice"`
	ArtworkSize      string                          `protobuf:"bytes,4,opt,name=ArtworkSize,proto3" json:"ArtworkSize"`
	AuctionY         string                          `protobuf:"bytes,5,opt,name=AuctionY,proto3" json:"AuctionY"`
	AuctionM         string                          `protobuf:"bytes,6,opt,name=AuctionM,proto3" json:"AuctionM"`
	AuctionD         string                          `protobuf:"bytes,7,opt,name=AuctionD,proto3" json:"AuctionD"`
	HdPic            string                          `protobuf:"bytes,8,opt,name=HdPic,proto3" json:"HdPic"`
	Tfnum            string                          `protobuf:"bytes,9,opt,name=Tfnum,proto3" json:"Tfnum"`
	AuctionBasePrice string                          `protobuf:"bytes,10,opt,name=AuctionBasePrice,proto3" json:"AuctionBasePrice"`
	Commission       string                          `protobuf:"bytes,11,opt,name=Commission,proto3" json:"Commission"`
	SignDate         string                          `protobuf:"bytes,12,opt,name=SignDate,proto3" json:"SignDate"`
	AuctionData      *ArtworkBidDataResp_AuctionInfo `protobuf:"bytes,13,opt,name=AuctionData,proto3" json:"AuctionData"`
	SoldPriceChinese string                          `protobuf:"bytes,14,opt,name=soldPriceChinese,proto3" json:"soldPriceChinese"`
}

func (x *ArtworkBidDataResp) Reset() {
	*x = ArtworkBidDataResp{}
	mi := &file_pb_custom_contract_proto_msgTypes[22]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ArtworkBidDataResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ArtworkBidDataResp) ProtoMessage() {}

func (x *ArtworkBidDataResp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_custom_contract_proto_msgTypes[22]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ArtworkBidDataResp.ProtoReflect.Descriptor instead.
func (*ArtworkBidDataResp) Descriptor() ([]byte, []int) {
	return file_pb_custom_contract_proto_rawDescGZIP(), []int{22}
}

func (x *ArtworkBidDataResp) GetBigData() []*ArtworkBidDataResp_BigInfo {
	if x != nil {
		return x.BigData
	}
	return nil
}

func (x *ArtworkBidDataResp) GetArtworkName() string {
	if x != nil {
		return x.ArtworkName
	}
	return ""
}

func (x *ArtworkBidDataResp) GetSoldPrice() string {
	if x != nil {
		return x.SoldPrice
	}
	return ""
}

func (x *ArtworkBidDataResp) GetArtworkSize() string {
	if x != nil {
		return x.ArtworkSize
	}
	return ""
}

func (x *ArtworkBidDataResp) GetAuctionY() string {
	if x != nil {
		return x.AuctionY
	}
	return ""
}

func (x *ArtworkBidDataResp) GetAuctionM() string {
	if x != nil {
		return x.AuctionM
	}
	return ""
}

func (x *ArtworkBidDataResp) GetAuctionD() string {
	if x != nil {
		return x.AuctionD
	}
	return ""
}

func (x *ArtworkBidDataResp) GetHdPic() string {
	if x != nil {
		return x.HdPic
	}
	return ""
}

func (x *ArtworkBidDataResp) GetTfnum() string {
	if x != nil {
		return x.Tfnum
	}
	return ""
}

func (x *ArtworkBidDataResp) GetAuctionBasePrice() string {
	if x != nil {
		return x.AuctionBasePrice
	}
	return ""
}

func (x *ArtworkBidDataResp) GetCommission() string {
	if x != nil {
		return x.Commission
	}
	return ""
}

func (x *ArtworkBidDataResp) GetSignDate() string {
	if x != nil {
		return x.SignDate
	}
	return ""
}

func (x *ArtworkBidDataResp) GetAuctionData() *ArtworkBidDataResp_AuctionInfo {
	if x != nil {
		return x.AuctionData
	}
	return nil
}

func (x *ArtworkBidDataResp) GetSoldPriceChinese() string {
	if x != nil {
		return x.SoldPriceChinese
	}
	return ""
}

// ContractData
type ContractDataReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	BatchNo string `protobuf:"bytes,1,opt,name=batchNo,proto3" json:"batchNo"`
}

func (x *ContractDataReq) Reset() {
	*x = ContractDataReq{}
	mi := &file_pb_custom_contract_proto_msgTypes[23]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ContractDataReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ContractDataReq) ProtoMessage() {}

func (x *ContractDataReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_custom_contract_proto_msgTypes[23]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ContractDataReq.ProtoReflect.Descriptor instead.
func (*ContractDataReq) Descriptor() ([]byte, []int) {
	return file_pb_custom_contract_proto_rawDescGZIP(), []int{23}
}

func (x *ContractDataReq) GetBatchNo() string {
	if x != nil {
		return x.BatchNo
	}
	return ""
}

type ContractDataResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Data []*ContractDataResp_ContractInfo `protobuf:"bytes,1,rep,name=data,proto3" json:"data"`
}

func (x *ContractDataResp) Reset() {
	*x = ContractDataResp{}
	mi := &file_pb_custom_contract_proto_msgTypes[24]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ContractDataResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ContractDataResp) ProtoMessage() {}

func (x *ContractDataResp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_custom_contract_proto_msgTypes[24]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ContractDataResp.ProtoReflect.Descriptor instead.
func (*ContractDataResp) Descriptor() ([]byte, []int) {
	return file_pb_custom_contract_proto_rawDescGZIP(), []int{24}
}

func (x *ContractDataResp) GetData() []*ContractDataResp_ContractInfo {
	if x != nil {
		return x.Data
	}
	return nil
}

// ContractView
type ContractViewReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AuctionArtworkUuid  string   `protobuf:"bytes,1,opt,name=auctionArtworkUuid,proto3" json:"auctionArtworkUuid"`
	CountryCode         string   `protobuf:"bytes,2,opt,name=countryCode,proto3" json:"countryCode"`
	Phone               string   `protobuf:"bytes,3,opt,name=phone,proto3" json:"phone"`
	SignOrder           int32    `protobuf:"varint,4,opt,name=signOrder,proto3" json:"signOrder"`
	RegisterType        int32    `protobuf:"varint,5,opt,name=registerType,proto3" json:"registerType"`
	AuctionUuid         string   `protobuf:"bytes,6,opt,name=auctionUuid,proto3" json:"auctionUuid"`
	AuctionArtworkUuids []string `protobuf:"bytes,7,rep,name=auctionArtworkUuids,proto3" json:"auctionArtworkUuids"`
}

func (x *ContractViewReq) Reset() {
	*x = ContractViewReq{}
	mi := &file_pb_custom_contract_proto_msgTypes[25]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ContractViewReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ContractViewReq) ProtoMessage() {}

func (x *ContractViewReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_custom_contract_proto_msgTypes[25]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ContractViewReq.ProtoReflect.Descriptor instead.
func (*ContractViewReq) Descriptor() ([]byte, []int) {
	return file_pb_custom_contract_proto_rawDescGZIP(), []int{25}
}

func (x *ContractViewReq) GetAuctionArtworkUuid() string {
	if x != nil {
		return x.AuctionArtworkUuid
	}
	return ""
}

func (x *ContractViewReq) GetCountryCode() string {
	if x != nil {
		return x.CountryCode
	}
	return ""
}

func (x *ContractViewReq) GetPhone() string {
	if x != nil {
		return x.Phone
	}
	return ""
}

func (x *ContractViewReq) GetSignOrder() int32 {
	if x != nil {
		return x.SignOrder
	}
	return 0
}

func (x *ContractViewReq) GetRegisterType() int32 {
	if x != nil {
		return x.RegisterType
	}
	return 0
}

func (x *ContractViewReq) GetAuctionUuid() string {
	if x != nil {
		return x.AuctionUuid
	}
	return ""
}

func (x *ContractViewReq) GetAuctionArtworkUuids() []string {
	if x != nil {
		return x.AuctionArtworkUuids
	}
	return nil
}

type ContractViewResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ViewUrls *ContractViewResp_ViewUrlsData `protobuf:"bytes,1,opt,name=ViewUrls,proto3" json:"ViewUrls"`
	ViewUrl  string                         `protobuf:"bytes,2,opt,name=viewUrl,proto3" json:"viewUrl"`
}

func (x *ContractViewResp) Reset() {
	*x = ContractViewResp{}
	mi := &file_pb_custom_contract_proto_msgTypes[26]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ContractViewResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ContractViewResp) ProtoMessage() {}

func (x *ContractViewResp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_custom_contract_proto_msgTypes[26]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ContractViewResp.ProtoReflect.Descriptor instead.
func (*ContractViewResp) Descriptor() ([]byte, []int) {
	return file_pb_custom_contract_proto_rawDescGZIP(), []int{26}
}

func (x *ContractViewResp) GetViewUrls() *ContractViewResp_ViewUrlsData {
	if x != nil {
		return x.ViewUrls
	}
	return nil
}

func (x *ContractViewResp) GetViewUrl() string {
	if x != nil {
		return x.ViewUrl
	}
	return ""
}

type UserInfoByPhoneReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Phone        string `protobuf:"bytes,1,opt,name=phone,proto3" json:"phone"`
	RegisterType int32  `protobuf:"varint,2,opt,name=registerType,proto3" json:"registerType"`
}

func (x *UserInfoByPhoneReq) Reset() {
	*x = UserInfoByPhoneReq{}
	mi := &file_pb_custom_contract_proto_msgTypes[27]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UserInfoByPhoneReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserInfoByPhoneReq) ProtoMessage() {}

func (x *UserInfoByPhoneReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_custom_contract_proto_msgTypes[27]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserInfoByPhoneReq.ProtoReflect.Descriptor instead.
func (*UserInfoByPhoneReq) Descriptor() ([]byte, []int) {
	return file_pb_custom_contract_proto_rawDescGZIP(), []int{27}
}

func (x *UserInfoByPhoneReq) GetPhone() string {
	if x != nil {
		return x.Phone
	}
	return ""
}

func (x *UserInfoByPhoneReq) GetRegisterType() int32 {
	if x != nil {
		return x.RegisterType
	}
	return 0
}

type UserInfoByPhoneResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UserId            string `protobuf:"bytes,1,opt,name=userId,proto3" json:"userId"`
	Phone             string `protobuf:"bytes,2,opt,name=phone,proto3" json:"phone"`
	FddCustomerId     string `protobuf:"bytes,3,opt,name=fddCustomerId,proto3" json:"fddCustomerId"`
	CountryCode       string `protobuf:"bytes,4,opt,name=countryCode,proto3" json:"countryCode"`
	IsMainland        uint32 `protobuf:"varint,5,opt,name=isMainland,proto3" json:"isMainland"`
	FddRegisterStatus int32  `protobuf:"varint,6,opt,name=fddRegisterStatus,proto3" json:"fddRegisterStatus"`
	Status            int32  `protobuf:"varint,7,opt,name=status,proto3" json:"status"`
	SpecialType       int32  `protobuf:"varint,8,opt,name=specialType,proto3" json:"specialType"`
}

func (x *UserInfoByPhoneResp) Reset() {
	*x = UserInfoByPhoneResp{}
	mi := &file_pb_custom_contract_proto_msgTypes[28]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UserInfoByPhoneResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserInfoByPhoneResp) ProtoMessage() {}

func (x *UserInfoByPhoneResp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_custom_contract_proto_msgTypes[28]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserInfoByPhoneResp.ProtoReflect.Descriptor instead.
func (*UserInfoByPhoneResp) Descriptor() ([]byte, []int) {
	return file_pb_custom_contract_proto_rawDescGZIP(), []int{28}
}

func (x *UserInfoByPhoneResp) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *UserInfoByPhoneResp) GetPhone() string {
	if x != nil {
		return x.Phone
	}
	return ""
}

func (x *UserInfoByPhoneResp) GetFddCustomerId() string {
	if x != nil {
		return x.FddCustomerId
	}
	return ""
}

func (x *UserInfoByPhoneResp) GetCountryCode() string {
	if x != nil {
		return x.CountryCode
	}
	return ""
}

func (x *UserInfoByPhoneResp) GetIsMainland() uint32 {
	if x != nil {
		return x.IsMainland
	}
	return 0
}

func (x *UserInfoByPhoneResp) GetFddRegisterStatus() int32 {
	if x != nil {
		return x.FddRegisterStatus
	}
	return 0
}

func (x *UserInfoByPhoneResp) GetStatus() int32 {
	if x != nil {
		return x.Status
	}
	return 0
}

func (x *UserInfoByPhoneResp) GetSpecialType() int32 {
	if x != nil {
		return x.SpecialType
	}
	return 0
}

type GetAuctionArtworkUuidsReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Phone       string `protobuf:"bytes,1,opt,name=phone,proto3" json:"phone"`
	UserID      string `protobuf:"bytes,2,opt,name=userID,proto3" json:"userID"`
	AuctionUuid string `protobuf:"bytes,3,opt,name=auctionUuid,proto3" json:"auctionUuid"`
}

func (x *GetAuctionArtworkUuidsReq) Reset() {
	*x = GetAuctionArtworkUuidsReq{}
	mi := &file_pb_custom_contract_proto_msgTypes[29]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetAuctionArtworkUuidsReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAuctionArtworkUuidsReq) ProtoMessage() {}

func (x *GetAuctionArtworkUuidsReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_custom_contract_proto_msgTypes[29]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAuctionArtworkUuidsReq.ProtoReflect.Descriptor instead.
func (*GetAuctionArtworkUuidsReq) Descriptor() ([]byte, []int) {
	return file_pb_custom_contract_proto_rawDescGZIP(), []int{29}
}

func (x *GetAuctionArtworkUuidsReq) GetPhone() string {
	if x != nil {
		return x.Phone
	}
	return ""
}

func (x *GetAuctionArtworkUuidsReq) GetUserID() string {
	if x != nil {
		return x.UserID
	}
	return ""
}

func (x *GetAuctionArtworkUuidsReq) GetAuctionUuid() string {
	if x != nil {
		return x.AuctionUuid
	}
	return ""
}

type GetAuctionArtworkUuidsResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AuctionArtworkUuids []string `protobuf:"bytes,1,rep,name=auctionArtworkUuids,proto3" json:"auctionArtworkUuids"`
}

func (x *GetAuctionArtworkUuidsResp) Reset() {
	*x = GetAuctionArtworkUuidsResp{}
	mi := &file_pb_custom_contract_proto_msgTypes[30]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetAuctionArtworkUuidsResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAuctionArtworkUuidsResp) ProtoMessage() {}

func (x *GetAuctionArtworkUuidsResp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_custom_contract_proto_msgTypes[30]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAuctionArtworkUuidsResp.ProtoReflect.Descriptor instead.
func (*GetAuctionArtworkUuidsResp) Descriptor() ([]byte, []int) {
	return file_pb_custom_contract_proto_rawDescGZIP(), []int{30}
}

func (x *GetAuctionArtworkUuidsResp) GetAuctionArtworkUuids() []string {
	if x != nil {
		return x.AuctionArtworkUuids
	}
	return nil
}

type SignViewReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AuctionArtworkUuids []string `protobuf:"bytes,1,rep,name=auctionArtworkUuids,proto3" json:"auctionArtworkUuids"`
}

func (x *SignViewReq) Reset() {
	*x = SignViewReq{}
	mi := &file_pb_custom_contract_proto_msgTypes[31]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SignViewReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SignViewReq) ProtoMessage() {}

func (x *SignViewReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_custom_contract_proto_msgTypes[31]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SignViewReq.ProtoReflect.Descriptor instead.
func (*SignViewReq) Descriptor() ([]byte, []int) {
	return file_pb_custom_contract_proto_rawDescGZIP(), []int{31}
}

func (x *SignViewReq) GetAuctionArtworkUuids() []string {
	if x != nil {
		return x.AuctionArtworkUuids
	}
	return nil
}

type SignViewResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Jmxy1 []string `protobuf:"bytes,2,rep,name=jmxy1,proto3" json:"jmxy1"`
	Jmxz2 []string `protobuf:"bytes,3,rep,name=jmxz2,proto3" json:"jmxz2"`
	Pmgg3 []string `protobuf:"bytes,5,rep,name=pmgg3,proto3" json:"pmgg3"`
	Ppqr5 []string `protobuf:"bytes,1,rep,name=ppqr5,proto3" json:"ppqr5"`
	Ppbl6 []string `protobuf:"bytes,4,rep,name=ppbl6,proto3" json:"ppbl6"`
}

func (x *SignViewResp) Reset() {
	*x = SignViewResp{}
	mi := &file_pb_custom_contract_proto_msgTypes[32]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SignViewResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SignViewResp) ProtoMessage() {}

func (x *SignViewResp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_custom_contract_proto_msgTypes[32]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SignViewResp.ProtoReflect.Descriptor instead.
func (*SignViewResp) Descriptor() ([]byte, []int) {
	return file_pb_custom_contract_proto_rawDescGZIP(), []int{32}
}

func (x *SignViewResp) GetJmxy1() []string {
	if x != nil {
		return x.Jmxy1
	}
	return nil
}

func (x *SignViewResp) GetJmxz2() []string {
	if x != nil {
		return x.Jmxz2
	}
	return nil
}

func (x *SignViewResp) GetPmgg3() []string {
	if x != nil {
		return x.Pmgg3
	}
	return nil
}

func (x *SignViewResp) GetPpqr5() []string {
	if x != nil {
		return x.Ppqr5
	}
	return nil
}

func (x *SignViewResp) GetPpbl6() []string {
	if x != nil {
		return x.Ppbl6
	}
	return nil
}

type RepairFddContractUrlReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ContractIDs []string `protobuf:"bytes,1,rep,name=contractIDs,proto3" json:"contractIDs"`
}

func (x *RepairFddContractUrlReq) Reset() {
	*x = RepairFddContractUrlReq{}
	mi := &file_pb_custom_contract_proto_msgTypes[33]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RepairFddContractUrlReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RepairFddContractUrlReq) ProtoMessage() {}

func (x *RepairFddContractUrlReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_custom_contract_proto_msgTypes[33]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RepairFddContractUrlReq.ProtoReflect.Descriptor instead.
func (*RepairFddContractUrlReq) Descriptor() ([]byte, []int) {
	return file_pb_custom_contract_proto_rawDescGZIP(), []int{33}
}

func (x *RepairFddContractUrlReq) GetContractIDs() []string {
	if x != nil {
		return x.ContractIDs
	}
	return nil
}

type RepairFddContractUrlResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Data []*RepairFddContractUrlResp_Info `protobuf:"bytes,1,rep,name=data,proto3" json:"data"`
}

func (x *RepairFddContractUrlResp) Reset() {
	*x = RepairFddContractUrlResp{}
	mi := &file_pb_custom_contract_proto_msgTypes[34]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RepairFddContractUrlResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RepairFddContractUrlResp) ProtoMessage() {}

func (x *RepairFddContractUrlResp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_custom_contract_proto_msgTypes[34]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RepairFddContractUrlResp.ProtoReflect.Descriptor instead.
func (*RepairFddContractUrlResp) Descriptor() ([]byte, []int) {
	return file_pb_custom_contract_proto_rawDescGZIP(), []int{34}
}

func (x *RepairFddContractUrlResp) GetData() []*RepairFddContractUrlResp_Info {
	if x != nil {
		return x.Data
	}
	return nil
}

type UserAuctionContractCountReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UserID      string `protobuf:"bytes,1,opt,name=userID,proto3" json:"userID"`
	Phone       string `protobuf:"bytes,2,opt,name=phone,proto3" json:"phone"`
	AuctionUuid string `protobuf:"bytes,3,opt,name=auctionUuid,proto3" json:"auctionUuid"`
}

func (x *UserAuctionContractCountReq) Reset() {
	*x = UserAuctionContractCountReq{}
	mi := &file_pb_custom_contract_proto_msgTypes[35]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UserAuctionContractCountReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserAuctionContractCountReq) ProtoMessage() {}

func (x *UserAuctionContractCountReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_custom_contract_proto_msgTypes[35]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserAuctionContractCountReq.ProtoReflect.Descriptor instead.
func (*UserAuctionContractCountReq) Descriptor() ([]byte, []int) {
	return file_pb_custom_contract_proto_rawDescGZIP(), []int{35}
}

func (x *UserAuctionContractCountReq) GetUserID() string {
	if x != nil {
		return x.UserID
	}
	return ""
}

func (x *UserAuctionContractCountReq) GetPhone() string {
	if x != nil {
		return x.Phone
	}
	return ""
}

func (x *UserAuctionContractCountReq) GetAuctionUuid() string {
	if x != nil {
		return x.AuctionUuid
	}
	return ""
}

type UserAuctionContractCountResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Count int64 `protobuf:"varint,1,opt,name=count,proto3" json:"count"`
}

func (x *UserAuctionContractCountResp) Reset() {
	*x = UserAuctionContractCountResp{}
	mi := &file_pb_custom_contract_proto_msgTypes[36]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UserAuctionContractCountResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserAuctionContractCountResp) ProtoMessage() {}

func (x *UserAuctionContractCountResp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_custom_contract_proto_msgTypes[36]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserAuctionContractCountResp.ProtoReflect.Descriptor instead.
func (*UserAuctionContractCountResp) Descriptor() ([]byte, []int) {
	return file_pb_custom_contract_proto_rawDescGZIP(), []int{36}
}

func (x *UserAuctionContractCountResp) GetCount() int64 {
	if x != nil {
		return x.Count
	}
	return 0
}

type SeriesOrderContractViewReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ArtworkUuid string `protobuf:"bytes,1,opt,name=artworkUuid,proto3" json:"artworkUuid"`
	UserID      string `protobuf:"bytes,2,opt,name=userID,proto3" json:"userID"`
	Phone       string `protobuf:"bytes,3,opt,name=phone,proto3" json:"phone"`
	SeriesUuid  string `protobuf:"bytes,4,opt,name=seriesUuid,proto3" json:"seriesUuid"`
}

func (x *SeriesOrderContractViewReq) Reset() {
	*x = SeriesOrderContractViewReq{}
	mi := &file_pb_custom_contract_proto_msgTypes[37]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SeriesOrderContractViewReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SeriesOrderContractViewReq) ProtoMessage() {}

func (x *SeriesOrderContractViewReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_custom_contract_proto_msgTypes[37]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SeriesOrderContractViewReq.ProtoReflect.Descriptor instead.
func (*SeriesOrderContractViewReq) Descriptor() ([]byte, []int) {
	return file_pb_custom_contract_proto_rawDescGZIP(), []int{37}
}

func (x *SeriesOrderContractViewReq) GetArtworkUuid() string {
	if x != nil {
		return x.ArtworkUuid
	}
	return ""
}

func (x *SeriesOrderContractViewReq) GetUserID() string {
	if x != nil {
		return x.UserID
	}
	return ""
}

func (x *SeriesOrderContractViewReq) GetPhone() string {
	if x != nil {
		return x.Phone
	}
	return ""
}

func (x *SeriesOrderContractViewReq) GetSeriesUuid() string {
	if x != nil {
		return x.SeriesUuid
	}
	return ""
}

type SeriesOrderContractViewResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ContractUrls map[string]string `protobuf:"bytes,1,rep,name=contractUrls,proto3" json:"contractUrls" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

func (x *SeriesOrderContractViewResp) Reset() {
	*x = SeriesOrderContractViewResp{}
	mi := &file_pb_custom_contract_proto_msgTypes[38]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SeriesOrderContractViewResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SeriesOrderContractViewResp) ProtoMessage() {}

func (x *SeriesOrderContractViewResp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_custom_contract_proto_msgTypes[38]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SeriesOrderContractViewResp.ProtoReflect.Descriptor instead.
func (*SeriesOrderContractViewResp) Descriptor() ([]byte, []int) {
	return file_pb_custom_contract_proto_rawDescGZIP(), []int{38}
}

func (x *SeriesOrderContractViewResp) GetContractUrls() map[string]string {
	if x != nil {
		return x.ContractUrls
	}
	return nil
}

type SeriesOrderContractSignReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ArtworkUuid     string `protobuf:"bytes,1,opt,name=artworkUuid,proto3" json:"artworkUuid"`
	SignImgFileData string `protobuf:"bytes,2,opt,name=signImgFileData,proto3" json:"signImgFileData"`
	UserID          string `protobuf:"bytes,3,opt,name=userID,proto3" json:"userID"`
	Phone           string `protobuf:"bytes,4,opt,name=phone,proto3" json:"phone"`
	SeriesUuid      string `protobuf:"bytes,5,opt,name=seriesUuid,proto3" json:"seriesUuid"`
}

func (x *SeriesOrderContractSignReq) Reset() {
	*x = SeriesOrderContractSignReq{}
	mi := &file_pb_custom_contract_proto_msgTypes[39]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SeriesOrderContractSignReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SeriesOrderContractSignReq) ProtoMessage() {}

func (x *SeriesOrderContractSignReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_custom_contract_proto_msgTypes[39]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SeriesOrderContractSignReq.ProtoReflect.Descriptor instead.
func (*SeriesOrderContractSignReq) Descriptor() ([]byte, []int) {
	return file_pb_custom_contract_proto_rawDescGZIP(), []int{39}
}

func (x *SeriesOrderContractSignReq) GetArtworkUuid() string {
	if x != nil {
		return x.ArtworkUuid
	}
	return ""
}

func (x *SeriesOrderContractSignReq) GetSignImgFileData() string {
	if x != nil {
		return x.SignImgFileData
	}
	return ""
}

func (x *SeriesOrderContractSignReq) GetUserID() string {
	if x != nil {
		return x.UserID
	}
	return ""
}

func (x *SeriesOrderContractSignReq) GetPhone() string {
	if x != nil {
		return x.Phone
	}
	return ""
}

func (x *SeriesOrderContractSignReq) GetSeriesUuid() string {
	if x != nil {
		return x.SeriesUuid
	}
	return ""
}

type SeriesOrderContractSignResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ContractUrls map[string]string `protobuf:"bytes,1,rep,name=contractUrls,proto3" json:"contractUrls" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

func (x *SeriesOrderContractSignResp) Reset() {
	*x = SeriesOrderContractSignResp{}
	mi := &file_pb_custom_contract_proto_msgTypes[40]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SeriesOrderContractSignResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SeriesOrderContractSignResp) ProtoMessage() {}

func (x *SeriesOrderContractSignResp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_custom_contract_proto_msgTypes[40]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SeriesOrderContractSignResp.ProtoReflect.Descriptor instead.
func (*SeriesOrderContractSignResp) Descriptor() ([]byte, []int) {
	return file_pb_custom_contract_proto_rawDescGZIP(), []int{40}
}

func (x *SeriesOrderContractSignResp) GetContractUrls() map[string]string {
	if x != nil {
		return x.ContractUrls
	}
	return nil
}

type SignOnlineV2Req struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UserInfo            *UserInfo `protobuf:"bytes,1,opt,name=userInfo,proto3" json:"userInfo"`
	SignImgFileData     string    `protobuf:"bytes,2,opt,name=signImgFileData,proto3" json:"signImgFileData"`
	AuctionUuid         string    `protobuf:"bytes,3,opt,name=auctionUuid,proto3" json:"auctionUuid"`
	AuctionArtworkUuids []string  `protobuf:"bytes,4,rep,name=auctionArtworkUuids,proto3" json:"auctionArtworkUuids"`
	SeriesUuid          string    `protobuf:"bytes,5,opt,name=seriesUuid,proto3" json:"seriesUuid"`
	CallbackUrl         string    `protobuf:"bytes,6,opt,name=callbackUrl,proto3" json:"callbackUrl"`
	SignOrder           int32     `protobuf:"varint,7,opt,name=signOrder,proto3" json:"signOrder"`
	SourceType          string    `protobuf:"bytes,8,opt,name=sourceType,proto3" json:"sourceType"`
}

func (x *SignOnlineV2Req) Reset() {
	*x = SignOnlineV2Req{}
	mi := &file_pb_custom_contract_proto_msgTypes[41]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SignOnlineV2Req) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SignOnlineV2Req) ProtoMessage() {}

func (x *SignOnlineV2Req) ProtoReflect() protoreflect.Message {
	mi := &file_pb_custom_contract_proto_msgTypes[41]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SignOnlineV2Req.ProtoReflect.Descriptor instead.
func (*SignOnlineV2Req) Descriptor() ([]byte, []int) {
	return file_pb_custom_contract_proto_rawDescGZIP(), []int{41}
}

func (x *SignOnlineV2Req) GetUserInfo() *UserInfo {
	if x != nil {
		return x.UserInfo
	}
	return nil
}

func (x *SignOnlineV2Req) GetSignImgFileData() string {
	if x != nil {
		return x.SignImgFileData
	}
	return ""
}

func (x *SignOnlineV2Req) GetAuctionUuid() string {
	if x != nil {
		return x.AuctionUuid
	}
	return ""
}

func (x *SignOnlineV2Req) GetAuctionArtworkUuids() []string {
	if x != nil {
		return x.AuctionArtworkUuids
	}
	return nil
}

func (x *SignOnlineV2Req) GetSeriesUuid() string {
	if x != nil {
		return x.SeriesUuid
	}
	return ""
}

func (x *SignOnlineV2Req) GetCallbackUrl() string {
	if x != nil {
		return x.CallbackUrl
	}
	return ""
}

func (x *SignOnlineV2Req) GetSignOrder() int32 {
	if x != nil {
		return x.SignOrder
	}
	return 0
}

func (x *SignOnlineV2Req) GetSourceType() string {
	if x != nil {
		return x.SourceType
	}
	return ""
}

type SignOnlineV2Resp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Url1     string   `protobuf:"bytes,1,opt,name=url1,proto3" json:"url1"`
	OrderNos []string `protobuf:"bytes,2,rep,name=orderNos,proto3" json:"orderNos"`
}

func (x *SignOnlineV2Resp) Reset() {
	*x = SignOnlineV2Resp{}
	mi := &file_pb_custom_contract_proto_msgTypes[42]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SignOnlineV2Resp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SignOnlineV2Resp) ProtoMessage() {}

func (x *SignOnlineV2Resp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_custom_contract_proto_msgTypes[42]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SignOnlineV2Resp.ProtoReflect.Descriptor instead.
func (*SignOnlineV2Resp) Descriptor() ([]byte, []int) {
	return file_pb_custom_contract_proto_rawDescGZIP(), []int{42}
}

func (x *SignOnlineV2Resp) GetUrl1() string {
	if x != nil {
		return x.Url1
	}
	return ""
}

func (x *SignOnlineV2Resp) GetOrderNos() []string {
	if x != nil {
		return x.OrderNos
	}
	return nil
}

type UserInfoV2Req struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UserID           string `protobuf:"bytes,1,opt,name=userID,proto3" json:"userID"`
	Phone            string `protobuf:"bytes,2,opt,name=phone,proto3" json:"phone"`
	PhoneCountryCode string `protobuf:"bytes,3,opt,name=phoneCountryCode,proto3" json:"phoneCountryCode"`
	RegisterType     int32  `protobuf:"varint,4,opt,name=registerType,proto3" json:"registerType"`
}

func (x *UserInfoV2Req) Reset() {
	*x = UserInfoV2Req{}
	mi := &file_pb_custom_contract_proto_msgTypes[43]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UserInfoV2Req) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserInfoV2Req) ProtoMessage() {}

func (x *UserInfoV2Req) ProtoReflect() protoreflect.Message {
	mi := &file_pb_custom_contract_proto_msgTypes[43]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserInfoV2Req.ProtoReflect.Descriptor instead.
func (*UserInfoV2Req) Descriptor() ([]byte, []int) {
	return file_pb_custom_contract_proto_rawDescGZIP(), []int{43}
}

func (x *UserInfoV2Req) GetUserID() string {
	if x != nil {
		return x.UserID
	}
	return ""
}

func (x *UserInfoV2Req) GetPhone() string {
	if x != nil {
		return x.Phone
	}
	return ""
}

func (x *UserInfoV2Req) GetPhoneCountryCode() string {
	if x != nil {
		return x.PhoneCountryCode
	}
	return ""
}

func (x *UserInfoV2Req) GetRegisterType() int32 {
	if x != nil {
		return x.RegisterType
	}
	return 0
}

type UserInfoV2Resp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UserInfo *UserInfo `protobuf:"bytes,1,opt,name=userInfo,proto3" json:"userInfo"`
}

func (x *UserInfoV2Resp) Reset() {
	*x = UserInfoV2Resp{}
	mi := &file_pb_custom_contract_proto_msgTypes[44]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UserInfoV2Resp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserInfoV2Resp) ProtoMessage() {}

func (x *UserInfoV2Resp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_custom_contract_proto_msgTypes[44]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserInfoV2Resp.ProtoReflect.Descriptor instead.
func (*UserInfoV2Resp) Descriptor() ([]byte, []int) {
	return file_pb_custom_contract_proto_rawDescGZIP(), []int{44}
}

func (x *UserInfoV2Resp) GetUserInfo() *UserInfo {
	if x != nil {
		return x.UserInfo
	}
	return nil
}

type UpdateUserInfoV2Req struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UserInfo *UserInfo `protobuf:"bytes,1,opt,name=userInfo,proto3" json:"userInfo"`
}

func (x *UpdateUserInfoV2Req) Reset() {
	*x = UpdateUserInfoV2Req{}
	mi := &file_pb_custom_contract_proto_msgTypes[45]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateUserInfoV2Req) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateUserInfoV2Req) ProtoMessage() {}

func (x *UpdateUserInfoV2Req) ProtoReflect() protoreflect.Message {
	mi := &file_pb_custom_contract_proto_msgTypes[45]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateUserInfoV2Req.ProtoReflect.Descriptor instead.
func (*UpdateUserInfoV2Req) Descriptor() ([]byte, []int) {
	return file_pb_custom_contract_proto_rawDescGZIP(), []int{45}
}

func (x *UpdateUserInfoV2Req) GetUserInfo() *UserInfo {
	if x != nil {
		return x.UserInfo
	}
	return nil
}

type UpdateUserInfoV2Resp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UserInfo *UserInfo `protobuf:"bytes,1,opt,name=userInfo,proto3" json:"userInfo"`
}

func (x *UpdateUserInfoV2Resp) Reset() {
	*x = UpdateUserInfoV2Resp{}
	mi := &file_pb_custom_contract_proto_msgTypes[46]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateUserInfoV2Resp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateUserInfoV2Resp) ProtoMessage() {}

func (x *UpdateUserInfoV2Resp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_custom_contract_proto_msgTypes[46]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateUserInfoV2Resp.ProtoReflect.Descriptor instead.
func (*UpdateUserInfoV2Resp) Descriptor() ([]byte, []int) {
	return file_pb_custom_contract_proto_rawDescGZIP(), []int{46}
}

func (x *UpdateUserInfoV2Resp) GetUserInfo() *UserInfo {
	if x != nil {
		return x.UserInfo
	}
	return nil
}

type AuctionInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AuctionTime     string `protobuf:"bytes,1,opt,name=AuctionTime,proto3" json:"AuctionTime"`
	AuctionAddress  string `protobuf:"bytes,2,opt,name=AuctionAddress,proto3" json:"AuctionAddress"`
	AuctionName     string `protobuf:"bytes,3,opt,name=AuctionName,proto3" json:"AuctionName"`
	AuctionLOT      string `protobuf:"bytes,4,opt,name=AuctionLOT,proto3" json:"AuctionLOT"`
	PreviewStartY   string `protobuf:"bytes,5,opt,name=PreviewStartY,proto3" json:"PreviewStartY"`
	PreviewStartM   string `protobuf:"bytes,6,opt,name=PreviewStartM,proto3" json:"PreviewStartM"`
	PreviewStartD   string `protobuf:"bytes,7,opt,name=PreviewStartD,proto3" json:"PreviewStartD"`
	PreviewStartHis string `protobuf:"bytes,8,opt,name=PreviewStartHis,proto3" json:"PreviewStartHis"`
	PreviewEndY     string `protobuf:"bytes,9,opt,name=PreviewEndY,proto3" json:"PreviewEndY"`
	PreviewEndM     string `protobuf:"bytes,10,opt,name=PreviewEndM,proto3" json:"PreviewEndM"`
	PreviewEndD     string `protobuf:"bytes,12,opt,name=PreviewEndD,proto3" json:"PreviewEndD"`
	PreviewEndHis   string `protobuf:"bytes,13,opt,name=PreviewEndHis,proto3" json:"PreviewEndHis"`
	PreviewTime     string `protobuf:"bytes,14,opt,name=PreviewTime,proto3" json:"PreviewTime"`
}

func (x *AuctionInfo) Reset() {
	*x = AuctionInfo{}
	mi := &file_pb_custom_contract_proto_msgTypes[47]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AuctionInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AuctionInfo) ProtoMessage() {}

func (x *AuctionInfo) ProtoReflect() protoreflect.Message {
	mi := &file_pb_custom_contract_proto_msgTypes[47]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AuctionInfo.ProtoReflect.Descriptor instead.
func (*AuctionInfo) Descriptor() ([]byte, []int) {
	return file_pb_custom_contract_proto_rawDescGZIP(), []int{47}
}

func (x *AuctionInfo) GetAuctionTime() string {
	if x != nil {
		return x.AuctionTime
	}
	return ""
}

func (x *AuctionInfo) GetAuctionAddress() string {
	if x != nil {
		return x.AuctionAddress
	}
	return ""
}

func (x *AuctionInfo) GetAuctionName() string {
	if x != nil {
		return x.AuctionName
	}
	return ""
}

func (x *AuctionInfo) GetAuctionLOT() string {
	if x != nil {
		return x.AuctionLOT
	}
	return ""
}

func (x *AuctionInfo) GetPreviewStartY() string {
	if x != nil {
		return x.PreviewStartY
	}
	return ""
}

func (x *AuctionInfo) GetPreviewStartM() string {
	if x != nil {
		return x.PreviewStartM
	}
	return ""
}

func (x *AuctionInfo) GetPreviewStartD() string {
	if x != nil {
		return x.PreviewStartD
	}
	return ""
}

func (x *AuctionInfo) GetPreviewStartHis() string {
	if x != nil {
		return x.PreviewStartHis
	}
	return ""
}

func (x *AuctionInfo) GetPreviewEndY() string {
	if x != nil {
		return x.PreviewEndY
	}
	return ""
}

func (x *AuctionInfo) GetPreviewEndM() string {
	if x != nil {
		return x.PreviewEndM
	}
	return ""
}

func (x *AuctionInfo) GetPreviewEndD() string {
	if x != nil {
		return x.PreviewEndD
	}
	return ""
}

func (x *AuctionInfo) GetPreviewEndHis() string {
	if x != nil {
		return x.PreviewEndHis
	}
	return ""
}

func (x *AuctionInfo) GetPreviewTime() string {
	if x != nil {
		return x.PreviewTime
	}
	return ""
}

type AuctionInfoV2Req struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AuctionUuid string `protobuf:"bytes,1,opt,name=auctionUuid,proto3" json:"auctionUuid"`
	SeriesUuid  string `protobuf:"bytes,2,opt,name=seriesUuid,proto3" json:"seriesUuid"`
}

func (x *AuctionInfoV2Req) Reset() {
	*x = AuctionInfoV2Req{}
	mi := &file_pb_custom_contract_proto_msgTypes[48]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AuctionInfoV2Req) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AuctionInfoV2Req) ProtoMessage() {}

func (x *AuctionInfoV2Req) ProtoReflect() protoreflect.Message {
	mi := &file_pb_custom_contract_proto_msgTypes[48]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AuctionInfoV2Req.ProtoReflect.Descriptor instead.
func (*AuctionInfoV2Req) Descriptor() ([]byte, []int) {
	return file_pb_custom_contract_proto_rawDescGZIP(), []int{48}
}

func (x *AuctionInfoV2Req) GetAuctionUuid() string {
	if x != nil {
		return x.AuctionUuid
	}
	return ""
}

func (x *AuctionInfoV2Req) GetSeriesUuid() string {
	if x != nil {
		return x.SeriesUuid
	}
	return ""
}

type AuctionInfoV2Resp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AuctionInfo *AuctionInfo `protobuf:"bytes,1,opt,name=auctionInfo,proto3" json:"auctionInfo"`
}

func (x *AuctionInfoV2Resp) Reset() {
	*x = AuctionInfoV2Resp{}
	mi := &file_pb_custom_contract_proto_msgTypes[49]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AuctionInfoV2Resp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AuctionInfoV2Resp) ProtoMessage() {}

func (x *AuctionInfoV2Resp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_custom_contract_proto_msgTypes[49]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AuctionInfoV2Resp.ProtoReflect.Descriptor instead.
func (*AuctionInfoV2Resp) Descriptor() ([]byte, []int) {
	return file_pb_custom_contract_proto_rawDescGZIP(), []int{49}
}

func (x *AuctionInfoV2Resp) GetAuctionInfo() *AuctionInfo {
	if x != nil {
		return x.AuctionInfo
	}
	return nil
}

type ContractViewV2Req struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AuctionUuid         string   `protobuf:"bytes,1,opt,name=auctionUuid,proto3" json:"auctionUuid"`
	AuctionArtworkUuids []string `protobuf:"bytes,2,rep,name=auctionArtworkUuids,proto3" json:"auctionArtworkUuids"`
	UserID              string   `protobuf:"bytes,3,opt,name=userID,proto3" json:"userID"`
	SeriesUuid          string   `protobuf:"bytes,4,opt,name=seriesUuid,proto3" json:"seriesUuid"`
	SourceType          string   `protobuf:"bytes,5,opt,name=sourceType,proto3" json:"sourceType"`
}

func (x *ContractViewV2Req) Reset() {
	*x = ContractViewV2Req{}
	mi := &file_pb_custom_contract_proto_msgTypes[50]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ContractViewV2Req) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ContractViewV2Req) ProtoMessage() {}

func (x *ContractViewV2Req) ProtoReflect() protoreflect.Message {
	mi := &file_pb_custom_contract_proto_msgTypes[50]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ContractViewV2Req.ProtoReflect.Descriptor instead.
func (*ContractViewV2Req) Descriptor() ([]byte, []int) {
	return file_pb_custom_contract_proto_rawDescGZIP(), []int{50}
}

func (x *ContractViewV2Req) GetAuctionUuid() string {
	if x != nil {
		return x.AuctionUuid
	}
	return ""
}

func (x *ContractViewV2Req) GetAuctionArtworkUuids() []string {
	if x != nil {
		return x.AuctionArtworkUuids
	}
	return nil
}

func (x *ContractViewV2Req) GetUserID() string {
	if x != nil {
		return x.UserID
	}
	return ""
}

func (x *ContractViewV2Req) GetSeriesUuid() string {
	if x != nil {
		return x.SeriesUuid
	}
	return ""
}

func (x *ContractViewV2Req) GetSourceType() string {
	if x != nil {
		return x.SourceType
	}
	return ""
}

type ContractViewV2Resp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ViewUrls *ContractViewV2Resp_ViewUrlsData `protobuf:"bytes,1,opt,name=ViewUrls,proto3" json:"ViewUrls"`
}

func (x *ContractViewV2Resp) Reset() {
	*x = ContractViewV2Resp{}
	mi := &file_pb_custom_contract_proto_msgTypes[51]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ContractViewV2Resp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ContractViewV2Resp) ProtoMessage() {}

func (x *ContractViewV2Resp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_custom_contract_proto_msgTypes[51]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ContractViewV2Resp.ProtoReflect.Descriptor instead.
func (*ContractViewV2Resp) Descriptor() ([]byte, []int) {
	return file_pb_custom_contract_proto_rawDescGZIP(), []int{51}
}

func (x *ContractViewV2Resp) GetViewUrls() *ContractViewV2Resp_ViewUrlsData {
	if x != nil {
		return x.ViewUrls
	}
	return nil
}

type UserContractNeedSignReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UserInfo   *UserInfo `protobuf:"bytes,1,opt,name=userInfo,proto3" json:"userInfo"`
	SeriesUuid string    `protobuf:"bytes,2,opt,name=seriesUuid,proto3" json:"seriesUuid"`
	SourceType string    `protobuf:"bytes,3,opt,name=sourceType,proto3" json:"sourceType"`
}

func (x *UserContractNeedSignReq) Reset() {
	*x = UserContractNeedSignReq{}
	mi := &file_pb_custom_contract_proto_msgTypes[52]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UserContractNeedSignReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserContractNeedSignReq) ProtoMessage() {}

func (x *UserContractNeedSignReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_custom_contract_proto_msgTypes[52]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserContractNeedSignReq.ProtoReflect.Descriptor instead.
func (*UserContractNeedSignReq) Descriptor() ([]byte, []int) {
	return file_pb_custom_contract_proto_rawDescGZIP(), []int{52}
}

func (x *UserContractNeedSignReq) GetUserInfo() *UserInfo {
	if x != nil {
		return x.UserInfo
	}
	return nil
}

func (x *UserContractNeedSignReq) GetSeriesUuid() string {
	if x != nil {
		return x.SeriesUuid
	}
	return ""
}

func (x *UserContractNeedSignReq) GetSourceType() string {
	if x != nil {
		return x.SourceType
	}
	return ""
}

type UserContractNeedSignResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AuctionArtworkUuids    []string `protobuf:"bytes,1,rep,name=auctionArtworkUuids,proto3" json:"auctionArtworkUuids"`       // 需要签署的作品
	BugAuctionArtworkUuids []string `protobuf:"bytes,2,rep,name=bugAuctionArtworkUuids,proto3" json:"bugAuctionArtworkUuids"` // 已拍的作品
}

func (x *UserContractNeedSignResp) Reset() {
	*x = UserContractNeedSignResp{}
	mi := &file_pb_custom_contract_proto_msgTypes[53]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UserContractNeedSignResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserContractNeedSignResp) ProtoMessage() {}

func (x *UserContractNeedSignResp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_custom_contract_proto_msgTypes[53]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserContractNeedSignResp.ProtoReflect.Descriptor instead.
func (*UserContractNeedSignResp) Descriptor() ([]byte, []int) {
	return file_pb_custom_contract_proto_rawDescGZIP(), []int{53}
}

func (x *UserContractNeedSignResp) GetAuctionArtworkUuids() []string {
	if x != nil {
		return x.AuctionArtworkUuids
	}
	return nil
}

func (x *UserContractNeedSignResp) GetBugAuctionArtworkUuids() []string {
	if x != nil {
		return x.BugAuctionArtworkUuids
	}
	return nil
}

type ArtworkContractViewReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UserID              string   `protobuf:"bytes,1,opt,name=userID,proto3" json:"userID"`
	AuctionArtworkUuids []string `protobuf:"bytes,2,rep,name=auctionArtworkUuids,proto3" json:"auctionArtworkUuids"`
	ArtworkUuids        []string `protobuf:"bytes,3,rep,name=artworkUuids,proto3" json:"artworkUuids"`
	SeriesUuid          string   `protobuf:"bytes,4,opt,name=seriesUuid,proto3" json:"seriesUuid"`
	SeriesType          int32    `protobuf:"varint,5,opt,name=seriesType,proto3" json:"seriesType"`
	AuctionUuid         string   `protobuf:"bytes,6,opt,name=auctionUuid,proto3" json:"auctionUuid"`
	OrderID             string   `protobuf:"bytes,7,opt,name=orderID,proto3" json:"orderID"`
	OrderNo             string   `protobuf:"bytes,8,opt,name=orderNo,proto3" json:"orderNo"`
}

func (x *ArtworkContractViewReq) Reset() {
	*x = ArtworkContractViewReq{}
	mi := &file_pb_custom_contract_proto_msgTypes[54]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ArtworkContractViewReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ArtworkContractViewReq) ProtoMessage() {}

func (x *ArtworkContractViewReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_custom_contract_proto_msgTypes[54]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ArtworkContractViewReq.ProtoReflect.Descriptor instead.
func (*ArtworkContractViewReq) Descriptor() ([]byte, []int) {
	return file_pb_custom_contract_proto_rawDescGZIP(), []int{54}
}

func (x *ArtworkContractViewReq) GetUserID() string {
	if x != nil {
		return x.UserID
	}
	return ""
}

func (x *ArtworkContractViewReq) GetAuctionArtworkUuids() []string {
	if x != nil {
		return x.AuctionArtworkUuids
	}
	return nil
}

func (x *ArtworkContractViewReq) GetArtworkUuids() []string {
	if x != nil {
		return x.ArtworkUuids
	}
	return nil
}

func (x *ArtworkContractViewReq) GetSeriesUuid() string {
	if x != nil {
		return x.SeriesUuid
	}
	return ""
}

func (x *ArtworkContractViewReq) GetSeriesType() int32 {
	if x != nil {
		return x.SeriesType
	}
	return 0
}

func (x *ArtworkContractViewReq) GetAuctionUuid() string {
	if x != nil {
		return x.AuctionUuid
	}
	return ""
}

func (x *ArtworkContractViewReq) GetOrderID() string {
	if x != nil {
		return x.OrderID
	}
	return ""
}

func (x *ArtworkContractViewReq) GetOrderNo() string {
	if x != nil {
		return x.OrderNo
	}
	return ""
}

type ContractNameInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Jmxy1 string `protobuf:"bytes,1,opt,name=jmxy1,proto3" json:"jmxy1"`
	Jmxz2 string `protobuf:"bytes,2,opt,name=jmxz2,proto3" json:"jmxz2"`
	Pmgg3 string `protobuf:"bytes,3,opt,name=pmgg3,proto3" json:"pmgg3"`
	Pmgz4 string `protobuf:"bytes,4,opt,name=pmgz4,proto3" json:"pmgz4"`
	Ppqr5 string `protobuf:"bytes,5,opt,name=ppqr5,proto3" json:"ppqr5"`
	Ppbl6 string `protobuf:"bytes,6,opt,name=ppbl6,proto3" json:"ppbl6"`
	Xsht1 string `protobuf:"bytes,7,opt,name=xsht1,proto3" json:"xsht1"`
}

func (x *ContractNameInfo) Reset() {
	*x = ContractNameInfo{}
	mi := &file_pb_custom_contract_proto_msgTypes[55]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ContractNameInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ContractNameInfo) ProtoMessage() {}

func (x *ContractNameInfo) ProtoReflect() protoreflect.Message {
	mi := &file_pb_custom_contract_proto_msgTypes[55]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ContractNameInfo.ProtoReflect.Descriptor instead.
func (*ContractNameInfo) Descriptor() ([]byte, []int) {
	return file_pb_custom_contract_proto_rawDescGZIP(), []int{55}
}

func (x *ContractNameInfo) GetJmxy1() string {
	if x != nil {
		return x.Jmxy1
	}
	return ""
}

func (x *ContractNameInfo) GetJmxz2() string {
	if x != nil {
		return x.Jmxz2
	}
	return ""
}

func (x *ContractNameInfo) GetPmgg3() string {
	if x != nil {
		return x.Pmgg3
	}
	return ""
}

func (x *ContractNameInfo) GetPmgz4() string {
	if x != nil {
		return x.Pmgz4
	}
	return ""
}

func (x *ContractNameInfo) GetPpqr5() string {
	if x != nil {
		return x.Ppqr5
	}
	return ""
}

func (x *ContractNameInfo) GetPpbl6() string {
	if x != nil {
		return x.Ppbl6
	}
	return ""
}

func (x *ContractNameInfo) GetXsht1() string {
	if x != nil {
		return x.Xsht1
	}
	return ""
}

type ArtworkContractViewResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Data       map[string]*ContractNameInfo `protobuf:"bytes,1,rep,name=data,proto3" json:"data" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	SeriesType int32                        `protobuf:"varint,2,opt,name=seriesType,proto3" json:"seriesType"`
}

func (x *ArtworkContractViewResp) Reset() {
	*x = ArtworkContractViewResp{}
	mi := &file_pb_custom_contract_proto_msgTypes[56]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ArtworkContractViewResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ArtworkContractViewResp) ProtoMessage() {}

func (x *ArtworkContractViewResp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_custom_contract_proto_msgTypes[56]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ArtworkContractViewResp.ProtoReflect.Descriptor instead.
func (*ArtworkContractViewResp) Descriptor() ([]byte, []int) {
	return file_pb_custom_contract_proto_rawDescGZIP(), []int{56}
}

func (x *ArtworkContractViewResp) GetData() map[string]*ContractNameInfo {
	if x != nil {
		return x.Data
	}
	return nil
}

func (x *ArtworkContractViewResp) GetSeriesType() int32 {
	if x != nil {
		return x.SeriesType
	}
	return 0
}

type SignedAuctionListReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UserID string `protobuf:"bytes,1,opt,name=userID,proto3" json:"userID"`
}

func (x *SignedAuctionListReq) Reset() {
	*x = SignedAuctionListReq{}
	mi := &file_pb_custom_contract_proto_msgTypes[57]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SignedAuctionListReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SignedAuctionListReq) ProtoMessage() {}

func (x *SignedAuctionListReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_custom_contract_proto_msgTypes[57]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SignedAuctionListReq.ProtoReflect.Descriptor instead.
func (*SignedAuctionListReq) Descriptor() ([]byte, []int) {
	return file_pb_custom_contract_proto_rawDescGZIP(), []int{57}
}

func (x *SignedAuctionListReq) GetUserID() string {
	if x != nil {
		return x.UserID
	}
	return ""
}

type SignedAuctionListResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Data []*SignedAuctionListResp_Info `protobuf:"bytes,1,rep,name=data,proto3" json:"data"`
}

func (x *SignedAuctionListResp) Reset() {
	*x = SignedAuctionListResp{}
	mi := &file_pb_custom_contract_proto_msgTypes[58]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SignedAuctionListResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SignedAuctionListResp) ProtoMessage() {}

func (x *SignedAuctionListResp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_custom_contract_proto_msgTypes[58]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SignedAuctionListResp.ProtoReflect.Descriptor instead.
func (*SignedAuctionListResp) Descriptor() ([]byte, []int) {
	return file_pb_custom_contract_proto_rawDescGZIP(), []int{58}
}

func (x *SignedAuctionListResp) GetData() []*SignedAuctionListResp_Info {
	if x != nil {
		return x.Data
	}
	return nil
}

type SignedAuctionContractsReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AuctionUuid string `protobuf:"bytes,1,opt,name=auctionUuid,proto3" json:"auctionUuid"`
	SeriesUuid  string `protobuf:"bytes,2,opt,name=seriesUuid,proto3" json:"seriesUuid"`
	UserID      string `protobuf:"bytes,3,opt,name=userID,proto3" json:"userID"`
	SeriesType  int32  `protobuf:"varint,4,opt,name=seriesType,proto3" json:"seriesType"`
}

func (x *SignedAuctionContractsReq) Reset() {
	*x = SignedAuctionContractsReq{}
	mi := &file_pb_custom_contract_proto_msgTypes[59]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SignedAuctionContractsReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SignedAuctionContractsReq) ProtoMessage() {}

func (x *SignedAuctionContractsReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_custom_contract_proto_msgTypes[59]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SignedAuctionContractsReq.ProtoReflect.Descriptor instead.
func (*SignedAuctionContractsReq) Descriptor() ([]byte, []int) {
	return file_pb_custom_contract_proto_rawDescGZIP(), []int{59}
}

func (x *SignedAuctionContractsReq) GetAuctionUuid() string {
	if x != nil {
		return x.AuctionUuid
	}
	return ""
}

func (x *SignedAuctionContractsReq) GetSeriesUuid() string {
	if x != nil {
		return x.SeriesUuid
	}
	return ""
}

func (x *SignedAuctionContractsReq) GetUserID() string {
	if x != nil {
		return x.UserID
	}
	return ""
}

func (x *SignedAuctionContractsReq) GetSeriesType() int32 {
	if x != nil {
		return x.SeriesType
	}
	return 0
}

type ViewUrlsData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Jmxy1  string   `protobuf:"bytes,1,opt,name=jmxy1,proto3" json:"jmxy1"`
	Jmxz2  string   `protobuf:"bytes,2,opt,name=jmxz2,proto3" json:"jmxz2"`
	Pmgg3  string   `protobuf:"bytes,3,opt,name=pmgg3,proto3" json:"pmgg3"`
	Pmgz4  string   `protobuf:"bytes,4,opt,name=pmgz4,proto3" json:"pmgz4"`
	Ppqr5S []string `protobuf:"bytes,5,rep,name=ppqr5s,proto3" json:"ppqr5s"`
	Ppbl6S []string `protobuf:"bytes,6,rep,name=ppbl6s,proto3" json:"ppbl6s"`
	Xsht1S []string `protobuf:"bytes,7,rep,name=xsht1s,proto3" json:"xsht1s"`
}

func (x *ViewUrlsData) Reset() {
	*x = ViewUrlsData{}
	mi := &file_pb_custom_contract_proto_msgTypes[60]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ViewUrlsData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ViewUrlsData) ProtoMessage() {}

func (x *ViewUrlsData) ProtoReflect() protoreflect.Message {
	mi := &file_pb_custom_contract_proto_msgTypes[60]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ViewUrlsData.ProtoReflect.Descriptor instead.
func (*ViewUrlsData) Descriptor() ([]byte, []int) {
	return file_pb_custom_contract_proto_rawDescGZIP(), []int{60}
}

func (x *ViewUrlsData) GetJmxy1() string {
	if x != nil {
		return x.Jmxy1
	}
	return ""
}

func (x *ViewUrlsData) GetJmxz2() string {
	if x != nil {
		return x.Jmxz2
	}
	return ""
}

func (x *ViewUrlsData) GetPmgg3() string {
	if x != nil {
		return x.Pmgg3
	}
	return ""
}

func (x *ViewUrlsData) GetPmgz4() string {
	if x != nil {
		return x.Pmgz4
	}
	return ""
}

func (x *ViewUrlsData) GetPpqr5S() []string {
	if x != nil {
		return x.Ppqr5S
	}
	return nil
}

func (x *ViewUrlsData) GetPpbl6S() []string {
	if x != nil {
		return x.Ppbl6S
	}
	return nil
}

func (x *ViewUrlsData) GetXsht1S() []string {
	if x != nil {
		return x.Xsht1S
	}
	return nil
}

type SignedAuctionContractsResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	SeriesType int32         `protobuf:"varint,1,opt,name=seriesType,proto3" json:"seriesType"`
	ViewUrls   *ViewUrlsData `protobuf:"bytes,2,opt,name=viewUrls,proto3" json:"viewUrls"`
}

func (x *SignedAuctionContractsResp) Reset() {
	*x = SignedAuctionContractsResp{}
	mi := &file_pb_custom_contract_proto_msgTypes[61]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SignedAuctionContractsResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SignedAuctionContractsResp) ProtoMessage() {}

func (x *SignedAuctionContractsResp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_custom_contract_proto_msgTypes[61]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SignedAuctionContractsResp.ProtoReflect.Descriptor instead.
func (*SignedAuctionContractsResp) Descriptor() ([]byte, []int) {
	return file_pb_custom_contract_proto_rawDescGZIP(), []int{61}
}

func (x *SignedAuctionContractsResp) GetSeriesType() int32 {
	if x != nil {
		return x.SeriesType
	}
	return 0
}

func (x *SignedAuctionContractsResp) GetViewUrls() *ViewUrlsData {
	if x != nil {
		return x.ViewUrls
	}
	return nil
}

type AuctionArtworkInfoReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AuctionArtworkUuid string `protobuf:"bytes,1,opt,name=auctionArtworkUuid,proto3" json:"auctionArtworkUuid"`
	UserID             string `protobuf:"bytes,2,opt,name=userID,proto3" json:"userID"`
}

func (x *AuctionArtworkInfoReq) Reset() {
	*x = AuctionArtworkInfoReq{}
	mi := &file_pb_custom_contract_proto_msgTypes[62]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AuctionArtworkInfoReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AuctionArtworkInfoReq) ProtoMessage() {}

func (x *AuctionArtworkInfoReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_custom_contract_proto_msgTypes[62]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AuctionArtworkInfoReq.ProtoReflect.Descriptor instead.
func (*AuctionArtworkInfoReq) Descriptor() ([]byte, []int) {
	return file_pb_custom_contract_proto_rawDescGZIP(), []int{62}
}

func (x *AuctionArtworkInfoReq) GetAuctionArtworkUuid() string {
	if x != nil {
		return x.AuctionArtworkUuid
	}
	return ""
}

func (x *AuctionArtworkInfoReq) GetUserID() string {
	if x != nil {
		return x.UserID
	}
	return ""
}

type AuctionArtworkInfoResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AuctionType string `protobuf:"bytes,1,opt,name=auctionType,proto3" json:"auctionType"`
}

func (x *AuctionArtworkInfoResp) Reset() {
	*x = AuctionArtworkInfoResp{}
	mi := &file_pb_custom_contract_proto_msgTypes[63]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AuctionArtworkInfoResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AuctionArtworkInfoResp) ProtoMessage() {}

func (x *AuctionArtworkInfoResp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_custom_contract_proto_msgTypes[63]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AuctionArtworkInfoResp.ProtoReflect.Descriptor instead.
func (*AuctionArtworkInfoResp) Descriptor() ([]byte, []int) {
	return file_pb_custom_contract_proto_rawDescGZIP(), []int{63}
}

func (x *AuctionArtworkInfoResp) GetAuctionType() string {
	if x != nil {
		return x.AuctionType
	}
	return ""
}

type SignInfoReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	OrderNos []string `protobuf:"bytes,1,rep,name=orderNos,proto3" json:"orderNos"`
}

func (x *SignInfoReq) Reset() {
	*x = SignInfoReq{}
	mi := &file_pb_custom_contract_proto_msgTypes[64]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SignInfoReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SignInfoReq) ProtoMessage() {}

func (x *SignInfoReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_custom_contract_proto_msgTypes[64]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SignInfoReq.ProtoReflect.Descriptor instead.
func (*SignInfoReq) Descriptor() ([]byte, []int) {
	return file_pb_custom_contract_proto_rawDescGZIP(), []int{64}
}

func (x *SignInfoReq) GetOrderNos() []string {
	if x != nil {
		return x.OrderNos
	}
	return nil
}

type SignInfoResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Data []*SignInfoResp_Info `protobuf:"bytes,1,rep,name=Data,proto3" json:"Data"`
}

func (x *SignInfoResp) Reset() {
	*x = SignInfoResp{}
	mi := &file_pb_custom_contract_proto_msgTypes[65]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SignInfoResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SignInfoResp) ProtoMessage() {}

func (x *SignInfoResp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_custom_contract_proto_msgTypes[65]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SignInfoResp.ProtoReflect.Descriptor instead.
func (*SignInfoResp) Descriptor() ([]byte, []int) {
	return file_pb_custom_contract_proto_rawDescGZIP(), []int{65}
}

func (x *SignInfoResp) GetData() []*SignInfoResp_Info {
	if x != nil {
		return x.Data
	}
	return nil
}

type ProtocolSignOnlineResp_Info struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Url1 string `protobuf:"bytes,1,opt,name=url1,proto3" json:"url1"`
}

func (x *ProtocolSignOnlineResp_Info) Reset() {
	*x = ProtocolSignOnlineResp_Info{}
	mi := &file_pb_custom_contract_proto_msgTypes[66]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ProtocolSignOnlineResp_Info) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ProtocolSignOnlineResp_Info) ProtoMessage() {}

func (x *ProtocolSignOnlineResp_Info) ProtoReflect() protoreflect.Message {
	mi := &file_pb_custom_contract_proto_msgTypes[66]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ProtocolSignOnlineResp_Info.ProtoReflect.Descriptor instead.
func (*ProtocolSignOnlineResp_Info) Descriptor() ([]byte, []int) {
	return file_pb_custom_contract_proto_rawDescGZIP(), []int{4, 0}
}

func (x *ProtocolSignOnlineResp_Info) GetUrl1() string {
	if x != nil {
		return x.Url1
	}
	return ""
}

type TemplateDataResp_Info struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Uuid           string `protobuf:"bytes,1,opt,name=uuid,proto3" json:"uuid"`
	ContractCdnUrl string `protobuf:"bytes,2,opt,name=contractCdnUrl,proto3" json:"contractCdnUrl"`
	FddTemplateId  string `protobuf:"bytes,3,opt,name=fddTemplateId,proto3" json:"fddTemplateId"`
	ContractName   string `protobuf:"bytes,4,opt,name=contractName,proto3" json:"contractName"`
	ShortName      string `protobuf:"bytes,5,opt,name=shortName,proto3" json:"shortName"`
}

func (x *TemplateDataResp_Info) Reset() {
	*x = TemplateDataResp_Info{}
	mi := &file_pb_custom_contract_proto_msgTypes[67]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TemplateDataResp_Info) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TemplateDataResp_Info) ProtoMessage() {}

func (x *TemplateDataResp_Info) ProtoReflect() protoreflect.Message {
	mi := &file_pb_custom_contract_proto_msgTypes[67]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TemplateDataResp_Info.ProtoReflect.Descriptor instead.
func (*TemplateDataResp_Info) Descriptor() ([]byte, []int) {
	return file_pb_custom_contract_proto_rawDescGZIP(), []int{6, 0}
}

func (x *TemplateDataResp_Info) GetUuid() string {
	if x != nil {
		return x.Uuid
	}
	return ""
}

func (x *TemplateDataResp_Info) GetContractCdnUrl() string {
	if x != nil {
		return x.ContractCdnUrl
	}
	return ""
}

func (x *TemplateDataResp_Info) GetFddTemplateId() string {
	if x != nil {
		return x.FddTemplateId
	}
	return ""
}

func (x *TemplateDataResp_Info) GetContractName() string {
	if x != nil {
		return x.ContractName
	}
	return ""
}

func (x *TemplateDataResp_Info) GetShortName() string {
	if x != nil {
		return x.ShortName
	}
	return ""
}

type UserContractDataRespContractInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ContractUuid     string `protobuf:"bytes,1,opt,name=contractUuid,proto3" json:"contractUuid"`
	UserId           string `protobuf:"bytes,2,opt,name=userId,proto3" json:"userId"`
	ContractId       string `protobuf:"bytes,3,opt,name=contractId,proto3" json:"contractId"`
	ContractName     string `protobuf:"bytes,4,opt,name=contractName,proto3" json:"contractName"`
	SignType         int32  `protobuf:"varint,5,opt,name=signType,proto3" json:"signType"`
	Status           int32  `protobuf:"varint,6,opt,name=status,proto3" json:"status"`
	BatchNo          string `protobuf:"bytes,7,opt,name=batchNo,proto3" json:"batchNo"`
	ViewUrl          string `protobuf:"bytes,8,opt,name=viewUrl,proto3" json:"viewUrl"`
	OfflineSignOrder string `protobuf:"bytes,9,opt,name=offlineSignOrder,proto3" json:"offlineSignOrder"`
	SignOrder        int32  `protobuf:"varint,10,opt,name=signOrder,proto3" json:"signOrder"`
}

func (x *UserContractDataRespContractInfo) Reset() {
	*x = UserContractDataRespContractInfo{}
	mi := &file_pb_custom_contract_proto_msgTypes[68]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UserContractDataRespContractInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserContractDataRespContractInfo) ProtoMessage() {}

func (x *UserContractDataRespContractInfo) ProtoReflect() protoreflect.Message {
	mi := &file_pb_custom_contract_proto_msgTypes[68]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserContractDataRespContractInfo.ProtoReflect.Descriptor instead.
func (*UserContractDataRespContractInfo) Descriptor() ([]byte, []int) {
	return file_pb_custom_contract_proto_rawDescGZIP(), []int{16, 0}
}

func (x *UserContractDataRespContractInfo) GetContractUuid() string {
	if x != nil {
		return x.ContractUuid
	}
	return ""
}

func (x *UserContractDataRespContractInfo) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *UserContractDataRespContractInfo) GetContractId() string {
	if x != nil {
		return x.ContractId
	}
	return ""
}

func (x *UserContractDataRespContractInfo) GetContractName() string {
	if x != nil {
		return x.ContractName
	}
	return ""
}

func (x *UserContractDataRespContractInfo) GetSignType() int32 {
	if x != nil {
		return x.SignType
	}
	return 0
}

func (x *UserContractDataRespContractInfo) GetStatus() int32 {
	if x != nil {
		return x.Status
	}
	return 0
}

func (x *UserContractDataRespContractInfo) GetBatchNo() string {
	if x != nil {
		return x.BatchNo
	}
	return ""
}

func (x *UserContractDataRespContractInfo) GetViewUrl() string {
	if x != nil {
		return x.ViewUrl
	}
	return ""
}

func (x *UserContractDataRespContractInfo) GetOfflineSignOrder() string {
	if x != nil {
		return x.OfflineSignOrder
	}
	return ""
}

func (x *UserContractDataRespContractInfo) GetSignOrder() int32 {
	if x != nil {
		return x.SignOrder
	}
	return 0
}

type ArtworkBidDataResp_AuctionInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AuctionTime     string `protobuf:"bytes,1,opt,name=AuctionTime,proto3" json:"AuctionTime"`
	AuctionAddress  string `protobuf:"bytes,2,opt,name=AuctionAddress,proto3" json:"AuctionAddress"`
	AuctionName     string `protobuf:"bytes,3,opt,name=AuctionName,proto3" json:"AuctionName"`
	AuctionLOT      string `protobuf:"bytes,4,opt,name=AuctionLOT,proto3" json:"AuctionLOT"`
	PreviewStartY   string `protobuf:"bytes,5,opt,name=PreviewStartY,proto3" json:"PreviewStartY"`
	PreviewStartM   string `protobuf:"bytes,6,opt,name=PreviewStartM,proto3" json:"PreviewStartM"`
	PreviewStartD   string `protobuf:"bytes,7,opt,name=PreviewStartD,proto3" json:"PreviewStartD"`
	PreviewStartHis string `protobuf:"bytes,8,opt,name=PreviewStartHis,proto3" json:"PreviewStartHis"`
	PreviewEndY     string `protobuf:"bytes,9,opt,name=PreviewEndY,proto3" json:"PreviewEndY"`
	PreviewEndM     string `protobuf:"bytes,10,opt,name=PreviewEndM,proto3" json:"PreviewEndM"`
	PreviewEndD     string `protobuf:"bytes,12,opt,name=PreviewEndD,proto3" json:"PreviewEndD"`
	PreviewEndHis   string `protobuf:"bytes,13,opt,name=PreviewEndHis,proto3" json:"PreviewEndHis"`
	PreviewTime     string `protobuf:"bytes,14,opt,name=PreviewTime,proto3" json:"PreviewTime"`
}

func (x *ArtworkBidDataResp_AuctionInfo) Reset() {
	*x = ArtworkBidDataResp_AuctionInfo{}
	mi := &file_pb_custom_contract_proto_msgTypes[69]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ArtworkBidDataResp_AuctionInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ArtworkBidDataResp_AuctionInfo) ProtoMessage() {}

func (x *ArtworkBidDataResp_AuctionInfo) ProtoReflect() protoreflect.Message {
	mi := &file_pb_custom_contract_proto_msgTypes[69]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ArtworkBidDataResp_AuctionInfo.ProtoReflect.Descriptor instead.
func (*ArtworkBidDataResp_AuctionInfo) Descriptor() ([]byte, []int) {
	return file_pb_custom_contract_proto_rawDescGZIP(), []int{22, 0}
}

func (x *ArtworkBidDataResp_AuctionInfo) GetAuctionTime() string {
	if x != nil {
		return x.AuctionTime
	}
	return ""
}

func (x *ArtworkBidDataResp_AuctionInfo) GetAuctionAddress() string {
	if x != nil {
		return x.AuctionAddress
	}
	return ""
}

func (x *ArtworkBidDataResp_AuctionInfo) GetAuctionName() string {
	if x != nil {
		return x.AuctionName
	}
	return ""
}

func (x *ArtworkBidDataResp_AuctionInfo) GetAuctionLOT() string {
	if x != nil {
		return x.AuctionLOT
	}
	return ""
}

func (x *ArtworkBidDataResp_AuctionInfo) GetPreviewStartY() string {
	if x != nil {
		return x.PreviewStartY
	}
	return ""
}

func (x *ArtworkBidDataResp_AuctionInfo) GetPreviewStartM() string {
	if x != nil {
		return x.PreviewStartM
	}
	return ""
}

func (x *ArtworkBidDataResp_AuctionInfo) GetPreviewStartD() string {
	if x != nil {
		return x.PreviewStartD
	}
	return ""
}

func (x *ArtworkBidDataResp_AuctionInfo) GetPreviewStartHis() string {
	if x != nil {
		return x.PreviewStartHis
	}
	return ""
}

func (x *ArtworkBidDataResp_AuctionInfo) GetPreviewEndY() string {
	if x != nil {
		return x.PreviewEndY
	}
	return ""
}

func (x *ArtworkBidDataResp_AuctionInfo) GetPreviewEndM() string {
	if x != nil {
		return x.PreviewEndM
	}
	return ""
}

func (x *ArtworkBidDataResp_AuctionInfo) GetPreviewEndD() string {
	if x != nil {
		return x.PreviewEndD
	}
	return ""
}

func (x *ArtworkBidDataResp_AuctionInfo) GetPreviewEndHis() string {
	if x != nil {
		return x.PreviewEndHis
	}
	return ""
}

func (x *ArtworkBidDataResp_AuctionInfo) GetPreviewTime() string {
	if x != nil {
		return x.PreviewTime
	}
	return ""
}

type ArtworkBidDataResp_BigInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	BidNo    string `protobuf:"bytes,1,opt,name=bidNo,proto3" json:"bidNo"`
	BidPrice string `protobuf:"bytes,2,opt,name=bidPrice,proto3" json:"bidPrice"`
}

func (x *ArtworkBidDataResp_BigInfo) Reset() {
	*x = ArtworkBidDataResp_BigInfo{}
	mi := &file_pb_custom_contract_proto_msgTypes[70]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ArtworkBidDataResp_BigInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ArtworkBidDataResp_BigInfo) ProtoMessage() {}

func (x *ArtworkBidDataResp_BigInfo) ProtoReflect() protoreflect.Message {
	mi := &file_pb_custom_contract_proto_msgTypes[70]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ArtworkBidDataResp_BigInfo.ProtoReflect.Descriptor instead.
func (*ArtworkBidDataResp_BigInfo) Descriptor() ([]byte, []int) {
	return file_pb_custom_contract_proto_rawDescGZIP(), []int{22, 1}
}

func (x *ArtworkBidDataResp_BigInfo) GetBidNo() string {
	if x != nil {
		return x.BidNo
	}
	return ""
}

func (x *ArtworkBidDataResp_BigInfo) GetBidPrice() string {
	if x != nil {
		return x.BidPrice
	}
	return ""
}

type ContractDataResp_ContractInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ContractUuid string `protobuf:"bytes,1,opt,name=contractUuid,proto3" json:"contractUuid"`
	ViewUrl      string `protobuf:"bytes,2,opt,name=viewUrl,proto3" json:"viewUrl"`
	ContractId   string `protobuf:"bytes,3,opt,name=contractId,proto3" json:"contractId"`
}

func (x *ContractDataResp_ContractInfo) Reset() {
	*x = ContractDataResp_ContractInfo{}
	mi := &file_pb_custom_contract_proto_msgTypes[71]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ContractDataResp_ContractInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ContractDataResp_ContractInfo) ProtoMessage() {}

func (x *ContractDataResp_ContractInfo) ProtoReflect() protoreflect.Message {
	mi := &file_pb_custom_contract_proto_msgTypes[71]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ContractDataResp_ContractInfo.ProtoReflect.Descriptor instead.
func (*ContractDataResp_ContractInfo) Descriptor() ([]byte, []int) {
	return file_pb_custom_contract_proto_rawDescGZIP(), []int{24, 0}
}

func (x *ContractDataResp_ContractInfo) GetContractUuid() string {
	if x != nil {
		return x.ContractUuid
	}
	return ""
}

func (x *ContractDataResp_ContractInfo) GetViewUrl() string {
	if x != nil {
		return x.ViewUrl
	}
	return ""
}

func (x *ContractDataResp_ContractInfo) GetContractId() string {
	if x != nil {
		return x.ContractId
	}
	return ""
}

type ContractViewResp_ViewUrlsData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Jmxy1  string   `protobuf:"bytes,1,opt,name=jmxy1,proto3" json:"jmxy1"`
	Jmxz2  string   `protobuf:"bytes,2,opt,name=jmxz2,proto3" json:"jmxz2"`
	Pmgg3  string   `protobuf:"bytes,3,opt,name=pmgg3,proto3" json:"pmgg3"`
	Pmgz4  string   `protobuf:"bytes,4,opt,name=pmgz4,proto3" json:"pmgz4"`
	Ppqr5  string   `protobuf:"bytes,5,opt,name=ppqr5,proto3" json:"ppqr5"`
	Ppbl6  string   `protobuf:"bytes,6,opt,name=ppbl6,proto3" json:"ppbl6"`
	Ppqr5S []string `protobuf:"bytes,7,rep,name=ppqr5s,proto3" json:"ppqr5s"`
	Ppbl6S []string `protobuf:"bytes,8,rep,name=ppbl6s,proto3" json:"ppbl6s"`
}

func (x *ContractViewResp_ViewUrlsData) Reset() {
	*x = ContractViewResp_ViewUrlsData{}
	mi := &file_pb_custom_contract_proto_msgTypes[72]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ContractViewResp_ViewUrlsData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ContractViewResp_ViewUrlsData) ProtoMessage() {}

func (x *ContractViewResp_ViewUrlsData) ProtoReflect() protoreflect.Message {
	mi := &file_pb_custom_contract_proto_msgTypes[72]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ContractViewResp_ViewUrlsData.ProtoReflect.Descriptor instead.
func (*ContractViewResp_ViewUrlsData) Descriptor() ([]byte, []int) {
	return file_pb_custom_contract_proto_rawDescGZIP(), []int{26, 0}
}

func (x *ContractViewResp_ViewUrlsData) GetJmxy1() string {
	if x != nil {
		return x.Jmxy1
	}
	return ""
}

func (x *ContractViewResp_ViewUrlsData) GetJmxz2() string {
	if x != nil {
		return x.Jmxz2
	}
	return ""
}

func (x *ContractViewResp_ViewUrlsData) GetPmgg3() string {
	if x != nil {
		return x.Pmgg3
	}
	return ""
}

func (x *ContractViewResp_ViewUrlsData) GetPmgz4() string {
	if x != nil {
		return x.Pmgz4
	}
	return ""
}

func (x *ContractViewResp_ViewUrlsData) GetPpqr5() string {
	if x != nil {
		return x.Ppqr5
	}
	return ""
}

func (x *ContractViewResp_ViewUrlsData) GetPpbl6() string {
	if x != nil {
		return x.Ppbl6
	}
	return ""
}

func (x *ContractViewResp_ViewUrlsData) GetPpqr5S() []string {
	if x != nil {
		return x.Ppqr5S
	}
	return nil
}

func (x *ContractViewResp_ViewUrlsData) GetPpbl6S() []string {
	if x != nil {
		return x.Ppbl6S
	}
	return nil
}

type RepairFddContractUrlResp_Info struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ContractID string `protobuf:"bytes,1,opt,name=contractID,proto3" json:"contractID"`
	ViewUrl    string `protobuf:"bytes,2,opt,name=viewUrl,proto3" json:"viewUrl"`
	SignType   uint32 `protobuf:"varint,3,opt,name=signType,proto3" json:"signType"`
}

func (x *RepairFddContractUrlResp_Info) Reset() {
	*x = RepairFddContractUrlResp_Info{}
	mi := &file_pb_custom_contract_proto_msgTypes[73]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RepairFddContractUrlResp_Info) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RepairFddContractUrlResp_Info) ProtoMessage() {}

func (x *RepairFddContractUrlResp_Info) ProtoReflect() protoreflect.Message {
	mi := &file_pb_custom_contract_proto_msgTypes[73]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RepairFddContractUrlResp_Info.ProtoReflect.Descriptor instead.
func (*RepairFddContractUrlResp_Info) Descriptor() ([]byte, []int) {
	return file_pb_custom_contract_proto_rawDescGZIP(), []int{34, 0}
}

func (x *RepairFddContractUrlResp_Info) GetContractID() string {
	if x != nil {
		return x.ContractID
	}
	return ""
}

func (x *RepairFddContractUrlResp_Info) GetViewUrl() string {
	if x != nil {
		return x.ViewUrl
	}
	return ""
}

func (x *RepairFddContractUrlResp_Info) GetSignType() uint32 {
	if x != nil {
		return x.SignType
	}
	return 0
}

type ContractViewV2Resp_ViewUrlsData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Jmxy1  string   `protobuf:"bytes,1,opt,name=jmxy1,proto3" json:"jmxy1"`
	Jmxz2  string   `protobuf:"bytes,2,opt,name=jmxz2,proto3" json:"jmxz2"`
	Pmgg3  string   `protobuf:"bytes,3,opt,name=pmgg3,proto3" json:"pmgg3"`
	Pmgz4  string   `protobuf:"bytes,4,opt,name=pmgz4,proto3" json:"pmgz4"`
	Ppqr5  string   `protobuf:"bytes,5,opt,name=ppqr5,proto3" json:"ppqr5"`
	Ppbl6  string   `protobuf:"bytes,6,opt,name=ppbl6,proto3" json:"ppbl6"`
	Ppqr5S []string `protobuf:"bytes,7,rep,name=ppqr5s,proto3" json:"ppqr5s"`
	Ppbl6S []string `protobuf:"bytes,8,rep,name=ppbl6s,proto3" json:"ppbl6s"`
}

func (x *ContractViewV2Resp_ViewUrlsData) Reset() {
	*x = ContractViewV2Resp_ViewUrlsData{}
	mi := &file_pb_custom_contract_proto_msgTypes[76]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ContractViewV2Resp_ViewUrlsData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ContractViewV2Resp_ViewUrlsData) ProtoMessage() {}

func (x *ContractViewV2Resp_ViewUrlsData) ProtoReflect() protoreflect.Message {
	mi := &file_pb_custom_contract_proto_msgTypes[76]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ContractViewV2Resp_ViewUrlsData.ProtoReflect.Descriptor instead.
func (*ContractViewV2Resp_ViewUrlsData) Descriptor() ([]byte, []int) {
	return file_pb_custom_contract_proto_rawDescGZIP(), []int{51, 0}
}

func (x *ContractViewV2Resp_ViewUrlsData) GetJmxy1() string {
	if x != nil {
		return x.Jmxy1
	}
	return ""
}

func (x *ContractViewV2Resp_ViewUrlsData) GetJmxz2() string {
	if x != nil {
		return x.Jmxz2
	}
	return ""
}

func (x *ContractViewV2Resp_ViewUrlsData) GetPmgg3() string {
	if x != nil {
		return x.Pmgg3
	}
	return ""
}

func (x *ContractViewV2Resp_ViewUrlsData) GetPmgz4() string {
	if x != nil {
		return x.Pmgz4
	}
	return ""
}

func (x *ContractViewV2Resp_ViewUrlsData) GetPpqr5() string {
	if x != nil {
		return x.Ppqr5
	}
	return ""
}

func (x *ContractViewV2Resp_ViewUrlsData) GetPpbl6() string {
	if x != nil {
		return x.Ppbl6
	}
	return ""
}

func (x *ContractViewV2Resp_ViewUrlsData) GetPpqr5S() []string {
	if x != nil {
		return x.Ppqr5S
	}
	return nil
}

func (x *ContractViewV2Resp_ViewUrlsData) GetPpbl6S() []string {
	if x != nil {
		return x.Ppbl6S
	}
	return nil
}

type SignedAuctionListResp_Info struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	SeriesUuid  string `protobuf:"bytes,1,opt,name=seriesUuid,proto3" json:"seriesUuid"`
	SeriesName  string `protobuf:"bytes,2,opt,name=seriesName,proto3" json:"seriesName"`
	SeriesType  int32  `protobuf:"varint,3,opt,name=seriesType,proto3" json:"seriesType"`
	AuctionUuid string `protobuf:"bytes,4,opt,name=auctionUuid,proto3" json:"auctionUuid"`
}

func (x *SignedAuctionListResp_Info) Reset() {
	*x = SignedAuctionListResp_Info{}
	mi := &file_pb_custom_contract_proto_msgTypes[78]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SignedAuctionListResp_Info) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SignedAuctionListResp_Info) ProtoMessage() {}

func (x *SignedAuctionListResp_Info) ProtoReflect() protoreflect.Message {
	mi := &file_pb_custom_contract_proto_msgTypes[78]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SignedAuctionListResp_Info.ProtoReflect.Descriptor instead.
func (*SignedAuctionListResp_Info) Descriptor() ([]byte, []int) {
	return file_pb_custom_contract_proto_rawDescGZIP(), []int{58, 0}
}

func (x *SignedAuctionListResp_Info) GetSeriesUuid() string {
	if x != nil {
		return x.SeriesUuid
	}
	return ""
}

func (x *SignedAuctionListResp_Info) GetSeriesName() string {
	if x != nil {
		return x.SeriesName
	}
	return ""
}

func (x *SignedAuctionListResp_Info) GetSeriesType() int32 {
	if x != nil {
		return x.SeriesType
	}
	return 0
}

func (x *SignedAuctionListResp_Info) GetAuctionUuid() string {
	if x != nil {
		return x.AuctionUuid
	}
	return ""
}

type SignInfoResp_Info struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	OrderNo  string `protobuf:"bytes,1,opt,name=orderNo,proto3" json:"orderNo"`
	SignDate string `protobuf:"bytes,2,opt,name=signDate,proto3" json:"signDate"`
	OrderID  string `protobuf:"bytes,3,opt,name=orderID,proto3" json:"orderID"`
}

func (x *SignInfoResp_Info) Reset() {
	*x = SignInfoResp_Info{}
	mi := &file_pb_custom_contract_proto_msgTypes[79]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SignInfoResp_Info) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SignInfoResp_Info) ProtoMessage() {}

func (x *SignInfoResp_Info) ProtoReflect() protoreflect.Message {
	mi := &file_pb_custom_contract_proto_msgTypes[79]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SignInfoResp_Info.ProtoReflect.Descriptor instead.
func (*SignInfoResp_Info) Descriptor() ([]byte, []int) {
	return file_pb_custom_contract_proto_rawDescGZIP(), []int{65, 0}
}

func (x *SignInfoResp_Info) GetOrderNo() string {
	if x != nil {
		return x.OrderNo
	}
	return ""
}

func (x *SignInfoResp_Info) GetSignDate() string {
	if x != nil {
		return x.SignDate
	}
	return ""
}

func (x *SignInfoResp_Info) GetOrderID() string {
	if x != nil {
		return x.OrderID
	}
	return ""
}

var File_pb_custom_contract_proto protoreflect.FileDescriptor

var file_pb_custom_contract_proto_rawDesc = []byte{
	0x0a, 0x18, 0x70, 0x62, 0x2f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x5f, 0x63, 0x6f, 0x6e, 0x74,
	0x72, 0x61, 0x63, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0e, 0x43, 0x75, 0x73, 0x74,
	0x6f, 0x6d, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x61, 0x63, 0x74, 0x1a, 0x1e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x77, 0x72, 0x61, 0x70,
	0x70, 0x65, 0x72, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x20, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x64, 0x65, 0x73, 0x63,
	0x72, 0x69, 0x70, 0x74, 0x6f, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1b, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x65, 0x6d,
	0x70, 0x74, 0x79, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x19, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x61, 0x6e, 0x79, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x16, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x76, 0x61, 0x6c,
	0x69, 0x64, 0x61, 0x74, 0x6f, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xfa, 0x03, 0x0a,
	0x08, 0x55, 0x73, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x20, 0x0a, 0x0b, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x72, 0x79, 0x43, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x70,
	0x68, 0x6f, 0x6e, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x70, 0x68, 0x6f, 0x6e,
	0x65, 0x12, 0x1a, 0x0a, 0x08, 0x75, 0x73, 0x65, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x08, 0x75, 0x73, 0x65, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x16, 0x0a,
	0x06, 0x67, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x67,
	0x65, 0x6e, 0x64, 0x65, 0x72, 0x12, 0x1a, 0x0a, 0x08, 0x62, 0x69, 0x72, 0x74, 0x68, 0x64, 0x61,
	0x79, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x62, 0x69, 0x72, 0x74, 0x68, 0x64, 0x61,
	0x79, 0x12, 0x18, 0x0a, 0x07, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x18, 0x06, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x07, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x12, 0x1a, 0x0a, 0x08, 0x62,
	0x61, 0x6e, 0x6b, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x62,
	0x61, 0x6e, 0x6b, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x62, 0x61, 0x6e, 0x6b, 0x4e,
	0x6f, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x62, 0x61, 0x6e, 0x6b, 0x4e, 0x6f, 0x12,
	0x24, 0x0a, 0x0d, 0x66, 0x64, 0x64, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x49, 0x64,
	0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x66, 0x64, 0x64, 0x43, 0x75, 0x73, 0x74, 0x6f,
	0x6d, 0x65, 0x72, 0x49, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x63, 0x61, 0x72, 0x64, 0x49, 0x64, 0x18,
	0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x63, 0x61, 0x72, 0x64, 0x49, 0x64, 0x12, 0x1a, 0x0a,
	0x08, 0x63, 0x61, 0x72, 0x64, 0x54, 0x79, 0x70, 0x65, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x08, 0x63, 0x61, 0x72, 0x64, 0x54, 0x79, 0x70, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x73, 0x65, 0x78,
	0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x73, 0x65, 0x78, 0x12, 0x22, 0x0a, 0x0c, 0x63,
	0x61, 0x72, 0x64, 0x54, 0x79, 0x70, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x0d, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0c, 0x63, 0x61, 0x72, 0x64, 0x54, 0x79, 0x70, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12,
	0x1e, 0x0a, 0x0a, 0x69, 0x73, 0x4d, 0x61, 0x69, 0x6e, 0x4c, 0x61, 0x6e, 0x64, 0x18, 0x0e, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x0a, 0x69, 0x73, 0x4d, 0x61, 0x69, 0x6e, 0x4c, 0x61, 0x6e, 0x64, 0x12,
	0x16, 0x0a, 0x06, 0x55, 0x73, 0x65, 0x72, 0x49, 0x44, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x06, 0x55, 0x73, 0x65, 0x72, 0x49, 0x44, 0x12, 0x2c, 0x0a, 0x11, 0x66, 0x64, 0x64, 0x52, 0x65,
	0x67, 0x69, 0x73, 0x74, 0x65, 0x72, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x10, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x11, 0x66, 0x64, 0x64, 0x52, 0x65, 0x67, 0x69, 0x73, 0x74, 0x65, 0x72, 0x53,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x22, 0x0a, 0x0c, 0x72, 0x65, 0x67, 0x69, 0x73, 0x74, 0x65,
	0x72, 0x54, 0x79, 0x70, 0x65, 0x18, 0x11, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0c, 0x72, 0x65, 0x67,
	0x69, 0x73, 0x74, 0x65, 0x72, 0x54, 0x79, 0x70, 0x65, 0x22, 0xde, 0x03, 0x0a, 0x16, 0x50, 0x72,
	0x6f, 0x74, 0x6f, 0x63, 0x6f, 0x6c, 0x53, 0x69, 0x67, 0x6e, 0x4f, 0x66, 0x66, 0x6c, 0x69, 0x6e,
	0x65, 0x52, 0x65, 0x71, 0x12, 0x34, 0x0a, 0x08, 0x75, 0x73, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x43,
	0x6f, 0x6e, 0x74, 0x72, 0x61, 0x63, 0x74, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f,
	0x52, 0x08, 0x75, 0x73, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x28, 0x0a, 0x0f, 0x73, 0x69,
	0x67, 0x6e, 0x49, 0x6d, 0x67, 0x46, 0x69, 0x6c, 0x65, 0x44, 0x61, 0x74, 0x61, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0f, 0x73, 0x69, 0x67, 0x6e, 0x49, 0x6d, 0x67, 0x46, 0x69, 0x6c, 0x65,
	0x44, 0x61, 0x74, 0x61, 0x12, 0x1c, 0x0a, 0x09, 0x73, 0x69, 0x67, 0x6e, 0x4f, 0x72, 0x64, 0x65,
	0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x09, 0x73, 0x69, 0x67, 0x6e, 0x4f, 0x72, 0x64,
	0x65, 0x72, 0x12, 0x16, 0x0a, 0x06, 0x62, 0x69, 0x64, 0x4e, 0x75, 0x6d, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x06, 0x62, 0x69, 0x64, 0x4e, 0x75, 0x6d, 0x12, 0x1a, 0x0a, 0x08, 0x62, 0x69,
	0x64, 0x50, 0x72, 0x69, 0x63, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x62, 0x69,
	0x64, 0x50, 0x72, 0x69, 0x63, 0x65, 0x12, 0x1e, 0x0a, 0x0a, 0x69, 0x73, 0x4d, 0x61, 0x69, 0x6e,
	0x4c, 0x61, 0x6e, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0a, 0x69, 0x73, 0x4d, 0x61,
	0x69, 0x6e, 0x4c, 0x61, 0x6e, 0x64, 0x12, 0x2e, 0x0a, 0x12, 0x61, 0x75, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x41, 0x72, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x55, 0x75, 0x69, 0x64, 0x18, 0x07, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x12, 0x61, 0x75, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x41, 0x72, 0x74, 0x77, 0x6f,
	0x72, 0x6b, 0x55, 0x75, 0x69, 0x64, 0x12, 0x26, 0x0a, 0x0e, 0x74, 0x65, 0x73, 0x74, 0x52, 0x65,
	0x74, 0x75, 0x72, 0x6e, 0x48, 0x6f, 0x73, 0x74, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e,
	0x74, 0x65, 0x73, 0x74, 0x52, 0x65, 0x74, 0x75, 0x72, 0x6e, 0x48, 0x6f, 0x73, 0x74, 0x12, 0x2e,
	0x0a, 0x12, 0x74, 0x65, 0x73, 0x74, 0x52, 0x65, 0x74, 0x75, 0x72, 0x6e, 0x45, 0x6e, 0x64, 0x50,
	0x6f, 0x69, 0x6e, 0x74, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x12, 0x74, 0x65, 0x73, 0x74,
	0x52, 0x65, 0x74, 0x75, 0x72, 0x6e, 0x45, 0x6e, 0x64, 0x50, 0x6f, 0x69, 0x6e, 0x74, 0x12, 0x20,
	0x0a, 0x0b, 0x61, 0x75, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x55, 0x75, 0x69, 0x64, 0x18, 0x0a, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0b, 0x61, 0x75, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x55, 0x75, 0x69, 0x64,
	0x12, 0x30, 0x0a, 0x13, 0x61, 0x75, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x41, 0x72, 0x74, 0x77, 0x6f,
	0x72, 0x6b, 0x55, 0x75, 0x69, 0x64, 0x73, 0x18, 0x0b, 0x20, 0x03, 0x28, 0x09, 0x52, 0x13, 0x61,
	0x75, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x41, 0x72, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x55, 0x75, 0x69,
	0x64, 0x73, 0x12, 0x16, 0x0a, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x44, 0x18, 0x0c, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x44, 0x22, 0x8b, 0x01, 0x0a, 0x17, 0x50,
	0x72, 0x6f, 0x74, 0x6f, 0x63, 0x6f, 0x6c, 0x53, 0x69, 0x67, 0x6e, 0x4f, 0x66, 0x66, 0x6c, 0x69,
	0x6e, 0x65, 0x52, 0x65, 0x73, 0x70, 0x12, 0x34, 0x0a, 0x08, 0x73, 0x69, 0x67, 0x6e, 0x54, 0x79,
	0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x18, 0x2e, 0x43, 0x75, 0x73, 0x74, 0x6f,
	0x6d, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x61, 0x63, 0x74, 0x2e, 0x53, 0x69, 0x67, 0x6e, 0x54, 0x79,
	0x70, 0x65, 0x52, 0x08, 0x73, 0x69, 0x67, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x12, 0x16, 0x0a, 0x06,
	0x75, 0x73, 0x65, 0x72, 0x49, 0x44, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x75, 0x73,
	0x65, 0x72, 0x49, 0x44, 0x12, 0x22, 0x0a, 0x0c, 0x66, 0x64, 0x64, 0x56, 0x65, 0x72, 0x69, 0x66,
	0x79, 0x55, 0x72, 0x6c, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x66, 0x64, 0x64, 0x56,
	0x65, 0x72, 0x69, 0x66, 0x79, 0x55, 0x72, 0x6c, 0x22, 0xa5, 0x03, 0x0a, 0x15, 0x50, 0x72, 0x6f,
	0x74, 0x6f, 0x63, 0x6f, 0x6c, 0x53, 0x69, 0x67, 0x6e, 0x4f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x52,
	0x65, 0x71, 0x12, 0x16, 0x0a, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x44, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x44, 0x12, 0x28, 0x0a, 0x0f, 0x73, 0x69,
	0x67, 0x6e, 0x49, 0x6d, 0x67, 0x46, 0x69, 0x6c, 0x65, 0x44, 0x61, 0x74, 0x61, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0f, 0x73, 0x69, 0x67, 0x6e, 0x49, 0x6d, 0x67, 0x46, 0x69, 0x6c, 0x65,
	0x44, 0x61, 0x74, 0x61, 0x12, 0x1e, 0x0a, 0x0a, 0x69, 0x73, 0x4d, 0x61, 0x69, 0x6e, 0x4c, 0x61,
	0x6e, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0a, 0x69, 0x73, 0x4d, 0x61, 0x69, 0x6e,
	0x4c, 0x61, 0x6e, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x05, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x12, 0x24, 0x0a, 0x0d, 0x66, 0x64,
	0x64, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x49, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0d, 0x66, 0x64, 0x64, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x49, 0x64,
	0x12, 0x2e, 0x0a, 0x12, 0x61, 0x75, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x41, 0x72, 0x74, 0x77, 0x6f,
	0x72, 0x6b, 0x55, 0x75, 0x69, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x12, 0x61, 0x75,
	0x63, 0x74, 0x69, 0x6f, 0x6e, 0x41, 0x72, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x55, 0x75, 0x69, 0x64,
	0x12, 0x26, 0x0a, 0x0e, 0x74, 0x65, 0x73, 0x74, 0x52, 0x65, 0x74, 0x75, 0x72, 0x6e, 0x48, 0x6f,
	0x73, 0x74, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x74, 0x65, 0x73, 0x74, 0x52, 0x65,
	0x74, 0x75, 0x72, 0x6e, 0x48, 0x6f, 0x73, 0x74, 0x12, 0x2e, 0x0a, 0x12, 0x74, 0x65, 0x73, 0x74,
	0x52, 0x65, 0x74, 0x75, 0x72, 0x6e, 0x45, 0x6e, 0x64, 0x50, 0x6f, 0x69, 0x6e, 0x74, 0x18, 0x09,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x12, 0x74, 0x65, 0x73, 0x74, 0x52, 0x65, 0x74, 0x75, 0x72, 0x6e,
	0x45, 0x6e, 0x64, 0x50, 0x6f, 0x69, 0x6e, 0x74, 0x12, 0x34, 0x0a, 0x08, 0x75, 0x73, 0x65, 0x72,
	0x49, 0x6e, 0x66, 0x6f, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x43, 0x75, 0x73,
	0x74, 0x6f, 0x6d, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x61, 0x63, 0x74, 0x2e, 0x55, 0x73, 0x65, 0x72,
	0x49, 0x6e, 0x66, 0x6f, 0x52, 0x08, 0x75, 0x73, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x30,
	0x0a, 0x13, 0x61, 0x75, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x41, 0x72, 0x74, 0x77, 0x6f, 0x72, 0x6b,
	0x55, 0x75, 0x69, 0x64, 0x73, 0x18, 0x0b, 0x20, 0x03, 0x28, 0x09, 0x52, 0x13, 0x61, 0x75, 0x63,
	0x74, 0x69, 0x6f, 0x6e, 0x41, 0x72, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x55, 0x75, 0x69, 0x64, 0x73,
	0x22, 0x75, 0x0a, 0x16, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x6f, 0x6c, 0x53, 0x69, 0x67, 0x6e,
	0x4f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x52, 0x65, 0x73, 0x70, 0x12, 0x3f, 0x0a, 0x04, 0x44, 0x61,
	0x74, 0x61, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2b, 0x2e, 0x43, 0x75, 0x73, 0x74, 0x6f,
	0x6d, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x61, 0x63, 0x74, 0x2e, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x63,
	0x6f, 0x6c, 0x53, 0x69, 0x67, 0x6e, 0x4f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x52, 0x65, 0x73, 0x70,
	0x2e, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x04, 0x44, 0x61, 0x74, 0x61, 0x1a, 0x1a, 0x0a, 0x04, 0x49,
	0x6e, 0x66, 0x6f, 0x12, 0x12, 0x0a, 0x04, 0x75, 0x72, 0x6c, 0x31, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x04, 0x75, 0x72, 0x6c, 0x31, 0x22, 0x31, 0x0a, 0x0f, 0x54, 0x65, 0x6d, 0x70, 0x6c,
	0x61, 0x74, 0x65, 0x44, 0x61, 0x74, 0x61, 0x52, 0x65, 0x71, 0x12, 0x1e, 0x0a, 0x0a, 0x74, 0x65,
	0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a,
	0x74, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x49, 0x64, 0x22, 0xfa, 0x01, 0x0a, 0x10, 0x54,
	0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x44, 0x61, 0x74, 0x61, 0x52, 0x65, 0x73, 0x70, 0x12,
	0x39, 0x0a, 0x04, 0x44, 0x61, 0x74, 0x61, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x25, 0x2e,
	0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x61, 0x63, 0x74, 0x2e, 0x54,
	0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x44, 0x61, 0x74, 0x61, 0x52, 0x65, 0x73, 0x70, 0x2e,
	0x49, 0x6e, 0x66, 0x6f, 0x52, 0x04, 0x44, 0x61, 0x74, 0x61, 0x1a, 0xaa, 0x01, 0x0a, 0x04, 0x49,
	0x6e, 0x66, 0x6f, 0x12, 0x12, 0x0a, 0x04, 0x75, 0x75, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x04, 0x75, 0x75, 0x69, 0x64, 0x12, 0x26, 0x0a, 0x0e, 0x63, 0x6f, 0x6e, 0x74, 0x72,
	0x61, 0x63, 0x74, 0x43, 0x64, 0x6e, 0x55, 0x72, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0e, 0x63, 0x6f, 0x6e, 0x74, 0x72, 0x61, 0x63, 0x74, 0x43, 0x64, 0x6e, 0x55, 0x72, 0x6c, 0x12,
	0x24, 0x0a, 0x0d, 0x66, 0x64, 0x64, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x49, 0x64,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x66, 0x64, 0x64, 0x54, 0x65, 0x6d, 0x70, 0x6c,
	0x61, 0x74, 0x65, 0x49, 0x64, 0x12, 0x22, 0x0a, 0x0c, 0x63, 0x6f, 0x6e, 0x74, 0x72, 0x61, 0x63,
	0x74, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x63, 0x6f, 0x6e,
	0x74, 0x72, 0x61, 0x63, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1c, 0x0a, 0x09, 0x73, 0x68, 0x6f,
	0x72, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x73, 0x68,
	0x6f, 0x72, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x22, 0xd9, 0x01, 0x0a, 0x0b, 0x55, 0x73, 0x65, 0x72,
	0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x71, 0x12, 0x14, 0x0a, 0x05, 0x70, 0x68, 0x6f, 0x6e, 0x65,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x12, 0x22, 0x0a,
	0x0c, 0x6e, 0x65, 0x65, 0x64, 0x52, 0x65, 0x67, 0x69, 0x73, 0x74, 0x65, 0x72, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x08, 0x52, 0x0c, 0x6e, 0x65, 0x65, 0x64, 0x52, 0x65, 0x67, 0x69, 0x73, 0x74, 0x65,
	0x72, 0x12, 0x20, 0x0a, 0x0b, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79, 0x43, 0x6f, 0x64, 0x65,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79, 0x43,
	0x6f, 0x64, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x63, 0x61, 0x72, 0x64, 0x49, 0x64, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x06, 0x63, 0x61, 0x72, 0x64, 0x49, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x75,
	0x73, 0x65, 0x72, 0x49, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x75, 0x73, 0x65,
	0x72, 0x49, 0x64, 0x12, 0x22, 0x0a, 0x0c, 0x72, 0x65, 0x67, 0x69, 0x73, 0x74, 0x65, 0x72, 0x54,
	0x79, 0x70, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0c, 0x72, 0x65, 0x67, 0x69, 0x73,
	0x74, 0x65, 0x72, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x6e, 0x69, 0x63, 0x6b, 0x6e,
	0x61, 0x6d, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x6e, 0x69, 0x63, 0x6b, 0x6e,
	0x61, 0x6d, 0x65, 0x22, 0xed, 0x01, 0x0a, 0x0f, 0x55, 0x73, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f,
	0x52, 0x65, 0x71, 0x52, 0x65, 0x73, 0x70, 0x12, 0x16, 0x0a, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x12,
	0x14, 0x0a, 0x05, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05,
	0x70, 0x68, 0x6f, 0x6e, 0x65, 0x12, 0x24, 0x0a, 0x0d, 0x66, 0x64, 0x64, 0x43, 0x75, 0x73, 0x74,
	0x6f, 0x6d, 0x65, 0x72, 0x49, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x66, 0x64,
	0x64, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x49, 0x64, 0x12, 0x20, 0x0a, 0x0b, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79, 0x43, 0x6f, 0x64, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0b, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x1e, 0x0a,
	0x0a, 0x69, 0x73, 0x4d, 0x61, 0x69, 0x6e, 0x6c, 0x61, 0x6e, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x0d, 0x52, 0x0a, 0x69, 0x73, 0x4d, 0x61, 0x69, 0x6e, 0x6c, 0x61, 0x6e, 0x64, 0x12, 0x2c, 0x0a,
	0x11, 0x66, 0x64, 0x64, 0x52, 0x65, 0x67, 0x69, 0x73, 0x74, 0x65, 0x72, 0x53, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x18, 0x06, 0x20, 0x01, 0x28, 0x05, 0x52, 0x11, 0x66, 0x64, 0x64, 0x52, 0x65, 0x67,
	0x69, 0x73, 0x74, 0x65, 0x72, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x16, 0x0a, 0x06, 0x73,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x07, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x73, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x22, 0xc1, 0x02, 0x0a, 0x11, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x55, 0x73,
	0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x71, 0x12, 0x16, 0x0a, 0x06, 0x75, 0x73, 0x65,
	0x72, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49,
	0x64, 0x12, 0x24, 0x0a, 0x0d, 0x66, 0x64, 0x64, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72,
	0x49, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x66, 0x64, 0x64, 0x43, 0x75, 0x73,
	0x74, 0x6f, 0x6d, 0x65, 0x72, 0x49, 0x64, 0x12, 0x1e, 0x0a, 0x0a, 0x69, 0x73, 0x4d, 0x61, 0x69,
	0x6e, 0x6c, 0x61, 0x6e, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0a, 0x69, 0x73, 0x4d,
	0x61, 0x69, 0x6e, 0x6c, 0x61, 0x6e, 0x64, 0x12, 0x24, 0x0a, 0x0d, 0x74, 0x72, 0x61, 0x6e, 0x73,
	0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x4e, 0x6f, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d,
	0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x4e, 0x6f, 0x12, 0x18, 0x0a,
	0x07, 0x66, 0x64, 0x64, 0x49, 0x6e, 0x66, 0x6f, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07,
	0x66, 0x64, 0x64, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x1c, 0x0a, 0x09, 0x66, 0x64, 0x64, 0x53, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x18, 0x06, 0x20, 0x01, 0x28, 0x05, 0x52, 0x09, 0x66, 0x64, 0x64, 0x53,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x22, 0x0a, 0x0c, 0x72, 0x65, 0x67, 0x69, 0x73, 0x74, 0x65,
	0x72, 0x54, 0x79, 0x70, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0c, 0x72, 0x65, 0x67,
	0x69, 0x73, 0x74, 0x65, 0x72, 0x54, 0x79, 0x70, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x18, 0x08, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x12, 0x34, 0x0a, 0x08, 0x75, 0x73, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x18, 0x09, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x43, 0x6f, 0x6e, 0x74,
	0x72, 0x61, 0x63, 0x74, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x08, 0x75,
	0x73, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x22, 0x2c, 0x0a, 0x12, 0x55, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x55, 0x73, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x73, 0x70, 0x12, 0x16, 0x0a,
	0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x75,
	0x73, 0x65, 0x72, 0x49, 0x64, 0x22, 0xdf, 0x04, 0x0a, 0x11, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x43, 0x6f, 0x6e, 0x74, 0x72, 0x61, 0x63, 0x74, 0x52, 0x65, 0x71, 0x12, 0x1e, 0x0a, 0x0a, 0x63,
	0x6f, 0x6e, 0x74, 0x72, 0x61, 0x63, 0x74, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0a, 0x63, 0x6f, 0x6e, 0x74, 0x72, 0x61, 0x63, 0x74, 0x49, 0x64, 0x12, 0x1e, 0x0a, 0x0a, 0x74,
	0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x49, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0a, 0x74, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x49, 0x64, 0x12, 0x22, 0x0a, 0x0c, 0x63,
	0x6f, 0x6e, 0x74, 0x72, 0x61, 0x63, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0c, 0x63, 0x6f, 0x6e, 0x74, 0x72, 0x61, 0x63, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x12,
	0x1c, 0x0a, 0x09, 0x73, 0x68, 0x6f, 0x72, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x09, 0x73, 0x68, 0x6f, 0x72, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x16, 0x0a,
	0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x75,
	0x73, 0x65, 0x72, 0x49, 0x64, 0x12, 0x1a, 0x0a, 0x08, 0x73, 0x69, 0x67, 0x6e, 0x54, 0x79, 0x70,
	0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x73, 0x69, 0x67, 0x6e, 0x54, 0x79, 0x70,
	0x65, 0x12, 0x18, 0x0a, 0x07, 0x62, 0x61, 0x74, 0x63, 0x68, 0x4e, 0x6f, 0x18, 0x07, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x07, 0x62, 0x61, 0x74, 0x63, 0x68, 0x4e, 0x6f, 0x12, 0x22, 0x0a, 0x0c, 0x63,
	0x6f, 0x6e, 0x74, 0x72, 0x61, 0x63, 0x74, 0x55, 0x75, 0x69, 0x64, 0x18, 0x08, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0c, 0x63, 0x6f, 0x6e, 0x74, 0x72, 0x61, 0x63, 0x74, 0x55, 0x75, 0x69, 0x64, 0x12,
	0x16, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x09, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x1c, 0x0a, 0x09, 0x73, 0x69, 0x67, 0x6e, 0x4f,
	0x72, 0x64, 0x65, 0x72, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x05, 0x52, 0x09, 0x73, 0x69, 0x67, 0x6e,
	0x4f, 0x72, 0x64, 0x65, 0x72, 0x12, 0x14, 0x0a, 0x05, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x18, 0x0b,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x6c,
	0x69, 0x6e, 0x65, 0x54, 0x79, 0x70, 0x65, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x6c,
	0x69, 0x6e, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x76, 0x69, 0x65, 0x77, 0x55,
	0x72, 0x6c, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x76, 0x69, 0x65, 0x77, 0x55, 0x72,
	0x6c, 0x12, 0x20, 0x0a, 0x0b, 0x61, 0x75, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x55, 0x75, 0x69, 0x64,
	0x18, 0x0e, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x61, 0x75, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x55,
	0x75, 0x69, 0x64, 0x12, 0x2e, 0x0a, 0x12, 0x61, 0x75, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x41, 0x72,
	0x74, 0x77, 0x6f, 0x72, 0x6b, 0x55, 0x75, 0x69, 0x64, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x12, 0x61, 0x75, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x41, 0x72, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x55,
	0x75, 0x69, 0x64, 0x12, 0x20, 0x0a, 0x0b, 0x64, 0x6f, 0x77, 0x6e, 0x6c, 0x6f, 0x61, 0x64, 0x55,
	0x72, 0x6c, 0x18, 0x10, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x6f, 0x77, 0x6e, 0x6c, 0x6f,
	0x61, 0x64, 0x55, 0x72, 0x6c, 0x12, 0x1e, 0x0a, 0x0a, 0x73, 0x65, 0x72, 0x69, 0x65, 0x73, 0x55,
	0x75, 0x69, 0x64, 0x18, 0x11, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x73, 0x65, 0x72, 0x69, 0x65,
	0x73, 0x55, 0x75, 0x69, 0x64, 0x12, 0x20, 0x0a, 0x0b, 0x6e, 0x65, 0x65, 0x64, 0x4f, 0x72, 0x64,
	0x65, 0x72, 0x4e, 0x6f, 0x18, 0x12, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0b, 0x6e, 0x65, 0x65, 0x64,
	0x4f, 0x72, 0x64, 0x65, 0x72, 0x4e, 0x6f, 0x12, 0x1e, 0x0a, 0x0a, 0x73, 0x6f, 0x75, 0x72, 0x63,
	0x65, 0x54, 0x79, 0x70, 0x65, 0x18, 0x13, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x73, 0x6f, 0x75,
	0x72, 0x63, 0x65, 0x54, 0x79, 0x70, 0x65, 0x22, 0x30, 0x0a, 0x12, 0x55, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x61, 0x63, 0x74, 0x52, 0x65, 0x73, 0x70, 0x12, 0x1a, 0x0a,
	0x08, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x4e, 0x6f, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x09, 0x52,
	0x08, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x4e, 0x6f, 0x73, 0x22, 0x64, 0x0a, 0x16, 0x55, 0x70, 0x64,
	0x61, 0x74, 0x65, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x61, 0x63, 0x74, 0x42, 0x61, 0x74, 0x63, 0x68,
	0x52, 0x65, 0x71, 0x12, 0x18, 0x0a, 0x07, 0x62, 0x61, 0x74, 0x63, 0x68, 0x4e, 0x6f, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x62, 0x61, 0x74, 0x63, 0x68, 0x4e, 0x6f, 0x12, 0x16, 0x0a,
	0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x73,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x18, 0x0a, 0x07, 0x66, 0x64, 0x64, 0x49, 0x6e, 0x66, 0x6f,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x66, 0x64, 0x64, 0x49, 0x6e, 0x66, 0x6f, 0x22,
	0x19, 0x0a, 0x17, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x61, 0x63,
	0x74, 0x42, 0x61, 0x74, 0x63, 0x68, 0x52, 0x65, 0x73, 0x70, 0x22, 0x4d, 0x0a, 0x13, 0x55, 0x73,
	0x65, 0x72, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x61, 0x63, 0x74, 0x44, 0x61, 0x74, 0x61, 0x52, 0x65,
	0x71, 0x12, 0x16, 0x0a, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x12, 0x1e, 0x0a, 0x0a, 0x63, 0x6f, 0x6e,
	0x74, 0x72, 0x61, 0x63, 0x74, 0x49, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x63,
	0x6f, 0x6e, 0x74, 0x72, 0x61, 0x63, 0x74, 0x49, 0x64, 0x22, 0xd9, 0x02, 0x0a, 0x14, 0x55, 0x73,
	0x65, 0x72, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x61, 0x63, 0x74, 0x44, 0x61, 0x74, 0x61, 0x52, 0x65,
	0x73, 0x70, 0x1a, 0xc0, 0x02, 0x0a, 0x0c, 0x63, 0x6f, 0x6e, 0x74, 0x72, 0x61, 0x63, 0x74, 0x49,
	0x6e, 0x66, 0x6f, 0x12, 0x22, 0x0a, 0x0c, 0x63, 0x6f, 0x6e, 0x74, 0x72, 0x61, 0x63, 0x74, 0x55,
	0x75, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x63, 0x6f, 0x6e, 0x74, 0x72,
	0x61, 0x63, 0x74, 0x55, 0x75, 0x69, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49,
	0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x12,
	0x1e, 0x0a, 0x0a, 0x63, 0x6f, 0x6e, 0x74, 0x72, 0x61, 0x63, 0x74, 0x49, 0x64, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0a, 0x63, 0x6f, 0x6e, 0x74, 0x72, 0x61, 0x63, 0x74, 0x49, 0x64, 0x12,
	0x22, 0x0a, 0x0c, 0x63, 0x6f, 0x6e, 0x74, 0x72, 0x61, 0x63, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x63, 0x6f, 0x6e, 0x74, 0x72, 0x61, 0x63, 0x74, 0x4e,
	0x61, 0x6d, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x73, 0x69, 0x67, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x73, 0x69, 0x67, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x12,
	0x16, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x06, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x18, 0x0a, 0x07, 0x62, 0x61, 0x74, 0x63, 0x68,
	0x4e, 0x6f, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x62, 0x61, 0x74, 0x63, 0x68, 0x4e,
	0x6f, 0x12, 0x18, 0x0a, 0x07, 0x76, 0x69, 0x65, 0x77, 0x55, 0x72, 0x6c, 0x18, 0x08, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x07, 0x76, 0x69, 0x65, 0x77, 0x55, 0x72, 0x6c, 0x12, 0x2a, 0x0a, 0x10, 0x6f,
	0x66, 0x66, 0x6c, 0x69, 0x6e, 0x65, 0x53, 0x69, 0x67, 0x6e, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x18,
	0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x6f, 0x66, 0x66, 0x6c, 0x69, 0x6e, 0x65, 0x53, 0x69,
	0x67, 0x6e, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x12, 0x1c, 0x0a, 0x09, 0x73, 0x69, 0x67, 0x6e, 0x4f,
	0x72, 0x64, 0x65, 0x72, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x05, 0x52, 0x09, 0x73, 0x69, 0x67, 0x6e,
	0x4f, 0x72, 0x64, 0x65, 0x72, 0x22, 0x22, 0x0a, 0x0a, 0x46, 0x64, 0x64, 0x49, 0x6e, 0x66, 0x6f,
	0x52, 0x65, 0x71, 0x12, 0x14, 0x0a, 0x05, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x05, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x22, 0x7d, 0x0a, 0x0b, 0x46, 0x64, 0x64,
	0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x73, 0x70, 0x12, 0x16, 0x0a, 0x06, 0x75, 0x73, 0x65, 0x72,
	0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64,
	0x12, 0x16, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x1e, 0x0a, 0x0a, 0x63, 0x75, 0x73, 0x74,
	0x6f, 0x6d, 0x65, 0x72, 0x49, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x63, 0x75,
	0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x49, 0x64, 0x12, 0x1e, 0x0a, 0x0a, 0x69, 0x73, 0x4d, 0x61,
	0x69, 0x6e, 0x6c, 0x61, 0x6e, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0a, 0x69, 0x73,
	0x4d, 0x61, 0x69, 0x6e, 0x6c, 0x61, 0x6e, 0x64, 0x22, 0x2b, 0x0a, 0x13, 0x4f, 0x66, 0x66, 0x6c,
	0x69, 0x6e, 0x65, 0x53, 0x69, 0x67, 0x6e, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x52, 0x65, 0x71, 0x12,
	0x14, 0x0a, 0x05, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05,
	0x70, 0x68, 0x6f, 0x6e, 0x65, 0x22, 0x34, 0x0a, 0x14, 0x4f, 0x66, 0x66, 0x6c, 0x69, 0x6e, 0x65,
	0x53, 0x69, 0x67, 0x6e, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x52, 0x65, 0x73, 0x70, 0x12, 0x1c, 0x0a,
	0x09, 0x73, 0x69, 0x67, 0x6e, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x09, 0x73, 0x69, 0x67, 0x6e, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x22, 0x59, 0x0a, 0x11, 0x41,
	0x72, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x42, 0x69, 0x64, 0x44, 0x61, 0x74, 0x61, 0x52, 0x65, 0x71,
	0x12, 0x14, 0x0a, 0x05, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x05, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x12, 0x2e, 0x0a, 0x12, 0x61, 0x75, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x41, 0x72, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x55, 0x75, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x12, 0x61, 0x75, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x41, 0x72, 0x74, 0x77, 0x6f,
	0x72, 0x6b, 0x55, 0x75, 0x69, 0x64, 0x22, 0xc5, 0x08, 0x0a, 0x12, 0x41, 0x72, 0x74, 0x77, 0x6f,
	0x72, 0x6b, 0x42, 0x69, 0x64, 0x44, 0x61, 0x74, 0x61, 0x52, 0x65, 0x73, 0x70, 0x12, 0x44, 0x0a,
	0x07, 0x62, 0x69, 0x67, 0x44, 0x61, 0x74, 0x61, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2a,
	0x2e, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x61, 0x63, 0x74, 0x2e,
	0x41, 0x72, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x42, 0x69, 0x64, 0x44, 0x61, 0x74, 0x61, 0x52, 0x65,
	0x73, 0x70, 0x2e, 0x42, 0x69, 0x67, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x07, 0x62, 0x69, 0x67, 0x44,
	0x61, 0x74, 0x61, 0x12, 0x20, 0x0a, 0x0b, 0x61, 0x72, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x4e, 0x61,
	0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x61, 0x72, 0x74, 0x77, 0x6f, 0x72,
	0x6b, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1c, 0x0a, 0x09, 0x73, 0x6f, 0x6c, 0x64, 0x50, 0x72, 0x69,
	0x63, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x73, 0x6f, 0x6c, 0x64, 0x50, 0x72,
	0x69, 0x63, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x41, 0x72, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x53, 0x69,
	0x7a, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x41, 0x72, 0x74, 0x77, 0x6f, 0x72,
	0x6b, 0x53, 0x69, 0x7a, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x41, 0x75, 0x63, 0x74, 0x69, 0x6f, 0x6e,
	0x59, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x41, 0x75, 0x63, 0x74, 0x69, 0x6f, 0x6e,
	0x59, 0x12, 0x1a, 0x0a, 0x08, 0x41, 0x75, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x4d, 0x18, 0x06, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x08, 0x41, 0x75, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x4d, 0x12, 0x1a, 0x0a,
	0x08, 0x41, 0x75, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x08, 0x41, 0x75, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x12, 0x14, 0x0a, 0x05, 0x48, 0x64, 0x50,
	0x69, 0x63, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x48, 0x64, 0x50, 0x69, 0x63, 0x12,
	0x14, 0x0a, 0x05, 0x54, 0x66, 0x6e, 0x75, 0x6d, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05,
	0x54, 0x66, 0x6e, 0x75, 0x6d, 0x12, 0x2a, 0x0a, 0x10, 0x41, 0x75, 0x63, 0x74, 0x69, 0x6f, 0x6e,
	0x42, 0x61, 0x73, 0x65, 0x50, 0x72, 0x69, 0x63, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x10, 0x41, 0x75, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x42, 0x61, 0x73, 0x65, 0x50, 0x72, 0x69, 0x63,
	0x65, 0x12, 0x1e, 0x0a, 0x0a, 0x43, 0x6f, 0x6d, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x18,
	0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x43, 0x6f, 0x6d, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f,
	0x6e, 0x12, 0x1a, 0x0a, 0x08, 0x53, 0x69, 0x67, 0x6e, 0x44, 0x61, 0x74, 0x65, 0x18, 0x0c, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x08, 0x53, 0x69, 0x67, 0x6e, 0x44, 0x61, 0x74, 0x65, 0x12, 0x50, 0x0a,
	0x0b, 0x41, 0x75, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x61, 0x74, 0x61, 0x18, 0x0d, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x2e, 0x2e, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x43, 0x6f, 0x6e, 0x74, 0x72,
	0x61, 0x63, 0x74, 0x2e, 0x41, 0x72, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x42, 0x69, 0x64, 0x44, 0x61,
	0x74, 0x61, 0x52, 0x65, 0x73, 0x70, 0x2e, 0x41, 0x75, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x6e,
	0x66, 0x6f, 0x52, 0x0b, 0x41, 0x75, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x61, 0x74, 0x61, 0x12,
	0x2a, 0x0a, 0x10, 0x73, 0x6f, 0x6c, 0x64, 0x50, 0x72, 0x69, 0x63, 0x65, 0x43, 0x68, 0x69, 0x6e,
	0x65, 0x73, 0x65, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x73, 0x6f, 0x6c, 0x64, 0x50,
	0x72, 0x69, 0x63, 0x65, 0x43, 0x68, 0x69, 0x6e, 0x65, 0x73, 0x65, 0x1a, 0xe3, 0x03, 0x0a, 0x0b,
	0x41, 0x75, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x20, 0x0a, 0x0b, 0x41,
	0x75, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0b, 0x41, 0x75, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x26, 0x0a,
	0x0e, 0x41, 0x75, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x41, 0x75, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x41, 0x64,
	0x64, 0x72, 0x65, 0x73, 0x73, 0x12, 0x20, 0x0a, 0x0b, 0x41, 0x75, 0x63, 0x74, 0x69, 0x6f, 0x6e,
	0x4e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x41, 0x75, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1e, 0x0a, 0x0a, 0x41, 0x75, 0x63, 0x74, 0x69,
	0x6f, 0x6e, 0x4c, 0x4f, 0x54, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x41, 0x75, 0x63,
	0x74, 0x69, 0x6f, 0x6e, 0x4c, 0x4f, 0x54, 0x12, 0x24, 0x0a, 0x0d, 0x50, 0x72, 0x65, 0x76, 0x69,
	0x65, 0x77, 0x53, 0x74, 0x61, 0x72, 0x74, 0x59, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d,
	0x50, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x53, 0x74, 0x61, 0x72, 0x74, 0x59, 0x12, 0x24, 0x0a,
	0x0d, 0x50, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x53, 0x74, 0x61, 0x72, 0x74, 0x4d, 0x18, 0x06,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x50, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x53, 0x74, 0x61,
	0x72, 0x74, 0x4d, 0x12, 0x24, 0x0a, 0x0d, 0x50, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x53, 0x74,
	0x61, 0x72, 0x74, 0x44, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x50, 0x72, 0x65, 0x76,
	0x69, 0x65, 0x77, 0x53, 0x74, 0x61, 0x72, 0x74, 0x44, 0x12, 0x28, 0x0a, 0x0f, 0x50, 0x72, 0x65,
	0x76, 0x69, 0x65, 0x77, 0x53, 0x74, 0x61, 0x72, 0x74, 0x48, 0x69, 0x73, 0x18, 0x08, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0f, 0x50, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x53, 0x74, 0x61, 0x72, 0x74,
	0x48, 0x69, 0x73, 0x12, 0x20, 0x0a, 0x0b, 0x50, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x45, 0x6e,
	0x64, 0x59, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x50, 0x72, 0x65, 0x76, 0x69, 0x65,
	0x77, 0x45, 0x6e, 0x64, 0x59, 0x12, 0x20, 0x0a, 0x0b, 0x50, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77,
	0x45, 0x6e, 0x64, 0x4d, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x50, 0x72, 0x65, 0x76,
	0x69, 0x65, 0x77, 0x45, 0x6e, 0x64, 0x4d, 0x12, 0x20, 0x0a, 0x0b, 0x50, 0x72, 0x65, 0x76, 0x69,
	0x65, 0x77, 0x45, 0x6e, 0x64, 0x44, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x50, 0x72,
	0x65, 0x76, 0x69, 0x65, 0x77, 0x45, 0x6e, 0x64, 0x44, 0x12, 0x24, 0x0a, 0x0d, 0x50, 0x72, 0x65,
	0x76, 0x69, 0x65, 0x77, 0x45, 0x6e, 0x64, 0x48, 0x69, 0x73, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0d, 0x50, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x45, 0x6e, 0x64, 0x48, 0x69, 0x73, 0x12,
	0x20, 0x0a, 0x0b, 0x50, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x0e,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x50, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x54, 0x69, 0x6d,
	0x65, 0x1a, 0x3b, 0x0a, 0x07, 0x42, 0x69, 0x67, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x14, 0x0a, 0x05,
	0x62, 0x69, 0x64, 0x4e, 0x6f, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x62, 0x69, 0x64,
	0x4e, 0x6f, 0x12, 0x1a, 0x0a, 0x08, 0x62, 0x69, 0x64, 0x50, 0x72, 0x69, 0x63, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x62, 0x69, 0x64, 0x50, 0x72, 0x69, 0x63, 0x65, 0x22, 0x2b,
	0x0a, 0x0f, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x61, 0x63, 0x74, 0x44, 0x61, 0x74, 0x61, 0x52, 0x65,
	0x71, 0x12, 0x18, 0x0a, 0x07, 0x62, 0x61, 0x74, 0x63, 0x68, 0x4e, 0x6f, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x07, 0x62, 0x61, 0x74, 0x63, 0x68, 0x4e, 0x6f, 0x22, 0xc3, 0x01, 0x0a, 0x10,
	0x43, 0x6f, 0x6e, 0x74, 0x72, 0x61, 0x63, 0x74, 0x44, 0x61, 0x74, 0x61, 0x52, 0x65, 0x73, 0x70,
	0x12, 0x41, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2d,
	0x2e, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x61, 0x63, 0x74, 0x2e,
	0x43, 0x6f, 0x6e, 0x74, 0x72, 0x61, 0x63, 0x74, 0x44, 0x61, 0x74, 0x61, 0x52, 0x65, 0x73, 0x70,
	0x2e, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x61, 0x63, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x04, 0x64,
	0x61, 0x74, 0x61, 0x1a, 0x6c, 0x0a, 0x0c, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x61, 0x63, 0x74, 0x49,
	0x6e, 0x66, 0x6f, 0x12, 0x22, 0x0a, 0x0c, 0x63, 0x6f, 0x6e, 0x74, 0x72, 0x61, 0x63, 0x74, 0x55,
	0x75, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x63, 0x6f, 0x6e, 0x74, 0x72,
	0x61, 0x63, 0x74, 0x55, 0x75, 0x69, 0x64, 0x12, 0x18, 0x0a, 0x07, 0x76, 0x69, 0x65, 0x77, 0x55,
	0x72, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x76, 0x69, 0x65, 0x77, 0x55, 0x72,
	0x6c, 0x12, 0x1e, 0x0a, 0x0a, 0x63, 0x6f, 0x6e, 0x74, 0x72, 0x61, 0x63, 0x74, 0x49, 0x64, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x63, 0x6f, 0x6e, 0x74, 0x72, 0x61, 0x63, 0x74, 0x49,
	0x64, 0x22, 0x8f, 0x02, 0x0a, 0x0f, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x61, 0x63, 0x74, 0x56, 0x69,
	0x65, 0x77, 0x52, 0x65, 0x71, 0x12, 0x2e, 0x0a, 0x12, 0x61, 0x75, 0x63, 0x74, 0x69, 0x6f, 0x6e,
	0x41, 0x72, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x55, 0x75, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x12, 0x61, 0x75, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x41, 0x72, 0x74, 0x77, 0x6f, 0x72,
	0x6b, 0x55, 0x75, 0x69, 0x64, 0x12, 0x20, 0x0a, 0x0b, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79,
	0x43, 0x6f, 0x64, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x72, 0x79, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x70, 0x68, 0x6f, 0x6e, 0x65,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x12, 0x1c, 0x0a,
	0x09, 0x73, 0x69, 0x67, 0x6e, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x09, 0x73, 0x69, 0x67, 0x6e, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x12, 0x22, 0x0a, 0x0c, 0x72,
	0x65, 0x67, 0x69, 0x73, 0x74, 0x65, 0x72, 0x54, 0x79, 0x70, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x0c, 0x72, 0x65, 0x67, 0x69, 0x73, 0x74, 0x65, 0x72, 0x54, 0x79, 0x70, 0x65, 0x12,
	0x20, 0x0a, 0x0b, 0x61, 0x75, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x55, 0x75, 0x69, 0x64, 0x18, 0x06,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x61, 0x75, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x55, 0x75, 0x69,
	0x64, 0x12, 0x30, 0x0a, 0x13, 0x61, 0x75, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x41, 0x72, 0x74, 0x77,
	0x6f, 0x72, 0x6b, 0x55, 0x75, 0x69, 0x64, 0x73, 0x18, 0x07, 0x20, 0x03, 0x28, 0x09, 0x52, 0x13,
	0x61, 0x75, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x41, 0x72, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x55, 0x75,
	0x69, 0x64, 0x73, 0x22, 0xbc, 0x02, 0x0a, 0x10, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x61, 0x63, 0x74,
	0x56, 0x69, 0x65, 0x77, 0x52, 0x65, 0x73, 0x70, 0x12, 0x49, 0x0a, 0x08, 0x56, 0x69, 0x65, 0x77,
	0x55, 0x72, 0x6c, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2d, 0x2e, 0x43, 0x75, 0x73,
	0x74, 0x6f, 0x6d, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x61, 0x63, 0x74, 0x2e, 0x43, 0x6f, 0x6e, 0x74,
	0x72, 0x61, 0x63, 0x74, 0x56, 0x69, 0x65, 0x77, 0x52, 0x65, 0x73, 0x70, 0x2e, 0x56, 0x69, 0x65,
	0x77, 0x55, 0x72, 0x6c, 0x73, 0x44, 0x61, 0x74, 0x61, 0x52, 0x08, 0x56, 0x69, 0x65, 0x77, 0x55,
	0x72, 0x6c, 0x73, 0x12, 0x18, 0x0a, 0x07, 0x76, 0x69, 0x65, 0x77, 0x55, 0x72, 0x6c, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x76, 0x69, 0x65, 0x77, 0x55, 0x72, 0x6c, 0x1a, 0xc2, 0x01,
	0x0a, 0x0c, 0x56, 0x69, 0x65, 0x77, 0x55, 0x72, 0x6c, 0x73, 0x44, 0x61, 0x74, 0x61, 0x12, 0x14,
	0x0a, 0x05, 0x6a, 0x6d, 0x78, 0x79, 0x31, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x6a,
	0x6d, 0x78, 0x79, 0x31, 0x12, 0x14, 0x0a, 0x05, 0x6a, 0x6d, 0x78, 0x7a, 0x32, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x05, 0x6a, 0x6d, 0x78, 0x7a, 0x32, 0x12, 0x14, 0x0a, 0x05, 0x70, 0x6d,
	0x67, 0x67, 0x33, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x70, 0x6d, 0x67, 0x67, 0x33,
	0x12, 0x14, 0x0a, 0x05, 0x70, 0x6d, 0x67, 0x7a, 0x34, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x05, 0x70, 0x6d, 0x67, 0x7a, 0x34, 0x12, 0x14, 0x0a, 0x05, 0x70, 0x70, 0x71, 0x72, 0x35, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x70, 0x70, 0x71, 0x72, 0x35, 0x12, 0x14, 0x0a, 0x05,
	0x70, 0x70, 0x62, 0x6c, 0x36, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x70, 0x70, 0x62,
	0x6c, 0x36, 0x12, 0x16, 0x0a, 0x06, 0x70, 0x70, 0x71, 0x72, 0x35, 0x73, 0x18, 0x07, 0x20, 0x03,
	0x28, 0x09, 0x52, 0x06, 0x70, 0x70, 0x71, 0x72, 0x35, 0x73, 0x12, 0x16, 0x0a, 0x06, 0x70, 0x70,
	0x62, 0x6c, 0x36, 0x73, 0x18, 0x08, 0x20, 0x03, 0x28, 0x09, 0x52, 0x06, 0x70, 0x70, 0x62, 0x6c,
	0x36, 0x73, 0x22, 0x4e, 0x0a, 0x12, 0x55, 0x73, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x42, 0x79,
	0x50, 0x68, 0x6f, 0x6e, 0x65, 0x52, 0x65, 0x71, 0x12, 0x14, 0x0a, 0x05, 0x70, 0x68, 0x6f, 0x6e,
	0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x12, 0x22,
	0x0a, 0x0c, 0x72, 0x65, 0x67, 0x69, 0x73, 0x74, 0x65, 0x72, 0x54, 0x79, 0x70, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x0c, 0x72, 0x65, 0x67, 0x69, 0x73, 0x74, 0x65, 0x72, 0x54, 0x79,
	0x70, 0x65, 0x22, 0x93, 0x02, 0x0a, 0x13, 0x55, 0x73, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x42,
	0x79, 0x50, 0x68, 0x6f, 0x6e, 0x65, 0x52, 0x65, 0x73, 0x70, 0x12, 0x16, 0x0a, 0x06, 0x75, 0x73,
	0x65, 0x72, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72,
	0x49, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x05, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x12, 0x24, 0x0a, 0x0d, 0x66, 0x64, 0x64, 0x43,
	0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x49, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0d, 0x66, 0x64, 0x64, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x49, 0x64, 0x12, 0x20,
	0x0a, 0x0b, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79, 0x43, 0x6f, 0x64, 0x65, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0b, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79, 0x43, 0x6f, 0x64, 0x65,
	0x12, 0x1e, 0x0a, 0x0a, 0x69, 0x73, 0x4d, 0x61, 0x69, 0x6e, 0x6c, 0x61, 0x6e, 0x64, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x0d, 0x52, 0x0a, 0x69, 0x73, 0x4d, 0x61, 0x69, 0x6e, 0x6c, 0x61, 0x6e, 0x64,
	0x12, 0x2c, 0x0a, 0x11, 0x66, 0x64, 0x64, 0x52, 0x65, 0x67, 0x69, 0x73, 0x74, 0x65, 0x72, 0x53,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x06, 0x20, 0x01, 0x28, 0x05, 0x52, 0x11, 0x66, 0x64, 0x64,
	0x52, 0x65, 0x67, 0x69, 0x73, 0x74, 0x65, 0x72, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x16,
	0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x07, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06,
	0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x20, 0x0a, 0x0b, 0x73, 0x70, 0x65, 0x63, 0x69, 0x61,
	0x6c, 0x54, 0x79, 0x70, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0b, 0x73, 0x70, 0x65,
	0x63, 0x69, 0x61, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x22, 0x6b, 0x0a, 0x19, 0x47, 0x65, 0x74, 0x41,
	0x75, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x41, 0x72, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x55, 0x75, 0x69,
	0x64, 0x73, 0x52, 0x65, 0x71, 0x12, 0x14, 0x0a, 0x05, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x75,
	0x73, 0x65, 0x72, 0x49, 0x44, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x75, 0x73, 0x65,
	0x72, 0x49, 0x44, 0x12, 0x20, 0x0a, 0x0b, 0x61, 0x75, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x55, 0x75,
	0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x61, 0x75, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x55, 0x75, 0x69, 0x64, 0x22, 0x4e, 0x0a, 0x1a, 0x47, 0x65, 0x74, 0x41, 0x75, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x41, 0x72, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x55, 0x75, 0x69, 0x64, 0x73, 0x52,
	0x65, 0x73, 0x70, 0x12, 0x30, 0x0a, 0x13, 0x61, 0x75, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x41, 0x72,
	0x74, 0x77, 0x6f, 0x72, 0x6b, 0x55, 0x75, 0x69, 0x64, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x09,
	0x52, 0x13, 0x61, 0x75, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x41, 0x72, 0x74, 0x77, 0x6f, 0x72, 0x6b,
	0x55, 0x75, 0x69, 0x64, 0x73, 0x22, 0x3f, 0x0a, 0x0b, 0x53, 0x69, 0x67, 0x6e, 0x56, 0x69, 0x65,
	0x77, 0x52, 0x65, 0x71, 0x12, 0x30, 0x0a, 0x13, 0x61, 0x75, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x41,
	0x72, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x55, 0x75, 0x69, 0x64, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28,
	0x09, 0x52, 0x13, 0x61, 0x75, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x41, 0x72, 0x74, 0x77, 0x6f, 0x72,
	0x6b, 0x55, 0x75, 0x69, 0x64, 0x73, 0x22, 0x7c, 0x0a, 0x0c, 0x53, 0x69, 0x67, 0x6e, 0x56, 0x69,
	0x65, 0x77, 0x52, 0x65, 0x73, 0x70, 0x12, 0x14, 0x0a, 0x05, 0x6a, 0x6d, 0x78, 0x79, 0x31, 0x18,
	0x02, 0x20, 0x03, 0x28, 0x09, 0x52, 0x05, 0x6a, 0x6d, 0x78, 0x79, 0x31, 0x12, 0x14, 0x0a, 0x05,
	0x6a, 0x6d, 0x78, 0x7a, 0x32, 0x18, 0x03, 0x20, 0x03, 0x28, 0x09, 0x52, 0x05, 0x6a, 0x6d, 0x78,
	0x7a, 0x32, 0x12, 0x14, 0x0a, 0x05, 0x70, 0x6d, 0x67, 0x67, 0x33, 0x18, 0x05, 0x20, 0x03, 0x28,
	0x09, 0x52, 0x05, 0x70, 0x6d, 0x67, 0x67, 0x33, 0x12, 0x14, 0x0a, 0x05, 0x70, 0x70, 0x71, 0x72,
	0x35, 0x18, 0x01, 0x20, 0x03, 0x28, 0x09, 0x52, 0x05, 0x70, 0x70, 0x71, 0x72, 0x35, 0x12, 0x14,
	0x0a, 0x05, 0x70, 0x70, 0x62, 0x6c, 0x36, 0x18, 0x04, 0x20, 0x03, 0x28, 0x09, 0x52, 0x05, 0x70,
	0x70, 0x62, 0x6c, 0x36, 0x22, 0x3b, 0x0a, 0x17, 0x52, 0x65, 0x70, 0x61, 0x69, 0x72, 0x46, 0x64,
	0x64, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x61, 0x63, 0x74, 0x55, 0x72, 0x6c, 0x52, 0x65, 0x71, 0x12,
	0x20, 0x0a, 0x0b, 0x63, 0x6f, 0x6e, 0x74, 0x72, 0x61, 0x63, 0x74, 0x49, 0x44, 0x73, 0x18, 0x01,
	0x20, 0x03, 0x28, 0x09, 0x52, 0x0b, 0x63, 0x6f, 0x6e, 0x74, 0x72, 0x61, 0x63, 0x74, 0x49, 0x44,
	0x73, 0x22, 0xbb, 0x01, 0x0a, 0x18, 0x52, 0x65, 0x70, 0x61, 0x69, 0x72, 0x46, 0x64, 0x64, 0x43,
	0x6f, 0x6e, 0x74, 0x72, 0x61, 0x63, 0x74, 0x55, 0x72, 0x6c, 0x52, 0x65, 0x73, 0x70, 0x12, 0x41,
	0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2d, 0x2e, 0x43,
	0x75, 0x73, 0x74, 0x6f, 0x6d, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x61, 0x63, 0x74, 0x2e, 0x52, 0x65,
	0x70, 0x61, 0x69, 0x72, 0x46, 0x64, 0x64, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x61, 0x63, 0x74, 0x55,
	0x72, 0x6c, 0x52, 0x65, 0x73, 0x70, 0x2e, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x04, 0x64, 0x61, 0x74,
	0x61, 0x1a, 0x5c, 0x0a, 0x04, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x1e, 0x0a, 0x0a, 0x63, 0x6f, 0x6e,
	0x74, 0x72, 0x61, 0x63, 0x74, 0x49, 0x44, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x63,
	0x6f, 0x6e, 0x74, 0x72, 0x61, 0x63, 0x74, 0x49, 0x44, 0x12, 0x18, 0x0a, 0x07, 0x76, 0x69, 0x65,
	0x77, 0x55, 0x72, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x76, 0x69, 0x65, 0x77,
	0x55, 0x72, 0x6c, 0x12, 0x1a, 0x0a, 0x08, 0x73, 0x69, 0x67, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x08, 0x73, 0x69, 0x67, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x22,
	0x6d, 0x0a, 0x1b, 0x55, 0x73, 0x65, 0x72, 0x41, 0x75, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x6f,
	0x6e, 0x74, 0x72, 0x61, 0x63, 0x74, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x12, 0x16,
	0x0a, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x44, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06,
	0x75, 0x73, 0x65, 0x72, 0x49, 0x44, 0x12, 0x14, 0x0a, 0x05, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x12, 0x20, 0x0a, 0x0b,
	0x61, 0x75, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x55, 0x75, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0b, 0x61, 0x75, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x55, 0x75, 0x69, 0x64, 0x22, 0x34,
	0x0a, 0x1c, 0x55, 0x73, 0x65, 0x72, 0x41, 0x75, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x6f, 0x6e,
	0x74, 0x72, 0x61, 0x63, 0x74, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x70, 0x12, 0x14,
	0x0a, 0x05, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x22, 0x8c, 0x01, 0x0a, 0x1a, 0x53, 0x65, 0x72, 0x69, 0x65, 0x73, 0x4f,
	0x72, 0x64, 0x65, 0x72, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x61, 0x63, 0x74, 0x56, 0x69, 0x65, 0x77,
	0x52, 0x65, 0x71, 0x12, 0x20, 0x0a, 0x0b, 0x61, 0x72, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x55, 0x75,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x61, 0x72, 0x74, 0x77, 0x6f, 0x72,
	0x6b, 0x55, 0x75, 0x69, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x44, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x44, 0x12, 0x14, 0x0a,
	0x05, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x70, 0x68,
	0x6f, 0x6e, 0x65, 0x12, 0x1e, 0x0a, 0x0a, 0x73, 0x65, 0x72, 0x69, 0x65, 0x73, 0x55, 0x75, 0x69,
	0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x73, 0x65, 0x72, 0x69, 0x65, 0x73, 0x55,
	0x75, 0x69, 0x64, 0x22, 0xc1, 0x01, 0x0a, 0x1b, 0x53, 0x65, 0x72, 0x69, 0x65, 0x73, 0x4f, 0x72,
	0x64, 0x65, 0x72, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x61, 0x63, 0x74, 0x56, 0x69, 0x65, 0x77, 0x52,
	0x65, 0x73, 0x70, 0x12, 0x61, 0x0a, 0x0c, 0x63, 0x6f, 0x6e, 0x74, 0x72, 0x61, 0x63, 0x74, 0x55,
	0x72, 0x6c, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x3d, 0x2e, 0x43, 0x75, 0x73, 0x74,
	0x6f, 0x6d, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x61, 0x63, 0x74, 0x2e, 0x53, 0x65, 0x72, 0x69, 0x65,
	0x73, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x61, 0x63, 0x74, 0x56, 0x69,
	0x65, 0x77, 0x52, 0x65, 0x73, 0x70, 0x2e, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x61, 0x63, 0x74, 0x55,
	0x72, 0x6c, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x0c, 0x63, 0x6f, 0x6e, 0x74, 0x72, 0x61,
	0x63, 0x74, 0x55, 0x72, 0x6c, 0x73, 0x1a, 0x3f, 0x0a, 0x11, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x61,
	0x63, 0x74, 0x55, 0x72, 0x6c, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b,
	0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a,
	0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61,
	0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0xb6, 0x01, 0x0a, 0x1a, 0x53, 0x65, 0x72, 0x69,
	0x65, 0x73, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x61, 0x63, 0x74, 0x53,
	0x69, 0x67, 0x6e, 0x52, 0x65, 0x71, 0x12, 0x20, 0x0a, 0x0b, 0x61, 0x72, 0x74, 0x77, 0x6f, 0x72,
	0x6b, 0x55, 0x75, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x61, 0x72, 0x74,
	0x77, 0x6f, 0x72, 0x6b, 0x55, 0x75, 0x69, 0x64, 0x12, 0x28, 0x0a, 0x0f, 0x73, 0x69, 0x67, 0x6e,
	0x49, 0x6d, 0x67, 0x46, 0x69, 0x6c, 0x65, 0x44, 0x61, 0x74, 0x61, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0f, 0x73, 0x69, 0x67, 0x6e, 0x49, 0x6d, 0x67, 0x46, 0x69, 0x6c, 0x65, 0x44, 0x61,
	0x74, 0x61, 0x12, 0x16, 0x0a, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x44, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x44, 0x12, 0x14, 0x0a, 0x05, 0x70, 0x68,
	0x6f, 0x6e, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x70, 0x68, 0x6f, 0x6e, 0x65,
	0x12, 0x1e, 0x0a, 0x0a, 0x73, 0x65, 0x72, 0x69, 0x65, 0x73, 0x55, 0x75, 0x69, 0x64, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x73, 0x65, 0x72, 0x69, 0x65, 0x73, 0x55, 0x75, 0x69, 0x64,
	0x22, 0xc1, 0x01, 0x0a, 0x1b, 0x53, 0x65, 0x72, 0x69, 0x65, 0x73, 0x4f, 0x72, 0x64, 0x65, 0x72,
	0x43, 0x6f, 0x6e, 0x74, 0x72, 0x61, 0x63, 0x74, 0x53, 0x69, 0x67, 0x6e, 0x52, 0x65, 0x73, 0x70,
	0x12, 0x61, 0x0a, 0x0c, 0x63, 0x6f, 0x6e, 0x74, 0x72, 0x61, 0x63, 0x74, 0x55, 0x72, 0x6c, 0x73,
	0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x3d, 0x2e, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x43,
	0x6f, 0x6e, 0x74, 0x72, 0x61, 0x63, 0x74, 0x2e, 0x53, 0x65, 0x72, 0x69, 0x65, 0x73, 0x4f, 0x72,
	0x64, 0x65, 0x72, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x61, 0x63, 0x74, 0x53, 0x69, 0x67, 0x6e, 0x52,
	0x65, 0x73, 0x70, 0x2e, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x61, 0x63, 0x74, 0x55, 0x72, 0x6c, 0x73,
	0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x0c, 0x63, 0x6f, 0x6e, 0x74, 0x72, 0x61, 0x63, 0x74, 0x55,
	0x72, 0x6c, 0x73, 0x1a, 0x3f, 0x0a, 0x11, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x61, 0x63, 0x74, 0x55,
	0x72, 0x6c, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61,
	0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65,
	0x3a, 0x02, 0x38, 0x01, 0x22, 0xc5, 0x02, 0x0a, 0x0f, 0x53, 0x69, 0x67, 0x6e, 0x4f, 0x6e, 0x6c,
	0x69, 0x6e, 0x65, 0x56, 0x32, 0x52, 0x65, 0x71, 0x12, 0x34, 0x0a, 0x08, 0x75, 0x73, 0x65, 0x72,
	0x49, 0x6e, 0x66, 0x6f, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x43, 0x75, 0x73,
	0x74, 0x6f, 0x6d, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x61, 0x63, 0x74, 0x2e, 0x55, 0x73, 0x65, 0x72,
	0x49, 0x6e, 0x66, 0x6f, 0x52, 0x08, 0x75, 0x73, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x28,
	0x0a, 0x0f, 0x73, 0x69, 0x67, 0x6e, 0x49, 0x6d, 0x67, 0x46, 0x69, 0x6c, 0x65, 0x44, 0x61, 0x74,
	0x61, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x73, 0x69, 0x67, 0x6e, 0x49, 0x6d, 0x67,
	0x46, 0x69, 0x6c, 0x65, 0x44, 0x61, 0x74, 0x61, 0x12, 0x20, 0x0a, 0x0b, 0x61, 0x75, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x55, 0x75, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x61,
	0x75, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x55, 0x75, 0x69, 0x64, 0x12, 0x30, 0x0a, 0x13, 0x61, 0x75,
	0x63, 0x74, 0x69, 0x6f, 0x6e, 0x41, 0x72, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x55, 0x75, 0x69, 0x64,
	0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x09, 0x52, 0x13, 0x61, 0x75, 0x63, 0x74, 0x69, 0x6f, 0x6e,
	0x41, 0x72, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x55, 0x75, 0x69, 0x64, 0x73, 0x12, 0x1e, 0x0a, 0x0a,
	0x73, 0x65, 0x72, 0x69, 0x65, 0x73, 0x55, 0x75, 0x69, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0a, 0x73, 0x65, 0x72, 0x69, 0x65, 0x73, 0x55, 0x75, 0x69, 0x64, 0x12, 0x20, 0x0a, 0x0b,
	0x63, 0x61, 0x6c, 0x6c, 0x62, 0x61, 0x63, 0x6b, 0x55, 0x72, 0x6c, 0x18, 0x06, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0b, 0x63, 0x61, 0x6c, 0x6c, 0x62, 0x61, 0x63, 0x6b, 0x55, 0x72, 0x6c, 0x12, 0x1c,
	0x0a, 0x09, 0x73, 0x69, 0x67, 0x6e, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x18, 0x07, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x09, 0x73, 0x69, 0x67, 0x6e, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x12, 0x1e, 0x0a, 0x0a,
	0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x54, 0x79, 0x70, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0a, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x54, 0x79, 0x70, 0x65, 0x22, 0x42, 0x0a, 0x10,
	0x53, 0x69, 0x67, 0x6e, 0x4f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x56, 0x32, 0x52, 0x65, 0x73, 0x70,
	0x12, 0x12, 0x0a, 0x04, 0x75, 0x72, 0x6c, 0x31, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04,
	0x75, 0x72, 0x6c, 0x31, 0x12, 0x1a, 0x0a, 0x08, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x4e, 0x6f, 0x73,
	0x18, 0x02, 0x20, 0x03, 0x28, 0x09, 0x52, 0x08, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x4e, 0x6f, 0x73,
	0x22, 0x8d, 0x01, 0x0a, 0x0d, 0x55, 0x73, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x56, 0x32, 0x52,
	0x65, 0x71, 0x12, 0x16, 0x0a, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x44, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x44, 0x12, 0x14, 0x0a, 0x05, 0x70, 0x68,
	0x6f, 0x6e, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x70, 0x68, 0x6f, 0x6e, 0x65,
	0x12, 0x2a, 0x0a, 0x10, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79,
	0x43, 0x6f, 0x64, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x70, 0x68, 0x6f, 0x6e,
	0x65, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x22, 0x0a, 0x0c,
	0x72, 0x65, 0x67, 0x69, 0x73, 0x74, 0x65, 0x72, 0x54, 0x79, 0x70, 0x65, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x0c, 0x72, 0x65, 0x67, 0x69, 0x73, 0x74, 0x65, 0x72, 0x54, 0x79, 0x70, 0x65,
	0x22, 0x46, 0x0a, 0x0e, 0x55, 0x73, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x56, 0x32, 0x52, 0x65,
	0x73, 0x70, 0x12, 0x34, 0x0a, 0x08, 0x75, 0x73, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x43, 0x6f, 0x6e,
	0x74, 0x72, 0x61, 0x63, 0x74, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x08,
	0x75, 0x73, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x22, 0x4b, 0x0a, 0x13, 0x55, 0x70, 0x64, 0x61,
	0x74, 0x65, 0x55, 0x73, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x56, 0x32, 0x52, 0x65, 0x71, 0x12,
	0x34, 0x0a, 0x08, 0x75, 0x73, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x18, 0x2e, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x61,
	0x63, 0x74, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x08, 0x75, 0x73, 0x65,
	0x72, 0x49, 0x6e, 0x66, 0x6f, 0x22, 0x4c, 0x0a, 0x14, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x55,
	0x73, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x56, 0x32, 0x52, 0x65, 0x73, 0x70, 0x12, 0x34, 0x0a,
	0x08, 0x75, 0x73, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x18, 0x2e, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x61, 0x63, 0x74,
	0x2e, 0x55, 0x73, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x08, 0x75, 0x73, 0x65, 0x72, 0x49,
	0x6e, 0x66, 0x6f, 0x22, 0xe3, 0x03, 0x0a, 0x0b, 0x41, 0x75, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x49,
	0x6e, 0x66, 0x6f, 0x12, 0x20, 0x0a, 0x0b, 0x41, 0x75, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x69,
	0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x41, 0x75, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x26, 0x0a, 0x0e, 0x41, 0x75, 0x63, 0x74, 0x69, 0x6f, 0x6e,
	0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x41,
	0x75, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x12, 0x20, 0x0a,
	0x0b, 0x41, 0x75, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0b, 0x41, 0x75, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x4e, 0x61, 0x6d, 0x65, 0x12,
	0x1e, 0x0a, 0x0a, 0x41, 0x75, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x4c, 0x4f, 0x54, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0a, 0x41, 0x75, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x4c, 0x4f, 0x54, 0x12,
	0x24, 0x0a, 0x0d, 0x50, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x53, 0x74, 0x61, 0x72, 0x74, 0x59,
	0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x50, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x53,
	0x74, 0x61, 0x72, 0x74, 0x59, 0x12, 0x24, 0x0a, 0x0d, 0x50, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77,
	0x53, 0x74, 0x61, 0x72, 0x74, 0x4d, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x50, 0x72,
	0x65, 0x76, 0x69, 0x65, 0x77, 0x53, 0x74, 0x61, 0x72, 0x74, 0x4d, 0x12, 0x24, 0x0a, 0x0d, 0x50,
	0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x53, 0x74, 0x61, 0x72, 0x74, 0x44, 0x18, 0x07, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0d, 0x50, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x53, 0x74, 0x61, 0x72, 0x74,
	0x44, 0x12, 0x28, 0x0a, 0x0f, 0x50, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x53, 0x74, 0x61, 0x72,
	0x74, 0x48, 0x69, 0x73, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x50, 0x72, 0x65, 0x76,
	0x69, 0x65, 0x77, 0x53, 0x74, 0x61, 0x72, 0x74, 0x48, 0x69, 0x73, 0x12, 0x20, 0x0a, 0x0b, 0x50,
	0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x45, 0x6e, 0x64, 0x59, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0b, 0x50, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x45, 0x6e, 0x64, 0x59, 0x12, 0x20, 0x0a,
	0x0b, 0x50, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x45, 0x6e, 0x64, 0x4d, 0x18, 0x0a, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0b, 0x50, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x45, 0x6e, 0x64, 0x4d, 0x12,
	0x20, 0x0a, 0x0b, 0x50, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x45, 0x6e, 0x64, 0x44, 0x18, 0x0c,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x50, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x45, 0x6e, 0x64,
	0x44, 0x12, 0x24, 0x0a, 0x0d, 0x50, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x45, 0x6e, 0x64, 0x48,
	0x69, 0x73, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x50, 0x72, 0x65, 0x76, 0x69, 0x65,
	0x77, 0x45, 0x6e, 0x64, 0x48, 0x69, 0x73, 0x12, 0x20, 0x0a, 0x0b, 0x50, 0x72, 0x65, 0x76, 0x69,
	0x65, 0x77, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x50, 0x72,
	0x65, 0x76, 0x69, 0x65, 0x77, 0x54, 0x69, 0x6d, 0x65, 0x22, 0x54, 0x0a, 0x10, 0x41, 0x75, 0x63,
	0x74, 0x69, 0x6f, 0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x56, 0x32, 0x52, 0x65, 0x71, 0x12, 0x20, 0x0a,
	0x0b, 0x61, 0x75, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x55, 0x75, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0b, 0x61, 0x75, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x55, 0x75, 0x69, 0x64, 0x12,
	0x1e, 0x0a, 0x0a, 0x73, 0x65, 0x72, 0x69, 0x65, 0x73, 0x55, 0x75, 0x69, 0x64, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0a, 0x73, 0x65, 0x72, 0x69, 0x65, 0x73, 0x55, 0x75, 0x69, 0x64, 0x22,
	0x52, 0x0a, 0x11, 0x41, 0x75, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x56, 0x32,
	0x52, 0x65, 0x73, 0x70, 0x12, 0x3d, 0x0a, 0x0b, 0x61, 0x75, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x49,
	0x6e, 0x66, 0x6f, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x43, 0x75, 0x73, 0x74,
	0x6f, 0x6d, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x61, 0x63, 0x74, 0x2e, 0x41, 0x75, 0x63, 0x74, 0x69,
	0x6f, 0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x0b, 0x61, 0x75, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x49,
	0x6e, 0x66, 0x6f, 0x22, 0xbf, 0x01, 0x0a, 0x11, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x61, 0x63, 0x74,
	0x56, 0x69, 0x65, 0x77, 0x56, 0x32, 0x52, 0x65, 0x71, 0x12, 0x20, 0x0a, 0x0b, 0x61, 0x75, 0x63,
	0x74, 0x69, 0x6f, 0x6e, 0x55, 0x75, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b,
	0x61, 0x75, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x55, 0x75, 0x69, 0x64, 0x12, 0x30, 0x0a, 0x13, 0x61,
	0x75, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x41, 0x72, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x55, 0x75, 0x69,
	0x64, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x09, 0x52, 0x13, 0x61, 0x75, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x41, 0x72, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x55, 0x75, 0x69, 0x64, 0x73, 0x12, 0x16, 0x0a,
	0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x44, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x75,
	0x73, 0x65, 0x72, 0x49, 0x44, 0x12, 0x1e, 0x0a, 0x0a, 0x73, 0x65, 0x72, 0x69, 0x65, 0x73, 0x55,
	0x75, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x73, 0x65, 0x72, 0x69, 0x65,
	0x73, 0x55, 0x75, 0x69, 0x64, 0x12, 0x1e, 0x0a, 0x0a, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x54,
	0x79, 0x70, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x73, 0x6f, 0x75, 0x72, 0x63,
	0x65, 0x54, 0x79, 0x70, 0x65, 0x22, 0xa6, 0x02, 0x0a, 0x12, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x61,
	0x63, 0x74, 0x56, 0x69, 0x65, 0x77, 0x56, 0x32, 0x52, 0x65, 0x73, 0x70, 0x12, 0x4b, 0x0a, 0x08,
	0x56, 0x69, 0x65, 0x77, 0x55, 0x72, 0x6c, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2f,
	0x2e, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x61, 0x63, 0x74, 0x2e,
	0x43, 0x6f, 0x6e, 0x74, 0x72, 0x61, 0x63, 0x74, 0x56, 0x69, 0x65, 0x77, 0x56, 0x32, 0x52, 0x65,
	0x73, 0x70, 0x2e, 0x56, 0x69, 0x65, 0x77, 0x55, 0x72, 0x6c, 0x73, 0x44, 0x61, 0x74, 0x61, 0x52,
	0x08, 0x56, 0x69, 0x65, 0x77, 0x55, 0x72, 0x6c, 0x73, 0x1a, 0xc2, 0x01, 0x0a, 0x0c, 0x56, 0x69,
	0x65, 0x77, 0x55, 0x72, 0x6c, 0x73, 0x44, 0x61, 0x74, 0x61, 0x12, 0x14, 0x0a, 0x05, 0x6a, 0x6d,
	0x78, 0x79, 0x31, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x6a, 0x6d, 0x78, 0x79, 0x31,
	0x12, 0x14, 0x0a, 0x05, 0x6a, 0x6d, 0x78, 0x7a, 0x32, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x05, 0x6a, 0x6d, 0x78, 0x7a, 0x32, 0x12, 0x14, 0x0a, 0x05, 0x70, 0x6d, 0x67, 0x67, 0x33, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x70, 0x6d, 0x67, 0x67, 0x33, 0x12, 0x14, 0x0a, 0x05,
	0x70, 0x6d, 0x67, 0x7a, 0x34, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x70, 0x6d, 0x67,
	0x7a, 0x34, 0x12, 0x14, 0x0a, 0x05, 0x70, 0x70, 0x71, 0x72, 0x35, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x05, 0x70, 0x70, 0x71, 0x72, 0x35, 0x12, 0x14, 0x0a, 0x05, 0x70, 0x70, 0x62, 0x6c,
	0x36, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x70, 0x70, 0x62, 0x6c, 0x36, 0x12, 0x16,
	0x0a, 0x06, 0x70, 0x70, 0x71, 0x72, 0x35, 0x73, 0x18, 0x07, 0x20, 0x03, 0x28, 0x09, 0x52, 0x06,
	0x70, 0x70, 0x71, 0x72, 0x35, 0x73, 0x12, 0x16, 0x0a, 0x06, 0x70, 0x70, 0x62, 0x6c, 0x36, 0x73,
	0x18, 0x08, 0x20, 0x03, 0x28, 0x09, 0x52, 0x06, 0x70, 0x70, 0x62, 0x6c, 0x36, 0x73, 0x22, 0x8f,
	0x01, 0x0a, 0x17, 0x55, 0x73, 0x65, 0x72, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x61, 0x63, 0x74, 0x4e,
	0x65, 0x65, 0x64, 0x53, 0x69, 0x67, 0x6e, 0x52, 0x65, 0x71, 0x12, 0x34, 0x0a, 0x08, 0x75, 0x73,
	0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x43,
	0x75, 0x73, 0x74, 0x6f, 0x6d, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x61, 0x63, 0x74, 0x2e, 0x55, 0x73,
	0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x08, 0x75, 0x73, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f,
	0x12, 0x1e, 0x0a, 0x0a, 0x73, 0x65, 0x72, 0x69, 0x65, 0x73, 0x55, 0x75, 0x69, 0x64, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x73, 0x65, 0x72, 0x69, 0x65, 0x73, 0x55, 0x75, 0x69, 0x64,
	0x12, 0x1e, 0x0a, 0x0a, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x54, 0x79, 0x70, 0x65, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x54, 0x79, 0x70, 0x65,
	0x22, 0x84, 0x01, 0x0a, 0x18, 0x55, 0x73, 0x65, 0x72, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x61, 0x63,
	0x74, 0x4e, 0x65, 0x65, 0x64, 0x53, 0x69, 0x67, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x12, 0x30, 0x0a,
	0x13, 0x61, 0x75, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x41, 0x72, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x55,
	0x75, 0x69, 0x64, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x09, 0x52, 0x13, 0x61, 0x75, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x41, 0x72, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x55, 0x75, 0x69, 0x64, 0x73, 0x12,
	0x36, 0x0a, 0x16, 0x62, 0x75, 0x67, 0x41, 0x75, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x41, 0x72, 0x74,
	0x77, 0x6f, 0x72, 0x6b, 0x55, 0x75, 0x69, 0x64, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x09, 0x52,
	0x16, 0x62, 0x75, 0x67, 0x41, 0x75, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x41, 0x72, 0x74, 0x77, 0x6f,
	0x72, 0x6b, 0x55, 0x75, 0x69, 0x64, 0x73, 0x22, 0x9c, 0x02, 0x0a, 0x16, 0x41, 0x72, 0x74, 0x77,
	0x6f, 0x72, 0x6b, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x61, 0x63, 0x74, 0x56, 0x69, 0x65, 0x77, 0x52,
	0x65, 0x71, 0x12, 0x16, 0x0a, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x44, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x44, 0x12, 0x30, 0x0a, 0x13, 0x61, 0x75,
	0x63, 0x74, 0x69, 0x6f, 0x6e, 0x41, 0x72, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x55, 0x75, 0x69, 0x64,
	0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x09, 0x52, 0x13, 0x61, 0x75, 0x63, 0x74, 0x69, 0x6f, 0x6e,
	0x41, 0x72, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x55, 0x75, 0x69, 0x64, 0x73, 0x12, 0x22, 0x0a, 0x0c,
	0x61, 0x72, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x55, 0x75, 0x69, 0x64, 0x73, 0x18, 0x03, 0x20, 0x03,
	0x28, 0x09, 0x52, 0x0c, 0x61, 0x72, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x55, 0x75, 0x69, 0x64, 0x73,
	0x12, 0x1e, 0x0a, 0x0a, 0x73, 0x65, 0x72, 0x69, 0x65, 0x73, 0x55, 0x75, 0x69, 0x64, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x73, 0x65, 0x72, 0x69, 0x65, 0x73, 0x55, 0x75, 0x69, 0x64,
	0x12, 0x1e, 0x0a, 0x0a, 0x73, 0x65, 0x72, 0x69, 0x65, 0x73, 0x54, 0x79, 0x70, 0x65, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x0a, 0x73, 0x65, 0x72, 0x69, 0x65, 0x73, 0x54, 0x79, 0x70, 0x65,
	0x12, 0x20, 0x0a, 0x0b, 0x61, 0x75, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x55, 0x75, 0x69, 0x64, 0x18,
	0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x61, 0x75, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x55, 0x75,
	0x69, 0x64, 0x12, 0x18, 0x0a, 0x07, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x49, 0x44, 0x18, 0x07, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x07, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x49, 0x44, 0x12, 0x18, 0x0a, 0x07,
	0x6f, 0x72, 0x64, 0x65, 0x72, 0x4e, 0x6f, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6f,
	0x72, 0x64, 0x65, 0x72, 0x4e, 0x6f, 0x22, 0xac, 0x01, 0x0a, 0x10, 0x43, 0x6f, 0x6e, 0x74, 0x72,
	0x61, 0x63, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x14, 0x0a, 0x05, 0x6a,
	0x6d, 0x78, 0x79, 0x31, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x6a, 0x6d, 0x78, 0x79,
	0x31, 0x12, 0x14, 0x0a, 0x05, 0x6a, 0x6d, 0x78, 0x7a, 0x32, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x05, 0x6a, 0x6d, 0x78, 0x7a, 0x32, 0x12, 0x14, 0x0a, 0x05, 0x70, 0x6d, 0x67, 0x67, 0x33,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x70, 0x6d, 0x67, 0x67, 0x33, 0x12, 0x14, 0x0a,
	0x05, 0x70, 0x6d, 0x67, 0x7a, 0x34, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x70, 0x6d,
	0x67, 0x7a, 0x34, 0x12, 0x14, 0x0a, 0x05, 0x70, 0x70, 0x71, 0x72, 0x35, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x05, 0x70, 0x70, 0x71, 0x72, 0x35, 0x12, 0x14, 0x0a, 0x05, 0x70, 0x70, 0x62,
	0x6c, 0x36, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x70, 0x70, 0x62, 0x6c, 0x36, 0x12,
	0x14, 0x0a, 0x05, 0x78, 0x73, 0x68, 0x74, 0x31, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05,
	0x78, 0x73, 0x68, 0x74, 0x31, 0x22, 0xdb, 0x01, 0x0a, 0x17, 0x41, 0x72, 0x74, 0x77, 0x6f, 0x72,
	0x6b, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x61, 0x63, 0x74, 0x56, 0x69, 0x65, 0x77, 0x52, 0x65, 0x73,
	0x70, 0x12, 0x45, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x31, 0x2e, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x61, 0x63, 0x74,
	0x2e, 0x41, 0x72, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x61, 0x63, 0x74,
	0x56, 0x69, 0x65, 0x77, 0x52, 0x65, 0x73, 0x70, 0x2e, 0x44, 0x61, 0x74, 0x61, 0x45, 0x6e, 0x74,
	0x72, 0x79, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x12, 0x1e, 0x0a, 0x0a, 0x73, 0x65, 0x72, 0x69,
	0x65, 0x73, 0x54, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0a, 0x73, 0x65,
	0x72, 0x69, 0x65, 0x73, 0x54, 0x79, 0x70, 0x65, 0x1a, 0x59, 0x0a, 0x09, 0x44, 0x61, 0x74, 0x61,
	0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x36, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x20, 0x2e, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x43,
	0x6f, 0x6e, 0x74, 0x72, 0x61, 0x63, 0x74, 0x2e, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x61, 0x63, 0x74,
	0x4e, 0x61, 0x6d, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a,
	0x02, 0x38, 0x01, 0x22, 0x2e, 0x0a, 0x14, 0x53, 0x69, 0x67, 0x6e, 0x65, 0x64, 0x41, 0x75, 0x63,
	0x74, 0x69, 0x6f, 0x6e, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x12, 0x16, 0x0a, 0x06, 0x75,
	0x73, 0x65, 0x72, 0x49, 0x44, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x75, 0x73, 0x65,
	0x72, 0x49, 0x44, 0x22, 0xe2, 0x01, 0x0a, 0x15, 0x53, 0x69, 0x67, 0x6e, 0x65, 0x64, 0x41, 0x75,
	0x63, 0x74, 0x69, 0x6f, 0x6e, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x70, 0x12, 0x3e, 0x0a,
	0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2a, 0x2e, 0x43, 0x75,
	0x73, 0x74, 0x6f, 0x6d, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x61, 0x63, 0x74, 0x2e, 0x53, 0x69, 0x67,
	0x6e, 0x65, 0x64, 0x41, 0x75, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65,
	0x73, 0x70, 0x2e, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x1a, 0x88, 0x01,
	0x0a, 0x04, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x1e, 0x0a, 0x0a, 0x73, 0x65, 0x72, 0x69, 0x65, 0x73,
	0x55, 0x75, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x73, 0x65, 0x72, 0x69,
	0x65, 0x73, 0x55, 0x75, 0x69, 0x64, 0x12, 0x1e, 0x0a, 0x0a, 0x73, 0x65, 0x72, 0x69, 0x65, 0x73,
	0x4e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x73, 0x65, 0x72, 0x69,
	0x65, 0x73, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1e, 0x0a, 0x0a, 0x73, 0x65, 0x72, 0x69, 0x65, 0x73,
	0x54, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0a, 0x73, 0x65, 0x72, 0x69,
	0x65, 0x73, 0x54, 0x79, 0x70, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x61, 0x75, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x55, 0x75, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x61, 0x75, 0x63,
	0x74, 0x69, 0x6f, 0x6e, 0x55, 0x75, 0x69, 0x64, 0x22, 0x95, 0x01, 0x0a, 0x19, 0x53, 0x69, 0x67,
	0x6e, 0x65, 0x64, 0x41, 0x75, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x61,
	0x63, 0x74, 0x73, 0x52, 0x65, 0x71, 0x12, 0x20, 0x0a, 0x0b, 0x61, 0x75, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x55, 0x75, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x61, 0x75, 0x63,
	0x74, 0x69, 0x6f, 0x6e, 0x55, 0x75, 0x69, 0x64, 0x12, 0x1e, 0x0a, 0x0a, 0x73, 0x65, 0x72, 0x69,
	0x65, 0x73, 0x55, 0x75, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x73, 0x65,
	0x72, 0x69, 0x65, 0x73, 0x55, 0x75, 0x69, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x75, 0x73, 0x65, 0x72,
	0x49, 0x44, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x44,
	0x12, 0x1e, 0x0a, 0x0a, 0x73, 0x65, 0x72, 0x69, 0x65, 0x73, 0x54, 0x79, 0x70, 0x65, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x0a, 0x73, 0x65, 0x72, 0x69, 0x65, 0x73, 0x54, 0x79, 0x70, 0x65,
	0x22, 0xae, 0x01, 0x0a, 0x0c, 0x56, 0x69, 0x65, 0x77, 0x55, 0x72, 0x6c, 0x73, 0x44, 0x61, 0x74,
	0x61, 0x12, 0x14, 0x0a, 0x05, 0x6a, 0x6d, 0x78, 0x79, 0x31, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x05, 0x6a, 0x6d, 0x78, 0x79, 0x31, 0x12, 0x14, 0x0a, 0x05, 0x6a, 0x6d, 0x78, 0x7a, 0x32,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x6a, 0x6d, 0x78, 0x7a, 0x32, 0x12, 0x14, 0x0a,
	0x05, 0x70, 0x6d, 0x67, 0x67, 0x33, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x70, 0x6d,
	0x67, 0x67, 0x33, 0x12, 0x14, 0x0a, 0x05, 0x70, 0x6d, 0x67, 0x7a, 0x34, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x05, 0x70, 0x6d, 0x67, 0x7a, 0x34, 0x12, 0x16, 0x0a, 0x06, 0x70, 0x70, 0x71,
	0x72, 0x35, 0x73, 0x18, 0x05, 0x20, 0x03, 0x28, 0x09, 0x52, 0x06, 0x70, 0x70, 0x71, 0x72, 0x35,
	0x73, 0x12, 0x16, 0x0a, 0x06, 0x70, 0x70, 0x62, 0x6c, 0x36, 0x73, 0x18, 0x06, 0x20, 0x03, 0x28,
	0x09, 0x52, 0x06, 0x70, 0x70, 0x62, 0x6c, 0x36, 0x73, 0x12, 0x16, 0x0a, 0x06, 0x78, 0x73, 0x68,
	0x74, 0x31, 0x73, 0x18, 0x07, 0x20, 0x03, 0x28, 0x09, 0x52, 0x06, 0x78, 0x73, 0x68, 0x74, 0x31,
	0x73, 0x22, 0x76, 0x0a, 0x1a, 0x53, 0x69, 0x67, 0x6e, 0x65, 0x64, 0x41, 0x75, 0x63, 0x74, 0x69,
	0x6f, 0x6e, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x61, 0x63, 0x74, 0x73, 0x52, 0x65, 0x73, 0x70, 0x12,
	0x1e, 0x0a, 0x0a, 0x73, 0x65, 0x72, 0x69, 0x65, 0x73, 0x54, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x0a, 0x73, 0x65, 0x72, 0x69, 0x65, 0x73, 0x54, 0x79, 0x70, 0x65, 0x12,
	0x38, 0x0a, 0x08, 0x76, 0x69, 0x65, 0x77, 0x55, 0x72, 0x6c, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x1c, 0x2e, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x61,
	0x63, 0x74, 0x2e, 0x56, 0x69, 0x65, 0x77, 0x55, 0x72, 0x6c, 0x73, 0x44, 0x61, 0x74, 0x61, 0x52,
	0x08, 0x76, 0x69, 0x65, 0x77, 0x55, 0x72, 0x6c, 0x73, 0x22, 0x5f, 0x0a, 0x15, 0x41, 0x75, 0x63,
	0x74, 0x69, 0x6f, 0x6e, 0x41, 0x72, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x49, 0x6e, 0x66, 0x6f, 0x52,
	0x65, 0x71, 0x12, 0x2e, 0x0a, 0x12, 0x61, 0x75, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x41, 0x72, 0x74,
	0x77, 0x6f, 0x72, 0x6b, 0x55, 0x75, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x12,
	0x61, 0x75, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x41, 0x72, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x55, 0x75,
	0x69, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x44, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x44, 0x22, 0x3a, 0x0a, 0x16, 0x41, 0x75,
	0x63, 0x74, 0x69, 0x6f, 0x6e, 0x41, 0x72, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x49, 0x6e, 0x66, 0x6f,
	0x52, 0x65, 0x73, 0x70, 0x12, 0x20, 0x0a, 0x0b, 0x61, 0x75, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x54,
	0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x61, 0x75, 0x63, 0x74, 0x69,
	0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x22, 0x29, 0x0a, 0x0b, 0x53, 0x69, 0x67, 0x6e, 0x49, 0x6e,
	0x66, 0x6f, 0x52, 0x65, 0x71, 0x12, 0x1a, 0x0a, 0x08, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x4e, 0x6f,
	0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x09, 0x52, 0x08, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x4e, 0x6f,
	0x73, 0x22, 0x9d, 0x01, 0x0a, 0x0c, 0x53, 0x69, 0x67, 0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65,
	0x73, 0x70, 0x12, 0x35, 0x0a, 0x04, 0x44, 0x61, 0x74, 0x61, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x21, 0x2e, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x61, 0x63,
	0x74, 0x2e, 0x53, 0x69, 0x67, 0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x73, 0x70, 0x2e, 0x49,
	0x6e, 0x66, 0x6f, 0x52, 0x04, 0x44, 0x61, 0x74, 0x61, 0x1a, 0x56, 0x0a, 0x04, 0x49, 0x6e, 0x66,
	0x6f, 0x12, 0x18, 0x0a, 0x07, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x4e, 0x6f, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x07, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x4e, 0x6f, 0x12, 0x1a, 0x0a, 0x08, 0x73,
	0x69, 0x67, 0x6e, 0x44, 0x61, 0x74, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x73,
	0x69, 0x67, 0x6e, 0x44, 0x61, 0x74, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x6f, 0x72, 0x64, 0x65, 0x72,
	0x49, 0x44, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x49,
	0x44, 0x2a, 0x44, 0x0a, 0x08, 0x53, 0x69, 0x67, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x12, 0x13, 0x0a,
	0x0f, 0x53, 0x69, 0x67, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x55, 0x6e, 0x6b, 0x6e, 0x6f, 0x77, 0x6e,
	0x10, 0x00, 0x12, 0x12, 0x0a, 0x0e, 0x53, 0x69, 0x67, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x43, 0x75,
	0x73, 0x74, 0x6f, 0x6d, 0x10, 0x01, 0x12, 0x0f, 0x0a, 0x0b, 0x53, 0x69, 0x67, 0x6e, 0x54, 0x79,
	0x70, 0x65, 0x46, 0x64, 0x64, 0x10, 0x02, 0x32, 0xb1, 0x17, 0x0a, 0x0e, 0x43, 0x75, 0x73, 0x74,
	0x6f, 0x6d, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x61, 0x63, 0x74, 0x12, 0x68, 0x0a, 0x13, 0x50, 0x72,
	0x6f, 0x74, 0x6f, 0x63, 0x6f, 0x6c, 0x53, 0x69, 0x67, 0x6e, 0x4f, 0x66, 0x66, 0x6c, 0x69, 0x6e,
	0x65, 0x12, 0x26, 0x2e, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x61,
	0x63, 0x74, 0x2e, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x6f, 0x6c, 0x53, 0x69, 0x67, 0x6e, 0x4f,
	0x66, 0x66, 0x6c, 0x69, 0x6e, 0x65, 0x52, 0x65, 0x71, 0x1a, 0x27, 0x2e, 0x43, 0x75, 0x73, 0x74,
	0x6f, 0x6d, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x61, 0x63, 0x74, 0x2e, 0x50, 0x72, 0x6f, 0x74, 0x6f,
	0x63, 0x6f, 0x6c, 0x53, 0x69, 0x67, 0x6e, 0x4f, 0x66, 0x66, 0x6c, 0x69, 0x6e, 0x65, 0x52, 0x65,
	0x73, 0x70, 0x22, 0x00, 0x12, 0x65, 0x0a, 0x12, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x6f, 0x6c,
	0x53, 0x69, 0x67, 0x6e, 0x4f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x12, 0x25, 0x2e, 0x43, 0x75, 0x73,
	0x74, 0x6f, 0x6d, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x61, 0x63, 0x74, 0x2e, 0x50, 0x72, 0x6f, 0x74,
	0x6f, 0x63, 0x6f, 0x6c, 0x53, 0x69, 0x67, 0x6e, 0x4f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x52, 0x65,
	0x71, 0x1a, 0x26, 0x2e, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x61,
	0x63, 0x74, 0x2e, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x6f, 0x6c, 0x53, 0x69, 0x67, 0x6e, 0x4f,
	0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x52, 0x65, 0x73, 0x70, 0x22, 0x00, 0x12, 0x53, 0x0a, 0x0c, 0x54,
	0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x44, 0x61, 0x74, 0x61, 0x12, 0x1f, 0x2e, 0x43, 0x75,
	0x73, 0x74, 0x6f, 0x6d, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x61, 0x63, 0x74, 0x2e, 0x54, 0x65, 0x6d,
	0x70, 0x6c, 0x61, 0x74, 0x65, 0x44, 0x61, 0x74, 0x61, 0x52, 0x65, 0x71, 0x1a, 0x20, 0x2e, 0x43,
	0x75, 0x73, 0x74, 0x6f, 0x6d, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x61, 0x63, 0x74, 0x2e, 0x54, 0x65,
	0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x44, 0x61, 0x74, 0x61, 0x52, 0x65, 0x73, 0x70, 0x22, 0x00,
	0x12, 0x4a, 0x0a, 0x08, 0x55, 0x73, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x1b, 0x2e, 0x43,
	0x75, 0x73, 0x74, 0x6f, 0x6d, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x61, 0x63, 0x74, 0x2e, 0x55, 0x73,
	0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x71, 0x1a, 0x1f, 0x2e, 0x43, 0x75, 0x73, 0x74,
	0x6f, 0x6d, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x61, 0x63, 0x74, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x49,
	0x6e, 0x66, 0x6f, 0x52, 0x65, 0x71, 0x52, 0x65, 0x73, 0x70, 0x22, 0x00, 0x12, 0x59, 0x0a, 0x0e,
	0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x55, 0x73, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x21,
	0x2e, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x61, 0x63, 0x74, 0x2e,
	0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x55, 0x73, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65,
	0x71, 0x1a, 0x22, 0x2e, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x61,
	0x63, 0x74, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x55, 0x73, 0x65, 0x72, 0x49, 0x6e, 0x66,
	0x6f, 0x52, 0x65, 0x73, 0x70, 0x22, 0x00, 0x12, 0x59, 0x0a, 0x0e, 0x55, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x61, 0x63, 0x74, 0x12, 0x21, 0x2e, 0x43, 0x75, 0x73, 0x74,
	0x6f, 0x6d, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x61, 0x63, 0x74, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x61, 0x63, 0x74, 0x52, 0x65, 0x71, 0x1a, 0x22, 0x2e, 0x43,
	0x75, 0x73, 0x74, 0x6f, 0x6d, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x61, 0x63, 0x74, 0x2e, 0x55, 0x70,
	0x64, 0x61, 0x74, 0x65, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x61, 0x63, 0x74, 0x52, 0x65, 0x73, 0x70,
	0x22, 0x00, 0x12, 0x68, 0x0a, 0x13, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x43, 0x6f, 0x6e, 0x74,
	0x72, 0x61, 0x63, 0x74, 0x42, 0x61, 0x74, 0x63, 0x68, 0x12, 0x26, 0x2e, 0x43, 0x75, 0x73, 0x74,
	0x6f, 0x6d, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x61, 0x63, 0x74, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x61, 0x63, 0x74, 0x42, 0x61, 0x74, 0x63, 0x68, 0x52, 0x65,
	0x71, 0x1a, 0x27, 0x2e, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x61,
	0x63, 0x74, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x61, 0x63,
	0x74, 0x42, 0x61, 0x74, 0x63, 0x68, 0x52, 0x65, 0x73, 0x70, 0x22, 0x00, 0x12, 0x5f, 0x0a, 0x10,
	0x55, 0x73, 0x65, 0x72, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x61, 0x63, 0x74, 0x44, 0x61, 0x74, 0x61,
	0x12, 0x23, 0x2e, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x61, 0x63,
	0x74, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x61, 0x63, 0x74, 0x44, 0x61,
	0x74, 0x61, 0x52, 0x65, 0x71, 0x1a, 0x24, 0x2e, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x43, 0x6f,
	0x6e, 0x74, 0x72, 0x61, 0x63, 0x74, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x43, 0x6f, 0x6e, 0x74, 0x72,
	0x61, 0x63, 0x74, 0x44, 0x61, 0x74, 0x61, 0x52, 0x65, 0x73, 0x70, 0x22, 0x00, 0x12, 0x44, 0x0a,
	0x07, 0x46, 0x64, 0x64, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x1a, 0x2e, 0x43, 0x75, 0x73, 0x74, 0x6f,
	0x6d, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x61, 0x63, 0x74, 0x2e, 0x46, 0x64, 0x64, 0x49, 0x6e, 0x66,
	0x6f, 0x52, 0x65, 0x71, 0x1a, 0x1b, 0x2e, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x43, 0x6f, 0x6e,
	0x74, 0x72, 0x61, 0x63, 0x74, 0x2e, 0x46, 0x64, 0x64, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x73,
	0x70, 0x22, 0x00, 0x12, 0x5f, 0x0a, 0x10, 0x4f, 0x66, 0x66, 0x6c, 0x69, 0x6e, 0x65, 0x53, 0x69,
	0x67, 0x6e, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x12, 0x23, 0x2e, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d,
	0x43, 0x6f, 0x6e, 0x74, 0x72, 0x61, 0x63, 0x74, 0x2e, 0x4f, 0x66, 0x66, 0x6c, 0x69, 0x6e, 0x65,
	0x53, 0x69, 0x67, 0x6e, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x52, 0x65, 0x71, 0x1a, 0x24, 0x2e, 0x43,
	0x75, 0x73, 0x74, 0x6f, 0x6d, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x61, 0x63, 0x74, 0x2e, 0x4f, 0x66,
	0x66, 0x6c, 0x69, 0x6e, 0x65, 0x53, 0x69, 0x67, 0x6e, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x52, 0x65,
	0x73, 0x70, 0x22, 0x00, 0x12, 0x59, 0x0a, 0x0e, 0x41, 0x72, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x42,
	0x69, 0x64, 0x44, 0x61, 0x74, 0x61, 0x12, 0x21, 0x2e, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x43,
	0x6f, 0x6e, 0x74, 0x72, 0x61, 0x63, 0x74, 0x2e, 0x41, 0x72, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x42,
	0x69, 0x64, 0x44, 0x61, 0x74, 0x61, 0x52, 0x65, 0x71, 0x1a, 0x22, 0x2e, 0x43, 0x75, 0x73, 0x74,
	0x6f, 0x6d, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x61, 0x63, 0x74, 0x2e, 0x41, 0x72, 0x74, 0x77, 0x6f,
	0x72, 0x6b, 0x42, 0x69, 0x64, 0x44, 0x61, 0x74, 0x61, 0x52, 0x65, 0x73, 0x70, 0x22, 0x00, 0x12,
	0x53, 0x0a, 0x0c, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x61, 0x63, 0x74, 0x44, 0x61, 0x74, 0x61, 0x12,
	0x1f, 0x2e, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x61, 0x63, 0x74,
	0x2e, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x61, 0x63, 0x74, 0x44, 0x61, 0x74, 0x61, 0x52, 0x65, 0x71,
	0x1a, 0x20, 0x2e, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x61, 0x63,
	0x74, 0x2e, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x61, 0x63, 0x74, 0x44, 0x61, 0x74, 0x61, 0x52, 0x65,
	0x73, 0x70, 0x22, 0x00, 0x12, 0x53, 0x0a, 0x0c, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x61, 0x63, 0x74,
	0x56, 0x69, 0x65, 0x77, 0x12, 0x1f, 0x2e, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x43, 0x6f, 0x6e,
	0x74, 0x72, 0x61, 0x63, 0x74, 0x2e, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x61, 0x63, 0x74, 0x56, 0x69,
	0x65, 0x77, 0x52, 0x65, 0x71, 0x1a, 0x20, 0x2e, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x43, 0x6f,
	0x6e, 0x74, 0x72, 0x61, 0x63, 0x74, 0x2e, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x61, 0x63, 0x74, 0x56,
	0x69, 0x65, 0x77, 0x52, 0x65, 0x73, 0x70, 0x22, 0x00, 0x12, 0x5c, 0x0a, 0x0f, 0x55, 0x73, 0x65,
	0x72, 0x49, 0x6e, 0x66, 0x6f, 0x42, 0x79, 0x50, 0x68, 0x6f, 0x6e, 0x65, 0x12, 0x22, 0x2e, 0x43,
	0x75, 0x73, 0x74, 0x6f, 0x6d, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x61, 0x63, 0x74, 0x2e, 0x55, 0x73,
	0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x42, 0x79, 0x50, 0x68, 0x6f, 0x6e, 0x65, 0x52, 0x65, 0x71,
	0x1a, 0x23, 0x2e, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x61, 0x63,
	0x74, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x42, 0x79, 0x50, 0x68, 0x6f, 0x6e,
	0x65, 0x52, 0x65, 0x73, 0x70, 0x22, 0x00, 0x12, 0x71, 0x0a, 0x16, 0x47, 0x65, 0x74, 0x41, 0x75,
	0x63, 0x74, 0x69, 0x6f, 0x6e, 0x41, 0x72, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x55, 0x75, 0x69, 0x64,
	0x73, 0x12, 0x29, 0x2e, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x61,
	0x63, 0x74, 0x2e, 0x47, 0x65, 0x74, 0x41, 0x75, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x41, 0x72, 0x74,
	0x77, 0x6f, 0x72, 0x6b, 0x55, 0x75, 0x69, 0x64, 0x73, 0x52, 0x65, 0x71, 0x1a, 0x2a, 0x2e, 0x43,
	0x75, 0x73, 0x74, 0x6f, 0x6d, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x61, 0x63, 0x74, 0x2e, 0x47, 0x65,
	0x74, 0x41, 0x75, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x41, 0x72, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x55,
	0x75, 0x69, 0x64, 0x73, 0x52, 0x65, 0x73, 0x70, 0x22, 0x00, 0x12, 0x47, 0x0a, 0x08, 0x53, 0x69,
	0x67, 0x6e, 0x56, 0x69, 0x65, 0x77, 0x12, 0x1b, 0x2e, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x43,
	0x6f, 0x6e, 0x74, 0x72, 0x61, 0x63, 0x74, 0x2e, 0x53, 0x69, 0x67, 0x6e, 0x56, 0x69, 0x65, 0x77,
	0x52, 0x65, 0x71, 0x1a, 0x1c, 0x2e, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x43, 0x6f, 0x6e, 0x74,
	0x72, 0x61, 0x63, 0x74, 0x2e, 0x53, 0x69, 0x67, 0x6e, 0x56, 0x69, 0x65, 0x77, 0x52, 0x65, 0x73,
	0x70, 0x22, 0x00, 0x12, 0x6b, 0x0a, 0x14, 0x52, 0x65, 0x70, 0x61, 0x69, 0x72, 0x46, 0x64, 0x64,
	0x43, 0x6f, 0x6e, 0x74, 0x72, 0x61, 0x63, 0x74, 0x55, 0x72, 0x6c, 0x12, 0x27, 0x2e, 0x43, 0x75,
	0x73, 0x74, 0x6f, 0x6d, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x61, 0x63, 0x74, 0x2e, 0x52, 0x65, 0x70,
	0x61, 0x69, 0x72, 0x46, 0x64, 0x64, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x61, 0x63, 0x74, 0x55, 0x72,
	0x6c, 0x52, 0x65, 0x71, 0x1a, 0x28, 0x2e, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x43, 0x6f, 0x6e,
	0x74, 0x72, 0x61, 0x63, 0x74, 0x2e, 0x52, 0x65, 0x70, 0x61, 0x69, 0x72, 0x46, 0x64, 0x64, 0x43,
	0x6f, 0x6e, 0x74, 0x72, 0x61, 0x63, 0x74, 0x55, 0x72, 0x6c, 0x52, 0x65, 0x73, 0x70, 0x22, 0x00,
	0x12, 0x77, 0x0a, 0x18, 0x55, 0x73, 0x65, 0x72, 0x41, 0x75, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x43,
	0x6f, 0x6e, 0x74, 0x72, 0x61, 0x63, 0x74, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x2b, 0x2e, 0x43,
	0x75, 0x73, 0x74, 0x6f, 0x6d, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x61, 0x63, 0x74, 0x2e, 0x55, 0x73,
	0x65, 0x72, 0x41, 0x75, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x61, 0x63,
	0x74, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x1a, 0x2c, 0x2e, 0x43, 0x75, 0x73, 0x74,
	0x6f, 0x6d, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x61, 0x63, 0x74, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x41,
	0x75, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x61, 0x63, 0x74, 0x43, 0x6f,
	0x75, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x70, 0x22, 0x00, 0x12, 0x74, 0x0a, 0x17, 0x53, 0x65, 0x72,
	0x69, 0x65, 0x73, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x61, 0x63, 0x74,
	0x56, 0x69, 0x65, 0x77, 0x12, 0x2a, 0x2e, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x43, 0x6f, 0x6e,
	0x74, 0x72, 0x61, 0x63, 0x74, 0x2e, 0x53, 0x65, 0x72, 0x69, 0x65, 0x73, 0x4f, 0x72, 0x64, 0x65,
	0x72, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x61, 0x63, 0x74, 0x56, 0x69, 0x65, 0x77, 0x52, 0x65, 0x71,
	0x1a, 0x2b, 0x2e, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x61, 0x63,
	0x74, 0x2e, 0x53, 0x65, 0x72, 0x69, 0x65, 0x73, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x43, 0x6f, 0x6e,
	0x74, 0x72, 0x61, 0x63, 0x74, 0x56, 0x69, 0x65, 0x77, 0x52, 0x65, 0x73, 0x70, 0x22, 0x00, 0x12,
	0x74, 0x0a, 0x17, 0x53, 0x65, 0x72, 0x69, 0x65, 0x73, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x43, 0x6f,
	0x6e, 0x74, 0x72, 0x61, 0x63, 0x74, 0x53, 0x69, 0x67, 0x6e, 0x12, 0x2a, 0x2e, 0x43, 0x75, 0x73,
	0x74, 0x6f, 0x6d, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x61, 0x63, 0x74, 0x2e, 0x53, 0x65, 0x72, 0x69,
	0x65, 0x73, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x61, 0x63, 0x74, 0x53,
	0x69, 0x67, 0x6e, 0x52, 0x65, 0x71, 0x1a, 0x2b, 0x2e, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x43,
	0x6f, 0x6e, 0x74, 0x72, 0x61, 0x63, 0x74, 0x2e, 0x53, 0x65, 0x72, 0x69, 0x65, 0x73, 0x4f, 0x72,
	0x64, 0x65, 0x72, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x61, 0x63, 0x74, 0x53, 0x69, 0x67, 0x6e, 0x52,
	0x65, 0x73, 0x70, 0x22, 0x00, 0x12, 0x53, 0x0a, 0x0c, 0x53, 0x69, 0x67, 0x6e, 0x4f, 0x6e, 0x6c,
	0x69, 0x6e, 0x65, 0x56, 0x32, 0x12, 0x1f, 0x2e, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x43, 0x6f,
	0x6e, 0x74, 0x72, 0x61, 0x63, 0x74, 0x2e, 0x53, 0x69, 0x67, 0x6e, 0x4f, 0x6e, 0x6c, 0x69, 0x6e,
	0x65, 0x56, 0x32, 0x52, 0x65, 0x71, 0x1a, 0x20, 0x2e, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x43,
	0x6f, 0x6e, 0x74, 0x72, 0x61, 0x63, 0x74, 0x2e, 0x53, 0x69, 0x67, 0x6e, 0x4f, 0x6e, 0x6c, 0x69,
	0x6e, 0x65, 0x56, 0x32, 0x52, 0x65, 0x73, 0x70, 0x22, 0x00, 0x12, 0x4d, 0x0a, 0x0a, 0x55, 0x73,
	0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x56, 0x32, 0x12, 0x1d, 0x2e, 0x43, 0x75, 0x73, 0x74, 0x6f,
	0x6d, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x61, 0x63, 0x74, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x49, 0x6e,
	0x66, 0x6f, 0x56, 0x32, 0x52, 0x65, 0x71, 0x1a, 0x1e, 0x2e, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d,
	0x43, 0x6f, 0x6e, 0x74, 0x72, 0x61, 0x63, 0x74, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x49, 0x6e, 0x66,
	0x6f, 0x56, 0x32, 0x52, 0x65, 0x73, 0x70, 0x22, 0x00, 0x12, 0x5f, 0x0a, 0x10, 0x55, 0x70, 0x64,
	0x61, 0x74, 0x65, 0x55, 0x73, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x56, 0x32, 0x12, 0x23, 0x2e,
	0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x61, 0x63, 0x74, 0x2e, 0x55,
	0x70, 0x64, 0x61, 0x74, 0x65, 0x55, 0x73, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x56, 0x32, 0x52,
	0x65, 0x71, 0x1a, 0x24, 0x2e, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x43, 0x6f, 0x6e, 0x74, 0x72,
	0x61, 0x63, 0x74, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x55, 0x73, 0x65, 0x72, 0x49, 0x6e,
	0x66, 0x6f, 0x56, 0x32, 0x52, 0x65, 0x73, 0x70, 0x22, 0x00, 0x12, 0x56, 0x0a, 0x0d, 0x41, 0x75,
	0x63, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x56, 0x32, 0x12, 0x20, 0x2e, 0x43, 0x75,
	0x73, 0x74, 0x6f, 0x6d, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x61, 0x63, 0x74, 0x2e, 0x41, 0x75, 0x63,
	0x74, 0x69, 0x6f, 0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x56, 0x32, 0x52, 0x65, 0x71, 0x1a, 0x21, 0x2e,
	0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x61, 0x63, 0x74, 0x2e, 0x41,
	0x75, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x56, 0x32, 0x52, 0x65, 0x73, 0x70,
	0x22, 0x00, 0x12, 0x59, 0x0a, 0x0e, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x61, 0x63, 0x74, 0x56, 0x69,
	0x65, 0x77, 0x56, 0x32, 0x12, 0x21, 0x2e, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x43, 0x6f, 0x6e,
	0x74, 0x72, 0x61, 0x63, 0x74, 0x2e, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x61, 0x63, 0x74, 0x56, 0x69,
	0x65, 0x77, 0x56, 0x32, 0x52, 0x65, 0x71, 0x1a, 0x22, 0x2e, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d,
	0x43, 0x6f, 0x6e, 0x74, 0x72, 0x61, 0x63, 0x74, 0x2e, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x61, 0x63,
	0x74, 0x56, 0x69, 0x65, 0x77, 0x56, 0x32, 0x52, 0x65, 0x73, 0x70, 0x22, 0x00, 0x12, 0x6b, 0x0a,
	0x14, 0x55, 0x73, 0x65, 0x72, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x61, 0x63, 0x74, 0x4e, 0x65, 0x65,
	0x64, 0x53, 0x69, 0x67, 0x6e, 0x12, 0x27, 0x2e, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x43, 0x6f,
	0x6e, 0x74, 0x72, 0x61, 0x63, 0x74, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x43, 0x6f, 0x6e, 0x74, 0x72,
	0x61, 0x63, 0x74, 0x4e, 0x65, 0x65, 0x64, 0x53, 0x69, 0x67, 0x6e, 0x52, 0x65, 0x71, 0x1a, 0x28,
	0x2e, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x61, 0x63, 0x74, 0x2e,
	0x55, 0x73, 0x65, 0x72, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x61, 0x63, 0x74, 0x4e, 0x65, 0x65, 0x64,
	0x53, 0x69, 0x67, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x22, 0x00, 0x12, 0x68, 0x0a, 0x13, 0x41, 0x72,
	0x74, 0x77, 0x6f, 0x72, 0x6b, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x61, 0x63, 0x74, 0x56, 0x69, 0x65,
	0x77, 0x12, 0x26, 0x2e, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x61,
	0x63, 0x74, 0x2e, 0x41, 0x72, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x61,
	0x63, 0x74, 0x56, 0x69, 0x65, 0x77, 0x52, 0x65, 0x71, 0x1a, 0x27, 0x2e, 0x43, 0x75, 0x73, 0x74,
	0x6f, 0x6d, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x61, 0x63, 0x74, 0x2e, 0x41, 0x72, 0x74, 0x77, 0x6f,
	0x72, 0x6b, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x61, 0x63, 0x74, 0x56, 0x69, 0x65, 0x77, 0x52, 0x65,
	0x73, 0x70, 0x22, 0x00, 0x12, 0x62, 0x0a, 0x11, 0x53, 0x69, 0x67, 0x6e, 0x65, 0x64, 0x41, 0x75,
	0x63, 0x74, 0x69, 0x6f, 0x6e, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x24, 0x2e, 0x43, 0x75, 0x73, 0x74,
	0x6f, 0x6d, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x61, 0x63, 0x74, 0x2e, 0x53, 0x69, 0x67, 0x6e, 0x65,
	0x64, 0x41, 0x75, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x1a,
	0x25, 0x2e, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x61, 0x63, 0x74,
	0x2e, 0x53, 0x69, 0x67, 0x6e, 0x65, 0x64, 0x41, 0x75, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x4c, 0x69,
	0x73, 0x74, 0x52, 0x65, 0x73, 0x70, 0x22, 0x00, 0x12, 0x71, 0x0a, 0x16, 0x53, 0x69, 0x67, 0x6e,
	0x65, 0x64, 0x41, 0x75, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x61, 0x63,
	0x74, 0x73, 0x12, 0x29, 0x2e, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x43, 0x6f, 0x6e, 0x74, 0x72,
	0x61, 0x63, 0x74, 0x2e, 0x53, 0x69, 0x67, 0x6e, 0x65, 0x64, 0x41, 0x75, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x61, 0x63, 0x74, 0x73, 0x52, 0x65, 0x71, 0x1a, 0x2a, 0x2e,
	0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x61, 0x63, 0x74, 0x2e, 0x53,
	0x69, 0x67, 0x6e, 0x65, 0x64, 0x41, 0x75, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x6f, 0x6e, 0x74,
	0x72, 0x61, 0x63, 0x74, 0x73, 0x52, 0x65, 0x73, 0x70, 0x22, 0x00, 0x12, 0x65, 0x0a, 0x12, 0x41,
	0x75, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x41, 0x72, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x49, 0x6e, 0x66,
	0x6f, 0x12, 0x25, 0x2e, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x61,
	0x63, 0x74, 0x2e, 0x41, 0x75, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x41, 0x72, 0x74, 0x77, 0x6f, 0x72,
	0x6b, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x71, 0x1a, 0x26, 0x2e, 0x43, 0x75, 0x73, 0x74, 0x6f,
	0x6d, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x61, 0x63, 0x74, 0x2e, 0x41, 0x75, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x41, 0x72, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x73, 0x70,
	0x22, 0x00, 0x12, 0x47, 0x0a, 0x08, 0x53, 0x69, 0x67, 0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x1b,
	0x2e, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x61, 0x63, 0x74, 0x2e,
	0x53, 0x69, 0x67, 0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x71, 0x1a, 0x1c, 0x2e, 0x43, 0x75,
	0x73, 0x74, 0x6f, 0x6d, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x61, 0x63, 0x74, 0x2e, 0x53, 0x69, 0x67,
	0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x73, 0x70, 0x22, 0x00, 0x42, 0x23, 0x5a, 0x21, 0x2e,
	0x2f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x72, 0x61, 0x63, 0x74,
	0x3b, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x72, 0x61, 0x63, 0x74,
	0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_pb_custom_contract_proto_rawDescOnce sync.Once
	file_pb_custom_contract_proto_rawDescData = file_pb_custom_contract_proto_rawDesc
)

func file_pb_custom_contract_proto_rawDescGZIP() []byte {
	file_pb_custom_contract_proto_rawDescOnce.Do(func() {
		file_pb_custom_contract_proto_rawDescData = protoimpl.X.CompressGZIP(file_pb_custom_contract_proto_rawDescData)
	})
	return file_pb_custom_contract_proto_rawDescData
}

var file_pb_custom_contract_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_pb_custom_contract_proto_msgTypes = make([]protoimpl.MessageInfo, 80)
var file_pb_custom_contract_proto_goTypes = []any{
	(SignType)(0),                            // 0: CustomContract.SignType
	(*UserInfo)(nil),                         // 1: CustomContract.UserInfo
	(*ProtocolSignOfflineReq)(nil),           // 2: CustomContract.ProtocolSignOfflineReq
	(*ProtocolSignOfflineResp)(nil),          // 3: CustomContract.ProtocolSignOfflineResp
	(*ProtocolSignOnlineReq)(nil),            // 4: CustomContract.ProtocolSignOnlineReq
	(*ProtocolSignOnlineResp)(nil),           // 5: CustomContract.ProtocolSignOnlineResp
	(*TemplateDataReq)(nil),                  // 6: CustomContract.TemplateDataReq
	(*TemplateDataResp)(nil),                 // 7: CustomContract.TemplateDataResp
	(*UserInfoReq)(nil),                      // 8: CustomContract.UserInfoReq
	(*UserInfoReqResp)(nil),                  // 9: CustomContract.UserInfoReqResp
	(*UpdateUserInfoReq)(nil),                // 10: CustomContract.UpdateUserInfoReq
	(*UpdateUserInfoResp)(nil),               // 11: CustomContract.UpdateUserInfoResp
	(*UpdateContractReq)(nil),                // 12: CustomContract.UpdateContractReq
	(*UpdateContractResp)(nil),               // 13: CustomContract.UpdateContractResp
	(*UpdateContractBatchReq)(nil),           // 14: CustomContract.UpdateContractBatchReq
	(*UpdateContractBatchResp)(nil),          // 15: CustomContract.UpdateContractBatchResp
	(*UserContractDataReq)(nil),              // 16: CustomContract.UserContractDataReq
	(*UserContractDataResp)(nil),             // 17: CustomContract.UserContractDataResp
	(*FddInfoReq)(nil),                       // 18: CustomContract.FddInfoReq
	(*FddInfoResp)(nil),                      // 19: CustomContract.FddInfoResp
	(*OfflineSignOrderReq)(nil),              // 20: CustomContract.OfflineSignOrderReq
	(*OfflineSignOrderResp)(nil),             // 21: CustomContract.OfflineSignOrderResp
	(*ArtworkBidDataReq)(nil),                // 22: CustomContract.ArtworkBidDataReq
	(*ArtworkBidDataResp)(nil),               // 23: CustomContract.ArtworkBidDataResp
	(*ContractDataReq)(nil),                  // 24: CustomContract.ContractDataReq
	(*ContractDataResp)(nil),                 // 25: CustomContract.ContractDataResp
	(*ContractViewReq)(nil),                  // 26: CustomContract.ContractViewReq
	(*ContractViewResp)(nil),                 // 27: CustomContract.ContractViewResp
	(*UserInfoByPhoneReq)(nil),               // 28: CustomContract.UserInfoByPhoneReq
	(*UserInfoByPhoneResp)(nil),              // 29: CustomContract.UserInfoByPhoneResp
	(*GetAuctionArtworkUuidsReq)(nil),        // 30: CustomContract.GetAuctionArtworkUuidsReq
	(*GetAuctionArtworkUuidsResp)(nil),       // 31: CustomContract.GetAuctionArtworkUuidsResp
	(*SignViewReq)(nil),                      // 32: CustomContract.SignViewReq
	(*SignViewResp)(nil),                     // 33: CustomContract.SignViewResp
	(*RepairFddContractUrlReq)(nil),          // 34: CustomContract.RepairFddContractUrlReq
	(*RepairFddContractUrlResp)(nil),         // 35: CustomContract.RepairFddContractUrlResp
	(*UserAuctionContractCountReq)(nil),      // 36: CustomContract.UserAuctionContractCountReq
	(*UserAuctionContractCountResp)(nil),     // 37: CustomContract.UserAuctionContractCountResp
	(*SeriesOrderContractViewReq)(nil),       // 38: CustomContract.SeriesOrderContractViewReq
	(*SeriesOrderContractViewResp)(nil),      // 39: CustomContract.SeriesOrderContractViewResp
	(*SeriesOrderContractSignReq)(nil),       // 40: CustomContract.SeriesOrderContractSignReq
	(*SeriesOrderContractSignResp)(nil),      // 41: CustomContract.SeriesOrderContractSignResp
	(*SignOnlineV2Req)(nil),                  // 42: CustomContract.SignOnlineV2Req
	(*SignOnlineV2Resp)(nil),                 // 43: CustomContract.SignOnlineV2Resp
	(*UserInfoV2Req)(nil),                    // 44: CustomContract.UserInfoV2Req
	(*UserInfoV2Resp)(nil),                   // 45: CustomContract.UserInfoV2Resp
	(*UpdateUserInfoV2Req)(nil),              // 46: CustomContract.UpdateUserInfoV2Req
	(*UpdateUserInfoV2Resp)(nil),             // 47: CustomContract.UpdateUserInfoV2Resp
	(*AuctionInfo)(nil),                      // 48: CustomContract.AuctionInfo
	(*AuctionInfoV2Req)(nil),                 // 49: CustomContract.AuctionInfoV2Req
	(*AuctionInfoV2Resp)(nil),                // 50: CustomContract.AuctionInfoV2Resp
	(*ContractViewV2Req)(nil),                // 51: CustomContract.ContractViewV2Req
	(*ContractViewV2Resp)(nil),               // 52: CustomContract.ContractViewV2Resp
	(*UserContractNeedSignReq)(nil),          // 53: CustomContract.UserContractNeedSignReq
	(*UserContractNeedSignResp)(nil),         // 54: CustomContract.UserContractNeedSignResp
	(*ArtworkContractViewReq)(nil),           // 55: CustomContract.ArtworkContractViewReq
	(*ContractNameInfo)(nil),                 // 56: CustomContract.ContractNameInfo
	(*ArtworkContractViewResp)(nil),          // 57: CustomContract.ArtworkContractViewResp
	(*SignedAuctionListReq)(nil),             // 58: CustomContract.SignedAuctionListReq
	(*SignedAuctionListResp)(nil),            // 59: CustomContract.SignedAuctionListResp
	(*SignedAuctionContractsReq)(nil),        // 60: CustomContract.SignedAuctionContractsReq
	(*ViewUrlsData)(nil),                     // 61: CustomContract.ViewUrlsData
	(*SignedAuctionContractsResp)(nil),       // 62: CustomContract.SignedAuctionContractsResp
	(*AuctionArtworkInfoReq)(nil),            // 63: CustomContract.AuctionArtworkInfoReq
	(*AuctionArtworkInfoResp)(nil),           // 64: CustomContract.AuctionArtworkInfoResp
	(*SignInfoReq)(nil),                      // 65: CustomContract.SignInfoReq
	(*SignInfoResp)(nil),                     // 66: CustomContract.SignInfoResp
	(*ProtocolSignOnlineResp_Info)(nil),      // 67: CustomContract.ProtocolSignOnlineResp.Info
	(*TemplateDataResp_Info)(nil),            // 68: CustomContract.TemplateDataResp.Info
	(*UserContractDataRespContractInfo)(nil), // 69: CustomContract.UserContractDataResp.contractInfo
	(*ArtworkBidDataResp_AuctionInfo)(nil),   // 70: CustomContract.ArtworkBidDataResp.AuctionInfo
	(*ArtworkBidDataResp_BigInfo)(nil),       // 71: CustomContract.ArtworkBidDataResp.BigInfo
	(*ContractDataResp_ContractInfo)(nil),    // 72: CustomContract.ContractDataResp.ContractInfo
	(*ContractViewResp_ViewUrlsData)(nil),    // 73: CustomContract.ContractViewResp.ViewUrlsData
	(*RepairFddContractUrlResp_Info)(nil),    // 74: CustomContract.RepairFddContractUrlResp.Info
	nil,                                      // 75: CustomContract.SeriesOrderContractViewResp.ContractUrlsEntry
	nil,                                      // 76: CustomContract.SeriesOrderContractSignResp.ContractUrlsEntry
	(*ContractViewV2Resp_ViewUrlsData)(nil),  // 77: CustomContract.ContractViewV2Resp.ViewUrlsData
	nil,                                      // 78: CustomContract.ArtworkContractViewResp.DataEntry
	(*SignedAuctionListResp_Info)(nil),       // 79: CustomContract.SignedAuctionListResp.Info
	(*SignInfoResp_Info)(nil),                // 80: CustomContract.SignInfoResp.Info
}
var file_pb_custom_contract_proto_depIdxs = []int32{
	1,  // 0: CustomContract.ProtocolSignOfflineReq.userInfo:type_name -> CustomContract.UserInfo
	0,  // 1: CustomContract.ProtocolSignOfflineResp.signType:type_name -> CustomContract.SignType
	1,  // 2: CustomContract.ProtocolSignOnlineReq.userInfo:type_name -> CustomContract.UserInfo
	67, // 3: CustomContract.ProtocolSignOnlineResp.Data:type_name -> CustomContract.ProtocolSignOnlineResp.Info
	68, // 4: CustomContract.TemplateDataResp.Data:type_name -> CustomContract.TemplateDataResp.Info
	1,  // 5: CustomContract.UpdateUserInfoReq.userInfo:type_name -> CustomContract.UserInfo
	71, // 6: CustomContract.ArtworkBidDataResp.bigData:type_name -> CustomContract.ArtworkBidDataResp.BigInfo
	70, // 7: CustomContract.ArtworkBidDataResp.AuctionData:type_name -> CustomContract.ArtworkBidDataResp.AuctionInfo
	72, // 8: CustomContract.ContractDataResp.data:type_name -> CustomContract.ContractDataResp.ContractInfo
	73, // 9: CustomContract.ContractViewResp.ViewUrls:type_name -> CustomContract.ContractViewResp.ViewUrlsData
	74, // 10: CustomContract.RepairFddContractUrlResp.data:type_name -> CustomContract.RepairFddContractUrlResp.Info
	75, // 11: CustomContract.SeriesOrderContractViewResp.contractUrls:type_name -> CustomContract.SeriesOrderContractViewResp.ContractUrlsEntry
	76, // 12: CustomContract.SeriesOrderContractSignResp.contractUrls:type_name -> CustomContract.SeriesOrderContractSignResp.ContractUrlsEntry
	1,  // 13: CustomContract.SignOnlineV2Req.userInfo:type_name -> CustomContract.UserInfo
	1,  // 14: CustomContract.UserInfoV2Resp.userInfo:type_name -> CustomContract.UserInfo
	1,  // 15: CustomContract.UpdateUserInfoV2Req.userInfo:type_name -> CustomContract.UserInfo
	1,  // 16: CustomContract.UpdateUserInfoV2Resp.userInfo:type_name -> CustomContract.UserInfo
	48, // 17: CustomContract.AuctionInfoV2Resp.auctionInfo:type_name -> CustomContract.AuctionInfo
	77, // 18: CustomContract.ContractViewV2Resp.ViewUrls:type_name -> CustomContract.ContractViewV2Resp.ViewUrlsData
	1,  // 19: CustomContract.UserContractNeedSignReq.userInfo:type_name -> CustomContract.UserInfo
	78, // 20: CustomContract.ArtworkContractViewResp.data:type_name -> CustomContract.ArtworkContractViewResp.DataEntry
	79, // 21: CustomContract.SignedAuctionListResp.data:type_name -> CustomContract.SignedAuctionListResp.Info
	61, // 22: CustomContract.SignedAuctionContractsResp.viewUrls:type_name -> CustomContract.ViewUrlsData
	80, // 23: CustomContract.SignInfoResp.Data:type_name -> CustomContract.SignInfoResp.Info
	56, // 24: CustomContract.ArtworkContractViewResp.DataEntry.value:type_name -> CustomContract.ContractNameInfo
	2,  // 25: CustomContract.CustomContract.ProtocolSignOffline:input_type -> CustomContract.ProtocolSignOfflineReq
	4,  // 26: CustomContract.CustomContract.ProtocolSignOnline:input_type -> CustomContract.ProtocolSignOnlineReq
	6,  // 27: CustomContract.CustomContract.TemplateData:input_type -> CustomContract.TemplateDataReq
	8,  // 28: CustomContract.CustomContract.UserInfo:input_type -> CustomContract.UserInfoReq
	10, // 29: CustomContract.CustomContract.UpdateUserInfo:input_type -> CustomContract.UpdateUserInfoReq
	12, // 30: CustomContract.CustomContract.UpdateContract:input_type -> CustomContract.UpdateContractReq
	14, // 31: CustomContract.CustomContract.UpdateContractBatch:input_type -> CustomContract.UpdateContractBatchReq
	16, // 32: CustomContract.CustomContract.UserContractData:input_type -> CustomContract.UserContractDataReq
	18, // 33: CustomContract.CustomContract.FddInfo:input_type -> CustomContract.FddInfoReq
	20, // 34: CustomContract.CustomContract.OfflineSignOrder:input_type -> CustomContract.OfflineSignOrderReq
	22, // 35: CustomContract.CustomContract.ArtworkBidData:input_type -> CustomContract.ArtworkBidDataReq
	24, // 36: CustomContract.CustomContract.ContractData:input_type -> CustomContract.ContractDataReq
	26, // 37: CustomContract.CustomContract.ContractView:input_type -> CustomContract.ContractViewReq
	28, // 38: CustomContract.CustomContract.UserInfoByPhone:input_type -> CustomContract.UserInfoByPhoneReq
	30, // 39: CustomContract.CustomContract.GetAuctionArtworkUuids:input_type -> CustomContract.GetAuctionArtworkUuidsReq
	32, // 40: CustomContract.CustomContract.SignView:input_type -> CustomContract.SignViewReq
	34, // 41: CustomContract.CustomContract.RepairFddContractUrl:input_type -> CustomContract.RepairFddContractUrlReq
	36, // 42: CustomContract.CustomContract.UserAuctionContractCount:input_type -> CustomContract.UserAuctionContractCountReq
	38, // 43: CustomContract.CustomContract.SeriesOrderContractView:input_type -> CustomContract.SeriesOrderContractViewReq
	40, // 44: CustomContract.CustomContract.SeriesOrderContractSign:input_type -> CustomContract.SeriesOrderContractSignReq
	42, // 45: CustomContract.CustomContract.SignOnlineV2:input_type -> CustomContract.SignOnlineV2Req
	44, // 46: CustomContract.CustomContract.UserInfoV2:input_type -> CustomContract.UserInfoV2Req
	46, // 47: CustomContract.CustomContract.UpdateUserInfoV2:input_type -> CustomContract.UpdateUserInfoV2Req
	49, // 48: CustomContract.CustomContract.AuctionInfoV2:input_type -> CustomContract.AuctionInfoV2Req
	51, // 49: CustomContract.CustomContract.ContractViewV2:input_type -> CustomContract.ContractViewV2Req
	53, // 50: CustomContract.CustomContract.UserContractNeedSign:input_type -> CustomContract.UserContractNeedSignReq
	55, // 51: CustomContract.CustomContract.ArtworkContractView:input_type -> CustomContract.ArtworkContractViewReq
	58, // 52: CustomContract.CustomContract.SignedAuctionList:input_type -> CustomContract.SignedAuctionListReq
	60, // 53: CustomContract.CustomContract.SignedAuctionContracts:input_type -> CustomContract.SignedAuctionContractsReq
	63, // 54: CustomContract.CustomContract.AuctionArtworkInfo:input_type -> CustomContract.AuctionArtworkInfoReq
	65, // 55: CustomContract.CustomContract.SignInfo:input_type -> CustomContract.SignInfoReq
	3,  // 56: CustomContract.CustomContract.ProtocolSignOffline:output_type -> CustomContract.ProtocolSignOfflineResp
	5,  // 57: CustomContract.CustomContract.ProtocolSignOnline:output_type -> CustomContract.ProtocolSignOnlineResp
	7,  // 58: CustomContract.CustomContract.TemplateData:output_type -> CustomContract.TemplateDataResp
	9,  // 59: CustomContract.CustomContract.UserInfo:output_type -> CustomContract.UserInfoReqResp
	11, // 60: CustomContract.CustomContract.UpdateUserInfo:output_type -> CustomContract.UpdateUserInfoResp
	13, // 61: CustomContract.CustomContract.UpdateContract:output_type -> CustomContract.UpdateContractResp
	15, // 62: CustomContract.CustomContract.UpdateContractBatch:output_type -> CustomContract.UpdateContractBatchResp
	17, // 63: CustomContract.CustomContract.UserContractData:output_type -> CustomContract.UserContractDataResp
	19, // 64: CustomContract.CustomContract.FddInfo:output_type -> CustomContract.FddInfoResp
	21, // 65: CustomContract.CustomContract.OfflineSignOrder:output_type -> CustomContract.OfflineSignOrderResp
	23, // 66: CustomContract.CustomContract.ArtworkBidData:output_type -> CustomContract.ArtworkBidDataResp
	25, // 67: CustomContract.CustomContract.ContractData:output_type -> CustomContract.ContractDataResp
	27, // 68: CustomContract.CustomContract.ContractView:output_type -> CustomContract.ContractViewResp
	29, // 69: CustomContract.CustomContract.UserInfoByPhone:output_type -> CustomContract.UserInfoByPhoneResp
	31, // 70: CustomContract.CustomContract.GetAuctionArtworkUuids:output_type -> CustomContract.GetAuctionArtworkUuidsResp
	33, // 71: CustomContract.CustomContract.SignView:output_type -> CustomContract.SignViewResp
	35, // 72: CustomContract.CustomContract.RepairFddContractUrl:output_type -> CustomContract.RepairFddContractUrlResp
	37, // 73: CustomContract.CustomContract.UserAuctionContractCount:output_type -> CustomContract.UserAuctionContractCountResp
	39, // 74: CustomContract.CustomContract.SeriesOrderContractView:output_type -> CustomContract.SeriesOrderContractViewResp
	41, // 75: CustomContract.CustomContract.SeriesOrderContractSign:output_type -> CustomContract.SeriesOrderContractSignResp
	43, // 76: CustomContract.CustomContract.SignOnlineV2:output_type -> CustomContract.SignOnlineV2Resp
	45, // 77: CustomContract.CustomContract.UserInfoV2:output_type -> CustomContract.UserInfoV2Resp
	47, // 78: CustomContract.CustomContract.UpdateUserInfoV2:output_type -> CustomContract.UpdateUserInfoV2Resp
	50, // 79: CustomContract.CustomContract.AuctionInfoV2:output_type -> CustomContract.AuctionInfoV2Resp
	52, // 80: CustomContract.CustomContract.ContractViewV2:output_type -> CustomContract.ContractViewV2Resp
	54, // 81: CustomContract.CustomContract.UserContractNeedSign:output_type -> CustomContract.UserContractNeedSignResp
	57, // 82: CustomContract.CustomContract.ArtworkContractView:output_type -> CustomContract.ArtworkContractViewResp
	59, // 83: CustomContract.CustomContract.SignedAuctionList:output_type -> CustomContract.SignedAuctionListResp
	62, // 84: CustomContract.CustomContract.SignedAuctionContracts:output_type -> CustomContract.SignedAuctionContractsResp
	64, // 85: CustomContract.CustomContract.AuctionArtworkInfo:output_type -> CustomContract.AuctionArtworkInfoResp
	66, // 86: CustomContract.CustomContract.SignInfo:output_type -> CustomContract.SignInfoResp
	56, // [56:87] is the sub-list for method output_type
	25, // [25:56] is the sub-list for method input_type
	25, // [25:25] is the sub-list for extension type_name
	25, // [25:25] is the sub-list for extension extendee
	0,  // [0:25] is the sub-list for field type_name
}

func init() { file_pb_custom_contract_proto_init() }
func file_pb_custom_contract_proto_init() {
	if File_pb_custom_contract_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_pb_custom_contract_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   80,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_pb_custom_contract_proto_goTypes,
		DependencyIndexes: file_pb_custom_contract_proto_depIdxs,
		EnumInfos:         file_pb_custom_contract_proto_enumTypes,
		MessageInfos:      file_pb_custom_contract_proto_msgTypes,
	}.Build()
	File_pb_custom_contract_proto = out.File
	file_pb_custom_contract_proto_rawDesc = nil
	file_pb_custom_contract_proto_goTypes = nil
	file_pb_custom_contract_proto_depIdxs = nil
}
