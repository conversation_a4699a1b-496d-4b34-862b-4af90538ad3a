// Code generated by protoc-gen-gogo. DO NOT EDIT.
// source: pb/custom_contract.proto

package custom_contract

import (
	fmt "fmt"
	math "math"
	proto "github.com/golang/protobuf/proto"
	_ "google.golang.org/protobuf/types/known/wrapperspb"
	_ "google.golang.org/protobuf/types/descriptorpb"
	_ "google.golang.org/protobuf/types/known/emptypb"
	_ "google.golang.org/protobuf/types/known/anypb"
	_ "github.com/mwitkow/go-proto-validators"
	github_com_mwitkow_go_proto_validators "github.com/mwitkow/go-proto-validators"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

func (this *UserInfo) Validate() error {
	return nil
}
func (this *ProtocolSignOfflineReq) Validate() error {
	if this.UserInfo != nil {
		if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(this.UserInfo); err != nil {
			return github_com_mwitkow_go_proto_validators.FieldError("UserInfo", err)
		}
	}
	return nil
}
func (this *ProtocolSignOfflineResp) Validate() error {
	return nil
}
func (this *ProtocolSignOnlineReq) Validate() error {
	if this.UserInfo != nil {
		if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(this.UserInfo); err != nil {
			return github_com_mwitkow_go_proto_validators.FieldError("UserInfo", err)
		}
	}
	return nil
}
func (this *ProtocolSignOnlineResp) Validate() error {
	if this.Data != nil {
		if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(this.Data); err != nil {
			return github_com_mwitkow_go_proto_validators.FieldError("Data", err)
		}
	}
	return nil
}
func (this *ProtocolSignOnlineResp_Info) Validate() error {
	return nil
}
func (this *TemplateDataReq) Validate() error {
	return nil
}
func (this *TemplateDataResp) Validate() error {
	for _, item := range this.Data {
		if item != nil {
			if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(item); err != nil {
				return github_com_mwitkow_go_proto_validators.FieldError("Data", err)
			}
		}
	}
	return nil
}
func (this *TemplateDataResp_Info) Validate() error {
	return nil
}
func (this *UserInfoReq) Validate() error {
	return nil
}
func (this *UserInfoReqResp) Validate() error {
	return nil
}
func (this *UpdateUserInfoReq) Validate() error {
	if this.UserInfo != nil {
		if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(this.UserInfo); err != nil {
			return github_com_mwitkow_go_proto_validators.FieldError("UserInfo", err)
		}
	}
	return nil
}
func (this *UpdateUserInfoResp) Validate() error {
	return nil
}
func (this *UpdateContractReq) Validate() error {
	return nil
}
func (this *UpdateContractResp) Validate() error {
	return nil
}
func (this *UpdateContractBatchReq) Validate() error {
	return nil
}
func (this *UpdateContractBatchResp) Validate() error {
	return nil
}
func (this *UserContractDataReq) Validate() error {
	return nil
}
func (this *UserContractDataResp) Validate() error {
	return nil
}
func (this *UserContractDataRespContractInfo) Validate() error {
	return nil
}
func (this *FddInfoReq) Validate() error {
	return nil
}
func (this *FddInfoResp) Validate() error {
	return nil
}
func (this *OfflineSignOrderReq) Validate() error {
	return nil
}
func (this *OfflineSignOrderResp) Validate() error {
	return nil
}
func (this *ArtworkBidDataReq) Validate() error {
	return nil
}
func (this *ArtworkBidDataResp) Validate() error {
	for _, item := range this.BigData {
		if item != nil {
			if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(item); err != nil {
				return github_com_mwitkow_go_proto_validators.FieldError("BigData", err)
			}
		}
	}
	if this.AuctionData != nil {
		if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(this.AuctionData); err != nil {
			return github_com_mwitkow_go_proto_validators.FieldError("AuctionData", err)
		}
	}
	return nil
}
func (this *ArtworkBidDataResp_AuctionInfo) Validate() error {
	return nil
}
func (this *ArtworkBidDataResp_BigInfo) Validate() error {
	return nil
}
func (this *ContractDataReq) Validate() error {
	return nil
}
func (this *ContractDataResp) Validate() error {
	for _, item := range this.Data {
		if item != nil {
			if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(item); err != nil {
				return github_com_mwitkow_go_proto_validators.FieldError("Data", err)
			}
		}
	}
	return nil
}
func (this *ContractDataResp_ContractInfo) Validate() error {
	return nil
}
func (this *ContractViewReq) Validate() error {
	return nil
}
func (this *ContractViewResp) Validate() error {
	if this.ViewUrls != nil {
		if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(this.ViewUrls); err != nil {
			return github_com_mwitkow_go_proto_validators.FieldError("ViewUrls", err)
		}
	}
	return nil
}
func (this *ContractViewResp_ViewUrlsData) Validate() error {
	return nil
}
func (this *UserInfoByPhoneReq) Validate() error {
	return nil
}
func (this *UserInfoByPhoneResp) Validate() error {
	return nil
}
func (this *GetAuctionArtworkUuidsReq) Validate() error {
	return nil
}
func (this *GetAuctionArtworkUuidsResp) Validate() error {
	return nil
}
func (this *SignViewReq) Validate() error {
	return nil
}
func (this *SignViewResp) Validate() error {
	return nil
}
func (this *RepairFddContractUrlReq) Validate() error {
	return nil
}
func (this *RepairFddContractUrlResp) Validate() error {
	for _, item := range this.Data {
		if item != nil {
			if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(item); err != nil {
				return github_com_mwitkow_go_proto_validators.FieldError("Data", err)
			}
		}
	}
	return nil
}
func (this *RepairFddContractUrlResp_Info) Validate() error {
	return nil
}
func (this *UserAuctionContractCountReq) Validate() error {
	return nil
}
func (this *UserAuctionContractCountResp) Validate() error {
	return nil
}
func (this *SeriesOrderContractViewReq) Validate() error {
	return nil
}
func (this *SeriesOrderContractViewResp) Validate() error {
	// Validation of proto3 map<> fields is unsupported.
	return nil
}
func (this *SeriesOrderContractSignReq) Validate() error {
	return nil
}
func (this *SeriesOrderContractSignResp) Validate() error {
	// Validation of proto3 map<> fields is unsupported.
	return nil
}
func (this *SignOnlineV2Req) Validate() error {
	if this.UserInfo != nil {
		if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(this.UserInfo); err != nil {
			return github_com_mwitkow_go_proto_validators.FieldError("UserInfo", err)
		}
	}
	return nil
}
func (this *SignOnlineV2Resp) Validate() error {
	return nil
}
func (this *UserInfoV2Req) Validate() error {
	return nil
}
func (this *UserInfoV2Resp) Validate() error {
	if this.UserInfo != nil {
		if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(this.UserInfo); err != nil {
			return github_com_mwitkow_go_proto_validators.FieldError("UserInfo", err)
		}
	}
	return nil
}
func (this *UpdateUserInfoV2Req) Validate() error {
	if this.UserInfo != nil {
		if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(this.UserInfo); err != nil {
			return github_com_mwitkow_go_proto_validators.FieldError("UserInfo", err)
		}
	}
	return nil
}
func (this *UpdateUserInfoV2Resp) Validate() error {
	if this.UserInfo != nil {
		if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(this.UserInfo); err != nil {
			return github_com_mwitkow_go_proto_validators.FieldError("UserInfo", err)
		}
	}
	return nil
}
func (this *AuctionInfo) Validate() error {
	return nil
}
func (this *AuctionInfoV2Req) Validate() error {
	return nil
}
func (this *AuctionInfoV2Resp) Validate() error {
	if this.AuctionInfo != nil {
		if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(this.AuctionInfo); err != nil {
			return github_com_mwitkow_go_proto_validators.FieldError("AuctionInfo", err)
		}
	}
	return nil
}
func (this *ContractViewV2Req) Validate() error {
	return nil
}
func (this *ContractViewV2Resp) Validate() error {
	if this.ViewUrls != nil {
		if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(this.ViewUrls); err != nil {
			return github_com_mwitkow_go_proto_validators.FieldError("ViewUrls", err)
		}
	}
	return nil
}
func (this *ContractViewV2Resp_ViewUrlsData) Validate() error {
	return nil
}
func (this *UserContractNeedSignReq) Validate() error {
	if this.UserInfo != nil {
		if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(this.UserInfo); err != nil {
			return github_com_mwitkow_go_proto_validators.FieldError("UserInfo", err)
		}
	}
	return nil
}
func (this *UserContractNeedSignResp) Validate() error {
	return nil
}
func (this *ArtworkContractViewReq) Validate() error {
	return nil
}
func (this *ContractNameInfo) Validate() error {
	return nil
}
func (this *ArtworkContractViewResp) Validate() error {
	// Validation of proto3 map<> fields is unsupported.
	return nil
}
func (this *SignedAuctionListReq) Validate() error {
	return nil
}
func (this *SignedAuctionListResp) Validate() error {
	for _, item := range this.Data {
		if item != nil {
			if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(item); err != nil {
				return github_com_mwitkow_go_proto_validators.FieldError("Data", err)
			}
		}
	}
	return nil
}
func (this *SignedAuctionListResp_Info) Validate() error {
	return nil
}
func (this *SignedAuctionContractsReq) Validate() error {
	return nil
}
func (this *ViewUrlsData) Validate() error {
	return nil
}
func (this *SignedAuctionContractsResp) Validate() error {
	if this.ViewUrls != nil {
		if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(this.ViewUrls); err != nil {
			return github_com_mwitkow_go_proto_validators.FieldError("ViewUrls", err)
		}
	}
	return nil
}
func (this *AuctionArtworkInfoReq) Validate() error {
	return nil
}
func (this *AuctionArtworkInfoResp) Validate() error {
	return nil
}
func (this *SignInfoReq) Validate() error {
	return nil
}
func (this *SignInfoResp) Validate() error {
	for _, item := range this.Data {
		if item != nil {
			if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(item); err != nil {
				return github_com_mwitkow_go_proto_validators.FieldError("Data", err)
			}
		}
	}
	return nil
}
func (this *SignInfoResp_Info) Validate() error {
	return nil
}
