// Code generated by protoc-gen-go-triple. DO NOT EDIT.
// versions:
// - protoc-gen-go-triple v1.0.8
// - protoc             v3.21.1
// source: pb/custom_contract.proto

package custom_contract

import (
	context "context"
	protocol "dubbo.apache.org/dubbo-go/v3/protocol"
	dubbo3 "dubbo.apache.org/dubbo-go/v3/protocol/dubbo3"
	invocation "dubbo.apache.org/dubbo-go/v3/protocol/invocation"
	grpc_go "github.com/dubbogo/grpc-go"
	codes "github.com/dubbogo/grpc-go/codes"
	metadata "github.com/dubbogo/grpc-go/metadata"
	status "github.com/dubbogo/grpc-go/status"
	common "github.com/dubbogo/triple/pkg/common"
	constant "github.com/dubbogo/triple/pkg/common/constant"
	triple "github.com/dubbogo/triple/pkg/triple"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc_go.SupportPackageIsVersion7

// CustomContractClient is the client API for CustomContract service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type CustomContractClient interface {
	ProtocolSignOffline(ctx context.Context, in *ProtocolSignOfflineReq, opts ...grpc_go.CallOption) (*ProtocolSignOfflineResp, common.ErrorWithAttachment)
	ProtocolSignOnline(ctx context.Context, in *ProtocolSignOnlineReq, opts ...grpc_go.CallOption) (*ProtocolSignOnlineResp, common.ErrorWithAttachment)
	TemplateData(ctx context.Context, in *TemplateDataReq, opts ...grpc_go.CallOption) (*TemplateDataResp, common.ErrorWithAttachment)
	UserInfo(ctx context.Context, in *UserInfoReq, opts ...grpc_go.CallOption) (*UserInfoReqResp, common.ErrorWithAttachment)
	UpdateUserInfo(ctx context.Context, in *UpdateUserInfoReq, opts ...grpc_go.CallOption) (*UpdateUserInfoResp, common.ErrorWithAttachment)
	UpdateContract(ctx context.Context, in *UpdateContractReq, opts ...grpc_go.CallOption) (*UpdateContractResp, common.ErrorWithAttachment)
	UpdateContractBatch(ctx context.Context, in *UpdateContractBatchReq, opts ...grpc_go.CallOption) (*UpdateContractBatchResp, common.ErrorWithAttachment)
	UserContractData(ctx context.Context, in *UserContractDataReq, opts ...grpc_go.CallOption) (*UserContractDataResp, common.ErrorWithAttachment)
	FddInfo(ctx context.Context, in *FddInfoReq, opts ...grpc_go.CallOption) (*FddInfoResp, common.ErrorWithAttachment)
	OfflineSignOrder(ctx context.Context, in *OfflineSignOrderReq, opts ...grpc_go.CallOption) (*OfflineSignOrderResp, common.ErrorWithAttachment)
	ArtworkBidData(ctx context.Context, in *ArtworkBidDataReq, opts ...grpc_go.CallOption) (*ArtworkBidDataResp, common.ErrorWithAttachment)
	ContractData(ctx context.Context, in *ContractDataReq, opts ...grpc_go.CallOption) (*ContractDataResp, common.ErrorWithAttachment)
	ContractView(ctx context.Context, in *ContractViewReq, opts ...grpc_go.CallOption) (*ContractViewResp, common.ErrorWithAttachment)
	UserInfoByPhone(ctx context.Context, in *UserInfoByPhoneReq, opts ...grpc_go.CallOption) (*UserInfoByPhoneResp, common.ErrorWithAttachment)
	GetAuctionArtworkUuids(ctx context.Context, in *GetAuctionArtworkUuidsReq, opts ...grpc_go.CallOption) (*GetAuctionArtworkUuidsResp, common.ErrorWithAttachment)
	SignView(ctx context.Context, in *SignViewReq, opts ...grpc_go.CallOption) (*SignViewResp, common.ErrorWithAttachment)
	RepairFddContractUrl(ctx context.Context, in *RepairFddContractUrlReq, opts ...grpc_go.CallOption) (*RepairFddContractUrlResp, common.ErrorWithAttachment)
	UserAuctionContractCount(ctx context.Context, in *UserAuctionContractCountReq, opts ...grpc_go.CallOption) (*UserAuctionContractCountResp, common.ErrorWithAttachment)
	SeriesOrderContractView(ctx context.Context, in *SeriesOrderContractViewReq, opts ...grpc_go.CallOption) (*SeriesOrderContractViewResp, common.ErrorWithAttachment)
	SeriesOrderContractSign(ctx context.Context, in *SeriesOrderContractSignReq, opts ...grpc_go.CallOption) (*SeriesOrderContractSignResp, common.ErrorWithAttachment)
	SignOnlineV2(ctx context.Context, in *SignOnlineV2Req, opts ...grpc_go.CallOption) (*SignOnlineV2Resp, common.ErrorWithAttachment)
	UserInfoV2(ctx context.Context, in *UserInfoV2Req, opts ...grpc_go.CallOption) (*UserInfoV2Resp, common.ErrorWithAttachment)
	UpdateUserInfoV2(ctx context.Context, in *UpdateUserInfoV2Req, opts ...grpc_go.CallOption) (*UpdateUserInfoV2Resp, common.ErrorWithAttachment)
	AuctionInfoV2(ctx context.Context, in *AuctionInfoV2Req, opts ...grpc_go.CallOption) (*AuctionInfoV2Resp, common.ErrorWithAttachment)
	ContractViewV2(ctx context.Context, in *ContractViewV2Req, opts ...grpc_go.CallOption) (*ContractViewV2Resp, common.ErrorWithAttachment)
	UserContractNeedSign(ctx context.Context, in *UserContractNeedSignReq, opts ...grpc_go.CallOption) (*UserContractNeedSignResp, common.ErrorWithAttachment)
	ArtworkContractView(ctx context.Context, in *ArtworkContractViewReq, opts ...grpc_go.CallOption) (*ArtworkContractViewResp, common.ErrorWithAttachment)
	SignedAuctionList(ctx context.Context, in *SignedAuctionListReq, opts ...grpc_go.CallOption) (*SignedAuctionListResp, common.ErrorWithAttachment)
	SignedAuctionContracts(ctx context.Context, in *SignedAuctionContractsReq, opts ...grpc_go.CallOption) (*SignedAuctionContractsResp, common.ErrorWithAttachment)
	AuctionArtworkInfo(ctx context.Context, in *AuctionArtworkInfoReq, opts ...grpc_go.CallOption) (*AuctionArtworkInfoResp, common.ErrorWithAttachment)
	SignInfo(ctx context.Context, in *SignInfoReq, opts ...grpc_go.CallOption) (*SignInfoResp, common.ErrorWithAttachment)
}

type customContractClient struct {
	cc *triple.TripleConn
}

type CustomContractClientImpl struct {
	ProtocolSignOffline      func(ctx context.Context, in *ProtocolSignOfflineReq) (*ProtocolSignOfflineResp, error)
	ProtocolSignOnline       func(ctx context.Context, in *ProtocolSignOnlineReq) (*ProtocolSignOnlineResp, error)
	TemplateData             func(ctx context.Context, in *TemplateDataReq) (*TemplateDataResp, error)
	UserInfo                 func(ctx context.Context, in *UserInfoReq) (*UserInfoReqResp, error)
	UpdateUserInfo           func(ctx context.Context, in *UpdateUserInfoReq) (*UpdateUserInfoResp, error)
	UpdateContract           func(ctx context.Context, in *UpdateContractReq) (*UpdateContractResp, error)
	UpdateContractBatch      func(ctx context.Context, in *UpdateContractBatchReq) (*UpdateContractBatchResp, error)
	UserContractData         func(ctx context.Context, in *UserContractDataReq) (*UserContractDataResp, error)
	FddInfo                  func(ctx context.Context, in *FddInfoReq) (*FddInfoResp, error)
	OfflineSignOrder         func(ctx context.Context, in *OfflineSignOrderReq) (*OfflineSignOrderResp, error)
	ArtworkBidData           func(ctx context.Context, in *ArtworkBidDataReq) (*ArtworkBidDataResp, error)
	ContractData             func(ctx context.Context, in *ContractDataReq) (*ContractDataResp, error)
	ContractView             func(ctx context.Context, in *ContractViewReq) (*ContractViewResp, error)
	UserInfoByPhone          func(ctx context.Context, in *UserInfoByPhoneReq) (*UserInfoByPhoneResp, error)
	GetAuctionArtworkUuids   func(ctx context.Context, in *GetAuctionArtworkUuidsReq) (*GetAuctionArtworkUuidsResp, error)
	SignView                 func(ctx context.Context, in *SignViewReq) (*SignViewResp, error)
	RepairFddContractUrl     func(ctx context.Context, in *RepairFddContractUrlReq) (*RepairFddContractUrlResp, error)
	UserAuctionContractCount func(ctx context.Context, in *UserAuctionContractCountReq) (*UserAuctionContractCountResp, error)
	SeriesOrderContractView  func(ctx context.Context, in *SeriesOrderContractViewReq) (*SeriesOrderContractViewResp, error)
	SeriesOrderContractSign  func(ctx context.Context, in *SeriesOrderContractSignReq) (*SeriesOrderContractSignResp, error)
	SignOnlineV2             func(ctx context.Context, in *SignOnlineV2Req) (*SignOnlineV2Resp, error)
	UserInfoV2               func(ctx context.Context, in *UserInfoV2Req) (*UserInfoV2Resp, error)
	UpdateUserInfoV2         func(ctx context.Context, in *UpdateUserInfoV2Req) (*UpdateUserInfoV2Resp, error)
	AuctionInfoV2            func(ctx context.Context, in *AuctionInfoV2Req) (*AuctionInfoV2Resp, error)
	ContractViewV2           func(ctx context.Context, in *ContractViewV2Req) (*ContractViewV2Resp, error)
	UserContractNeedSign     func(ctx context.Context, in *UserContractNeedSignReq) (*UserContractNeedSignResp, error)
	ArtworkContractView      func(ctx context.Context, in *ArtworkContractViewReq) (*ArtworkContractViewResp, error)
	SignedAuctionList        func(ctx context.Context, in *SignedAuctionListReq) (*SignedAuctionListResp, error)
	SignedAuctionContracts   func(ctx context.Context, in *SignedAuctionContractsReq) (*SignedAuctionContractsResp, error)
	AuctionArtworkInfo       func(ctx context.Context, in *AuctionArtworkInfoReq) (*AuctionArtworkInfoResp, error)
	SignInfo                 func(ctx context.Context, in *SignInfoReq) (*SignInfoResp, error)
}

func (c *CustomContractClientImpl) GetDubboStub(cc *triple.TripleConn) CustomContractClient {
	return NewCustomContractClient(cc)
}

func (c *CustomContractClientImpl) XXX_InterfaceName() string {
	return "CustomContract.CustomContract"
}

func NewCustomContractClient(cc *triple.TripleConn) CustomContractClient {
	return &customContractClient{cc}
}

func (c *customContractClient) ProtocolSignOffline(ctx context.Context, in *ProtocolSignOfflineReq, opts ...grpc_go.CallOption) (*ProtocolSignOfflineResp, common.ErrorWithAttachment) {
	out := new(ProtocolSignOfflineResp)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/ProtocolSignOffline", in, out)
}

func (c *customContractClient) ProtocolSignOnline(ctx context.Context, in *ProtocolSignOnlineReq, opts ...grpc_go.CallOption) (*ProtocolSignOnlineResp, common.ErrorWithAttachment) {
	out := new(ProtocolSignOnlineResp)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/ProtocolSignOnline", in, out)
}

func (c *customContractClient) TemplateData(ctx context.Context, in *TemplateDataReq, opts ...grpc_go.CallOption) (*TemplateDataResp, common.ErrorWithAttachment) {
	out := new(TemplateDataResp)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/TemplateData", in, out)
}

func (c *customContractClient) UserInfo(ctx context.Context, in *UserInfoReq, opts ...grpc_go.CallOption) (*UserInfoReqResp, common.ErrorWithAttachment) {
	out := new(UserInfoReqResp)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/UserInfo", in, out)
}

func (c *customContractClient) UpdateUserInfo(ctx context.Context, in *UpdateUserInfoReq, opts ...grpc_go.CallOption) (*UpdateUserInfoResp, common.ErrorWithAttachment) {
	out := new(UpdateUserInfoResp)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/UpdateUserInfo", in, out)
}

func (c *customContractClient) UpdateContract(ctx context.Context, in *UpdateContractReq, opts ...grpc_go.CallOption) (*UpdateContractResp, common.ErrorWithAttachment) {
	out := new(UpdateContractResp)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/UpdateContract", in, out)
}

func (c *customContractClient) UpdateContractBatch(ctx context.Context, in *UpdateContractBatchReq, opts ...grpc_go.CallOption) (*UpdateContractBatchResp, common.ErrorWithAttachment) {
	out := new(UpdateContractBatchResp)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/UpdateContractBatch", in, out)
}

func (c *customContractClient) UserContractData(ctx context.Context, in *UserContractDataReq, opts ...grpc_go.CallOption) (*UserContractDataResp, common.ErrorWithAttachment) {
	out := new(UserContractDataResp)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/UserContractData", in, out)
}

func (c *customContractClient) FddInfo(ctx context.Context, in *FddInfoReq, opts ...grpc_go.CallOption) (*FddInfoResp, common.ErrorWithAttachment) {
	out := new(FddInfoResp)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/FddInfo", in, out)
}

func (c *customContractClient) OfflineSignOrder(ctx context.Context, in *OfflineSignOrderReq, opts ...grpc_go.CallOption) (*OfflineSignOrderResp, common.ErrorWithAttachment) {
	out := new(OfflineSignOrderResp)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/OfflineSignOrder", in, out)
}

func (c *customContractClient) ArtworkBidData(ctx context.Context, in *ArtworkBidDataReq, opts ...grpc_go.CallOption) (*ArtworkBidDataResp, common.ErrorWithAttachment) {
	out := new(ArtworkBidDataResp)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/ArtworkBidData", in, out)
}

func (c *customContractClient) ContractData(ctx context.Context, in *ContractDataReq, opts ...grpc_go.CallOption) (*ContractDataResp, common.ErrorWithAttachment) {
	out := new(ContractDataResp)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/ContractData", in, out)
}

func (c *customContractClient) ContractView(ctx context.Context, in *ContractViewReq, opts ...grpc_go.CallOption) (*ContractViewResp, common.ErrorWithAttachment) {
	out := new(ContractViewResp)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/ContractView", in, out)
}

func (c *customContractClient) UserInfoByPhone(ctx context.Context, in *UserInfoByPhoneReq, opts ...grpc_go.CallOption) (*UserInfoByPhoneResp, common.ErrorWithAttachment) {
	out := new(UserInfoByPhoneResp)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/UserInfoByPhone", in, out)
}

func (c *customContractClient) GetAuctionArtworkUuids(ctx context.Context, in *GetAuctionArtworkUuidsReq, opts ...grpc_go.CallOption) (*GetAuctionArtworkUuidsResp, common.ErrorWithAttachment) {
	out := new(GetAuctionArtworkUuidsResp)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/GetAuctionArtworkUuids", in, out)
}

func (c *customContractClient) SignView(ctx context.Context, in *SignViewReq, opts ...grpc_go.CallOption) (*SignViewResp, common.ErrorWithAttachment) {
	out := new(SignViewResp)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/SignView", in, out)
}

func (c *customContractClient) RepairFddContractUrl(ctx context.Context, in *RepairFddContractUrlReq, opts ...grpc_go.CallOption) (*RepairFddContractUrlResp, common.ErrorWithAttachment) {
	out := new(RepairFddContractUrlResp)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/RepairFddContractUrl", in, out)
}

func (c *customContractClient) UserAuctionContractCount(ctx context.Context, in *UserAuctionContractCountReq, opts ...grpc_go.CallOption) (*UserAuctionContractCountResp, common.ErrorWithAttachment) {
	out := new(UserAuctionContractCountResp)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/UserAuctionContractCount", in, out)
}

func (c *customContractClient) SeriesOrderContractView(ctx context.Context, in *SeriesOrderContractViewReq, opts ...grpc_go.CallOption) (*SeriesOrderContractViewResp, common.ErrorWithAttachment) {
	out := new(SeriesOrderContractViewResp)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/SeriesOrderContractView", in, out)
}

func (c *customContractClient) SeriesOrderContractSign(ctx context.Context, in *SeriesOrderContractSignReq, opts ...grpc_go.CallOption) (*SeriesOrderContractSignResp, common.ErrorWithAttachment) {
	out := new(SeriesOrderContractSignResp)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/SeriesOrderContractSign", in, out)
}

func (c *customContractClient) SignOnlineV2(ctx context.Context, in *SignOnlineV2Req, opts ...grpc_go.CallOption) (*SignOnlineV2Resp, common.ErrorWithAttachment) {
	out := new(SignOnlineV2Resp)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/SignOnlineV2", in, out)
}

func (c *customContractClient) UserInfoV2(ctx context.Context, in *UserInfoV2Req, opts ...grpc_go.CallOption) (*UserInfoV2Resp, common.ErrorWithAttachment) {
	out := new(UserInfoV2Resp)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/UserInfoV2", in, out)
}

func (c *customContractClient) UpdateUserInfoV2(ctx context.Context, in *UpdateUserInfoV2Req, opts ...grpc_go.CallOption) (*UpdateUserInfoV2Resp, common.ErrorWithAttachment) {
	out := new(UpdateUserInfoV2Resp)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/UpdateUserInfoV2", in, out)
}

func (c *customContractClient) AuctionInfoV2(ctx context.Context, in *AuctionInfoV2Req, opts ...grpc_go.CallOption) (*AuctionInfoV2Resp, common.ErrorWithAttachment) {
	out := new(AuctionInfoV2Resp)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/AuctionInfoV2", in, out)
}

func (c *customContractClient) ContractViewV2(ctx context.Context, in *ContractViewV2Req, opts ...grpc_go.CallOption) (*ContractViewV2Resp, common.ErrorWithAttachment) {
	out := new(ContractViewV2Resp)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/ContractViewV2", in, out)
}

func (c *customContractClient) UserContractNeedSign(ctx context.Context, in *UserContractNeedSignReq, opts ...grpc_go.CallOption) (*UserContractNeedSignResp, common.ErrorWithAttachment) {
	out := new(UserContractNeedSignResp)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/UserContractNeedSign", in, out)
}

func (c *customContractClient) ArtworkContractView(ctx context.Context, in *ArtworkContractViewReq, opts ...grpc_go.CallOption) (*ArtworkContractViewResp, common.ErrorWithAttachment) {
	out := new(ArtworkContractViewResp)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/ArtworkContractView", in, out)
}

func (c *customContractClient) SignedAuctionList(ctx context.Context, in *SignedAuctionListReq, opts ...grpc_go.CallOption) (*SignedAuctionListResp, common.ErrorWithAttachment) {
	out := new(SignedAuctionListResp)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/SignedAuctionList", in, out)
}

func (c *customContractClient) SignedAuctionContracts(ctx context.Context, in *SignedAuctionContractsReq, opts ...grpc_go.CallOption) (*SignedAuctionContractsResp, common.ErrorWithAttachment) {
	out := new(SignedAuctionContractsResp)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/SignedAuctionContracts", in, out)
}

func (c *customContractClient) AuctionArtworkInfo(ctx context.Context, in *AuctionArtworkInfoReq, opts ...grpc_go.CallOption) (*AuctionArtworkInfoResp, common.ErrorWithAttachment) {
	out := new(AuctionArtworkInfoResp)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/AuctionArtworkInfo", in, out)
}

func (c *customContractClient) SignInfo(ctx context.Context, in *SignInfoReq, opts ...grpc_go.CallOption) (*SignInfoResp, common.ErrorWithAttachment) {
	out := new(SignInfoResp)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/SignInfo", in, out)
}

// CustomContractServer is the server API for CustomContract service.
// All implementations must embed UnimplementedCustomContractServer
// for forward compatibility
type CustomContractServer interface {
	ProtocolSignOffline(context.Context, *ProtocolSignOfflineReq) (*ProtocolSignOfflineResp, error)
	ProtocolSignOnline(context.Context, *ProtocolSignOnlineReq) (*ProtocolSignOnlineResp, error)
	TemplateData(context.Context, *TemplateDataReq) (*TemplateDataResp, error)
	UserInfo(context.Context, *UserInfoReq) (*UserInfoReqResp, error)
	UpdateUserInfo(context.Context, *UpdateUserInfoReq) (*UpdateUserInfoResp, error)
	UpdateContract(context.Context, *UpdateContractReq) (*UpdateContractResp, error)
	UpdateContractBatch(context.Context, *UpdateContractBatchReq) (*UpdateContractBatchResp, error)
	UserContractData(context.Context, *UserContractDataReq) (*UserContractDataResp, error)
	FddInfo(context.Context, *FddInfoReq) (*FddInfoResp, error)
	OfflineSignOrder(context.Context, *OfflineSignOrderReq) (*OfflineSignOrderResp, error)
	ArtworkBidData(context.Context, *ArtworkBidDataReq) (*ArtworkBidDataResp, error)
	ContractData(context.Context, *ContractDataReq) (*ContractDataResp, error)
	ContractView(context.Context, *ContractViewReq) (*ContractViewResp, error)
	UserInfoByPhone(context.Context, *UserInfoByPhoneReq) (*UserInfoByPhoneResp, error)
	GetAuctionArtworkUuids(context.Context, *GetAuctionArtworkUuidsReq) (*GetAuctionArtworkUuidsResp, error)
	SignView(context.Context, *SignViewReq) (*SignViewResp, error)
	RepairFddContractUrl(context.Context, *RepairFddContractUrlReq) (*RepairFddContractUrlResp, error)
	UserAuctionContractCount(context.Context, *UserAuctionContractCountReq) (*UserAuctionContractCountResp, error)
	SeriesOrderContractView(context.Context, *SeriesOrderContractViewReq) (*SeriesOrderContractViewResp, error)
	SeriesOrderContractSign(context.Context, *SeriesOrderContractSignReq) (*SeriesOrderContractSignResp, error)
	SignOnlineV2(context.Context, *SignOnlineV2Req) (*SignOnlineV2Resp, error)
	UserInfoV2(context.Context, *UserInfoV2Req) (*UserInfoV2Resp, error)
	UpdateUserInfoV2(context.Context, *UpdateUserInfoV2Req) (*UpdateUserInfoV2Resp, error)
	AuctionInfoV2(context.Context, *AuctionInfoV2Req) (*AuctionInfoV2Resp, error)
	ContractViewV2(context.Context, *ContractViewV2Req) (*ContractViewV2Resp, error)
	UserContractNeedSign(context.Context, *UserContractNeedSignReq) (*UserContractNeedSignResp, error)
	ArtworkContractView(context.Context, *ArtworkContractViewReq) (*ArtworkContractViewResp, error)
	SignedAuctionList(context.Context, *SignedAuctionListReq) (*SignedAuctionListResp, error)
	SignedAuctionContracts(context.Context, *SignedAuctionContractsReq) (*SignedAuctionContractsResp, error)
	AuctionArtworkInfo(context.Context, *AuctionArtworkInfoReq) (*AuctionArtworkInfoResp, error)
	SignInfo(context.Context, *SignInfoReq) (*SignInfoResp, error)
	mustEmbedUnimplementedCustomContractServer()
}

// UnimplementedCustomContractServer must be embedded to have forward compatible implementations.
type UnimplementedCustomContractServer struct {
	proxyImpl protocol.Invoker
}

func (UnimplementedCustomContractServer) ProtocolSignOffline(context.Context, *ProtocolSignOfflineReq) (*ProtocolSignOfflineResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ProtocolSignOffline not implemented")
}
func (UnimplementedCustomContractServer) ProtocolSignOnline(context.Context, *ProtocolSignOnlineReq) (*ProtocolSignOnlineResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ProtocolSignOnline not implemented")
}
func (UnimplementedCustomContractServer) TemplateData(context.Context, *TemplateDataReq) (*TemplateDataResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method TemplateData not implemented")
}
func (UnimplementedCustomContractServer) UserInfo(context.Context, *UserInfoReq) (*UserInfoReqResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UserInfo not implemented")
}
func (UnimplementedCustomContractServer) UpdateUserInfo(context.Context, *UpdateUserInfoReq) (*UpdateUserInfoResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateUserInfo not implemented")
}
func (UnimplementedCustomContractServer) UpdateContract(context.Context, *UpdateContractReq) (*UpdateContractResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateContract not implemented")
}
func (UnimplementedCustomContractServer) UpdateContractBatch(context.Context, *UpdateContractBatchReq) (*UpdateContractBatchResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateContractBatch not implemented")
}
func (UnimplementedCustomContractServer) UserContractData(context.Context, *UserContractDataReq) (*UserContractDataResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UserContractData not implemented")
}
func (UnimplementedCustomContractServer) FddInfo(context.Context, *FddInfoReq) (*FddInfoResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FddInfo not implemented")
}
func (UnimplementedCustomContractServer) OfflineSignOrder(context.Context, *OfflineSignOrderReq) (*OfflineSignOrderResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method OfflineSignOrder not implemented")
}
func (UnimplementedCustomContractServer) ArtworkBidData(context.Context, *ArtworkBidDataReq) (*ArtworkBidDataResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ArtworkBidData not implemented")
}
func (UnimplementedCustomContractServer) ContractData(context.Context, *ContractDataReq) (*ContractDataResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ContractData not implemented")
}
func (UnimplementedCustomContractServer) ContractView(context.Context, *ContractViewReq) (*ContractViewResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ContractView not implemented")
}
func (UnimplementedCustomContractServer) UserInfoByPhone(context.Context, *UserInfoByPhoneReq) (*UserInfoByPhoneResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UserInfoByPhone not implemented")
}
func (UnimplementedCustomContractServer) GetAuctionArtworkUuids(context.Context, *GetAuctionArtworkUuidsReq) (*GetAuctionArtworkUuidsResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetAuctionArtworkUuids not implemented")
}
func (UnimplementedCustomContractServer) SignView(context.Context, *SignViewReq) (*SignViewResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SignView not implemented")
}
func (UnimplementedCustomContractServer) RepairFddContractUrl(context.Context, *RepairFddContractUrlReq) (*RepairFddContractUrlResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RepairFddContractUrl not implemented")
}
func (UnimplementedCustomContractServer) UserAuctionContractCount(context.Context, *UserAuctionContractCountReq) (*UserAuctionContractCountResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UserAuctionContractCount not implemented")
}
func (UnimplementedCustomContractServer) SeriesOrderContractView(context.Context, *SeriesOrderContractViewReq) (*SeriesOrderContractViewResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SeriesOrderContractView not implemented")
}
func (UnimplementedCustomContractServer) SeriesOrderContractSign(context.Context, *SeriesOrderContractSignReq) (*SeriesOrderContractSignResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SeriesOrderContractSign not implemented")
}
func (UnimplementedCustomContractServer) SignOnlineV2(context.Context, *SignOnlineV2Req) (*SignOnlineV2Resp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SignOnlineV2 not implemented")
}
func (UnimplementedCustomContractServer) UserInfoV2(context.Context, *UserInfoV2Req) (*UserInfoV2Resp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UserInfoV2 not implemented")
}
func (UnimplementedCustomContractServer) UpdateUserInfoV2(context.Context, *UpdateUserInfoV2Req) (*UpdateUserInfoV2Resp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateUserInfoV2 not implemented")
}
func (UnimplementedCustomContractServer) AuctionInfoV2(context.Context, *AuctionInfoV2Req) (*AuctionInfoV2Resp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AuctionInfoV2 not implemented")
}
func (UnimplementedCustomContractServer) ContractViewV2(context.Context, *ContractViewV2Req) (*ContractViewV2Resp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ContractViewV2 not implemented")
}
func (UnimplementedCustomContractServer) UserContractNeedSign(context.Context, *UserContractNeedSignReq) (*UserContractNeedSignResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UserContractNeedSign not implemented")
}
func (UnimplementedCustomContractServer) ArtworkContractView(context.Context, *ArtworkContractViewReq) (*ArtworkContractViewResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ArtworkContractView not implemented")
}
func (UnimplementedCustomContractServer) SignedAuctionList(context.Context, *SignedAuctionListReq) (*SignedAuctionListResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SignedAuctionList not implemented")
}
func (UnimplementedCustomContractServer) SignedAuctionContracts(context.Context, *SignedAuctionContractsReq) (*SignedAuctionContractsResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SignedAuctionContracts not implemented")
}
func (UnimplementedCustomContractServer) AuctionArtworkInfo(context.Context, *AuctionArtworkInfoReq) (*AuctionArtworkInfoResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AuctionArtworkInfo not implemented")
}
func (UnimplementedCustomContractServer) SignInfo(context.Context, *SignInfoReq) (*SignInfoResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SignInfo not implemented")
}
func (s *UnimplementedCustomContractServer) XXX_SetProxyImpl(impl protocol.Invoker) {
	s.proxyImpl = impl
}

func (s *UnimplementedCustomContractServer) XXX_GetProxyImpl() protocol.Invoker {
	return s.proxyImpl
}

func (s *UnimplementedCustomContractServer) XXX_ServiceDesc() *grpc_go.ServiceDesc {
	return &CustomContract_ServiceDesc
}
func (s *UnimplementedCustomContractServer) XXX_InterfaceName() string {
	return "CustomContract.CustomContract"
}

func (UnimplementedCustomContractServer) mustEmbedUnimplementedCustomContractServer() {}

// UnsafeCustomContractServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to CustomContractServer will
// result in compilation errors.
type UnsafeCustomContractServer interface {
	mustEmbedUnimplementedCustomContractServer()
}

func RegisterCustomContractServer(s grpc_go.ServiceRegistrar, srv CustomContractServer) {
	s.RegisterService(&CustomContract_ServiceDesc, srv)
}

func _CustomContract_ProtocolSignOffline_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(ProtocolSignOfflineReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("ProtocolSignOffline", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _CustomContract_ProtocolSignOnline_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(ProtocolSignOnlineReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("ProtocolSignOnline", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _CustomContract_TemplateData_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(TemplateDataReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("TemplateData", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _CustomContract_UserInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(UserInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("UserInfo", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _CustomContract_UpdateUserInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateUserInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("UpdateUserInfo", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _CustomContract_UpdateContract_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateContractReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("UpdateContract", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _CustomContract_UpdateContractBatch_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateContractBatchReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("UpdateContractBatch", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _CustomContract_UserContractData_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(UserContractDataReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("UserContractData", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _CustomContract_FddInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(FddInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("FddInfo", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _CustomContract_OfflineSignOrder_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(OfflineSignOrderReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("OfflineSignOrder", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _CustomContract_ArtworkBidData_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(ArtworkBidDataReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("ArtworkBidData", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _CustomContract_ContractData_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(ContractDataReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("ContractData", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _CustomContract_ContractView_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(ContractViewReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("ContractView", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _CustomContract_UserInfoByPhone_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(UserInfoByPhoneReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("UserInfoByPhone", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _CustomContract_GetAuctionArtworkUuids_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAuctionArtworkUuidsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("GetAuctionArtworkUuids", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _CustomContract_SignView_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(SignViewReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("SignView", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _CustomContract_RepairFddContractUrl_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(RepairFddContractUrlReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("RepairFddContractUrl", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _CustomContract_UserAuctionContractCount_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(UserAuctionContractCountReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("UserAuctionContractCount", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _CustomContract_SeriesOrderContractView_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(SeriesOrderContractViewReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("SeriesOrderContractView", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _CustomContract_SeriesOrderContractSign_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(SeriesOrderContractSignReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("SeriesOrderContractSign", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _CustomContract_SignOnlineV2_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(SignOnlineV2Req)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("SignOnlineV2", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _CustomContract_UserInfoV2_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(UserInfoV2Req)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("UserInfoV2", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _CustomContract_UpdateUserInfoV2_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateUserInfoV2Req)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("UpdateUserInfoV2", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _CustomContract_AuctionInfoV2_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(AuctionInfoV2Req)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("AuctionInfoV2", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _CustomContract_ContractViewV2_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(ContractViewV2Req)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("ContractViewV2", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _CustomContract_UserContractNeedSign_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(UserContractNeedSignReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("UserContractNeedSign", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _CustomContract_ArtworkContractView_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(ArtworkContractViewReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("ArtworkContractView", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _CustomContract_SignedAuctionList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(SignedAuctionListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("SignedAuctionList", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _CustomContract_SignedAuctionContracts_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(SignedAuctionContractsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("SignedAuctionContracts", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _CustomContract_AuctionArtworkInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(AuctionArtworkInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("AuctionArtworkInfo", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _CustomContract_SignInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(SignInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("SignInfo", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

// CustomContract_ServiceDesc is the grpc_go.ServiceDesc for CustomContract service.
// It's only intended for direct use with grpc_go.RegisterService,
// and not to be introspected or modified (even as a copy)
var CustomContract_ServiceDesc = grpc_go.ServiceDesc{
	ServiceName: "CustomContract.CustomContract",
	HandlerType: (*CustomContractServer)(nil),
	Methods: []grpc_go.MethodDesc{
		{
			MethodName: "ProtocolSignOffline",
			Handler:    _CustomContract_ProtocolSignOffline_Handler,
		},
		{
			MethodName: "ProtocolSignOnline",
			Handler:    _CustomContract_ProtocolSignOnline_Handler,
		},
		{
			MethodName: "TemplateData",
			Handler:    _CustomContract_TemplateData_Handler,
		},
		{
			MethodName: "UserInfo",
			Handler:    _CustomContract_UserInfo_Handler,
		},
		{
			MethodName: "UpdateUserInfo",
			Handler:    _CustomContract_UpdateUserInfo_Handler,
		},
		{
			MethodName: "UpdateContract",
			Handler:    _CustomContract_UpdateContract_Handler,
		},
		{
			MethodName: "UpdateContractBatch",
			Handler:    _CustomContract_UpdateContractBatch_Handler,
		},
		{
			MethodName: "UserContractData",
			Handler:    _CustomContract_UserContractData_Handler,
		},
		{
			MethodName: "FddInfo",
			Handler:    _CustomContract_FddInfo_Handler,
		},
		{
			MethodName: "OfflineSignOrder",
			Handler:    _CustomContract_OfflineSignOrder_Handler,
		},
		{
			MethodName: "ArtworkBidData",
			Handler:    _CustomContract_ArtworkBidData_Handler,
		},
		{
			MethodName: "ContractData",
			Handler:    _CustomContract_ContractData_Handler,
		},
		{
			MethodName: "ContractView",
			Handler:    _CustomContract_ContractView_Handler,
		},
		{
			MethodName: "UserInfoByPhone",
			Handler:    _CustomContract_UserInfoByPhone_Handler,
		},
		{
			MethodName: "GetAuctionArtworkUuids",
			Handler:    _CustomContract_GetAuctionArtworkUuids_Handler,
		},
		{
			MethodName: "SignView",
			Handler:    _CustomContract_SignView_Handler,
		},
		{
			MethodName: "RepairFddContractUrl",
			Handler:    _CustomContract_RepairFddContractUrl_Handler,
		},
		{
			MethodName: "UserAuctionContractCount",
			Handler:    _CustomContract_UserAuctionContractCount_Handler,
		},
		{
			MethodName: "SeriesOrderContractView",
			Handler:    _CustomContract_SeriesOrderContractView_Handler,
		},
		{
			MethodName: "SeriesOrderContractSign",
			Handler:    _CustomContract_SeriesOrderContractSign_Handler,
		},
		{
			MethodName: "SignOnlineV2",
			Handler:    _CustomContract_SignOnlineV2_Handler,
		},
		{
			MethodName: "UserInfoV2",
			Handler:    _CustomContract_UserInfoV2_Handler,
		},
		{
			MethodName: "UpdateUserInfoV2",
			Handler:    _CustomContract_UpdateUserInfoV2_Handler,
		},
		{
			MethodName: "AuctionInfoV2",
			Handler:    _CustomContract_AuctionInfoV2_Handler,
		},
		{
			MethodName: "ContractViewV2",
			Handler:    _CustomContract_ContractViewV2_Handler,
		},
		{
			MethodName: "UserContractNeedSign",
			Handler:    _CustomContract_UserContractNeedSign_Handler,
		},
		{
			MethodName: "ArtworkContractView",
			Handler:    _CustomContract_ArtworkContractView_Handler,
		},
		{
			MethodName: "SignedAuctionList",
			Handler:    _CustomContract_SignedAuctionList_Handler,
		},
		{
			MethodName: "SignedAuctionContracts",
			Handler:    _CustomContract_SignedAuctionContracts_Handler,
		},
		{
			MethodName: "AuctionArtworkInfo",
			Handler:    _CustomContract_AuctionArtworkInfo_Handler,
		},
		{
			MethodName: "SignInfo",
			Handler:    _CustomContract_SignInfo_Handler,
		},
	},
	Streams:  []grpc_go.StreamDesc{},
	Metadata: "pb/custom_contract.proto",
}
