// Code generated by protoc-gen-gogo. DO NOT EDIT.
// source: api/department/department.proto

package department

import (
	fmt "fmt"
	math "math"
	proto "github.com/golang/protobuf/proto"
	_ "github.com/mwitkow/go-proto-validators"
	github_com_mwitkow_go_proto_validators "github.com/mwitkow/go-proto-validators"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

func (this *FindDepartmentHeadResponse) Validate() error {
	for _, item := range this.List {
		if item != nil {
			if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(item); err != nil {
				return github_com_mwitkow_go_proto_validators.FieldError("List", err)
			}
		}
	}
	return nil
}
func (this *FindDepartmentHead) Validate() error {
	return nil
}
func (this *FindDepartmentHeadRequest) Validate() error {
	return nil
}
func (this *CreateDepartmentHeadResponse) Validate() error {
	return nil
}
func (this *CreateDepartmentHeadRequest) Validate() error {
	return nil
}
func (this *UserIdsByFatherDepIdResponse) Validate() error {
	return nil
}
func (this *UserIdsByFatherDepIdRequest) Validate() error {
	return nil
}
func (this *BaseListV2Request) Validate() error {
	return nil
}
func (this *BaseListV2Response) Validate() error {
	for _, item := range this.List {
		if item != nil {
			if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(item); err != nil {
				return github_com_mwitkow_go_proto_validators.FieldError("List", err)
			}
		}
	}
	return nil
}
func (this *BaseDetailV2Response) Validate() error {
	return nil
}
func (this *UserIsLeaderResponse) Validate() error {
	return nil
}
func (this *FindLeadersFromDIdResponse) Validate() error {
	for _, item := range this.List {
		if item != nil {
			if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(item); err != nil {
				return github_com_mwitkow_go_proto_validators.FieldError("List", err)
			}
		}
	}
	return nil
}
func (this *MyDepartmentsDirectLeadersRequest) Validate() error {
	return nil
}
func (this *MyDirectLeadersRequest) Validate() error {
	return nil
}
func (this *LeaderInfo) Validate() error {
	return nil
}
func (this *MyDirectLeadersResponse) Validate() error {
	for _, item := range this.List {
		if item != nil {
			if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(item); err != nil {
				return github_com_mwitkow_go_proto_validators.FieldError("List", err)
			}
		}
	}
	return nil
}
func (this *PositionInfo) Validate() error {
	return nil
}
func (this *NodeWithPosition) Validate() error {
	for _, item := range this.Sons {
		if item != nil {
			if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(item); err != nil {
				return github_com_mwitkow_go_proto_validators.FieldError("Sons", err)
			}
		}
	}
	for _, item := range this.Positions {
		if item != nil {
			if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(item); err != nil {
				return github_com_mwitkow_go_proto_validators.FieldError("Positions", err)
			}
		}
	}
	return nil
}
func (this *TreeWithPositionResponse) Validate() error {
	for _, item := range this.Nodes {
		if item != nil {
			if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(item); err != nil {
				return github_com_mwitkow_go_proto_validators.FieldError("Nodes", err)
			}
		}
	}
	return nil
}
func (this *DepartmentBossesByAuthReq) Validate() error {
	return nil
}
func (this *DepartmentBoss) Validate() error {
	return nil
}
func (this *DepartmentAndBoss) Validate() error {
	for _, item := range this.Bosses {
		if item != nil {
			if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(item); err != nil {
				return github_com_mwitkow_go_proto_validators.FieldError("Bosses", err)
			}
		}
	}
	return nil
}
func (this *DepartmentBossesByAuthResponse) Validate() error {
	for _, item := range this.List {
		if item != nil {
			if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(item); err != nil {
				return github_com_mwitkow_go_proto_validators.FieldError("List", err)
			}
		}
	}
	return nil
}
func (this *MyBossesResponse) Validate() error {
	for _, item := range this.List {
		if item != nil {
			if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(item); err != nil {
				return github_com_mwitkow_go_proto_validators.FieldError("List", err)
			}
		}
	}
	return nil
}
func (this *Bosses) Validate() error {
	for _, item := range this.Bosses {
		if item != nil {
			if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(item); err != nil {
				return github_com_mwitkow_go_proto_validators.FieldError("Bosses", err)
			}
		}
	}
	return nil
}
func (this *Boss) Validate() error {
	return nil
}
func (this *MyBossesReq) Validate() error {
	return nil
}
func (this *TreeRequest) Validate() error {
	if this.Domain == "" {
		return github_com_mwitkow_go_proto_validators.FieldError("Domain", fmt.Errorf(`70001`))
	}
	return nil
}
func (this *Node) Validate() error {
	for _, item := range this.Sons {
		if item != nil {
			if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(item); err != nil {
				return github_com_mwitkow_go_proto_validators.FieldError("Sons", err)
			}
		}
	}
	return nil
}
func (this *TreeResponse) Validate() error {
	for _, item := range this.Nodes {
		if item != nil {
			if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(item); err != nil {
				return github_com_mwitkow_go_proto_validators.FieldError("Nodes", err)
			}
		}
	}
	return nil
}
func (this *UserIdByBossIdInAuthReq) Validate() error {
	return nil
}
func (this *CoBindRequest) Validate() error {
	return nil
}
func (this *CommonResponse) Validate() error {
	return nil
}
func (this *UserIdByBossIdRequest) Validate() error {
	if this.Domain == "" {
		return github_com_mwitkow_go_proto_validators.FieldError("Domain", fmt.Errorf(`70001`))
	}
	return nil
}
func (this *UserIdByBossIdResponse) Validate() error {
	return nil
}
func (this *DepartmentsByAuthRequest) Validate() error {
	if this.Domain == "" {
		return github_com_mwitkow_go_proto_validators.FieldError("Domain", fmt.Errorf(`70001`))
	}
	return nil
}
func (this *FindLeaderFromDIdRequest) Validate() error {
	return nil
}
func (this *LeaderNameResponse) Validate() error {
	return nil
}
func (this *BasesRequest) Validate() error {
	return nil
}
func (this *BaseDep) Validate() error {
	return nil
}
func (this *BasesResponse) Validate() error {
	for _, item := range this.Data {
		if item != nil {
			if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(item); err != nil {
				return github_com_mwitkow_go_proto_validators.FieldError("Data", err)
			}
		}
	}
	return nil
}
func (this *BaseListResponse) Validate() error {
	for _, item := range this.Data {
		if item != nil {
			if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(item); err != nil {
				return github_com_mwitkow_go_proto_validators.FieldError("Data", err)
			}
		}
	}
	return nil
}
func (this *BaseDetailResponse) Validate() error {
	for _, item := range this.AllPositions {
		if item != nil {
			if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(item); err != nil {
				return github_com_mwitkow_go_proto_validators.FieldError("AllPositions", err)
			}
		}
	}
	return nil
}
func (this *BaseAllRequest) Validate() error {
	if this.Domain == "" {
		return github_com_mwitkow_go_proto_validators.FieldError("Domain", fmt.Errorf(`70001`))
	}
	return nil
}
func (this *BaseAllResponse) Validate() error {
	for _, item := range this.Data {
		if item != nil {
			if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(item); err != nil {
				return github_com_mwitkow_go_proto_validators.FieldError("Data", err)
			}
		}
	}
	return nil
}
func (this *InfoByUserIdRequest) Validate() error {
	if !(this.UserID > 0) {
		return github_com_mwitkow_go_proto_validators.FieldError("UserID", fmt.Errorf(`70010`))
	}
	if this.Domain == "" {
		return github_com_mwitkow_go_proto_validators.FieldError("Domain", fmt.Errorf(`70001`))
	}
	return nil
}
func (this *InfoByUserIdsRequest) Validate() error {
	for _, item := range this.UserIDs {
		if !(item > 0) {
			return github_com_mwitkow_go_proto_validators.FieldError("UserIDs", fmt.Errorf(`70010`))
		}
	}
	if this.Domain == "" {
		return github_com_mwitkow_go_proto_validators.FieldError("Domain", fmt.Errorf(`70001`))
	}
	return nil
}
func (this *InfoByUserIdsResponse) Validate() error {
	// Validation of proto3 map<> fields is unsupported.
	return nil
}
func (this *InfoByUserIdResponse) Validate() error {
	return nil
}
func (this *UsersRequest) Validate() error {
	if this.Domain == "" {
		return github_com_mwitkow_go_proto_validators.FieldError("Domain", fmt.Errorf(`70001`))
	}
	return nil
}
func (this *UsersResponse) Validate() error {
	for _, item := range this.Users {
		if item != nil {
			if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(item); err != nil {
				return github_com_mwitkow_go_proto_validators.FieldError("Users", err)
			}
		}
	}
	return nil
}
func (this *UserInfo) Validate() error {
	return nil
}
func (this *RemoveUserRequest) Validate() error {
	if this.Domain == "" {
		return github_com_mwitkow_go_proto_validators.FieldError("Domain", fmt.Errorf(`70004`))
	}
	return nil
}
func (this *RemoveUserResponse) Validate() error {
	return nil
}
func (this *RemoveRequest) Validate() error {
	if !(this.ID > 0) {
		return github_com_mwitkow_go_proto_validators.FieldError("ID", fmt.Errorf(`70004`))
	}
	if this.Domain == "" {
		return github_com_mwitkow_go_proto_validators.FieldError("Domain", fmt.Errorf(`70001`))
	}
	return nil
}
func (this *RemoveResponse) Validate() error {
	return nil
}
func (this *DetailsRequest) Validate() error {
	if this.Domain == "" {
		return github_com_mwitkow_go_proto_validators.FieldError("Domain", fmt.Errorf(`70001`))
	}
	return nil
}
func (this *DetailRequest) Validate() error {
	if this.Domain == "" {
		return github_com_mwitkow_go_proto_validators.FieldError("Domain", fmt.Errorf(`70001`))
	}
	return nil
}
func (this *DetailsResponse) Validate() error {
	for _, item := range this.Data {
		if item != nil {
			if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(item); err != nil {
				return github_com_mwitkow_go_proto_validators.FieldError("Data", err)
			}
		}
	}
	return nil
}
func (this *DetailResponse) Validate() error {
	for _, item := range this.Rules {
		if item != nil {
			if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(item); err != nil {
				return github_com_mwitkow_go_proto_validators.FieldError("Rules", err)
			}
		}
	}
	if this.LeaderPosition != nil {
		if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(this.LeaderPosition); err != nil {
			return github_com_mwitkow_go_proto_validators.FieldError("LeaderPosition", err)
		}
	}
	for _, item := range this.Positions {
		if item != nil {
			if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(item); err != nil {
				return github_com_mwitkow_go_proto_validators.FieldError("Positions", err)
			}
		}
	}
	for _, item := range this.AllPositions {
		if item != nil {
			if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(item); err != nil {
				return github_com_mwitkow_go_proto_validators.FieldError("AllPositions", err)
			}
		}
	}
	for _, item := range this.DepartmentTreeRule {
		if item != nil {
			if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(item); err != nil {
				return github_com_mwitkow_go_proto_validators.FieldError("DepartmentTreeRule", err)
			}
		}
	}
	return nil
}
func (this *Leader) Validate() error {
	return nil
}
func (this *Position) Validate() error {
	for _, item := range this.Rules {
		if item != nil {
			if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(item); err != nil {
				return github_com_mwitkow_go_proto_validators.FieldError("Rules", err)
			}
		}
	}
	for _, item := range this.Users {
		if item != nil {
			if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(item); err != nil {
				return github_com_mwitkow_go_proto_validators.FieldError("Users", err)
			}
		}
	}
	return nil
}
func (this *CreateRequest) Validate() error {
	if this.Name == "" {
		return github_com_mwitkow_go_proto_validators.FieldError("Name", fmt.Errorf(`70011`))
	}
	for _, item := range this.Rules {
		if item != nil {
			if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(item); err != nil {
				return github_com_mwitkow_go_proto_validators.FieldError("Rules", err)
			}
		}
	}
	for _, item := range this.Leaders {
		if item != nil {
			if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(item); err != nil {
				return github_com_mwitkow_go_proto_validators.FieldError("Leaders", err)
			}
		}
	}
	for _, item := range this.DepartmentTreeRule {
		if item != nil {
			if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(item); err != nil {
				return github_com_mwitkow_go_proto_validators.FieldError("DepartmentTreeRule", err)
			}
		}
	}
	return nil
}
func (this *UpdateResponse) Validate() error {
	return nil
}
func (this *CreateResponse) Validate() error {
	return nil
}
func (this *ListRequest) Validate() error {
	return nil
}
func (this *RuleData) Validate() error {
	return nil
}
func (this *Rule) Validate() error {
	for _, item := range this.RuleData {
		if item != nil {
			if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(item); err != nil {
				return github_com_mwitkow_go_proto_validators.FieldError("RuleData", err)
			}
		}
	}
	return nil
}
func (this *RuleInfo) Validate() error {
	return nil
}
func (this *DepartmentRule) Validate() error {
	for _, item := range this.RuleData {
		if item != nil {
			if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(item); err != nil {
				return github_com_mwitkow_go_proto_validators.FieldError("RuleData", err)
			}
		}
	}
	return nil
}
func (this *ListResponse) Validate() error {
	for _, item := range this.Data {
		if item != nil {
			if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(item); err != nil {
				return github_com_mwitkow_go_proto_validators.FieldError("Data", err)
			}
		}
	}
	return nil
}
func (this *RuleDetail) Validate() error {
	for _, item := range this.RuleData {
		if item != nil {
			if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(item); err != nil {
				return github_com_mwitkow_go_proto_validators.FieldError("RuleData", err)
			}
		}
	}
	for _, item := range this.Son {
		if item != nil {
			if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(item); err != nil {
				return github_com_mwitkow_go_proto_validators.FieldError("Son", err)
			}
		}
	}
	return nil
}
