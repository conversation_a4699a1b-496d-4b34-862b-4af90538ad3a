//
// Licensed to the Apache Software Foundation (ASF) under one or more
// contributor license agreements.  See the NOTICE file distributed with
// this work for additional information regarding copyright ownership.
// The ASF licenses this file to You under the Apache License, Version 2.0
// (the "License"); you may not use this file except in compliance with
// the License.  You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.24.0--rc1
// source: api/department/department.proto

package department

import (
	_ "github.com/mwitkow/go-proto-validators"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type FindDepartmentHeadResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	List  []*FindDepartmentHead `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`
	Count uint64                `protobuf:"varint,2,opt,name=count,proto3" json:"count,omitempty"`
}

func (x *FindDepartmentHeadResponse) Reset() {
	*x = FindDepartmentHeadResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_department_department_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FindDepartmentHeadResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FindDepartmentHeadResponse) ProtoMessage() {}

func (x *FindDepartmentHeadResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_department_department_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FindDepartmentHeadResponse.ProtoReflect.Descriptor instead.
func (*FindDepartmentHeadResponse) Descriptor() ([]byte, []int) {
	return file_api_department_department_proto_rawDescGZIP(), []int{0}
}

func (x *FindDepartmentHeadResponse) GetList() []*FindDepartmentHead {
	if x != nil {
		return x.List
	}
	return nil
}

func (x *FindDepartmentHeadResponse) GetCount() uint64 {
	if x != nil {
		return x.Count
	}
	return 0
}

type FindDepartmentHead struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id        uint64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	StoreId   uint64 `protobuf:"varint,2,opt,name=storeId,proto3" json:"storeId,omitempty"`
	StoreName string `protobuf:"bytes,3,opt,name=storeName,proto3" json:"storeName,omitempty"`
	Alias     string `protobuf:"bytes,4,opt,name=alias,proto3" json:"alias,omitempty"`
	Address   string `protobuf:"bytes,5,opt,name=address,proto3" json:"address,omitempty"`
}

func (x *FindDepartmentHead) Reset() {
	*x = FindDepartmentHead{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_department_department_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FindDepartmentHead) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FindDepartmentHead) ProtoMessage() {}

func (x *FindDepartmentHead) ProtoReflect() protoreflect.Message {
	mi := &file_api_department_department_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FindDepartmentHead.ProtoReflect.Descriptor instead.
func (*FindDepartmentHead) Descriptor() ([]byte, []int) {
	return file_api_department_department_proto_rawDescGZIP(), []int{1}
}

func (x *FindDepartmentHead) GetId() uint64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *FindDepartmentHead) GetStoreId() uint64 {
	if x != nil {
		return x.StoreId
	}
	return 0
}

func (x *FindDepartmentHead) GetStoreName() string {
	if x != nil {
		return x.StoreName
	}
	return ""
}

func (x *FindDepartmentHead) GetAlias() string {
	if x != nil {
		return x.Alias
	}
	return ""
}

func (x *FindDepartmentHead) GetAddress() string {
	if x != nil {
		return x.Address
	}
	return ""
}

type FindDepartmentHeadRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	StoreId   uint64 `protobuf:"varint,1,opt,name=storeId,proto3" json:"storeId,omitempty"`
	StoreName string `protobuf:"bytes,2,opt,name=storeName,proto3" json:"storeName,omitempty"`
}

func (x *FindDepartmentHeadRequest) Reset() {
	*x = FindDepartmentHeadRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_department_department_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FindDepartmentHeadRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FindDepartmentHeadRequest) ProtoMessage() {}

func (x *FindDepartmentHeadRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_department_department_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FindDepartmentHeadRequest.ProtoReflect.Descriptor instead.
func (*FindDepartmentHeadRequest) Descriptor() ([]byte, []int) {
	return file_api_department_department_proto_rawDescGZIP(), []int{2}
}

func (x *FindDepartmentHeadRequest) GetStoreId() uint64 {
	if x != nil {
		return x.StoreId
	}
	return 0
}

func (x *FindDepartmentHeadRequest) GetStoreName() string {
	if x != nil {
		return x.StoreName
	}
	return ""
}

type CreateDepartmentHeadResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id uint64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
}

func (x *CreateDepartmentHeadResponse) Reset() {
	*x = CreateDepartmentHeadResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_department_department_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateDepartmentHeadResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateDepartmentHeadResponse) ProtoMessage() {}

func (x *CreateDepartmentHeadResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_department_department_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateDepartmentHeadResponse.ProtoReflect.Descriptor instead.
func (*CreateDepartmentHeadResponse) Descriptor() ([]byte, []int) {
	return file_api_department_department_proto_rawDescGZIP(), []int{3}
}

func (x *CreateDepartmentHeadResponse) GetId() uint64 {
	if x != nil {
		return x.Id
	}
	return 0
}

type CreateDepartmentHeadRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	StoreId   uint64 `protobuf:"varint,1,opt,name=storeId,proto3" json:"storeId,omitempty"`
	StoreName string `protobuf:"bytes,2,opt,name=storeName,proto3" json:"storeName,omitempty"`
	Alias     string `protobuf:"bytes,3,opt,name=alias,proto3" json:"alias,omitempty"`
	Address   string `protobuf:"bytes,4,opt,name=address,proto3" json:"address,omitempty"`
}

func (x *CreateDepartmentHeadRequest) Reset() {
	*x = CreateDepartmentHeadRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_department_department_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateDepartmentHeadRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateDepartmentHeadRequest) ProtoMessage() {}

func (x *CreateDepartmentHeadRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_department_department_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateDepartmentHeadRequest.ProtoReflect.Descriptor instead.
func (*CreateDepartmentHeadRequest) Descriptor() ([]byte, []int) {
	return file_api_department_department_proto_rawDescGZIP(), []int{4}
}

func (x *CreateDepartmentHeadRequest) GetStoreId() uint64 {
	if x != nil {
		return x.StoreId
	}
	return 0
}

func (x *CreateDepartmentHeadRequest) GetStoreName() string {
	if x != nil {
		return x.StoreName
	}
	return ""
}

func (x *CreateDepartmentHeadRequest) GetAlias() string {
	if x != nil {
		return x.Alias
	}
	return ""
}

func (x *CreateDepartmentHeadRequest) GetAddress() string {
	if x != nil {
		return x.Address
	}
	return ""
}

type UserIdsByFatherDepIdResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UserIds []uint32 `protobuf:"varint,1,rep,packed,name=userIds,proto3" json:"userIds,omitempty"`
}

func (x *UserIdsByFatherDepIdResponse) Reset() {
	*x = UserIdsByFatherDepIdResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_department_department_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UserIdsByFatherDepIdResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserIdsByFatherDepIdResponse) ProtoMessage() {}

func (x *UserIdsByFatherDepIdResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_department_department_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserIdsByFatherDepIdResponse.ProtoReflect.Descriptor instead.
func (*UserIdsByFatherDepIdResponse) Descriptor() ([]byte, []int) {
	return file_api_department_department_proto_rawDescGZIP(), []int{5}
}

func (x *UserIdsByFatherDepIdResponse) GetUserIds() []uint32 {
	if x != nil {
		return x.UserIds
	}
	return nil
}

type UserIdsByFatherDepIdRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Domain    string   `protobuf:"bytes,1,opt,name=domain,proto3" json:"domain,omitempty"`
	FatherIds []uint32 `protobuf:"varint,2,rep,packed,name=fatherIds,proto3" json:"fatherIds,omitempty"`
}

func (x *UserIdsByFatherDepIdRequest) Reset() {
	*x = UserIdsByFatherDepIdRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_department_department_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UserIdsByFatherDepIdRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserIdsByFatherDepIdRequest) ProtoMessage() {}

func (x *UserIdsByFatherDepIdRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_department_department_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserIdsByFatherDepIdRequest.ProtoReflect.Descriptor instead.
func (*UserIdsByFatherDepIdRequest) Descriptor() ([]byte, []int) {
	return file_api_department_department_proto_rawDescGZIP(), []int{6}
}

func (x *UserIdsByFatherDepIdRequest) GetDomain() string {
	if x != nil {
		return x.Domain
	}
	return ""
}

func (x *UserIdsByFatherDepIdRequest) GetFatherIds() []uint32 {
	if x != nil {
		return x.FatherIds
	}
	return nil
}

type BaseListV2Request struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UserId           uint32   `protobuf:"varint,1,opt,name=userId,proto3" json:"userId,omitempty"`                            //当前查看的用户id
	Page             uint32   `protobuf:"varint,2,opt,name=page,proto3" json:"page,omitempty"`                                //当前查看的用户id
	PageSize         uint32   `protobuf:"varint,3,opt,name=pageSize,proto3" json:"pageSize,omitempty"`                        //当前查看的用户id
	DepartmentId     uint32   `protobuf:"varint,4,opt,name=departmentId,proto3" json:"departmentId,omitempty"`                //当前查看的用户id
	NotDepartmentIds []uint32 `protobuf:"varint,5,rep,packed,name=notDepartmentIds,proto3" json:"notDepartmentIds,omitempty"` //当前查看的用户id
	DepartmentIds    []uint32 `protobuf:"varint,6,rep,packed,name=departmentIds,proto3" json:"departmentIds,omitempty"`       //当前查看的用户id
}

func (x *BaseListV2Request) Reset() {
	*x = BaseListV2Request{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_department_department_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BaseListV2Request) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BaseListV2Request) ProtoMessage() {}

func (x *BaseListV2Request) ProtoReflect() protoreflect.Message {
	mi := &file_api_department_department_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BaseListV2Request.ProtoReflect.Descriptor instead.
func (*BaseListV2Request) Descriptor() ([]byte, []int) {
	return file_api_department_department_proto_rawDescGZIP(), []int{7}
}

func (x *BaseListV2Request) GetUserId() uint32 {
	if x != nil {
		return x.UserId
	}
	return 0
}

func (x *BaseListV2Request) GetPage() uint32 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *BaseListV2Request) GetPageSize() uint32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *BaseListV2Request) GetDepartmentId() uint32 {
	if x != nil {
		return x.DepartmentId
	}
	return 0
}

func (x *BaseListV2Request) GetNotDepartmentIds() []uint32 {
	if x != nil {
		return x.NotDepartmentIds
	}
	return nil
}

func (x *BaseListV2Request) GetDepartmentIds() []uint32 {
	if x != nil {
		return x.DepartmentIds
	}
	return nil
}

type BaseListV2Response struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Count uint64                  `protobuf:"varint,1,opt,name=count,proto3" json:"count,omitempty"`
	List  []*BaseDetailV2Response `protobuf:"bytes,2,rep,name=list,proto3" json:"list,omitempty"`
}

func (x *BaseListV2Response) Reset() {
	*x = BaseListV2Response{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_department_department_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BaseListV2Response) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BaseListV2Response) ProtoMessage() {}

func (x *BaseListV2Response) ProtoReflect() protoreflect.Message {
	mi := &file_api_department_department_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BaseListV2Response.ProtoReflect.Descriptor instead.
func (*BaseListV2Response) Descriptor() ([]byte, []int) {
	return file_api_department_department_proto_rawDescGZIP(), []int{8}
}

func (x *BaseListV2Response) GetCount() uint64 {
	if x != nil {
		return x.Count
	}
	return 0
}

func (x *BaseListV2Response) GetList() []*BaseDetailV2Response {
	if x != nil {
		return x.List
	}
	return nil
}

type BaseDetailV2Response struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ID             uint64 `protobuf:"varint,1,opt,name=ID,proto3" json:"ID,omitempty"`
	Name           string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	Remark         string `protobuf:"bytes,3,opt,name=remark,proto3" json:"remark,omitempty"`
	DepartmentCode string `protobuf:"bytes,4,opt,name=departmentCode,proto3" json:"departmentCode,omitempty"`
	Level          uint32 `protobuf:"varint,5,opt,name=level,proto3" json:"level,omitempty"`
}

func (x *BaseDetailV2Response) Reset() {
	*x = BaseDetailV2Response{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_department_department_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BaseDetailV2Response) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BaseDetailV2Response) ProtoMessage() {}

func (x *BaseDetailV2Response) ProtoReflect() protoreflect.Message {
	mi := &file_api_department_department_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BaseDetailV2Response.ProtoReflect.Descriptor instead.
func (*BaseDetailV2Response) Descriptor() ([]byte, []int) {
	return file_api_department_department_proto_rawDescGZIP(), []int{9}
}

func (x *BaseDetailV2Response) GetID() uint64 {
	if x != nil {
		return x.ID
	}
	return 0
}

func (x *BaseDetailV2Response) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *BaseDetailV2Response) GetRemark() string {
	if x != nil {
		return x.Remark
	}
	return ""
}

func (x *BaseDetailV2Response) GetDepartmentCode() string {
	if x != nil {
		return x.DepartmentCode
	}
	return ""
}

func (x *BaseDetailV2Response) GetLevel() uint32 {
	if x != nil {
		return x.Level
	}
	return 0
}

type UserIsLeaderResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	IsLeader bool `protobuf:"varint,1,opt,name=isLeader,proto3" json:"isLeader,omitempty"`
}

func (x *UserIsLeaderResponse) Reset() {
	*x = UserIsLeaderResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_department_department_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UserIsLeaderResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserIsLeaderResponse) ProtoMessage() {}

func (x *UserIsLeaderResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_department_department_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserIsLeaderResponse.ProtoReflect.Descriptor instead.
func (*UserIsLeaderResponse) Descriptor() ([]byte, []int) {
	return file_api_department_department_proto_rawDescGZIP(), []int{10}
}

func (x *UserIsLeaderResponse) GetIsLeader() bool {
	if x != nil {
		return x.IsLeader
	}
	return false
}

type FindLeadersFromDIdResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	List []*LeaderNameResponse `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`
}

func (x *FindLeadersFromDIdResponse) Reset() {
	*x = FindLeadersFromDIdResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_department_department_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FindLeadersFromDIdResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FindLeadersFromDIdResponse) ProtoMessage() {}

func (x *FindLeadersFromDIdResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_department_department_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FindLeadersFromDIdResponse.ProtoReflect.Descriptor instead.
func (*FindLeadersFromDIdResponse) Descriptor() ([]byte, []int) {
	return file_api_department_department_proto_rawDescGZIP(), []int{11}
}

func (x *FindLeadersFromDIdResponse) GetList() []*LeaderNameResponse {
	if x != nil {
		return x.List
	}
	return nil
}

type MyDepartmentsDirectLeadersRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UserId        uint32   `protobuf:"varint,1,opt,name=userId,proto3" json:"userId,omitempty"`                      //当前查看的用户id
	DepartmentIds []uint64 `protobuf:"varint,3,rep,packed,name=departmentIds,proto3" json:"departmentIds,omitempty"` //当前查看的用户id
	Domain        string   `protobuf:"bytes,4,opt,name=domain,proto3" json:"domain,omitempty"`
}

func (x *MyDepartmentsDirectLeadersRequest) Reset() {
	*x = MyDepartmentsDirectLeadersRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_department_department_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MyDepartmentsDirectLeadersRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MyDepartmentsDirectLeadersRequest) ProtoMessage() {}

func (x *MyDepartmentsDirectLeadersRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_department_department_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MyDepartmentsDirectLeadersRequest.ProtoReflect.Descriptor instead.
func (*MyDepartmentsDirectLeadersRequest) Descriptor() ([]byte, []int) {
	return file_api_department_department_proto_rawDescGZIP(), []int{12}
}

func (x *MyDepartmentsDirectLeadersRequest) GetUserId() uint32 {
	if x != nil {
		return x.UserId
	}
	return 0
}

func (x *MyDepartmentsDirectLeadersRequest) GetDepartmentIds() []uint64 {
	if x != nil {
		return x.DepartmentIds
	}
	return nil
}

func (x *MyDepartmentsDirectLeadersRequest) GetDomain() string {
	if x != nil {
		return x.Domain
	}
	return ""
}

type MyDirectLeadersRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UserId       uint32 `protobuf:"varint,1,opt,name=userId,proto3" json:"userId,omitempty"`             //当前查看的用户id
	Level        uint32 `protobuf:"varint,2,opt,name=level,proto3" json:"level,omitempty"`               //当前查看的用户id
	DepartmentId uint32 `protobuf:"varint,3,opt,name=departmentId,proto3" json:"departmentId,omitempty"` //当前查看的用户id
	Domain       string `protobuf:"bytes,4,opt,name=domain,proto3" json:"domain,omitempty"`
}

func (x *MyDirectLeadersRequest) Reset() {
	*x = MyDirectLeadersRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_department_department_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MyDirectLeadersRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MyDirectLeadersRequest) ProtoMessage() {}

func (x *MyDirectLeadersRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_department_department_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MyDirectLeadersRequest.ProtoReflect.Descriptor instead.
func (*MyDirectLeadersRequest) Descriptor() ([]byte, []int) {
	return file_api_department_department_proto_rawDescGZIP(), []int{13}
}

func (x *MyDirectLeadersRequest) GetUserId() uint32 {
	if x != nil {
		return x.UserId
	}
	return 0
}

func (x *MyDirectLeadersRequest) GetLevel() uint32 {
	if x != nil {
		return x.Level
	}
	return 0
}

func (x *MyDirectLeadersRequest) GetDepartmentId() uint32 {
	if x != nil {
		return x.DepartmentId
	}
	return 0
}

func (x *MyDirectLeadersRequest) GetDomain() string {
	if x != nil {
		return x.Domain
	}
	return ""
}

type LeaderInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UserId   uint32 `protobuf:"varint,1,opt,name=userId,proto3" json:"userId,omitempty"`    //当前查看的用户id
	UserName string `protobuf:"bytes,2,opt,name=userName,proto3" json:"userName,omitempty"` //当前查看的用户id
}

func (x *LeaderInfo) Reset() {
	*x = LeaderInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_department_department_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LeaderInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LeaderInfo) ProtoMessage() {}

func (x *LeaderInfo) ProtoReflect() protoreflect.Message {
	mi := &file_api_department_department_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LeaderInfo.ProtoReflect.Descriptor instead.
func (*LeaderInfo) Descriptor() ([]byte, []int) {
	return file_api_department_department_proto_rawDescGZIP(), []int{14}
}

func (x *LeaderInfo) GetUserId() uint32 {
	if x != nil {
		return x.UserId
	}
	return 0
}

func (x *LeaderInfo) GetUserName() string {
	if x != nil {
		return x.UserName
	}
	return ""
}

type MyDirectLeadersResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	List []*LeaderInfo `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`
}

func (x *MyDirectLeadersResponse) Reset() {
	*x = MyDirectLeadersResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_department_department_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MyDirectLeadersResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MyDirectLeadersResponse) ProtoMessage() {}

func (x *MyDirectLeadersResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_department_department_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MyDirectLeadersResponse.ProtoReflect.Descriptor instead.
func (*MyDirectLeadersResponse) Descriptor() ([]byte, []int) {
	return file_api_department_department_proto_rawDescGZIP(), []int{15}
}

func (x *MyDirectLeadersResponse) GetList() []*LeaderInfo {
	if x != nil {
		return x.List
	}
	return nil
}

type PositionInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ID             uint64   `protobuf:"varint,1,opt,name=ID,proto3" json:"ID,omitempty"`
	Name           string   `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	DepartmentID   uint64   `protobuf:"varint,10,opt,name=departmentID,proto3" json:"departmentID,omitempty"`
	Num            int32    `protobuf:"varint,15,opt,name=num,proto3" json:"num,omitempty"`
	DepartmentName string   `protobuf:"bytes,16,opt,name=departmentName,proto3" json:"departmentName,omitempty"`
	Color          string   `protobuf:"bytes,17,opt,name=color,proto3" json:"color,omitempty"`
	OperatorName   string   `protobuf:"bytes,18,opt,name=operatorName,proto3" json:"operatorName,omitempty"`
	MenuAuths      []string `protobuf:"bytes,19,rep,name=menuAuths,proto3" json:"menuAuths,omitempty"`
	LinkerNum      uint32   `protobuf:"varint,20,opt,name=linkerNum,proto3" json:"linkerNum,omitempty"`
	DepartmentIDs  []uint32 `protobuf:"varint,21,rep,packed,name=departmentIDs,proto3" json:"departmentIDs,omitempty"`
}

func (x *PositionInfo) Reset() {
	*x = PositionInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_department_department_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PositionInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PositionInfo) ProtoMessage() {}

func (x *PositionInfo) ProtoReflect() protoreflect.Message {
	mi := &file_api_department_department_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PositionInfo.ProtoReflect.Descriptor instead.
func (*PositionInfo) Descriptor() ([]byte, []int) {
	return file_api_department_department_proto_rawDescGZIP(), []int{16}
}

func (x *PositionInfo) GetID() uint64 {
	if x != nil {
		return x.ID
	}
	return 0
}

func (x *PositionInfo) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *PositionInfo) GetDepartmentID() uint64 {
	if x != nil {
		return x.DepartmentID
	}
	return 0
}

func (x *PositionInfo) GetNum() int32 {
	if x != nil {
		return x.Num
	}
	return 0
}

func (x *PositionInfo) GetDepartmentName() string {
	if x != nil {
		return x.DepartmentName
	}
	return ""
}

func (x *PositionInfo) GetColor() string {
	if x != nil {
		return x.Color
	}
	return ""
}

func (x *PositionInfo) GetOperatorName() string {
	if x != nil {
		return x.OperatorName
	}
	return ""
}

func (x *PositionInfo) GetMenuAuths() []string {
	if x != nil {
		return x.MenuAuths
	}
	return nil
}

func (x *PositionInfo) GetLinkerNum() uint32 {
	if x != nil {
		return x.LinkerNum
	}
	return 0
}

func (x *PositionInfo) GetDepartmentIDs() []uint32 {
	if x != nil {
		return x.DepartmentIDs
	}
	return nil
}

type NodeWithPosition struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ID        uint32              `protobuf:"varint,1,opt,name=ID,proto3" json:"ID,omitempty"`
	Name      string              `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	Sons      []*NodeWithPosition `protobuf:"bytes,3,rep,name=sons,proto3" json:"sons,omitempty"`
	Level     uint32              `protobuf:"varint,4,opt,name=level,proto3" json:"level,omitempty"`
	Positions []*PositionInfo     `protobuf:"bytes,5,rep,name=positions,proto3" json:"positions,omitempty"`
}

func (x *NodeWithPosition) Reset() {
	*x = NodeWithPosition{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_department_department_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *NodeWithPosition) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NodeWithPosition) ProtoMessage() {}

func (x *NodeWithPosition) ProtoReflect() protoreflect.Message {
	mi := &file_api_department_department_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NodeWithPosition.ProtoReflect.Descriptor instead.
func (*NodeWithPosition) Descriptor() ([]byte, []int) {
	return file_api_department_department_proto_rawDescGZIP(), []int{17}
}

func (x *NodeWithPosition) GetID() uint32 {
	if x != nil {
		return x.ID
	}
	return 0
}

func (x *NodeWithPosition) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *NodeWithPosition) GetSons() []*NodeWithPosition {
	if x != nil {
		return x.Sons
	}
	return nil
}

func (x *NodeWithPosition) GetLevel() uint32 {
	if x != nil {
		return x.Level
	}
	return 0
}

func (x *NodeWithPosition) GetPositions() []*PositionInfo {
	if x != nil {
		return x.Positions
	}
	return nil
}

type TreeWithPositionResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Nodes []*NodeWithPosition `protobuf:"bytes,3,rep,name=nodes,proto3" json:"nodes,omitempty"`
}

func (x *TreeWithPositionResponse) Reset() {
	*x = TreeWithPositionResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_department_department_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TreeWithPositionResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TreeWithPositionResponse) ProtoMessage() {}

func (x *TreeWithPositionResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_department_department_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TreeWithPositionResponse.ProtoReflect.Descriptor instead.
func (*TreeWithPositionResponse) Descriptor() ([]byte, []int) {
	return file_api_department_department_proto_rawDescGZIP(), []int{18}
}

func (x *TreeWithPositionResponse) GetNodes() []*NodeWithPosition {
	if x != nil {
		return x.Nodes
	}
	return nil
}

type DepartmentBossesByAuthReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AuthKey string `protobuf:"bytes,1,opt,name=authKey,proto3" json:"authKey,omitempty"`
	Domain  string `protobuf:"bytes,2,opt,name=domain,proto3" json:"domain,omitempty"`
}

func (x *DepartmentBossesByAuthReq) Reset() {
	*x = DepartmentBossesByAuthReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_department_department_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DepartmentBossesByAuthReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DepartmentBossesByAuthReq) ProtoMessage() {}

func (x *DepartmentBossesByAuthReq) ProtoReflect() protoreflect.Message {
	mi := &file_api_department_department_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DepartmentBossesByAuthReq.ProtoReflect.Descriptor instead.
func (*DepartmentBossesByAuthReq) Descriptor() ([]byte, []int) {
	return file_api_department_department_proto_rawDescGZIP(), []int{19}
}

func (x *DepartmentBossesByAuthReq) GetAuthKey() string {
	if x != nil {
		return x.AuthKey
	}
	return ""
}

func (x *DepartmentBossesByAuthReq) GetDomain() string {
	if x != nil {
		return x.Domain
	}
	return ""
}

type DepartmentBoss struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ID     uint32 `protobuf:"varint,1,opt,name=ID,proto3" json:"ID,omitempty"`
	Name   string `protobuf:"bytes,4,opt,name=name,proto3" json:"name,omitempty"`
	TelNum string `protobuf:"bytes,2,opt,name=telNum,proto3" json:"telNum,omitempty"`
	Avatar string `protobuf:"bytes,3,opt,name=avatar,proto3" json:"avatar,omitempty"`
}

func (x *DepartmentBoss) Reset() {
	*x = DepartmentBoss{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_department_department_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DepartmentBoss) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DepartmentBoss) ProtoMessage() {}

func (x *DepartmentBoss) ProtoReflect() protoreflect.Message {
	mi := &file_api_department_department_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DepartmentBoss.ProtoReflect.Descriptor instead.
func (*DepartmentBoss) Descriptor() ([]byte, []int) {
	return file_api_department_department_proto_rawDescGZIP(), []int{20}
}

func (x *DepartmentBoss) GetID() uint32 {
	if x != nil {
		return x.ID
	}
	return 0
}

func (x *DepartmentBoss) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *DepartmentBoss) GetTelNum() string {
	if x != nil {
		return x.TelNum
	}
	return ""
}

func (x *DepartmentBoss) GetAvatar() string {
	if x != nil {
		return x.Avatar
	}
	return ""
}

type DepartmentAndBoss struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ID     uint32            `protobuf:"varint,1,opt,name=ID,proto3" json:"ID,omitempty"`
	Name   string            `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	Bosses []*DepartmentBoss `protobuf:"bytes,3,rep,name=bosses,proto3" json:"bosses,omitempty"`
}

func (x *DepartmentAndBoss) Reset() {
	*x = DepartmentAndBoss{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_department_department_proto_msgTypes[21]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DepartmentAndBoss) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DepartmentAndBoss) ProtoMessage() {}

func (x *DepartmentAndBoss) ProtoReflect() protoreflect.Message {
	mi := &file_api_department_department_proto_msgTypes[21]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DepartmentAndBoss.ProtoReflect.Descriptor instead.
func (*DepartmentAndBoss) Descriptor() ([]byte, []int) {
	return file_api_department_department_proto_rawDescGZIP(), []int{21}
}

func (x *DepartmentAndBoss) GetID() uint32 {
	if x != nil {
		return x.ID
	}
	return 0
}

func (x *DepartmentAndBoss) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *DepartmentAndBoss) GetBosses() []*DepartmentBoss {
	if x != nil {
		return x.Bosses
	}
	return nil
}

type DepartmentBossesByAuthResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	List []*DepartmentAndBoss `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`
}

func (x *DepartmentBossesByAuthResponse) Reset() {
	*x = DepartmentBossesByAuthResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_department_department_proto_msgTypes[22]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DepartmentBossesByAuthResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DepartmentBossesByAuthResponse) ProtoMessage() {}

func (x *DepartmentBossesByAuthResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_department_department_proto_msgTypes[22]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DepartmentBossesByAuthResponse.ProtoReflect.Descriptor instead.
func (*DepartmentBossesByAuthResponse) Descriptor() ([]byte, []int) {
	return file_api_department_department_proto_rawDescGZIP(), []int{22}
}

func (x *DepartmentBossesByAuthResponse) GetList() []*DepartmentAndBoss {
	if x != nil {
		return x.List
	}
	return nil
}

type MyBossesResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	List []*Bosses `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`
}

func (x *MyBossesResponse) Reset() {
	*x = MyBossesResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_department_department_proto_msgTypes[23]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MyBossesResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MyBossesResponse) ProtoMessage() {}

func (x *MyBossesResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_department_department_proto_msgTypes[23]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MyBossesResponse.ProtoReflect.Descriptor instead.
func (*MyBossesResponse) Descriptor() ([]byte, []int) {
	return file_api_department_department_proto_rawDescGZIP(), []int{23}
}

func (x *MyBossesResponse) GetList() []*Bosses {
	if x != nil {
		return x.List
	}
	return nil
}

type Bosses struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Bosses []*Boss `protobuf:"bytes,1,rep,name=bosses,proto3" json:"bosses,omitempty"`
}

func (x *Bosses) Reset() {
	*x = Bosses{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_department_department_proto_msgTypes[24]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Bosses) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Bosses) ProtoMessage() {}

func (x *Bosses) ProtoReflect() protoreflect.Message {
	mi := &file_api_department_department_proto_msgTypes[24]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Bosses.ProtoReflect.Descriptor instead.
func (*Bosses) Descriptor() ([]byte, []int) {
	return file_api_department_department_proto_rawDescGZIP(), []int{24}
}

func (x *Bosses) GetBosses() []*Boss {
	if x != nil {
		return x.Bosses
	}
	return nil
}

type Boss struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ID           uint32 `protobuf:"varint,1,opt,name=ID,proto3" json:"ID,omitempty"`                     //当前查看的用户id
	Name         string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`                  //当前查看的用户id
	Avatar       string `protobuf:"bytes,3,opt,name=avatar,proto3" json:"avatar,omitempty"`              //当前查看的用户id
	DepartmentId uint32 `protobuf:"varint,4,opt,name=departmentId,proto3" json:"departmentId,omitempty"` //当前查看的用户id
}

func (x *Boss) Reset() {
	*x = Boss{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_department_department_proto_msgTypes[25]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Boss) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Boss) ProtoMessage() {}

func (x *Boss) ProtoReflect() protoreflect.Message {
	mi := &file_api_department_department_proto_msgTypes[25]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Boss.ProtoReflect.Descriptor instead.
func (*Boss) Descriptor() ([]byte, []int) {
	return file_api_department_department_proto_rawDescGZIP(), []int{25}
}

func (x *Boss) GetID() uint32 {
	if x != nil {
		return x.ID
	}
	return 0
}

func (x *Boss) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *Boss) GetAvatar() string {
	if x != nil {
		return x.Avatar
	}
	return ""
}

func (x *Boss) GetDepartmentId() uint32 {
	if x != nil {
		return x.DepartmentId
	}
	return 0
}

type MyBossesReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UserId       uint32 `protobuf:"varint,1,opt,name=userId,proto3" json:"userId,omitempty"`             //当前查看的用户id
	Level        uint32 `protobuf:"varint,2,opt,name=level,proto3" json:"level,omitempty"`               //当前查看的用户id
	DepartmentId uint32 `protobuf:"varint,3,opt,name=departmentId,proto3" json:"departmentId,omitempty"` //当前查看的用户id
	IsDesignate  bool   `protobuf:"varint,4,opt,name=isDesignate,proto3" json:"isDesignate,omitempty"`
}

func (x *MyBossesReq) Reset() {
	*x = MyBossesReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_department_department_proto_msgTypes[26]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MyBossesReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MyBossesReq) ProtoMessage() {}

func (x *MyBossesReq) ProtoReflect() protoreflect.Message {
	mi := &file_api_department_department_proto_msgTypes[26]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MyBossesReq.ProtoReflect.Descriptor instead.
func (*MyBossesReq) Descriptor() ([]byte, []int) {
	return file_api_department_department_proto_rawDescGZIP(), []int{26}
}

func (x *MyBossesReq) GetUserId() uint32 {
	if x != nil {
		return x.UserId
	}
	return 0
}

func (x *MyBossesReq) GetLevel() uint32 {
	if x != nil {
		return x.Level
	}
	return 0
}

func (x *MyBossesReq) GetDepartmentId() uint32 {
	if x != nil {
		return x.DepartmentId
	}
	return 0
}

func (x *MyBossesReq) GetIsDesignate() bool {
	if x != nil {
		return x.IsDesignate
	}
	return false
}

type TreeRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	NowUserId           uint32   `protobuf:"varint,1,opt,name=nowUserId,proto3" json:"nowUserId,omitempty"` //当前查看的用户id
	Domain              string   `protobuf:"bytes,2,opt,name=domain,proto3" json:"domain,omitempty"`
	FilterDepartmentIds []uint32 `protobuf:"varint,3,rep,packed,name=filterDepartmentIds,proto3" json:"filterDepartmentIds,omitempty"` //当前查看的用户id
}

func (x *TreeRequest) Reset() {
	*x = TreeRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_department_department_proto_msgTypes[27]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TreeRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TreeRequest) ProtoMessage() {}

func (x *TreeRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_department_department_proto_msgTypes[27]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TreeRequest.ProtoReflect.Descriptor instead.
func (*TreeRequest) Descriptor() ([]byte, []int) {
	return file_api_department_department_proto_rawDescGZIP(), []int{27}
}

func (x *TreeRequest) GetNowUserId() uint32 {
	if x != nil {
		return x.NowUserId
	}
	return 0
}

func (x *TreeRequest) GetDomain() string {
	if x != nil {
		return x.Domain
	}
	return ""
}

func (x *TreeRequest) GetFilterDepartmentIds() []uint32 {
	if x != nil {
		return x.FilterDepartmentIds
	}
	return nil
}

type Node struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ID          uint32   `protobuf:"varint,1,opt,name=ID,proto3" json:"ID,omitempty"`
	Name        string   `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	Sons        []*Node  `protobuf:"bytes,3,rep,name=sons,proto3" json:"sons,omitempty"`
	Level       uint32   `protobuf:"varint,4,opt,name=level,proto3" json:"level,omitempty"`
	PathName    []string `protobuf:"bytes,5,rep,name=pathName,proto3" json:"pathName,omitempty"`
	FlagNum     uint32   `protobuf:"varint,21,opt,name=flagNum,proto3" json:"flagNum,omitempty"`
	SonMaxDepth uint32   `protobuf:"varint,6,opt,name=sonMaxDepth,proto3" json:"sonMaxDepth,omitempty"`
	PathIds     []uint32 `protobuf:"varint,7,rep,packed,name=pathIds,proto3" json:"pathIds,omitempty"`
	Sync        bool     `protobuf:"varint,11,opt,name=sync,proto3" json:"sync,omitempty"`
	SyncId      string   `protobuf:"bytes,12,opt,name=syncId,proto3" json:"syncId,omitempty"`
	StaffNum    uint32   `protobuf:"varint,13,opt,name=staffNum,proto3" json:"staffNum,omitempty"`
}

func (x *Node) Reset() {
	*x = Node{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_department_department_proto_msgTypes[28]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Node) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Node) ProtoMessage() {}

func (x *Node) ProtoReflect() protoreflect.Message {
	mi := &file_api_department_department_proto_msgTypes[28]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Node.ProtoReflect.Descriptor instead.
func (*Node) Descriptor() ([]byte, []int) {
	return file_api_department_department_proto_rawDescGZIP(), []int{28}
}

func (x *Node) GetID() uint32 {
	if x != nil {
		return x.ID
	}
	return 0
}

func (x *Node) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *Node) GetSons() []*Node {
	if x != nil {
		return x.Sons
	}
	return nil
}

func (x *Node) GetLevel() uint32 {
	if x != nil {
		return x.Level
	}
	return 0
}

func (x *Node) GetPathName() []string {
	if x != nil {
		return x.PathName
	}
	return nil
}

func (x *Node) GetFlagNum() uint32 {
	if x != nil {
		return x.FlagNum
	}
	return 0
}

func (x *Node) GetSonMaxDepth() uint32 {
	if x != nil {
		return x.SonMaxDepth
	}
	return 0
}

func (x *Node) GetPathIds() []uint32 {
	if x != nil {
		return x.PathIds
	}
	return nil
}

func (x *Node) GetSync() bool {
	if x != nil {
		return x.Sync
	}
	return false
}

func (x *Node) GetSyncId() string {
	if x != nil {
		return x.SyncId
	}
	return ""
}

func (x *Node) GetStaffNum() uint32 {
	if x != nil {
		return x.StaffNum
	}
	return 0
}

type TreeResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Nodes []*Node `protobuf:"bytes,3,rep,name=nodes,proto3" json:"nodes,omitempty"`
}

func (x *TreeResponse) Reset() {
	*x = TreeResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_department_department_proto_msgTypes[29]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TreeResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TreeResponse) ProtoMessage() {}

func (x *TreeResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_department_department_proto_msgTypes[29]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TreeResponse.ProtoReflect.Descriptor instead.
func (*TreeResponse) Descriptor() ([]byte, []int) {
	return file_api_department_department_proto_rawDescGZIP(), []int{29}
}

func (x *TreeResponse) GetNodes() []*Node {
	if x != nil {
		return x.Nodes
	}
	return nil
}

type UserIdByBossIdInAuthReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Domain string `protobuf:"bytes,1,opt,name=Domain,proto3" json:"Domain,omitempty"`
	UserId uint64 `protobuf:"varint,2,opt,name=UserId,proto3" json:"UserId,omitempty"`
	Auth   string `protobuf:"bytes,3,opt,name=Auth,proto3" json:"Auth,omitempty"`
}

func (x *UserIdByBossIdInAuthReq) Reset() {
	*x = UserIdByBossIdInAuthReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_department_department_proto_msgTypes[30]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UserIdByBossIdInAuthReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserIdByBossIdInAuthReq) ProtoMessage() {}

func (x *UserIdByBossIdInAuthReq) ProtoReflect() protoreflect.Message {
	mi := &file_api_department_department_proto_msgTypes[30]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserIdByBossIdInAuthReq.ProtoReflect.Descriptor instead.
func (*UserIdByBossIdInAuthReq) Descriptor() ([]byte, []int) {
	return file_api_department_department_proto_rawDescGZIP(), []int{30}
}

func (x *UserIdByBossIdInAuthReq) GetDomain() string {
	if x != nil {
		return x.Domain
	}
	return ""
}

func (x *UserIdByBossIdInAuthReq) GetUserId() uint64 {
	if x != nil {
		return x.UserId
	}
	return 0
}

func (x *UserIdByBossIdInAuthReq) GetAuth() string {
	if x != nil {
		return x.Auth
	}
	return ""
}

type CoBindRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Domain      string   `protobuf:"bytes,1,opt,name=domain,proto3" json:"domain,omitempty"`
	ID          uint32   `protobuf:"varint,2,opt,name=ID,proto3" json:"ID,omitempty"`
	PositionIds []uint32 `protobuf:"varint,3,rep,packed,name=positionIds,proto3" json:"positionIds,omitempty"`
}

func (x *CoBindRequest) Reset() {
	*x = CoBindRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_department_department_proto_msgTypes[31]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CoBindRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CoBindRequest) ProtoMessage() {}

func (x *CoBindRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_department_department_proto_msgTypes[31]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CoBindRequest.ProtoReflect.Descriptor instead.
func (*CoBindRequest) Descriptor() ([]byte, []int) {
	return file_api_department_department_proto_rawDescGZIP(), []int{31}
}

func (x *CoBindRequest) GetDomain() string {
	if x != nil {
		return x.Domain
	}
	return ""
}

func (x *CoBindRequest) GetID() uint32 {
	if x != nil {
		return x.ID
	}
	return 0
}

func (x *CoBindRequest) GetPositionIds() []uint32 {
	if x != nil {
		return x.PositionIds
	}
	return nil
}

type CommonResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *CommonResponse) Reset() {
	*x = CommonResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_department_department_proto_msgTypes[32]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CommonResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CommonResponse) ProtoMessage() {}

func (x *CommonResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_department_department_proto_msgTypes[32]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CommonResponse.ProtoReflect.Descriptor instead.
func (*CommonResponse) Descriptor() ([]byte, []int) {
	return file_api_department_department_proto_rawDescGZIP(), []int{32}
}

type UserIdByBossIdRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Domain string `protobuf:"bytes,2,opt,name=Domain,proto3" json:"Domain,omitempty"`
	UserId uint64 `protobuf:"varint,1,opt,name=UserId,json=userId,proto3" json:"UserId,omitempty"`
}

func (x *UserIdByBossIdRequest) Reset() {
	*x = UserIdByBossIdRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_department_department_proto_msgTypes[33]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UserIdByBossIdRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserIdByBossIdRequest) ProtoMessage() {}

func (x *UserIdByBossIdRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_department_department_proto_msgTypes[33]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserIdByBossIdRequest.ProtoReflect.Descriptor instead.
func (*UserIdByBossIdRequest) Descriptor() ([]byte, []int) {
	return file_api_department_department_proto_rawDescGZIP(), []int{33}
}

func (x *UserIdByBossIdRequest) GetDomain() string {
	if x != nil {
		return x.Domain
	}
	return ""
}

func (x *UserIdByBossIdRequest) GetUserId() uint64 {
	if x != nil {
		return x.UserId
	}
	return 0
}

type UserIdByBossIdResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UserIds []uint64 `protobuf:"varint,1,rep,packed,name=UserIds,json=userIds,proto3" json:"UserIds,omitempty"`
}

func (x *UserIdByBossIdResponse) Reset() {
	*x = UserIdByBossIdResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_department_department_proto_msgTypes[34]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UserIdByBossIdResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserIdByBossIdResponse) ProtoMessage() {}

func (x *UserIdByBossIdResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_department_department_proto_msgTypes[34]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserIdByBossIdResponse.ProtoReflect.Descriptor instead.
func (*UserIdByBossIdResponse) Descriptor() ([]byte, []int) {
	return file_api_department_department_proto_rawDescGZIP(), []int{34}
}

func (x *UserIdByBossIdResponse) GetUserIds() []uint64 {
	if x != nil {
		return x.UserIds
	}
	return nil
}

type DepartmentsByAuthRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Domain string `protobuf:"bytes,2,opt,name=Domain,proto3" json:"Domain,omitempty"`
	Url    string `protobuf:"bytes,1,opt,name=Url,json=url,proto3" json:"Url,omitempty"`
}

func (x *DepartmentsByAuthRequest) Reset() {
	*x = DepartmentsByAuthRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_department_department_proto_msgTypes[35]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DepartmentsByAuthRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DepartmentsByAuthRequest) ProtoMessage() {}

func (x *DepartmentsByAuthRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_department_department_proto_msgTypes[35]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DepartmentsByAuthRequest.ProtoReflect.Descriptor instead.
func (*DepartmentsByAuthRequest) Descriptor() ([]byte, []int) {
	return file_api_department_department_proto_rawDescGZIP(), []int{35}
}

func (x *DepartmentsByAuthRequest) GetDomain() string {
	if x != nil {
		return x.Domain
	}
	return ""
}

func (x *DepartmentsByAuthRequest) GetUrl() string {
	if x != nil {
		return x.Url
	}
	return ""
}

type FindLeaderFromDIdRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	DepartmentId uint64 `protobuf:"varint,1,opt,name=DepartmentId,proto3" json:"DepartmentId,omitempty"`
	Domain       string `protobuf:"bytes,2,opt,name=Domain,proto3" json:"Domain,omitempty"`
}

func (x *FindLeaderFromDIdRequest) Reset() {
	*x = FindLeaderFromDIdRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_department_department_proto_msgTypes[36]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FindLeaderFromDIdRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FindLeaderFromDIdRequest) ProtoMessage() {}

func (x *FindLeaderFromDIdRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_department_department_proto_msgTypes[36]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FindLeaderFromDIdRequest.ProtoReflect.Descriptor instead.
func (*FindLeaderFromDIdRequest) Descriptor() ([]byte, []int) {
	return file_api_department_department_proto_rawDescGZIP(), []int{36}
}

func (x *FindLeaderFromDIdRequest) GetDepartmentId() uint64 {
	if x != nil {
		return x.DepartmentId
	}
	return 0
}

func (x *FindLeaderFromDIdRequest) GetDomain() string {
	if x != nil {
		return x.Domain
	}
	return ""
}

type LeaderNameResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	LeaderName string `protobuf:"bytes,1,opt,name=LeaderName,proto3" json:"LeaderName,omitempty"`
	LeaderId   uint64 `protobuf:"varint,2,opt,name=LeaderId,proto3" json:"LeaderId,omitempty"`
}

func (x *LeaderNameResponse) Reset() {
	*x = LeaderNameResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_department_department_proto_msgTypes[37]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LeaderNameResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LeaderNameResponse) ProtoMessage() {}

func (x *LeaderNameResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_department_department_proto_msgTypes[37]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LeaderNameResponse.ProtoReflect.Descriptor instead.
func (*LeaderNameResponse) Descriptor() ([]byte, []int) {
	return file_api_department_department_proto_rawDescGZIP(), []int{37}
}

func (x *LeaderNameResponse) GetLeaderName() string {
	if x != nil {
		return x.LeaderName
	}
	return ""
}

func (x *LeaderNameResponse) GetLeaderId() uint64 {
	if x != nil {
		return x.LeaderId
	}
	return 0
}

type BasesRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	DepartmentCode string `protobuf:"bytes,1,opt,name=DepartmentCode,proto3" json:"DepartmentCode,omitempty"`
	Domain         string `protobuf:"bytes,2,opt,name=Domain,proto3" json:"Domain,omitempty"`
}

func (x *BasesRequest) Reset() {
	*x = BasesRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_department_department_proto_msgTypes[38]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BasesRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BasesRequest) ProtoMessage() {}

func (x *BasesRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_department_department_proto_msgTypes[38]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BasesRequest.ProtoReflect.Descriptor instead.
func (*BasesRequest) Descriptor() ([]byte, []int) {
	return file_api_department_department_proto_rawDescGZIP(), []int{38}
}

func (x *BasesRequest) GetDepartmentCode() string {
	if x != nil {
		return x.DepartmentCode
	}
	return ""
}

func (x *BasesRequest) GetDomain() string {
	if x != nil {
		return x.Domain
	}
	return ""
}

type BaseDep struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ID   uint64 `protobuf:"varint,1,opt,name=ID,proto3" json:"ID,omitempty"`
	Name string `protobuf:"bytes,2,opt,name=Name,json=name,proto3" json:"Name,omitempty"`
}

func (x *BaseDep) Reset() {
	*x = BaseDep{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_department_department_proto_msgTypes[39]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BaseDep) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BaseDep) ProtoMessage() {}

func (x *BaseDep) ProtoReflect() protoreflect.Message {
	mi := &file_api_department_department_proto_msgTypes[39]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BaseDep.ProtoReflect.Descriptor instead.
func (*BaseDep) Descriptor() ([]byte, []int) {
	return file_api_department_department_proto_rawDescGZIP(), []int{39}
}

func (x *BaseDep) GetID() uint64 {
	if x != nil {
		return x.ID
	}
	return 0
}

func (x *BaseDep) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

type BasesResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Data []*BaseDep `protobuf:"bytes,2,rep,name=Data,proto3" json:"Data,omitempty"`
}

func (x *BasesResponse) Reset() {
	*x = BasesResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_department_department_proto_msgTypes[40]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BasesResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BasesResponse) ProtoMessage() {}

func (x *BasesResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_department_department_proto_msgTypes[40]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BasesResponse.ProtoReflect.Descriptor instead.
func (*BasesResponse) Descriptor() ([]byte, []int) {
	return file_api_department_department_proto_rawDescGZIP(), []int{40}
}

func (x *BasesResponse) GetData() []*BaseDep {
	if x != nil {
		return x.Data
	}
	return nil
}

type BaseListResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Count    uint64                `protobuf:"varint,1,opt,name=Count,proto3" json:"Count,omitempty"`
	Data     []*BaseDetailResponse `protobuf:"bytes,2,rep,name=Data,proto3" json:"Data,omitempty"`
	Page     uint64                `protobuf:"varint,3,opt,name=page,proto3" json:"page,omitempty"`
	PageSize uint64                `protobuf:"varint,4,opt,name=pageSize,proto3" json:"pageSize,omitempty"`
}

func (x *BaseListResponse) Reset() {
	*x = BaseListResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_department_department_proto_msgTypes[41]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BaseListResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BaseListResponse) ProtoMessage() {}

func (x *BaseListResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_department_department_proto_msgTypes[41]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BaseListResponse.ProtoReflect.Descriptor instead.
func (*BaseListResponse) Descriptor() ([]byte, []int) {
	return file_api_department_department_proto_rawDescGZIP(), []int{41}
}

func (x *BaseListResponse) GetCount() uint64 {
	if x != nil {
		return x.Count
	}
	return 0
}

func (x *BaseListResponse) GetData() []*BaseDetailResponse {
	if x != nil {
		return x.Data
	}
	return nil
}

func (x *BaseListResponse) GetPage() uint64 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *BaseListResponse) GetPageSize() uint64 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

type BaseDetailResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ID             uint64      `protobuf:"varint,1,opt,name=ID,proto3" json:"ID,omitempty"`
	Name           string      `protobuf:"bytes,2,opt,name=Name,json=name,proto3" json:"Name,omitempty"`
	Remark         string      `protobuf:"bytes,3,opt,name=Remark,json=remark,proto3" json:"Remark,omitempty"`
	DepartmentCode string      `protobuf:"bytes,4,opt,name=DepartmentCode,json=departmentCode,proto3" json:"DepartmentCode,omitempty"`
	AllPositions   []*Position `protobuf:"bytes,5,rep,name=AllPositions,json=allPositions,proto3" json:"AllPositions,omitempty"`
	SyncId         string      `protobuf:"bytes,6,opt,name=syncId,proto3" json:"syncId,omitempty"`
}

func (x *BaseDetailResponse) Reset() {
	*x = BaseDetailResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_department_department_proto_msgTypes[42]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BaseDetailResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BaseDetailResponse) ProtoMessage() {}

func (x *BaseDetailResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_department_department_proto_msgTypes[42]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BaseDetailResponse.ProtoReflect.Descriptor instead.
func (*BaseDetailResponse) Descriptor() ([]byte, []int) {
	return file_api_department_department_proto_rawDescGZIP(), []int{42}
}

func (x *BaseDetailResponse) GetID() uint64 {
	if x != nil {
		return x.ID
	}
	return 0
}

func (x *BaseDetailResponse) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *BaseDetailResponse) GetRemark() string {
	if x != nil {
		return x.Remark
	}
	return ""
}

func (x *BaseDetailResponse) GetDepartmentCode() string {
	if x != nil {
		return x.DepartmentCode
	}
	return ""
}

func (x *BaseDetailResponse) GetAllPositions() []*Position {
	if x != nil {
		return x.AllPositions
	}
	return nil
}

func (x *BaseDetailResponse) GetSyncId() string {
	if x != nil {
		return x.SyncId
	}
	return ""
}

type BaseAllRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Domain         string `protobuf:"bytes,2,opt,name=Domain,proto3" json:"Domain,omitempty"`
	UserID         uint32 `protobuf:"varint,3,opt,name=userID,proto3" json:"userID,omitempty"`
	DepartmentName string `protobuf:"bytes,4,opt,name=departmentName,proto3" json:"departmentName,omitempty"`
	Page           uint64 `protobuf:"varint,5,opt,name=page,proto3" json:"page,omitempty"`
	PageSize       uint64 `protobuf:"varint,6,opt,name=pageSize,proto3" json:"pageSize,omitempty"`
}

func (x *BaseAllRequest) Reset() {
	*x = BaseAllRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_department_department_proto_msgTypes[43]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BaseAllRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BaseAllRequest) ProtoMessage() {}

func (x *BaseAllRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_department_department_proto_msgTypes[43]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BaseAllRequest.ProtoReflect.Descriptor instead.
func (*BaseAllRequest) Descriptor() ([]byte, []int) {
	return file_api_department_department_proto_rawDescGZIP(), []int{43}
}

func (x *BaseAllRequest) GetDomain() string {
	if x != nil {
		return x.Domain
	}
	return ""
}

func (x *BaseAllRequest) GetUserID() uint32 {
	if x != nil {
		return x.UserID
	}
	return 0
}

func (x *BaseAllRequest) GetDepartmentName() string {
	if x != nil {
		return x.DepartmentName
	}
	return ""
}

func (x *BaseAllRequest) GetPage() uint64 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *BaseAllRequest) GetPageSize() uint64 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

type BaseAllResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Data []*CreateRequest `protobuf:"bytes,1,rep,name=Data,proto3" json:"Data,omitempty"`
}

func (x *BaseAllResponse) Reset() {
	*x = BaseAllResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_department_department_proto_msgTypes[44]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BaseAllResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BaseAllResponse) ProtoMessage() {}

func (x *BaseAllResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_department_department_proto_msgTypes[44]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BaseAllResponse.ProtoReflect.Descriptor instead.
func (*BaseAllResponse) Descriptor() ([]byte, []int) {
	return file_api_department_department_proto_rawDescGZIP(), []int{44}
}

func (x *BaseAllResponse) GetData() []*CreateRequest {
	if x != nil {
		return x.Data
	}
	return nil
}

type InfoByUserIdRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UserID uint64 `protobuf:"varint,1,opt,name=UserID,json=ID,proto3" json:"UserID,omitempty"`
	Domain string `protobuf:"bytes,2,opt,name=Domain,proto3" json:"Domain,omitempty"`
}

func (x *InfoByUserIdRequest) Reset() {
	*x = InfoByUserIdRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_department_department_proto_msgTypes[45]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *InfoByUserIdRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InfoByUserIdRequest) ProtoMessage() {}

func (x *InfoByUserIdRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_department_department_proto_msgTypes[45]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InfoByUserIdRequest.ProtoReflect.Descriptor instead.
func (*InfoByUserIdRequest) Descriptor() ([]byte, []int) {
	return file_api_department_department_proto_rawDescGZIP(), []int{45}
}

func (x *InfoByUserIdRequest) GetUserID() uint64 {
	if x != nil {
		return x.UserID
	}
	return 0
}

func (x *InfoByUserIdRequest) GetDomain() string {
	if x != nil {
		return x.Domain
	}
	return ""
}

type InfoByUserIdsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UserIDs []uint64 `protobuf:"varint,1,rep,packed,name=UserIDs,json=userID,proto3" json:"UserIDs,omitempty"`
	Domain  string   `protobuf:"bytes,2,opt,name=Domain,proto3" json:"Domain,omitempty"`
}

func (x *InfoByUserIdsRequest) Reset() {
	*x = InfoByUserIdsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_department_department_proto_msgTypes[46]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *InfoByUserIdsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InfoByUserIdsRequest) ProtoMessage() {}

func (x *InfoByUserIdsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_department_department_proto_msgTypes[46]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InfoByUserIdsRequest.ProtoReflect.Descriptor instead.
func (*InfoByUserIdsRequest) Descriptor() ([]byte, []int) {
	return file_api_department_department_proto_rawDescGZIP(), []int{46}
}

func (x *InfoByUserIdsRequest) GetUserIDs() []uint64 {
	if x != nil {
		return x.UserIDs
	}
	return nil
}

func (x *InfoByUserIdsRequest) GetDomain() string {
	if x != nil {
		return x.Domain
	}
	return ""
}

type InfoByUserIdsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	DepartmentByUserId map[uint64]string `protobuf:"bytes,1,rep,name=DepartmentByUserId,json=departmentByUserId,proto3" json:"DepartmentByUserId,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

func (x *InfoByUserIdsResponse) Reset() {
	*x = InfoByUserIdsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_department_department_proto_msgTypes[47]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *InfoByUserIdsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InfoByUserIdsResponse) ProtoMessage() {}

func (x *InfoByUserIdsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_department_department_proto_msgTypes[47]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InfoByUserIdsResponse.ProtoReflect.Descriptor instead.
func (*InfoByUserIdsResponse) Descriptor() ([]byte, []int) {
	return file_api_department_department_proto_rawDescGZIP(), []int{47}
}

func (x *InfoByUserIdsResponse) GetDepartmentByUserId() map[uint64]string {
	if x != nil {
		return x.DepartmentByUserId
	}
	return nil
}

type InfoByUserIdResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	DepartmentID   uint64 `protobuf:"varint,1,opt,name=DepartmentID,json=departmentID,proto3" json:"DepartmentID,omitempty"`
	DepartmentName string `protobuf:"bytes,2,opt,name=DepartmentName,json=departmentName,proto3" json:"DepartmentName,omitempty"`
}

func (x *InfoByUserIdResponse) Reset() {
	*x = InfoByUserIdResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_department_department_proto_msgTypes[48]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *InfoByUserIdResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InfoByUserIdResponse) ProtoMessage() {}

func (x *InfoByUserIdResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_department_department_proto_msgTypes[48]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InfoByUserIdResponse.ProtoReflect.Descriptor instead.
func (*InfoByUserIdResponse) Descriptor() ([]byte, []int) {
	return file_api_department_department_proto_rawDescGZIP(), []int{48}
}

func (x *InfoByUserIdResponse) GetDepartmentID() uint64 {
	if x != nil {
		return x.DepartmentID
	}
	return 0
}

func (x *InfoByUserIdResponse) GetDepartmentName() string {
	if x != nil {
		return x.DepartmentName
	}
	return ""
}

type UsersRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ID            uint64   `protobuf:"varint,1,opt,name=ID,proto3" json:"ID,omitempty"`
	PositionID    uint64   `protobuf:"varint,2,opt,name=PositionID,json=positionID,proto3" json:"PositionID,omitempty"`
	Domain        string   `protobuf:"bytes,3,opt,name=Domain,proto3" json:"Domain,omitempty"`
	Name          string   `protobuf:"bytes,4,opt,name=Name,json=name,proto3" json:"Name,omitempty"`
	Page          uint64   `protobuf:"varint,5,opt,name=Page,proto3" json:"Page,omitempty"`
	PageSize      uint64   `protobuf:"varint,6,opt,name=PageSize,json=pageSize,proto3" json:"PageSize,omitempty"`
	NotPositionID uint64   `protobuf:"varint,7,opt,name=NotPositionID,json=notPositionID,proto3" json:"NotPositionID,omitempty"`
	InPositionIds []uint64 `protobuf:"varint,8,rep,packed,name=InPositionIds,json=inPositionIds,proto3" json:"InPositionIds,omitempty"`
}

func (x *UsersRequest) Reset() {
	*x = UsersRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_department_department_proto_msgTypes[49]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UsersRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UsersRequest) ProtoMessage() {}

func (x *UsersRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_department_department_proto_msgTypes[49]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UsersRequest.ProtoReflect.Descriptor instead.
func (*UsersRequest) Descriptor() ([]byte, []int) {
	return file_api_department_department_proto_rawDescGZIP(), []int{49}
}

func (x *UsersRequest) GetID() uint64 {
	if x != nil {
		return x.ID
	}
	return 0
}

func (x *UsersRequest) GetPositionID() uint64 {
	if x != nil {
		return x.PositionID
	}
	return 0
}

func (x *UsersRequest) GetDomain() string {
	if x != nil {
		return x.Domain
	}
	return ""
}

func (x *UsersRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *UsersRequest) GetPage() uint64 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *UsersRequest) GetPageSize() uint64 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *UsersRequest) GetNotPositionID() uint64 {
	if x != nil {
		return x.NotPositionID
	}
	return 0
}

func (x *UsersRequest) GetInPositionIds() []uint64 {
	if x != nil {
		return x.InPositionIds
	}
	return nil
}

type UsersResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Users []*UserInfo `protobuf:"bytes,1,rep,name=Users,json=users,proto3" json:"Users,omitempty"`
	Count uint64      `protobuf:"varint,2,opt,name=Count,json=count,proto3" json:"Count,omitempty"`
}

func (x *UsersResponse) Reset() {
	*x = UsersResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_department_department_proto_msgTypes[50]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UsersResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UsersResponse) ProtoMessage() {}

func (x *UsersResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_department_department_proto_msgTypes[50]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UsersResponse.ProtoReflect.Descriptor instead.
func (*UsersResponse) Descriptor() ([]byte, []int) {
	return file_api_department_department_proto_rawDescGZIP(), []int{50}
}

func (x *UsersResponse) GetUsers() []*UserInfo {
	if x != nil {
		return x.Users
	}
	return nil
}

func (x *UsersResponse) GetCount() uint64 {
	if x != nil {
		return x.Count
	}
	return 0
}

type UserInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UserID     uint64 `protobuf:"varint,1,opt,name=UserID,json=userID,proto3" json:"UserID,omitempty"`
	UserName   string `protobuf:"bytes,2,opt,name=UserName,json=userName,proto3" json:"UserName,omitempty"`
	Avatar     string `protobuf:"bytes,3,opt,name=Avatar,json=avatar,proto3" json:"Avatar,omitempty"`
	Position   string `protobuf:"bytes,4,opt,name=Position,json=position,proto3" json:"Position,omitempty"`
	Department string `protobuf:"bytes,5,opt,name=Department,json=department,proto3" json:"Department,omitempty"`
	TelNum     string `protobuf:"bytes,6,opt,name=TelNum,json=telNum,proto3" json:"TelNum,omitempty"`
}

func (x *UserInfo) Reset() {
	*x = UserInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_department_department_proto_msgTypes[51]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UserInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserInfo) ProtoMessage() {}

func (x *UserInfo) ProtoReflect() protoreflect.Message {
	mi := &file_api_department_department_proto_msgTypes[51]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserInfo.ProtoReflect.Descriptor instead.
func (*UserInfo) Descriptor() ([]byte, []int) {
	return file_api_department_department_proto_rawDescGZIP(), []int{51}
}

func (x *UserInfo) GetUserID() uint64 {
	if x != nil {
		return x.UserID
	}
	return 0
}

func (x *UserInfo) GetUserName() string {
	if x != nil {
		return x.UserName
	}
	return ""
}

func (x *UserInfo) GetAvatar() string {
	if x != nil {
		return x.Avatar
	}
	return ""
}

func (x *UserInfo) GetPosition() string {
	if x != nil {
		return x.Position
	}
	return ""
}

func (x *UserInfo) GetDepartment() string {
	if x != nil {
		return x.Department
	}
	return ""
}

func (x *UserInfo) GetTelNum() string {
	if x != nil {
		return x.TelNum
	}
	return ""
}

// 删除用户的绑定关系
type RemoveUserRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Domain   string `protobuf:"bytes,1,opt,name=Domain,json=departmentID,proto3" json:"Domain,omitempty"`
	LeaderID uint64 `protobuf:"varint,2,opt,name=LeaderID,json=leaderID,proto3" json:"LeaderID,omitempty"`
}

func (x *RemoveUserRequest) Reset() {
	*x = RemoveUserRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_department_department_proto_msgTypes[52]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RemoveUserRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RemoveUserRequest) ProtoMessage() {}

func (x *RemoveUserRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_department_department_proto_msgTypes[52]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RemoveUserRequest.ProtoReflect.Descriptor instead.
func (*RemoveUserRequest) Descriptor() ([]byte, []int) {
	return file_api_department_department_proto_rawDescGZIP(), []int{52}
}

func (x *RemoveUserRequest) GetDomain() string {
	if x != nil {
		return x.Domain
	}
	return ""
}

func (x *RemoveUserRequest) GetLeaderID() uint64 {
	if x != nil {
		return x.LeaderID
	}
	return 0
}

type RemoveUserResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *RemoveUserResponse) Reset() {
	*x = RemoveUserResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_department_department_proto_msgTypes[53]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RemoveUserResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RemoveUserResponse) ProtoMessage() {}

func (x *RemoveUserResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_department_department_proto_msgTypes[53]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RemoveUserResponse.ProtoReflect.Descriptor instead.
func (*RemoveUserResponse) Descriptor() ([]byte, []int) {
	return file_api_department_department_proto_rawDescGZIP(), []int{53}
}

type RemoveRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ID     uint64 `protobuf:"varint,1,opt,name=ID,proto3" json:"ID,omitempty"`
	Domain string `protobuf:"bytes,2,opt,name=Domain,json=departmentID,proto3" json:"Domain,omitempty"`
}

func (x *RemoveRequest) Reset() {
	*x = RemoveRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_department_department_proto_msgTypes[54]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RemoveRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RemoveRequest) ProtoMessage() {}

func (x *RemoveRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_department_department_proto_msgTypes[54]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RemoveRequest.ProtoReflect.Descriptor instead.
func (*RemoveRequest) Descriptor() ([]byte, []int) {
	return file_api_department_department_proto_rawDescGZIP(), []int{54}
}

func (x *RemoveRequest) GetID() uint64 {
	if x != nil {
		return x.ID
	}
	return 0
}

func (x *RemoveRequest) GetDomain() string {
	if x != nil {
		return x.Domain
	}
	return ""
}

type RemoveResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *RemoveResponse) Reset() {
	*x = RemoveResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_department_department_proto_msgTypes[55]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RemoveResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RemoveResponse) ProtoMessage() {}

func (x *RemoveResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_department_department_proto_msgTypes[55]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RemoveResponse.ProtoReflect.Descriptor instead.
func (*RemoveResponse) Descriptor() ([]byte, []int) {
	return file_api_department_department_proto_rawDescGZIP(), []int{55}
}

type DetailsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	IDs    []uint64 `protobuf:"varint,1,rep,packed,name=IDs,proto3" json:"IDs,omitempty"`
	Domain string   `protobuf:"bytes,2,opt,name=Domain,proto3" json:"Domain,omitempty"`
}

func (x *DetailsRequest) Reset() {
	*x = DetailsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_department_department_proto_msgTypes[56]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DetailsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DetailsRequest) ProtoMessage() {}

func (x *DetailsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_department_department_proto_msgTypes[56]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DetailsRequest.ProtoReflect.Descriptor instead.
func (*DetailsRequest) Descriptor() ([]byte, []int) {
	return file_api_department_department_proto_rawDescGZIP(), []int{56}
}

func (x *DetailsRequest) GetIDs() []uint64 {
	if x != nil {
		return x.IDs
	}
	return nil
}

func (x *DetailsRequest) GetDomain() string {
	if x != nil {
		return x.Domain
	}
	return ""
}

type DetailRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ID     int64  `protobuf:"varint,1,opt,name=ID,proto3" json:"ID,omitempty"`
	Domain string `protobuf:"bytes,2,opt,name=Domain,proto3" json:"Domain,omitempty"`
}

func (x *DetailRequest) Reset() {
	*x = DetailRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_department_department_proto_msgTypes[57]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DetailRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DetailRequest) ProtoMessage() {}

func (x *DetailRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_department_department_proto_msgTypes[57]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DetailRequest.ProtoReflect.Descriptor instead.
func (*DetailRequest) Descriptor() ([]byte, []int) {
	return file_api_department_department_proto_rawDescGZIP(), []int{57}
}

func (x *DetailRequest) GetID() int64 {
	if x != nil {
		return x.ID
	}
	return 0
}

func (x *DetailRequest) GetDomain() string {
	if x != nil {
		return x.Domain
	}
	return ""
}

type DetailsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Data []*DetailResponse `protobuf:"bytes,1,rep,name=data,proto3" json:"data,omitempty"`
}

func (x *DetailsResponse) Reset() {
	*x = DetailsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_department_department_proto_msgTypes[58]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DetailsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DetailsResponse) ProtoMessage() {}

func (x *DetailsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_department_department_proto_msgTypes[58]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DetailsResponse.ProtoReflect.Descriptor instead.
func (*DetailsResponse) Descriptor() ([]byte, []int) {
	return file_api_department_department_proto_rawDescGZIP(), []int{58}
}

func (x *DetailsResponse) GetData() []*DetailResponse {
	if x != nil {
		return x.Data
	}
	return nil
}

type DetailResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ID             uint64            `protobuf:"varint,1,opt,name=ID,proto3" json:"ID,omitempty"`
	Name           string            `protobuf:"bytes,2,opt,name=Name,json=name,proto3" json:"Name,omitempty"`
	Remark         string            `protobuf:"bytes,3,opt,name=Remark,json=remark,proto3" json:"Remark,omitempty"`
	LeaderID       int64             `protobuf:"varint,4,opt,name=LeaderID,json=leaderID,proto3" json:"LeaderID,omitempty"`
	LeaderName     string            `protobuf:"bytes,5,opt,name=LeaderName,json=leaderName,proto3" json:"LeaderName,omitempty"`
	Rules          []*DepartmentRule `protobuf:"bytes,6,rep,name=Rules,json=rules,proto3" json:"Rules,omitempty"` //部门本身的权限
	DepartmentCode string            `protobuf:"bytes,14,opt,name=DepartmentCode,json=departmentCode,proto3" json:"DepartmentCode,omitempty"`
	//repeated Leader               Leaders       = 7  [json_name = "leader"];
	LeaderPosition     *Position     `protobuf:"bytes,7,opt,name=LeaderPosition,json=leaderPosition,proto3" json:"LeaderPosition,omitempty"`
	Positions          []*Position   `protobuf:"bytes,8,rep,name=Positions,json=positions,proto3" json:"Positions,omitempty"`
	AllPositions       []*Position   `protobuf:"bytes,9,rep,name=AllPositions,json=allPositions,proto3" json:"AllPositions,omitempty"`
	DepartmentTreeRule []*RuleDetail `protobuf:"bytes,10,rep,name=DepartmentTreeRule,json=departmentTreeRule,proto3" json:"DepartmentTreeRule,omitempty"`
	Sync               bool          `protobuf:"varint,11,opt,name=sync,proto3" json:"sync,omitempty"`
	SyncID             string        `protobuf:"bytes,12,opt,name=syncID,proto3" json:"syncID,omitempty"`
	CreatedAt          string        `protobuf:"bytes,13,opt,name=CreatedAt,json=createdAt,proto3" json:"CreatedAt,omitempty"`
	Num                int32         `protobuf:"varint,15,opt,name=Num,json=num,proto3" json:"Num,omitempty"`
	Pid                uint32        `protobuf:"varint,16,opt,name=pid,proto3" json:"pid,omitempty"`
	Level              uint32        `protobuf:"varint,17,opt,name=level,proto3" json:"level,omitempty"`
	LevelPath          string        `protobuf:"bytes,18,opt,name=levelPath,proto3" json:"levelPath,omitempty"`
	PName              string        `protobuf:"bytes,19,opt,name=pName,proto3" json:"pName,omitempty"`
	UpdatedAt          string        `protobuf:"bytes,20,opt,name=updatedAt,proto3" json:"updatedAt,omitempty"`
	FlagNum            uint32        `protobuf:"varint,21,opt,name=flagNum,proto3" json:"flagNum,omitempty"`
}

func (x *DetailResponse) Reset() {
	*x = DetailResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_department_department_proto_msgTypes[59]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DetailResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DetailResponse) ProtoMessage() {}

func (x *DetailResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_department_department_proto_msgTypes[59]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DetailResponse.ProtoReflect.Descriptor instead.
func (*DetailResponse) Descriptor() ([]byte, []int) {
	return file_api_department_department_proto_rawDescGZIP(), []int{59}
}

func (x *DetailResponse) GetID() uint64 {
	if x != nil {
		return x.ID
	}
	return 0
}

func (x *DetailResponse) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *DetailResponse) GetRemark() string {
	if x != nil {
		return x.Remark
	}
	return ""
}

func (x *DetailResponse) GetLeaderID() int64 {
	if x != nil {
		return x.LeaderID
	}
	return 0
}

func (x *DetailResponse) GetLeaderName() string {
	if x != nil {
		return x.LeaderName
	}
	return ""
}

func (x *DetailResponse) GetRules() []*DepartmentRule {
	if x != nil {
		return x.Rules
	}
	return nil
}

func (x *DetailResponse) GetDepartmentCode() string {
	if x != nil {
		return x.DepartmentCode
	}
	return ""
}

func (x *DetailResponse) GetLeaderPosition() *Position {
	if x != nil {
		return x.LeaderPosition
	}
	return nil
}

func (x *DetailResponse) GetPositions() []*Position {
	if x != nil {
		return x.Positions
	}
	return nil
}

func (x *DetailResponse) GetAllPositions() []*Position {
	if x != nil {
		return x.AllPositions
	}
	return nil
}

func (x *DetailResponse) GetDepartmentTreeRule() []*RuleDetail {
	if x != nil {
		return x.DepartmentTreeRule
	}
	return nil
}

func (x *DetailResponse) GetSync() bool {
	if x != nil {
		return x.Sync
	}
	return false
}

func (x *DetailResponse) GetSyncID() string {
	if x != nil {
		return x.SyncID
	}
	return ""
}

func (x *DetailResponse) GetCreatedAt() string {
	if x != nil {
		return x.CreatedAt
	}
	return ""
}

func (x *DetailResponse) GetNum() int32 {
	if x != nil {
		return x.Num
	}
	return 0
}

func (x *DetailResponse) GetPid() uint32 {
	if x != nil {
		return x.Pid
	}
	return 0
}

func (x *DetailResponse) GetLevel() uint32 {
	if x != nil {
		return x.Level
	}
	return 0
}

func (x *DetailResponse) GetLevelPath() string {
	if x != nil {
		return x.LevelPath
	}
	return ""
}

func (x *DetailResponse) GetPName() string {
	if x != nil {
		return x.PName
	}
	return ""
}

func (x *DetailResponse) GetUpdatedAt() string {
	if x != nil {
		return x.UpdatedAt
	}
	return ""
}

func (x *DetailResponse) GetFlagNum() uint32 {
	if x != nil {
		return x.FlagNum
	}
	return 0
}

type Leader struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ID   int64  `protobuf:"varint,1,opt,name=ID,proto3" json:"ID,omitempty"`
	Name string `protobuf:"bytes,2,opt,name=Name,json=name,proto3" json:"Name,omitempty"`
}

func (x *Leader) Reset() {
	*x = Leader{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_department_department_proto_msgTypes[60]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Leader) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Leader) ProtoMessage() {}

func (x *Leader) ProtoReflect() protoreflect.Message {
	mi := &file_api_department_department_proto_msgTypes[60]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Leader.ProtoReflect.Descriptor instead.
func (*Leader) Descriptor() ([]byte, []int) {
	return file_api_department_department_proto_rawDescGZIP(), []int{60}
}

func (x *Leader) GetID() int64 {
	if x != nil {
		return x.ID
	}
	return 0
}

func (x *Leader) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

type Position struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ID           int64       `protobuf:"varint,1,opt,name=ID,proto3" json:"ID,omitempty"`
	Name         string      `protobuf:"bytes,2,opt,name=Name,json=name,proto3" json:"Name,omitempty"`
	Rules        []*RuleInfo `protobuf:"bytes,3,rep,name=Rules,json=rules,proto3" json:"Rules,omitempty"`
	Users        []*UserInfo `protobuf:"bytes,4,rep,name=Users,json=users,proto3" json:"Users,omitempty"`
	PositionCode string      `protobuf:"bytes,5,opt,name=PositionCode,json=positionCode,proto3" json:"PositionCode,omitempty"`
}

func (x *Position) Reset() {
	*x = Position{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_department_department_proto_msgTypes[61]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Position) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Position) ProtoMessage() {}

func (x *Position) ProtoReflect() protoreflect.Message {
	mi := &file_api_department_department_proto_msgTypes[61]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Position.ProtoReflect.Descriptor instead.
func (*Position) Descriptor() ([]byte, []int) {
	return file_api_department_department_proto_rawDescGZIP(), []int{61}
}

func (x *Position) GetID() int64 {
	if x != nil {
		return x.ID
	}
	return 0
}

func (x *Position) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *Position) GetRules() []*RuleInfo {
	if x != nil {
		return x.Rules
	}
	return nil
}

func (x *Position) GetUsers() []*UserInfo {
	if x != nil {
		return x.Users
	}
	return nil
}

func (x *Position) GetPositionCode() string {
	if x != nil {
		return x.PositionCode
	}
	return ""
}

type CreateRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ID                 uint64        `protobuf:"varint,1,opt,name=ID,proto3" json:"ID,omitempty"`
	Name               string        `protobuf:"bytes,2,opt,name=Name,json=name,proto3" json:"Name,omitempty"`
	Remark             string        `protobuf:"bytes,3,opt,name=Remark,json=remark,proto3" json:"Remark,omitempty"`
	Domain             string        `protobuf:"bytes,4,opt,name=Domain,json=domain,proto3" json:"Domain,omitempty"`
	Rules              []*Rule       `protobuf:"bytes,6,rep,name=Rules,json=rules,proto3" json:"Rules,omitempty"`
	Leaders            []*Leader     `protobuf:"bytes,7,rep,name=Leaders,json=leaders,proto3" json:"Leaders,omitempty"`
	DepartmentCode     string        `protobuf:"bytes,8,opt,name=DepartmentCode,json=departmentCode,proto3" json:"DepartmentCode,omitempty"`
	DepartmentTreeRule []*RuleDetail `protobuf:"bytes,9,rep,name=DepartmentTreeRule,json=departmentTreeRule,proto3" json:"DepartmentTreeRule,omitempty"`
	Sync               bool          `protobuf:"varint,10,opt,name=sync,proto3" json:"sync,omitempty"`
	SyncID             string        `protobuf:"bytes,11,opt,name=syncID,proto3" json:"syncID,omitempty"`
	Pid                uint32        `protobuf:"varint,12,opt,name=pid,proto3" json:"pid,omitempty"`
}

func (x *CreateRequest) Reset() {
	*x = CreateRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_department_department_proto_msgTypes[62]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateRequest) ProtoMessage() {}

func (x *CreateRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_department_department_proto_msgTypes[62]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateRequest.ProtoReflect.Descriptor instead.
func (*CreateRequest) Descriptor() ([]byte, []int) {
	return file_api_department_department_proto_rawDescGZIP(), []int{62}
}

func (x *CreateRequest) GetID() uint64 {
	if x != nil {
		return x.ID
	}
	return 0
}

func (x *CreateRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *CreateRequest) GetRemark() string {
	if x != nil {
		return x.Remark
	}
	return ""
}

func (x *CreateRequest) GetDomain() string {
	if x != nil {
		return x.Domain
	}
	return ""
}

func (x *CreateRequest) GetRules() []*Rule {
	if x != nil {
		return x.Rules
	}
	return nil
}

func (x *CreateRequest) GetLeaders() []*Leader {
	if x != nil {
		return x.Leaders
	}
	return nil
}

func (x *CreateRequest) GetDepartmentCode() string {
	if x != nil {
		return x.DepartmentCode
	}
	return ""
}

func (x *CreateRequest) GetDepartmentTreeRule() []*RuleDetail {
	if x != nil {
		return x.DepartmentTreeRule
	}
	return nil
}

func (x *CreateRequest) GetSync() bool {
	if x != nil {
		return x.Sync
	}
	return false
}

func (x *CreateRequest) GetSyncID() string {
	if x != nil {
		return x.SyncID
	}
	return ""
}

func (x *CreateRequest) GetPid() uint32 {
	if x != nil {
		return x.Pid
	}
	return 0
}

type UpdateResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *UpdateResponse) Reset() {
	*x = UpdateResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_department_department_proto_msgTypes[63]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateResponse) ProtoMessage() {}

func (x *UpdateResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_department_department_proto_msgTypes[63]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateResponse.ProtoReflect.Descriptor instead.
func (*UpdateResponse) Descriptor() ([]byte, []int) {
	return file_api_department_department_proto_rawDescGZIP(), []int{63}
}

//创建
type CreateResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ID uint64 `protobuf:"varint,1,opt,name=ID,proto3" json:"ID,omitempty"`
}

func (x *CreateResponse) Reset() {
	*x = CreateResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_department_department_proto_msgTypes[64]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateResponse) ProtoMessage() {}

func (x *CreateResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_department_department_proto_msgTypes[64]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateResponse.ProtoReflect.Descriptor instead.
func (*CreateResponse) Descriptor() ([]byte, []int) {
	return file_api_department_department_proto_rawDescGZIP(), []int{64}
}

func (x *CreateResponse) GetID() uint64 {
	if x != nil {
		return x.ID
	}
	return 0
}

type ListRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	LeaderID uint64 `protobuf:"varint,1,opt,name=LeaderID,json=leaderID,proto3" json:"LeaderID,omitempty"`
	Domain   string `protobuf:"bytes,2,opt,name=Domain,json=domain,proto3" json:"Domain,omitempty"`
	PageSize uint64 `protobuf:"varint,3,opt,name=PageSize,json=pageSize,proto3" json:"PageSize,omitempty"`
	Page     uint64 `protobuf:"varint,4,opt,name=Page,json=page,proto3" json:"Page,omitempty"`
	Key      string `protobuf:"bytes,5,opt,name=Key,json=key,proto3" json:"Key,omitempty"`
}

func (x *ListRequest) Reset() {
	*x = ListRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_department_department_proto_msgTypes[65]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListRequest) ProtoMessage() {}

func (x *ListRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_department_department_proto_msgTypes[65]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListRequest.ProtoReflect.Descriptor instead.
func (*ListRequest) Descriptor() ([]byte, []int) {
	return file_api_department_department_proto_rawDescGZIP(), []int{65}
}

func (x *ListRequest) GetLeaderID() uint64 {
	if x != nil {
		return x.LeaderID
	}
	return 0
}

func (x *ListRequest) GetDomain() string {
	if x != nil {
		return x.Domain
	}
	return ""
}

func (x *ListRequest) GetPageSize() uint64 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *ListRequest) GetPage() uint64 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *ListRequest) GetKey() string {
	if x != nil {
		return x.Key
	}
	return ""
}

type RuleData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	DataField []string `protobuf:"bytes,1,rep,name=DataField,json=dataField,proto3" json:"DataField,omitempty"`
	ID        uint64   `protobuf:"varint,2,opt,name=ID,proto3" json:"ID,omitempty"`
	Title     string   `protobuf:"bytes,3,opt,name=Title,proto3" json:"Title,omitempty"`
}

func (x *RuleData) Reset() {
	*x = RuleData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_department_department_proto_msgTypes[66]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RuleData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RuleData) ProtoMessage() {}

func (x *RuleData) ProtoReflect() protoreflect.Message {
	mi := &file_api_department_department_proto_msgTypes[66]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RuleData.ProtoReflect.Descriptor instead.
func (*RuleData) Descriptor() ([]byte, []int) {
	return file_api_department_department_proto_rawDescGZIP(), []int{66}
}

func (x *RuleData) GetDataField() []string {
	if x != nil {
		return x.DataField
	}
	return nil
}

func (x *RuleData) GetID() uint64 {
	if x != nil {
		return x.ID
	}
	return 0
}

func (x *RuleData) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

//列表
type Rule struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ID       uint64      `protobuf:"varint,1,opt,name=ID,proto3" json:"ID,omitempty"`
	Name     string      `protobuf:"bytes,2,opt,name=Name,proto3" json:"Name,omitempty"`
	RuleData []*RuleData `protobuf:"bytes,3,rep,name=RuleData,proto3" json:"RuleData,omitempty"`
}

func (x *Rule) Reset() {
	*x = Rule{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_department_department_proto_msgTypes[67]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Rule) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Rule) ProtoMessage() {}

func (x *Rule) ProtoReflect() protoreflect.Message {
	mi := &file_api_department_department_proto_msgTypes[67]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Rule.ProtoReflect.Descriptor instead.
func (*Rule) Descriptor() ([]byte, []int) {
	return file_api_department_department_proto_rawDescGZIP(), []int{67}
}

func (x *Rule) GetID() uint64 {
	if x != nil {
		return x.ID
	}
	return 0
}

func (x *Rule) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *Rule) GetRuleData() []*RuleData {
	if x != nil {
		return x.RuleData
	}
	return nil
}

//权限信息
type RuleInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ID            uint64   `protobuf:"varint,1,opt,name=ID,proto3" json:"ID,omitempty"`
	Title         string   `protobuf:"bytes,2,opt,name=Title,proto3" json:"Title,omitempty"`
	Url           string   `protobuf:"bytes,3,opt,name=Url,proto3" json:"Url,omitempty"`
	Method        string   `protobuf:"bytes,4,opt,name=Method,proto3" json:"Method,omitempty"`
	RuleDataID    uint64   `protobuf:"varint,5,opt,name=RuleDataID,proto3" json:"RuleDataID,omitempty"`
	RuleDataName  string   `protobuf:"bytes,6,opt,name=RuleDataName,proto3" json:"RuleDataName,omitempty"`
	RuleDataField []string `protobuf:"bytes,7,rep,name=RuleDataField,proto3" json:"RuleDataField,omitempty"`
}

func (x *RuleInfo) Reset() {
	*x = RuleInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_department_department_proto_msgTypes[68]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RuleInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RuleInfo) ProtoMessage() {}

func (x *RuleInfo) ProtoReflect() protoreflect.Message {
	mi := &file_api_department_department_proto_msgTypes[68]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RuleInfo.ProtoReflect.Descriptor instead.
func (*RuleInfo) Descriptor() ([]byte, []int) {
	return file_api_department_department_proto_rawDescGZIP(), []int{68}
}

func (x *RuleInfo) GetID() uint64 {
	if x != nil {
		return x.ID
	}
	return 0
}

func (x *RuleInfo) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

func (x *RuleInfo) GetUrl() string {
	if x != nil {
		return x.Url
	}
	return ""
}

func (x *RuleInfo) GetMethod() string {
	if x != nil {
		return x.Method
	}
	return ""
}

func (x *RuleInfo) GetRuleDataID() uint64 {
	if x != nil {
		return x.RuleDataID
	}
	return 0
}

func (x *RuleInfo) GetRuleDataName() string {
	if x != nil {
		return x.RuleDataName
	}
	return ""
}

func (x *RuleInfo) GetRuleDataField() []string {
	if x != nil {
		return x.RuleDataField
	}
	return nil
}

type DepartmentRule struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ID       uint64      `protobuf:"varint,1,opt,name=ID,proto3" json:"ID,omitempty"`
	Title    string      `protobuf:"bytes,2,opt,name=Title,proto3" json:"Title,omitempty"`
	Url      string      `protobuf:"bytes,3,opt,name=Url,proto3" json:"Url,omitempty"`
	Method   string      `protobuf:"bytes,4,opt,name=Method,proto3" json:"Method,omitempty"`
	RuleData []*RuleData `protobuf:"bytes,7,rep,name=RuleData,proto3" json:"RuleData,omitempty"`
}

func (x *DepartmentRule) Reset() {
	*x = DepartmentRule{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_department_department_proto_msgTypes[69]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DepartmentRule) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DepartmentRule) ProtoMessage() {}

func (x *DepartmentRule) ProtoReflect() protoreflect.Message {
	mi := &file_api_department_department_proto_msgTypes[69]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DepartmentRule.ProtoReflect.Descriptor instead.
func (*DepartmentRule) Descriptor() ([]byte, []int) {
	return file_api_department_department_proto_rawDescGZIP(), []int{69}
}

func (x *DepartmentRule) GetID() uint64 {
	if x != nil {
		return x.ID
	}
	return 0
}

func (x *DepartmentRule) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

func (x *DepartmentRule) GetUrl() string {
	if x != nil {
		return x.Url
	}
	return ""
}

func (x *DepartmentRule) GetMethod() string {
	if x != nil {
		return x.Method
	}
	return ""
}

func (x *DepartmentRule) GetRuleData() []*RuleData {
	if x != nil {
		return x.RuleData
	}
	return nil
}

type ListResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Count uint64            `protobuf:"varint,1,opt,name=Count,proto3" json:"Count,omitempty"`
	Data  []*DetailResponse `protobuf:"bytes,3,rep,name=Data,proto3" json:"Data,omitempty"`
}

func (x *ListResponse) Reset() {
	*x = ListResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_department_department_proto_msgTypes[70]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListResponse) ProtoMessage() {}

func (x *ListResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_department_department_proto_msgTypes[70]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListResponse.ProtoReflect.Descriptor instead.
func (*ListResponse) Descriptor() ([]byte, []int) {
	return file_api_department_department_proto_rawDescGZIP(), []int{70}
}

func (x *ListResponse) GetCount() uint64 {
	if x != nil {
		return x.Count
	}
	return 0
}

func (x *ListResponse) GetData() []*DetailResponse {
	if x != nil {
		return x.Data
	}
	return nil
}

type RuleDetail struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ID       uint64        `protobuf:"varint,1,opt,name=ID,proto3" json:"ID,omitempty"`
	Type     string        `protobuf:"bytes,2,opt,name=Type,json=type,proto3" json:"Type,omitempty"`
	Pid      uint64        `protobuf:"varint,3,opt,name=Pid,json=pid,proto3" json:"Pid,omitempty"`
	Title    string        `protobuf:"bytes,4,opt,name=Title,json=title,proto3" json:"Title,omitempty"`
	Icon     string        `protobuf:"bytes,5,opt,name=Icon,json=icon,proto3" json:"Icon,omitempty"`
	Url      string        `protobuf:"bytes,6,opt,name=Url,json=url,proto3" json:"Url,omitempty"`
	Method   string        `protobuf:"bytes,7,opt,name=Method,json=method,proto3" json:"Method,omitempty"`
	Weigh    uint64        `protobuf:"varint,8,opt,name=Weigh,json=weigh,proto3" json:"Weigh,omitempty"`
	Status   string        `protobuf:"bytes,9,opt,name=Status,json=status,proto3" json:"Status,omitempty"`
	Remark   string        `protobuf:"bytes,10,opt,name=Remark,json=remark,proto3" json:"Remark,omitempty"`
	Extend   string        `protobuf:"bytes,11,opt,name=Extend,json=extend,proto3" json:"Extend,omitempty"`
	RuleData []*RuleData   `protobuf:"bytes,12,rep,name=RuleData,json=ruleData,proto3" json:"RuleData,omitempty"`
	Son      []*RuleDetail `protobuf:"bytes,13,rep,name=Son,json=rules,proto3" json:"Son,omitempty"`
	GrayIcon string        `protobuf:"bytes,19,opt,name=grayIcon,proto3" json:"grayIcon,omitempty"`
}

func (x *RuleDetail) Reset() {
	*x = RuleDetail{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_department_department_proto_msgTypes[71]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RuleDetail) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RuleDetail) ProtoMessage() {}

func (x *RuleDetail) ProtoReflect() protoreflect.Message {
	mi := &file_api_department_department_proto_msgTypes[71]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RuleDetail.ProtoReflect.Descriptor instead.
func (*RuleDetail) Descriptor() ([]byte, []int) {
	return file_api_department_department_proto_rawDescGZIP(), []int{71}
}

func (x *RuleDetail) GetID() uint64 {
	if x != nil {
		return x.ID
	}
	return 0
}

func (x *RuleDetail) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

func (x *RuleDetail) GetPid() uint64 {
	if x != nil {
		return x.Pid
	}
	return 0
}

func (x *RuleDetail) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

func (x *RuleDetail) GetIcon() string {
	if x != nil {
		return x.Icon
	}
	return ""
}

func (x *RuleDetail) GetUrl() string {
	if x != nil {
		return x.Url
	}
	return ""
}

func (x *RuleDetail) GetMethod() string {
	if x != nil {
		return x.Method
	}
	return ""
}

func (x *RuleDetail) GetWeigh() uint64 {
	if x != nil {
		return x.Weigh
	}
	return 0
}

func (x *RuleDetail) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

func (x *RuleDetail) GetRemark() string {
	if x != nil {
		return x.Remark
	}
	return ""
}

func (x *RuleDetail) GetExtend() string {
	if x != nil {
		return x.Extend
	}
	return ""
}

func (x *RuleDetail) GetRuleData() []*RuleData {
	if x != nil {
		return x.RuleData
	}
	return nil
}

func (x *RuleDetail) GetSon() []*RuleDetail {
	if x != nil {
		return x.Son
	}
	return nil
}

func (x *RuleDetail) GetGrayIcon() string {
	if x != nil {
		return x.GrayIcon
	}
	return ""
}

var File_api_department_department_proto protoreflect.FileDescriptor

var file_api_department_department_proto_rawDesc = []byte{
	0x0a, 0x1f, 0x61, 0x70, 0x69, 0x2f, 0x64, 0x65, 0x70, 0x61, 0x72, 0x74, 0x6d, 0x65, 0x6e, 0x74,
	0x2f, 0x64, 0x65, 0x70, 0x61, 0x72, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x12, 0x0a, 0x64, 0x65, 0x70, 0x61, 0x72, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x1a, 0x3d, 0x67,
	0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x6d, 0x77, 0x69, 0x74, 0x6b, 0x6f,
	0x77, 0x2f, 0x67, 0x6f, 0x2d, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2d, 0x76, 0x61, 0x6c, 0x69, 0x64,
	0x61, 0x74, 0x6f, 0x72, 0x73, 0x40, 0x76, 0x30, 0x2e, 0x33, 0x2e, 0x32, 0x2f, 0x76, 0x61, 0x6c,
	0x69, 0x64, 0x61, 0x74, 0x6f, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x66, 0x0a, 0x1a,
	0x46, 0x69, 0x6e, 0x64, 0x44, 0x65, 0x70, 0x61, 0x72, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x48, 0x65,
	0x61, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x32, 0x0a, 0x04, 0x6c, 0x69,
	0x73, 0x74, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x64, 0x65, 0x70, 0x61, 0x72,
	0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x46, 0x69, 0x6e, 0x64, 0x44, 0x65, 0x70, 0x61, 0x72, 0x74,
	0x6d, 0x65, 0x6e, 0x74, 0x48, 0x65, 0x61, 0x64, 0x52, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x12, 0x14,
	0x0a, 0x05, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x04, 0x52, 0x05, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x22, 0x8c, 0x01, 0x0a, 0x12, 0x46, 0x69, 0x6e, 0x64, 0x44, 0x65, 0x70,
	0x61, 0x72, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x48, 0x65, 0x61, 0x64, 0x12, 0x0e, 0x0a, 0x02, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x02, 0x69, 0x64, 0x12, 0x18, 0x0a, 0x07, 0x73,
	0x74, 0x6f, 0x72, 0x65, 0x49, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x04, 0x52, 0x07, 0x73, 0x74,
	0x6f, 0x72, 0x65, 0x49, 0x64, 0x12, 0x1c, 0x0a, 0x09, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x4e, 0x61,
	0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x4e,
	0x61, 0x6d, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x61, 0x6c, 0x69, 0x61, 0x73, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x05, 0x61, 0x6c, 0x69, 0x61, 0x73, 0x12, 0x18, 0x0a, 0x07, 0x61, 0x64, 0x64,
	0x72, 0x65, 0x73, 0x73, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x64, 0x64, 0x72,
	0x65, 0x73, 0x73, 0x22, 0x53, 0x0a, 0x19, 0x46, 0x69, 0x6e, 0x64, 0x44, 0x65, 0x70, 0x61, 0x72,
	0x74, 0x6d, 0x65, 0x6e, 0x74, 0x48, 0x65, 0x61, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x12, 0x18, 0x0a, 0x07, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x04, 0x52, 0x07, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x49, 0x64, 0x12, 0x1c, 0x0a, 0x09, 0x73, 0x74,
	0x6f, 0x72, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x73,
	0x74, 0x6f, 0x72, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x22, 0x2e, 0x0a, 0x1c, 0x43, 0x72, 0x65, 0x61,
	0x74, 0x65, 0x44, 0x65, 0x70, 0x61, 0x72, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x48, 0x65, 0x61, 0x64,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x04, 0x52, 0x02, 0x69, 0x64, 0x22, 0x85, 0x01, 0x0a, 0x1b, 0x43, 0x72, 0x65,
	0x61, 0x74, 0x65, 0x44, 0x65, 0x70, 0x61, 0x72, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x48, 0x65, 0x61,
	0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x18, 0x0a, 0x07, 0x73, 0x74, 0x6f, 0x72,
	0x65, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x07, 0x73, 0x74, 0x6f, 0x72, 0x65,
	0x49, 0x64, 0x12, 0x1c, 0x0a, 0x09, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x4e, 0x61, 0x6d, 0x65,
	0x12, 0x14, 0x0a, 0x05, 0x61, 0x6c, 0x69, 0x61, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x05, 0x61, 0x6c, 0x69, 0x61, 0x73, 0x12, 0x18, 0x0a, 0x07, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73,
	0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73,
	0x22, 0x38, 0x0a, 0x1c, 0x55, 0x73, 0x65, 0x72, 0x49, 0x64, 0x73, 0x42, 0x79, 0x46, 0x61, 0x74,
	0x68, 0x65, 0x72, 0x44, 0x65, 0x70, 0x49, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x12, 0x18, 0x0a, 0x07, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28,
	0x0d, 0x52, 0x07, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x73, 0x22, 0x53, 0x0a, 0x1b, 0x55, 0x73,
	0x65, 0x72, 0x49, 0x64, 0x73, 0x42, 0x79, 0x46, 0x61, 0x74, 0x68, 0x65, 0x72, 0x44, 0x65, 0x70,
	0x49, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x16, 0x0a, 0x06, 0x64, 0x6f, 0x6d,
	0x61, 0x69, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x64, 0x6f, 0x6d, 0x61, 0x69,
	0x6e, 0x12, 0x1c, 0x0a, 0x09, 0x66, 0x61, 0x74, 0x68, 0x65, 0x72, 0x49, 0x64, 0x73, 0x18, 0x02,
	0x20, 0x03, 0x28, 0x0d, 0x52, 0x09, 0x66, 0x61, 0x74, 0x68, 0x65, 0x72, 0x49, 0x64, 0x73, 0x22,
	0xd1, 0x01, 0x0a, 0x11, 0x42, 0x61, 0x73, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x56, 0x32, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x16, 0x0a, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x12, 0x12, 0x0a,
	0x04, 0x70, 0x61, 0x67, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x04, 0x70, 0x61, 0x67,
	0x65, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x61, 0x67, 0x65, 0x53, 0x69, 0x7a, 0x65, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x0d, 0x52, 0x08, 0x70, 0x61, 0x67, 0x65, 0x53, 0x69, 0x7a, 0x65, 0x12, 0x22, 0x0a,
	0x0c, 0x64, 0x65, 0x70, 0x61, 0x72, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x0d, 0x52, 0x0c, 0x64, 0x65, 0x70, 0x61, 0x72, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x49,
	0x64, 0x12, 0x2a, 0x0a, 0x10, 0x6e, 0x6f, 0x74, 0x44, 0x65, 0x70, 0x61, 0x72, 0x74, 0x6d, 0x65,
	0x6e, 0x74, 0x49, 0x64, 0x73, 0x18, 0x05, 0x20, 0x03, 0x28, 0x0d, 0x52, 0x10, 0x6e, 0x6f, 0x74,
	0x44, 0x65, 0x70, 0x61, 0x72, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x73, 0x12, 0x24, 0x0a,
	0x0d, 0x64, 0x65, 0x70, 0x61, 0x72, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x73, 0x18, 0x06,
	0x20, 0x03, 0x28, 0x0d, 0x52, 0x0d, 0x64, 0x65, 0x70, 0x61, 0x72, 0x74, 0x6d, 0x65, 0x6e, 0x74,
	0x49, 0x64, 0x73, 0x22, 0x60, 0x0a, 0x12, 0x42, 0x61, 0x73, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x56,
	0x32, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x05, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x12,
	0x34, 0x0a, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x20, 0x2e,
	0x64, 0x65, 0x70, 0x61, 0x72, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x42, 0x61, 0x73, 0x65, 0x44,
	0x65, 0x74, 0x61, 0x69, 0x6c, 0x56, 0x32, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x52,
	0x04, 0x6c, 0x69, 0x73, 0x74, 0x22, 0x90, 0x01, 0x0a, 0x14, 0x42, 0x61, 0x73, 0x65, 0x44, 0x65,
	0x74, 0x61, 0x69, 0x6c, 0x56, 0x32, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x0e,
	0x0a, 0x02, 0x49, 0x44, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x02, 0x49, 0x44, 0x12, 0x12,
	0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61,
	0x6d, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x72, 0x65, 0x6d, 0x61, 0x72, 0x6b, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x06, 0x72, 0x65, 0x6d, 0x61, 0x72, 0x6b, 0x12, 0x26, 0x0a, 0x0e, 0x64, 0x65,
	0x70, 0x61, 0x72, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x43, 0x6f, 0x64, 0x65, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0e, 0x64, 0x65, 0x70, 0x61, 0x72, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x43, 0x6f,
	0x64, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x6c, 0x65, 0x76, 0x65, 0x6c, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x0d, 0x52, 0x05, 0x6c, 0x65, 0x76, 0x65, 0x6c, 0x22, 0x32, 0x0a, 0x14, 0x55, 0x73, 0x65, 0x72,
	0x49, 0x73, 0x4c, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x12, 0x1a, 0x0a, 0x08, 0x69, 0x73, 0x4c, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x08, 0x52, 0x08, 0x69, 0x73, 0x4c, 0x65, 0x61, 0x64, 0x65, 0x72, 0x22, 0x50, 0x0a, 0x1a,
	0x46, 0x69, 0x6e, 0x64, 0x4c, 0x65, 0x61, 0x64, 0x65, 0x72, 0x73, 0x46, 0x72, 0x6f, 0x6d, 0x44,
	0x49, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x32, 0x0a, 0x04, 0x6c, 0x69,
	0x73, 0x74, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x64, 0x65, 0x70, 0x61, 0x72,
	0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x4c, 0x65, 0x61, 0x64, 0x65, 0x72, 0x4e, 0x61, 0x6d, 0x65,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x52, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x22, 0x79,
	0x0a, 0x21, 0x4d, 0x79, 0x44, 0x65, 0x70, 0x61, 0x72, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x44,
	0x69, 0x72, 0x65, 0x63, 0x74, 0x4c, 0x65, 0x61, 0x64, 0x65, 0x72, 0x73, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x12, 0x16, 0x0a, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0d, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x12, 0x24, 0x0a, 0x0d, 0x64,
	0x65, 0x70, 0x61, 0x72, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x73, 0x18, 0x03, 0x20, 0x03,
	0x28, 0x04, 0x52, 0x0d, 0x64, 0x65, 0x70, 0x61, 0x72, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x49, 0x64,
	0x73, 0x12, 0x16, 0x0a, 0x06, 0x64, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x06, 0x64, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x22, 0x82, 0x01, 0x0a, 0x16, 0x4d, 0x79,
	0x44, 0x69, 0x72, 0x65, 0x63, 0x74, 0x4c, 0x65, 0x61, 0x64, 0x65, 0x72, 0x73, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x12, 0x16, 0x0a, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0d, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x12, 0x14, 0x0a, 0x05,
	0x6c, 0x65, 0x76, 0x65, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x05, 0x6c, 0x65, 0x76,
	0x65, 0x6c, 0x12, 0x22, 0x0a, 0x0c, 0x64, 0x65, 0x70, 0x61, 0x72, 0x74, 0x6d, 0x65, 0x6e, 0x74,
	0x49, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0c, 0x64, 0x65, 0x70, 0x61, 0x72, 0x74,
	0x6d, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x64, 0x6f, 0x6d, 0x61, 0x69, 0x6e,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x64, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x22, 0x40,
	0x0a, 0x0a, 0x4c, 0x65, 0x61, 0x64, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x16, 0x0a, 0x06,
	0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x06, 0x75, 0x73,
	0x65, 0x72, 0x49, 0x64, 0x12, 0x1a, 0x0a, 0x08, 0x75, 0x73, 0x65, 0x72, 0x4e, 0x61, 0x6d, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x75, 0x73, 0x65, 0x72, 0x4e, 0x61, 0x6d, 0x65,
	0x22, 0x45, 0x0a, 0x17, 0x4d, 0x79, 0x44, 0x69, 0x72, 0x65, 0x63, 0x74, 0x4c, 0x65, 0x61, 0x64,
	0x65, 0x72, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x2a, 0x0a, 0x04, 0x6c,
	0x69, 0x73, 0x74, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x64, 0x65, 0x70, 0x61,
	0x72, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x4c, 0x65, 0x61, 0x64, 0x65, 0x72, 0x49, 0x6e, 0x66,
	0x6f, 0x52, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x22, 0xac, 0x02, 0x0a, 0x0c, 0x50, 0x6f, 0x73, 0x69,
	0x74, 0x69, 0x6f, 0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x0e, 0x0a, 0x02, 0x49, 0x44, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x04, 0x52, 0x02, 0x49, 0x44, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x22, 0x0a, 0x0c,
	0x64, 0x65, 0x70, 0x61, 0x72, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x49, 0x44, 0x18, 0x0a, 0x20, 0x01,
	0x28, 0x04, 0x52, 0x0c, 0x64, 0x65, 0x70, 0x61, 0x72, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x49, 0x44,
	0x12, 0x10, 0x0a, 0x03, 0x6e, 0x75, 0x6d, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x05, 0x52, 0x03, 0x6e,
	0x75, 0x6d, 0x12, 0x26, 0x0a, 0x0e, 0x64, 0x65, 0x70, 0x61, 0x72, 0x74, 0x6d, 0x65, 0x6e, 0x74,
	0x4e, 0x61, 0x6d, 0x65, 0x18, 0x10, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x64, 0x65, 0x70, 0x61,
	0x72, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x63, 0x6f,
	0x6c, 0x6f, 0x72, 0x18, 0x11, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x63, 0x6f, 0x6c, 0x6f, 0x72,
	0x12, 0x22, 0x0a, 0x0c, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x4e, 0x61, 0x6d, 0x65,
	0x18, 0x12, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72,
	0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1c, 0x0a, 0x09, 0x6d, 0x65, 0x6e, 0x75, 0x41, 0x75, 0x74, 0x68,
	0x73, 0x18, 0x13, 0x20, 0x03, 0x28, 0x09, 0x52, 0x09, 0x6d, 0x65, 0x6e, 0x75, 0x41, 0x75, 0x74,
	0x68, 0x73, 0x12, 0x1c, 0x0a, 0x09, 0x6c, 0x69, 0x6e, 0x6b, 0x65, 0x72, 0x4e, 0x75, 0x6d, 0x18,
	0x14, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x09, 0x6c, 0x69, 0x6e, 0x6b, 0x65, 0x72, 0x4e, 0x75, 0x6d,
	0x12, 0x24, 0x0a, 0x0d, 0x64, 0x65, 0x70, 0x61, 0x72, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x49, 0x44,
	0x73, 0x18, 0x15, 0x20, 0x03, 0x28, 0x0d, 0x52, 0x0d, 0x64, 0x65, 0x70, 0x61, 0x72, 0x74, 0x6d,
	0x65, 0x6e, 0x74, 0x49, 0x44, 0x73, 0x22, 0xb6, 0x01, 0x0a, 0x10, 0x4e, 0x6f, 0x64, 0x65, 0x57,
	0x69, 0x74, 0x68, 0x50, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x0e, 0x0a, 0x02, 0x49,
	0x44, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x02, 0x49, 0x44, 0x12, 0x12, 0x0a, 0x04, 0x6e,
	0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12,
	0x30, 0x0a, 0x04, 0x73, 0x6f, 0x6e, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1c, 0x2e,
	0x64, 0x65, 0x70, 0x61, 0x72, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x4e, 0x6f, 0x64, 0x65, 0x57,
	0x69, 0x74, 0x68, 0x50, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x04, 0x73, 0x6f, 0x6e,
	0x73, 0x12, 0x14, 0x0a, 0x05, 0x6c, 0x65, 0x76, 0x65, 0x6c, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0d,
	0x52, 0x05, 0x6c, 0x65, 0x76, 0x65, 0x6c, 0x12, 0x36, 0x0a, 0x09, 0x70, 0x6f, 0x73, 0x69, 0x74,
	0x69, 0x6f, 0x6e, 0x73, 0x18, 0x05, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x64, 0x65, 0x70,
	0x61, 0x72, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x50, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e,
	0x49, 0x6e, 0x66, 0x6f, 0x52, 0x09, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x22,
	0x4e, 0x0a, 0x18, 0x54, 0x72, 0x65, 0x65, 0x57, 0x69, 0x74, 0x68, 0x50, 0x6f, 0x73, 0x69, 0x74,
	0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x32, 0x0a, 0x05, 0x6e,
	0x6f, 0x64, 0x65, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x64, 0x65, 0x70,
	0x61, 0x72, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x4e, 0x6f, 0x64, 0x65, 0x57, 0x69, 0x74, 0x68,
	0x50, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x05, 0x6e, 0x6f, 0x64, 0x65, 0x73, 0x22,
	0x4d, 0x0a, 0x19, 0x44, 0x65, 0x70, 0x61, 0x72, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x42, 0x6f, 0x73,
	0x73, 0x65, 0x73, 0x42, 0x79, 0x41, 0x75, 0x74, 0x68, 0x52, 0x65, 0x71, 0x12, 0x18, 0x0a, 0x07,
	0x61, 0x75, 0x74, 0x68, 0x4b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61,
	0x75, 0x74, 0x68, 0x4b, 0x65, 0x79, 0x12, 0x16, 0x0a, 0x06, 0x64, 0x6f, 0x6d, 0x61, 0x69, 0x6e,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x64, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x22, 0x64,
	0x0a, 0x0e, 0x44, 0x65, 0x70, 0x61, 0x72, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x42, 0x6f, 0x73, 0x73,
	0x12, 0x0e, 0x0a, 0x02, 0x49, 0x44, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x02, 0x49, 0x44,
	0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04,
	0x6e, 0x61, 0x6d, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x74, 0x65, 0x6c, 0x4e, 0x75, 0x6d, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x74, 0x65, 0x6c, 0x4e, 0x75, 0x6d, 0x12, 0x16, 0x0a, 0x06,
	0x61, 0x76, 0x61, 0x74, 0x61, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x61, 0x76,
	0x61, 0x74, 0x61, 0x72, 0x22, 0x6b, 0x0a, 0x11, 0x44, 0x65, 0x70, 0x61, 0x72, 0x74, 0x6d, 0x65,
	0x6e, 0x74, 0x41, 0x6e, 0x64, 0x42, 0x6f, 0x73, 0x73, 0x12, 0x0e, 0x0a, 0x02, 0x49, 0x44, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x02, 0x49, 0x44, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x32, 0x0a,
	0x06, 0x62, 0x6f, 0x73, 0x73, 0x65, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1a, 0x2e,
	0x64, 0x65, 0x70, 0x61, 0x72, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x44, 0x65, 0x70, 0x61, 0x72,
	0x74, 0x6d, 0x65, 0x6e, 0x74, 0x42, 0x6f, 0x73, 0x73, 0x52, 0x06, 0x62, 0x6f, 0x73, 0x73, 0x65,
	0x73, 0x22, 0x53, 0x0a, 0x1e, 0x44, 0x65, 0x70, 0x61, 0x72, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x42,
	0x6f, 0x73, 0x73, 0x65, 0x73, 0x42, 0x79, 0x41, 0x75, 0x74, 0x68, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x12, 0x31, 0x0a, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x01, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x1d, 0x2e, 0x64, 0x65, 0x70, 0x61, 0x72, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x44,
	0x65, 0x70, 0x61, 0x72, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x41, 0x6e, 0x64, 0x42, 0x6f, 0x73, 0x73,
	0x52, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x22, 0x3a, 0x0a, 0x10, 0x4d, 0x79, 0x42, 0x6f, 0x73, 0x73,
	0x65, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x26, 0x0a, 0x04, 0x6c, 0x69,
	0x73, 0x74, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x64, 0x65, 0x70, 0x61, 0x72,
	0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x42, 0x6f, 0x73, 0x73, 0x65, 0x73, 0x52, 0x04, 0x6c, 0x69,
	0x73, 0x74, 0x22, 0x32, 0x0a, 0x06, 0x42, 0x6f, 0x73, 0x73, 0x65, 0x73, 0x12, 0x28, 0x0a, 0x06,
	0x62, 0x6f, 0x73, 0x73, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x10, 0x2e, 0x64,
	0x65, 0x70, 0x61, 0x72, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x42, 0x6f, 0x73, 0x73, 0x52, 0x06,
	0x62, 0x6f, 0x73, 0x73, 0x65, 0x73, 0x22, 0x66, 0x0a, 0x04, 0x42, 0x6f, 0x73, 0x73, 0x12, 0x0e,
	0x0a, 0x02, 0x49, 0x44, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x02, 0x49, 0x44, 0x12, 0x12,
	0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61,
	0x6d, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x61, 0x76, 0x61, 0x74, 0x61, 0x72, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x06, 0x61, 0x76, 0x61, 0x74, 0x61, 0x72, 0x12, 0x22, 0x0a, 0x0c, 0x64, 0x65,
	0x70, 0x61, 0x72, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0d,
	0x52, 0x0c, 0x64, 0x65, 0x70, 0x61, 0x72, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x22, 0x81,
	0x01, 0x0a, 0x0b, 0x4d, 0x79, 0x42, 0x6f, 0x73, 0x73, 0x65, 0x73, 0x52, 0x65, 0x71, 0x12, 0x16,
	0x0a, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x06,
	0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x6c, 0x65, 0x76, 0x65, 0x6c, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x05, 0x6c, 0x65, 0x76, 0x65, 0x6c, 0x12, 0x22, 0x0a, 0x0c,
	0x64, 0x65, 0x70, 0x61, 0x72, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x0d, 0x52, 0x0c, 0x64, 0x65, 0x70, 0x61, 0x72, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x49, 0x64,
	0x12, 0x20, 0x0a, 0x0b, 0x69, 0x73, 0x44, 0x65, 0x73, 0x69, 0x67, 0x6e, 0x61, 0x74, 0x65, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0b, 0x69, 0x73, 0x44, 0x65, 0x73, 0x69, 0x67, 0x6e, 0x61,
	0x74, 0x65, 0x22, 0x84, 0x01, 0x0a, 0x0b, 0x54, 0x72, 0x65, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x12, 0x1c, 0x0a, 0x09, 0x6e, 0x6f, 0x77, 0x55, 0x73, 0x65, 0x72, 0x49, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x09, 0x6e, 0x6f, 0x77, 0x55, 0x73, 0x65, 0x72, 0x49, 0x64,
	0x12, 0x25, 0x0a, 0x06, 0x64, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x42, 0x0d, 0xe2, 0xdf, 0x1f, 0x09, 0x2a, 0x05, 0x37, 0x30, 0x30, 0x30, 0x31, 0x58, 0x01, 0x52,
	0x06, 0x64, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x12, 0x30, 0x0a, 0x13, 0x66, 0x69, 0x6c, 0x74, 0x65,
	0x72, 0x44, 0x65, 0x70, 0x61, 0x72, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x73, 0x18, 0x03,
	0x20, 0x03, 0x28, 0x0d, 0x52, 0x13, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x44, 0x65, 0x70, 0x61,
	0x72, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x73, 0x22, 0xa0, 0x02, 0x0a, 0x04, 0x4e, 0x6f,
	0x64, 0x65, 0x12, 0x0e, 0x0a, 0x02, 0x49, 0x44, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x02,
	0x49, 0x44, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x24, 0x0a, 0x04, 0x73, 0x6f, 0x6e, 0x73, 0x18, 0x03,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x10, 0x2e, 0x64, 0x65, 0x70, 0x61, 0x72, 0x74, 0x6d, 0x65, 0x6e,
	0x74, 0x2e, 0x4e, 0x6f, 0x64, 0x65, 0x52, 0x04, 0x73, 0x6f, 0x6e, 0x73, 0x12, 0x14, 0x0a, 0x05,
	0x6c, 0x65, 0x76, 0x65, 0x6c, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x05, 0x6c, 0x65, 0x76,
	0x65, 0x6c, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x61, 0x74, 0x68, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x05,
	0x20, 0x03, 0x28, 0x09, 0x52, 0x08, 0x70, 0x61, 0x74, 0x68, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x18,
	0x0a, 0x07, 0x66, 0x6c, 0x61, 0x67, 0x4e, 0x75, 0x6d, 0x18, 0x15, 0x20, 0x01, 0x28, 0x0d, 0x52,
	0x07, 0x66, 0x6c, 0x61, 0x67, 0x4e, 0x75, 0x6d, 0x12, 0x20, 0x0a, 0x0b, 0x73, 0x6f, 0x6e, 0x4d,
	0x61, 0x78, 0x44, 0x65, 0x70, 0x74, 0x68, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0b, 0x73,
	0x6f, 0x6e, 0x4d, 0x61, 0x78, 0x44, 0x65, 0x70, 0x74, 0x68, 0x12, 0x18, 0x0a, 0x07, 0x70, 0x61,
	0x74, 0x68, 0x49, 0x64, 0x73, 0x18, 0x07, 0x20, 0x03, 0x28, 0x0d, 0x52, 0x07, 0x70, 0x61, 0x74,
	0x68, 0x49, 0x64, 0x73, 0x12, 0x12, 0x0a, 0x04, 0x73, 0x79, 0x6e, 0x63, 0x18, 0x0b, 0x20, 0x01,
	0x28, 0x08, 0x52, 0x04, 0x73, 0x79, 0x6e, 0x63, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x79, 0x6e, 0x63,
	0x49, 0x64, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x73, 0x79, 0x6e, 0x63, 0x49, 0x64,
	0x12, 0x1a, 0x0a, 0x08, 0x73, 0x74, 0x61, 0x66, 0x66, 0x4e, 0x75, 0x6d, 0x18, 0x0d, 0x20, 0x01,
	0x28, 0x0d, 0x52, 0x08, 0x73, 0x74, 0x61, 0x66, 0x66, 0x4e, 0x75, 0x6d, 0x22, 0x36, 0x0a, 0x0c,
	0x54, 0x72, 0x65, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x26, 0x0a, 0x05,
	0x6e, 0x6f, 0x64, 0x65, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x10, 0x2e, 0x64, 0x65,
	0x70, 0x61, 0x72, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x4e, 0x6f, 0x64, 0x65, 0x52, 0x05, 0x6e,
	0x6f, 0x64, 0x65, 0x73, 0x22, 0x5d, 0x0a, 0x17, 0x55, 0x73, 0x65, 0x72, 0x49, 0x64, 0x42, 0x79,
	0x42, 0x6f, 0x73, 0x73, 0x49, 0x64, 0x49, 0x6e, 0x41, 0x75, 0x74, 0x68, 0x52, 0x65, 0x71, 0x12,
	0x16, 0x0a, 0x06, 0x44, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x06, 0x44, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x12, 0x16, 0x0a, 0x06, 0x55, 0x73, 0x65, 0x72, 0x49,
	0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x04, 0x52, 0x06, 0x55, 0x73, 0x65, 0x72, 0x49, 0x64, 0x12,
	0x12, 0x0a, 0x04, 0x41, 0x75, 0x74, 0x68, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x41,
	0x75, 0x74, 0x68, 0x22, 0x59, 0x0a, 0x0d, 0x43, 0x6f, 0x42, 0x69, 0x6e, 0x64, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x12, 0x16, 0x0a, 0x06, 0x64, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x64, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x12, 0x0e, 0x0a, 0x02,
	0x49, 0x44, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x02, 0x49, 0x44, 0x12, 0x20, 0x0a, 0x0b,
	0x70, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28,
	0x0d, 0x52, 0x0b, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x73, 0x22, 0x10,
	0x0a, 0x0e, 0x43, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x22, 0x56, 0x0a, 0x15, 0x55, 0x73, 0x65, 0x72, 0x49, 0x64, 0x42, 0x79, 0x42, 0x6f, 0x73, 0x73,
	0x49, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x25, 0x0a, 0x06, 0x44, 0x6f, 0x6d,
	0x61, 0x69, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0d, 0xe2, 0xdf, 0x1f, 0x09, 0x2a,
	0x05, 0x37, 0x30, 0x30, 0x30, 0x31, 0x58, 0x01, 0x52, 0x06, 0x44, 0x6f, 0x6d, 0x61, 0x69, 0x6e,
	0x12, 0x16, 0x0a, 0x06, 0x55, 0x73, 0x65, 0x72, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04,
	0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x22, 0x32, 0x0a, 0x16, 0x55, 0x73, 0x65, 0x72,
	0x49, 0x64, 0x42, 0x79, 0x42, 0x6f, 0x73, 0x73, 0x49, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x55, 0x73, 0x65, 0x72, 0x49, 0x64, 0x73, 0x18, 0x01, 0x20,
	0x03, 0x28, 0x04, 0x52, 0x07, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x73, 0x22, 0x53, 0x0a, 0x18,
	0x44, 0x65, 0x70, 0x61, 0x72, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x42, 0x79, 0x41, 0x75, 0x74,
	0x68, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x25, 0x0a, 0x06, 0x44, 0x6f, 0x6d, 0x61,
	0x69, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0d, 0xe2, 0xdf, 0x1f, 0x09, 0x2a, 0x05,
	0x37, 0x30, 0x30, 0x30, 0x31, 0x58, 0x01, 0x52, 0x06, 0x44, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x12,
	0x10, 0x0a, 0x03, 0x55, 0x72, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x75, 0x72,
	0x6c, 0x22, 0x56, 0x0a, 0x18, 0x46, 0x69, 0x6e, 0x64, 0x4c, 0x65, 0x61, 0x64, 0x65, 0x72, 0x46,
	0x72, 0x6f, 0x6d, 0x44, 0x49, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x22, 0x0a,
	0x0c, 0x44, 0x65, 0x70, 0x61, 0x72, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x04, 0x52, 0x0c, 0x44, 0x65, 0x70, 0x61, 0x72, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x49,
	0x64, 0x12, 0x16, 0x0a, 0x06, 0x44, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x06, 0x44, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x22, 0x50, 0x0a, 0x12, 0x4c, 0x65, 0x61,
	0x64, 0x65, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12,
	0x1e, 0x0a, 0x0a, 0x4c, 0x65, 0x61, 0x64, 0x65, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0a, 0x4c, 0x65, 0x61, 0x64, 0x65, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x12,
	0x1a, 0x0a, 0x08, 0x4c, 0x65, 0x61, 0x64, 0x65, 0x72, 0x49, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x04, 0x52, 0x08, 0x4c, 0x65, 0x61, 0x64, 0x65, 0x72, 0x49, 0x64, 0x22, 0x4e, 0x0a, 0x0c, 0x42,
	0x61, 0x73, 0x65, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x26, 0x0a, 0x0e, 0x44,
	0x65, 0x70, 0x61, 0x72, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x43, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0e, 0x44, 0x65, 0x70, 0x61, 0x72, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x43,
	0x6f, 0x64, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x44, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x06, 0x44, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x22, 0x2d, 0x0a, 0x07, 0x42,
	0x61, 0x73, 0x65, 0x44, 0x65, 0x70, 0x12, 0x0e, 0x0a, 0x02, 0x49, 0x44, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x04, 0x52, 0x02, 0x49, 0x44, 0x12, 0x12, 0x0a, 0x04, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x22, 0x38, 0x0a, 0x0d, 0x42, 0x61,
	0x73, 0x65, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x27, 0x0a, 0x04, 0x44,
	0x61, 0x74, 0x61, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x64, 0x65, 0x70, 0x61,
	0x72, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x42, 0x61, 0x73, 0x65, 0x44, 0x65, 0x70, 0x52, 0x04,
	0x44, 0x61, 0x74, 0x61, 0x22, 0x8c, 0x01, 0x0a, 0x10, 0x42, 0x61, 0x73, 0x65, 0x4c, 0x69, 0x73,
	0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x43, 0x6f, 0x75,
	0x6e, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x05, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12,
	0x32, 0x0a, 0x04, 0x44, 0x61, 0x74, 0x61, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1e, 0x2e,
	0x64, 0x65, 0x70, 0x61, 0x72, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x42, 0x61, 0x73, 0x65, 0x44,
	0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x52, 0x04, 0x44,
	0x61, 0x74, 0x61, 0x12, 0x12, 0x0a, 0x04, 0x70, 0x61, 0x67, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x04, 0x52, 0x04, 0x70, 0x61, 0x67, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x61, 0x67, 0x65, 0x53,
	0x69, 0x7a, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x04, 0x52, 0x08, 0x70, 0x61, 0x67, 0x65, 0x53,
	0x69, 0x7a, 0x65, 0x22, 0xca, 0x01, 0x0a, 0x12, 0x42, 0x61, 0x73, 0x65, 0x44, 0x65, 0x74, 0x61,
	0x69, 0x6c, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x0e, 0x0a, 0x02, 0x49, 0x44,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x02, 0x49, 0x44, 0x12, 0x12, 0x0a, 0x04, 0x4e, 0x61,
	0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x16,
	0x0a, 0x06, 0x52, 0x65, 0x6d, 0x61, 0x72, 0x6b, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06,
	0x72, 0x65, 0x6d, 0x61, 0x72, 0x6b, 0x12, 0x26, 0x0a, 0x0e, 0x44, 0x65, 0x70, 0x61, 0x72, 0x74,
	0x6d, 0x65, 0x6e, 0x74, 0x43, 0x6f, 0x64, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e,
	0x64, 0x65, 0x70, 0x61, 0x72, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x38,
	0x0a, 0x0c, 0x41, 0x6c, 0x6c, 0x50, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x05,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x64, 0x65, 0x70, 0x61, 0x72, 0x74, 0x6d, 0x65, 0x6e,
	0x74, 0x2e, 0x50, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x0c, 0x61, 0x6c, 0x6c, 0x50,
	0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x79, 0x6e, 0x63,
	0x49, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x73, 0x79, 0x6e, 0x63, 0x49, 0x64,
	0x22, 0xa7, 0x01, 0x0a, 0x0e, 0x42, 0x61, 0x73, 0x65, 0x41, 0x6c, 0x6c, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x12, 0x25, 0x0a, 0x06, 0x44, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x42, 0x0d, 0xe2, 0xdf, 0x1f, 0x09, 0x2a, 0x05, 0x37, 0x30, 0x30, 0x30, 0x31,
	0x58, 0x01, 0x52, 0x06, 0x44, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x12, 0x16, 0x0a, 0x06, 0x75, 0x73,
	0x65, 0x72, 0x49, 0x44, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72,
	0x49, 0x44, 0x12, 0x26, 0x0a, 0x0e, 0x64, 0x65, 0x70, 0x61, 0x72, 0x74, 0x6d, 0x65, 0x6e, 0x74,
	0x4e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x64, 0x65, 0x70, 0x61,
	0x72, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x70, 0x61,
	0x67, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x04, 0x52, 0x04, 0x70, 0x61, 0x67, 0x65, 0x12, 0x1a,
	0x0a, 0x08, 0x70, 0x61, 0x67, 0x65, 0x53, 0x69, 0x7a, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x04,
	0x52, 0x08, 0x70, 0x61, 0x67, 0x65, 0x53, 0x69, 0x7a, 0x65, 0x22, 0x40, 0x0a, 0x0f, 0x42, 0x61,
	0x73, 0x65, 0x41, 0x6c, 0x6c, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x2d, 0x0a,
	0x04, 0x44, 0x61, 0x74, 0x61, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x64, 0x65,
	0x70, 0x61, 0x72, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x52, 0x04, 0x44, 0x61, 0x74, 0x61, 0x22, 0x5f, 0x0a, 0x13,
	0x49, 0x6e, 0x66, 0x6f, 0x42, 0x79, 0x55, 0x73, 0x65, 0x72, 0x49, 0x64, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x12, 0x21, 0x0a, 0x06, 0x55, 0x73, 0x65, 0x72, 0x49, 0x44, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x04, 0x42, 0x0d, 0xe2, 0xdf, 0x1f, 0x09, 0x10, 0x00, 0x2a, 0x05, 0x37, 0x30, 0x30,
	0x31, 0x30, 0x52, 0x02, 0x49, 0x44, 0x12, 0x25, 0x0a, 0x06, 0x44, 0x6f, 0x6d, 0x61, 0x69, 0x6e,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0d, 0xe2, 0xdf, 0x1f, 0x09, 0x2a, 0x05, 0x37, 0x30,
	0x30, 0x30, 0x31, 0x58, 0x01, 0x52, 0x06, 0x44, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x22, 0x65, 0x0a,
	0x14, 0x49, 0x6e, 0x66, 0x6f, 0x42, 0x79, 0x55, 0x73, 0x65, 0x72, 0x49, 0x64, 0x73, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x26, 0x0a, 0x07, 0x55, 0x73, 0x65, 0x72, 0x49, 0x44, 0x73,
	0x18, 0x01, 0x20, 0x03, 0x28, 0x04, 0x42, 0x0d, 0xe2, 0xdf, 0x1f, 0x09, 0x10, 0x00, 0x2a, 0x05,
	0x37, 0x30, 0x30, 0x31, 0x30, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x44, 0x12, 0x25, 0x0a,
	0x06, 0x44, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0d, 0xe2,
	0xdf, 0x1f, 0x09, 0x2a, 0x05, 0x37, 0x30, 0x30, 0x30, 0x31, 0x58, 0x01, 0x52, 0x06, 0x44, 0x6f,
	0x6d, 0x61, 0x69, 0x6e, 0x22, 0xc9, 0x01, 0x0a, 0x15, 0x49, 0x6e, 0x66, 0x6f, 0x42, 0x79, 0x55,
	0x73, 0x65, 0x72, 0x49, 0x64, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x69,
	0x0a, 0x12, 0x44, 0x65, 0x70, 0x61, 0x72, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x42, 0x79, 0x55, 0x73,
	0x65, 0x72, 0x49, 0x64, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x39, 0x2e, 0x64, 0x65, 0x70,
	0x61, 0x72, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x49, 0x6e, 0x66, 0x6f, 0x42, 0x79, 0x55, 0x73,
	0x65, 0x72, 0x49, 0x64, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x44, 0x65,
	0x70, 0x61, 0x72, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x42, 0x79, 0x55, 0x73, 0x65, 0x72, 0x49, 0x64,
	0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x12, 0x64, 0x65, 0x70, 0x61, 0x72, 0x74, 0x6d, 0x65, 0x6e,
	0x74, 0x42, 0x79, 0x55, 0x73, 0x65, 0x72, 0x49, 0x64, 0x1a, 0x45, 0x0a, 0x17, 0x44, 0x65, 0x70,
	0x61, 0x72, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x42, 0x79, 0x55, 0x73, 0x65, 0x72, 0x49, 0x64, 0x45,
	0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x04, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01,
	0x22, 0x62, 0x0a, 0x14, 0x49, 0x6e, 0x66, 0x6f, 0x42, 0x79, 0x55, 0x73, 0x65, 0x72, 0x49, 0x64,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x22, 0x0a, 0x0c, 0x44, 0x65, 0x70, 0x61,
	0x72, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x49, 0x44, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0c,
	0x64, 0x65, 0x70, 0x61, 0x72, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x49, 0x44, 0x12, 0x26, 0x0a, 0x0e,
	0x44, 0x65, 0x70, 0x61, 0x72, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x64, 0x65, 0x70, 0x61, 0x72, 0x74, 0x6d, 0x65, 0x6e, 0x74,
	0x4e, 0x61, 0x6d, 0x65, 0x22, 0xf5, 0x01, 0x0a, 0x0c, 0x55, 0x73, 0x65, 0x72, 0x73, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x0e, 0x0a, 0x02, 0x49, 0x44, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x04, 0x52, 0x02, 0x49, 0x44, 0x12, 0x1e, 0x0a, 0x0a, 0x50, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f,
	0x6e, 0x49, 0x44, 0x18, 0x02, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0a, 0x70, 0x6f, 0x73, 0x69, 0x74,
	0x69, 0x6f, 0x6e, 0x49, 0x44, 0x12, 0x25, 0x0a, 0x06, 0x44, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0d, 0xe2, 0xdf, 0x1f, 0x09, 0x2a, 0x05, 0x37, 0x30, 0x30,
	0x30, 0x31, 0x58, 0x01, 0x52, 0x06, 0x44, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x12, 0x12, 0x0a, 0x04,
	0x4e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65,
	0x12, 0x12, 0x0a, 0x04, 0x50, 0x61, 0x67, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x04, 0x52, 0x04,
	0x50, 0x61, 0x67, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x50, 0x61, 0x67, 0x65, 0x53, 0x69, 0x7a, 0x65,
	0x18, 0x06, 0x20, 0x01, 0x28, 0x04, 0x52, 0x08, 0x70, 0x61, 0x67, 0x65, 0x53, 0x69, 0x7a, 0x65,
	0x12, 0x24, 0x0a, 0x0d, 0x4e, 0x6f, 0x74, 0x50, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x49,
	0x44, 0x18, 0x07, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0d, 0x6e, 0x6f, 0x74, 0x50, 0x6f, 0x73, 0x69,
	0x74, 0x69, 0x6f, 0x6e, 0x49, 0x44, 0x12, 0x24, 0x0a, 0x0d, 0x49, 0x6e, 0x50, 0x6f, 0x73, 0x69,
	0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x73, 0x18, 0x08, 0x20, 0x03, 0x28, 0x04, 0x52, 0x0d, 0x69,
	0x6e, 0x50, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x73, 0x22, 0x51, 0x0a, 0x0d,
	0x55, 0x73, 0x65, 0x72, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x2a, 0x0a,
	0x05, 0x55, 0x73, 0x65, 0x72, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x64,
	0x65, 0x70, 0x61, 0x72, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x49, 0x6e,
	0x66, 0x6f, 0x52, 0x05, 0x75, 0x73, 0x65, 0x72, 0x73, 0x12, 0x14, 0x0a, 0x05, 0x43, 0x6f, 0x75,
	0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x04, 0x52, 0x05, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x22,
	0xaa, 0x01, 0x0a, 0x08, 0x55, 0x73, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x16, 0x0a, 0x06,
	0x55, 0x73, 0x65, 0x72, 0x49, 0x44, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x06, 0x75, 0x73,
	0x65, 0x72, 0x49, 0x44, 0x12, 0x1a, 0x0a, 0x08, 0x55, 0x73, 0x65, 0x72, 0x4e, 0x61, 0x6d, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x75, 0x73, 0x65, 0x72, 0x4e, 0x61, 0x6d, 0x65,
	0x12, 0x16, 0x0a, 0x06, 0x41, 0x76, 0x61, 0x74, 0x61, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x06, 0x61, 0x76, 0x61, 0x74, 0x61, 0x72, 0x12, 0x1a, 0x0a, 0x08, 0x50, 0x6f, 0x73, 0x69,
	0x74, 0x69, 0x6f, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x70, 0x6f, 0x73, 0x69,
	0x74, 0x69, 0x6f, 0x6e, 0x12, 0x1e, 0x0a, 0x0a, 0x44, 0x65, 0x70, 0x61, 0x72, 0x74, 0x6d, 0x65,
	0x6e, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x64, 0x65, 0x70, 0x61, 0x72, 0x74,
	0x6d, 0x65, 0x6e, 0x74, 0x12, 0x16, 0x0a, 0x06, 0x54, 0x65, 0x6c, 0x4e, 0x75, 0x6d, 0x18, 0x06,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x74, 0x65, 0x6c, 0x4e, 0x75, 0x6d, 0x22, 0x6b, 0x0a, 0x11,
	0x52, 0x65, 0x6d, 0x6f, 0x76, 0x65, 0x55, 0x73, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x12, 0x2b, 0x0a, 0x06, 0x44, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x42, 0x0d, 0xe2, 0xdf, 0x1f, 0x09, 0x2a, 0x05, 0x37, 0x30, 0x30, 0x30, 0x34, 0x58, 0x01,
	0x52, 0x0c, 0x64, 0x65, 0x70, 0x61, 0x72, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x49, 0x44, 0x12, 0x29,
	0x0a, 0x08, 0x4c, 0x65, 0x61, 0x64, 0x65, 0x72, 0x49, 0x44, 0x18, 0x02, 0x20, 0x01, 0x28, 0x04,
	0x42, 0x0d, 0xe2, 0xdf, 0x1f, 0x09, 0x2a, 0x05, 0x37, 0x30, 0x30, 0x31, 0x30, 0x58, 0x01, 0x52,
	0x08, 0x6c, 0x65, 0x61, 0x64, 0x65, 0x72, 0x49, 0x44, 0x22, 0x14, 0x0a, 0x12, 0x52, 0x65, 0x6d,
	0x6f, 0x76, 0x65, 0x55, 0x73, 0x65, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22,
	0x5b, 0x0a, 0x0d, 0x52, 0x65, 0x6d, 0x6f, 0x76, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x12, 0x1d, 0x0a, 0x02, 0x49, 0x44, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x42, 0x0d, 0xe2, 0xdf,
	0x1f, 0x09, 0x10, 0x00, 0x2a, 0x05, 0x37, 0x30, 0x30, 0x30, 0x34, 0x52, 0x02, 0x49, 0x44, 0x12,
	0x2b, 0x0a, 0x06, 0x44, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42,
	0x0d, 0xe2, 0xdf, 0x1f, 0x09, 0x2a, 0x05, 0x37, 0x30, 0x30, 0x30, 0x31, 0x58, 0x01, 0x52, 0x0c,
	0x64, 0x65, 0x70, 0x61, 0x72, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x49, 0x44, 0x22, 0x10, 0x0a, 0x0e,
	0x52, 0x65, 0x6d, 0x6f, 0x76, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x49,
	0x0a, 0x0e, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x12, 0x10, 0x0a, 0x03, 0x49, 0x44, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x04, 0x52, 0x03, 0x49,
	0x44, 0x73, 0x12, 0x25, 0x0a, 0x06, 0x44, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x42, 0x0d, 0xe2, 0xdf, 0x1f, 0x09, 0x2a, 0x05, 0x37, 0x30, 0x30, 0x30, 0x31, 0x58,
	0x01, 0x52, 0x06, 0x44, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x22, 0x46, 0x0a, 0x0d, 0x44, 0x65, 0x74,
	0x61, 0x69, 0x6c, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x0e, 0x0a, 0x02, 0x49, 0x44,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x49, 0x44, 0x12, 0x25, 0x0a, 0x06, 0x44, 0x6f,
	0x6d, 0x61, 0x69, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0d, 0xe2, 0xdf, 0x1f, 0x09,
	0x2a, 0x05, 0x37, 0x30, 0x30, 0x30, 0x31, 0x58, 0x01, 0x52, 0x06, 0x44, 0x6f, 0x6d, 0x61, 0x69,
	0x6e, 0x22, 0x41, 0x0a, 0x0f, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x12, 0x2e, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x01, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x64, 0x65, 0x70, 0x61, 0x72, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e,
	0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x52, 0x04,
	0x64, 0x61, 0x74, 0x61, 0x22, 0xc6, 0x05, 0x0a, 0x0e, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x0e, 0x0a, 0x02, 0x49, 0x44, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x04, 0x52, 0x02, 0x49, 0x44, 0x12, 0x12, 0x0a, 0x04, 0x4e, 0x61, 0x6d, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x52,
	0x65, 0x6d, 0x61, 0x72, 0x6b, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x72, 0x65, 0x6d,
	0x61, 0x72, 0x6b, 0x12, 0x1a, 0x0a, 0x08, 0x4c, 0x65, 0x61, 0x64, 0x65, 0x72, 0x49, 0x44, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x6c, 0x65, 0x61, 0x64, 0x65, 0x72, 0x49, 0x44, 0x12,
	0x1e, 0x0a, 0x0a, 0x4c, 0x65, 0x61, 0x64, 0x65, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0a, 0x6c, 0x65, 0x61, 0x64, 0x65, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x12,
	0x30, 0x0a, 0x05, 0x52, 0x75, 0x6c, 0x65, 0x73, 0x18, 0x06, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1a,
	0x2e, 0x64, 0x65, 0x70, 0x61, 0x72, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x44, 0x65, 0x70, 0x61,
	0x72, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x75, 0x6c, 0x65, 0x52, 0x05, 0x72, 0x75, 0x6c, 0x65,
	0x73, 0x12, 0x26, 0x0a, 0x0e, 0x44, 0x65, 0x70, 0x61, 0x72, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x43,
	0x6f, 0x64, 0x65, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x64, 0x65, 0x70, 0x61, 0x72,
	0x74, 0x6d, 0x65, 0x6e, 0x74, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x3c, 0x0a, 0x0e, 0x4c, 0x65, 0x61,
	0x64, 0x65, 0x72, 0x50, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x07, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x14, 0x2e, 0x64, 0x65, 0x70, 0x61, 0x72, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x50,
	0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x0e, 0x6c, 0x65, 0x61, 0x64, 0x65, 0x72, 0x50,
	0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x32, 0x0a, 0x09, 0x50, 0x6f, 0x73, 0x69, 0x74,
	0x69, 0x6f, 0x6e, 0x73, 0x18, 0x08, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x64, 0x65, 0x70,
	0x61, 0x72, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x50, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e,
	0x52, 0x09, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x38, 0x0a, 0x0c, 0x41,
	0x6c, 0x6c, 0x50, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x09, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x14, 0x2e, 0x64, 0x65, 0x70, 0x61, 0x72, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x50,
	0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x0c, 0x61, 0x6c, 0x6c, 0x50, 0x6f, 0x73, 0x69,
	0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x46, 0x0a, 0x12, 0x44, 0x65, 0x70, 0x61, 0x72, 0x74, 0x6d,
	0x65, 0x6e, 0x74, 0x54, 0x72, 0x65, 0x65, 0x52, 0x75, 0x6c, 0x65, 0x18, 0x0a, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x16, 0x2e, 0x64, 0x65, 0x70, 0x61, 0x72, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x52,
	0x75, 0x6c, 0x65, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x12, 0x64, 0x65, 0x70, 0x61, 0x72,
	0x74, 0x6d, 0x65, 0x6e, 0x74, 0x54, 0x72, 0x65, 0x65, 0x52, 0x75, 0x6c, 0x65, 0x12, 0x12, 0x0a,
	0x04, 0x73, 0x79, 0x6e, 0x63, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x08, 0x52, 0x04, 0x73, 0x79, 0x6e,
	0x63, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x79, 0x6e, 0x63, 0x49, 0x44, 0x18, 0x0c, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x06, 0x73, 0x79, 0x6e, 0x63, 0x49, 0x44, 0x12, 0x1c, 0x0a, 0x09, 0x43, 0x72, 0x65,
	0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x63, 0x72,
	0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x10, 0x0a, 0x03, 0x4e, 0x75, 0x6d, 0x18, 0x0f,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x03, 0x6e, 0x75, 0x6d, 0x12, 0x10, 0x0a, 0x03, 0x70, 0x69, 0x64,
	0x18, 0x10, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x03, 0x70, 0x69, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x6c,
	0x65, 0x76, 0x65, 0x6c, 0x18, 0x11, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x05, 0x6c, 0x65, 0x76, 0x65,
	0x6c, 0x12, 0x1c, 0x0a, 0x09, 0x6c, 0x65, 0x76, 0x65, 0x6c, 0x50, 0x61, 0x74, 0x68, 0x18, 0x12,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x6c, 0x65, 0x76, 0x65, 0x6c, 0x50, 0x61, 0x74, 0x68, 0x12,
	0x14, 0x0a, 0x05, 0x70, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x13, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05,
	0x70, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1c, 0x0a, 0x09, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64,
	0x41, 0x74, 0x18, 0x14, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x64, 0x41, 0x74, 0x12, 0x18, 0x0a, 0x07, 0x66, 0x6c, 0x61, 0x67, 0x4e, 0x75, 0x6d, 0x18, 0x15,
	0x20, 0x01, 0x28, 0x0d, 0x52, 0x07, 0x66, 0x6c, 0x61, 0x67, 0x4e, 0x75, 0x6d, 0x22, 0x2c, 0x0a,
	0x06, 0x4c, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x0e, 0x0a, 0x02, 0x49, 0x44, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x02, 0x49, 0x44, 0x12, 0x12, 0x0a, 0x04, 0x4e, 0x61, 0x6d, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x22, 0xaa, 0x01, 0x0a, 0x08,
	0x50, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x0e, 0x0a, 0x02, 0x49, 0x44, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x49, 0x44, 0x12, 0x12, 0x0a, 0x04, 0x4e, 0x61, 0x6d, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x2a, 0x0a, 0x05,
	0x52, 0x75, 0x6c, 0x65, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x64, 0x65,
	0x70, 0x61, 0x72, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x52, 0x75, 0x6c, 0x65, 0x49, 0x6e, 0x66,
	0x6f, 0x52, 0x05, 0x72, 0x75, 0x6c, 0x65, 0x73, 0x12, 0x2a, 0x0a, 0x05, 0x55, 0x73, 0x65, 0x72,
	0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x64, 0x65, 0x70, 0x61, 0x72, 0x74,
	0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x05, 0x75,
	0x73, 0x65, 0x72, 0x73, 0x12, 0x22, 0x0a, 0x0c, 0x50, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e,
	0x43, 0x6f, 0x64, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x70, 0x6f, 0x73, 0x69,
	0x74, 0x69, 0x6f, 0x6e, 0x43, 0x6f, 0x64, 0x65, 0x22, 0xf6, 0x02, 0x0a, 0x0d, 0x43, 0x72, 0x65,
	0x61, 0x74, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x0e, 0x0a, 0x02, 0x49, 0x44,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x02, 0x49, 0x44, 0x12, 0x21, 0x0a, 0x04, 0x4e, 0x61,
	0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0d, 0xe2, 0xdf, 0x1f, 0x09, 0x2a, 0x05,
	0x37, 0x30, 0x30, 0x31, 0x31, 0x58, 0x01, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x16, 0x0a,
	0x06, 0x52, 0x65, 0x6d, 0x61, 0x72, 0x6b, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x72,
	0x65, 0x6d, 0x61, 0x72, 0x6b, 0x12, 0x16, 0x0a, 0x06, 0x44, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x64, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x12, 0x26, 0x0a,
	0x05, 0x52, 0x75, 0x6c, 0x65, 0x73, 0x18, 0x06, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x10, 0x2e, 0x64,
	0x65, 0x70, 0x61, 0x72, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x52, 0x75, 0x6c, 0x65, 0x52, 0x05,
	0x72, 0x75, 0x6c, 0x65, 0x73, 0x12, 0x2c, 0x0a, 0x07, 0x4c, 0x65, 0x61, 0x64, 0x65, 0x72, 0x73,
	0x18, 0x07, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x64, 0x65, 0x70, 0x61, 0x72, 0x74, 0x6d,
	0x65, 0x6e, 0x74, 0x2e, 0x4c, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x07, 0x6c, 0x65, 0x61, 0x64,
	0x65, 0x72, 0x73, 0x12, 0x26, 0x0a, 0x0e, 0x44, 0x65, 0x70, 0x61, 0x72, 0x74, 0x6d, 0x65, 0x6e,
	0x74, 0x43, 0x6f, 0x64, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x64, 0x65, 0x70,
	0x61, 0x72, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x46, 0x0a, 0x12, 0x44,
	0x65, 0x70, 0x61, 0x72, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x54, 0x72, 0x65, 0x65, 0x52, 0x75, 0x6c,
	0x65, 0x18, 0x09, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x64, 0x65, 0x70, 0x61, 0x72, 0x74,
	0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x52, 0x75, 0x6c, 0x65, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52,
	0x12, 0x64, 0x65, 0x70, 0x61, 0x72, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x54, 0x72, 0x65, 0x65, 0x52,
	0x75, 0x6c, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x73, 0x79, 0x6e, 0x63, 0x18, 0x0a, 0x20, 0x01, 0x28,
	0x08, 0x52, 0x04, 0x73, 0x79, 0x6e, 0x63, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x79, 0x6e, 0x63, 0x49,
	0x44, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x73, 0x79, 0x6e, 0x63, 0x49, 0x44, 0x12,
	0x10, 0x0a, 0x03, 0x70, 0x69, 0x64, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x03, 0x70, 0x69,
	0x64, 0x22, 0x10, 0x0a, 0x0e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x22, 0x20, 0x0a, 0x0e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x0e, 0x0a, 0x02, 0x49, 0x44, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x04, 0x52, 0x02, 0x49, 0x44, 0x22, 0x83, 0x01, 0x0a, 0x0b, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1a, 0x0a, 0x08, 0x4c, 0x65, 0x61, 0x64, 0x65, 0x72, 0x49,
	0x44, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x08, 0x6c, 0x65, 0x61, 0x64, 0x65, 0x72, 0x49,
	0x44, 0x12, 0x16, 0x0a, 0x06, 0x44, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x06, 0x64, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x12, 0x1a, 0x0a, 0x08, 0x50, 0x61, 0x67,
	0x65, 0x53, 0x69, 0x7a, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x04, 0x52, 0x08, 0x70, 0x61, 0x67,
	0x65, 0x53, 0x69, 0x7a, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x50, 0x61, 0x67, 0x65, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x04, 0x52, 0x04, 0x70, 0x61, 0x67, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x4b, 0x65, 0x79,
	0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x22, 0x4e, 0x0a, 0x08, 0x52,
	0x75, 0x6c, 0x65, 0x44, 0x61, 0x74, 0x61, 0x12, 0x1c, 0x0a, 0x09, 0x44, 0x61, 0x74, 0x61, 0x46,
	0x69, 0x65, 0x6c, 0x64, 0x18, 0x01, 0x20, 0x03, 0x28, 0x09, 0x52, 0x09, 0x64, 0x61, 0x74, 0x61,
	0x46, 0x69, 0x65, 0x6c, 0x64, 0x12, 0x0e, 0x0a, 0x02, 0x49, 0x44, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x04, 0x52, 0x02, 0x49, 0x44, 0x12, 0x14, 0x0a, 0x05, 0x54, 0x69, 0x74, 0x6c, 0x65, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x54, 0x69, 0x74, 0x6c, 0x65, 0x22, 0x5c, 0x0a, 0x04, 0x52,
	0x75, 0x6c, 0x65, 0x12, 0x0e, 0x0a, 0x02, 0x49, 0x44, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52,
	0x02, 0x49, 0x44, 0x12, 0x12, 0x0a, 0x04, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x04, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x30, 0x0a, 0x08, 0x52, 0x75, 0x6c, 0x65, 0x44,
	0x61, 0x74, 0x61, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x64, 0x65, 0x70, 0x61,
	0x72, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x52, 0x75, 0x6c, 0x65, 0x44, 0x61, 0x74, 0x61, 0x52,
	0x08, 0x52, 0x75, 0x6c, 0x65, 0x44, 0x61, 0x74, 0x61, 0x22, 0xc4, 0x01, 0x0a, 0x08, 0x52, 0x75,
	0x6c, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x0e, 0x0a, 0x02, 0x49, 0x44, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x04, 0x52, 0x02, 0x49, 0x44, 0x12, 0x14, 0x0a, 0x05, 0x54, 0x69, 0x74, 0x6c, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x54, 0x69, 0x74, 0x6c, 0x65, 0x12, 0x10, 0x0a, 0x03,
	0x55, 0x72, 0x6c, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x55, 0x72, 0x6c, 0x12, 0x16,
	0x0a, 0x06, 0x4d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06,
	0x4d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x12, 0x1e, 0x0a, 0x0a, 0x52, 0x75, 0x6c, 0x65, 0x44, 0x61,
	0x74, 0x61, 0x49, 0x44, 0x18, 0x05, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0a, 0x52, 0x75, 0x6c, 0x65,
	0x44, 0x61, 0x74, 0x61, 0x49, 0x44, 0x12, 0x22, 0x0a, 0x0c, 0x52, 0x75, 0x6c, 0x65, 0x44, 0x61,
	0x74, 0x61, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x52, 0x75,
	0x6c, 0x65, 0x44, 0x61, 0x74, 0x61, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x24, 0x0a, 0x0d, 0x52, 0x75,
	0x6c, 0x65, 0x44, 0x61, 0x74, 0x61, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x18, 0x07, 0x20, 0x03, 0x28,
	0x09, 0x52, 0x0d, 0x52, 0x75, 0x6c, 0x65, 0x44, 0x61, 0x74, 0x61, 0x46, 0x69, 0x65, 0x6c, 0x64,
	0x22, 0x92, 0x01, 0x0a, 0x0e, 0x44, 0x65, 0x70, 0x61, 0x72, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x52,
	0x75, 0x6c, 0x65, 0x12, 0x0e, 0x0a, 0x02, 0x49, 0x44, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52,
	0x02, 0x49, 0x44, 0x12, 0x14, 0x0a, 0x05, 0x54, 0x69, 0x74, 0x6c, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x05, 0x54, 0x69, 0x74, 0x6c, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x55, 0x72, 0x6c,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x55, 0x72, 0x6c, 0x12, 0x16, 0x0a, 0x06, 0x4d,
	0x65, 0x74, 0x68, 0x6f, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x4d, 0x65, 0x74,
	0x68, 0x6f, 0x64, 0x12, 0x30, 0x0a, 0x08, 0x52, 0x75, 0x6c, 0x65, 0x44, 0x61, 0x74, 0x61, 0x18,
	0x07, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x64, 0x65, 0x70, 0x61, 0x72, 0x74, 0x6d, 0x65,
	0x6e, 0x74, 0x2e, 0x52, 0x75, 0x6c, 0x65, 0x44, 0x61, 0x74, 0x61, 0x52, 0x08, 0x52, 0x75, 0x6c,
	0x65, 0x44, 0x61, 0x74, 0x61, 0x22, 0x54, 0x0a, 0x0c, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x04, 0x52, 0x05, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x2e, 0x0a, 0x04, 0x44,
	0x61, 0x74, 0x61, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x64, 0x65, 0x70, 0x61,
	0x72, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x52, 0x04, 0x44, 0x61, 0x74, 0x61, 0x22, 0xee, 0x02, 0x0a, 0x0a,
	0x52, 0x75, 0x6c, 0x65, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x12, 0x0e, 0x0a, 0x02, 0x49, 0x44,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x02, 0x49, 0x44, 0x12, 0x12, 0x0a, 0x04, 0x54, 0x79,
	0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x10,
	0x0a, 0x03, 0x50, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x04, 0x52, 0x03, 0x70, 0x69, 0x64,
	0x12, 0x14, 0x0a, 0x05, 0x54, 0x69, 0x74, 0x6c, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x49, 0x63, 0x6f, 0x6e, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x69, 0x63, 0x6f, 0x6e, 0x12, 0x10, 0x0a, 0x03, 0x55, 0x72,
	0x6c, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x75, 0x72, 0x6c, 0x12, 0x16, 0x0a, 0x06,
	0x4d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x6d, 0x65,
	0x74, 0x68, 0x6f, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x57, 0x65, 0x69, 0x67, 0x68, 0x18, 0x08, 0x20,
	0x01, 0x28, 0x04, 0x52, 0x05, 0x77, 0x65, 0x69, 0x67, 0x68, 0x12, 0x16, 0x0a, 0x06, 0x53, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x12, 0x16, 0x0a, 0x06, 0x52, 0x65, 0x6d, 0x61, 0x72, 0x6b, 0x18, 0x0a, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x06, 0x72, 0x65, 0x6d, 0x61, 0x72, 0x6b, 0x12, 0x16, 0x0a, 0x06, 0x45, 0x78,
	0x74, 0x65, 0x6e, 0x64, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x65, 0x78, 0x74, 0x65,
	0x6e, 0x64, 0x12, 0x30, 0x0a, 0x08, 0x52, 0x75, 0x6c, 0x65, 0x44, 0x61, 0x74, 0x61, 0x18, 0x0c,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x64, 0x65, 0x70, 0x61, 0x72, 0x74, 0x6d, 0x65, 0x6e,
	0x74, 0x2e, 0x52, 0x75, 0x6c, 0x65, 0x44, 0x61, 0x74, 0x61, 0x52, 0x08, 0x72, 0x75, 0x6c, 0x65,
	0x44, 0x61, 0x74, 0x61, 0x12, 0x2a, 0x0a, 0x03, 0x53, 0x6f, 0x6e, 0x18, 0x0d, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x16, 0x2e, 0x64, 0x65, 0x70, 0x61, 0x72, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x52,
	0x75, 0x6c, 0x65, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x05, 0x72, 0x75, 0x6c, 0x65, 0x73,
	0x12, 0x1a, 0x0a, 0x08, 0x67, 0x72, 0x61, 0x79, 0x49, 0x63, 0x6f, 0x6e, 0x18, 0x13, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x08, 0x67, 0x72, 0x61, 0x79, 0x49, 0x63, 0x6f, 0x6e, 0x32, 0xd4, 0x15, 0x0a,
	0x0a, 0x44, 0x65, 0x70, 0x61, 0x72, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x12, 0x41, 0x0a, 0x06, 0x43,
	0x72, 0x65, 0x61, 0x74, 0x65, 0x12, 0x19, 0x2e, 0x64, 0x65, 0x70, 0x61, 0x72, 0x74, 0x6d, 0x65,
	0x6e, 0x74, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x1a, 0x1a, 0x2e, 0x64, 0x65, 0x70, 0x61, 0x72, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x43, 0x72,
	0x65, 0x61, 0x74, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x41,
	0x0a, 0x06, 0x52, 0x65, 0x6d, 0x6f, 0x76, 0x65, 0x12, 0x19, 0x2e, 0x64, 0x65, 0x70, 0x61, 0x72,
	0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x52, 0x65, 0x6d, 0x6f, 0x76, 0x65, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x1a, 0x1a, 0x2e, 0x64, 0x65, 0x70, 0x61, 0x72, 0x74, 0x6d, 0x65, 0x6e, 0x74,
	0x2e, 0x52, 0x65, 0x6d, 0x6f, 0x76, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22,
	0x00, 0x12, 0x3f, 0x0a, 0x06, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x12, 0x19, 0x2e, 0x64, 0x65,
	0x70, 0x61, 0x72, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1a, 0x2e, 0x64, 0x65, 0x70, 0x61, 0x72, 0x74, 0x6d,
	0x65, 0x6e, 0x74, 0x2e, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x12, 0x42, 0x0a, 0x07, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x1a, 0x2e,
	0x64, 0x65, 0x70, 0x61, 0x72, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x44, 0x65, 0x74, 0x61, 0x69,
	0x6c, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1b, 0x2e, 0x64, 0x65, 0x70, 0x61,
	0x72, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x3f, 0x0a, 0x06, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x12, 0x19, 0x2e, 0x64, 0x65, 0x70, 0x61, 0x72, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x43, 0x72,
	0x65, 0x61, 0x74, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1a, 0x2e, 0x64, 0x65,
	0x70, 0x61, 0x72, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x46, 0x0a, 0x0d, 0x42, 0x69, 0x6e, 0x64, 0x50,
	0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x19, 0x2e, 0x64, 0x65, 0x70, 0x61, 0x72,
	0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x43, 0x6f, 0x42, 0x69, 0x6e, 0x64, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x1a, 0x1a, 0x2e, 0x64, 0x65, 0x70, 0x61, 0x72, 0x74, 0x6d, 0x65, 0x6e, 0x74,
	0x2e, 0x43, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12,
	0x48, 0x0a, 0x0f, 0x55, 0x6e, 0x42, 0x69, 0x6e, 0x64, 0x50, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f,
	0x6e, 0x73, 0x12, 0x19, 0x2e, 0x64, 0x65, 0x70, 0x61, 0x72, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e,
	0x43, 0x6f, 0x42, 0x69, 0x6e, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1a, 0x2e,
	0x64, 0x65, 0x70, 0x61, 0x72, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x43, 0x6f, 0x6d, 0x6d, 0x6f,
	0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x39, 0x0a, 0x04, 0x4c, 0x69, 0x73,
	0x74, 0x12, 0x17, 0x2e, 0x64, 0x65, 0x70, 0x61, 0x72, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x4c,
	0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x18, 0x2e, 0x64, 0x65, 0x70,
	0x61, 0x72, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x12, 0x39, 0x0a, 0x04, 0x54, 0x72, 0x65, 0x65, 0x12, 0x17, 0x2e, 0x64,
	0x65, 0x70, 0x61, 0x72, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x54, 0x72, 0x65, 0x65, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x18, 0x2e, 0x64, 0x65, 0x70, 0x61, 0x72, 0x74, 0x6d, 0x65,
	0x6e, 0x74, 0x2e, 0x54, 0x72, 0x65, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12,
	0x44, 0x0a, 0x08, 0x42, 0x61, 0x73, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x1a, 0x2e, 0x64, 0x65,
	0x70, 0x61, 0x72, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x42, 0x61, 0x73, 0x65, 0x41, 0x6c, 0x6c,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1c, 0x2e, 0x64, 0x65, 0x70, 0x61, 0x72, 0x74,
	0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x42, 0x61, 0x73, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x48, 0x0a, 0x0c, 0x42, 0x61, 0x73, 0x65, 0x53, 0x79, 0x6e,
	0x63, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x1a, 0x2e, 0x64, 0x65, 0x70, 0x61, 0x72, 0x74, 0x6d, 0x65,
	0x6e, 0x74, 0x2e, 0x42, 0x61, 0x73, 0x65, 0x41, 0x6c, 0x6c, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x1a, 0x1c, 0x2e, 0x64, 0x65, 0x70, 0x61, 0x72, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x42,
	0x61, 0x73, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12,
	0x42, 0x0a, 0x07, 0x42, 0x61, 0x73, 0x65, 0x41, 0x6c, 0x6c, 0x12, 0x1a, 0x2e, 0x64, 0x65, 0x70,
	0x61, 0x72, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x42, 0x61, 0x73, 0x65, 0x41, 0x6c, 0x6c, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1b, 0x2e, 0x64, 0x65, 0x70, 0x61, 0x72, 0x74, 0x6d,
	0x65, 0x6e, 0x74, 0x2e, 0x42, 0x61, 0x73, 0x65, 0x41, 0x6c, 0x6c, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x12, 0x3c, 0x0a, 0x05, 0x55, 0x73, 0x65, 0x72, 0x73, 0x12, 0x18, 0x2e, 0x64,
	0x65, 0x70, 0x61, 0x72, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x73, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x19, 0x2e, 0x64, 0x65, 0x70, 0x61, 0x72, 0x74, 0x6d,
	0x65, 0x6e, 0x74, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x12, 0x42, 0x0a, 0x0b, 0x53, 0x65, 0x6c, 0x6c, 0x65, 0x72, 0x55, 0x73, 0x65, 0x72, 0x73,
	0x12, 0x18, 0x2e, 0x64, 0x65, 0x70, 0x61, 0x72, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x55, 0x73,
	0x65, 0x72, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x19, 0x2e, 0x64, 0x65, 0x70,
	0x61, 0x72, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x73, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x51, 0x0a, 0x0c, 0x49, 0x6e, 0x66, 0x6f, 0x42, 0x79, 0x55,
	0x73, 0x65, 0x72, 0x49, 0x64, 0x12, 0x1f, 0x2e, 0x64, 0x65, 0x70, 0x61, 0x72, 0x74, 0x6d, 0x65,
	0x6e, 0x74, 0x2e, 0x49, 0x6e, 0x66, 0x6f, 0x42, 0x79, 0x55, 0x73, 0x65, 0x72, 0x49, 0x64, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x20, 0x2e, 0x64, 0x65, 0x70, 0x61, 0x72, 0x74, 0x6d,
	0x65, 0x6e, 0x74, 0x2e, 0x49, 0x6e, 0x66, 0x6f, 0x42, 0x79, 0x55, 0x73, 0x65, 0x72, 0x49, 0x64,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x54, 0x0a, 0x0d, 0x49, 0x6e, 0x66, 0x6f,
	0x42, 0x79, 0x55, 0x73, 0x65, 0x72, 0x49, 0x64, 0x73, 0x12, 0x20, 0x2e, 0x64, 0x65, 0x70, 0x61,
	0x72, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x49, 0x6e, 0x66, 0x6f, 0x42, 0x79, 0x55, 0x73, 0x65,
	0x72, 0x49, 0x64, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x21, 0x2e, 0x64, 0x65,
	0x70, 0x61, 0x72, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x49, 0x6e, 0x66, 0x6f, 0x42, 0x79, 0x55,
	0x73, 0x65, 0x72, 0x49, 0x64, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x4b,
	0x0a, 0x0a, 0x52, 0x65, 0x6d, 0x6f, 0x76, 0x65, 0x55, 0x73, 0x65, 0x72, 0x12, 0x1d, 0x2e, 0x64,
	0x65, 0x70, 0x61, 0x72, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x52, 0x65, 0x6d, 0x6f, 0x76, 0x65,
	0x55, 0x73, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1e, 0x2e, 0x64, 0x65,
	0x70, 0x61, 0x72, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x52, 0x65, 0x6d, 0x6f, 0x76, 0x65, 0x55,
	0x73, 0x65, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x3c, 0x0a, 0x05, 0x42,
	0x61, 0x73, 0x65, 0x73, 0x12, 0x18, 0x2e, 0x64, 0x65, 0x70, 0x61, 0x72, 0x74, 0x6d, 0x65, 0x6e,
	0x74, 0x2e, 0x42, 0x61, 0x73, 0x65, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x19,
	0x2e, 0x64, 0x65, 0x70, 0x61, 0x72, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x42, 0x61, 0x73, 0x65,
	0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x59, 0x0a, 0x11, 0x46, 0x69, 0x6e,
	0x64, 0x4c, 0x65, 0x61, 0x64, 0x65, 0x72, 0x46, 0x72, 0x6f, 0x6d, 0x44, 0x49, 0x64, 0x12, 0x24,
	0x2e, 0x64, 0x65, 0x70, 0x61, 0x72, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x46, 0x69, 0x6e, 0x64,
	0x4c, 0x65, 0x61, 0x64, 0x65, 0x72, 0x46, 0x72, 0x6f, 0x6d, 0x44, 0x49, 0x64, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x1a, 0x1e, 0x2e, 0x64, 0x65, 0x70, 0x61, 0x72, 0x74, 0x6d, 0x65, 0x6e,
	0x74, 0x2e, 0x4c, 0x65, 0x61, 0x64, 0x65, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x12, 0x62, 0x0a, 0x12, 0x46, 0x69, 0x6e, 0x64, 0x4c, 0x65, 0x61, 0x64,
	0x65, 0x72, 0x73, 0x46, 0x72, 0x6f, 0x6d, 0x44, 0x49, 0x64, 0x12, 0x24, 0x2e, 0x64, 0x65, 0x70,
	0x61, 0x72, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x46, 0x69, 0x6e, 0x64, 0x4c, 0x65, 0x61, 0x64,
	0x65, 0x72, 0x46, 0x72, 0x6f, 0x6d, 0x44, 0x49, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x1a, 0x26, 0x2e, 0x64, 0x65, 0x70, 0x61, 0x72, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x46, 0x69,
	0x6e, 0x64, 0x4c, 0x65, 0x61, 0x64, 0x65, 0x72, 0x73, 0x46, 0x72, 0x6f, 0x6d, 0x44, 0x49, 0x64,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x54, 0x0a, 0x0c, 0x55, 0x73, 0x65, 0x72,
	0x49, 0x73, 0x4c, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x22, 0x2e, 0x64, 0x65, 0x70, 0x61, 0x72,
	0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x4d, 0x79, 0x44, 0x69, 0x72, 0x65, 0x63, 0x74, 0x4c, 0x65,
	0x61, 0x64, 0x65, 0x72, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x20, 0x2e, 0x64,
	0x65, 0x70, 0x61, 0x72, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x49, 0x73,
	0x4c, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x54,
	0x0a, 0x11, 0x44, 0x65, 0x70, 0x61, 0x72, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x42, 0x79, 0x41,
	0x75, 0x74, 0x68, 0x12, 0x24, 0x2e, 0x64, 0x65, 0x70, 0x61, 0x72, 0x74, 0x6d, 0x65, 0x6e, 0x74,
	0x2e, 0x44, 0x65, 0x70, 0x61, 0x72, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x42, 0x79, 0x41, 0x75,
	0x74, 0x68, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x19, 0x2e, 0x64, 0x65, 0x70, 0x61,
	0x72, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x42, 0x61, 0x73, 0x65, 0x73, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x12, 0x57, 0x0a, 0x0e, 0x55, 0x73, 0x65, 0x72, 0x49, 0x64, 0x42, 0x79,
	0x42, 0x6f, 0x73, 0x73, 0x49, 0x64, 0x12, 0x21, 0x2e, 0x64, 0x65, 0x70, 0x61, 0x72, 0x74, 0x6d,
	0x65, 0x6e, 0x74, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x49, 0x64, 0x42, 0x79, 0x42, 0x6f, 0x73, 0x73,
	0x49, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x22, 0x2e, 0x64, 0x65, 0x70, 0x61,
	0x72, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x49, 0x64, 0x42, 0x79, 0x42,
	0x6f, 0x73, 0x73, 0x49, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x5f, 0x0a,
	0x14, 0x55, 0x73, 0x65, 0x72, 0x49, 0x64, 0x42, 0x79, 0x42, 0x6f, 0x73, 0x73, 0x49, 0x64, 0x49,
	0x6e, 0x41, 0x75, 0x74, 0x68, 0x12, 0x23, 0x2e, 0x64, 0x65, 0x70, 0x61, 0x72, 0x74, 0x6d, 0x65,
	0x6e, 0x74, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x49, 0x64, 0x42, 0x79, 0x42, 0x6f, 0x73, 0x73, 0x49,
	0x64, 0x49, 0x6e, 0x41, 0x75, 0x74, 0x68, 0x52, 0x65, 0x71, 0x1a, 0x22, 0x2e, 0x64, 0x65, 0x70,
	0x61, 0x72, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x49, 0x64, 0x42, 0x79,
	0x42, 0x6f, 0x73, 0x73, 0x49, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x41,
	0x0a, 0x08, 0x4d, 0x79, 0x42, 0x6f, 0x73, 0x73, 0x65, 0x73, 0x12, 0x17, 0x2e, 0x64, 0x65, 0x70,
	0x61, 0x72, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x4d, 0x79, 0x42, 0x6f, 0x73, 0x73, 0x65, 0x73,
	0x52, 0x65, 0x71, 0x1a, 0x1c, 0x2e, 0x64, 0x65, 0x70, 0x61, 0x72, 0x74, 0x6d, 0x65, 0x6e, 0x74,
	0x2e, 0x4d, 0x79, 0x42, 0x6f, 0x73, 0x73, 0x65, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x12, 0x6b, 0x0a, 0x16, 0x44, 0x65, 0x70, 0x61, 0x72, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x42,
	0x6f, 0x73, 0x73, 0x65, 0x73, 0x42, 0x79, 0x41, 0x75, 0x74, 0x68, 0x12, 0x25, 0x2e, 0x64, 0x65,
	0x70, 0x61, 0x72, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x44, 0x65, 0x70, 0x61, 0x72, 0x74, 0x6d,
	0x65, 0x6e, 0x74, 0x42, 0x6f, 0x73, 0x73, 0x65, 0x73, 0x42, 0x79, 0x41, 0x75, 0x74, 0x68, 0x52,
	0x65, 0x71, 0x1a, 0x2a, 0x2e, 0x64, 0x65, 0x70, 0x61, 0x72, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e,
	0x44, 0x65, 0x70, 0x61, 0x72, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x42, 0x6f, 0x73, 0x73, 0x65, 0x73,
	0x42, 0x79, 0x41, 0x75, 0x74, 0x68, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x51,
	0x0a, 0x10, 0x54, 0x72, 0x65, 0x65, 0x57, 0x69, 0x74, 0x68, 0x50, 0x6f, 0x73, 0x69, 0x74, 0x69,
	0x6f, 0x6e, 0x12, 0x17, 0x2e, 0x64, 0x65, 0x70, 0x61, 0x72, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e,
	0x54, 0x72, 0x65, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x24, 0x2e, 0x64, 0x65,
	0x70, 0x61, 0x72, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x54, 0x72, 0x65, 0x65, 0x57, 0x69, 0x74,
	0x68, 0x50, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x12, 0x5a, 0x0a, 0x0f, 0x4d, 0x79, 0x44, 0x69, 0x72, 0x65, 0x63, 0x74, 0x4c, 0x65, 0x61,
	0x64, 0x65, 0x72, 0x73, 0x12, 0x22, 0x2e, 0x64, 0x65, 0x70, 0x61, 0x72, 0x74, 0x6d, 0x65, 0x6e,
	0x74, 0x2e, 0x4d, 0x79, 0x44, 0x69, 0x72, 0x65, 0x63, 0x74, 0x4c, 0x65, 0x61, 0x64, 0x65, 0x72,
	0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x23, 0x2e, 0x64, 0x65, 0x70, 0x61, 0x72,
	0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x4d, 0x79, 0x44, 0x69, 0x72, 0x65, 0x63, 0x74, 0x4c, 0x65,
	0x61, 0x64, 0x65, 0x72, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x70, 0x0a,
	0x1a, 0x4d, 0x79, 0x44, 0x65, 0x70, 0x61, 0x72, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x44, 0x69,
	0x72, 0x65, 0x63, 0x74, 0x4c, 0x65, 0x61, 0x64, 0x65, 0x72, 0x73, 0x12, 0x2d, 0x2e, 0x64, 0x65,
	0x70, 0x61, 0x72, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x4d, 0x79, 0x44, 0x65, 0x70, 0x61, 0x72,
	0x74, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x44, 0x69, 0x72, 0x65, 0x63, 0x74, 0x4c, 0x65, 0x61, 0x64,
	0x65, 0x72, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x23, 0x2e, 0x64, 0x65, 0x70,
	0x61, 0x72, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x4d, 0x79, 0x44, 0x69, 0x72, 0x65, 0x63, 0x74,
	0x4c, 0x65, 0x61, 0x64, 0x65, 0x72, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12,
	0x4b, 0x0a, 0x0a, 0x42, 0x61, 0x73, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x56, 0x32, 0x12, 0x1d, 0x2e,
	0x64, 0x65, 0x70, 0x61, 0x72, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x42, 0x61, 0x73, 0x65, 0x4c,
	0x69, 0x73, 0x74, 0x56, 0x32, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1e, 0x2e, 0x64,
	0x65, 0x70, 0x61, 0x72, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x42, 0x61, 0x73, 0x65, 0x4c, 0x69,
	0x73, 0x74, 0x56, 0x32, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x6a, 0x0a, 0x15,
	0x55, 0x73, 0x65, 0x72, 0x49, 0x64, 0x73, 0x42, 0x79, 0x46, 0x61, 0x74, 0x68, 0x65, 0x72, 0x44,
	0x65, 0x70, 0x49, 0x64, 0x73, 0x12, 0x27, 0x2e, 0x64, 0x65, 0x70, 0x61, 0x72, 0x74, 0x6d, 0x65,
	0x6e, 0x74, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x49, 0x64, 0x73, 0x42, 0x79, 0x46, 0x61, 0x74, 0x68,
	0x65, 0x72, 0x44, 0x65, 0x70, 0x49, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x28,
	0x2e, 0x64, 0x65, 0x70, 0x61, 0x72, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x55, 0x73, 0x65, 0x72,
	0x49, 0x64, 0x73, 0x42, 0x79, 0x46, 0x61, 0x74, 0x68, 0x65, 0x72, 0x44, 0x65, 0x70, 0x49, 0x64,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x69, 0x0a, 0x14, 0x43, 0x72, 0x65, 0x61,
	0x74, 0x65, 0x44, 0x65, 0x70, 0x61, 0x72, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x48, 0x65, 0x61, 0x64,
	0x12, 0x27, 0x2e, 0x64, 0x65, 0x70, 0x61, 0x72, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x43, 0x72,
	0x65, 0x61, 0x74, 0x65, 0x44, 0x65, 0x70, 0x61, 0x72, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x48, 0x65,
	0x61, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x28, 0x2e, 0x64, 0x65, 0x70, 0x61,
	0x72, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x44, 0x65, 0x70,
	0x61, 0x72, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x48, 0x65, 0x61, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x12, 0x69, 0x0a, 0x14, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x44, 0x65, 0x70,
	0x61, 0x72, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x48, 0x65, 0x61, 0x64, 0x12, 0x27, 0x2e, 0x64, 0x65,
	0x70, 0x61, 0x72, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x44,
	0x65, 0x70, 0x61, 0x72, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x48, 0x65, 0x61, 0x64, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x1a, 0x28, 0x2e, 0x64, 0x65, 0x70, 0x61, 0x72, 0x74, 0x6d, 0x65, 0x6e,
	0x74, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x44, 0x65, 0x70, 0x61, 0x72, 0x74, 0x6d, 0x65,
	0x6e, 0x74, 0x48, 0x65, 0x61, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x63,
	0x0a, 0x12, 0x46, 0x69, 0x6e, 0x64, 0x44, 0x65, 0x70, 0x61, 0x72, 0x74, 0x6d, 0x65, 0x6e, 0x74,
	0x48, 0x65, 0x61, 0x64, 0x12, 0x25, 0x2e, 0x64, 0x65, 0x70, 0x61, 0x72, 0x74, 0x6d, 0x65, 0x6e,
	0x74, 0x2e, 0x46, 0x69, 0x6e, 0x64, 0x44, 0x65, 0x70, 0x61, 0x72, 0x74, 0x6d, 0x65, 0x6e, 0x74,
	0x48, 0x65, 0x61, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x26, 0x2e, 0x64, 0x65,
	0x70, 0x61, 0x72, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x46, 0x69, 0x6e, 0x64, 0x44, 0x65, 0x70,
	0x61, 0x72, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x48, 0x65, 0x61, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x42, 0x0f, 0x5a, 0x0d, 0x2e, 0x2f, 0x3b, 0x64, 0x65, 0x70, 0x61, 0x72, 0x74,
	0x6d, 0x65, 0x6e, 0x74, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_department_department_proto_rawDescOnce sync.Once
	file_api_department_department_proto_rawDescData = file_api_department_department_proto_rawDesc
)

func file_api_department_department_proto_rawDescGZIP() []byte {
	file_api_department_department_proto_rawDescOnce.Do(func() {
		file_api_department_department_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_department_department_proto_rawDescData)
	})
	return file_api_department_department_proto_rawDescData
}

var file_api_department_department_proto_msgTypes = make([]protoimpl.MessageInfo, 73)
var file_api_department_department_proto_goTypes = []interface{}{
	(*FindDepartmentHeadResponse)(nil),        // 0: department.FindDepartmentHeadResponse
	(*FindDepartmentHead)(nil),                // 1: department.FindDepartmentHead
	(*FindDepartmentHeadRequest)(nil),         // 2: department.FindDepartmentHeadRequest
	(*CreateDepartmentHeadResponse)(nil),      // 3: department.CreateDepartmentHeadResponse
	(*CreateDepartmentHeadRequest)(nil),       // 4: department.CreateDepartmentHeadRequest
	(*UserIdsByFatherDepIdResponse)(nil),      // 5: department.UserIdsByFatherDepIdResponse
	(*UserIdsByFatherDepIdRequest)(nil),       // 6: department.UserIdsByFatherDepIdRequest
	(*BaseListV2Request)(nil),                 // 7: department.BaseListV2Request
	(*BaseListV2Response)(nil),                // 8: department.BaseListV2Response
	(*BaseDetailV2Response)(nil),              // 9: department.BaseDetailV2Response
	(*UserIsLeaderResponse)(nil),              // 10: department.UserIsLeaderResponse
	(*FindLeadersFromDIdResponse)(nil),        // 11: department.FindLeadersFromDIdResponse
	(*MyDepartmentsDirectLeadersRequest)(nil), // 12: department.MyDepartmentsDirectLeadersRequest
	(*MyDirectLeadersRequest)(nil),            // 13: department.MyDirectLeadersRequest
	(*LeaderInfo)(nil),                        // 14: department.LeaderInfo
	(*MyDirectLeadersResponse)(nil),           // 15: department.MyDirectLeadersResponse
	(*PositionInfo)(nil),                      // 16: department.PositionInfo
	(*NodeWithPosition)(nil),                  // 17: department.NodeWithPosition
	(*TreeWithPositionResponse)(nil),          // 18: department.TreeWithPositionResponse
	(*DepartmentBossesByAuthReq)(nil),         // 19: department.DepartmentBossesByAuthReq
	(*DepartmentBoss)(nil),                    // 20: department.DepartmentBoss
	(*DepartmentAndBoss)(nil),                 // 21: department.DepartmentAndBoss
	(*DepartmentBossesByAuthResponse)(nil),    // 22: department.DepartmentBossesByAuthResponse
	(*MyBossesResponse)(nil),                  // 23: department.MyBossesResponse
	(*Bosses)(nil),                            // 24: department.Bosses
	(*Boss)(nil),                              // 25: department.Boss
	(*MyBossesReq)(nil),                       // 26: department.MyBossesReq
	(*TreeRequest)(nil),                       // 27: department.TreeRequest
	(*Node)(nil),                              // 28: department.Node
	(*TreeResponse)(nil),                      // 29: department.TreeResponse
	(*UserIdByBossIdInAuthReq)(nil),           // 30: department.UserIdByBossIdInAuthReq
	(*CoBindRequest)(nil),                     // 31: department.CoBindRequest
	(*CommonResponse)(nil),                    // 32: department.CommonResponse
	(*UserIdByBossIdRequest)(nil),             // 33: department.UserIdByBossIdRequest
	(*UserIdByBossIdResponse)(nil),            // 34: department.UserIdByBossIdResponse
	(*DepartmentsByAuthRequest)(nil),          // 35: department.DepartmentsByAuthRequest
	(*FindLeaderFromDIdRequest)(nil),          // 36: department.FindLeaderFromDIdRequest
	(*LeaderNameResponse)(nil),                // 37: department.LeaderNameResponse
	(*BasesRequest)(nil),                      // 38: department.BasesRequest
	(*BaseDep)(nil),                           // 39: department.BaseDep
	(*BasesResponse)(nil),                     // 40: department.BasesResponse
	(*BaseListResponse)(nil),                  // 41: department.BaseListResponse
	(*BaseDetailResponse)(nil),                // 42: department.BaseDetailResponse
	(*BaseAllRequest)(nil),                    // 43: department.BaseAllRequest
	(*BaseAllResponse)(nil),                   // 44: department.BaseAllResponse
	(*InfoByUserIdRequest)(nil),               // 45: department.InfoByUserIdRequest
	(*InfoByUserIdsRequest)(nil),              // 46: department.InfoByUserIdsRequest
	(*InfoByUserIdsResponse)(nil),             // 47: department.InfoByUserIdsResponse
	(*InfoByUserIdResponse)(nil),              // 48: department.InfoByUserIdResponse
	(*UsersRequest)(nil),                      // 49: department.UsersRequest
	(*UsersResponse)(nil),                     // 50: department.UsersResponse
	(*UserInfo)(nil),                          // 51: department.UserInfo
	(*RemoveUserRequest)(nil),                 // 52: department.RemoveUserRequest
	(*RemoveUserResponse)(nil),                // 53: department.RemoveUserResponse
	(*RemoveRequest)(nil),                     // 54: department.RemoveRequest
	(*RemoveResponse)(nil),                    // 55: department.RemoveResponse
	(*DetailsRequest)(nil),                    // 56: department.DetailsRequest
	(*DetailRequest)(nil),                     // 57: department.DetailRequest
	(*DetailsResponse)(nil),                   // 58: department.DetailsResponse
	(*DetailResponse)(nil),                    // 59: department.DetailResponse
	(*Leader)(nil),                            // 60: department.Leader
	(*Position)(nil),                          // 61: department.Position
	(*CreateRequest)(nil),                     // 62: department.CreateRequest
	(*UpdateResponse)(nil),                    // 63: department.UpdateResponse
	(*CreateResponse)(nil),                    // 64: department.CreateResponse
	(*ListRequest)(nil),                       // 65: department.ListRequest
	(*RuleData)(nil),                          // 66: department.RuleData
	(*Rule)(nil),                              // 67: department.Rule
	(*RuleInfo)(nil),                          // 68: department.RuleInfo
	(*DepartmentRule)(nil),                    // 69: department.DepartmentRule
	(*ListResponse)(nil),                      // 70: department.ListResponse
	(*RuleDetail)(nil),                        // 71: department.RuleDetail
	nil,                                       // 72: department.InfoByUserIdsResponse.DepartmentByUserIdEntry
}
var file_api_department_department_proto_depIdxs = []int32{
	1,  // 0: department.FindDepartmentHeadResponse.list:type_name -> department.FindDepartmentHead
	9,  // 1: department.BaseListV2Response.list:type_name -> department.BaseDetailV2Response
	37, // 2: department.FindLeadersFromDIdResponse.list:type_name -> department.LeaderNameResponse
	14, // 3: department.MyDirectLeadersResponse.list:type_name -> department.LeaderInfo
	17, // 4: department.NodeWithPosition.sons:type_name -> department.NodeWithPosition
	16, // 5: department.NodeWithPosition.positions:type_name -> department.PositionInfo
	17, // 6: department.TreeWithPositionResponse.nodes:type_name -> department.NodeWithPosition
	20, // 7: department.DepartmentAndBoss.bosses:type_name -> department.DepartmentBoss
	21, // 8: department.DepartmentBossesByAuthResponse.list:type_name -> department.DepartmentAndBoss
	24, // 9: department.MyBossesResponse.list:type_name -> department.Bosses
	25, // 10: department.Bosses.bosses:type_name -> department.Boss
	28, // 11: department.Node.sons:type_name -> department.Node
	28, // 12: department.TreeResponse.nodes:type_name -> department.Node
	39, // 13: department.BasesResponse.Data:type_name -> department.BaseDep
	42, // 14: department.BaseListResponse.Data:type_name -> department.BaseDetailResponse
	61, // 15: department.BaseDetailResponse.AllPositions:type_name -> department.Position
	62, // 16: department.BaseAllResponse.Data:type_name -> department.CreateRequest
	72, // 17: department.InfoByUserIdsResponse.DepartmentByUserId:type_name -> department.InfoByUserIdsResponse.DepartmentByUserIdEntry
	51, // 18: department.UsersResponse.Users:type_name -> department.UserInfo
	59, // 19: department.DetailsResponse.data:type_name -> department.DetailResponse
	69, // 20: department.DetailResponse.Rules:type_name -> department.DepartmentRule
	61, // 21: department.DetailResponse.LeaderPosition:type_name -> department.Position
	61, // 22: department.DetailResponse.Positions:type_name -> department.Position
	61, // 23: department.DetailResponse.AllPositions:type_name -> department.Position
	71, // 24: department.DetailResponse.DepartmentTreeRule:type_name -> department.RuleDetail
	68, // 25: department.Position.Rules:type_name -> department.RuleInfo
	51, // 26: department.Position.Users:type_name -> department.UserInfo
	67, // 27: department.CreateRequest.Rules:type_name -> department.Rule
	60, // 28: department.CreateRequest.Leaders:type_name -> department.Leader
	71, // 29: department.CreateRequest.DepartmentTreeRule:type_name -> department.RuleDetail
	66, // 30: department.Rule.RuleData:type_name -> department.RuleData
	66, // 31: department.DepartmentRule.RuleData:type_name -> department.RuleData
	59, // 32: department.ListResponse.Data:type_name -> department.DetailResponse
	66, // 33: department.RuleDetail.RuleData:type_name -> department.RuleData
	71, // 34: department.RuleDetail.Son:type_name -> department.RuleDetail
	62, // 35: department.Department.Create:input_type -> department.CreateRequest
	54, // 36: department.Department.Remove:input_type -> department.RemoveRequest
	57, // 37: department.Department.Detail:input_type -> department.DetailRequest
	56, // 38: department.Department.Details:input_type -> department.DetailsRequest
	62, // 39: department.Department.Update:input_type -> department.CreateRequest
	31, // 40: department.Department.BindPositions:input_type -> department.CoBindRequest
	31, // 41: department.Department.UnBindPositions:input_type -> department.CoBindRequest
	65, // 42: department.Department.List:input_type -> department.ListRequest
	27, // 43: department.Department.Tree:input_type -> department.TreeRequest
	43, // 44: department.Department.BaseList:input_type -> department.BaseAllRequest
	43, // 45: department.Department.BaseSyncList:input_type -> department.BaseAllRequest
	43, // 46: department.Department.BaseAll:input_type -> department.BaseAllRequest
	49, // 47: department.Department.Users:input_type -> department.UsersRequest
	49, // 48: department.Department.SellerUsers:input_type -> department.UsersRequest
	45, // 49: department.Department.InfoByUserId:input_type -> department.InfoByUserIdRequest
	46, // 50: department.Department.InfoByUserIds:input_type -> department.InfoByUserIdsRequest
	52, // 51: department.Department.RemoveUser:input_type -> department.RemoveUserRequest
	38, // 52: department.Department.Bases:input_type -> department.BasesRequest
	36, // 53: department.Department.FindLeaderFromDId:input_type -> department.FindLeaderFromDIdRequest
	36, // 54: department.Department.FindLeadersFromDId:input_type -> department.FindLeaderFromDIdRequest
	13, // 55: department.Department.UserIsLeader:input_type -> department.MyDirectLeadersRequest
	35, // 56: department.Department.DepartmentsByAuth:input_type -> department.DepartmentsByAuthRequest
	33, // 57: department.Department.UserIdByBossId:input_type -> department.UserIdByBossIdRequest
	30, // 58: department.Department.UserIdByBossIdInAuth:input_type -> department.UserIdByBossIdInAuthReq
	26, // 59: department.Department.MyBosses:input_type -> department.MyBossesReq
	19, // 60: department.Department.DepartmentBossesByAuth:input_type -> department.DepartmentBossesByAuthReq
	27, // 61: department.Department.TreeWithPosition:input_type -> department.TreeRequest
	13, // 62: department.Department.MyDirectLeaders:input_type -> department.MyDirectLeadersRequest
	12, // 63: department.Department.MyDepartmentsDirectLeaders:input_type -> department.MyDepartmentsDirectLeadersRequest
	7,  // 64: department.Department.BaseListV2:input_type -> department.BaseListV2Request
	6,  // 65: department.Department.UserIdsByFatherDepIds:input_type -> department.UserIdsByFatherDepIdRequest
	4,  // 66: department.Department.CreateDepartmentHead:input_type -> department.CreateDepartmentHeadRequest
	4,  // 67: department.Department.UpdateDepartmentHead:input_type -> department.CreateDepartmentHeadRequest
	2,  // 68: department.Department.FindDepartmentHead:input_type -> department.FindDepartmentHeadRequest
	64, // 69: department.Department.Create:output_type -> department.CreateResponse
	55, // 70: department.Department.Remove:output_type -> department.RemoveResponse
	59, // 71: department.Department.Detail:output_type -> department.DetailResponse
	58, // 72: department.Department.Details:output_type -> department.DetailsResponse
	63, // 73: department.Department.Update:output_type -> department.UpdateResponse
	32, // 74: department.Department.BindPositions:output_type -> department.CommonResponse
	32, // 75: department.Department.UnBindPositions:output_type -> department.CommonResponse
	70, // 76: department.Department.List:output_type -> department.ListResponse
	29, // 77: department.Department.Tree:output_type -> department.TreeResponse
	41, // 78: department.Department.BaseList:output_type -> department.BaseListResponse
	41, // 79: department.Department.BaseSyncList:output_type -> department.BaseListResponse
	44, // 80: department.Department.BaseAll:output_type -> department.BaseAllResponse
	50, // 81: department.Department.Users:output_type -> department.UsersResponse
	50, // 82: department.Department.SellerUsers:output_type -> department.UsersResponse
	48, // 83: department.Department.InfoByUserId:output_type -> department.InfoByUserIdResponse
	47, // 84: department.Department.InfoByUserIds:output_type -> department.InfoByUserIdsResponse
	53, // 85: department.Department.RemoveUser:output_type -> department.RemoveUserResponse
	40, // 86: department.Department.Bases:output_type -> department.BasesResponse
	37, // 87: department.Department.FindLeaderFromDId:output_type -> department.LeaderNameResponse
	11, // 88: department.Department.FindLeadersFromDId:output_type -> department.FindLeadersFromDIdResponse
	10, // 89: department.Department.UserIsLeader:output_type -> department.UserIsLeaderResponse
	40, // 90: department.Department.DepartmentsByAuth:output_type -> department.BasesResponse
	34, // 91: department.Department.UserIdByBossId:output_type -> department.UserIdByBossIdResponse
	34, // 92: department.Department.UserIdByBossIdInAuth:output_type -> department.UserIdByBossIdResponse
	23, // 93: department.Department.MyBosses:output_type -> department.MyBossesResponse
	22, // 94: department.Department.DepartmentBossesByAuth:output_type -> department.DepartmentBossesByAuthResponse
	18, // 95: department.Department.TreeWithPosition:output_type -> department.TreeWithPositionResponse
	15, // 96: department.Department.MyDirectLeaders:output_type -> department.MyDirectLeadersResponse
	15, // 97: department.Department.MyDepartmentsDirectLeaders:output_type -> department.MyDirectLeadersResponse
	8,  // 98: department.Department.BaseListV2:output_type -> department.BaseListV2Response
	5,  // 99: department.Department.UserIdsByFatherDepIds:output_type -> department.UserIdsByFatherDepIdResponse
	3,  // 100: department.Department.CreateDepartmentHead:output_type -> department.CreateDepartmentHeadResponse
	3,  // 101: department.Department.UpdateDepartmentHead:output_type -> department.CreateDepartmentHeadResponse
	0,  // 102: department.Department.FindDepartmentHead:output_type -> department.FindDepartmentHeadResponse
	69, // [69:103] is the sub-list for method output_type
	35, // [35:69] is the sub-list for method input_type
	35, // [35:35] is the sub-list for extension type_name
	35, // [35:35] is the sub-list for extension extendee
	0,  // [0:35] is the sub-list for field type_name
}

func init() { file_api_department_department_proto_init() }
func file_api_department_department_proto_init() {
	if File_api_department_department_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_api_department_department_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FindDepartmentHeadResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_department_department_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FindDepartmentHead); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_department_department_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FindDepartmentHeadRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_department_department_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateDepartmentHeadResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_department_department_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateDepartmentHeadRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_department_department_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UserIdsByFatherDepIdResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_department_department_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UserIdsByFatherDepIdRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_department_department_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BaseListV2Request); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_department_department_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BaseListV2Response); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_department_department_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BaseDetailV2Response); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_department_department_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UserIsLeaderResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_department_department_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FindLeadersFromDIdResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_department_department_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MyDepartmentsDirectLeadersRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_department_department_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MyDirectLeadersRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_department_department_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LeaderInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_department_department_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MyDirectLeadersResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_department_department_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PositionInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_department_department_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*NodeWithPosition); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_department_department_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TreeWithPositionResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_department_department_proto_msgTypes[19].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DepartmentBossesByAuthReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_department_department_proto_msgTypes[20].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DepartmentBoss); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_department_department_proto_msgTypes[21].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DepartmentAndBoss); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_department_department_proto_msgTypes[22].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DepartmentBossesByAuthResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_department_department_proto_msgTypes[23].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MyBossesResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_department_department_proto_msgTypes[24].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Bosses); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_department_department_proto_msgTypes[25].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Boss); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_department_department_proto_msgTypes[26].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MyBossesReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_department_department_proto_msgTypes[27].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TreeRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_department_department_proto_msgTypes[28].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Node); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_department_department_proto_msgTypes[29].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TreeResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_department_department_proto_msgTypes[30].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UserIdByBossIdInAuthReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_department_department_proto_msgTypes[31].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CoBindRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_department_department_proto_msgTypes[32].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CommonResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_department_department_proto_msgTypes[33].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UserIdByBossIdRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_department_department_proto_msgTypes[34].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UserIdByBossIdResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_department_department_proto_msgTypes[35].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DepartmentsByAuthRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_department_department_proto_msgTypes[36].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FindLeaderFromDIdRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_department_department_proto_msgTypes[37].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LeaderNameResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_department_department_proto_msgTypes[38].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BasesRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_department_department_proto_msgTypes[39].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BaseDep); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_department_department_proto_msgTypes[40].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BasesResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_department_department_proto_msgTypes[41].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BaseListResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_department_department_proto_msgTypes[42].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BaseDetailResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_department_department_proto_msgTypes[43].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BaseAllRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_department_department_proto_msgTypes[44].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BaseAllResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_department_department_proto_msgTypes[45].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*InfoByUserIdRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_department_department_proto_msgTypes[46].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*InfoByUserIdsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_department_department_proto_msgTypes[47].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*InfoByUserIdsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_department_department_proto_msgTypes[48].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*InfoByUserIdResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_department_department_proto_msgTypes[49].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UsersRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_department_department_proto_msgTypes[50].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UsersResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_department_department_proto_msgTypes[51].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UserInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_department_department_proto_msgTypes[52].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RemoveUserRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_department_department_proto_msgTypes[53].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RemoveUserResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_department_department_proto_msgTypes[54].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RemoveRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_department_department_proto_msgTypes[55].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RemoveResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_department_department_proto_msgTypes[56].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DetailsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_department_department_proto_msgTypes[57].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DetailRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_department_department_proto_msgTypes[58].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DetailsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_department_department_proto_msgTypes[59].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DetailResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_department_department_proto_msgTypes[60].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Leader); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_department_department_proto_msgTypes[61].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Position); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_department_department_proto_msgTypes[62].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_department_department_proto_msgTypes[63].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_department_department_proto_msgTypes[64].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_department_department_proto_msgTypes[65].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_department_department_proto_msgTypes[66].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RuleData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_department_department_proto_msgTypes[67].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Rule); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_department_department_proto_msgTypes[68].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RuleInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_department_department_proto_msgTypes[69].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DepartmentRule); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_department_department_proto_msgTypes[70].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_department_department_proto_msgTypes[71].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RuleDetail); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_department_department_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   73,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_api_department_department_proto_goTypes,
		DependencyIndexes: file_api_department_department_proto_depIdxs,
		MessageInfos:      file_api_department_department_proto_msgTypes,
	}.Build()
	File_api_department_department_proto = out.File
	file_api_department_department_proto_rawDesc = nil
	file_api_department_department_proto_goTypes = nil
	file_api_department_department_proto_depIdxs = nil
}
