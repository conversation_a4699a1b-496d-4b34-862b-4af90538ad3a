// Code generated by protoc-gen-go-triple. DO NOT EDIT.
// versions:
// - protoc-gen-go-triple v1.0.8
// - protoc             v4.24.0--rc1
// source: api/department/department.proto

package department

import (
	context "context"
	protocol "dubbo.apache.org/dubbo-go/v3/protocol"
	dubbo3 "dubbo.apache.org/dubbo-go/v3/protocol/dubbo3"
	invocation "dubbo.apache.org/dubbo-go/v3/protocol/invocation"
	grpc_go "github.com/dubbogo/grpc-go"
	codes "github.com/dubbogo/grpc-go/codes"
	metadata "github.com/dubbogo/grpc-go/metadata"
	status "github.com/dubbogo/grpc-go/status"
	common "github.com/dubbogo/triple/pkg/common"
	constant "github.com/dubbogo/triple/pkg/common/constant"
	triple "github.com/dubbogo/triple/pkg/triple"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc_go.SupportPackageIsVersion7

// DepartmentClient is the client API for Department service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type DepartmentClient interface {
	Create(ctx context.Context, in *CreateRequest, opts ...grpc_go.CallOption) (*CreateResponse, common.ErrorWithAttachment)
	Remove(ctx context.Context, in *RemoveRequest, opts ...grpc_go.CallOption) (*RemoveResponse, common.ErrorWithAttachment)
	Detail(ctx context.Context, in *DetailRequest, opts ...grpc_go.CallOption) (*DetailResponse, common.ErrorWithAttachment)
	Details(ctx context.Context, in *DetailsRequest, opts ...grpc_go.CallOption) (*DetailsResponse, common.ErrorWithAttachment)
	Update(ctx context.Context, in *CreateRequest, opts ...grpc_go.CallOption) (*UpdateResponse, common.ErrorWithAttachment)
	BindPositions(ctx context.Context, in *CoBindRequest, opts ...grpc_go.CallOption) (*CommonResponse, common.ErrorWithAttachment)
	UnBindPositions(ctx context.Context, in *CoBindRequest, opts ...grpc_go.CallOption) (*CommonResponse, common.ErrorWithAttachment)
	List(ctx context.Context, in *ListRequest, opts ...grpc_go.CallOption) (*ListResponse, common.ErrorWithAttachment)
	Tree(ctx context.Context, in *TreeRequest, opts ...grpc_go.CallOption) (*TreeResponse, common.ErrorWithAttachment)
	BaseList(ctx context.Context, in *BaseAllRequest, opts ...grpc_go.CallOption) (*BaseListResponse, common.ErrorWithAttachment)
	BaseSyncList(ctx context.Context, in *BaseAllRequest, opts ...grpc_go.CallOption) (*BaseListResponse, common.ErrorWithAttachment)
	BaseAll(ctx context.Context, in *BaseAllRequest, opts ...grpc_go.CallOption) (*BaseAllResponse, common.ErrorWithAttachment)
	Users(ctx context.Context, in *UsersRequest, opts ...grpc_go.CallOption) (*UsersResponse, common.ErrorWithAttachment)
	SellerUsers(ctx context.Context, in *UsersRequest, opts ...grpc_go.CallOption) (*UsersResponse, common.ErrorWithAttachment)
	InfoByUserId(ctx context.Context, in *InfoByUserIdRequest, opts ...grpc_go.CallOption) (*InfoByUserIdResponse, common.ErrorWithAttachment)
	InfoByUserIds(ctx context.Context, in *InfoByUserIdsRequest, opts ...grpc_go.CallOption) (*InfoByUserIdsResponse, common.ErrorWithAttachment)
	RemoveUser(ctx context.Context, in *RemoveUserRequest, opts ...grpc_go.CallOption) (*RemoveUserResponse, common.ErrorWithAttachment)
	Bases(ctx context.Context, in *BasesRequest, opts ...grpc_go.CallOption) (*BasesResponse, common.ErrorWithAttachment)
	FindLeaderFromDId(ctx context.Context, in *FindLeaderFromDIdRequest, opts ...grpc_go.CallOption) (*LeaderNameResponse, common.ErrorWithAttachment)
	FindLeadersFromDId(ctx context.Context, in *FindLeaderFromDIdRequest, opts ...grpc_go.CallOption) (*FindLeadersFromDIdResponse, common.ErrorWithAttachment)
	UserIsLeader(ctx context.Context, in *MyDirectLeadersRequest, opts ...grpc_go.CallOption) (*UserIsLeaderResponse, common.ErrorWithAttachment)
	DepartmentsByAuth(ctx context.Context, in *DepartmentsByAuthRequest, opts ...grpc_go.CallOption) (*BasesResponse, common.ErrorWithAttachment)
	UserIdByBossId(ctx context.Context, in *UserIdByBossIdRequest, opts ...grpc_go.CallOption) (*UserIdByBossIdResponse, common.ErrorWithAttachment)
	UserIdByBossIdInAuth(ctx context.Context, in *UserIdByBossIdInAuthReq, opts ...grpc_go.CallOption) (*UserIdByBossIdResponse, common.ErrorWithAttachment)
	MyBosses(ctx context.Context, in *MyBossesReq, opts ...grpc_go.CallOption) (*MyBossesResponse, common.ErrorWithAttachment)
	DepartmentBossesByAuth(ctx context.Context, in *DepartmentBossesByAuthReq, opts ...grpc_go.CallOption) (*DepartmentBossesByAuthResponse, common.ErrorWithAttachment)
	TreeWithPosition(ctx context.Context, in *TreeRequest, opts ...grpc_go.CallOption) (*TreeWithPositionResponse, common.ErrorWithAttachment)
	MyDirectLeaders(ctx context.Context, in *MyDirectLeadersRequest, opts ...grpc_go.CallOption) (*MyDirectLeadersResponse, common.ErrorWithAttachment)
	MyDepartmentsDirectLeaders(ctx context.Context, in *MyDepartmentsDirectLeadersRequest, opts ...grpc_go.CallOption) (*MyDirectLeadersResponse, common.ErrorWithAttachment)
	BaseListV2(ctx context.Context, in *BaseListV2Request, opts ...grpc_go.CallOption) (*BaseListV2Response, common.ErrorWithAttachment)
	UserIdsByFatherDepIds(ctx context.Context, in *UserIdsByFatherDepIdRequest, opts ...grpc_go.CallOption) (*UserIdsByFatherDepIdResponse, common.ErrorWithAttachment)
	CreateDepartmentHead(ctx context.Context, in *CreateDepartmentHeadRequest, opts ...grpc_go.CallOption) (*CreateDepartmentHeadResponse, common.ErrorWithAttachment)
	UpdateDepartmentHead(ctx context.Context, in *CreateDepartmentHeadRequest, opts ...grpc_go.CallOption) (*CreateDepartmentHeadResponse, common.ErrorWithAttachment)
	FindDepartmentHead(ctx context.Context, in *FindDepartmentHeadRequest, opts ...grpc_go.CallOption) (*FindDepartmentHeadResponse, common.ErrorWithAttachment)
}

type departmentClient struct {
	cc *triple.TripleConn
}

type DepartmentClientImpl struct {
	Create                     func(ctx context.Context, in *CreateRequest) (*CreateResponse, error)
	Remove                     func(ctx context.Context, in *RemoveRequest) (*RemoveResponse, error)
	Detail                     func(ctx context.Context, in *DetailRequest) (*DetailResponse, error)
	Details                    func(ctx context.Context, in *DetailsRequest) (*DetailsResponse, error)
	Update                     func(ctx context.Context, in *CreateRequest) (*UpdateResponse, error)
	BindPositions              func(ctx context.Context, in *CoBindRequest) (*CommonResponse, error)
	UnBindPositions            func(ctx context.Context, in *CoBindRequest) (*CommonResponse, error)
	List                       func(ctx context.Context, in *ListRequest) (*ListResponse, error)
	Tree                       func(ctx context.Context, in *TreeRequest) (*TreeResponse, error)
	BaseList                   func(ctx context.Context, in *BaseAllRequest) (*BaseListResponse, error)
	BaseSyncList               func(ctx context.Context, in *BaseAllRequest) (*BaseListResponse, error)
	BaseAll                    func(ctx context.Context, in *BaseAllRequest) (*BaseAllResponse, error)
	Users                      func(ctx context.Context, in *UsersRequest) (*UsersResponse, error)
	SellerUsers                func(ctx context.Context, in *UsersRequest) (*UsersResponse, error)
	InfoByUserId               func(ctx context.Context, in *InfoByUserIdRequest) (*InfoByUserIdResponse, error)
	InfoByUserIds              func(ctx context.Context, in *InfoByUserIdsRequest) (*InfoByUserIdsResponse, error)
	RemoveUser                 func(ctx context.Context, in *RemoveUserRequest) (*RemoveUserResponse, error)
	Bases                      func(ctx context.Context, in *BasesRequest) (*BasesResponse, error)
	FindLeaderFromDId          func(ctx context.Context, in *FindLeaderFromDIdRequest) (*LeaderNameResponse, error)
	FindLeadersFromDId         func(ctx context.Context, in *FindLeaderFromDIdRequest) (*FindLeadersFromDIdResponse, error)
	UserIsLeader               func(ctx context.Context, in *MyDirectLeadersRequest) (*UserIsLeaderResponse, error)
	DepartmentsByAuth          func(ctx context.Context, in *DepartmentsByAuthRequest) (*BasesResponse, error)
	UserIdByBossId             func(ctx context.Context, in *UserIdByBossIdRequest) (*UserIdByBossIdResponse, error)
	UserIdByBossIdInAuth       func(ctx context.Context, in *UserIdByBossIdInAuthReq) (*UserIdByBossIdResponse, error)
	MyBosses                   func(ctx context.Context, in *MyBossesReq) (*MyBossesResponse, error)
	DepartmentBossesByAuth     func(ctx context.Context, in *DepartmentBossesByAuthReq) (*DepartmentBossesByAuthResponse, error)
	TreeWithPosition           func(ctx context.Context, in *TreeRequest) (*TreeWithPositionResponse, error)
	MyDirectLeaders            func(ctx context.Context, in *MyDirectLeadersRequest) (*MyDirectLeadersResponse, error)
	MyDepartmentsDirectLeaders func(ctx context.Context, in *MyDepartmentsDirectLeadersRequest) (*MyDirectLeadersResponse, error)
	BaseListV2                 func(ctx context.Context, in *BaseListV2Request) (*BaseListV2Response, error)
	UserIdsByFatherDepIds      func(ctx context.Context, in *UserIdsByFatherDepIdRequest) (*UserIdsByFatherDepIdResponse, error)
	CreateDepartmentHead       func(ctx context.Context, in *CreateDepartmentHeadRequest) (*CreateDepartmentHeadResponse, error)
	UpdateDepartmentHead       func(ctx context.Context, in *CreateDepartmentHeadRequest) (*CreateDepartmentHeadResponse, error)
	FindDepartmentHead         func(ctx context.Context, in *FindDepartmentHeadRequest) (*FindDepartmentHeadResponse, error)
}

func (c *DepartmentClientImpl) GetDubboStub(cc *triple.TripleConn) DepartmentClient {
	return NewDepartmentClient(cc)
}

func (c *DepartmentClientImpl) XXX_InterfaceName() string {
	return "department.Department"
}

func NewDepartmentClient(cc *triple.TripleConn) DepartmentClient {
	return &departmentClient{cc}
}

func (c *departmentClient) Create(ctx context.Context, in *CreateRequest, opts ...grpc_go.CallOption) (*CreateResponse, common.ErrorWithAttachment) {
	out := new(CreateResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/Create", in, out)
}

func (c *departmentClient) Remove(ctx context.Context, in *RemoveRequest, opts ...grpc_go.CallOption) (*RemoveResponse, common.ErrorWithAttachment) {
	out := new(RemoveResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/Remove", in, out)
}

func (c *departmentClient) Detail(ctx context.Context, in *DetailRequest, opts ...grpc_go.CallOption) (*DetailResponse, common.ErrorWithAttachment) {
	out := new(DetailResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/Detail", in, out)
}

func (c *departmentClient) Details(ctx context.Context, in *DetailsRequest, opts ...grpc_go.CallOption) (*DetailsResponse, common.ErrorWithAttachment) {
	out := new(DetailsResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/Details", in, out)
}

func (c *departmentClient) Update(ctx context.Context, in *CreateRequest, opts ...grpc_go.CallOption) (*UpdateResponse, common.ErrorWithAttachment) {
	out := new(UpdateResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/Update", in, out)
}

func (c *departmentClient) BindPositions(ctx context.Context, in *CoBindRequest, opts ...grpc_go.CallOption) (*CommonResponse, common.ErrorWithAttachment) {
	out := new(CommonResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/BindPositions", in, out)
}

func (c *departmentClient) UnBindPositions(ctx context.Context, in *CoBindRequest, opts ...grpc_go.CallOption) (*CommonResponse, common.ErrorWithAttachment) {
	out := new(CommonResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/UnBindPositions", in, out)
}

func (c *departmentClient) List(ctx context.Context, in *ListRequest, opts ...grpc_go.CallOption) (*ListResponse, common.ErrorWithAttachment) {
	out := new(ListResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/List", in, out)
}

func (c *departmentClient) Tree(ctx context.Context, in *TreeRequest, opts ...grpc_go.CallOption) (*TreeResponse, common.ErrorWithAttachment) {
	out := new(TreeResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/Tree", in, out)
}

func (c *departmentClient) BaseList(ctx context.Context, in *BaseAllRequest, opts ...grpc_go.CallOption) (*BaseListResponse, common.ErrorWithAttachment) {
	out := new(BaseListResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/BaseList", in, out)
}

func (c *departmentClient) BaseSyncList(ctx context.Context, in *BaseAllRequest, opts ...grpc_go.CallOption) (*BaseListResponse, common.ErrorWithAttachment) {
	out := new(BaseListResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/BaseSyncList", in, out)
}

func (c *departmentClient) BaseAll(ctx context.Context, in *BaseAllRequest, opts ...grpc_go.CallOption) (*BaseAllResponse, common.ErrorWithAttachment) {
	out := new(BaseAllResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/BaseAll", in, out)
}

func (c *departmentClient) Users(ctx context.Context, in *UsersRequest, opts ...grpc_go.CallOption) (*UsersResponse, common.ErrorWithAttachment) {
	out := new(UsersResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/Users", in, out)
}

func (c *departmentClient) SellerUsers(ctx context.Context, in *UsersRequest, opts ...grpc_go.CallOption) (*UsersResponse, common.ErrorWithAttachment) {
	out := new(UsersResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/SellerUsers", in, out)
}

func (c *departmentClient) InfoByUserId(ctx context.Context, in *InfoByUserIdRequest, opts ...grpc_go.CallOption) (*InfoByUserIdResponse, common.ErrorWithAttachment) {
	out := new(InfoByUserIdResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/InfoByUserId", in, out)
}

func (c *departmentClient) InfoByUserIds(ctx context.Context, in *InfoByUserIdsRequest, opts ...grpc_go.CallOption) (*InfoByUserIdsResponse, common.ErrorWithAttachment) {
	out := new(InfoByUserIdsResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/InfoByUserIds", in, out)
}

func (c *departmentClient) RemoveUser(ctx context.Context, in *RemoveUserRequest, opts ...grpc_go.CallOption) (*RemoveUserResponse, common.ErrorWithAttachment) {
	out := new(RemoveUserResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/RemoveUser", in, out)
}

func (c *departmentClient) Bases(ctx context.Context, in *BasesRequest, opts ...grpc_go.CallOption) (*BasesResponse, common.ErrorWithAttachment) {
	out := new(BasesResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/Bases", in, out)
}

func (c *departmentClient) FindLeaderFromDId(ctx context.Context, in *FindLeaderFromDIdRequest, opts ...grpc_go.CallOption) (*LeaderNameResponse, common.ErrorWithAttachment) {
	out := new(LeaderNameResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/FindLeaderFromDId", in, out)
}

func (c *departmentClient) FindLeadersFromDId(ctx context.Context, in *FindLeaderFromDIdRequest, opts ...grpc_go.CallOption) (*FindLeadersFromDIdResponse, common.ErrorWithAttachment) {
	out := new(FindLeadersFromDIdResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/FindLeadersFromDId", in, out)
}

func (c *departmentClient) UserIsLeader(ctx context.Context, in *MyDirectLeadersRequest, opts ...grpc_go.CallOption) (*UserIsLeaderResponse, common.ErrorWithAttachment) {
	out := new(UserIsLeaderResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/UserIsLeader", in, out)
}

func (c *departmentClient) DepartmentsByAuth(ctx context.Context, in *DepartmentsByAuthRequest, opts ...grpc_go.CallOption) (*BasesResponse, common.ErrorWithAttachment) {
	out := new(BasesResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/DepartmentsByAuth", in, out)
}

func (c *departmentClient) UserIdByBossId(ctx context.Context, in *UserIdByBossIdRequest, opts ...grpc_go.CallOption) (*UserIdByBossIdResponse, common.ErrorWithAttachment) {
	out := new(UserIdByBossIdResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/UserIdByBossId", in, out)
}

func (c *departmentClient) UserIdByBossIdInAuth(ctx context.Context, in *UserIdByBossIdInAuthReq, opts ...grpc_go.CallOption) (*UserIdByBossIdResponse, common.ErrorWithAttachment) {
	out := new(UserIdByBossIdResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/UserIdByBossIdInAuth", in, out)
}

func (c *departmentClient) MyBosses(ctx context.Context, in *MyBossesReq, opts ...grpc_go.CallOption) (*MyBossesResponse, common.ErrorWithAttachment) {
	out := new(MyBossesResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/MyBosses", in, out)
}

func (c *departmentClient) DepartmentBossesByAuth(ctx context.Context, in *DepartmentBossesByAuthReq, opts ...grpc_go.CallOption) (*DepartmentBossesByAuthResponse, common.ErrorWithAttachment) {
	out := new(DepartmentBossesByAuthResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/DepartmentBossesByAuth", in, out)
}

func (c *departmentClient) TreeWithPosition(ctx context.Context, in *TreeRequest, opts ...grpc_go.CallOption) (*TreeWithPositionResponse, common.ErrorWithAttachment) {
	out := new(TreeWithPositionResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/TreeWithPosition", in, out)
}

func (c *departmentClient) MyDirectLeaders(ctx context.Context, in *MyDirectLeadersRequest, opts ...grpc_go.CallOption) (*MyDirectLeadersResponse, common.ErrorWithAttachment) {
	out := new(MyDirectLeadersResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/MyDirectLeaders", in, out)
}

func (c *departmentClient) MyDepartmentsDirectLeaders(ctx context.Context, in *MyDepartmentsDirectLeadersRequest, opts ...grpc_go.CallOption) (*MyDirectLeadersResponse, common.ErrorWithAttachment) {
	out := new(MyDirectLeadersResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/MyDepartmentsDirectLeaders", in, out)
}

func (c *departmentClient) BaseListV2(ctx context.Context, in *BaseListV2Request, opts ...grpc_go.CallOption) (*BaseListV2Response, common.ErrorWithAttachment) {
	out := new(BaseListV2Response)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/BaseListV2", in, out)
}

func (c *departmentClient) UserIdsByFatherDepIds(ctx context.Context, in *UserIdsByFatherDepIdRequest, opts ...grpc_go.CallOption) (*UserIdsByFatherDepIdResponse, common.ErrorWithAttachment) {
	out := new(UserIdsByFatherDepIdResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/UserIdsByFatherDepIds", in, out)
}

func (c *departmentClient) CreateDepartmentHead(ctx context.Context, in *CreateDepartmentHeadRequest, opts ...grpc_go.CallOption) (*CreateDepartmentHeadResponse, common.ErrorWithAttachment) {
	out := new(CreateDepartmentHeadResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/CreateDepartmentHead", in, out)
}

func (c *departmentClient) UpdateDepartmentHead(ctx context.Context, in *CreateDepartmentHeadRequest, opts ...grpc_go.CallOption) (*CreateDepartmentHeadResponse, common.ErrorWithAttachment) {
	out := new(CreateDepartmentHeadResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/UpdateDepartmentHead", in, out)
}

func (c *departmentClient) FindDepartmentHead(ctx context.Context, in *FindDepartmentHeadRequest, opts ...grpc_go.CallOption) (*FindDepartmentHeadResponse, common.ErrorWithAttachment) {
	out := new(FindDepartmentHeadResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/FindDepartmentHead", in, out)
}

// DepartmentServer is the server API for Department service.
// All implementations must embed UnimplementedDepartmentServer
// for forward compatibility
type DepartmentServer interface {
	Create(context.Context, *CreateRequest) (*CreateResponse, error)
	Remove(context.Context, *RemoveRequest) (*RemoveResponse, error)
	Detail(context.Context, *DetailRequest) (*DetailResponse, error)
	Details(context.Context, *DetailsRequest) (*DetailsResponse, error)
	Update(context.Context, *CreateRequest) (*UpdateResponse, error)
	BindPositions(context.Context, *CoBindRequest) (*CommonResponse, error)
	UnBindPositions(context.Context, *CoBindRequest) (*CommonResponse, error)
	List(context.Context, *ListRequest) (*ListResponse, error)
	Tree(context.Context, *TreeRequest) (*TreeResponse, error)
	BaseList(context.Context, *BaseAllRequest) (*BaseListResponse, error)
	BaseSyncList(context.Context, *BaseAllRequest) (*BaseListResponse, error)
	BaseAll(context.Context, *BaseAllRequest) (*BaseAllResponse, error)
	Users(context.Context, *UsersRequest) (*UsersResponse, error)
	SellerUsers(context.Context, *UsersRequest) (*UsersResponse, error)
	InfoByUserId(context.Context, *InfoByUserIdRequest) (*InfoByUserIdResponse, error)
	InfoByUserIds(context.Context, *InfoByUserIdsRequest) (*InfoByUserIdsResponse, error)
	RemoveUser(context.Context, *RemoveUserRequest) (*RemoveUserResponse, error)
	Bases(context.Context, *BasesRequest) (*BasesResponse, error)
	FindLeaderFromDId(context.Context, *FindLeaderFromDIdRequest) (*LeaderNameResponse, error)
	FindLeadersFromDId(context.Context, *FindLeaderFromDIdRequest) (*FindLeadersFromDIdResponse, error)
	UserIsLeader(context.Context, *MyDirectLeadersRequest) (*UserIsLeaderResponse, error)
	DepartmentsByAuth(context.Context, *DepartmentsByAuthRequest) (*BasesResponse, error)
	UserIdByBossId(context.Context, *UserIdByBossIdRequest) (*UserIdByBossIdResponse, error)
	UserIdByBossIdInAuth(context.Context, *UserIdByBossIdInAuthReq) (*UserIdByBossIdResponse, error)
	MyBosses(context.Context, *MyBossesReq) (*MyBossesResponse, error)
	DepartmentBossesByAuth(context.Context, *DepartmentBossesByAuthReq) (*DepartmentBossesByAuthResponse, error)
	TreeWithPosition(context.Context, *TreeRequest) (*TreeWithPositionResponse, error)
	MyDirectLeaders(context.Context, *MyDirectLeadersRequest) (*MyDirectLeadersResponse, error)
	MyDepartmentsDirectLeaders(context.Context, *MyDepartmentsDirectLeadersRequest) (*MyDirectLeadersResponse, error)
	BaseListV2(context.Context, *BaseListV2Request) (*BaseListV2Response, error)
	UserIdsByFatherDepIds(context.Context, *UserIdsByFatherDepIdRequest) (*UserIdsByFatherDepIdResponse, error)
	CreateDepartmentHead(context.Context, *CreateDepartmentHeadRequest) (*CreateDepartmentHeadResponse, error)
	UpdateDepartmentHead(context.Context, *CreateDepartmentHeadRequest) (*CreateDepartmentHeadResponse, error)
	FindDepartmentHead(context.Context, *FindDepartmentHeadRequest) (*FindDepartmentHeadResponse, error)
	mustEmbedUnimplementedDepartmentServer()
}

// UnimplementedDepartmentServer must be embedded to have forward compatible implementations.
type UnimplementedDepartmentServer struct {
	proxyImpl protocol.Invoker
}

func (UnimplementedDepartmentServer) Create(context.Context, *CreateRequest) (*CreateResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Create not implemented")
}
func (UnimplementedDepartmentServer) Remove(context.Context, *RemoveRequest) (*RemoveResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Remove not implemented")
}
func (UnimplementedDepartmentServer) Detail(context.Context, *DetailRequest) (*DetailResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Detail not implemented")
}
func (UnimplementedDepartmentServer) Details(context.Context, *DetailsRequest) (*DetailsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Details not implemented")
}
func (UnimplementedDepartmentServer) Update(context.Context, *CreateRequest) (*UpdateResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Update not implemented")
}
func (UnimplementedDepartmentServer) BindPositions(context.Context, *CoBindRequest) (*CommonResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method BindPositions not implemented")
}
func (UnimplementedDepartmentServer) UnBindPositions(context.Context, *CoBindRequest) (*CommonResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UnBindPositions not implemented")
}
func (UnimplementedDepartmentServer) List(context.Context, *ListRequest) (*ListResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method List not implemented")
}
func (UnimplementedDepartmentServer) Tree(context.Context, *TreeRequest) (*TreeResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Tree not implemented")
}
func (UnimplementedDepartmentServer) BaseList(context.Context, *BaseAllRequest) (*BaseListResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method BaseList not implemented")
}
func (UnimplementedDepartmentServer) BaseSyncList(context.Context, *BaseAllRequest) (*BaseListResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method BaseSyncList not implemented")
}
func (UnimplementedDepartmentServer) BaseAll(context.Context, *BaseAllRequest) (*BaseAllResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method BaseAll not implemented")
}
func (UnimplementedDepartmentServer) Users(context.Context, *UsersRequest) (*UsersResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Users not implemented")
}
func (UnimplementedDepartmentServer) SellerUsers(context.Context, *UsersRequest) (*UsersResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SellerUsers not implemented")
}
func (UnimplementedDepartmentServer) InfoByUserId(context.Context, *InfoByUserIdRequest) (*InfoByUserIdResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method InfoByUserId not implemented")
}
func (UnimplementedDepartmentServer) InfoByUserIds(context.Context, *InfoByUserIdsRequest) (*InfoByUserIdsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method InfoByUserIds not implemented")
}
func (UnimplementedDepartmentServer) RemoveUser(context.Context, *RemoveUserRequest) (*RemoveUserResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RemoveUser not implemented")
}
func (UnimplementedDepartmentServer) Bases(context.Context, *BasesRequest) (*BasesResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Bases not implemented")
}
func (UnimplementedDepartmentServer) FindLeaderFromDId(context.Context, *FindLeaderFromDIdRequest) (*LeaderNameResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FindLeaderFromDId not implemented")
}
func (UnimplementedDepartmentServer) FindLeadersFromDId(context.Context, *FindLeaderFromDIdRequest) (*FindLeadersFromDIdResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FindLeadersFromDId not implemented")
}
func (UnimplementedDepartmentServer) UserIsLeader(context.Context, *MyDirectLeadersRequest) (*UserIsLeaderResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UserIsLeader not implemented")
}
func (UnimplementedDepartmentServer) DepartmentsByAuth(context.Context, *DepartmentsByAuthRequest) (*BasesResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DepartmentsByAuth not implemented")
}
func (UnimplementedDepartmentServer) UserIdByBossId(context.Context, *UserIdByBossIdRequest) (*UserIdByBossIdResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UserIdByBossId not implemented")
}
func (UnimplementedDepartmentServer) UserIdByBossIdInAuth(context.Context, *UserIdByBossIdInAuthReq) (*UserIdByBossIdResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UserIdByBossIdInAuth not implemented")
}
func (UnimplementedDepartmentServer) MyBosses(context.Context, *MyBossesReq) (*MyBossesResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method MyBosses not implemented")
}
func (UnimplementedDepartmentServer) DepartmentBossesByAuth(context.Context, *DepartmentBossesByAuthReq) (*DepartmentBossesByAuthResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DepartmentBossesByAuth not implemented")
}
func (UnimplementedDepartmentServer) TreeWithPosition(context.Context, *TreeRequest) (*TreeWithPositionResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method TreeWithPosition not implemented")
}
func (UnimplementedDepartmentServer) MyDirectLeaders(context.Context, *MyDirectLeadersRequest) (*MyDirectLeadersResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method MyDirectLeaders not implemented")
}
func (UnimplementedDepartmentServer) MyDepartmentsDirectLeaders(context.Context, *MyDepartmentsDirectLeadersRequest) (*MyDirectLeadersResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method MyDepartmentsDirectLeaders not implemented")
}
func (UnimplementedDepartmentServer) BaseListV2(context.Context, *BaseListV2Request) (*BaseListV2Response, error) {
	return nil, status.Errorf(codes.Unimplemented, "method BaseListV2 not implemented")
}
func (UnimplementedDepartmentServer) UserIdsByFatherDepIds(context.Context, *UserIdsByFatherDepIdRequest) (*UserIdsByFatherDepIdResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UserIdsByFatherDepIds not implemented")
}
func (UnimplementedDepartmentServer) CreateDepartmentHead(context.Context, *CreateDepartmentHeadRequest) (*CreateDepartmentHeadResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateDepartmentHead not implemented")
}
func (UnimplementedDepartmentServer) UpdateDepartmentHead(context.Context, *CreateDepartmentHeadRequest) (*CreateDepartmentHeadResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateDepartmentHead not implemented")
}
func (UnimplementedDepartmentServer) FindDepartmentHead(context.Context, *FindDepartmentHeadRequest) (*FindDepartmentHeadResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FindDepartmentHead not implemented")
}
func (s *UnimplementedDepartmentServer) XXX_SetProxyImpl(impl protocol.Invoker) {
	s.proxyImpl = impl
}

func (s *UnimplementedDepartmentServer) XXX_GetProxyImpl() protocol.Invoker {
	return s.proxyImpl
}

func (s *UnimplementedDepartmentServer) XXX_ServiceDesc() *grpc_go.ServiceDesc {
	return &Department_ServiceDesc
}
func (s *UnimplementedDepartmentServer) XXX_InterfaceName() string {
	return "department.Department"
}

func (UnimplementedDepartmentServer) mustEmbedUnimplementedDepartmentServer() {}

// UnsafeDepartmentServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to DepartmentServer will
// result in compilation errors.
type UnsafeDepartmentServer interface {
	mustEmbedUnimplementedDepartmentServer()
}

func RegisterDepartmentServer(s grpc_go.ServiceRegistrar, srv DepartmentServer) {
	s.RegisterService(&Department_ServiceDesc, srv)
}

func _Department_Create_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("Create", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Department_Remove_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(RemoveRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("Remove", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Department_Detail_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(DetailRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("Detail", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Department_Details_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(DetailsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("Details", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Department_Update_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("Update", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Department_BindPositions_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(CoBindRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("BindPositions", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Department_UnBindPositions_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(CoBindRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("UnBindPositions", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Department_List_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("List", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Department_Tree_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(TreeRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("Tree", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Department_BaseList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(BaseAllRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("BaseList", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Department_BaseSyncList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(BaseAllRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("BaseSyncList", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Department_BaseAll_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(BaseAllRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("BaseAll", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Department_Users_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(UsersRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("Users", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Department_SellerUsers_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(UsersRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("SellerUsers", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Department_InfoByUserId_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(InfoByUserIdRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("InfoByUserId", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Department_InfoByUserIds_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(InfoByUserIdsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("InfoByUserIds", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Department_RemoveUser_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(RemoveUserRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("RemoveUser", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Department_Bases_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(BasesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("Bases", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Department_FindLeaderFromDId_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(FindLeaderFromDIdRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("FindLeaderFromDId", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Department_FindLeadersFromDId_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(FindLeaderFromDIdRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("FindLeadersFromDId", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Department_UserIsLeader_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(MyDirectLeadersRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("UserIsLeader", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Department_DepartmentsByAuth_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(DepartmentsByAuthRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("DepartmentsByAuth", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Department_UserIdByBossId_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(UserIdByBossIdRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("UserIdByBossId", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Department_UserIdByBossIdInAuth_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(UserIdByBossIdInAuthReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("UserIdByBossIdInAuth", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Department_MyBosses_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(MyBossesReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("MyBosses", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Department_DepartmentBossesByAuth_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(DepartmentBossesByAuthReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("DepartmentBossesByAuth", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Department_TreeWithPosition_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(TreeRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("TreeWithPosition", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Department_MyDirectLeaders_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(MyDirectLeadersRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("MyDirectLeaders", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Department_MyDepartmentsDirectLeaders_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(MyDepartmentsDirectLeadersRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("MyDepartmentsDirectLeaders", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Department_BaseListV2_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(BaseListV2Request)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("BaseListV2", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Department_UserIdsByFatherDepIds_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(UserIdsByFatherDepIdRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("UserIdsByFatherDepIds", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Department_CreateDepartmentHead_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateDepartmentHeadRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("CreateDepartmentHead", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Department_UpdateDepartmentHead_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateDepartmentHeadRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("UpdateDepartmentHead", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Department_FindDepartmentHead_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(FindDepartmentHeadRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("FindDepartmentHead", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

// Department_ServiceDesc is the grpc_go.ServiceDesc for Department service.
// It's only intended for direct use with grpc_go.RegisterService,
// and not to be introspected or modified (even as a copy)
var Department_ServiceDesc = grpc_go.ServiceDesc{
	ServiceName: "department.Department",
	HandlerType: (*DepartmentServer)(nil),
	Methods: []grpc_go.MethodDesc{
		{
			MethodName: "Create",
			Handler:    _Department_Create_Handler,
		},
		{
			MethodName: "Remove",
			Handler:    _Department_Remove_Handler,
		},
		{
			MethodName: "Detail",
			Handler:    _Department_Detail_Handler,
		},
		{
			MethodName: "Details",
			Handler:    _Department_Details_Handler,
		},
		{
			MethodName: "Update",
			Handler:    _Department_Update_Handler,
		},
		{
			MethodName: "BindPositions",
			Handler:    _Department_BindPositions_Handler,
		},
		{
			MethodName: "UnBindPositions",
			Handler:    _Department_UnBindPositions_Handler,
		},
		{
			MethodName: "List",
			Handler:    _Department_List_Handler,
		},
		{
			MethodName: "Tree",
			Handler:    _Department_Tree_Handler,
		},
		{
			MethodName: "BaseList",
			Handler:    _Department_BaseList_Handler,
		},
		{
			MethodName: "BaseSyncList",
			Handler:    _Department_BaseSyncList_Handler,
		},
		{
			MethodName: "BaseAll",
			Handler:    _Department_BaseAll_Handler,
		},
		{
			MethodName: "Users",
			Handler:    _Department_Users_Handler,
		},
		{
			MethodName: "SellerUsers",
			Handler:    _Department_SellerUsers_Handler,
		},
		{
			MethodName: "InfoByUserId",
			Handler:    _Department_InfoByUserId_Handler,
		},
		{
			MethodName: "InfoByUserIds",
			Handler:    _Department_InfoByUserIds_Handler,
		},
		{
			MethodName: "RemoveUser",
			Handler:    _Department_RemoveUser_Handler,
		},
		{
			MethodName: "Bases",
			Handler:    _Department_Bases_Handler,
		},
		{
			MethodName: "FindLeaderFromDId",
			Handler:    _Department_FindLeaderFromDId_Handler,
		},
		{
			MethodName: "FindLeadersFromDId",
			Handler:    _Department_FindLeadersFromDId_Handler,
		},
		{
			MethodName: "UserIsLeader",
			Handler:    _Department_UserIsLeader_Handler,
		},
		{
			MethodName: "DepartmentsByAuth",
			Handler:    _Department_DepartmentsByAuth_Handler,
		},
		{
			MethodName: "UserIdByBossId",
			Handler:    _Department_UserIdByBossId_Handler,
		},
		{
			MethodName: "UserIdByBossIdInAuth",
			Handler:    _Department_UserIdByBossIdInAuth_Handler,
		},
		{
			MethodName: "MyBosses",
			Handler:    _Department_MyBosses_Handler,
		},
		{
			MethodName: "DepartmentBossesByAuth",
			Handler:    _Department_DepartmentBossesByAuth_Handler,
		},
		{
			MethodName: "TreeWithPosition",
			Handler:    _Department_TreeWithPosition_Handler,
		},
		{
			MethodName: "MyDirectLeaders",
			Handler:    _Department_MyDirectLeaders_Handler,
		},
		{
			MethodName: "MyDepartmentsDirectLeaders",
			Handler:    _Department_MyDepartmentsDirectLeaders_Handler,
		},
		{
			MethodName: "BaseListV2",
			Handler:    _Department_BaseListV2_Handler,
		},
		{
			MethodName: "UserIdsByFatherDepIds",
			Handler:    _Department_UserIdsByFatherDepIds_Handler,
		},
		{
			MethodName: "CreateDepartmentHead",
			Handler:    _Department_CreateDepartmentHead_Handler,
		},
		{
			MethodName: "UpdateDepartmentHead",
			Handler:    _Department_UpdateDepartmentHead_Handler,
		},
		{
			MethodName: "FindDepartmentHead",
			Handler:    _Department_FindDepartmentHead_Handler,
		},
	},
	Streams:  []grpc_go.StreamDesc{},
	Metadata: "api/department/department.proto",
}
