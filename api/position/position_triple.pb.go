// Code generated by protoc-gen-go-triple. DO NOT EDIT.
// versions:
// - protoc-gen-go-triple v1.0.8
// - protoc             v3.21.12
// source: api/position/position.proto

package position

import (
	context "context"
	protocol "dubbo.apache.org/dubbo-go/v3/protocol"
	dubbo3 "dubbo.apache.org/dubbo-go/v3/protocol/dubbo3"
	invocation "dubbo.apache.org/dubbo-go/v3/protocol/invocation"
	grpc_go "github.com/dubbogo/grpc-go"
	codes "github.com/dubbogo/grpc-go/codes"
	metadata "github.com/dubbogo/grpc-go/metadata"
	status "github.com/dubbogo/grpc-go/status"
	common "github.com/dubbogo/triple/pkg/common"
	constant "github.com/dubbogo/triple/pkg/common/constant"
	triple "github.com/dubbogo/triple/pkg/triple"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc_go.SupportPackageIsVersion7

// PositionClient is the client API for Position service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type PositionClient interface {
	Create(ctx context.Context, in *CreateRequest, opts ...grpc_go.CallOption) (*CreateResponse, common.ErrorWithAttachment)
	BatchAddPositionRules(ctx context.Context, in *BatchAddRulesRequest, opts ...grpc_go.CallOption) (*CommonResponse, common.ErrorWithAttachment)
	ListByDid(ctx context.Context, in *ListByDidRequest, opts ...grpc_go.CallOption) (*ListByDidResponse, common.ErrorWithAttachment)
	Remove(ctx context.Context, in *RemoveRequest, opts ...grpc_go.CallOption) (*RemoveResponse, common.ErrorWithAttachment)
	BatchRemove(ctx context.Context, in *BatchRemoveRequest, opts ...grpc_go.CallOption) (*RemoveResponse, common.ErrorWithAttachment)
	Detail(ctx context.Context, in *DetailRequest, opts ...grpc_go.CallOption) (*CreateRequest, common.ErrorWithAttachment)
	Update(ctx context.Context, in *CreateRequest, opts ...grpc_go.CallOption) (*CreateResponse, common.ErrorWithAttachment)
	List(ctx context.Context, in *ListRequest, opts ...grpc_go.CallOption) (*ListResponse, common.ErrorWithAttachment)
	RemoveUser(ctx context.Context, in *RemoveUserRequest, opts ...grpc_go.CallOption) (*RemoveUserResponse, common.ErrorWithAttachment)
	BindUser(ctx context.Context, in *BindUserRequest, opts ...grpc_go.CallOption) (*BindUserResponse, common.ErrorWithAttachment)
	BindUserForName(ctx context.Context, in *BindUserRequest, opts ...grpc_go.CallOption) (*BindUserResponse, common.ErrorWithAttachment)
	AndBindUser(ctx context.Context, in *AddUserRequest, opts ...grpc_go.CallOption) (*BindUserResponse, common.ErrorWithAttachment)
	FindOneFromName(ctx context.Context, in *FindOneFromNameRequest, opts ...grpc_go.CallOption) (*CreateRequest, common.ErrorWithAttachment)
	PositionByUrl(ctx context.Context, in *PositionByUrlRequest, opts ...grpc_go.CallOption) (*PositionByUrlResponse, common.ErrorWithAttachment)
	PositionByUrls(ctx context.Context, in *PositionByUrlsRequest, opts ...grpc_go.CallOption) (*PositionByUrlResponse, common.ErrorWithAttachment)
	DoIHavaAuth(ctx context.Context, in *DoIHavaAuthRequest, opts ...grpc_go.CallOption) (*DoIHavaAuthResponse, common.ErrorWithAttachment)
	DoHavaAuthBatch(ctx context.Context, in *DoHavaAuthBatchRequest, opts ...grpc_go.CallOption) (*DoHavaAuthBatchResponse, common.ErrorWithAttachment)
	SearchDepartment(ctx context.Context, in *SearchDepartmentRequest, opts ...grpc_go.CallOption) (*SearchDepartmentResponse, common.ErrorWithAttachment)
	FindUsersByRule(ctx context.Context, in *FindUsersByRuleRequest, opts ...grpc_go.CallOption) (*PositionUsersResponse, common.ErrorWithAttachment)
	FindRuleByUserId(ctx context.Context, in *FindRuleByUserIdRequest, opts ...grpc_go.CallOption) (*FindRuleByUserIdResponse, common.ErrorWithAttachment)
	BindUserV2(ctx context.Context, in *BindUserV2Request, opts ...grpc_go.CallOption) (*CommonResponse, common.ErrorWithAttachment)
	BindUserForNameV2(ctx context.Context, in *BindUserV2Request, opts ...grpc_go.CallOption) (*CommonResponse, common.ErrorWithAttachment)
	AndBindUserV2(ctx context.Context, in *BindUserV2Request, opts ...grpc_go.CallOption) (*CommonResponse, common.ErrorWithAttachment)
	UserInfoV2(ctx context.Context, in *CreateResponse, opts ...grpc_go.CallOption) (*BindUserV2Request, common.ErrorWithAttachment)
	UserInfosV2(ctx context.Context, in *UserInfosV2Request, opts ...grpc_go.CallOption) (*UserInfosV2Response, common.ErrorWithAttachment)
	DetailV2(ctx context.Context, in *DetailRequest, opts ...grpc_go.CallOption) (*DetailResponse, common.ErrorWithAttachment)
	DepartmentIdsByUrlAndUserId(ctx context.Context, in *DepartmentIdsByUrlAndUserIdRequest, opts ...grpc_go.CallOption) (*DepartmentIdsByUrlAndUserIdResponse, common.ErrorWithAttachment)
	PositionLogList(ctx context.Context, in *LogListRequest, opts ...grpc_go.CallOption) (*LogListResponse, common.ErrorWithAttachment)
	CreatePositionLog(ctx context.Context, in *LogRequest, opts ...grpc_go.CallOption) (*CreateResponse, common.ErrorWithAttachment)
}

type positionClient struct {
	cc *triple.TripleConn
}

type PositionClientImpl struct {
	Create                      func(ctx context.Context, in *CreateRequest) (*CreateResponse, error)
	BatchAddPositionRules       func(ctx context.Context, in *BatchAddRulesRequest) (*CommonResponse, error)
	ListByDid                   func(ctx context.Context, in *ListByDidRequest) (*ListByDidResponse, error)
	Remove                      func(ctx context.Context, in *RemoveRequest) (*RemoveResponse, error)
	BatchRemove                 func(ctx context.Context, in *BatchRemoveRequest) (*RemoveResponse, error)
	Detail                      func(ctx context.Context, in *DetailRequest) (*CreateRequest, error)
	Update                      func(ctx context.Context, in *CreateRequest) (*CreateResponse, error)
	List                        func(ctx context.Context, in *ListRequest) (*ListResponse, error)
	RemoveUser                  func(ctx context.Context, in *RemoveUserRequest) (*RemoveUserResponse, error)
	BindUser                    func(ctx context.Context, in *BindUserRequest) (*BindUserResponse, error)
	BindUserForName             func(ctx context.Context, in *BindUserRequest) (*BindUserResponse, error)
	AndBindUser                 func(ctx context.Context, in *AddUserRequest) (*BindUserResponse, error)
	FindOneFromName             func(ctx context.Context, in *FindOneFromNameRequest) (*CreateRequest, error)
	PositionByUrl               func(ctx context.Context, in *PositionByUrlRequest) (*PositionByUrlResponse, error)
	PositionByUrls              func(ctx context.Context, in *PositionByUrlsRequest) (*PositionByUrlResponse, error)
	DoIHavaAuth                 func(ctx context.Context, in *DoIHavaAuthRequest) (*DoIHavaAuthResponse, error)
	DoHavaAuthBatch             func(ctx context.Context, in *DoHavaAuthBatchRequest) (*DoHavaAuthBatchResponse, error)
	SearchDepartment            func(ctx context.Context, in *SearchDepartmentRequest) (*SearchDepartmentResponse, error)
	FindUsersByRule             func(ctx context.Context, in *FindUsersByRuleRequest) (*PositionUsersResponse, error)
	FindRuleByUserId            func(ctx context.Context, in *FindRuleByUserIdRequest) (*FindRuleByUserIdResponse, error)
	BindUserV2                  func(ctx context.Context, in *BindUserV2Request) (*CommonResponse, error)
	BindUserForNameV2           func(ctx context.Context, in *BindUserV2Request) (*CommonResponse, error)
	AndBindUserV2               func(ctx context.Context, in *BindUserV2Request) (*CommonResponse, error)
	UserInfoV2                  func(ctx context.Context, in *CreateResponse) (*BindUserV2Request, error)
	UserInfosV2                 func(ctx context.Context, in *UserInfosV2Request) (*UserInfosV2Response, error)
	DetailV2                    func(ctx context.Context, in *DetailRequest) (*DetailResponse, error)
	DepartmentIdsByUrlAndUserId func(ctx context.Context, in *DepartmentIdsByUrlAndUserIdRequest) (*DepartmentIdsByUrlAndUserIdResponse, error)
	PositionLogList             func(ctx context.Context, in *LogListRequest) (*LogListResponse, error)
	CreatePositionLog           func(ctx context.Context, in *LogRequest) (*CreateResponse, error)
}

func (c *PositionClientImpl) GetDubboStub(cc *triple.TripleConn) PositionClient {
	return NewPositionClient(cc)
}

func (c *PositionClientImpl) XXX_InterfaceName() string {
	return "position.Position"
}

func NewPositionClient(cc *triple.TripleConn) PositionClient {
	return &positionClient{cc}
}

func (c *positionClient) Create(ctx context.Context, in *CreateRequest, opts ...grpc_go.CallOption) (*CreateResponse, common.ErrorWithAttachment) {
	out := new(CreateResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/Create", in, out)
}

func (c *positionClient) BatchAddPositionRules(ctx context.Context, in *BatchAddRulesRequest, opts ...grpc_go.CallOption) (*CommonResponse, common.ErrorWithAttachment) {
	out := new(CommonResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/BatchAddPositionRules", in, out)
}

func (c *positionClient) ListByDid(ctx context.Context, in *ListByDidRequest, opts ...grpc_go.CallOption) (*ListByDidResponse, common.ErrorWithAttachment) {
	out := new(ListByDidResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/ListByDid", in, out)
}

func (c *positionClient) Remove(ctx context.Context, in *RemoveRequest, opts ...grpc_go.CallOption) (*RemoveResponse, common.ErrorWithAttachment) {
	out := new(RemoveResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/Remove", in, out)
}

func (c *positionClient) BatchRemove(ctx context.Context, in *BatchRemoveRequest, opts ...grpc_go.CallOption) (*RemoveResponse, common.ErrorWithAttachment) {
	out := new(RemoveResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/BatchRemove", in, out)
}

func (c *positionClient) Detail(ctx context.Context, in *DetailRequest, opts ...grpc_go.CallOption) (*CreateRequest, common.ErrorWithAttachment) {
	out := new(CreateRequest)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/Detail", in, out)
}

func (c *positionClient) Update(ctx context.Context, in *CreateRequest, opts ...grpc_go.CallOption) (*CreateResponse, common.ErrorWithAttachment) {
	out := new(CreateResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/Update", in, out)
}

func (c *positionClient) List(ctx context.Context, in *ListRequest, opts ...grpc_go.CallOption) (*ListResponse, common.ErrorWithAttachment) {
	out := new(ListResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/List", in, out)
}

func (c *positionClient) RemoveUser(ctx context.Context, in *RemoveUserRequest, opts ...grpc_go.CallOption) (*RemoveUserResponse, common.ErrorWithAttachment) {
	out := new(RemoveUserResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/RemoveUser", in, out)
}

func (c *positionClient) BindUser(ctx context.Context, in *BindUserRequest, opts ...grpc_go.CallOption) (*BindUserResponse, common.ErrorWithAttachment) {
	out := new(BindUserResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/BindUser", in, out)
}

func (c *positionClient) BindUserForName(ctx context.Context, in *BindUserRequest, opts ...grpc_go.CallOption) (*BindUserResponse, common.ErrorWithAttachment) {
	out := new(BindUserResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/BindUserForName", in, out)
}

func (c *positionClient) AndBindUser(ctx context.Context, in *AddUserRequest, opts ...grpc_go.CallOption) (*BindUserResponse, common.ErrorWithAttachment) {
	out := new(BindUserResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/AndBindUser", in, out)
}

func (c *positionClient) FindOneFromName(ctx context.Context, in *FindOneFromNameRequest, opts ...grpc_go.CallOption) (*CreateRequest, common.ErrorWithAttachment) {
	out := new(CreateRequest)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/FindOneFromName", in, out)
}

func (c *positionClient) PositionByUrl(ctx context.Context, in *PositionByUrlRequest, opts ...grpc_go.CallOption) (*PositionByUrlResponse, common.ErrorWithAttachment) {
	out := new(PositionByUrlResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/PositionByUrl", in, out)
}

func (c *positionClient) PositionByUrls(ctx context.Context, in *PositionByUrlsRequest, opts ...grpc_go.CallOption) (*PositionByUrlResponse, common.ErrorWithAttachment) {
	out := new(PositionByUrlResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/PositionByUrls", in, out)
}

func (c *positionClient) DoIHavaAuth(ctx context.Context, in *DoIHavaAuthRequest, opts ...grpc_go.CallOption) (*DoIHavaAuthResponse, common.ErrorWithAttachment) {
	out := new(DoIHavaAuthResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/DoIHavaAuth", in, out)
}

func (c *positionClient) DoHavaAuthBatch(ctx context.Context, in *DoHavaAuthBatchRequest, opts ...grpc_go.CallOption) (*DoHavaAuthBatchResponse, common.ErrorWithAttachment) {
	out := new(DoHavaAuthBatchResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/DoHavaAuthBatch", in, out)
}

func (c *positionClient) SearchDepartment(ctx context.Context, in *SearchDepartmentRequest, opts ...grpc_go.CallOption) (*SearchDepartmentResponse, common.ErrorWithAttachment) {
	out := new(SearchDepartmentResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/SearchDepartment", in, out)
}

func (c *positionClient) FindUsersByRule(ctx context.Context, in *FindUsersByRuleRequest, opts ...grpc_go.CallOption) (*PositionUsersResponse, common.ErrorWithAttachment) {
	out := new(PositionUsersResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/FindUsersByRule", in, out)
}

func (c *positionClient) FindRuleByUserId(ctx context.Context, in *FindRuleByUserIdRequest, opts ...grpc_go.CallOption) (*FindRuleByUserIdResponse, common.ErrorWithAttachment) {
	out := new(FindRuleByUserIdResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/FindRuleByUserId", in, out)
}

func (c *positionClient) BindUserV2(ctx context.Context, in *BindUserV2Request, opts ...grpc_go.CallOption) (*CommonResponse, common.ErrorWithAttachment) {
	out := new(CommonResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/BindUserV2", in, out)
}

func (c *positionClient) BindUserForNameV2(ctx context.Context, in *BindUserV2Request, opts ...grpc_go.CallOption) (*CommonResponse, common.ErrorWithAttachment) {
	out := new(CommonResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/BindUserForNameV2", in, out)
}

func (c *positionClient) AndBindUserV2(ctx context.Context, in *BindUserV2Request, opts ...grpc_go.CallOption) (*CommonResponse, common.ErrorWithAttachment) {
	out := new(CommonResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/AndBindUserV2", in, out)
}

func (c *positionClient) UserInfoV2(ctx context.Context, in *CreateResponse, opts ...grpc_go.CallOption) (*BindUserV2Request, common.ErrorWithAttachment) {
	out := new(BindUserV2Request)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/UserInfoV2", in, out)
}

func (c *positionClient) UserInfosV2(ctx context.Context, in *UserInfosV2Request, opts ...grpc_go.CallOption) (*UserInfosV2Response, common.ErrorWithAttachment) {
	out := new(UserInfosV2Response)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/UserInfosV2", in, out)
}

func (c *positionClient) DetailV2(ctx context.Context, in *DetailRequest, opts ...grpc_go.CallOption) (*DetailResponse, common.ErrorWithAttachment) {
	out := new(DetailResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/DetailV2", in, out)
}

func (c *positionClient) DepartmentIdsByUrlAndUserId(ctx context.Context, in *DepartmentIdsByUrlAndUserIdRequest, opts ...grpc_go.CallOption) (*DepartmentIdsByUrlAndUserIdResponse, common.ErrorWithAttachment) {
	out := new(DepartmentIdsByUrlAndUserIdResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/DepartmentIdsByUrlAndUserId", in, out)
}

func (c *positionClient) PositionLogList(ctx context.Context, in *LogListRequest, opts ...grpc_go.CallOption) (*LogListResponse, common.ErrorWithAttachment) {
	out := new(LogListResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/PositionLogList", in, out)
}

func (c *positionClient) CreatePositionLog(ctx context.Context, in *LogRequest, opts ...grpc_go.CallOption) (*CreateResponse, common.ErrorWithAttachment) {
	out := new(CreateResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/CreatePositionLog", in, out)
}

// PositionServer is the server API for Position service.
// All implementations must embed UnimplementedPositionServer
// for forward compatibility
type PositionServer interface {
	Create(context.Context, *CreateRequest) (*CreateResponse, error)
	BatchAddPositionRules(context.Context, *BatchAddRulesRequest) (*CommonResponse, error)
	ListByDid(context.Context, *ListByDidRequest) (*ListByDidResponse, error)
	Remove(context.Context, *RemoveRequest) (*RemoveResponse, error)
	BatchRemove(context.Context, *BatchRemoveRequest) (*RemoveResponse, error)
	Detail(context.Context, *DetailRequest) (*CreateRequest, error)
	Update(context.Context, *CreateRequest) (*CreateResponse, error)
	List(context.Context, *ListRequest) (*ListResponse, error)
	RemoveUser(context.Context, *RemoveUserRequest) (*RemoveUserResponse, error)
	BindUser(context.Context, *BindUserRequest) (*BindUserResponse, error)
	BindUserForName(context.Context, *BindUserRequest) (*BindUserResponse, error)
	AndBindUser(context.Context, *AddUserRequest) (*BindUserResponse, error)
	FindOneFromName(context.Context, *FindOneFromNameRequest) (*CreateRequest, error)
	PositionByUrl(context.Context, *PositionByUrlRequest) (*PositionByUrlResponse, error)
	PositionByUrls(context.Context, *PositionByUrlsRequest) (*PositionByUrlResponse, error)
	DoIHavaAuth(context.Context, *DoIHavaAuthRequest) (*DoIHavaAuthResponse, error)
	DoHavaAuthBatch(context.Context, *DoHavaAuthBatchRequest) (*DoHavaAuthBatchResponse, error)
	SearchDepartment(context.Context, *SearchDepartmentRequest) (*SearchDepartmentResponse, error)
	FindUsersByRule(context.Context, *FindUsersByRuleRequest) (*PositionUsersResponse, error)
	FindRuleByUserId(context.Context, *FindRuleByUserIdRequest) (*FindRuleByUserIdResponse, error)
	BindUserV2(context.Context, *BindUserV2Request) (*CommonResponse, error)
	BindUserForNameV2(context.Context, *BindUserV2Request) (*CommonResponse, error)
	AndBindUserV2(context.Context, *BindUserV2Request) (*CommonResponse, error)
	UserInfoV2(context.Context, *CreateResponse) (*BindUserV2Request, error)
	UserInfosV2(context.Context, *UserInfosV2Request) (*UserInfosV2Response, error)
	DetailV2(context.Context, *DetailRequest) (*DetailResponse, error)
	DepartmentIdsByUrlAndUserId(context.Context, *DepartmentIdsByUrlAndUserIdRequest) (*DepartmentIdsByUrlAndUserIdResponse, error)
	PositionLogList(context.Context, *LogListRequest) (*LogListResponse, error)
	CreatePositionLog(context.Context, *LogRequest) (*CreateResponse, error)
	mustEmbedUnimplementedPositionServer()
}

// UnimplementedPositionServer must be embedded to have forward compatible implementations.
type UnimplementedPositionServer struct {
	proxyImpl protocol.Invoker
}

func (UnimplementedPositionServer) Create(context.Context, *CreateRequest) (*CreateResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Create not implemented")
}
func (UnimplementedPositionServer) BatchAddPositionRules(context.Context, *BatchAddRulesRequest) (*CommonResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method BatchAddPositionRules not implemented")
}
func (UnimplementedPositionServer) ListByDid(context.Context, *ListByDidRequest) (*ListByDidResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListByDid not implemented")
}
func (UnimplementedPositionServer) Remove(context.Context, *RemoveRequest) (*RemoveResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Remove not implemented")
}
func (UnimplementedPositionServer) BatchRemove(context.Context, *BatchRemoveRequest) (*RemoveResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method BatchRemove not implemented")
}
func (UnimplementedPositionServer) Detail(context.Context, *DetailRequest) (*CreateRequest, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Detail not implemented")
}
func (UnimplementedPositionServer) Update(context.Context, *CreateRequest) (*CreateResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Update not implemented")
}
func (UnimplementedPositionServer) List(context.Context, *ListRequest) (*ListResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method List not implemented")
}
func (UnimplementedPositionServer) RemoveUser(context.Context, *RemoveUserRequest) (*RemoveUserResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RemoveUser not implemented")
}
func (UnimplementedPositionServer) BindUser(context.Context, *BindUserRequest) (*BindUserResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method BindUser not implemented")
}
func (UnimplementedPositionServer) BindUserForName(context.Context, *BindUserRequest) (*BindUserResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method BindUserForName not implemented")
}
func (UnimplementedPositionServer) AndBindUser(context.Context, *AddUserRequest) (*BindUserResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AndBindUser not implemented")
}
func (UnimplementedPositionServer) FindOneFromName(context.Context, *FindOneFromNameRequest) (*CreateRequest, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FindOneFromName not implemented")
}
func (UnimplementedPositionServer) PositionByUrl(context.Context, *PositionByUrlRequest) (*PositionByUrlResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PositionByUrl not implemented")
}
func (UnimplementedPositionServer) PositionByUrls(context.Context, *PositionByUrlsRequest) (*PositionByUrlResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PositionByUrls not implemented")
}
func (UnimplementedPositionServer) DoIHavaAuth(context.Context, *DoIHavaAuthRequest) (*DoIHavaAuthResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DoIHavaAuth not implemented")
}
func (UnimplementedPositionServer) DoHavaAuthBatch(context.Context, *DoHavaAuthBatchRequest) (*DoHavaAuthBatchResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DoHavaAuthBatch not implemented")
}
func (UnimplementedPositionServer) SearchDepartment(context.Context, *SearchDepartmentRequest) (*SearchDepartmentResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SearchDepartment not implemented")
}
func (UnimplementedPositionServer) FindUsersByRule(context.Context, *FindUsersByRuleRequest) (*PositionUsersResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FindUsersByRule not implemented")
}
func (UnimplementedPositionServer) FindRuleByUserId(context.Context, *FindRuleByUserIdRequest) (*FindRuleByUserIdResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FindRuleByUserId not implemented")
}
func (UnimplementedPositionServer) BindUserV2(context.Context, *BindUserV2Request) (*CommonResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method BindUserV2 not implemented")
}
func (UnimplementedPositionServer) BindUserForNameV2(context.Context, *BindUserV2Request) (*CommonResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method BindUserForNameV2 not implemented")
}
func (UnimplementedPositionServer) AndBindUserV2(context.Context, *BindUserV2Request) (*CommonResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AndBindUserV2 not implemented")
}
func (UnimplementedPositionServer) UserInfoV2(context.Context, *CreateResponse) (*BindUserV2Request, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UserInfoV2 not implemented")
}
func (UnimplementedPositionServer) UserInfosV2(context.Context, *UserInfosV2Request) (*UserInfosV2Response, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UserInfosV2 not implemented")
}
func (UnimplementedPositionServer) DetailV2(context.Context, *DetailRequest) (*DetailResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DetailV2 not implemented")
}
func (UnimplementedPositionServer) DepartmentIdsByUrlAndUserId(context.Context, *DepartmentIdsByUrlAndUserIdRequest) (*DepartmentIdsByUrlAndUserIdResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DepartmentIdsByUrlAndUserId not implemented")
}
func (UnimplementedPositionServer) PositionLogList(context.Context, *LogListRequest) (*LogListResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PositionLogList not implemented")
}
func (UnimplementedPositionServer) CreatePositionLog(context.Context, *LogRequest) (*CreateResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreatePositionLog not implemented")
}
func (s *UnimplementedPositionServer) XXX_SetProxyImpl(impl protocol.Invoker) {
	s.proxyImpl = impl
}

func (s *UnimplementedPositionServer) XXX_GetProxyImpl() protocol.Invoker {
	return s.proxyImpl
}

func (s *UnimplementedPositionServer) XXX_ServiceDesc() *grpc_go.ServiceDesc {
	return &Position_ServiceDesc
}
func (s *UnimplementedPositionServer) XXX_InterfaceName() string {
	return "position.Position"
}

func (UnimplementedPositionServer) mustEmbedUnimplementedPositionServer() {}

// UnsafePositionServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to PositionServer will
// result in compilation errors.
type UnsafePositionServer interface {
	mustEmbedUnimplementedPositionServer()
}

func RegisterPositionServer(s grpc_go.ServiceRegistrar, srv PositionServer) {
	s.RegisterService(&Position_ServiceDesc, srv)
}

func _Position_Create_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("Create", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Position_BatchAddPositionRules_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchAddRulesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("BatchAddPositionRules", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Position_ListByDid_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListByDidRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("ListByDid", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Position_Remove_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(RemoveRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("Remove", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Position_BatchRemove_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchRemoveRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("BatchRemove", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Position_Detail_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(DetailRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("Detail", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Position_Update_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("Update", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Position_List_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("List", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Position_RemoveUser_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(RemoveUserRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("RemoveUser", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Position_BindUser_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(BindUserRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("BindUser", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Position_BindUserForName_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(BindUserRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("BindUserForName", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Position_AndBindUser_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddUserRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("AndBindUser", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Position_FindOneFromName_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(FindOneFromNameRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("FindOneFromName", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Position_PositionByUrl_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(PositionByUrlRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("PositionByUrl", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Position_PositionByUrls_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(PositionByUrlsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("PositionByUrls", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Position_DoIHavaAuth_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(DoIHavaAuthRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("DoIHavaAuth", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Position_DoHavaAuthBatch_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(DoHavaAuthBatchRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("DoHavaAuthBatch", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Position_SearchDepartment_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(SearchDepartmentRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("SearchDepartment", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Position_FindUsersByRule_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(FindUsersByRuleRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("FindUsersByRule", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Position_FindRuleByUserId_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(FindRuleByUserIdRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("FindRuleByUserId", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Position_BindUserV2_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(BindUserV2Request)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("BindUserV2", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Position_BindUserForNameV2_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(BindUserV2Request)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("BindUserForNameV2", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Position_AndBindUserV2_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(BindUserV2Request)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("AndBindUserV2", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Position_UserInfoV2_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateResponse)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("UserInfoV2", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Position_UserInfosV2_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(UserInfosV2Request)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("UserInfosV2", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Position_DetailV2_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(DetailRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("DetailV2", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Position_DepartmentIdsByUrlAndUserId_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(DepartmentIdsByUrlAndUserIdRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("DepartmentIdsByUrlAndUserId", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Position_PositionLogList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(LogListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("PositionLogList", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Position_CreatePositionLog_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(LogRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("CreatePositionLog", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

// Position_ServiceDesc is the grpc_go.ServiceDesc for Position service.
// It's only intended for direct use with grpc_go.RegisterService,
// and not to be introspected or modified (even as a copy)
var Position_ServiceDesc = grpc_go.ServiceDesc{
	ServiceName: "position.Position",
	HandlerType: (*PositionServer)(nil),
	Methods: []grpc_go.MethodDesc{
		{
			MethodName: "Create",
			Handler:    _Position_Create_Handler,
		},
		{
			MethodName: "BatchAddPositionRules",
			Handler:    _Position_BatchAddPositionRules_Handler,
		},
		{
			MethodName: "ListByDid",
			Handler:    _Position_ListByDid_Handler,
		},
		{
			MethodName: "Remove",
			Handler:    _Position_Remove_Handler,
		},
		{
			MethodName: "BatchRemove",
			Handler:    _Position_BatchRemove_Handler,
		},
		{
			MethodName: "Detail",
			Handler:    _Position_Detail_Handler,
		},
		{
			MethodName: "Update",
			Handler:    _Position_Update_Handler,
		},
		{
			MethodName: "List",
			Handler:    _Position_List_Handler,
		},
		{
			MethodName: "RemoveUser",
			Handler:    _Position_RemoveUser_Handler,
		},
		{
			MethodName: "BindUser",
			Handler:    _Position_BindUser_Handler,
		},
		{
			MethodName: "BindUserForName",
			Handler:    _Position_BindUserForName_Handler,
		},
		{
			MethodName: "AndBindUser",
			Handler:    _Position_AndBindUser_Handler,
		},
		{
			MethodName: "FindOneFromName",
			Handler:    _Position_FindOneFromName_Handler,
		},
		{
			MethodName: "PositionByUrl",
			Handler:    _Position_PositionByUrl_Handler,
		},
		{
			MethodName: "PositionByUrls",
			Handler:    _Position_PositionByUrls_Handler,
		},
		{
			MethodName: "DoIHavaAuth",
			Handler:    _Position_DoIHavaAuth_Handler,
		},
		{
			MethodName: "DoHavaAuthBatch",
			Handler:    _Position_DoHavaAuthBatch_Handler,
		},
		{
			MethodName: "SearchDepartment",
			Handler:    _Position_SearchDepartment_Handler,
		},
		{
			MethodName: "FindUsersByRule",
			Handler:    _Position_FindUsersByRule_Handler,
		},
		{
			MethodName: "FindRuleByUserId",
			Handler:    _Position_FindRuleByUserId_Handler,
		},
		{
			MethodName: "BindUserV2",
			Handler:    _Position_BindUserV2_Handler,
		},
		{
			MethodName: "BindUserForNameV2",
			Handler:    _Position_BindUserForNameV2_Handler,
		},
		{
			MethodName: "AndBindUserV2",
			Handler:    _Position_AndBindUserV2_Handler,
		},
		{
			MethodName: "UserInfoV2",
			Handler:    _Position_UserInfoV2_Handler,
		},
		{
			MethodName: "UserInfosV2",
			Handler:    _Position_UserInfosV2_Handler,
		},
		{
			MethodName: "DetailV2",
			Handler:    _Position_DetailV2_Handler,
		},
		{
			MethodName: "DepartmentIdsByUrlAndUserId",
			Handler:    _Position_DepartmentIdsByUrlAndUserId_Handler,
		},
		{
			MethodName: "PositionLogList",
			Handler:    _Position_PositionLogList_Handler,
		},
		{
			MethodName: "CreatePositionLog",
			Handler:    _Position_CreatePositionLog_Handler,
		},
	},
	Streams:  []grpc_go.StreamDesc{},
	Metadata: "api/position/position.proto",
}
