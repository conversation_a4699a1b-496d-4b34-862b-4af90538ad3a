// Code generated by protoc-gen-gogo. DO NOT EDIT.
// source: api/position/position.proto

package position

import (
	fmt "fmt"
	math "math"
	proto "github.com/golang/protobuf/proto"
	_ "github.com/mwitkow/go-proto-validators"
	github_com_mwitkow_go_proto_validators "github.com/mwitkow/go-proto-validators"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

func (this *DepartmentIdsByUrlAndUserIdRequest) Validate() error {
	return nil
}
func (this *DepartmentIdsByUrlAndUserIdResponse) Validate() error {
	return nil
}
func (this *UserInfosV2Request) Validate() error {
	return nil
}
func (this *UserInfosV2Response) Validate() error {
	for _, item := range this.UserInfo {
		if item != nil {
			if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(item); err != nil {
				return github_com_mwitkow_go_proto_validators.FieldError("UserInfo", err)
			}
		}
	}
	return nil
}
func (this *BatchRemoveRequest) Validate() error {
	return nil
}
func (this *FindRuleByUserIdRequest) Validate() error {
	return nil
}
func (this *FindRuleByUserIdResponse) Validate() error {
	for _, item := range this.Rules {
		if item != nil {
			if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(item); err != nil {
				return github_com_mwitkow_go_proto_validators.FieldError("Rules", err)
			}
		}
	}
	return nil
}
func (this *FindUsersByRuleRequest) Validate() error {
	return nil
}
func (this *PositionUsersResponse) Validate() error {
	for _, item := range this.PositionUsers {
		if item != nil {
			if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(item); err != nil {
				return github_com_mwitkow_go_proto_validators.FieldError("PositionUsers", err)
			}
		}
	}
	return nil
}
func (this *PositionByUrlResponse) Validate() error {
	for _, item := range this.List {
		if item != nil {
			if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(item); err != nil {
				return github_com_mwitkow_go_proto_validators.FieldError("List", err)
			}
		}
	}
	return nil
}
func (this *PositionByUrlRequest) Validate() error {
	return nil
}
func (this *PositionByUrlsRequest) Validate() error {
	return nil
}
func (this *SearchDepartmentRequest) Validate() error {
	for _, item := range this.Urls {
		if item == "" {
			return github_com_mwitkow_go_proto_validators.FieldError("Urls", fmt.Errorf(`70014`))
		}
	}
	return nil
}
func (this *PDInfo) Validate() error {
	return nil
}
func (this *SearchDepartmentResponse) Validate() error {
	for _, item := range this.List {
		if item != nil {
			if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(item); err != nil {
				return github_com_mwitkow_go_proto_validators.FieldError("List", err)
			}
		}
	}
	return nil
}
func (this *DoIHavaAuthRequest) Validate() error {
	if this.Url == "" {
		return github_com_mwitkow_go_proto_validators.FieldError("Url", fmt.Errorf(`70014`))
	}
	return nil
}
func (this *DoHavaAuthBatchRequest) Validate() error {
	if this.Url == "" {
		return github_com_mwitkow_go_proto_validators.FieldError("Url", fmt.Errorf(`70014`))
	}
	return nil
}
func (this *DoIHavaAuthResponse) Validate() error {
	return nil
}
func (this *DoHavaAuthBatchResponse) Validate() error {
	return nil
}
func (this *FindOneFromNameRequest) Validate() error {
	return nil
}
func (this *ListByDidRequest) Validate() error {
	return nil
}
func (this *BasePosition) Validate() error {
	return nil
}
func (this *ListByDidResponse) Validate() error {
	for _, item := range this.Positions {
		if item != nil {
			if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(item); err != nil {
				return github_com_mwitkow_go_proto_validators.FieldError("Positions", err)
			}
		}
	}
	return nil
}
func (this *BindUserV2Request) Validate() error {
	if this.UserInfo != nil {
		if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(this.UserInfo); err != nil {
			return github_com_mwitkow_go_proto_validators.FieldError("UserInfo", err)
		}
	}
	for _, item := range this.DepPositions {
		if item != nil {
			if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(item); err != nil {
				return github_com_mwitkow_go_proto_validators.FieldError("DepPositions", err)
			}
		}
	}
	return nil
}
func (this *DepPosition) Validate() error {
	for _, item := range this.Positions {
		if item != nil {
			if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(item); err != nil {
				return github_com_mwitkow_go_proto_validators.FieldError("Positions", err)
			}
		}
	}
	return nil
}
func (this *PositionV2) Validate() error {
	return nil
}
func (this *UserInfo) Validate() error {
	return nil
}
func (this *BindUserRequest) Validate() error {
	for _, item := range this.PositionUsers {
		if item != nil {
			if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(item); err != nil {
				return github_com_mwitkow_go_proto_validators.FieldError("PositionUsers", err)
			}
		}
	}
	return nil
}
func (this *AddUserRequest) Validate() error {
	if this.PositionUser != nil {
		if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(this.PositionUser); err != nil {
			return github_com_mwitkow_go_proto_validators.FieldError("PositionUser", err)
		}
	}
	return nil
}
func (this *BindUserResponse) Validate() error {
	return nil
}
func (this *CommonResponse) Validate() error {
	return nil
}
func (this *PositionUser) Validate() error {
	return nil
}
func (this *RemoveUserRequest) Validate() error {
	if this.Domain == "" {
		return github_com_mwitkow_go_proto_validators.FieldError("Domain", fmt.Errorf(`70001`))
	}
	return nil
}
func (this *RemoveUserResponse) Validate() error {
	return nil
}
func (this *CreateResponse) Validate() error {
	return nil
}
func (this *RemoveRequest) Validate() error {
	if this.Domain == "" {
		return github_com_mwitkow_go_proto_validators.FieldError("Domain", fmt.Errorf(`70001`))
	}
	return nil
}
func (this *RemoveResponse) Validate() error {
	return nil
}
func (this *DetailRequest) Validate() error {
	if this.Domain == "" {
		return github_com_mwitkow_go_proto_validators.FieldError("Domain", fmt.Errorf(`70001`))
	}
	return nil
}
func (this *DetailResponse) Validate() error {
	for _, item := range this.Rules {
		if item != nil {
			if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(item); err != nil {
				return github_com_mwitkow_go_proto_validators.FieldError("Rules", err)
			}
		}
	}
	for _, item := range this.PositionTreeRule {
		if item != nil {
			if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(item); err != nil {
				return github_com_mwitkow_go_proto_validators.FieldError("PositionTreeRule", err)
			}
		}
	}
	return nil
}
func (this *CreateRequest) Validate() error {
	for _, item := range this.Rules {
		if item != nil {
			if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(item); err != nil {
				return github_com_mwitkow_go_proto_validators.FieldError("Rules", err)
			}
		}
	}
	for _, item := range this.PositionTreeRule {
		if item != nil {
			if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(item); err != nil {
				return github_com_mwitkow_go_proto_validators.FieldError("PositionTreeRule", err)
			}
		}
	}
	return nil
}
func (this *BatchAddRulesRequest) Validate() error {
	if len(this.Ids) < 1 {
		return github_com_mwitkow_go_proto_validators.FieldError("Ids", fmt.Errorf(`请选择岗位，岗位数不可超过50`))
	}
	if len(this.Ids) > 50 {
		return github_com_mwitkow_go_proto_validators.FieldError("Ids", fmt.Errorf(`请选择岗位，岗位数不可超过50`))
	}
	for _, item := range this.Ids {
		if !(item > 0) {
			return github_com_mwitkow_go_proto_validators.FieldError("Ids", fmt.Errorf(`请选择岗位，岗位数不可超过50`))
		}
	}
	if len(this.RuleIds) < 1 {
		return github_com_mwitkow_go_proto_validators.FieldError("RuleIds", fmt.Errorf(`选中的权限不符合规范(数量在1-50之间),并且元素不能有空值`))
	}
	if len(this.RuleIds) > 50 {
		return github_com_mwitkow_go_proto_validators.FieldError("RuleIds", fmt.Errorf(`选中的权限不符合规范(数量在1-50之间),并且元素不能有空值`))
	}
	for _, item := range this.RuleIds {
		if !(item > 0) {
			return github_com_mwitkow_go_proto_validators.FieldError("RuleIds", fmt.Errorf(`选中的权限不符合规范(数量在1-50之间),并且元素不能有空值`))
		}
	}
	return nil
}
func (this *Leader) Validate() error {
	return nil
}
func (this *ListRequest) Validate() error {
	if this.Domain == "" {
		return github_com_mwitkow_go_proto_validators.FieldError("Domain", fmt.Errorf(`70001`))
	}
	return nil
}
func (this *Rule) Validate() error {
	return nil
}
func (this *RuleInfo) Validate() error {
	for _, item := range this.Son {
		if item != nil {
			if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(item); err != nil {
				return github_com_mwitkow_go_proto_validators.FieldError("Son", err)
			}
		}
	}
	for _, item := range this.RuleData {
		if item != nil {
			if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(item); err != nil {
				return github_com_mwitkow_go_proto_validators.FieldError("RuleData", err)
			}
		}
	}
	for _, item := range this.PositionRuleFields {
		if item != nil {
			if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(item); err != nil {
				return github_com_mwitkow_go_proto_validators.FieldError("PositionRuleFields", err)
			}
		}
	}
	for _, item := range this.InterfaceList {
		if item != nil {
			if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(item); err != nil {
				return github_com_mwitkow_go_proto_validators.FieldError("InterfaceList", err)
			}
		}
	}
	for _, item := range this.ButtonList {
		if item != nil {
			if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(item); err != nil {
				return github_com_mwitkow_go_proto_validators.FieldError("ButtonList", err)
			}
		}
	}
	return nil
}
func (this *PositionRuleField) Validate() error {
	return nil
}
func (this *RuleData) Validate() error {
	return nil
}
func (this *ListResponse) Validate() error {
	for _, item := range this.Data {
		if item != nil {
			if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(item); err != nil {
				return github_com_mwitkow_go_proto_validators.FieldError("Data", err)
			}
		}
	}
	return nil
}
func (this *LogListRequest) Validate() error {
	return nil
}
func (this *LogListResponse) Validate() error {
	for _, item := range this.List {
		if item != nil {
			if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(item); err != nil {
				return github_com_mwitkow_go_proto_validators.FieldError("List", err)
			}
		}
	}
	return nil
}
func (this *LogRequest) Validate() error {
	return nil
}
func (this *LogResponse) Validate() error {
	return nil
}
