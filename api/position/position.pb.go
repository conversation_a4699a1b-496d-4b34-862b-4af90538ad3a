//
// Licensed to the Apache Software Foundation (ASF) under one or more
// contributor license agreements.  See the NOTICE file distributed with
// this work for additional information regarding copyright ownership.
// The ASF licenses this file to You under the Apache License, Version 2.0
// (the "License"); you may not use this file except in compliance with
// the License.  You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.29.1
// 	protoc        v3.21.12
// source: api/position/position.proto

package position

import (
	_ "github.com/mwitkow/go-proto-validators"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type DepartmentIdsByUrlAndUserIdRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UserId uint32 `protobuf:"varint,1,opt,name=userId,proto3" json:"userId,omitempty"`
	Domain string `protobuf:"bytes,2,opt,name=domain,proto3" json:"domain,omitempty"`
	Url    string `protobuf:"bytes,3,opt,name=url,proto3" json:"url,omitempty"`
}

func (x *DepartmentIdsByUrlAndUserIdRequest) Reset() {
	*x = DepartmentIdsByUrlAndUserIdRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_position_position_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DepartmentIdsByUrlAndUserIdRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DepartmentIdsByUrlAndUserIdRequest) ProtoMessage() {}

func (x *DepartmentIdsByUrlAndUserIdRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_position_position_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DepartmentIdsByUrlAndUserIdRequest.ProtoReflect.Descriptor instead.
func (*DepartmentIdsByUrlAndUserIdRequest) Descriptor() ([]byte, []int) {
	return file_api_position_position_proto_rawDescGZIP(), []int{0}
}

func (x *DepartmentIdsByUrlAndUserIdRequest) GetUserId() uint32 {
	if x != nil {
		return x.UserId
	}
	return 0
}

func (x *DepartmentIdsByUrlAndUserIdRequest) GetDomain() string {
	if x != nil {
		return x.Domain
	}
	return ""
}

func (x *DepartmentIdsByUrlAndUserIdRequest) GetUrl() string {
	if x != nil {
		return x.Url
	}
	return ""
}

type DepartmentIdsByUrlAndUserIdResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	DepartmentIds []uint32 `protobuf:"varint,1,rep,packed,name=departmentIds,proto3" json:"departmentIds,omitempty"`
}

func (x *DepartmentIdsByUrlAndUserIdResponse) Reset() {
	*x = DepartmentIdsByUrlAndUserIdResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_position_position_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DepartmentIdsByUrlAndUserIdResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DepartmentIdsByUrlAndUserIdResponse) ProtoMessage() {}

func (x *DepartmentIdsByUrlAndUserIdResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_position_position_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DepartmentIdsByUrlAndUserIdResponse.ProtoReflect.Descriptor instead.
func (*DepartmentIdsByUrlAndUserIdResponse) Descriptor() ([]byte, []int) {
	return file_api_position_position_proto_rawDescGZIP(), []int{1}
}

func (x *DepartmentIdsByUrlAndUserIdResponse) GetDepartmentIds() []uint32 {
	if x != nil {
		return x.DepartmentIds
	}
	return nil
}

type UserInfosV2Request struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	IDs []uint32 `protobuf:"varint,2,rep,packed,name=IDs,proto3" json:"IDs,omitempty"`
}

func (x *UserInfosV2Request) Reset() {
	*x = UserInfosV2Request{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_position_position_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UserInfosV2Request) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserInfosV2Request) ProtoMessage() {}

func (x *UserInfosV2Request) ProtoReflect() protoreflect.Message {
	mi := &file_api_position_position_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserInfosV2Request.ProtoReflect.Descriptor instead.
func (*UserInfosV2Request) Descriptor() ([]byte, []int) {
	return file_api_position_position_proto_rawDescGZIP(), []int{2}
}

func (x *UserInfosV2Request) GetIDs() []uint32 {
	if x != nil {
		return x.IDs
	}
	return nil
}

type UserInfosV2Response struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UserInfo []*BindUserV2Request `protobuf:"bytes,1,rep,name=userInfo,proto3" json:"userInfo,omitempty"`
}

func (x *UserInfosV2Response) Reset() {
	*x = UserInfosV2Response{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_position_position_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UserInfosV2Response) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserInfosV2Response) ProtoMessage() {}

func (x *UserInfosV2Response) ProtoReflect() protoreflect.Message {
	mi := &file_api_position_position_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserInfosV2Response.ProtoReflect.Descriptor instead.
func (*UserInfosV2Response) Descriptor() ([]byte, []int) {
	return file_api_position_position_proto_rawDescGZIP(), []int{3}
}

func (x *UserInfosV2Response) GetUserInfo() []*BindUserV2Request {
	if x != nil {
		return x.UserInfo
	}
	return nil
}

type BatchRemoveRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	IDs []uint32 `protobuf:"varint,2,rep,packed,name=IDs,proto3" json:"IDs,omitempty"`
}

func (x *BatchRemoveRequest) Reset() {
	*x = BatchRemoveRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_position_position_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BatchRemoveRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BatchRemoveRequest) ProtoMessage() {}

func (x *BatchRemoveRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_position_position_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BatchRemoveRequest.ProtoReflect.Descriptor instead.
func (*BatchRemoveRequest) Descriptor() ([]byte, []int) {
	return file_api_position_position_proto_rawDescGZIP(), []int{4}
}

func (x *BatchRemoveRequest) GetIDs() []uint32 {
	if x != nil {
		return x.IDs
	}
	return nil
}

type FindRuleByUserIdRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Domain string   `protobuf:"bytes,1,opt,name=domain,proto3" json:"domain,omitempty"`
	Urls   []string `protobuf:"bytes,2,rep,name=urls,proto3" json:"urls,omitempty"`
	UserId uint64   `protobuf:"varint,3,opt,name=userId,proto3" json:"userId,omitempty"`
}

func (x *FindRuleByUserIdRequest) Reset() {
	*x = FindRuleByUserIdRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_position_position_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FindRuleByUserIdRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FindRuleByUserIdRequest) ProtoMessage() {}

func (x *FindRuleByUserIdRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_position_position_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FindRuleByUserIdRequest.ProtoReflect.Descriptor instead.
func (*FindRuleByUserIdRequest) Descriptor() ([]byte, []int) {
	return file_api_position_position_proto_rawDescGZIP(), []int{5}
}

func (x *FindRuleByUserIdRequest) GetDomain() string {
	if x != nil {
		return x.Domain
	}
	return ""
}

func (x *FindRuleByUserIdRequest) GetUrls() []string {
	if x != nil {
		return x.Urls
	}
	return nil
}

func (x *FindRuleByUserIdRequest) GetUserId() uint64 {
	if x != nil {
		return x.UserId
	}
	return 0
}

type FindRuleByUserIdResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Rules []*RuleInfo `protobuf:"bytes,1,rep,name=rules,proto3" json:"rules,omitempty"`
}

func (x *FindRuleByUserIdResponse) Reset() {
	*x = FindRuleByUserIdResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_position_position_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FindRuleByUserIdResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FindRuleByUserIdResponse) ProtoMessage() {}

func (x *FindRuleByUserIdResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_position_position_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FindRuleByUserIdResponse.ProtoReflect.Descriptor instead.
func (*FindRuleByUserIdResponse) Descriptor() ([]byte, []int) {
	return file_api_position_position_proto_rawDescGZIP(), []int{6}
}

func (x *FindRuleByUserIdResponse) GetRules() []*RuleInfo {
	if x != nil {
		return x.Rules
	}
	return nil
}

type FindUsersByRuleRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Domain       string `protobuf:"bytes,1,opt,name=Domain,json=domain,proto3" json:"Domain,omitempty"`
	Url          string `protobuf:"bytes,2,opt,name=Url,json=url,proto3" json:"Url,omitempty"` //权限的唯一值
	DepartmentId uint64 `protobuf:"varint,3,opt,name=DepartmentId,json=departmentId,proto3" json:"DepartmentId,omitempty"`
}

func (x *FindUsersByRuleRequest) Reset() {
	*x = FindUsersByRuleRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_position_position_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FindUsersByRuleRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FindUsersByRuleRequest) ProtoMessage() {}

func (x *FindUsersByRuleRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_position_position_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FindUsersByRuleRequest.ProtoReflect.Descriptor instead.
func (*FindUsersByRuleRequest) Descriptor() ([]byte, []int) {
	return file_api_position_position_proto_rawDescGZIP(), []int{7}
}

func (x *FindUsersByRuleRequest) GetDomain() string {
	if x != nil {
		return x.Domain
	}
	return ""
}

func (x *FindUsersByRuleRequest) GetUrl() string {
	if x != nil {
		return x.Url
	}
	return ""
}

func (x *FindUsersByRuleRequest) GetDepartmentId() uint64 {
	if x != nil {
		return x.DepartmentId
	}
	return 0
}

type PositionUsersResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PositionUsers []*PositionUser `protobuf:"bytes,1,rep,name=positionUsers,proto3" json:"positionUsers,omitempty"`
}

func (x *PositionUsersResponse) Reset() {
	*x = PositionUsersResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_position_position_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PositionUsersResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PositionUsersResponse) ProtoMessage() {}

func (x *PositionUsersResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_position_position_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PositionUsersResponse.ProtoReflect.Descriptor instead.
func (*PositionUsersResponse) Descriptor() ([]byte, []int) {
	return file_api_position_position_proto_rawDescGZIP(), []int{8}
}

func (x *PositionUsersResponse) GetPositionUsers() []*PositionUser {
	if x != nil {
		return x.PositionUsers
	}
	return nil
}

type PositionByUrlResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	List []*BasePosition `protobuf:"bytes,1,rep,name=List,json=domain,proto3" json:"List,omitempty"`
}

func (x *PositionByUrlResponse) Reset() {
	*x = PositionByUrlResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_position_position_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PositionByUrlResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PositionByUrlResponse) ProtoMessage() {}

func (x *PositionByUrlResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_position_position_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PositionByUrlResponse.ProtoReflect.Descriptor instead.
func (*PositionByUrlResponse) Descriptor() ([]byte, []int) {
	return file_api_position_position_proto_rawDescGZIP(), []int{9}
}

func (x *PositionByUrlResponse) GetList() []*BasePosition {
	if x != nil {
		return x.List
	}
	return nil
}

type PositionByUrlRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Domain       string `protobuf:"bytes,1,opt,name=Domain,json=domain,proto3" json:"Domain,omitempty"`
	DepartmentId uint64 `protobuf:"varint,2,opt,name=DepartmentId,json=departmentId,proto3" json:"DepartmentId,omitempty"`
	Url          string `protobuf:"bytes,3,opt,name=Url,json=url,proto3" json:"Url,omitempty"`
}

func (x *PositionByUrlRequest) Reset() {
	*x = PositionByUrlRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_position_position_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PositionByUrlRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PositionByUrlRequest) ProtoMessage() {}

func (x *PositionByUrlRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_position_position_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PositionByUrlRequest.ProtoReflect.Descriptor instead.
func (*PositionByUrlRequest) Descriptor() ([]byte, []int) {
	return file_api_position_position_proto_rawDescGZIP(), []int{10}
}

func (x *PositionByUrlRequest) GetDomain() string {
	if x != nil {
		return x.Domain
	}
	return ""
}

func (x *PositionByUrlRequest) GetDepartmentId() uint64 {
	if x != nil {
		return x.DepartmentId
	}
	return 0
}

func (x *PositionByUrlRequest) GetUrl() string {
	if x != nil {
		return x.Url
	}
	return ""
}

type PositionByUrlsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Domain        string   `protobuf:"bytes,1,opt,name=Domain,json=domain,proto3" json:"Domain,omitempty"`
	DepartmentIds []uint64 `protobuf:"varint,2,rep,packed,name=departmentIds,proto3" json:"departmentIds,omitempty"`
	Urls          []string `protobuf:"bytes,3,rep,name=urls,proto3" json:"urls,omitempty"`
}

func (x *PositionByUrlsRequest) Reset() {
	*x = PositionByUrlsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_position_position_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PositionByUrlsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PositionByUrlsRequest) ProtoMessage() {}

func (x *PositionByUrlsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_position_position_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PositionByUrlsRequest.ProtoReflect.Descriptor instead.
func (*PositionByUrlsRequest) Descriptor() ([]byte, []int) {
	return file_api_position_position_proto_rawDescGZIP(), []int{11}
}

func (x *PositionByUrlsRequest) GetDomain() string {
	if x != nil {
		return x.Domain
	}
	return ""
}

func (x *PositionByUrlsRequest) GetDepartmentIds() []uint64 {
	if x != nil {
		return x.DepartmentIds
	}
	return nil
}

func (x *PositionByUrlsRequest) GetUrls() []string {
	if x != nil {
		return x.Urls
	}
	return nil
}

type SearchDepartmentRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Domain string   `protobuf:"bytes,1,opt,name=Domain,json=domain,proto3" json:"Domain,omitempty"`
	UserId uint64   `protobuf:"varint,2,opt,name=UserId,json=userId,proto3" json:"UserId,omitempty"`
	Urls   []string `protobuf:"bytes,3,rep,name=Urls,json=url,proto3" json:"Urls,omitempty"`
}

func (x *SearchDepartmentRequest) Reset() {
	*x = SearchDepartmentRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_position_position_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SearchDepartmentRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SearchDepartmentRequest) ProtoMessage() {}

func (x *SearchDepartmentRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_position_position_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SearchDepartmentRequest.ProtoReflect.Descriptor instead.
func (*SearchDepartmentRequest) Descriptor() ([]byte, []int) {
	return file_api_position_position_proto_rawDescGZIP(), []int{12}
}

func (x *SearchDepartmentRequest) GetDomain() string {
	if x != nil {
		return x.Domain
	}
	return ""
}

func (x *SearchDepartmentRequest) GetUserId() uint64 {
	if x != nil {
		return x.UserId
	}
	return 0
}

func (x *SearchDepartmentRequest) GetUrls() []string {
	if x != nil {
		return x.Urls
	}
	return nil
}

type PDInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ID           uint64 `protobuf:"varint,1,opt,name=ID,proto3" json:"ID,omitempty"`
	Name         string `protobuf:"bytes,2,opt,name=Name,proto3" json:"Name,omitempty"`
	PositionID   uint64 `protobuf:"varint,3,opt,name=PositionID,proto3" json:"PositionID,omitempty"`
	PositionName string `protobuf:"bytes,4,opt,name=PositionName,proto3" json:"PositionName,omitempty"`
}

func (x *PDInfo) Reset() {
	*x = PDInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_position_position_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PDInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PDInfo) ProtoMessage() {}

func (x *PDInfo) ProtoReflect() protoreflect.Message {
	mi := &file_api_position_position_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PDInfo.ProtoReflect.Descriptor instead.
func (*PDInfo) Descriptor() ([]byte, []int) {
	return file_api_position_position_proto_rawDescGZIP(), []int{13}
}

func (x *PDInfo) GetID() uint64 {
	if x != nil {
		return x.ID
	}
	return 0
}

func (x *PDInfo) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *PDInfo) GetPositionID() uint64 {
	if x != nil {
		return x.PositionID
	}
	return 0
}

func (x *PDInfo) GetPositionName() string {
	if x != nil {
		return x.PositionName
	}
	return ""
}

type SearchDepartmentResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	List []*PDInfo `protobuf:"bytes,1,rep,name=List,json=list,proto3" json:"List,omitempty"`
}

func (x *SearchDepartmentResponse) Reset() {
	*x = SearchDepartmentResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_position_position_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SearchDepartmentResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SearchDepartmentResponse) ProtoMessage() {}

func (x *SearchDepartmentResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_position_position_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SearchDepartmentResponse.ProtoReflect.Descriptor instead.
func (*SearchDepartmentResponse) Descriptor() ([]byte, []int) {
	return file_api_position_position_proto_rawDescGZIP(), []int{14}
}

func (x *SearchDepartmentResponse) GetList() []*PDInfo {
	if x != nil {
		return x.List
	}
	return nil
}

type DoIHavaAuthRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Domain       string `protobuf:"bytes,1,opt,name=Domain,json=domain,proto3" json:"Domain,omitempty"`
	UserId       uint64 `protobuf:"varint,2,opt,name=UserId,json=userId,proto3" json:"UserId,omitempty"`
	Url          string `protobuf:"bytes,3,opt,name=Url,json=url,proto3" json:"Url,omitempty"`
	DepartmentID uint64 `protobuf:"varint,4,opt,name=DepartmentID,json=department,proto3" json:"DepartmentID,omitempty"`
	Type         string `protobuf:"bytes,5,opt,name=type,proto3" json:"type,omitempty"`
}

func (x *DoIHavaAuthRequest) Reset() {
	*x = DoIHavaAuthRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_position_position_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DoIHavaAuthRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DoIHavaAuthRequest) ProtoMessage() {}

func (x *DoIHavaAuthRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_position_position_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DoIHavaAuthRequest.ProtoReflect.Descriptor instead.
func (*DoIHavaAuthRequest) Descriptor() ([]byte, []int) {
	return file_api_position_position_proto_rawDescGZIP(), []int{15}
}

func (x *DoIHavaAuthRequest) GetDomain() string {
	if x != nil {
		return x.Domain
	}
	return ""
}

func (x *DoIHavaAuthRequest) GetUserId() uint64 {
	if x != nil {
		return x.UserId
	}
	return 0
}

func (x *DoIHavaAuthRequest) GetUrl() string {
	if x != nil {
		return x.Url
	}
	return ""
}

func (x *DoIHavaAuthRequest) GetDepartmentID() uint64 {
	if x != nil {
		return x.DepartmentID
	}
	return 0
}

func (x *DoIHavaAuthRequest) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

type DoHavaAuthBatchRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Domain       string   `protobuf:"bytes,1,opt,name=domain,proto3" json:"domain,omitempty"`
	UserIds      []uint32 `protobuf:"varint,2,rep,packed,name=userIds,proto3" json:"userIds,omitempty"`
	Url          string   `protobuf:"bytes,3,opt,name=url,proto3" json:"url,omitempty"`
	DepartmentID uint64   `protobuf:"varint,4,opt,name=departmentID,json=department,proto3" json:"departmentID,omitempty"`
}

func (x *DoHavaAuthBatchRequest) Reset() {
	*x = DoHavaAuthBatchRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_position_position_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DoHavaAuthBatchRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DoHavaAuthBatchRequest) ProtoMessage() {}

func (x *DoHavaAuthBatchRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_position_position_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DoHavaAuthBatchRequest.ProtoReflect.Descriptor instead.
func (*DoHavaAuthBatchRequest) Descriptor() ([]byte, []int) {
	return file_api_position_position_proto_rawDescGZIP(), []int{16}
}

func (x *DoHavaAuthBatchRequest) GetDomain() string {
	if x != nil {
		return x.Domain
	}
	return ""
}

func (x *DoHavaAuthBatchRequest) GetUserIds() []uint32 {
	if x != nil {
		return x.UserIds
	}
	return nil
}

func (x *DoHavaAuthBatchRequest) GetUrl() string {
	if x != nil {
		return x.Url
	}
	return ""
}

func (x *DoHavaAuthBatchRequest) GetDepartmentID() uint64 {
	if x != nil {
		return x.DepartmentID
	}
	return 0
}

type DoIHavaAuthResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Hava bool `protobuf:"varint,1,opt,name=Hava,proto3" json:"Hava,omitempty"`
}

func (x *DoIHavaAuthResponse) Reset() {
	*x = DoIHavaAuthResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_position_position_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DoIHavaAuthResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DoIHavaAuthResponse) ProtoMessage() {}

func (x *DoIHavaAuthResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_position_position_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DoIHavaAuthResponse.ProtoReflect.Descriptor instead.
func (*DoIHavaAuthResponse) Descriptor() ([]byte, []int) {
	return file_api_position_position_proto_rawDescGZIP(), []int{17}
}

func (x *DoIHavaAuthResponse) GetHava() bool {
	if x != nil {
		return x.Hava
	}
	return false
}

type DoHavaAuthBatchResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Hava       bool `protobuf:"varint,1,opt,name=hava,proto3" json:"hava,omitempty"`             //是否同时有次权限
	AllNotHave bool `protobuf:"varint,2,opt,name=allNotHave,proto3" json:"allNotHave,omitempty"` //是否全部没有此权限，查看是否部门人员有次权限
}

func (x *DoHavaAuthBatchResponse) Reset() {
	*x = DoHavaAuthBatchResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_position_position_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DoHavaAuthBatchResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DoHavaAuthBatchResponse) ProtoMessage() {}

func (x *DoHavaAuthBatchResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_position_position_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DoHavaAuthBatchResponse.ProtoReflect.Descriptor instead.
func (*DoHavaAuthBatchResponse) Descriptor() ([]byte, []int) {
	return file_api_position_position_proto_rawDescGZIP(), []int{18}
}

func (x *DoHavaAuthBatchResponse) GetHava() bool {
	if x != nil {
		return x.Hava
	}
	return false
}

func (x *DoHavaAuthBatchResponse) GetAllNotHave() bool {
	if x != nil {
		return x.AllNotHave
	}
	return false
}

type FindOneFromNameRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Domain       string `protobuf:"bytes,1,opt,name=Domain,json=domain,proto3" json:"Domain,omitempty"`
	DepartmentId uint64 `protobuf:"varint,2,opt,name=DepartmentId,json=departmentId,proto3" json:"DepartmentId,omitempty"`
	Name         string `protobuf:"bytes,3,opt,name=Name,json=name,proto3" json:"Name,omitempty"`
}

func (x *FindOneFromNameRequest) Reset() {
	*x = FindOneFromNameRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_position_position_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FindOneFromNameRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FindOneFromNameRequest) ProtoMessage() {}

func (x *FindOneFromNameRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_position_position_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FindOneFromNameRequest.ProtoReflect.Descriptor instead.
func (*FindOneFromNameRequest) Descriptor() ([]byte, []int) {
	return file_api_position_position_proto_rawDescGZIP(), []int{19}
}

func (x *FindOneFromNameRequest) GetDomain() string {
	if x != nil {
		return x.Domain
	}
	return ""
}

func (x *FindOneFromNameRequest) GetDepartmentId() uint64 {
	if x != nil {
		return x.DepartmentId
	}
	return 0
}

func (x *FindOneFromNameRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

type ListByDidRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Domain string `protobuf:"bytes,1,opt,name=Domain,json=domain,proto3" json:"Domain,omitempty"`
	UserId uint64 `protobuf:"varint,2,opt,name=UserId,json=userId,proto3" json:"UserId,omitempty"`
}

func (x *ListByDidRequest) Reset() {
	*x = ListByDidRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_position_position_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListByDidRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListByDidRequest) ProtoMessage() {}

func (x *ListByDidRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_position_position_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListByDidRequest.ProtoReflect.Descriptor instead.
func (*ListByDidRequest) Descriptor() ([]byte, []int) {
	return file_api_position_position_proto_rawDescGZIP(), []int{20}
}

func (x *ListByDidRequest) GetDomain() string {
	if x != nil {
		return x.Domain
	}
	return ""
}

func (x *ListByDidRequest) GetUserId() uint64 {
	if x != nil {
		return x.UserId
	}
	return 0
}

// 绑定用户的绑定关系
type BasePosition struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ID             uint64 `protobuf:"varint,1,opt,name=ID,proto3" json:"ID,omitempty"`
	Name           string `protobuf:"bytes,2,opt,name=Name,json=name,proto3" json:"Name,omitempty"`
	DepartmentId   uint64 `protobuf:"varint,10,opt,name=DepartmentId,json=departmentId,proto3" json:"DepartmentId,omitempty"`
	PositionCode   string `protobuf:"bytes,11,opt,name=PositionCode,json=positionCode,proto3" json:"PositionCode,omitempty"`
	DepartmentCode string `protobuf:"bytes,12,opt,name=DepartmentCode,json=departmentCode,proto3" json:"DepartmentCode,omitempty"`
}

func (x *BasePosition) Reset() {
	*x = BasePosition{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_position_position_proto_msgTypes[21]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BasePosition) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BasePosition) ProtoMessage() {}

func (x *BasePosition) ProtoReflect() protoreflect.Message {
	mi := &file_api_position_position_proto_msgTypes[21]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BasePosition.ProtoReflect.Descriptor instead.
func (*BasePosition) Descriptor() ([]byte, []int) {
	return file_api_position_position_proto_rawDescGZIP(), []int{21}
}

func (x *BasePosition) GetID() uint64 {
	if x != nil {
		return x.ID
	}
	return 0
}

func (x *BasePosition) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *BasePosition) GetDepartmentId() uint64 {
	if x != nil {
		return x.DepartmentId
	}
	return 0
}

func (x *BasePosition) GetPositionCode() string {
	if x != nil {
		return x.PositionCode
	}
	return ""
}

func (x *BasePosition) GetDepartmentCode() string {
	if x != nil {
		return x.DepartmentCode
	}
	return ""
}

type ListByDidResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Positions []*BasePosition `protobuf:"bytes,1,rep,name=Positions,json=positions,proto3" json:"Positions,omitempty"`
}

func (x *ListByDidResponse) Reset() {
	*x = ListByDidResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_position_position_proto_msgTypes[22]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListByDidResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListByDidResponse) ProtoMessage() {}

func (x *ListByDidResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_position_position_proto_msgTypes[22]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListByDidResponse.ProtoReflect.Descriptor instead.
func (*ListByDidResponse) Descriptor() ([]byte, []int) {
	return file_api_position_position_proto_rawDescGZIP(), []int{22}
}

func (x *ListByDidResponse) GetPositions() []*BasePosition {
	if x != nil {
		return x.Positions
	}
	return nil
}

// 绑定用户的绑定关系
type BindUserV2Request struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Domain       string         `protobuf:"bytes,1,opt,name=Domain,json=domain,proto3" json:"Domain,omitempty"`
	UserInfo     *UserInfo      `protobuf:"bytes,2,opt,name=userInfo,proto3" json:"userInfo,omitempty"`
	DepPositions []*DepPosition `protobuf:"bytes,5,rep,name=depPositions,json=positionUsers,proto3" json:"depPositions,omitempty"`
}

func (x *BindUserV2Request) Reset() {
	*x = BindUserV2Request{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_position_position_proto_msgTypes[23]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BindUserV2Request) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BindUserV2Request) ProtoMessage() {}

func (x *BindUserV2Request) ProtoReflect() protoreflect.Message {
	mi := &file_api_position_position_proto_msgTypes[23]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BindUserV2Request.ProtoReflect.Descriptor instead.
func (*BindUserV2Request) Descriptor() ([]byte, []int) {
	return file_api_position_position_proto_rawDescGZIP(), []int{23}
}

func (x *BindUserV2Request) GetDomain() string {
	if x != nil {
		return x.Domain
	}
	return ""
}

func (x *BindUserV2Request) GetUserInfo() *UserInfo {
	if x != nil {
		return x.UserInfo
	}
	return nil
}

func (x *BindUserV2Request) GetDepPositions() []*DepPosition {
	if x != nil {
		return x.DepPositions
	}
	return nil
}

type DepPosition struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ID        uint32        `protobuf:"varint,1,opt,name=ID,proto3" json:"ID,omitempty"`
	Positions []*PositionV2 `protobuf:"bytes,2,rep,name=positions,proto3" json:"positions,omitempty"`
	Name      string        `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`
	IsLeader  bool          `protobuf:"varint,4,opt,name=isLeader,proto3" json:"isLeader,omitempty"`
}

func (x *DepPosition) Reset() {
	*x = DepPosition{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_position_position_proto_msgTypes[24]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DepPosition) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DepPosition) ProtoMessage() {}

func (x *DepPosition) ProtoReflect() protoreflect.Message {
	mi := &file_api_position_position_proto_msgTypes[24]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DepPosition.ProtoReflect.Descriptor instead.
func (*DepPosition) Descriptor() ([]byte, []int) {
	return file_api_position_position_proto_rawDescGZIP(), []int{24}
}

func (x *DepPosition) GetID() uint32 {
	if x != nil {
		return x.ID
	}
	return 0
}

func (x *DepPosition) GetPositions() []*PositionV2 {
	if x != nil {
		return x.Positions
	}
	return nil
}

func (x *DepPosition) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *DepPosition) GetIsLeader() bool {
	if x != nil {
		return x.IsLeader
	}
	return false
}

type PositionV2 struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ID    uint32 `protobuf:"varint,1,opt,name=ID,proto3" json:"ID,omitempty"`
	Name  string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	Color string `protobuf:"bytes,3,opt,name=color,proto3" json:"color,omitempty"`
}

func (x *PositionV2) Reset() {
	*x = PositionV2{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_position_position_proto_msgTypes[25]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PositionV2) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PositionV2) ProtoMessage() {}

func (x *PositionV2) ProtoReflect() protoreflect.Message {
	mi := &file_api_position_position_proto_msgTypes[25]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PositionV2.ProtoReflect.Descriptor instead.
func (*PositionV2) Descriptor() ([]byte, []int) {
	return file_api_position_position_proto_rawDescGZIP(), []int{25}
}

func (x *PositionV2) GetID() uint32 {
	if x != nil {
		return x.ID
	}
	return 0
}

func (x *PositionV2) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *PositionV2) GetColor() string {
	if x != nil {
		return x.Color
	}
	return ""
}

type UserInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UserID   uint32 `protobuf:"varint,1,opt,name=UserID,json=userID,proto3" json:"UserID,omitempty"`
	UserName string `protobuf:"bytes,2,opt,name=UserName,json=userName,proto3" json:"UserName,omitempty"`
	Avatar   string `protobuf:"bytes,3,opt,name=Avatar,json=avatar,proto3" json:"Avatar,omitempty"`
}

func (x *UserInfo) Reset() {
	*x = UserInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_position_position_proto_msgTypes[26]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UserInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserInfo) ProtoMessage() {}

func (x *UserInfo) ProtoReflect() protoreflect.Message {
	mi := &file_api_position_position_proto_msgTypes[26]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserInfo.ProtoReflect.Descriptor instead.
func (*UserInfo) Descriptor() ([]byte, []int) {
	return file_api_position_position_proto_rawDescGZIP(), []int{26}
}

func (x *UserInfo) GetUserID() uint32 {
	if x != nil {
		return x.UserID
	}
	return 0
}

func (x *UserInfo) GetUserName() string {
	if x != nil {
		return x.UserName
	}
	return ""
}

func (x *UserInfo) GetAvatar() string {
	if x != nil {
		return x.Avatar
	}
	return ""
}

// 绑定用户的绑定关系
type BindUserRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Domain        string          `protobuf:"bytes,1,opt,name=Domain,json=domain,proto3" json:"Domain,omitempty"`
	UserID        uint64          `protobuf:"varint,2,opt,name=UserID,json=userID,proto3" json:"UserID,omitempty"`
	UserName      string          `protobuf:"bytes,3,opt,name=UserName,proto3" json:"UserName,omitempty"`
	Avatar        string          `protobuf:"bytes,4,opt,name=Avatar,proto3" json:"Avatar,omitempty"`
	PositionUsers []*PositionUser `protobuf:"bytes,5,rep,name=positionUsers,proto3" json:"positionUsers,omitempty"`
}

func (x *BindUserRequest) Reset() {
	*x = BindUserRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_position_position_proto_msgTypes[27]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BindUserRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BindUserRequest) ProtoMessage() {}

func (x *BindUserRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_position_position_proto_msgTypes[27]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BindUserRequest.ProtoReflect.Descriptor instead.
func (*BindUserRequest) Descriptor() ([]byte, []int) {
	return file_api_position_position_proto_rawDescGZIP(), []int{27}
}

func (x *BindUserRequest) GetDomain() string {
	if x != nil {
		return x.Domain
	}
	return ""
}

func (x *BindUserRequest) GetUserID() uint64 {
	if x != nil {
		return x.UserID
	}
	return 0
}

func (x *BindUserRequest) GetUserName() string {
	if x != nil {
		return x.UserName
	}
	return ""
}

func (x *BindUserRequest) GetAvatar() string {
	if x != nil {
		return x.Avatar
	}
	return ""
}

func (x *BindUserRequest) GetPositionUsers() []*PositionUser {
	if x != nil {
		return x.PositionUsers
	}
	return nil
}

type AddUserRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Domain       string        `protobuf:"bytes,1,opt,name=Domain,json=domain,proto3" json:"Domain,omitempty"`
	UserID       uint64        `protobuf:"varint,2,opt,name=UserID,json=userID,proto3" json:"UserID,omitempty"`
	UserName     string        `protobuf:"bytes,3,opt,name=UserName,proto3" json:"UserName,omitempty"`
	Avatar       string        `protobuf:"bytes,4,opt,name=Avatar,proto3" json:"Avatar,omitempty"`
	PositionUser *PositionUser `protobuf:"bytes,5,opt,name=positionUser,proto3" json:"positionUser,omitempty"`
}

func (x *AddUserRequest) Reset() {
	*x = AddUserRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_position_position_proto_msgTypes[28]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AddUserRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddUserRequest) ProtoMessage() {}

func (x *AddUserRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_position_position_proto_msgTypes[28]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddUserRequest.ProtoReflect.Descriptor instead.
func (*AddUserRequest) Descriptor() ([]byte, []int) {
	return file_api_position_position_proto_rawDescGZIP(), []int{28}
}

func (x *AddUserRequest) GetDomain() string {
	if x != nil {
		return x.Domain
	}
	return ""
}

func (x *AddUserRequest) GetUserID() uint64 {
	if x != nil {
		return x.UserID
	}
	return 0
}

func (x *AddUserRequest) GetUserName() string {
	if x != nil {
		return x.UserName
	}
	return ""
}

func (x *AddUserRequest) GetAvatar() string {
	if x != nil {
		return x.Avatar
	}
	return ""
}

func (x *AddUserRequest) GetPositionUser() *PositionUser {
	if x != nil {
		return x.PositionUser
	}
	return nil
}

type BindUserResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *BindUserResponse) Reset() {
	*x = BindUserResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_position_position_proto_msgTypes[29]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BindUserResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BindUserResponse) ProtoMessage() {}

func (x *BindUserResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_position_position_proto_msgTypes[29]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BindUserResponse.ProtoReflect.Descriptor instead.
func (*BindUserResponse) Descriptor() ([]byte, []int) {
	return file_api_position_position_proto_rawDescGZIP(), []int{29}
}

type CommonResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *CommonResponse) Reset() {
	*x = CommonResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_position_position_proto_msgTypes[30]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CommonResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CommonResponse) ProtoMessage() {}

func (x *CommonResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_position_position_proto_msgTypes[30]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CommonResponse.ProtoReflect.Descriptor instead.
func (*CommonResponse) Descriptor() ([]byte, []int) {
	return file_api_position_position_proto_rawDescGZIP(), []int{30}
}

type PositionUser struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PositionID     uint64 `protobuf:"varint,1,opt,name=PositionID,json=domain,proto3" json:"PositionID,omitempty"`
	DepartmentID   uint64 `protobuf:"varint,2,opt,name=DepartmentID,json=departmentID,proto3" json:"DepartmentID,omitempty"`
	DepartmentCode string `protobuf:"bytes,3,opt,name=DepartmentCode,json=departmentCode,proto3" json:"DepartmentCode,omitempty"`
	PositionCode   string `protobuf:"bytes,4,opt,name=PositionCode,json=positionCode,proto3" json:"PositionCode,omitempty"`
	UserId         uint64 `protobuf:"varint,5,opt,name=UserId,json=userId,proto3" json:"UserId,omitempty"`
	UserName       string `protobuf:"bytes,6,opt,name=UserName,json=userName,proto3" json:"UserName,omitempty"`
	DepartmentName string `protobuf:"bytes,7,opt,name=DepartmentName,json=departmentName,proto3" json:"DepartmentName,omitempty"`
	PositionName   string `protobuf:"bytes,8,opt,name=PositionName,json=pPositionName,proto3" json:"PositionName,omitempty"`
}

func (x *PositionUser) Reset() {
	*x = PositionUser{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_position_position_proto_msgTypes[31]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PositionUser) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PositionUser) ProtoMessage() {}

func (x *PositionUser) ProtoReflect() protoreflect.Message {
	mi := &file_api_position_position_proto_msgTypes[31]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PositionUser.ProtoReflect.Descriptor instead.
func (*PositionUser) Descriptor() ([]byte, []int) {
	return file_api_position_position_proto_rawDescGZIP(), []int{31}
}

func (x *PositionUser) GetPositionID() uint64 {
	if x != nil {
		return x.PositionID
	}
	return 0
}

func (x *PositionUser) GetDepartmentID() uint64 {
	if x != nil {
		return x.DepartmentID
	}
	return 0
}

func (x *PositionUser) GetDepartmentCode() string {
	if x != nil {
		return x.DepartmentCode
	}
	return ""
}

func (x *PositionUser) GetPositionCode() string {
	if x != nil {
		return x.PositionCode
	}
	return ""
}

func (x *PositionUser) GetUserId() uint64 {
	if x != nil {
		return x.UserId
	}
	return 0
}

func (x *PositionUser) GetUserName() string {
	if x != nil {
		return x.UserName
	}
	return ""
}

func (x *PositionUser) GetDepartmentName() string {
	if x != nil {
		return x.DepartmentName
	}
	return ""
}

func (x *PositionUser) GetPositionName() string {
	if x != nil {
		return x.PositionName
	}
	return ""
}

// 删除用户的绑定关系
type RemoveUserRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Domain string `protobuf:"bytes,1,opt,name=Domain,json=domain,proto3" json:"Domain,omitempty"`
	ID     uint64 `protobuf:"varint,2,opt,name=ID,proto3" json:"ID,omitempty"`
}

func (x *RemoveUserRequest) Reset() {
	*x = RemoveUserRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_position_position_proto_msgTypes[32]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RemoveUserRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RemoveUserRequest) ProtoMessage() {}

func (x *RemoveUserRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_position_position_proto_msgTypes[32]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RemoveUserRequest.ProtoReflect.Descriptor instead.
func (*RemoveUserRequest) Descriptor() ([]byte, []int) {
	return file_api_position_position_proto_rawDescGZIP(), []int{32}
}

func (x *RemoveUserRequest) GetDomain() string {
	if x != nil {
		return x.Domain
	}
	return ""
}

func (x *RemoveUserRequest) GetID() uint64 {
	if x != nil {
		return x.ID
	}
	return 0
}

type RemoveUserResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *RemoveUserResponse) Reset() {
	*x = RemoveUserResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_position_position_proto_msgTypes[33]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RemoveUserResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RemoveUserResponse) ProtoMessage() {}

func (x *RemoveUserResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_position_position_proto_msgTypes[33]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RemoveUserResponse.ProtoReflect.Descriptor instead.
func (*RemoveUserResponse) Descriptor() ([]byte, []int) {
	return file_api_position_position_proto_rawDescGZIP(), []int{33}
}

type CreateResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ID uint64 `protobuf:"varint,1,opt,name=ID,proto3" json:"ID,omitempty"`
}

func (x *CreateResponse) Reset() {
	*x = CreateResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_position_position_proto_msgTypes[34]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateResponse) ProtoMessage() {}

func (x *CreateResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_position_position_proto_msgTypes[34]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateResponse.ProtoReflect.Descriptor instead.
func (*CreateResponse) Descriptor() ([]byte, []int) {
	return file_api_position_position_proto_rawDescGZIP(), []int{34}
}

func (x *CreateResponse) GetID() uint64 {
	if x != nil {
		return x.ID
	}
	return 0
}

type RemoveRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ID     uint64 `protobuf:"varint,1,opt,name=ID,proto3" json:"ID,omitempty"`
	Domain string `protobuf:"bytes,2,opt,name=Domain,json=domain,proto3" json:"Domain,omitempty"`
}

func (x *RemoveRequest) Reset() {
	*x = RemoveRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_position_position_proto_msgTypes[35]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RemoveRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RemoveRequest) ProtoMessage() {}

func (x *RemoveRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_position_position_proto_msgTypes[35]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RemoveRequest.ProtoReflect.Descriptor instead.
func (*RemoveRequest) Descriptor() ([]byte, []int) {
	return file_api_position_position_proto_rawDescGZIP(), []int{35}
}

func (x *RemoveRequest) GetID() uint64 {
	if x != nil {
		return x.ID
	}
	return 0
}

func (x *RemoveRequest) GetDomain() string {
	if x != nil {
		return x.Domain
	}
	return ""
}

type RemoveResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *RemoveResponse) Reset() {
	*x = RemoveResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_position_position_proto_msgTypes[36]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RemoveResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RemoveResponse) ProtoMessage() {}

func (x *RemoveResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_position_position_proto_msgTypes[36]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RemoveResponse.ProtoReflect.Descriptor instead.
func (*RemoveResponse) Descriptor() ([]byte, []int) {
	return file_api_position_position_proto_rawDescGZIP(), []int{36}
}

type DetailRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ID     uint64 `protobuf:"varint,1,opt,name=ID,proto3" json:"ID,omitempty"`
	Domain string `protobuf:"bytes,2,opt,name=Domain,json=domain,proto3" json:"Domain,omitempty"`
}

func (x *DetailRequest) Reset() {
	*x = DetailRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_position_position_proto_msgTypes[37]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DetailRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DetailRequest) ProtoMessage() {}

func (x *DetailRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_position_position_proto_msgTypes[37]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DetailRequest.ProtoReflect.Descriptor instead.
func (*DetailRequest) Descriptor() ([]byte, []int) {
	return file_api_position_position_proto_rawDescGZIP(), []int{37}
}

func (x *DetailRequest) GetID() uint64 {
	if x != nil {
		return x.ID
	}
	return 0
}

func (x *DetailRequest) GetDomain() string {
	if x != nil {
		return x.Domain
	}
	return ""
}

type DetailResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ID        uint64 `protobuf:"varint,1,opt,name=ID,proto3" json:"ID,omitempty"`
	Name      string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	Remark    string `protobuf:"bytes,3,opt,name=remark,proto3" json:"remark,omitempty"`
	CreatedAt string `protobuf:"bytes,6,opt,name=createdAt,proto3" json:"createdAt,omitempty"`
	UpdatedAt string `protobuf:"bytes,7,opt,name=updatedAt,proto3" json:"updatedAt,omitempty"`
	//int64                         status            = 8 ;
	Rules            []*RuleInfo `protobuf:"bytes,9,rep,name=rules,proto3" json:"rules,omitempty"` //关联部门
	DepartmentID     uint64      `protobuf:"varint,10,opt,name=departmentID,proto3" json:"departmentID,omitempty"`
	PositionCode     string      `protobuf:"bytes,11,opt,name=positionCode,proto3" json:"positionCode,omitempty"`
	DepartmentCode   string      `protobuf:"bytes,12,opt,name=departmentCode,proto3" json:"departmentCode,omitempty"`
	PositionTreeRule []*RuleInfo `protobuf:"bytes,13,rep,name=positionTreeRule,proto3" json:"positionTreeRule,omitempty"`
	Color            string      `protobuf:"bytes,17,opt,name=color,proto3" json:"color,omitempty"`
	OperatorName     string      `protobuf:"bytes,18,opt,name=operatorName,proto3" json:"operatorName,omitempty"`
	MenuAuths        []string    `protobuf:"bytes,19,rep,name=menuAuths,proto3" json:"menuAuths,omitempty"`
	LinkerNum        uint32      `protobuf:"varint,20,opt,name=linkerNum,proto3" json:"linkerNum,omitempty"`
	IsLeader         bool        `protobuf:"varint,22,opt,name=isLeader,proto3" json:"isLeader,omitempty"`
}

func (x *DetailResponse) Reset() {
	*x = DetailResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_position_position_proto_msgTypes[38]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DetailResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DetailResponse) ProtoMessage() {}

func (x *DetailResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_position_position_proto_msgTypes[38]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DetailResponse.ProtoReflect.Descriptor instead.
func (*DetailResponse) Descriptor() ([]byte, []int) {
	return file_api_position_position_proto_rawDescGZIP(), []int{38}
}

func (x *DetailResponse) GetID() uint64 {
	if x != nil {
		return x.ID
	}
	return 0
}

func (x *DetailResponse) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *DetailResponse) GetRemark() string {
	if x != nil {
		return x.Remark
	}
	return ""
}

func (x *DetailResponse) GetCreatedAt() string {
	if x != nil {
		return x.CreatedAt
	}
	return ""
}

func (x *DetailResponse) GetUpdatedAt() string {
	if x != nil {
		return x.UpdatedAt
	}
	return ""
}

func (x *DetailResponse) GetRules() []*RuleInfo {
	if x != nil {
		return x.Rules
	}
	return nil
}

func (x *DetailResponse) GetDepartmentID() uint64 {
	if x != nil {
		return x.DepartmentID
	}
	return 0
}

func (x *DetailResponse) GetPositionCode() string {
	if x != nil {
		return x.PositionCode
	}
	return ""
}

func (x *DetailResponse) GetDepartmentCode() string {
	if x != nil {
		return x.DepartmentCode
	}
	return ""
}

func (x *DetailResponse) GetPositionTreeRule() []*RuleInfo {
	if x != nil {
		return x.PositionTreeRule
	}
	return nil
}

func (x *DetailResponse) GetColor() string {
	if x != nil {
		return x.Color
	}
	return ""
}

func (x *DetailResponse) GetOperatorName() string {
	if x != nil {
		return x.OperatorName
	}
	return ""
}

func (x *DetailResponse) GetMenuAuths() []string {
	if x != nil {
		return x.MenuAuths
	}
	return nil
}

func (x *DetailResponse) GetLinkerNum() uint32 {
	if x != nil {
		return x.LinkerNum
	}
	return 0
}

func (x *DetailResponse) GetIsLeader() bool {
	if x != nil {
		return x.IsLeader
	}
	return false
}

type CreateRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ID               uint64      `protobuf:"varint,1,opt,name=ID,proto3" json:"ID,omitempty"`
	Name             string      `protobuf:"bytes,2,opt,name=Name,json=name,proto3" json:"Name,omitempty"`
	Remark           string      `protobuf:"bytes,3,opt,name=Remark,json=remark,proto3" json:"Remark,omitempty"`
	CreatedAt        string      `protobuf:"bytes,6,opt,name=CreatedAt,json=createAt,proto3" json:"CreatedAt,omitempty"`
	UpdatedAt        string      `protobuf:"bytes,7,opt,name=UpdatedAt,json=updateAt,proto3" json:"UpdatedAt,omitempty"`
	Status           int64       `protobuf:"varint,8,opt,name=Status,json=status,proto3" json:"Status,omitempty"`
	Rules            []*RuleInfo `protobuf:"bytes,9,rep,name=Rules,json=rules,proto3" json:"Rules,omitempty"` //关联部门
	DepartmentID     uint64      `protobuf:"varint,10,opt,name=DepartmentID,json=departmentID,proto3" json:"DepartmentID,omitempty"`
	PositionCode     string      `protobuf:"bytes,11,opt,name=PositionCode,json=positionCode,proto3" json:"PositionCode,omitempty"`
	DepartmentCode   string      `protobuf:"bytes,12,opt,name=DepartmentCode,json=departmentCode,proto3" json:"DepartmentCode,omitempty"`
	PositionTreeRule []*RuleInfo `protobuf:"bytes,13,rep,name=PositionTreeRule,json=positionTreeRules,proto3" json:"PositionTreeRule,omitempty"`
	Domain           string      `protobuf:"bytes,14,opt,name=Domain,json=domain,proto3" json:"Domain,omitempty"`
	Num              int32       `protobuf:"varint,15,opt,name=Num,json=num,proto3" json:"Num,omitempty"`
	DepartmentName   string      `protobuf:"bytes,16,opt,name=DepartmentName,proto3" json:"DepartmentName,omitempty"`
	Color            string      `protobuf:"bytes,17,opt,name=color,proto3" json:"color,omitempty"`
	OperatorName     string      `protobuf:"bytes,18,opt,name=operatorName,proto3" json:"operatorName,omitempty"`
	MenuAuths        []string    `protobuf:"bytes,19,rep,name=menuAuths,proto3" json:"menuAuths,omitempty"`
	LinkerNum        uint32      `protobuf:"varint,20,opt,name=linkerNum,proto3" json:"linkerNum,omitempty"`
	DepartmentIDs    []uint32    `protobuf:"varint,21,rep,packed,name=departmentIDs,proto3" json:"departmentIDs,omitempty"`
	IsLeader         bool        `protobuf:"varint,22,opt,name=isLeader,proto3" json:"isLeader,omitempty"`
}

func (x *CreateRequest) Reset() {
	*x = CreateRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_position_position_proto_msgTypes[39]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateRequest) ProtoMessage() {}

func (x *CreateRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_position_position_proto_msgTypes[39]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateRequest.ProtoReflect.Descriptor instead.
func (*CreateRequest) Descriptor() ([]byte, []int) {
	return file_api_position_position_proto_rawDescGZIP(), []int{39}
}

func (x *CreateRequest) GetID() uint64 {
	if x != nil {
		return x.ID
	}
	return 0
}

func (x *CreateRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *CreateRequest) GetRemark() string {
	if x != nil {
		return x.Remark
	}
	return ""
}

func (x *CreateRequest) GetCreatedAt() string {
	if x != nil {
		return x.CreatedAt
	}
	return ""
}

func (x *CreateRequest) GetUpdatedAt() string {
	if x != nil {
		return x.UpdatedAt
	}
	return ""
}

func (x *CreateRequest) GetStatus() int64 {
	if x != nil {
		return x.Status
	}
	return 0
}

func (x *CreateRequest) GetRules() []*RuleInfo {
	if x != nil {
		return x.Rules
	}
	return nil
}

func (x *CreateRequest) GetDepartmentID() uint64 {
	if x != nil {
		return x.DepartmentID
	}
	return 0
}

func (x *CreateRequest) GetPositionCode() string {
	if x != nil {
		return x.PositionCode
	}
	return ""
}

func (x *CreateRequest) GetDepartmentCode() string {
	if x != nil {
		return x.DepartmentCode
	}
	return ""
}

func (x *CreateRequest) GetPositionTreeRule() []*RuleInfo {
	if x != nil {
		return x.PositionTreeRule
	}
	return nil
}

func (x *CreateRequest) GetDomain() string {
	if x != nil {
		return x.Domain
	}
	return ""
}

func (x *CreateRequest) GetNum() int32 {
	if x != nil {
		return x.Num
	}
	return 0
}

func (x *CreateRequest) GetDepartmentName() string {
	if x != nil {
		return x.DepartmentName
	}
	return ""
}

func (x *CreateRequest) GetColor() string {
	if x != nil {
		return x.Color
	}
	return ""
}

func (x *CreateRequest) GetOperatorName() string {
	if x != nil {
		return x.OperatorName
	}
	return ""
}

func (x *CreateRequest) GetMenuAuths() []string {
	if x != nil {
		return x.MenuAuths
	}
	return nil
}

func (x *CreateRequest) GetLinkerNum() uint32 {
	if x != nil {
		return x.LinkerNum
	}
	return 0
}

func (x *CreateRequest) GetDepartmentIDs() []uint32 {
	if x != nil {
		return x.DepartmentIDs
	}
	return nil
}

func (x *CreateRequest) GetIsLeader() bool {
	if x != nil {
		return x.IsLeader
	}
	return false
}

type BatchAddRulesRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ids          []uint32 `protobuf:"varint,1,rep,packed,name=ids,json=domain,proto3" json:"ids,omitempty"`
	RuleIds      []uint32 `protobuf:"varint,2,rep,packed,name=ruleIds,proto3" json:"ruleIds,omitempty"`
	OperatorName string   `protobuf:"bytes,3,opt,name=operatorName,proto3" json:"operatorName,omitempty"`
}

func (x *BatchAddRulesRequest) Reset() {
	*x = BatchAddRulesRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_position_position_proto_msgTypes[40]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BatchAddRulesRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BatchAddRulesRequest) ProtoMessage() {}

func (x *BatchAddRulesRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_position_position_proto_msgTypes[40]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BatchAddRulesRequest.ProtoReflect.Descriptor instead.
func (*BatchAddRulesRequest) Descriptor() ([]byte, []int) {
	return file_api_position_position_proto_rawDescGZIP(), []int{40}
}

func (x *BatchAddRulesRequest) GetIds() []uint32 {
	if x != nil {
		return x.Ids
	}
	return nil
}

func (x *BatchAddRulesRequest) GetRuleIds() []uint32 {
	if x != nil {
		return x.RuleIds
	}
	return nil
}

func (x *BatchAddRulesRequest) GetOperatorName() string {
	if x != nil {
		return x.OperatorName
	}
	return ""
}

type Leader struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ID   int64  `protobuf:"varint,1,opt,name=ID,proto3" json:"ID,omitempty"`
	Name string `protobuf:"bytes,2,opt,name=Name,json=name,proto3" json:"Name,omitempty"`
}

func (x *Leader) Reset() {
	*x = Leader{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_position_position_proto_msgTypes[41]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Leader) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Leader) ProtoMessage() {}

func (x *Leader) ProtoReflect() protoreflect.Message {
	mi := &file_api_position_position_proto_msgTypes[41]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Leader.ProtoReflect.Descriptor instead.
func (*Leader) Descriptor() ([]byte, []int) {
	return file_api_position_position_proto_rawDescGZIP(), []int{41}
}

func (x *Leader) GetID() int64 {
	if x != nil {
		return x.ID
	}
	return 0
}

func (x *Leader) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

//更新
type ListRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	DepartmentID    uint64   `protobuf:"varint,1,opt,name=DepartmentID,json=departmentID,proto3" json:"DepartmentID,omitempty"`
	PageSize        uint64   `protobuf:"varint,2,opt,name=PageSize,json=pageSize,proto3" json:"PageSize,omitempty"`
	Page            uint64   `protobuf:"varint,3,opt,name=Page,json=page,proto3" json:"Page,omitempty"`
	Domain          string   `protobuf:"bytes,4,opt,name=Domain,json=domain,proto3" json:"Domain,omitempty"`
	Key             string   `protobuf:"bytes,5,opt,name=Key,json=key,proto3" json:"Key,omitempty"`
	ID              string   `protobuf:"bytes,6,opt,name=ID,proto3" json:"ID,omitempty"`                                //ID搜
	Name            string   `protobuf:"bytes,7,opt,name=name,proto3" json:"name,omitempty"`                            //岗位名称
	MenuAuth        string   `protobuf:"bytes,8,opt,name=menuAuth,proto3" json:"menuAuth,omitempty"`                    //权限名称
	OperatorName    string   `protobuf:"bytes,9,opt,name=operatorName,proto3" json:"operatorName,omitempty"`            //操作人
	UpdateStartAt   string   `protobuf:"bytes,10,opt,name=updateStartAt,proto3" json:"updateStartAt,omitempty"`         //更新开始时间
	UpdateEndAt     string   `protobuf:"bytes,11,opt,name=updateEndAt,proto3" json:"updateEndAt,omitempty"`             //更新结束时间
	NotDepartmentID uint32   `protobuf:"varint,13,opt,name=notDepartmentID,proto3" json:"notDepartmentID,omitempty"`    //新版本 非部门查找
	SortField       string   `protobuf:"bytes,14,opt,name=sortField,proto3" json:"sortField,omitempty"`                 //排序字段
	SortOrder       string   `protobuf:"bytes,15,opt,name=sortOrder,proto3" json:"sortOrder,omitempty"`                 //顺序
	DepartmentIds   []uint32 `protobuf:"varint,16,rep,packed,name=departmentIds,proto3" json:"departmentIds,omitempty"` //顺序
}

func (x *ListRequest) Reset() {
	*x = ListRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_position_position_proto_msgTypes[42]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListRequest) ProtoMessage() {}

func (x *ListRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_position_position_proto_msgTypes[42]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListRequest.ProtoReflect.Descriptor instead.
func (*ListRequest) Descriptor() ([]byte, []int) {
	return file_api_position_position_proto_rawDescGZIP(), []int{42}
}

func (x *ListRequest) GetDepartmentID() uint64 {
	if x != nil {
		return x.DepartmentID
	}
	return 0
}

func (x *ListRequest) GetPageSize() uint64 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *ListRequest) GetPage() uint64 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *ListRequest) GetDomain() string {
	if x != nil {
		return x.Domain
	}
	return ""
}

func (x *ListRequest) GetKey() string {
	if x != nil {
		return x.Key
	}
	return ""
}

func (x *ListRequest) GetID() string {
	if x != nil {
		return x.ID
	}
	return ""
}

func (x *ListRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *ListRequest) GetMenuAuth() string {
	if x != nil {
		return x.MenuAuth
	}
	return ""
}

func (x *ListRequest) GetOperatorName() string {
	if x != nil {
		return x.OperatorName
	}
	return ""
}

func (x *ListRequest) GetUpdateStartAt() string {
	if x != nil {
		return x.UpdateStartAt
	}
	return ""
}

func (x *ListRequest) GetUpdateEndAt() string {
	if x != nil {
		return x.UpdateEndAt
	}
	return ""
}

func (x *ListRequest) GetNotDepartmentID() uint32 {
	if x != nil {
		return x.NotDepartmentID
	}
	return 0
}

func (x *ListRequest) GetSortField() string {
	if x != nil {
		return x.SortField
	}
	return ""
}

func (x *ListRequest) GetSortOrder() string {
	if x != nil {
		return x.SortOrder
	}
	return ""
}

func (x *ListRequest) GetDepartmentIds() []uint32 {
	if x != nil {
		return x.DepartmentIds
	}
	return nil
}

//列表
type Rule struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ID         uint64 `protobuf:"varint,1,opt,name=ID,proto3" json:"ID,omitempty"`
	Name       string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	RuleDataID uint64 `protobuf:"varint,3,opt,name=RuleDataID,proto3" json:"RuleDataID,omitempty"`
}

func (x *Rule) Reset() {
	*x = Rule{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_position_position_proto_msgTypes[43]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Rule) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Rule) ProtoMessage() {}

func (x *Rule) ProtoReflect() protoreflect.Message {
	mi := &file_api_position_position_proto_msgTypes[43]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Rule.ProtoReflect.Descriptor instead.
func (*Rule) Descriptor() ([]byte, []int) {
	return file_api_position_position_proto_rawDescGZIP(), []int{43}
}

func (x *Rule) GetID() uint64 {
	if x != nil {
		return x.ID
	}
	return 0
}

func (x *Rule) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *Rule) GetRuleDataID() uint64 {
	if x != nil {
		return x.RuleDataID
	}
	return 0
}

//权限信息
type RuleInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ID                 uint64               `protobuf:"varint,1,opt,name=ID,proto3" json:"ID,omitempty"`
	Name               string               `protobuf:"bytes,2,opt,name=Name,proto3" json:"Name,omitempty"`
	Url                string               `protobuf:"bytes,3,opt,name=Url,proto3" json:"Url,omitempty"`
	Method             string               `protobuf:"bytes,4,opt,name=Method,proto3" json:"Method,omitempty"`
	RuleDataID         uint64               `protobuf:"varint,5,opt,name=RuleDataID,proto3" json:"RuleDataID,omitempty"`
	RuleDataName       string               `protobuf:"bytes,6,opt,name=RuleDataName,proto3" json:"RuleDataName,omitempty"`
	RuleDataField      []string             `protobuf:"bytes,7,rep,name=RuleDataField,proto3" json:"RuleDataField,omitempty"`
	Son                []*RuleInfo          `protobuf:"bytes,8,rep,name=Son,proto3" json:"Son,omitempty"`
	Pid                uint64               `protobuf:"varint,9,opt,name=Pid,proto3" json:"Pid,omitempty"`
	RuleData           []*RuleData          `protobuf:"bytes,10,rep,name=RuleData,proto3" json:"RuleData,omitempty"`
	MenuType           string               `protobuf:"bytes,11,opt,name=MenuType,proto3" json:"MenuType,omitempty"`
	Type               string               `protobuf:"bytes,12,opt,name=Type,proto3" json:"Type,omitempty"`
	PositionRuleFields []*PositionRuleField `protobuf:"bytes,13,rep,name=PositionRuleFields,proto3" json:"PositionRuleFields,omitempty"`
	DataRange          uint32               `protobuf:"varint,14,opt,name=dataRange,proto3" json:"dataRange,omitempty"` //数据范围 0-默认所有 1-仅自己 2- 自己以及下属 3-本部门
	InterfaceList      []*RuleInfo          `protobuf:"bytes,15,rep,name=interfaceList,proto3" json:"interfaceList,omitempty"`
	ButtonList         []*RuleInfo          `protobuf:"bytes,16,rep,name=buttonList,proto3" json:"buttonList,omitempty"`
	DataDepartmentIds  []uint32             `protobuf:"varint,17,rep,packed,name=dataDepartmentIds,proto3" json:"dataDepartmentIds,omitempty"`
}

func (x *RuleInfo) Reset() {
	*x = RuleInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_position_position_proto_msgTypes[44]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RuleInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RuleInfo) ProtoMessage() {}

func (x *RuleInfo) ProtoReflect() protoreflect.Message {
	mi := &file_api_position_position_proto_msgTypes[44]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RuleInfo.ProtoReflect.Descriptor instead.
func (*RuleInfo) Descriptor() ([]byte, []int) {
	return file_api_position_position_proto_rawDescGZIP(), []int{44}
}

func (x *RuleInfo) GetID() uint64 {
	if x != nil {
		return x.ID
	}
	return 0
}

func (x *RuleInfo) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *RuleInfo) GetUrl() string {
	if x != nil {
		return x.Url
	}
	return ""
}

func (x *RuleInfo) GetMethod() string {
	if x != nil {
		return x.Method
	}
	return ""
}

func (x *RuleInfo) GetRuleDataID() uint64 {
	if x != nil {
		return x.RuleDataID
	}
	return 0
}

func (x *RuleInfo) GetRuleDataName() string {
	if x != nil {
		return x.RuleDataName
	}
	return ""
}

func (x *RuleInfo) GetRuleDataField() []string {
	if x != nil {
		return x.RuleDataField
	}
	return nil
}

func (x *RuleInfo) GetSon() []*RuleInfo {
	if x != nil {
		return x.Son
	}
	return nil
}

func (x *RuleInfo) GetPid() uint64 {
	if x != nil {
		return x.Pid
	}
	return 0
}

func (x *RuleInfo) GetRuleData() []*RuleData {
	if x != nil {
		return x.RuleData
	}
	return nil
}

func (x *RuleInfo) GetMenuType() string {
	if x != nil {
		return x.MenuType
	}
	return ""
}

func (x *RuleInfo) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

func (x *RuleInfo) GetPositionRuleFields() []*PositionRuleField {
	if x != nil {
		return x.PositionRuleFields
	}
	return nil
}

func (x *RuleInfo) GetDataRange() uint32 {
	if x != nil {
		return x.DataRange
	}
	return 0
}

func (x *RuleInfo) GetInterfaceList() []*RuleInfo {
	if x != nil {
		return x.InterfaceList
	}
	return nil
}

func (x *RuleInfo) GetButtonList() []*RuleInfo {
	if x != nil {
		return x.ButtonList
	}
	return nil
}

func (x *RuleInfo) GetDataDepartmentIds() []uint32 {
	if x != nil {
		return x.DataDepartmentIds
	}
	return nil
}

//数据权限
type PositionRuleField struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ID          uint64 `protobuf:"varint,1,opt,name=ID,proto3" json:"ID,omitempty"`
	RuleFieldId uint64 `protobuf:"varint,2,opt,name=ruleFieldId,proto3" json:"ruleFieldId,omitempty"`
	RuleId      uint64 `protobuf:"varint,3,opt,name=ruleId,proto3" json:"ruleId,omitempty"`
	FieldCnName string `protobuf:"bytes,4,opt,name=fieldCnName,proto3" json:"fieldCnName,omitempty"`
	FieldKey    string `protobuf:"bytes,5,opt,name=fieldKey,proto3" json:"fieldKey,omitempty"`
	Index       uint64 `protobuf:"varint,6,opt,name=index,proto3" json:"index,omitempty"`
}

func (x *PositionRuleField) Reset() {
	*x = PositionRuleField{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_position_position_proto_msgTypes[45]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PositionRuleField) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PositionRuleField) ProtoMessage() {}

func (x *PositionRuleField) ProtoReflect() protoreflect.Message {
	mi := &file_api_position_position_proto_msgTypes[45]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PositionRuleField.ProtoReflect.Descriptor instead.
func (*PositionRuleField) Descriptor() ([]byte, []int) {
	return file_api_position_position_proto_rawDescGZIP(), []int{45}
}

func (x *PositionRuleField) GetID() uint64 {
	if x != nil {
		return x.ID
	}
	return 0
}

func (x *PositionRuleField) GetRuleFieldId() uint64 {
	if x != nil {
		return x.RuleFieldId
	}
	return 0
}

func (x *PositionRuleField) GetRuleId() uint64 {
	if x != nil {
		return x.RuleId
	}
	return 0
}

func (x *PositionRuleField) GetFieldCnName() string {
	if x != nil {
		return x.FieldCnName
	}
	return ""
}

func (x *PositionRuleField) GetFieldKey() string {
	if x != nil {
		return x.FieldKey
	}
	return ""
}

func (x *PositionRuleField) GetIndex() uint64 {
	if x != nil {
		return x.Index
	}
	return 0
}

type RuleData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ID    uint64 `protobuf:"varint,1,opt,name=ID,proto3" json:"ID,omitempty"`
	Title string `protobuf:"bytes,2,opt,name=Title,proto3" json:"Title,omitempty"`
}

func (x *RuleData) Reset() {
	*x = RuleData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_position_position_proto_msgTypes[46]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RuleData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RuleData) ProtoMessage() {}

func (x *RuleData) ProtoReflect() protoreflect.Message {
	mi := &file_api_position_position_proto_msgTypes[46]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RuleData.ProtoReflect.Descriptor instead.
func (*RuleData) Descriptor() ([]byte, []int) {
	return file_api_position_position_proto_rawDescGZIP(), []int{46}
}

func (x *RuleData) GetID() uint64 {
	if x != nil {
		return x.ID
	}
	return 0
}

func (x *RuleData) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

type ListResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Count uint64 `protobuf:"varint,1,opt,name=Count,proto3" json:"Count,omitempty"`
	//uint64 NextID                 = 2;
	Data []*CreateRequest `protobuf:"bytes,4,rep,name=data,proto3" json:"data,omitempty"`
}

func (x *ListResponse) Reset() {
	*x = ListResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_position_position_proto_msgTypes[47]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListResponse) ProtoMessage() {}

func (x *ListResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_position_position_proto_msgTypes[47]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListResponse.ProtoReflect.Descriptor instead.
func (*ListResponse) Descriptor() ([]byte, []int) {
	return file_api_position_position_proto_rawDescGZIP(), []int{47}
}

func (x *ListResponse) GetCount() uint64 {
	if x != nil {
		return x.Count
	}
	return 0
}

func (x *ListResponse) GetData() []*CreateRequest {
	if x != nil {
		return x.Data
	}
	return nil
}

type LogListRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Domain         string `protobuf:"bytes,1,opt,name=domain,proto3" json:"domain,omitempty"`
	PageSize       uint64 `protobuf:"varint,2,opt,name=pageSize,proto3" json:"pageSize,omitempty"`
	Page           uint64 `protobuf:"varint,3,opt,name=page,proto3" json:"page,omitempty"`
	OperateType    string `protobuf:"bytes,4,opt,name=operateType,proto3" json:"operateType,omitempty"`   //类型
	OperatorName   string `protobuf:"bytes,5,opt,name=operatorName,proto3" json:"operatorName,omitempty"` //人员
	StartCreatedAt string `protobuf:"bytes,6,opt,name=startCreatedAt,proto3" json:"startCreatedAt,omitempty"`
	EndCreatedAt   string `protobuf:"bytes,7,opt,name=endCreatedAt,proto3" json:"endCreatedAt,omitempty"`
	OperatorTel    string `protobuf:"bytes,8,opt,name=operatorTel,proto3" json:"operatorTel,omitempty"`
}

func (x *LogListRequest) Reset() {
	*x = LogListRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_position_position_proto_msgTypes[48]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LogListRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LogListRequest) ProtoMessage() {}

func (x *LogListRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_position_position_proto_msgTypes[48]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LogListRequest.ProtoReflect.Descriptor instead.
func (*LogListRequest) Descriptor() ([]byte, []int) {
	return file_api_position_position_proto_rawDescGZIP(), []int{48}
}

func (x *LogListRequest) GetDomain() string {
	if x != nil {
		return x.Domain
	}
	return ""
}

func (x *LogListRequest) GetPageSize() uint64 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *LogListRequest) GetPage() uint64 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *LogListRequest) GetOperateType() string {
	if x != nil {
		return x.OperateType
	}
	return ""
}

func (x *LogListRequest) GetOperatorName() string {
	if x != nil {
		return x.OperatorName
	}
	return ""
}

func (x *LogListRequest) GetStartCreatedAt() string {
	if x != nil {
		return x.StartCreatedAt
	}
	return ""
}

func (x *LogListRequest) GetEndCreatedAt() string {
	if x != nil {
		return x.EndCreatedAt
	}
	return ""
}

func (x *LogListRequest) GetOperatorTel() string {
	if x != nil {
		return x.OperatorTel
	}
	return ""
}

type LogListResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Count uint64         `protobuf:"varint,1,opt,name=count,proto3" json:"count,omitempty"`
	List  []*LogResponse `protobuf:"bytes,2,rep,name=list,proto3" json:"list,omitempty"`
}

func (x *LogListResponse) Reset() {
	*x = LogListResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_position_position_proto_msgTypes[49]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LogListResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LogListResponse) ProtoMessage() {}

func (x *LogListResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_position_position_proto_msgTypes[49]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LogListResponse.ProtoReflect.Descriptor instead.
func (*LogListResponse) Descriptor() ([]byte, []int) {
	return file_api_position_position_proto_rawDescGZIP(), []int{49}
}

func (x *LogListResponse) GetCount() uint64 {
	if x != nil {
		return x.Count
	}
	return 0
}

func (x *LogListResponse) GetList() []*LogResponse {
	if x != nil {
		return x.List
	}
	return nil
}

type LogRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id           uint64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	OperateType  string `protobuf:"bytes,2,opt,name=operateType,proto3" json:"operateType,omitempty"`
	Info         string `protobuf:"bytes,3,opt,name=info,proto3" json:"info,omitempty"`
	OperatorName string `protobuf:"bytes,4,opt,name=operatorName,proto3" json:"operatorName,omitempty"`
	OperatorTel  string `protobuf:"bytes,5,opt,name=operatorTel,proto3" json:"operatorTel,omitempty"`
	OperatorId   uint32 `protobuf:"varint,6,opt,name=operatorId,proto3" json:"operatorId,omitempty"`
	PositionId   uint32 `protobuf:"varint,7,opt,name=positionId,proto3" json:"positionId,omitempty"`
	Domain       string `protobuf:"bytes,8,opt,name=domain,proto3" json:"domain,omitempty"`
}

func (x *LogRequest) Reset() {
	*x = LogRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_position_position_proto_msgTypes[50]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LogRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LogRequest) ProtoMessage() {}

func (x *LogRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_position_position_proto_msgTypes[50]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LogRequest.ProtoReflect.Descriptor instead.
func (*LogRequest) Descriptor() ([]byte, []int) {
	return file_api_position_position_proto_rawDescGZIP(), []int{50}
}

func (x *LogRequest) GetId() uint64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *LogRequest) GetOperateType() string {
	if x != nil {
		return x.OperateType
	}
	return ""
}

func (x *LogRequest) GetInfo() string {
	if x != nil {
		return x.Info
	}
	return ""
}

func (x *LogRequest) GetOperatorName() string {
	if x != nil {
		return x.OperatorName
	}
	return ""
}

func (x *LogRequest) GetOperatorTel() string {
	if x != nil {
		return x.OperatorTel
	}
	return ""
}

func (x *LogRequest) GetOperatorId() uint32 {
	if x != nil {
		return x.OperatorId
	}
	return 0
}

func (x *LogRequest) GetPositionId() uint32 {
	if x != nil {
		return x.PositionId
	}
	return 0
}

func (x *LogRequest) GetDomain() string {
	if x != nil {
		return x.Domain
	}
	return ""
}

type LogResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id           uint64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	OperateType  string `protobuf:"bytes,2,opt,name=operateType,proto3" json:"operateType,omitempty"`
	Info         string `protobuf:"bytes,3,opt,name=info,proto3" json:"info,omitempty"`
	OperatorName string `protobuf:"bytes,4,opt,name=operatorName,proto3" json:"operatorName,omitempty"`
	OperatorTel  string `protobuf:"bytes,5,opt,name=OperatorTel,proto3" json:"OperatorTel,omitempty"`
	CreatedAt    string `protobuf:"bytes,6,opt,name=createdAt,proto3" json:"createdAt,omitempty"`
}

func (x *LogResponse) Reset() {
	*x = LogResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_position_position_proto_msgTypes[51]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LogResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LogResponse) ProtoMessage() {}

func (x *LogResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_position_position_proto_msgTypes[51]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LogResponse.ProtoReflect.Descriptor instead.
func (*LogResponse) Descriptor() ([]byte, []int) {
	return file_api_position_position_proto_rawDescGZIP(), []int{51}
}

func (x *LogResponse) GetId() uint64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *LogResponse) GetOperateType() string {
	if x != nil {
		return x.OperateType
	}
	return ""
}

func (x *LogResponse) GetInfo() string {
	if x != nil {
		return x.Info
	}
	return ""
}

func (x *LogResponse) GetOperatorName() string {
	if x != nil {
		return x.OperatorName
	}
	return ""
}

func (x *LogResponse) GetOperatorTel() string {
	if x != nil {
		return x.OperatorTel
	}
	return ""
}

func (x *LogResponse) GetCreatedAt() string {
	if x != nil {
		return x.CreatedAt
	}
	return ""
}

var File_api_position_position_proto protoreflect.FileDescriptor

var file_api_position_position_proto_rawDesc = []byte{
	0x0a, 0x1b, 0x61, 0x70, 0x69, 0x2f, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x2f, 0x70,
	0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x08, 0x70,
	0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x1a, 0x3d, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e,
	0x63, 0x6f, 0x6d, 0x2f, 0x6d, 0x77, 0x69, 0x74, 0x6b, 0x6f, 0x77, 0x2f, 0x67, 0x6f, 0x2d, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x2d, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x6f, 0x72, 0x73, 0x40,
	0x76, 0x30, 0x2e, 0x33, 0x2e, 0x32, 0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x6f, 0x72,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x66, 0x0a, 0x22, 0x44, 0x65, 0x70, 0x61, 0x72, 0x74,
	0x6d, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x73, 0x42, 0x79, 0x55, 0x72, 0x6c, 0x41, 0x6e, 0x64, 0x55,
	0x73, 0x65, 0x72, 0x49, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x16, 0x0a, 0x06,
	0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x06, 0x75, 0x73,
	0x65, 0x72, 0x49, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x64, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x64, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x12, 0x10, 0x0a, 0x03,
	0x75, 0x72, 0x6c, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x75, 0x72, 0x6c, 0x22, 0x4b,
	0x0a, 0x23, 0x44, 0x65, 0x70, 0x61, 0x72, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x73, 0x42,
	0x79, 0x55, 0x72, 0x6c, 0x41, 0x6e, 0x64, 0x55, 0x73, 0x65, 0x72, 0x49, 0x64, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x24, 0x0a, 0x0d, 0x64, 0x65, 0x70, 0x61, 0x72, 0x74, 0x6d,
	0x65, 0x6e, 0x74, 0x49, 0x64, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0d, 0x52, 0x0d, 0x64, 0x65,
	0x70, 0x61, 0x72, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x73, 0x22, 0x26, 0x0a, 0x12, 0x55,
	0x73, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x73, 0x56, 0x32, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x12, 0x10, 0x0a, 0x03, 0x49, 0x44, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0d, 0x52, 0x03,
	0x49, 0x44, 0x73, 0x22, 0x4e, 0x0a, 0x13, 0x55, 0x73, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x73,
	0x56, 0x32, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x37, 0x0a, 0x08, 0x75, 0x73,
	0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x70,
	0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x42, 0x69, 0x6e, 0x64, 0x55, 0x73, 0x65, 0x72,
	0x56, 0x32, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x52, 0x08, 0x75, 0x73, 0x65, 0x72, 0x49,
	0x6e, 0x66, 0x6f, 0x22, 0x26, 0x0a, 0x12, 0x42, 0x61, 0x74, 0x63, 0x68, 0x52, 0x65, 0x6d, 0x6f,
	0x76, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x10, 0x0a, 0x03, 0x49, 0x44, 0x73,
	0x18, 0x02, 0x20, 0x03, 0x28, 0x0d, 0x52, 0x03, 0x49, 0x44, 0x73, 0x22, 0x5d, 0x0a, 0x17, 0x46,
	0x69, 0x6e, 0x64, 0x52, 0x75, 0x6c, 0x65, 0x42, 0x79, 0x55, 0x73, 0x65, 0x72, 0x49, 0x64, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x16, 0x0a, 0x06, 0x64, 0x6f, 0x6d, 0x61, 0x69, 0x6e,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x64, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x12, 0x12,
	0x0a, 0x04, 0x75, 0x72, 0x6c, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x09, 0x52, 0x04, 0x75, 0x72,
	0x6c, 0x73, 0x12, 0x16, 0x0a, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x04, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x22, 0x44, 0x0a, 0x18, 0x46, 0x69,
	0x6e, 0x64, 0x52, 0x75, 0x6c, 0x65, 0x42, 0x79, 0x55, 0x73, 0x65, 0x72, 0x49, 0x64, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x28, 0x0a, 0x05, 0x72, 0x75, 0x6c, 0x65, 0x73, 0x18,
	0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e,
	0x2e, 0x52, 0x75, 0x6c, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x05, 0x72, 0x75, 0x6c, 0x65, 0x73,
	0x22, 0x66, 0x0a, 0x16, 0x46, 0x69, 0x6e, 0x64, 0x55, 0x73, 0x65, 0x72, 0x73, 0x42, 0x79, 0x52,
	0x75, 0x6c, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x16, 0x0a, 0x06, 0x44, 0x6f,
	0x6d, 0x61, 0x69, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x64, 0x6f, 0x6d, 0x61,
	0x69, 0x6e, 0x12, 0x10, 0x0a, 0x03, 0x55, 0x72, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x03, 0x75, 0x72, 0x6c, 0x12, 0x22, 0x0a, 0x0c, 0x44, 0x65, 0x70, 0x61, 0x72, 0x74, 0x6d, 0x65,
	0x6e, 0x74, 0x49, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0c, 0x64, 0x65, 0x70, 0x61,
	0x72, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x22, 0x55, 0x0a, 0x15, 0x50, 0x6f, 0x73, 0x69,
	0x74, 0x69, 0x6f, 0x6e, 0x55, 0x73, 0x65, 0x72, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x12, 0x3c, 0x0a, 0x0d, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x55, 0x73, 0x65,
	0x72, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x70, 0x6f, 0x73, 0x69, 0x74,
	0x69, 0x6f, 0x6e, 0x2e, 0x50, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x55, 0x73, 0x65, 0x72,
	0x52, 0x0d, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x55, 0x73, 0x65, 0x72, 0x73, 0x22,
	0x45, 0x0a, 0x15, 0x50, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x42, 0x79, 0x55, 0x72, 0x6c,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x2c, 0x0a, 0x04, 0x4c, 0x69, 0x73, 0x74,
	0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f,
	0x6e, 0x2e, 0x42, 0x61, 0x73, 0x65, 0x50, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x06,
	0x64, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x22, 0x64, 0x0a, 0x14, 0x50, 0x6f, 0x73, 0x69, 0x74, 0x69,
	0x6f, 0x6e, 0x42, 0x79, 0x55, 0x72, 0x6c, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x16,
	0x0a, 0x06, 0x44, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06,
	0x64, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x12, 0x22, 0x0a, 0x0c, 0x44, 0x65, 0x70, 0x61, 0x72, 0x74,
	0x6d, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0c, 0x64, 0x65,
	0x70, 0x61, 0x72, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x10, 0x0a, 0x03, 0x55, 0x72,
	0x6c, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x75, 0x72, 0x6c, 0x22, 0x69, 0x0a, 0x15,
	0x50, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x42, 0x79, 0x55, 0x72, 0x6c, 0x73, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x16, 0x0a, 0x06, 0x44, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x64, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x12, 0x24, 0x0a,
	0x0d, 0x64, 0x65, 0x70, 0x61, 0x72, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x73, 0x18, 0x02,
	0x20, 0x03, 0x28, 0x04, 0x52, 0x0d, 0x64, 0x65, 0x70, 0x61, 0x72, 0x74, 0x6d, 0x65, 0x6e, 0x74,
	0x49, 0x64, 0x73, 0x12, 0x12, 0x0a, 0x04, 0x75, 0x72, 0x6c, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28,
	0x09, 0x52, 0x04, 0x75, 0x72, 0x6c, 0x73, 0x22, 0x7a, 0x0a, 0x17, 0x53, 0x65, 0x61, 0x72, 0x63,
	0x68, 0x44, 0x65, 0x70, 0x61, 0x72, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x12, 0x16, 0x0a, 0x06, 0x44, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x06, 0x64, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x12, 0x25, 0x0a, 0x06, 0x55, 0x73,
	0x65, 0x72, 0x49, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x04, 0x42, 0x0d, 0xe2, 0xdf, 0x1f, 0x09,
	0x2a, 0x05, 0x37, 0x30, 0x30, 0x31, 0x30, 0x58, 0x01, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49,
	0x64, 0x12, 0x20, 0x0a, 0x04, 0x55, 0x72, 0x6c, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x09, 0x42,
	0x0d, 0xe2, 0xdf, 0x1f, 0x09, 0x2a, 0x05, 0x37, 0x30, 0x30, 0x31, 0x34, 0x58, 0x01, 0x52, 0x03,
	0x75, 0x72, 0x6c, 0x22, 0x70, 0x0a, 0x06, 0x50, 0x44, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x0e, 0x0a,
	0x02, 0x49, 0x44, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x02, 0x49, 0x44, 0x12, 0x12, 0x0a,
	0x04, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x4e, 0x61, 0x6d,
	0x65, 0x12, 0x1e, 0x0a, 0x0a, 0x50, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x44, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0a, 0x50, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x49,
	0x44, 0x12, 0x22, 0x0a, 0x0c, 0x50, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x4e, 0x61, 0x6d,
	0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x50, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f,
	0x6e, 0x4e, 0x61, 0x6d, 0x65, 0x22, 0x40, 0x0a, 0x18, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x44,
	0x65, 0x70, 0x61, 0x72, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x12, 0x24, 0x0a, 0x04, 0x4c, 0x69, 0x73, 0x74, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x10, 0x2e, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x50, 0x44, 0x49, 0x6e, 0x66,
	0x6f, 0x52, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x22, 0xb9, 0x01, 0x0a, 0x12, 0x44, 0x6f, 0x49, 0x48,
	0x61, 0x76, 0x61, 0x41, 0x75, 0x74, 0x68, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x16,
	0x0a, 0x06, 0x44, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06,
	0x64, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x12, 0x25, 0x0a, 0x06, 0x55, 0x73, 0x65, 0x72, 0x49, 0x64,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x04, 0x42, 0x0d, 0xe2, 0xdf, 0x1f, 0x09, 0x2a, 0x05, 0x37, 0x30,
	0x30, 0x31, 0x30, 0x58, 0x01, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x12, 0x1f, 0x0a,
	0x03, 0x55, 0x72, 0x6c, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0d, 0xe2, 0xdf, 0x1f, 0x09,
	0x2a, 0x05, 0x37, 0x30, 0x30, 0x31, 0x34, 0x58, 0x01, 0x52, 0x03, 0x75, 0x72, 0x6c, 0x12, 0x2f,
	0x0a, 0x0c, 0x44, 0x65, 0x70, 0x61, 0x72, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x49, 0x44, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x04, 0x42, 0x0d, 0xe2, 0xdf, 0x1f, 0x09, 0x2a, 0x05, 0x37, 0x30, 0x30, 0x31,
	0x34, 0x58, 0x01, 0x52, 0x0a, 0x64, 0x65, 0x70, 0x61, 0x72, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x12,
	0x12, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x74,
	0x79, 0x70, 0x65, 0x22, 0x9c, 0x01, 0x0a, 0x16, 0x44, 0x6f, 0x48, 0x61, 0x76, 0x61, 0x41, 0x75,
	0x74, 0x68, 0x42, 0x61, 0x74, 0x63, 0x68, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x16,
	0x0a, 0x06, 0x64, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06,
	0x64, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x12, 0x18, 0x0a, 0x07, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64,
	0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0d, 0x52, 0x07, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x73,
	0x12, 0x1f, 0x0a, 0x03, 0x75, 0x72, 0x6c, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0d, 0xe2,
	0xdf, 0x1f, 0x09, 0x2a, 0x05, 0x37, 0x30, 0x30, 0x31, 0x34, 0x58, 0x01, 0x52, 0x03, 0x75, 0x72,
	0x6c, 0x12, 0x2f, 0x0a, 0x0c, 0x64, 0x65, 0x70, 0x61, 0x72, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x49,
	0x44, 0x18, 0x04, 0x20, 0x01, 0x28, 0x04, 0x42, 0x0d, 0xe2, 0xdf, 0x1f, 0x09, 0x2a, 0x05, 0x37,
	0x30, 0x30, 0x31, 0x34, 0x58, 0x01, 0x52, 0x0a, 0x64, 0x65, 0x70, 0x61, 0x72, 0x74, 0x6d, 0x65,
	0x6e, 0x74, 0x22, 0x29, 0x0a, 0x13, 0x44, 0x6f, 0x49, 0x48, 0x61, 0x76, 0x61, 0x41, 0x75, 0x74,
	0x68, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x48, 0x61, 0x76,
	0x61, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x04, 0x48, 0x61, 0x76, 0x61, 0x22, 0x4d, 0x0a,
	0x17, 0x44, 0x6f, 0x48, 0x61, 0x76, 0x61, 0x41, 0x75, 0x74, 0x68, 0x42, 0x61, 0x74, 0x63, 0x68,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x68, 0x61, 0x76, 0x61,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x04, 0x68, 0x61, 0x76, 0x61, 0x12, 0x1e, 0x0a, 0x0a,
	0x61, 0x6c, 0x6c, 0x4e, 0x6f, 0x74, 0x48, 0x61, 0x76, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08,
	0x52, 0x0a, 0x61, 0x6c, 0x6c, 0x4e, 0x6f, 0x74, 0x48, 0x61, 0x76, 0x65, 0x22, 0x68, 0x0a, 0x16,
	0x46, 0x69, 0x6e, 0x64, 0x4f, 0x6e, 0x65, 0x46, 0x72, 0x6f, 0x6d, 0x4e, 0x61, 0x6d, 0x65, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x16, 0x0a, 0x06, 0x44, 0x6f, 0x6d, 0x61, 0x69, 0x6e,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x64, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x12, 0x22,
	0x0a, 0x0c, 0x44, 0x65, 0x70, 0x61, 0x72, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x04, 0x52, 0x0c, 0x64, 0x65, 0x70, 0x61, 0x72, 0x74, 0x6d, 0x65, 0x6e, 0x74,
	0x49, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x22, 0x42, 0x0a, 0x10, 0x4c, 0x69, 0x73, 0x74, 0x42, 0x79,
	0x44, 0x69, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x16, 0x0a, 0x06, 0x44, 0x6f,
	0x6d, 0x61, 0x69, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x64, 0x6f, 0x6d, 0x61,
	0x69, 0x6e, 0x12, 0x16, 0x0a, 0x06, 0x55, 0x73, 0x65, 0x72, 0x49, 0x64, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x04, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x22, 0xa2, 0x01, 0x0a, 0x0c, 0x42,
	0x61, 0x73, 0x65, 0x50, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x0e, 0x0a, 0x02, 0x49,
	0x44, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x02, 0x49, 0x44, 0x12, 0x12, 0x0a, 0x04, 0x4e,
	0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12,
	0x22, 0x0a, 0x0c, 0x44, 0x65, 0x70, 0x61, 0x72, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x18,
	0x0a, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0c, 0x64, 0x65, 0x70, 0x61, 0x72, 0x74, 0x6d, 0x65, 0x6e,
	0x74, 0x49, 0x64, 0x12, 0x22, 0x0a, 0x0c, 0x50, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x43,
	0x6f, 0x64, 0x65, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x70, 0x6f, 0x73, 0x69, 0x74,
	0x69, 0x6f, 0x6e, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x26, 0x0a, 0x0e, 0x44, 0x65, 0x70, 0x61, 0x72,
	0x74, 0x6d, 0x65, 0x6e, 0x74, 0x43, 0x6f, 0x64, 0x65, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0e, 0x64, 0x65, 0x70, 0x61, 0x72, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x43, 0x6f, 0x64, 0x65, 0x22,
	0x49, 0x0a, 0x11, 0x4c, 0x69, 0x73, 0x74, 0x42, 0x79, 0x44, 0x69, 0x64, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x12, 0x34, 0x0a, 0x09, 0x50, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e,
	0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x69,
	0x6f, 0x6e, 0x2e, 0x42, 0x61, 0x73, 0x65, 0x50, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x52,
	0x09, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x22, 0x97, 0x01, 0x0a, 0x11, 0x42,
	0x69, 0x6e, 0x64, 0x55, 0x73, 0x65, 0x72, 0x56, 0x32, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x12, 0x16, 0x0a, 0x06, 0x44, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x06, 0x64, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x12, 0x2e, 0x0a, 0x08, 0x75, 0x73, 0x65, 0x72,
	0x49, 0x6e, 0x66, 0x6f, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x70, 0x6f, 0x73,
	0x69, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x08,
	0x75, 0x73, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x3a, 0x0a, 0x0c, 0x64, 0x65, 0x70, 0x50,
	0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x05, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x15,
	0x2e, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x44, 0x65, 0x70, 0x50, 0x6f, 0x73,
	0x69, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x0d, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x55,
	0x73, 0x65, 0x72, 0x73, 0x22, 0x81, 0x01, 0x0a, 0x0b, 0x44, 0x65, 0x70, 0x50, 0x6f, 0x73, 0x69,
	0x74, 0x69, 0x6f, 0x6e, 0x12, 0x0e, 0x0a, 0x02, 0x49, 0x44, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d,
	0x52, 0x02, 0x49, 0x44, 0x12, 0x32, 0x0a, 0x09, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e,
	0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x69,
	0x6f, 0x6e, 0x2e, 0x50, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x56, 0x32, 0x52, 0x09, 0x70,
	0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x1a, 0x0a, 0x08,
	0x69, 0x73, 0x4c, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x04, 0x20, 0x01, 0x28, 0x08, 0x52, 0x08,
	0x69, 0x73, 0x4c, 0x65, 0x61, 0x64, 0x65, 0x72, 0x22, 0x46, 0x0a, 0x0a, 0x50, 0x6f, 0x73, 0x69,
	0x74, 0x69, 0x6f, 0x6e, 0x56, 0x32, 0x12, 0x0e, 0x0a, 0x02, 0x49, 0x44, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0d, 0x52, 0x02, 0x49, 0x44, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x63, 0x6f,
	0x6c, 0x6f, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x63, 0x6f, 0x6c, 0x6f, 0x72,
	0x22, 0x56, 0x0a, 0x08, 0x55, 0x73, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x16, 0x0a, 0x06,
	0x55, 0x73, 0x65, 0x72, 0x49, 0x44, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x06, 0x75, 0x73,
	0x65, 0x72, 0x49, 0x44, 0x12, 0x1a, 0x0a, 0x08, 0x55, 0x73, 0x65, 0x72, 0x4e, 0x61, 0x6d, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x75, 0x73, 0x65, 0x72, 0x4e, 0x61, 0x6d, 0x65,
	0x12, 0x16, 0x0a, 0x06, 0x41, 0x76, 0x61, 0x74, 0x61, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x06, 0x61, 0x76, 0x61, 0x74, 0x61, 0x72, 0x22, 0xb3, 0x01, 0x0a, 0x0f, 0x42, 0x69, 0x6e,
	0x64, 0x55, 0x73, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x16, 0x0a, 0x06,
	0x44, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x64, 0x6f,
	0x6d, 0x61, 0x69, 0x6e, 0x12, 0x16, 0x0a, 0x06, 0x55, 0x73, 0x65, 0x72, 0x49, 0x44, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x04, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x44, 0x12, 0x1a, 0x0a, 0x08,
	0x55, 0x73, 0x65, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08,
	0x55, 0x73, 0x65, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x41, 0x76, 0x61, 0x74,
	0x61, 0x72, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x41, 0x76, 0x61, 0x74, 0x61, 0x72,
	0x12, 0x3c, 0x0a, 0x0d, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x55, 0x73, 0x65, 0x72,
	0x73, 0x18, 0x05, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x69,
	0x6f, 0x6e, 0x2e, 0x50, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x55, 0x73, 0x65, 0x72, 0x52,
	0x0d, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x55, 0x73, 0x65, 0x72, 0x73, 0x22, 0xb0,
	0x01, 0x0a, 0x0e, 0x41, 0x64, 0x64, 0x55, 0x73, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x12, 0x16, 0x0a, 0x06, 0x44, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x06, 0x64, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x12, 0x16, 0x0a, 0x06, 0x55, 0x73, 0x65,
	0x72, 0x49, 0x44, 0x18, 0x02, 0x20, 0x01, 0x28, 0x04, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49,
	0x44, 0x12, 0x1a, 0x0a, 0x08, 0x55, 0x73, 0x65, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x08, 0x55, 0x73, 0x65, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x16, 0x0a,
	0x06, 0x41, 0x76, 0x61, 0x74, 0x61, 0x72, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x41,
	0x76, 0x61, 0x74, 0x61, 0x72, 0x12, 0x3a, 0x0a, 0x0c, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f,
	0x6e, 0x55, 0x73, 0x65, 0x72, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x70, 0x6f,
	0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x50, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x55,
	0x73, 0x65, 0x72, 0x52, 0x0c, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x55, 0x73, 0x65,
	0x72, 0x22, 0x12, 0x0a, 0x10, 0x42, 0x69, 0x6e, 0x64, 0x55, 0x73, 0x65, 0x72, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x10, 0x0a, 0x0e, 0x43, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0xaa, 0x02, 0x0a, 0x0c, 0x50, 0x6f, 0x73, 0x69,
	0x74, 0x69, 0x6f, 0x6e, 0x55, 0x73, 0x65, 0x72, 0x12, 0x29, 0x0a, 0x0a, 0x50, 0x6f, 0x73, 0x69,
	0x74, 0x69, 0x6f, 0x6e, 0x49, 0x44, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x42, 0x0d, 0xe2, 0xdf,
	0x1f, 0x09, 0x2a, 0x05, 0x37, 0x30, 0x30, 0x30, 0x31, 0x58, 0x01, 0x52, 0x06, 0x64, 0x6f, 0x6d,
	0x61, 0x69, 0x6e, 0x12, 0x22, 0x0a, 0x0c, 0x44, 0x65, 0x70, 0x61, 0x72, 0x74, 0x6d, 0x65, 0x6e,
	0x74, 0x49, 0x44, 0x18, 0x02, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0c, 0x64, 0x65, 0x70, 0x61, 0x72,
	0x74, 0x6d, 0x65, 0x6e, 0x74, 0x49, 0x44, 0x12, 0x26, 0x0a, 0x0e, 0x44, 0x65, 0x70, 0x61, 0x72,
	0x74, 0x6d, 0x65, 0x6e, 0x74, 0x43, 0x6f, 0x64, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0e, 0x64, 0x65, 0x70, 0x61, 0x72, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x43, 0x6f, 0x64, 0x65, 0x12,
	0x22, 0x0a, 0x0c, 0x50, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x6f, 0x64, 0x65, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x43,
	0x6f, 0x64, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x55, 0x73, 0x65, 0x72, 0x49, 0x64, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x04, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x12, 0x1a, 0x0a, 0x08, 0x55,
	0x73, 0x65, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x75,
	0x73, 0x65, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x26, 0x0a, 0x0e, 0x44, 0x65, 0x70, 0x61, 0x72,
	0x74, 0x6d, 0x65, 0x6e, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0e, 0x64, 0x65, 0x70, 0x61, 0x72, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x12,
	0x23, 0x0a, 0x0c, 0x50, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x4e, 0x61, 0x6d, 0x65, 0x18,
	0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x70, 0x50, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e,
	0x4e, 0x61, 0x6d, 0x65, 0x22, 0x4a, 0x0a, 0x11, 0x52, 0x65, 0x6d, 0x6f, 0x76, 0x65, 0x55, 0x73,
	0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x25, 0x0a, 0x06, 0x44, 0x6f, 0x6d,
	0x61, 0x69, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0d, 0xe2, 0xdf, 0x1f, 0x09, 0x2a,
	0x05, 0x37, 0x30, 0x30, 0x30, 0x31, 0x58, 0x01, 0x52, 0x06, 0x64, 0x6f, 0x6d, 0x61, 0x69, 0x6e,
	0x12, 0x0e, 0x0a, 0x02, 0x49, 0x44, 0x18, 0x02, 0x20, 0x01, 0x28, 0x04, 0x52, 0x02, 0x49, 0x44,
	0x22, 0x14, 0x0a, 0x12, 0x52, 0x65, 0x6d, 0x6f, 0x76, 0x65, 0x55, 0x73, 0x65, 0x72, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x20, 0x0a, 0x0e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x0e, 0x0a, 0x02, 0x49, 0x44, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x04, 0x52, 0x02, 0x49, 0x44, 0x22, 0x46, 0x0a, 0x0d, 0x52, 0x65, 0x6d, 0x6f,
	0x76, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x0e, 0x0a, 0x02, 0x49, 0x44, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x02, 0x49, 0x44, 0x12, 0x25, 0x0a, 0x06, 0x44, 0x6f, 0x6d,
	0x61, 0x69, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0d, 0xe2, 0xdf, 0x1f, 0x09, 0x2a,
	0x05, 0x37, 0x30, 0x30, 0x30, 0x31, 0x58, 0x01, 0x52, 0x06, 0x64, 0x6f, 0x6d, 0x61, 0x69, 0x6e,
	0x22, 0x10, 0x0a, 0x0e, 0x52, 0x65, 0x6d, 0x6f, 0x76, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x22, 0x46, 0x0a, 0x0d, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x12, 0x0e, 0x0a, 0x02, 0x49, 0x44, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52,
	0x02, 0x49, 0x44, 0x12, 0x25, 0x0a, 0x06, 0x44, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x42, 0x0d, 0xe2, 0xdf, 0x1f, 0x09, 0x2a, 0x05, 0x37, 0x30, 0x30, 0x30, 0x31,
	0x58, 0x01, 0x52, 0x06, 0x64, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x22, 0xf4, 0x03, 0x0a, 0x0e, 0x44,
	0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x0e, 0x0a,
	0x02, 0x49, 0x44, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x02, 0x49, 0x44, 0x12, 0x12, 0x0a,
	0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d,
	0x65, 0x12, 0x16, 0x0a, 0x06, 0x72, 0x65, 0x6d, 0x61, 0x72, 0x6b, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x06, 0x72, 0x65, 0x6d, 0x61, 0x72, 0x6b, 0x12, 0x1c, 0x0a, 0x09, 0x63, 0x72, 0x65,
	0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x63, 0x72,
	0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x1c, 0x0a, 0x09, 0x75, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x64, 0x41, 0x74, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x75, 0x70, 0x64, 0x61,
	0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x28, 0x0a, 0x05, 0x72, 0x75, 0x6c, 0x65, 0x73, 0x18, 0x09,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x2e,
	0x52, 0x75, 0x6c, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x05, 0x72, 0x75, 0x6c, 0x65, 0x73, 0x12,
	0x22, 0x0a, 0x0c, 0x64, 0x65, 0x70, 0x61, 0x72, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x49, 0x44, 0x18,
	0x0a, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0c, 0x64, 0x65, 0x70, 0x61, 0x72, 0x74, 0x6d, 0x65, 0x6e,
	0x74, 0x49, 0x44, 0x12, 0x22, 0x0a, 0x0c, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x43,
	0x6f, 0x64, 0x65, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x70, 0x6f, 0x73, 0x69, 0x74,
	0x69, 0x6f, 0x6e, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x26, 0x0a, 0x0e, 0x64, 0x65, 0x70, 0x61, 0x72,
	0x74, 0x6d, 0x65, 0x6e, 0x74, 0x43, 0x6f, 0x64, 0x65, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0e, 0x64, 0x65, 0x70, 0x61, 0x72, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x43, 0x6f, 0x64, 0x65, 0x12,
	0x3e, 0x0a, 0x10, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x72, 0x65, 0x65, 0x52,
	0x75, 0x6c, 0x65, 0x18, 0x0d, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x70, 0x6f, 0x73, 0x69,
	0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x52, 0x75, 0x6c, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x10, 0x70,
	0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x72, 0x65, 0x65, 0x52, 0x75, 0x6c, 0x65, 0x12,
	0x14, 0x0a, 0x05, 0x63, 0x6f, 0x6c, 0x6f, 0x72, 0x18, 0x11, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05,
	0x63, 0x6f, 0x6c, 0x6f, 0x72, 0x12, 0x22, 0x0a, 0x0c, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f,
	0x72, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x12, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x6f, 0x70, 0x65,
	0x72, 0x61, 0x74, 0x6f, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1c, 0x0a, 0x09, 0x6d, 0x65, 0x6e,
	0x75, 0x41, 0x75, 0x74, 0x68, 0x73, 0x18, 0x13, 0x20, 0x03, 0x28, 0x09, 0x52, 0x09, 0x6d, 0x65,
	0x6e, 0x75, 0x41, 0x75, 0x74, 0x68, 0x73, 0x12, 0x1c, 0x0a, 0x09, 0x6c, 0x69, 0x6e, 0x6b, 0x65,
	0x72, 0x4e, 0x75, 0x6d, 0x18, 0x14, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x09, 0x6c, 0x69, 0x6e, 0x6b,
	0x65, 0x72, 0x4e, 0x75, 0x6d, 0x12, 0x1a, 0x0a, 0x08, 0x69, 0x73, 0x4c, 0x65, 0x61, 0x64, 0x65,
	0x72, 0x18, 0x16, 0x20, 0x01, 0x28, 0x08, 0x52, 0x08, 0x69, 0x73, 0x4c, 0x65, 0x61, 0x64, 0x65,
	0x72, 0x22, 0x82, 0x05, 0x0a, 0x0d, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x12, 0x0e, 0x0a, 0x02, 0x49, 0x44, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52,
	0x02, 0x49, 0x44, 0x12, 0x12, 0x0a, 0x04, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x52, 0x65, 0x6d, 0x61, 0x72,
	0x6b, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x72, 0x65, 0x6d, 0x61, 0x72, 0x6b, 0x12,
	0x1b, 0x0a, 0x09, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x18, 0x06, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x08, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x41, 0x74, 0x12, 0x1b, 0x0a, 0x09,
	0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x08, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x41, 0x74, 0x12, 0x16, 0x0a, 0x06, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x18, 0x08, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x12, 0x28, 0x0a, 0x05, 0x52, 0x75, 0x6c, 0x65, 0x73, 0x18, 0x09, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x12, 0x2e, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x52, 0x75, 0x6c, 0x65,
	0x49, 0x6e, 0x66, 0x6f, 0x52, 0x05, 0x72, 0x75, 0x6c, 0x65, 0x73, 0x12, 0x22, 0x0a, 0x0c, 0x44,
	0x65, 0x70, 0x61, 0x72, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x49, 0x44, 0x18, 0x0a, 0x20, 0x01, 0x28,
	0x04, 0x52, 0x0c, 0x64, 0x65, 0x70, 0x61, 0x72, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x49, 0x44, 0x12,
	0x22, 0x0a, 0x0c, 0x50, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x6f, 0x64, 0x65, 0x18,
	0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x43,
	0x6f, 0x64, 0x65, 0x12, 0x26, 0x0a, 0x0e, 0x44, 0x65, 0x70, 0x61, 0x72, 0x74, 0x6d, 0x65, 0x6e,
	0x74, 0x43, 0x6f, 0x64, 0x65, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x64, 0x65, 0x70,
	0x61, 0x72, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x3f, 0x0a, 0x10, 0x50,
	0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x72, 0x65, 0x65, 0x52, 0x75, 0x6c, 0x65, 0x18,
	0x0d, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e,
	0x2e, 0x52, 0x75, 0x6c, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x11, 0x70, 0x6f, 0x73, 0x69, 0x74,
	0x69, 0x6f, 0x6e, 0x54, 0x72, 0x65, 0x65, 0x52, 0x75, 0x6c, 0x65, 0x73, 0x12, 0x16, 0x0a, 0x06,
	0x44, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x64, 0x6f,
	0x6d, 0x61, 0x69, 0x6e, 0x12, 0x10, 0x0a, 0x03, 0x4e, 0x75, 0x6d, 0x18, 0x0f, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x03, 0x6e, 0x75, 0x6d, 0x12, 0x26, 0x0a, 0x0e, 0x44, 0x65, 0x70, 0x61, 0x72, 0x74,
	0x6d, 0x65, 0x6e, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x10, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e,
	0x44, 0x65, 0x70, 0x61, 0x72, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x14,
	0x0a, 0x05, 0x63, 0x6f, 0x6c, 0x6f, 0x72, 0x18, 0x11, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x63,
	0x6f, 0x6c, 0x6f, 0x72, 0x12, 0x22, 0x0a, 0x0c, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72,
	0x4e, 0x61, 0x6d, 0x65, 0x18, 0x12, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x6f, 0x70, 0x65, 0x72,
	0x61, 0x74, 0x6f, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1c, 0x0a, 0x09, 0x6d, 0x65, 0x6e, 0x75,
	0x41, 0x75, 0x74, 0x68, 0x73, 0x18, 0x13, 0x20, 0x03, 0x28, 0x09, 0x52, 0x09, 0x6d, 0x65, 0x6e,
	0x75, 0x41, 0x75, 0x74, 0x68, 0x73, 0x12, 0x1c, 0x0a, 0x09, 0x6c, 0x69, 0x6e, 0x6b, 0x65, 0x72,
	0x4e, 0x75, 0x6d, 0x18, 0x14, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x09, 0x6c, 0x69, 0x6e, 0x6b, 0x65,
	0x72, 0x4e, 0x75, 0x6d, 0x12, 0x24, 0x0a, 0x0d, 0x64, 0x65, 0x70, 0x61, 0x72, 0x74, 0x6d, 0x65,
	0x6e, 0x74, 0x49, 0x44, 0x73, 0x18, 0x15, 0x20, 0x03, 0x28, 0x0d, 0x52, 0x0d, 0x64, 0x65, 0x70,
	0x61, 0x72, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x49, 0x44, 0x73, 0x12, 0x1a, 0x0a, 0x08, 0x69, 0x73,
	0x4c, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x16, 0x20, 0x01, 0x28, 0x08, 0x52, 0x08, 0x69, 0x73,
	0x4c, 0x65, 0x61, 0x64, 0x65, 0x72, 0x22, 0xfd, 0x01, 0x0a, 0x14, 0x42, 0x61, 0x74, 0x63, 0x68,
	0x41, 0x64, 0x64, 0x52, 0x75, 0x6c, 0x65, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12,
	0x4a, 0x0a, 0x03, 0x69, 0x64, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0d, 0x42, 0x35, 0xe2, 0xdf,
	0x1f, 0x31, 0x10, 0x00, 0x2a, 0x29, 0xe8, 0xaf, 0xb7, 0xe9, 0x80, 0x89, 0xe6, 0x8b, 0xa9, 0xe5,
	0xb2, 0x97, 0xe4, 0xbd, 0x8d, 0xef, 0xbc, 0x8c, 0xe5, 0xb2, 0x97, 0xe4, 0xbd, 0x8d, 0xe6, 0x95,
	0xb0, 0xe4, 0xb8, 0x8d, 0xe5, 0x8f, 0xaf, 0xe8, 0xb6, 0x85, 0xe8, 0xbf, 0x87, 0x35, 0x30, 0x60,
	0x01, 0x68, 0x32, 0x52, 0x06, 0x64, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x12, 0x75, 0x0a, 0x07, 0x72,
	0x75, 0x6c, 0x65, 0x49, 0x64, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0d, 0x42, 0x5b, 0xe2, 0xdf,
	0x1f, 0x57, 0x10, 0x00, 0x2a, 0x4f, 0xe9, 0x80, 0x89, 0xe4, 0xb8, 0xad, 0xe7, 0x9a, 0x84, 0xe6,
	0x9d, 0x83, 0xe9, 0x99, 0x90, 0xe4, 0xb8, 0x8d, 0xe7, 0xac, 0xa6, 0xe5, 0x90, 0x88, 0xe8, 0xa7,
	0x84, 0xe8, 0x8c, 0x83, 0x28, 0xe6, 0x95, 0xb0, 0xe9, 0x87, 0x8f, 0xe5, 0x9c, 0xa8, 0x31, 0x2d,
	0x35, 0x30, 0xe4, 0xb9, 0x8b, 0xe9, 0x97, 0xb4, 0x29, 0x2c, 0xe5, 0xb9, 0xb6, 0xe4, 0xb8, 0x94,
	0xe5, 0x85, 0x83, 0xe7, 0xb4, 0xa0, 0xe4, 0xb8, 0x8d, 0xe8, 0x83, 0xbd, 0xe6, 0x9c, 0x89, 0xe7,
	0xa9, 0xba, 0xe5, 0x80, 0xbc, 0x60, 0x01, 0x68, 0x32, 0x52, 0x07, 0x72, 0x75, 0x6c, 0x65, 0x49,
	0x64, 0x73, 0x12, 0x22, 0x0a, 0x0c, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x4e, 0x61,
	0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74,
	0x6f, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x22, 0x2c, 0x0a, 0x06, 0x4c, 0x65, 0x61, 0x64, 0x65, 0x72,
	0x12, 0x0e, 0x0a, 0x02, 0x49, 0x44, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x49, 0x44,
	0x12, 0x12, 0x0a, 0x04, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04,
	0x6e, 0x61, 0x6d, 0x65, 0x22, 0xd2, 0x03, 0x0a, 0x0b, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x12, 0x22, 0x0a, 0x0c, 0x44, 0x65, 0x70, 0x61, 0x72, 0x74, 0x6d, 0x65,
	0x6e, 0x74, 0x49, 0x44, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0c, 0x64, 0x65, 0x70, 0x61,
	0x72, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x49, 0x44, 0x12, 0x1a, 0x0a, 0x08, 0x50, 0x61, 0x67, 0x65,
	0x53, 0x69, 0x7a, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x04, 0x52, 0x08, 0x70, 0x61, 0x67, 0x65,
	0x53, 0x69, 0x7a, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x50, 0x61, 0x67, 0x65, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x04, 0x52, 0x04, 0x70, 0x61, 0x67, 0x65, 0x12, 0x25, 0x0a, 0x06, 0x44, 0x6f, 0x6d, 0x61,
	0x69, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0d, 0xe2, 0xdf, 0x1f, 0x09, 0x2a, 0x05,
	0x37, 0x30, 0x30, 0x30, 0x31, 0x58, 0x01, 0x52, 0x06, 0x64, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x12,
	0x10, 0x0a, 0x03, 0x4b, 0x65, 0x79, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65,
	0x79, 0x12, 0x0e, 0x0a, 0x02, 0x49, 0x44, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x49,
	0x44, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x6d, 0x65, 0x6e, 0x75, 0x41, 0x75, 0x74,
	0x68, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x6d, 0x65, 0x6e, 0x75, 0x41, 0x75, 0x74,
	0x68, 0x12, 0x22, 0x0a, 0x0c, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x4e, 0x61, 0x6d,
	0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f,
	0x72, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x24, 0x0a, 0x0d, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x53,
	0x74, 0x61, 0x72, 0x74, 0x41, 0x74, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x75, 0x70,
	0x64, 0x61, 0x74, 0x65, 0x53, 0x74, 0x61, 0x72, 0x74, 0x41, 0x74, 0x12, 0x20, 0x0a, 0x0b, 0x75,
	0x70, 0x64, 0x61, 0x74, 0x65, 0x45, 0x6e, 0x64, 0x41, 0x74, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0b, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x45, 0x6e, 0x64, 0x41, 0x74, 0x12, 0x28, 0x0a,
	0x0f, 0x6e, 0x6f, 0x74, 0x44, 0x65, 0x70, 0x61, 0x72, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x49, 0x44,
	0x18, 0x0d, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0f, 0x6e, 0x6f, 0x74, 0x44, 0x65, 0x70, 0x61, 0x72,
	0x74, 0x6d, 0x65, 0x6e, 0x74, 0x49, 0x44, 0x12, 0x1c, 0x0a, 0x09, 0x73, 0x6f, 0x72, 0x74, 0x46,
	0x69, 0x65, 0x6c, 0x64, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x73, 0x6f, 0x72, 0x74,
	0x46, 0x69, 0x65, 0x6c, 0x64, 0x12, 0x1c, 0x0a, 0x09, 0x73, 0x6f, 0x72, 0x74, 0x4f, 0x72, 0x64,
	0x65, 0x72, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x73, 0x6f, 0x72, 0x74, 0x4f, 0x72,
	0x64, 0x65, 0x72, 0x12, 0x24, 0x0a, 0x0d, 0x64, 0x65, 0x70, 0x61, 0x72, 0x74, 0x6d, 0x65, 0x6e,
	0x74, 0x49, 0x64, 0x73, 0x18, 0x10, 0x20, 0x03, 0x28, 0x0d, 0x52, 0x0d, 0x64, 0x65, 0x70, 0x61,
	0x72, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x73, 0x22, 0x4a, 0x0a, 0x04, 0x52, 0x75, 0x6c,
	0x65, 0x12, 0x0e, 0x0a, 0x02, 0x49, 0x44, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x02, 0x49,
	0x44, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x1e, 0x0a, 0x0a, 0x52, 0x75, 0x6c, 0x65, 0x44, 0x61, 0x74,
	0x61, 0x49, 0x44, 0x18, 0x03, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0a, 0x52, 0x75, 0x6c, 0x65, 0x44,
	0x61, 0x74, 0x61, 0x49, 0x44, 0x22, 0xe1, 0x04, 0x0a, 0x08, 0x52, 0x75, 0x6c, 0x65, 0x49, 0x6e,
	0x66, 0x6f, 0x12, 0x0e, 0x0a, 0x02, 0x49, 0x44, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x02,
	0x49, 0x44, 0x12, 0x12, 0x0a, 0x04, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x04, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x55, 0x72, 0x6c, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x03, 0x55, 0x72, 0x6c, 0x12, 0x16, 0x0a, 0x06, 0x4d, 0x65, 0x74, 0x68,
	0x6f, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x4d, 0x65, 0x74, 0x68, 0x6f, 0x64,
	0x12, 0x1e, 0x0a, 0x0a, 0x52, 0x75, 0x6c, 0x65, 0x44, 0x61, 0x74, 0x61, 0x49, 0x44, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x04, 0x52, 0x0a, 0x52, 0x75, 0x6c, 0x65, 0x44, 0x61, 0x74, 0x61, 0x49, 0x44,
	0x12, 0x22, 0x0a, 0x0c, 0x52, 0x75, 0x6c, 0x65, 0x44, 0x61, 0x74, 0x61, 0x4e, 0x61, 0x6d, 0x65,
	0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x52, 0x75, 0x6c, 0x65, 0x44, 0x61, 0x74, 0x61,
	0x4e, 0x61, 0x6d, 0x65, 0x12, 0x24, 0x0a, 0x0d, 0x52, 0x75, 0x6c, 0x65, 0x44, 0x61, 0x74, 0x61,
	0x46, 0x69, 0x65, 0x6c, 0x64, 0x18, 0x07, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0d, 0x52, 0x75, 0x6c,
	0x65, 0x44, 0x61, 0x74, 0x61, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x12, 0x24, 0x0a, 0x03, 0x53, 0x6f,
	0x6e, 0x18, 0x08, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x69,
	0x6f, 0x6e, 0x2e, 0x52, 0x75, 0x6c, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x03, 0x53, 0x6f, 0x6e,
	0x12, 0x10, 0x0a, 0x03, 0x50, 0x69, 0x64, 0x18, 0x09, 0x20, 0x01, 0x28, 0x04, 0x52, 0x03, 0x50,
	0x69, 0x64, 0x12, 0x2e, 0x0a, 0x08, 0x52, 0x75, 0x6c, 0x65, 0x44, 0x61, 0x74, 0x61, 0x18, 0x0a,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x2e,
	0x52, 0x75, 0x6c, 0x65, 0x44, 0x61, 0x74, 0x61, 0x52, 0x08, 0x52, 0x75, 0x6c, 0x65, 0x44, 0x61,
	0x74, 0x61, 0x12, 0x1a, 0x0a, 0x08, 0x4d, 0x65, 0x6e, 0x75, 0x54, 0x79, 0x70, 0x65, 0x18, 0x0b,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x4d, 0x65, 0x6e, 0x75, 0x54, 0x79, 0x70, 0x65, 0x12, 0x12,
	0x0a, 0x04, 0x54, 0x79, 0x70, 0x65, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x54, 0x79,
	0x70, 0x65, 0x12, 0x4b, 0x0a, 0x12, 0x50, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x75,
	0x6c, 0x65, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x73, 0x18, 0x0d, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1b,
	0x2e, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x50, 0x6f, 0x73, 0x69, 0x74, 0x69,
	0x6f, 0x6e, 0x52, 0x75, 0x6c, 0x65, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x52, 0x12, 0x50, 0x6f, 0x73,
	0x69, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x75, 0x6c, 0x65, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x73, 0x12,
	0x1c, 0x0a, 0x09, 0x64, 0x61, 0x74, 0x61, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x18, 0x0e, 0x20, 0x01,
	0x28, 0x0d, 0x52, 0x09, 0x64, 0x61, 0x74, 0x61, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x12, 0x38, 0x0a,
	0x0d, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x66, 0x61, 0x63, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x18, 0x0f,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x2e,
	0x52, 0x75, 0x6c, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x0d, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x66,
	0x61, 0x63, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x32, 0x0a, 0x0a, 0x62, 0x75, 0x74, 0x74, 0x6f,
	0x6e, 0x4c, 0x69, 0x73, 0x74, 0x18, 0x10, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x70, 0x6f,
	0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x52, 0x75, 0x6c, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x52,
	0x0a, 0x62, 0x75, 0x74, 0x74, 0x6f, 0x6e, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x2c, 0x0a, 0x11, 0x64,
	0x61, 0x74, 0x61, 0x44, 0x65, 0x70, 0x61, 0x72, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x73,
	0x18, 0x11, 0x20, 0x03, 0x28, 0x0d, 0x52, 0x11, 0x64, 0x61, 0x74, 0x61, 0x44, 0x65, 0x70, 0x61,
	0x72, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x73, 0x22, 0xb1, 0x01, 0x0a, 0x11, 0x50, 0x6f,
	0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x75, 0x6c, 0x65, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x12,
	0x0e, 0x0a, 0x02, 0x49, 0x44, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x02, 0x49, 0x44, 0x12,
	0x20, 0x0a, 0x0b, 0x72, 0x75, 0x6c, 0x65, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x49, 0x64, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x04, 0x52, 0x0b, 0x72, 0x75, 0x6c, 0x65, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x49,
	0x64, 0x12, 0x16, 0x0a, 0x06, 0x72, 0x75, 0x6c, 0x65, 0x49, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x04, 0x52, 0x06, 0x72, 0x75, 0x6c, 0x65, 0x49, 0x64, 0x12, 0x20, 0x0a, 0x0b, 0x66, 0x69, 0x65,
	0x6c, 0x64, 0x43, 0x6e, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b,
	0x66, 0x69, 0x65, 0x6c, 0x64, 0x43, 0x6e, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x66,
	0x69, 0x65, 0x6c, 0x64, 0x4b, 0x65, 0x79, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x66,
	0x69, 0x65, 0x6c, 0x64, 0x4b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x69, 0x6e, 0x64, 0x65, 0x78,
	0x18, 0x06, 0x20, 0x01, 0x28, 0x04, 0x52, 0x05, 0x69, 0x6e, 0x64, 0x65, 0x78, 0x22, 0x30, 0x0a,
	0x08, 0x52, 0x75, 0x6c, 0x65, 0x44, 0x61, 0x74, 0x61, 0x12, 0x0e, 0x0a, 0x02, 0x49, 0x44, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x02, 0x49, 0x44, 0x12, 0x14, 0x0a, 0x05, 0x54, 0x69, 0x74,
	0x6c, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x54, 0x69, 0x74, 0x6c, 0x65, 0x22,
	0x51, 0x0a, 0x0c, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12,
	0x14, 0x0a, 0x05, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x05,
	0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x2b, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x04, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x43,
	0x72, 0x65, 0x61, 0x74, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x52, 0x04, 0x64, 0x61,
	0x74, 0x61, 0x22, 0x8c, 0x02, 0x0a, 0x0e, 0x4c, 0x6f, 0x67, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x16, 0x0a, 0x06, 0x64, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x64, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x12, 0x1a, 0x0a,
	0x08, 0x70, 0x61, 0x67, 0x65, 0x53, 0x69, 0x7a, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x04, 0x52,
	0x08, 0x70, 0x61, 0x67, 0x65, 0x53, 0x69, 0x7a, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x70, 0x61, 0x67,
	0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x04, 0x52, 0x04, 0x70, 0x61, 0x67, 0x65, 0x12, 0x20, 0x0a,
	0x0b, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x65, 0x54, 0x79, 0x70, 0x65, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0b, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12,
	0x22, 0x0a, 0x0c, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x4e,
	0x61, 0x6d, 0x65, 0x12, 0x26, 0x0a, 0x0e, 0x73, 0x74, 0x61, 0x72, 0x74, 0x43, 0x72, 0x65, 0x61,
	0x74, 0x65, 0x64, 0x41, 0x74, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x73, 0x74, 0x61,
	0x72, 0x74, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x22, 0x0a, 0x0c, 0x65,
	0x6e, 0x64, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x18, 0x07, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0c, 0x65, 0x6e, 0x64, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12,
	0x20, 0x0a, 0x0b, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x54, 0x65, 0x6c, 0x18, 0x08,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x54, 0x65,
	0x6c, 0x22, 0x52, 0x0a, 0x0f, 0x4c, 0x6f, 0x67, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x04, 0x52, 0x05, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x29, 0x0a, 0x04, 0x6c, 0x69,
	0x73, 0x74, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x70, 0x6f, 0x73, 0x69, 0x74,
	0x69, 0x6f, 0x6e, 0x2e, 0x4c, 0x6f, 0x67, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x52,
	0x04, 0x6c, 0x69, 0x73, 0x74, 0x22, 0xf0, 0x01, 0x0a, 0x0a, 0x4c, 0x6f, 0x67, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04,
	0x52, 0x02, 0x69, 0x64, 0x12, 0x20, 0x0a, 0x0b, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x65, 0x54,
	0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x6f, 0x70, 0x65, 0x72, 0x61,
	0x74, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x69, 0x6e, 0x66, 0x6f, 0x12, 0x22, 0x0a, 0x0c, 0x6f, 0x70,
	0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0c, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x20,
	0x0a, 0x0b, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x54, 0x65, 0x6c, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0b, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x54, 0x65, 0x6c,
	0x12, 0x1e, 0x0a, 0x0a, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x18, 0x06,
	0x20, 0x01, 0x28, 0x0d, 0x52, 0x0a, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x49, 0x64,
	0x12, 0x1e, 0x0a, 0x0a, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x18, 0x07,
	0x20, 0x01, 0x28, 0x0d, 0x52, 0x0a, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64,
	0x12, 0x16, 0x0a, 0x06, 0x64, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x06, 0x64, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x22, 0xb7, 0x01, 0x0a, 0x0b, 0x4c, 0x6f, 0x67,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x04, 0x52, 0x02, 0x69, 0x64, 0x12, 0x20, 0x0a, 0x0b, 0x6f, 0x70, 0x65, 0x72,
	0x61, 0x74, 0x65, 0x54, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x6f,
	0x70, 0x65, 0x72, 0x61, 0x74, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x69, 0x6e,
	0x66, 0x6f, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x69, 0x6e, 0x66, 0x6f, 0x12, 0x22,
	0x0a, 0x0c, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x4e, 0x61,
	0x6d, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x54, 0x65,
	0x6c, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f,
	0x72, 0x54, 0x65, 0x6c, 0x12, 0x1c, 0x0a, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41,
	0x74, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64,
	0x41, 0x74, 0x32, 0x85, 0x11, 0x0a, 0x08, 0x50, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x12,
	0x3b, 0x0a, 0x06, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x12, 0x17, 0x2e, 0x70, 0x6f, 0x73, 0x69,
	0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x1a, 0x18, 0x2e, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x43, 0x72,
	0x65, 0x61, 0x74, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x51, 0x0a, 0x15,
	0x42, 0x61, 0x74, 0x63, 0x68, 0x41, 0x64, 0x64, 0x50, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e,
	0x52, 0x75, 0x6c, 0x65, 0x73, 0x12, 0x1e, 0x2e, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e,
	0x2e, 0x42, 0x61, 0x74, 0x63, 0x68, 0x41, 0x64, 0x64, 0x52, 0x75, 0x6c, 0x65, 0x73, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x18, 0x2e, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e,
	0x2e, 0x43, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12,
	0x44, 0x0a, 0x09, 0x4c, 0x69, 0x73, 0x74, 0x42, 0x79, 0x44, 0x69, 0x64, 0x12, 0x1a, 0x2e, 0x70,
	0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x42, 0x79, 0x44, 0x69,
	0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1b, 0x2e, 0x70, 0x6f, 0x73, 0x69, 0x74,
	0x69, 0x6f, 0x6e, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x42, 0x79, 0x44, 0x69, 0x64, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x3b, 0x0a, 0x06, 0x52, 0x65, 0x6d, 0x6f, 0x76, 0x65, 0x12,
	0x17, 0x2e, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x52, 0x65, 0x6d, 0x6f, 0x76,
	0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x18, 0x2e, 0x70, 0x6f, 0x73, 0x69, 0x74,
	0x69, 0x6f, 0x6e, 0x2e, 0x52, 0x65, 0x6d, 0x6f, 0x76, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x12, 0x45, 0x0a, 0x0b, 0x42, 0x61, 0x74, 0x63, 0x68, 0x52, 0x65, 0x6d, 0x6f, 0x76,
	0x65, 0x12, 0x1c, 0x2e, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x42, 0x61, 0x74,
	0x63, 0x68, 0x52, 0x65, 0x6d, 0x6f, 0x76, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a,
	0x18, 0x2e, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x52, 0x65, 0x6d, 0x6f, 0x76,
	0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x3a, 0x0a, 0x06, 0x44, 0x65, 0x74,
	0x61, 0x69, 0x6c, 0x12, 0x17, 0x2e, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x44,
	0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x17, 0x2e, 0x70,
	0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x3b, 0x0a, 0x06, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x12,
	0x17, 0x2e, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74,
	0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x18, 0x2e, 0x70, 0x6f, 0x73, 0x69, 0x74,
	0x69, 0x6f, 0x6e, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x12, 0x35, 0x0a, 0x04, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x15, 0x2e, 0x70, 0x6f, 0x73,
	0x69, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x1a, 0x16, 0x2e, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x4c, 0x69, 0x73,
	0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x47, 0x0a, 0x0a, 0x52, 0x65, 0x6d,
	0x6f, 0x76, 0x65, 0x55, 0x73, 0x65, 0x72, 0x12, 0x1b, 0x2e, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x69,
	0x6f, 0x6e, 0x2e, 0x52, 0x65, 0x6d, 0x6f, 0x76, 0x65, 0x55, 0x73, 0x65, 0x72, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x1a, 0x1c, 0x2e, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x2e,
	0x52, 0x65, 0x6d, 0x6f, 0x76, 0x65, 0x55, 0x73, 0x65, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x12, 0x41, 0x0a, 0x08, 0x42, 0x69, 0x6e, 0x64, 0x55, 0x73, 0x65, 0x72, 0x12, 0x19,
	0x2e, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x42, 0x69, 0x6e, 0x64, 0x55, 0x73,
	0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1a, 0x2e, 0x70, 0x6f, 0x73, 0x69,
	0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x42, 0x69, 0x6e, 0x64, 0x55, 0x73, 0x65, 0x72, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x48, 0x0a, 0x0f, 0x42, 0x69, 0x6e, 0x64, 0x55, 0x73, 0x65,
	0x72, 0x46, 0x6f, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x19, 0x2e, 0x70, 0x6f, 0x73, 0x69, 0x74,
	0x69, 0x6f, 0x6e, 0x2e, 0x42, 0x69, 0x6e, 0x64, 0x55, 0x73, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x1a, 0x1a, 0x2e, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x42,
	0x69, 0x6e, 0x64, 0x55, 0x73, 0x65, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12,
	0x43, 0x0a, 0x0b, 0x41, 0x6e, 0x64, 0x42, 0x69, 0x6e, 0x64, 0x55, 0x73, 0x65, 0x72, 0x12, 0x18,
	0x2e, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x41, 0x64, 0x64, 0x55, 0x73, 0x65,
	0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1a, 0x2e, 0x70, 0x6f, 0x73, 0x69, 0x74,
	0x69, 0x6f, 0x6e, 0x2e, 0x42, 0x69, 0x6e, 0x64, 0x55, 0x73, 0x65, 0x72, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x12, 0x4c, 0x0a, 0x0f, 0x46, 0x69, 0x6e, 0x64, 0x4f, 0x6e, 0x65, 0x46,
	0x72, 0x6f, 0x6d, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x20, 0x2e, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x69,
	0x6f, 0x6e, 0x2e, 0x46, 0x69, 0x6e, 0x64, 0x4f, 0x6e, 0x65, 0x46, 0x72, 0x6f, 0x6d, 0x4e, 0x61,
	0x6d, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x17, 0x2e, 0x70, 0x6f, 0x73, 0x69,
	0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x12, 0x50, 0x0a, 0x0d, 0x50, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x42, 0x79,
	0x55, 0x72, 0x6c, 0x12, 0x1e, 0x2e, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x50,
	0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x42, 0x79, 0x55, 0x72, 0x6c, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x1a, 0x1f, 0x2e, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x50,
	0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x42, 0x79, 0x55, 0x72, 0x6c, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x12, 0x52, 0x0a, 0x0e, 0x50, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e,
	0x42, 0x79, 0x55, 0x72, 0x6c, 0x73, 0x12, 0x1f, 0x2e, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f,
	0x6e, 0x2e, 0x50, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x42, 0x79, 0x55, 0x72, 0x6c, 0x73,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1f, 0x2e, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x69,
	0x6f, 0x6e, 0x2e, 0x50, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x42, 0x79, 0x55, 0x72, 0x6c,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x4a, 0x0a, 0x0b, 0x44, 0x6f, 0x49, 0x48,
	0x61, 0x76, 0x61, 0x41, 0x75, 0x74, 0x68, 0x12, 0x1c, 0x2e, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x69,
	0x6f, 0x6e, 0x2e, 0x44, 0x6f, 0x49, 0x48, 0x61, 0x76, 0x61, 0x41, 0x75, 0x74, 0x68, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1d, 0x2e, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e,
	0x2e, 0x44, 0x6f, 0x49, 0x48, 0x61, 0x76, 0x61, 0x41, 0x75, 0x74, 0x68, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x12, 0x56, 0x0a, 0x0f, 0x44, 0x6f, 0x48, 0x61, 0x76, 0x61, 0x41, 0x75,
	0x74, 0x68, 0x42, 0x61, 0x74, 0x63, 0x68, 0x12, 0x20, 0x2e, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x69,
	0x6f, 0x6e, 0x2e, 0x44, 0x6f, 0x48, 0x61, 0x76, 0x61, 0x41, 0x75, 0x74, 0x68, 0x42, 0x61, 0x74,
	0x63, 0x68, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x21, 0x2e, 0x70, 0x6f, 0x73, 0x69,
	0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x44, 0x6f, 0x48, 0x61, 0x76, 0x61, 0x41, 0x75, 0x74, 0x68, 0x42,
	0x61, 0x74, 0x63, 0x68, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x59, 0x0a, 0x10,
	0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x44, 0x65, 0x70, 0x61, 0x72, 0x74, 0x6d, 0x65, 0x6e, 0x74,
	0x12, 0x21, 0x2e, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x53, 0x65, 0x61, 0x72,
	0x63, 0x68, 0x44, 0x65, 0x70, 0x61, 0x72, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x1a, 0x22, 0x2e, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x53,
	0x65, 0x61, 0x72, 0x63, 0x68, 0x44, 0x65, 0x70, 0x61, 0x72, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x54, 0x0a, 0x0f, 0x46, 0x69, 0x6e, 0x64, 0x55,
	0x73, 0x65, 0x72, 0x73, 0x42, 0x79, 0x52, 0x75, 0x6c, 0x65, 0x12, 0x20, 0x2e, 0x70, 0x6f, 0x73,
	0x69, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x46, 0x69, 0x6e, 0x64, 0x55, 0x73, 0x65, 0x72, 0x73, 0x42,
	0x79, 0x52, 0x75, 0x6c, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1f, 0x2e, 0x70,
	0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x50, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e,
	0x55, 0x73, 0x65, 0x72, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x59, 0x0a,
	0x10, 0x46, 0x69, 0x6e, 0x64, 0x52, 0x75, 0x6c, 0x65, 0x42, 0x79, 0x55, 0x73, 0x65, 0x72, 0x49,
	0x64, 0x12, 0x21, 0x2e, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x46, 0x69, 0x6e,
	0x64, 0x52, 0x75, 0x6c, 0x65, 0x42, 0x79, 0x55, 0x73, 0x65, 0x72, 0x49, 0x64, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x1a, 0x22, 0x2e, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x2e,
	0x46, 0x69, 0x6e, 0x64, 0x52, 0x75, 0x6c, 0x65, 0x42, 0x79, 0x55, 0x73, 0x65, 0x72, 0x49, 0x64,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x43, 0x0a, 0x0a, 0x42, 0x69, 0x6e, 0x64,
	0x55, 0x73, 0x65, 0x72, 0x56, 0x32, 0x12, 0x1b, 0x2e, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f,
	0x6e, 0x2e, 0x42, 0x69, 0x6e, 0x64, 0x55, 0x73, 0x65, 0x72, 0x56, 0x32, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x1a, 0x18, 0x2e, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x43,
	0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x4a, 0x0a,
	0x11, 0x42, 0x69, 0x6e, 0x64, 0x55, 0x73, 0x65, 0x72, 0x46, 0x6f, 0x72, 0x4e, 0x61, 0x6d, 0x65,
	0x56, 0x32, 0x12, 0x1b, 0x2e, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x42, 0x69,
	0x6e, 0x64, 0x55, 0x73, 0x65, 0x72, 0x56, 0x32, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a,
	0x18, 0x2e, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x43, 0x6f, 0x6d, 0x6d, 0x6f,
	0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x46, 0x0a, 0x0d, 0x41, 0x6e, 0x64,
	0x42, 0x69, 0x6e, 0x64, 0x55, 0x73, 0x65, 0x72, 0x56, 0x32, 0x12, 0x1b, 0x2e, 0x70, 0x6f, 0x73,
	0x69, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x42, 0x69, 0x6e, 0x64, 0x55, 0x73, 0x65, 0x72, 0x56, 0x32,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x18, 0x2e, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x69,
	0x6f, 0x6e, 0x2e, 0x43, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x12, 0x43, 0x0a, 0x0a, 0x55, 0x73, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x56, 0x32, 0x12,
	0x18, 0x2e, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74,
	0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x1a, 0x1b, 0x2e, 0x70, 0x6f, 0x73, 0x69,
	0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x42, 0x69, 0x6e, 0x64, 0x55, 0x73, 0x65, 0x72, 0x56, 0x32, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x4a, 0x0a, 0x0b, 0x55, 0x73, 0x65, 0x72, 0x49, 0x6e,
	0x66, 0x6f, 0x73, 0x56, 0x32, 0x12, 0x1c, 0x2e, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e,
	0x2e, 0x55, 0x73, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x73, 0x56, 0x32, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x1a, 0x1d, 0x2e, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x55,
	0x73, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x73, 0x56, 0x32, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x12, 0x3d, 0x0a, 0x08, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x56, 0x32, 0x12, 0x17,
	0x2e, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x18, 0x2e, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x69,
	0x6f, 0x6e, 0x2e, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x12, 0x7a, 0x0a, 0x1b, 0x44, 0x65, 0x70, 0x61, 0x72, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x49,
	0x64, 0x73, 0x42, 0x79, 0x55, 0x72, 0x6c, 0x41, 0x6e, 0x64, 0x55, 0x73, 0x65, 0x72, 0x49, 0x64,
	0x12, 0x2c, 0x2e, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x44, 0x65, 0x70, 0x61,
	0x72, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x73, 0x42, 0x79, 0x55, 0x72, 0x6c, 0x41, 0x6e,
	0x64, 0x55, 0x73, 0x65, 0x72, 0x49, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2d,
	0x2e, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x44, 0x65, 0x70, 0x61, 0x72, 0x74,
	0x6d, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x73, 0x42, 0x79, 0x55, 0x72, 0x6c, 0x41, 0x6e, 0x64, 0x55,
	0x73, 0x65, 0x72, 0x49, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x46, 0x0a,
	0x0f, 0x50, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x4c, 0x6f, 0x67, 0x4c, 0x69, 0x73, 0x74,
	0x12, 0x18, 0x2e, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x4c, 0x6f, 0x67, 0x4c,
	0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x19, 0x2e, 0x70, 0x6f, 0x73,
	0x69, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x4c, 0x6f, 0x67, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x43, 0x0a, 0x11, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x50,
	0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x4c, 0x6f, 0x67, 0x12, 0x14, 0x2e, 0x70, 0x6f, 0x73,
	0x69, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x4c, 0x6f, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x1a, 0x18, 0x2e, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x43, 0x72, 0x65, 0x61,
	0x74, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x42, 0x0d, 0x5a, 0x0b, 0x2e, 0x2f,
	0x3b, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x33,
}

var (
	file_api_position_position_proto_rawDescOnce sync.Once
	file_api_position_position_proto_rawDescData = file_api_position_position_proto_rawDesc
)

func file_api_position_position_proto_rawDescGZIP() []byte {
	file_api_position_position_proto_rawDescOnce.Do(func() {
		file_api_position_position_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_position_position_proto_rawDescData)
	})
	return file_api_position_position_proto_rawDescData
}

var file_api_position_position_proto_msgTypes = make([]protoimpl.MessageInfo, 52)
var file_api_position_position_proto_goTypes = []interface{}{
	(*DepartmentIdsByUrlAndUserIdRequest)(nil),  // 0: position.DepartmentIdsByUrlAndUserIdRequest
	(*DepartmentIdsByUrlAndUserIdResponse)(nil), // 1: position.DepartmentIdsByUrlAndUserIdResponse
	(*UserInfosV2Request)(nil),                  // 2: position.UserInfosV2Request
	(*UserInfosV2Response)(nil),                 // 3: position.UserInfosV2Response
	(*BatchRemoveRequest)(nil),                  // 4: position.BatchRemoveRequest
	(*FindRuleByUserIdRequest)(nil),             // 5: position.FindRuleByUserIdRequest
	(*FindRuleByUserIdResponse)(nil),            // 6: position.FindRuleByUserIdResponse
	(*FindUsersByRuleRequest)(nil),              // 7: position.FindUsersByRuleRequest
	(*PositionUsersResponse)(nil),               // 8: position.PositionUsersResponse
	(*PositionByUrlResponse)(nil),               // 9: position.PositionByUrlResponse
	(*PositionByUrlRequest)(nil),                // 10: position.PositionByUrlRequest
	(*PositionByUrlsRequest)(nil),               // 11: position.PositionByUrlsRequest
	(*SearchDepartmentRequest)(nil),             // 12: position.SearchDepartmentRequest
	(*PDInfo)(nil),                              // 13: position.PDInfo
	(*SearchDepartmentResponse)(nil),            // 14: position.SearchDepartmentResponse
	(*DoIHavaAuthRequest)(nil),                  // 15: position.DoIHavaAuthRequest
	(*DoHavaAuthBatchRequest)(nil),              // 16: position.DoHavaAuthBatchRequest
	(*DoIHavaAuthResponse)(nil),                 // 17: position.DoIHavaAuthResponse
	(*DoHavaAuthBatchResponse)(nil),             // 18: position.DoHavaAuthBatchResponse
	(*FindOneFromNameRequest)(nil),              // 19: position.FindOneFromNameRequest
	(*ListByDidRequest)(nil),                    // 20: position.ListByDidRequest
	(*BasePosition)(nil),                        // 21: position.BasePosition
	(*ListByDidResponse)(nil),                   // 22: position.ListByDidResponse
	(*BindUserV2Request)(nil),                   // 23: position.BindUserV2Request
	(*DepPosition)(nil),                         // 24: position.DepPosition
	(*PositionV2)(nil),                          // 25: position.PositionV2
	(*UserInfo)(nil),                            // 26: position.UserInfo
	(*BindUserRequest)(nil),                     // 27: position.BindUserRequest
	(*AddUserRequest)(nil),                      // 28: position.AddUserRequest
	(*BindUserResponse)(nil),                    // 29: position.BindUserResponse
	(*CommonResponse)(nil),                      // 30: position.CommonResponse
	(*PositionUser)(nil),                        // 31: position.PositionUser
	(*RemoveUserRequest)(nil),                   // 32: position.RemoveUserRequest
	(*RemoveUserResponse)(nil),                  // 33: position.RemoveUserResponse
	(*CreateResponse)(nil),                      // 34: position.CreateResponse
	(*RemoveRequest)(nil),                       // 35: position.RemoveRequest
	(*RemoveResponse)(nil),                      // 36: position.RemoveResponse
	(*DetailRequest)(nil),                       // 37: position.DetailRequest
	(*DetailResponse)(nil),                      // 38: position.DetailResponse
	(*CreateRequest)(nil),                       // 39: position.CreateRequest
	(*BatchAddRulesRequest)(nil),                // 40: position.BatchAddRulesRequest
	(*Leader)(nil),                              // 41: position.Leader
	(*ListRequest)(nil),                         // 42: position.ListRequest
	(*Rule)(nil),                                // 43: position.Rule
	(*RuleInfo)(nil),                            // 44: position.RuleInfo
	(*PositionRuleField)(nil),                   // 45: position.PositionRuleField
	(*RuleData)(nil),                            // 46: position.RuleData
	(*ListResponse)(nil),                        // 47: position.ListResponse
	(*LogListRequest)(nil),                      // 48: position.LogListRequest
	(*LogListResponse)(nil),                     // 49: position.LogListResponse
	(*LogRequest)(nil),                          // 50: position.LogRequest
	(*LogResponse)(nil),                         // 51: position.LogResponse
}
var file_api_position_position_proto_depIdxs = []int32{
	23, // 0: position.UserInfosV2Response.userInfo:type_name -> position.BindUserV2Request
	44, // 1: position.FindRuleByUserIdResponse.rules:type_name -> position.RuleInfo
	31, // 2: position.PositionUsersResponse.positionUsers:type_name -> position.PositionUser
	21, // 3: position.PositionByUrlResponse.List:type_name -> position.BasePosition
	13, // 4: position.SearchDepartmentResponse.List:type_name -> position.PDInfo
	21, // 5: position.ListByDidResponse.Positions:type_name -> position.BasePosition
	26, // 6: position.BindUserV2Request.userInfo:type_name -> position.UserInfo
	24, // 7: position.BindUserV2Request.depPositions:type_name -> position.DepPosition
	25, // 8: position.DepPosition.positions:type_name -> position.PositionV2
	31, // 9: position.BindUserRequest.positionUsers:type_name -> position.PositionUser
	31, // 10: position.AddUserRequest.positionUser:type_name -> position.PositionUser
	44, // 11: position.DetailResponse.rules:type_name -> position.RuleInfo
	44, // 12: position.DetailResponse.positionTreeRule:type_name -> position.RuleInfo
	44, // 13: position.CreateRequest.Rules:type_name -> position.RuleInfo
	44, // 14: position.CreateRequest.PositionTreeRule:type_name -> position.RuleInfo
	44, // 15: position.RuleInfo.Son:type_name -> position.RuleInfo
	46, // 16: position.RuleInfo.RuleData:type_name -> position.RuleData
	45, // 17: position.RuleInfo.PositionRuleFields:type_name -> position.PositionRuleField
	44, // 18: position.RuleInfo.interfaceList:type_name -> position.RuleInfo
	44, // 19: position.RuleInfo.buttonList:type_name -> position.RuleInfo
	39, // 20: position.ListResponse.data:type_name -> position.CreateRequest
	51, // 21: position.LogListResponse.list:type_name -> position.LogResponse
	39, // 22: position.Position.Create:input_type -> position.CreateRequest
	40, // 23: position.Position.BatchAddPositionRules:input_type -> position.BatchAddRulesRequest
	20, // 24: position.Position.ListByDid:input_type -> position.ListByDidRequest
	35, // 25: position.Position.Remove:input_type -> position.RemoveRequest
	4,  // 26: position.Position.BatchRemove:input_type -> position.BatchRemoveRequest
	37, // 27: position.Position.Detail:input_type -> position.DetailRequest
	39, // 28: position.Position.Update:input_type -> position.CreateRequest
	42, // 29: position.Position.List:input_type -> position.ListRequest
	32, // 30: position.Position.RemoveUser:input_type -> position.RemoveUserRequest
	27, // 31: position.Position.BindUser:input_type -> position.BindUserRequest
	27, // 32: position.Position.BindUserForName:input_type -> position.BindUserRequest
	28, // 33: position.Position.AndBindUser:input_type -> position.AddUserRequest
	19, // 34: position.Position.FindOneFromName:input_type -> position.FindOneFromNameRequest
	10, // 35: position.Position.PositionByUrl:input_type -> position.PositionByUrlRequest
	11, // 36: position.Position.PositionByUrls:input_type -> position.PositionByUrlsRequest
	15, // 37: position.Position.DoIHavaAuth:input_type -> position.DoIHavaAuthRequest
	16, // 38: position.Position.DoHavaAuthBatch:input_type -> position.DoHavaAuthBatchRequest
	12, // 39: position.Position.SearchDepartment:input_type -> position.SearchDepartmentRequest
	7,  // 40: position.Position.FindUsersByRule:input_type -> position.FindUsersByRuleRequest
	5,  // 41: position.Position.FindRuleByUserId:input_type -> position.FindRuleByUserIdRequest
	23, // 42: position.Position.BindUserV2:input_type -> position.BindUserV2Request
	23, // 43: position.Position.BindUserForNameV2:input_type -> position.BindUserV2Request
	23, // 44: position.Position.AndBindUserV2:input_type -> position.BindUserV2Request
	34, // 45: position.Position.UserInfoV2:input_type -> position.CreateResponse
	2,  // 46: position.Position.UserInfosV2:input_type -> position.UserInfosV2Request
	37, // 47: position.Position.DetailV2:input_type -> position.DetailRequest
	0,  // 48: position.Position.DepartmentIdsByUrlAndUserId:input_type -> position.DepartmentIdsByUrlAndUserIdRequest
	48, // 49: position.Position.PositionLogList:input_type -> position.LogListRequest
	50, // 50: position.Position.CreatePositionLog:input_type -> position.LogRequest
	34, // 51: position.Position.Create:output_type -> position.CreateResponse
	30, // 52: position.Position.BatchAddPositionRules:output_type -> position.CommonResponse
	22, // 53: position.Position.ListByDid:output_type -> position.ListByDidResponse
	36, // 54: position.Position.Remove:output_type -> position.RemoveResponse
	36, // 55: position.Position.BatchRemove:output_type -> position.RemoveResponse
	39, // 56: position.Position.Detail:output_type -> position.CreateRequest
	34, // 57: position.Position.Update:output_type -> position.CreateResponse
	47, // 58: position.Position.List:output_type -> position.ListResponse
	33, // 59: position.Position.RemoveUser:output_type -> position.RemoveUserResponse
	29, // 60: position.Position.BindUser:output_type -> position.BindUserResponse
	29, // 61: position.Position.BindUserForName:output_type -> position.BindUserResponse
	29, // 62: position.Position.AndBindUser:output_type -> position.BindUserResponse
	39, // 63: position.Position.FindOneFromName:output_type -> position.CreateRequest
	9,  // 64: position.Position.PositionByUrl:output_type -> position.PositionByUrlResponse
	9,  // 65: position.Position.PositionByUrls:output_type -> position.PositionByUrlResponse
	17, // 66: position.Position.DoIHavaAuth:output_type -> position.DoIHavaAuthResponse
	18, // 67: position.Position.DoHavaAuthBatch:output_type -> position.DoHavaAuthBatchResponse
	14, // 68: position.Position.SearchDepartment:output_type -> position.SearchDepartmentResponse
	8,  // 69: position.Position.FindUsersByRule:output_type -> position.PositionUsersResponse
	6,  // 70: position.Position.FindRuleByUserId:output_type -> position.FindRuleByUserIdResponse
	30, // 71: position.Position.BindUserV2:output_type -> position.CommonResponse
	30, // 72: position.Position.BindUserForNameV2:output_type -> position.CommonResponse
	30, // 73: position.Position.AndBindUserV2:output_type -> position.CommonResponse
	23, // 74: position.Position.UserInfoV2:output_type -> position.BindUserV2Request
	3,  // 75: position.Position.UserInfosV2:output_type -> position.UserInfosV2Response
	38, // 76: position.Position.DetailV2:output_type -> position.DetailResponse
	1,  // 77: position.Position.DepartmentIdsByUrlAndUserId:output_type -> position.DepartmentIdsByUrlAndUserIdResponse
	49, // 78: position.Position.PositionLogList:output_type -> position.LogListResponse
	34, // 79: position.Position.CreatePositionLog:output_type -> position.CreateResponse
	51, // [51:80] is the sub-list for method output_type
	22, // [22:51] is the sub-list for method input_type
	22, // [22:22] is the sub-list for extension type_name
	22, // [22:22] is the sub-list for extension extendee
	0,  // [0:22] is the sub-list for field type_name
}

func init() { file_api_position_position_proto_init() }
func file_api_position_position_proto_init() {
	if File_api_position_position_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_api_position_position_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DepartmentIdsByUrlAndUserIdRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_position_position_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DepartmentIdsByUrlAndUserIdResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_position_position_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UserInfosV2Request); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_position_position_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UserInfosV2Response); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_position_position_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BatchRemoveRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_position_position_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FindRuleByUserIdRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_position_position_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FindRuleByUserIdResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_position_position_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FindUsersByRuleRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_position_position_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PositionUsersResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_position_position_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PositionByUrlResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_position_position_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PositionByUrlRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_position_position_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PositionByUrlsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_position_position_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SearchDepartmentRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_position_position_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PDInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_position_position_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SearchDepartmentResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_position_position_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DoIHavaAuthRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_position_position_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DoHavaAuthBatchRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_position_position_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DoIHavaAuthResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_position_position_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DoHavaAuthBatchResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_position_position_proto_msgTypes[19].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FindOneFromNameRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_position_position_proto_msgTypes[20].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListByDidRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_position_position_proto_msgTypes[21].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BasePosition); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_position_position_proto_msgTypes[22].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListByDidResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_position_position_proto_msgTypes[23].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BindUserV2Request); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_position_position_proto_msgTypes[24].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DepPosition); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_position_position_proto_msgTypes[25].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PositionV2); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_position_position_proto_msgTypes[26].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UserInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_position_position_proto_msgTypes[27].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BindUserRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_position_position_proto_msgTypes[28].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AddUserRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_position_position_proto_msgTypes[29].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BindUserResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_position_position_proto_msgTypes[30].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CommonResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_position_position_proto_msgTypes[31].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PositionUser); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_position_position_proto_msgTypes[32].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RemoveUserRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_position_position_proto_msgTypes[33].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RemoveUserResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_position_position_proto_msgTypes[34].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_position_position_proto_msgTypes[35].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RemoveRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_position_position_proto_msgTypes[36].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RemoveResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_position_position_proto_msgTypes[37].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DetailRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_position_position_proto_msgTypes[38].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DetailResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_position_position_proto_msgTypes[39].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_position_position_proto_msgTypes[40].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BatchAddRulesRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_position_position_proto_msgTypes[41].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Leader); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_position_position_proto_msgTypes[42].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_position_position_proto_msgTypes[43].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Rule); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_position_position_proto_msgTypes[44].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RuleInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_position_position_proto_msgTypes[45].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PositionRuleField); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_position_position_proto_msgTypes[46].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RuleData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_position_position_proto_msgTypes[47].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_position_position_proto_msgTypes[48].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LogListRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_position_position_proto_msgTypes[49].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LogListResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_position_position_proto_msgTypes[50].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LogRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_position_position_proto_msgTypes[51].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LogResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_position_position_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   52,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_api_position_position_proto_goTypes,
		DependencyIndexes: file_api_position_position_proto_depIdxs,
		MessageInfos:      file_api_position_position_proto_msgTypes,
	}.Build()
	File_api_position_position_proto = out.File
	file_api_position_position_proto_rawDesc = nil
	file_api_position_position_proto_goTypes = nil
	file_api_position_position_proto_depIdxs = nil
}
