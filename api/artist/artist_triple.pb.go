// Code generated by protoc-gen-go-triple. DO NOT EDIT.
// versions:
// - protoc-gen-go-triple v1.0.8
// - protoc             v4.22.0--rc2
// source: pb/artist.proto

package artist

import (
	context "context"
	protocol "dubbo.apache.org/dubbo-go/v3/protocol"
	dubbo3 "dubbo.apache.org/dubbo-go/v3/protocol/dubbo3"
	invocation "dubbo.apache.org/dubbo-go/v3/protocol/invocation"
	grpc_go "github.com/dubbogo/grpc-go"
	codes "github.com/dubbogo/grpc-go/codes"
	metadata "github.com/dubbogo/grpc-go/metadata"
	status "github.com/dubbogo/grpc-go/status"
	common "github.com/dubbogo/triple/pkg/common"
	constant "github.com/dubbogo/triple/pkg/common/constant"
	triple "github.com/dubbogo/triple/pkg/triple"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc_go.SupportPackageIsVersion7

// ArtistClient is the client API for Artist service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type ArtistClient interface {
	ArtistList(ctx context.Context, in *ArtistListRequest, opts ...grpc_go.CallOption) (*ArtistListResponse, common.ErrorWithAttachment)
	CreateProfile(ctx context.Context, in *ProfileRequest, opts ...grpc_go.CallOption) (*ProfileResponse, common.ErrorWithAttachment)
	UpdateProfile(ctx context.Context, in *ProfileRequest, opts ...grpc_go.CallOption) (*ProfileResponse, common.ErrorWithAttachment)
	UpdateMedia(ctx context.Context, in *MediaRequest, opts ...grpc_go.CallOption) (*MediaResponse, common.ErrorWithAttachment)
	UpdateIndex(ctx context.Context, in *IndexRequest, opts ...grpc_go.CallOption) (*IndexResponse, common.ErrorWithAttachment)
	UpdateHonor(ctx context.Context, in *HonorRequest, opts ...grpc_go.CallOption) (*HonorResponse, common.ErrorWithAttachment)
	UploadMedia(ctx context.Context, in *UploadMediaRequest, opts ...grpc_go.CallOption) (*UploadMediaResponse, common.ErrorWithAttachment)
	ArtistDetail(ctx context.Context, in *DetailRequest, opts ...grpc_go.CallOption) (*DetailResponse, common.ErrorWithAttachment)
	DelArtist(ctx context.Context, in *DelRequest, opts ...grpc_go.CallOption) (*DelResponse, common.ErrorWithAttachment)
	HonorDel(ctx context.Context, in *HonorDelRequest, opts ...grpc_go.CallOption) (*HonorDelResponse, common.ErrorWithAttachment)
	CreateArtistBatch(ctx context.Context, in *CreateArtistBatchRequest, opts ...grpc_go.CallOption) (*CreateArtistBatchResponse, common.ErrorWithAttachment)
	ArtistIdName(ctx context.Context, in *ArtIdNameRequest, opts ...grpc_go.CallOption) (*ArtIdNameResponse, common.ErrorWithAttachment)
	ArtistInfo(ctx context.Context, in *ArtistInfoRequest, opts ...grpc_go.CallOption) (*ArtistInfoResponse, common.ErrorWithAttachment)
	ArtistData(ctx context.Context, in *ArtistDataRequest, opts ...grpc_go.CallOption) (*ArtistDataResponse, common.ErrorWithAttachment)
	InvitationAdd(ctx context.Context, in *InvitationAddRequest, opts ...grpc_go.CallOption) (*InvitationAddResponse, common.ErrorWithAttachment)
	InvitationUpdate(ctx context.Context, in *InvitationUpdateRequest, opts ...grpc_go.CallOption) (*InvitationUpdateResponse, common.ErrorWithAttachment)
	InvitationDel(ctx context.Context, in *InvitationDelRequest, opts ...grpc_go.CallOption) (*InvitationDelResponse, common.ErrorWithAttachment)
	InvitationList(ctx context.Context, in *InvitationListRequest, opts ...grpc_go.CallOption) (*InvitationListResponse, common.ErrorWithAttachment)
	InvitationInfo(ctx context.Context, in *InvitationInfoRequest, opts ...grpc_go.CallOption) (*InvitationInfoResponse, common.ErrorWithAttachment)
	ContractAdd(ctx context.Context, in *ContractAddRequest, opts ...grpc_go.CallOption) (*ContractAddResponse, common.ErrorWithAttachment)
	ContractUpdate(ctx context.Context, in *ContractAddRequest, opts ...grpc_go.CallOption) (*ContractUpdateResponse, common.ErrorWithAttachment)
	ContractList(ctx context.Context, in *ContractListRequest, opts ...grpc_go.CallOption) (*ContractListResponse, common.ErrorWithAttachment)
	ArtistChainUpdate(ctx context.Context, in *ArtistChainUpdateRequest, opts ...grpc_go.CallOption) (*ArtistChainUpdateResponse, common.ErrorWithAttachment)
	ExportArtist(ctx context.Context, in *ExportArtistRequest, opts ...grpc_go.CallOption) (*ExportArtistResponse, common.ErrorWithAttachment)
	ExportFieldList(ctx context.Context, in *ExportFieldListRequest, opts ...grpc_go.CallOption) (*ExportFieldListResponse, common.ErrorWithAttachment)
	GetInfoByUuids(ctx context.Context, in *GetInfoByUuidsRequest, opts ...grpc_go.CallOption) (*GetInfoByUuidsResponse, common.ErrorWithAttachment)
	GetCardIdWithImg(ctx context.Context, in *GetCardIdWithImgReq, opts ...grpc_go.CallOption) (*GetCardIdWithImgResp, common.ErrorWithAttachment)
	IdcardConvertArtistId(ctx context.Context, in *IdcardConvertArtistIdRequest, opts ...grpc_go.CallOption) (*IdcardConvertArtistIdResponse, common.ErrorWithAttachment)
	CheckCaaCertNum(ctx context.Context, in *CheckCaaCertNumRequest, opts ...grpc_go.CallOption) (*CheckCaaCertNumResponse, common.ErrorWithAttachment)
	ArtistDetailData(ctx context.Context, in *ArtistDetailDataReq, opts ...grpc_go.CallOption) (*ArtistDetailDataResp, common.ErrorWithAttachment)
	UpdateInsource(ctx context.Context, in *UpdateInsourceReq, opts ...grpc_go.CallOption) (*UpdateInsourceNoneResp, common.ErrorWithAttachment)
	DelContract(ctx context.Context, in *DelContractReq, opts ...grpc_go.CallOption) (*DelContractResp, common.ErrorWithAttachment)
	FindArtistUsePhone(ctx context.Context, in *FindArtistUsePhoneRequest, opts ...grpc_go.CallOption) (*ArtistDataResponse, common.ErrorWithAttachment)
	AddIndexesLog(ctx context.Context, in *AddIndexesLogReq, opts ...grpc_go.CallOption) (*emptypb.Empty, common.ErrorWithAttachment)
	IndexesLog(ctx context.Context, in *IndexesLogReq, opts ...grpc_go.CallOption) (*IndexesLogResp, common.ErrorWithAttachment)
	GetIndex(ctx context.Context, in *GetIndexRequest, opts ...grpc_go.CallOption) (*GetIndexDataResp, common.ErrorWithAttachment)
	SaveCertInfo(ctx context.Context, in *SaveCertInfoRequest, opts ...grpc_go.CallOption) (*SaveCertInfoResponse, common.ErrorWithAttachment)
	CreateChainMnemonic(ctx context.Context, in *CreateChainMnemonicRequest, opts ...grpc_go.CallOption) (*CreateChainMnemonicResponse, common.ErrorWithAttachment)
	HonorCount(ctx context.Context, in *HonorCountReq, opts ...grpc_go.CallOption) (*HonorCountResp, common.ErrorWithAttachment)
	SaveLowArtist(ctx context.Context, in *LowArtist, opts ...grpc_go.CallOption) (*SaveLowArtistResp, common.ErrorWithAttachment)
	BatchUpdLowArtist(ctx context.Context, in *BatchUpdLowArtistReq, opts ...grpc_go.CallOption) (*SaveLowArtistResp, common.ErrorWithAttachment)
	LowArtistList(ctx context.Context, in *LowArtistListReq, opts ...grpc_go.CallOption) (*LowArtistListResp, common.ErrorWithAttachment)
	GetExtData(ctx context.Context, in *GetExtDataReq, opts ...grpc_go.CallOption) (*GetExtDataResp, common.ErrorWithAttachment)
	BlackListUpdate(ctx context.Context, in *BlackListUpdateReq, opts ...grpc_go.CallOption) (*BlackListUpdateResp, common.ErrorWithAttachment)
	BlackList(ctx context.Context, in *BlackListReq, opts ...grpc_go.CallOption) (*BlackListResp, common.ErrorWithAttachment)
	ArtistSearch(ctx context.Context, in *ArtistSearchReq, opts ...grpc_go.CallOption) (*ArtistSearchResp, common.ErrorWithAttachment)
	// -------------------------------------------一键查询审批---------------------------------------------------------
	OneQuery(ctx context.Context, in *OneQueryReq, opts ...grpc_go.CallOption) (*OneQueryResp, common.ErrorWithAttachment)
	OneQueryOld(ctx context.Context, in *OneQueryReq, opts ...grpc_go.CallOption) (*OneQueryResp, common.ErrorWithAttachment)
	OneQueryAudit(ctx context.Context, in *OneQueryAuditReq, opts ...grpc_go.CallOption) (*OneQueryAuditResp, common.ErrorWithAttachment)
	CreateArtistOneQueryCheckRecord(ctx context.Context, in *ArtistOneQueryCheckRecordData, opts ...grpc_go.CallOption) (*emptypb.Empty, common.ErrorWithAttachment)
	UpdateArtistOneQueryCheckRecord(ctx context.Context, in *ArtistOneQueryCheckRecordData, opts ...grpc_go.CallOption) (*emptypb.Empty, common.ErrorWithAttachment)
	SaveArtistOneQueryCheckRecord(ctx context.Context, in *ArtistOneQueryCheckRecordData, opts ...grpc_go.CallOption) (*emptypb.Empty, common.ErrorWithAttachment)
	DeleteArtistOneQueryCheckRecord(ctx context.Context, in *DeleteArtistOneQueryCheckRecordRequest, opts ...grpc_go.CallOption) (*emptypb.Empty, common.ErrorWithAttachment)
	GetArtistOneQueryCheckRecordDetail(ctx context.Context, in *GetArtistOneQueryCheckRecordByIdRequest, opts ...grpc_go.CallOption) (*ArtistOneQueryCheckRecordData, common.ErrorWithAttachment)
	GetArtistOneQueryCheckRecordList(ctx context.Context, in *GetArtistOneQueryCheckRecordListRequest, opts ...grpc_go.CallOption) (*GetArtistOneQueryCheckRecordListResp, common.ErrorWithAttachment)
	UpdateOneQueryCheckStatusThatUserNotPerformed(ctx context.Context, in *UpdateOneQueryCheckStatusThatUserNotPerformedReq, opts ...grpc_go.CallOption) (*UpdateOneQueryCheckStatusThatUserNotPerformedResp, common.ErrorWithAttachment)
	// -------------------------------------------邀请函新增函数---------------------------------------------------------
	GetInvitationLetterList(ctx context.Context, in *GetInvitationLetterListRequest, opts ...grpc_go.CallOption) (*GetInvitationLetterListResp, common.ErrorWithAttachment)
	UpdateInvitationLetterTravelInfo(ctx context.Context, in *UpdateInvitationLetterTravelInfoReq, opts ...grpc_go.CallOption) (*emptypb.Empty, common.ErrorWithAttachment)
	GetInvitationLetterTravelInfo(ctx context.Context, in *GetInvitationLetterTravelInfoReq, opts ...grpc_go.CallOption) (*GetInvitationLetterTravelInfoResp, common.ErrorWithAttachment)
	GetInvitationLetterDetail(ctx context.Context, in *GetInvitationLetterDetailReq, opts ...grpc_go.CallOption) (*GetInvitationLetterDetailResp, common.ErrorWithAttachment)
	UpdateInvitationLetter(ctx context.Context, in *UpdateInvitationLetterReq, opts ...grpc_go.CallOption) (*UpdateInvitationLetterResp, common.ErrorWithAttachment)
	InvitationLetterSetToHistory(ctx context.Context, in *InvitationLetterSetToHistoryReq, opts ...grpc_go.CallOption) (*InvitationLetterSetToHistoryResp, common.ErrorWithAttachment)
	RecordInfoByArtistUid(ctx context.Context, in *RecordInfoByArtistUidReq, opts ...grpc_go.CallOption) (*RecordInfoByArtistUidResp, common.ErrorWithAttachment)
	OneQueryAllArtist(ctx context.Context, in *OneQueryAllArtistReq, opts ...grpc_go.CallOption) (*OneQueryResp, common.ErrorWithAttachment)
	SaveArtistProfile(ctx context.Context, in *SaveArtistProfileReq, opts ...grpc_go.CallOption) (*SaveArtistProfileResp, common.ErrorWithAttachment)
	BindContractArtwork(ctx context.Context, in *BindContractArtworkReq, opts ...grpc_go.CallOption) (*BindContractArtworkResp, common.ErrorWithAttachment)
	ArtistBaseInfo(ctx context.Context, in *ArtistBaseInfoReq, opts ...grpc_go.CallOption) (*ArtistBaseInfoResp, common.ErrorWithAttachment)
	ArtistBaseList(ctx context.Context, in *ArtistBaseListReq, opts ...grpc_go.CallOption) (*ArtistBaseListResp, common.ErrorWithAttachment)
	SaveInterviewVideo(ctx context.Context, in *SaveInterviewVideoReq, opts ...grpc_go.CallOption) (*SaveInterviewVideoResp, common.ErrorWithAttachment)
	SaveArtistSupplementInfo(ctx context.Context, in *SaveArtistSupplementInfoReq, opts ...grpc_go.CallOption) (*SaveArtistSupplementInfoResp, common.ErrorWithAttachment)
	SaveArtistBankInfo(ctx context.Context, in *SaveArtistBankInfoRequest, opts ...grpc_go.CallOption) (*SaveArtistBankInfoResponse, common.ErrorWithAttachment)
	SaveArtistResume(ctx context.Context, in *SaveArtistResumeRequest, opts ...grpc_go.CallOption) (*SaveArtistResumeResponse, common.ErrorWithAttachment)
	UpdateArtistJoinShow(ctx context.Context, in *UpdateArtistJoinShowReq, opts ...grpc_go.CallOption) (*UpdateArtistJoinShowResp, common.ErrorWithAttachment)
	UpdateArtistContract(ctx context.Context, in *UpdateArtistContractReq, opts ...grpc_go.CallOption) (*UpdateArtistContractResp, common.ErrorWithAttachment)
	GetArtistProfileList(ctx context.Context, in *GetArtistProfileListRequest, opts ...grpc_go.CallOption) (*GetArtistProfileListResp, common.ErrorWithAttachment)
}

type artistClient struct {
	cc *triple.TripleConn
}

type ArtistClientImpl struct {
	ArtistList                                    func(ctx context.Context, in *ArtistListRequest) (*ArtistListResponse, error)
	CreateProfile                                 func(ctx context.Context, in *ProfileRequest) (*ProfileResponse, error)
	UpdateProfile                                 func(ctx context.Context, in *ProfileRequest) (*ProfileResponse, error)
	UpdateMedia                                   func(ctx context.Context, in *MediaRequest) (*MediaResponse, error)
	UpdateIndex                                   func(ctx context.Context, in *IndexRequest) (*IndexResponse, error)
	UpdateHonor                                   func(ctx context.Context, in *HonorRequest) (*HonorResponse, error)
	UploadMedia                                   func(ctx context.Context, in *UploadMediaRequest) (*UploadMediaResponse, error)
	ArtistDetail                                  func(ctx context.Context, in *DetailRequest) (*DetailResponse, error)
	DelArtist                                     func(ctx context.Context, in *DelRequest) (*DelResponse, error)
	HonorDel                                      func(ctx context.Context, in *HonorDelRequest) (*HonorDelResponse, error)
	CreateArtistBatch                             func(ctx context.Context, in *CreateArtistBatchRequest) (*CreateArtistBatchResponse, error)
	ArtistIdName                                  func(ctx context.Context, in *ArtIdNameRequest) (*ArtIdNameResponse, error)
	ArtistInfo                                    func(ctx context.Context, in *ArtistInfoRequest) (*ArtistInfoResponse, error)
	ArtistData                                    func(ctx context.Context, in *ArtistDataRequest) (*ArtistDataResponse, error)
	InvitationAdd                                 func(ctx context.Context, in *InvitationAddRequest) (*InvitationAddResponse, error)
	InvitationUpdate                              func(ctx context.Context, in *InvitationUpdateRequest) (*InvitationUpdateResponse, error)
	InvitationDel                                 func(ctx context.Context, in *InvitationDelRequest) (*InvitationDelResponse, error)
	InvitationList                                func(ctx context.Context, in *InvitationListRequest) (*InvitationListResponse, error)
	InvitationInfo                                func(ctx context.Context, in *InvitationInfoRequest) (*InvitationInfoResponse, error)
	ContractAdd                                   func(ctx context.Context, in *ContractAddRequest) (*ContractAddResponse, error)
	ContractUpdate                                func(ctx context.Context, in *ContractAddRequest) (*ContractUpdateResponse, error)
	ContractList                                  func(ctx context.Context, in *ContractListRequest) (*ContractListResponse, error)
	ArtistChainUpdate                             func(ctx context.Context, in *ArtistChainUpdateRequest) (*ArtistChainUpdateResponse, error)
	ExportArtist                                  func(ctx context.Context, in *ExportArtistRequest) (*ExportArtistResponse, error)
	ExportFieldList                               func(ctx context.Context, in *ExportFieldListRequest) (*ExportFieldListResponse, error)
	GetInfoByUuids                                func(ctx context.Context, in *GetInfoByUuidsRequest) (*GetInfoByUuidsResponse, error)
	GetCardIdWithImg                              func(ctx context.Context, in *GetCardIdWithImgReq) (*GetCardIdWithImgResp, error)
	IdcardConvertArtistId                         func(ctx context.Context, in *IdcardConvertArtistIdRequest) (*IdcardConvertArtistIdResponse, error)
	CheckCaaCertNum                               func(ctx context.Context, in *CheckCaaCertNumRequest) (*CheckCaaCertNumResponse, error)
	ArtistDetailData                              func(ctx context.Context, in *ArtistDetailDataReq) (*ArtistDetailDataResp, error)
	UpdateInsource                                func(ctx context.Context, in *UpdateInsourceReq) (*UpdateInsourceNoneResp, error)
	DelContract                                   func(ctx context.Context, in *DelContractReq) (*DelContractResp, error)
	FindArtistUsePhone                            func(ctx context.Context, in *FindArtistUsePhoneRequest) (*ArtistDataResponse, error)
	AddIndexesLog                                 func(ctx context.Context, in *AddIndexesLogReq) (*emptypb.Empty, error)
	IndexesLog                                    func(ctx context.Context, in *IndexesLogReq) (*IndexesLogResp, error)
	GetIndex                                      func(ctx context.Context, in *GetIndexRequest) (*GetIndexDataResp, error)
	SaveCertInfo                                  func(ctx context.Context, in *SaveCertInfoRequest) (*SaveCertInfoResponse, error)
	CreateChainMnemonic                           func(ctx context.Context, in *CreateChainMnemonicRequest) (*CreateChainMnemonicResponse, error)
	HonorCount                                    func(ctx context.Context, in *HonorCountReq) (*HonorCountResp, error)
	SaveLowArtist                                 func(ctx context.Context, in *LowArtist) (*SaveLowArtistResp, error)
	BatchUpdLowArtist                             func(ctx context.Context, in *BatchUpdLowArtistReq) (*SaveLowArtistResp, error)
	LowArtistList                                 func(ctx context.Context, in *LowArtistListReq) (*LowArtistListResp, error)
	GetExtData                                    func(ctx context.Context, in *GetExtDataReq) (*GetExtDataResp, error)
	BlackListUpdate                               func(ctx context.Context, in *BlackListUpdateReq) (*BlackListUpdateResp, error)
	BlackList                                     func(ctx context.Context, in *BlackListReq) (*BlackListResp, error)
	ArtistSearch                                  func(ctx context.Context, in *ArtistSearchReq) (*ArtistSearchResp, error)
	OneQuery                                      func(ctx context.Context, in *OneQueryReq) (*OneQueryResp, error)
	OneQueryOld                                   func(ctx context.Context, in *OneQueryReq) (*OneQueryResp, error)
	OneQueryAudit                                 func(ctx context.Context, in *OneQueryAuditReq) (*OneQueryAuditResp, error)
	CreateArtistOneQueryCheckRecord               func(ctx context.Context, in *ArtistOneQueryCheckRecordData) (*emptypb.Empty, error)
	UpdateArtistOneQueryCheckRecord               func(ctx context.Context, in *ArtistOneQueryCheckRecordData) (*emptypb.Empty, error)
	SaveArtistOneQueryCheckRecord                 func(ctx context.Context, in *ArtistOneQueryCheckRecordData) (*emptypb.Empty, error)
	DeleteArtistOneQueryCheckRecord               func(ctx context.Context, in *DeleteArtistOneQueryCheckRecordRequest) (*emptypb.Empty, error)
	GetArtistOneQueryCheckRecordDetail            func(ctx context.Context, in *GetArtistOneQueryCheckRecordByIdRequest) (*ArtistOneQueryCheckRecordData, error)
	GetArtistOneQueryCheckRecordList              func(ctx context.Context, in *GetArtistOneQueryCheckRecordListRequest) (*GetArtistOneQueryCheckRecordListResp, error)
	UpdateOneQueryCheckStatusThatUserNotPerformed func(ctx context.Context, in *UpdateOneQueryCheckStatusThatUserNotPerformedReq) (*UpdateOneQueryCheckStatusThatUserNotPerformedResp, error)
	GetInvitationLetterList                       func(ctx context.Context, in *GetInvitationLetterListRequest) (*GetInvitationLetterListResp, error)
	UpdateInvitationLetterTravelInfo              func(ctx context.Context, in *UpdateInvitationLetterTravelInfoReq) (*emptypb.Empty, error)
	GetInvitationLetterTravelInfo                 func(ctx context.Context, in *GetInvitationLetterTravelInfoReq) (*GetInvitationLetterTravelInfoResp, error)
	GetInvitationLetterDetail                     func(ctx context.Context, in *GetInvitationLetterDetailReq) (*GetInvitationLetterDetailResp, error)
	UpdateInvitationLetter                        func(ctx context.Context, in *UpdateInvitationLetterReq) (*UpdateInvitationLetterResp, error)
	InvitationLetterSetToHistory                  func(ctx context.Context, in *InvitationLetterSetToHistoryReq) (*InvitationLetterSetToHistoryResp, error)
	RecordInfoByArtistUid                         func(ctx context.Context, in *RecordInfoByArtistUidReq) (*RecordInfoByArtistUidResp, error)
	OneQueryAllArtist                             func(ctx context.Context, in *OneQueryAllArtistReq) (*OneQueryResp, error)
	SaveArtistProfile                             func(ctx context.Context, in *SaveArtistProfileReq) (*SaveArtistProfileResp, error)
	BindContractArtwork                           func(ctx context.Context, in *BindContractArtworkReq) (*BindContractArtworkResp, error)
	ArtistBaseInfo                                func(ctx context.Context, in *ArtistBaseInfoReq) (*ArtistBaseInfoResp, error)
	ArtistBaseList                                func(ctx context.Context, in *ArtistBaseListReq) (*ArtistBaseListResp, error)
	SaveInterviewVideo                            func(ctx context.Context, in *SaveInterviewVideoReq) (*SaveInterviewVideoResp, error)
	SaveArtistSupplementInfo                      func(ctx context.Context, in *SaveArtistSupplementInfoReq) (*SaveArtistSupplementInfoResp, error)
	SaveArtistBankInfo                            func(ctx context.Context, in *SaveArtistBankInfoRequest) (*SaveArtistBankInfoResponse, error)
	SaveArtistResume                              func(ctx context.Context, in *SaveArtistResumeRequest) (*SaveArtistResumeResponse, error)
	UpdateArtistJoinShow                          func(ctx context.Context, in *UpdateArtistJoinShowReq) (*UpdateArtistJoinShowResp, error)
	UpdateArtistContract                          func(ctx context.Context, in *UpdateArtistContractReq) (*UpdateArtistContractResp, error)
	GetArtistProfileList                          func(ctx context.Context, in *GetArtistProfileListRequest) (*GetArtistProfileListResp, error)
}

func (c *ArtistClientImpl) GetDubboStub(cc *triple.TripleConn) ArtistClient {
	return NewArtistClient(cc)
}

func (c *ArtistClientImpl) XXX_InterfaceName() string {
	return "artist.Artist"
}

func NewArtistClient(cc *triple.TripleConn) ArtistClient {
	return &artistClient{cc}
}

func (c *artistClient) ArtistList(ctx context.Context, in *ArtistListRequest, opts ...grpc_go.CallOption) (*ArtistListResponse, common.ErrorWithAttachment) {
	out := new(ArtistListResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/ArtistList", in, out)
}

func (c *artistClient) CreateProfile(ctx context.Context, in *ProfileRequest, opts ...grpc_go.CallOption) (*ProfileResponse, common.ErrorWithAttachment) {
	out := new(ProfileResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/CreateProfile", in, out)
}

func (c *artistClient) UpdateProfile(ctx context.Context, in *ProfileRequest, opts ...grpc_go.CallOption) (*ProfileResponse, common.ErrorWithAttachment) {
	out := new(ProfileResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/UpdateProfile", in, out)
}

func (c *artistClient) UpdateMedia(ctx context.Context, in *MediaRequest, opts ...grpc_go.CallOption) (*MediaResponse, common.ErrorWithAttachment) {
	out := new(MediaResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/UpdateMedia", in, out)
}

func (c *artistClient) UpdateIndex(ctx context.Context, in *IndexRequest, opts ...grpc_go.CallOption) (*IndexResponse, common.ErrorWithAttachment) {
	out := new(IndexResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/UpdateIndex", in, out)
}

func (c *artistClient) UpdateHonor(ctx context.Context, in *HonorRequest, opts ...grpc_go.CallOption) (*HonorResponse, common.ErrorWithAttachment) {
	out := new(HonorResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/UpdateHonor", in, out)
}

func (c *artistClient) UploadMedia(ctx context.Context, in *UploadMediaRequest, opts ...grpc_go.CallOption) (*UploadMediaResponse, common.ErrorWithAttachment) {
	out := new(UploadMediaResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/UploadMedia", in, out)
}

func (c *artistClient) ArtistDetail(ctx context.Context, in *DetailRequest, opts ...grpc_go.CallOption) (*DetailResponse, common.ErrorWithAttachment) {
	out := new(DetailResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/ArtistDetail", in, out)
}

func (c *artistClient) DelArtist(ctx context.Context, in *DelRequest, opts ...grpc_go.CallOption) (*DelResponse, common.ErrorWithAttachment) {
	out := new(DelResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/DelArtist", in, out)
}

func (c *artistClient) HonorDel(ctx context.Context, in *HonorDelRequest, opts ...grpc_go.CallOption) (*HonorDelResponse, common.ErrorWithAttachment) {
	out := new(HonorDelResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/HonorDel", in, out)
}

func (c *artistClient) CreateArtistBatch(ctx context.Context, in *CreateArtistBatchRequest, opts ...grpc_go.CallOption) (*CreateArtistBatchResponse, common.ErrorWithAttachment) {
	out := new(CreateArtistBatchResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/CreateArtistBatch", in, out)
}

func (c *artistClient) ArtistIdName(ctx context.Context, in *ArtIdNameRequest, opts ...grpc_go.CallOption) (*ArtIdNameResponse, common.ErrorWithAttachment) {
	out := new(ArtIdNameResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/ArtistIdName", in, out)
}

func (c *artistClient) ArtistInfo(ctx context.Context, in *ArtistInfoRequest, opts ...grpc_go.CallOption) (*ArtistInfoResponse, common.ErrorWithAttachment) {
	out := new(ArtistInfoResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/ArtistInfo", in, out)
}

func (c *artistClient) ArtistData(ctx context.Context, in *ArtistDataRequest, opts ...grpc_go.CallOption) (*ArtistDataResponse, common.ErrorWithAttachment) {
	out := new(ArtistDataResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/ArtistData", in, out)
}

func (c *artistClient) InvitationAdd(ctx context.Context, in *InvitationAddRequest, opts ...grpc_go.CallOption) (*InvitationAddResponse, common.ErrorWithAttachment) {
	out := new(InvitationAddResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/InvitationAdd", in, out)
}

func (c *artistClient) InvitationUpdate(ctx context.Context, in *InvitationUpdateRequest, opts ...grpc_go.CallOption) (*InvitationUpdateResponse, common.ErrorWithAttachment) {
	out := new(InvitationUpdateResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/InvitationUpdate", in, out)
}

func (c *artistClient) InvitationDel(ctx context.Context, in *InvitationDelRequest, opts ...grpc_go.CallOption) (*InvitationDelResponse, common.ErrorWithAttachment) {
	out := new(InvitationDelResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/InvitationDel", in, out)
}

func (c *artistClient) InvitationList(ctx context.Context, in *InvitationListRequest, opts ...grpc_go.CallOption) (*InvitationListResponse, common.ErrorWithAttachment) {
	out := new(InvitationListResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/InvitationList", in, out)
}

func (c *artistClient) InvitationInfo(ctx context.Context, in *InvitationInfoRequest, opts ...grpc_go.CallOption) (*InvitationInfoResponse, common.ErrorWithAttachment) {
	out := new(InvitationInfoResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/InvitationInfo", in, out)
}

func (c *artistClient) ContractAdd(ctx context.Context, in *ContractAddRequest, opts ...grpc_go.CallOption) (*ContractAddResponse, common.ErrorWithAttachment) {
	out := new(ContractAddResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/ContractAdd", in, out)
}

func (c *artistClient) ContractUpdate(ctx context.Context, in *ContractAddRequest, opts ...grpc_go.CallOption) (*ContractUpdateResponse, common.ErrorWithAttachment) {
	out := new(ContractUpdateResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/ContractUpdate", in, out)
}

func (c *artistClient) ContractList(ctx context.Context, in *ContractListRequest, opts ...grpc_go.CallOption) (*ContractListResponse, common.ErrorWithAttachment) {
	out := new(ContractListResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/ContractList", in, out)
}

func (c *artistClient) ArtistChainUpdate(ctx context.Context, in *ArtistChainUpdateRequest, opts ...grpc_go.CallOption) (*ArtistChainUpdateResponse, common.ErrorWithAttachment) {
	out := new(ArtistChainUpdateResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/ArtistChainUpdate", in, out)
}

func (c *artistClient) ExportArtist(ctx context.Context, in *ExportArtistRequest, opts ...grpc_go.CallOption) (*ExportArtistResponse, common.ErrorWithAttachment) {
	out := new(ExportArtistResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/ExportArtist", in, out)
}

func (c *artistClient) ExportFieldList(ctx context.Context, in *ExportFieldListRequest, opts ...grpc_go.CallOption) (*ExportFieldListResponse, common.ErrorWithAttachment) {
	out := new(ExportFieldListResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/ExportFieldList", in, out)
}

func (c *artistClient) GetInfoByUuids(ctx context.Context, in *GetInfoByUuidsRequest, opts ...grpc_go.CallOption) (*GetInfoByUuidsResponse, common.ErrorWithAttachment) {
	out := new(GetInfoByUuidsResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/GetInfoByUuids", in, out)
}

func (c *artistClient) GetCardIdWithImg(ctx context.Context, in *GetCardIdWithImgReq, opts ...grpc_go.CallOption) (*GetCardIdWithImgResp, common.ErrorWithAttachment) {
	out := new(GetCardIdWithImgResp)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/GetCardIdWithImg", in, out)
}

func (c *artistClient) IdcardConvertArtistId(ctx context.Context, in *IdcardConvertArtistIdRequest, opts ...grpc_go.CallOption) (*IdcardConvertArtistIdResponse, common.ErrorWithAttachment) {
	out := new(IdcardConvertArtistIdResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/IdcardConvertArtistId", in, out)
}

func (c *artistClient) CheckCaaCertNum(ctx context.Context, in *CheckCaaCertNumRequest, opts ...grpc_go.CallOption) (*CheckCaaCertNumResponse, common.ErrorWithAttachment) {
	out := new(CheckCaaCertNumResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/CheckCaaCertNum", in, out)
}

func (c *artistClient) ArtistDetailData(ctx context.Context, in *ArtistDetailDataReq, opts ...grpc_go.CallOption) (*ArtistDetailDataResp, common.ErrorWithAttachment) {
	out := new(ArtistDetailDataResp)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/ArtistDetailData", in, out)
}

func (c *artistClient) UpdateInsource(ctx context.Context, in *UpdateInsourceReq, opts ...grpc_go.CallOption) (*UpdateInsourceNoneResp, common.ErrorWithAttachment) {
	out := new(UpdateInsourceNoneResp)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/UpdateInsource", in, out)
}

func (c *artistClient) DelContract(ctx context.Context, in *DelContractReq, opts ...grpc_go.CallOption) (*DelContractResp, common.ErrorWithAttachment) {
	out := new(DelContractResp)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/DelContract", in, out)
}

func (c *artistClient) FindArtistUsePhone(ctx context.Context, in *FindArtistUsePhoneRequest, opts ...grpc_go.CallOption) (*ArtistDataResponse, common.ErrorWithAttachment) {
	out := new(ArtistDataResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/FindArtistUsePhone", in, out)
}

func (c *artistClient) AddIndexesLog(ctx context.Context, in *AddIndexesLogReq, opts ...grpc_go.CallOption) (*emptypb.Empty, common.ErrorWithAttachment) {
	out := new(emptypb.Empty)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/AddIndexesLog", in, out)
}

func (c *artistClient) IndexesLog(ctx context.Context, in *IndexesLogReq, opts ...grpc_go.CallOption) (*IndexesLogResp, common.ErrorWithAttachment) {
	out := new(IndexesLogResp)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/IndexesLog", in, out)
}

func (c *artistClient) GetIndex(ctx context.Context, in *GetIndexRequest, opts ...grpc_go.CallOption) (*GetIndexDataResp, common.ErrorWithAttachment) {
	out := new(GetIndexDataResp)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/GetIndex", in, out)
}

func (c *artistClient) SaveCertInfo(ctx context.Context, in *SaveCertInfoRequest, opts ...grpc_go.CallOption) (*SaveCertInfoResponse, common.ErrorWithAttachment) {
	out := new(SaveCertInfoResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/SaveCertInfo", in, out)
}

func (c *artistClient) CreateChainMnemonic(ctx context.Context, in *CreateChainMnemonicRequest, opts ...grpc_go.CallOption) (*CreateChainMnemonicResponse, common.ErrorWithAttachment) {
	out := new(CreateChainMnemonicResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/CreateChainMnemonic", in, out)
}

func (c *artistClient) HonorCount(ctx context.Context, in *HonorCountReq, opts ...grpc_go.CallOption) (*HonorCountResp, common.ErrorWithAttachment) {
	out := new(HonorCountResp)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/HonorCount", in, out)
}

func (c *artistClient) SaveLowArtist(ctx context.Context, in *LowArtist, opts ...grpc_go.CallOption) (*SaveLowArtistResp, common.ErrorWithAttachment) {
	out := new(SaveLowArtistResp)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/SaveLowArtist", in, out)
}

func (c *artistClient) BatchUpdLowArtist(ctx context.Context, in *BatchUpdLowArtistReq, opts ...grpc_go.CallOption) (*SaveLowArtistResp, common.ErrorWithAttachment) {
	out := new(SaveLowArtistResp)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/BatchUpdLowArtist", in, out)
}

func (c *artistClient) LowArtistList(ctx context.Context, in *LowArtistListReq, opts ...grpc_go.CallOption) (*LowArtistListResp, common.ErrorWithAttachment) {
	out := new(LowArtistListResp)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/LowArtistList", in, out)
}

func (c *artistClient) GetExtData(ctx context.Context, in *GetExtDataReq, opts ...grpc_go.CallOption) (*GetExtDataResp, common.ErrorWithAttachment) {
	out := new(GetExtDataResp)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/GetExtData", in, out)
}

func (c *artistClient) BlackListUpdate(ctx context.Context, in *BlackListUpdateReq, opts ...grpc_go.CallOption) (*BlackListUpdateResp, common.ErrorWithAttachment) {
	out := new(BlackListUpdateResp)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/BlackListUpdate", in, out)
}

func (c *artistClient) BlackList(ctx context.Context, in *BlackListReq, opts ...grpc_go.CallOption) (*BlackListResp, common.ErrorWithAttachment) {
	out := new(BlackListResp)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/BlackList", in, out)
}

func (c *artistClient) ArtistSearch(ctx context.Context, in *ArtistSearchReq, opts ...grpc_go.CallOption) (*ArtistSearchResp, common.ErrorWithAttachment) {
	out := new(ArtistSearchResp)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/ArtistSearch", in, out)
}

func (c *artistClient) OneQuery(ctx context.Context, in *OneQueryReq, opts ...grpc_go.CallOption) (*OneQueryResp, common.ErrorWithAttachment) {
	out := new(OneQueryResp)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/OneQuery", in, out)
}

func (c *artistClient) OneQueryOld(ctx context.Context, in *OneQueryReq, opts ...grpc_go.CallOption) (*OneQueryResp, common.ErrorWithAttachment) {
	out := new(OneQueryResp)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/OneQueryOld", in, out)
}

func (c *artistClient) OneQueryAudit(ctx context.Context, in *OneQueryAuditReq, opts ...grpc_go.CallOption) (*OneQueryAuditResp, common.ErrorWithAttachment) {
	out := new(OneQueryAuditResp)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/OneQueryAudit", in, out)
}

func (c *artistClient) CreateArtistOneQueryCheckRecord(ctx context.Context, in *ArtistOneQueryCheckRecordData, opts ...grpc_go.CallOption) (*emptypb.Empty, common.ErrorWithAttachment) {
	out := new(emptypb.Empty)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/CreateArtistOneQueryCheckRecord", in, out)
}

func (c *artistClient) UpdateArtistOneQueryCheckRecord(ctx context.Context, in *ArtistOneQueryCheckRecordData, opts ...grpc_go.CallOption) (*emptypb.Empty, common.ErrorWithAttachment) {
	out := new(emptypb.Empty)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/UpdateArtistOneQueryCheckRecord", in, out)
}

func (c *artistClient) SaveArtistOneQueryCheckRecord(ctx context.Context, in *ArtistOneQueryCheckRecordData, opts ...grpc_go.CallOption) (*emptypb.Empty, common.ErrorWithAttachment) {
	out := new(emptypb.Empty)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/SaveArtistOneQueryCheckRecord", in, out)
}

func (c *artistClient) DeleteArtistOneQueryCheckRecord(ctx context.Context, in *DeleteArtistOneQueryCheckRecordRequest, opts ...grpc_go.CallOption) (*emptypb.Empty, common.ErrorWithAttachment) {
	out := new(emptypb.Empty)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/DeleteArtistOneQueryCheckRecord", in, out)
}

func (c *artistClient) GetArtistOneQueryCheckRecordDetail(ctx context.Context, in *GetArtistOneQueryCheckRecordByIdRequest, opts ...grpc_go.CallOption) (*ArtistOneQueryCheckRecordData, common.ErrorWithAttachment) {
	out := new(ArtistOneQueryCheckRecordData)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/GetArtistOneQueryCheckRecordDetail", in, out)
}

func (c *artistClient) GetArtistOneQueryCheckRecordList(ctx context.Context, in *GetArtistOneQueryCheckRecordListRequest, opts ...grpc_go.CallOption) (*GetArtistOneQueryCheckRecordListResp, common.ErrorWithAttachment) {
	out := new(GetArtistOneQueryCheckRecordListResp)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/GetArtistOneQueryCheckRecordList", in, out)
}

func (c *artistClient) UpdateOneQueryCheckStatusThatUserNotPerformed(ctx context.Context, in *UpdateOneQueryCheckStatusThatUserNotPerformedReq, opts ...grpc_go.CallOption) (*UpdateOneQueryCheckStatusThatUserNotPerformedResp, common.ErrorWithAttachment) {
	out := new(UpdateOneQueryCheckStatusThatUserNotPerformedResp)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/UpdateOneQueryCheckStatusThatUserNotPerformed", in, out)
}

func (c *artistClient) GetInvitationLetterList(ctx context.Context, in *GetInvitationLetterListRequest, opts ...grpc_go.CallOption) (*GetInvitationLetterListResp, common.ErrorWithAttachment) {
	out := new(GetInvitationLetterListResp)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/GetInvitationLetterList", in, out)
}

func (c *artistClient) UpdateInvitationLetterTravelInfo(ctx context.Context, in *UpdateInvitationLetterTravelInfoReq, opts ...grpc_go.CallOption) (*emptypb.Empty, common.ErrorWithAttachment) {
	out := new(emptypb.Empty)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/UpdateInvitationLetterTravelInfo", in, out)
}

func (c *artistClient) GetInvitationLetterTravelInfo(ctx context.Context, in *GetInvitationLetterTravelInfoReq, opts ...grpc_go.CallOption) (*GetInvitationLetterTravelInfoResp, common.ErrorWithAttachment) {
	out := new(GetInvitationLetterTravelInfoResp)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/GetInvitationLetterTravelInfo", in, out)
}

func (c *artistClient) GetInvitationLetterDetail(ctx context.Context, in *GetInvitationLetterDetailReq, opts ...grpc_go.CallOption) (*GetInvitationLetterDetailResp, common.ErrorWithAttachment) {
	out := new(GetInvitationLetterDetailResp)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/GetInvitationLetterDetail", in, out)
}

func (c *artistClient) UpdateInvitationLetter(ctx context.Context, in *UpdateInvitationLetterReq, opts ...grpc_go.CallOption) (*UpdateInvitationLetterResp, common.ErrorWithAttachment) {
	out := new(UpdateInvitationLetterResp)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/UpdateInvitationLetter", in, out)
}

func (c *artistClient) InvitationLetterSetToHistory(ctx context.Context, in *InvitationLetterSetToHistoryReq, opts ...grpc_go.CallOption) (*InvitationLetterSetToHistoryResp, common.ErrorWithAttachment) {
	out := new(InvitationLetterSetToHistoryResp)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/InvitationLetterSetToHistory", in, out)
}

func (c *artistClient) RecordInfoByArtistUid(ctx context.Context, in *RecordInfoByArtistUidReq, opts ...grpc_go.CallOption) (*RecordInfoByArtistUidResp, common.ErrorWithAttachment) {
	out := new(RecordInfoByArtistUidResp)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/RecordInfoByArtistUid", in, out)
}

func (c *artistClient) OneQueryAllArtist(ctx context.Context, in *OneQueryAllArtistReq, opts ...grpc_go.CallOption) (*OneQueryResp, common.ErrorWithAttachment) {
	out := new(OneQueryResp)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/OneQueryAllArtist", in, out)
}

func (c *artistClient) SaveArtistProfile(ctx context.Context, in *SaveArtistProfileReq, opts ...grpc_go.CallOption) (*SaveArtistProfileResp, common.ErrorWithAttachment) {
	out := new(SaveArtistProfileResp)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/SaveArtistProfile", in, out)
}

func (c *artistClient) BindContractArtwork(ctx context.Context, in *BindContractArtworkReq, opts ...grpc_go.CallOption) (*BindContractArtworkResp, common.ErrorWithAttachment) {
	out := new(BindContractArtworkResp)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/BindContractArtwork", in, out)
}

func (c *artistClient) ArtistBaseInfo(ctx context.Context, in *ArtistBaseInfoReq, opts ...grpc_go.CallOption) (*ArtistBaseInfoResp, common.ErrorWithAttachment) {
	out := new(ArtistBaseInfoResp)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/ArtistBaseInfo", in, out)
}

func (c *artistClient) ArtistBaseList(ctx context.Context, in *ArtistBaseListReq, opts ...grpc_go.CallOption) (*ArtistBaseListResp, common.ErrorWithAttachment) {
	out := new(ArtistBaseListResp)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/ArtistBaseList", in, out)
}

func (c *artistClient) SaveInterviewVideo(ctx context.Context, in *SaveInterviewVideoReq, opts ...grpc_go.CallOption) (*SaveInterviewVideoResp, common.ErrorWithAttachment) {
	out := new(SaveInterviewVideoResp)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/SaveInterviewVideo", in, out)
}

func (c *artistClient) SaveArtistSupplementInfo(ctx context.Context, in *SaveArtistSupplementInfoReq, opts ...grpc_go.CallOption) (*SaveArtistSupplementInfoResp, common.ErrorWithAttachment) {
	out := new(SaveArtistSupplementInfoResp)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/SaveArtistSupplementInfo", in, out)
}

func (c *artistClient) SaveArtistBankInfo(ctx context.Context, in *SaveArtistBankInfoRequest, opts ...grpc_go.CallOption) (*SaveArtistBankInfoResponse, common.ErrorWithAttachment) {
	out := new(SaveArtistBankInfoResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/SaveArtistBankInfo", in, out)
}

func (c *artistClient) SaveArtistResume(ctx context.Context, in *SaveArtistResumeRequest, opts ...grpc_go.CallOption) (*SaveArtistResumeResponse, common.ErrorWithAttachment) {
	out := new(SaveArtistResumeResponse)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/SaveArtistResume", in, out)
}

func (c *artistClient) UpdateArtistJoinShow(ctx context.Context, in *UpdateArtistJoinShowReq, opts ...grpc_go.CallOption) (*UpdateArtistJoinShowResp, common.ErrorWithAttachment) {
	out := new(UpdateArtistJoinShowResp)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/UpdateArtistJoinShow", in, out)
}

func (c *artistClient) UpdateArtistContract(ctx context.Context, in *UpdateArtistContractReq, opts ...grpc_go.CallOption) (*UpdateArtistContractResp, common.ErrorWithAttachment) {
	out := new(UpdateArtistContractResp)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/UpdateArtistContract", in, out)
}

func (c *artistClient) GetArtistProfileList(ctx context.Context, in *GetArtistProfileListRequest, opts ...grpc_go.CallOption) (*GetArtistProfileListResp, common.ErrorWithAttachment) {
	out := new(GetArtistProfileListResp)
	interfaceKey := ctx.Value(constant.InterfaceKey).(string)
	return out, c.cc.Invoke(ctx, "/"+interfaceKey+"/GetArtistProfileList", in, out)
}

// ArtistServer is the server API for Artist service.
// All implementations must embed UnimplementedArtistServer
// for forward compatibility
type ArtistServer interface {
	ArtistList(context.Context, *ArtistListRequest) (*ArtistListResponse, error)
	CreateProfile(context.Context, *ProfileRequest) (*ProfileResponse, error)
	UpdateProfile(context.Context, *ProfileRequest) (*ProfileResponse, error)
	UpdateMedia(context.Context, *MediaRequest) (*MediaResponse, error)
	UpdateIndex(context.Context, *IndexRequest) (*IndexResponse, error)
	UpdateHonor(context.Context, *HonorRequest) (*HonorResponse, error)
	UploadMedia(context.Context, *UploadMediaRequest) (*UploadMediaResponse, error)
	ArtistDetail(context.Context, *DetailRequest) (*DetailResponse, error)
	DelArtist(context.Context, *DelRequest) (*DelResponse, error)
	HonorDel(context.Context, *HonorDelRequest) (*HonorDelResponse, error)
	CreateArtistBatch(context.Context, *CreateArtistBatchRequest) (*CreateArtistBatchResponse, error)
	ArtistIdName(context.Context, *ArtIdNameRequest) (*ArtIdNameResponse, error)
	ArtistInfo(context.Context, *ArtistInfoRequest) (*ArtistInfoResponse, error)
	ArtistData(context.Context, *ArtistDataRequest) (*ArtistDataResponse, error)
	InvitationAdd(context.Context, *InvitationAddRequest) (*InvitationAddResponse, error)
	InvitationUpdate(context.Context, *InvitationUpdateRequest) (*InvitationUpdateResponse, error)
	InvitationDel(context.Context, *InvitationDelRequest) (*InvitationDelResponse, error)
	InvitationList(context.Context, *InvitationListRequest) (*InvitationListResponse, error)
	InvitationInfo(context.Context, *InvitationInfoRequest) (*InvitationInfoResponse, error)
	ContractAdd(context.Context, *ContractAddRequest) (*ContractAddResponse, error)
	ContractUpdate(context.Context, *ContractAddRequest) (*ContractUpdateResponse, error)
	ContractList(context.Context, *ContractListRequest) (*ContractListResponse, error)
	ArtistChainUpdate(context.Context, *ArtistChainUpdateRequest) (*ArtistChainUpdateResponse, error)
	ExportArtist(context.Context, *ExportArtistRequest) (*ExportArtistResponse, error)
	ExportFieldList(context.Context, *ExportFieldListRequest) (*ExportFieldListResponse, error)
	GetInfoByUuids(context.Context, *GetInfoByUuidsRequest) (*GetInfoByUuidsResponse, error)
	GetCardIdWithImg(context.Context, *GetCardIdWithImgReq) (*GetCardIdWithImgResp, error)
	IdcardConvertArtistId(context.Context, *IdcardConvertArtistIdRequest) (*IdcardConvertArtistIdResponse, error)
	CheckCaaCertNum(context.Context, *CheckCaaCertNumRequest) (*CheckCaaCertNumResponse, error)
	ArtistDetailData(context.Context, *ArtistDetailDataReq) (*ArtistDetailDataResp, error)
	UpdateInsource(context.Context, *UpdateInsourceReq) (*UpdateInsourceNoneResp, error)
	DelContract(context.Context, *DelContractReq) (*DelContractResp, error)
	FindArtistUsePhone(context.Context, *FindArtistUsePhoneRequest) (*ArtistDataResponse, error)
	AddIndexesLog(context.Context, *AddIndexesLogReq) (*emptypb.Empty, error)
	IndexesLog(context.Context, *IndexesLogReq) (*IndexesLogResp, error)
	GetIndex(context.Context, *GetIndexRequest) (*GetIndexDataResp, error)
	SaveCertInfo(context.Context, *SaveCertInfoRequest) (*SaveCertInfoResponse, error)
	CreateChainMnemonic(context.Context, *CreateChainMnemonicRequest) (*CreateChainMnemonicResponse, error)
	HonorCount(context.Context, *HonorCountReq) (*HonorCountResp, error)
	SaveLowArtist(context.Context, *LowArtist) (*SaveLowArtistResp, error)
	BatchUpdLowArtist(context.Context, *BatchUpdLowArtistReq) (*SaveLowArtistResp, error)
	LowArtistList(context.Context, *LowArtistListReq) (*LowArtistListResp, error)
	GetExtData(context.Context, *GetExtDataReq) (*GetExtDataResp, error)
	BlackListUpdate(context.Context, *BlackListUpdateReq) (*BlackListUpdateResp, error)
	BlackList(context.Context, *BlackListReq) (*BlackListResp, error)
	ArtistSearch(context.Context, *ArtistSearchReq) (*ArtistSearchResp, error)
	// -------------------------------------------一键查询审批---------------------------------------------------------
	OneQuery(context.Context, *OneQueryReq) (*OneQueryResp, error)
	OneQueryOld(context.Context, *OneQueryReq) (*OneQueryResp, error)
	OneQueryAudit(context.Context, *OneQueryAuditReq) (*OneQueryAuditResp, error)
	CreateArtistOneQueryCheckRecord(context.Context, *ArtistOneQueryCheckRecordData) (*emptypb.Empty, error)
	UpdateArtistOneQueryCheckRecord(context.Context, *ArtistOneQueryCheckRecordData) (*emptypb.Empty, error)
	SaveArtistOneQueryCheckRecord(context.Context, *ArtistOneQueryCheckRecordData) (*emptypb.Empty, error)
	DeleteArtistOneQueryCheckRecord(context.Context, *DeleteArtistOneQueryCheckRecordRequest) (*emptypb.Empty, error)
	GetArtistOneQueryCheckRecordDetail(context.Context, *GetArtistOneQueryCheckRecordByIdRequest) (*ArtistOneQueryCheckRecordData, error)
	GetArtistOneQueryCheckRecordList(context.Context, *GetArtistOneQueryCheckRecordListRequest) (*GetArtistOneQueryCheckRecordListResp, error)
	UpdateOneQueryCheckStatusThatUserNotPerformed(context.Context, *UpdateOneQueryCheckStatusThatUserNotPerformedReq) (*UpdateOneQueryCheckStatusThatUserNotPerformedResp, error)
	// -------------------------------------------邀请函新增函数---------------------------------------------------------
	GetInvitationLetterList(context.Context, *GetInvitationLetterListRequest) (*GetInvitationLetterListResp, error)
	UpdateInvitationLetterTravelInfo(context.Context, *UpdateInvitationLetterTravelInfoReq) (*emptypb.Empty, error)
	GetInvitationLetterTravelInfo(context.Context, *GetInvitationLetterTravelInfoReq) (*GetInvitationLetterTravelInfoResp, error)
	GetInvitationLetterDetail(context.Context, *GetInvitationLetterDetailReq) (*GetInvitationLetterDetailResp, error)
	UpdateInvitationLetter(context.Context, *UpdateInvitationLetterReq) (*UpdateInvitationLetterResp, error)
	InvitationLetterSetToHistory(context.Context, *InvitationLetterSetToHistoryReq) (*InvitationLetterSetToHistoryResp, error)
	RecordInfoByArtistUid(context.Context, *RecordInfoByArtistUidReq) (*RecordInfoByArtistUidResp, error)
	OneQueryAllArtist(context.Context, *OneQueryAllArtistReq) (*OneQueryResp, error)
	SaveArtistProfile(context.Context, *SaveArtistProfileReq) (*SaveArtistProfileResp, error)
	BindContractArtwork(context.Context, *BindContractArtworkReq) (*BindContractArtworkResp, error)
	ArtistBaseInfo(context.Context, *ArtistBaseInfoReq) (*ArtistBaseInfoResp, error)
	ArtistBaseList(context.Context, *ArtistBaseListReq) (*ArtistBaseListResp, error)
	SaveInterviewVideo(context.Context, *SaveInterviewVideoReq) (*SaveInterviewVideoResp, error)
	SaveArtistSupplementInfo(context.Context, *SaveArtistSupplementInfoReq) (*SaveArtistSupplementInfoResp, error)
	SaveArtistBankInfo(context.Context, *SaveArtistBankInfoRequest) (*SaveArtistBankInfoResponse, error)
	SaveArtistResume(context.Context, *SaveArtistResumeRequest) (*SaveArtistResumeResponse, error)
	UpdateArtistJoinShow(context.Context, *UpdateArtistJoinShowReq) (*UpdateArtistJoinShowResp, error)
	UpdateArtistContract(context.Context, *UpdateArtistContractReq) (*UpdateArtistContractResp, error)
	GetArtistProfileList(context.Context, *GetArtistProfileListRequest) (*GetArtistProfileListResp, error)
	mustEmbedUnimplementedArtistServer()
}

// UnimplementedArtistServer must be embedded to have forward compatible implementations.
type UnimplementedArtistServer struct {
	proxyImpl protocol.Invoker
}

func (UnimplementedArtistServer) ArtistList(context.Context, *ArtistListRequest) (*ArtistListResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ArtistList not implemented")
}
func (UnimplementedArtistServer) CreateProfile(context.Context, *ProfileRequest) (*ProfileResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateProfile not implemented")
}
func (UnimplementedArtistServer) UpdateProfile(context.Context, *ProfileRequest) (*ProfileResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateProfile not implemented")
}
func (UnimplementedArtistServer) UpdateMedia(context.Context, *MediaRequest) (*MediaResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateMedia not implemented")
}
func (UnimplementedArtistServer) UpdateIndex(context.Context, *IndexRequest) (*IndexResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateIndex not implemented")
}
func (UnimplementedArtistServer) UpdateHonor(context.Context, *HonorRequest) (*HonorResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateHonor not implemented")
}
func (UnimplementedArtistServer) UploadMedia(context.Context, *UploadMediaRequest) (*UploadMediaResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UploadMedia not implemented")
}
func (UnimplementedArtistServer) ArtistDetail(context.Context, *DetailRequest) (*DetailResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ArtistDetail not implemented")
}
func (UnimplementedArtistServer) DelArtist(context.Context, *DelRequest) (*DelResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DelArtist not implemented")
}
func (UnimplementedArtistServer) HonorDel(context.Context, *HonorDelRequest) (*HonorDelResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method HonorDel not implemented")
}
func (UnimplementedArtistServer) CreateArtistBatch(context.Context, *CreateArtistBatchRequest) (*CreateArtistBatchResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateArtistBatch not implemented")
}
func (UnimplementedArtistServer) ArtistIdName(context.Context, *ArtIdNameRequest) (*ArtIdNameResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ArtistIdName not implemented")
}
func (UnimplementedArtistServer) ArtistInfo(context.Context, *ArtistInfoRequest) (*ArtistInfoResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ArtistInfo not implemented")
}
func (UnimplementedArtistServer) ArtistData(context.Context, *ArtistDataRequest) (*ArtistDataResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ArtistData not implemented")
}
func (UnimplementedArtistServer) InvitationAdd(context.Context, *InvitationAddRequest) (*InvitationAddResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method InvitationAdd not implemented")
}
func (UnimplementedArtistServer) InvitationUpdate(context.Context, *InvitationUpdateRequest) (*InvitationUpdateResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method InvitationUpdate not implemented")
}
func (UnimplementedArtistServer) InvitationDel(context.Context, *InvitationDelRequest) (*InvitationDelResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method InvitationDel not implemented")
}
func (UnimplementedArtistServer) InvitationList(context.Context, *InvitationListRequest) (*InvitationListResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method InvitationList not implemented")
}
func (UnimplementedArtistServer) InvitationInfo(context.Context, *InvitationInfoRequest) (*InvitationInfoResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method InvitationInfo not implemented")
}
func (UnimplementedArtistServer) ContractAdd(context.Context, *ContractAddRequest) (*ContractAddResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ContractAdd not implemented")
}
func (UnimplementedArtistServer) ContractUpdate(context.Context, *ContractAddRequest) (*ContractUpdateResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ContractUpdate not implemented")
}
func (UnimplementedArtistServer) ContractList(context.Context, *ContractListRequest) (*ContractListResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ContractList not implemented")
}
func (UnimplementedArtistServer) ArtistChainUpdate(context.Context, *ArtistChainUpdateRequest) (*ArtistChainUpdateResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ArtistChainUpdate not implemented")
}
func (UnimplementedArtistServer) ExportArtist(context.Context, *ExportArtistRequest) (*ExportArtistResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ExportArtist not implemented")
}
func (UnimplementedArtistServer) ExportFieldList(context.Context, *ExportFieldListRequest) (*ExportFieldListResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ExportFieldList not implemented")
}
func (UnimplementedArtistServer) GetInfoByUuids(context.Context, *GetInfoByUuidsRequest) (*GetInfoByUuidsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetInfoByUuids not implemented")
}
func (UnimplementedArtistServer) GetCardIdWithImg(context.Context, *GetCardIdWithImgReq) (*GetCardIdWithImgResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetCardIdWithImg not implemented")
}
func (UnimplementedArtistServer) IdcardConvertArtistId(context.Context, *IdcardConvertArtistIdRequest) (*IdcardConvertArtistIdResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method IdcardConvertArtistId not implemented")
}
func (UnimplementedArtistServer) CheckCaaCertNum(context.Context, *CheckCaaCertNumRequest) (*CheckCaaCertNumResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CheckCaaCertNum not implemented")
}
func (UnimplementedArtistServer) ArtistDetailData(context.Context, *ArtistDetailDataReq) (*ArtistDetailDataResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ArtistDetailData not implemented")
}
func (UnimplementedArtistServer) UpdateInsource(context.Context, *UpdateInsourceReq) (*UpdateInsourceNoneResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateInsource not implemented")
}
func (UnimplementedArtistServer) DelContract(context.Context, *DelContractReq) (*DelContractResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DelContract not implemented")
}
func (UnimplementedArtistServer) FindArtistUsePhone(context.Context, *FindArtistUsePhoneRequest) (*ArtistDataResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FindArtistUsePhone not implemented")
}
func (UnimplementedArtistServer) AddIndexesLog(context.Context, *AddIndexesLogReq) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AddIndexesLog not implemented")
}
func (UnimplementedArtistServer) IndexesLog(context.Context, *IndexesLogReq) (*IndexesLogResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method IndexesLog not implemented")
}
func (UnimplementedArtistServer) GetIndex(context.Context, *GetIndexRequest) (*GetIndexDataResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetIndex not implemented")
}
func (UnimplementedArtistServer) SaveCertInfo(context.Context, *SaveCertInfoRequest) (*SaveCertInfoResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SaveCertInfo not implemented")
}
func (UnimplementedArtistServer) CreateChainMnemonic(context.Context, *CreateChainMnemonicRequest) (*CreateChainMnemonicResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateChainMnemonic not implemented")
}
func (UnimplementedArtistServer) HonorCount(context.Context, *HonorCountReq) (*HonorCountResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method HonorCount not implemented")
}
func (UnimplementedArtistServer) SaveLowArtist(context.Context, *LowArtist) (*SaveLowArtistResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SaveLowArtist not implemented")
}
func (UnimplementedArtistServer) BatchUpdLowArtist(context.Context, *BatchUpdLowArtistReq) (*SaveLowArtistResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method BatchUpdLowArtist not implemented")
}
func (UnimplementedArtistServer) LowArtistList(context.Context, *LowArtistListReq) (*LowArtistListResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method LowArtistList not implemented")
}
func (UnimplementedArtistServer) GetExtData(context.Context, *GetExtDataReq) (*GetExtDataResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetExtData not implemented")
}
func (UnimplementedArtistServer) BlackListUpdate(context.Context, *BlackListUpdateReq) (*BlackListUpdateResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method BlackListUpdate not implemented")
}
func (UnimplementedArtistServer) BlackList(context.Context, *BlackListReq) (*BlackListResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method BlackList not implemented")
}
func (UnimplementedArtistServer) ArtistSearch(context.Context, *ArtistSearchReq) (*ArtistSearchResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ArtistSearch not implemented")
}
func (UnimplementedArtistServer) OneQuery(context.Context, *OneQueryReq) (*OneQueryResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method OneQuery not implemented")
}
func (UnimplementedArtistServer) OneQueryOld(context.Context, *OneQueryReq) (*OneQueryResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method OneQueryOld not implemented")
}
func (UnimplementedArtistServer) OneQueryAudit(context.Context, *OneQueryAuditReq) (*OneQueryAuditResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method OneQueryAudit not implemented")
}
func (UnimplementedArtistServer) CreateArtistOneQueryCheckRecord(context.Context, *ArtistOneQueryCheckRecordData) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateArtistOneQueryCheckRecord not implemented")
}
func (UnimplementedArtistServer) UpdateArtistOneQueryCheckRecord(context.Context, *ArtistOneQueryCheckRecordData) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateArtistOneQueryCheckRecord not implemented")
}
func (UnimplementedArtistServer) SaveArtistOneQueryCheckRecord(context.Context, *ArtistOneQueryCheckRecordData) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SaveArtistOneQueryCheckRecord not implemented")
}
func (UnimplementedArtistServer) DeleteArtistOneQueryCheckRecord(context.Context, *DeleteArtistOneQueryCheckRecordRequest) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteArtistOneQueryCheckRecord not implemented")
}
func (UnimplementedArtistServer) GetArtistOneQueryCheckRecordDetail(context.Context, *GetArtistOneQueryCheckRecordByIdRequest) (*ArtistOneQueryCheckRecordData, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetArtistOneQueryCheckRecordDetail not implemented")
}
func (UnimplementedArtistServer) GetArtistOneQueryCheckRecordList(context.Context, *GetArtistOneQueryCheckRecordListRequest) (*GetArtistOneQueryCheckRecordListResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetArtistOneQueryCheckRecordList not implemented")
}
func (UnimplementedArtistServer) UpdateOneQueryCheckStatusThatUserNotPerformed(context.Context, *UpdateOneQueryCheckStatusThatUserNotPerformedReq) (*UpdateOneQueryCheckStatusThatUserNotPerformedResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateOneQueryCheckStatusThatUserNotPerformed not implemented")
}
func (UnimplementedArtistServer) GetInvitationLetterList(context.Context, *GetInvitationLetterListRequest) (*GetInvitationLetterListResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetInvitationLetterList not implemented")
}
func (UnimplementedArtistServer) UpdateInvitationLetterTravelInfo(context.Context, *UpdateInvitationLetterTravelInfoReq) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateInvitationLetterTravelInfo not implemented")
}
func (UnimplementedArtistServer) GetInvitationLetterTravelInfo(context.Context, *GetInvitationLetterTravelInfoReq) (*GetInvitationLetterTravelInfoResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetInvitationLetterTravelInfo not implemented")
}
func (UnimplementedArtistServer) GetInvitationLetterDetail(context.Context, *GetInvitationLetterDetailReq) (*GetInvitationLetterDetailResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetInvitationLetterDetail not implemented")
}
func (UnimplementedArtistServer) UpdateInvitationLetter(context.Context, *UpdateInvitationLetterReq) (*UpdateInvitationLetterResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateInvitationLetter not implemented")
}
func (UnimplementedArtistServer) InvitationLetterSetToHistory(context.Context, *InvitationLetterSetToHistoryReq) (*InvitationLetterSetToHistoryResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method InvitationLetterSetToHistory not implemented")
}
func (UnimplementedArtistServer) RecordInfoByArtistUid(context.Context, *RecordInfoByArtistUidReq) (*RecordInfoByArtistUidResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RecordInfoByArtistUid not implemented")
}
func (UnimplementedArtistServer) OneQueryAllArtist(context.Context, *OneQueryAllArtistReq) (*OneQueryResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method OneQueryAllArtist not implemented")
}
func (UnimplementedArtistServer) SaveArtistProfile(context.Context, *SaveArtistProfileReq) (*SaveArtistProfileResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SaveArtistProfile not implemented")
}
func (UnimplementedArtistServer) BindContractArtwork(context.Context, *BindContractArtworkReq) (*BindContractArtworkResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method BindContractArtwork not implemented")
}
func (UnimplementedArtistServer) ArtistBaseInfo(context.Context, *ArtistBaseInfoReq) (*ArtistBaseInfoResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ArtistBaseInfo not implemented")
}
func (UnimplementedArtistServer) ArtistBaseList(context.Context, *ArtistBaseListReq) (*ArtistBaseListResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ArtistBaseList not implemented")
}
func (UnimplementedArtistServer) SaveInterviewVideo(context.Context, *SaveInterviewVideoReq) (*SaveInterviewVideoResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SaveInterviewVideo not implemented")
}
func (UnimplementedArtistServer) SaveArtistSupplementInfo(context.Context, *SaveArtistSupplementInfoReq) (*SaveArtistSupplementInfoResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SaveArtistSupplementInfo not implemented")
}
func (UnimplementedArtistServer) SaveArtistBankInfo(context.Context, *SaveArtistBankInfoRequest) (*SaveArtistBankInfoResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SaveArtistBankInfo not implemented")
}
func (UnimplementedArtistServer) SaveArtistResume(context.Context, *SaveArtistResumeRequest) (*SaveArtistResumeResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SaveArtistResume not implemented")
}
func (UnimplementedArtistServer) UpdateArtistJoinShow(context.Context, *UpdateArtistJoinShowReq) (*UpdateArtistJoinShowResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateArtistJoinShow not implemented")
}
func (UnimplementedArtistServer) UpdateArtistContract(context.Context, *UpdateArtistContractReq) (*UpdateArtistContractResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateArtistContract not implemented")
}
func (UnimplementedArtistServer) GetArtistProfileList(context.Context, *GetArtistProfileListRequest) (*GetArtistProfileListResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetArtistProfileList not implemented")
}
func (s *UnimplementedArtistServer) XXX_SetProxyImpl(impl protocol.Invoker) {
	s.proxyImpl = impl
}

func (s *UnimplementedArtistServer) XXX_GetProxyImpl() protocol.Invoker {
	return s.proxyImpl
}

func (s *UnimplementedArtistServer) XXX_ServiceDesc() *grpc_go.ServiceDesc {
	return &Artist_ServiceDesc
}
func (s *UnimplementedArtistServer) XXX_InterfaceName() string {
	return "artist.Artist"
}

func (UnimplementedArtistServer) mustEmbedUnimplementedArtistServer() {}

// UnsafeArtistServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to ArtistServer will
// result in compilation errors.
type UnsafeArtistServer interface {
	mustEmbedUnimplementedArtistServer()
}

func RegisterArtistServer(s grpc_go.ServiceRegistrar, srv ArtistServer) {
	s.RegisterService(&Artist_ServiceDesc, srv)
}

func _Artist_ArtistList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(ArtistListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("ArtistList", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Artist_CreateProfile_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(ProfileRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("CreateProfile", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Artist_UpdateProfile_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(ProfileRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("UpdateProfile", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Artist_UpdateMedia_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(MediaRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("UpdateMedia", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Artist_UpdateIndex_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(IndexRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("UpdateIndex", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Artist_UpdateHonor_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(HonorRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("UpdateHonor", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Artist_UploadMedia_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(UploadMediaRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("UploadMedia", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Artist_ArtistDetail_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(DetailRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("ArtistDetail", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Artist_DelArtist_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(DelRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("DelArtist", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Artist_HonorDel_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(HonorDelRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("HonorDel", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Artist_CreateArtistBatch_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateArtistBatchRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("CreateArtistBatch", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Artist_ArtistIdName_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(ArtIdNameRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("ArtistIdName", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Artist_ArtistInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(ArtistInfoRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("ArtistInfo", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Artist_ArtistData_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(ArtistDataRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("ArtistData", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Artist_InvitationAdd_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(InvitationAddRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("InvitationAdd", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Artist_InvitationUpdate_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(InvitationUpdateRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("InvitationUpdate", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Artist_InvitationDel_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(InvitationDelRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("InvitationDel", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Artist_InvitationList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(InvitationListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("InvitationList", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Artist_InvitationInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(InvitationInfoRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("InvitationInfo", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Artist_ContractAdd_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(ContractAddRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("ContractAdd", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Artist_ContractUpdate_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(ContractAddRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("ContractUpdate", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Artist_ContractList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(ContractListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("ContractList", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Artist_ArtistChainUpdate_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(ArtistChainUpdateRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("ArtistChainUpdate", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Artist_ExportArtist_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(ExportArtistRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("ExportArtist", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Artist_ExportFieldList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(ExportFieldListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("ExportFieldList", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Artist_GetInfoByUuids_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetInfoByUuidsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("GetInfoByUuids", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Artist_GetCardIdWithImg_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetCardIdWithImgReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("GetCardIdWithImg", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Artist_IdcardConvertArtistId_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(IdcardConvertArtistIdRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("IdcardConvertArtistId", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Artist_CheckCaaCertNum_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(CheckCaaCertNumRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("CheckCaaCertNum", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Artist_ArtistDetailData_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(ArtistDetailDataReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("ArtistDetailData", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Artist_UpdateInsource_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateInsourceReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("UpdateInsource", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Artist_DelContract_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(DelContractReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("DelContract", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Artist_FindArtistUsePhone_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(FindArtistUsePhoneRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("FindArtistUsePhone", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Artist_AddIndexesLog_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddIndexesLogReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("AddIndexesLog", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Artist_IndexesLog_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(IndexesLogReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("IndexesLog", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Artist_GetIndex_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetIndexRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("GetIndex", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Artist_SaveCertInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(SaveCertInfoRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("SaveCertInfo", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Artist_CreateChainMnemonic_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateChainMnemonicRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("CreateChainMnemonic", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Artist_HonorCount_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(HonorCountReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("HonorCount", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Artist_SaveLowArtist_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(LowArtist)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("SaveLowArtist", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Artist_BatchUpdLowArtist_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchUpdLowArtistReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("BatchUpdLowArtist", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Artist_LowArtistList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(LowArtistListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("LowArtistList", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Artist_GetExtData_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetExtDataReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("GetExtData", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Artist_BlackListUpdate_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(BlackListUpdateReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("BlackListUpdate", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Artist_BlackList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(BlackListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("BlackList", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Artist_ArtistSearch_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(ArtistSearchReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("ArtistSearch", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Artist_OneQuery_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(OneQueryReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("OneQuery", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Artist_OneQueryOld_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(OneQueryReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("OneQueryOld", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Artist_OneQueryAudit_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(OneQueryAuditReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("OneQueryAudit", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Artist_CreateArtistOneQueryCheckRecord_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(ArtistOneQueryCheckRecordData)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("CreateArtistOneQueryCheckRecord", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Artist_UpdateArtistOneQueryCheckRecord_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(ArtistOneQueryCheckRecordData)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("UpdateArtistOneQueryCheckRecord", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Artist_SaveArtistOneQueryCheckRecord_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(ArtistOneQueryCheckRecordData)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("SaveArtistOneQueryCheckRecord", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Artist_DeleteArtistOneQueryCheckRecord_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteArtistOneQueryCheckRecordRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("DeleteArtistOneQueryCheckRecord", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Artist_GetArtistOneQueryCheckRecordDetail_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetArtistOneQueryCheckRecordByIdRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("GetArtistOneQueryCheckRecordDetail", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Artist_GetArtistOneQueryCheckRecordList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetArtistOneQueryCheckRecordListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("GetArtistOneQueryCheckRecordList", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Artist_UpdateOneQueryCheckStatusThatUserNotPerformed_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateOneQueryCheckStatusThatUserNotPerformedReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("UpdateOneQueryCheckStatusThatUserNotPerformed", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Artist_GetInvitationLetterList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetInvitationLetterListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("GetInvitationLetterList", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Artist_UpdateInvitationLetterTravelInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateInvitationLetterTravelInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("UpdateInvitationLetterTravelInfo", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Artist_GetInvitationLetterTravelInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetInvitationLetterTravelInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("GetInvitationLetterTravelInfo", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Artist_GetInvitationLetterDetail_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetInvitationLetterDetailReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("GetInvitationLetterDetail", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Artist_UpdateInvitationLetter_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateInvitationLetterReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("UpdateInvitationLetter", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Artist_InvitationLetterSetToHistory_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(InvitationLetterSetToHistoryReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("InvitationLetterSetToHistory", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Artist_RecordInfoByArtistUid_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(RecordInfoByArtistUidReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("RecordInfoByArtistUid", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Artist_OneQueryAllArtist_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(OneQueryAllArtistReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("OneQueryAllArtist", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Artist_SaveArtistProfile_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(SaveArtistProfileReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("SaveArtistProfile", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Artist_BindContractArtwork_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(BindContractArtworkReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("BindContractArtwork", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Artist_ArtistBaseInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(ArtistBaseInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("ArtistBaseInfo", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Artist_ArtistBaseList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(ArtistBaseListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("ArtistBaseList", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Artist_SaveInterviewVideo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(SaveInterviewVideoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("SaveInterviewVideo", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Artist_SaveArtistSupplementInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(SaveArtistSupplementInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("SaveArtistSupplementInfo", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Artist_SaveArtistBankInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(SaveArtistBankInfoRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("SaveArtistBankInfo", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Artist_SaveArtistResume_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(SaveArtistResumeRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("SaveArtistResume", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Artist_UpdateArtistJoinShow_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateArtistJoinShowReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("UpdateArtistJoinShow", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Artist_UpdateArtistContract_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateArtistContractReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("UpdateArtistContract", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

func _Artist_GetArtistProfileList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc_go.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetArtistProfileListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	base := srv.(dubbo3.Dubbo3GrpcService)
	args := []interface{}{}
	args = append(args, in)
	md, _ := metadata.FromIncomingContext(ctx)
	invAttachment := make(map[string]interface{}, len(md))
	for k, v := range md {
		invAttachment[k] = v
	}
	invo := invocation.NewRPCInvocation("GetArtistProfileList", args, invAttachment)
	if interceptor == nil {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	info := &grpc_go.UnaryServerInfo{
		Server:     srv,
		FullMethod: ctx.Value("XXX_TRIPLE_GO_INTERFACE_NAME").(string),
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		result := base.XXX_GetProxyImpl().Invoke(ctx, invo)
		return result, result.Error()
	}
	return interceptor(ctx, in, info, handler)
}

// Artist_ServiceDesc is the grpc_go.ServiceDesc for Artist service.
// It's only intended for direct use with grpc_go.RegisterService,
// and not to be introspected or modified (even as a copy)
var Artist_ServiceDesc = grpc_go.ServiceDesc{
	ServiceName: "artist.Artist",
	HandlerType: (*ArtistServer)(nil),
	Methods: []grpc_go.MethodDesc{
		{
			MethodName: "ArtistList",
			Handler:    _Artist_ArtistList_Handler,
		},
		{
			MethodName: "CreateProfile",
			Handler:    _Artist_CreateProfile_Handler,
		},
		{
			MethodName: "UpdateProfile",
			Handler:    _Artist_UpdateProfile_Handler,
		},
		{
			MethodName: "UpdateMedia",
			Handler:    _Artist_UpdateMedia_Handler,
		},
		{
			MethodName: "UpdateIndex",
			Handler:    _Artist_UpdateIndex_Handler,
		},
		{
			MethodName: "UpdateHonor",
			Handler:    _Artist_UpdateHonor_Handler,
		},
		{
			MethodName: "UploadMedia",
			Handler:    _Artist_UploadMedia_Handler,
		},
		{
			MethodName: "ArtistDetail",
			Handler:    _Artist_ArtistDetail_Handler,
		},
		{
			MethodName: "DelArtist",
			Handler:    _Artist_DelArtist_Handler,
		},
		{
			MethodName: "HonorDel",
			Handler:    _Artist_HonorDel_Handler,
		},
		{
			MethodName: "CreateArtistBatch",
			Handler:    _Artist_CreateArtistBatch_Handler,
		},
		{
			MethodName: "ArtistIdName",
			Handler:    _Artist_ArtistIdName_Handler,
		},
		{
			MethodName: "ArtistInfo",
			Handler:    _Artist_ArtistInfo_Handler,
		},
		{
			MethodName: "ArtistData",
			Handler:    _Artist_ArtistData_Handler,
		},
		{
			MethodName: "InvitationAdd",
			Handler:    _Artist_InvitationAdd_Handler,
		},
		{
			MethodName: "InvitationUpdate",
			Handler:    _Artist_InvitationUpdate_Handler,
		},
		{
			MethodName: "InvitationDel",
			Handler:    _Artist_InvitationDel_Handler,
		},
		{
			MethodName: "InvitationList",
			Handler:    _Artist_InvitationList_Handler,
		},
		{
			MethodName: "InvitationInfo",
			Handler:    _Artist_InvitationInfo_Handler,
		},
		{
			MethodName: "ContractAdd",
			Handler:    _Artist_ContractAdd_Handler,
		},
		{
			MethodName: "ContractUpdate",
			Handler:    _Artist_ContractUpdate_Handler,
		},
		{
			MethodName: "ContractList",
			Handler:    _Artist_ContractList_Handler,
		},
		{
			MethodName: "ArtistChainUpdate",
			Handler:    _Artist_ArtistChainUpdate_Handler,
		},
		{
			MethodName: "ExportArtist",
			Handler:    _Artist_ExportArtist_Handler,
		},
		{
			MethodName: "ExportFieldList",
			Handler:    _Artist_ExportFieldList_Handler,
		},
		{
			MethodName: "GetInfoByUuids",
			Handler:    _Artist_GetInfoByUuids_Handler,
		},
		{
			MethodName: "GetCardIdWithImg",
			Handler:    _Artist_GetCardIdWithImg_Handler,
		},
		{
			MethodName: "IdcardConvertArtistId",
			Handler:    _Artist_IdcardConvertArtistId_Handler,
		},
		{
			MethodName: "CheckCaaCertNum",
			Handler:    _Artist_CheckCaaCertNum_Handler,
		},
		{
			MethodName: "ArtistDetailData",
			Handler:    _Artist_ArtistDetailData_Handler,
		},
		{
			MethodName: "UpdateInsource",
			Handler:    _Artist_UpdateInsource_Handler,
		},
		{
			MethodName: "DelContract",
			Handler:    _Artist_DelContract_Handler,
		},
		{
			MethodName: "FindArtistUsePhone",
			Handler:    _Artist_FindArtistUsePhone_Handler,
		},
		{
			MethodName: "AddIndexesLog",
			Handler:    _Artist_AddIndexesLog_Handler,
		},
		{
			MethodName: "IndexesLog",
			Handler:    _Artist_IndexesLog_Handler,
		},
		{
			MethodName: "GetIndex",
			Handler:    _Artist_GetIndex_Handler,
		},
		{
			MethodName: "SaveCertInfo",
			Handler:    _Artist_SaveCertInfo_Handler,
		},
		{
			MethodName: "CreateChainMnemonic",
			Handler:    _Artist_CreateChainMnemonic_Handler,
		},
		{
			MethodName: "HonorCount",
			Handler:    _Artist_HonorCount_Handler,
		},
		{
			MethodName: "SaveLowArtist",
			Handler:    _Artist_SaveLowArtist_Handler,
		},
		{
			MethodName: "BatchUpdLowArtist",
			Handler:    _Artist_BatchUpdLowArtist_Handler,
		},
		{
			MethodName: "LowArtistList",
			Handler:    _Artist_LowArtistList_Handler,
		},
		{
			MethodName: "GetExtData",
			Handler:    _Artist_GetExtData_Handler,
		},
		{
			MethodName: "BlackListUpdate",
			Handler:    _Artist_BlackListUpdate_Handler,
		},
		{
			MethodName: "BlackList",
			Handler:    _Artist_BlackList_Handler,
		},
		{
			MethodName: "ArtistSearch",
			Handler:    _Artist_ArtistSearch_Handler,
		},
		{
			MethodName: "OneQuery",
			Handler:    _Artist_OneQuery_Handler,
		},
		{
			MethodName: "OneQueryOld",
			Handler:    _Artist_OneQueryOld_Handler,
		},
		{
			MethodName: "OneQueryAudit",
			Handler:    _Artist_OneQueryAudit_Handler,
		},
		{
			MethodName: "CreateArtistOneQueryCheckRecord",
			Handler:    _Artist_CreateArtistOneQueryCheckRecord_Handler,
		},
		{
			MethodName: "UpdateArtistOneQueryCheckRecord",
			Handler:    _Artist_UpdateArtistOneQueryCheckRecord_Handler,
		},
		{
			MethodName: "SaveArtistOneQueryCheckRecord",
			Handler:    _Artist_SaveArtistOneQueryCheckRecord_Handler,
		},
		{
			MethodName: "DeleteArtistOneQueryCheckRecord",
			Handler:    _Artist_DeleteArtistOneQueryCheckRecord_Handler,
		},
		{
			MethodName: "GetArtistOneQueryCheckRecordDetail",
			Handler:    _Artist_GetArtistOneQueryCheckRecordDetail_Handler,
		},
		{
			MethodName: "GetArtistOneQueryCheckRecordList",
			Handler:    _Artist_GetArtistOneQueryCheckRecordList_Handler,
		},
		{
			MethodName: "UpdateOneQueryCheckStatusThatUserNotPerformed",
			Handler:    _Artist_UpdateOneQueryCheckStatusThatUserNotPerformed_Handler,
		},
		{
			MethodName: "GetInvitationLetterList",
			Handler:    _Artist_GetInvitationLetterList_Handler,
		},
		{
			MethodName: "UpdateInvitationLetterTravelInfo",
			Handler:    _Artist_UpdateInvitationLetterTravelInfo_Handler,
		},
		{
			MethodName: "GetInvitationLetterTravelInfo",
			Handler:    _Artist_GetInvitationLetterTravelInfo_Handler,
		},
		{
			MethodName: "GetInvitationLetterDetail",
			Handler:    _Artist_GetInvitationLetterDetail_Handler,
		},
		{
			MethodName: "UpdateInvitationLetter",
			Handler:    _Artist_UpdateInvitationLetter_Handler,
		},
		{
			MethodName: "InvitationLetterSetToHistory",
			Handler:    _Artist_InvitationLetterSetToHistory_Handler,
		},
		{
			MethodName: "RecordInfoByArtistUid",
			Handler:    _Artist_RecordInfoByArtistUid_Handler,
		},
		{
			MethodName: "OneQueryAllArtist",
			Handler:    _Artist_OneQueryAllArtist_Handler,
		},
		{
			MethodName: "SaveArtistProfile",
			Handler:    _Artist_SaveArtistProfile_Handler,
		},
		{
			MethodName: "BindContractArtwork",
			Handler:    _Artist_BindContractArtwork_Handler,
		},
		{
			MethodName: "ArtistBaseInfo",
			Handler:    _Artist_ArtistBaseInfo_Handler,
		},
		{
			MethodName: "ArtistBaseList",
			Handler:    _Artist_ArtistBaseList_Handler,
		},
		{
			MethodName: "SaveInterviewVideo",
			Handler:    _Artist_SaveInterviewVideo_Handler,
		},
		{
			MethodName: "SaveArtistSupplementInfo",
			Handler:    _Artist_SaveArtistSupplementInfo_Handler,
		},
		{
			MethodName: "SaveArtistBankInfo",
			Handler:    _Artist_SaveArtistBankInfo_Handler,
		},
		{
			MethodName: "SaveArtistResume",
			Handler:    _Artist_SaveArtistResume_Handler,
		},
		{
			MethodName: "UpdateArtistJoinShow",
			Handler:    _Artist_UpdateArtistJoinShow_Handler,
		},
		{
			MethodName: "UpdateArtistContract",
			Handler:    _Artist_UpdateArtistContract_Handler,
		},
		{
			MethodName: "GetArtistProfileList",
			Handler:    _Artist_GetArtistProfileList_Handler,
		},
	},
	Streams:  []grpc_go.StreamDesc{},
	Metadata: "pb/artist.proto",
}
