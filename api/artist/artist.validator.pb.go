// Code generated by protoc-gen-gogo. DO NOT EDIT.
// source: pb/artist.proto

package artist

import (
	fmt "fmt"
	math "math"
	proto "github.com/golang/protobuf/proto"
	_ "github.com/mwitkow/go-proto-validators"
	_ "google.golang.org/protobuf/types/descriptorpb"
	_ "google.golang.org/protobuf/types/known/emptypb"
	github_com_mwitkow_go_proto_validators "github.com/mwitkow/go-proto-validators"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

func (this *ArtistListRequest) Validate() error {
	return nil
}
func (this *ArtistListResponse) Validate() error {
	for _, item := range this.Data {
		if item != nil {
			if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(item); err != nil {
				return github_com_mwitkow_go_proto_validators.FieldError("Data", err)
			}
		}
	}
	return nil
}
func (this *ProfileRequest) Validate() error {
	if this.Name == "" {
		return github_com_mwitkow_go_proto_validators.FieldError("Name", fmt.Errorf(`画家名字不能为空`))
	}
	if !(this.JoinShow > 0) {
		return github_com_mwitkow_go_proto_validators.FieldError("JoinShow", fmt.Errorf(`请选择是否参加画展`))
	}
	return nil
}
func (this *ProfileResponse) Validate() error {
	if this.DataInfo != nil {
		if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(this.DataInfo); err != nil {
			return github_com_mwitkow_go_proto_validators.FieldError("DataInfo", err)
		}
	}
	if this.OldBank != nil {
		if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(this.OldBank); err != nil {
			return github_com_mwitkow_go_proto_validators.FieldError("OldBank", err)
		}
	}
	return nil
}
func (this *ProfileResponse_Data) Validate() error {
	return nil
}
func (this *ProfileResponse_OldBankInfo) Validate() error {
	return nil
}
func (this *CardIdInfo) Validate() error {
	return nil
}
func (this *MediaRequest) Validate() error {
	if this.Uid == "" {
		return github_com_mwitkow_go_proto_validators.FieldError("Uid", fmt.Errorf(`画家Id不能为空`))
	}
	for _, item := range this.InterviewVideos {
		if item != nil {
			if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(item); err != nil {
				return github_com_mwitkow_go_proto_validators.FieldError("InterviewVideos", err)
			}
		}
	}
	for _, item := range this.ThirdPartyVideos {
		if item != nil {
			if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(item); err != nil {
				return github_com_mwitkow_go_proto_validators.FieldError("ThirdPartyVideos", err)
			}
		}
	}
	if this.CardIdInfo != nil {
		if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(this.CardIdInfo); err != nil {
			return github_com_mwitkow_go_proto_validators.FieldError("CardIdInfo", err)
		}
	}
	return nil
}
func (this *MediaRequest_VideosMap) Validate() error {
	return nil
}
func (this *IndexRequest) Validate() error {
	if this.Uid == "" {
		return github_com_mwitkow_go_proto_validators.FieldError("Uid", fmt.Errorf(`画家Id不能为空`))
	}
	return nil
}
func (this *IndexResponse) Validate() error {
	if this.DataInfo != nil {
		if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(this.DataInfo); err != nil {
			return github_com_mwitkow_go_proto_validators.FieldError("DataInfo", err)
		}
	}
	return nil
}
func (this *IndexResponse_Data) Validate() error {
	return nil
}
func (this *MediaResponse) Validate() error {
	if this.DataInfo != nil {
		if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(this.DataInfo); err != nil {
			return github_com_mwitkow_go_proto_validators.FieldError("DataInfo", err)
		}
	}
	return nil
}
func (this *MediaResponse_Data) Validate() error {
	return nil
}
func (this *HonorRequest) Validate() error {
	if this.Uid == "" {
		return github_com_mwitkow_go_proto_validators.FieldError("Uid", fmt.Errorf(`画家Id不能为空`))
	}
	return nil
}
func (this *HonorResponse) Validate() error {
	if this.DataInfo != nil {
		if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(this.DataInfo); err != nil {
			return github_com_mwitkow_go_proto_validators.FieldError("DataInfo", err)
		}
	}
	return nil
}
func (this *HonorResponse_HonorResData) Validate() error {
	return nil
}
func (this *UploadMediaRequest) Validate() error {
	return nil
}
func (this *UploadMediaResponse) Validate() error {
	return nil
}
func (this *DetailRequest) Validate() error {
	if this.Uid == "" {
		return github_com_mwitkow_go_proto_validators.FieldError("Uid", fmt.Errorf(`画家Id不能为空`))
	}
	return nil
}
func (this *DetailResponse) Validate() error {
	if this.ProfileInfo != nil {
		if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(this.ProfileInfo); err != nil {
			return github_com_mwitkow_go_proto_validators.FieldError("ProfileInfo", err)
		}
	}
	if this.MediaInfo != nil {
		if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(this.MediaInfo); err != nil {
			return github_com_mwitkow_go_proto_validators.FieldError("MediaInfo", err)
		}
	}
	for _, item := range this.HonorInfo {
		if item != nil {
			if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(item); err != nil {
				return github_com_mwitkow_go_proto_validators.FieldError("HonorInfo", err)
			}
		}
	}
	return nil
}
func (this *DelRequest) Validate() error {
	if this.Uid == "" {
		return github_com_mwitkow_go_proto_validators.FieldError("Uid", fmt.Errorf(`画家Id不能为空`))
	}
	return nil
}
func (this *DelResponse) Validate() error {
	return nil
}
func (this *HonorDelRequest) Validate() error {
	return nil
}
func (this *HonorDelResponse) Validate() error {
	return nil
}
func (this *ArtistInfo) Validate() error {
	return nil
}
func (this *CreateArtistBatchRequest) Validate() error {
	for _, item := range this.ArtistInfoBatch {
		if item != nil {
			if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(item); err != nil {
				return github_com_mwitkow_go_proto_validators.FieldError("ArtistInfoBatch", err)
			}
		}
	}
	return nil
}
func (this *CreateArtistBatchResponse) Validate() error {
	return nil
}
func (this *ArtIdNameRequest) Validate() error {
	return nil
}
func (this *ArtIdNameResponse) Validate() error {
	for _, item := range this.Items {
		if item != nil {
			if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(item); err != nil {
				return github_com_mwitkow_go_proto_validators.FieldError("Items", err)
			}
		}
	}
	return nil
}
func (this *ArtIdNameResponse_IdKvName) Validate() error {
	return nil
}
func (this *ArtistInfoRequest) Validate() error {
	if this.Uid == "" {
		return github_com_mwitkow_go_proto_validators.FieldError("Uid", fmt.Errorf(`画家Id不能为空`))
	}
	return nil
}
func (this *ArtistInfoResponse) Validate() error {
	if this.ProfileInfo != nil {
		if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(this.ProfileInfo); err != nil {
			return github_com_mwitkow_go_proto_validators.FieldError("ProfileInfo", err)
		}
	}
	if this.MediaRequest != nil {
		if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(this.MediaRequest); err != nil {
			return github_com_mwitkow_go_proto_validators.FieldError("MediaRequest", err)
		}
	}
	if this.ArtistChainInfo != nil {
		if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(this.ArtistChainInfo); err != nil {
			return github_com_mwitkow_go_proto_validators.FieldError("ArtistChainInfo", err)
		}
	}
	if this.CardIdInfo != nil {
		if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(this.CardIdInfo); err != nil {
			return github_com_mwitkow_go_proto_validators.FieldError("CardIdInfo", err)
		}
	}
	return nil
}
func (this *ArtistInfoResponse_ArtistChainField) Validate() error {
	return nil
}
func (this *ArtistDataRequest) Validate() error {
	for _, item := range this.Uids {
		if item == "" {
			return github_com_mwitkow_go_proto_validators.FieldError("Uids", fmt.Errorf(`画家Id不能为空`))
		}
	}
	return nil
}
func (this *ArtistDataResponse) Validate() error {
	for _, item := range this.Data {
		if item != nil {
			if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(item); err != nil {
				return github_com_mwitkow_go_proto_validators.FieldError("Data", err)
			}
		}
	}
	return nil
}
func (this *ArtistDataResponseInfo) Validate() error {
	if this.ProfileInfo != nil {
		if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(this.ProfileInfo); err != nil {
			return github_com_mwitkow_go_proto_validators.FieldError("ProfileInfo", err)
		}
	}
	if this.MediaRequest != nil {
		if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(this.MediaRequest); err != nil {
			return github_com_mwitkow_go_proto_validators.FieldError("MediaRequest", err)
		}
	}
	return nil
}
func (this *InvitationAddRequest) Validate() error {
	for _, item := range this.ArtistUuids {
		if item == "" {
			return github_com_mwitkow_go_proto_validators.FieldError("ArtistUuids", fmt.Errorf(`请选择画家`))
		}
	}
	if this.Address == "" {
		return github_com_mwitkow_go_proto_validators.FieldError("Address", fmt.Errorf(`地址不能为空`))
	}
	if this.ShowUid == "" {
		return github_com_mwitkow_go_proto_validators.FieldError("ShowUid", fmt.Errorf(`画展包uid不能为空`))
	}
	return nil
}
func (this *InvitationAddResponse) Validate() error {
	return nil
}
func (this *InvitationUpdateRequest) Validate() error {
	if this.Uuid == "" {
		return github_com_mwitkow_go_proto_validators.FieldError("Uuid", fmt.Errorf(`标识错误`))
	}
	if this.ArtistUuid == "" {
		return github_com_mwitkow_go_proto_validators.FieldError("ArtistUuid", fmt.Errorf(`请选择画家`))
	}
	if this.Address == "" {
		return github_com_mwitkow_go_proto_validators.FieldError("Address", fmt.Errorf(`地址不能为空`))
	}
	if this.InvitationDesc == "" {
		return github_com_mwitkow_go_proto_validators.FieldError("InvitationDesc", fmt.Errorf(`邀请函主题不能为空`))
	}
	if this.ShowDesc == "" {
		return github_com_mwitkow_go_proto_validators.FieldError("ShowDesc", fmt.Errorf(`画展主题不能为空`))
	}
	if this.ShowTime == "" {
		return github_com_mwitkow_go_proto_validators.FieldError("ShowTime", fmt.Errorf(`时间不能为空`))
	}
	return nil
}
func (this *InvitationUpdateResponse) Validate() error {
	return nil
}
func (this *InvitationDelRequest) Validate() error {
	return nil
}
func (this *InvitationDelResponse) Validate() error {
	return nil
}
func (this *InvitationListRequest) Validate() error {
	if this.ArtistUuid == "" {
		return github_com_mwitkow_go_proto_validators.FieldError("ArtistUuid", fmt.Errorf(`请选择画家`))
	}
	return nil
}
func (this *InvitationListResponse) Validate() error {
	for _, item := range this.Data {
		if item != nil {
			if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(item); err != nil {
				return github_com_mwitkow_go_proto_validators.FieldError("Data", err)
			}
		}
	}
	return nil
}
func (this *InvitationInfoRequest) Validate() error {
	if this.Uuid == "" {
		return github_com_mwitkow_go_proto_validators.FieldError("Uuid", fmt.Errorf(`请选择数据`))
	}
	return nil
}
func (this *InvitationInfoResponse) Validate() error {
	if this.Info != nil {
		if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(this.Info); err != nil {
			return github_com_mwitkow_go_proto_validators.FieldError("Info", err)
		}
	}
	return nil
}
func (this *ContractAddRequest) Validate() error {
	for _, item := range this.BindArtworkData {
		if item != nil {
			if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(item); err != nil {
				return github_com_mwitkow_go_proto_validators.FieldError("BindArtworkData", err)
			}
		}
	}
	return nil
}
func (this *ContractAddRequest_ArtworkInfo) Validate() error {
	return nil
}
func (this *ContractAddResponse) Validate() error {
	return nil
}
func (this *ContractUpdateResponse) Validate() error {
	return nil
}
func (this *ContractListRequest) Validate() error {
	if this.Uid == "" {
		return github_com_mwitkow_go_proto_validators.FieldError("Uid", fmt.Errorf(`请选择画家`))
	}
	return nil
}
func (this *ContractListResponse) Validate() error {
	for _, item := range this.Data {
		if item != nil {
			if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(item); err != nil {
				return github_com_mwitkow_go_proto_validators.FieldError("Data", err)
			}
		}
	}
	return nil
}
func (this *ArtistChainUpdateRequest) Validate() error {
	if this.ArtistUuid == "" {
		return github_com_mwitkow_go_proto_validators.FieldError("ArtistUuid", fmt.Errorf(`请选择画家`))
	}
	return nil
}
func (this *ArtistChainUpdateResponse) Validate() error {
	return nil
}
func (this *ExportArtistRequest) Validate() error {
	if this.ColumnId == "" {
		return github_com_mwitkow_go_proto_validators.FieldError("ColumnId", fmt.Errorf(`请选择字段`))
	}
	return nil
}
func (this *ExportArtistResponse) Validate() error {
	for _, item := range this.Data {
		if item != nil {
			if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(item); err != nil {
				return github_com_mwitkow_go_proto_validators.FieldError("Data", err)
			}
		}
	}
	return nil
}
func (this *ExportArtistResponse_Info) Validate() error {
	return nil
}
func (this *ExportFieldListRequest) Validate() error {
	if !(this.ExportType > 0) {
		return github_com_mwitkow_go_proto_validators.FieldError("ExportType", fmt.Errorf(`导出类型必须大于0`))
	}
	return nil
}
func (this *ExportFieldListResponse) Validate() error {
	for _, item := range this.Data {
		if item != nil {
			if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(item); err != nil {
				return github_com_mwitkow_go_proto_validators.FieldError("Data", err)
			}
		}
	}
	return nil
}
func (this *ExportFieldListResponse_Info) Validate() error {
	return nil
}
func (this *GetInfoByUuidsRequest) Validate() error {
	return nil
}
func (this *GetInfoByUuidsResponse) Validate() error {
	for _, item := range this.Data {
		if item != nil {
			if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(item); err != nil {
				return github_com_mwitkow_go_proto_validators.FieldError("Data", err)
			}
		}
	}
	return nil
}
func (this *GetInfoByUuidsResponse_Info) Validate() error {
	return nil
}
func (this *GetCardIdWithImgReq) Validate() error {
	if this.CardFace == "" {
		return github_com_mwitkow_go_proto_validators.FieldError("CardFace", fmt.Errorf(`身份证照片不能为空`))
	}
	return nil
}
func (this *GetCardIdWithImgResp) Validate() error {
	return nil
}
func (this *ArtistDetailDataReq) Validate() error {
	return nil
}
func (this *ArtistDetailDataResp) Validate() error {
	for _, item := range this.Data {
		if item != nil {
			if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(item); err != nil {
				return github_com_mwitkow_go_proto_validators.FieldError("Data", err)
			}
		}
	}
	return nil
}
func (this *ArtistDetailDataResp_Info) Validate() error {
	if this.ProfileInfo != nil {
		if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(this.ProfileInfo); err != nil {
			return github_com_mwitkow_go_proto_validators.FieldError("ProfileInfo", err)
		}
	}
	if this.MediaInfo != nil {
		if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(this.MediaInfo); err != nil {
			return github_com_mwitkow_go_proto_validators.FieldError("MediaInfo", err)
		}
	}
	for _, item := range this.HonorInfo {
		if item != nil {
			if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(item); err != nil {
				return github_com_mwitkow_go_proto_validators.FieldError("HonorInfo", err)
			}
		}
	}
	return nil
}
func (this *IdcardConvertArtistIdRequest) Validate() error {
	return nil
}
func (this *IdcardConvertArtistIdResponse) Validate() error {
	return nil
}
func (this *CheckCaaCertNumRequest) Validate() error {
	return nil
}
func (this *CheckCaaCertNumResponse) Validate() error {
	return nil
}
func (this *UpdateInsourceReq) Validate() error {
	return nil
}
func (this *UpdateInsourceNoneResp) Validate() error {
	return nil
}
func (this *DelContractReq) Validate() error {
	if !(this.Id > 0) {
		return github_com_mwitkow_go_proto_validators.FieldError("Id", fmt.Errorf(`合同唯一标识缺失`))
	}
	return nil
}
func (this *DelContractResp) Validate() error {
	return nil
}
func (this *FindArtistUsePhoneRequest) Validate() error {
	return nil
}
func (this *OneQueryReq) Validate() error {
	return nil
}
func (this *OneQueryResp) Validate() error {
	for _, item := range this.Data {
		if item != nil {
			if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(item); err != nil {
				return github_com_mwitkow_go_proto_validators.FieldError("Data", err)
			}
		}
	}
	return nil
}
func (this *OneQueryResp_Info) Validate() error {
	return nil
}
func (this *AddIndexesLogReq) Validate() error {
	return nil
}
func (this *IndexesLogReq) Validate() error {
	return nil
}
func (this *IndexesLogResp) Validate() error {
	for _, item := range this.SendLog {
		if item != nil {
			if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(item); err != nil {
				return github_com_mwitkow_go_proto_validators.FieldError("SendLog", err)
			}
		}
	}
	for _, item := range this.EditLog {
		if item != nil {
			if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(item); err != nil {
				return github_com_mwitkow_go_proto_validators.FieldError("EditLog", err)
			}
		}
	}
	return nil
}
func (this *IndexesLogResp_SendInfo) Validate() error {
	return nil
}
func (this *IndexesLogResp_EditInfo) Validate() error {
	return nil
}
func (this *GetIndexRequest) Validate() error {
	if this.Uid == "" {
		return github_com_mwitkow_go_proto_validators.FieldError("Uid", fmt.Errorf(`画家Id不能为空`))
	}
	return nil
}
func (this *GetIndexDataResp) Validate() error {
	return nil
}
func (this *SaveCertInfoRequest) Validate() error {
	return nil
}
func (this *SaveCertInfoResponse) Validate() error {
	return nil
}
func (this *CreateChainMnemonicRequest) Validate() error {
	return nil
}
func (this *CreateChainMnemonicResponse) Validate() error {
	return nil
}
func (this *HonorCountReq) Validate() error {
	if this.ArtistUid == "" {
		return github_com_mwitkow_go_proto_validators.FieldError("ArtistUid", fmt.Errorf(`画家ID不能为空`))
	}
	return nil
}
func (this *HonorCountResp) Validate() error {
	return nil
}
func (this *LowArtist) Validate() error {
	for _, item := range this.BlackArtistData {
		if item != nil {
			if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(item); err != nil {
				return github_com_mwitkow_go_proto_validators.FieldError("BlackArtistData", err)
			}
		}
	}
	return nil
}
func (this *LowArtist_BlackArtistInfo) Validate() error {
	return nil
}
func (this *LowArtistListReq) Validate() error {
	return nil
}
func (this *LowArtistListResp) Validate() error {
	for _, item := range this.Data {
		if item != nil {
			if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(item); err != nil {
				return github_com_mwitkow_go_proto_validators.FieldError("Data", err)
			}
		}
	}
	return nil
}
func (this *BatchUpdLowArtistReq) Validate() error {
	return nil
}
func (this *SaveLowArtistResp) Validate() error {
	return nil
}
func (this *OneQueryAuditReq) Validate() error {
	return nil
}
func (this *OneQueryAuditResp) Validate() error {
	return nil
}
func (this *GetExtDataReq) Validate() error {
	return nil
}
func (this *ExtData) Validate() error {
	return nil
}
func (this *GetExtDataResp) Validate() error {
	if this.Data != nil {
		if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(this.Data); err != nil {
			return github_com_mwitkow_go_proto_validators.FieldError("Data", err)
		}
	}
	return nil
}
func (this *InvitationLetterData) Validate() error {
	return nil
}
func (this *ArtistOneQueryCheckRecordData) Validate() error {
	return nil
}
func (this *GetArtistOneQueryCheckRecordListRequest) Validate() error {
	if this.Query != nil {
		if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(this.Query); err != nil {
			return github_com_mwitkow_go_proto_validators.FieldError("Query", err)
		}
	}
	return nil
}
func (this *GetArtistOneQueryCheckRecordByIdRequest) Validate() error {
	return nil
}
func (this *DeleteArtistOneQueryCheckRecordRequest) Validate() error {
	return nil
}
func (this *GetArtistOneQueryCheckRecordListResp) Validate() error {
	for _, item := range this.List {
		if item != nil {
			if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(item); err != nil {
				return github_com_mwitkow_go_proto_validators.FieldError("List", err)
			}
		}
	}
	return nil
}
func (this *GetInvitationLetterListRequest) Validate() error {
	if this.Query != nil {
		if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(this.Query); err != nil {
			return github_com_mwitkow_go_proto_validators.FieldError("Query", err)
		}
	}
	return nil
}
func (this *GetInvitationLetterListResp) Validate() error {
	for _, item := range this.List {
		if item != nil {
			if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(item); err != nil {
				return github_com_mwitkow_go_proto_validators.FieldError("List", err)
			}
		}
	}
	return nil
}
func (this *UpdateOneQueryCheckStatusThatUserNotPerformedReq) Validate() error {
	return nil
}
func (this *UpdateOneQueryCheckStatusThatUserNotPerformedResp) Validate() error {
	return nil
}
func (this *InvitationLetterTravelInfo) Validate() error {
	return nil
}
func (this *UpdateInvitationLetterTravelInfoReq) Validate() error {
	if this.TravelInfo != nil {
		if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(this.TravelInfo); err != nil {
			return github_com_mwitkow_go_proto_validators.FieldError("TravelInfo", err)
		}
	}
	return nil
}
func (this *GetInvitationLetterTravelInfoReq) Validate() error {
	return nil
}
func (this *GetInvitationLetterTravelInfoResp) Validate() error {
	if this.Data != nil {
		if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(this.Data); err != nil {
			return github_com_mwitkow_go_proto_validators.FieldError("Data", err)
		}
	}
	return nil
}
func (this *GetInvitationLetterDetailReq) Validate() error {
	return nil
}
func (this *GetInvitationLetterDetailResp) Validate() error {
	return nil
}
func (this *UpdateInvitationLetterReq) Validate() error {
	return nil
}
func (this *UpdateInvitationLetterResp) Validate() error {
	return nil
}
func (this *RecordInfoByArtistUidReq) Validate() error {
	return nil
}
func (this *RecordInfoByArtistUidResp) Validate() error {
	return nil
}
func (this *OneQueryAllArtistReq) Validate() error {
	return nil
}
func (this *SaveArtistProfileReq) Validate() error {
	if this.ArtistUuid == "" {
		return github_com_mwitkow_go_proto_validators.FieldError("ArtistUuid", fmt.Errorf(`画家ID不能为空`))
	}
	return nil
}
func (this *SaveArtistProfileResp) Validate() error {
	return nil
}
func (this *BindContractArtworkReq) Validate() error {
	return nil
}
func (this *BindContractArtworkResp) Validate() error {
	return nil
}
func (this *ArtistBaseInfoReq) Validate() error {
	return nil
}
func (this *ArtistBaseInfoResp) Validate() error {
	return nil
}
func (this *ArtistBaseListReq) Validate() error {
	return nil
}
func (this *ArtistBaseListResp) Validate() error {
	for _, item := range this.Data {
		if item != nil {
			if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(item); err != nil {
				return github_com_mwitkow_go_proto_validators.FieldError("Data", err)
			}
		}
	}
	return nil
}
func (this *ArtistBaseListResp_Info) Validate() error {
	return nil
}
func (this *InvitationLetterSetToHistoryReq) Validate() error {
	return nil
}
func (this *InvitationLetterSetToHistoryResp) Validate() error {
	return nil
}
func (this *SaveInterviewVideoReq) Validate() error {
	return nil
}
func (this *SaveInterviewVideoResp) Validate() error {
	return nil
}
func (this *SaveArtistSupplementInfoReq) Validate() error {
	return nil
}
func (this *SaveArtistSupplementInfoResp) Validate() error {
	return nil
}
func (this *BlackListUpdateReq) Validate() error {
	if this.ArtistUuid == "" {
		return github_com_mwitkow_go_proto_validators.FieldError("ArtistUuid", fmt.Errorf(`画家不能为空`))
	}
	if !(this.Action > 0) {
		return github_com_mwitkow_go_proto_validators.FieldError("Action", fmt.Errorf(`操作类型`))
	}
	return nil
}
func (this *BlackListUpdateResp) Validate() error {
	return nil
}
func (this *BlackListReq) Validate() error {
	return nil
}
func (this *BlackListResp) Validate() error {
	for _, item := range this.Data {
		if item != nil {
			if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(item); err != nil {
				return github_com_mwitkow_go_proto_validators.FieldError("Data", err)
			}
		}
	}
	return nil
}
func (this *BlackListResp_Info) Validate() error {
	return nil
}
func (this *SaveArtistBankInfoRequest) Validate() error {
	return nil
}
func (this *SaveArtistBankInfoResponse) Validate() error {
	return nil
}
func (this *SaveArtistResumeRequest) Validate() error {
	return nil
}
func (this *SaveArtistResumeResponse) Validate() error {
	return nil
}
func (this *ArtistSearchReq) Validate() error {
	return nil
}
func (this *ArtistSearchResp) Validate() error {
	for _, item := range this.Data {
		if item != nil {
			if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(item); err != nil {
				return github_com_mwitkow_go_proto_validators.FieldError("Data", err)
			}
		}
	}
	return nil
}
func (this *ArtistSearchResp_Info) Validate() error {
	return nil
}
func (this *UpdateArtistJoinShowReq) Validate() error {
	return nil
}
func (this *UpdateArtistJoinShowResp) Validate() error {
	return nil
}
func (this *UpdateArtistContractReq) Validate() error {
	return nil
}
func (this *UpdateArtistContractResp) Validate() error {
	return nil
}
func (this *ArtistProfileData) Validate() error {
	return nil
}
func (this *GetArtistProfileListRequest) Validate() error {
	if this.Query != nil {
		if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(this.Query); err != nil {
			return github_com_mwitkow_go_proto_validators.FieldError("Query", err)
		}
	}
	return nil
}
func (this *GetArtistProfileListResp) Validate() error {
	for _, item := range this.List {
		if item != nil {
			if err := github_com_mwitkow_go_proto_validators.CallValidatorIfExists(item); err != nil {
				return github_com_mwitkow_go_proto_validators.FieldError("List", err)
			}
		}
	}
	return nil
}
