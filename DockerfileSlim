FROM busybox:glibc

COPY ./conf/Shanghai /usr/share/zoneinfo/Asia/Shanghai
COPY ./conf/certs /etc/ssl/certs
RUN ln -sf /usr/share/zoneinfo/Asia/Shanghai /etc/localtime && echo 'Asia/Shanghai' >/etc/timezone

#ENV TZ Asia/Shanghai
ARG GIT_COMMIT=default_value
ENV GIT_COMMIT=$GIT_COMMIT

WORKDIR /app/main-client
#通过名称引用
COPY  ./build/app ./bin/mainServer
COPY  ./conf/ /app/conf/
COPY  ./conf/ ./conf/
COPY ./data /app/data
COPY  ./conf/ /app/main-client/bin/conf/
COPY ./cmd/static /app/main-client/bin/static
WORKDIR /app/main-client/bin
CMD ["./mainServer"]