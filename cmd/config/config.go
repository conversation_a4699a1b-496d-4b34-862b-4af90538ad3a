package config

import (
	"github.com/BurntSushi/toml"
	"github.com/fonchain_enterprise/client-auction/pkg/service/order/payment"
	"net/url"
	"os"
	"strings"
)

/********start-配置信息*********/
type Service struct {
	Domain             string
	AppMode            string
	HttpPort           string
	Key                string
	Cert               string
	IsHTTPS            bool
	OssDomain          bool
	ServerDM           string
	Env                string
	SingleUrl          string
	GhId               string
	FddPersonVerifyUrl string
	FddSignContractUrl string
	ContractTemplate   string
	FddVerifyHost      string
	FddSignHost        string
	FddApiHost         string
	Host               string
	ShopHost           string
	ShopQrUrl          string
}

func (s *Service) GetFullHost() string {
	if strings.HasPrefix(s.Host, "http") {
		return s.Host
	}
	host := url.URL{Scheme: "https", Host: s.Host}
	if !s.IsHTTPS {
		host.Scheme = "http"
	}
	return host.String()
}

type File struct {
	ImagePath string
}

// Oss 对象存储配置信息
type Bos struct {
	AccessKeyId     string
	AccessKeySecret string
	Endpoint        string
	Host            string
	BucketName      string
	BosBaseDir      string
	CdnHost         string
}

type Redis struct {
	RedisDB     string
	RedisAddr   string
	RedisPW     string
	RedisDBNAme string
	PoolSize    int
}

type WxPay struct {
	MchID                      string
	MchCertificateSerialNumber string
	MchAPIv3Key                string
	PayNotifyCodeUrl           string
	V2BatchPayNotifyCodeUrl    string
	SuppleNotifyCodeUrl        string
}

type Fdd struct {
	URL             string
	AppId           string
	AppSecret       string
	ContentType     string
	FileContentType string
}

type Msg struct {
	DefaultSignNo string
}

type Config struct {
	Service Service `toml:"service"`
	File    File    `toml:"file"`
	Bos     Bos     `toml:"bos"`
	Redis   Redis   `toml:"redis"`
	Fdd     Fdd     `toml:"fdd"`
	Msg     Msg     `toml:"msg"`
	payment.AllPaymentConfig
	Otel     OtelConfig `toml:"otel"`
	ZapLog   ZapLog     `toml:"zapLog"`
	Fonchain Fonchain   `toml:"fonchain"`
}

type ZapLog struct {
	Level      string
	Filename   string
	MaxSize    int
	MaxAge     int
	MaxBackups int
	Otel       OtelConfig `toml:"otel"`
}

type OtelConfig struct {
	Host  string  `toml:"host"`
	Rate  float64 `toml:"rate"`
	Name  string  `toml:"name"`
	Open  bool    `toml:"open"`
	Debug bool    `toml:"debug"`
}

type Fonchain struct {
	Path   string `toml:"path"`
	Ledger string `toml:"ledger"`
}

/********start-配置信息*********/

var AppConfig *Config

func newConfig() *Config {
	return new(Config)
}

func LoadEnv(path string) error {
	_, err := os.Stat(path)
	if err != nil {
		return err
	}

	AppConfig = newConfig()
	if _, err := toml.DecodeFile(path, AppConfig); err != nil {
		return err
	}

	return nil
}
