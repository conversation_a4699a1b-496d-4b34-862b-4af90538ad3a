package main

import (
	"dubbo.apache.org/dubbo-go/v3/common/logger"
	"fmt"
	appconfig "github.com/fonchain_enterprise/client-auction/cmd/config"
	"github.com/fonchain_enterprise/client-auction/pkg/common/cache"
	_ "github.com/fonchain_enterprise/client-auction/pkg/common/filter"
	"github.com/fonchain_enterprise/client-auction/pkg/common/m"
	"github.com/fonchain_enterprise/client-auction/pkg/common/oss"
	"github.com/fonchain_enterprise/client-auction/pkg/common/otel"
	zapLogger "github.com/fonchain_enterprise/client-auction/pkg/logger"
	"github.com/fonchain_enterprise/client-auction/pkg/logic"
	"github.com/fonchain_enterprise/client-auction/pkg/logic/culture"
	"github.com/fonchain_enterprise/client-auction/pkg/router"
	_ "github.com/fonchain_enterprise/client-auction/pkg/service"
	"github.com/fonchain_enterprise/utils/zap_log"
	"github.com/robfig/cron/v3"
	"os"
)

// export DUBBO_GO_CONFIG_PATH=$PATH_TO_APP/conf/dubbogo.yaml
func main() {

	if err := bootstrap(); err != nil {
		panic(err)
	}
	//logger.SetLogger(zap_log.GetFakeLogger())

	//dubbo日志
	logger.Info("帐号微服务启动成功,git commit:", os.Getenv("GIT_COMMIT"))

	if appconfig.AppConfig.Service.AppMode != "local" {
		c := cron.New()
		c.AddFunc("@every 2h", logic.SynMoneyRate)          //汇率
		c.AddFunc("@every 5m", culture.CirculationOverTime) //流转超时重置
		/*
			c.AddFunc("@every 60s", culture.SyncCast)            //文创类型同步去ntf 铸造
			c.AddFunc("@every 60s", culture.SynCirculation)      //流转完毕的同步去ntf流转

		*/
		c.Start()
	}

	fmt.Println("1--", appconfig.AppConfig.Otel.Open)
	if appconfig.AppConfig.Otel.Open == true {
		df := otel.InitTracer(appconfig.AppConfig.Otel.Host, appconfig.AppConfig.Otel.Rate,
			appconfig.AppConfig.Otel.Name, appconfig.AppConfig.Otel.Debug)
		defer df()
	}

	r := router.NewRouter()

	_ = r.Run(appconfig.AppConfig.Service.HttpPort)
}

// Bootstrap 注册需要单例配置的服务
func bootstrap() (err error) {
	err = zap_log.InitZapLog(m.LOG_CONFIG)

	if err != nil {
		return err
	}

	err = appconfig.LoadEnv(m.SERVER_CONFIG)

	if err != nil {
		return err
	}

	fmt.Println("当前读取配置文件:", appconfig.AppConfig)
	fmt.Println("当前读取配置文件domain:", appconfig.AppConfig.Service.Domain)
	//bos 初始化
	bossConfig := oss.Bos{
		AccessKeyId:     appconfig.AppConfig.Bos.AccessKeyId,
		AccessKeySecret: appconfig.AppConfig.Bos.AccessKeySecret,
		Endpoint:        appconfig.AppConfig.Bos.Endpoint,
		Host:            appconfig.AppConfig.Bos.Host,
		BucketName:      appconfig.AppConfig.Bos.BucketName,
		BosBaseDir:      appconfig.AppConfig.Bos.BosBaseDir,
		CdnHost:         appconfig.AppConfig.Bos.CdnHost,
	}
	zapLogger.LogInit(appconfig.AppConfig)
	err = oss.LoadOssEnv(bossConfig)
	if err != nil {
		return err
	}

	redisConfig := cache.RedisConfig{
		RedisDB:     appconfig.AppConfig.Redis.RedisDB,
		RedisAddr:   appconfig.AppConfig.Redis.RedisAddr,
		RedisPw:     appconfig.AppConfig.Redis.RedisPW,
		RedisDbName: appconfig.AppConfig.Redis.RedisDBNAme,
		PoolSize:    appconfig.AppConfig.Redis.PoolSize,
	}
	cache.LoadRedis(redisConfig)

	return nil
}
