<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>支付结果</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            background-color: #f7f7f7;
            margin: 0;
            padding: 0;
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100vh;
        }
        .cancel-container {
            background-color: #fff;
            padding: 40px;
            border-radius: 8px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            text-align: center;
        }
        .cancel-icon {
            font-size: 48px;
            color: #f44336;
        }
        .cancel-title {
            font-size: 24px;
            color: #333;
            margin-top: 20px;
        }
        .message {
            font-size: 16px;
            color: #666;
            margin-top: 10px;
        }
        .cancel-button {
            display: inline-block;
            background-color: #f44336;
            color: white;
            padding: 10px 20px;
            border-radius: 5px;
            text-decoration: none;
            margin-top: 20px;
            font-size: 16px;
        }
        .cancel-button:hover {
            background-color: #e53935;
        }
        .success-container {
            background-color: #fff;
            padding: 40px;
            border-radius: 8px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            text-align: center;
        }
        .success-icon {
            font-size: 48px;
            color: #4CAF50;
        }
        .success-title {
            font-size: 24px;
            color: #333;
            margin-top: 20px;
        }
        .inconfirm-container {
            background-color: #fff;
            padding: 40px;
            border-radius: 8px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            text-align: center;
            max-width: 400px;
            margin: auto;
        }
        .inconfirm-icon {
            font-size: 48px;
            color: #2196f3;
        }
        .inconfirm-title {
            font-size: 24px;
            color: #333;
            margin-top: 20px;
        }
        .inconfirm-timer {
            font-size: 18px;
            color: #333;
            margin-top: 20px;
        }
        .details {
            font-size: 16px;
            color: #333;
            margin-top: 20px;
            padding: 20px;
            background-color: #eef7ff;
            border-radius: 8px;
        }
        .detail-item {
            margin-bottom: 10px;
        }
        .detail-item:last-child {
            margin-bottom: 0;
        }
        .detail-label {
            font-weight: bold;
            margin-bottom: 5px;
        }
        .detail-value {
            font-style: italic;
        }
        .back-to-artistpay-button {
            display: inline-block;
            padding: 10px 20px;
            margin-top: 20px;
            text-align: center;
            text-decoration: none;
            color: white;
            background-color: #007bff;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
        }

        .back-to-artistpay-button:hover {
            background-color: #3d95ea;
        }
        .back-to-artistpay-button:active {
            background-color: #0254b4;
        }
        .button-container {
            display: flex;
            justify-content: center;
            margin-top: 20px; /* 根据需要调整间距 */
        }
    </style>
</head>
<body>
<div class="{{.Result}}-container">
    <i class="{{.Result}}-icon">{{ if eq .Result `success` }}✓{{ else if eq .Result `inconfirm`}}🕒{{ else }}✗{{ end }}</i>
    <h1 class="{{.Result}}-title">{{ .HtmlTitle }}</h1>
    <p class="message">{{ .Message }}</p>
    {{ if eq .Result `inconfirm` }}<div class="inconfirm-timer" id="timer">10秒后自动刷新页面...</div>{{ end }}
    <div class="details">
        <div class="detail-item">
            <div class="detail-label">订单号:</div>
            <div class="detail-value" id="show-uid">{{ .HtmlOrderNo }}</div>
        </div>
        <div class="detail-item">
            <div class="detail-label">价格:</div>
            <div class="detail-value" id="order-id">{{ .HtmlCurrency }} {{ .HtmlPrice }}</div>
        </div>
    </div>
</div>
<script>
    {{ if eq .Result `inconfirm` }}
    function startTimer(duration, display) {
        var timer = duration, seconds;
        setInterval(function () {
            seconds = parseInt(timer % 60, 10);

            display.textContent = seconds + '秒后自动刷新页面...';

            if (--timer < 0) {
                timer = duration;
                window.location.reload(true); // 刷新页面
            }
        }, 1000);
    }

    window.onload = function () {
        var duration = 10; // 设置倒计时时间为10秒
        var display = document.querySelector('#timer');
        startTimer(duration, display);
    };
    {{ end }}
</script>
</body>
</html>