<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>支付页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            background-color: #f4f4f4;
            margin: 0;
            padding: 0;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            height: 100vh;
            text-align: center;
        }

        h1 {
            color: #333;
        }
        span {
            color: #555;
            font-size: 1.2em;
        }
        p {
            color: #555;
            font-size: 1.2em;
        }

        button {
            background-color: #007BFF;
            color: white;
            border: none;
            padding: 10px 20px;
            margin: 5px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 1em;
            transition: background-color 0.3s ease;
        }

        button:hover {
            background-color: #0056b3;
        }

        .payment-form {
            display: none;
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: #fff;
            padding: 20px;
            border: 1px solid #ccc;
            border-radius: 10px;
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
            z-index: 1000;
            width: 90%;
            max-width: 400px;
        }

        .overlay {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            z-index: 999;
        }

        .payment-form h3 {
            color: #333;
            margin-bottom: 10px;
        }

        .payment-form p {
            margin-bottom: 10px;
        }

        .payment-form input[type="number"] {
            width: 100%;
            padding: 10px;
            margin-bottom: 10px;
            border: 1px solid #ccc;
            border-radius: 5px;
            font-size: 1em;
        }

        .payment-form button {
            width: 100%;
            margin-top: 10px;
        }

        .payment-form button[type="button"] {
            background-color: #ccc;
            color: #333;
        }

        .payment-form button[type="button"]:hover {
            background-color: #aaa;
        }

        @media (max-width: 600px) {
            button {
                padding: 8px 16px;
                font-size: 0.9em;
            }

            .payment-form input[type="number"] {
                padding: 8px;
                font-size: 0.9em;
            }

            .payment-form button {
                padding: 8px;
                font-size: 0.9em;
            }
        }
    </style>
    <script>
        // 获取URL参数
        function getQueryParam(param) {
            const urlParams = new URLSearchParams(window.location.search);
            return urlParams.get(param);
        }

        // 发起POST请求
        async function postRequest(url, data) {
            try {
                const response = await fetch(url, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(data)
                });
                return await response.json();
            } catch (error) {
                console.error('请求失败:', error);
                alert('请求失败，请稍后重试');
            }
        }

        // 页面加载时调用接口获取订单信息
        async function fetchOrderInfo() {
            const qrUid = getQueryParam('qrUid');
            if (!qrUid) {
                alert('未找到QR UID参数');
                return;
            }

            const response = await postRequest('/api/v1/offlineQrcode/info', { qrUid });
            if (response && response.status === 0) {
                const data = response.data;
                const payStatus = data.payStatus;
                const price = parseFloat(data.price);
                const paidPrice = parseFloat(data.paidPrice);
                const remainingAmount = price - paidPrice;
                document.getElementById('price_total').innerText = "总金额："+data.currency+"  "+price;
                document.getElementById('remaining-amount').innerText = remainingAmount.toFixed(2);
                document.getElementById('remaining-currency').innerText = data.currency;
                if (payStatus === 2) {
                    document.getElementById('payment-status').innerText = '已付款';
                    document.getElementById('payment-status').style.color = 'green';
                    document.getElementById('payment-status').style.fontWeight = 'bold';
                } else if (payStatus === 1) {
                    document.getElementById('payment-status').innerText = '未支付';
                    document.getElementById('full-payment-btn').style.display = 'inline-block';
                }else if (payStatus === 4){
                    document.getElementById('remaining-amount-currency').innerText = "待付款："+data.currency;
                    document.getElementById('remaining-amount-value').innerText = remainingAmount;
                    document.getElementById('full-payment-btn').style.display = 'inline-block';
                }
                document.getElementById('partial-payment-btn').style.display = 'inline-block';

                // 设置支付部分按钮的点击事件
                document.getElementById('partial-payment-btn').addEventListener('click', () => {
                    document.getElementById('payment-form').style.display = 'block';
                    document.getElementById('overlay').style.display = 'block';
                });

                // 设置支付部分表单的提交事件
                document.getElementById('payment-form').addEventListener('submit', (e) => {
                    e.preventDefault();
                    const partialAmount = parseFloat(document.getElementById('partial-amount').value);
                    //const remainingAmount = parseFloat(document.getElementById('remaining-amount').innerText);
                    if (!isNaN(partialAmount) && partialAmount > 0) {
                        createOrder(partialAmount);
                        document.getElementById('payment-form').style.display = 'none';
                        document.getElementById('overlay').style.display = 'none';
                    } else {
                        alert('输入的金额无效');
                    }
                });
            } else {
                alert('获取订单信息失败');
            }
        }

        // 创建订单
        async function createOrder(price) {
            const qrUid = getQueryParam('qrUid');
            const response = await postRequest('/api/v1/offlineQrcode/createOrder', {
                price: price.toFixed(2),
                qrUid,
                successEp: '/api/v1/mock/web/pay/result',
                failedEp: '/api/v1/mock/web/pay/cancel'
            });

            if (response && response.status === 0) {
                const checkoutSessionUrl = response.data.checkoutSessionUrl;
                window.location.href = checkoutSessionUrl;
            } else {
                alert('创建订单失败');
            }
        }

        // 页面加载时调用
        window.onload = fetchOrderInfo;
    </script>
</head>
<body>
<h1>支付页面</h1>
<span id="price_total"></span>
<p><span id="remaining-amount-currency"></span>  <span id="remaining-amount-value"></span></p>
<p id="payment-status"></p>
<button id="full-payment-btn" style="display: none;" onclick="createOrder(parseFloat(document.getElementById('remaining-amount').innerText))">全部支付</button>
<button id="partial-payment-btn" style="display: none;">支付部分</button>

<!-- 支付部分表单 -->
<div id="overlay" class="overlay"></div>
<div id="payment-form" class="payment-form">
    <h3>支付部分金额</h3>
    <p>剩余金额：<span id="remaining-currency"></span> <span id="remaining-amount"></span></p>
    <form>
        <label for="partial-amount">输入支付金额：</label>
        <input type="number" id="partial-amount" style="width:90%" step="0.01" min="0" required>
        <button type="submit">确定</button>
        <button type="button" onclick="document.getElementById('payment-form').style.display='none'; document.getElementById('overlay').style.display='none';">取消</button>
    </form>
</div>
</body>
</html>